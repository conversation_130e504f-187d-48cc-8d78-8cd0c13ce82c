#
# Bug#53806: Wrong estimates for range query in partitioned MyISAM table
# Bug#46754: 'rows' field doesn't reflect partition pruning
#
CREATE TABLE t1 (a INT PRIMARY KEY)
PARTITION BY RANGE (a) (
PARTITION p0 VALUES LESS THAN (1),
PARTITION p1 VALUES LESS THAN (2),
PARTITION p2 VALUES LESS THAN (3),
PARTITION p3 VALUES LESS THAN (4),
PARTITION p4 VALUES LESS THAN (5),
PARTITION p5 VALUES LESS THAN (6),
PARTITION max VALUES LESS THAN MAXVALUE);
INSERT INTO t1 VALUES (-1),(0),(1),(2),(3),(4),(5),(6),(7),(8);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT * FROM t1 WHERE a <= 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	#	#	#	#	#	#	#	#	3	100.00	#
Warnings:
#	#	#
EXPLAIN SELECT * FROM t1 WHERE a < 7;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	#	#	#	#	#	#	#	#	8	100.00	#
Warnings:
#	#	#
EXPLAIN SELECT * FROM t1 WHERE a <= 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	#	#	#	#	#	#	#	#	3	100.00	#
Warnings:
#	#	#
DROP TABLE t1;
#
# Bug#49742: Partition Pruning not working correctly for RANGE
#
CREATE TABLE t1 (a INT PRIMARY KEY)
PARTITION BY RANGE (a) (
PARTITION p0 VALUES LESS THAN (1),
PARTITION p1 VALUES LESS THAN (2),
PARTITION p2 VALUES LESS THAN (3),
PARTITION p3 VALUES LESS THAN (4),
PARTITION p4 VALUES LESS THAN (5),
PARTITION p5 VALUES LESS THAN (6),
PARTITION max VALUES LESS THAN MAXVALUE);
INSERT INTO t1 VALUES (-1),(0),(1),(2),(3),(4),(5),(6),(7),(8);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT * FROM t1 WHERE a < 1;
a
-1
0
EXPLAIN SELECT * FROM t1 WHERE a < 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 1)
SELECT * FROM t1 WHERE a < 2;
a
-1
0
1
EXPLAIN SELECT * FROM t1 WHERE a < 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 2)
SELECT * FROM t1 WHERE a < 3;
a
-1
0
1
2
EXPLAIN SELECT * FROM t1 WHERE a < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 3)
SELECT * FROM t1 WHERE a < 4;
a
-1
0
1
2
3
EXPLAIN SELECT * FROM t1 WHERE a < 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 4)
SELECT * FROM t1 WHERE a < 5;
a
-1
0
1
2
3
4
EXPLAIN SELECT * FROM t1 WHERE a < 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 5)
SELECT * FROM t1 WHERE a < 6;
a
-1
0
1
2
3
4
5
EXPLAIN SELECT * FROM t1 WHERE a < 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 6)
SELECT * FROM t1 WHERE a < 7;
a
-1
0
1
2
3
4
5
6
EXPLAIN SELECT * FROM t1 WHERE a < 7;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,max	range	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 7)
SELECT * FROM t1 WHERE a <= 1;
a
-1
0
1
EXPLAIN SELECT * FROM t1 WHERE a <= 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= 1)
SELECT * FROM t1 WHERE a <= 2;
a
-1
0
1
2
EXPLAIN SELECT * FROM t1 WHERE a <= 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= 2)
SELECT * FROM t1 WHERE a <= 3;
a
-1
0
1
2
3
EXPLAIN SELECT * FROM t1 WHERE a <= 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= 3)
SELECT * FROM t1 WHERE a <= 4;
a
-1
0
1
2
3
4
EXPLAIN SELECT * FROM t1 WHERE a <= 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= 4)
SELECT * FROM t1 WHERE a <= 5;
a
-1
0
1
2
3
4
5
EXPLAIN SELECT * FROM t1 WHERE a <= 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= 5)
SELECT * FROM t1 WHERE a <= 6;
a
-1
0
1
2
3
4
5
6
EXPLAIN SELECT * FROM t1 WHERE a <= 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,max	range	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= 6)
SELECT * FROM t1 WHERE a <= 7;
a
-1
0
1
2
3
4
5
6
7
EXPLAIN SELECT * FROM t1 WHERE a <= 7;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,max	range	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= 7)
SELECT * FROM t1 WHERE a = 1;
a
1
EXPLAIN SELECT * FROM t1 WHERE a = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1	const	PRIMARY	PRIMARY	4	const	#	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select '1' AS `a` from `test`.`t1` where true
SELECT * FROM t1 WHERE a = 2;
a
2
EXPLAIN SELECT * FROM t1 WHERE a = 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2	const	PRIMARY	PRIMARY	4	const	#	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select '2' AS `a` from `test`.`t1` where true
SELECT * FROM t1 WHERE a = 3;
a
3
EXPLAIN SELECT * FROM t1 WHERE a = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p3	const	PRIMARY	PRIMARY	4	const	#	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select '3' AS `a` from `test`.`t1` where true
SELECT * FROM t1 WHERE a = 4;
a
4
EXPLAIN SELECT * FROM t1 WHERE a = 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p4	const	PRIMARY	PRIMARY	4	const	#	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select '4' AS `a` from `test`.`t1` where true
SELECT * FROM t1 WHERE a = 5;
a
5
EXPLAIN SELECT * FROM t1 WHERE a = 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p5	const	PRIMARY	PRIMARY	4	const	#	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select '5' AS `a` from `test`.`t1` where true
SELECT * FROM t1 WHERE a = 6;
a
6
EXPLAIN SELECT * FROM t1 WHERE a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	const	PRIMARY	PRIMARY	4	const	#	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select '6' AS `a` from `test`.`t1` where true
SELECT * FROM t1 WHERE a = 7;
a
7
EXPLAIN SELECT * FROM t1 WHERE a = 7;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	const	PRIMARY	PRIMARY	4	const	#	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select '7' AS `a` from `test`.`t1` where true
SELECT * FROM t1 WHERE a >= 1;
a
1
2
3
4
5
6
7
8
EXPLAIN SELECT * FROM t1 WHERE a >= 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2,p3,p4,p5,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= 1)
SELECT * FROM t1 WHERE a >= 2;
a
2
3
4
5
6
7
8
EXPLAIN SELECT * FROM t1 WHERE a >= 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2,p3,p4,p5,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= 2)
SELECT * FROM t1 WHERE a >= 3;
a
3
4
5
6
7
8
EXPLAIN SELECT * FROM t1 WHERE a >= 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p3,p4,p5,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= 3)
SELECT * FROM t1 WHERE a >= 4;
a
4
5
6
7
8
EXPLAIN SELECT * FROM t1 WHERE a >= 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p4,p5,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= 4)
SELECT * FROM t1 WHERE a >= 5;
a
5
6
7
8
EXPLAIN SELECT * FROM t1 WHERE a >= 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p5,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= 5)
SELECT * FROM t1 WHERE a >= 6;
a
6
7
8
EXPLAIN SELECT * FROM t1 WHERE a >= 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= 6)
SELECT * FROM t1 WHERE a >= 7;
a
7
8
EXPLAIN SELECT * FROM t1 WHERE a >= 7;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	index	PRIMARY	PRIMARY	4	NULL	#	66.67	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= 7)
SELECT * FROM t1 WHERE a > 1;
a
2
3
4
5
6
7
8
EXPLAIN SELECT * FROM t1 WHERE a > 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2,p3,p4,p5,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 1)
SELECT * FROM t1 WHERE a > 2;
a
3
4
5
6
7
8
EXPLAIN SELECT * FROM t1 WHERE a > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p3,p4,p5,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 2)
SELECT * FROM t1 WHERE a > 3;
a
4
5
6
7
8
EXPLAIN SELECT * FROM t1 WHERE a > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p4,p5,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 3)
SELECT * FROM t1 WHERE a > 4;
a
5
6
7
8
EXPLAIN SELECT * FROM t1 WHERE a > 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p5,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 4)
SELECT * FROM t1 WHERE a > 5;
a
6
7
8
EXPLAIN SELECT * FROM t1 WHERE a > 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 5)
SELECT * FROM t1 WHERE a > 6;
a
7
8
EXPLAIN SELECT * FROM t1 WHERE a > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	index	PRIMARY	PRIMARY	4	NULL	#	66.67	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 6)
SELECT * FROM t1 WHERE a > 7;
a
8
EXPLAIN SELECT * FROM t1 WHERE a > 7;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	range	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 7)
DROP TABLE t1;
CREATE TABLE t1 (a INT PRIMARY KEY)
PARTITION BY RANGE (a) (
PARTITION p0 VALUES LESS THAN (1),
PARTITION p1 VALUES LESS THAN (2),
PARTITION p2 VALUES LESS THAN (3),
PARTITION p3 VALUES LESS THAN (4),
PARTITION p4 VALUES LESS THAN (5),
PARTITION max VALUES LESS THAN MAXVALUE);
INSERT INTO t1 VALUES (-1),(0),(1),(2),(3),(4),(5),(6),(7);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT * FROM t1 WHERE a < 1;
a
-1
0
EXPLAIN SELECT * FROM t1 WHERE a < 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 1)
SELECT * FROM t1 WHERE a < 2;
a
-1
0
1
EXPLAIN SELECT * FROM t1 WHERE a < 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 2)
SELECT * FROM t1 WHERE a < 3;
a
-1
0
1
2
EXPLAIN SELECT * FROM t1 WHERE a < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 3)
SELECT * FROM t1 WHERE a < 4;
a
-1
0
1
2
3
EXPLAIN SELECT * FROM t1 WHERE a < 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 4)
SELECT * FROM t1 WHERE a < 5;
a
-1
0
1
2
3
4
EXPLAIN SELECT * FROM t1 WHERE a < 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 5)
SELECT * FROM t1 WHERE a < 6;
a
-1
0
1
2
3
4
5
EXPLAIN SELECT * FROM t1 WHERE a < 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,max	range	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 6)
SELECT * FROM t1 WHERE a <= 1;
a
-1
0
1
EXPLAIN SELECT * FROM t1 WHERE a <= 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= 1)
SELECT * FROM t1 WHERE a <= 2;
a
-1
0
1
2
EXPLAIN SELECT * FROM t1 WHERE a <= 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= 2)
SELECT * FROM t1 WHERE a <= 3;
a
-1
0
1
2
3
EXPLAIN SELECT * FROM t1 WHERE a <= 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= 3)
SELECT * FROM t1 WHERE a <= 4;
a
-1
0
1
2
3
4
EXPLAIN SELECT * FROM t1 WHERE a <= 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= 4)
SELECT * FROM t1 WHERE a <= 5;
a
-1
0
1
2
3
4
5
EXPLAIN SELECT * FROM t1 WHERE a <= 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,max	range	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= 5)
SELECT * FROM t1 WHERE a <= 6;
a
-1
0
1
2
3
4
5
6
EXPLAIN SELECT * FROM t1 WHERE a <= 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,max	range	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= 6)
SELECT * FROM t1 WHERE a = 1;
a
1
EXPLAIN SELECT * FROM t1 WHERE a = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1	const	PRIMARY	PRIMARY	4	const	#	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select '1' AS `a` from `test`.`t1` where true
SELECT * FROM t1 WHERE a = 2;
a
2
EXPLAIN SELECT * FROM t1 WHERE a = 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2	const	PRIMARY	PRIMARY	4	const	#	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select '2' AS `a` from `test`.`t1` where true
SELECT * FROM t1 WHERE a = 3;
a
3
EXPLAIN SELECT * FROM t1 WHERE a = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p3	const	PRIMARY	PRIMARY	4	const	#	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select '3' AS `a` from `test`.`t1` where true
SELECT * FROM t1 WHERE a = 4;
a
4
EXPLAIN SELECT * FROM t1 WHERE a = 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p4	const	PRIMARY	PRIMARY	4	const	#	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select '4' AS `a` from `test`.`t1` where true
SELECT * FROM t1 WHERE a = 5;
a
5
EXPLAIN SELECT * FROM t1 WHERE a = 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	const	PRIMARY	PRIMARY	4	const	#	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select '5' AS `a` from `test`.`t1` where true
SELECT * FROM t1 WHERE a = 6;
a
6
EXPLAIN SELECT * FROM t1 WHERE a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	const	PRIMARY	PRIMARY	4	const	#	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select '6' AS `a` from `test`.`t1` where true
SELECT * FROM t1 WHERE a >= 1;
a
1
2
3
4
5
6
7
EXPLAIN SELECT * FROM t1 WHERE a >= 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2,p3,p4,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= 1)
SELECT * FROM t1 WHERE a >= 2;
a
2
3
4
5
6
7
EXPLAIN SELECT * FROM t1 WHERE a >= 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2,p3,p4,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= 2)
SELECT * FROM t1 WHERE a >= 3;
a
3
4
5
6
7
EXPLAIN SELECT * FROM t1 WHERE a >= 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p3,p4,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= 3)
SELECT * FROM t1 WHERE a >= 4;
a
4
5
6
7
EXPLAIN SELECT * FROM t1 WHERE a >= 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p4,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= 4)
SELECT * FROM t1 WHERE a >= 5;
a
5
6
7
EXPLAIN SELECT * FROM t1 WHERE a >= 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= 5)
SELECT * FROM t1 WHERE a >= 6;
a
6
7
EXPLAIN SELECT * FROM t1 WHERE a >= 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	index	PRIMARY	PRIMARY	4	NULL	#	66.67	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= 6)
SELECT * FROM t1 WHERE a > 1;
a
2
3
4
5
6
7
EXPLAIN SELECT * FROM t1 WHERE a > 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2,p3,p4,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 1)
SELECT * FROM t1 WHERE a > 2;
a
3
4
5
6
7
EXPLAIN SELECT * FROM t1 WHERE a > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p3,p4,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 2)
SELECT * FROM t1 WHERE a > 3;
a
4
5
6
7
EXPLAIN SELECT * FROM t1 WHERE a > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p4,max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 3)
SELECT * FROM t1 WHERE a > 4;
a
5
6
7
EXPLAIN SELECT * FROM t1 WHERE a > 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	index	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 4)
SELECT * FROM t1 WHERE a > 5;
a
6
7
EXPLAIN SELECT * FROM t1 WHERE a > 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	index	PRIMARY	PRIMARY	4	NULL	#	66.67	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 5)
SELECT * FROM t1 WHERE a > 6;
a
7
EXPLAIN SELECT * FROM t1 WHERE a > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	max	range	PRIMARY	PRIMARY	4	NULL	#	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 6)
DROP TABLE t1;
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
# test of RANGE and index
CREATE TABLE t1 (a DATE, KEY(a))
PARTITION BY RANGE (TO_DAYS(a))
(PARTITION `pNULL` VALUES LESS THAN (0),
PARTITION `p0001-01-01` VALUES LESS THAN (366 + 1),
PARTITION `p1001-01-01` VALUES LESS THAN (TO_DAYS('1001-01-01') + 1),
PARTITION `p2001-01-01` VALUES LESS THAN (TO_DAYS('2001-01-01') + 1));
INSERT INTO t1 VALUES ('0000-00-00'), ('0000-01-02'), ('0001-01-01'),
('1001-00-00'), ('1001-01-01'), ('1002-00-00'), ('2001-01-01');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT * FROM t1 WHERE a < '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
SELECT * FROM t1 WHERE a <= '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
SELECT * FROM t1 WHERE a >= '1001-01-01';
a
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a > '1001-01-01';
a
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a = '1001-01-01';
a
1001-01-01
SELECT * FROM t1 WHERE a < '1001-00-00';
a
0000-00-00
0000-01-02
0001-01-01
SELECT * FROM t1 WHERE a <= '1001-00-00';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
SELECT * FROM t1 WHERE a >= '1001-00-00';
a
1001-00-00
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a > '1001-00-00';
a
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a = '1001-00-00';
a
1001-00-00
SET @previous_sql_mode = @@sql_mode;
SET sql_mode = "ALLOW_INVALID_DATES";
SELECT * FROM t1 WHERE a < '1999-02-31';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a <= '1999-02-31';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a >= '1999-02-31';
a
2001-01-01
SELECT * FROM t1 WHERE a > '1999-02-31';
a
2001-01-01
SELECT * FROM t1 WHERE a = '1999-02-31';
a
SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1002-00-00';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
SELECT * FROM t1 WHERE a BETWEEN '0001-01-02' AND '1002-00-00';
a
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a BETWEEN '0001-01-01' AND '1001-01-01';
a
0001-01-01
1001-00-00
1001-01-01
EXPLAIN SELECT * FROM t1 WHERE a < '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01	range	a	a	4	NULL	4	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a <= '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01	range	a	a	4	NULL	5	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a >= '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p1001-01-01,p2001-01-01	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a > '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p2001-01-01	range	a	a	4	NULL	2	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a = '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1001-01-01	ref	a	a	4	const	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a < '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a <= '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01	range	a	a	4	NULL	4	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a >= '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p1001-01-01,p2001-01-01	range	a	a	4	NULL	4	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a > '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p1001-01-01,p2001-01-01	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a = '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL	ref	a	a	4	const	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a < '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01,p2001-01-01	range	a	a	4	NULL	6	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a <= '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01,p2001-01-01	range	a	a	4	NULL	6	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a >= '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p2001-01-01	range	a	a	4	NULL	1	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a > '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p2001-01-01	range	a	a	4	NULL	1	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a = '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL	ref	a	a	4	const	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1002-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01,p2001-01-01	range	a	a	4	NULL	6	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0000-00-00' and '1002-00-00')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01	range	a	a	4	NULL	5	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0000-00-00' and '1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0001-01-02' AND '1002-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p1001-01-01,p2001-01-01	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0001-01-02' and '1002-00-00')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0001-01-01' AND '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0001-01-01' and '1001-01-01')
SET sql_mode = @previous_sql_mode;
# test without index
ALTER TABLE t1 DROP KEY a;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT * FROM t1 WHERE a < '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
SELECT * FROM t1 WHERE a <= '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
SELECT * FROM t1 WHERE a >= '1001-01-01';
a
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a > '1001-01-01';
a
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a = '1001-01-01';
a
1001-01-01
SELECT * FROM t1 WHERE a < '1001-00-00';
a
0000-00-00
0000-01-02
0001-01-01
SELECT * FROM t1 WHERE a <= '1001-00-00';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
SELECT * FROM t1 WHERE a >= '1001-00-00';
a
1001-00-00
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a > '1001-00-00';
a
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a = '1001-00-00';
a
1001-00-00
SET @previous_sql_mode = @@sql_mode;
SET sql_mode = "ALLOW_INVALID_DATES";
SELECT * FROM t1 WHERE a < '1999-02-31';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a <= '1999-02-31';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a >= '1999-02-31';
a
2001-01-01
SELECT * FROM t1 WHERE a > '1999-02-31';
a
2001-01-01
SELECT * FROM t1 WHERE a = '1999-02-31';
a
SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1002-00-00';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
SELECT * FROM t1 WHERE a BETWEEN '0001-01-02' AND '1002-00-00';
a
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a BETWEEN '0001-01-01' AND '1001-01-01';
a
0001-01-01
1001-00-00
1001-01-01
EXPLAIN SELECT * FROM t1 WHERE a < '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a <= '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a >= '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p1001-01-01,p2001-01-01	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a > '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p2001-01-01	ALL	NULL	NULL	NULL	NULL	4	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a = '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1001-01-01	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a < '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a <= '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a >= '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p1001-01-01,p2001-01-01	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a > '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p1001-01-01,p2001-01-01	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a = '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a < '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01,p2001-01-01	ALL	NULL	NULL	NULL	NULL	7	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a <= '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01,p2001-01-01	ALL	NULL	NULL	NULL	NULL	7	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a >= '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p2001-01-01	ALL	NULL	NULL	NULL	NULL	4	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a > '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p2001-01-01	ALL	NULL	NULL	NULL	NULL	4	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a = '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1002-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01,p2001-01-01	ALL	NULL	NULL	NULL	NULL	7	14.29	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0000-00-00' and '1002-00-00')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	16.67	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0000-00-00' and '1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0001-01-02' AND '1002-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p1001-01-01,p2001-01-01	ALL	NULL	NULL	NULL	NULL	5	20.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0001-01-02' and '1002-00-00')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0001-01-01' AND '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p0001-01-01,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	16.67	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0001-01-01' and '1001-01-01')
SET sql_mode = @previous_sql_mode;
DROP TABLE t1;
# test of LIST and index
CREATE TABLE t1 (a DATE, KEY(a))
PARTITION BY LIST (TO_DAYS(a))
(PARTITION `p0001-01-01` VALUES IN (TO_DAYS('0001-01-01')),
PARTITION `p2001-01-01` VALUES IN (TO_DAYS('2001-01-01')),
PARTITION `pNULL` VALUES IN (NULL),
PARTITION `p0000-01-02` VALUES IN (TO_DAYS('0000-01-02')),
PARTITION `p1001-01-01` VALUES IN (TO_DAYS('1001-01-01')));
INSERT INTO t1 VALUES ('0000-00-00'), ('0000-01-02'), ('0001-01-01'),
('1001-00-00'), ('1001-01-01'), ('1002-00-00'), ('2001-01-01');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT * FROM t1 WHERE a < '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
SELECT * FROM t1 WHERE a <= '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
SELECT * FROM t1 WHERE a >= '1001-01-01';
a
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a > '1001-01-01';
a
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a = '1001-01-01';
a
1001-01-01
SELECT * FROM t1 WHERE a < '1001-00-00';
a
0000-00-00
0000-01-02
0001-01-01
SELECT * FROM t1 WHERE a <= '1001-00-00';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
SELECT * FROM t1 WHERE a >= '1001-00-00';
a
1001-00-00
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a > '1001-00-00';
a
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a = '1001-00-00';
a
1001-00-00
SET @previous_sql_mode = @@sql_mode;
SET sql_mode = "ALLOW_INVALID_DATES";
SELECT * FROM t1 WHERE a < '1999-02-31';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a <= '1999-02-31';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a >= '1999-02-31';
a
2001-01-01
SELECT * FROM t1 WHERE a > '1999-02-31';
a
2001-01-01
SELECT * FROM t1 WHERE a = '1999-02-31';
a
SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1002-00-00';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
SELECT * FROM t1 WHERE a BETWEEN '0001-01-02' AND '1002-00-00';
a
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a BETWEEN '0001-01-01' AND '1001-01-01';
a
0001-01-01
1001-00-00
1001-01-01
EXPLAIN SELECT * FROM t1 WHERE a < '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02	range	a	a	4	NULL	4	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a <= '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	range	a	a	4	NULL	5	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a >= '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL,p1001-01-01	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a > '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL	range	a	a	4	NULL	2	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a = '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1001-01-01	ref	a	a	4	const	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a < '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a <= '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02	range	a	a	4	NULL	4	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a >= '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL,p1001-01-01	range	a	a	4	NULL	4	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a > '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL,p1001-01-01	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a = '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL	ref	a	a	4	const	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a < '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	index	a	a	4	NULL	6	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a <= '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	index	a	a	4	NULL	6	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a >= '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL	range	a	a	4	NULL	1	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a > '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL	range	a	a	4	NULL	1	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a = '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL	ref	a	a	4	const	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1002-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	index	a	a	4	NULL	6	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0000-00-00' and '1002-00-00')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	range	a	a	4	NULL	5	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0000-00-00' and '1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0001-01-02' AND '1002-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p1001-01-01	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0001-01-02' and '1002-00-00')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0001-01-01' AND '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p1001-01-01	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0001-01-01' and '1001-01-01')
SET sql_mode = @previous_sql_mode;
# test without index
ALTER TABLE t1 DROP KEY a;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT * FROM t1 WHERE a < '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
SELECT * FROM t1 WHERE a <= '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
SELECT * FROM t1 WHERE a >= '1001-01-01';
a
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a > '1001-01-01';
a
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a = '1001-01-01';
a
1001-01-01
SELECT * FROM t1 WHERE a < '1001-00-00';
a
0000-00-00
0000-01-02
0001-01-01
SELECT * FROM t1 WHERE a <= '1001-00-00';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
SELECT * FROM t1 WHERE a >= '1001-00-00';
a
1001-00-00
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a > '1001-00-00';
a
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a = '1001-00-00';
a
1001-00-00
SET @previous_sql_mode = @@sql_mode;
SET sql_mode = "ALLOW_INVALID_DATES";
SELECT * FROM t1 WHERE a < '1999-02-31';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a <= '1999-02-31';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a >= '1999-02-31';
a
2001-01-01
SELECT * FROM t1 WHERE a > '1999-02-31';
a
2001-01-01
SELECT * FROM t1 WHERE a = '1999-02-31';
a
SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1002-00-00';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
SELECT * FROM t1 WHERE a BETWEEN '0001-01-02' AND '1002-00-00';
a
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a BETWEEN '0001-01-01' AND '1001-01-01';
a
0001-01-01
1001-00-00
1001-01-01
EXPLAIN SELECT * FROM t1 WHERE a < '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a <= '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a >= '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL,p1001-01-01	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a > '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL	ALL	NULL	NULL	NULL	NULL	4	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a = '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1001-01-01	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a < '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a <= '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a >= '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL,p1001-01-01	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a > '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL,p1001-01-01	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a = '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a < '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a <= '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a >= '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL	ALL	NULL	NULL	NULL	NULL	4	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a > '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL	ALL	NULL	NULL	NULL	NULL	4	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a = '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1002-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	16.67	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0000-00-00' and '1002-00-00')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	16.67	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0000-00-00' and '1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0001-01-02' AND '1002-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p1001-01-01	ALL	NULL	NULL	NULL	NULL	4	25.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0001-01-02' and '1002-00-00')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0001-01-01' AND '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p1001-01-01	ALL	NULL	NULL	NULL	NULL	5	20.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0001-01-01' and '1001-01-01')
SET sql_mode = @previous_sql_mode;
DROP TABLE t1;
# TO_SECONDS, test of LIST and index
CREATE TABLE t1 (a DATE, KEY(a))
PARTITION BY LIST (TO_SECONDS(a))
(PARTITION `p0001-01-01` VALUES IN (TO_SECONDS('0001-01-01')),
PARTITION `p2001-01-01` VALUES IN (TO_SECONDS('2001-01-01')),
PARTITION `pNULL` VALUES IN (NULL),
PARTITION `p0000-01-02` VALUES IN (TO_SECONDS('0000-01-02')),
PARTITION `p1001-01-01` VALUES IN (TO_SECONDS('1001-01-01')));
INSERT INTO t1 VALUES ('0000-00-00'), ('0000-01-02'), ('0001-01-01'),
('1001-00-00'), ('1001-01-01'), ('1002-00-00'), ('2001-01-01');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT * FROM t1 WHERE a < '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
SELECT * FROM t1 WHERE a <= '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
SELECT * FROM t1 WHERE a >= '1001-01-01';
a
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a > '1001-01-01';
a
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a = '1001-01-01';
a
1001-01-01
SELECT * FROM t1 WHERE a < '1001-00-00';
a
0000-00-00
0000-01-02
0001-01-01
SELECT * FROM t1 WHERE a <= '1001-00-00';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
SELECT * FROM t1 WHERE a >= '1001-00-00';
a
1001-00-00
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a > '1001-00-00';
a
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a = '1001-00-00';
a
1001-00-00
SET @previous_sql_mode = @@sql_mode;
SET sql_mode = "ALLOW_INVALID_DATES";
SELECT * FROM t1 WHERE a < '1999-02-31';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a <= '1999-02-31';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a >= '1999-02-31';
a
2001-01-01
SELECT * FROM t1 WHERE a > '1999-02-31';
a
2001-01-01
SELECT * FROM t1 WHERE a = '1999-02-31';
a
SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1002-00-00';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
SELECT * FROM t1 WHERE a BETWEEN '0001-01-02' AND '1002-00-00';
a
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a BETWEEN '0001-01-01' AND '1001-01-01';
a
0001-01-01
1001-00-00
1001-01-01
EXPLAIN SELECT * FROM t1 WHERE a < '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02	range	a	a	4	NULL	4	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a <= '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	range	a	a	4	NULL	5	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a >= '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL,p1001-01-01	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a > '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL	range	a	a	4	NULL	2	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a = '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1001-01-01	ref	a	a	4	const	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a < '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a <= '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02	range	a	a	4	NULL	4	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a >= '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL,p1001-01-01	range	a	a	4	NULL	4	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a > '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL,p1001-01-01	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a = '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL	ref	a	a	4	const	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a < '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	index	a	a	4	NULL	6	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a <= '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	index	a	a	4	NULL	6	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a >= '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL	range	a	a	4	NULL	1	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a > '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL	range	a	a	4	NULL	1	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a = '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL	ref	a	a	4	const	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1002-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	index	a	a	4	NULL	6	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0000-00-00' and '1002-00-00')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	range	a	a	4	NULL	5	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0000-00-00' and '1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0001-01-02' AND '1002-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p1001-01-01	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0001-01-02' and '1002-00-00')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0001-01-01' AND '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p1001-01-01	range	a	a	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0001-01-01' and '1001-01-01')
SET sql_mode = @previous_sql_mode;
# test without index
ALTER TABLE t1 DROP KEY a;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT * FROM t1 WHERE a < '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
SELECT * FROM t1 WHERE a <= '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
SELECT * FROM t1 WHERE a >= '1001-01-01';
a
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a > '1001-01-01';
a
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a = '1001-01-01';
a
1001-01-01
SELECT * FROM t1 WHERE a < '1001-00-00';
a
0000-00-00
0000-01-02
0001-01-01
SELECT * FROM t1 WHERE a <= '1001-00-00';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
SELECT * FROM t1 WHERE a >= '1001-00-00';
a
1001-00-00
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a > '1001-00-00';
a
1001-01-01
1002-00-00
2001-01-01
SELECT * FROM t1 WHERE a = '1001-00-00';
a
1001-00-00
SET @previous_sql_mode = @@sql_mode;
SET sql_mode = "ALLOW_INVALID_DATES";
SELECT * FROM t1 WHERE a < '1999-02-31';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a <= '1999-02-31';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a >= '1999-02-31';
a
2001-01-01
SELECT * FROM t1 WHERE a > '1999-02-31';
a
2001-01-01
SELECT * FROM t1 WHERE a = '1999-02-31';
a
SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1002-00-00';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1001-01-01';
a
0000-00-00
0000-01-02
0001-01-01
1001-00-00
1001-01-01
SELECT * FROM t1 WHERE a BETWEEN '0001-01-02' AND '1002-00-00';
a
1001-00-00
1001-01-01
1002-00-00
SELECT * FROM t1 WHERE a BETWEEN '0001-01-01' AND '1001-01-01';
a
0001-01-01
1001-00-00
1001-01-01
EXPLAIN SELECT * FROM t1 WHERE a < '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a <= '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a >= '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL,p1001-01-01	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a > '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL	ALL	NULL	NULL	NULL	NULL	4	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a = '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1001-01-01	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a < '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a <= '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a >= '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL,p1001-01-01	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a > '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL,p1001-01-01	ALL	NULL	NULL	NULL	NULL	5	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a = '1001-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1001-00-00')
EXPLAIN SELECT * FROM t1 WHERE a < '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a <= '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` <= DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a >= '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL	ALL	NULL	NULL	NULL	NULL	4	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` >= DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a > '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2001-01-01,pNULL	ALL	NULL	NULL	NULL	NULL	4	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a = '1999-02-31';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = DATE'1999-02-31')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1002-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	16.67	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0000-00-00' and '1002-00-00')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0000-00-00' AND '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p0000-01-02,p1001-01-01	ALL	NULL	NULL	NULL	NULL	6	16.67	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0000-00-00' and '1001-01-01')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0001-01-02' AND '1002-00-00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pNULL,p1001-01-01	ALL	NULL	NULL	NULL	NULL	4	25.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0001-01-02' and '1002-00-00')
EXPLAIN SELECT * FROM t1 WHERE a BETWEEN '0001-01-01' AND '1001-01-01';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0001-01-01,pNULL,p1001-01-01	ALL	NULL	NULL	NULL	NULL	5	20.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between '0001-01-01' and '1001-01-01')
SET sql_mode = @previous_sql_mode;
DROP TABLE t1;
SET sql_mode = default;
# Test with DATETIME column NOT NULL
CREATE TABLE t1 (
a int(10) unsigned NOT NULL,
b DATETIME NOT NULL,
PRIMARY KEY (a, b)
) PARTITION BY RANGE (TO_DAYS(b))
(PARTITION p20090401 VALUES LESS THAN (TO_DAYS('2009-04-02')),
PARTITION p20090402 VALUES LESS THAN (TO_DAYS('2009-04-03')),
PARTITION p20090403 VALUES LESS THAN (TO_DAYS('2009-04-04')),
PARTITION p20090404 VALUES LESS THAN (TO_DAYS('2009-04-05')),
PARTITION p20090405 VALUES LESS THAN MAXVALUE);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1, '2009-01-01'), (1, '2009-04-01'), (2, '2009-04-01'),
(1, '2009-04-02'), (2, '2009-04-02'), (1, '2009-04-02 23:59:59'),
(1, '2009-04-03'), (2, '2009-04-03'), (1, '2009-04-04'), (2, '2009-04-04'),
(1, '2009-04-05'), (1, '2009-04-06'), (1, '2009-04-07');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT * FROM t1 WHERE b < CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b <= CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b = CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	index	PRIMARY	PRIMARY	9	NULL	#	50.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b >= CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b > CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b < CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b <= CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b = CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090402	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b >= CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b > CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b < CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b <= CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b = CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	index	PRIMARY	PRIMARY	9	NULL	#	50.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b >= CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b > CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b < '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b <= '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b = '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	index	PRIMARY	PRIMARY	9	NULL	#	50.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b >= '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b > '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b < '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < TIMESTAMP'2009-04-02 23:59:59')
EXPLAIN SELECT * FROM t1 WHERE b <= '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= TIMESTAMP'2009-04-02 23:59:59')
EXPLAIN SELECT * FROM t1 WHERE b = '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090402	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = TIMESTAMP'2009-04-02 23:59:59')
EXPLAIN SELECT * FROM t1 WHERE b >= '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= TIMESTAMP'2009-04-02 23:59:59')
EXPLAIN SELECT * FROM t1 WHERE b > '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > TIMESTAMP'2009-04-02 23:59:59')
EXPLAIN SELECT * FROM t1 WHERE b < '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b <= '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b = '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	index	PRIMARY	PRIMARY	9	NULL	#	50.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b >= '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b > '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1
WHERE b < CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b <= CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b = CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	index	PRIMARY	PRIMARY	9	NULL	#	50.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b >= CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b > CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b < CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b <= CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b = CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090402	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b >= CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b > CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	9	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-02 23:59:58' as datetime)))
DROP TABLE t1;
# Test with DATE column NOT NULL
CREATE TABLE t1 (
a int(10) unsigned NOT NULL,
b DATE NOT NULL,
PRIMARY KEY (a, b)
) PARTITION BY RANGE (TO_DAYS(b))
(PARTITION p20090401 VALUES LESS THAN (TO_DAYS('2009-04-02')),
PARTITION p20090402 VALUES LESS THAN (TO_DAYS('2009-04-03')),
PARTITION p20090403 VALUES LESS THAN (TO_DAYS('2009-04-04')),
PARTITION p20090404 VALUES LESS THAN (TO_DAYS('2009-04-05')),
PARTITION p20090405 VALUES LESS THAN MAXVALUE);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1, '2009-01-01'), (1, '2009-04-01'), (2, '2009-04-01'),
(1, '2009-04-02'), (2, '2009-04-02'), (1, '2009-04-03'), (2, '2009-04-03'),
(1, '2009-04-04'), (2, '2009-04-04'), (1, '2009-04-05'), (1, '2009-04-06'),
(1, '2009-04-07');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT * FROM t1 WHERE b < CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b <= CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b = CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	index	PRIMARY	PRIMARY	7	NULL	#	50.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b >= CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b > CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b < CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b <= CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b = CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	no matching row in const table
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where multiple equal(cast('2009-04-02 23:59:59' as datetime), `test`.`t1`.`b`)
EXPLAIN SELECT * FROM t1
WHERE b >= CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b > CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b < CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b <= CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b = CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	index	PRIMARY	PRIMARY	7	NULL	#	50.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b >= CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b > CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b < '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b <= '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b = '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	index	PRIMARY	PRIMARY	7	NULL	#	50.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b >= '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b > '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b < '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= DATE'2009-04-02')
EXPLAIN SELECT * FROM t1 WHERE b <= '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= DATE'2009-04-02')
EXPLAIN SELECT * FROM t1 WHERE b = '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	Impossible WHERE
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where false
EXPLAIN SELECT * FROM t1 WHERE b >= '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > DATE'2009-04-02')
EXPLAIN SELECT * FROM t1 WHERE b > '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > DATE'2009-04-02')
EXPLAIN SELECT * FROM t1 WHERE b < '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b <= '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b = '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	index	PRIMARY	PRIMARY	7	NULL	#	50.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b >= '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b > '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > DATE'2009-04-03')
EXPLAIN SELECT * FROM t1
WHERE b < CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b <= CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b = CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	no matching row in const table
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where multiple equal(cast('2009-04-03 00:00:01' as datetime), `test`.`t1`.`b`)
EXPLAIN SELECT * FROM t1
WHERE b >= CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b > CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b < CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b <= CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b = CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	no matching row in const table
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where multiple equal(cast('2009-04-02 23:59:58' as datetime), `test`.`t1`.`b`)
EXPLAIN SELECT * FROM t1
WHERE b >= CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b > CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	index	PRIMARY	PRIMARY	7	NULL	#	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-02 23:59:58' as datetime)))
DROP TABLE t1;
# Test with DATETIME column NULL
CREATE TABLE t1 (
a int(10) unsigned NOT NULL,
b DATETIME NULL
) PARTITION BY RANGE (TO_DAYS(b))
(PARTITION p20090401 VALUES LESS THAN (TO_DAYS('2009-04-02')),
PARTITION p20090402 VALUES LESS THAN (TO_DAYS('2009-04-03')),
PARTITION p20090403 VALUES LESS THAN (TO_DAYS('2009-04-04')),
PARTITION p20090404 VALUES LESS THAN (TO_DAYS('2009-04-05')),
PARTITION p20090405 VALUES LESS THAN MAXVALUE);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1, '2009-01-01'), (1, '2009-04-01'), (2, '2009-04-01'),
(1, '2009-04-02'), (2, '2009-04-02'), (1, '2009-04-02 23:59:59'),
(1, '2009-04-03'), (2, '2009-04-03'), (1, '2009-04-04'), (2, '2009-04-04'),
(1, '2009-04-05'), (1, '2009-04-06'), (1, '2009-04-07');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT * FROM t1 WHERE b < CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b <= CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b = CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b >= CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b > CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b < CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b <= CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b = CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b >= CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b > CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b < CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b <= CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b = CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b >= CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b > CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b < '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b <= '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b = '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b >= '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b > '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b < '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < TIMESTAMP'2009-04-02 23:59:59')
EXPLAIN SELECT * FROM t1 WHERE b <= '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= TIMESTAMP'2009-04-02 23:59:59')
EXPLAIN SELECT * FROM t1 WHERE b = '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = TIMESTAMP'2009-04-02 23:59:59')
EXPLAIN SELECT * FROM t1 WHERE b >= '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= TIMESTAMP'2009-04-02 23:59:59')
EXPLAIN SELECT * FROM t1 WHERE b > '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > TIMESTAMP'2009-04-02 23:59:59')
EXPLAIN SELECT * FROM t1 WHERE b < '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b <= '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b = '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b >= '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1 WHERE b > '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > TIMESTAMP'2009-04-03 00:00:00')
EXPLAIN SELECT * FROM t1
WHERE b < CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b <= CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b = CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b >= CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b > CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b < CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b <= CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b = CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b >= CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b > CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-02 23:59:58' as datetime)))
DROP TABLE t1;
# Test with DATE column NULL
CREATE TABLE t1 (
a int(10) unsigned NOT NULL,
b DATE NULL
) PARTITION BY RANGE (TO_DAYS(b))
(PARTITION p20090401 VALUES LESS THAN (TO_DAYS('2009-04-02')),
PARTITION p20090402 VALUES LESS THAN (TO_DAYS('2009-04-03')),
PARTITION p20090403 VALUES LESS THAN (TO_DAYS('2009-04-04')),
PARTITION p20090404 VALUES LESS THAN (TO_DAYS('2009-04-05')),
PARTITION p20090405 VALUES LESS THAN MAXVALUE);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1, '2009-01-01'), (1, '2009-04-01'), (2, '2009-04-01'),
(1, '2009-04-02'), (2, '2009-04-02'), (1, '2009-04-03'), (2, '2009-04-03'),
(1, '2009-04-04'), (2, '2009-04-04'), (1, '2009-04-05'), (1, '2009-04-06'),
(1, '2009-04-07');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT * FROM t1 WHERE b < CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b <= CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b = CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b >= CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b > CAST('2009-04-03' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-03' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b < CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b <= CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b = CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b >= CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b > CAST('2009-04-02 23:59:59' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-02 23:59:59' as datetime)))
EXPLAIN SELECT * FROM t1 WHERE b < CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b <= CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b = CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b >= CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b > CAST('2009-04-03' AS DATE);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-03' as date)))
EXPLAIN SELECT * FROM t1 WHERE b < '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b <= '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b = '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b >= '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b > '2009-04-03 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b < '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= DATE'2009-04-02')
EXPLAIN SELECT * FROM t1 WHERE b <= '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= DATE'2009-04-02')
EXPLAIN SELECT * FROM t1 WHERE b = '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	Impossible WHERE
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where false
EXPLAIN SELECT * FROM t1 WHERE b >= '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > DATE'2009-04-02')
EXPLAIN SELECT * FROM t1 WHERE b > '2009-04-02 23:59:59';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > DATE'2009-04-02')
EXPLAIN SELECT * FROM t1 WHERE b < '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b <= '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b = '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090403	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b >= '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= DATE'2009-04-03')
EXPLAIN SELECT * FROM t1 WHERE b > '2009-04-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > DATE'2009-04-03')
EXPLAIN SELECT * FROM t1
WHERE b < CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b <= CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402,p20090403	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b = CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b >= CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b > CAST('2009-04-03 00:00:01' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-03 00:00:01' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b < CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b <= CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090402	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` <= <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b = CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` = <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b >= CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` >= <cache>(cast('2009-04-02 23:59:58' as datetime)))
EXPLAIN SELECT * FROM t1
WHERE b > CAST('2009-04-02 23:59:58' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p20090401,p20090403,p20090404,p20090405	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` > <cache>(cast('2009-04-02 23:59:58' as datetime)))
DROP TABLE t1;
# For better code coverage of the patch
CREATE TABLE t1 (
a int(10) unsigned NOT NULL,
b DATE
) PARTITION BY RANGE ( TO_DAYS(b) )
(PARTITION p20090401 VALUES LESS THAN (TO_DAYS('2009-04-02')),
PARTITION p20090402 VALUES LESS THAN (TO_DAYS('2009-04-03')),
PARTITION p20090403 VALUES LESS THAN (TO_DAYS('2009-04-04')),
PARTITION p20090404 VALUES LESS THAN (TO_DAYS('2009-04-05')),
PARTITION p20090405 VALUES LESS THAN MAXVALUE);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1, '2009-01-01'), (2, NULL);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
# test with an invalid date, which lead to item->null_value is set.
EXPLAIN SELECT * FROM t1 WHERE b < CAST('2009-04-99' AS DATETIME);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Warning	1292	Incorrect datetime value: '2009-04-99'
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`b` < <cache>(cast('2009-04-99' as datetime)))
DROP TABLE t1;
SET @previous_sql_mode = @@sql_mode;
SET sql_mode = 'ALLOW_INVALID_DATES';
CREATE TABLE t1
(a INT NOT NULL AUTO_INCREMENT,
b DATETIME,
PRIMARY KEY (a,b),
KEY (b))
PARTITION BY RANGE (to_days(b))
(PARTITION p0 VALUES LESS THAN (733681) COMMENT = 'LESS THAN 2008-10-01',
PARTITION p1 VALUES LESS THAN (733712) COMMENT = 'LESS THAN 2008-11-01',
PARTITION pX VALUES LESS THAN MAXVALUE);
SELECT a,b FROM t1 WHERE b >= '2008-12-01' AND b < '2009-12-00';
a	b
DROP TABLE t1;
SET sql_mode = @previous_sql_mode;
create table t1 ( a int not null) partition by hash(a) partitions 2;
insert into t1 values (1),(2),(3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where a=5 and a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	Impossible WHERE
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where false
drop table t1;
create table t1 (
a int(11) not null
) partition by hash (a) partitions 2;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t1 values (1),(2),(3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where a=1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = 1)
explain select * from t1 where a=2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = 2)
explain select * from t1 where a=1 or a=2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1	ALL	NULL	NULL	NULL	NULL	#	55.56	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` = 1) or (`test`.`t1`.`a` = 2))
create table t2 (
a int not null,
b int not null
) partition by key(a,b) partitions 2;
insert into t2 values (1,1),(2,2),(3,3);
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
explain select * from t2 where a=1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`a` = 1)
explain select * from t2 where b=1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` = 1)
explain select * from t2 where a=1 and b=1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`b` = 1) and (`test`.`t2`.`a` = 1))
create table t3 (
a int
)
partition by range (a*1) (
partition p0 values less than (10),
partition p1 values less than (20)
);
insert into t3 values (5),(15);
ANALYZE TABLE t3;
Table	Op	Msg_type	Msg_text
test.t3	analyze	status	OK
explain select * from t3 where a=11;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where (`test`.`t3`.`a` = 11)
explain select * from t3 where a=10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where (`test`.`t3`.`a` = 10)
explain select * from t3 where a=20;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where (`test`.`t3`.`a` = 20)
explain select * from t3 where a=30;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where (`test`.`t3`.`a` = 30)
create table t4 (a int not null, b int not null) partition by LIST (a+b) (
partition p0 values in (12),
partition p1 values in (14)
);
insert into t4 values (10,2), (10,4);
ANALYZE TABLE t4;
Table	Op	Msg_type	Msg_text
test.t4	analyze	status	OK
explain select * from t4 where (a=10 and b=1) or (a=10 and b=2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t4	p0	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b` from `test`.`t4` where (((`test`.`t4`.`b` = 1) and (`test`.`t4`.`a` = 10)) or ((`test`.`t4`.`b` = 2) and (`test`.`t4`.`a` = 10)))
explain select * from t4
where (a=10 and b=1) or (a=10 and b=2) or (a=10 and b = 3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t4	p0	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b` from `test`.`t4` where (((`test`.`t4`.`b` = 1) and (`test`.`t4`.`a` = 10)) or ((`test`.`t4`.`b` = 2) and (`test`.`t4`.`a` = 10)) or ((`test`.`t4`.`b` = 3) and (`test`.`t4`.`a` = 10)))
explain select * from t4 where (a=10 and b=2) or (a=10 and b=3)
or (a=10 and b = 4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t4	p0,p1	ALL	NULL	NULL	NULL	NULL	#	57.81	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b` from `test`.`t4` where (((`test`.`t4`.`b` = 2) and (`test`.`t4`.`a` = 10)) or ((`test`.`t4`.`b` = 3) and (`test`.`t4`.`a` = 10)) or ((`test`.`t4`.`b` = 4) and (`test`.`t4`.`a` = 10)))
explain select * from t4 where (a=10 and b=1) or a=11;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t4	p0,p1	ALL	NULL	NULL	NULL	NULL	#	62.50	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b` from `test`.`t4` where (((`test`.`t4`.`b` = 1) and (`test`.`t4`.`a` = 10)) or (`test`.`t4`.`a` = 11))
explain select * from t4 where (a=10 and b=2) or a=11;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t4	p0,p1	ALL	NULL	NULL	NULL	NULL	#	62.50	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b` from `test`.`t4` where (((`test`.`t4`.`b` = 2) and (`test`.`t4`.`a` = 10)) or (`test`.`t4`.`a` = 11))
drop table t1, t2, t3, t4;
create table t5 (a int not null, b int not null,
c int not null, d int not null)
partition by LIST(a+b) subpartition by HASH (c+d) subpartitions 2
(
partition p0 values in (12),
partition p1 values in (14)
);
insert into t5 values (10,2,0,0), (10,4,0,0), (10,2,0,1), (10,4,0,1);
ANALYZE TABLE t5;
Table	Op	Msg_type	Msg_text
test.t5	analyze	status	OK
explain select * from t5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t5	p0_p0sp0,p0_p0sp1,p1_p1sp0,p1_p1sp1	ALL	NULL	NULL	NULL	NULL	#	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`a` AS `a`,`test`.`t5`.`b` AS `b`,`test`.`t5`.`c` AS `c`,`test`.`t5`.`d` AS `d` from `test`.`t5`
explain select * from t5
where (a=10 and b=1) or (a=10 and b=2) or (a=10 and b = 3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t5	p0_p0sp0,p0_p0sp1	ALL	NULL	NULL	NULL	NULL	#	57.81	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`a` AS `a`,`test`.`t5`.`b` AS `b`,`test`.`t5`.`c` AS `c`,`test`.`t5`.`d` AS `d` from `test`.`t5` where (((`test`.`t5`.`b` = 1) and (`test`.`t5`.`a` = 10)) or ((`test`.`t5`.`b` = 2) and (`test`.`t5`.`a` = 10)) or ((`test`.`t5`.`b` = 3) and (`test`.`t5`.`a` = 10)))
explain select * from t5 where (a=10 and b=2) or (a=10 and b=3)
or (a=10 and b = 4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t5	p0_p0sp0,p0_p0sp1,p1_p1sp0,p1_p1sp1	ALL	NULL	NULL	NULL	NULL	#	25.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`a` AS `a`,`test`.`t5`.`b` AS `b`,`test`.`t5`.`c` AS `c`,`test`.`t5`.`d` AS `d` from `test`.`t5` where (((`test`.`t5`.`b` = 2) and (`test`.`t5`.`a` = 10)) or ((`test`.`t5`.`b` = 3) and (`test`.`t5`.`a` = 10)) or ((`test`.`t5`.`b` = 4) and (`test`.`t5`.`a` = 10)))
explain select * from t5 where (c=1 and d=1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t5	p0_p0sp0,p1_p1sp0	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`a` AS `a`,`test`.`t5`.`b` AS `b`,`test`.`t5`.`c` AS `c`,`test`.`t5`.`d` AS `d` from `test`.`t5` where ((`test`.`t5`.`d` = 1) and (`test`.`t5`.`c` = 1))
explain select * from t5 where (c=2 and d=1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t5	p0_p0sp1,p1_p1sp1	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`a` AS `a`,`test`.`t5`.`b` AS `b`,`test`.`t5`.`c` AS `c`,`test`.`t5`.`d` AS `d` from `test`.`t5` where ((`test`.`t5`.`d` = 1) and (`test`.`t5`.`c` = 2))
explain select * from t5 where (a=10 and b=2 and c=1 and d=1) or
(c=2 and d=1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t5	p0_p0sp0,p0_p0sp1,p1_p1sp1	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`a` AS `a`,`test`.`t5`.`b` AS `b`,`test`.`t5`.`c` AS `c`,`test`.`t5`.`d` AS `d` from `test`.`t5` where (((`test`.`t5`.`d` = 1) and (`test`.`t5`.`c` = 1) and (`test`.`t5`.`b` = 2) and (`test`.`t5`.`a` = 10)) or ((`test`.`t5`.`d` = 1) and (`test`.`t5`.`c` = 2)))
explain select * from t5 where (a=10 and b=2 and c=1 and d=1) or
(b=2 and c=2 and d=1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t5	p0_p0sp0,p0_p0sp1,p1_p1sp1	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`a` AS `a`,`test`.`t5`.`b` AS `b`,`test`.`t5`.`c` AS `c`,`test`.`t5`.`d` AS `d` from `test`.`t5` where (((`test`.`t5`.`d` = 1) and (`test`.`t5`.`c` = 1) and (`test`.`t5`.`b` = 2) and (`test`.`t5`.`a` = 10)) or ((`test`.`t5`.`d` = 1) and (`test`.`t5`.`c` = 2) and (`test`.`t5`.`b` = 2)))
create table t6 (a int not null) partition by LIST(a) (
partition p1 values in (1),
partition p3 values in (3),
partition p5 values in (5),
partition p7 values in (7),
partition p9 values in (9)
);
insert into t6 values (1),(3),(5);
ANALYZE TABLE t6;
Table	Op	Msg_type	Msg_text
test.t6	analyze	status	OK
explain select * from t6 where a <  1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where (`test`.`t6`.`a` < 1)
explain select * from t6 where a <= 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where (`test`.`t6`.`a` <= 1)
explain select * from t6 where a >  9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where (`test`.`t6`.`a` > 9)
explain select * from t6 where a >= 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p9	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where (`test`.`t6`.`a` >= 9)
explain select * from t6 where a > 0 and a < 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p1,p3	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where ((`test`.`t6`.`a` > 0) and (`test`.`t6`.`a` < 5))
explain select * from t6 where a > 5 and a < 12;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p7,p9	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where ((`test`.`t6`.`a` > 5) and (`test`.`t6`.`a` < 12))
explain select * from t6 where a > 3 and a < 8 ;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p5,p7	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where ((`test`.`t6`.`a` > 3) and (`test`.`t6`.`a` < 8))
explain select * from t6 where a >= 0 and a <= 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p1,p3,p5	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where ((`test`.`t6`.`a` >= 0) and (`test`.`t6`.`a` <= 5))
explain select * from t6 where a >= 5 and a <= 12;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p5,p7,p9	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where ((`test`.`t6`.`a` >= 5) and (`test`.`t6`.`a` <= 12))
explain select * from t6 where a >= 3 and a <= 8;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p3,p5,p7	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where ((`test`.`t6`.`a` >= 3) and (`test`.`t6`.`a` <= 8))
explain select * from t6 where a > 3 and a < 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where ((`test`.`t6`.`a` > 3) and (`test`.`t6`.`a` < 5))
drop table t6;
create table t6 (a int unsigned not null) partition by LIST(a) (
partition p1 values in (1),
partition p3 values in (3),
partition p5 values in (5),
partition p7 values in (7),
partition p9 values in (9)
);
insert into t6 values (1),(3),(5);
ANALYZE TABLE t6;
Table	Op	Msg_type	Msg_text
test.t6	analyze	status	OK
explain select * from t6 where a <  1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where (`test`.`t6`.`a` < 1)
explain select * from t6 where a <= 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where (`test`.`t6`.`a` <= 1)
explain select * from t6 where a >  9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where (`test`.`t6`.`a` > 9)
explain select * from t6 where a >= 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p9	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where (`test`.`t6`.`a` >= 9)
explain select * from t6 where a > 0 and a < 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p1,p3	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where ((`test`.`t6`.`a` > 0) and (`test`.`t6`.`a` < 5))
explain select * from t6 where a > 5 and a < 12;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p7,p9	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where ((`test`.`t6`.`a` > 5) and (`test`.`t6`.`a` < 12))
explain select * from t6 where a > 3 and a < 8 ;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p5,p7	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where ((`test`.`t6`.`a` > 3) and (`test`.`t6`.`a` < 8))
explain select * from t6 where a >= 0 and a <= 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p1,p3,p5	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where (`test`.`t6`.`a` <= 5)
explain select * from t6 where a >= 5 and a <= 12;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p5,p7,p9	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where ((`test`.`t6`.`a` >= 5) and (`test`.`t6`.`a` <= 12))
explain select * from t6 where a >= 3 and a <= 8;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	p3,p5,p7	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where ((`test`.`t6`.`a` >= 3) and (`test`.`t6`.`a` <= 8))
explain select * from t6 where a > 3 and a < 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t6	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`a` AS `a` from `test`.`t6` where ((`test`.`t6`.`a` > 3) and (`test`.`t6`.`a` < 5))
create table t7 (a int not null) partition by RANGE(a) (
partition p10 values less than (10),
partition p30 values less than (30),
partition p50 values less than (50),
partition p70 values less than (70),
partition p90 values less than (90)
);
insert into t7 values (10),(30),(50);
ANALYZE TABLE t7;
Table	Op	Msg_type	Msg_text
test.t7	analyze	status	OK
explain select * from t7 where a < 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` < 5)
explain select * from t7 where a < 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` < 9)
explain select * from t7 where a <= 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` <= 9)
explain select * from t7 where a = 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` = 9)
explain select * from t7 where a >= 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10,p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` >= 9)
explain select * from t7 where a > 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` > 9)
explain select * from t7 where a < 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` < 10)
explain select * from t7 where a <= 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10,p30	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` <= 10)
explain select * from t7 where a = 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p30	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` = 10)
explain select * from t7 where a >= 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` >= 10)
explain select * from t7 where a > 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` > 10)
explain select * from t7 where a < 89;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10,p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` < 89)
explain select * from t7 where a <= 89;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10,p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` <= 89)
explain select * from t7 where a = 89;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p90	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` = 89)
explain select * from t7 where a > 89;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` > 89)
explain select * from t7 where a >= 89;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p90	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` >= 89)
explain select * from t7 where a < 90;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10,p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` < 90)
explain select * from t7 where a <= 90;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10,p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` <= 90)
explain select * from t7 where a = 90;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` = 90)
explain select * from t7 where a > 90;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` > 90)
explain select * from t7 where a >= 90;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` >= 90)
explain select * from t7 where a > 91;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` > 91)
explain select * from t7 where a > 11 and a < 29;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p30	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where ((`test`.`t7`.`a` > 11) and (`test`.`t7`.`a` < 29))
drop table t7;
create table t7 (a int unsigned not null) partition by RANGE(a) (
partition p10 values less than (10),
partition p30 values less than (30),
partition p50 values less than (50),
partition p70 values less than (70),
partition p90 values less than (90)
);
insert into t7 values (10),(30),(50);
ANALYZE TABLE t7;
Table	Op	Msg_type	Msg_text
test.t7	analyze	status	OK
explain select * from t7 where a < 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` < 5)
explain select * from t7 where a < 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` < 9)
explain select * from t7 where a <= 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` <= 9)
explain select * from t7 where a = 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` = 9)
explain select * from t7 where a >= 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10,p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` >= 9)
explain select * from t7 where a > 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` > 9)
explain select * from t7 where a < 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` < 10)
explain select * from t7 where a <= 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10,p30	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` <= 10)
explain select * from t7 where a = 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p30	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` = 10)
explain select * from t7 where a >= 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` >= 10)
explain select * from t7 where a > 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` > 10)
explain select * from t7 where a < 89;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10,p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` < 89)
explain select * from t7 where a <= 89;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10,p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` <= 89)
explain select * from t7 where a = 89;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p90	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` = 89)
explain select * from t7 where a > 89;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` > 89)
explain select * from t7 where a >= 89;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p90	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` >= 89)
explain select * from t7 where a < 90;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10,p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` < 90)
explain select * from t7 where a <= 90;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p10,p30,p50,p70,p90	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` <= 90)
explain select * from t7 where a = 90;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` = 90)
explain select * from t7 where a > 90;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` > 90)
explain select * from t7 where a >= 90;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` >= 90)
explain select * from t7 where a > 91;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where (`test`.`t7`.`a` > 91)
explain select * from t7 where a > 11 and a < 29;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	p30	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` where ((`test`.`t7`.`a` > 11) and (`test`.`t7`.`a` < 29))
create table t8 (a date not null) partition by RANGE(YEAR(a)) (
partition p0 values less than (1980),
partition p1 values less than (1990),
partition p2 values less than (2000)
);
insert into t8 values ('1985-05-05'),('1995-05-05');
ANALYZE TABLE t8;
Table	Op	Msg_type	Msg_text
test.t8	analyze	status	OK
explain select * from t8 where a < '1980-02-02';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t8	p0,p1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t8`.`a` AS `a` from `test`.`t8` where (`test`.`t8`.`a` < DATE'1980-02-02')
create table t9 (a date not null) partition by RANGE(TO_DAYS(a)) (
partition p0 values less than (732299), -- 2004-12-19
partition p1 values less than (732468), -- 2005-06-06
partition p2 values less than (732664)  -- 2005-12-19
);
insert into t9 values ('2005-05-05'), ('2005-04-04');
ANALYZE TABLE t9;
Table	Op	Msg_type	Msg_text
test.t9	analyze	status	OK
explain select * from t9 where a <  '2004-12-19';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t9	p0	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t9`.`a` AS `a` from `test`.`t9` where (`test`.`t9`.`a` < DATE'2004-12-19')
explain select * from t9 where a <= '2004-12-19';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t9	p0,p1	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t9`.`a` AS `a` from `test`.`t9` where (`test`.`t9`.`a` <= DATE'2004-12-19')
drop table t5,t6,t7,t8,t9;
create table t1 (
a1 int not null
)
partition by range (a1) (
partition p0 values less than (3),
partition p1 values less than (6),
partition p2 values less than (9)
);
insert into t1 values (1),(2),(3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where a1 > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a1` AS `a1` from `test`.`t1` where (`test`.`t1`.`a1` > 3)
explain select * from t1 where a1 >= 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a1` AS `a1` from `test`.`t1` where (`test`.`t1`.`a1` >= 3)
explain select * from t1 where a1 < 3 and a1 > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a1` AS `a1` from `test`.`t1` where ((`test`.`t1`.`a1` < 3) and (`test`.`t1`.`a1` > 3))
drop table t1;
create table t3 (a int, b int)
partition by list(a) subpartition by hash(b) subpartitions 4 (
partition p0 values in (1),
partition p1 values in (2),
partition p2 values in (3),
partition p3 values in (4)
);
insert into t3 values (1,1),(2,2),(3,3);
ANALYZE TABLE t3;
Table	Op	Msg_type	Msg_text
test.t3	analyze	status	OK
explain select * from t3 where a=2 or b=1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p0_p0sp1,p1_p1sp0,p1_p1sp1,p1_p1sp2,p1_p1sp3,p2_p2sp1,p3_p3sp1	ALL	NULL	NULL	NULL	NULL	#	75.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b` from `test`.`t3` where ((`test`.`t3`.`a` = 2) or (`test`.`t3`.`b` = 1))
explain select * from t3 where a=4 or b=2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p0_p0sp2,p1_p1sp2,p2_p2sp2,p3_p3sp0,p3_p3sp1,p3_p3sp2,p3_p3sp3	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b` from `test`.`t3` where ((`test`.`t3`.`a` = 4) or (`test`.`t3`.`b` = 2))
explain select * from t3 where (a=2 or b=1) and (a=4 or b=2) ;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p1_p1sp2,p3_p3sp1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b` from `test`.`t3` where (((`test`.`t3`.`a` = 2) or (`test`.`t3`.`b` = 1)) and ((`test`.`t3`.`a` = 4) or (`test`.`t3`.`b` = 2)))
drop table t3;
create table t1 (a int) partition by hash(a) partitions 2;
insert into t1 values (1),(2);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where a is null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` is null)
explain select * from t1 where a is not null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` is not null)
drop table t1;
create table t1 (a int not null, b int not null, key(a), key(b))
partition by hash(a) partitions 4;
insert into t1 values (1,1),(2,2),(3,3),(4,4);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain
select * from t1 x, t1 y
where x.b = y.b and (x.a=1 or x.a=2) and (y.a=2 or y.a=3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p1,p2	range	a,b	a	4	NULL	#	100.00	Using index condition
1	SIMPLE	y	p2,p3	ref	a,b	b	4	test.x.b	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`b` = `test`.`x`.`b`) and ((`test`.`x`.`a` = 1) or (`test`.`x`.`a` = 2)) and ((`test`.`y`.`a` = 2) or (`test`.`y`.`a` = 3)))
explain
select * from t1 x, t1 y where x.a = y.a and (x.a=1 or x.a=2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p1,p2	range	a	a	4	NULL	#	100.00	Using index condition
1	SIMPLE	y	p1,p2	ref	a	a	4	test.x.a	#	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`a` = `test`.`x`.`a`) and ((`test`.`x`.`a` = 1) or (`test`.`x`.`a` = 2)))
drop table t1;
create table t1 (a int) partition by hash(a) partitions 20;
insert into t1 values (1),(2),(3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where a >  1 and a < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` > 1) and (`test`.`t1`.`a` < 3))
explain select * from t1 where a >= 1 and a < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` >= 1) and (`test`.`t1`.`a` < 3))
explain select * from t1 where a >  1 and a <= 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2,p3	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` > 1) and (`test`.`t1`.`a` <= 3))
explain select * from t1 where a >= 1 and a <= 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2,p3	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` >= 1) and (`test`.`t1`.`a` <= 3))
drop table t1;
create table t1 (a int, b int)
partition by list(a) subpartition by hash(b) subpartitions 20
(
partition p0 values in (0),
partition p1 values in (1),
partition p2 values in (2),
partition p3 values in (3)
);
insert into t1 values (1,1),(2,2),(3,3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where b >  1 and b < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0_p0sp2,p1_p1sp2,p2_p2sp2,p3_p3sp2	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where ((`test`.`t1`.`b` > 1) and (`test`.`t1`.`b` < 3))
explain select * from t1 where b >  1 and b < 3 and (a =1 or a =2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1_p1sp2,p2_p2sp2	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where ((`test`.`t1`.`b` > 1) and (`test`.`t1`.`b` < 3) and ((`test`.`t1`.`a` = 1) or (`test`.`t1`.`a` = 2)))
drop table t1;
create table t1 (a int) partition by list(a) (
partition p0 values in (1,2),
partition p1 values in (3,4)
);
insert into t1 values (1),(1),(2),(2),(3),(4),(3),(4);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
# This won't do any table access
explain update t1 set a=100 where a=5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	No matching rows after partition pruning
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 100 where (`test`.`t1`.`a` = 5)
explain update t1 set a=100 where a=5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	No matching rows after partition pruning
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 100 where (`test`.`t1`.`a` = 5)
flush status;
update t1 set a=100 where a=5;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	0
show status like 'Handler_external_lock';
Variable_name	Value
Handler_external_lock	0
# ... as compared to this, which will scan both partitions
explain update t1 set a=100 where a+1=5+1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	p0,p1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where; Using temporary
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 100 where ((`test`.`t1`.`a` + 1) = (5 + 1))
explain update t1 set a=100 where a+1=5+1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	p0,p1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where; Using temporary
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 100 where ((`test`.`t1`.`a` + 1) = (5 + 1))
flush status;
update t1 set a=100 where a+1=5+1;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	8
show status like 'Handler_external_lock';
Variable_name	Value
Handler_external_lock	2
# This will only scan partition p1
explain update t1 set a=3 where a=4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	p1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 3 where (`test`.`t1`.`a` = 4)
explain update t1 set a=3 where a=4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	p1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 3 where (`test`.`t1`.`a` = 4)
flush status;
update t1 set a=3 where a=4;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	4
show status like 'Handler_external_lock';
Variable_name	Value
Handler_external_lock	2
# This will only scan partition p1 but with a LIMIT
explain update t1 set a=4 where a=3 LIMIT 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	p1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 4 where (`test`.`t1`.`a` = 3) limit 2
explain update t1 set a=4 where a=3 LIMIT 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	p1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 4 where (`test`.`t1`.`a` = 3) limit 2
flush status;
update t1 set a=4 where a=3 LIMIT 2;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	1
show status like 'Handler_external_lock';
Variable_name	Value
Handler_external_lock	2
select * from t1;
a
1
1
2
2
3
3
4
4
# Same as above for DELETE:
explain delete from t1 where a=5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	No matching rows after partition pruning
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` = 5)
explain delete from t1 where a=5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	No matching rows after partition pruning
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` = 5)
flush status;
delete from t1 where a=5;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	0
show status like 'Handler_external_lock';
Variable_name	Value
Handler_external_lock	0
explain delete from t1 where a+1=5+1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	p0,p1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t1` where ((`test`.`t1`.`a` + 1) = (5 + 1))
explain delete from t1 where a+1=5+1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	p0,p1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t1` where ((`test`.`t1`.`a` + 1) = (5 + 1))
flush status;
delete from t1 where a+1=5+1;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	8
show status like 'Handler_external_lock';
Variable_name	Value
Handler_external_lock	2
# Will only delete from p0
explain delete from t1 where a=1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	p0	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` = 1)
explain delete from t1 where a=1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	p0	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` = 1)
flush status;
delete from t1 where a=1;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	4
show status like 'Handler_external_lock';
Variable_name	Value
Handler_external_lock	2
select * from t1;
a
2
2
3
3
4
4
insert into t1 values (1), (1);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
# Same as above multi-table UPDATE/DELETE
create table t2 like t1;
insert into t2 select * from t1;
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
explain update t1,t2 set t1.a=1000, t2.a=1000 where t1.a=5 and t2.a=5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	No matching rows after partition pruning
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`a` = 1000,`test`.`t2`.`a` = 1000 where ((`test`.`t1`.`a` = 5) and (`test`.`t2`.`a` = 5))
explain update t1,t2 set t1.a=1000, t2.a=1000 where t1.a=5 and t2.a=5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	No matching rows after partition pruning
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`a` = 1000,`test`.`t2`.`a` = 1000 where ((`test`.`t1`.`a` = 5) and (`test`.`t2`.`a` = 5))
flush status;
update t1,t2 set t1.a=1000, t2.a=1000 where t1.a=5 and t2.a=5;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	0
show status like 'Handler_external_lock';
Variable_name	Value
Handler_external_lock	0
explain delete t1,t2 from t1, t2 where t1.a=5 and t2.a=5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	No matching rows after partition pruning
Warnings:
Note	1003	delete `test`.`t1`, `test`.`t2` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`a` = 5) and (`test`.`t2`.`a` = 5))
explain delete t1,t2 from t1, t2 where t1.a=5 and t2.a=5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	No matching rows after partition pruning
Warnings:
Note	1003	delete `test`.`t1`, `test`.`t2` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`a` = 5) and (`test`.`t2`.`a` = 5))
flush status;
delete t1,t2 from t1, t2 where t1.a=5 and t2.a=5;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	0
show status like 'Handler_external_lock';
Variable_name	Value
Handler_external_lock	0
explain delete t1,t2 from t1, t2 where t1.a=t2.a and t2.a=5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	No matching rows after partition pruning
Warnings:
Note	1003	delete `test`.`t1`, `test`.`t2` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`a` = `test`.`t2`.`a`) and (`test`.`t2`.`a` = 5))
explain delete t1,t2 from t1, t2 where t1.a=t2.a and t2.a=5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	No matching rows after partition pruning
Warnings:
Note	1003	delete `test`.`t1`, `test`.`t2` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`a` = `test`.`t2`.`a`) and (`test`.`t2`.`a` = 5))
flush status;
delete t1,t2 from t1, t2 where t1.a=t2.a and t2.a=5;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	0
show status like 'Handler_external_lock';
Variable_name	Value
Handler_external_lock	0
explain delete t1,t2 from t1, t2 where t1.a=t2.a and t2.a=3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	p1	ALL	NULL	NULL	NULL	NULL	#	25.00	Using where
1	DELETE	t2	p1	ALL	NULL	NULL	NULL	NULL	#	25.00	Using where
Warnings:
Note	1003	delete `test`.`t1`, `test`.`t2` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`a` = 3) and (`test`.`t2`.`a` = 3))
explain delete t1,t2 from t1, t2 where t1.a=t2.a and t2.a=3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	p1	ALL	NULL	NULL	NULL	NULL	#	25.00	Using where
1	DELETE	t2	p1	ALL	NULL	NULL	NULL	NULL	#	25.00	Using where
Warnings:
Note	1003	delete `test`.`t1`, `test`.`t2` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`a` = 3) and (`test`.`t2`.`a` = 3))
flush status;
delete t1,t2 from t1, t2 where t1.a=t2.a and t2.a=3;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	12
show status like 'Handler_external_lock';
Variable_name	Value
Handler_external_lock	4
select * from t1;
a
1
1
2
2
4
4
drop table t1,t2;
CREATE TABLE `t1` (
`a` int(11) default NULL
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE `t2` (
`a` int(11) default NULL,
KEY `a` (`a`)
) ;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t2 select A.a + 10*(B.a + 10* C.a) from t1 A, t1 B, t1 C ;
insert into t1 select a from t2;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
drop table t2;
CREATE TABLE `t2` (
`a` int(11) default NULL,
`b` int(11) default NULL
)
PARTITION BY RANGE (a) (
PARTITION p0 VALUES LESS THAN (200),
PARTITION p1 VALUES LESS THAN (400),
PARTITION p2 VALUES LESS THAN (600),
PARTITION p3 VALUES LESS THAN (800),
PARTITION p4 VALUES LESS THAN (1001));
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE `t3` (
`a` int(11) default NULL,
`b` int(11) default NULL
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
explain insert into t2 select a,1 from t1 where a < 200;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t2	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	#	NULL	NULL
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	insert into `test`.`t2` /* select#1 */ select `test`.`t1`.`a` AS `a`,1 AS `1` from `test`.`t1` where (`test`.`t1`.`a` < 200)
insert into t2 select a,1 from t1 where a < 200;
insert into t2 select a,2 from t1 where a >= 200 and a < 400;
insert into t2 select a,3 from t1 where a >= 400 and a < 600;
insert into t2 select a,4 from t1 where a >= 600 and a < 800;
insert into t2 select a,5 from t1 where a >= 800 and a < 1001;
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
explain select * from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	#	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2`
explain insert into t3 select * from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t3	NULL	ALL	NULL	NULL	NULL	NULL	#	NULL	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	#	100.00	NULL
Warnings:
Note	1003	insert into `test`.`t3` /* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2`
explain   insert into t3 select * from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t3	NULL	ALL	NULL	NULL	NULL	NULL	#	NULL	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	#	100.00	NULL
Warnings:
Note	1003	insert into `test`.`t3` /* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2`
explain select * from t2 where a < 801 and a > 200;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	#	11.11	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`a` < 801) and (`test`.`t2`.`a` > 200))
explain insert into t3 select * from t2 where a < 801 and a > 200;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t3	NULL	ALL	NULL	NULL	NULL	NULL	#	NULL	NULL
1	SIMPLE	t2	p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	#	11.11	Using where
Warnings:
Note	1003	insert into `test`.`t3` /* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`a` < 801) and (`test`.`t2`.`a` > 200))
explain select * from t2 where a < 801 and a > 800;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p4	ALL	NULL	NULL	NULL	NULL	#	11.11	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`a` < 801) and (`test`.`t2`.`a` > 800))
explain insert into t3 select * from t2 where a < 801 and a > 800;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t3	NULL	ALL	NULL	NULL	NULL	NULL	#	NULL	NULL
1	SIMPLE	t2	p4	ALL	NULL	NULL	NULL	NULL	#	11.11	Using where
Warnings:
Note	1003	insert into `test`.`t3` /* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`a` < 801) and (`test`.`t2`.`a` > 800))
explain select * from t2 where a > 600;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p3,p4	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`a` > 600)
explain insert into t3 select * from t2 where a > 600;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t3	NULL	ALL	NULL	NULL	NULL	NULL	#	NULL	NULL
1	SIMPLE	t2	p3,p4	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	insert into `test`.`t3` /* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`a` > 600)
explain select * from t2 where a > 600 and b = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p3,p4	ALL	NULL	NULL	NULL	NULL	#	3.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`b` = 1) and (`test`.`t2`.`a` > 600))
explain insert into t3 select * from t2 where a > 600 and b = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t3	NULL	ALL	NULL	NULL	NULL	NULL	#	NULL	NULL
1	SIMPLE	t2	p3,p4	ALL	NULL	NULL	NULL	NULL	#	3.33	Using where
Warnings:
Note	1003	insert into `test`.`t3` /* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`b` = 1) and (`test`.`t2`.`a` > 600))
explain select * from t2 where a > 600 and b = 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p3,p4	ALL	NULL	NULL	NULL	NULL	#	3.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`b` = 4) and (`test`.`t2`.`a` > 600))
explain insert into t3 select * from t2 where a > 600 and b = 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t3	NULL	ALL	NULL	NULL	NULL	NULL	#	NULL	NULL
1	SIMPLE	t2	p3,p4	ALL	NULL	NULL	NULL	NULL	#	3.33	Using where
Warnings:
Note	1003	insert into `test`.`t3` /* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`b` = 4) and (`test`.`t2`.`a` > 600))
explain select * from t2 where a > 600 and b = 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p3,p4	ALL	NULL	NULL	NULL	NULL	#	3.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`b` = 5) and (`test`.`t2`.`a` > 600))
explain insert into t3 select * from t2 where a > 600 and b = 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t3	NULL	ALL	NULL	NULL	NULL	NULL	#	NULL	NULL
1	SIMPLE	t2	p3,p4	ALL	NULL	NULL	NULL	NULL	#	3.33	Using where
Warnings:
Note	1003	insert into `test`.`t3` /* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`b` = 5) and (`test`.`t2`.`a` > 600))
explain select * from t2 where b = 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	#	10.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` = 5)
explain insert into t3 select * from t2 where b = 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t3	NULL	ALL	NULL	NULL	NULL	NULL	#	NULL	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	#	10.00	Using where
Warnings:
Note	1003	insert into `test`.`t3` /* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` = 5)
DROP TABLE t3;
explain update t2 set b = 100 where b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`b` = 100 where (`test`.`t2`.`b` = 6)
explain update t2 set b = 100 where b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`b` = 100 where (`test`.`t2`.`b` = 6)
flush status;
update t2 set b = 100 where b = 6;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	1010
explain update t2 set a = 1002 where a = 1001;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	No matching rows after partition pruning
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`a` = 1002 where (`test`.`t2`.`a` = 1001)
flush status;
update t2 set a = 1002 where a = 1001;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	0
explain update t2 set b = 6 where a = 600;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	p3	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`b` = 6 where (`test`.`t2`.`a` = 600)
flush status;
update t2 set b = 6 where a = 600;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	200
explain update t2 set b = 6 where a > 600 and a < 800;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	p3	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`b` = 6 where ((`test`.`t2`.`a` > 600) and (`test`.`t2`.`a` < 800))
flush status;
update t2 set b = 6 where a > 600 and a < 800;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	200
explain delete from t2 where a > 600;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	p3,p4	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`a` > 600)
explain delete from t2 where a > 600;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	p3,p4	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`a` > 600)
flush status;
delete from t2 where a > 600;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	400
drop table t2;
CREATE TABLE `t2` (
`a` int(11) default NULL,
`b` int(11) default NULL,
index (b)
)
PARTITION BY RANGE (a) (
PARTITION p0 VALUES LESS THAN (200),
PARTITION p1 VALUES LESS THAN (400),
PARTITION p2 VALUES LESS THAN (600),
PARTITION p3 VALUES LESS THAN (800),
PARTITION p4 VALUES LESS THAN (1001));
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t2 select a,1 from t1 where a < 100;
insert into t2 select a,2 from t1 where a >= 200 and a < 300;
insert into t2 select a,3 from t1 where a >= 300 and a < 400;
insert into t2 select a,4 from t1 where a >= 400 and a < 500;
insert into t2 select a,5 from t1 where a >= 500 and a < 600;
insert into t2 select a,6 from t1 where a >= 600 and a < 700;
insert into t2 select a,7 from t1 where a >= 700 and a < 800;
insert into t2 select a,8 from t1 where a >= 800 and a < 900;
insert into t2 select a,9 from t1 where a >= 900 and a < 1001;
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
explain select * from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	#	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2`
explain select * from t2 where a = 101;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0	ALL	NULL	NULL	NULL	NULL	#	10.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`a` = 101)
explain select * from t2 where a = 550;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p2	ALL	NULL	NULL	NULL	NULL	#	10.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`a` = 550)
explain select * from t2 where a = 833;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p4	ALL	NULL	NULL	NULL	NULL	#	10.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`a` = 833)
explain select * from t2 where (a = 100 OR a = 900);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p4	ALL	NULL	NULL	NULL	NULL	#	19.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`a` = 100) or (`test`.`t2`.`a` = 900))
explain select * from t2 where (a > 100 AND a < 600);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2	ALL	NULL	NULL	NULL	NULL	#	11.11	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`a` > 100) and (`test`.`t2`.`a` < 600))
explain select * from t2 where b = 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ref	b	b	5	const	#	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` = 4)
explain select * from t2 where b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ref	b	b	5	const	#	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` = 6)
explain select * from t2 where b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ref	b	b	5	const	#	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` = 6)
explain select * from t2 where b in (1,3,5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	34.07	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` in (1,3,5))
explain select * from t2 where b in (1,3,5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	34.07	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` in (1,3,5))
explain select * from t2 where b in (2,4,6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	32.97	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` in (2,4,6))
explain select * from t2 where b in (2,4,6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	32.97	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` in (2,4,6))
explain select * from t2 where b in (7,8,9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	32.97	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` in (7,8,9))
explain select * from t2 where b in (7,8,9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	32.97	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` in (7,8,9))
explain select * from t2 where b > 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	43.96	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` > 5)
explain select * from t2 where b > 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	43.96	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` > 5)
explain select * from t2 where b > 5 and b < 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	32.97	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`b` > 5) and (`test`.`t2`.`b` < 9))
explain select * from t2 where b > 5 and b < 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	32.97	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`b` > 5) and (`test`.`t2`.`b` < 9))
explain select * from t2 where b > 5 and b < 7;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	range	b	b	5	NULL	#	100.00	Using index condition
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`b` > 5) and (`test`.`t2`.`b` < 7))
explain select * from t2 where b > 5 and b < 7;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	range	b	b	5	NULL	#	100.00	Using index condition
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`b` > 5) and (`test`.`t2`.`b` < 7))
explain select * from t2 where b > 0 and b < 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	45.05	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`b` > 0) and (`test`.`t2`.`b` < 5))
explain select * from t2 where b > 0 and b < 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	45.05	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where ((`test`.`t2`.`b` > 0) and (`test`.`t2`.`b` < 5))
explain update t2 set a = 111 where b = 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	p0,p1,p2,p3,p4	range	b	b	5	const	#	100.00	Using where; Using temporary
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`a` = 111 where (`test`.`t2`.`b` = 10)
explain update t2 set a = 111 where b = 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	p0,p1,p2,p3,p4	range	b	b	5	const	#	100.00	Using where; Using temporary
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`a` = 111 where (`test`.`t2`.`b` = 10)
flush status;
update t2 set a = 111 where b = 10;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	0
show status like 'Handler_read_key';
Variable_name	Value
Handler_read_key	5
explain update t2 set a = 111 where b in (5,6,7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	100.00	Using where; Using temporary
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`a` = 111 where (`test`.`t2`.`b` in (5,6,7))
flush status;
update t2 set a = 111 where b in (5,6,7);
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	910
show status like 'Handler_read_key';
Variable_name	Value
Handler_read_key	305
explain update t2 set a = 222 where b = 7;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	p0,p1,p2,p3,p4	range	b	b	5	const	#	100.00	Using where; Using temporary
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`a` = 222 where (`test`.`t2`.`b` = 7)
flush status;
update t2 set a = 222 where b = 7;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	0
show status like 'Handler_read_key';
Variable_name	Value
Handler_read_key	105
explain delete from t2 where b = 8;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	p0,p1,p2,p3,p4	range	b	b	5	const	#	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`b` = 8)
explain delete from t2 where b = 8;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	p0,p1,p2,p3,p4	range	b	b	5	const	#	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`b` = 8)
flush status;
delete from t2 where b = 8;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	0
show status like 'Handler_read_key';
Variable_name	Value
Handler_read_key	5
explain delete from t2 where b > 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`b` > 5)
flush status;
delete from t2 where b > 5;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	810
show status like 'Handler_read_key';
Variable_name	Value
Handler_read_key	5
show status like 'Handler_read_prev';
Variable_name	Value
Handler_read_prev	0
show status like 'Handler_read_next';
Variable_name	Value
Handler_read_next	0
explain delete from t2 where b < 5 or b > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	p0,p1,p2,p3,p4	ALL	b	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t2` where ((`test`.`t2`.`b` < 5) or (`test`.`t2`.`b` > 3))
flush status;
delete from t2 where b < 5 or b > 3;
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	510
show status like 'Handler_read_key';
Variable_name	Value
Handler_read_key	5
show status like 'Handler_read_prev';
Variable_name	Value
Handler_read_prev	0
show status like 'Handler_read_next';
Variable_name	Value
Handler_read_next	0
drop table t1, t2;
create table t1 ( f_int1 mediumint, f_int2 integer)
partition by list(mod(f_int1,4)) (
partition p_3 values in (-3),
partition p_2 values in (-2),
partition p_1 values in (-1),
partition p0 values in (0),
partition p1 values in (1),
partition p2 values in (2),
partition p3 values in (3)
);
insert into t1 values (9, 9), (8, 8), (7, 7), (6, 6), (5, 5),
(4, 4), (3, 3), (2, 2), (1, 1);
select * from t1 where f_int1 between 5 and 15 order by f_int1;
f_int1	f_int2
5	5
6	6
7	7
8	8
9	9
drop table t1;
create table t1 (f_int1 integer) partition by list(abs(mod(f_int1,2)))
subpartition by hash(f_int1) subpartitions 2
(
partition part1 values in (0),
partition part2 values in (1),
partition part4 values in (null)
);
insert into t1 set f_int1 = null;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
select * from t1 where f_int1 is null;
f_int1
NULL
explain select * from t1 where f_int1 is null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	part4_part4sp0	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`f_int1` AS `f_int1` from `test`.`t1` where (`test`.`t1`.`f_int1` is null)
drop table t1;
create table t1 (a int not null, b int not null)
partition by list(a)
subpartition by hash(b) subpartitions 4
(
partition p0 values in (1),
partition p1 values in (2),
partition p2 values in (3)
);
insert into t1 values (1,1),(1,2),(1,3),(1,4),
(2,1),(2,2),(2,3),(2,4);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where a=1 AND (b=1 OR b=2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0_p0sp1,p0_p0sp2	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where ((`test`.`t1`.`a` = 1) and ((`test`.`t1`.`b` = 1) or (`test`.`t1`.`b` = 2)))
drop table t1;
create table t1 (a int, b int not null)
partition by list(a)
subpartition by hash(b) subpartitions 2
(
partition p0 values in (1),
partition p1 values in (2),
partition p2 values in (3),
partition pn values in (NULL)
);
insert into t1 values (1,1),(1,2),(1,3),(1,4),
(2,1),(2,2),(2,3),(2,4), (NULL,1);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where a IS NULL AND (b=1 OR b=2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pn_pnsp0,pn_pnsp1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where ((`test`.`t1`.`a` is null) and ((`test`.`t1`.`b` = 1) or (`test`.`t1`.`b` = 2)))
explain select * from t1 where (a IS NULL or a < 1) AND (b=1 OR b=2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pn_pnsp0,pn_pnsp1	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (((`test`.`t1`.`a` is null) or (`test`.`t1`.`a` < 1)) and ((`test`.`t1`.`b` = 1) or (`test`.`t1`.`b` = 2)))
explain select * from t1 where (a IS NULL or a < 2) AND (b=1 OR b=2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0_p0sp0,p0_p0sp1,pn_pnsp0,pn_pnsp1	ALL	NULL	NULL	NULL	NULL	#	20.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (((`test`.`t1`.`a` is null) or (`test`.`t1`.`a` < 2)) and ((`test`.`t1`.`b` = 1) or (`test`.`t1`.`b` = 2)))
explain select * from t1 where (a IS NULL or a <= 1) AND (b=1 OR b=2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0_p0sp0,p0_p0sp1,pn_pnsp0,pn_pnsp1	ALL	NULL	NULL	NULL	NULL	#	20.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (((`test`.`t1`.`a` is null) or (`test`.`t1`.`a` <= 1)) and ((`test`.`t1`.`b` = 1) or (`test`.`t1`.`b` = 2)))
drop table t1;
create table t1 ( a int)  partition by list (MOD(a, 10))
( partition p0  values in (0), partition p1 values in (1),
partition p2 values in (2), partition p3 values in (3),
partition p4 values in (4), partition p5 values in (5),
partition p6 values in (6), partition pn values in (NULL)
);
insert into t1 values (NULL), (0),(1),(2),(3),(4),(5),(6);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where a is null or a < 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,pn	ALL	NULL	NULL	NULL	NULL	#	41.66	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` is null) or (`test`.`t1`.`a` < 2))
drop table t1;
create table t1 (s1 int) partition by list (s1)
(partition p1 values in (0),
partition p2 values in (1),
partition p3 values in (null));
insert into t1 values (0),(1),(null);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
select count(*) from t1 where s1 < 0 or s1 is null;
count(*)
1
explain select count(*) from t1 where s1 < 0 or s1 is null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p3	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` where ((`test`.`t1`.`s1` < 0) or (`test`.`t1`.`s1` is null))
drop table t1;
create table t1 (a char(32) primary key)
partition by key()
partitions 100;
insert into t1 values ('na');
select * from t1;
a
na
select * from t1 where a like 'n%';
a
na
drop table t1;
create table t1 (s1 varchar(15)) partition by key (s1);
select * from t1 where s1 = 0 or s1 is null;
s1
insert into t1 values ('aa'),('bb'),('0');
analyze table t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where s1 = 0 or s1 is null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0	ALL	NULL	NULL	NULL	NULL	#	55.56	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`s1` AS `s1` from `test`.`t1` where ((`test`.`t1`.`s1` = 0) or (`test`.`t1`.`s1` is null))
drop table t1;
create table t2 (a int, b int)
partition by LIST(a)
subpartition by HASH(b) subpartitions 40
( partition p_0_long_partition_name values in(1),
partition p_1_long_partition_name values in(2));
insert into t2 values (1,1),(2,2);
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
explain select * from t2;
id	1
select_type	SIMPLE
table	t2
partitions	p_0_long_partition_name_p_0_long_partition_namesp0,p_0_long_partition_name_p_0_long_partition_namesp1,p_0_long_partition_name_p_0_long_partition_namesp2,p_0_long_partition_name_p_0_long_partition_namesp3,p_0_long_partition_name_p_0_long_partition_namesp4,p_0_long_partition_name_p_0_long_partition_namesp5,p_0_long_partition_name_p_0_long_partition_namesp6,p_0_long_partition_name_p_0_long_partition_namesp7,p_0_long_partition_name_p_0_long_partition_namesp8,p_0_long_partition_name_p_0_long_partition_namesp9,p_0_long_partition_name_p_0_long_partition_namesp10,p_0_long_partition_name_p_0_long_partition_namesp11,p_0_long_partition_name_p_0_long_partition_namesp12,p_0_long_partition_name_p_0_long_partition_namesp13,p_0_long_partition_name_p_0_long_partition_namesp14,p_0_long_partition_name_p_0_long_partition_namesp15,p_0_long_partition_name_p_0_long_partition_namesp16,p_0_long_partition_name_p_0_long_partition_namesp17,p_0_long_partition_name_p_0_long_partition_namesp18,p_0_long_partition_name_p_0_long_partition_namesp19,p_0_long_partition_name_p_0_long_partition_namesp20,p_0_long_partition_name_p_0_long_partition_namesp21,p_0_long_partition_name_p_0_long_partition_namesp22,p_0_long_partition_name_p_0_long_partition_namesp23,p_0_long_partition_name_p_0_long_partition_namesp24,p_0_long_partition_name_p_0_long_partition_namesp25,p_0_long_partition_name_p_0_long_partition_namesp26,p_0_long_partition_name_p_0_long_partition_namesp27,p_0_long_partition_name_p_0_long_partition_namesp28,p_0_long_partition_name_p_0_long_partition_namesp29,p_0_long_partition_name_p_0_long_partition_namesp30,p_0_long_partition_name_p_0_long_partition_namesp31,p_0_long_partition_name_p_0_long_partition_namesp32,p_0_long_partition_name_p_0_long_partition_namesp33,p_0_long_partition_name_p_0_long_partition_namesp34,p_0_long_partition_name_p_0_long_partition_namesp35,p_0_long_partition_name_p_0_long_partition_namesp36,p_0_long_partition_name_p_0_long_partition_namesp37,p_0_long_partition_name_p_0_long_partition_namesp38,p_0_long_partition_name_p_0_long_partition_namesp39,p_1_long_partition_name_p_1_long_partition_namesp0,p_1_long_partition_name_p_1_long_partition_namesp1,p_1_long_partition_name_p_1_long_partition_namesp2,p_1_long_partition_name_p_1_long_partition_namesp3,p_1_long_partition_name_p_1_long_partition_namesp4,p_1_long_partition_name_p_1_long_partition_namesp5,p_1_long_partition_name_p_1_long_partition_namesp6,p_1_long_partition_name_p_1_long_partition_namesp7,p_1_long_partition_name_p_1_long_partition_namesp8,p_1_long_partition_name_p_1_long_partition_namesp9,p_1_long_partition_name_p_1_long_partition_namesp10,p_1_long_partition_name_p_1_long_partition_namesp11,p_1_long_partition_name_p_1_long_partition_namesp12,p_1_long_partition_name_p_1_long_partition_namesp13,p_1_long_partition_name_p_1_long_partition_namesp14,p_1_long_partition_name_p_1_long_partition_namesp15,p_1_long_partition_name_p_1_long_partition_namesp16,p_1_long_partition_name_p_1_long_partition_namesp17,p_1_long_partition_name_p_1_long_partition_namesp18,p_1_long_partition_name_p_1_long_partition_namesp19,p_1_long_partition_name_p_1_long_partition_namesp20,p_1_long_partition_name_p_1_long_partition_namesp21,p_1_long_partition_name_p_1_long_partition_namesp22,p_1_long_partition_name_p_1_long_partition_namesp23,p_1_long_partition_name_p_1_long_partition_namesp24,p_1_long_partition_name_p_1_long_partition_namesp25,p_1_long_partition_name_p_1_long_partition_namesp26,p_1_long_partition_name_p_1_long_partition_namesp27,p_1_long_partition_name_p_1_long_partition_namesp28,p_1_long_partition_name_p_1_long_partition_namesp29,p_1_long_partition_name_p_1_long_partition_namesp30,p_1_long_partition_name_p_1_long_partition_namesp31,p_1_long_partition_name_p_1_long_partition_namesp32,p_1_long_partition_name_p_1_long_partition_namesp33,p_1_long_partition_name_p_1_long_partition_namesp34,p_1_long_partition_name_p_1_long_partition_namesp35,p_1_long_partition_name_p_1_long_partition_namesp36,p_1_long_partition_name_p_1_long_partition_namesp37,p_1_long_partition_name_p_1_long_partition_namesp38,p_1_long_partition_name_p_1_long_partition_namesp39
type	ALL
possible_keys	NULL
key	NULL
key_len	NULL
ref	NULL
rows	#
filtered	100.00
Extra	NULL
Warnings:
Level	Note
Code	1003
Message	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2`
drop table t2;
create table t1 (s1 int);
explain select 1 from t1 union all select 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	NULL
2	UNION	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	No tables used
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` union all /* select#2 */ select 2 AS `2`
drop table t1;
create table t1 (a bigint unsigned not null) partition by range(a) (
partition p0 values less than (10),
partition p1 values less than (100),
partition p2 values less than (1000),
partition p3 values less than (18446744073709551000),
partition p4 values less than (18446744073709551614)
);
insert into t1 values (5),(15),(105),(1005);
insert into t1 values (18446744073709551000+1);
insert into t1 values (18446744073709551614-1);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where a < 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 10)
explain select * from t1
where a >= 18446744073709551000-1 and a <= 18446744073709551000+1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p3,p4	ALL	NULL	NULL	NULL	NULL	#	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` >= 18446744073709550999) and (`test`.`t1`.`a` <= 18446744073709551001))
explain select * from t1
where a between 18446744073709551001 and 18446744073709551002;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p4	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between 18446744073709551001 and 18446744073709551002)
explain select * from t1 where a = 18446744073709551000;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p4	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = 18446744073709551000)
explain select * from t1 where a = 18446744073709551613;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p4	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = 18446744073709551613)
explain select * from t1 where a = 18446744073709551614;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = 18446744073709551614)
drop table t1;
create table t1 (a int)
partition by range(a) (
partition p0 values less than (64),
partition p1 values less than (128),
partition p2 values less than (255)
);
create table t2 (a int)
partition by range(a+0) (
partition p0 values less than (64),
partition p1 values less than (128),
partition p2 values less than (255)
);
insert into t1 values (0x20), (0x20), (0x41), (0x41), (0xFE), (0xFE);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
insert into t2 values (0x20), (0x20), (0x41), (0x41), (0xFE), (0xFE);
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
explain select * from t1 where a=0;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = 0)
explain select * from t2 where a=0;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a` from `test`.`t2` where (`test`.`t2`.`a` = 0)
explain select * from t1 where a=0xFE;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = 0xfe)
explain select * from t2 where a=0xFE;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p2	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a` from `test`.`t2` where (`test`.`t2`.`a` = 0xfe)
explain select * from t1 where a > 0xFE AND a <= 0xFF;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` > 0xfe) and (`test`.`t1`.`a` <= 0xff))
explain select * from t2 where a > 0xFE AND a <= 0xFF;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a` from `test`.`t2` where ((`test`.`t2`.`a` > 0xfe) and (`test`.`t2`.`a` <= 0xff))
explain select * from t1 where a >= 0xFE AND a <= 0xFF;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` >= 0xfe) and (`test`.`t1`.`a` <= 0xff))
explain select * from t2 where a >= 0xFE AND a <= 0xFF;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p2	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a` from `test`.`t2` where ((`test`.`t2`.`a` >= 0xfe) and (`test`.`t2`.`a` <= 0xff))
explain select * from t1 where a < 64 AND a >= 63;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` < 64) and (`test`.`t1`.`a` >= 63))
explain select * from t2 where a < 64 AND a >= 63;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a` from `test`.`t2` where ((`test`.`t2`.`a` < 64) and (`test`.`t2`.`a` >= 63))
explain select * from t1 where a <= 64 AND a >= 63;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1	ALL	NULL	NULL	NULL	NULL	#	25.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` <= 64) and (`test`.`t1`.`a` >= 63))
explain select * from t2 where a <= 64 AND a >= 63;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1	ALL	NULL	NULL	NULL	NULL	#	25.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a` from `test`.`t2` where ((`test`.`t2`.`a` <= 64) and (`test`.`t2`.`a` >= 63))
drop table t1;
drop table t2;
create table t1(a bigint unsigned not null) partition by range(a+0) (
partition p1 values less than (10),
partition p2 values less than (20),
partition p3 values less than (2305561538531885056),
partition p4 values less than (2305561538531950591)
);
insert into t1 values (9),(19),(0xFFFF0000FFFF000-1), (0xFFFF0000FFFFFFF-1);
insert into t1 values (9),(19),(0xFFFF0000FFFF000-1), (0xFFFF0000FFFFFFF-1);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where
a >= 2305561538531885056-10 and a <= 2305561538531885056-8;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p3	ALL	NULL	NULL	NULL	NULL	#	25.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` >= 2305561538531885046) and (`test`.`t1`.`a` <= 2305561538531885048))
explain select * from t1 where
a > 0xFFFFFFFFFFFFFFEC and a < 0xFFFFFFFFFFFFFFEE;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` > 18446744073709551596) and (`test`.`t1`.`a` < 18446744073709551598))
explain select * from t1 where a>=0 and a <= 0xFFFFFFFFFFFFFFFF;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	#	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where true
drop table t1;
create table t1 (a bigint) partition by range(a+0) (
partition p1 values less than (-1000),
partition p2 values less than (-10),
partition p3 values less than (10),
partition p4 values less than (1000)
);
insert into t1 values (-15),(-5),(5),(15),(-15),(-5),(5),(15);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where a>-2 and a <=0;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p3	ALL	NULL	NULL	NULL	NULL	#	25.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where ((`test`.`t1`.`a` > -2) and (`test`.`t1`.`a` <= 0))
drop table t1;
CREATE TABLE t1 ( recdate  DATETIME NOT NULL )
PARTITION BY RANGE( TO_DAYS(recdate) ) (
PARTITION p0 VALUES LESS THAN ( TO_DAYS('2007-03-08') ),
PARTITION p1 VALUES LESS THAN ( TO_DAYS('2007-04-01') )
);
INSERT INTO t1 VALUES ('2007-03-01 12:00:00');
INSERT INTO t1 VALUES ('2007-03-07 12:00:00');
INSERT INTO t1 VALUES ('2007-03-08 12:00:00');
INSERT INTO t1 VALUES ('2007-03-15 12:00:00');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
must use p0 only:
explain select * from t1 where recdate < '2007-03-08 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`recdate` AS `recdate` from `test`.`t1` where (`test`.`t1`.`recdate` < TIMESTAMP'2007-03-08 00:00:00')
drop table t1;
CREATE TABLE t1 ( recdate  DATETIME NOT NULL )
PARTITION BY RANGE( YEAR(recdate) ) (
PARTITION p0 VALUES LESS THAN (2006),
PARTITION p1 VALUES LESS THAN (2007)
);
INSERT INTO t1 VALUES ('2005-03-01 12:00:00');
INSERT INTO t1 VALUES ('2005-03-01 12:00:00');
INSERT INTO t1 VALUES ('2006-03-01 12:00:00');
INSERT INTO t1 VALUES ('2006-03-01 12:00:00');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
must use p0 only:
explain select * from t1 where recdate < '2006-01-01 00:00:00';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0	ALL	NULL	NULL	NULL	NULL	#	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`recdate` AS `recdate` from `test`.`t1` where (`test`.`t1`.`recdate` < TIMESTAMP'2006-01-01 00:00:00')
drop table t1;
#
# BUG#33730 Full table scan instead selected partitions for query more than 10 partitions
#
create table t0 (a int);
insert into t0 values (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
create table t1 (a int)
partition by range(a+0) (
partition p0 values less than (64),
partition p1 values less than (128),
partition p2 values less than (255)
);
insert into t1 select A.a + 10*B.a from t0 A, t0 B;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where a between 10 and 13;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0	ALL	NULL	NULL	NULL	NULL	#	11.11	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between 10 and 13)
explain select * from t1 where a between 10 and 10+33;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2	ALL	NULL	NULL	NULL	NULL	#	11.11	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` between 10 and <cache>((10 + 33)))
drop table t0, t1;
#
# Bug#71095: Wrong results with PARTITION BY LIST COLUMNS()
#
CREATE TABLE t1
(c1 int,
c2 int,
c3 int,
c4 int,
PRIMARY KEY (c1,c2))
PARTITION BY LIST COLUMNS (c2)
(PARTITION p1 VALUES IN (1,2),
PARTITION p2 VALUES IN (3,4));
INSERT INTO t1 VALUES (1, 1, 1, 1), (2, 3, 1, 1);
INSERT INTO t1 VALUES (1, 2, 1, 1), (2, 4, 1, 1);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT * FROM t1 WHERE c1 = 1 AND c2 < 1;
c1	c2	c3	c4
SELECT * FROM t1 WHERE c1 = 1 AND c2 <= 1;
c1	c2	c3	c4
1	1	1	1
SELECT * FROM t1 WHERE c1 = 1 AND c2 = 1;
c1	c2	c3	c4
1	1	1	1
SELECT * FROM t1 WHERE c1 = 1 AND c2 >= 1;
c1	c2	c3	c4
1	1	1	1
1	2	1	1
SELECT * FROM t1 WHERE c1 = 1 AND c2 > 1;
c1	c2	c3	c4
1	2	1	1
SELECT * FROM t1 WHERE c1 = 1 AND c2 < 3;
c1	c2	c3	c4
1	1	1	1
1	2	1	1
SELECT * FROM t1 WHERE c1 = 1 AND c2 <= 3;
c1	c2	c3	c4
1	1	1	1
1	2	1	1
SELECT * FROM t1 WHERE c1 = 2 AND c2 <= 3;
c1	c2	c3	c4
2	3	1	1
SELECT * FROM t1 WHERE c1 = 2 AND c2 = 3;
c1	c2	c3	c4
2	3	1	1
SELECT * FROM t1 WHERE c1 = 2 AND c2 >= 3;
c1	c2	c3	c4
2	3	1	1
2	4	1	1
SELECT * FROM t1 WHERE c1 = 2 AND c2 > 3;
c1	c2	c3	c4
2	4	1	1
SELECT * FROM t1 WHERE c1 = 2 AND c2 < 4;
c1	c2	c3	c4
2	3	1	1
SELECT * FROM t1 WHERE c1 = 2 AND c2 <= 4;
c1	c2	c3	c4
2	3	1	1
2	4	1	1
SELECT * FROM t1 WHERE c1 = 2 AND c2 = 4;
c1	c2	c3	c4
2	4	1	1
SELECT * FROM t1 WHERE c1 = 2 AND c2 >= 4;
c1	c2	c3	c4
2	4	1	1
SELECT * FROM t1 WHERE c1 = 2 AND c2 > 4;
c1	c2	c3	c4
EXPLAIN SELECT * FROM t1 WHERE c1 = 1 AND c2 < 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	no matching row in const table
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`c4` AS `c4` from `test`.`t1` where ((`test`.`t1`.`c2` < 1) and multiple equal(1, `test`.`t1`.`c1`))
EXPLAIN SELECT * FROM t1 WHERE c1 = 1 AND c2 <= 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1	range	PRIMARY	PRIMARY	8	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`c4` AS `c4` from `test`.`t1` where ((`test`.`t1`.`c1` = 1) and (`test`.`t1`.`c2` <= 1))
EXPLAIN SELECT * FROM t1 WHERE c1 = 1 AND c2 = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1	const	PRIMARY	PRIMARY	8	const,const	#	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select '1' AS `c1`,'1' AS `c2`,'1' AS `c3`,'1' AS `c4` from `test`.`t1` where true
EXPLAIN SELECT * FROM t1 WHERE c1 = 1 AND c2 >= 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2	range	PRIMARY	PRIMARY	8	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`c4` AS `c4` from `test`.`t1` where ((`test`.`t1`.`c1` = 1) and (`test`.`t1`.`c2` >= 1))
EXPLAIN SELECT * FROM t1 WHERE c1 = 1 AND c2 > 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2	range	PRIMARY	PRIMARY	8	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`c4` AS `c4` from `test`.`t1` where ((`test`.`t1`.`c1` = 1) and (`test`.`t1`.`c2` > 1))
EXPLAIN SELECT * FROM t1 WHERE c1 = 1 AND c2 < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1	range	PRIMARY	PRIMARY	8	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`c4` AS `c4` from `test`.`t1` where ((`test`.`t1`.`c1` = 1) and (`test`.`t1`.`c2` < 3))
EXPLAIN SELECT * FROM t1 WHERE c1 = 1 AND c2 <= 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2	range	PRIMARY	PRIMARY	8	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`c4` AS `c4` from `test`.`t1` where ((`test`.`t1`.`c1` = 1) and (`test`.`t1`.`c2` <= 3))
EXPLAIN SELECT * FROM t1 WHERE c1 = 2 AND c2 <= 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2	range	PRIMARY	PRIMARY	8	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`c4` AS `c4` from `test`.`t1` where ((`test`.`t1`.`c1` = 2) and (`test`.`t1`.`c2` <= 3))
EXPLAIN SELECT * FROM t1 WHERE c1 = 2 AND c2 = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2	const	PRIMARY	PRIMARY	8	const,const	#	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select '2' AS `c1`,'3' AS `c2`,'1' AS `c3`,'1' AS `c4` from `test`.`t1` where true
EXPLAIN SELECT * FROM t1 WHERE c1 = 2 AND c2 >= 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2	range	PRIMARY	PRIMARY	8	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`c4` AS `c4` from `test`.`t1` where ((`test`.`t1`.`c1` = 2) and (`test`.`t1`.`c2` >= 3))
EXPLAIN SELECT * FROM t1 WHERE c1 = 2 AND c2 > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2	range	PRIMARY	PRIMARY	8	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`c4` AS `c4` from `test`.`t1` where ((`test`.`t1`.`c1` = 2) and (`test`.`t1`.`c2` > 3))
EXPLAIN SELECT * FROM t1 WHERE c1 = 2 AND c2 < 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2	range	PRIMARY	PRIMARY	8	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`c4` AS `c4` from `test`.`t1` where ((`test`.`t1`.`c1` = 2) and (`test`.`t1`.`c2` < 4))
EXPLAIN SELECT * FROM t1 WHERE c1 = 2 AND c2 <= 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2	range	PRIMARY	PRIMARY	8	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`c4` AS `c4` from `test`.`t1` where ((`test`.`t1`.`c1` = 2) and (`test`.`t1`.`c2` <= 4))
EXPLAIN SELECT * FROM t1 WHERE c1 = 2 AND c2 = 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2	const	PRIMARY	PRIMARY	8	const,const	#	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select '2' AS `c1`,'4' AS `c2`,'1' AS `c3`,'1' AS `c4` from `test`.`t1` where true
EXPLAIN SELECT * FROM t1 WHERE c1 = 2 AND c2 >= 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p2	range	PRIMARY	PRIMARY	8	NULL	#	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`c4` AS `c4` from `test`.`t1` where ((`test`.`t1`.`c1` = 2) and (`test`.`t1`.`c2` >= 4))
EXPLAIN SELECT * FROM t1 WHERE c1 = 2 AND c2 > 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	#	NULL	no matching row in const table
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`c4` AS `c4` from `test`.`t1` where ((`test`.`t1`.`c2` > 4) and multiple equal(2, `test`.`t1`.`c1`))
DROP TABLE t1;
#
# Bug#32134145 - PARTITION PRUNING FAILS WITH EXPLICIT TIME_ZONE
# OFFSET IN DATETIME LITERAL
#
CREATE TABLE tp (
id int unsigned NOT NULL,
ts timestamp NOT NULL,
PRIMARY KEY (id, ts)
)
PARTITION BY RANGE (unix_timestamp(ts))
(
PARTITION p1 VALUES LESS THAN (1580515200),
PARTITION p2 VALUES LESS THAN (1583020800)
);
SET time_zone = '+00:00';
INSERT INTO tp VALUES (1, '2020-01-01');
INSERT INTO tp VALUES (1, '2020-02-01');
SELECT COUNT(*) FROM tp PARTITION(p1);
COUNT(*)
1
SELECT COUNT(*) FROM tp PARTITION(p2);
COUNT(*)
1
SELECT * FROM tp WHERE ts >= '2020-01-01 00:00:00+00:00' AND ts <='2020-02-01 00:00:00+00:00';
id	ts
1	2020-01-01 00:00:00
1	2020-02-01 00:00:00
SET time_zone = '+01:00';
SELECT * FROM tp WHERE ts >= '2020-01-01 00:00:00+00:00' AND ts <='2020-02-01 00:00:00+00:00';
id	ts
1	2020-01-01 01:00:00
1	2020-02-01 01:00:00
ALTER TABLE tp REMOVE PARTITIONING;
SELECT * FROM tp WHERE ts >= '2020-01-01 00:00:00+00:00' AND ts <='2020-02-01 00:00:00+00:00';
id	ts
1	2020-01-01 01:00:00
1	2020-02-01 01:00:00
SET time_zone = DEFAULT;
DROP TABLE tp;
#
# Bug#33060953 - UPGRADE FROM 5.7 TO 8.0 CHANGE EXECTION PLAN
# IN PARTITIONED TABLE, EVEN AFTER REVERTING NEW OPTIMIZER FLAG
#
CREATE TABLE t1 ( pk INT PRIMARY KEY AUTO_INCREMENT, c INT)
PARTITION BY RANGE (pk) (
PARTITION p0 VALUES LESS THAN (1),
PARTITION p1 VALUES LESS THAN (2),
PARTITION p2 VALUES LESS THAN (3),
PARTITION p3 VALUES LESS THAN (4),
PARTITION p4 VALUES LESS THAN (100)
);
CREATE TABLE t2 ( pk INT PRIMARY KEY AUTO_INCREMENT, c INT);
CREATE TABLE t3 ( pk INT PRIMARY KEY AUTO_INCREMENT, c INT)
PARTITION BY RANGE (pk) (
PARTITION p0 VALUES LESS THAN (1),
PARTITION p1 VALUES LESS THAN (2),
PARTITION p2 VALUES LESS THAN (3),
PARTITION p3 VALUES LESS THAN (4),
PARTITION p4 VALUES LESS THAN (100)
);
CREATE TABLE t4 ( pk INT PRIMARY KEY AUTO_INCREMENT, c INT);
CREATE TABLE t5 ( pk INT PRIMARY KEY AUTO_INCREMENT, c INT);
INSERT INTO t1(c) VALUES (1),  (2),  (3),  (4),  (5),  (6),  (7),  (8),  (9),  (10);
INSERT INTO t1(c) VALUES (11), (12), (13), (14), (15), (16), (17), (18), (19), (20);
INSERT INTO t2(c) VALUES (21), (22), (23), (24), (25), (26), (27), (28), (29), (30);
INSERT INTO t3(c) VALUES (31), (32), (33), (34), (35), (36), (37), (38), (39), (40);
INSERT INTO t4(c) VALUES (41), (42), (43), (44), (45), (46), (47), (48), (49), (50);
INSERT INTO t5(c) VALUES (51), (52), (53), (54), (55), (56), (57), (58), (59), (60);
ANALYZE TABLE t1, t2, t3, t4, t5;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
test.t4	analyze	status	OK
test.t5	analyze	status	OK
EXPLAIN SELECT * FROM t1 JOIN t2 on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))
SELECT * FROM t1 JOIN t2 on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c
3	3	3	23
4	4	4	24
5	5	5	25
6	6	6	26
7	7	7	27
8	8	8	28
9	9	9	29
10	10	10	30
EXPLAIN SELECT * FROM t1 LEFT JOIN t2 on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	20	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` > 2))) where true
SELECT * FROM t1 LEFT JOIN t2 on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c
1	1	NULL	NULL
2	2	NULL	NULL
3	3	3	23
4	4	4	24
5	5	5	25
6	6	6	26
7	7	7	27
8	8	8	28
9	9	9	29
10	10	10	30
11	11	NULL	NULL
12	12	NULL	NULL
13	13	NULL	NULL
14	14	NULL	NULL
15	15	NULL	NULL
16	16	NULL	NULL
17	17	NULL	NULL
18	18	NULL	NULL
19	19	NULL	NULL
20	20	NULL	NULL
EXPLAIN SELECT * FROM t2 LEFT JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` left join `test`.`t1` on(((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))) where true
SELECT * FROM t2 LEFT JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c
1	21	NULL	NULL
2	22	NULL	NULL
3	23	3	3
4	24	4	4
5	25	5	5
6	26	6	6
7	27	7	7
8	28	8	8
9	29	9	9
10	30	10	10
EXPLAIN SELECT * FROM t2 JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` join `test`.`t1` where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))
SELECT * FROM t2 JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c
3	23	3	3
4	24	4	4
5	25	5	5
6	26	6	6
7	27	7	7
8	28	8	8
9	29	9	9
10	30	10	10
EXPLAIN SELECT * FROM t1 JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c` from `test`.`t1` join `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t2`.`pk`)) where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))
SELECT * FROM t1 JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c	pk	c
3	3	3	23	3	33
4	4	4	24	4	34
5	5	5	25	5	35
6	6	6	26	6	36
7	7	7	27	7	37
8	8	8	28	8	38
9	9	9	29	9	39
10	10	10	30	10	40
EXPLAIN SELECT * FROM t1 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	20	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c` from `test`.`t1` left join (`test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t1`.`pk`))) on(((`test`.`t2`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` > 2))) where true
SELECT * FROM t1 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c	pk	c
1	1	NULL	NULL	NULL	NULL
2	2	NULL	NULL	NULL	NULL
3	3	3	23	3	33
4	4	4	24	4	34
5	5	5	25	5	35
6	6	6	26	6	36
7	7	7	27	7	37
8	8	8	28	8	38
9	9	9	29	9	39
10	10	10	30	10	40
11	11	NULL	NULL	NULL	NULL
12	12	NULL	NULL	NULL	NULL
13	13	NULL	NULL	NULL	NULL
14	14	NULL	NULL	NULL	NULL
15	15	NULL	NULL	NULL	NULL
16	16	NULL	NULL	NULL	NULL
17	17	NULL	NULL	NULL	NULL
18	18	NULL	NULL	NULL	NULL
19	19	NULL	NULL	NULL	NULL
20	20	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM (t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t2`.`pk`)) left join `test`.`t1` on(((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))) where true
SELECT * FROM (t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c	pk	c
1	21	1	31	NULL	NULL
2	22	2	32	NULL	NULL
3	23	3	33	3	3
4	24	4	34	4	4
5	25	5	35	5	5
6	26	6	36	6	6
7	27	7	37	7	7
8	28	8	38	8	8
9	29	9	39	9	9
10	30	10	40	10	10
EXPLAIN SELECT * FROM (t2 LEFT JOIN t3 on t2.pk = t3.pk) JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t2`.`pk`)) join `test`.`t1` where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))
SELECT * FROM (t2 LEFT JOIN t3 on t2.pk = t3.pk) JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c	pk	c
3	23	3	33	3	3
4	24	4	34	4	4
5	25	5	35	5	5
6	26	6	36	6	6
7	27	7	37	7	7
8	28	8	38	8	8
9	29	9	39	9	9
10	30	10	40	10	10
EXPLAIN SELECT * FROM t1 JOIN ((t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t4 on t2.c = t4.c + 15) on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c` from `test`.`t1` join `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t2`.`pk`)) left join `test`.`t4` on((`test`.`t2`.`c` = (`test`.`t4`.`c` + 15))) where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))
SELECT * FROM t1 JOIN ((t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t4 on t2.c = t4.c + 15) on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c	pk	c	pk	c
3	3	3	23	3	33	NULL	NULL
4	4	4	24	4	34	NULL	NULL
5	5	5	25	5	35	NULL	NULL
6	6	6	26	6	36	NULL	NULL
7	7	7	27	7	37	NULL	NULL
8	8	8	28	8	38	NULL	NULL
9	9	9	29	9	39	NULL	NULL
10	10	10	30	10	40	NULL	NULL
EXPLAIN SELECT * FROM t1 LEFT JOIN ((t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t4 on t2.c = t4.c + 15) on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	20	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c` from `test`.`t1` left join (`test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t1`.`pk`)) left join `test`.`t4` on((`test`.`t2`.`c` = (`test`.`t4`.`c` + 15)))) on(((`test`.`t2`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` > 2))) where true
SELECT * FROM t1 LEFT JOIN ((t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t4 on t2.c = t4.c + 15) on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c	pk	c	pk	c
1	1	NULL	NULL	NULL	NULL	NULL	NULL
2	2	NULL	NULL	NULL	NULL	NULL	NULL
3	3	3	23	3	33	NULL	NULL
4	4	4	24	4	34	NULL	NULL
5	5	5	25	5	35	NULL	NULL
6	6	6	26	6	36	NULL	NULL
7	7	7	27	7	37	NULL	NULL
8	8	8	28	8	38	NULL	NULL
9	9	9	29	9	39	NULL	NULL
10	10	10	30	10	40	NULL	NULL
11	11	NULL	NULL	NULL	NULL	NULL	NULL
12	12	NULL	NULL	NULL	NULL	NULL	NULL
13	13	NULL	NULL	NULL	NULL	NULL	NULL
14	14	NULL	NULL	NULL	NULL	NULL	NULL
15	15	NULL	NULL	NULL	NULL	NULL	NULL
16	16	NULL	NULL	NULL	NULL	NULL	NULL
17	17	NULL	NULL	NULL	NULL	NULL	NULL
18	18	NULL	NULL	NULL	NULL	NULL	NULL
19	19	NULL	NULL	NULL	NULL	NULL	NULL
20	20	NULL	NULL	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM ((t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t4 on t2.c = t4.c + 15) LEFT JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t2`.`pk`)) left join `test`.`t4` on((`test`.`t2`.`c` = (`test`.`t4`.`c` + 15))) left join `test`.`t1` on(((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))) where true
SELECT * FROM ((t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t4 on t2.c = t4.c + 15) LEFT JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c	pk	c	pk	c
1	21	1	31	NULL	NULL	NULL	NULL
2	22	2	32	NULL	NULL	NULL	NULL
3	23	3	33	NULL	NULL	3	3
4	24	4	34	NULL	NULL	4	4
5	25	5	35	NULL	NULL	5	5
6	26	6	36	NULL	NULL	6	6
7	27	7	37	NULL	NULL	7	7
8	28	8	38	NULL	NULL	8	8
9	29	9	39	NULL	NULL	9	9
10	30	10	40	NULL	NULL	10	10
EXPLAIN SELECT * FROM ((t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t4 on t2.c = t4.c + 15) JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t2`.`pk`)) left join `test`.`t4` on((`test`.`t2`.`c` = (`test`.`t4`.`c` + 15))) join `test`.`t1` where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))
SELECT * FROM ((t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t4 on t2.c = t4.c + 15) JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c	pk	c	pk	c
3	23	3	33	NULL	NULL	3	3
4	24	4	34	NULL	NULL	4	4
5	25	5	35	NULL	NULL	5	5
6	26	6	36	NULL	NULL	6	6
7	27	7	37	NULL	NULL	7	7
8	28	8	38	NULL	NULL	8	8
9	29	9	39	NULL	NULL	9	9
10	30	10	40	NULL	NULL	10	10
EXPLAIN SELECT * FROM t1 JOIN (t4 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t2.c = t4.c + 20) on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c` from `test`.`t1` join `test`.`t4` join `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t2`.`pk`)) where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2) and (`test`.`t2`.`c` = (`test`.`t4`.`c` + 20)))
SELECT * FROM t1 JOIN (t4 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t2.c = t4.c + 20) on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c	pk	c	pk	c
EXPLAIN SELECT * FROM t1 LEFT JOIN (t4 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t2.c = t4.c + 20) on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	20	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c` from `test`.`t1` left join (`test`.`t4` join `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t1`.`pk`))) on(((`test`.`t2`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` > 2) and (`test`.`t2`.`c` = (`test`.`t4`.`c` + 20)))) where true
SELECT * FROM t1 LEFT JOIN (t4 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t2.c = t4.c + 20) on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c	pk	c	pk	c
1	1	NULL	NULL	NULL	NULL	NULL	NULL
2	2	NULL	NULL	NULL	NULL	NULL	NULL
3	3	NULL	NULL	NULL	NULL	NULL	NULL
4	4	NULL	NULL	NULL	NULL	NULL	NULL
5	5	NULL	NULL	NULL	NULL	NULL	NULL
6	6	NULL	NULL	NULL	NULL	NULL	NULL
7	7	NULL	NULL	NULL	NULL	NULL	NULL
8	8	NULL	NULL	NULL	NULL	NULL	NULL
9	9	NULL	NULL	NULL	NULL	NULL	NULL
10	10	NULL	NULL	NULL	NULL	NULL	NULL
11	11	NULL	NULL	NULL	NULL	NULL	NULL
12	12	NULL	NULL	NULL	NULL	NULL	NULL
13	13	NULL	NULL	NULL	NULL	NULL	NULL
14	14	NULL	NULL	NULL	NULL	NULL	NULL
15	15	NULL	NULL	NULL	NULL	NULL	NULL
16	16	NULL	NULL	NULL	NULL	NULL	NULL
17	17	NULL	NULL	NULL	NULL	NULL	NULL
18	18	NULL	NULL	NULL	NULL	NULL	NULL
19	19	NULL	NULL	NULL	NULL	NULL	NULL
20	20	NULL	NULL	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM (t4 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t2.c = t4.c + 20) LEFT JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t4` left join (`test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t2`.`pk`))) on((`test`.`t2`.`c` = (`test`.`t4`.`c` + 20))) left join `test`.`t1` on(((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))) where true
SELECT * FROM (t4 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t2.c = t4.c + 20) LEFT JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c	pk	c	pk	c
1	41	NULL	NULL	NULL	NULL	NULL	NULL
2	42	NULL	NULL	NULL	NULL	NULL	NULL
3	43	NULL	NULL	NULL	NULL	NULL	NULL
4	44	NULL	NULL	NULL	NULL	NULL	NULL
5	45	NULL	NULL	NULL	NULL	NULL	NULL
6	46	NULL	NULL	NULL	NULL	NULL	NULL
7	47	NULL	NULL	NULL	NULL	NULL	NULL
8	48	NULL	NULL	NULL	NULL	NULL	NULL
9	49	NULL	NULL	NULL	NULL	NULL	NULL
10	50	NULL	NULL	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM (t4 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t2.c = t4.c + 20) JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t4` join `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t2`.`pk`)) join `test`.`t1` where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2) and (`test`.`t2`.`c` = (`test`.`t4`.`c` + 20)))
SELECT * FROM (t4 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t2.c = t4.c + 20) JOIN t1 on t1.pk = t2.pk AND t1.pk > 2;
pk	c	pk	c	pk	c	pk	c
EXPLAIN SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk) JOIN (t3 LEFT JOIN t4 on t3.pk = t4.pk) on t1.pk = t3.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2	range	PRIMARY	PRIMARY	4	NULL	2	100.00	Using where
1	SIMPLE	t3	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	t4	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`pk` = `test`.`t1`.`pk`)) join `test`.`t3` left join `test`.`t4` on((`test`.`t4`.`pk` = `test`.`t1`.`pk`)) where ((`test`.`t3`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` < 3))
SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk) JOIN (t3 LEFT JOIN t4 on t3.pk = t4.pk) on t1.pk = t3.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c
1	1	1	21	1	31	1	41
2	2	2	22	2	32	2	42
EXPLAIN SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk) LEFT JOIN (t3 LEFT JOIN t4 on t3.pk = t4.pk) on t1.pk = t3.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	20	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	t4	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`pk` = `test`.`t1`.`pk`)) left join (`test`.`t3` left join `test`.`t4` on((`test`.`t4`.`pk` = `test`.`t1`.`pk`))) on(((`test`.`t3`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` < 3))) where true
SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk) LEFT JOIN (t3 LEFT JOIN t4 on t3.pk = t4.pk) on t1.pk = t3.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c
1	1	1	21	1	31	1	41
2	2	2	22	2	32	2	42
3	3	3	23	NULL	NULL	NULL	NULL
4	4	4	24	NULL	NULL	NULL	NULL
5	5	5	25	NULL	NULL	NULL	NULL
6	6	6	26	NULL	NULL	NULL	NULL
7	7	7	27	NULL	NULL	NULL	NULL
8	8	8	28	NULL	NULL	NULL	NULL
9	9	9	29	NULL	NULL	NULL	NULL
10	10	10	30	NULL	NULL	NULL	NULL
11	11	NULL	NULL	NULL	NULL	NULL	NULL
12	12	NULL	NULL	NULL	NULL	NULL	NULL
13	13	NULL	NULL	NULL	NULL	NULL	NULL
14	14	NULL	NULL	NULL	NULL	NULL	NULL
15	15	NULL	NULL	NULL	NULL	NULL	NULL
16	16	NULL	NULL	NULL	NULL	NULL	NULL
17	17	NULL	NULL	NULL	NULL	NULL	NULL
18	18	NULL	NULL	NULL	NULL	NULL	NULL
19	19	NULL	NULL	NULL	NULL	NULL	NULL
20	20	NULL	NULL	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM (t3 LEFT JOIN t4 on t3.pk = t4.pk) LEFT JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk) on t1.pk = t3.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t4	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t1	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	Using where
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t3` left join `test`.`t4` on((`test`.`t4`.`pk` = `test`.`t3`.`pk`)) left join (`test`.`t1` left join `test`.`t2` on((`test`.`t2`.`pk` = `test`.`t3`.`pk`))) on(((`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` < 3))) where true
SELECT * FROM (t3 LEFT JOIN t4 on t3.pk = t4.pk) LEFT JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk) on t1.pk = t3.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c
1	31	1	41	1	1	1	21
2	32	2	42	2	2	2	22
3	33	3	43	NULL	NULL	NULL	NULL
4	34	4	44	NULL	NULL	NULL	NULL
5	35	5	45	NULL	NULL	NULL	NULL
6	36	6	46	NULL	NULL	NULL	NULL
7	37	7	47	NULL	NULL	NULL	NULL
8	38	8	48	NULL	NULL	NULL	NULL
9	39	9	49	NULL	NULL	NULL	NULL
10	40	10	50	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM (t3 LEFT JOIN t4 on t3.pk = t4.pk) JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk) on t1.pk = t3.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p0,p1,p2	range	PRIMARY	PRIMARY	4	NULL	2	100.00	Using where
1	SIMPLE	t1	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t4	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t3` left join `test`.`t4` on((`test`.`t4`.`pk` = `test`.`t3`.`pk`)) join `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`pk` = `test`.`t3`.`pk`)) where ((`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` < 3))
SELECT * FROM (t3 LEFT JOIN t4 on t3.pk = t4.pk) JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk) on t1.pk = t3.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c
1	31	1	41	1	1	1	21
2	32	2	42	2	2	2	22
EXPLAIN SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) JOIN ((t5 LEFT JOIN t3 on t5.pk = t3.pk) LEFT JOIN t4 on t5.c = t4.c + 15) on t1.pk = t3.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2	range	PRIMARY	PRIMARY	4	NULL	2	100.00	Using where
1	SIMPLE	t5	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` < 2))) join `test`.`t5` join `test`.`t3` left join `test`.`t4` on((`test`.`t5`.`c` = (`test`.`t4`.`c` + 15))) where ((`test`.`t5`.`pk` = `test`.`t1`.`pk`) and (`test`.`t3`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` < 3))
SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) JOIN ((t5 LEFT JOIN t3 on t5.pk = t3.pk) LEFT JOIN t4 on t5.c = t4.c + 15) on t1.pk = t3.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c	pk	c
1	1	1	21	1	51	1	31	NULL	NULL
2	2	NULL	NULL	2	52	2	32	NULL	NULL
EXPLAIN SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) LEFT JOIN ((t5 LEFT JOIN t3 on t5.pk = t3.pk) LEFT JOIN t4 on t5.c = t4.c + 15) on t1.pk = t3.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	20	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	t5	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	t3	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` < 2))) left join (`test`.`t5` join `test`.`t3` left join `test`.`t4` on((`test`.`t5`.`c` = (`test`.`t4`.`c` + 15)))) on(((`test`.`t5`.`pk` = `test`.`t1`.`pk`) and (`test`.`t3`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` < 3))) where true
SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) LEFT JOIN ((t5 LEFT JOIN t3 on t5.pk = t3.pk) LEFT JOIN t4 on t5.c = t4.c + 15) on t1.pk = t3.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c	pk	c
1	1	1	21	1	51	1	31	NULL	NULL
2	2	NULL	NULL	2	52	2	32	NULL	NULL
3	3	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
4	4	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
5	5	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
6	6	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
7	7	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
8	8	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
9	9	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
10	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
11	11	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
12	12	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
13	13	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
14	14	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
15	15	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
16	16	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
17	17	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
18	18	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
19	19	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
20	20	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM ((t5 LEFT JOIN t3 on t5.pk = t3.pk) LEFT JOIN t4 on t5.c = t4.c + 15) LEFT JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) on t1.pk = t3.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t5	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	NULL
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
1	SIMPLE	t1	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	Using where
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t5` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t5`.`pk`)) left join `test`.`t4` on((`test`.`t5`.`c` = (`test`.`t4`.`c` + 15))) left join (`test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` < 2)))) on(((`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` < 3))) where true
SELECT * FROM ((t5 LEFT JOIN t3 on t5.pk = t3.pk) LEFT JOIN t4 on t5.c = t4.c + 15) LEFT JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) on t1.pk = t3.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c	pk	c
1	51	1	31	NULL	NULL	1	1	1	21
2	52	2	32	NULL	NULL	2	2	NULL	NULL
3	53	3	33	NULL	NULL	NULL	NULL	NULL	NULL
4	54	4	34	NULL	NULL	NULL	NULL	NULL	NULL
5	55	5	35	NULL	NULL	NULL	NULL	NULL	NULL
6	56	6	36	1	41	NULL	NULL	NULL	NULL
7	57	7	37	2	42	NULL	NULL	NULL	NULL
8	58	8	38	3	43	NULL	NULL	NULL	NULL
9	59	9	39	4	44	NULL	NULL	NULL	NULL
10	60	10	40	5	45	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM ((t5 LEFT JOIN t3 on t5.pk = t3.pk) LEFT JOIN t4 on t5.c = t4.c + 15) JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) on t1.pk = t3.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t5	NULL	range	PRIMARY	PRIMARY	4	NULL	2	100.00	Using where
1	SIMPLE	t3	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	NULL
1	SIMPLE	t1	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	Using where
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t5` join `test`.`t3` left join `test`.`t4` on((`test`.`t5`.`c` = (`test`.`t4`.`c` + 15))) join `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t5`.`pk`) and (`test`.`t5`.`pk` < 2))) where ((`test`.`t3`.`pk` = `test`.`t5`.`pk`) and (`test`.`t1`.`pk` = `test`.`t5`.`pk`) and (`test`.`t5`.`pk` < 3))
SELECT * FROM ((t5 LEFT JOIN t3 on t5.pk = t3.pk) LEFT JOIN t4 on t5.c = t4.c + 15) JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) on t1.pk = t3.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c	pk	c
1	51	1	31	NULL	NULL	1	1	1	21
2	52	2	32	NULL	NULL	2	2	NULL	NULL
EXPLAIN SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) JOIN (t4 LEFT JOIN (t5 LEFT JOIN t3 on t5.pk = t3.pk) on t5.c = t4.c + 20) on t1.pk = t3.pk AND t1.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t5	NULL	range	PRIMARY	PRIMARY	4	NULL	7	100.00	Using where
1	SIMPLE	t3	p4	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	NULL
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	Using where
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t5`.`pk`) and (`test`.`t5`.`pk` > 3))) join `test`.`t4` join `test`.`t5` join `test`.`t3` where ((`test`.`t3`.`pk` = `test`.`t5`.`pk`) and (`test`.`t1`.`pk` = `test`.`t5`.`pk`) and (`test`.`t5`.`pk` > 3) and (`test`.`t5`.`c` = (`test`.`t4`.`c` + 20)))
SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) JOIN (t4 LEFT JOIN (t5 LEFT JOIN t3 on t5.pk = t3.pk) on t5.c = t4.c + 20) on t1.pk = t3.pk AND t1.pk > 3;
pk	c	pk	c	pk	c	pk	c	pk	c
EXPLAIN SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) LEFT JOIN (t4 LEFT JOIN (t5 LEFT JOIN t3 on t5.pk = t3.pk) on t5.c = t4.c + 20) on t1.pk = t3.pk AND t1.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	20	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	t5	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	t3	p4	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` > 3))) left join (`test`.`t4` join `test`.`t5` join `test`.`t3`) on(((`test`.`t5`.`pk` = `test`.`t1`.`pk`) and (`test`.`t3`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` > 3) and (`test`.`t5`.`c` = (`test`.`t4`.`c` + 20)))) where true
SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) LEFT JOIN (t4 LEFT JOIN (t5 LEFT JOIN t3 on t5.pk = t3.pk) on t5.c = t4.c + 20) on t1.pk = t3.pk AND t1.pk > 3;
pk	c	pk	c	pk	c	pk	c	pk	c
1	1	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
2	2	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
3	3	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
4	4	4	24	NULL	NULL	NULL	NULL	NULL	NULL
5	5	5	25	NULL	NULL	NULL	NULL	NULL	NULL
6	6	6	26	NULL	NULL	NULL	NULL	NULL	NULL
7	7	7	27	NULL	NULL	NULL	NULL	NULL	NULL
8	8	8	28	NULL	NULL	NULL	NULL	NULL	NULL
9	9	9	29	NULL	NULL	NULL	NULL	NULL	NULL
10	10	10	30	NULL	NULL	NULL	NULL	NULL	NULL
11	11	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
12	12	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
13	13	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
14	14	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
15	15	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
16	16	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
17	17	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
18	18	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
19	19	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
20	20	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM (t4 LEFT JOIN (t5 LEFT JOIN t3 on t5.pk = t3.pk) on t5.c = t4.c + 20) LEFT JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) on t1.pk = t3.pk AND t1.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t5	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	NULL
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	Using where
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t4` left join (`test`.`t5` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t5`.`pk`))) on((`test`.`t5`.`c` = (`test`.`t4`.`c` + 20))) left join (`test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` > 3)))) on(((`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` > 3))) where true
SELECT * FROM (t4 LEFT JOIN (t5 LEFT JOIN t3 on t5.pk = t3.pk) on t5.c = t4.c + 20) LEFT JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) on t1.pk = t3.pk AND t1.pk > 3;
pk	c	pk	c	pk	c	pk	c	pk	c
1	41	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
2	42	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
3	43	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
4	44	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
5	45	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
6	46	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
7	47	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
8	48	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
9	49	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
10	50	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM (t4 LEFT JOIN (t5 LEFT JOIN t3 on t5.pk = t3.pk) on t5.c = t4.c + 20) JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) on t1.pk = t3.pk AND t1.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t5	NULL	range	PRIMARY	PRIMARY	4	NULL	7	100.00	Using where
1	SIMPLE	t3	p4	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	NULL
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	Using where
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t4` join `test`.`t5` join `test`.`t3` join `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t5`.`pk`) and (`test`.`t5`.`pk` > 3))) where ((`test`.`t3`.`pk` = `test`.`t5`.`pk`) and (`test`.`t1`.`pk` = `test`.`t5`.`pk`) and (`test`.`t5`.`pk` > 3) and (`test`.`t5`.`c` = (`test`.`t4`.`c` + 20)))
SELECT * FROM (t4 LEFT JOIN (t5 LEFT JOIN t3 on t5.pk = t3.pk) on t5.c = t4.c + 20) JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) on t1.pk = t3.pk AND t1.pk > 3;
pk	c	pk	c	pk	c	pk	c	pk	c
EXPLAIN SELECT * FROM t1 JOIN t2 on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p3,p4	index	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; Using index
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t3` join `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`pk` = `test`.`t3`.`pk`) and (`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM t1 JOIN t2 on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
pk	c	pk	c
3	3	3	23
4	4	4	24
5	5	5	25
6	6	6	26
7	7	7	27
8	8	8	28
9	9	9	29
10	10	10	30
EXPLAIN SELECT * FROM t1 LEFT JOIN t2 on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p3,p4	index	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; Using index
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t3` join `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`pk` = `test`.`t3`.`pk`)) where ((`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM t1 LEFT JOIN t2 on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
pk	c	pk	c
3	3	3	23
4	4	4	24
5	5	5	25
6	6	6	26
7	7	7	27
8	8	8	28
9	9	9	29
10	10	10	30
EXPLAIN SELECT * FROM t2 LEFT JOIN t1 on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p3,p4	index	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; Using index
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t3` join `test`.`t2` join `test`.`t1` where ((`test`.`t2`.`pk` = `test`.`t3`.`pk`) and (`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM t2 LEFT JOIN t1 on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
pk	c	pk	c
3	23	3	3
4	24	4	4
5	25	5	5
6	26	6	6
7	27	7	7
8	28	8	8
9	29	9	9
10	30	10	10
EXPLAIN SELECT * FROM t2 JOIN t1 on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p3,p4	index	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; Using index
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t3` join `test`.`t2` join `test`.`t1` where ((`test`.`t2`.`pk` = `test`.`t3`.`pk`) and (`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM t2 JOIN t1 on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
pk	c	pk	c
3	23	3	3
4	24	4	4
5	25	5	5
6	26	6	6
7	27	7	7
8	28	8	8
9	29	9	9
10	30	10	10
EXPLAIN SELECT * FROM t1 JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p3,p4	index	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; Using index
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c` from `test`.`t3` join `test`.`t1` join `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t3`.`pk`)) where ((`test`.`t2`.`pk` = `test`.`t3`.`pk`) and (`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM t1 JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
pk	c	pk	c	pk	c
3	3	3	23	3	33
4	4	4	24	4	34
5	5	5	25	5	35
6	6	6	26	6	36
7	7	7	27	7	37
8	8	8	28	8	38
9	9	9	29	9	39
10	10	10	30	10	40
EXPLAIN SELECT * FROM t1 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p3,p4	index	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; Using index
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c` from `test`.`t3` join `test`.`t1` left join (`test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t3`.`pk`))) on(((`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t2`.`pk` = `test`.`t3`.`pk`))) where ((`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM t1 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
pk	c	pk	c	pk	c
3	3	3	23	3	33
4	4	4	24	4	34
5	5	5	25	5	35
6	6	6	26	6	36
7	7	7	27	7	37
8	8	8	28	8	38
9	9	9	29	9	39
10	10	10	30	10	40
EXPLAIN SELECT * FROM (t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t1 on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p3,p4	index	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; Using index
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t3` join `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t3`.`pk`)) join `test`.`t1` where ((`test`.`t2`.`pk` = `test`.`t3`.`pk`) and (`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM (t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t1 on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
pk	c	pk	c	pk	c
3	23	3	33	3	3
4	24	4	34	4	4
5	25	5	35	5	5
6	26	6	36	6	6
7	27	7	37	7	7
8	28	8	38	8	8
9	29	9	39	9	9
10	30	10	40	10	10
EXPLAIN SELECT * FROM (t2 LEFT JOIN t3 on t2.pk = t3.pk) JOIN t1 on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p3,p4	index	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; Using index
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t3` join `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t3`.`pk`)) join `test`.`t1` where ((`test`.`t2`.`pk` = `test`.`t3`.`pk`) and (`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM (t2 LEFT JOIN t3 on t2.pk = t3.pk) JOIN t1 on t1.pk = t2.pk WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2);
pk	c	pk	c	pk	c
3	23	3	33	3	3
4	24	4	34	4	4
5	25	5	35	5	5
6	26	6	36	6	6
7	27	7	37	7	7
8	28	8	38	8	8
9	29	9	39	9	9
10	30	10	40	10	10
EXPLAIN SELECT * FROM t2 JOIN (SELECT * FROM t1 WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2)) t13 on t13.pk = t2.pk AND t13.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p3,p4	index	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; Using index
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t3` join `test`.`t2` join `test`.`t1` where ((`test`.`t2`.`pk` = `test`.`t3`.`pk`) and (`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t1`.`pk` > 3) and (`test`.`t3`.`pk` > 2))
SELECT * FROM t2 JOIN (SELECT * FROM t1 WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2)) t13 on t13.pk = t2.pk AND t13.pk > 3;
pk	c	pk	c
4	24	4	4
5	25	5	5
6	26	6	6
7	27	7	7
8	28	8	8
9	29	9	9
10	30	10	10
EXPLAIN SELECT * FROM t2 LEFT JOIN (SELECT * FROM t1 WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2)) t13 on t13.pk = t2.pk AND t13.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t3	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where; Using index
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` left join (`test`.`t3` join `test`.`t1`) on(((`test`.`t3`.`pk` = `test`.`t2`.`pk`) and (`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t1`.`pk` > 3) and (`test`.`t2`.`pk` > 2))) where true
SELECT * FROM t2 LEFT JOIN (SELECT * FROM t1 WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2)) t13 on t13.pk = t2.pk AND t13.pk > 3;
pk	c	pk	c
1	21	NULL	NULL
2	22	NULL	NULL
3	23	NULL	NULL
4	24	4	4
5	25	5	5
6	26	6	6
7	27	7	7
8	28	8	8
9	29	9	9
10	30	10	10
EXPLAIN SELECT * FROM (SELECT * FROM t1 WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2)) t13 LEFT JOIN t2 on t13.pk = t2.pk AND t13.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p3,p4	index	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; Using index
1	SIMPLE	t1	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t3` join `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t3`.`pk`) and (`test`.`t1`.`pk` > 3))) where ((`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM (SELECT * FROM t1 WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2)) t13 LEFT JOIN t2 on t13.pk = t2.pk AND t13.pk > 3;
pk	c	pk	c
3	3	NULL	NULL
4	4	4	24
5	5	5	25
6	6	6	26
7	7	7	27
8	8	8	28
9	9	9	29
10	10	10	30
EXPLAIN SELECT * FROM (SELECT * FROM t1 WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2)) t13 JOIN t2 on t13.pk = t2.pk AND t13.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p3,p4	index	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; Using index
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t3` join `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`pk` = `test`.`t3`.`pk`) and (`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t1`.`pk` > 3) and (`test`.`t3`.`pk` > 2))
SELECT * FROM (SELECT * FROM t1 WHERE t1.pk IN (SELECT pk FROM t3 WHERE pk > 2)) t13 JOIN t2 on t13.pk = t2.pk AND t13.pk > 3;
pk	c	pk	c
4	4	4	24
5	5	5	25
6	6	6	26
7	7	7	27
8	8	8	28
9	9	9	29
10	10	10	30
EXPLAIN SELECT * FROM t1 JOIN t2 on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	tx	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where; Not exists; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t1` join `test`.`t2` anti join (`test`.`t1` `tx`) on(((`test`.`tx`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))) where (`test`.`t1`.`pk` = `test`.`t2`.`pk`)
SELECT * FROM t1 JOIN t2 on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
pk	c	pk	c
1	1	1	21
2	2	2	22
EXPLAIN SELECT * FROM t1 LEFT JOIN t2 on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	20	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	tx	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where; Not exists; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`pk` = `test`.`t1`.`pk`)) anti join (`test`.`t1` `tx`) on(((`test`.`tx`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` > 2))) where true
SELECT * FROM t1 LEFT JOIN t2 on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
pk	c	pk	c
1	1	1	21
2	2	2	22
EXPLAIN SELECT * FROM t2 LEFT JOIN t1 on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	PRIMARY	t1	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where
2	DEPENDENT SUBQUERY	tx	p3,p4	unique_subquery	PRIMARY	PRIMARY	4	func	1	100.00	Using where; Using index; Full scan on NULL key
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` left join `test`.`t1` on((`test`.`t1`.`pk` = `test`.`t2`.`pk`)) where <in_optimizer>(`test`.`t1`.`pk`,<exists>(<primary_index_lookup>(<cache>(`test`.`t1`.`pk`) in t1 on PRIMARY where ((`test`.`tx`.`pk` > 2) and <if>(outer_field_is_not_null, (<cache>(`test`.`t1`.`pk`) = `test`.`tx`.`pk`), true)))) is false)
SELECT * FROM t2 LEFT JOIN t1 on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
pk	c	pk	c
1	21	1	1
2	22	2	2
EXPLAIN SELECT * FROM t2 JOIN t1 on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	tx	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where; Not exists; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` join `test`.`t1` anti join (`test`.`t1` `tx`) on(((`test`.`tx`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))) where (`test`.`t1`.`pk` = `test`.`t2`.`pk`)
SELECT * FROM t2 JOIN t1 on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
pk	c	pk	c
1	21	1	1
2	22	2	2
EXPLAIN SELECT * FROM t1 JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	tx	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where; Not exists; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c` from `test`.`t1` join `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t2`.`pk`)) anti join (`test`.`t1` `tx`) on(((`test`.`tx`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))) where (`test`.`t1`.`pk` = `test`.`t2`.`pk`)
SELECT * FROM t1 JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
pk	c	pk	c	pk	c
1	1	1	21	1	31
2	2	2	22	2	32
EXPLAIN SELECT * FROM t1 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	20	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	tx	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where; Not exists; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c` from `test`.`t1` left join (`test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t1`.`pk`))) on((`test`.`t2`.`pk` = `test`.`t1`.`pk`)) anti join (`test`.`t1` `tx`) on(((`test`.`tx`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` > 2))) where true
SELECT * FROM t1 LEFT JOIN (t2 LEFT JOIN t3 on t2.pk = t3.pk) on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
pk	c	pk	c	pk	c
1	1	1	21	1	31
2	2	2	22	2	32
EXPLAIN SELECT * FROM (t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t1 on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	PRIMARY	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	PRIMARY	t1	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where
2	DEPENDENT SUBQUERY	tx	p3,p4	unique_subquery	PRIMARY	PRIMARY	4	func	1	100.00	Using where; Using index; Full scan on NULL key
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t2`.`pk`)) left join `test`.`t1` on((`test`.`t1`.`pk` = `test`.`t2`.`pk`)) where <in_optimizer>(`test`.`t1`.`pk`,<exists>(<primary_index_lookup>(<cache>(`test`.`t1`.`pk`) in t1 on PRIMARY where ((`test`.`tx`.`pk` > 2) and <if>(outer_field_is_not_null, (<cache>(`test`.`t1`.`pk`) = `test`.`tx`.`pk`), true)))) is false)
SELECT * FROM (t2 LEFT JOIN t3 on t2.pk = t3.pk) LEFT JOIN t1 on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
pk	c	pk	c	pk	c
1	21	1	31	1	1
2	22	2	32	2	2
EXPLAIN SELECT * FROM (t2 LEFT JOIN t3 on t2.pk = t3.pk) JOIN t1 on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	tx	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where; Not exists; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`pk` = `test`.`t2`.`pk`)) join `test`.`t1` anti join (`test`.`t1` `tx`) on(((`test`.`tx`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))) where (`test`.`t1`.`pk` = `test`.`t2`.`pk`)
SELECT * FROM (t2 LEFT JOIN t3 on t2.pk = t3.pk) JOIN t1 on t1.pk = t2.pk WHERE t1.pk NOT IN (SELECT pk FROM t1 as tx WHERE pk > 2);
pk	c	pk	c	pk	c
1	21	1	31	1	1
2	22	2	32	2	2
EXPLAIN SELECT * FROM t2 JOIN (SELECT * FROM t1 WHERE t1.pk NOT IN (SELECT pk FROM t3 WHERE pk > 2)) t13 on t13.pk = t2.pk AND t13.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where
1	SIMPLE	t3	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where; Not exists; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` join `test`.`t1` anti join (`test`.`t3`) on(((`test`.`t3`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))) where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t1`.`pk` > 3))
SELECT * FROM t2 JOIN (SELECT * FROM t1 WHERE t1.pk NOT IN (SELECT pk FROM t3 WHERE pk > 2)) t13 on t13.pk = t2.pk AND t13.pk > 3;
pk	c	pk	c
EXPLAIN SELECT * FROM t2 LEFT JOIN (SELECT * FROM t1 WHERE t1.pk NOT IN (SELECT pk FROM t3 WHERE pk > 2)) t13 on t13.pk = t2.pk AND t13.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where
1	SIMPLE	t3	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where; Not exists; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` left join (`test`.`t1` anti join (`test`.`t3`) on(((`test`.`t3`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2)))) on(((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t1`.`pk` > 3))) where true
SELECT * FROM t2 LEFT JOIN (SELECT * FROM t1 WHERE t1.pk NOT IN (SELECT pk FROM t3 WHERE pk > 2)) t13 on t13.pk = t2.pk AND t13.pk > 3;
pk	c	pk	c
1	21	NULL	NULL
2	22	NULL	NULL
3	23	NULL	NULL
4	24	NULL	NULL
5	25	NULL	NULL
6	26	NULL	NULL
7	27	NULL	NULL
8	28	NULL	NULL
9	29	NULL	NULL
10	30	NULL	NULL
EXPLAIN SELECT * FROM (SELECT * FROM t1 WHERE t1.pk NOT IN (SELECT pk FROM t3 WHERE pk > 2)) t13 LEFT JOIN t2 on t13.pk = t2.pk AND t13.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	20	100.00	NULL
1	SIMPLE	<subquery3>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.t1.pk	1	100.00	Using where; Not exists
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
3	MATERIALIZED	t3	p3,p4	index	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t1` anti join (`test`.`t3`) on(((`<subquery3>`.`pk` = `test`.`t1`.`pk`) and (`test`.`t3`.`pk` > 2))) left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` > 3))) where true
SELECT * FROM (SELECT * FROM t1 WHERE t1.pk NOT IN (SELECT pk FROM t3 WHERE pk > 2)) t13 LEFT JOIN t2 on t13.pk = t2.pk AND t13.pk > 3;
pk	c	pk	c
1	1	NULL	NULL
2	2	NULL	NULL
11	11	NULL	NULL
12	12	NULL	NULL
13	13	NULL	NULL
14	14	NULL	NULL
15	15	NULL	NULL
16	16	NULL	NULL
17	17	NULL	NULL
18	18	NULL	NULL
19	19	NULL	NULL
20	20	NULL	NULL
EXPLAIN SELECT * FROM (SELECT * FROM t1 WHERE t1.pk NOT IN (SELECT pk FROM t3 WHERE pk > 2)) t13 JOIN t2 on t13.pk = t2.pk AND t13.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where
1	SIMPLE	t3	p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where; Not exists; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t1` anti join (`test`.`t3`) on(((`test`.`t3`.`pk` = `test`.`t2`.`pk`) and (`test`.`t2`.`pk` > 2))) join `test`.`t2` where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`test`.`t1`.`pk` > 3))
SELECT * FROM (SELECT * FROM t1 WHERE t1.pk NOT IN (SELECT pk FROM t3 WHERE pk > 2)) t13 JOIN t2 on t13.pk = t2.pk AND t13.pk > 3;
pk	c	pk	c
EXPLAIN SELECT * FROM t1 JOIN t2 on t1.pk = t2.pk WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	<subquery2>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.t2.pk	1	100.00	NULL
2	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t1` join `test`.`t2` semi join (`test`.`t3`) where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`<subquery2>`.`c` = `test`.`t2`.`pk`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM t1 JOIN t2 on t1.pk = t2.pk WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2);
pk	c	pk	c
EXPLAIN SELECT * FROM t1 LEFT JOIN t2 on t1.pk = t2.pk WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	<subquery2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	100.00	Using where
1	SIMPLE	t1	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	<subquery2>.c	1	100.00	NULL
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
2	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`pk` = `test`.`t1`.`pk`)) semi join (`test`.`t3`) where ((`test`.`t1`.`pk` = `<subquery2>`.`c`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM t1 LEFT JOIN t2 on t1.pk = t2.pk WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2);
pk	c	pk	c
EXPLAIN SELECT * FROM t2 LEFT JOIN t1 on t1.pk = t2.pk WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	<subquery2>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.t2.pk	1	100.00	NULL
2	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` join `test`.`t1` semi join (`test`.`t3`) where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`<subquery2>`.`c` = `test`.`t2`.`pk`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM t2 LEFT JOIN t1 on t1.pk = t2.pk WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2);
pk	c	pk	c
EXPLAIN SELECT * FROM t2 JOIN t1 on t1.pk = t2.pk WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	NULL
1	SIMPLE	<subquery2>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.t2.pk	1	100.00	NULL
2	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` join `test`.`t1` semi join (`test`.`t3`) where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`<subquery2>`.`c` = `test`.`t2`.`pk`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM t2 JOIN t1 on t1.pk = t2.pk WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2);
pk	c	pk	c
EXPLAIN SELECT * FROM t2 JOIN (SELECT * FROM t1 WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t13.pk = t2.pk AND t13.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where
1	SIMPLE	<subquery3>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.t2.pk	1	100.00	NULL
3	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` join `test`.`t1` semi join (`test`.`t3`) where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`<subquery3>`.`c` = `test`.`t2`.`pk`) and (`test`.`t1`.`pk` > 3) and (`test`.`t3`.`pk` > 2))
SELECT * FROM t2 JOIN (SELECT * FROM t1 WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t13.pk = t2.pk AND t13.pk > 3;
pk	c	pk	c
EXPLAIN SELECT * FROM t2 LEFT JOIN (SELECT * FROM t1 WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t13.pk = t2.pk AND t13.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where
1	SIMPLE	<subquery3>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.t1.pk	1	100.00	Using where
3	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c` from `test`.`t2` left join (`test`.`t1` semi join (`test`.`t3`)) on(((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`<subquery3>`.`c` = `test`.`t2`.`pk`) and (`test`.`t1`.`pk` > 3) and (`test`.`t3`.`pk` > 2))) where true
SELECT * FROM t2 LEFT JOIN (SELECT * FROM t1 WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t13.pk = t2.pk AND t13.pk > 3;
pk	c	pk	c
1	21	NULL	NULL
2	22	NULL	NULL
3	23	NULL	NULL
4	24	NULL	NULL
5	25	NULL	NULL
6	26	NULL	NULL
7	27	NULL	NULL
8	28	NULL	NULL
9	29	NULL	NULL
10	30	NULL	NULL
EXPLAIN SELECT * FROM (SELECT * FROM t1 WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 LEFT JOIN t2 on t13.pk = t2.pk AND t13.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	<subquery3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	100.00	Using where
1	SIMPLE	t1	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	<subquery3>.c	1	100.00	NULL
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
3	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t1` semi join (`test`.`t3`) left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` > 3))) where ((`test`.`t1`.`pk` = `<subquery3>`.`c`) and (`test`.`t3`.`pk` > 2))
SELECT * FROM (SELECT * FROM t1 WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 LEFT JOIN t2 on t13.pk = t2.pk AND t13.pk > 3;
pk	c	pk	c
EXPLAIN SELECT * FROM (SELECT * FROM t1 WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 JOIN t2 on t13.pk = t2.pk AND t13.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.t2.pk	1	100.00	Using where
1	SIMPLE	<subquery3>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.t2.pk	1	100.00	NULL
3	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t1` semi join (`test`.`t3`) join `test`.`t2` where ((`test`.`t1`.`pk` = `test`.`t2`.`pk`) and (`<subquery3>`.`c` = `test`.`t2`.`pk`) and (`test`.`t1`.`pk` > 3) and (`test`.`t3`.`pk` > 2))
SELECT * FROM (SELECT * FROM t1 WHERE t1.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 JOIN t2 on t13.pk = t2.pk AND t13.pk > 3;
pk	c	pk	c
EXPLAIN SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk) JOIN (t3 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t3.pk = t13.pk) on t1.pk = t3.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2	range	PRIMARY	PRIMARY	4	NULL	2	100.00	Using where
1	SIMPLE	t3	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	tx	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; FirstMatch(tx)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`tx`.`pk` AS `pk`,`test`.`tx`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`pk` = `test`.`t1`.`pk`)) join `test`.`t3` left join (`test`.`t1` `tx` semi join (`test`.`t3`)) on(((`test`.`tx`.`pk` = `test`.`t1`.`pk`) and (`test`.`t3`.`c` = `test`.`t1`.`pk`) and (`test`.`t3`.`pk` > 2))) where ((`test`.`t3`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` < 3))
SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk) JOIN (t3 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t3.pk = t13.pk) on t1.pk = t3.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c
1	1	1	21	1	31	NULL	NULL
2	2	2	22	2	32	NULL	NULL
EXPLAIN SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk) LEFT JOIN (t3 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t3.pk = t13.pk) on t1.pk = t3.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	20	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	t3	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	tx	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	<subquery3>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.tx.pk	1	100.00	Using where
3	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`tx`.`pk` AS `pk`,`test`.`tx`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`pk` = `test`.`t1`.`pk`)) left join (`test`.`t3` left join (`test`.`t1` `tx` semi join (`test`.`t3`)) on(((`test`.`tx`.`pk` = `test`.`t1`.`pk`) and (`<subquery3>`.`c` = `test`.`t1`.`pk`) and (`test`.`t3`.`pk` > 2)))) on(((`test`.`t3`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` < 3))) where true
SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk) LEFT JOIN (t3 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t3.pk = t13.pk) on t1.pk = t3.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c
1	1	1	21	1	31	NULL	NULL
2	2	2	22	2	32	NULL	NULL
3	3	3	23	NULL	NULL	NULL	NULL
4	4	4	24	NULL	NULL	NULL	NULL
5	5	5	25	NULL	NULL	NULL	NULL
6	6	6	26	NULL	NULL	NULL	NULL
7	7	7	27	NULL	NULL	NULL	NULL
8	8	8	28	NULL	NULL	NULL	NULL
9	9	9	29	NULL	NULL	NULL	NULL
10	10	10	30	NULL	NULL	NULL	NULL
11	11	NULL	NULL	NULL	NULL	NULL	NULL
12	12	NULL	NULL	NULL	NULL	NULL	NULL
13	13	NULL	NULL	NULL	NULL	NULL	NULL
14	14	NULL	NULL	NULL	NULL	NULL	NULL
15	15	NULL	NULL	NULL	NULL	NULL	NULL
16	16	NULL	NULL	NULL	NULL	NULL	NULL
17	17	NULL	NULL	NULL	NULL	NULL	NULL
18	18	NULL	NULL	NULL	NULL	NULL	NULL
19	19	NULL	NULL	NULL	NULL	NULL	NULL
20	20	NULL	NULL	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM (t3 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t3.pk = t13.pk) LEFT JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk) on t1.pk = t3.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	tx	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	<subquery3>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.tx.pk	1	100.00	Using where
1	SIMPLE	t1	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	Using where
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
3	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`tx`.`pk` AS `pk`,`test`.`tx`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t3` left join (`test`.`t1` `tx` semi join (`test`.`t3`)) on(((`test`.`tx`.`pk` = `test`.`t3`.`pk`) and (`<subquery3>`.`c` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` > 2))) left join (`test`.`t1` left join `test`.`t2` on((`test`.`t2`.`pk` = `test`.`t3`.`pk`))) on(((`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` < 3))) where true
SELECT * FROM (t3 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t3.pk = t13.pk) LEFT JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk) on t1.pk = t3.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c
1	31	NULL	NULL	1	1	1	21
2	32	NULL	NULL	2	2	2	22
3	33	NULL	NULL	NULL	NULL	NULL	NULL
4	34	NULL	NULL	NULL	NULL	NULL	NULL
5	35	NULL	NULL	NULL	NULL	NULL	NULL
6	36	NULL	NULL	NULL	NULL	NULL	NULL
7	37	NULL	NULL	NULL	NULL	NULL	NULL
8	38	NULL	NULL	NULL	NULL	NULL	NULL
9	39	NULL	NULL	NULL	NULL	NULL	NULL
10	40	NULL	NULL	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM (t3 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t3.pk = t13.pk) JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk) on t1.pk = t3.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	p0,p1,p2	range	PRIMARY	PRIMARY	4	NULL	2	100.00	Using where
1	SIMPLE	t1	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	tx	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t3.pk	1	100.00	NULL
1	SIMPLE	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where; FirstMatch(tx)
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`pk` AS `pk`,`test`.`t3`.`c` AS `c`,`test`.`tx`.`pk` AS `pk`,`test`.`tx`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t3` left join (`test`.`t1` `tx` semi join (`test`.`t3`)) on(((`test`.`tx`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`c` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` > 2))) join `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`pk` = `test`.`t3`.`pk`)) where ((`test`.`t1`.`pk` = `test`.`t3`.`pk`) and (`test`.`t3`.`pk` < 3))
SELECT * FROM (t3 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t3.pk = t13.pk) JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk) on t1.pk = t3.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c
1	31	NULL	NULL	1	1	1	21
2	32	NULL	NULL	2	2	2	22
EXPLAIN SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) JOIN ((t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) LEFT JOIN t4 on t5.c = t4.c + 15) on t1.pk = t13.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tx	p0,p1,p2	range	PRIMARY	PRIMARY	4	NULL	2	100.00	Using where
1	SIMPLE	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	12.50	Using where; FirstMatch(tx); Using join buffer (hash join)
1	SIMPLE	t1	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.tx.pk	1	100.00	NULL
1	SIMPLE	t5	NULL	eq_ref	PRIMARY	PRIMARY	4	test.tx.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.tx.pk	1	100.00	Using where
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`tx`.`pk` AS `pk`,`test`.`tx`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`tx`.`pk`) and (`test`.`tx`.`pk` < 2))) join `test`.`t5` join `test`.`t1` `tx` semi join (`test`.`t3`) left join `test`.`t4` on((`test`.`t5`.`c` = (`test`.`t4`.`c` + 15))) where ((`test`.`t3`.`c` = `test`.`tx`.`pk`) and (`test`.`t1`.`pk` = `test`.`tx`.`pk`) and (`test`.`t5`.`pk` = `test`.`tx`.`pk`) and (`test`.`tx`.`pk` < 3) and (`test`.`t3`.`pk` > 2))
SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) JOIN ((t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) LEFT JOIN t4 on t5.c = t4.c + 15) on t1.pk = t13.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c	pk	c
EXPLAIN SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) LEFT JOIN ((t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) LEFT JOIN t4 on t5.c = t4.c + 15) on t1.pk = t13.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	20	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	t5	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	tx	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	<subquery3>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.tx.pk	1	100.00	Using where
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where
3	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`tx`.`pk` AS `pk`,`test`.`tx`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` < 2))) left join (`test`.`t5` join `test`.`t1` `tx` semi join (`test`.`t3`) left join `test`.`t4` on((`test`.`t5`.`c` = (`test`.`t4`.`c` + 15)))) on(((`test`.`t5`.`pk` = `test`.`t1`.`pk`) and (`test`.`tx`.`pk` = `test`.`t1`.`pk`) and (`<subquery3>`.`c` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` < 3) and (`test`.`t3`.`pk` > 2))) where true
SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) LEFT JOIN ((t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) LEFT JOIN t4 on t5.c = t4.c + 15) on t1.pk = t13.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c	pk	c
1	1	1	21	NULL	NULL	NULL	NULL	NULL	NULL
2	2	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
3	3	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
4	4	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
5	5	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
6	6	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
7	7	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
8	8	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
9	9	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
10	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
11	11	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
12	12	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
13	13	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
14	14	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
15	15	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
16	16	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
17	17	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
18	18	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
19	19	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
20	20	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM ((t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) LEFT JOIN t4 on t5.c = t4.c + 15) LEFT JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) on t1.pk = t13.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t5	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	tx	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	NULL
1	SIMPLE	<subquery3>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.tx.pk	1	100.00	Using where
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
1	SIMPLE	t1	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.tx.pk	1	100.00	Using where
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
3	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`tx`.`pk` AS `pk`,`test`.`tx`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t5` left join (`test`.`t1` `tx` semi join (`test`.`t3`)) on(((`test`.`tx`.`pk` = `test`.`t5`.`pk`) and (`<subquery3>`.`c` = `test`.`t5`.`pk`) and (`test`.`t3`.`pk` > 2))) left join `test`.`t4` on((`test`.`t5`.`c` = (`test`.`t4`.`c` + 15))) left join (`test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`tx`.`pk`) and (`test`.`tx`.`pk` < 2)))) on(((`test`.`t1`.`pk` = `test`.`tx`.`pk`) and (`test`.`tx`.`pk` < 3))) where true
SELECT * FROM ((t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) LEFT JOIN t4 on t5.c = t4.c + 15) LEFT JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) on t1.pk = t13.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c	pk	c
1	51	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
2	52	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
3	53	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
4	54	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
5	55	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
6	56	NULL	NULL	1	41	NULL	NULL	NULL	NULL
7	57	NULL	NULL	2	42	NULL	NULL	NULL	NULL
8	58	NULL	NULL	3	43	NULL	NULL	NULL	NULL
9	59	NULL	NULL	4	44	NULL	NULL	NULL	NULL
10	60	NULL	NULL	5	45	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM ((t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) LEFT JOIN t4 on t5.c = t4.c + 15) JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) on t1.pk = t13.pk AND t1.pk < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tx	p0,p1,p2	range	PRIMARY	PRIMARY	4	NULL	2	100.00	Using where
1	SIMPLE	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	12.50	Using where; FirstMatch(tx); Using join buffer (hash join)
1	SIMPLE	t5	NULL	eq_ref	PRIMARY	PRIMARY	4	test.tx.pk	1	100.00	NULL
1	SIMPLE	t1	p0,p1,p2	eq_ref	PRIMARY	PRIMARY	4	test.tx.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.tx.pk	1	100.00	Using where
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`tx`.`pk` AS `pk`,`test`.`tx`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t5` join `test`.`t1` `tx` semi join (`test`.`t3`) left join `test`.`t4` on((`test`.`t5`.`c` = (`test`.`t4`.`c` + 15))) join `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`tx`.`pk`) and (`test`.`tx`.`pk` < 2))) where ((`test`.`t3`.`c` = `test`.`tx`.`pk`) and (`test`.`t5`.`pk` = `test`.`tx`.`pk`) and (`test`.`t1`.`pk` = `test`.`tx`.`pk`) and (`test`.`tx`.`pk` < 3) and (`test`.`t3`.`pk` > 2))
SELECT * FROM ((t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) LEFT JOIN t4 on t5.c = t4.c + 15) JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk < 2) on t1.pk = t13.pk AND t1.pk < 3;
pk	c	pk	c	pk	c	pk	c	pk	c
EXPLAIN SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) JOIN (t4 LEFT JOIN (t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) on t5.c = t4.c + 20) on t1.pk = t13.pk AND t1.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t5	NULL	range	PRIMARY	PRIMARY	4	NULL	7	100.00	Using where
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	NULL
1	SIMPLE	tx	p4	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	Using where
1	SIMPLE	<subquery3>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.t5.pk	1	100.00	NULL
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
3	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`tx`.`pk` AS `pk`,`test`.`tx`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t5`.`pk`) and (`test`.`t5`.`pk` > 3))) join `test`.`t4` join `test`.`t5` join `test`.`t1` `tx` semi join (`test`.`t3`) where ((`test`.`t1`.`pk` = `test`.`t5`.`pk`) and (`test`.`tx`.`pk` = `test`.`t5`.`pk`) and (`<subquery3>`.`c` = `test`.`t5`.`pk`) and (`test`.`t5`.`pk` > 3) and (`test`.`t3`.`pk` > 2) and (`test`.`t5`.`c` = (`test`.`t4`.`c` + 20)))
SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) JOIN (t4 LEFT JOIN (t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) on t5.c = t4.c + 20) on t1.pk = t13.pk AND t1.pk > 3;
pk	c	pk	c	pk	c	pk	c	pk	c
EXPLAIN SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) LEFT JOIN (t4 LEFT JOIN (t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) on t5.c = t4.c + 20) on t1.pk = t13.pk AND t1.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	20	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	t5	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
1	SIMPLE	tx	p4	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	NULL
1	SIMPLE	<subquery3>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.tx.pk	1	100.00	Using where
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where
3	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`tx`.`pk` AS `pk`,`test`.`tx`.`c` AS `c` from `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` > 3))) left join (`test`.`t4` join `test`.`t5` join `test`.`t1` `tx` semi join (`test`.`t3`)) on(((`test`.`t5`.`pk` = `test`.`t1`.`pk`) and (`test`.`tx`.`pk` = `test`.`t1`.`pk`) and (`<subquery3>`.`c` = `test`.`t1`.`pk`) and (`test`.`t1`.`pk` > 3) and (`test`.`t3`.`pk` > 2) and (`test`.`t5`.`c` = (`test`.`t4`.`c` + 20)))) where true
SELECT * FROM (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) LEFT JOIN (t4 LEFT JOIN (t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) on t5.c = t4.c + 20) on t1.pk = t13.pk AND t1.pk > 3;
pk	c	pk	c	pk	c	pk	c	pk	c
1	1	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
2	2	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
3	3	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
4	4	4	24	NULL	NULL	NULL	NULL	NULL	NULL
5	5	5	25	NULL	NULL	NULL	NULL	NULL	NULL
6	6	6	26	NULL	NULL	NULL	NULL	NULL	NULL
7	7	7	27	NULL	NULL	NULL	NULL	NULL	NULL
8	8	8	28	NULL	NULL	NULL	NULL	NULL	NULL
9	9	9	29	NULL	NULL	NULL	NULL	NULL	NULL
10	10	10	30	NULL	NULL	NULL	NULL	NULL	NULL
11	11	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
12	12	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
13	13	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
14	14	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
15	15	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
16	16	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
17	17	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
18	18	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
19	19	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
20	20	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM (t4 LEFT JOIN (t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) on t5.c = t4.c + 20) LEFT JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) on t1.pk = t13.pk AND t1.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
1	SIMPLE	t5	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where
1	SIMPLE	tx	p0,p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	4	test.t5.pk	1	100.00	NULL
1	SIMPLE	<subquery3>	NULL	eq_ref	<auto_distinct_key>	<auto_distinct_key>	5	test.tx.pk	1	100.00	Using where
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	test.tx.pk	1	100.00	Using where
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.pk	1	100.00	Using where
3	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`tx`.`pk` AS `pk`,`test`.`tx`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t4` left join (`test`.`t5` left join (`test`.`t1` `tx` semi join (`test`.`t3`)) on(((`test`.`tx`.`pk` = `test`.`t5`.`pk`) and (`<subquery3>`.`c` = `test`.`t5`.`pk`) and (`test`.`t3`.`pk` > 2)))) on((`test`.`t5`.`c` = (`test`.`t4`.`c` + 20))) left join (`test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`tx`.`pk`) and (`test`.`tx`.`pk` > 3)))) on(((`test`.`t1`.`pk` = `test`.`tx`.`pk`) and (`test`.`tx`.`pk` > 3))) where true
SELECT * FROM (t4 LEFT JOIN (t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) on t5.c = t4.c + 20) LEFT JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) on t1.pk = t13.pk AND t1.pk > 3;
pk	c	pk	c	pk	c	pk	c	pk	c
1	41	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
2	42	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
3	43	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
4	44	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
5	45	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
6	46	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
7	47	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
8	48	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
9	49	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
10	50	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
EXPLAIN SELECT * FROM (t4 LEFT JOIN (t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) on t5.c = t4.c + 20) JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) on t1.pk = t13.pk AND t1.pk > 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	<subquery3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	100.00	Using where
1	SIMPLE	t5	NULL	eq_ref	PRIMARY	PRIMARY	4	<subquery3>.c	1	100.00	NULL
1	SIMPLE	tx	p4	eq_ref	PRIMARY	PRIMARY	4	<subquery3>.c	1	100.00	NULL
1	SIMPLE	t1	p4	eq_ref	PRIMARY	PRIMARY	4	<subquery3>.c	1	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	<subquery3>.c	1	100.00	Using where
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where; Using join buffer (hash join)
3	MATERIALIZED	t3	p3,p4	range	PRIMARY	PRIMARY	4	NULL	8	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t4`.`pk` AS `pk`,`test`.`t4`.`c` AS `c`,`test`.`t5`.`pk` AS `pk`,`test`.`t5`.`c` AS `c`,`test`.`tx`.`pk` AS `pk`,`test`.`tx`.`c` AS `c`,`test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c` AS `c` from `test`.`t4` join `test`.`t5` join `test`.`t1` `tx` semi join (`test`.`t3`) join `test`.`t1` left join `test`.`t2` on(((`test`.`t2`.`pk` = `test`.`t5`.`pk`) and (`test`.`t5`.`pk` > 3))) where ((`test`.`t5`.`pk` = `<subquery3>`.`c`) and (`test`.`tx`.`pk` = `<subquery3>`.`c`) and (`test`.`t1`.`pk` = `<subquery3>`.`c`) and (`<subquery3>`.`c` > 3) and (`test`.`t3`.`pk` > 2) and (`test`.`t5`.`c` = (`test`.`t4`.`c` + 20)))
SELECT * FROM (t4 LEFT JOIN (t5 LEFT JOIN (SELECT * FROM t1 as tx WHERE tx.pk IN (SELECT c FROM t3 WHERE pk > 2)) t13 on t5.pk = t13.pk) on t5.c = t4.c + 20) JOIN (t1 LEFT JOIN t2 on t1.pk = t2.pk and t1.pk > 3) on t1.pk = t13.pk AND t1.pk > 3;
pk	c	pk	c	pk	c	pk	c	pk	c
DROP TABLE t1, t2, t3, t4, t5;
#
# Bug #34801284 partitions: MySQL is trying to perform a consistent read but the read view is not assigned
#
CREATE TABLE t1 (c0 int);
CREATE TABLE t2 (c1 int, c2 int) PARTITION BY KEY (c1) PARTITIONS 4;
SELECT * from t2 WHERE c2 IN ((SELECT c0 FROM t1 LIMIT 1),null);
c1	c2
SELECT * from t2 WHERE c2 NOT IN ((SELECT c0 FROM t1 LIMIT 1),null);
c1	c2
DROP TABLE t1, t2;
#
# Bug#37397306 - looks like not use pruning when it inserts with now() function in 8.0
#
CREATE TABLE t1 (
f1 DATETIME NOT NULL,
f2 INT NOT NULL,
f3 DATE NOT NULL,
PRIMARY KEY (f1,f2)
)
PARTITION BY RANGE COLUMNS(f1)
(
PARTITION p01 VALUES LESS THAN ('1975-12-31 10:21:55'),
PARTITION p02 VALUES LESS THAN ('1976-12-31 10:21:55'),
PARTITION p03 VALUES LESS THAN ('2090-12-31 10:21:55')
);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SET TIMESTAMP=UNIX_TIMESTAMP('2019-03-11 12:00:00');
EXPLAIN INSERT INTO t1 (f1,f2,f3) VALUES (now(),10001,'1976-11-31');
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	p03	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` (`test`.`t1`.`f1`,`test`.`t1`.`f2`,`test`.`t1`.`f3`) values (now(),10001,'1976-11-31')
SET TIMESTAMP=UNIX_TIMESTAMP('1975-03-11 12:00:00');
EXPLAIN INSERT INTO t1 (f1,f2,f3) VALUES (now(),10001,'2000-11-31');
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	p01	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` (`test`.`t1`.`f1`,`test`.`t1`.`f2`,`test`.`t1`.`f3`) values (now(),10001,'2000-11-31')
SET TIMESTAMP=DEFAULT;
DROP TABLE t1;
CREATE TABLE t1(a INTEGER PRIMARY KEY, b INTEGER)
PARTITION BY RANGE (a)
(
PARTITION p1 VALUES LESS THAN (10),
PARTITION p2 VALUES LESS THAN MAXVALUE
);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
set @a=1,@b=22;
EXPLAIN INSERT INTO t1(a,b) VALUES (@a,@b);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	p1	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` (`test`.`t1`.`a`,`test`.`t1`.`b`) values ((@`a`),(@`b`))
EXPLAIN INSERT INTO t1 VALUES (@a, 1), (@b, 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	p1,p2	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` values ((@`a`),1),((@`b`),2)
set @a=11,@b=22;
EXPLAIN INSERT INTO t1(a,b) VALUES (@a,@b);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	p2	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` (`test`.`t1`.`a`,`test`.`t1`.`b`) values ((@`a`),(@`b`))
EXPLAIN INSERT INTO t1 VALUES (@a, 1), (@b, 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	p2	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` values ((@`a`),1),((@`b`),2)
PREPARE s from "EXPLAIN INSERT INTO t1(a,b) VALUES (?,?)";
set @a=1,@b=22;
EXECUTE s USING @a,@b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	p1	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` (`test`.`t1`.`a`,`test`.`t1`.`b`) values (?,?)
set @a=11,@b=22;
EXECUTE s USING @a,@b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	p2	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` (`test`.`t1`.`a`,`test`.`t1`.`b`) values (?,?)
PREPARE p from "EXPLAIN INSERT INTO t1(a,b) VALUES (?,1), (?,2)";
set @a=1,@b=22;
EXECUTE p USING @a,@b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	p1,p2	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` (`test`.`t1`.`a`,`test`.`t1`.`b`) values (?,1),(?,2)
set @a=11,@b=22;
EXECUTE p USING @a,@b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	p2	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` (`test`.`t1`.`a`,`test`.`t1`.`b`) values (?,1),(?,2)
DROP TABLE t1;
CREATE TABLE t1 (
id INT PRIMARY KEY,
f1 FLOAT
)
PARTITION BY RANGE (id)
(
PARTITION p0 VALUES LESS THAN (10),
PARTITION p1 VALUES LESS THAN MAXVALUE
);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN INSERT INTO t1 (id, f1) VALUES (1,RAND());
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	p0	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` (`test`.`t1`.`id`,`test`.`t1`.`f1`) values (1,rand())
EXPLAIN INSERT INTO t1  VALUES (20,RAND());
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	p1	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` values (20,rand())
DROP TABLE t1;
