DROP TABLE IF EXISTS t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
5	10
UPDATE t2 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	50
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
4	10
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
5	10
UPDATE t2 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	50
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
4	10
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
5	10
UPDATE t2 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	50
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
4	10
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
5	10
UPDATE t2 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	50
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
4	10
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
5	10
UPDATE t2 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	50
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
4	10
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
5	10
UPDATE t2 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	50
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
4	10
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 < 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
6	15
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	51
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
5	15
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
6	15
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	51
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
5	15
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
6	15
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	51
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
5	15
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
6	15
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	51
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
5	15
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
6	15
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	51
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
5	15
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
6	15
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	51
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
5	15
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <= 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t2 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	46
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t2 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	46
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t2 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	46
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t2 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	46
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t2 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	46
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t2 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	46
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
5	35
UPDATE t2 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	50
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
5	40
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
5	35
UPDATE t2 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	50
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
5	40
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
5	35
UPDATE t2 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	50
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
5	40
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
5	35
UPDATE t2 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	50
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
5	40
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
5	35
UPDATE t2 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	50
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
5	40
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
5	35
UPDATE t2 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	50
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
5	40
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
1	6
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
1	6
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
1	6
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
1	6
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
1	6
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 >= 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
COUNT(c1)	SUM(c1)
1	6
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
9	40
UPDATE t2 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	54
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
8	44
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
9	40
UPDATE t2 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	54
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
8	44
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
9	40
UPDATE t2 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	54
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
8	44
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
9	40
UPDATE t2 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	54
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
8	44
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
9	40
UPDATE t2 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	54
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
8	44
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
9	40
UPDATE t2 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	54
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
8	44
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 != 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
9	40
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	54
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
8	44
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
9	40
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	54
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
8	44
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
9	40
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	54
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
8	44
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
9	40
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	54
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
8	44
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
9	40
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	54
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
8	44
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
9	40
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	54
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
8	44
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <> 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
UPDATE t1 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
COUNT(c1)	SUM(c1)
0	NULL
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	46
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	46
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	46
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	46
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	46
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	45
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t2 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
COUNT(c1)	SUM(c1)
10	46
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	5
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
1	5
UPDATE t3 SET c1 = c1 + 1 WHERE c1 <=> 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
COUNT(c1)	SUM(c1)
1	6
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
COUNT(c1)	SUM(c1)
0	NULL
DROP TABLE t1,t2,t3;
