# ----------------------------------------------------------------------
# Setup
# Creating local configuration file for keyring component: component_keyring_file
# Creating manifest file for current MySQL server instance
# Re-starting mysql server with manifest file
# ----------------------------------------------------------------------
#
# Bug#31313533 : IMPORT FAILS FOR ENCRYPT AND COMPRESSION ENABLED TDE TABLES
#
# SETUP
#
# Try to create a table with ZLib compression with strict mode OFF.
# If there is an error about Punch Hole not supported, make InnoDB
# think that <PERSON> Hole is working but actually ignore the calls.
#
CREATE TABLE t1(c1 INT PRIMARY KEY) COMPRESSION="ZLIB";
DROP TABLE t1;

# Test 1 : Check that <PERSON>XPORT and IMPOR<PERSON> is working fine on same FS

CREATE TABLE t1(c1 int NOT NULL AUTO_INCREMENT,
c2 varchar(65000) DEFAULT NULL,
c3 varchar(255) GENERATED ALWAYS AS (substr(c2,2,100)) STORED,
c4 varchar(255) GENERATED ALWAYS AS (substr(c2,10,200)) VIRTUAL,
b bit(64) DEFAULT NULL,
p_c1 bigint DEFAULT NULL,
PRIMARY KEY (c1)) ENGINE=Innodb AUTO_INCREMENT=50001 DEFAULT CHARSET=latin1 COMPRESSION='zlib' ENCRYPTION='Y';
SELECT c1, HEX(SUBSTRING(c2, 10, 10)), HEX(SUBSTRING(c3, 10, 10)),
HEX(SUBSTRING(c4, 10, 10)), HEX(b)
FROM t1 ORDER BY c1 limit 10;
c1	HEX(SUBSTRING(c2, 10, 10))	HEX(SUBSTRING(c3, 10, 10))	HEX(SUBSTRING(c4, 10, 10))	HEX(b)
50001	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50002	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50003	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50004	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50005	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50006	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50007	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50008	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50009	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50010	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
# Flush tables for export
FLUSH TABLES t1 FOR EXPORT;
# Copy .cfp .cfg .ibd file to temp
UNLOCK TABLES;
DROP TABLE t1;
CREATE TABLE t1(c1 int NOT NULL AUTO_INCREMENT,
c2 varchar(65000) DEFAULT NULL,
c3 varchar(255) GENERATED ALWAYS AS (substr(c2,2,100)) STORED,
c4 varchar(255) GENERATED ALWAYS AS (substr(c2,10,200)) VIRTUAL,
b bit(64) DEFAULT NULL,
p_c1 bigint DEFAULT NULL,
PRIMARY KEY (c1)) ENGINE=Innodb AUTO_INCREMENT=50001 DEFAULT CHARSET=latin1 COMPRESSION='zlib' ENCRYPTION='Y';
ALTER TABLE t1 DISCARD TABLESPACE;
# Copy .cfp/.cfg and .ibd files from temp to datadir
# Start import
ALTER TABLE t1 IMPORT TABLESPACE;
SELECT c1, HEX(SUBSTRING(c2, 10, 10)), HEX(SUBSTRING(c3, 10, 10)),
HEX(SUBSTRING(c4, 10, 10)), HEX(b) FROM t1 ORDER BY c1 limit 10;
c1	HEX(SUBSTRING(c2, 10, 10))	HEX(SUBSTRING(c3, 10, 10))	HEX(SUBSTRING(c4, 10, 10))	HEX(b)
50001	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50002	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50003	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50004	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50005	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50006	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50007	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50008	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50009	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
50010	51AB8660A7143704DEA7	AB8660A7143704DEA78C	A78C87578839E440B5B3	A
# Cleanup
DROP TABLE t1;

# Test 2 : Check that EXPORT and IMPORT is working fine on different FS

# Copy and unzip the dir having cfg/cfg/ibd file from a different FS Block Size
CREATE TABLE t1(c1 int NOT NULL AUTO_INCREMENT,
c2 varchar(65000) DEFAULT NULL,
c3 varchar(255) GENERATED ALWAYS AS (substr(c2,2,100)) STORED,
c4 varchar(255) GENERATED ALWAYS AS (substr(c2,10,200)) VIRTUAL,
b bit(64) DEFAULT NULL,
p_c1 bigint DEFAULT NULL,
PRIMARY KEY (c1)) ENGINE=Innodb AUTO_INCREMENT=50001 DEFAULT CHARSET=latin1 COMPRESSION='zlib' ENCRYPTION='Y';
ALTER TABLE t1 DISCARD TABLESPACE;
# Copy .cfp/.cfg and .ibd files from temp to datadir
# Start import
ALTER TABLE t1 IMPORT TABLESPACE;
SELECT c1, HEX(SUBSTRING(c2, 10, 10)), HEX(SUBSTRING(c3, 10, 10)),
HEX(SUBSTRING(c4, 10, 10)), HEX(b) FROM t1 ORDER BY c1 limit 10;
c1	HEX(SUBSTRING(c2, 10, 10))	HEX(SUBSTRING(c3, 10, 10))	HEX(SUBSTRING(c4, 10, 10))	HEX(b)
50001	066C8ADC3EC72175CA77	6C8ADC3EC72175CA77CE	77CEF1FBCA5BFE739000	A
50002	066C8ADC3EC72175CA77	6C8ADC3EC72175CA77CE	77CEF1FBCA5BFE739000	A
50003	066C8ADC3EC72175CA77	6C8ADC3EC72175CA77CE	77CEF1FBCA5BFE739000	A
50004	066C8ADC3EC72175CA77	6C8ADC3EC72175CA77CE	77CEF1FBCA5BFE739000	A
50005	066C8ADC3EC72175CA77	6C8ADC3EC72175CA77CE	77CEF1FBCA5BFE739000	A
50006	066C8ADC3EC72175CA77	6C8ADC3EC72175CA77CE	77CEF1FBCA5BFE739000	A
50007	066C8ADC3EC72175CA77	6C8ADC3EC72175CA77CE	77CEF1FBCA5BFE739000	A
50008	066C8ADC3EC72175CA77	6C8ADC3EC72175CA77CE	77CEF1FBCA5BFE739000	A
50009	066C8ADC3EC72175CA77	6C8ADC3EC72175CA77CE	77CEF1FBCA5BFE739000	A
50010	066C8ADC3EC72175CA77	6C8ADC3EC72175CA77CE	77CEF1FBCA5BFE739000	A
# Cleanup
DROP TABLE t1;
# Remove copied files
# ----------------------------------------------------------------------
# Teardown
# Removing manifest file for current MySQL server instance
# Removing local keyring file for keyring component: component_keyring_file
# Removing local configuration file for keyring component: component_keyring_file
# Restarting server without the manifest file
# ----------------------------------------------------------------------
