Warnings:
Warning	1681	'FLUSH PRIVILEGES' is deprecated and will be removed in a future release.
update performance_schema.setup_instruments
set enabled='NO' where name='wait/lock/table/sql/handler';
select * from performance_schema.setup_actors
order by USER, HOST, `ROLE`;
HOST	USER	ROLE	ENABLED	HISTORY
localhost	user1	%	YES	YES
localhost	user2	%	YES	YES
localhost	user3	%	YES	YES
localhost	user4	%	YES	YES
select * from performance_schema.setup_objects
order by object_type, object_schema, object_name;
OBJECT_TYPE	OBJECT_SCHEMA	OBJECT_NAME	ENABLED	TIMED
TABLE	test	t1	YES	YES
TABLE	test	t2	YES	NO
TABLE	test	t3	YES	NO
select * from performance_schema.setup_consumers;
NAME	ENABLED
events_stages_current	YES
events_stages_history	YES
events_stages_history_long	YES
events_statements_cpu	YES
events_statements_current	YES
events_statements_history	YES
events_statements_history_long	YES
events_transactions_current	YES
events_transactions_history	YES
events_transactions_history_long	YES
events_waits_current	YES
events_waits_history	YES
events_waits_history_long	YES
global_instrumentation	YES
thread_instrumentation	YES
statements_digest	YES
flush tables;
truncate performance_schema.objects_summary_global_by_type;
truncate performance_schema.table_io_waits_summary_by_index_usage;
truncate performance_schema.table_io_waits_summary_by_table;
truncate performance_schema.table_lock_waits_summary_by_table;
truncate performance_schema.events_waits_summary_by_thread_by_event_name;
truncate performance_schema.events_waits_summary_by_account_by_event_name;
truncate performance_schema.events_waits_summary_by_user_by_event_name;
truncate performance_schema.events_waits_summary_by_host_by_event_name;
truncate performance_schema.events_waits_summary_global_by_event_name;
truncate performance_schema.events_waits_history_long;
show global variables like "performance_schema%table%";
Variable_name	Value
performance_schema_max_table_handles	-1
performance_schema_max_table_instances	-1
performance_schema_max_table_lock_stat	-1
show global status like "performance_schema%table%_lost";
Variable_name	Value
Performance_schema_table_handles_lost	0
Performance_schema_table_instances_lost	0
Performance_schema_table_lock_stat_lost	0
"================== Step 1 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	0
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	0
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	index_b	0	0	0	0	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	0	0	0	0	0	0	0
TABLE	test	t2	index_b	0	0	0	0	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	0	0	0	0	0	0	0
TABLE	test	t3	index_b	0	0	0	0	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	0	0	0	0	0	0	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	0
TABLE	test	t2	0
TABLE	test	t3	0
select concat(current_user(), " is connected") as status;
status
user1@localhost is connected
"================== Step 2 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/table/sql/handler	0
user1	wait/lock/table/sql/handler	0
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	0
user1	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	0
user1	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	0
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	0
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	index_b	0	0	0	0	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	0	0	0	0	0	0	0
TABLE	test	t2	index_b	0	0	0	0	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	0	0	0	0	0	0	0
TABLE	test	t3	index_b	0	0	0	0	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	0	0	0	0	0	0	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	0
TABLE	test	t2	0
TABLE	test	t3	0
insert into test.t1 set a=101, b=1, c=1;
insert into test.t2 set a=102, b=2, c=2;
insert into test.t2 set a=103, b=3, c=3;
insert into test.t3 set a=104, b=4, c=4;
insert into test.t3 set a=105, b=5, c=5;
insert into test.t3 set a=106, b=6, c=6;
select * from test.t1;
a	b	c	d
101	1	1	0
select * from test.t2;
a	b	c	d
102	2	2	0
103	3	3	0
select * from test.t3;
a	b	c	d
104	4	4	0
105	5	5	0
106	6	6	0
update test.t1 set d=d+1;
update test.t2 set d=d+1;
update test.t3 set d=d+1;
update test.t1 set d=d+1 where a=101;
update test.t2 set d=d+1 where a=101;
update test.t3 set d=d+1 where a=101;
select * from test.t1 force index(index_b) where b=5;
a	b	c	d
select * from test.t2 force index(index_b) where b=5;
a	b	c	d
select * from test.t3 force index(index_b) where b=5;
a	b	c	d
105	5	5	1
"================== con1 marker =================="
"================== Step 3-A =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	34
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	34
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	8	TABLE	test	t1
wait/io/table/sql/handler	11	TABLE	test	t2
wait/io/table/sql/handler	15	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	4	3	1	3	1	0	0
TABLE	test	t1	index_b	1	1	0	1	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	3	1	2	1	0	2	0
TABLE	test	t2	NULL	7	5	2	5	2	0	0
TABLE	test	t2	index_b	1	1	0	1	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	3	1	2	1	0	2	0
TABLE	test	t3	NULL	10	7	3	7	3	0	0
TABLE	test	t3	index_b	1	1	0	1	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	4	1	3	1	0	3	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	8	5	3	5	1	2	0
TABLE	test	t2	11	7	4	7	2	2	0
TABLE	test	t3	15	9	6	9	3	3	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	8
TABLE	test	t2	11
TABLE	test	t3	15
flush tables;
"================== con1 FLUSH =================="
"================== Step 3-B =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	34
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	34
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	8	TABLE	test	t1
wait/io/table/sql/handler	11	TABLE	test	t2
wait/io/table/sql/handler	15	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	4	3	1	3	1	0	0
TABLE	test	t1	index_b	1	1	0	1	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	3	1	2	1	0	2	0
TABLE	test	t2	NULL	7	5	2	5	2	0	0
TABLE	test	t2	index_b	1	1	0	1	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	3	1	2	1	0	2	0
TABLE	test	t3	NULL	10	7	3	7	3	0	0
TABLE	test	t3	index_b	1	1	0	1	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	4	1	3	1	0	3	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	8	5	3	5	1	2	0
TABLE	test	t2	11	7	4	7	2	2	0
TABLE	test	t3	15	9	6	9	3	3	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	8
TABLE	test	t2	11
TABLE	test	t3	15
select concat(current_user(), " is connected") as status;
status
user2@localhost is connected
"================== Step 4 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
username	event_name	count_star
user2	wait/io/table/sql/handler	0
user2	wait/lock/table/sql/handler	0
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	0
user2	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	0
user2	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	34
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	34
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	8	TABLE	test	t1
wait/io/table/sql/handler	11	TABLE	test	t2
wait/io/table/sql/handler	15	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	4	3	1	3	1	0	0
TABLE	test	t1	index_b	1	1	0	1	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	3	1	2	1	0	2	0
TABLE	test	t2	NULL	7	5	2	5	2	0	0
TABLE	test	t2	index_b	1	1	0	1	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	3	1	2	1	0	2	0
TABLE	test	t3	NULL	10	7	3	7	3	0	0
TABLE	test	t3	index_b	1	1	0	1	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	4	1	3	1	0	3	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	8	5	3	5	1	2	0
TABLE	test	t2	11	7	4	7	2	2	0
TABLE	test	t3	15	9	6	9	3	3	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	8
TABLE	test	t2	11
TABLE	test	t3	15
insert into test.t1 set a=201, b=1, c=1;
insert into test.t2 set a=202, b=2, c=2;
insert into test.t2 set a=203, b=3, c=3;
insert into test.t3 set a=204, b=4, c=4;
insert into test.t3 set a=205, b=5, c=5;
insert into test.t3 set a=206, b=6, c=6;
select * from test.t1;
a	b	c	d
101	1	1	2
201	1	1	0
select * from test.t2;
a	b	c	d
102	2	2	1
103	3	3	1
202	2	2	0
203	3	3	0
select * from test.t3;
a	b	c	d
104	4	4	1
105	5	5	1
106	6	6	1
204	4	4	0
205	5	5	0
206	6	6	0
update test.t1 set d=d+1;
update test.t2 set d=d+1;
update test.t3 set d=d+1;
update test.t1 set d=d+1 where a=201;
update test.t2 set d=d+1 where a=201;
update test.t3 set d=d+1 where a=201;
select * from test.t1 force index(index_b) where b=5;
a	b	c	d
select * from test.t2 force index(index_b) where b=5;
a	b	c	d
select * from test.t3 force index(index_b) where b=5;
a	b	c	d
105	5	5	2
205	5	5	1
"================== con2 marker =================="
"================== Step 5 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
username	event_name	count_star
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	53
user2	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	87
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	87
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	19	TABLE	test	t1
wait/io/table/sql/handler	28	TABLE	test	t2
wait/io/table/sql/handler	40	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	10	8	2	8	2	0	0
TABLE	test	t1	index_b	2	2	0	2	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	7	2	5	2	0	5	0
TABLE	test	t2	NULL	18	14	4	14	4	0	0
TABLE	test	t2	index_b	2	2	0	2	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	8	2	6	2	0	6	0
TABLE	test	t3	NULL	26	20	6	20	6	0	0
TABLE	test	t3	index_b	3	3	0	3	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	11	2	9	2	0	9	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	19	12	7	12	2	5	0
TABLE	test	t2	28	18	10	18	4	6	0
TABLE	test	t3	40	25	15	25	6	9	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	19
TABLE	test	t2	28
TABLE	test	t3	40
select concat(current_user(), " is connected") as status;
status
user3@localhost is connected
"================== Step 6 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
username	event_name	count_star
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
username	event_name	count_star
user3	wait/io/table/sql/handler	0
user3	wait/lock/table/sql/handler	0
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	53
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	0
user3	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	0
user3	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	87
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	87
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	19	TABLE	test	t1
wait/io/table/sql/handler	28	TABLE	test	t2
wait/io/table/sql/handler	40	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	10	8	2	8	2	0	0
TABLE	test	t1	index_b	2	2	0	2	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	7	2	5	2	0	5	0
TABLE	test	t2	NULL	18	14	4	14	4	0	0
TABLE	test	t2	index_b	2	2	0	2	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	8	2	6	2	0	6	0
TABLE	test	t3	NULL	26	20	6	20	6	0	0
TABLE	test	t3	index_b	3	3	0	3	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	11	2	9	2	0	9	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	19	12	7	12	2	5	0
TABLE	test	t2	28	18	10	18	4	6	0
TABLE	test	t3	40	25	15	25	6	9	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	19
TABLE	test	t2	28
TABLE	test	t3	40
insert into test.t1 set a=301, b=1, c=1;
insert into test.t2 set a=302, b=2, c=2;
insert into test.t2 set a=303, b=3, c=3;
insert into test.t3 set a=304, b=4, c=4;
insert into test.t3 set a=305, b=5, c=5;
insert into test.t3 set a=306, b=6, c=6;
select * from test.t1;
a	b	c	d
101	1	1	3
201	1	1	2
301	1	1	0
select * from test.t2;
a	b	c	d
102	2	2	2
103	3	3	2
202	2	2	1
203	3	3	1
302	2	2	0
303	3	3	0
select * from test.t3;
a	b	c	d
104	4	4	2
105	5	5	2
106	6	6	2
204	4	4	1
205	5	5	1
206	6	6	1
304	4	4	0
305	5	5	0
306	6	6	0
update test.t1 set d=d+1;
update test.t2 set d=d+1;
update test.t3 set d=d+1;
update test.t1 set d=d+1 where a=301;
update test.t2 set d=d+1 where a=301;
update test.t3 set d=d+1 where a=301;
select * from test.t1 force index(index_b) where b=5;
a	b	c	d
select * from test.t2 force index(index_b) where b=5;
a	b	c	d
select * from test.t3 force index(index_b) where b=5;
a	b	c	d
105	5	5	3
205	5	5	2
305	5	5	1
"================== con3 marker =================="
"================== Step 7 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
username	event_name	count_star
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
username	event_name	count_star
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	53
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	72
user3	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	159
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	159
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	33	TABLE	test	t1
wait/io/table/sql/handler	51	TABLE	test	t2
wait/io/table/sql/handler	75	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	18	15	3	15	3	0	0
TABLE	test	t1	index_b	3	3	0	3	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	12	3	9	3	0	9	0
TABLE	test	t2	NULL	33	27	6	27	6	0	0
TABLE	test	t2	index_b	3	3	0	3	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	15	3	12	3	0	12	0
TABLE	test	t3	NULL	48	39	9	39	9	0	0
TABLE	test	t3	index_b	6	6	0	6	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	21	3	18	3	0	18	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	33	21	12	21	3	9	0
TABLE	test	t2	51	33	18	33	6	12	0
TABLE	test	t3	75	48	27	48	9	18	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	33
TABLE	test	t2	51
TABLE	test	t3	75
select concat(current_user(), " is connected") as status;
status
user4@localhost is connected
"================== Step 8 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
username	event_name	count_star
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
username	event_name	count_star
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
username	event_name	count_star
user4	wait/io/table/sql/handler	0
user4	wait/lock/table/sql/handler	0
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	53
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	72
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	0
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	0
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	159
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	159
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	33	TABLE	test	t1
wait/io/table/sql/handler	51	TABLE	test	t2
wait/io/table/sql/handler	75	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	18	15	3	15	3	0	0
TABLE	test	t1	index_b	3	3	0	3	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	12	3	9	3	0	9	0
TABLE	test	t2	NULL	33	27	6	27	6	0	0
TABLE	test	t2	index_b	3	3	0	3	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	15	3	12	3	0	12	0
TABLE	test	t3	NULL	48	39	9	39	9	0	0
TABLE	test	t3	index_b	6	6	0	6	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	21	3	18	3	0	18	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	33	21	12	21	3	9	0
TABLE	test	t2	51	33	18	33	6	12	0
TABLE	test	t3	75	48	27	48	9	18	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	33
TABLE	test	t2	51
TABLE	test	t3	75
insert into test.t1 set a=401, b=1, c=1;
insert into test.t2 set a=402, b=2, c=2;
insert into test.t2 set a=403, b=3, c=3;
insert into test.t3 set a=404, b=4, c=4;
insert into test.t3 set a=405, b=5, c=5;
insert into test.t3 set a=406, b=6, c=6;
select * from test.t1;
a	b	c	d
101	1	1	4
201	1	1	3
301	1	1	2
401	1	1	0
select * from test.t2;
a	b	c	d
102	2	2	3
103	3	3	3
202	2	2	2
203	3	3	2
302	2	2	1
303	3	3	1
402	2	2	0
403	3	3	0
select * from test.t3;
a	b	c	d
104	4	4	3
105	5	5	3
106	6	6	3
204	4	4	2
205	5	5	2
206	6	6	2
304	4	4	1
305	5	5	1
306	6	6	1
404	4	4	0
405	5	5	0
406	6	6	0
update test.t1 set d=d+1;
update test.t2 set d=d+1;
update test.t3 set d=d+1;
update test.t1 set d=d+1 where a=401;
update test.t2 set d=d+1 where a=401;
update test.t3 set d=d+1 where a=401;
select * from test.t1 force index(index_b) where b=5;
a	b	c	d
select * from test.t2 force index(index_b) where b=5;
a	b	c	d
select * from test.t3 force index(index_b) where b=5;
a	b	c	d
105	5	5	4
205	5	5	3
305	5	5	2
405	5	5	1
"================== con4 marker =================="
"================== Step 9 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
username	event_name	count_star
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
username	event_name	count_star
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
username	event_name	count_star
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	53
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	72
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	91
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	250
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	250
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	50	TABLE	test	t1
wait/io/table/sql/handler	80	TABLE	test	t2
wait/io/table/sql/handler	120	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	28	24	4	24	4	0	0
TABLE	test	t1	index_b	4	4	0	4	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	18	4	14	4	0	14	0
TABLE	test	t2	NULL	52	44	8	44	8	0	0
TABLE	test	t2	index_b	4	4	0	4	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	24	4	20	4	0	20	0
TABLE	test	t3	NULL	76	64	12	64	12	0	0
TABLE	test	t3	index_b	10	10	0	10	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	34	4	30	4	0	30	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	50	32	18	32	4	14	0
TABLE	test	t2	80	52	28	52	8	20	0
TABLE	test	t3	120	78	42	78	12	30	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	50
TABLE	test	t2	80
TABLE	test	t3	120
lock tables test.t1 read, test.t2 read, test.t3 read;
unlock tables;
lock tables test.t1 write, test.t2 write, test.t3 write;
unlock tables;
"================== con1 marker =================="
"================== Step 10 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
username	event_name	count_star
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
username	event_name	count_star
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
username	event_name	count_star
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	53
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	72
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	91
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	250
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	250
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	50	TABLE	test	t1
wait/io/table/sql/handler	80	TABLE	test	t2
wait/io/table/sql/handler	120	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	28	24	4	24	4	0	0
TABLE	test	t1	index_b	4	4	0	4	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	18	4	14	4	0	14	0
TABLE	test	t2	NULL	52	44	8	44	8	0	0
TABLE	test	t2	index_b	4	4	0	4	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	24	4	20	4	0	20	0
TABLE	test	t3	NULL	76	64	12	64	12	0	0
TABLE	test	t3	index_b	10	10	0	10	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	34	4	30	4	0	30	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	50	32	18	32	4	14	0
TABLE	test	t2	80	52	28	52	8	20	0
TABLE	test	t3	120	78	42	78	12	30	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	50
TABLE	test	t2	80
TABLE	test	t3	120
flush tables;
"================== flush marker =================="
"================== Step 11 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
username	event_name	count_star
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
username	event_name	count_star
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
username	event_name	count_star
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	53
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	72
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	91
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	250
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	250
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	50	TABLE	test	t1
wait/io/table/sql/handler	80	TABLE	test	t2
wait/io/table/sql/handler	120	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	28	24	4	24	4	0	0
TABLE	test	t1	index_b	4	4	0	4	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	18	4	14	4	0	14	0
TABLE	test	t2	NULL	52	44	8	44	8	0	0
TABLE	test	t2	index_b	4	4	0	4	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	24	4	20	4	0	20	0
TABLE	test	t3	NULL	76	64	12	64	12	0	0
TABLE	test	t3	index_b	10	10	0	10	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	34	4	30	4	0	30	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	50	32	18	32	4	14	0
TABLE	test	t2	80	52	28	52	8	20	0
TABLE	test	t3	120	78	42	78	12	30	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	50
TABLE	test	t2	80
TABLE	test	t3	120
set global read_only=1;
set global read_only=0;
"================== global read_only marker =================="
"================== Step 12 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
username	event_name	count_star
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
username	event_name	count_star
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
username	event_name	count_star
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	53
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	72
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	91
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	250
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	250
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	50	TABLE	test	t1
wait/io/table/sql/handler	80	TABLE	test	t2
wait/io/table/sql/handler	120	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	28	24	4	24	4	0	0
TABLE	test	t1	index_b	4	4	0	4	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	18	4	14	4	0	14	0
TABLE	test	t2	NULL	52	44	8	44	8	0	0
TABLE	test	t2	index_b	4	4	0	4	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	24	4	20	4	0	20	0
TABLE	test	t3	NULL	76	64	12	64	12	0	0
TABLE	test	t3	index_b	10	10	0	10	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	34	4	30	4	0	30	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	50	32	18	32	4	14	0
TABLE	test	t2	80	52	28	52	8	20	0
TABLE	test	t3	120	78	42	78	12	30	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	50
TABLE	test	t2	80
TABLE	test	t3	120
"================== con1 disconnected =================="
"================== Step 13 =================="
call dump_thread();
username	status
user1	not found
username	event_name	count_star
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
username	event_name	count_star
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
username	event_name	count_star
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	53
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	72
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	91
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	250
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	250
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	50	TABLE	test	t1
wait/io/table/sql/handler	80	TABLE	test	t2
wait/io/table/sql/handler	120	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	28	24	4	24	4	0	0
TABLE	test	t1	index_b	4	4	0	4	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	18	4	14	4	0	14	0
TABLE	test	t2	NULL	52	44	8	44	8	0	0
TABLE	test	t2	index_b	4	4	0	4	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	24	4	20	4	0	20	0
TABLE	test	t3	NULL	76	64	12	64	12	0	0
TABLE	test	t3	index_b	10	10	0	10	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	34	4	30	4	0	30	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	50	32	18	32	4	14	0
TABLE	test	t2	80	52	28	52	8	20	0
TABLE	test	t3	120	78	42	78	12	30	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	50
TABLE	test	t2	80
TABLE	test	t3	120
"================== con2 disconnected =================="
"================== Step 14 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	event_name	count_star
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
username	event_name	count_star
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	53
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	72
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	91
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	250
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	250
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	50	TABLE	test	t1
wait/io/table/sql/handler	80	TABLE	test	t2
wait/io/table/sql/handler	120	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	28	24	4	24	4	0	0
TABLE	test	t1	index_b	4	4	0	4	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	18	4	14	4	0	14	0
TABLE	test	t2	NULL	52	44	8	44	8	0	0
TABLE	test	t2	index_b	4	4	0	4	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	24	4	20	4	0	20	0
TABLE	test	t3	NULL	76	64	12	64	12	0	0
TABLE	test	t3	index_b	10	10	0	10	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	34	4	30	4	0	30	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	50	32	18	32	4	14	0
TABLE	test	t2	80	52	28	52	8	20	0
TABLE	test	t3	120	78	42	78	12	30	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	50
TABLE	test	t2	80
TABLE	test	t3	120
"================== con3 disconnected =================="
"================== Step 15 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	event_name	count_star
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	53
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	72
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	91
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	250
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	250
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	50	TABLE	test	t1
wait/io/table/sql/handler	80	TABLE	test	t2
wait/io/table/sql/handler	120	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	28	24	4	24	4	0	0
TABLE	test	t1	index_b	4	4	0	4	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	18	4	14	4	0	14	0
TABLE	test	t2	NULL	52	44	8	44	8	0	0
TABLE	test	t2	index_b	4	4	0	4	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	24	4	20	4	0	20	0
TABLE	test	t3	NULL	76	64	12	64	12	0	0
TABLE	test	t3	index_b	10	10	0	10	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	34	4	30	4	0	30	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	50	32	18	32	4	14	0
TABLE	test	t2	80	52	28	52	8	20	0
TABLE	test	t3	120	78	42	78	12	30	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	50
TABLE	test	t2	80
TABLE	test	t3	120
"================== con4 disconnected =================="
"================== Step 16 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	53
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	72
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	91
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	250
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	250
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	50	TABLE	test	t1
wait/io/table/sql/handler	80	TABLE	test	t2
wait/io/table/sql/handler	120	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	28	24	4	24	4	0	0
TABLE	test	t1	index_b	4	4	0	4	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	18	4	14	4	0	14	0
TABLE	test	t2	NULL	52	44	8	44	8	0	0
TABLE	test	t2	index_b	4	4	0	4	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	24	4	20	4	0	20	0
TABLE	test	t3	NULL	76	64	12	64	12	0	0
TABLE	test	t3	index_b	10	10	0	10	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	34	4	30	4	0	30	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	50	32	18	32	4	14	0
TABLE	test	t2	80	52	28	52	8	20	0
TABLE	test	t3	120	78	42	78	12	30	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	50
TABLE	test	t2	80
TABLE	test	t3	120
truncate performance_schema.events_waits_summary_by_thread_by_event_name;
"================== BY_THREAD truncated =================="
"================== Step 17 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	34
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	53
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	72
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	91
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	250
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	250
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	50	TABLE	test	t1
wait/io/table/sql/handler	80	TABLE	test	t2
wait/io/table/sql/handler	120	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	28	24	4	24	4	0	0
TABLE	test	t1	index_b	4	4	0	4	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	18	4	14	4	0	14	0
TABLE	test	t2	NULL	52	44	8	44	8	0	0
TABLE	test	t2	index_b	4	4	0	4	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	24	4	20	4	0	20	0
TABLE	test	t3	NULL	76	64	12	64	12	0	0
TABLE	test	t3	index_b	10	10	0	10	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	34	4	30	4	0	30	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	50	32	18	32	4	14	0
TABLE	test	t2	80	52	28	52	8	20	0
TABLE	test	t3	120	78	42	78	12	30	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	50
TABLE	test	t2	80
TABLE	test	t3	120
truncate performance_schema.events_waits_summary_by_account_by_event_name;
"================== BY_ACCOUNT truncated =================="
"================== Step 18 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	0
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	0
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	0
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	0
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	34
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	53
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	72
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	91
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	250
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	250
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	50	TABLE	test	t1
wait/io/table/sql/handler	80	TABLE	test	t2
wait/io/table/sql/handler	120	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	28	24	4	24	4	0	0
TABLE	test	t1	index_b	4	4	0	4	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	18	4	14	4	0	14	0
TABLE	test	t2	NULL	52	44	8	44	8	0	0
TABLE	test	t2	index_b	4	4	0	4	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	24	4	20	4	0	20	0
TABLE	test	t3	NULL	76	64	12	64	12	0	0
TABLE	test	t3	index_b	10	10	0	10	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	34	4	30	4	0	30	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	50	32	18	32	4	14	0
TABLE	test	t2	80	52	28	52	8	20	0
TABLE	test	t3	120	78	42	78	12	30	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	50
TABLE	test	t2	80
TABLE	test	t3	120
truncate performance_schema.events_waits_summary_by_user_by_event_name;
"================== BY_USER truncated =================="
"================== Step 19 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	0
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	0
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	0
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	0
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	0
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	0
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	0
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	0
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	250
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	250
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	50	TABLE	test	t1
wait/io/table/sql/handler	80	TABLE	test	t2
wait/io/table/sql/handler	120	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	28	24	4	24	4	0	0
TABLE	test	t1	index_b	4	4	0	4	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	18	4	14	4	0	14	0
TABLE	test	t2	NULL	52	44	8	44	8	0	0
TABLE	test	t2	index_b	4	4	0	4	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	24	4	20	4	0	20	0
TABLE	test	t3	NULL	76	64	12	64	12	0	0
TABLE	test	t3	index_b	10	10	0	10	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	34	4	30	4	0	30	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	50	32	18	32	4	14	0
TABLE	test	t2	80	52	28	52	8	20	0
TABLE	test	t3	120	78	42	78	12	30	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	50
TABLE	test	t2	80
TABLE	test	t3	120
truncate performance_schema.events_waits_summary_by_host_by_event_name;
"================== BY_HOST truncated =================="
"================== Step 21 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	0
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	0
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	0
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	0
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	0
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	0
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	0
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	0
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	0
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	250
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	50	TABLE	test	t1
wait/io/table/sql/handler	80	TABLE	test	t2
wait/io/table/sql/handler	120	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	28	24	4	24	4	0	0
TABLE	test	t1	index_b	4	4	0	4	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	18	4	14	4	0	14	0
TABLE	test	t2	NULL	52	44	8	44	8	0	0
TABLE	test	t2	index_b	4	4	0	4	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	24	4	20	4	0	20	0
TABLE	test	t3	NULL	76	64	12	64	12	0	0
TABLE	test	t3	index_b	10	10	0	10	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	34	4	30	4	0	30	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	50	32	18	32	4	14	0
TABLE	test	t2	80	52	28	52	8	20	0
TABLE	test	t3	120	78	42	78	12	30	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	50
TABLE	test	t2	80
TABLE	test	t3	120
truncate performance_schema.events_waits_summary_global_by_event_name;
"================== GLOBAL truncated =================="
"================== Step 21 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/table/sql/handler	0
user1	localhost	wait/lock/table/sql/handler	0
user2	localhost	wait/io/table/sql/handler	0
user2	localhost	wait/lock/table/sql/handler	0
user3	localhost	wait/io/table/sql/handler	0
user3	localhost	wait/lock/table/sql/handler	0
user4	localhost	wait/io/table/sql/handler	0
user4	localhost	wait/lock/table/sql/handler	0
execute dump_waits_user;
user	event_name	count_star
user1	wait/io/table/sql/handler	0
user1	wait/lock/table/sql/handler	0
user2	wait/io/table/sql/handler	0
user2	wait/lock/table/sql/handler	0
user3	wait/io/table/sql/handler	0
user3	wait/lock/table/sql/handler	0
user4	wait/io/table/sql/handler	0
user4	wait/lock/table/sql/handler	0
execute dump_waits_host;
host	event_name	count_star
localhost	wait/io/table/sql/handler	0
localhost	wait/lock/table/sql/handler	0
execute dump_waits_global;
event_name	count_star
wait/io/table/sql/handler	0
wait/lock/table/sql/handler	0
execute dump_waits_history;
event_name	count(event_name)	object_type	object_schema	object_name
wait/io/table/sql/handler	50	TABLE	test	t1
wait/io/table/sql/handler	80	TABLE	test	t2
wait/io/table/sql/handler	120	TABLE	test	t3
execute dump_waits_index_io;
object_type	object_schema	object_name	index_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	NULL	0	0	0	0	0	0	0
TABLE	test	t1	index_b	0	0	0	0	0	0	0
TABLE	test	t1	index_cb	0	0	0	0	0	0	0
TABLE	test	t1	PRIMARY	0	0	0	0	0	0	0
TABLE	test	t2	NULL	0	0	0	0	0	0	0
TABLE	test	t2	index_b	0	0	0	0	0	0	0
TABLE	test	t2	index_cb	0	0	0	0	0	0	0
TABLE	test	t2	PRIMARY	0	0	0	0	0	0	0
TABLE	test	t3	NULL	0	0	0	0	0	0	0
TABLE	test	t3	index_b	0	0	0	0	0	0	0
TABLE	test	t3	index_cb	0	0	0	0	0	0	0
TABLE	test	t3	PRIMARY	0	0	0	0	0	0	0
execute dump_waits_table_io;
object_type	object_schema	object_name	count_star	count_read	count_write	count_fetch	count_insert	count_update	count_delete
TABLE	test	t1	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0
execute dump_waits_table_lock;
object_type	object_schema	object_name	count_star	count_read	count_write	count_read_normal	count_read_with_shared_locks	count_read_high_priority	count_read_no_insert	count_read_external	count_write_low_priority	count_write_external
TABLE	test	t1	0	0	0	0	0	0	0	0	0	0
TABLE	test	t2	0	0	0	0	0	0	0	0	0	0
TABLE	test	t3	0	0	0	0	0	0	0	0	0	0
execute dump_objects_summary;
object_type	object_schema	object_name	count_star
TABLE	test	t1	0
TABLE	test	t2	0
TABLE	test	t3	0
show global status like "performance_schema%table%_lost";
Variable_name	Value
Performance_schema_table_handles_lost	0
Performance_schema_table_instances_lost	0
Performance_schema_table_lock_stat_lost	0
Warnings:
Warning	1681	'FLUSH PRIVILEGES' is deprecated and will be removed in a future release.
