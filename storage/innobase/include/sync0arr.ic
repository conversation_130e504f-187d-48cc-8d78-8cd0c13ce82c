/*****************************************************************************

Copyright (c) 1995, 2025, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License, version 2.0, as published by the
Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON><PERSON>ITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0,
for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

*****************************************************************************/

/** @file include/sync0arr.ic
 The wait array for synchronization primitives

 Inline code

 Created 9/5/1995 Heikki Tuuri
 *******************************************************/

extern ulint sync_array_size;
extern sync_array_t **sync_wait_array;

#include "ut0counter.h"

/** Get an instance of the sync wait array.
 @return an instance of the sync wait array. */

static inline sync_array_t *sync_array_get() {
  if (sync_array_size <= 1) {
    return (sync_wait_array[0]);
  }

  return (
      sync_wait_array[default_indexer_t<>::get_rnd_index() % sync_array_size]);
}

static inline sync_array_t *sync_array_get_and_reserve_cell(
    void *object, ulint type, ut::Location location, sync_cell_t **cell) {
  sync_array_t *sync_arr = nullptr;

  *cell = nullptr;
  for (ulint i = 0; i < sync_array_size && *cell == nullptr; ++i) {
    /* Although the sync_array is get in a random way currently,
    we still try at most sync_array_size times, in case any
    of the sync_array we get is full */
    sync_arr = sync_array_get();
    *cell = sync_array_reserve_cell(sync_arr, object, type, location);
  }

  /* This won't be true every time, for the loop above may execute
  more than srv_sync_array_size times to reserve a cell.
  But an assertion here makes the code more solid. */
  ut_a(*cell != nullptr);

  return (sync_arr);
}
