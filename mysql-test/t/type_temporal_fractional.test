--source include/no_valgrind_without_big.inc

#SET @@default_storage_engine=InnoDB;

--disable_warnings
DROP TABLE IF EXISTS t1, t2;
--enable_warnings

SET NAMES latin1;


--echo #
--echo # Testing functions CURRENT_TIME and CURRENT_TIMESTAMP
--echo #
SELECT CURRENT_TIME(6) RLIKE '^[0-9]{2}:[0-9]{2}:[0-9]{2}.[0-9]{6}$';
SELECT CURRENT_TIMESTAMP(6) RLIKE '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}.[0-9]{6}$';
SELECT LEFT(CURRENT_TIME(6), 8) = CONCAT(CURRENT_TIME);
SELECT LEFT(CURRENT_TIMESTAMP(6), 8) = CONCAT(CURRENT_TIMESTAMP);
SELECT RIGHT(CURRENT_TIME(6), 7) = RIGHT(CURRENT_TIMESTAMP(6), 7);
SELECT CURRENT_TIMESTAMP(6)=TIMESTAMP(CURRENT_DATE, CURRENT_TIME(6));
SELECT CURRENT_TIMESTAMP(6)=NOW(6);
SELECT CURRENT_TIMESTAMP(6)=LOCALTIME(6);
SELECT CURRENT_TIMESTAMP(6)=LOCALTIMESTAMP(6);
SELECT CURRENT_TIMESTAMP(6)<=SYSDATE(6);
SELECT CURRENT_TIMESTAMP(6)<=SYSDATE(6) + 0;
SELECT MICROSECOND(CURRENT_TIME(6))=MICROSECOND(UTC_TIME(6));
SELECT MICROSECOND(CURRENT_TIMESTAMP(6))=MICROSECOND(UTC_TIMESTAMP(6));
CREATE TABLE t1 AS
SELECT
 CONCAT(CURRENT_TIME()), CONCAT(CURRENT_TIME(6)),
 CONCAT(UTC_TIME()), CONCAT(UTC_TIME(6)),
 CONCAT(CURRENT_TIMESTAMP()), CONCAT(CURRENT_TIMESTAMP(6)),
 CONCAT(UTC_TIMESTAMP()), CONCAT(UTC_TIMESTAMP(6)),
 CONCAT(LOCALTIME()), CONCAT(LOCALTIME(6)),
 CONCAT(LOCALTIMESTAMP()), CONCAT(LOCALTIMESTAMP(6)),
 CONCAT(SYSDATE()), CONCAT(SYSDATE(6));
DESCRIBE t1;
DROP TABLE t1;

CREATE TABLE t1 AS SELECT
  NOW(0), NOW(1), NOW(2), NOW(3), NOW(4), NOW(5), NOW(6);
SHOW CREATE TABLE t1;
DROP TABLE t1;

CREATE TABLE t1 AS SELECT
  SYSDATE(0),
  SYSDATE(1), SYSDATE(2), SYSDATE(3),
  SYSDATE(4), SYSDATE(5), SYSDATE(6);
SHOW CREATE TABLE t1;
DROP TABLE t1;

CREATE TABLE t1 AS SELECT
  FROM_UNIXTIME(1),
  FROM_UNIXTIME(1.1), FROM_UNIXTIME(1.12), FROM_UNIXTIME(1.123),
  FROM_UNIXTIME(1.1234), FROM_UNIXTIME(1.12345), FROM_UNIXTIME(1.123456),
  FROM_UNIXTIME(1.1234567);
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding from VARCHAR and TIME(6) to TIME(N)
--echo #
CREATE TABLE t1 (a VARCHAR(32),
  t6 TIME(6), t5 TIME(5), t4 TIME(4),
  t3 TIME(3), t2 TIME(2), t1 TIME(1),
  t0 TIME);
INSERT INTO t1 (a) VALUES ('10:10:10.999999');
INSERT INTO t1 (a) VALUES ('10:10:10.999994');
INSERT INTO t1 (a) VALUES ('10:10:10.999949');
INSERT INTO t1 (a) VALUES ('10:10:10.999499');
INSERT INTO t1 (a) VALUES ('10:10:10.994999');
INSERT INTO t1 (a) VALUES ('10:10:10.949999');
INSERT INTO t1 (a) VALUES ('10:10:10.499999');
UPDATE t1 SET t0=a, t1=a, t2=a, t3=a, t4=a, t5=a, t6=a;
--query_vertical SELECT * FROM t1;
ALTER TABLE t1 MODIFY a TIME(6);
UPDATE t1 SET t0=a, t1=a, t2=a, t3=a, t4=a, t5=a, t6=a;
--query_vertical SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding from TIME(N) to INT
--echo #
CREATE TABLE t1 (a INT, b TIME(6));
INSERT INTO t1 (b) VALUES ('10:10:59.500000');
INSERT INTO t1 (b) VALUES ('10:10:10.500000');
INSERT INTO t1 (b) VALUES ('10:10:10.499999');
INSERT INTO t1 (b) VALUES ('-10:10:59.500000');
INSERT INTO t1 (b) VALUES ('-10:10:10.500000');
INSERT INTO t1 (b) VALUES ('-10:10:10.499999');
UPDATE t1 SET a=b;
SELECT * FROM t1;
DROP TABLE t1;
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (TIME'01:59:59.499999');
INSERT INTO t1 VALUES (TIME'01:59:59.500000');
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Inserting TIME into a REAL column
--echo #
CREATE TABLE t1 (a REAL);
INSERT INTO t1 VALUES (TIME'01:02:03.123');
SELECT * FROM t1;
DROP TABLE t1;


--echo #
--echo # Testing rounding when altering TIME(N) to a smaller size
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('10:10:10.999999');
ALTER TABLE t1 MODIFY a TIME(5);
SELECT * FROM t1;
DROP TABLE t1;
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('10:10:10.999999');
ALTER TABLE t1 MODIFY a TIME;
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding from DATETIME(6) to TIME(N)
--echo #
CREATE TABLE t1 (a DATETIME(6),
  t6 TIME(6), t5 TIME(5), t4 TIME(4),
  t3 TIME(3), t2 TIME(2), t1 TIME(1),
  t0 TIME);
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.999999');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.999994');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.999949');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.999499');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.994999');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.949999');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.499999');
UPDATE t1 SET t0=a, t1=a, t2=a, t3=a, t4=a, t5=a, t6=a;
--query_vertical SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding from DECIMAL and DOUBLE to TIME(N)
--echo #
CREATE TABLE t1 (a DECIMAL(30,6),
  t6 TIME(6), t5 TIME(5), t4 TIME(4),
  t3 TIME(3), t2 TIME(2), t1 TIME(1),
  t0 TIME);
INSERT INTO t1 (a) VALUES (101010.999999);
INSERT INTO t1 (a) VALUES (101010.999994);
INSERT INTO t1 (a) VALUES (101010.999949);
INSERT INTO t1 (a) VALUES (101010.999499);
INSERT INTO t1 (a) VALUES (101010.994999);
INSERT INTO t1 (a) VALUES (101010.949999);
INSERT INTO t1 (a) VALUES (101010.499999);
UPDATE t1 SET t0=a, t1=a, t2=a, t3=a, t4=a, t5=a, t6=a;
--query_vertical SELECT * FROM t1;
ALTER TABLE t1 MODIFY a DOUBLE;
UPDATE t1 SET t0=a, t1=a, t2=a, t3=a, t4=a, t5=a, t6=a;
--query_vertical SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding when casting from DECIMAL to TIME(N)
--echo #
CREATE TABLE t1 (a DECIMAL(23,4));
INSERT INTO t1 VALUES (NULL),(101010.9999),(-101010.9999);
INSERT IGNORE INTO t1 VALUES (9999999999999999999999.1), (999999.1);
SELECT a, CAST(a AS TIME(3)) FROM t1;
DROP TABLE t1;


--echo #
--echo # Testing that CAST converts days to hours, while EXTRACT/HOUR do not
--echo #
SELECT CAST('1 00:00:00' as TIME), EXTRACT(HOUR FROM '1 00:00:00'), HOUR('1 00:00:00');
CREATE TABLE t1 (a VARCHAR(30));
INSERT INTO t1 VALUES ('1 00:00:00');
SELECT CAST(a AS TIME), EXTRACT(HOUR FROM a), HOUR(a) FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding with CAST
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('10:10:10.999999');
INSERT INTO t1 VALUES ('10:10:10.999998');
INSERT INTO t1 VALUES ('10:10:10.999997');
INSERT INTO t1 VALUES ('10:10:10.999996');
INSERT INTO t1 VALUES ('10:10:10.999995');
INSERT INTO t1 VALUES ('10:10:10.999994');
INSERT INTO t1 VALUES ('10:10:10.999993');
INSERT INTO t1 VALUES ('10:10:10.999992');
INSERT INTO t1 VALUES ('10:10:10.999991');
INSERT INTO t1 VALUES ('10:10:10.999990');
SELECT a, CAST(a AS TIME(5)) FROM t1;
UPDATE t1 SET a=-a;
SELECT a, CAST(a AS TIME(5)) FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (a TIME(5));
INSERT INTO t1 VALUES ('10:10:10.99999');
INSERT INTO t1 VALUES ('10:10:10.99998');
INSERT INTO t1 VALUES ('10:10:10.99997');
INSERT INTO t1 VALUES ('10:10:10.99996');
INSERT INTO t1 VALUES ('10:10:10.99995');
INSERT INTO t1 VALUES ('10:10:10.99994');
INSERT INTO t1 VALUES ('10:10:10.99993');
INSERT INTO t1 VALUES ('10:10:10.99992');
INSERT INTO t1 VALUES ('10:10:10.99991');
INSERT INTO t1 VALUES ('10:10:10.99990');
SELECT a, CAST(a AS TIME(4)) FROM t1;
UPDATE t1 SET a=-a;
SELECT a, CAST(a AS TIME(4)) FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (a TIME(4));
INSERT INTO t1 VALUES ('10:10:10.9999');
INSERT INTO t1 VALUES ('10:10:10.9998');
INSERT INTO t1 VALUES ('10:10:10.9997');
INSERT INTO t1 VALUES ('10:10:10.9996');
INSERT INTO t1 VALUES ('10:10:10.9995');
INSERT INTO t1 VALUES ('10:10:10.9994');
INSERT INTO t1 VALUES ('10:10:10.9993');
INSERT INTO t1 VALUES ('10:10:10.9992');
INSERT INTO t1 VALUES ('10:10:10.9991');
INSERT INTO t1 VALUES ('10:10:10.9990');
SELECT a, CAST(a AS TIME(3)) FROM t1;
UPDATE t1 SET a=-a;
SELECT a, CAST(a AS TIME(3)) FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (a TIME(3));
INSERT INTO t1 VALUES ('10:10:10.999');
INSERT INTO t1 VALUES ('10:10:10.998');
INSERT INTO t1 VALUES ('10:10:10.997');
INSERT INTO t1 VALUES ('10:10:10.996');
INSERT INTO t1 VALUES ('10:10:10.995');
INSERT INTO t1 VALUES ('10:10:10.994');
INSERT INTO t1 VALUES ('10:10:10.993');
INSERT INTO t1 VALUES ('10:10:10.992');
INSERT INTO t1 VALUES ('10:10:10.991');
INSERT INTO t1 VALUES ('10:10:10.990');
SELECT a, CAST(a AS TIME(2)) FROM t1;
UPDATE t1 SET a=-a;
SELECT a, CAST(a AS TIME(2)) FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (a TIME(2));
INSERT INTO t1 VALUES ('10:10:10.99');
INSERT INTO t1 VALUES ('10:10:10.98');
INSERT INTO t1 VALUES ('10:10:10.97');
INSERT INTO t1 VALUES ('10:10:10.96');
INSERT INTO t1 VALUES ('10:10:10.95');
INSERT INTO t1 VALUES ('10:10:10.94');
INSERT INTO t1 VALUES ('10:10:10.93');
INSERT INTO t1 VALUES ('10:10:10.92');
INSERT INTO t1 VALUES ('10:10:10.91');
INSERT INTO t1 VALUES ('10:10:10.90');
SELECT a, CAST(a AS TIME(1)) FROM t1;
UPDATE t1 SET a=-a;
SELECT a, CAST(a AS TIME(1)) FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (a TIME(1));
INSERT INTO t1 VALUES ('10:10:10.9');
INSERT INTO t1 VALUES ('10:10:10.8');
INSERT INTO t1 VALUES ('10:10:10.7');
INSERT INTO t1 VALUES ('10:10:10.6');
INSERT INTO t1 VALUES ('10:10:10.5');
INSERT INTO t1 VALUES ('10:10:10.4');
INSERT INTO t1 VALUES ('10:10:10.3');
INSERT INTO t1 VALUES ('10:10:10.2');
INSERT INTO t1 VALUES ('10:10:10.1');
INSERT INTO t1 VALUES ('10:10:10.0');
SELECT a, CAST(a AS TIME) FROM t1;
UPDATE t1 SET a=-a;
SELECT a, CAST(a AS TIME) FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing internal representation format for TIME(N)
--echo #
CREATE TABLE t1 (a6 VARCHAR(32));
INSERT INTO t1 VALUES ('00:00:00.000000');
INSERT INTO t1 VALUES ('00:00:00.000001');
INSERT INTO t1 VALUES ('00:00:00.000002');
INSERT INTO t1 VALUES ('00:00:00.000003');
INSERT INTO t1 VALUES ('00:00:00.000004');
INSERT INTO t1 VALUES ('00:00:00.000005');
INSERT INTO t1 VALUES ('00:00:00.000006');
INSERT INTO t1 VALUES ('00:00:00.000007');
INSERT INTO t1 VALUES ('00:00:00.000008');
INSERT INTO t1 VALUES ('00:00:00.000009');
INSERT INTO t1 VALUES ('00:00:00.000010');
INSERT INTO t1 VALUES ('00:00:00.000011');
INSERT INTO t1 VALUES ('00:00:00.000012');
INSERT INTO t1 VALUES ('00:00:00.000013');
INSERT INTO t1 VALUES ('00:00:00.000014');
INSERT INTO t1 VALUES ('00:00:00.000015');
INSERT INTO t1 VALUES ('00:00:00.000016');
INSERT INTO t1 VALUES ('00:00:00.000099');
INSERT INTO t1 VALUES ('00:00:00.000999');
INSERT INTO t1 VALUES ('00:00:00.009999');
INSERT INTO t1 VALUES ('00:00:00.099999');
INSERT INTO t1 VALUES ('00:00:00.100000');
INSERT INTO t1 VALUES ('00:00:00.900000');
INSERT INTO t1 VALUES ('00:00:00.990000');
INSERT INTO t1 VALUES ('00:00:00.999000');
INSERT INTO t1 VALUES ('00:00:00.999900');
INSERT INTO t1 VALUES ('00:00:00.999990');
INSERT INTO t1 VALUES ('00:00:00.999999');

INSERT INTO t1 VALUES ('00:00:01.000000');
INSERT INTO t1 VALUES ('00:00:01.000001');
INSERT INTO t1 VALUES ('00:00:01.000002');
INSERT INTO t1 VALUES ('00:00:01.000003');
INSERT INTO t1 VALUES ('00:00:01.000004');
INSERT INTO t1 VALUES ('00:00:01.000005');
INSERT INTO t1 VALUES ('00:00:01.000006');
INSERT INTO t1 VALUES ('00:00:01.000007');
INSERT INTO t1 VALUES ('00:00:01.000008');
INSERT INTO t1 VALUES ('00:00:01.000009');
INSERT INTO t1 VALUES ('00:00:01.000010');
INSERT INTO t1 VALUES ('00:00:01.000011');
INSERT INTO t1 VALUES ('00:00:01.000012');
INSERT INTO t1 VALUES ('00:00:01.000013');
INSERT INTO t1 VALUES ('00:00:01.000014');
INSERT INTO t1 VALUES ('00:00:01.000015');
INSERT INTO t1 VALUES ('00:00:01.000016');
INSERT INTO t1 VALUES ('00:00:01.000099');
INSERT INTO t1 VALUES ('00:00:01.000999');
INSERT INTO t1 VALUES ('00:00:01.009999');
INSERT INTO t1 VALUES ('00:00:01.090000');
INSERT INTO t1 VALUES ('00:00:01.099999');
INSERT INTO t1 VALUES ('00:00:01.100000');
INSERT INTO t1 VALUES ('00:00:01.900000');
INSERT INTO t1 VALUES ('00:00:01.990000');
INSERT INTO t1 VALUES ('00:00:01.999000');
INSERT INTO t1 VALUES ('00:00:01.999900');
INSERT INTO t1 VALUES ('00:00:01.999990');
INSERT INTO t1 VALUES ('00:00:01.999999');

INSERT INTO t1 VALUES ('00:01:00.000001');
INSERT INTO t1 VALUES ('00:01:00.000008');
INSERT INTO t1 VALUES ('00:01:00.000015');
INSERT INTO t1 VALUES ('00:01:00.000016');
INSERT INTO t1 VALUES ('00:01:00.000099');
INSERT INTO t1 VALUES ('00:01:00.000999');
INSERT INTO t1 VALUES ('00:01:00.009999');
INSERT INTO t1 VALUES ('00:01:00.099999');
INSERT INTO t1 VALUES ('00:01:00.100000');
INSERT INTO t1 VALUES ('00:01:00.900000');
INSERT INTO t1 VALUES ('00:01:00.999999');

INSERT INTO t1 VALUES ('01:00:00.000001');
INSERT INTO t1 VALUES ('01:00:00.000008');
INSERT INTO t1 VALUES ('01:00:00.000015');
INSERT INTO t1 VALUES ('01:00:00.000016');
INSERT INTO t1 VALUES ('01:00:00.000099');
INSERT INTO t1 VALUES ('01:00:00.000999');
INSERT INTO t1 VALUES ('01:00:00.009999');
INSERT INTO t1 VALUES ('01:00:00.099999');
INSERT INTO t1 VALUES ('01:00:00.100000');
INSERT INTO t1 VALUES ('01:00:00.900000');
INSERT INTO t1 VALUES ('01:00:00.990000');
INSERT INTO t1 VALUES ('01:00:00.999000');
INSERT INTO t1 VALUES ('01:00:00.999900');
INSERT INTO t1 VALUES ('01:00:00.999990');
INSERT INTO t1 VALUES ('01:00:00.999999');

INSERT INTO t1 VALUES ('838:59:58.000001');
INSERT INTO t1 VALUES ('838:59:58.000008');
INSERT INTO t1 VALUES ('838:59:58.000015');
INSERT INTO t1 VALUES ('838:59:58.000016');
INSERT INTO t1 VALUES ('838:59:58.000099');
INSERT INTO t1 VALUES ('838:59:58.000999');
INSERT INTO t1 VALUES ('838:59:58.009999');
INSERT INTO t1 VALUES ('838:59:58.099999');
INSERT INTO t1 VALUES ('838:59:58.100000');
INSERT INTO t1 VALUES ('838:59:58.900000');
INSERT INTO t1 VALUES ('838:59:58.990000');
INSERT INTO t1 VALUES ('838:59:58.999000');
INSERT INTO t1 VALUES ('838:59:58.999900');
INSERT INTO t1 VALUES ('838:59:58.999990');
INSERT INTO t1 VALUES ('838:59:58.999999');
INSERT INTO t1 VALUES ('838:59:59.000000');
INSERT INTO t1 SELECT CONCAT('-', a6) FROM t1;
ALTER TABLE t1
  ADD a0 VARCHAR(32),
  ADD a1 VARCHAR(32), ADD a2 VARCHAR(32), ADD a3 VARCHAR(32),
  ADD a4 VARCHAR(32), ADD a5 VARCHAR(32),
  ADD t0 TIME(0),
  ADD t1 TIME(1), ADD t2 TIME(2), ADD t3 TIME(3),
  ADD t4 TIME(4), ADD t5 TIME(5), ADD t6 TIME(6);
UPDATE t1 SET
  a0=LEFT(a6, LENGTH(a6) - 6),
  a1=LEFT(a6, LENGTH(a6) - 5),
  a2=LEFT(a6, LENGTH(a6) - 4),
  a3=LEFT(a6, LENGTH(a6) - 3),
  a4=LEFT(a6, LENGTH(a6) - 2),
  a5=LEFT(a6, LENGTH(a6) - 1);
UPDATE t1 SET t0=a0, t1=a1, t2=a2, t3=a3, t4=a4, t5=a5, t6= a6;
SELECT a6, t6, HEX(WEIGHT_STRING(t6)) FROM t1 ORDER BY t6;
SELECT a5, t5, HEX(WEIGHT_STRING(t5)) FROM t1 ORDER BY t5, a6;
SELECT a4, t4, HEX(WEIGHT_STRING(t4)) FROM t1 ORDER BY t4, a6;
SELECT a3, t3, HEX(WEIGHT_STRING(t3)) FROM t1 ORDER BY t3, a6;
SELECT a2, t2, HEX(WEIGHT_STRING(t2)) FROM t1 ORDER BY t2, a6;
SELECT a1, t1, HEX(WEIGHT_STRING(t1)) FROM t1 ORDER BY t1, a6;
CREATE VIEW v1 AS
SELECT a6, t0, t1, t2, t3, t4, t5, t6,
HEX(WEIGHT_STRING(t0)) as wst0,
HEX(WEIGHT_STRING(t1)) as wst1,
HEX(WEIGHT_STRING(t2)) as wst2,
HEX(WEIGHT_STRING(t3)) as wst3,
HEX(WEIGHT_STRING(t4)) as wst4,
HEX(WEIGHT_STRING(t5)) as wst5,
HEX(WEIGHT_STRING(t6)) as wst6
FROM t1;
--query_vertical SELECT * FROM  v1 WHERE a6 LIKE '%.999999' ORDER BY a6, t6;
--query_vertical SELECT * FROM  v1 WHERE a6 LIKE '%.000000' ORDER BY a6, t6;
--query_vertical SELECT * FROM  v1 WHERE a6 LIKE '%.100000' ORDER BY a6, t6;
DROP VIEW v1;
DROP TABLE t1;


--echo #
--echo # Testing that TIME(0) is a synonym to non-fractional TIME
--echo #

CREATE TABLE t1 (a TIME(0) NOT NULL);
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Testing bad FSPs
--echo #
--error ER_PARSE_ERROR
CREATE TABLE t1 (a TIME(-1));
--error ER_TOO_BIG_PRECISION
CREATE TABLE t1 (a TIME(7));
--error ER_TOO_BIG_PRECISION
CREATE TABLE t1 (a TIME(31));

--echo #
--echo # Testing bad FSPs with TIME functions
--echo #
--error ER_PARSE_ERROR
SELECT CURTIME(-1);
--error ER_TOO_BIG_PRECISION
SELECT CURTIME(7);
--error ER_TOO_BIG_PRECISION
SELECT CURTIME(31);

--echo #
--echo # Testing INSERT, ORDER, KEY, BETWEEN, comparison
--echo #
CREATE TABLE t1 (a TIME(6) NOT NULL);
INSERT INTO t1 VALUES ('-838:59:59.000000');
INSERT INTO t1 VALUES ('-01:00:00.999999');
INSERT INTO t1 VALUES ('-01:00:00.000001');
INSERT INTO t1 VALUES ('-01:00:00.000000');
INSERT INTO t1 VALUES ('-00:00:00.000001');
INSERT INTO t1 VALUES ('00:00:00.000000');
INSERT INTO t1 VALUES ('23:59:59.999999');
INSERT INTO t1 VALUES ('01:02:03');
INSERT INTO t1 VALUES ('01:02:03.4');
INSERT INTO t1 VALUES ('01:02:03.45');
INSERT INTO t1 VALUES ('01:02:03.456');
INSERT INTO t1 VALUES ('01:02:03.4567');
INSERT INTO t1 VALUES ('01:02:03.45678');
INSERT INTO t1 VALUES ('01:02:03.4567891');
INSERT INTO t1 VALUES ('838:59:59.000000');
SELECT * FROM t1;
SELECT * FROM t1 ORDER BY a DESC;
SELECT * FROM t1 WHERE a='01:02:03.45';
SELECT * FROM t1 WHERE a='01:02:03.4567';
SELECT * FROM t1 WHERE a='01:02:03.45670';
SELECT * FROM t1 WHERE a='01:02:03.456700';
SELECT * FROM t1 WHERE a BETWEEN '01:02:00' AND '01:03:00';
SELECT * FROM t1 WHERE a BETWEEN TIME'01:02:03.456' AND TIME'01:02:03.45678';
ALTER TABLE t1 ADD KEY(a);
# Testing key order
SELECT * FROM t1 ORDER BY a;
SELECT * FROM t1 ORDER BY a DESC;

analyze table t1;
EXPLAIN SELECT * FROM t1 WHERE a='01:02:03.456700';
SELECT * FROM t1 WHERE a='01:02:03.456700';
SELECT * FROM t1 WHERE a BETWEEN '01:02:00' AND '01:03:00';
SELECT * FROM t1 WHERE a BETWEEN TIME'01:02:03.456' AND TIME'01:02:03.45678';
DROP TABLE t1;

--echo #
--echo # Testing TIME with days
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('00:00:00');
INSERT INTO t1 VALUES ('-24:00:00'), ('24:00:00');
INSERT INTO t1 VALUES ('-1 00:00:00'), ('1 00:00:00');
INSERT INTO t1 VALUES ('-10 00:00:00'), ('10 00:00:00');
SELECT * FROM t1 ORDER BY a;
DROP TABLE t1;

--echo #
--echo # Testing rare formats
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES (1122), ('1:2:3'), ('112233'),('-00:00:00'),('-00:00:00.000000');
SELECT * FROM t1 ORDER BY a;
DROP TABLE t1;

--echo #
--echo # Testing bad values
--echo #
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES (106060);
INSERT INTO t1 VALUES (106060.0);
INSERT INTO t1 VALUES (106060e0);
INSERT INTO t1 VALUES ('106060');
SELECT * FROM t1;
DROP TABLE t1;
SET sql_mode = default;
--echo #
--echo # Testing bad values with TRADITIONAL mode
--echo #
SET sql_mode=traditional;
CREATE TABLE t1 (a TIME(6));
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES ('00:00:00.111111 xxx');
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (106060);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (106060.0);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (106060e0);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES ('106060');
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES ('838:59:59.0000009');
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES ('-838:59:59.0000009');
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (CAST(0xFFFFFFFF00000000 AS SIGNED));
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (CAST(0xFFFFFFFF00000000 AS UNSIGNED));
SELECT * FROM t1;
DROP TABLE t1;
SET sql_mode=default;

--echo #
--echo # Testing rounding
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES (105959.1234567);
INSERT INTO t1 VALUES ('00:00:00.1111110');
INSERT INTO t1 VALUES ('00:00:00.1111111');
INSERT INTO t1 VALUES ('00:00:00.1111114');
INSERT INTO t1 VALUES ('00:00:00.1111115');
INSERT INTO t1 VALUES ('00:00:00.1111119');
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES (0.1111110);
INSERT INTO t1 VALUES (0.1111111);
INSERT INTO t1 VALUES (0.1111114);
INSERT INTO t1 VALUES (0.1111115);
INSERT INTO t1 VALUES (0.1111119);
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES (0.1111110e0);
INSERT INTO t1 VALUES (0.1111111e0);
INSERT INTO t1 VALUES (0.1111114e0);
INSERT INTO t1 VALUES (0.1111115e0);
INSERT INTO t1 VALUES (0.1111119e0);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing huge values
--echo #
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES (CAST(0xFFFFFFFF00000000 AS SIGNED));
INSERT INTO t1 VALUES (CAST(0xFFFFFFFF00000000 AS UNSIGNED));
INSERT INTO t1 VALUES ( 100000000000000000.1);
INSERT INTO t1 VALUES ( 100000000000000000.1e0);
INSERT INTO t1 VALUES (-100000000000000000.1);
INSERT INTO t1 VALUES (-100000000000000000.1e0);
INSERT INTO t1 VALUES (1000000000.0 * 1000000000);
INSERT INTO t1 VALUES (-1000000000.0 * 1000000000);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing truncation warnings
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('00:00:00.111111 xxx');
SELECT * FROM t1;
DROP TABLE t1;
SET sql_mode = default;

--echo #
--echo # Testing IN
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('-99:00:00.000000');
INSERT INTO t1 VALUES ('-99:00:00.000001');
INSERT INTO t1 VALUES ('-99:00:00.000002');
INSERT INTO t1 VALUES ('-00:00:00.000001');
INSERT INTO t1 VALUES ('-00:00:00.000002');
INSERT INTO t1 VALUES ('00:00:00.000000');
INSERT INTO t1 VALUES ('00:00:00.000001');
INSERT INTO t1 VALUES ('00:00:00.000002');
INSERT INTO t1 VALUES ('10:00:00.000000');
INSERT INTO t1 VALUES ('10:00:00.000001');
INSERT INTO t1 VALUES ('10:00:00.000002');
INSERT INTO t1 VALUES ('99:00:00.000000');
INSERT INTO t1 VALUES ('99:00:00.000001');
INSERT INTO t1 VALUES ('99:00:00.000002');
SELECT * FROM t1 WHERE a IN ('00:00:00', '-99:00:00.000001', '99:00:00.000002');
SELECT * FROM t1 WHERE a IN (0, -990000, 990000);
SELECT * FROM t1 WHERE a IN (0.000001, -990000.000001, 990000.000001);
--echo #
--echo # Testing Item_temporal_with_ref::print
--echo #
analyze table t1;
EXPLAIN SELECT * FROM t1 WHERE a IN (990000,0);

--echo #
--echo # Testing IN with index: field->store_packed
--echo #
ALTER TABLE t1 ADD KEY(a);
SELECT * FROM t1 WHERE a IN ('00:00:00', '-99:00:00.000001', '99:00:00.000002');
SELECT * FROM t1 WHERE a IN (0, -990000, 990000);
SELECT * FROM t1 WHERE a IN (0.000001, -990000.000001, 990000.000001);
DROP TABLE t1;

--echo #
--echo # Testing CREATE TABLE LIKE
--echo #
CREATE TABLE t1 (a TIME(6));
CREATE TABLE t2 LIKE t1;
SHOW CREATE TABLE t2;
DROP TABLE t2, t1;

--echo #
--echo # Testing CREATE TABLE .. SELECT
--echo #
CREATE TABLE t1 (a TIME(6));
CREATE TABLE t2 AS SELECT * FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2, t1;

--echo #
--echo # Testing JOIN between TIME(6) and TIME(6)
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('00:01:03.1');
INSERT INTO t1 VALUES ('00:01:03.12');
INSERT INTO t1 VALUES ('00:01:03.123');
INSERT INTO t1 VALUES ('00:01:03.1234');
INSERT INTO t1 VALUES ('00:01:03.12345');
INSERT INTO t1 VALUES ('00:01:03.123456');
CREATE TABLE t2 (a TIME(6));
INSERT INTO t2 SELECT * FROM t1;
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t1 ADD KEY(a);
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t2 ADD KEY(a);
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t1 DROP KEY a;
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing JOIN between TIME and TIME(6)
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('-836:59:59.000000');
INSERT INTO t1 VALUES ('10:11:12.000000');
INSERT INTO t1 VALUES ('10:11:12.000001');
INSERT INTO t1 VALUES ('10:11:12.000002');
INSERT INTO t1 VALUES ('10:11:13.000000');
INSERT INTO t1 VALUES ('10:11:13.000001');
INSERT INTO t1 VALUES ('10:11:13.000002');
INSERT INTO t1 VALUES ('835:59:59.999999');
INSERT INTO t1 VALUES ('836:59:59.000000');
CREATE TABLE t2 (a TIME);
INSERT INTO t2 VALUES ('10:11:12');
INSERT INTO t2 VALUES ('10:11:13');
INSERT INTO t2 VALUES ('-836:59:59');
INSERT INTO t2 VALUES ('836:59:59');
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t1 ADD KEY(a);
ALTER TABLE t2 ADD KEY(a);
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
DROP TABLE t2, t1;

--echo #
--echo # Testing unique index
--echo #
CREATE TABLE t1 (a TIME(6), UNIQUE(a));
INSERT INTO t1 VALUES ('00:01:02');
INSERT INTO t1 VALUES ('00:01:02.1');
INSERT INTO t1 VALUES ('00:01:02.12');
INSERT INTO t1 VALUES ('00:01:02.123');
INSERT INTO t1 VALUES ('00:01:02.1234');
INSERT INTO t1 VALUES ('00:01:02.12345');
--error ER_DUP_ENTRY
INSERT INTO t1 VALUES ('00:01:02.12345');
DROP TABLE t1;

--echo #
--echo # Testing GROUP BY
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('00:01:02');
INSERT INTO t1 VALUES ('00:01:02.0');
INSERT INTO t1 VALUES ('00:01:02.01');
INSERT INTO t1 VALUES ('00:01:02.010');
INSERT INTO t1 VALUES ('00:01:02.02');
INSERT INTO t1 VALUES ('00:01:02.020');
SELECT a, COUNT(*) FROM t1 GROUP BY a;
DROP TABLE t1;

--echo #
--echo # Testing MIN() and MAX()
--echo #
CREATE TABLE t1 (a INT, b TIME(6));
INSERT INTO t1 VALUES (1, '00:01:02.000001');
INSERT INTO t1 VALUES (1, '00:01:02');
INSERT INTO t1 VALUES (2, '10:01:02');
INSERT INTO t1 VALUES (2, '10:01:02.000001');
INSERT INTO t1 VALUES (3, '10:11:02');
INSERT INTO t1 VALUES (3, '10:11:02.000001');
SELECT MIN(b), MAX(b) FROM t1;
SELECT a, MIN(b), MAX(b) FROM t1 GROUP BY a;
UPDATE t1 SET b=-b;
SELECT MIN(b), MAX(b) FROM t1;
SELECT a, MIN(b), MAX(b) FROM t1 GROUP BY a;
DROP TABLE t1;

--echo #
--echo # Testing MIN(TIME) and MAX(TIME) in arithmetic expressions and CAST
--echo # This covers Item_sum_hybrid::val_int(), val_real(), val_decimal()
--echo #
CREATE TABLE t1
(
  t0 TIME,
  t1 TIME(1), t2 TIME(2), t3 TIME(3),
  t4 TIME(4), t5 TIME(5), t6 TIME(6)
);
INSERT INTO t1 VALUES
(
  '10:10:10',
  '10:10:10.9', '10:10:10.99', '10:10:10.999',
  '10:10:10.9999', '10:10:10.99999', '10:10:10.999999'
);
CREATE TABLE t2 AS
SELECT
  MAX(t0) + 1,
  MAX(t1) + 1, MAX(t2) + 1, MAX(t3) + 1,
  MAX(t4) + 1, MAX(t5) + 1, MAX(t6) + 1
FROM t1;
SHOW COLUMNS FROM t2;
SELECT * FROM t2;
DROP TABLE t2;
CREATE TABLE t2 AS
SELECT
  MAX(t0) + 1.1,
  MAX(t1) + 1.1, MAX(t2) + 1.1, MAX(t3) + 1.1,
  MAX(t4) + 1.1, MAX(t5) + 1.1, MAX(t6) + 1.1
FROM t1;
SHOW COLUMNS FROM t2;
SELECT * FROM t2;
DROP TABLE t2;
CREATE TABLE t2 AS
SELECT
  MAX(t0) + 1.0e0,
  MAX(t1) + 1.0e0, MAX(t2) + 1.0e0, MAX(t3) + 1.0e0,
  MAX(t4) + 1.0e0, MAX(t5) + 1.0e0, MAX(t6) + 1.0e0
FROM t1;
SHOW COLUMNS FROM t2;
SELECT * FROM t2;
DROP TABLE t2;
SELECT
  MAX(t0) + 1,
  MAX(t1) + 1, MAX(t2) + 1, MAX(t3) + 1,
  MAX(t4) + 1, MAX(t5) + 1, MAX(t6) + 1
FROM t1;
SELECT
  CAST(MAX(t0) AS SIGNED),
  CAST(MAX(t1) AS SIGNED), CAST(MAX(t2) AS SIGNED), CAST(MAX(t3) AS SIGNED),
  CAST(MAX(t4) AS SIGNED), CAST(MAX(t5) AS SIGNED), CAST(MAX(t6) AS SIGNED)
FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing LEAST, GREATEST
--echo #
SELECT LEAST(CAST('00:00:00.1' as TIME(6)), '00:00:01.1') AS a;
SELECT LEAST(TIME'00:00:00.1', TIME'00:00:00.12') AS a;
SELECT GREATEST(CAST('00:00:00.1' as TIME(6)), '00:00:01.1') AS a;
SELECT GREATEST(TIME'00:00:00.1', TIME'00:00:00.12') AS a;
CREATE TABLE t1 AS SELECT
  LEAST(TIME'00:00:00.1', TIME'00:00:00.12'),
  GREATEST(TIME'00:00:00.1', TIME'00:00:00.12');
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Testing INSERT with near-maximum and near-minimum supported values.
--echo #
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('838:59:59.999999');
INSERT INTO t1 VALUES ('838:59:59');
INSERT INTO t1 VALUES ('838:59:59.0000001');
INSERT INTO t1 VALUES ('838:59:59.0000002');
INSERT INTO t1 VALUES ('838:59:59.0000003');
INSERT INTO t1 VALUES ('838:59:59.0000004');
INSERT INTO t1 VALUES ('838:59:59.0000005');
INSERT INTO t1 VALUES ('838:59:59.0000006');
INSERT INTO t1 VALUES ('838:59:59.0000007');
INSERT INTO t1 VALUES ('838:59:59.0000008');
INSERT INTO t1 VALUES ('838:59:59.0000009');
INSERT INTO t1 VALUES ('838:59:59.1');
INSERT INTO t1 VALUES ('839:00:00');
INSERT INTO t1 VALUES ('-838:59:59');
INSERT INTO t1 VALUES ('-838:59:59.0000001');
INSERT INTO t1 VALUES ('-838:59:59.0000002');
INSERT INTO t1 VALUES ('-838:59:59.0000003');
INSERT INTO t1 VALUES ('-838:59:59.0000004');
INSERT INTO t1 VALUES ('-838:59:59.0000005');
INSERT INTO t1 VALUES ('-838:59:59.0000006');
INSERT INTO t1 VALUES ('-838:59:59.0000007');
INSERT INTO t1 VALUES ('-838:59:59.0000008');
INSERT INTO t1 VALUES ('-838:59:59.0000009');
INSERT INTO t1 VALUES ('-838:59:59.000001');
INSERT INTO t1 VALUES ('-838:59:59.999999');
INSERT INTO t1 VALUES ('-839:00:00');
INSERT INTO t1 VALUES ('-839:00:00.1');
INSERT INTO t1 VALUES ('-838:59:59.999999');
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing integer INSERT value
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES (20203);
INSERT INTO t1 VALUES (8385959);
INSERT INTO t1 VALUES (8390000);
INSERT INTO t1 VALUES (-8385959);
INSERT INTO t1 VALUES (-8390000);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing decimal INSERT values
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES (20203.4);
INSERT INTO t1 VALUES (20203.45);
INSERT INTO t1 VALUES (20203.456);
INSERT INTO t1 VALUES (20203.4567);
INSERT INTO t1 VALUES (20203.45678);
INSERT INTO t1 VALUES (20203.456789);
INSERT INTO t1 VALUES (20203.4567894);
INSERT INTO t1 VALUES (20203.4567895);
INSERT INTO t1 VALUES (20203.4567896);
INSERT INTO t1 VALUES (20203.456789678);
INSERT INTO t1 VALUES (8385959.999999);
INSERT INTO t1 VALUES (8385959.000000);
INSERT INTO t1 VALUES (8385959.0000001);
INSERT INTO t1 VALUES (8385959.0000002);
INSERT INTO t1 VALUES (8385959.0000003);
INSERT INTO t1 VALUES (8385959.0000004);
INSERT INTO t1 VALUES (8385959.0000005);
INSERT INTO t1 VALUES (8385959.0000006);
INSERT INTO t1 VALUES (8385959.0000007);
INSERT INTO t1 VALUES (8385959.0000008);
INSERT INTO t1 VALUES (8385959.0000009);
INSERT INTO t1 VALUES (8385959.000001);
INSERT INTO t1 VALUES (8390000.000000);
INSERT INTO t1 VALUES (-8385959.000000);
INSERT INTO t1 VALUES (-8385959.0000001);
INSERT INTO t1 VALUES (-8385959.0000002);
INSERT INTO t1 VALUES (-8385959.0000003);
INSERT INTO t1 VALUES (-8385959.0000004);
INSERT INTO t1 VALUES (-8385959.0000005);
INSERT INTO t1 VALUES (-8385959.0000006);
INSERT INTO t1 VALUES (-8385959.0000007);
INSERT INTO t1 VALUES (-8385959.0000008);
INSERT INTO t1 VALUES (-8385959.0000009);
INSERT INTO t1 VALUES (-8385959.000001);
INSERT INTO t1 VALUES (-8390000.000000);
INSERT INTO t1 VALUES (-8385959.999999);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing double INSERT values
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES (123.4567e-3);
INSERT INTO t1 VALUES (123.4567e-2);
INSERT INTO t1 VALUES (123.4567e-1);
INSERT INTO t1 VALUES (123.4567e0);
INSERT INTO t1 VALUES (123.4567e1);
INSERT INTO t1 VALUES (123.4567e2);
INSERT INTO t1 VALUES (123.4567e3);
INSERT INTO t1 VALUES (8385959.999999e0);
INSERT INTO t1 VALUES (8385959e0);
INSERT INTO t1 VALUES (8385959.1e0);
INSERT INTO t1 VALUES (8390000.0e0);
INSERT INTO t1 VALUES (-8385959.0e0);
INSERT INTO t1 VALUES (-8385959.1e0);
INSERT INTO t1 VALUES (-8385959.999999e0);
INSERT INTO t1 VALUES (-8390000.0e0);
SELECT * FROM t1;
DROP TABLE t1;
SET sql_mode = default;
--echo #
--echo # Testing UPDATE between TIME(6) and TIME/INT/DECIMAL/FLOAT
--echo #
CREATE TABLE t1 (t0 TIME, t6 TIME(6), i INT, d DECIMAL(20,6), f DOUBLE);
INSERT INTO t1 (t0) VALUES ('11:22:33');
UPDATE t1 SET t6=t0, i=t0, d=t0, f=t0;
SELECT * FROM t1;
UPDATE t1 SET t6='11:22:33.123';
UPDATE t1 SET t0=t6, i=t6, d=t6, f=t6;
SELECT * FROM t1;
UPDATE t1 SET i=112233, d=112233.123, f=112233.123;
UPDATE t1 SET t6=i;
SELECT t6 FROM t1;
UPDATE t1 SET t6=d;
SELECT t6 FROM t1;
UPDATE t1 SET t6=f;
SELECT t6 FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing JOIN with comparison between TIME(6) and INT
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('11:22:33');
INSERT INTO t1 VALUES ('11:22:33.123');
INSERT INTO t1 VALUES ('-11:22:33');
INSERT INTO t1 VALUES ('-11:22:33.123456');
CREATE TABLE t2 (b INT);
INSERT INTO t2 VALUES (112233);
INSERT INTO t2 VALUES (-112233);
SELECT * FROM t1, t2 WHERE a=b;
DROP TABLE t1, t2;

--echo #
--echo # Testing JOIN with comparison between TIME(6) and DECIMAL(20,6)
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('11:22:33');
INSERT INTO t1 VALUES ('11:22:33.123');
INSERT INTO t1 VALUES ('-11:22:33');
INSERT INTO t1 VALUES ('-11:22:33.123456');
CREATE TABLE t2 (b DECIMAL(20,6));
INSERT INTO t2 VALUES (112233.123);
INSERT INTO t2 VALUES (-112233.123456);
SELECT * FROM t1, t2 WHERE a=b;
DROP TABLE t1, t2;

--echo #
--echo # Testing JOIN with comparison between TIME(6) and DECIMAL(20,3)
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('11:22:33');
INSERT INTO t1 VALUES ('11:22:33.123');
INSERT INTO t1 VALUES ('-11:22:33');
INSERT INTO t1 VALUES ('-11:22:33.123456');
CREATE TABLE t2 (b DECIMAL(20,3));
INSERT INTO t2 VALUES (112233.123);
INSERT INTO t2 VALUES (-112233.123456);
SELECT * FROM t1, t2 WHERE a=b;
DROP TABLE t1, t2;

--echo #
--echo # Testing JOIN with comparison between TIME(6) and DOUBLE
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('11:22:33');
INSERT INTO t1 VALUES ('11:22:33.123');
INSERT INTO t1 VALUES ('-11:22:33');
INSERT INTO t1 VALUES ('-11:22:33.123456');
CREATE TABLE t2 (b DOUBLE);
INSERT INTO t2 VALUES (112233.123);
INSERT INTO t2 VALUES (-112233.123456);
SELECT * FROM t1, t2 WHERE a=b;
DROP TABLE t1, t2;

--echo #
--echo # Testing JOIN with comparison between TIME(6) and VARCHAR
--echo #
## TS-TODO-QQ: should TIME(6) and VARCHAR(N) be compared as TIME(6) values?
## probably yes, because DATETIME(6) and VARCHR(N) are compared as DATETIME.
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('11:22:33');
INSERT INTO t1 VALUES ('11:22:33.123');
INSERT INTO t1 VALUES ('-11:22:33');
INSERT INTO t1 VALUES ('-11:22:33.123456');
CREATE TABLE t2 (b VARCHAR(20));
INSERT INTO t2 VALUES ('11:22:33.123');
INSERT INTO t2 VALUES ('-11:22:33.123456');
SELECT * FROM t1, t2 WHERE a=b;
DROP TABLE t1, t2;

--echo #
--echo # Testing arithmetic with INT, DECIMAL, FLOAT
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('-00:00:00.000001');
INSERT INTO t1 VALUES ('00:00:00.000000');
INSERT INTO t1 VALUES ('00:00:00.000001');
SELECT a, a + 0, a + 1, a + 1.0, a + 1e0 FROM t1;
CREATE TABLE t2 AS SELECT a + 1 AS i, a + 1.0 AS d, a + 1e0 AS f FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t1, t2;

--echo #
--echo # Testing that TIME behaves as INT/DECIMAL for numeric arithmetic
--echo #
CREATE TABLE t1 (t0 TIME, t3 TIME(3), t6 TIME(6));
CREATE TABLE t2 AS SELECT
  t0 + 1, t3 + 1, t6 + 1,
  t0 - 1, t3 - 1, t6 - 1,
  t0 * 1, t3 * 1, t6 * 1,
  t0 / 1, t3 / 1, t6 / 1,
  TIME'10:10:10' + 1,
  TIME'10:10:10.123' + 1,
  TIME'10:10:10.123456' + 1,
  TIME'10:10:10' - 1,
  TIME'10:10:10.123' - 1,
  TIME'10:10:10.123456' - 1,
  TIME'10:10:10' * 1,
  TIME'10:10:10.123' * 1,
  TIME'10:10:10.123456' * 1,
  TIME'10:10:10' / 1,
  TIME'10:10:10.123' / 1,
  TIME'10:10:10.123456' / 1
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing that TIME behaves as DECIMAL for SUM() and AVG()
--echo #
SET @t='800:11:12.123456';
CREATE TABLE t1 (t0 TIME, t3 TIME(3), t6 TIME(6));
INSERT INTO t1 VALUES (@t, @t, @t);
--query_vertical SELECT MIN(t0), MAX(t0), AVG(t0), SUM(t0) FROM t1
--query_vertical SELECT MIN(t3), MAX(t3), AVG(t3), SUM(t3) FROM t1
--query_vertical SELECT MIN(t6), MAX(t6), AVG(t6), SUM(t6) FROM t1
SET @t='800:11:12.000000';
INSERT INTO t1 VALUES (@t, @t, @t);
--query_vertical SELECT MIN(t0), MAX(t0), AVG(t0), SUM(t0) FROM t1
--query_vertical SELECT MIN(t3), MAX(t3), AVG(t3), SUM(t3) FROM t1
--query_vertical SELECT MIN(t6), MAX(t6), AVG(t6), SUM(t6) FROM t1
CREATE TABLE t2 AS SELECT
  MIN(t0), MAX(t0), AVG(t0), SUM(t0),
  MIN(t3), MAX(t3), AVG(t3), SUM(t3),
  MIN(t6), MAX(t6), AVG(t6), SUM(t6)
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing functions ADDTIME, SUBTIME, TIMEDIFF
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('-01:00:00.000001');
INSERT INTO t1 VALUES ('-01:00:00.000000');
INSERT INTO t1 VALUES ('-00:00:00.000001');
INSERT INTO t1 VALUES ('00:00:00.000000');
INSERT INTO t1 VALUES ('00:00:00.000001');
INSERT INTO t1 VALUES ('01:00:00.000000');
INSERT INTO t1 VALUES ('01:00:00.000001');
SELECT ADDTIME(a, '00:00:00.000001') FROM t1;
SELECT ADDTIME(a, '10:00:00.000001') FROM t1;
SELECT ADDTIME(a, a) FROM t1;
SELECT SUBTIME(a, '00:00:00.000001') FROM t1;
SELECT SUBTIME(a, '10:00:00.000001') FROM t1;
SELECT SUBTIME(a, a) FROM t1;
SELECT TIMEDIFF(a, '00:00:00.000001') FROM t1;
SELECT TIMEDIFF('00:00:00.000001', a) FROM t1;
SELECT TIMEDIFF(a, a) FROM t1;
SELECT TIMEDIFF(CAST(a AS TIME(0)), CAST('10:10:10' AS TIME(0))) FROM t1;
CREATE TABLE t2 AS SELECT
  ADDTIME(a, '00:00:00.000001'),
  ADDTIME(a,a),
  SUBTIME(a, '00:00:00.000001'),
  SUBTIME(a,a),
  TIMEDIFF(a,'00:00:00.000001'),
  TIMEDIFF(a,a),
  TIMEDIFF(CAST(a AS TIME(0)), CAST('10:10:10' AS TIME(0)))
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT MAX(ADDTIME(a, '00:00:00.1')) FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2;
DROP TABLE t2;
DROP TABLE t1;

SELECT TIMEDIFF(TIME'00:00:00', TIME'00:00:00');
SELECT TIMEDIFF(TIME'00:00:00', TIME'00:00:00.0');
SELECT TIMEDIFF(TIME'00:00:00', TIME'00:00:00.00');
SELECT TIMEDIFF(TIME'00:00:00', TIME'00:00:00.000');
SELECT TIMEDIFF(TIME'00:00:00', TIME'00:00:00.0000');
SELECT TIMEDIFF(TIME'00:00:00', TIME'00:00:00.00000');
SELECT TIMEDIFF(TIME'00:00:00', TIME'00:00:00.000000');
SELECT TIMEDIFF(TIME'00:00:00.0', TIME'00:00:00');
SELECT TIMEDIFF(TIME'00:00:00.00', TIME'00:00:00');
SELECT TIMEDIFF(TIME'00:00:00.000', TIME'00:00:00');
SELECT TIMEDIFF(TIME'00:00:00.0000', TIME'00:00:00');
SELECT TIMEDIFF(TIME'00:00:00.00000', TIME'00:00:00');
SELECT TIMEDIFF(TIME'00:00:00.000000', TIME'00:00:00');

CREATE TABLE t1 AS SELECT
  TIMEDIFF(TIME'00:00:00', TIME'00:00:00'),
  TIMEDIFF(TIME'00:00:00', TIME'00:00:00.0'),
  TIMEDIFF(TIME'00:00:00', TIME'00:00:00.00'),
  TIMEDIFF(TIME'00:00:00', TIME'00:00:00.000'),
  TIMEDIFF(TIME'00:00:00', TIME'00:00:00.0000'),
  TIMEDIFF(TIME'00:00:00', TIME'00:00:00.00000'),
  TIMEDIFF(TIME'00:00:00', TIME'00:00:00.000000'),
  TIMEDIFF(TIME'00:00:00.0', TIME'00:00:00'),
  TIMEDIFF(TIME'00:00:00.00', TIME'00:00:00'),
  TIMEDIFF(TIME'00:00:00.000', TIME'00:00:00'),
  TIMEDIFF(TIME'00:00:00.0000', TIME'00:00:00'),
  TIMEDIFF(TIME'00:00:00.00000', TIME'00:00:00'),
  TIMEDIFF(TIME'00:00:00.000000', TIME'00:00:00');
SHOW CREATE TABLE t1;
DROP TABLE t1;

SELECT ADDTIME(TIME'00:00:00', TIME'00:00:00');
SELECT ADDTIME(TIME'00:00:00', TIME'00:00:00.0');
SELECT ADDTIME(TIME'00:00:00', TIME'00:00:00.00');
SELECT ADDTIME(TIME'00:00:00', TIME'00:00:00.000');
SELECT ADDTIME(TIME'00:00:00', TIME'00:00:00.0000');
SELECT ADDTIME(TIME'00:00:00', TIME'00:00:00.00000');
SELECT ADDTIME(TIME'00:00:00', TIME'00:00:00.000000');
SELECT ADDTIME(TIME'00:00:00.0', TIME'00:00:00');
SELECT ADDTIME(TIME'00:00:00.00', TIME'00:00:00');
SELECT ADDTIME(TIME'00:00:00.000', TIME'00:00:00');
SELECT ADDTIME(TIME'00:00:00.0000', TIME'00:00:00');
SELECT ADDTIME(TIME'00:00:00.00000', TIME'00:00:00');
SELECT ADDTIME(TIME'00:00:00.000000', TIME'00:00:00');

SELECT SUBTIME(TIME'00:00:00', TIME'00:00:00');
SELECT SUBTIME(TIME'00:00:00', TIME'00:00:00.0');
SELECT SUBTIME(TIME'00:00:00', TIME'00:00:00.00');
SELECT SUBTIME(TIME'00:00:00', TIME'00:00:00.000');
SELECT SUBTIME(TIME'00:00:00', TIME'00:00:00.0000');
SELECT SUBTIME(TIME'00:00:00', TIME'00:00:00.00000');
SELECT SUBTIME(TIME'00:00:00', TIME'00:00:00.000000');
SELECT SUBTIME(TIME'00:00:00.0', TIME'00:00:00');
SELECT SUBTIME(TIME'00:00:00.00', TIME'00:00:00');
SELECT SUBTIME(TIME'00:00:00.000', TIME'00:00:00');
SELECT SUBTIME(TIME'00:00:00.0000', TIME'00:00:00');
SELECT SUBTIME(TIME'00:00:00.00000', TIME'00:00:00');
SELECT SUBTIME(TIME'00:00:00.000000', TIME'00:00:00');


--echo #
--echo # Testing TIME + INTERVAL
--echo #
SELECT CAST('00:00:00' AS TIME) + INTERVAL 2000 HOUR;
SELECT TIME(CAST('00:00:00' AS TIME) + INTERVAL 34 DAY);
SELECT TIME(CAST('00:00:00' AS TIME) + INTERVAL 35 DAY);
SELECT TIME(CAST('00:00:00' AS TIME) + INTERVAL 1 MONTH);
SELECT TIME(CAST('00:00:00' AS TIME) + INTERVAL 1 YEAR);
SET @@timestamp=UNIX_TIMESTAMP('2001-01-01 00:00:00');
SELECT CAST(CAST('00:00:00.123456' AS TIME(6)) + INTERVAL 1 DAY AS DATETIME(6)); 
SET @@timestamp=default;
SELECT CAST(CAST('2001-01-01 00:00:00.123456' AS DATETIME(6)) + INTERVAL 30 HOUR AS TIME(6)); 
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('-10:00:00.1'), ('00:00:00.1'), ('10:00:00.1');
SELECT a, a + INTERVAL 1 HOUR, a - INTERVAL 1 HOUR FROM t1;
SELECT a, a + INTERVAL 1 SECOND, a - INTERVAL 1 SECOND FROM t1;
SELECT a, a + INTERVAL 1.1 SECOND, a - INTERVAL 1.1 SECOND FROM t1;
CREATE TABLE t2 AS SELECT
a + INTERVAL 1 HOUR,
a - INTERVAL 1 HOUR,
a + INTERVAL 1 SECOND,
a - INTERVAL 1 SECOND,
a + INTERVAL 1.1 SECOND,
a - INTERVAL 1.1 SECOND
FROM t1;
DESCRIBE t2;
DROP TABLE t2;
ALTER TABLE t1 MODIFY a TIME;
SELECT a, a + INTERVAL 1 HOUR, a - INTERVAL 1 HOUR FROM t1;
SELECT a, a + INTERVAL 1 SECOND, a - INTERVAL 1 SECOND FROM t1;
SELECT a, a + INTERVAL 1.1 SECOND, a - INTERVAL 1.1 SECOND FROM t1;
CREATE TABLE t2 AS SELECT
a + INTERVAL 1 HOUR,
a - INTERVAL 1 HOUR,
a + INTERVAL 1 SECOND,
a - INTERVAL 1 SECOND,
a + INTERVAL 1.1 SECOND,
a - INTERVAL 1.1 SECOND
FROM t1;
DESCRIBE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing SEC_TO_TIME
--echo #
CREATE TABLE t1 AS SELECT SEC_TO_TIME(3661), CAST(SEC_TO_TIME(3661) AS CHAR);
SHOW CREATE TABLE t1;
SELECT * FROM t1;
DROP TABLE t1;
CREATE TABLE t1 AS SELECT
  SEC_TO_TIME(3661.1),
  SEC_TO_TIME(3661.11),
  SEC_TO_TIME(3661.111),
  SEC_TO_TIME(3661.1111),
  SEC_TO_TIME(3661.11111),
  SEC_TO_TIME(3661.111111),
  SEC_TO_TIME(3661.1111111);
SHOW CREATE TABLE t1;
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing copy from TIME to TIME(6) and vice versa
--echo #
CREATE TABLE t1 (a TIME(6), b TIME);
INSERT INTO t1 VALUES ('01:02:03.123456', '00:00:00');
UPDATE t1 SET b=a;
SELECT * FROM t1;
UPDATE t1 SET b='10:11:12';
UPDATE t1 SET a=b;
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER from TIME(6) to TIME and vice versa
--echo #
CREATE TABLE t1 (a TIME);
INSERT INTO t1 VALUES ('01:02:03');
ALTER TABLE t1 MODIFY a TIME(6);
SELECT * FROM t1;
UPDATE t1 SET a='01:03:03.456';
SELECT * FROM t1;
ALTER TABLE t1 MODIFY a TIME;
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER between TIME and INT
--echo #
CREATE TABLE t1 (a TIME, b TIME(6));
INSERT INTO t1 VALUES ('11:22:33', '11:22:33');
ALTER TABLE t1 MODIFY a INT, MODIFY b INT;
SELECT * FROM t1;
UPDATE t1 SET a=112233, b=112233;
ALTER TABLE t1 MODIFY a TIME, MODIFY b TIME(6);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER between TIME and DOUBLE
--echo #
CREATE TABLE t1 (a TIME, b TIME(6));
INSERT INTO t1 VALUES ('11:22:33', '11:22:33.1234');
ALTER TABLE t1 MODIFY a DOUBLE, MODIFY b DOUBLE;
SELECT * FROM t1;
UPDATE t1 SET a=112233, b=112233.1234;
ALTER TABLE t1 MODIFY a TIME, MODIFY b TIME(6);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER between TIME and DECIMAL
--echo #
CREATE TABLE t1 (a TIME, b TIME(6));
INSERT INTO t1 VALUES ('11:22:33', '11:22:33.1234');
ALTER TABLE t1 MODIFY a DECIMAL(20,6), MODIFY b DECIMAL(20,6);
SELECT * FROM t1;
UPDATE t1 SET a=112233, b=112233.1234;
ALTER TABLE t1 MODIFY a TIME, MODIFY b TIME(6);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER from TIME to various other temporal types
--echo #
SET @@timestamp=UNIX_TIMESTAMP('2001-01-01 00:00:00');
CREATE TABLE t1 (a TIME(6), b TIME(6), c TIME(6));
INSERT INTO t1 VALUES ('10:10:10.1', '10:10:10.1', '10:10:10.1');
INSERT INTO t1 VALUES ('34:10:10.1', '34:10:10.1', '34:10:10.1');
ALTER TABLE t1 MODIFY a DATETIME(6), MODIFY b TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), MODIFY c DATE;
SELECT * FROM t1;
DROP TABLE t1;
SET @@timestamp=default;

--echo #
--echo # Testing UPDATE from TIME to various other temporal types
--echo #
SET @@timestamp=UNIX_TIMESTAMP('2001-01-01 00:00:00');
CREATE TABLE t1 (t6 TIME(6), d DATE, ts6 TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), dt6 DATETIME(6));
INSERT INTO t1 (t6) VALUES ('10:10:10.1'), ('34:10:10.1');
UPDATE t1 SET d=t6, ts6=t6, dt6=t6;
SELECT * FROM t1;
DROP TABLE t1;
SET @@timestamp=default;


--echo #
--echo # Testing EXPLAIN SELECT for CAST(TIME(N))
--echo #
EXPLAIN SELECT CAST('10:10:10' AS TIME);
EXPLAIN SELECT CAST('10:10:10' AS TIME(0));
EXPLAIN SELECT CAST('10:10:10' AS TIME(1));
EXPLAIN SELECT CAST('10:10:10' AS TIME(2));
EXPLAIN SELECT CAST('10:10:10' AS TIME(3));
EXPLAIN SELECT CAST('10:10:10' AS TIME(4));
EXPLAIN SELECT CAST('10:10:10' AS TIME(5));
EXPLAIN SELECT CAST('10:10:10' AS TIME(6));


--echo #
--echo # Testing CAST with bad FSPs
--echo #
--error ER_PARSE_ERROR
SELECT CAST(1 AS TIME(-1));
--error ER_TOO_BIG_PRECISION
SELECT CAST(1 AS TIME(7));
--error ER_TOO_BIG_PRECISION
SELECT CAST(1 AS TIME(31));

--echo #
--echo # Testing conversion from TIME(6) to INT
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('10:10:58.499');
INSERT INTO t1 VALUES ('10:10:58.999');
INSERT INTO t1 VALUES ('10:10:59.499');
INSERT INTO t1 VALUES ('10:10:59.999');
INSERT INTO t1 VALUES ('-10:10:58.499');
INSERT INTO t1 VALUES ('-10:10:58.999');
INSERT INTO t1 VALUES ('-10:10:59.499');
INSERT INTO t1 VALUES ('-10:10:59.999');
ALTER TABLE t1 ADD b BIGINT, ADD c TIME(6);
UPDATE t1 SET b=a, c=a;
ALTER TABLE t1 MODIFY c BIGINT;
SELECT a, CAST(a AS SIGNED), b, c FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing INT value and CAST of TIME(6) to various other types
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('11:22:33.123');
INSERT INTO t1 VALUES ('11:22:33.999');
INSERT INTO t1 VALUES ('-11:22:33.123');
INSERT INTO t1 VALUES ('-11:22:33.999');
INSERT INTO t1 VALUES ('-836:56:56.999999');
SELECT a << 0 FROM t1;
SELECT CAST(a AS SIGNED) FROM t1;
SELECT CAST(a AS UNSIGNED) FROM t1;
SELECT CAST(a AS DECIMAL(20,6)) FROM t1;
SELECT CAST(a AS DECIMAL(20,3)) FROM t1;
SELECT CAST(a AS CHAR) FROM t1;
SELECT CAST(a AS CHAR(6)) FROM t1;
CREATE TABLE t2 AS SELECT CAST(a AS CHAR) AS a FROM t1;
SHOW CREATE TABLE t2;
SELECT a, LENGTH(a) FROM t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing DEFAULT value
--echo #
CREATE TABLE t1 (a TIME(6) NOT NULL DEFAULT '11:22:33.123456');
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Mixing TIME(6) with another TIME(6) in IF
--echo #
CREATE TABLE t1 (a TIME(6), b TIME(6));
CREATE TABLE t2 AS SELECT IF(1, a, b) AS a FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Mixing TIME(N) with TIME(M) in IF
--echo #
CREATE TABLE t1 (a TIME(1), b TIME(2));
CREATE TABLE t2 AS SELECT IF(1, a, b) AS a FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Mixing varios TIME(N) in CASE
--echo #
CREATE TABLE t1 (t0 TIME, t1 TIME(1), t3 TIME(3), t4 TIME(4), t6 TIME(6));
CREATE TABLE t2 AS SELECT
  CASE WHEN 1 THEN t0 ELSE t1 END,
  CASE WHEN 1 THEN t0 ELSE t3 END,
  CASE WHEN 1 THEN t0 ELSE t4 END,
  CASE WHEN 1 THEN t0 ELSE t6 END,
  CASE WHEN 1 THEN t1 ELSE t0 END,
  CASE WHEN 1 THEN t1 ELSE t3 END,
  CASE WHEN 1 THEN t1 ELSE t4 END,
  CASE WHEN 1 THEN t1 ELSE t6 END,
  CASE WHEN 1 THEN t3 ELSE t0 END,
  CASE WHEN 1 THEN t3 ELSE t1 END,
  CASE WHEN 1 THEN t3 ELSE t4 END,
  CASE WHEN 1 THEN t3 ELSE t6 END,
  CASE WHEN 1 THEN t4 ELSE t0 END,
  CASE WHEN 1 THEN t4 ELSE t1 END,
  CASE WHEN 1 THEN t4 ELSE t3 END,
  CASE WHEN 1 THEN t4 ELSE t6 END,
  CASE WHEN 1 THEN t6 ELSE t0 END,
  CASE WHEN 1 THEN t6 ELSE t1 END,
  CASE WHEN 1 THEN t6 ELSE t3 END,
  CASE WHEN 1 THEN t6 ELSE t4 END
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;


--echo #
--echo # Mixing various TIME(N) in COALESCE
--echo #
CREATE TABLE t1 (a TIME(6), b TIME(6), c TIME, d TIME, e TIME(3), f TIME(4));
CREATE TABLE t2 AS SELECT
  COALESCE(a,b), COALESCE(c,b),
  COALESCE(c,d), COALESCE(e,f),
  COALESCE(c,e), COALESCE(c,f)
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT
  CONCAT(COALESCE(a, b)), CONCAT(COALESCE(c, b)),
  CONCAT(COALESCE(c, d)), CONCAT(COALESCE(e, f)),
  CONCAT(COALESCE(c, e)), CONCAT(COALESCE(c, f))
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Mixing TIME(6) NOT NULL with another TIME(6) NOT NULL
--echo #
CREATE TABLE t1 (a TIME(6) NOT NULL, b TIME(6) NOT NULL);
CREATE TABLE t2 AS SELECT IF(1, a, b) AS a FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # UNION between TIME(6) and TIME(6)
--echo #
CREATE TABLE t1 (a TIME(6) NOT NULL, b TIME(6) NOT NULL);
INSERT INTO t1 VALUES ('11:22:33.123456', '00:11:22.123456');
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT b FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2 ORDER BY a;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # UNION between TIME(6) and TIME
--echo #
CREATE TABLE t1 (t0 TIME NOT NULL, t6 TIME(6) NOT NULL, t3 TIME(3), t4 TIME(4));
INSERT INTO t1 VALUES
  ('11:22:33.123456', '00:11:22.123456', '00:11:23.123', '00:11:24.123');
CREATE TABLE t2 AS SELECT t0 FROM t1 UNION SELECT t6 FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2 ORDER BY t0;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT t6 FROM t1 UNION SELECT t0 FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2 ORDER BY t6;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT t3 FROM t1 UNION SELECT t4 FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2 ORDER BY t3;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # UNION between TIME(6) and various other types
--echo #
CREATE TABLE t1 (a TIME(6), b INT, c DOUBLE, d DECIMAL(20,6), e VARCHAR(20));
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT b FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT c FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT d FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT e FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing TIME(6) field in combination with TIME type functions
--echo #
CREATE TABLE t1 (a TIME(6), b TIME(6));
INSERT INTO t1 VALUES ('10:00:00.123456', '00:00:00.123456');
INSERT INTO t1 VALUES ('10:00:00', '00:00:00');
SELECT * FROM t1 WHERE a=ADDTIME(b, '10:00:00');
SELECT * FROM t1 WHERE b=TIMEDIFF(a, '10:00:00');
SELECT a FROM t1 WHERE a=MAKETIME(10,0,0);
SELECT a FROM t1 WHERE a=SEC_TO_TIME(36000);
DELETE FROM t1;
INSERT INTO t1 VALUES (CURTIME(), CURTIME());
SELECT COUNT(*) FROM t1 WHERE a <= CURTIME();
DROP TABLE t1;

--echo #
--echo # ORDER BY a TIME(6) function
--echo #
CREATE TABLE t1(a TIME(6));
INSERT INTO t1 (a) VALUES ('835:00:00.000002');
INSERT INTO t1 (a) VALUES ('835:00:00.000000');
INSERT INTO t1 (a) VALUES ('835:00:00.000001');
INSERT INTO t1 (a) VALUES ('00:00:00.000003');
INSERT INTO t1 (a) VALUES ('00:00:00.000000');
INSERT INTO t1 (a) VALUES ('00:00:00.000001');
INSERT INTO t1 (a) VALUES ('-836:00:00.000000');
INSERT INTO t1 (a) VALUES ('-836:00:00.000001');
INSERT INTO t1 (a) VALUES ('-836:00:00.000002');
SELECT * FROM t1 ORDER BY ADDTIME(a, '00:00:00');
SELECT * FROM t1 ORDER BY TIMEDIFF(a, '00:00:00');
SELECT * FROM t1 ORDER BY ADDTIME(a, '00:00:00') DESC;
SELECT * FROM t1 ORDER BY TIMEDIFF(a, '00:00:00') DESC;
DROP TABLE t1;


--echo #
--echo # Testing partitions
--echo #
CREATE TABLE t1 (a TIME(6)) PARTITION BY KEY(a) PARTITIONS 4;
INSERT INTO t1 VALUES ('00:00:00.000000');
INSERT INTO t1 VALUES ('00:00:00.000001');
INSERT INTO t1 VALUES ('00:00:00.000002');
INSERT INTO t1 VALUES ('00:00:00.000003');
INSERT INTO t1 VALUES ('00:00:00.000004');
INSERT INTO t1 VALUES ('00:00:00.000005');
INSERT INTO t1 VALUES ('00:00:00.000006');
INSERT INTO t1 VALUES ('00:00:00.000010');
INSERT INTO t1 VALUES ('00:00:00.000011');
INSERT INTO t1 VALUES ('00:00:00.000012');
INSERT INTO t1 VALUES ('00:00:00.000013');
INSERT INTO t1 VALUES ('00:00:00.000014');
INSERT INTO t1 VALUES ('00:00:00.000015');
INSERT INTO t1 VALUES ('00:00:00.000016');
INSERT INTO t1 VALUES ('00:00:00.000110');
INSERT INTO t1 VALUES ('00:00:00.000111');
INSERT INTO t1 VALUES ('00:00:00.000112');
INSERT INTO t1 VALUES ('00:00:00.000113');
INSERT INTO t1 VALUES ('00:00:00.000114');
INSERT INTO t1 VALUES ('00:00:00.000115');
INSERT INTO t1 VALUES ('00:00:00.000116');
INSERT INTO t1 VALUES ('00:00:00.000210');
INSERT INTO t1 VALUES ('00:00:00.000211');
INSERT INTO t1 VALUES ('00:00:00.000212');
INSERT INTO t1 VALUES ('00:00:00.000213');
INSERT INTO t1 VALUES ('00:00:00.000214');
INSERT INTO t1 VALUES ('00:00:00.000215');
INSERT INTO t1 VALUES ('00:00:00.000216');
INSERT INTO t1 VALUES ('00:00:01.000000');
INSERT INTO t1 VALUES ('00:00:01.000001');
INSERT INTO t1 VALUES ('00:00:01.000002');
INSERT INTO t1 VALUES ('00:00:01.000003');
INSERT INTO t1 VALUES ('00:00:01.000004');
INSERT INTO t1 VALUES ('00:00:01.000005');
INSERT INTO t1 VALUES ('00:00:01.000006');
INSERT INTO t1 VALUES ('00:00:02.000000');
INSERT INTO t1 VALUES ('00:00:02.000001');
INSERT INTO t1 VALUES ('00:00:02.000002');
INSERT INTO t1 VALUES ('00:00:02.000003');
INSERT INTO t1 VALUES ('00:00:02.000004');
INSERT INTO t1 VALUES ('00:00:02.000005');
INSERT INTO t1 VALUES ('00:00:02.000006');
SELECT * FROM t1 PARTITION(p0);
SELECT * FROM t1 PARTITION(p1);
SELECT * FROM t1 PARTITION(p2);
SELECT * FROM t1 PARTITION(p3);
ALTER TABLE t1 PARTITION BY RANGE COLUMNS (a)
(
  PARTITION p0 VALUES LESS THAN ('00:00:00.000200'),
  PARTITION p1 VALUES LESS THAN ('00:00:01'),
  PARTITION p2 VALUES LESS THAN ('00:00:02.000003'),
  PARTITION p3 VALUES LESS THAN MAXVALUE
);
SELECT * FROM t1 PARTITION(p0);
SELECT * FROM t1 PARTITION(p1);
SELECT * FROM t1 PARTITION(p2);
SELECT * FROM t1 PARTITION(p3);
DROP TABLE t1;

--echo #
--echo # Checking that a TIME literal can be a partition LESS value
--echo #
CREATE TABLE t1 (s1 TIME(6))
  PARTITION BY RANGE COLUMNS (s1)
  (PARTITION p1 VALUES LESS THAN (TIME'01:01:01.000001'));
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Testing MAKETIME()
--echo #
CREATE TABLE t1 AS
SELECT
  CAST(MAKETIME(1, 1, 1) AS TIME) as a,
  CAST(MAKETIME(1, 1, 1) AS CHAR) as b;
SELECT MAKETIME(1, 1, 1);
SELECT MAKETIME(1, 1, 1.0);
SELECT MAKETIME(1, 1, 1.00);
SELECT MAKETIME(1, 1, 1.000);
SELECT MAKETIME(1, 1, 1.0000);
SELECT MAKETIME(1, 1, 1.00000);
SELECT MAKETIME(1, 1, 1.000000);
SELECT MAKETIME(0, 0, -0.123);
SHOW CREATE TABLE t1;
DROP TABLE t1;
CREATE TABLE t1 AS SELECT
  MAKETIME(1, 1, 1),
  MAKETIME(1, 1, 1.0),
  MAKETIME(1, 1, 1.00),
  MAKETIME(1, 1, 1.000),
  MAKETIME(1, 1, 1.0000),
  MAKETIME(1, 1, 1.00000),
  MAKETIME(1, 1, 1.000000);
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding with MAKETIME()
--echo #
CREATE TABLE t1 (hour INT, minute INT, second DECIMAL(23,7));
INSERT INTO t1 VALUES
  (836, 59, 59.999999),
  (836, 59, 59.9999990),
  (836, 59, 59.9999991),
  (836, 59, 59.9999992),
  (836, 59, 59.9999993),
  (836, 59, 59.9999995),
  (836, 59, 59.9999996),
  (836, 59, 59.9999997),
  (836, 59, 59.9999998),
  (836, 59, 59.9999999);
SELECT hour, minute, second, MAKETIME(hour, minute, second) FROM t1;
SELECT hour + 1, minute, second, MAKETIME(hour + 1, minute, second) FROM t1;
SELECT -hour, minute, second, MAKETIME(-hour, minute, second) FROM t1;
SELECT -hour - 1, minute, second, MAKETIME(-hour - 1, minute, second) FROM t1;
DROP TABLE t1;
SELECT MAKETIME(838, 59, 59.0000005);
SELECT MAKETIME(838, 59, 59.00000056);
SELECT MAKETIME(838, 59, 59.000000567);
SELECT MAKETIME(838, 59, 59.0000005678);
SELECT MAKETIME(838, 59, 59.00000056789);

--echo #
--echo # Testing CAST to TIME and TIME(6)
--echo #
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1 AS SELECT
CAST(1 AS TIME),
CAST(100000000 AS TIME),
CAST(1.1 AS TIME),
CAST('1' AS TIME),
CAST(1e0 AS TIME),
CAST(1 AS TIME(0)),
CAST(100000000 AS TIME(0)),
CAST(1.1 AS TIME(0)),
CAST('1' AS TIME(0)),
CAST(1e0 AS TIME(0)),
CAST(1 AS TIME(6)),
CAST(100000000 AS TIME(6)),
CAST(1.1 AS TIME(6)),
CAST('1' AS TIME(6)),
CAST(1e0 AS TIME(6));
SHOW CREATE TABLE t1;
DROP TABLE t1;
SET sql_mode = default;
CREATE TABLE t1 (a TIME, b TIME(6));
INSERT INTO t1 VALUES ('11:22:33', '11:22:33.123456');
CREATE TABLE t2 AS SELECT CAST(a AS TIME), CAST(b AS TIME) FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT CAST(a AS TIME(0)), CAST(b AS TIME(0)) FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT CAST(a AS TIME(6)), CAST(b AS TIME(6)) FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;

--echo #
--echo # Testing Item_typecast_time::val_str
--echo #
SELECT CONCAT(CAST(a AS TIME(0))), CONCAT(CAST(b AS TIME(0))) FROM t1;
SELECT CONCAT(CAST(a AS TIME(6))), CONCAT(CAST(b AS TIME(6))) FROM t1;
SELECT CONCAT(CAST(a AS TIME)), CONCAT(CAST(b AS TIME)) FROM t1;

--echo #
--echo # Testing Item_typecast_time::get_time
--echo #
SELECT CAST(a AS TIME(0)), CAST(b AS TIME(0)) FROM t1;
SELECT CAST(a AS TIME(6)), CAST(b AS TIME(6)) FROM t1;
SELECT CAST(a AS TIME), CAST(b AS TIME) FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing Item_typecast_time::val_int
--echo #
SELECT CAST('10:11:12' AS TIME(0)) + 1;

--echo #
--echo # Testing Item_typecast_time::val_decimal
--echo #
SELECT CAST('10:11:12' AS TIME(6)) + 1;

--echo #
--echo # Testing function TIME(expr)
--echo #
CREATE TABLE t1 AS SELECT
  TIME(101),
  TIME(101.1),
  TIME(101.12),
  TIME(101.123),
  TIME(101.1234),
  TIME(101.12345),
  TIME(101.123456),
  TIME('00:01:01'),
  TIME('00:01:01.1'),
  TIME('00:01:01.12'),
  TIME('00:01:01.123'),
  TIME('00:01:01.1234'),
  TIME('00:01:01.12345'),
  TIME('00:01:01.123456'),
  TIME(CONCAT('00:01:01', '')),
  TIME(CONCAT('00:01:01', '.1')),
  TIME(CONCAT('00:01:01', '.12')),
  TIME(CONCAT('00:01:01', '.123')),
  TIME(CONCAT('00:01:01', '.1234')),
  TIME(CONCAT('00:01:01', '.12345')),
  TIME(CONCAT('00:01:01', '.123456')),
  TIME(TIME'00:01:01'),
  TIME(TIME'00:01:01.1'),
  TIME(TIME'00:01:01.12'),
  TIME(TIME'00:01:01.123'),
  TIME(TIME'00:01:01.1234'),
  TIME(TIME'00:01:01.12345'),
  TIME(TIME'00:01:01.123456'),
  TIME(TIMESTAMP('2001-01-01 00:00:00')),
  TIME(TIMESTAMP('2001-01-01 00:00:00.1')),
  TIME(TIMESTAMP('2001-01-01 00:00:00.12')),
  TIME(TIMESTAMP('2001-01-01 00:00:00.123')),
  TIME(TIMESTAMP('2001-01-01 00:00:00.1234')),
  TIME(TIMESTAMP('2001-01-01 00:00:00.12345')),
  TIME(TIMESTAMP('2001-01-01 00:00:00.123456'));
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Testing comparison between a TIME(6) field and a TIME(N) type cast
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('10:11:12.000000');
INSERT INTO t1 VALUES ('10:11:12.000001');
INSERT INTO t1 VALUES ('10:11:12.000002');
INSERT INTO t1 VALUES ('10:11:12.000003');
SELECT * FROM t1 WHERE a=CAST('10:11:12.000003' AS TIME(6));
SELECT * FROM t1 WHERE a=CAST('10:11:12.000003' AS TIME(0));
DROP TABLE t1;

--echo #
--echo # Testing comparison between CAST(x AS TIME(N))
--echo #
SELECT CAST('10:11:12.123' AS TIME(0)) = 101112;
SELECT CAST(101112.123 AS TIME(0)) = 101112;
SELECT CAST(101112.123e0 AS TIME(0)) = 101112;

--echo #
--echo # Testing Item_typecast_time::val_int_packed()
--echo #
CREATE TABLE t1 (a TIME, b TIME(6));
INSERT INTO t1 VALUES ('24:00:00', '24:00:00.123456');
SELECT CAST('24:00:00' AS TIME) = (SELECT a FROM t1);
SELECT CAST('24:00:00' AS TIME(6)) = (SELECT a FROM t1);
SELECT CAST('24:00:00.123456' AS TIME(0)) = (SELECT a FROM t1);
SELECT CAST('24:00:00.123456' AS TIME(6)) = (SELECT b FROM t1);
DROP TABLE t1;

--echo #
--echo # Testing function MICROSECOND
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('-11:12:13.000001');
INSERT INTO t1 VALUES ('11:12:13.000001');
INSERT INTO t1 VALUES ('11:12:13.100001');
INSERT INTO t1 VALUES ('11:12:13.999999');
SELECT a, MICROSECOND(a) FROM t1;
SELECT a, EXTRACT(MICROSECOND FROM a) FROM t1;
DROP TABLE t1;
SELECT MICROSECOND(CAST(123.456 AS TIME(6)));
SELECT EXTRACT(MICROSECOND FROM CAST(123.456 AS TIME(6)));

--echo #
--echo # Testing PS
--echo #
CREATE TABLE t1 (a TIME(6));
PREPARE stmt FROM 'INSERT INTO t1 VALUES (?)';
SET @a='11:22:33.123456';
SET @b=112233.123456;
SET @c=112233.123456e0;
EXECUTE stmt USING @a;
DEALLOCATE PREPARE stmt;
SELECT * FROM t1;
PREPARE stmt FROM 'SELECT * FROM t1 WHERE a=?';
EXECUTE stmt USING @a;
EXECUTE stmt USING @b;
EXECUTE stmt USING @c;
DEALLOCATE PREPARE stmt;
DROP TABLE t1;

--echo #
--echo # Testing TIME(6) and user variables
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('11:22:33.123456');
SET @a='11:22:33.123456';
SELECT * FROM t1 WHERE a=@a;
SET @a=112233.123456;
SELECT * FROM t1 WHERE a=@a;
SET @a=112233.123456e0;
SELECT * FROM t1 WHERE a=@a;
SET @a=NULL;
SELECT a INTO @a FROM t1 LIMIT 1;
SELECT @a;
DROP TABLE t1;

## MEMORY does not support BLOB
if (`SELECT @@default_storage_engine != 'MEMORY'`)
{
  CREATE TABLE t1 AS SELECT @a AS a;
  SHOW CREATE TABLE t1;
  SELECT * FROM t1;
  DROP TABLE t1;
}

--echo #
--echo # Testing SP
--echo #
DELIMITER //;
CREATE PROCEDURE p1 ()
BEGIN
  DECLARE a TIME(6);
  SET a='11:22:33.123';
  SELECT a;
END//
SHOW CREATE PROCEDURE p1//
CALL p1//
DROP PROCEDURE p1//
CREATE PROCEDURE p1 (a TIME(6))
BEGIN
  SELECT a;
END//
SHOW CREATE PROCEDURE p1//
CALL p1(112233)//
CALL p1(112233.123)//
CALL p1(112233.123e0)//
CALL p1('11:22:33.123')//
DROP PROCEDURE p1//
CREATE PROCEDURE p1()
BEGIN
  DECLARE a TIME(6);
  CREATE TABLE t1 AS SELECT a;
  SHOW CREATE TABLE t1;
  DROP TABLE t1;
END//
SHOW CREATE PROCEDURE p1//
CALL p1//
DROP PROCEDURE p1//
CREATE FUNCTION p1 (a TIME(6)) RETURNS TIME(6)
BEGIN
  RETURN a;
END//
SHOW CREATE FUNCTION p1//
SELECT p1(112233.123)//
DROP FUNCTION p1//
DELIMITER ;//


--echo #
--echo # Testing INFORMATION_SCHEMA.COLUMNS
--echo #
CREATE TABLE t1 (a TIME(6));
--replace_column 19 #
--query_vertical SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='t1';
DROP TABLE t1;

--echo #
--echo # SELECT from a subquery
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('00:00:00.123456'), ('11:11:11.123456');
SELECT * FROM (SELECT * FROM t1) t12;
DROP TABLE t1;

--echo #
--echo # Testing IN and = subqueries
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('00:00:00.123456'), ('11:11:11.123456');
SELECT * FROM t1 WHERE a IN (SELECT MIN(a) FROM t1);
SELECT * FROM t1 WHERE a = (SELECT a FROM t1 ORDER BY a DESC LIMIT 1);
DROP TABLE t1;

--echo #
--echo # Testing IN subquery + GROUP
--echo #
CREATE TABLE t1 (id INT, a TIME(6));
INSERT INTO t1 VALUES (1,'00:00:00.123456'), (1,'11:00:00.123456');
INSERT INTO t1 VALUES (2,'00:01:00.123456'), (2,'11:01:00.123456');
INSERT INTO t1 VALUES (3, NULL);
SELECT * FROM t1 WHERE a IN (SELECT MAX(a) FROM t1 GROUP BY id);
DROP TABLE t1;

--echo #
--echo # Testing VIEW
--echo #
CREATE TABLE t1 (a TIME(6));
INSERT INTO t1 VALUES ('00:00:00.123456'), ('11:11:11.123456');
CREATE VIEW v1 AS SELECT a FROM t1;
SELECT * FROM v1 WHERE a='00:00:00.123456';
SELECT MIN(a), MAX(a) FROM v1;
SELECT * FROM t1 WHERE a IN (SELECT MAX(a) FROM v1);
DROP VIEW v1;
CREATE VIEW v1 AS SELECT MAX(a) AS a FROM t1;
SELECT * FROM v1;
SELECT * FROM t1 WHERE a=(SELECT a FROM v1);
DROP VIEW v1;
DROP TABLE t1;

--echo #
--echo # Testing hybrid functions in TIME context
--echo #

SELECT DATE_ADD(TIME'00:00:00.0', INTERVAL 10.1 SECOND);
SELECT DATE_ADD(TIME'00:00:00.0', INTERVAL 10000000.1 SECOND);
SELECT DATE_ADD(TIME'00:00:00.0', INTERVAL 100000000000000000.1 SECOND);
SELECT DATE_ADD(TIME'00:00:00.0', INTERVAL 1000000000000000000000.1 SECOND);

CREATE TABLE t1 AS SELECT
  DATE_ADD(TIME'00:00:00', INTERVAL 1 SECOND) AS t0s0,
  DATE_ADD(TIME'00:00:00', INTERVAL 1.1 SECOND) AS t0s1,
  DATE_ADD(TIME'00:00:00', INTERVAL 1.12 SECOND) AS t0s2,
  DATE_ADD(TIME'00:00:00', INTERVAL 1.123 SECOND) AS t0s3,
  DATE_ADD(TIME'00:00:00', INTERVAL 1.1234 SECOND) AS t0s4,
  DATE_ADD(TIME'00:00:00', INTERVAL 1.12345 SECOND) AS t0s5,
  DATE_ADD(TIME'00:00:00', INTERVAL 1.123456 SECOND) AS t0s6,
  DATE_ADD(TIME'00:00:00.1', INTERVAL 1 SECOND) AS t1s0,
  DATE_ADD(TIME'00:00:00.12', INTERVAL 1 SECOND) AS t2s0,
  DATE_ADD(TIME'00:00:00.123', INTERVAL 1 SECOND) AS t3s0,
  DATE_ADD(TIME'00:00:00.1234', INTERVAL 1 SECOND) AS t4s0,
  DATE_ADD(TIME'00:00:00.12345', INTERVAL 1 SECOND) AS t5s0,
  DATE_ADD(TIME'00:00:00.123456', INTERVAL 1 SECOND) AS t6s0,
  DATE_ADD(TIME'00:00:00', INTERVAL 1 MICROSECOND) AS t0ms;
SHOW CREATE TABLE t1;
DROP TABLE t1;

CREATE TABLE t1 AS SELECT
  ADDTIME(TIME'00:00:00', TIME'00:00:01') AS t0s0,
  ADDTIME(TIME'00:00:00', TIME'00:00:01.1') AS t0s1,
  ADDTIME(TIME'00:00:00', TIME'00:00:01.12') AS t0s2,
  ADDTIME(TIME'00:00:00', TIME'00:00:01.123') AS t0s3,
  ADDTIME(TIME'00:00:00', TIME'00:00:01.1234') AS t0s4,
  ADDTIME(TIME'00:00:00', TIME'00:00:01.12345') AS t0s5,
  ADDTIME(TIME'00:00:00', TIME'00:00:01.123457') AS t0s6,
  ADDTIME(TIME'00:00:00.1', TIME '00:00:00') AS t1s0,
  ADDTIME(TIME'00:00:00.12', TIME '00:00:00') AS t2s0,
  ADDTIME(TIME'00:00:00.123', TIME '00:00:00') AS t3s0,
  ADDTIME(TIME'00:00:00.1234', TIME '00:00:00') AS t4s0,
  ADDTIME(TIME'00:00:00.12345', TIME '00:00:00') AS t5s0,
  ADDTIME(TIME'00:00:00.123456', TIME '00:00:00') AS t6s0;
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Testing that CAST from various fields to TIME rounds.
--echo # This tests Field::get_time().
CREATE TABLE t1 (a VARCHAR(32));
INSERT INTO t1 VALUES
('838:59:58.0000009'), ('838:59:58.9'),
('-838:59:58.0000009'), ('-838:59:58.9'),
('10:10:10.9999994'), ('10:10:10.9999995'),
('-10:10:10.9999994'), ('-10:10:10.9999995');
SELECT a, CAST(a AS TIME), CAST(a AS TIME(6)) FROM t1;
DROP TABLE t1;
CREATE TABLE t1 (a DECIMAL(30,7));
INSERT INTO t1 VALUES
(8385958.0000009), (8385958.9),
(-8385958.0000009), (-8385958.9),
(101010.9999994), (101010.9999995),
(-101010.9999994), (-101010.9999995);
SELECT a, CAST(a AS TIME), CAST(a AS TIME(6)) FROM t1;
ALTER TABLE t1 MODIFY a DOUBLE;
SELECT a, CAST(a AS TIME), CAST(a AS TIME(6)) FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing that Item::get_time() rounds
--echo #
query_vertical SELECT
  CAST('10:10:10.9999994' AS TIME),
  CAST('10:10:10.9999995' AS TIME),
  CAST('10:10:10.9999994' AS TIME(6)),
  CAST('10:10:10.9999995' AS TIME(6)),
  CAST(101010.9999994 AS TIME),
  CAST(101010.9999995 AS TIME),
  CAST(101010.9999994 AS TIME(6)),
  CAST(101010.9999995 AS TIME(6));

--echo #
--echo # Testing that comparison rounds
--echo #
CREATE TABLE t1 (t0 TIME, t6 TIME(6));
INSERT INTO t1 VALUES ('00:00:00', '00:00:00.999999');
INSERT INTO t1 VALUES ('00:00:01', '00:00:01.000000');
SELECT t0 FROM t1 WHERE t6='00:00:00.9999998';
SELECT t6 FROM t1 WHERE t6='00:00:00.9999998';
DROP TABLE t1;

--echo #
--echo # Testing that EXTRACT rounds
--echo #
query_vertical SELECT
  EXTRACT(MICROSECOND FROM '00:00:00.9999994'),
  EXTRACT(MICROSECOND FROM '00:00:00.9999995'),
  EXTRACT(MICROSECOND FROM 0.9999994),
  EXTRACT(MICROSECOND FROM 0.9999995);


###################################################################

--echo #
--echo # Testing that DATETIME(0) is a synonym to non-fractional DATETIME
--echo #

CREATE TABLE t1 (a DATETIME(0) NOT NULL);
SHOW CREATE TABLE t1;
DROP TABLE t1;


--echo #
--echo # Testing internal representation format for DATETIME(N)
--echo #
CREATE TABLE t1 (a6 VARCHAR(32));
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000001');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000002');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000003');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000004');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000005');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000006');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000007');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000008');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000009');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000010');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000011');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000012');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000013');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000014');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000015');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000016');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000099');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000999');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.009999');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.099999');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.100000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.900000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.990000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.999000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.999900');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.999990');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.999999');

INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000001');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000002');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000003');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000004');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000005');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000006');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000007');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000008');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000009');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000010');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000011');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000012');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000013');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000014');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000015');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000016');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000099');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.000999');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.009999');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.090000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.099999');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.100000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.900000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.990000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.999000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.999900');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.999990');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.999999');

INSERT INTO t1 VALUES ('0000-00-00 00:01:00.000001');
INSERT INTO t1 VALUES ('0000-00-00 00:01:00.000008');
INSERT INTO t1 VALUES ('0000-00-00 00:01:00.000015');
INSERT INTO t1 VALUES ('0000-00-00 00:01:00.000016');
INSERT INTO t1 VALUES ('0000-00-00 00:01:00.000099');
INSERT INTO t1 VALUES ('0000-00-00 00:01:00.000999');
INSERT INTO t1 VALUES ('0000-00-00 00:01:00.009999');
INSERT INTO t1 VALUES ('0000-00-00 00:01:00.099999');
INSERT INTO t1 VALUES ('0000-00-00 00:01:00.100000');
INSERT INTO t1 VALUES ('0000-00-00 00:01:00.900000');
INSERT INTO t1 VALUES ('0000-00-00 00:01:00.999999');

INSERT INTO t1 VALUES ('0000-00-00 01:00:00.000001');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.000008');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.000015');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.000016');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.000099');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.000999');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.009999');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.099999');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.100000');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.900000');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.990000');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.999000');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.999900');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.999990');
INSERT INTO t1 VALUES ('0000-00-00 01:00:00.999999');

INSERT INTO t1 VALUES ('1001-01-01 00:00:00.000001');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.000008');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.000015');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.000016');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.000099');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.000999');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.009999');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.099999');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.100000');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.900000');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.990000');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.999000');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.999900');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.999990');
INSERT INTO t1 VALUES ('1001-01-01 00:00:00.999999');

INSERT INTO t1 VALUES ('2001-01-01 23:59:58.000001');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.000008');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.000015');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.000016');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.000099');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.000999');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.009999');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.099999');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.100000');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.900000');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.990000');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.999000');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.999900');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.999990');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.999999');
INSERT INTO t1 VALUES ('2001-01-01 23:59:59.000000');

INSERT INTO t1 VALUES ('9999-12-31 23:59:58.000001');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.000008');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.000015');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.000016');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.000099');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.000999');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.009999');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.099999');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.100000');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.900000');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.990000');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.999000');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.999900');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.999990');
INSERT INTO t1 VALUES ('9999-12-31 23:59:58.999999');
INSERT INTO t1 VALUES ('9999-12-31 23:59:59.000000');

ALTER TABLE t1
  ADD a0 VARCHAR(32),
  ADD a1 VARCHAR(32), ADD a2 VARCHAR(32), ADD a3 VARCHAR(32),  
  ADD a4 VARCHAR(32), ADD a5 VARCHAR(32),
  ADD d0 DATETIME(0),
  ADD d1 DATETIME(1), ADD d2 DATETIME(2), ADD d3 DATETIME(3),
  ADD d4 DATETIME(4), ADD d5 DATETIME(5), ADD d6 DATETIME(6);
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
UPDATE t1 SET
  a0= LEFT(a6, LENGTH(a6) - 6),
  a1= LEFT(a6, LENGTH(a6) - 5),
  a2= LEFT(a6, LENGTH(a6) - 4),
  a3= LEFT(a6, LENGTH(a6) - 3),
  a4= LEFT(a6, LENGTH(a6) - 2),
  a5= LEFT(a6, LENGTH(a6) - 1);
UPDATE IGNORE t1 SET d0= a0, d1= a1, d2= a2, d3= a3, d4= a4, d5= a5, d6= a6;
SELECT a6, d6, HEX(WEIGHT_STRING(d6)) FROM t1 ORDER BY d6;
SELECT a5, d5, HEX(WEIGHT_STRING(d5)) FROM t1 ORDER BY d5, a6;
SELECT a4, d4, HEX(WEIGHT_STRING(d4)) FROM t1 ORDER BY d4, a6;
SELECT a3, d3, HEX(WEIGHT_STRING(d3)) FROM t1 ORDER BY d3, a6;
SELECT a2, d2, HEX(WEIGHT_STRING(d2)) FROM t1 ORDER BY d2, a6;
SELECT a1, d1, HEX(WEIGHT_STRING(d1)) FROM t1 ORDER BY d1, a6;
SELECT a0, d0, HEX(WEIGHT_STRING(d0)) FROM t1 ORDER BY d0, a6;
CREATE VIEW v1 AS
SELECT a6, d0, d1, d2, d3, d4, d5, d6,
  HEX(WEIGHT_STRING(d0)) as wst0,
  HEX(WEIGHT_STRING(d1)) as wst1,
  HEX(WEIGHT_STRING(d2)) as wst2,
  HEX(WEIGHT_STRING(d3)) as wst3,
  HEX(WEIGHT_STRING(d4)) as wst4,
  HEX(WEIGHT_STRING(d5)) as wst5,
  HEX(WEIGHT_STRING(d6)) as wst6
FROM t1;
--query_vertical SELECT * FROM  v1 WHERE a6 LIKE '%.999999' ORDER BY a6, d6;
--query_vertical SELECT * FROM  v1 WHERE a6 LIKE '%.000000' ORDER BY a6, d6;
--query_vertical SELECT * FROM  v1 WHERE a6 LIKE '%.100000' ORDER BY a6, d6;
SET sql_mode = default;
DROP VIEW v1;
DROP TABLE t1;


--echo #
--echo # Testing bad FSPs
--echo #
--error ER_PARSE_ERROR
CREATE TABLE t1 (a DATETIME(-1));
--error ER_TOO_BIG_PRECISION
CREATE TABLE t1 (a DATETIME(7));
--error ER_TOO_BIG_PRECISION
CREATE TABLE t1 (a DATETIME(31));

--echo #
--echo # Testing bad FSPs with DATETIME functions
--echo #
--error ER_PARSE_ERROR
SELECT SYSDATE(-1);
--error ER_TOO_BIG_PRECISION
SELECT SYSDATE(7);
--error ER_TOO_BIG_PRECISION
SELECT SYSDATE(31);
--error ER_PARSE_ERROR
SELECT NOW(-1);
--error ER_TOO_BIG_PRECISION
SELECT NOW(7);
--error ER_TOO_BIG_PRECISION
SELECT NOW(31);



--echo #
--echo # Testing INSERT, ORDER, KEY, BETWEEN, comparison
--echo #
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1 (a DATETIME(6) NOT NULL);
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000000');
INSERT INTO t1 VALUES ('0000-00-00 23:59:59.999999');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03.4');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03.45');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03.456');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03.4567');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03.45678');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03.4567891');
INSERT INTO t1 VALUES ('9999-12-31 23:59:59.000000');
SELECT * FROM t1;
SELECT * FROM t1 ORDER BY a DESC;
SELECT * FROM t1 WHERE a='2000-01-01 01:02:03.45';
SELECT * FROM t1 WHERE a='2000-01-01 01:02:03.4567';
SELECT * FROM t1 WHERE a='2000-01-01 01:02:03.45670';
SELECT * FROM t1 WHERE a='2000-01-01 01:02:03.456700';
SELECT * FROM t1 WHERE a BETWEEN '2000-01-01 01:02:00' AND '2000-01-01 01:03:00';
ALTER TABLE t1 ADD KEY(a);
# Testing key order
SELECT * FROM t1 ORDER BY a;
SELECT * FROM t1 ORDER BY a DESC;

analyze table t1;
EXPLAIN SELECT * FROM t1 WHERE a='2000-01-01 01:02:03.456700';
SELECT * FROM t1 WHERE a='2000-01-01 01:02:03.456700';
SELECT * FROM t1 WHERE a BETWEEN '2000-01-01 01:02:00' AND '2000-01-01 01:03:00';
SELECT * FROM t1 WHERE a BETWEEN TIMESTAMP'2000-01-01 01:02:03.456' AND TIMESTAMP'2000-01-01 01:02:03.45678';
DROP TABLE t1;


--echo #
--echo # Tesint range: stored_field_cmp_to_item
--echo #
CREATE TABLE t1 (a datetime(6), key(a));
INSERT INTO t1 VALUES
  ('2000-01-01 00:00:00'), ('2000-01-01 00:00:00'),
  ('2000-01-01 00:00:01'), ('2000-01-01 00:00:01');
SELECT * FROM t1 WHERE a >= 20000101000000;
SELECT * FROM t1 WHERE a >= 20000101000000.0;
UPDATE t1 SET a=ADDTIME(a, 0.000001);
SELECT * FROM t1;
SELECT * FROM t1 WHERE a >= 20000101000000.000001;
DROP TABLE t1;



--echo #
--echo # Rare DATETIME formats
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES (101.101);
INSERT INTO t1 VALUES (990101.102);
INSERT INTO t1 VALUES ('990101.103');
INSERT INTO t1 VALUES (131.104);
INSERT INTO t1 VALUES ('000131.105');
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing bad values
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('xxxx');
INSERT INTO t1 VALUES ('1999-01-01 00:00:00.123456 xxx');
INSERT INTO t1 VALUES ('1999-01-01 00:00:00 xxx');
INSERT INTO t1 VALUES ('1999-01-01 xxx');
INSERT INTO t1 VALUES ('1999-00-00 00:00:00');
INSERT INTO t1 VALUES ('-0000-00-00');
INSERT INTO t1 VALUES ('-0000-00-00 00:00:00');
INSERT INTO t1 VALUES ('-0000-00-00 00:00:00.000000');
INSERT INTO t1 VALUES (-0.1);
INSERT INTO t1 VALUES (-1.0);
INSERT INTO t1 VALUES (-1.1);
INSERT INTO t1 VALUES (-0.1e0);
INSERT INTO t1 VALUES (-1.0e0);
INSERT INTO t1 VALUES (-1.1e0);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing huge values
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES (CAST(0xFFFFFFFF00000000 AS SIGNED));
INSERT INTO t1 VALUES (CAST(0xFFFFFFFF00000000 AS UNSIGNED));
INSERT INTO t1 VALUES ( 100000000000000000.1);
INSERT INTO t1 VALUES ( 100000000000000000.1e0);
INSERT INTO t1 VALUES (-100000000000000000.1);
INSERT INTO t1 VALUES (-100000000000000000.1e0);
SELECT * FROM t1;
DROP TABLE t1;
SET sql_mode = default;
--echo #
--echo # Testing values with zeros in dates
--echo #
SET sql_mode='NO_ZERO_IN_DATE';
CREATE TABLE t1 (a DATETIME, b DATETIME(6));
INSERT INTO t1 VALUES ('1999-00-00 00:00:00', '1999-00-00 00:00:00');
SELECT * FROM t1;
DROP TABLE t1;
SET sql_mode=default;

--echo #
--echo # Testing bad values with TRADITIONAL mode
--echo #
SET sql_mode=traditional;
CREATE TABLE t1 (a DATETIME, b DATETIME(6));
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES ('xxx', NULL);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL,'xxx');
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES ('1999-01-01 00:00:00 xxx', NULL);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES ('1999-00-00 00:00:00 xxx', NULL);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL, '1999-00-00 00:00:00 xxx');
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES ('1999-00-00 xxx', NULL);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL, '1999-00-00 xxx');
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES ('1999-00-00 00:00:00', NULL);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL, '1999-00-00 00:00:00');
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL, TIMESTAMP('0000-00-00 10:00:00'));
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL, 199991231000000);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL, -0.1);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL, -1.0);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL, -1.1);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL, -0.1e0);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL, -1.0e0);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL, -1.1e0);
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL, CAST(0xFFFFFFFF00000000 AS SIGNED));
--error ER_TRUNCATED_WRONG_VALUE
INSERT INTO t1 VALUES (NULL, CAST(0xFFFFFFFF00000000 AS UNSIGNED));
SELECT * FROM t1;
DROP TABLE t1;
SET sql_mode=default;

--echo #
--echo # Testing rounding
--echo #
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES (20010101100000.1234567);
INSERT INTO t1 VALUES (20010228235959.9999997);
INSERT INTO t1 VALUES (99990228235959.9999997);
INSERT INTO t1 VALUES (99991231235959.9999997);
INSERT INTO t1 VALUES ('2001-01-01 10:00:00.1234567');
INSERT INTO t1 VALUES ('2001-02-28 23:59:59.9999997');
INSERT INTO t1 VALUES ('9999-02-28 23:59:59.9999997');
INSERT INTO t1 VALUES ('9999-12-31 23:59:59.9999997');
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding when altering DATETIME(N) to a smaller size
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999999');
ALTER TABLE t1 MODIFY a DATETIME(5);
SELECT * FROM t1;
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999999');
ALTER TABLE t1 MODIFY a TIME;
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding from VARCHAR and DATETIME(6) to DATETIME(N)
--echo #
CREATE TABLE t1 (a VARCHAR(32),
  t6 DATETIME(6), t5 DATETIME(5), t4 DATETIME(4),
  t3 DATETIME(3), t2 DATETIME(2), t1 DATETIME(1),
  t0 DATETIME);
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.999999');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.999994');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.999949');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.999499');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.994999');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.949999');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.499999');
UPDATE t1 SET t0=a, t1=a, t2=a, t3=a, t4=a, t5=a, t6=a;
--query_vertical SELECT * FROM t1;
ALTER TABLE t1 MODIFY a DATETIME(6);
UPDATE t1 SET t0=a, t1=a, t2=a, t3=a, t4=a, t5=a, t6=a;
--query_vertical SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding from DATETIME(N) to BIGINT
--echo #
CREATE TABLE t1 (a BIGINT, b DATETIME(6));
INSERT INTO t1 (b) VALUES ('2001-01-01 10:10:59.500000');
INSERT INTO t1 (b) VALUES ('2001-01-01 10:10:10.500000');
INSERT INTO t1 (b) VALUES ('2001-01-01 10:10:10.499999');
UPDATE t1 SET a=b;
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding from TIME(6) to DATETIME(N)
--echo #
SET timestamp=UNIX_TIMESTAMP('2001-01-01 00:00:00');
CREATE TABLE t1 (a TIME(6),
  t6 DATETIME(6), t5 DATETIME(5), t4 DATETIME(4),
  t3 DATETIME(3), t2 DATETIME(2), t1 DATETIME(1),
  t0 DATETIME);
INSERT INTO t1 (a) VALUES ('10:10:10.999999');
INSERT INTO t1 (a) VALUES ('10:10:10.999994');
INSERT INTO t1 (a) VALUES ('10:10:10.999949');
INSERT INTO t1 (a) VALUES ('10:10:10.999499');
INSERT INTO t1 (a) VALUES ('10:10:10.994999');
INSERT INTO t1 (a) VALUES ('10:10:10.949999');
INSERT INTO t1 (a) VALUES ('10:10:10.499999');
UPDATE t1 SET t0=a, t1=a, t2=a, t3=a, t4=a, t5=a, t6=a;
--query_vertical SELECT * FROM t1;
DROP TABLE t1;
SET timestamp=default;

--echo #
--echo # Testing rounding from DECIMAL to DATETIME(N)
--echo #
CREATE TABLE t1 (a DECIMAL(30,6),
  t6 DATETIME(6), t5 DATETIME(5), t4 DATETIME(4),
  t3 DATETIME(3), t2 DATETIME(2), t1 DATETIME(1),
  t0 DATETIME);
INSERT INTO t1 (a) VALUES (20010101101010.999999);
INSERT INTO t1 (a) VALUES (20010101101010.999994);
INSERT INTO t1 (a) VALUES (20010101101010.999949);
INSERT INTO t1 (a) VALUES (20010101101010.999499);
INSERT INTO t1 (a) VALUES (20010101101010.994999);
INSERT INTO t1 (a) VALUES (20010101101010.949999);
INSERT INTO t1 (a) VALUES (20010101101010.499999);
UPDATE t1 SET t0=a, t1=a, t2=a, t3=a, t4=a, t5=a, t6=a;
--query_vertical SELECT * FROM t1;
DROP TABLE t1;


--echo #
--echo # Testing truncation warnings
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.111111 xxx');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.1111110');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.1111111');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.1111114');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.1111115');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.1111119');
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES (0.1111110);
INSERT INTO t1 VALUES (0.1111111);
INSERT INTO t1 VALUES (0.1111114);
INSERT INTO t1 VALUES (0.1111115);
INSERT INTO t1 VALUES (0.1111119);
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES (0.1111110e0);
INSERT INTO t1 VALUES (0.1111111e0);
INSERT INTO t1 VALUES (0.1111114e0);
INSERT INTO t1 VALUES (0.1111115e0);
INSERT INTO t1 VALUES (0.1111119e0);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing implicit CAST from TIME to DATETIME
--echo #
SET @@timestamp=UNIX_TIMESTAMP('2008-01-03 10:20:30.1');
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES (CURRENT_TIME), (CURRENT_TIME(6));
INSERT INTO t1 VALUES (TIME'08:00:00.123456'), (TIME'240:00:00.000001');
INSERT INTO t1 VALUES (TIME'-10:00:00.000001'), (TIME'-240:00:00.000001');
SELECT * FROM t1;
DROP TABLE t1;
SET @@timestamp=default;

--echo #
--echo # Testing IN
--echo #

CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000001');
INSERT INTO t1 VALUES ('2000-01-01 00:00:00.000000');
INSERT INTO t1 VALUES ('2000-01-01 00:00:00.000001');
INSERT INTO t1 VALUES ('2000-01-01 00:00:00.000002');
INSERT INTO t1 VALUES ('2000-01-01 10:00:00.000000');
INSERT INTO t1 VALUES ('2000-01-01 10:00:00.000001');
INSERT INTO t1 VALUES ('2000-01-01 10:00:00.000002');
INSERT INTO t1 VALUES ('2000-01-01 23:00:00.000000');
INSERT INTO t1 VALUES ('2000-01-01 23:00:00.000001');
INSERT INTO t1 VALUES ('2000-01-01 23:00:00.000002');
SELECT * FROM t1 WHERE a IN ('2000-01-01 00:00:00', '0000-00-00 00:00:00.000001', '2000-01-01 23:00:00.000002');
SELECT * FROM t1 WHERE a IN (0, 20000101000000, 20000101230000);
SELECT * FROM t1 WHERE a IN (0.000001, 20000101000000.000001, 20000101230000.000001);
SELECT * FROM t1 WHERE a IN (0e0, 0.000001e0);
--echo #
--echo # Testing Item_temporal_with_ref::print
--echo #
analyze table t1;
EXPLAIN SELECT * FROM t1 WHERE a IN (0.000001,0);
--echo #
--echo # Testing IN with index: involves field->store_packed()
--echo #
ALTER TABLE t1 ADD KEY(a);
SELECT * FROM t1 WHERE a IN ('2000-01-01 00:00:00', '0000-00-00 00:00:00.000001','2000-01-01 23:00:00.000002');
SELECT * FROM t1 WHERE a IN (0, 20000101000000, 20000101230000);
SELECT * FROM t1 WHERE a IN (0.000001, 20000101000000.000001, 20000101230000.000001);
SELECT * FROM t1 WHERE a IN (0e0, 0.000001e0);
DROP TABLE t1;


--echo #
--echo # Testing CREATE TABLE LIKE
--echo #
CREATE TABLE t1 (a DATETIME(6));
CREATE TABLE t2 LIKE t1;
SHOW CREATE TABLE t2;
DROP TABLE t2, t1;

--echo #
--echo # Testing CREATE TABLE .. SELECT
--echo #
CREATE TABLE t1 (a DATETIME(6));
CREATE TABLE t2 AS SELECT * FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2, t1;

--echo #
--echo # Testing JOIN between DATETIME(6) and DATETIME(6)
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2000-01-01 00:01:03.1');
INSERT INTO t1 VALUES ('2000-01-01 00:01:03.12');
INSERT INTO t1 VALUES ('2000-01-01 00:01:03.123');
INSERT INTO t1 VALUES ('2000-01-01 00:01:03.1234');
INSERT INTO t1 VALUES ('2000-01-01 00:01:03.12345');
INSERT INTO t1 VALUES ('2000-01-01 00:01:03.123456');
CREATE TABLE t2 (a DATETIME(6));
INSERT INTO t2 SELECT * FROM t1;
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t1 ADD KEY(a);
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t2 ADD KEY(a);
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t1 DROP KEY a;
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing JOIN between DATETIME and DATETIME(6)
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('0000-00-00 23:59:59.000000');
INSERT INTO t1 VALUES ('2000-01-01 10:11:12.000000');
INSERT INTO t1 VALUES ('2000-01-01 10:11:12.000001');
INSERT INTO t1 VALUES ('2000-01-01 10:11:12.000002');
INSERT INTO t1 VALUES ('2000-01-01 10:11:13.000000');
INSERT INTO t1 VALUES ('2000-01-01 10:11:13.000001');
INSERT INTO t1 VALUES ('2000-01-01 10:11:13.000002');
INSERT INTO t1 VALUES ('2000-01-01 23:59:59.999999');
INSERT INTO t1 VALUES ('2000-01-01 23:59:59.000000');
CREATE TABLE t2 (a DATETIME);
INSERT INTO t2 VALUES ('2000-01-01 10:11:12');
INSERT INTO t2 VALUES ('2000-01-01 10:11:13');
INSERT INTO t2 VALUES ('0000-00-00 23:59:59');
INSERT INTO t2 VALUES ('2000-01-01 23:59:59');
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t1 ADD KEY(a);
ALTER TABLE t2 ADD KEY(a);
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
DROP TABLE t2, t1;

--echo #
--echo # Testing unique index
--echo #
CREATE TABLE t1 (a DATETIME(6), UNIQUE(a));
INSERT INTO t1 VALUES ('2000-01-01 00:01:02');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.1');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.12');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.123');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.1234');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.12345');
--error ER_DUP_ENTRY
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.12345');
DROP TABLE t1;

--echo #
--echo # Testing GROUP BY
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2000-01-01 00:01:02');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.0');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.01');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.010');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.02');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.020');
SELECT a, COUNT(*) FROM t1 GROUP BY a;
DROP TABLE t1;

--echo #
--echo # Testing MIN() and MAX()
--echo #
CREATE TABLE t1 (a INT, b DATETIME(6));
INSERT INTO t1 VALUES (1, '2001-01-01 00:01:02.000001');
INSERT INTO t1 VALUES (1, '2001-01-01 00:01:02');
INSERT INTO t1 VALUES (2, '2001-01-01 10:01:02');
INSERT INTO t1 VALUES (2, '2001-01-01 10:01:02.000001');
INSERT INTO t1 VALUES (3, '2001-01-01 10:11:02');
INSERT INTO t1 VALUES (3, '2001-01-01 10:11:02.000001');
SELECT MIN(b), MAX(b) FROM t1;
SELECT a, MIN(b), MAX(b) FROM t1 GROUP BY a;
DROP TABLE t1;

--echo #
--echo # Testing MIN(DATETIME) and MAX(DATETIME) in arithmetic expressions and CAST
--echo # This covers Item_sum_hybrid::val_int(), val_real(), val_decimal()
--echo #
CREATE TABLE t1
(
  t0 DATETIME,
  t1 DATETIME(1), t2 DATETIME(2), t3 DATETIME(3),
  t4 DATETIME(4), t5 DATETIME(5), t6 DATETIME(6)
);
INSERT INTO t1 VALUES
(
  '2001-01-01 10:10:10',
  '2001-01-01 10:10:10.9', '2001-01-01 10:10:10.99', '2001-01-01 10:10:10.999',
  '2001-01-01 10:10:10.9999', '2001-01-01 10:10:10.99999', '2001-01-01 10:10:10.999999'
);
CREATE TABLE t2 AS
SELECT
  MAX(t0) + 1,
  MAX(t1) + 1, MAX(t2) + 1, MAX(t3) + 1,
  MAX(t4) + 1, MAX(t5) + 1, MAX(t6) + 1
FROM t1;
SHOW COLUMNS FROM t2;
SELECT * FROM t2;
DROP TABLE t2;
CREATE TABLE t2 AS
SELECT
  MAX(t0) + 1.1,
  MAX(t1) + 1.1, MAX(t2) + 1.1, MAX(t3) + 1.1,
  MAX(t4) + 1.1, MAX(t5) + 1.1, MAX(t6) + 1.1
FROM t1;
SHOW COLUMNS FROM t2;
SELECT * FROM t2;
DROP TABLE t2;
CREATE TABLE t2 AS
SELECT
  MAX(t0) + 1.0e0,
  MAX(t1) + 1.0e0, MAX(t2) + 1.0e0, MAX(t3) + 1.0e0,
  MAX(t4) + 1.0e0, MAX(t5) + 1.0e0, MAX(t6) + 1.0e0
FROM t1;
SHOW COLUMNS FROM t2;
DROP TABLE t2;
SELECT
  MAX(t0) + 1,
  MAX(t1) + 1, MAX(t2) + 1, MAX(t3) + 1,
  MAX(t4) + 1, MAX(t5) + 1, MAX(t6) + 1
FROM t1;
SELECT
  CAST(MAX(t0) AS SIGNED),
  CAST(MAX(t1) AS SIGNED), CAST(MAX(t2) AS SIGNED), CAST(MAX(t3) AS SIGNED),
  CAST(MAX(t4) AS SIGNED), CAST(MAX(t5) AS SIGNED), CAST(MAX(t6) AS SIGNED)
FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing LEAST
--echo #
SELECT LEAST(TIMESTAMP'2001-01-01 00:00:00.1', TIMESTAMP'2001-01-01 00:00:00.2');
SELECT LEAST(TIMESTAMP'2001-01-01 00:00:00.1', TIMESTAMP'2001-01-01 00:00:00.2') + 1;
SELECT LEAST(TIMESTAMP'2001-01-01 00:00:00.1', TIMESTAMP'2001-01-01 00:00:00.2') + 1.0;
SELECT LEAST(TIMESTAMP'2001-01-01 00:00:00.1', TIMESTAMP'2001-01-01 00:00:00.2') + 1e0;
SELECT LEAST(CAST('2001-01-01 00:00:00.1' as DATETIME(6)), '2001-01-02 00:00:00.1') AS a;
SELECT LEAST(CAST('2001-01-01 00:00:00.1' as DATETIME(6)), '2001-01-02 00:00:00.1') + 0 AS a;
SELECT CAST(LEAST(CAST('2001-01-01 00:00:00.1' as DATETIME(6)), '2001-01-02 00:00:00.1') AS SIGNED) AS a;
SELECT CAST(LEAST(CAST('2001-01-01 00:00:00.1' as DATETIME(6)), '2001-01-02 00:00:00.1') AS DECIMAL(30,6)) AS a;
SELECT GREATEST(CAST('2001-01-01 00:00:00.1' as DATETIME(6)), '2001-01-02 00:00:00.1') AS a;
SELECT GREATEST(CAST('2001-01-01 00:00:00.1' as DATETIME(6)), '2001-01-02 00:00:00.1') + 0 AS a;
SELECT CAST(GREATEST(CAST('2001-01-01 00:00:00.1' as DATETIME(6)), '2001-01-02 00:00:00.1') AS SIGNED) AS a;
SELECT CAST(GREATEST(CAST('2001-01-01 00:00:00.1' as DATETIME(6)), '2001-01-02 00:00:00.1') AS DECIMAL(30,6)) AS a;
CREATE TABLE t1 AS SELECT
  LEAST(CAST('2001-01-01 00:00:00' AS DATETIME(1)),
        CAST('2001-01-01 00:00:00' AS DATETIME(2))) AS l,
  GREATEST(CAST('2001-01-01 00:00:00' AS DATETIME(1)),
           CAST('2001-01-01 00:00:00' AS DATETIME(2))) AS g;
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Testing INSERT, MAX and MIN values
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('9999-12-31 23:59:59');
INSERT INTO t1 VALUES ('9999-12-31 23:59:59.1');
INSERT INTO t1 VALUES ('9999-12-31 24:00:00');
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing integer INSERT values
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES (19990102);
INSERT INTO t1 VALUES (19990102112233);
INSERT INTO t1 VALUES (100000101112233);
INSERT INTO t1 VALUES (0);
INSERT INTO t1 VALUES (-1);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing decimal INSERT values
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES (20010101223344.4);
INSERT INTO t1 VALUES (20010101223344.45);
INSERT INTO t1 VALUES (20010101223344.456);
INSERT INTO t1 VALUES (20010101223344.4567);
INSERT INTO t1 VALUES (20010101223344.45678);
INSERT INTO t1 VALUES (20010101223344.456789);
INSERT INTO t1 VALUES (2001010120203.4567894);
INSERT INTO t1 VALUES (2001010120203.4567895);
INSERT INTO t1 VALUES (2001010120203.4567896);
INSERT INTO t1 VALUES (2001010120203.456789678);
INSERT INTO t1 VALUES (200101018385959.000000);
INSERT INTO t1 VALUES (-200101018385959.000000);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing double INSERT values
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES (112.233e-3);
INSERT INTO t1 VALUES (112.223e-2);
INSERT INTO t1 VALUES (112.233e-1);
INSERT INTO t1 VALUES (112.233e0);
INSERT INTO t1 VALUES (112.233e1);
INSERT INTO t1 VALUES (112.233e2);
INSERT INTO t1 VALUES (112.233e3);
INSERT INTO t1 VALUES (112.233e4);
INSERT INTO t1 VALUES (-123.456e0);
SELECT * FROM t1;
DROP TABLE t1;


--echo #
--echo # Testing UPDATE between DATETIME(6) and DATETIME/BIGINT/DECIMAL/DOUBLE
--echo #
CREATE TABLE t1 (t0 DATETIME, t6 DATETIME(6), i BIGINT, d DECIMAL(20,6), f DOUBLE);
INSERT INTO t1 (t0) VALUES ('0000-00-00 11:22:33');
UPDATE t1 SET t6=t0, i=t0, d=t0, f=t0;
SELECT * FROM t1;
UPDATE t1 SET t6='0000-00-00 11:22:33.1';
UPDATE t1 SET t0=t6, i=t6, d=t6, f=t6;
SELECT * FROM t1;
UPDATE t1 SET i=20000101112233, d=20001010112233.1, f=00001010112233.1;
UPDATE t1 SET t6=i;
SELECT t6 FROM t1;
UPDATE t1 SET t6=d;
SELECT t6 FROM t1;
UPDATE t1 SET t6=f;
SELECT t6 FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing JOIN with comparison between DATETIME(6) and DATE
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2000-01-01 00:00:00.000000');
INSERT INTO t1 VALUES ('2000-01-01 00:00:00.000001');
INSERT INTO t1 VALUES ('2000-01-02 00:00:00.000000');
CREATE TABLE t2 (a DATE);
INSERT INTO t2 VALUES ('2000-01-01');
INSERT INTO t2 VALUES ('2000-01-02');
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t2 MODIFY a DATETIME(6);
SELECT * FROM t1, t2 WHERE t1.a=CAST(t2.a AS DATE) ORDER BY t1.a, t2.a;
DROP TABLE t2;
DROP TABLE t1;


--echo #
--echo # Testing JOIN with comparison between DATETIME(6) and BIGINT
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2000-01-01 00:00:00');
INSERT INTO t1 VALUES ('2000-01-01 11:22:33');
INSERT INTO t1 VALUES ('2000-01-01 11:22:33.123');
CREATE TABLE t2 (b BIGINT);
INSERT INTO t2 VALUES (20000101);
INSERT INTO t2 VALUES (20000101112233);
SELECT * FROM t1, t2 WHERE a=b;
DROP TABLE t1, t2;

--echo #
--echo # Testing JOIN with comparison between DATETIME(6) and DECIMAL(20,6)
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 11:22:33');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123456');
CREATE TABLE t2 (b DECIMAL(20,6));
INSERT INTO t2 VALUES (20010101112233);
INSERT INTO t2 VALUES (20010101112233.123);
INSERT INTO t2 VALUES (20010101112233.123456);
SELECT * FROM t1, t2 WHERE a=b;
DROP TABLE t1, t2;

--echo #
--echo # Testing JOIN with comparison between DATETIME(6) and DECIMAL(20,3)
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 11:22:33');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123456');
CREATE TABLE t2 (b DECIMAL(20,3));
INSERT INTO t2 VALUES (20010101112233);
INSERT INTO t2 VALUES (20010101112233.123);
SELECT * FROM t1, t2 WHERE a=b;
DROP TABLE t1, t2;

--echo #
--echo # Testing JOIN with comparison between DATETIME(6) and DOUBLE
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('0000-00-00 11:22:33');
INSERT INTO t1 VALUES ('0000-00-00 11:22:33.123');
INSERT INTO t1 VALUES ('0000-00-00 11:22:33.123456');
CREATE TABLE t2 (b DOUBLE);
INSERT INTO t2 VALUES (112233);
INSERT INTO t2 VALUES (112233.123);
INSERT INTO t2 VALUES (112233.123456);
SELECT * FROM t1, t2 WHERE a=b;
DROP TABLE t1, t2;

--echo #
--echo # Testing JOIN with comparison between DATETIME(6) and VARCHAR
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 11:22:33');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123');
CREATE TABLE t2 (b VARCHAR(64));
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.123');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.1230');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.12300');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.123000');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.0');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.00');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.000');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.0000');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.00000');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.000000');
SELECT * FROM t1, t2 WHERE a=b ORDER BY BINARY a, BINARY b;
ALTER TABLE t1 ADD KEY (a);
SELECT * FROM t1, t2 WHERE a=b ORDER BY BINARY a, BINARY b;
ALTER TABLE t2 ADD KEY(b);
SELECT * FROM t1, t2 WHERE a=b ORDER BY BINARY a, BINARY b;
ALTER TABLE t1 DROP KEY a;
SELECT * FROM t1, t2 WHERE a=b ORDER BY BINARY a, BINARY b;
DROP TABLE t1, t2;

--echo #
--echo # Testing arithmetic with INT, DECIMAL, FLOAT
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000000');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000001');
SELECT a, a + 0, a + 1, a + 1.0, a + 1e0 FROM t1;
CREATE TABLE t2 AS SELECT a + 1 AS i, a + 1.0 AS d, a + 1e0 AS f FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t1, t2;

--echo #
--echo # Testing that DATETIME behaves as INT/DECIMAL for numeric arithmetic
--echo #
CREATE TABLE t1 (t0 DATETIME, t3 DATETIME(3), t6 DATETIME(6));
CREATE TABLE t2 AS SELECT
  t0 + 1, t3 + 1, t6 + 1,
  t0 - 1, t3 - 1, t6 - 1,
  t0 * 1, t3 * 1, t6 * 1,
  t0 / 1, t3 / 1, t6 / 1,
  TIMESTAMP'2001-01-01 10:10:10' + 1,
  TIMESTAMP'2001-01-01 10:10:10.123' + 1,
  TIMESTAMP'2001-01-01 10:10:10.123456' + 1,
  TIMESTAMP'2001-01-01 10:10:10' - 1,
  TIMESTAMP'2001-01-01 10:10:10.123' - 1,
  TIMESTAMP'2001-01-01 10:10:10.123456' - 1,
  TIMESTAMP'2001-01-01 10:10:10' * 1,
  TIMESTAMP'2001-01-01 10:10:10.123' * 1,
  TIMESTAMP'2001-01-01 10:10:10.123456' * 1,
  TIMESTAMP'2001-01-01 10:10:10' / 1,
  TIMESTAMP'2001-01-01 10:10:10.123' / 1,
  TIMESTAMP'2001-01-01 10:10:10.123456' / 1
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing that DATETIME behaves as DECIMAL for SUM() and AVG()
--echo #
SET @t='2010-01-01 00:11:12.123456';
CREATE TABLE t1 (t0 DATETIME, t3 DATETIME(3), t6 DATETIME(6));
INSERT INTO t1 VALUES (@t, @t, @t);
--query_vertical SELECT MIN(t0), MAX(t0), AVG(t0), SUM(t0) FROM t1
--query_vertical SELECT MIN(t3), MAX(t3), AVG(t3), SUM(t3) FROM t1
--query_vertical SELECT MIN(t6), MAX(t6), AVG(t6), SUM(t6) FROM t1
SET @t='2010-01-01 00:11:12.000000';
INSERT INTO t1 VALUES (@t, @t, @t);
--query_vertical SELECT MIN(t0), MAX(t0), AVG(t0), SUM(t0) FROM t1
--query_vertical SELECT MIN(t3), MAX(t3), AVG(t3), SUM(t3) FROM t1
--query_vertical SELECT MIN(t6), MAX(t6), AVG(t6), SUM(t6) FROM t1
CREATE TABLE t2 AS SELECT
  MIN(t0), MAX(t0), AVG(t0), SUM(t0),
  MIN(t3), MAX(t3), AVG(t3), SUM(t3),
  MIN(t6), MAX(t6), AVG(t6), SUM(t6)
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;
SET sql_mode = default;
--echo #
--echo # Testing functions ADDTIME, SUBTIME, TIMESTAMP, TIMEDIFF, DATE_ADD/SUB
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('1000-01-01 01:00:00.000000');
INSERT INTO t1 VALUES ('1000-01-01 01:00:00.000001');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000000');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000001');
INSERT INTO t1 VALUES ('2001-01-01 01:00:00.000000');
INSERT INTO t1 VALUES ('2001-01-01 01:00:00.000001');
SELECT ADDTIME(a, '00:00:00.000001') FROM t1 ORDER BY a;
SELECT ADDTIME(a, '10:00:00.000001') FROM t1 ORDER BY a;
SELECT ADDTIME(CAST(a AS DATETIME(0)), 100000) FROM t1 ORDER BY a;
SELECT ADDTIME(CAST(a AS DATETIME(0)), 100000.1) FROM t1 ORDER BY a;
SELECT a, ADDTIME(CAST(a AS DATETIME(0)), CAST('10:00:00' AS TIME(0))) FROM t1 ORDER BY a;
SELECT a, ADDTIME(CAST(a AS DATETIME(6)), CAST('10:00:00' AS TIME(0))) FROM t1 ORDER BY a;
SELECT a, ADDTIME(CAST(a AS DATETIME(0)), CAST('10:00:00' AS TIME(6))) FROM t1 ORDER BY a;
SELECT ADDTIME(a, a) FROM t1 ORDER BY a;
CREATE TABLE t2 AS SELECT
  ADDTIME(a, '00:00:00.000001'),
  ADDTIME(a, '10:00:00.000001'),
  ADDTIME(CAST(a AS DATETIME(0)), 100000),
  ADDTIME(CAST(a AS DATETIME(0)), 100000.1),
  ADDTIME(CAST(a AS DATETIME(0)), CAST('10:00:00' AS TIME(0))),
  ADDTIME(CAST(a AS DATETIME(6)), CAST('10:00:00' AS TIME(0))),
  ADDTIME(CAST(a AS DATETIME(0)), CAST('10:00:00' AS TIME(6)))
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
SELECT TIMESTAMP(a, '10:00:00') FROM t1;
SELECT TIMESTAMP(CAST(a AS DATETIME(0)), CAST('00:00:00' AS TIME(0))) FROM t1;
SELECT TIMESTAMP(CAST(a AS DATETIME(6)), CAST('00:00:00' AS TIME(0))) FROM t1;
SELECT TIMESTAMP(CAST(a AS DATETIME(0)), CAST('00:00:00' AS TIME(6))) FROM t1;
CREATE TABLE t2 AS SELECT
  TIMESTAMP(a, '10:00:00'),
  TIMESTAMP(CAST(a AS DATETIME(0)), CAST('00:00:00' AS TIME(0))),
  TIMESTAMP(CAST(a AS DATETIME(6)), CAST('00:00:00' AS TIME(0))),
  TIMESTAMP(CAST(a AS DATETIME(0)), CAST('00:00:00' AS TIME(6)))
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
SELECT SUBTIME(a, '00:00:00.000001') FROM t1 ORDER BY a;
SELECT SUBTIME(a, '10:00:00.000001') FROM t1 ORDER BY a;
SELECT SUBTIME(a, a) FROM t1 ORDER BY a;
SELECT DATE_ADD(a, INTERVAL 1 MINUTE) FROM t1 ORDER BY a;
SELECT DATE_SUB(a, INTERVAL 1 MINUTE) FROM t1 ORDER BY a;
SELECT a, DATE_ADD(a, INTERVAL 1.1 SECOND) FROM t1 ORDER BY a;
SELECT a, DATE_ADD(a, INTERVAL 1.000009 SECOND) FROM t1 ORDER BY a;
SELECT a, DATE_SUB(a, INTERVAL 1.1 SECOND) FROM t1 ORDER BY a;
SELECT a, DATE_SUB(a, INTERVAL 1.000009 SECOND) FROM t1 ORDER BY a;
SELECT a, DATE_ADD(a, INTERVAL -0.1 SECOND) FROM t1 ORDER BY a;
SELECT a, DATE_SUB(a, INTERVAL -0.1 SECOND) FROM t1 ORDER BY a;
SELECT DATE_ADD(CAST(a AS DATETIME), INTERVAL 1 MINUTE) FROM t1 ORDER BY a;
SELECT DATE_SUB(CAST(a AS DATETIME), INTERVAL 1 MINUTE) FROM t1 ORDER BY a;
SELECT DATE_ADD(CAST(a AS DATETIME), INTERVAL 1 MICROSECOND) FROM t1 ORDER BY a;
SELECT DATE_SUB(CAST(a AS DATETIME), INTERVAL 1 MICROSECOND) FROM t1 ORDER BY a;
CREATE TABLE t2 AS SELECT
  DATE_ADD(a, INTERVAL 1 SECOND),
  DATE_SUB(a, INTERVAL 1 SECOND),
  DATE_ADD(CAST(a AS DATETIME), INTERVAL 1 SECOND),
  DATE_SUB(CAST(a AS DATETIME), INTERVAL 1 SECOND),
  DATE_ADD(CAST(a AS DATETIME), INTERVAL 1 MICROSECOND),
  DATE_SUB(CAST(a AS DATETIME), INTERVAL 1 MICROSECOND)
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT MAX(ADDTIME(a, '00:00:00.1')) FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2;
DROP TABLE t2;
DROP TABLE t1;

CREATE TABLE t1 AS SELECT
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00', INTERVAL 1 SECOND) AS t0s0,
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00', INTERVAL 1.1 SECOND) AS t0s1,
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00', INTERVAL 1.12 SECOND) AS t0s2,
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00', INTERVAL 1.123 SECOND) AS t0s3,
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00', INTERVAL 1.1234 SECOND) AS t0s4,
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00', INTERVAL 1.12345 SECOND) AS t0s5,
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00', INTERVAL 1.123456 SECOND) AS t0s6,
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00.1', INTERVAL 1 SECOND) AS t1s0,
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00.12', INTERVAL 1 SECOND) AS t2s0,
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00.123', INTERVAL 1 SECOND) AS t3s0,
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00.1234', INTERVAL 1 SECOND) AS t4s0,
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00.12345', INTERVAL 1 SECOND) AS t5s0,
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00.123456', INTERVAL 1 SECOND) AS t6s0,
  DATE_ADD(TIMESTAMP'2001-01-01 00:00:00', INTERVAL 1 MICROSECOND) AS t0ms;
SHOW CREATE TABLE t1;
DROP TABLE t1;


CREATE TABLE t1 AS SELECT
  CONVERT_TZ(TIMESTAMP'2001-01-01 00:00:00', '+00:00', '+01:00') AS d0,
  CONVERT_TZ(TIMESTAMP'2001-01-01 00:00:00.1', '+00:00', '+01:00') AS d1,
  CONVERT_TZ(TIMESTAMP'2001-01-01 00:00:00.12', '+00:00', '+01:00') AS d2,
  CONVERT_TZ(TIMESTAMP'2001-01-01 00:00:00.123', '+00:00', '+01:00') AS d3,
  CONVERT_TZ(TIMESTAMP'2001-01-01 00:00:00.1234', '+00:00', '+01:00') AS d4,
  CONVERT_TZ(TIMESTAMP'2001-01-01 00:00:00.12345', '+00:00', '+01:00') AS d5,
  CONVERT_TZ(TIMESTAMP'2001-01-01 00:00:00.123456', '+00:00', '+01:00') AS d6;

SHOW CREATE TABLE t1;
DROP TABLE t1;


--echo #
--echo # Testing copy from DATETIME to DATETIME(6) and vice versa
--echo #
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1 (a DATETIME(6), b DATETIME);
INSERT INTO t1 VALUES ('2001-01-01 01:02:03.123456','0000-00-00 00:00:00');
UPDATE t1 SET b=a;
SELECT * FROM t1;
UPDATE t1 SET b='2002-02-02 10:11:12';
UPDATE t1 SET a=b;
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER from DATETIME(6) to DATETIME and vice versa
--echo #
CREATE TABLE t1 (a DATETIME);
INSERT INTO t1 VALUES ('2000-01-01 01:02:03');
ALTER TABLE t1 MODIFY a DATETIME(6);
SELECT * FROM t1;
UPDATE t1 SET a='2000-01-01 01:03:03.456';
SELECT * FROM t1;
ALTER TABLE t1 MODIFY a DATETIME;
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER between DATETIME and BIGINT
--echo #
CREATE TABLE t1 (a DATETIME, b DATETIME(6));
INSERT INTO t1 VALUES ('2000-01-01 11:22:33', '2000-01-01 11:22:33');
ALTER TABLE t1 MODIFY a BIGINT, MODIFY b BIGINT;
SELECT * FROM t1;
UPDATE t1 SET a=20000101112233, b=20000101112233;
ALTER TABLE t1 MODIFY a DATETIME, MODIFY b DATETIME(6);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER between DATETIME(6) and DOUBLE
--echo #
CREATE TABLE t1 (a DATETIME, b DATETIME(6));
INSERT INTO t1 VALUES ('0001-00-00 11:22:33', '0001-00-00 11:22:33.1234');
ALTER TABLE t1 MODIFY a DOUBLE, MODIFY b DOUBLE;
SELECT * FROM t1;
UPDATE t1 SET a=20010101101112, b=20010101101112.1;
ALTER TABLE t1 MODIFY a DATETIME, MODIFY b DATETIME(6);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER between DATETIME(6) and DECIMAL
--echo #
CREATE TABLE t1 (a DATETIME, b DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 11:22:33', '2001-01-01 11:22:33.1234');
ALTER TABLE t1 MODIFY a DECIMAL(30,6), MODIFY b DECIMAL(30,6);
SELECT * FROM t1;
UPDATE t1 SET a=20010101112233, b=20010101112233.1234;
ALTER TABLE t1 MODIFY a DATETIME, MODIFY b DATETIME(6);
SELECT * FROM t1;
DROP TABLE t1;


--echo #
--echo # Testing ALTER from DATETIME to various other temporal types
--echo #
CREATE TABLE t1 (a DATETIME(6), b DATETIME(6), c DATETIME(6));
INSERT INTO t1 VALUES
  ('2001-01-01 10:10:10.1', '2001-01-01 10:10:10.1', '2001-01-01 10:10:10.1');
ALTER TABLE t1 MODIFY a TIME(6), MODIFY b TIMESTAMP(6), MODIFY c DATE;
SELECT * FROM t1;
DROP TABLE t1;


--echo #
--echo # Testing EXPLAIN SELECT for CAST(DATETIME(N))
--echo #
EXPLAIN SELECT CAST('10:10:10' AS DATETIME);
EXPLAIN SELECT CAST('10:10:10' AS DATETIME(0));
EXPLAIN SELECT CAST('10:10:10' AS DATETIME(1));
EXPLAIN SELECT CAST('10:10:10' AS DATETIME(2));
EXPLAIN SELECT CAST('10:10:10' AS DATETIME(3));
EXPLAIN SELECT CAST('10:10:10' AS DATETIME(4));
EXPLAIN SELECT CAST('10:10:10' AS DATETIME(5));
EXPLAIN SELECT CAST('10:10:10' AS DATETIME(6));

--echo #
--echo # Testing CAST with bad FSPs
--echo #
--error ER_PARSE_ERROR
SELECT CAST(1 AS DATETIME(-1));
--error ER_TOO_BIG_PRECISION
SELECT CAST(1 AS DATETIME(7));
--error ER_TOO_BIG_PRECISION
SELECT CAST(1 AS DATETIME(31));

--echo #
--echo # Testing conversion from DATETIME(6) to INT
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 10:10:58.499');
INSERT INTO t1 VALUES ('2001-01-01 10:10:58.999');
INSERT INTO t1 VALUES ('2001-01-01 10:10:59.499');
INSERT INTO t1 VALUES ('2001-01-01 10:10:59.999');
INSERT INTO t1 VALUES ('2001-12-31 23:59:58.499');
INSERT INTO t1 VALUES ('2001-12-31 23:59:58.999');
INSERT INTO t1 VALUES ('2001-12-31 23:59:59.499');
INSERT INTO t1 VALUES ('2001-12-31 23:59:59.999');
ALTER TABLE t1 ADD b BIGINT, ADD c DATETIME(6);
UPDATE t1 SET b=a, c=a;
ALTER TABLE t1 MODIFY c BIGINT;
SELECT a, CAST(a AS SIGNED), b, c FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing INT value and CAST of DATETIME(6) to various other types
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.999');
INSERT INTO t1 VALUES ('2001-12-31 23:59:58.499');
INSERT INTO t1 VALUES ('2001-12-31 23:59:58.999');
INSERT INTO t1 VALUES ('2001-12-31 23:59:59.499');
INSERT INTO t1 VALUES ('2001-12-31 23:59:59.999');
SELECT a << 0 FROM t1;
SELECT CAST(a AS SIGNED) FROM t1;
SELECT CAST(a AS UNSIGNED) FROM t1;
SELECT CAST(a AS DECIMAL(30,6)) FROM t1;
SELECT CAST(a AS DECIMAL(30,3)) FROM t1;
SELECT CAST(a AS CHAR) FROM t1;
SELECT CAST(a AS CHAR(6)) FROM t1;
CREATE TABLE t2 AS SELECT CAST(a AS CHAR) AS a FROM t1;
SHOW CREATE TABLE t2;
SELECT a, LENGTH(a) FROM t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing explicit CAST from TIME to DATETIME
--echo #
SET @@timestamp=UNIX_TIMESTAMP('2001-01-01 00:00:00');
CREATE TABLE t1 (a TIME, b TIME(6), c INT, d VARCHAR(32), e INT);
INSERT INTO t1 VALUES ('15:00:00', '15:00:00.123456', 150000, '15:00:00', 15*3600);
INSERT INTO t1 VALUES ('45:00:00', '45:00:00.123456', 450000, '45:00:00', 45*3600);
INSERT INTO t1 VALUES ('-15:00:00', '-15:00:00.123456', -150000, '-15:00:00', -15*3600);
INSERT INTO t1 VALUES ('-45:00:00', '-45:00:00.123456', -450000, '-45:00:00', -45*3600);
--query_vertical SELECT a, CAST(a AS DATETIME), CAST(b AS DATETIME), CAST(c AS DATETIME), CAST(d AS DATETIME), CAST(SEC_TO_TIME(e) AS DATETIME), CAST(CAST(a AS TIME) AS DATETIME) FROM t1;
--query_vertical SELECT a, CAST(a AS DATETIME(6)), CAST(b AS DATETIME(6)), CAST(c AS DATETIME(6)), CAST(d AS DATETIME(6)), CAST(SEC_TO_TIME(e) AS DATETIME(6)), CAST(CAST(a AS TIME) AS DATETIME(6)) FROM t1;
DROP TABLE t1;
SET @@timestamp=default;

--echo #
--echo # Testing comparison between TIME and DATETIME
--echo # This tests Field_time[f]::val_date_temporal()
--echo #
SET @@timestamp=UNIX_TIMESTAMP('2001-01-01 10:20:30.123');
CREATE TABLE t1 (a TIME, b DATETIME, a6 TIME(6), b6 DATETIME(6));
INSERT INTO t1 VALUES
(current_time, current_timestamp, current_time(6), current_timestamp(6));
SELECT a, b FROM t1 WHERE a=b;
SELECT a6, b6 FROM t1 WHERE a6=b6;
--echo # In this query both expressions make DATETIME data type
SELECT LEAST(a,b), LEAST(b,a) FROM t1;
SELECT CONCAT(LEAST(a,b)), CONCAT(LEAST(b,a)) FROM t1;
--echo # In this query all expressions make VARCHAR data type
--echo # So the result format depends on which value wins, with the first value having higher precedence:
SELECT LEAST(a,b,'2001-01-02'), LEAST(b,a,'2001-01-02'), LEAST(a,b,'2000-01-01') FROM t1;
--echo # More tests with LEAST:
SELECT a, b FROM t1 WHERE a=LEAST(a,a);
SELECT a, b FROM t1 WHERE a=LEAST(a,b);
DROP TABLE t1;
SET @@timestamp=DEFAULT;

--echo #
--echo # Comparison between DATETIME column and constant TIME epxression
--echo #
CREATE TABLE t1 (a DATETIME(6));
SET @@timestamp=UNIX_TIMESTAMP('2010-01-01 10:50:50.123');
SELECT NOW(6);
INSERT INTO t1 VALUES (CURRENT_TIMESTAMP(6)), ('2010-01-01 00:00:00');
SELECT * FROM t1 WHERE a=TIME'10:50:50.123';
SELECT * FROM t1 WHERE a=CURRENT_TIME(6);
SELECT * FROM t1 WHERE a=CAST('10:50:50.123' AS TIME(6));
SELECT * FROM t1 WHERE a=MAKETIME(10,50,50.123);
SELECT * FROM t1 WHERE a=SEC_TO_TIME(39050.123);
SELECT * FROM t1 WHERE TIME'10:50:50.123'=a;
SELECT * FROM t1 WHERE CURRENT_TIME(6)=a;
SELECT * FROM t1 WHERE CAST('10:50:50.123' AS TIME(6))=a;
SELECT * FROM t1 WHERE MAKETIME(10,50,50.123)=a;
SELECT * FROM t1 WHERE SEC_TO_TIME(39050.123)=a;
DROP TABLE t1;
SET @@timestamp=DEFAULT;


--echo #
--echo # BETWEEN for combinations of DATETIME and TIME
--echo #
SET @@timestamp=UNIX_TIMESTAMP('2010-01-01 10:50:50.123');
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES
 ('2010-01-01 10:50:50.100'), ('2010-01-01 10:50:50.123'), ('2010-01-01 10:50:50.200');
SELECT * FROM t1 WHERE a BETWEEN TIME'10:50:50.120' AND TIME'10:50:50.123';
SELECT * FROM t1 WHERE a BETWEEN TIMESTAMP'2010-01-01 10:50:50.120' AND TIME'10:50:50.123';
SELECT * FROM t1 WHERE a BETWEEN TIME'10:50:50.120' AND TIMESTAMP'2010-01-01 10:50:50.123';
ALTER TABLE t1 MODIFY a TIME(6);
SELECT * FROM t1;
SELECT * FROM t1 WHERE a BETWEEN TIME'10:50:50.120' AND TIME'10:50:50.123';
SELECT * FROM t1 WHERE a BETWEEN TIMESTAMP'2010-01-01 10:50:50.120' AND TIME'10:50:50.123';
SELECT * FROM t1 WHERE a BETWEEN TIME'10:50:50.120' AND TIMESTAMP'2010-01-01 10:50:50.123';
DROP TABLE t1;
SET @@timestamp=DEFAULT;


CREATE TABLE t1 (c INT, d VARCHAR(32));
INSERT INTO t1 VALUES (156060, '15:60:60');
SELECT CAST(c AS DATETIME), CAST(d AS DATETIME) FROM t1;
SELECT CAST(c AS DATETIME(6)), CAST(d AS DATETIME(6)) FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing function TIMESTAMP(expr)
--echo #
CREATE TABLE t1 AS SELECT
  TIMESTAMP(20010101101010),
  TIMESTAMP(20010101101010.1),
  TIMESTAMP(20010101101010.12),
  TIMESTAMP(20010101101010.123),
  TIMESTAMP(20010101101010.1234),
  TIMESTAMP(20010101101010.12345),
  TIMESTAMP(20010101101010.123456),
  TIMESTAMP('2001-01-01 00:01:01'),
  TIMESTAMP('2001-01-01 00:01:01.1'),
  TIMESTAMP('2001-01-01 00:01:01.12'),
  TIMESTAMP('2001-01-01 00:01:01.123'),
  TIMESTAMP('2001-01-01 00:01:01.1234'),
  TIMESTAMP('2001-01-01 00:01:01.12345'),
  TIMESTAMP('2001-01-01 00:01:01.123456'),
  TIMESTAMP(CONCAT('2001-01-01 00:01:01','')),
  TIMESTAMP(CONCAT('2001-01-01 00:01:01','.1')),
  TIMESTAMP(CONCAT('2001-01-01 00:01:01','.12')),
  TIMESTAMP(CONCAT('2001-01-01 00:01:01','.123')),
  TIMESTAMP(CONCAT('2001-01-01 00:01:01','.1234')),
  TIMESTAMP(CONCAT('2001-01-01 00:01:01','.12345')),
  TIMESTAMP(CONCAT('2001-01-01 00:01:01','.123456')),
  TIMESTAMP(TIME'00:01:01'),
  TIMESTAMP(TIME'00:01:01.1'),
  TIMESTAMP(TIME'00:01:01.12'),
  TIMESTAMP(TIME'00:01:01.123'),
  TIMESTAMP(TIME'00:01:01.1234'),
  TIMESTAMP(TIME'00:01:01.12345'),
  TIMESTAMP(TIME'00:01:01.123456'),
  TIMESTAMP(TIMESTAMP('2001-01-01 00:00:00')),
  TIMESTAMP(TIMESTAMP('2001-01-01 00:00:00.1')),
  TIMESTAMP(TIMESTAMP('2001-01-01 00:00:00.12')),
  TIMESTAMP(TIMESTAMP('2001-01-01 00:00:00.123')),
  TIMESTAMP(TIMESTAMP('2001-01-01 00:00:00.1234')),
  TIMESTAMP(TIMESTAMP('2001-01-01 00:00:00.12345')),
  TIMESTAMP(TIMESTAMP('2001-01-01 00:00:00.123456'));
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Testing DEFAULT value
--echo #
CREATE TABLE t1 (a DATETIME(6) NOT NULL DEFAULT '2000-01-01 11:22:33.123456');
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Mixing varios DATETIME(N) in CASE
--echo #
CREATE TABLE t1 (
  t0 DATETIME, t1 DATETIME(1), t3 DATETIME(3),t4 DATETIME(4), t6 DATETIME(6));
CREATE TABLE t2 AS SELECT
  CASE WHEN 1 THEN t0 ELSE t1 END,
  CASE WHEN 1 THEN t0 ELSE t3 END,
  CASE WHEN 1 THEN t0 ELSE t4 END,
  CASE WHEN 1 THEN t0 ELSE t6 END,
  CASE WHEN 1 THEN t1 ELSE t0 END,
  CASE WHEN 1 THEN t1 ELSE t3 END,
  CASE WHEN 1 THEN t1 ELSE t4 END,
  CASE WHEN 1 THEN t1 ELSE t6 END,
  CASE WHEN 1 THEN t3 ELSE t0 END,
  CASE WHEN 1 THEN t3 ELSE t1 END,
  CASE WHEN 1 THEN t3 ELSE t4 END,
  CASE WHEN 1 THEN t3 ELSE t6 END,
  CASE WHEN 1 THEN t4 ELSE t0 END,
  CASE WHEN 1 THEN t4 ELSE t1 END,
  CASE WHEN 1 THEN t4 ELSE t3 END,
  CASE WHEN 1 THEN t4 ELSE t6 END,
  CASE WHEN 1 THEN t6 ELSE t0 END,
  CASE WHEN 1 THEN t6 ELSE t1 END,
  CASE WHEN 1 THEN t6 ELSE t3 END,
  CASE WHEN 1 THEN t6 ELSE t4 END
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;


--echo #
--echo # Mixing various DATETTIME(N) in COALESCE
--echo #
CREATE TABLE t1 (
  a DATETIME(6),
  b DATETIME(6),
  c DATETIME,
  d DATETIME,
  e DATETIME(3),
  f DATETIME(4));
CREATE TABLE t2 AS SELECT
  COALESCE(a, b), COALESCE(c, b),
  COALESCE(c, d), COALESCE(e, f),
  COALESCE(c, e), COALESCE(c, f)
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT
  CONCAT(COALESCE(a, b)), CONCAT(COALESCE(c, b)),
  CONCAT(COALESCE(c, d)), CONCAT(COALESCE(e, f)),
  CONCAT(COALESCE(c, e)), CONCAT(COALESCE(c, f))
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;


--echo #
--echo # Mixing DATETIME(6) with another DATETIME(6) in IF
--echo #
CREATE TABLE t1 (a DATETIME(6), b DATETIME(6));
CREATE TABLE t2 AS SELECT IF(1, a, b) AS a FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Mixing DATETIME(N) with DATETIME(M) in IF
--echo #
CREATE TABLE t1 (a DATETIME(1), b DATETIME(2));
CREATE TABLE t2 AS SELECT IF(1, a, b), IF(1, b, a) FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Mixing DATETIME(6) NOT NULL with another DATETIME(6) NOT NULL
--echo #
CREATE TABLE t1 (a DATETIME(6) NOT NULL, b DATETIME(6) NOT NULL);
CREATE TABLE t2 AS SELECT IF(1, a, b) AS a FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # UNION between DATETIME(6) and DATETIME(6)
--echo #
CREATE TABLE t1 (a DATETIME(6) NOT NULL, b DATETIME(6) NOT NULL);
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123456', '2001-01-01 00:11:22.123456');
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT b FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2 ORDER BY a;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # UNION between DATETIME(n)
--echo #
CREATE TABLE t1 (t0 DATETIME, t1 DATETIME(1), t3 DATETIME(3), t4 DATETIME(4));
CREATE TABLE t2 AS SELECT t0 FROM t1 UNION SELECT t1 FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT t1 FROM t1 UNION SELECT t3 FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT t3 FROM t1 UNION SELECT t4 FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # UNION between DATETIME(6) and DATETIME / DATE
--echo #
CREATE TABLE t1 (a DATETIME NOT NULL, b DATETIME(6) NOT NULL, c DATE NOT NULL);
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123456', '2001-01-01 00:11:22.123456','2001-01-01');
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT b FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2 ORDER BY a;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT b FROM t1 UNION SELECT a FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2 ORDER BY b;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT b FROM t1 UNION SELECT c FROM t1;
SHOW COLUMNS FROM t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT c FROM t1 UNION SELECT b FROM t1;
SHOW COLUMNS FROM t2;
SELECT * FROM t2 ORDER BY c;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # UNION between DATETIME(6) and various other types
--echo #
CREATE TABLE t1 (a DATETIME(6), b BIGINT, c DOUBLE, d DECIMAL(20,6), e VARCHAR(20));
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT b FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT c FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT d FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT e FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing DATETIME(6) field in combination with DATETIME type functions
--echo #
CREATE TABLE t1 (a DATETIME(6), b TIME(6));
INSERT INTO t1 VALUES ('2001-01-01 10:00:00.123456', '10:00:00.123456');
INSERT INTO t1 VALUES ('2001-01-01 10:00:00', '10:00:00');
SELECT * FROM t1 WHERE a=TIMESTAMP('2001-01-01', b);
SELECT * FROM t1 WHERE a=ADDTIME('2001-01-01 00:00:00', '10:00:00');
SELECT * FROM t1 WHERE a=ADDTIME('2001-01-01 00:00:00', '10:00:00.123456');
DELETE FROM t1;
INSERT INTO t1 VALUES (CURRENT_TIMESTAMP, CURRENT_TIME);
SELECT COUNT(*) FROM t1 WHERE a <= CURRENT_TIMESTAMP;
SELECT COUNT(*) FROM t1 WHERE a >= SUBTIME(CURRENT_TIMESTAMP, '10:00:00');
DROP TABLE t1;

--echo #
--echo # ORDER BY DATETIME(6) function
--echo #
CREATE TABLE t1(a DATETIME(6));
INSERT INTO t1 (a) VALUES ('9999-12-31 00:00:00.000002');
INSERT INTO t1 (a) VALUES ('9999-12-31 00:00:00.000001');
INSERT INTO t1 (a) VALUES ('9999-12-31 00:00:00.000000');
INSERT INTO t1 (a) VALUES ('9999-12-31 23:59:59.000001');
INSERT INTO t1 (a) VALUES ('2001-01-01 00:00:00.000003');
INSERT INTO t1 (a) VALUES ('2001-01-01 00:00:00.000000');
INSERT INTO t1 (a) VALUES ('2001-01-01 00:00:00.000001');
INSERT INTO t1 (a) VALUES ('0000-00-00 00:00:00.000000');
SELECT * FROM t1 ORDER BY ADDTIME(a, '00:00:00');
SELECT * FROM t1 ORDER BY ADDTIME(a, '00:00:00') DESC;
DROP TABLE t1;

--echo #
--echo # Testing partitions
--echo #
CREATE TABLE t1 (a DATETIME(6)) PARTITION BY KEY(a) PARTITIONS 4;
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000000');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000001');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000002');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000003');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000004');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000005');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000006');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000010');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000011');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000012');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000013');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000014');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000015');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000016');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000110');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000111');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000112');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000113');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000114');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000115');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000116');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000210');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000211');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000212');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000213');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000214');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000215');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000216');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000000');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000001');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000002');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000003');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000004');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000005');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000006');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000000');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000001');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000002');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000003');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000004');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000005');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000006');
SELECT * FROM t1 PARTITION(p0);
SELECT * FROM t1 PARTITION(p1);
SELECT * FROM t1 PARTITION(p2);
SELECT * FROM t1 PARTITION(p3);
ALTER TABLE t1 PARTITION BY RANGE COLUMNS (a)
(
  PARTITION p0 VALUES LESS THAN ('2001-01-01 00:00:00.000200'),
  PARTITION p1 VALUES LESS THAN ('2001-01-01 00:00:01'),
  PARTITION p2 VALUES LESS THAN ('2001-01-01 00:00:02.000003'),
  PARTITION p3 VALUES LESS THAN MAXVALUE
);
SELECT * FROM t1 PARTITION(p0);
SELECT * FROM t1 PARTITION(p1);
SELECT * FROM t1 PARTITION(p2);
SELECT * FROM t1 PARTITION(p3);
DROP TABLE t1;

--echo #
--echo # Checking that DATETIME/DATE literals can be a partition LESS value
--echo #
CREATE TABLE t1 (s1 DATETIME(6))
  PARTITION BY RANGE COLUMNS (s1)
  (PARTITION p1 VALUES LESS THAN (TIMESTAMP'2001-01-01 01:01:01.000001'),
   PARTITION p2 VALUES LESS THAN (DATE'2002-01-01'));
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Testing CAST to DATETIME and DATETIME(6)
--echo #
CREATE TABLE t1 AS SELECT
CAST(111 AS DATETIME),
CAST(100000000 AS DATETIME),
CAST(111.1 AS DATETIME),
CAST('20001111' AS DATETIME),
CAST(111e0 AS DATETIME),
CAST(111 AS DATETIME(0)),
CAST(100000000 AS DATETIME(0)),
CAST(111.1 AS DATETIME(0)),
CAST('20001111' AS DATETIME(0)),
CAST(111e0 AS DATETIME(0)),
CAST(111 AS DATETIME(6)),
CAST(100000000 AS DATETIME(6)),
CAST(111.1 AS DATETIME(6)),
CAST('20001111' AS DATETIME(6)),
CAST(111e0 AS DATETIME(6));
SHOW CREATE TABLE t1;
DROP TABLE t1;

CREATE TABLE t1 (a DATETIME, b DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 11:22:33', '2001-01-01 11:22:33.123456');
CREATE TABLE t2 AS SELECT CAST(a AS DATETIME), CAST(b AS DATETIME) FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT CAST(a AS DATETIME(0)), CAST(b AS DATETIME(0)) FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT CAST(a AS DATETIME(6)), CAST(b AS DATETIME(6)) FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;

--echo #
--echo # Testing Item_typecast_datetime::val_str
--echo #
SELECT CONCAT(CAST(a AS DATETIME(0))), CONCAT(CAST(b AS DATETIME(0))) FROM t1;
SELECT CONCAT(CAST(a AS DATETIME(6))), CONCAT(CAST(b AS DATETIME(6))) FROM t1;
SELECT CONCAT(CAST(a AS DATETIME)), CONCAT(CAST(b AS DATETIME)) FROM t1;

--echo #
--echo # Testing Item_typecast_datetime::get_time
--echo #
SELECT CAST(a AS DATETIME(0)), CAST(b AS DATETIME(0)) FROM t1;
SELECT CAST(a AS DATETIME(6)), CAST(b AS DATETIME(6)) FROM t1;
SELECT CAST(a AS DATETIME), CAST(b AS DATETIME) FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing Item_typecast_datetime::val_int
--echo #
SELECT CAST('2001-01-01 10:11:12' AS DATETIME(0)) + 1;

--echo #
--echo # Testing Item_typecast_time::val_decimal
--echo #
SELECT CAST('2001-01-01 10:11:12' AS DATETIME(6)) + 1;

--echo #
--echo # Testing CAST from numeric types to DATETIME
--echo #
CREATE TABLE t1 (bi BIGINT, d DOUBLE, dc DECIMAL(30,6));
INSERT INTO t1 VALUES (10101101112, 10101101112.1, 10101101112.1);
INSERT INTO t1 VALUES (20010101101112, 20010101101112.1, 20010101101112.1);
query_vertical SELECT
  CAST(bi AS DATETIME(1)),
  CAST(d AS DATETIME(1)),
  CAST(dc AS DATETIME(1)),
  CAST(10101101112 AS DATETIME(1)),
  CAST(10101101112.1 AS DATETIME(1)),
  CAST(20010101101112 AS DATETIME(1)),
  CAST(20010101101112.1 AS DATETIME(1))
FROM t1;
DROP TABLE t1;

SELECT CAST(1e300 AS DATETIME);
SELECT CAST(999999999999999999999999.999 AS DATETIME);
SELECT CAST(9223372036854775807 AS DATETIME);

--echo #
--echo # Testing comparison between a DATETIME(6) field and a DATETIME(N) type cast
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 10:11:12.000000');
INSERT INTO t1 VALUES ('2001-01-01 10:11:12.000001');
INSERT INTO t1 VALUES ('2001-01-01 10:11:12.000002');
INSERT INTO t1 VALUES ('2001-01-01 10:11:12.000003');
SELECT * FROM t1 WHERE a=CAST('2001-01-01 10:11:12.000003' AS DATETIME(6));
SELECT * FROM t1 WHERE a=CAST('2001-01-01 10:11:12.000003' AS DATETIME(0));
DROP TABLE t1;

--echo #
--echo # Testing comparison between CAST(x AS DATETIME(N)) and INT
--echo #
SELECT CAST('2001-01-01 10:11:12.123' AS DATETIME(0)) = 20010101101112;
SELECT CAST(20010101101112.123 AS DATETIME(0))        = 20010101101112;
SELECT CAST(00010101101112.123e0 AS DATETIME(0))      = 20010101101112;

--echo #
--echo # Testing Item_typecast_time::val_int_packed()
--echo #
CREATE TABLE t1 (a DATETIME, b DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 23:00:00', '2001-01-01 23:00:00.123456');
SELECT CAST('2001-01-01 23:00:00' AS DATETIME) = (SELECT a FROM t1);
SELECT CAST('2001-01-01 23:00:00' AS DATETIME(6)) = (SELECT a FROM t1);
SELECT CAST('2001-01-01 23:00:00.123456' AS DATETIME(0)) = (SELECT a FROM t1);
SELECT CAST('2001-01-01 23:00:00.123456' AS DATETIME(6)) = (SELECT b FROM t1);
DROP TABLE t1;

--echo #
--echo # Testing function MICROSECOND
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 11:12:13.000001');
INSERT INTO t1 VALUES ('2001-01-01 11:12:13.100001');
INSERT INTO t1 VALUES ('2001-01-01 11:12:13.999999');
SELECT a, MICROSECOND(a) FROM t1;
SELECT a, EXTRACT(MICROSECOND FROM a) FROM t1;
DROP TABLE t1;

SELECT MICROSECOND(CAST(20010101235959.456 AS DATETIME(6)));
SELECT EXTRACT(MICROSECOND FROM CAST(20010101235959.456 AS DATETIME(6)));

--echo #
--echo # Testing PS
--echo #
CREATE TABLE t1 (a DATETIME(6));
PREPARE stmt FROM 'INSERT INTO t1 VALUES (?)';
SET @a='2001-01-01 11:22:33.123456';
SET @b=20010101112233.123456;
SET @c=0.123456e0;
EXECUTE stmt USING @a;
EXECUTE stmt USING @c;
DEALLOCATE PREPARE stmt;
SELECT * FROM t1;
PREPARE stmt FROM 'SELECT * FROM t1 WHERE a=?';
EXECUTE stmt USING @a;
EXECUTE stmt USING @b;
EXECUTE stmt USING @c;
DEALLOCATE PREPARE stmt;
DROP TABLE t1;

--echo #
--echo # Testing DATETIME(6) and user variables
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123456');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.123456');
SET @a='2001-01-01 11:22:33.123456';
SELECT * FROM t1 WHERE a=@a;
SET @a=20010101112233.123456;
SELECT * FROM t1 WHERE a=@a;
SET @a=0.123456e0;
SELECT * FROM t1 WHERE a=@a;
SET @a=NULL;
SELECT a INTO @a FROM t1 LIMIT 1;
SELECT @a;
DROP TABLE t1;
# MEMORY does not support BLOB
if (`SELECT @@default_storage_engine != 'MEMORY'`)
{
  CREATE TABLE t1 AS SELECT @a AS a;
  SHOW CREATE TABLE t1;
  SELECT * FROM t1;
  DROP TABLE t1;
}

--echo #
--echo # Testing SP
--echo #
DELIMITER //;
CREATE PROCEDURE p1 ()
BEGIN
  DECLARE a DATETIME(6);
  SET a='2001-01-01 11:22:33.123';
  SELECT a;
END//
SHOW CREATE PROCEDURE p1//
CALL p1//
DROP PROCEDURE p1//
CREATE PROCEDURE p1 (a DATETIME(6))
BEGIN
  SELECT a;
END//
SHOW CREATE PROCEDURE p1//
CALL p1(20010101112233)//
CALL p1(20010101112233.123)//
CALL p1(0.123e0)//
CALL p1('2001-01-01 11:22:33.123')//
DROP PROCEDURE p1//
CREATE PROCEDURE p1()
BEGIN
  DECLARE a DATETIME(6);
  CREATE TABLE t1 AS SELECT a;
  SHOW CREATE TABLE t1;
  DROP TABLE t1;
END//
SHOW CREATE PROCEDURE p1//
CALL p1//
DROP PROCEDURE p1//
CREATE FUNCTION p1 (a DATETIME(6)) RETURNS DATETIME(6)
BEGIN
  RETURN a;
END//
SHOW CREATE FUNCTION p1//
SELECT p1(20010101112233)//
SELECT p1(20010101112233.123)//
DROP FUNCTION p1//
DELIMITER ;//

--echo #
--echo # Testing INFORMATION_SCHEMA.COLUMNS
--echo #
CREATE TABLE t1 (a DATETIME(6));
--replace_column 19 #
--query_vertical SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='t1';
DROP TABLE t1;

--echo #
--echo # SELECT from a subquery
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.123456'), ('2001-01-01 11:11:11.123456');
SELECT * FROM (SELECT * FROM t1) t12;
DROP TABLE t1;

--echo #
--echo # Testing IN and = subqueries
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.123456'), ('2001-01-01 11:11:11.123456');
SELECT * FROM t1 WHERE a IN (SELECT MIN(a) FROM t1);
SELECT * FROM t1 WHERE a = (SELECT a FROM t1 ORDER BY a DESC LIMIT 1);
DROP TABLE t1;

--echo #
--echo # Testing IN subquery + GROUP
--echo #
CREATE TABLE t1 (id INT, a DATETIME(6));
INSERT INTO t1 VALUES (1, '2001-01-01 00:00:00.123456'), (1, '2001-01-01 11:00:00.123456');
INSERT INTO t1 VALUES (2, '2001-01-01 00:01:00.123456'), (2, '2001-01-01 11:01:00.123456');
INSERT INTO t1 VALUES (3, NULL);
SELECT * FROM t1 WHERE a IN (SELECT MAX(a) FROM t1 GROUP BY id);
DROP TABLE t1;

--echo #
--echo # Testing VIEW
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.123456'), ('2001-01-01 11:11:11.123456');
CREATE VIEW v1 AS SELECT a FROM t1;
SELECT * FROM v1 WHERE a='2001-01-01 00:00:00.123456';
SELECT MIN(a), MAX(a) FROM v1;
SELECT * FROM t1 WHERE a IN (SELECT MAX(a) FROM v1);
DROP VIEW v1;
CREATE VIEW v1 AS SELECT MAX(a) AS a FROM t1;
SELECT * FROM v1;
SELECT * FROM t1 WHERE a=(SELECT a FROM v1);
DROP VIEW v1;
DROP TABLE t1;

--echo #
--echo # Testing that CAST from various fields to DATETIME rounds.
--echo # This tests Field::get_date().
--echo #
CREATE TABLE t1 (a VARCHAR(32));
INSERT INTO t1 VALUES
('9999-12-31 23:59:59.0000009'), ('9999-12-31 23:59:59.9'),
('2001-01-01 10:10:10.9999994'), ('2001-01-01 10:10:10.9999995');
SELECT a, CAST(a AS DATETIME), CAST(a AS DATETIME(6)) FROM t1;
DROP TABLE t1;
CREATE TABLE t1 (a DECIMAL(30, 7));
INSERT INTO t1 VALUES
  (99991231235959.0000009), (99991231235959.9),
  (20010101101010.9999994), (20010101101010.9999995);
SELECT a, CAST(a AS DATETIME), CAST(a AS DATETIME(6)) FROM t1;
DROP TABLE t1;


--echo #
--echo # Testing rounding with CAST
--echo #
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999999');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999998');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999997');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999996');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999995');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999994');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999993');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999992');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999991');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999990');
SELECT a, CAST(a AS DATETIME(5)) FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (a DATETIME(5));
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.99999');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.99998');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.99997');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.99996');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.99995');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.99994');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.99993');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.99992');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.99991');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.99990');
SELECT a, CAST(a AS DATETIME(4)) FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (a DATETIME(4));
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.9999');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.9998');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.9997');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.9996');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.9995');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.9994');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.9993');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.9992');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.9991');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.9990');
SELECT a, CAST(a AS DATETIME(3)) FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (a DATETIME(3));
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.998');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.997');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.996');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.995');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.994');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.993');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.992');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.991');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.990');
SELECT a, CAST(a AS DATETIME(2)) FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (a DATETIME(2));
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.99');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.98');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.97');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.96');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.95');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.94');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.93');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.92');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.91');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.90');
SELECT a, CAST(a AS DATETIME(1)) FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (a DATETIME(1));
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.9');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.8');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.7');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.6');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.5');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.4');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.3');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.2');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.1');
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.0');
SELECT a, CAST(a AS DATETIME) FROM t1;
DROP TABLE t1;


--echo #
--echo # Testing that Item::get_date() rounds
--echo #
query_vertical SELECT
  CAST('2001-01-01 10:10:10.9999994' AS DATETIME),
  CAST('2001-01-01 10:10:10.9999995' AS DATETIME),
  CAST('2001-01-01 10:10:10.9999994' AS DATETIME(6)),
  CAST('2001-01-01 10:10:10.9999995' AS DATETIME(6)),
  CAST(20010101101010.9999994 AS DATETIME),
  CAST(20010101101010.9999995 AS DATETIME),
  CAST(20010101101010.9999994 AS DATETIME(6)),
  CAST(20010101101010.9999995 AS DATETIME(6));

--echo #
--echo # Testing that comparison rounds
--echo #
CREATE TABLE t1 (t0 DATETIME, t6 DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 00:00:00', '2001-01-01 00:00:00.999999');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01', '2001-01-01 00:00:01.000000');
SELECT t0 FROM t1 WHERE t6='2001-01-01 00:00:00.9999998';
SELECT t6 FROM t1 WHERE t6='2001-01-01 00:00:00.9999998';
DROP TABLE t1;

--echo #
--echo # Testing that EXTRACT rounds
--echo #
query_vertical SELECT
  EXTRACT(MICROSECOND FROM '2001-01-01 00:00:00.9999994'),
  EXTRACT(MICROSECOND FROM '2001-01-01 00:00:00.9999995'),
  EXTRACT(MICROSECOND FROM 20010101000000.9999994),
  EXTRACT(MICROSECOND FROM 20010101000000.9999995);

##############################################################################

## Difference between TIMESTAMP vs DATETIME
## - does not allow wrong dates (e.g. zero month, or non-existing leap day)
## - can have DEFAULT CURRENT_TIMESTSAMP 
## - can have ON UPDATE CURRENT_TIMESTAMP
## - neither DEFAULT nor ON UPDATE is the same as DEFAULT + ON UPDATE 
## - NOT NULL by default, update to NULL sets to CURRENT_TIMESTAMP
## - can have "NULL" attribute, non-default


--echo #
--echo # Testing that TIMESTAMP(0) is a synonym to non-fractional TIMESTAMP
--echo #

CREATE TABLE t1 (a TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0));
SHOW CREATE TABLE t1;
DROP TABLE t1;


--echo #
--echo # Testing internal representation format for DATETIME(N)
--echo #
SET time_zone='+00:00';
CREATE TABLE t1 (a6 VARCHAR(32));
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000000');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000001');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000002');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000003');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000004');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000005');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000006');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000007');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000008');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000009');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000010');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000011');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000012');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000013');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000014');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000015');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000016');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000099');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.000999');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.009999');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.099999');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.100000');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.900000');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.990000');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.999000');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.999900');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.999990');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01.999999');

INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000000');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000001');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000002');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000003');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000004');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000005');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000006');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000007');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000008');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000009');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000010');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000011');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000012');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000013');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000014');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000015');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000016');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000099');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.000999');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.009999');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.090000');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.099999');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.100000');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.900000');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.990000');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.999000');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.999900');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.999990');
INSERT INTO t1 VALUES ('1970-01-01 00:00:02.999999');

INSERT INTO t1 VALUES ('1970-01-01 00:01:00.000001');
INSERT INTO t1 VALUES ('1970-01-01 00:01:00.000008');
INSERT INTO t1 VALUES ('1970-01-01 00:01:00.000015');
INSERT INTO t1 VALUES ('1970-01-01 00:01:00.000016');
INSERT INTO t1 VALUES ('1970-01-01 00:01:00.000099');
INSERT INTO t1 VALUES ('1970-01-01 00:01:00.000999');
INSERT INTO t1 VALUES ('1970-01-01 00:01:00.009999');
INSERT INTO t1 VALUES ('1970-01-01 00:01:00.099999');
INSERT INTO t1 VALUES ('1970-01-01 00:01:00.100000');
INSERT INTO t1 VALUES ('1970-01-01 00:01:00.900000');
INSERT INTO t1 VALUES ('1970-01-01 00:01:00.999999');

INSERT INTO t1 VALUES ('1970-01-01 01:00:00.000001');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.000008');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.000015');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.000016');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.000099');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.000999');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.009999');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.099999');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.100000');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.900000');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.990000');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.999000');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.999900');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.999990');
INSERT INTO t1 VALUES ('1970-01-01 01:00:00.999999');

INSERT INTO t1 VALUES ('2001-01-01 23:59:58.000001');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.000008');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.000015');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.000016');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.000099');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.000999');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.009999');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.099999');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.100000');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.900000');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.990000');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.999000');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.999900');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.999990');
INSERT INTO t1 VALUES ('2001-01-01 23:59:58.999999');
INSERT INTO t1 VALUES ('2001-01-01 23:59:59.000000');

INSERT INTO t1 VALUES ('2037-01-01 23:59:58.000001');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.000008');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.000015');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.000016');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.000099');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.000999');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.009999');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.099999');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.100000');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.900000');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.990000');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.999000');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.999900');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.999990');
INSERT INTO t1 VALUES ('2037-01-01 23:59:58.999999');
INSERT INTO t1 VALUES ('2037-01-01 23:59:59.000000');

ALTER TABLE t1
  ADD a0 VARCHAR(32),
  ADD a1 VARCHAR(32), ADD a2 VARCHAR(32), ADD a3 VARCHAR(32),  
  ADD a4 VARCHAR(32), ADD a5 VARCHAR(32),
  ADD d0 TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  ADD d1 TIMESTAMP(1), ADD d2 TIMESTAMP(2), ADD d3 TIMESTAMP(3),
  ADD d4 TIMESTAMP(4), ADD d5 TIMESTAMP(5), ADD d6 TIMESTAMP(6);
UPDATE t1 SET
  a0= LEFT(a6, LENGTH(a6) - 6),
  a1= LEFT(a6, LENGTH(a6) - 5),
  a2= LEFT(a6, LENGTH(a6) - 4),
  a3= LEFT(a6, LENGTH(a6) - 3),
  a4= LEFT(a6, LENGTH(a6) - 2),
  a5= LEFT(a6, LENGTH(a6) - 1);
UPDATE t1 SET d0= a0, d1= a1, d2= a2, d3= a3, d4= a4, d5= a5, d6= a6;
SELECT a6, d6, HEX(WEIGHT_STRING(d6)) FROM t1 ORDER BY d6;
SELECT a5, d5, HEX(WEIGHT_STRING(d5)) FROM t1 ORDER BY d5, a6;
SELECT a4, d4, HEX(WEIGHT_STRING(d4)) FROM t1 ORDER BY d4, a6;
SELECT a3, d3, HEX(WEIGHT_STRING(d3)) FROM t1 ORDER BY d3, a6;
SELECT a2, d2, HEX(WEIGHT_STRING(d2)) FROM t1 ORDER BY d2, a6;
SELECT a1, d1, HEX(WEIGHT_STRING(d1)) FROM t1 ORDER BY d1, a6;
SELECT a0, d0, HEX(WEIGHT_STRING(d0)) FROM t1 ORDER BY d0, a6;
CREATE VIEW v1 AS
SELECT a6, d0, d1, d2, d3, d4, d5, d6,
HEX(WEIGHT_STRING(d0)) as wst0,
HEX(WEIGHT_STRING(d1)) as wst1,
HEX(WEIGHT_STRING(d2)) as wst2,
HEX(WEIGHT_STRING(d3)) as wst3,
HEX(WEIGHT_STRING(d4)) as wst4,
HEX(WEIGHT_STRING(d5)) as wst5,
HEX(WEIGHT_STRING(d6)) as wst6
FROM t1;
--query_vertical SELECT * FROM  v1 WHERE a6 LIKE '%.999999' ORDER BY a6, d6;
--query_vertical SELECT * FROM  v1 WHERE a6 LIKE '%.000000' ORDER BY a6, d6;
--query_vertical SELECT * FROM  v1 WHERE a6 LIKE '%.100000' ORDER BY a6, d6;
DROP VIEW v1;
DROP TABLE t1;
SET time_zone=default;


--echo #
--echo # Testing bad FSPs
--echo #
--error ER_PARSE_ERROR
CREATE TABLE t1 (a TIMESTAMP(-1));
--error ER_TOO_BIG_PRECISION
CREATE TABLE t1 (a TIMESTAMP(7));
--error ER_TOO_BIG_PRECISION
CREATE TABLE t1 (a TIMESTAMP(31));

--echo #
--echo # Testing rounding when altering TIMESTAMP(N) to a smaller size
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999999');
ALTER TABLE t1 MODIFY a TIMESTAMP(5);
SELECT * FROM t1;
DROP TABLE t1;
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 10:10:10.999999');
ALTER TABLE t1 MODIFY a TIME;
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding from VARCHAR and DATETIME(6) to TIMESTAMP(N)
--echo #
CREATE TABLE t1 (a VARCHAR(32),
  t6 TIMESTAMP(6) NULL DEFAULT NULL,
  t5 TIMESTAMP(5) NULL DEFAULT NULL,
  t4 TIMESTAMP(4) NULL DEFAULT NULL,
  t3 TIMESTAMP(3) NULL DEFAULT NULL,
  t2 TIMESTAMP(2) NULL DEFAULT NULL,
  t1 TIMESTAMP(1) NULL DEFAULT NULL,
  t0 TIMESTAMP    NULL DEFAULT NULL);
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.999999');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.999994');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.999949');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.999499');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.994999');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.949999');
INSERT INTO t1 (a) VALUES ('2001-01-01 10:10:10.499999');
UPDATE t1 SET t0=a, t1=a, t2=a, t3=a, t4=a, t5=a, t6=a;
--query_vertical SELECT * FROM t1;
ALTER TABLE t1 MODIFY a TIMESTAMP(6);
UPDATE t1 SET t0=a, t1=a, t2=a, t3=a, t4=a, t5=a, t6=a;
--query_vertical SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding from TIMESTAMP(N) to BIGINT
--echo #
CREATE TABLE t1 (a BIGINT, b TIMESTAMP(6) NULL DEFAULT NULL);
INSERT INTO t1 (b) VALUES ('2001-01-01 10:10:59.500000');
INSERT INTO t1 (b) VALUES ('2001-01-01 10:10:10.500000');
INSERT INTO t1 (b) VALUES ('2001-01-01 10:10:10.499999');
UPDATE t1 SET a=b;
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing rounding from TIME(6) to TIMESTAMP(N)
--echo #
SET timestamp=UNIX_TIMESTAMP('2001-01-01 00:00:00');
CREATE TABLE t1 (a TIME(6),
  t6 TIMESTAMP(6), t5 TIMESTAMP(5), t4 TIMESTAMP(4),
  t3 TIMESTAMP(3), t2 TIMESTAMP(2), t1 TIMESTAMP(1),
  t0 TIMESTAMP);
INSERT INTO t1 (a) VALUES ('10:10:10.999999');
INSERT INTO t1 (a) VALUES ('10:10:10.999994');
INSERT INTO t1 (a) VALUES ('10:10:10.999949');
INSERT INTO t1 (a) VALUES ('10:10:10.999499');
INSERT INTO t1 (a) VALUES ('10:10:10.994999');
INSERT INTO t1 (a) VALUES ('10:10:10.949999');
INSERT INTO t1 (a) VALUES ('10:10:10.499999');
UPDATE t1 SET t0=a, t1=a, t2=a, t3=a, t4=a, t5=a, t6=a;
--query_vertical SELECT * FROM t1;
DROP TABLE t1;
SET timestamp=default;

--echo #
--echo # Testing rounding from DECIMAL to TIMESTAMP(N)
--echo #
CREATE TABLE t1 (a DECIMAL(30,6),
  t6 TIMESTAMP(6), t5 TIMESTAMP(5), t4 TIMESTAMP(4),
  t3 TIMESTAMP(3), t2 TIMESTAMP(2), t1 TIMESTAMP(1),
  t0 TIMESTAMP);
INSERT INTO t1 (a) VALUES (20010101101010.999999);
INSERT INTO t1 (a) VALUES (20010101101010.999994);
INSERT INTO t1 (a) VALUES (20010101101010.999949);
INSERT INTO t1 (a) VALUES (20010101101010.999499);
INSERT INTO t1 (a) VALUES (20010101101010.994999);
INSERT INTO t1 (a) VALUES (20010101101010.949999);
INSERT INTO t1 (a) VALUES (20010101101010.499999);
UPDATE t1 SET t0=a, t1=a, t2=a, t3=a, t4=a, t5=a, t6=a;
--query_vertical SELECT * FROM t1;
DROP TABLE t1;


--echo #
--echo # Testing INSERT, ORDER, KEY, BETWEEN, comparison
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL);
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000000');
INSERT INTO t1 VALUES ('0000-00-00 23:59:59.999999');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03.4');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03.45');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03.456');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03.4567');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03.45678');
INSERT INTO t1 VALUES ('2000-01-01 01:02:03.4567891');
INSERT INTO t1 VALUES ('9999-12-31 23:59:59.000000');
SELECT * FROM t1;
SELECT * FROM t1 ORDER BY a DESC;
SELECT * FROM t1 WHERE a='2000-01-01 01:02:03.45';
SELECT * FROM t1 WHERE a='2000-01-01 01:02:03.4567';
SELECT * FROM t1 WHERE a='2000-01-01 01:02:03.45670';
SELECT * FROM t1 WHERE a='2000-01-01 01:02:03.456700';
SELECT * FROM t1 WHERE a BETWEEN '2000-01-01 01:02:00' AND '2000-01-01 01:03:00';
ALTER TABLE t1 ADD KEY(a);
# Testing key order
SELECT * FROM t1 ORDER BY a;
SELECT * FROM t1 ORDER BY a DESC;

analyze table t1;
EXPLAIN SELECT * FROM t1 WHERE a='2000-01-01 01:02:03.456700';
SELECT * FROM t1 WHERE a='2000-01-01 01:02:03.456700';
SELECT * FROM t1 WHERE a BETWEEN '2000-01-01 01:02:00' AND '2000-01-01 01:03:00';
SELECT * FROM t1 WHERE a BETWEEN TIMESTAMP'2000-01-01 01:02:03.456' AND '2000-01-01 01:02:03.45678';
DROP TABLE t1;


--echo #
--echo # Testing range: stored_field_cmp_to_item
--echo #
CREATE TABLE t1 (a datetime(6), key(a));
INSERT INTO t1 VALUES
  ('2000-01-01 00:00:00'), ('2000-01-01 00:00:00'),
  ('2000-01-01 00:00:01'), ('2000-01-01 00:00:01');
SELECT * FROM t1 WHERE a >= 20000101000000;
SELECT * FROM t1 WHERE a >= 20000101000000.0;
UPDATE t1 SET a=ADDTIME(a, 0.000001);
SELECT * FROM t1;
SELECT * FROM t1 WHERE a >= 20000101000000.000001;
DROP TABLE t1;


--echo #
--echo # Rare TIMESTAMP formats
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES (101.101);
INSERT INTO t1 VALUES (990101.102);
INSERT INTO t1 VALUES ('990101.103');
INSERT INTO t1 VALUES (131.104);
INSERT INTO t1 VALUES ('000131.105');
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing bad values
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('xxxx');
INSERT INTO t1 VALUES ('1999-01-01 00:00:00.123456 xxx');
INSERT INTO t1 VALUES ('1999-01-01 00:00:00 xxx');
INSERT INTO t1 VALUES ('1999-01-01 xxx');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.123');
INSERT INTO t1 VALUES (0.123);
INSERT INTO t1 VALUES (0.123e0);
INSERT INTO t1 VALUES (CAST('0000-00-00 00:00:00.123' AS DATETIME(6)));
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing huge values
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ( 100000000000000000.1);
INSERT INTO t1 VALUES ( 100000000000000000.1e0);
INSERT INTO t1 VALUES (-100000000000000000.1);
INSERT INTO t1 VALUES (-100000000000000000.1e0);
INSERT INTO t1 VALUES (CAST(0xFFFFFFFF00000000 AS SIGNED));
INSERT INTO t1 VALUES (CAST(0xFFFFFFFF00000000 AS UNSIGNED));
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing implicit CAST from TIME to TIMESTAMP
--echo #
SET time_zone='+00:00';
SET @@timestamp=UNIX_TIMESTAMP('2008-01-03 10:20:30.1');
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES (CURRENT_TIME), (CURRENT_TIME(6));
INSERT INTO t1 VALUES (TIME'08:00:00.123456'), (TIME'240:00:00.000001');
INSERT INTO t1 VALUES (TIME'-10:00:00.000001'), (TIME'-240:00:00.000001');
SELECT CURRENT_DATE, CURRENT_TIME(6);
SELECT a, HEX(WEIGHT_STRING(a)) FROM t1;
DELETE FROM t1;
SET time_zone='-12:00';
INSERT INTO t1 VALUES (CURRENT_TIME), (CURRENT_TIME(6));
INSERT INTO t1 VALUES (TIME'08:00:00.123456'), (TIME'240:00:00.000001');
INSERT INTO t1 VALUES (TIME'-10:00:00.000001'), (TIME'-240:00:00.000001');
SELECT CURRENT_DATE, CURRENT_TIME(6);
SELECT a, HEX(WEIGHT_STRING(a)) FROM t1;
SET time_zone='+00:00';
SELECT CURRENT_DATE, CURRENT_TIME(6);
SELECT a, HEX(WEIGHT_STRING(a)) FROM t1;
DROP TABLE t1;
SET time_zone=default;
SET @@timestamp=default;


--echo #
--echo # Testing IN
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000001');
INSERT INTO t1 VALUES ('2000-01-01 00:00:00.000000');
INSERT INTO t1 VALUES ('2000-01-01 00:00:00.000001');
INSERT INTO t1 VALUES ('2000-01-01 00:00:00.000002');
INSERT INTO t1 VALUES ('2000-01-01 10:00:00.000000');
INSERT INTO t1 VALUES ('2000-01-01 10:00:00.000001');
INSERT INTO t1 VALUES ('2000-01-01 10:00:00.000002');
INSERT INTO t1 VALUES ('2000-01-01 23:00:00.000000');
INSERT INTO t1 VALUES ('2000-01-01 23:00:00.000001');
INSERT INTO t1 VALUES ('2000-01-01 23:00:00.000002');
SELECT * FROM t1;
SELECT * FROM t1 WHERE a IN ('2000-01-01 00:00:00', '0000-00-00 00:00:00.000001', '2000-01-01 23:00:00.000002');
SELECT * FROM t1 WHERE a IN (0, 20000101000000, 20000101230000);
# This returns an empty set with a warning:
SELECT * FROM t1 WHERE a=0.1;
# So does this:
SELECT * FROM t1 WHERE a IN (0.1);
# Perhaps it is fine, as INSERT INTO t1 VALUES (0.1) stores '0000-00-00 00:00:00'
# "IN (0.000001)" also returns '0000-00-00 00:00:00' for the same reasons
SELECT * FROM t1 WHERE a IN (20000101000000.000001, 20000101230000.000001);
# The following query returns an empty set with a warning
# because 0.000001 is a bad TIMESTAMP value.
# TS-TODO-QQ: Perhaps it should return the other two records,
# but the same happens for other data types, e.g.:
# SELECT * FROM t1 WHERE time_column IN ('800:00:00','900:00:00');
SELECT * FROM t1 WHERE
  a IN (0.000001, 20000101000000.000001, 20000101230000.000001);
SELECT * FROM t1 WHERE a IN (0e0, 0.000001e0);
--echo #
--echo # Testing IN with index: involves field->store_packed()
--echo #
ALTER TABLE t1 ADD KEY(a);
SELECT * FROM t1 WHERE a IN ('2000-01-01 00:00:00', '0000-00-00 00:00:00.000001', '2000-01-01 23:00:00.000002');
SELECT * FROM t1 WHERE a IN (0, 20000101000000, 20000101230000);
SELECT * FROM t1 WHERE a IN (0.000001, 20000101000000.000001, 20000101230000.000001);
SELECT * FROM t1 WHERE a IN (20000101000000.000001, 20000101230000.000001);
SELECT * FROM t1 WHERE a IN (0e0, 0.000001e0);
DROP TABLE t1;


--echo #
--echo # Testing CREATE TABLE LIKE
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
CREATE TABLE t2 LIKE t1;
SHOW CREATE TABLE t2;
DROP TABLE t2, t1;

--echo #
--echo # Testing CREATE TABLE .. SELECT
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
CREATE TABLE t2 AS SELECT * FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2, t1;

--echo #
--echo # Testing JOIN between TIMESTAMP(6) and TIMESTAMP(6)
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2000-01-01 00:01:03.1');
INSERT INTO t1 VALUES ('2000-01-01 00:01:03.12');
INSERT INTO t1 VALUES ('2000-01-01 00:01:03.123');
INSERT INTO t1 VALUES ('2000-01-01 00:01:03.1234');
INSERT INTO t1 VALUES ('2000-01-01 00:01:03.12345');
INSERT INTO t1 VALUES ('2000-01-01 00:01:03.123456');
CREATE TABLE t2 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t2 SELECT * FROM t1;
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t1 ADD KEY(a);
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t2 ADD KEY(a);
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t1 DROP KEY a;
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing JOIN between TIMESTAMP and TIMESTAMP(6)
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('0000-00-00 23:59:59.000000');
INSERT INTO t1 VALUES ('2000-01-01 10:11:12.000000');
INSERT INTO t1 VALUES ('2000-01-01 10:11:12.000001');
INSERT INTO t1 VALUES ('2000-01-01 10:11:12.000002');
INSERT INTO t1 VALUES ('2000-01-01 10:11:13.000000');
INSERT INTO t1 VALUES ('2000-01-01 10:11:13.000001');
INSERT INTO t1 VALUES ('2000-01-01 10:11:13.000002');
INSERT INTO t1 VALUES ('2000-01-01 23:59:59.999999');
INSERT INTO t1 VALUES ('2000-01-01 23:59:59.000000');
CREATE TABLE t2 (a TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP);
INSERT INTO t2 VALUES ('2000-01-01 10:11:12');
INSERT INTO t2 VALUES ('2000-01-01 10:11:13');
INSERT INTO t2 VALUES ('0000-00-00 23:59:59');
INSERT INTO t2 VALUES ('2000-01-01 23:59:59');
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t1 ADD KEY(a);
ALTER TABLE t2 ADD KEY(a);
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
DROP TABLE t2, t1;

--echo #
--echo # Testing unique index
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), UNIQUE(a));
INSERT INTO t1 VALUES ('2000-01-01 00:01:02');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.1');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.12');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.123');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.1234');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.12345');
--error ER_DUP_ENTRY
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.12345');
DROP TABLE t1;

--echo #
--echo # Testing GROUP BY
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2000-01-01 00:01:02');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.0');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.01');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.010');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.02');
INSERT INTO t1 VALUES ('2000-01-01 00:01:02.020');
SELECT a, COUNT(*) FROM t1 GROUP BY a;
DROP TABLE t1;

--echo #
--echo # Testing MIN() and MAX()
--echo #
CREATE TABLE t1 (a INT, b TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES (1, '2001-01-01 00:01:02.000001');
INSERT INTO t1 VALUES (1, '2001-01-01 00:01:02');
INSERT INTO t1 VALUES (2, '2001-01-01 10:01:02');
INSERT INTO t1 VALUES (2, '2001-01-01 10:01:02.000001');
INSERT INTO t1 VALUES (3, '2001-01-01 10:11:02');
INSERT INTO t1 VALUES (3, '2001-01-01 10:11:02.000001');
SELECT MIN(b), MAX(b) FROM t1;
SELECT a, MIN(b), MAX(b) FROM t1 GROUP BY a;
DROP TABLE t1;

--echo #
--echo # Testing LEAST
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.1');
SELECT LEAST(a, '2001-01-02 00:00:00.1') AS a FROM t1;
SELECT LEAST(a, '2001-01-02 00:00:00.1') + 0 AS a FROM t1;
SELECT CAST(LEAST(a, '2001-01-02 00:00:00.1') AS SIGNED) AS a FROM t1;
SELECT CAST(LEAST(a, '2001-01-02 00:00:00.1') AS DECIMAL(30,6)) AS a FROM t1;
SELECT GREATEST(a, '2001-01-02 00:00:00.1') AS a FROM t1;
SELECT GREATEST(a, '2001-01-02 00:00:00.1') + 0 AS a FROM t1;
SELECT CAST(GREATEST(a, '2001-01-02 00:00:00.1') AS SIGNED) AS a FROM t1;
SELECT CAST(GREATEST(a, '2001-01-02 00:00:00.1') AS DECIMAL(30,6)) AS a FROM t1;
DROP TABLE t1;
CREATE TABLE t1 (a TIMESTAMP(1) NOT NULL DEFAULT CURRENT_TIMESTAMP(1) ON UPDATE CURRENT_TIMESTAMP(1), b TIMESTAMP(2) NOT NULL DEFAULT '0000-00-00 00:00:00.000000');
CREATE TABLE t2 AS SELECT LEAST(a, b), GREATEST(a, b) FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing INSERT, MAX and MIN values
--echo # '1970-01-01 00:00:01.000000' UTC to '2038-01-19 03:14:07.999999' UTC
--echo #
SET time_zone='+00:00';
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2038-01-19 03:14:07');
INSERT INTO t1 VALUES ('2038-01-19 03:14:07.999999');
INSERT INTO t1 VALUES ('2038-01-19 03:14:08');
INSERT INTO t1 VALUES ('1970-01-01 00:00:00');
INSERT INTO t1 VALUES ('1970-01-01 00:00:00.999999');
INSERT INTO t1 VALUES ('1970-01-01 00:00:01');
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing bad values
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.1');
INSERT INTO t1 VALUES ('0000-00-00 00:00:01.1');
INSERT INTO t1 VALUES (0.1);
INSERT INTO t1 VALUES (1.1);
INSERT INTO t1 VALUES (0.1e0);
INSERT INTO t1 VALUES (1.1e0);
INSERT INTO t1 VALUES (-0.1);
INSERT INTO t1 VALUES (-1.0);
INSERT INTO t1 VALUES (-1.1);
INSERT INTO t1 VALUES (-0.1e0);
INSERT INTO t1 VALUES (-1.0e0);
INSERT INTO t1 VALUES (-1.1e0);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing integer INSERT value
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES (19990102);
INSERT INTO t1 VALUES (19990102112233);
INSERT INTO t1 VALUES (100000101112233);
INSERT INTO t1 VALUES (0);
INSERT INTO t1 VALUES (-1);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing decimal INSERT values
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES (20010101223344.4);
INSERT INTO t1 VALUES (20010101223344.45);
INSERT INTO t1 VALUES (20010101223344.456);
INSERT INTO t1 VALUES (20010101223344.4567);
INSERT INTO t1 VALUES (20010101223344.45678);
INSERT INTO t1 VALUES (20010101223344.456789);
INSERT INTO t1 VALUES (20010101202030.4567894);
INSERT INTO t1 VALUES (20010101202030.4567895);
INSERT INTO t1 VALUES (20010101202030.4567896);
INSERT INTO t1 VALUES (20010101202030.456789678);
INSERT INTO t1 VALUES (200101018385959.000000);
INSERT INTO t1 VALUES (-200101018385959.000000);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing double INSERT values
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES (112.233e-3);
INSERT INTO t1 VALUES (112.223e-2);
INSERT INTO t1 VALUES (112.233e-1);
INSERT INTO t1 VALUES (112.233e0);
INSERT INTO t1 VALUES (112.233e1);
INSERT INTO t1 VALUES (112.233e2);
INSERT INTO t1 VALUES (112.233e3);
INSERT INTO t1 VALUES (112.233e4);
INSERT INTO t1 VALUES (-123.456e0);
SELECT * FROM t1;
DROP TABLE t1;


--echo #
--echo # Testing UPDATE between TIMESTAMP(6) and TIMESTAMP/BIGINT/DECIMAL/DOUBLE
--echo #
CREATE TABLE t1 (t0 TIMESTAMP, t6 TIMESTAMP(6), i BIGINT, d DECIMAL(20,6), f DOUBLE);
INSERT INTO t1 (t0) VALUES ('2000-01-01 11:22:33');
UPDATE t1 SET t6=t0, i=t0, d=t0, f=t0;
SELECT t6, i, d FROM t1;
UPDATE t1 SET t6='2001-02-02 11:22:33.1';
UPDATE t1 SET t0=t6, i=t6, d=t6, f=t6;
SELECT * FROM t1;
UPDATE t1 SET i=20000101112233, d=20001010112233.1, f=00001010112233.1;
UPDATE t1 SET t6=i;
SELECT t6 FROM t1;
UPDATE t1 SET t6=d;
SELECT t6 FROM t1;
UPDATE t1 SET t6=f;
SELECT t6 FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing JOIN with comparison between TIMESTAMP(6) and DATE
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2000-01-01 00:00:00.000000');
INSERT INTO t1 VALUES ('2000-01-01 00:00:00.000001');
INSERT INTO t1 VALUES ('2000-01-02 00:00:00.000000');
CREATE TABLE t2 (a DATE);
INSERT INTO t2 VALUES ('2000-01-01');
INSERT INTO t2 VALUES ('2000-01-02');
SELECT * FROM t1, t2 WHERE t1.a=t2.a ORDER BY t1.a, t2.a;
ALTER TABLE t2 MODIFY a TIMESTAMP(6);
SELECT * FROM t1, t2 WHERE t1.a=CAST(t2.a AS DATE) ORDER BY t1.a, t2.a;
DROP TABLE t2;
DROP TABLE t1;


--echo #
--echo # Testing JOIN with comparison between TIMESTAMP(6) and BIGINT
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2000-01-01 00:00:00');
INSERT INTO t1 VALUES ('2000-01-01 11:22:33');
INSERT INTO t1 VALUES ('2000-01-01 11:22:33.123');
CREATE TABLE t2 (b BIGINT);
INSERT INTO t2 VALUES (20000101);
INSERT INTO t2 VALUES (20000101112233);
SELECT * FROM t1, t2 WHERE a=b;
DROP TABLE t1, t2;

--echo #
--echo # Testing JOIN with comparison between TIMESTAMP(6) and DECIMAL(20,6)
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 11:22:33');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123456');
CREATE TABLE t2 (b DECIMAL(20,6));
INSERT INTO t2 VALUES (20010101112233);
INSERT INTO t2 VALUES (20010101112233.123);
INSERT INTO t2 VALUES (20010101112233.123456);
SELECT * FROM t1, t2 WHERE a=b;
DROP TABLE t1, t2;

--echo #
--echo # Testing JOIN with comparison between TIMESTAMP(6) and DECIMAL(20,3)
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 11:22:33');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123456');
CREATE TABLE t2 (b DECIMAL(20,3));
INSERT INTO t2 VALUES (20010101112233);
INSERT INTO t2 VALUES (20010101112233.123);
SELECT * FROM t1, t2 WHERE a=b;
DROP TABLE t1, t2;

--echo #
--echo # Testing JOIN with comparison between TIMESTAMP(6) and DOUBLE
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2000-01-01 11:22:33');
INSERT INTO t1 VALUES ('2000-01-01 11:22:33.1');
INSERT INTO t1 VALUES ('2000-01-01 11:22:33.12');
CREATE TABLE t2 (b DOUBLE);
INSERT INTO t2 VALUES (20000101112233);
INSERT INTO t2 VALUES (20000101112233.1);
INSERT INTO t2 VALUES (20000101112233.12);
SELECT * FROM t2;
SELECT * FROM t1, t2 WHERE a=b;
DROP TABLE t1, t2;

--echo #
--echo # Testing JOIN with comparison between TIMESTAMP(6) and VARCHAR
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 11:22:33');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123');
CREATE TABLE t2 (b VARCHAR(64));
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.123');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.1230');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.12300');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.123000');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.0');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.00');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.000');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.0000');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.00000');
INSERT INTO t2 VALUES ('2001-01-01 11:22:33.000000');
SELECT * FROM t1, t2 WHERE a=b ORDER BY BINARY a, BINARY b;
ALTER TABLE t1 ADD KEY (a);
SELECT * FROM t1, t2 WHERE a=b ORDER BY BINARY a, BINARY b;
ALTER TABLE t2 ADD KEY (b);
SELECT * FROM t1, t2 WHERE a=b ORDER BY BINARY a, BINARY b;
ALTER TABLE t1 DROP KEY a;
SELECT * FROM t1, t2 WHERE a=b ORDER BY BINARY a, BINARY b;
DROP TABLE t1, t2;

--echo #
--echo # Testing arithmetic with INT, DECIMAL, FLOAT
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000000');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000001');
SELECT a, a + 0, a + 1, a + 1.0, a + 1e0 FROM t1;
CREATE TABLE t2 AS SELECT a + 1 AS i, a + 1.0 AS d, a + 1e0 AS f FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t1, t2;

--echo #
--echo # Testing that TIMESTAMP behaves as INT/DECIMAL for numeric arithmetic
--echo #
CREATE TABLE t1 (t0 TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, t3 TIMESTAMP(3) NOT NULL DEFAULT '0000-00-00 00:00:00', t6 TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00');
CREATE TABLE t2 AS SELECT
  t0 + 1, t3 + 1, t6 + 1,
  t0 - 1, t3 - 1, t6 - 1,
  t0 * 1, t3 * 1, t6 * 1,
  t0 / 1, t3 / 1, t6 / 1,
  TIMESTAMP'2001-01-01 10:10:10' + 1,
  TIMESTAMP'2001-01-01 10:10:10.123' + 1,
  TIMESTAMP'2001-01-01 10:10:10.123456' + 1,
  TIMESTAMP'2001-01-01 10:10:10' - 1,
  TIMESTAMP'2001-01-01 10:10:10.123' - 1,
  TIMESTAMP'2001-01-01 10:10:10.123456' - 1,
  TIMESTAMP'2001-01-01 10:10:10' * 1,
  TIMESTAMP'2001-01-01 10:10:10.123' * 1,
  TIMESTAMP'2001-01-01 10:10:10.123456' * 1,
  TIMESTAMP'2001-01-01 10:10:10' / 1,
  TIMESTAMP'2001-01-01 10:10:10.123' / 1,
  TIMESTAMP'2001-01-01 10:10:10.123456' / 1
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing that TIMESTAMP behaves as DECIMAL for SUM() and AVG()
--echo #
SET @t='2010-01-01 00:11:12.123456';
CREATE TABLE t1 (t0 DATETIME, t3 DATETIME(3), t6 DATETIME(6));
INSERT INTO t1 VALUES (@t, @t, @t);
--query_vertical SELECT MIN(t0), MAX(t0), AVG(t0), SUM(t0) FROM t1
--query_vertical SELECT MIN(t3), MAX(t3), AVG(t3), SUM(t3) FROM t1
--query_vertical SELECT MIN(t6), MAX(t6), AVG(t6), SUM(t6) FROM t1
SET @t='2010-01-01 00:11:12.000000';
INSERT INTO t1 VALUES (@t, @t, @t);
--query_vertical SELECT MIN(t0), MAX(t0), AVG(t0), SUM(t0) FROM t1
--query_vertical SELECT MIN(t3), MAX(t3), AVG(t3), SUM(t3) FROM t1
--query_vertical SELECT MIN(t6), MAX(t6), AVG(t6), SUM(t6) FROM t1
CREATE TABLE t2 AS SELECT
  MIN(t0), MAX(t0), AVG(t0), SUM(t0),
  MIN(t3), MAX(t3), AVG(t3), SUM(t3),
  MIN(t6), MAX(t6), AVG(t6), SUM(t6)
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;


--echo #
--echo # Testing functions ADDTIME, SUBTIME, TIMESTAMP, TIMEDIFF, DATE_ADD/SUB
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('1970-01-01 01:00:01.000000');
INSERT INTO t1 VALUES ('1970-01-01 01:00:01.000001');
INSERT INTO t1 VALUES ('1971-01-01 01:00:00.000000');
INSERT INTO t1 VALUES ('1971-01-01 01:00:00.000001');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000000');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000001');
INSERT INTO t1 VALUES ('2001-01-01 01:00:00.000000');
INSERT INTO t1 VALUES ('2001-01-01 01:00:00.000001');
SELECT ADDTIME(a, '00:00:00.000001') FROM t1 ORDER BY a;
SELECT ADDTIME(a, '10:00:00.000001') FROM t1 ORDER BY a;
SELECT ADDTIME(CAST(a AS DATETIME(0)), 100000) FROM t1 ORDER BY a;
SELECT ADDTIME(CAST(a AS DATETIME(0)), 100000.1) FROM t1 ORDER BY a;
SELECT a, ADDTIME(CAST(a AS DATETIME(0)), CAST('10:00:00' AS TIME(0))) FROM t1 ORDER BY a;
SELECT a, ADDTIME(CAST(a AS DATETIME(6)), CAST('10:00:00' AS TIME(0))) FROM t1 ORDER BY a;
SELECT a, ADDTIME(CAST(a AS DATETIME(0)), CAST('10:00:00' AS TIME(6))) FROM t1 ORDER BY a;
SELECT ADDTIME(a, a) FROM t1 ORDER BY a;
CREATE TABLE t2 AS SELECT
  ADDTIME(a, '00:00:00.000001'),
  ADDTIME(a, '10:00:00.000001'),
  ADDTIME(CAST(a AS DATETIME(0)), 100000),
  ADDTIME(CAST(a AS DATETIME(0)), 100000.1),
  ADDTIME(a,a)
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
SELECT TIMESTAMP(a, '10:00:00') FROM t1;
SELECT TIMESTAMP(CAST(a AS DATETIME(0)), CAST('00:00:00' AS TIME(0))) FROM t1;
SELECT TIMESTAMP(CAST(a AS DATETIME(6)), CAST('00:00:00' AS TIME(0))) FROM t1;
SELECT TIMESTAMP(CAST(a AS DATETIME(0)), CAST('00:00:00' AS TIME(6))) FROM t1;
CREATE TABLE t2 AS SELECT
  TIMESTAMP(a, '10:00:00'),
  TIMESTAMP(CAST(a AS DATETIME(0)), CAST('00:00:00' AS TIME(0))),
  TIMESTAMP(CAST(a AS DATETIME(6)), CAST('00:00:00' AS TIME(0))),
  TIMESTAMP(CAST(a AS DATETIME(0)), CAST('00:00:00' AS TIME(6)))
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
SELECT SUBTIME(a, '00:00:00.000001') FROM t1 ORDER BY a;
SELECT SUBTIME(a, '10:00:00.000001') FROM t1 ORDER BY a;
SELECT SUBTIME(a, a) FROM t1 ORDER BY a;
CREATE TABLE t2 AS SELECT
  SUBTIME(a, '00:00:00.000001'),
  SUBTIME(a, '10:00:00.000001'),
  SUBTIME(CAST(a AS DATETIME(0)), 100000),
  SUBTIME(CAST(a AS DATETIME(0)), 100000.1),
  SUBTIME(a,a)
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
SELECT DATE_ADD(a, INTERVAL 1 MINUTE) FROM t1 ORDER BY a;
SELECT DATE_SUB(a, INTERVAL 1 MINUTE) FROM t1 ORDER BY a;
SELECT DATE_ADD(CAST(a AS DATETIME), INTERVAL 1 MINUTE) FROM t1 ORDER BY a;
SELECT DATE_SUB(CAST(a AS DATETIME), INTERVAL 1 MINUTE) FROM t1 ORDER BY a;
SELECT DATE_ADD(CAST(a AS DATETIME), INTERVAL 1 MICROSECOND) FROM t1 ORDER BY a;
SELECT DATE_SUB(CAST(a AS DATETIME), INTERVAL 1 MICROSECOND) FROM t1 ORDER BY a;
SELECT a, DATE_ADD(a, INTERVAL -0.1 SECOND) FROM t1 ORDER BY a;
SELECT a, DATE_SUB(a, INTERVAL -0.1 SECOND) FROM t1 ORDER BY a;
CREATE TABLE t2 AS SELECT
  DATE_ADD(a, INTERVAL 1 SECOND),
  DATE_SUB(a, INTERVAL 1 SECOND),
  DATE_ADD(CAST(a AS DATETIME), INTERVAL 1 SECOND),
  DATE_SUB(CAST(a AS DATETIME), INTERVAL 1 SECOND),
  DATE_ADD(CAST(a AS DATETIME), INTERVAL 1 MICROSECOND),
  DATE_SUB(CAST(a AS DATETIME), INTERVAL 1 MICROSECOND)
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT MAX(DATE_ADD(a, INTERVAL 1 MINUTE)) FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing explicit CAST of datetime hybrid functions to DATETIME
--echo #
SET @@timestamp=UNIX_TIMESTAMP('2001-01-01 00:00:00');
SELECT CAST(ADDTIME('20:10:10', 10) AS DATETIME); 
SELECT CAST(CAST('10:10:20' AS TIME) + INTERVAL 10 HOUR AS DATETIME);
SELECT CAST(CAST('10:10:20' AS TIME(6)) + INTERVAL 10 HOUR AS DATETIME);
SELECT CAST(STR_TO_DATE('23:59:01','%H:%i:%s') AS DATETIME);
SELECT CAST(STR_TO_DATE('23:59:01.123','%H:%i:%s.%f') AS DATETIME(6));
SELECT CAST(ADDTIME('40:10:10', 10) as DATETIME); 
SELECT CAST(CAST('10:10:20' AS TIME) + INTERVAL 30 HOUR AS DATETIME);
SELECT CAST(CAST('10:10:20' AS TIME(6)) + INTERVAL 30 HOUR AS DATETIME);
SET @@timestamp=default;

--echo #
--echo # Testing copy from TIMESTAMP to TIMESTAMP(6) and vice versa
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), b TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00.000000');
INSERT INTO t1 VALUES ('2001-01-01 01:02:03.123456', '0000-00-00 00:00:00');
UPDATE t1 SET b=a;
SELECT b FROM t1;
UPDATE t1 SET b='2002-02-02 10:11:12';
UPDATE t1 SET a=b;
SELECT a FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER from TIMESTAMP(6) to TIMESTAMP and vice versa
--echo #
CREATE TABLE t1 (a TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP);
INSERT INTO t1 VALUES ('2000-01-01 01:02:03');
ALTER TABLE t1 MODIFY a TIMESTAMP(6);
SELECT * FROM t1;
UPDATE t1 SET a='2000-01-01 01:03:03.456';
SELECT * FROM t1;
ALTER TABLE t1 MODIFY a TIMESTAMP;
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER between TIMESTAMP and BIGINT
--echo #
CREATE TABLE t1 (a TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, b TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00.000000');
INSERT INTO t1 VALUES ('2000-01-01 11:22:33', '2000-01-01 11:22:33');
ALTER TABLE t1 MODIFY a BIGINT, MODIFY b BIGINT;
SELECT * FROM t1;
UPDATE t1 SET a=20000101112233, b=20000101112233;
ALTER TABLE t1 MODIFY a TIMESTAMP, MODIFY b TIMESTAMP(6);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER between TIMESTAMP(6) and DOUBLE
--echo #
CREATE TABLE t1 (a TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, b TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00.000000');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33', '2001-01-01 11:22:33.1234');
ALTER TABLE t1 MODIFY a DOUBLE, MODIFY b DOUBLE;
SELECT * FROM t1;
UPDATE t1 SET a=20010101101112, b=20010101101112.1;
ALTER TABLE t1 MODIFY a TIMESTAMP, MODIFY b TIMESTAMP(6);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER between TIMESTAMP(6) and DECIMAL
--echo #
CREATE TABLE t1 (a TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, b TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33', '2001-01-01 11:22:33.1234');
ALTER TABLE t1 MODIFY a DECIMAL(30,6), MODIFY b DECIMAL(30,6);
SELECT * FROM t1;
UPDATE t1 SET a=20010101112233, b=20010101112233.1234;
ALTER TABLE t1 MODIFY a TIMESTAMP, MODIFY b TIMESTAMP(6);
SELECT * FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing ALTER from TIMESTAMP to various other temporal types
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), b TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00', c TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00');
INSERT INTO t1 VALUES
  ('2001-01-01 10:10:10.1', '2001-01-01 10:10:10.1', '2001-01-01 10:10:10.1');
ALTER TABLE t1 MODIFY a TIME(6), MODIFY b DATETIME(6), MODIFY c DATE;
SELECT * FROM t1;
DROP TABLE t1;


--echo #
--echo # Testing INT value and CAST of TIMESTAMP(6) to various other types
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123');
SELECT CAST(a AS DATETIME) FROM t1;
SELECT CAST(a AS DATETIME(6)) FROM t1;
SELECT CAST(a AS TIME) FROM t1;
SELECT CAST(a AS TIME(6)) FROM t1;
SELECT a << 0 FROM t1;
SELECT CAST(a AS SIGNED) FROM t1;
SELECT CAST(a AS UNSIGNED) FROM t1;
SELECT CAST(a AS DECIMAL(30,6)) FROM t1;
SELECT CAST(a AS DECIMAL(30,3)) FROM t1;
SELECT CAST(a AS CHAR) FROM t1;
SELECT CAST(a AS CHAR(6)) FROM t1;
CREATE TABLE t2 AS SELECT CAST(a AS CHAR) AS a FROM t1;
SHOW CREATE TABLE t2;
SELECT a, LENGTH(a) FROM t2;
DROP TABLE t2;
DROP TABLE t1;


--echo #
--echo # Testing that default precision in "DEFAULT CURRENT_TIMESTAMP(N)"
--echo # and "ON UPDATE CURRENT_TIMESTAMP(N)" is the same to the precision
--echo # of the field itself.
--echo #
CREATE TABLE t1 (a TIMESTAMP(1) NOT NULL DEFAULT CURRENT_TIMESTAMP(1) ON UPDATE CURRENT_TIMESTAMP(1));
SHOW CREATE TABLE t1;
DROP TABLE t1;
CREATE TABLE t1 (a TIMESTAMP(2) NOT NULL DEFAULT CURRENT_TIMESTAMP(2) ON UPDATE CURRENT_TIMESTAMP(2));
SHOW CREATE TABLE t1;
DROP TABLE t1;
CREATE TABLE t1 (a TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3));
SHOW CREATE TABLE t1;
DROP TABLE t1;
CREATE TABLE t1 (a TIMESTAMP(4) NOT NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4));
SHOW CREATE TABLE t1;
DROP TABLE t1;
CREATE TABLE t1 (a TIMESTAMP(5) NOT NULL DEFAULT CURRENT_TIMESTAMP(5) ON UPDATE CURRENT_TIMESTAMP(5));
SHOW CREATE TABLE t1;
DROP TABLE t1;
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
SHOW CREATE TABLE t1;
DROP TABLE t1;


--echo #
--echo # Testing DEFAULT value
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT '2000-01-01 11:22:33.123456');
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Testing DEFAULT CURRENT_TIMESTAMP
--echo #
CREATE TABLE t1 (a TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP(0));
DROP TABLE t1;
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP DEFAULT CURRENT_TIMESTAMP(1));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP DEFAULT CURRENT_TIMESTAMP(2));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP DEFAULT CURRENT_TIMESTAMP(3));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP DEFAULT CURRENT_TIMESTAMP(4));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP DEFAULT CURRENT_TIMESTAMP(5));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP DEFAULT CURRENT_TIMESTAMP(6));

--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP);
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(0));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(1));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(2));
CREATE TABLE t1 (a TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3));
SHOW COLUMNS FROM t1;
DROP TABLE t1;
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(4));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(5));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(6));

--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP);
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(1));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(2));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(4));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(5));
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6));
SHOW COLUMNS FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (a DATETIME DEFAULT CURRENT_TIMESTAMP(0));
DROP TABLE t1;
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME DEFAULT CURRENT_TIMESTAMP(1));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME DEFAULT CURRENT_TIMESTAMP(2));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME DEFAULT CURRENT_TIMESTAMP(3));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME DEFAULT CURRENT_TIMESTAMP(4));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME DEFAULT CURRENT_TIMESTAMP(5));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME DEFAULT CURRENT_TIMESTAMP(6));

--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME(3) DEFAULT CURRENT_TIMESTAMP);
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME(3) DEFAULT CURRENT_TIMESTAMP(0));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME(3) DEFAULT CURRENT_TIMESTAMP(1));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME(3) DEFAULT CURRENT_TIMESTAMP(2));
CREATE TABLE t1 (a DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3));
SHOW COLUMNS FROM t1;
DROP TABLE t1;
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME(3) DEFAULT CURRENT_TIMESTAMP(4));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME(3) DEFAULT CURRENT_TIMESTAMP(5));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME(3) DEFAULT CURRENT_TIMESTAMP(6));

--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME(6) DEFAULT CURRENT_TIMESTAMP);
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME(6) DEFAULT CURRENT_TIMESTAMP(0));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME(6) DEFAULT CURRENT_TIMESTAMP(1));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME(6) DEFAULT CURRENT_TIMESTAMP(2));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME(6) DEFAULT CURRENT_TIMESTAMP(4));
--error ER_INVALID_DEFAULT
CREATE TABLE t1 (a DATETIME(6) DEFAULT CURRENT_TIMESTAMP(5));
CREATE TABLE t1 (a DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6));
SHOW COLUMNS FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing that DEFAULT CURRENT_TIMESTAMP(n) truncates rather than rounds
--echo #
SET timestamp=UNIX_TIMESTAMP('2001-01-01 00:00:00.999999');
CREATE TABLE t1 (
  a TIMESTAMP(2) NOT NULL DEFAULT CURRENT_TIMESTAMP(2) ON UPDATE CURRENT_TIMESTAMP(2),
  b DATETIME(2) DEFAULT CURRENT_TIMESTAMP(2)
);
INSERT INTO t1 VALUES (DEFAULT, DEFAULT);
INSERT INTO t1 VALUES ();
SELECT CURRENT_TIMESTAMP(6);
SELECT * FROM t1;
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # Testing ON UPDATE CURRENT_TIMESTAMP
--echo #
--error ER_INVALID_ON_UPDATE
CREATE TABLE t1 (a TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP);
--error ER_INVALID_ON_UPDATE
CREATE TABLE t1 (a TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(0));
--error ER_INVALID_ON_UPDATE
CREATE TABLE t1 (a TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(1));
--error ER_INVALID_ON_UPDATE
CREATE TABLE t1 (a TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(2));
--error ER_INVALID_ON_UPDATE
CREATE TABLE t1 (a TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(4));
--error ER_INVALID_ON_UPDATE
CREATE TABLE t1 (a TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(5));
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00.000000' ON UPDATE CURRENT_TIMESTAMP(6));
SHOW COLUMNS FROM t1;
SHOW CREATE TABLE t1;
DROP TABLE t1;

--error ER_INVALID_ON_UPDATE
CREATE TABLE t1 (a DATETIME(6) ON UPDATE CURRENT_TIMESTAMP);
--error ER_INVALID_ON_UPDATE
CREATE TABLE t1 (a DATETIME(6) ON UPDATE CURRENT_TIMESTAMP(0));
--error ER_INVALID_ON_UPDATE
CREATE TABLE t1 (a DATETIME(6) ON UPDATE CURRENT_TIMESTAMP(1));
--error ER_INVALID_ON_UPDATE
CREATE TABLE t1 (a DATETIME(6) ON UPDATE CURRENT_TIMESTAMP(2));
--error ER_INVALID_ON_UPDATE
CREATE TABLE t1 (a DATETIME(6) ON UPDATE CURRENT_TIMESTAMP(4));
--error ER_INVALID_ON_UPDATE
CREATE TABLE t1 (a DATETIME(6) ON UPDATE CURRENT_TIMESTAMP(5));
CREATE TABLE t1 (a DATETIME(6) ON UPDATE CURRENT_TIMESTAMP(6));
SHOW COLUMNS FROM t1;
SHOW CREATE TABLE t1;
DROP TABLE t1;

--echo #
--echo # Testing that ON UPDATE CURRENT_TIMESTAMP(n) truncates rather than rounds
--echo #
SET timestamp=UNIX_TIMESTAMP('2001-01-01 00:00:00.999999');
CREATE TABLE t1 (
  a TIMESTAMP(2) ON UPDATE CURRENT_TIMESTAMP(2),
  b DATETIME(2) ON UPDATE CURRENT_TIMESTAMP(2),
  c INT);
INSERT INTO t1 VALUES ('1999-01-01 00:00:00', '1999-01-01 00:00:00', 1);
SELECT * FROM t1;
UPDATE t1 SET c=2;
SELECT CURRENT_TIMESTAMP(6);
SELECT * FROM t1;
DROP TABLE t1;
SET timestamp=DEFAULT;



--echo #
--echo # Mixing varios TIMESTAMP(N) in CASE
--echo #
CREATE TABLE t1 (
  t0 TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  t1 TIMESTAMP(1) NOT NULL DEFAULT '0000-00-00 00:00:00.0',
  t3 TIMESTAMP(3) NOT NULL DEFAULT '0000-00-00 00:00:00.0',
  t4 TIMESTAMP(4) NOT NULL DEFAULT '0000-00-00 00:00:00.0',
  t6 TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00.0');
CREATE TABLE t2 AS SELECT
  CASE WHEN 1 THEN t0 ELSE t1 END,
  CASE WHEN 1 THEN t0 ELSE t3 END,
  CASE WHEN 1 THEN t0 ELSE t4 END,
  CASE WHEN 1 THEN t0 ELSE t6 END,
  CASE WHEN 1 THEN t1 ELSE t0 END,
  CASE WHEN 1 THEN t1 ELSE t3 END,
  CASE WHEN 1 THEN t1 ELSE t4 END,
  CASE WHEN 1 THEN t1 ELSE t6 END,
  CASE WHEN 1 THEN t3 ELSE t0 END,
  CASE WHEN 1 THEN t3 ELSE t1 END,
  CASE WHEN 1 THEN t3 ELSE t4 END,
  CASE WHEN 1 THEN t3 ELSE t6 END,
  CASE WHEN 1 THEN t4 ELSE t0 END,
  CASE WHEN 1 THEN t4 ELSE t1 END,
  CASE WHEN 1 THEN t4 ELSE t3 END,
  CASE WHEN 1 THEN t4 ELSE t6 END,
  CASE WHEN 1 THEN t6 ELSE t0 END,
  CASE WHEN 1 THEN t6 ELSE t1 END,
  CASE WHEN 1 THEN t6 ELSE t3 END,
  CASE WHEN 1 THEN t6 ELSE t4 END
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;


--echo #
--echo # Mixing various TIMESTAMP(N) in COALESCE
--echo #
CREATE TABLE t1 (
  a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  b TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00',
  c TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00.0',
  d TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00.0',
  e TIMESTAMP(3) NOT NULL DEFAULT '0000-00-00 00:00:00.0',
  f TIMESTAMP(4) NOT NULL DEFAULT '0000-00-00 00:00:00.0');
CREATE TABLE t2 AS SELECT
  COALESCE(a, b), COALESCE(c, b),
  COALESCE(c, d), COALESCE(e, f),
  COALESCE(c, e), COALESCE(c, f)
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT
  CONCAT(COALESCE(a, b)), CONCAT(COALESCE(c, b)),
  CONCAT(COALESCE(c, d)), CONCAT(COALESCE(e, f)),
  CONCAT(COALESCE(c, e)), CONCAT(COALESCE(c, f))
FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;


--echo #
--echo # Mixing TIMESTAMP(6) with another TIMESTAMP(6) in IF
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), b TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00');
CREATE TABLE t2 AS SELECT IF(1, a, b) AS a FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Mixing TIMESTAMP(N) and TIMESTAMP(M) in IF
--echo #
CREATE TABLE t1 (a TIMESTAMP(1) NOT NULL DEFAULT CURRENT_TIMESTAMP(1) ON UPDATE CURRENT_TIMESTAMP(1), b TIMESTAMP(2) NOT NULL DEFAULT '0000-00-00 00:00:00');
CREATE TABLE t2 AS SELECT IF(1, a, b), IF(1, b, a) FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;


--echo #
--echo # Mixing TIMESTAMP(6) NOT NULL with another TIMESTAMP(6) NOT NULL
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), b TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00');
CREATE TABLE t2 AS SELECT IF(1, a, b) AS a FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # UNION between TIMESTAMP(6) and TIMESTAMP(6)
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), b TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123456', '2001-01-01 00:11:22.123456');
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT b FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2 ORDER BY a;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # UNION between TIMESTAMP(n)
--echo #
CREATE TABLE t1 (t0 TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, t1 TIMESTAMP(1) NOT NULL DEFAULT '0000-00-00 00:00:00.0', t3 TIMESTAMP(3) NOT NULL DEFAULT '0000-00-00 00:00:00.0', t4 TIMESTAMP(4) NOT NULL DEFAULT '0000-00-00 00:00:00.0');
CREATE TABLE t2 AS SELECT t0 FROM t1 UNION SELECT t1 FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT t1 FROM t1 UNION SELECT t3 FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT t3 FROM t1 UNION SELECT t4 FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # UNION between TIMESTAMP(6) and TIMESTAMP / DATE / DATETIME
--echo #
CREATE TABLE t1 (a TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, b TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00', c DATE NOT NULL, d DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123456', '2001-01-01 00:11:22.123456','2001-01-01','2001-01-01 20:20:20.123456');
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT b FROM t1;
SHOW COLUMNS FROM t2;
SELECT * FROM t2 ORDER BY a;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT b FROM t1 UNION SELECT a FROM t1;
SHOW COLUMNS FROM t2;
SELECT * FROM t2 ORDER BY b;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT c FROM t1 UNION SELECT b FROM t1;
SHOW COLUMNS FROM t2;
SELECT * FROM t2 ORDER BY c;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT d FROM t1;
SHOW COLUMNS FROM t2;
SELECT * FROM t2 ORDER BY a;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT b FROM t1 UNION SELECT c FROM t1;
SHOW COLUMNS FROM t2;
SELECT * FROM t2 ORDER BY b;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT c FROM t1 UNION SELECT b FROM t1;
SHOW COLUMNS FROM t2;
SELECT * FROM t2 ORDER BY c;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # UNION between TIMESTAMP(6) and various other types
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), b BIGINT, c DOUBLE, d DECIMAL(20,6), e VARCHAR(20));
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT b FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT c FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT d FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT a FROM t1 UNION SELECT e FROM t1;
SHOW CREATE TABLE t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing TIMESTAMP(6) field in combination with DATETIME type functions
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), b TIME(6));
INSERT INTO t1 VALUES ('2001-01-01 10:00:00.123456', '10:00:00.123456');
INSERT INTO t1 VALUES ('2001-01-01 10:00:00', '10:00:00');
SELECT * FROM t1 WHERE a=TIMESTAMP('2001-01-01', b);
SELECT * FROM t1 WHERE a=ADDTIME('2001-01-01 00:00:00', '10:00:00');
SELECT * FROM t1 WHERE a=ADDTIME('2001-01-01 00:00:00', '10:00:00.123456');
DELETE FROM t1;
INSERT INTO t1 VALUES (CURRENT_TIMESTAMP, CURRENT_TIME);
SELECT COUNT(*) FROM t1 WHERE a <= CURRENT_TIMESTAMP;
SELECT COUNT(*) FROM t1 WHERE a >= SUBTIME(CURRENT_TIMESTAMP, '10:00:00');
DROP TABLE t1;

--echo #
--echo # Testing partitions
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)) PARTITION BY KEY(a) PARTITIONS 4;
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000000');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000001');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000002');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000003');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000004');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000005');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000006');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000010');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000011');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000012');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000013');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000014');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000015');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000016');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000110');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000111');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000112');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000113');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000114');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000115');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000116');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000210');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000211');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000212');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000213');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000214');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000215');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.000216');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000000');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000001');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000002');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000003');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000004');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000005');
INSERT INTO t1 VALUES ('2001-01-01 00:00:01.000006');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000000');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000001');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000002');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000003');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000004');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000005');
INSERT INTO t1 VALUES ('2001-01-01 00:00:02.000006');
SELECT * FROM t1 PARTITION(p0);
SELECT * FROM t1 PARTITION(p1);
SELECT * FROM t1 PARTITION(p2);
SELECT * FROM t1 PARTITION(p3);
--error ER_FIELD_TYPE_NOT_ALLOWED_AS_PARTITION_FIELD
ALTER TABLE t1 PARTITION BY RANGE COLUMNS (a)
(
  PARTITION p0 VALUES LESS THAN ('2001-01-01 00:00:00.000200'),
  PARTITION p1 VALUES LESS THAN ('2001-01-01 00:00:01'),
  PARTITION p2 VALUES LESS THAN ('2001-01-01 00:00:02.000003'),
  PARTITION p3 VALUES LESS THAN MAXVALUE
);
SELECT * FROM t1 PARTITION(p0);
SELECT * FROM t1 PARTITION(p1);
SELECT * FROM t1 PARTITION(p2);
SELECT * FROM t1 PARTITION(p3);
DROP TABLE t1;

--echo #
--echo # CAST from TIMESTAMP to TATETIME
--echo #
CREATE TABLE t1 (a TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, b TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00');
INSERT INTO t1 VALUES ('2001-01-01 11:22:33', '2001-01-01 11:22:33.123456');
CREATE TABLE t2 AS SELECT CAST(a AS DATETIME), CAST(b AS DATETIME) FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT CAST(a AS DATETIME(0)), CAST(b AS DATETIME(0)) FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2;
DROP TABLE t2;
CREATE TABLE t2 AS SELECT CAST(a AS DATETIME(6)), CAST(b AS DATETIME(6)) FROM t1;
SHOW CREATE TABLE t2;
SELECT * FROM t2;
DROP TABLE t2;
DROP TABLE t1;

--echo #
--echo # Testing comparison between a TIMESTAMP(6) field and a DATETIME(N) cast
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 10:11:12.000000');
INSERT INTO t1 VALUES ('2001-01-01 10:11:12.000001');
INSERT INTO t1 VALUES ('2001-01-01 10:11:12.000002');
INSERT INTO t1 VALUES ('2001-01-01 10:11:12.000003');
SELECT * FROM t1 WHERE a=CAST('2001-01-01 10:11:12.000003' AS DATETIME(6));
SELECT * FROM t1 WHERE a=CAST('2001-01-01 10:11:12.000003' AS DATETIME(0));
DROP TABLE t1;

--echo #
--echo # Testing TIMESTAMP subquery in combination with CAST to DATETIME(N)
--echo #
CREATE TABLE t1 (a TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, b TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00');
INSERT INTO t1 VALUES ('2001-01-01 23:00:00', '2001-01-01 23:00:00.123456');
SELECT CAST('2001-01-01 23:00:00' AS DATETIME) = (SELECT a FROM t1);
SELECT CAST('2001-01-01 23:00:00' AS DATETIME(6)) = (SELECT a FROM t1);
SELECT CAST('2001-01-01 23:00:00.123456' AS DATETIME(0)) = (SELECT a FROM t1);
SELECT CAST('2001-01-01 23:00:00.123456' AS DATETIME(6)) = (SELECT b FROM t1);
DROP TABLE t1;

--echo #
--echo # Testing TIMESTAMP column with function MICROSECOND
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 11:12:13.000001');
INSERT INTO t1 VALUES ('2001-01-01 11:12:13.100001');
INSERT INTO t1 VALUES ('2001-01-01 11:12:13.999999');
SELECT a, MICROSECOND(a) FROM t1;
SELECT a, EXTRACT(MICROSECOND FROM a) FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing PS
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
PREPARE stmt FROM 'INSERT INTO t1 VALUES (?)';
SET @a='2001-01-01 11:22:33.123456';
SET @b=20010101112233.123456;
SET @c=0.123456e0;
EXECUTE stmt USING @a;
EXECUTE stmt USING @c;
DEALLOCATE PREPARE stmt;
SELECT * FROM t1;
PREPARE stmt FROM 'SELECT * FROM t1 WHERE a=?';
EXECUTE stmt USING @a;
EXECUTE stmt USING @b;
EXECUTE stmt USING @c;
DEALLOCATE PREPARE stmt;
DROP TABLE t1;

--echo #
--echo # Testing TIMESTAMP(6) and user variables
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 11:22:33.123456');
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.100000');
SELECT * FROM t1;
SET @a='2001-01-01 11:22:33.123456';
SELECT * FROM t1 WHERE a=@a;
SET @a=20010101112233.123456;
SELECT * FROM t1 WHERE a=@a;
SET @a=20010101000000.1e0;
SELECT @a;
SELECT * FROM t1 WHERE a=@a;
SET @a=NULL;
SELECT a INTO @a FROM t1 LIMIT 1;
SELECT @a;
DROP TABLE t1;
# MEMORY does not support BLOB
if (`SELECT @@default_storage_engine != 'MEMORY'`)
{
  CREATE TABLE t1 AS SELECT @a AS a;
  SHOW CREATE TABLE t1;
  SELECT * FROM t1;
  DROP TABLE t1;
}

--echo #
--echo # Testing SP
--echo #
DELIMITER //;
CREATE PROCEDURE p1 ()
BEGIN
  DECLARE a TIMESTAMP(6);
  SET a='2001-01-01 11:22:33.123';
  SELECT a;
END//
SHOW CREATE PROCEDURE p1//
CALL p1//
DROP PROCEDURE p1//
CREATE PROCEDURE p1 (a TIMESTAMP(6))
BEGIN
  SELECT a;
END//
SHOW CREATE PROCEDURE p1//
CALL p1(20010101112233)//
CALL p1(20010101112233.123)//
CALL p1(0.123e0)//
CALL p1('2001-01-01 11:22:33.123')//
DROP PROCEDURE p1//
CREATE PROCEDURE p1()
BEGIN
  DECLARE a TIMESTAMP(6);
  CREATE TABLE t1 AS SELECT a;
  SHOW CREATE TABLE t1;
  DROP TABLE t1;
END//
SHOW CREATE PROCEDURE p1//
CALL p1//
DROP PROCEDURE p1//
CREATE FUNCTION p1 (a TIMESTAMP(6)) RETURNS TIMESTAMP(6)
BEGIN
  RETURN a;
END//
SHOW CREATE FUNCTION p1//
SELECT p1(20010101112233)//
SELECT p1(20010101112233.123)//
DROP FUNCTION p1//
DELIMITER ;//

--echo #
--echo # Testing INFORMATION_SCHEMA.COLUMNS
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
--replace_column 19 #
--query_vertical SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='t1';
DROP TABLE t1;


--echo #
--echo # SELECT from a subquery
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.123456'), ('2001-01-01 11:11:11.123456');
SELECT * FROM (SELECT * FROM t1) t12;
DROP TABLE t1;

--echo #
--echo # Testing IN and = subqueries
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.123456'), ('2001-01-01 11:11:11.123456');
SELECT * FROM t1 WHERE a IN (SELECT MIN(a) FROM t1);
SELECT * FROM t1 WHERE a = (SELECT a FROM t1 ORDER BY a DESC LIMIT 1);
DROP TABLE t1;

--echo #
--echo # Testing IN subquery + GROUP
--echo #
CREATE TABLE t1 (id INT, a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES (1, '2001-01-01 00:00:00.123456'), (1, '2001-01-01 11:00:00.123456');
INSERT INTO t1 VALUES (2, '2001-01-01 00:01:00.123456'), (2, '2001-01-01 11:01:00.123456');
SELECT * FROM t1 WHERE a IN (SELECT MAX(a) FROM t1 GROUP BY id);
DROP TABLE t1;

--echo #
--echo # Testing VIEW
--echo #
CREATE TABLE t1 (a TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.123456'), ('2001-01-01 11:11:11.123456');
CREATE VIEW v1 AS SELECT a FROM t1;
SELECT * FROM v1 WHERE a='2001-01-01 00:00:00.123456';
SELECT MIN(a), MAX(a) FROM v1;
SELECT * FROM t1 WHERE a IN (SELECT MAX(a) FROM v1);
DROP VIEW v1;
CREATE VIEW v1 AS SELECT MAX(a) AS a FROM t1;
SELECT * FROM v1;
SELECT * FROM t1 WHERE a=(SELECT a FROM v1);
DROP VIEW v1;
DROP TABLE t1;

--echo #
--echo # Testing default value
--echo #
CREATE TABLE t1 (a DATETIME(6), b TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 (a) VALUES (CURRENT_TIMESTAMP(6));
SELECT a=b FROM t1;
DROP TABLE t1;

SET time_zone=DEFAULT;

####################################################################
# Testing types together
CREATE TABLE t1 (a DATETIME(6), b TIME(6));
INSERT INTO t1 VALUES ('1212-12-12 21:21:21.555555', '00:00:00.21');
INSERT INTO t1 VALUES ('9999-12-31 23:59:59.999999', '00:00:00.000001');
SELECT a + b FROM t1;
DROP TABLE t1;

--echo #
--echo # Testing that IF always returns correct number of decimal digits
--echo #
SET timestamp=UNIX_TIMESTAMP('2011-11-10 18:04:30');
CREATE TABLE t1 (dt2 DATETIME(2), t3 TIME(3), d DATE);
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.12', '00:00:00.567', '2002-01-01');
SELECT if(0, dt2, t3), CONCAT(if(0, dt2, t3)) FROM t1;
SELECT if(1, dt2, t3), CONCAT(if(1, dt2, t3)) FROM t1;
SELECT if(0, t3, dt2), CONCAT(if(0, t3, dt2)) FROM t1;
SELECT if(1, t3, dt2), CONCAT(if(1, t3, dt2)) FROM t1;
SELECT if(0, t3, d), CONCAT(if(0, t3, d)) FROM t1;
SELECT if(1, t3, d), CONCAT(if(1, t3, d)) FROM t1;
SELECT if(0, d, t3), CONCAT(if(0, d, t3)) FROM t1;
SELECT if(1, d, t3), CONCAT(if(1, d, t3)) FROM t1;
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # Testing that CASE always returns correct number of decimal digits
--echo #
SET timestamp=UNIX_TIMESTAMP('2011-11-10 18:04:30');
CREATE TABLE t1 (dt2 DATETIME(2), t3 TIME(3), d DATE);
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.12', '00:00:00.567', '2002-01-01');
SELECT CASE WHEN 0 THEN dt2 ELSE t3 END FROM t1;
SELECT CONCAT(CASE WHEN 0 THEN dt2 ELSE t3 END) FROM t1;
SELECT CASE WHEN 1 THEN dt2 ELSE t3 END FROM t1;
SELECT CONCAT(CASE WHEN 1 THEN dt2 ELSE t3 END) FROM t1;
SELECT CASE WHEN 0 THEN t3 ELSE dt2 END FROM t1;
SELECT CONCAT(CASE WHEN 0 THEN t3 ELSE dt2 END) FROM t1;
SELECT CASE WHEN 1 THEN t3 ELSE dt2 END FROM t1;
SELECT CONCAT(CASE WHEN 1 THEN t3 ELSE dt2 END) FROM t1;
SELECT CASE WHEN 0 THEN d ELSE t3 END FROM t1;
SELECT CONCAT(CASE WHEN 0 THEN d ELSE t3 END) FROM t1;
SELECT CASE WHEN 1 THEN d ELSE t3 END FROM t1;
SELECT CONCAT(CASE WHEN 1 THEN d ELSE t3 END) FROM t1;
SELECT CASE WHEN 0 THEN t3 ELSE d END FROM t1;
SELECT CONCAT(CASE WHEN 0 THEN t3 ELSE d END) FROM t1;
SELECT CASE WHEN 1 THEN t3 ELSE d END FROM t1;
SELECT CONCAT(CASE WHEN 1 THEN t3 ELSE d END) FROM t1;
DROP TABLE t1;
SET timestamp=DEFAULT;


--echo #
--echo # Testing that COALESCE always returns correct number of decimal digits
--echo #
SET timestamp=UNIX_TIMESTAMP('2011-11-10 18:04:30');
CREATE TABLE t1 (dt2 DATETIME(2), t3 TIME(3), d DATE);
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.12', '00:00:00.567', '2002-01-01');
SELECT COALESCE(dt2, t3) FROM t1;
SELECT CONCAT(COALESCE(dt2, t3)) FROM t1;
SELECT COALESCE(t3, dt2) FROM t1;
SELECT CONCAT(COALESCE(t3, dt2)) FROM t1;
SELECT COALESCE(d, t3) FROM t1;
SELECT CONCAT(COALESCE(d, t3)) FROM t1;
SELECT COALESCE(t3, d) FROM t1;
SELECT CONCAT(COALESCE(t3, d)) FROM t1;
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # Testing that IFNULL always returns correct number of decimal digits
--echo #
SET timestamp=UNIX_TIMESTAMP('2011-11-10 18:04:30');
CREATE TABLE t1 (dt2 DATETIME(2), t3 TIME(3), d DATE);
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.12', '00:00:00.567', '2002-01-01');
SELECT IFNULL(dt2, t3) FROM t1;
SELECT CONCAT(IFNULL(dt2, t3)) FROM t1;
SELECT IFNULL(t3, dt2) FROM t1;
SELECT CONCAT(IFNULL(t3, dt2)) FROM t1;
SELECT IFNULL(d, t3) FROM t1;
SELECT CONCAT(IFNULL(d, t3)) FROM t1;
SELECT IFNULL(t3, d) FROM t1;
SELECT CONCAT(IFNULL(t3, d)) FROM t1;
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # Testing that LEAST always returns correct number of decimal digits
--echo #
SET timestamp=UNIX_TIMESTAMP('2011-11-10 18:04:30');
CREATE TABLE t1 (dt2 DATETIME(2), t3 TIME(3), d DATE);
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.12', '00:00:00.567', '2002-01-01');
INSERT INTO t1 VALUES ('2012-01-01 00:00:00.12', '00:00:00.567', '2013-01-01');
SELECT LEAST(dt2, t3) FROM t1;
SELECT CONCAT(LEAST(dt2, t3)) FROM t1;
SELECT LEAST(t3, dt2) FROM t1;
SELECT CONCAT(LEAST(t3, dt2)) FROM t1;
SELECT LEAST(d, t3) FROM t1;
SELECT CONCAT(LEAST(d, t3)) FROM t1;
SELECT LEAST(t3, d) FROM t1;
SELECT CONCAT(LEAST(t3, d)) FROM t1;
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # Testing that GREATEST always returns correct number of decimal digits
--echo #
SET timestamp=UNIX_TIMESTAMP('2011-11-10 18:04:30');
CREATE TABLE t1 (dt2 DATETIME(2), t3 TIME(3), d DATE);
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.12', '00:00:00.567', '2002-01-01');
INSERT INTO t1 VALUES ('2012-01-01 00:00:00.12', '00:00:00.567', '2013-01-01');
SELECT GREATEST(dt2, t3) FROM t1;
SELECT CONCAT(GREATEST(dt2, t3)) FROM t1;
SELECT GREATEST(t3, dt2) FROM t1;
SELECT CONCAT(GREATEST(t3, dt2)) FROM t1;
SELECT GREATEST(d, t3) FROM t1;
SELECT CONCAT(GREATEST(d, t3)) FROM t1;
SELECT GREATEST(t3, d) FROM t1;
SELECT CONCAT(GREATEST(t3, d)) FROM t1;
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # Testing that hybrid type functions correctly convert
--echo # from DATETIME to TIME
--echo #
CREATE TABLE t1 (a DATETIME(3));
INSERT INTO t1 VALUES ('2001-01-01 10:20:30.123');
SELECT CAST(IF(0, a, a) AS TIME(6)) FROM t1;
SELECT CAST(COALESCE(a, a) AS TIME(6)) FROM t1;
SELECT CAST(IFNULL(a, a) AS TIME(6)) FROM t1;
SELECT CAST(CASE WHEN 0 THEN a ELSE a END AS TIME(6)) FROM t1;
SELECT CAST(GREATEST(a, a) AS TIME(6)) FROM t1;
SELECT CAST(LEAST(a, a) AS TIME(6)) FROM t1;
DROP TABLE t1;


--echo #
--echo # Testing that hybrid type functions correctly convert
--echo # from TIME to DATETIME
--echo #
SET timestamp=UNIX_TIMESTAMP('2011-11-10 00:00:00');
CREATE TABLE t1 (a TIME(3));
INSERT INTO t1 VALUES ('10:20:30.123');
SELECT CAST(IF(0, a, a) AS DATETIME(6)) FROM t1;
SELECT CAST(COALESCE(a, a) AS DATETIME(6)) FROM t1;
SELECT CAST(IFNULL(a, a) AS DATETIME(6)) FROM t1;
SELECT CAST(CASE WHEN 0 THEN a ELSE a END AS DATETIME(6)) FROM t1;
SELECT CAST(GREATEST(a, a) AS DATETIME(6)) FROM t1;
SELECT CAST(LEAST(a, a) AS DATETIME(6)) FROM t1;
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # Storing TIME functions into VARCHAR column
--echo #
SET @@timestamp=1000000000;
CREATE TABLE t1 (a VARCHAR(32));
INSERT INTO t1 VALUES (CAST('00:00:00' AS TIME));
INSERT INTO t1 VALUES (CAST('00:00:00' AS TIME(6)));
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES (SEC_TO_TIME(10));
INSERT INTO t1 VALUES (SEC_TO_TIME(-0.1));
INSERT INTO t1 VALUES (SEC_TO_TIME(-0.01));
INSERT INTO t1 VALUES (SEC_TO_TIME(-0.001));
INSERT INTO t1 VALUES (SEC_TO_TIME(-0.0001));
INSERT INTO t1 VALUES (SEC_TO_TIME(-0.00001));
INSERT INTO t1 VALUES (SEC_TO_TIME(-0.000001));
INSERT INTO t1 VALUES (SEC_TO_TIME(10.0));
INSERT INTO t1 VALUES (SEC_TO_TIME(10.1));
INSERT INTO t1 VALUES (SEC_TO_TIME(10.12));
INSERT INTO t1 VALUES (SEC_TO_TIME(10.123));
INSERT INTO t1 VALUES (SEC_TO_TIME(10.1234));
INSERT INTO t1 VALUES (SEC_TO_TIME(10.12345));
INSERT INTO t1 VALUES (SEC_TO_TIME(10.123456));
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES (TIMEDIFF(NOW(), NOW()));
INSERT INTO t1 VALUES (TIMEDIFF(NOW(6), NOW(6)));
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES (MAKETIME(10, 10, 10));
INSERT INTO t1 VALUES (MAKETIME(10, 10, 10.1));
INSERT INTO t1 VALUES (MAKETIME(10, 10, 10.12));
INSERT INTO t1 VALUES (MAKETIME(10, 10, 10.123));
INSERT INTO t1 VALUES (MAKETIME(10, 10, 10.1234));
INSERT INTO t1 VALUES (MAKETIME(10, 10, 10.12345));
INSERT INTO t1 VALUES (MAKETIME(10, 10, 10.123456));
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES (CURRENT_TIME());
INSERT INTO t1 VALUES (CURRENT_TIME(6));
SELECT LENGTH(a) FROM t1;
DELETE FROM t1;
DROP TABLE t1;
SET @@timestamp=default;


--echo #
--echo # Storing DATETIME functions into VARCHAR column
--echo #
SET @@time_zone='+00:00';
SET @@timestamp=1000000000;
CREATE TABLE t1 (a VARCHAR(32));
INSERT INTO t1 VALUES (CAST('2001-01-01 00:00:00' AS DATETIME));
INSERT INTO t1 VALUES (CAST('2001-01-01 00:00:00' AS DATETIME(6)));
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES (FROM_UNIXTIME(1000000000));
INSERT INTO t1 VALUES (FROM_UNIXTIME(1000000000.0));
INSERT INTO t1 VALUES (FROM_UNIXTIME(1000000000.1));
INSERT INTO t1 VALUES (FROM_UNIXTIME(1000000000.12));
INSERT INTO t1 VALUES (FROM_UNIXTIME(1000000000.123));
INSERT INTO t1 VALUES (FROM_UNIXTIME(1000000000.1234));
INSERT INTO t1 VALUES (FROM_UNIXTIME(1000000000.12345));
INSERT INTO t1 VALUES (FROM_UNIXTIME(1000000000.123456));
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES (CONVERT_TZ(CAST('2001-01-01 00:00:00' AS DATETIME), '+00:00', '+10:00'));
INSERT INTO t1 VALUES (CONVERT_TZ(CAST('2001-01-01 00:00:00' AS DATETIME(6)), '+00:00', '+10:00'));
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES (CURRENT_TIMESTAMP());
INSERT INTO t1 VALUES (CURRENT_TIMESTAMP(6));
SELECT LENGTH(a) FROM t1;
DELETE FROM t1;
DROP TABLE t1;
SET @@timestamp=default;
SET @@time_zone=default;

--echo #
--echo # Storing DATE/TIME hybrid functions into VARCHAR column
--echo #
SET @@time_zone='+00:00';
SET @@timestamp=1000000000;
CREATE TABLE t1 (a VARCHAR(32));
INSERT INTO t1 VALUES (ADDTIME('10:10:10', 10));
INSERT INTO t1 VALUES (ADDTIME('10:10:10', 10.123456));
INSERT INTO t1 VALUES (ADDTIME('10:10:10.000000', 10));
INSERT INTO t1 VALUES (ADDTIME('10:10:10.123456', 10));
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES (ADDTIME(CURRENT_TIME, 10));
INSERT INTO t1 VALUES (ADDTIME(CURRENT_TIME, 10.123456));
INSERT INTO t1 VALUES (ADDTIME(CURRENT_TIMESTAMP, 10));
INSERT INTO t1 VALUES (ADDTIME(CURRENT_TIMESTAMP, 10.123456));
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES(DATE_ADD('2001-01-01 00:00:00', INTERVAL 1 SECOND));
INSERT INTO t1 VALUES(DATE_ADD('2001-01-01 00:00:00', INTERVAL 1.1 SECOND));
INSERT INTO t1 VALUES(DATE_ADD('2001-01-01 00:00:00.000000', INTERVAL 1 SECOND));
INSERT INTO t1 VALUES(DATE_ADD('2001-01-01 00:00:00.123456', INTERVAL 1 SECOND));
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES(DATE_ADD(CAST('2001-01-01 00:00:00' AS DATETIME), INTERVAL 1 SECOND));
INSERT INTO t1 VALUES(DATE_ADD(CAST('2001-01-01 00:00:00' AS DATETIME(6)), INTERVAL 1 SECOND));
INSERT INTO t1 VALUES(DATE_ADD(CAST('2001-01-01 00:00:00' AS DATETIME), INTERVAL 1.1 SECOND));
INSERT INTO t1 VALUES(DATE_ADD(CAST('2001-01-01 00:00:00' AS DATETIME(6)), INTERVAL 1.1 SECOND));
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES(DATE_ADD(CAST('00:00:00' AS TIME), INTERVAL 1 SECOND));
INSERT INTO t1 VALUES(DATE_ADD(CAST('00:00:00' AS TIME(6)), INTERVAL 1 SECOND));
INSERT INTO t1 VALUES(DATE_ADD(CAST('00:00:00' AS TIME), INTERVAL 1.1 SECOND));
INSERT INTO t1 VALUES(DATE_ADD(CAST('00:00:00' AS TIME(6)), INTERVAL 1.1 SECOND));
SELECT * FROM t1;
DELETE FROM t1;
INSERT INTO t1 VALUES (STR_TO_DATE('2000', '%Y'));
INSERT INTO t1 VALUES (STR_TO_DATE('2000 10', '%Y %h'));
INSERT INTO t1 VALUES (STR_TO_DATE('2000 10 000000', '%Y %h %f'));
INSERT INTO t1 VALUES (STR_TO_DATE('2000 10 123456', '%Y %h %f'));
INSERT INTO t1 VALUES (STR_TO_DATE('2000 10 000000', '2000 %h 000000'));
INSERT INTO t1 VALUES (STR_TO_DATE('2000 10 123456', '2000 %h 123456'));
INSERT INTO t1 VALUES (STR_TO_DATE('2000 10 000000', '2000 %h %f'));
INSERT INTO t1 VALUES (STR_TO_DATE('2000 10 123456', '2000 %h %f'));
SELECT * FROM t1;
DELETE FROM t1;
DROP TABLE t1;
SET @@timestamp=default;
SET @@time_zone=default;


--echo #
--echo # Testing ALTER from DATE to various other temporal types
--echo #
CREATE TABLE t1 (a DATE, b DATE, c DATE);
INSERT INTO t1 VALUES ('2001-01-01', '2001-01-01', '2001-01-01');
ALTER TABLE t1 MODIFY a DATETIME(6), MODIFY b TIMESTAMP(6), MODIFY c TIME(6);
SELECT * FROM t1;
DROP TABLE t1;

####################################################################

--echo #
--echo # Examples from the WL#946 section "Literals"
--echo #
CREATE TABLE t1 (datetime_column DATETIME(1));
INSERT INTO t1 VALUES ('0000/1/1T1.23');
SELECT * FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (datetime_column DATETIME(1));
INSERT INTO t1 VALUES ('1111.11.11 11.11.11.55');
SELECT * FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (time_column TIME(1));
INSERT INTO t1 VALUES ('1.1');
SELECT * FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (time_column TIME(1));
INSERT INTO t1 VALUES ('1:2:3:4');
SELECT * FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (datetime_column DATETIME(2));
INSERT INTO t1 VALUES ('73-1-1 1:1:1.2');
SELECT * FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (datetime_column DATETIME(2));
INSERT INTO t1 VALUES (1101010101.55);
SELECT * FROM t1;
DROP TABLE t1;

SELECT TIMEDIFF('00:00:00.1', '00:00:00.3');

--echo #
--echo # Examples from the WL#946 section "Treat like decimals"
--echo #
CREATE TABLE t1 (time_column TIME(6));
INSERT INTO t1 SET time_column=TIME'1.000001';
SELECT * FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (time_column TIME(6));
INSERT INTO t1 SET time_column=TIME'1.0000005';
SELECT * FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (time_column TIME(6));
INSERT INTO t1 SET time_column=TIME'1.0000005' + 0.000001;
SELECT * FROM t1;
DROP TABLE t1;

SELECT TIME'00:00:00.1' = TIME'00:00:00.100';

CREATE TABLE t1 (timestamp_column TIMESTAMP(0));
INSERT INTO t1 SET timestamp_column = '2005-01-01 23:59:59.9';
SELECT * FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (ts3 TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3), ts2 TIMESTAMP(2) NOT NULL DEFAULT '0000-00-00 00:00:00');
CREATE TABLE t2 AS SELECT ts3 FROM t1 UNION SELECT ts2 FROM t1;
SHOW COLUMNS FROM t2;
DROP TABLE t2;
DROP TABLE t1;


--echo #
--echo # Examples from the WL#946 section "Behaviour changes"
--echo #

# 1
CREATE TABLE t1 AS SELECT
  TIME'10:10:10' + INTERVAL 1 SECOND,
  ADDTIME(TIME'10:10:10', '1.1');
SHOW COLUMNS FROM t1;
DROP TABLE t1;

# 2
SELECT TIME'10:10:10' + INTERVAL .6 SECOND;

# 3
SELECT TIME('20:20:20') + INTERVAL .4 SECOND;

# 4
CREATE TABLE t1 AS SELECT
  TIMESTAMP'2001-01-01 00:00:00' + INTERVAL 1 MICROSECOND AS a;
SHOW COLUMNS FROM t1;
DROP TABLE t1;

# 5
SELECT ADDTIME('9999-01-01 00:00:00', '.1');
SELECT ADDTIME(TIMESTAMP'9999-01-01 00:00:00', '.1');

# 6
SET timestamp=UNIX_TIMESTAMP('2001-01-01 10:20:30');
SELECT CURRENT_TIME(6), CURRENT_TIME;
SET timestamp=DEFAULT;

# 7
SET time_zone='+00:00';
SELECT FROM_UNIXTIME(0.5);
SET time_zone=DEFAULT;

# 8
CREATE TABLE t1 (s1 DATETIME);
INSERT INTO t1 VALUES ('2000-12-31 23:59:59.9');
SELECT * FROM t1;
DROP TABLE t1;

# 9
SELECT SEC_TO_TIME(36000.123);

# 10
SET timestamp=1308300674;
SELECT @@timestamp;
CREATE TABLE t1 AS SELECT @@timestamp;
SHOW COLUMNS FROM t1;
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # Examples from the WL#946 section "Metadata"
--echo #
CREATE TABLE t1 (a TIME(3));
SHOW COLUMNS FROM t1;
DESC t1;
SHOW FIELDS FROM t1;
SHOW CREATE TABLE t1;
DROP TABLE t1;

####################################################################

--echo #
--echo # Bug#12546965 - 60990: INCORRECT DATE COMPARISON 
--echo #
CREATE TABLE t1
(first_usage DATE, last_recharge DATETIME, life_time SMALLINT(4) UNSIGNED);
INSERT INTO t1 VALUES ('2011-04-27', null, 900);
SELECT
  DATE_ADD(GREATEST(first_usage, IFNULL(last_recharge, first_usage)), INTERVAL life_time DAY ) as dt,
  DATE_ADD(GREATEST(first_usage, IFNULL(last_recharge, first_usage)), INTERVAL life_time DAY ) <
  DATE('2011-04-28') as exp FROM t1; 
DROP TABLE t1;


--echo #
--echo # Bug 12565857 - 61193: INCORRECT ROUNDING OF DATE DECIMAL(17,3)
--echo #
SELECT TIME(20110512154559.616),
  DATE(20110512154559.616), DATE(FLOOR(20110512154559.616)); 


--echo #
--echo # "SELECT 3 IN (SELECT MAX(a) FROM t1)" returned 0 instead of NULL
--echo # at some point of WL#946 development.
--echo # This case is not covered elsewhere in the tests.
--echo #
CREATE TABLE t1 (a INT);
SELECT 3 IN (SELECT MAX(a) FROM t1);
DROP TABLE t1;


# Testing buggy cases found by Peter in earlier patch versions
CREATE TABLE t1(s1 DATETIME(6), s2 TIME(6));
INSERT INTO t1 VALUES ('1212-12-12 21:21:21.555555', '00:00:00.21');
SELECT s1 + s2 FROM t1;
DROP TABLE t1;


CREATE TABLE t1 (s1 TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6));
INSERT INTO t1 VALUES ('1971-01-01 00:00:00.0000099');
# result was '1970-01-01 00:00:00.000009' (truncated) at time of reporting
SELECT * FROM t1;
DROP TABLE t1;

SET @@timestamp=UNIX_TIMESTAMP('2001-01-01 10:00:00');
CREATE TABLE t1 (s0 TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0), s6 TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00');
INSERT INTO t1 VALUES (TIME'11:11:11.123456', TIME'11:11:11.123456');
# result was '0000-00-00 00:00:00' at time of reporting
SELECT * FROM t1;
DROP TABLE t1;
SET @@timestamp=default;

SET sql_mode='' /* not 'no_zero_in_date' */;
CREATE TABLE t1 (s1 DATETIME);
# The second INSERT failed at time of reporting
INSERT INTO t1 VALUES ('1000-00-01 00:00:00');
INSERT INTO t1 VALUES (TIMESTAMP'1000-00-01 00:00:00');
SELECT * FROM t1;
DROP TABLE t1;

# SYSDATE(n) post-decimal digits are not zeros
# Disable warnings, see Bug#61216: Incorrect warning for "datetime LIKE string"
SELECT SYSDATE(6) LIKE '____-__-__ __:__:__.______';
SELECT SYSDATE(6) NOT LIKE '%.000000' || SYSDATE(6) NOT LIKE '%.000000';

CREATE TABLE t1 (s1 DATETIME(2));
INSERT INTO t1 VALUES ('1970-01-01 11:11:11.1234');
# result was '1970-01-01 11:11:11.00' at time of reporting
SELECT * FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (s1 DATETIME(2));
INSERT INTO t1 VALUES (TIMESTAMP '0001-01-01 00:00:00.05');
# result was 6 post-decimal digits at time of reporting
SELECT TIMESTAMPADD(second, 1, s1) FROM t1;
DROP TABLE t1;

# This  query failed at time of reporting:
CREATE TABLE t1 AS SELECT TIME'11:11:11.1234567';
SHOW COLUMNS FROM t1;
SELECT * FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (s1 datetime(6));
INSERT INTO t1 VALUES ('2011-05-18 16:17:11.291816');
# Was "sum(s1+0) <> sum(s1)" at time of reporting
SELECT MAX(s1), MAX(s1 + 0), MAX(s1) FROM t1;
DROP TABLE t1;

# This query returned error at time of reporting:
SELECT TIMESTAMP'2011-05-18 16:17:11.';

# This query returned 6 post-decimal digits at time of reporting
SELECT TIMESTAMP('2011-05-18 16:17:11.2', '11.29');

# There was a question why the result does not end with '.0'
# TIMESTAMPADD is a synonym for DATE_ADD, so it's a hybrid function
# with VARCHAR result type in this context.
# So, no, it should not end with '.0'.
SELECT TIMESTAMPADD(second, 1, '2011-05-18 16:17:11.0');
SELECT DATE_ADD('2011-05-18 16:17:11.0', INTERVAL 1 SECOND);
# it ends with either 0 or 6 fractional digits:
SELECT TIMESTAMPADD(second, 1.1, '2011-05-18 16:17:11.0');
SELECT DATE_ADD('2011-05-18 16:17:11.0', INTERVAL 1.1 SECOND);

CREATE TABLE t1 (time_column TIME(6));
INSERT INTO t1 VALUES (0);
# this query returned error:
UPDATE t1 SET time_column=TIME '1.0000005';
SELECT * FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (time_column TIME(6));
INSERT INTO t1 VALUES (0);
# this query returned error:
UPDATE t1 SET time_column=TIME '1.0000005' + 0.000001;
SELECT * FROM t1;
DROP TABLE t1;

CREATE TABLE t1 (timestamp_column TIMESTAMP(0));
INSERT INTO t1 VALUES (DEFAULT);
# This query truncated (not rounded) at time of reporting:
UPDATE t1 SET timestamp_column = '2005-01-01 23:59:59.9';
SELECT * FROM t1;
DROP TABLE t1;

# This query created TIME(0) at time of reporting:
CREATE TABLE t1 AS SELECT TIME('00:00:00.123');
SHOW COLUMNS FROM t1;
SELECT * FROM t1;
DROP TABLE t1;

# This query created DATETIME(0) at time of reporting:
CREATE TABLE t1 AS SELECT TIMESTAMP('2000-01-01 00:00:00.123');
SHOW COLUMNS FROM t1;
SELECT * FROM t1;
DROP TABLE t1;

# Literal with more than 6 digits led to error instead of rounding
CREATE TABLE t1 AS SELECT TIME'10:20:30.1234567';
SHOW COLUMNS FROM t1;
SELECT * FROM t1;
DROP TABLE t1;

# Originally we wanted this to ignore the decimal part,
# but then changed the requirement.
CREATE TABLE t1 (a TIME(1));
INSERT INTO t1 VALUES ('1.1');
SELECT * FROM t1;
DROP TABLE t1;

# Wrong UNIX_TIMESTAMP(CURRENT_TIMESTAMP(6)) produced DECIMAL(15,6).
# The correct type is DECIMAL(16,6)
SET time_zone='+00:00';
SET @@timestamp=UNIX_TIMESTAMP('2011-09-21 20:40:05.123456');
CREATE TABLE t1 AS SELECT UNIX_TIMESTAMP(CURRENT_TIMESTAMP(6));
SHOW CREATE TABLE t1;
SELECT * FROM t1;
DROP TABLE t1;
SET @@timestamp=DEFAULT;
SET time_zone=DEFAULT;


# Reported by Bernt:
# FROM_UNIXTIME(@@timestamp) returned .000000 in fractional digits.
SET @@timestamp=UNIX_TIMESTAMP('1970-01-02 03:04:05.123456');
SELECT FROM_UNIXTIME(@@timestamp);
SET @@timestamp=default;

--echo #
--echo # Bugs found by Saikumar during testing.
--echo # The below function calls made server crash.
--echo #
SET TIMESTAMP=UNIX_TIMESTAMP('2001-01-01 00:00:00');
SELECT ADDTIME(UTC_DATE, '23:28:14.014837') FROM dual;
SELECT SUBTIME(UTC_DATE, '04:44:03.014042') FROM dual ;
SELECT TIMEDIFF(CURRENT_DATE, '2004-12-07') FROM dual;
SELECT TIMEDIFF('0000-00-00 00:00:00.00000', UTC_DATE) FROM dual;
SELECT TIME(CURRENT_DATE) FROM dual;
CREATE TABLE t1 (
  pk TIMESTAMP(1) NOT NULL DEFAULT '0000-00-00  00:00:00.0',
  PRIMARY KEY (pk)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 /*!50100 PARTITION BY KEY (pk)PARTITIONS 2 */;
INSERT INTO t1 VALUES ('2011-07-13 13:17:58.3'), ('2011-07-13 13:17:59.3');
SELECT EXTRACT(HOUR_SECOND FROM CURRENT_DATE) FROM t1;
DROP TABLE t1;
SET TIMESTAMP=DEFAULT;

--echo #
--echo # bug7
--echo #
CREATE TABLE t1 (
  col_timestamp_2_not_null_key timestamp(2) NULL DEFAULT '0000-00-00 00:00:00.00',
  col_datetime_4_not_null_key datetime(4) NOT NULL,
  KEY col_timestamp_2_not_null_key (col_timestamp_2_not_null_key),
  KEY col_datetime_4_not_null_key (col_datetime_4_not_null_key));
UPDATE t1 SET col_datetime_4_not_null_key = 9
WHERE col_timestamp_2_not_null_key !=9;
DROP TABLE t1;


--echo #
--echo # bug8
--echo #
CREATE TABLE t1 (
  col_time_3_not_null_key time(3) NOT NULL,
  col_datetime_4_not_null_key datetime(4) NOT NULL,
  KEY col_time_3_not_null_key (col_time_3_not_null_key),
  KEY col_datetime_4_not_null_key (col_datetime_4_not_null_key));
SELECT col_time_3_not_null_key 
FROM t1 
WHERE col_datetime_4_not_null_key > 7;
DROP TABLE t1;

--echo #
--echo # bug9
--echo #
CREATE TABLE t1 (  col_time_3_key time(3) DEFAULT NULL,  col_datetime_5_not_null datetime(5) NOT NULL,  pk datetime(6) NOT NULL DEFAULT '0000-00-00 00:00:00.000000',  PRIMARY KEY (pk),  KEY col_time_3_key (col_time_3_key));
INSERT INTO t1 VALUES 
  (NULL,'2003-02-10 18:14:45.03977','2011-07-16 20:53:44.979841'),
  ('07:57:19.051','0000-00-00 00:00:00.00000','2011-07-16 20:53:45.979841'),
  ('05:18:39.031','0000-00-00 00:00:00.00000','2011-07-16 20:53:46.979841'),
  ('12:15:34.041','2001-12-22 00:00:00.00000','2011-07-16 20:53:47.979841'),
  ('03:40:59.005','0000-00-00 00:00:00.00000','2011-07-16 20:53:48.979841'),
  ('01:41:57.004','0000-00-00 00:00:00.00000','2011-07-16 20:53:49.979841');
SELECT col_datetime_5_not_null 
FROM t1 
WHERE TIMESTAMPADD(QUARTER, 5, col_time_3_key);
DROP TABLE t1;

--echo #
--echo # bug10
--echo #
CREATE TABLE t1 (
  col_time_1_not_null_key time(1) NOT NULL,
  col_time_2_key time(2) DEFAULT NULL,
  col_datetime_6_not_null_key datetime(6) NOT NULL,
  col_datetime_4_not_null_key datetime(4) NOT NULL,
  col_datetime_6_key datetime(6) DEFAULT NULL,
  col_timestamp_4_key timestamp(4) NULL DEFAULT '0000-00-00 00:00:00.0000',
  col_timestamp_5_key timestamp(5) NULL DEFAULT '0000-00-00 00:00:00.00000',
  col_datetime_key datetime DEFAULT NULL,
  col_datetime_5_not_null_key datetime(5) NOT NULL,
  col_time_5_key time(5) DEFAULT NULL,
  col_datetime_3_key datetime(3) DEFAULT NULL,
  pk datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  col_time_not_null_key time NOT NULL,
  col_time_4_not_null_key time(4) NOT NULL,
  PRIMARY KEY (pk),
  KEY col_time_1_not_null_key (col_time_1_not_null_key),
  KEY col_time_2_key (col_time_2_key),
  KEY col_datetime_6_not_null_key (col_datetime_6_not_null_key),
  KEY col_datetime_4_not_null_key (col_datetime_4_not_null_key),
  KEY col_datetime_6_key (col_datetime_6_key),
  KEY col_timestamp_4_key (col_timestamp_4_key),
  KEY col_timestamp_5_key (col_timestamp_5_key),
  KEY col_datetime_key (col_datetime_key),
  KEY col_datetime_5_not_null_key (col_datetime_5_not_null_key),
  KEY col_time_5_key (col_time_5_key),
  KEY col_datetime_3_key (col_datetime_3_key),
  KEY col_time_not_null_key (col_time_not_null_key),
  KEY col_time_4_not_null_key (col_time_4_not_null_key))
/*!50100 PARTITION BY KEY (pk)PARTITIONS 2 */;
--source include/turn_off_only_full_group_by.inc

SELECT col_datetime_3_key , COUNT( col_timestamp_5_key )
FROM t1 FORCE KEY
  (PRIMARY, col_datetime_5_not_null_key, col_datetime_6_key, col_time_5_key, col_time_2_key) 
WHERE
  (col_time_4_not_null_key NOT
    BETWEEN '2002-01-01 21:50:38.023562' AND '20090504151448.022774'
    AND col_time_1_not_null_key IN
    ('2000-07-13 21:35:51.034786',
     '2004-10-13 19:51:18.029716',
     '2001-11-23 10:22:42.001689',
     '2001-12-07 06:56:47.034426',
     '20081109153313.042580'
    )
  )
AND
  (col_timestamp_4_key BETWEEN '0000-00-00 00:00:00' AND '2008-06-16 10:42:11.032174'
   OR
   col_time_not_null_key BETWEEN '2007-02-13 14:29:42.008072' AND '2009-05-18 17:06:18.036503'
  )
AND
(
  (col_datetime_key <> '2004-09-06 19:57:07.024714')
   AND col_datetime_5_not_null_key NOT IN 
    ('20061212182915.005997',
     '0000-00-00 00:00:00',
     '20000920051358.001283',
     '2000-09-03 00:07:36.061713'
    )
)
AND
(col_datetime_4_not_null_key IS NULL OR col_time_5_key BETWEEN '20051201163954.001782' AND '0000-00-00 00:00:00')
GROUP BY col_datetime_6_not_null_key;

--source include/restore_sql_mode_after_turn_off_only_full_group_by.inc
DROP TABLE t1;


--echo #
--echo # bug11
--echo #

CREATE TABLE t1 (
  col_time_5 time(5) DEFAULT NULL,
  col_time_1_not_null time(1) NOT NULL,
  col_time_2_key time(2) DEFAULT NULL,
  col_timestamp_2_not_null timestamp(2) NULL DEFAULT '0000-00-00 00:00:00.00',
  col_datetime_not_null datetime NOT NULL,
  col_timestamp_3_not_null_key timestamp(3) NULL DEFAULT '0000-00-00 00:00:00.000',
  col_datetime_5_key datetime(5) DEFAULT NULL,
  pk time(3) NOT NULL DEFAULT '00:00:00.000',
  PRIMARY KEY (pk),
  KEY col_time_2_key (col_time_2_key),
  KEY col_timestamp_3_not_null_key (col_timestamp_3_not_null_key),
  KEY col_datetime_5_key (col_datetime_5_key))
/*!50100 PARTITION BY KEY (pk)PARTITIONS 2 */;
INSERT INTO t1 VALUES (
  '00:20:00.00000',
  '15:50:06.1',
  '00:20:07.00',
  '0000-00-00 00:00:00.00',
  '2009-07-16 09:31:21',
  '2009-10-14 21:00:00.000',
  '0000-00-00 00:00:00.00000',
  '22:59:36.734');
SELECT col_datetime_5_key AS c1
FROM t1
WHERE col_datetime_not_null NOT BETWEEN col_time_5 AND
LEAST(col_time_1_not_null,
      ADDDATE(CAST(col_time_2_key AS DATE),
      col_timestamp_2_not_null))
ORDER BY 1;
DROP TABLE t1;

--echo #
--echo # bug13
--echo #
SET timestamp=UNIX_TIMESTAMP('2011-07-31 10:00:00');
CREATE TABLE t1 (
  col_datetime_2_not_null_key datetime(2) NOT NULL,
  col_datetime_5 datetime(5) DEFAULT NULL,
  col_time_key time DEFAULT NULL,
  col_time_not_null time NOT NULL,
  pk datetime(5) NOT NULL DEFAULT '0000-00-00 00:00:00.00000',
  PRIMARY KEY (pk),
  KEY col_datetime_2_not_null_key (col_datetime_2_not_null_key),
  KEY col_time_key (col_time_key));
INSERT INTO t1 VALUES
  ('2001-10-20 00:00:00.00','2001-09-20 11:18:18.03630','03:00:10','00:20:01','2011-07-19 17:37:06.26725'),
  ('0000-00-00 00:00:00.00','0000-00-00 00:00:00.00000','08:30:41','00:20:01','2011-07-19 17:37:07.26725'),
  ('0000-00-00 00:00:00.00','0000-00-00 00:00:00.00000','20:07:31','01:12:26','2011-07-19 17:37:08.26725'),
  ('2000-01-14 17:55:57.03','0000-00-00 00:00:00.00000','00:20:04','14:35:10','2011-07-19 17:37:09.26725'),
  ('2006-12-27 07:25:14.04',NULL,'08:07:59','07:58:50','2011-07-19 17:37:10.26725'),
  ('2000-10-17 22:03:12.01','0000-00-00 00:00:00.00000','00:20:02','23:21:51','2011-07-19 17:37:11.26725'),
  ('0000-00-00 00:00:00.00','0000-00-00 00:00:00.00000','00:20:05','00:20:04','2011-07-19 17:37:12.26725'),
  ('0000-00-00 00:00:00.00','0000-00-00 00:00:00.00000','00:20:04','23:19:52','2011-07-19 17:37:13.26725'),
  ('0000-00-00 00:00:00.00','2008-06-04 23:05:50.03642','00:20:03','22:28:03','2011-07-19 17:37:14.26725'),
  ('2000-10-22 23:52:09.01',NULL,'00:20:07','00:20:07','2011-07-19 17:37:15.26725');
# Different warnings in the hypergraph optimizer.
--disable_warnings
SELECT col_datetime_2_not_null_key AS c1, col_time_not_null AS c2
FROM t1
WHERE
  pk = LEAST(STR_TO_DATE(DATE_FORMAT(col_time_key,
                                     CONCAT_WS( ':','%I','%m' )) ,
                         CONCAT_WS('-','%y','%H','%V','%k','%k' )),
             '0000-00-00')
OR
  col_datetime_5 = CURRENT_DATE()
ORDER BY 1;
--enable_warnings
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # bug15
--echo #
CREATE TABLE t1 (
  col_datetime_5 datetime(5) DEFAULT NULL,
  col_timestamp_6_key timestamp(6) NOT NULL DEFAULT '0000-00-00 00:00:00.000000',
  col_time_2_not_null time(2) NOT NULL,
  KEY col_timestamp_6_key (col_timestamp_6_key))
  ENGINE=MEMORY DEFAULT CHARSET=latin1;
INSERT INTO t1 VALUES (current_timestamp(5),current_timestamp(6),current_time(6));
SELECT col_datetime_5 AS c1 FROM t1
WHERE col_time_2_not_null = GREATEST(CURRENT_DATE(),col_timestamp_6_key)
ORDER BY 1;
DROP TABLE t1;

#
# Bug#18
#
SET @@time_zone='+00:00';
CREATE TABLE t1 (col_datetime_4_not_null DATETIME(4) NOT NULL);
INSERT INTO t1 VALUES
  ('0000-00-00 00:00:00.0000'),('2006-05-12 07:06:44.0441'),('2007-11-08 00:00:00.0000'),
  ('2007-07-23 00:00:00.0000'),('2006-01-10 22:19:14.0158'),('2006-09-13 18:54:05.0013'),
  ('2002-03-26 00:00:00.0000'),('2002-10-22 10:53:06.0151'),('0000-00-00 00:00:00.0000'),
  ('2001-06-04 00:00:00.0000'),('0000-00-00 00:00:00.0000'),('2000-12-11 10:47:58.0505'),
  ('2009-04-21 20:01:40.0570'),('2007-03-12 10:48:41.0031'),('0000-00-00 00:00:00.0000'),
  ('2009-06-22 00:00:00.0000'),('2008-01-21 15:28:44.0592'),('2003-10-05 00:43:55.0577'),
  ('2002-11-04 00:46:30.0630'),('2006-01-19 11:38:03.0378'),('0000-00-00 00:00:00.0000'),
  ('2001-02-04 00:00:00.0000'),('2004-10-22 21:59:04.0394'),('2006-03-20 18:54:13.0139'),
  ('2004-06-09 03:17:31.0403'),('0000-00-00 00:00:00.0000'),('2003-06-01 17:59:12.0365'),
  ('0000-00-00 00:00:00.0000'),('2009-06-15 08:58:58.0329'),('0000-00-00 00:00:00.0000'),
  ('2004-03-26 00:00:00.0000'),('2009-04-27 00:00:00.0000'),('2000-09-07 00:00:00.0000'),
  ('2006-11-04 00:51:03.0501'),('2005-02-20 00:30:47.0647'),('0000-00-00 00:00:00.0000'),
  ('2004-12-07 00:00:00.0000'),('0000-00-00 00:00:00.0000'),('0000-00-00 00:00:00.0000'),
  ('2002-08-17 00:27:20.0536'),('2006-10-12 12:12:28.0337'),('0000-00-00 00:00:00.0000'),
  ('0000-00-00 00:00:00.0000'),('2009-09-09 14:16:05.0354'),('2000-02-25 00:00:00.0000'),
  ('2003-12-16 05:38:37.0626'),('2000-10-05 03:46:43.0067'),('0000-00-00 00:00:00.0000'),
  ('2000-10-08 06:45:51.0547'),('0000-00-00 00:00:00.0000'),('2000-04-06 01:46:21.0620'),
  ('2001-08-10 23:15:40.0304'),('2001-06-24 10:14:00.0497'),('0000-00-00 00:00:00.0000'),
  ('0000-00-00 00:00:00.0000'),('2004-10-22 00:00:00.0000'),('0000-00-00 00:00:00.0000'),
  ('0000-00-00 00:00:00.0000'),('2005-08-23 06:34:23.0058'),('2005-03-28 18:34:18.0138'),
  ('2004-05-18 00:00:00.0000');
SELECT UNIX_TIMESTAMP(col_datetime_4_not_null) FROM t1 ORDER BY 1;
DROP TABLE t1;
SET @@time_zone=DEFAULT;

#
# Bug19
#
SET @@timestamp=UNIX_TIMESTAMP('2011-09-21 10:20:30.123456');
CREATE TABLE t1
(
  col_timestamp_4_not_null_key TIMESTAMP(4) NULL DEFAULT '0000-00-00 00:00:00.0000',
  col_datetime_6_not_null DATETIME(6) NOT NULL,
  col_time_2_not_null TIME(2) NOT NULL,
  pk TIME(5) NOT NULL DEFAULT '00:00:00.00000',
  col_timestamp_5_not_null_key TIMESTAMP(5) NULL DEFAULT '0000-00-00 00:00:00.00000',
  PRIMARY KEY (pk),
  KEY col_timestamp_4_not_null_key (col_timestamp_4_not_null_key),
  KEY col_timestamp_5_not_null_key (col_timestamp_5_not_null_key)
) /*!50100 PARTITION BY KEY (pk)PARTITIONS 2 */;
SELECT
  LEAST('2008-05-18', LOCALTIMESTAMP(), col_timestamp_5_not_null_key ) AS c1
FROM t1
WHERE col_time_2_not_null = TIME
(STR_TO_DATE
(DATE_FORMAT
(DATE('2009-05-04'),
  CONCAT_WS(':', '%M', '%a', '%x', '%S', '%j' )),
  CONCAT_WS('-', '%V', '%S', '%U' )))
AND
  col_timestamp_4_not_null_key
  BETWEEN CURDATE()
  AND STR_TO_DATE(DATE_FORMAT(col_datetime_6_not_null,
                              CONCAT_WS('-', '%I', '%S', '%v' )),
                  CONCAT_WS('-', '%V', '%H' ));
DROP TABLE t1;
SET @@timestamp=DEFAULT;

#
# Bug#21
#
SET @@timestamp= UNIX_TIMESTAMP('2011-09-21 08:20:30.123456');
CREATE TABLE t1 (
  col_datetime_6 DATETIME(6) DEFAULT NULL,
  col_time_1_key TIME(1) DEFAULT NULL,
  col_time_4_key TIME(4) DEFAULT NULL,
  col_time_1 TIME(1) DEFAULT NULL,
  pk TIME(4) NOT NULL DEFAULT '00:00:00.0000',
  col_time_2_key TIME(2) DEFAULT NULL,
  col_datetime DATETIME DEFAULT NULL,
  col_datetime_5_not_null DATETIME(5) NOT NULL,
  col_time_3_key TIME(3) DEFAULT NULL,
  col_time_3 TIME(3) DEFAULT NULL,
  PRIMARY KEY (pk),
  KEY col_time_1_key (col_time_1_key),
  KEY col_time_4_key (col_time_4_key),
  KEY col_time_2_key (col_time_2_key),
  KEY col_time_3_key (col_time_3_key)
)/*!50100 PARTITION BY KEY (pk)PARTITIONS 2 */;
INSERT INTO t1 VALUES ('2004-06-28 22:47:21.052552','17:11:33.0','00:20:09.0000','00:20:04.0','02:05:58.4461','08:36:44.02','0000-00-00 00:00:00','0000-00-00 00:00:00.00000','02:17:50.049','07:31:13.030');
SELECT col_datetime_6 AS c1, col_datetime AS c2 
FROM t1 
WHERE col_time_2_key
  NOT BETWEEN
    SUBTIME(DATE('0000-00-00 00:00:00'), '19:30:30.005477' )
  AND CURRENT_TIME()
ORDER BY
  col_time_1, col_time_3, col_datetime_6, col_time_3_key,
  col_datetime_5_not_null, col_time_1_key, col_time_4_key;
DROP TABLE t1;
SET @@timestamp=DEFAULT;

#
# Bug#22
#
SET @@timestamp=UNIX_TIMESTAMP('2011-09-21 10:20:30');
CREATE TABLE t1 (
  col_time_6_not_null_key TIME(6) NOT NULL,
  col_time_not_null TIME NOT NULL,
  pk TIME(4) NOT NULL DEFAULT '00:00:00.0000',
  col_time_key TIME DEFAULT NULL,
  col_time_2_not_null_key TIME(2) NOT NULL,
  col_time_4 TIME(4) DEFAULT NULL,
  col_time_3_not_null TIME(3) NOT NULL,
  col_datetime_2 DATETIME(2) DEFAULT NULL,
  PRIMARY KEY (pk),
  KEY col_time_6_not_null_key (col_time_6_not_null_key),
  KEY col_time_key (col_time_key),
  KEY col_time_2_not_null_key (col_time_2_not_null_key)
) /*!50100 PARTITION BY KEY (pk)PARTITIONS 2 */;
INSERT INTO t1 VALUES (
  CURRENT_TIME(4),
  CURRENT_TIME(),
  CURRENT_TIME(4),
  CURRENT_TIME(),
  CURRENT_TIME(2),
  CURRENT_TIME(4),
  CURRENT_TIME(3),
  CURRENT_TIMESTAMP(2));
SELECT col_datetime_2 AS c1, col_time_not_null AS c2
FROM t1
WHERE
  col_time_6_not_null_key >
  COALESCE(col_time_2_not_null_key, col_time_4, col_time_not_null, col_time_3_not_null)
ORDER BY 1;
DROP TABLE t1;
SET @@timestamp=DEFAULT;

#
# Bug#23
#
CREATE TABLE t1 (
  col_time_6_not_null_key TIME(6) NOT NULL,
  col_time_5_key TIME(5) DEFAULT NULL,
  col_timestamp TIMESTAMP NULL DEFAULT '0000-00-00 00:00:00',
  col_time_key TIME DEFAULT NULL,
  col_datetime_key DATETIME DEFAULT NULL,
  col_time_not_null TIME NOT NULL,
  pk TIMESTAMP(4) NOT NULL DEFAULT '0000-00-00 00:00:00.0000',
  PRIMARY KEY (pk),
  KEY col_time_6_not_null_key (col_time_6_not_null_key),
  KEY col_time_5_key (col_time_5_key),
  KEY col_time_key (col_time_key),
  KEY col_datetime_key (col_datetime_key)
) /*!50100 PARTITION BY KEY (pk)PARTITIONS 2 */;
INSERT INTO t1 VALUES
('03:07:29.013169',NULL,'0000-00-00 00:00:00','18:29:54','2000-06-10 19:41:30',
  '13:56:51','2011-09-07 23:08:20.8362'),('00:20:06.000000','08:25:11.04580',
  '2009-06-16 04:33:32','00:20:04','2004-08-23 21:05:10','00:20:06',
  '2011-09-07 23:08:22.8362'),('00:20:03.000000','00:20:07.00000',
  '2007-05-28 13:16:25','00:26:33','0000-00-00 00:00:00','01:04:48',
  '2011-09-07 23:08:24.8362'),('19:48:23.009935','00:20:07.00000',
  '2009-04-09 13:29:15','00:20:01',NULL,'21:43:24','2011-09-07 23:08:26.8362'),
('00:20:01.000000','22:27:30.00296','2009-03-21 23:00:46',NULL,'2003-01-23 14:57:31',
 '00:20:09','2011-09-07 23:08:28.8362'),('23:09:37.056340','07:01:38.05720',
 '2006-01-25 15:25:59','00:20:05',NULL,'05:10:32','2011-09-07 23:08:19.8362'),
('09:44:10.025082','00:20:07.00000','2009-01-06 18:48:29','00:20:02',
 '0000-00-00 00:00:00','00:20:00','2011-09-07 23:08:21.8362'),
('23:02:50.013380','22:48:12.05831',NULL,NULL,'2003-11-18 04:32:18','00:20:06',
 '2011-09-07 23:08:23.8362'),('00:20:01.000000','16:19:55.00007',
 '0000-00-00 00:00:00','07:55:21','0000-00-00 00:00:00','14:48:29',
 '2011-09-07 23:08:25.8362'),('06:00:36.034953','00:20:01.00000','2007-03-15 21:00:00',
 '00:20:01',NULL,'23:29:59','2011-09-07 23:08:27.8362');
SELECT col_time_5_key AS c1, col_time_key AS c2, col_timestamp AS c3 
FROM t1 
WHERE col_time_not_null
  NOT BETWEEN EXTRACT(YEAR_MONTH FROM '0000-00-00 00:00:00')
  AND COALESCE (col_time_6_not_null_key, col_datetime_key)
ORDER BY 1, 2;
DROP TABLE t1;

#
# Bug#24
#
SET @@timestamp=UNIX_TIMESTAMP('2011-09-21 10:20:30.123456');
CREATE TABLE t1 (
  col_time_5_not_null TIME(5) NOT NULL,
  col_timestamp_4_not_null_key TIMESTAMP(4) NOT NULL DEFAULT '0000-00-00 00:00:00.0000',
  col_timestamp_1 TIMESTAMP(1) NOT NULL DEFAULT '0000-00-00 00:00:00.0',
  pk TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00',
  col_date_key date DEFAULT NULL,
  col_time TIME DEFAULT NULL,
  col_timestamp TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00',
  col_time_3_not_null_key TIME(3) NOT NULL,
  PRIMARY KEY (pk),
  KEY col_timestamp_4_not_null_key (col_timestamp_4_not_null_key),
  KEY col_date_key (col_date_key),
  KEY col_time_3_not_null_key (col_time_3_not_null_key)
) ENGINE=InnoDB /*!50100 PARTITION BY KEY (pk)PARTITIONS 2 */;
INSERT INTO t1 VALUES (
  CURRENT_TIME(5),
  CURRENT_TIMESTAMP(4),
  CURRENT_TIMESTAMP(1),
  CURRENT_TIMESTAMP(),
  CURRENT_DATE(),
  CURRENT_TIME(),
  CURRENT_TIMESTAMP(),CURRENT_TIME());
SELECT col_time_3_not_null_key AS c1
FROM t1
WHERE col_timestamp <> col_date_key
  AND col_time <=> MAKETIME(24, 60, 4)
ORDER BY 1;
DROP TABLE t1;
SET @@timestamp=DEFAULT;

#
# Bug#24
#
SET @@timestamp=UNIX_TIMESTAMP('2011-09-21 09:30:40');
CREATE TABLE t1 (
  col_datetime_5 DATETIME(5) DEFAULT NULL,
  col_time_2_key TIME(2) DEFAULT NULL,
  col_time_3_not_null TIME(3) NOT NULL,
  col_timestamp_1 TIMESTAMP(1) NOT NULL DEFAULT '0000-00-00 00:00:00.0',
  col_time_4_key TIME(4) DEFAULT NULL,
  col_datetime_3_key DATETIME(3) DEFAULT NULL,
  col_time_not_null TIME NOT NULL,
  col_time_6_key TIME(6) DEFAULT NULL,
  col_time_5_not_null TIME(5) NOT NULL,
  col_datetime_2_not_null DATETIME(2) NOT NULL,
  col_datetime_4 DATETIME(4) DEFAULT NULL,
  col_time_5 TIME(5) DEFAULT NULL,
  col_datetime_3 DATETIME(3) DEFAULT NULL,
  col_timestamp_2_not_null_key TIMESTAMP(2) NOT NULL DEFAULT '0000-00-00 00:00:00.00',
  col_time_3_key TIME(3) DEFAULT NULL,
  col_time_6_not_null_key TIME(6) NOT NULL,
  col_timestamp_2_key TIMESTAMP(2) NOT NULL DEFAULT '0000-00-00 00:00:00.00',
  col_timestamp_3 TIMESTAMP(3) NOT NULL DEFAULT '0000-00-00 00:00:00.000',
  col_timestamp_6 TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00.000000',
  col_date date DEFAULT NULL,
  col_timestamp_3_not_null TIMESTAMP(3) NOT NULL DEFAULT '0000-00-00 00:00:00.000',
  col_datetime_1_not_null_key DATETIME(1) NOT NULL,
  KEY col_time_2_key (col_time_2_key),
  KEY col_time_4_key (col_time_4_key),
  KEY col_datetime_3_key (col_datetime_3_key),
  KEY col_time_6_key (col_time_6_key),
  KEY col_timestamp_2_not_null_key (col_timestamp_2_not_null_key),
  KEY col_time_3_key (col_time_3_key),
  KEY col_time_6_not_null_key (col_time_6_not_null_key),
  KEY col_timestamp_2_key (col_timestamp_2_key),
  KEY col_datetime_1_not_null_key (col_datetime_1_not_null_key)
);
INSERT INTO t1 VALUES
('0000-00-00 00:00:00.00000','00:20:08.00','00:20:01.000','0000-00-00 00:00:00.0','00:20:01.0000','0000-00-00 00:00:00.000','00:20:04','09:45:20.061990','00:20:09.00000','0000-00-00 00:00:00.00','0000-00-00 00:00:00.0000','00:20:04.00000','2001-05-23 09:36:42.052','2009-01-05 21:00:00.00','00:20:05.000','14:46:32.052047','2002-11-17 18:55:09.02','0000-00-00 00:00:00.000','2008-09-13 21:00:00.000000','2008-08-19','2007-02-10 21:00:00.000','0000-00-00 00:00:00.0'),
('2000-07-14 05:54:53.06239','00:20:05.00','00:20:05.000','1999-12-31 21:00:00.0','07:03:39.0208','2000-08-23 21:06:58.019','00:20:04','00:20:02.000000','15:44:52.01936','2005-11-17 00:00:00.00','2004-08-13 00:00:00.0000','06:06:48.02990','2006-09-05 02:09:02.053','2002-05-20 13:05:03.02','16:55:37.028','18:50:32.053805','2006-07-19 07:48:53.03','2007-03-18 04:55:22.063','2005-03-03 01:11:52.063987','2000-11-21','2006-01-03 23:43:37.061','2004-03-24 11:09:52.0'),
('0000-00-00 00:00:00.00000','18:28:33.04','00:20:01.000','2000-01-26 21:00:00.0','00:20:04.0000','0000-00-00 00:00:00.000','00:20:04','05:34:26.042699','00:40:37.02528','0000-00-00 00:00:00.00','2005-10-05 00:00:00.0000','23:03:38.03254','2002-07-10 00:00:00.000','2004-08-17 21:00:00.00','00:20:09.000','00:20:02.000000','2007-04-28 12:39:49.01','2009-10-19 21:00:00.000','2002-10-15 22:19:51.010640','2004-09-02','2000-06-03 21:00:00.000','2006-01-21 21:56:34.1'),
('2002-06-04 00:00:00.00000','17:18:15.01','00:20:07.000','0000-00-00 00:00:00.0','00:20:08.0000','0000-00-00 00:00:00.000','00:20:02','03:08:54.060553','07:16:55.00371','2004-05-23 00:18:17.06','2006-10-15 07:36:25.0250','21:17:33.03725','0000-00-00 00:00:00.000','2006-01-09 21:00:00.00','17:37:00.021','20:34:56.023570','2006-07-19 12:40:27.03','0000-00-00 00:00:00.000','0000-00-00 00:00:00.000000','0000-00-00','2001-02-17 08:11:26.052','0000-00-00 00:00:00.0'),
('2008-06-08 06:20:14.03026','00:20:02.00','00:20:09.000','2005-04-05 12:46:20.0','17:11:24.0634','2002-11-21 00:00:00.000','10:43:56','00:20:08.000000','00:20:07.00000','2008-05-13 10:13:26.06','2001-04-17 00:00:00.0000','01:47:22.02812','2009-09-16 14:44:14.026','0000-00-00 00:00:00.00','00:20:01.000','01:09:04.037569','2004-07-22 03:01:37.04','0000-00-00 00:00:00.000','2001-05-21 21:00:00.000000','0000-00-00','0000-00-00 00:00:00.000','0000-00-00 00:00:00.0'),
('0000-00-00 00:00:00.00000','00:20:08.00','12:51:21.056','0000-00-00 00:00:00.0','00:39:29.0565','2003-01-04 00:00:00.000','01:19:53','00:20:05.000000','17:19:59.03478','2001-07-12 00:00:00.00','2008-12-16 13:56:12.0187','00:21:45.00721','0000-00-00 00:00:00.000','0000-00-00 00:00:00.00','13:54:49.005','00:20:07.000000','2004-02-23 10:21:57.02','0000-00-00 00:00:00.000','0000-00-00 00:00:00.000000','2005-10-10','2000-07-10 21:37:07.038','0000-00-00 00:00:00.0'),
('0000-00-00 00:00:00.00000','23:11:02.06','00:20:04.000','2002-09-06 08:31:08.0','20:03:26.0050','0000-00-00 00:00:00.000','09:40:06','17:18:46.036378','01:21:25.03109','0000-00-00 00:00:00.00','2005-11-11 12:10:13.0077','00:20:09.00000','0000-00-00 00:00:00.000','0000-00-00 00:00:00.00','09:04:28.034','00:20:08.000000','0000-00-00 00:00:00.00','2009-07-28 08:02:12.002','0000-00-00 00:00:00.000000','2005-04-06','2006-06-09 07:10:23.064','0000-00-00 00:00:00.0'),
('0000-00-00 00:00:00.00000','00:20:09.00','04:15:43.040','2002-09-15 21:00:00.0','00:20:03.0000','0000-00-00 00:00:00.000','11:07:33','00:20:00.000000','00:20:09.00000','0000-00-00 00:00:00.00','0000-00-00 00:00:00.0000','04:06:54.01875','2001-12-06 21:09:30.042','2002-09-23 22:33:09.03','13:56:42.014','07:56:26.029465','0000-00-00 00:00:00.00','2001-05-19 19:41:58.064','2006-03-28 06:12:25.002048','0000-00-00','0000-00-00 00:00:00.000','0000-00-00 00:00:00.0'),
('2008-05-22 00:00:00.00000','21:46:17.04','00:20:00.000','2004-05-01 21:00:00.0','00:20:04.0000','0000-00-00 00:00:00.000','16:08:23','00:20:00.000000','00:39:12.04586','2001-08-25 00:00:00.00','2003-09-08 20:19:27.0197','16:01:03.03502','2005-02-14 00:00:00.000','0000-00-00 00:00:00.00','13:29:52.038','00:20:02.000000','2004-10-07 23:51:00.06','2009-02-03 23:21:26.017','0000-00-00 00:00:00.000000','0000-00-00','0000-00-00 00:00:00.000','2004-09-13 00:00:00.0'),
('2005-10-13 00:00:00.00000','00:20:08.00','18:41:54.050','2007-09-10 21:00:00.0','01:55:43.0424','0000-00-00 00:00:00.000','00:20:07','06:49:20.035790','00:20:04.00000','0000-00-00 00:00:00.00','2008-02-13 10:19:26.0346','00:20:01.00000','0000-00-00 00:00:00.000','0000-00-00 00:00:00.00','15:01:22.011','18:47:41.003944','2008-02-11 21:00:00.00','0000-00-00 00:00:00.000','2005-02-17 12:54:03.001418','2000-08-18','0000-00-00 00:00:00.000','2004-09-19 23:37:42.0');
SELECT col_timestamp_3 AS c1, col_date AS c2, CURTIME() AS c3
FROM t1
WHERE col_time_3_not_null NOT IN
  (col_time_not_null,
   CAST(col_time_5 AS DATETIME(3)),
   MAKEDATE(DAYOFMONTH('2002-09-13'), DATEDIFF('2009-12-15', col_timestamp_6)),
   CURRENT_DATE())
AND
  col_time_3_key NOT
  BETWEEN LEAST(CURRENT_TIME(), TIMESTAMPADD(HOUR, 187, MAKETIME(209, 60, 1)))
  AND CURTIME()
AND col_datetime_2_not_null NOT IN
  (DATE('2009-03-28'), col_timestamp_2_not_null_key,
   ADDDATE(col_timestamp_2_key, 28),
   col_datetime_3,
   UTC_DATE())
OR col_time_6_key IS NOT NULL
AND col_timestamp_3_not_null IN
  (col_timestamp_1, col_datetime_3_key, col_time_4_key)
OR col_time_6_not_null_key
  BETWEEN TIMESTAMP('0000-00-00', '16:50:23.062015')
  AND col_datetime_5
AND col_datetime_4 >= col_time_2_key
AND col_time_5_not_null
  BETWEEN col_timestamp_3_not_null
  AND col_datetime_1_not_null_key
ORDER BY col_time_4_key, col_datetime_3_key;
DROP TABLE t1;
SET @@timestamp=DEFAULT;

#
# Bug#28
#
CREATE TABLE t1 (
  col_datetime_1_key DATETIME(1) DEFAULT NULL,
  col_time_5_not_null_key TIME(5) NOT NULL,
  col_datetime_2_not_null DATETIME(2) NOT NULL,
  pk DATETIME(2) NOT NULL DEFAULT '0000-00-00 00:00:00.00',
  col_time_2_not_null_key TIME(2) NOT NULL,
  col_time_2_key TIME(2) DEFAULT NULL,
  col_timestamp_3_not_null TIMESTAMP(3) NOT NULL DEFAULT '0000-00-00 00:00:00.000',
  col_timestamp_4 TIMESTAMP(4) NOT NULL DEFAULT '0000-00-00 00:00:00.0000',
  PRIMARY KEY (pk),
  KEY col_datetime_1_key (col_datetime_1_key),
  KEY col_time_5_not_null_key (col_time_5_not_null_key),
  KEY col_time_2_not_null_key (col_time_2_not_null_key),
  KEY col_time_2_key (col_time_2_key)
)/*!50100 PARTITION BY KEY (pk)PARTITIONS 2 */;
INSERT INTO t1 VALUES ('2007-11-05 00:00:00.0','00:20:01.00000','0000-00-00 00:00:00.00','0000-00-00 00:00:00.00','00:20:08.00','07:54:36.06','0000-00-00 00:00:00.000','2008-02-19 21:00:00.0000'),('2001-09-22 23:10:01.0','00:20:00.00000','0000-00-00 00:00:00.00','2011-09-09 20:59:53.03','00:20:01.00','00:20:02.00','0000-00-00 00:00:00.000','2003-07-11 13:55:04.0379'),('2007-10-20 00:00:00.0','22:15:28.06072','0000-00-00 00:00:00.00','2011-09-09 20:59:54.03','00:20:03.00','00:20:07.00','0000-00-00 00:00:00.000','0000-00-00 00:00:00.0000'),('0000-00-00 00:00:00.0','00:20:03.00000','2009-01-27 00:00:00.00','2011-09-09 20:59:55.03','03:34:18.06','14:32:24.01','0000-00-00 00:00:00.000','2003-11-17 09:58:11.0160'),('2008-04-10 00:00:00.0','00:20:05.00000','2004-10-16 01:19:20.06','2011-09-09 20:59:56.03','21:14:08.05','02:32:00.04','2008-09-15 17:48:32.047','2003-01-16 21:00:00.0000'),('0000-00-00 00:00:00.0','00:20:03.00000','0000-00-00 00:00:00.00','2011-09-09 20:59:57.03','00:20:05.00','22:08:39.02','0000-00-00 00:00:00.000','2003-03-19 02:39:29.0637'),('2009-04-10 21:16:38.0','00:20:07.00000','2001-05-17 15:25:33.04','2011-09-09 20:59:58.03','00:20:05.00','09:36:28.04','2005-01-12 10:44:24.031','0000-00-00 00:00:00.0000'),('2001-06-10 20:23:08.0','07:38:25.03484','2002-06-22 00:00:00.00','2011-09-09 20:59:59.03','02:05:30.01','04:24:09.06','2002-05-25 21:00:00.000','2006-05-16 06:48:03.0399');
SELECT
  col_datetime_2_not_null AS c1,
  col_time_2_key AS c2,
  col_time_5_not_null_key AS c3 
FROM t1 
WHERE col_time_2_not_null_key NOT IN 
 (TIMESTAMP(MAKEDATE(19 + 0, 213)),
  MAKEDATE(46, DATEDIFF('0000-00-00 00:00:00', '0000-00-00 00:00:00')))
AND col_datetime_1_key IS NULL
OR col_timestamp_4 < col_timestamp_3_not_null 
ORDER BY 1;
DROP TABLE t1;

#
# Bug#30
#
SET @@timestamp=UNIX_TIMESTAMP('2011-09-21 10:20:30.123456');
CREATE TABLE t1 (
  col_time_1_not_null TIME(1) NOT NULL,
  col_time_6 TIME(6) DEFAULT NULL,
  col_datetime_6_not_null_key DATETIME(6) NOT NULL,
  col_timestamp_key TIMESTAMP NULL DEFAULT '0000-00-00 00:00:00',
  col_timestamp_2 TIMESTAMP(2) NULL DEFAULT '0000-00-00 00:00:00.00',
  col_timestamp_not_null TIMESTAMP NULL DEFAULT '0000-00-00 00:00:00',
  col_timestamp TIMESTAMP NULL DEFAULT '0000-00-00 00:00:00',
  pk DATETIME(3) NOT NULL DEFAULT '0000-00-00 00:00:00.000',
  col_time_6_not_null TIME(6) NOT NULL,
  col_datetime_3_not_null DATETIME(3) NOT NULL,
  PRIMARY KEY (pk),
  KEY col_datetime_6_not_null_key (col_datetime_6_not_null_key),
  KEY col_timestamp_key (col_timestamp_key)
) /*!50100 PARTITION BY KEY (pk)PARTITIONS 2 */;

INSERT INTO t1 VALUES (
  CURRENT_TIME(1),
  CURRENT_TIME(6),
  CURRENT_TIMESTAMP(6),
  CURRENT_TIMESTAMP(),
  CURRENT_TIMESTAMP(2),
  CURRENT_TIMESTAMP(),
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP(3),
  CURRENT_TIME(6),
  CURRENT_TIMESTAMP(3));

SELECT col_timestamp_not_null AS c1, col_datetime_6_not_null_key AS c2 
FROM t1 
WHERE col_timestamp IS NOT NULL
AND col_timestamp_2 <> col_time_1_not_null
AND col_timestamp_key BETWEEN col_datetime_3_not_null AND CURRENT_TIME()
OR col_time_6_not_null != LEAST(col_time_6, UTC_TIME());
DROP TABLE t1;
SET @@timestamp=DEFAULT;

#
# Bug#31
#
SET @@timestamp=UNIX_TIMESTAMP('2011-09-21 10:20:30.123456');
CREATE TABLE t1 (
  col_timestamp_2 TIMESTAMP(2) NOT NULL DEFAULT '0000-00-00 00:00:00.00',
  col_datetime_5 DATETIME(5) DEFAULT NULL,
  col_time_3 TIME(3) DEFAULT NULL,
  col_time_4_not_null TIME(4) NOT NULL,
  col_datetime_5_not_null DATETIME(5) NOT NULL,
  col_timestamp_5_key TIMESTAMP(5) NOT NULL DEFAULT '0000-00-00 00:00:00.00000',
  col_time_1_not_null_key TIME(1) NOT NULL,
  col_datetime DATETIME DEFAULT NULL,
  col_timestamp_4 TIMESTAMP(4) NOT NULL DEFAULT '0000-00-00 00:00:00.0000',
  KEY col_timestamp_5_key (col_timestamp_5_key),
  KEY col_time_1_not_null_key (col_time_1_not_null_key)
);
INSERT INTO t1 VALUES (
  CURRENT_TIMESTAMP(2),
  CURRENT_TIMESTAMP(5),
  CURRENT_TIME(3),
  CURRENT_TIME(4),
  CURRENT_TIMESTAMP(5),
  CURRENT_TIMESTAMP(5),
  CURRENT_TIME(1),
  CURRENT_TIMESTAMP(),
  CURRENT_TIMESTAMP(4));
SELECT col_datetime_5_not_null AS c1, col_time_1_not_null_key AS c2 
FROM t1 
WHERE
  col_time_4_not_null = MAKETIME(24, 60, 3 )
AND
  col_time_3 NOT IN (
    col_datetime_5,
    UTC_DATE(),
    col_timestamp_5_key, 
    ADDDATE('0000-00-00 00:00:00',
            DATEDIFF('2001-09-21',FROM_UNIXTIME(1018888192,
                                                CONCAT_WS('-','%s','%V','%u')))),
    col_timestamp_4)
AND
  col_timestamp_2 NOT BETWEEN col_datetime AND '2005-09-12' 
ORDER BY 1;
SELECT col_datetime_5_not_null AS c1, col_time_1_not_null_key AS c2 
FROM t1 
WHERE col_time_4_not_null=MAKETIME(24,60, 3)
AND col_time_3 NOT IN
  (col_datetime_5, UTC_DATE(), col_timestamp_5_key,  col_timestamp_4)
AND
  col_timestamp_2 NOT BETWEEN col_datetime AND '2005-09-12' 
ORDER BY 1;
DROP TABLE t1;
SET @@timestamp=DEFAULT;

#
# Bug#32
#
SET @@timestamp=UNIX_TIMESTAMP('2011-09-21 10:20:30.123456');
CREATE TABLE t1 (
  col_time_4_key TIME(4) DEFAULT NULL,
  col_datetime_4_not_null DATETIME(4) NOT NULL,
  col_timestamp_5 TIMESTAMP(5) NOT NULL DEFAULT '0000-00-00 00:00:00.00000',
  col_datetime_not_null_key DATETIME NOT NULL,
  col_timestamp_key TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00',
  KEY col_time_4_key (col_time_4_key),
  KEY col_datetime_not_null_key (col_datetime_not_null_key),
  KEY col_timestamp_key (col_timestamp_key)
);
INSERT INTO t1 VALUES ('00:20:08.0000','0000-00-00 00:00:00.0000','0000-00-00 00:00:00.00000','0000-00-00 00:00:00','2006-10-24 21:00:00'),('19:24:23.0232','2006-12-11 22:04:06.0393','2004-12-08 21:00:00.00000','2009-06-04 03:01:35','2003-08-19 21:00:00');
SELECT UNIX_TIMESTAMP(col_datetime_4_not_null) AS c1
FROM t1
WHERE col_time_4_key NOT
  BETWEEN '2001-07-06'
  AND LEAST(FROM_DAYS(col_timestamp_key), col_timestamp_5)
OR CONVERT_TZ(MAKETIME(24, 5, 7), 'Japan', 'Japan') IS NULL
OR CURRENT_TIMESTAMP() IS NOT NULL
OR col_datetime_not_null_key >= UTC_TIME()
ORDER BY 1 ;
DROP TABLE t1;
SET @@timestamp=DEFAULT;

#
# Bug#33
#
CREATE TABLE t1 (
  col_time_not_null_key TIME NOT NULL,
  col_datetime_5_not_null_key DATETIME(5) NOT NULL,
  col_timestamp_6_key TIMESTAMP(6) NOT NULL DEFAULT '0000-00-00 00:00:00.000000',
  col_time_1 TIME(1) DEFAULT NULL,
  pk DATETIME(1) NOT NULL DEFAULT '0000-00-00 00:00:00.0',
  col_time_2_key TIME(2) DEFAULT NULL,
  PRIMARY KEY (pk),
  KEY col_time_not_null_key (col_time_not_null_key),
  KEY col_datetime_5_not_null_key (col_datetime_5_not_null_key),
  KEY col_timestamp_6_key (col_timestamp_6_key),
  KEY col_time_2_key (col_time_2_key)
)/*!50100 PARTITION BY KEY (pk)PARTITIONS 2 */;
INSERT INTO t1 VALUES ('00:20:02','2007-08-09 05:14:12.05054','2007-06-20 21:00:00.000000','06:16:36.0','2011-09-09 21:37:45.2','19:15:37.02'),('18:00:14','0000-00-00 00:00:00.00000','2000-12-10 12:35:01.029761','00:20:04.0','2011-09-09 21:37:46.2','06:21:03.03');
SELECT col_time_1 AS c1 
FROM t1 
WHERE col_time_not_null_key <= col_timestamp_6_key
AND col_time_2_key > SUBTIME('2006-07-16', '05:05:02.040778')
ORDER BY col_datetime_5_not_null_key, col_time_1;
DROP TABLE t1;

#
# Bug#35
#
CREATE TABLE t1 (
  col_timestamp_3_not_null_key timestamp(3) NULL DEFAULT '0000-00-00 00:00:00.000',
  pk time(1) NOT NULL DEFAULT '00:00:00.0',
  col_datetime_6 datetime(6) DEFAULT NULL,
  col_time_5_not_null_key time(5) NOT NULL,
  col_timestamp_1_not_null_key timestamp(1) NULL DEFAULT '0000-00-00 00:00:00.0',
  col_datetime_4_key datetime(4) DEFAULT NULL,
  col_time_2_not_null_key time(2) NOT NULL,
  PRIMARY KEY (pk),
  KEY col_timestamp_3_not_null_key (col_timestamp_3_not_null_key),
  KEY col_time_5_not_null_key (col_time_5_not_null_key),
  KEY col_timestamp_1_not_null_key (col_timestamp_1_not_null_key),
  KEY col_datetime_4_key (col_datetime_4_key),
  KEY col_time_2_not_null_key (col_time_2_not_null_key)
) ENGINE=InnoDB DEFAULT CHARSET=latin1
/*!50100 PARTITION BY KEY (pk)PARTITIONS 2 */;
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000','14:09:27.6','0000-00-00 00:00:00.000000','18:38:36.04289','2003-07-01 21:00:00.0','2009-09-15 09:25:49.0110','15:47:52.01');
INSERT INTO t1 VALUES ('2004-04-08 23:25:16.012','14:09:26.6',NULL,'05:45:23.05509','2008-12-11 21:00:00.0','2001-04-23 20:41:27.0470','05:09:23.04');
--disable_result_log
SELECT SUBTIME(col_timestamp_3_not_null_key , SYSDATE()) AS c1 FROM t1 
WHERE col_timestamp_1_not_null_key NOT IN 
(col_datetime_4_key , GREATEST(DATE('2006-04-26'), UTC_DATE()))
ORDER BY col_datetime_6 , col_time_2_not_null_key , col_time_5_not_null_key;
--enable_result_log
DROP TABLE t1;

#
# Bug#36
#
CREATE TABLE t1 (
  col_time_1_not_null_key time(1) NOT NULL,
  pk timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  col_datetime_3_not_null_key datetime(3) NOT NULL,
  col_time_2_key time(2) DEFAULT NULL,
  PRIMARY KEY (pk),
  KEY col_time_1_not_null_key (col_time_1_not_null_key),
  KEY col_datetime_3_not_null_key (col_datetime_3_not_null_key),
  KEY col_time_2_key (col_time_2_key)
) DEFAULT CHARSET=latin1
/*!50100 PARTITION BY KEY (pk)PARTITIONS 2 */;
INSERT INTO t1 VALUES ('00:20:05.0','1999-12-31 21:00:01','0000-00-00 00:00:00.000','04:25:59.01');
INSERT INTO t1 VALUES ('00:15:56.0','1999-12-31 21:00:03','0000-00-00 00:00:00.000','00:20:06.00');
INSERT INTO t1 VALUES ('00:20:03.0','1999-12-31 21:00:05','2002-10-18 16:08:58.006','00:20:00.00');
INSERT INTO t1 VALUES ('00:20:09.0','1999-12-31 21:00:07','2002-11-11 17:21:02.045','18:39:12.05');
INSERT INTO t1 VALUES ('13:51:22.0','1999-12-31 21:00:09','2002-08-07 10:27:35.055','00:20:07.00');
INSERT INTO t1 VALUES ('00:20:06.0','0000-00-00 00:00:00','2003-08-12 00:00:00.000','20:35:34.01');
--disable_result_log
SELECT col_time_1_not_null_key AS c1
FROM t1
WHERE col_datetime_3_not_null_key NOT IN
(LEAST( DATE('0000-00-00 00:00:00'), CURDATE()), col_time_2_key);
--enable_result_log
DROP TABLE t1;

#
# Bug#13354645 CRASH IN ITEM_TEMPORAL_HYBRID_FUNC
#
SET timestamp=UNIX_TIMESTAMP('2001-11-07 15:13:00');
CREATE TABLE t1 (
  col_time_3_not_null time(3) NOT NULL,
  col_datetime_4_not_null_key datetime(4) NOT NULL,
  col_datetime_key datetime DEFAULT NULL,
  col_time_1_key time(1) DEFAULT NULL,
  col_time_5 time(5) DEFAULT NULL,
  col_timestamp_6_not_null_key timestamp(6) NULL DEFAULT '0000-00-00 00:00:00.000000',
  pk datetime(6) NOT NULL DEFAULT '0000-00-00 00:00:00.000000',
  PRIMARY KEY (pk),
  KEY col_datetime_4_not_null_key (col_datetime_4_not_null_key),
  KEY col_datetime_key (col_datetime_key),
  KEY col_time_1_key (col_time_1_key),
  KEY col_timestamp_6_not_null_key (col_timestamp_6_not_null_key));
INSERT INTO t1 VALUES ('12:52:24.022','0000-00-00 00:00:00.0000','2003-05-24 00:00:00','00:20:09.0','15:11:20.03463','0000-00-00 00:00:00.000000','2011-11-07 12:01:35.111932');
INSERT INTO t1 VALUES ('18:52:49.043','2000-10-13 00:00:00.0000','2005-07-22 00:00:00',NULL,'08:19:02.03845','2000-09-20 03:26:09.009008','2011-11-07 12:01:36.111932');
INSERT INTO t1 VALUES ('23:46:05.010','2005-12-27 04:06:15.0087','0000-00-00 00:00:00','23:40:13.0','08:30:24.03114','0000-00-00 00:00:00.000000','2011-11-07 12:01:37.111932');
INSERT INTO t1 VALUES ('00:20:05.000','2009-06-01 08:01:58.0532','2007-03-26 12:18:48','00:20:03.0','20:32:22.04519','2000-08-19 21:00:00.000000','2011-11-07 12:01:38.111932');
INSERT INTO t1 VALUES ('00:20:05.000','2002-05-01 00:00:00.0000','0000-00-00 00:00:00','04:31:10.0','00:20:04.00000','0000-00-00 00:00:00.000000','2011-11-07 12:01:39.111932');
INSERT INTO t1 VALUES ('00:20:06.000','2007-07-25 22:20:58.0023','0000-00-00 00:00:00','00:23:16.1',NULL,'2008-03-19 16:17:15.042024','2011-11-07 12:01:40.111932');
INSERT INTO t1 VALUES ('02:55:32.040','0000-00-00 00:00:00.0000',NULL,'00:20:02.0','08:45:57.00998','2009-06-25 10:21:16.011345','2011-11-07 12:01:41.111932');
INSERT INTO t1 VALUES ('00:20:06.000','2009-01-07 01:12:15.0324','2000-05-26 00:00:00','17:41:45.1','00:20:07.00000','0000-00-00 00:00:00.000000','2011-11-07 12:01:42.111932');
INSERT INTO t1 VALUES ('12:01:30.061','2009-04-01 00:00:00.0000','2001-05-08 05:31:04','07:43:41.1','11:12:29.03251','2009-12-22 17:45:46.040987','2011-11-07 12:01:43.111932');
INSERT INTO t1 VALUES ('00:16:22.057','2005-05-10 00:18:58.0345',NULL,'02:24:15.0',NULL,'2008-02-14 20:55:46.023678','2011-11-07 12:01:44.111932');
INSERT INTO t1 VALUES ('06:54:17.018','2002-12-27 12:28:03.0382','0000-00-00 00:00:00','19:19:54.0','01:42:30.03406','0000-00-00 00:00:00.000000','2011-11-07 12:01:45.111932');
INSERT INTO t1 VALUES ('00:20:04.000','2007-03-11 00:00:00.0000','0000-00-00 00:00:00',NULL,'13:12:03.05778','2005-02-19 02:31:13.046418','2011-11-07 12:01:46.111932');
INSERT INTO t1 VALUES ('00:20:01.000','2009-08-13 00:00:00.0000','0000-00-00 00:00:00','00:20:02.0',NULL,'2008-05-21 10:53:59.004633','2011-11-07 12:01:47.111932');
INSERT INTO t1 VALUES ('02:44:13.025','2001-07-02 14:06:37.0411','2009-04-01 00:43:45','00:20:07.0','16:15:02.01279','2004-07-28 17:34:20.031118','2011-11-07 12:01:48.111932');
INSERT INTO t1 VALUES ('14:51:02.019','2006-02-24 04:12:05.0014','0000-00-00 00:00:00','00:20:08.0','00:20:09.00000','2001-04-25 21:00:00.000000','2011-11-07 12:01:49.111932');
SELECT
  col_timestamp_6_not_null_key AS c1,
  col_datetime_key AS c2,
  UTC_DATE() AS c3 
FROM t1
WHERE col_time_1_key BETWEEN
  TIMESTAMPADD(MONTH, 38 ,CONVERT_TZ( DATE(MAKEDATE(207, 38 )), '+00:00','+04:00'))
  AND LOCALTIMESTAMP()
ORDER BY col_datetime_4_not_null_key , col_time_5 , col_time_3_not_null;
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # Bug#13359097 ASSERT FAILURE IN MAKE_SORTKEY
--echo #
SET timestamp=UNIX_TIMESTAMP('2011-11-01 17:48:00');
CREATE TABLE t1 (
  pk time(6) NOT NULL DEFAULT '00:00:00.000000',
  col_date_not_null date NOT NULL,
  PRIMARY KEY (pk)
);
INSERT INTO t1 VALUES ('00:00:00.000000','2001-09-10');
INSERT INTO t1 VALUES ('12:07:41.148239','2000-05-23');
SELECT GREATEST('2004-04-07', COALESCE (pk, col_date_not_null)) FROM  t1 ORDER BY 1;
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # BUG 13385275 - ASSERT FAILURE IN TIME_FROM_LONGLONG_PACKED 
--echo #
CREATE TABLE t1 (
  col_date_not_null_key date NOT NULL,
  KEY col_date_not_null_key (col_date_not_null_key));
INSERT INTO t1 VALUES ('0000-00-00'), ('2000-11-20'); 
SELECT ADDTIME(GREATEST(col_date_not_null_key, TIMESTAMP '2007-03-11 08:51:19.009408'),
              '12:20:40.029940')
FROM t1; 
DROP TABLE t1;

--echo #
--echo # BUG#13386657 ASSERT FAILURE IN ITEM::GET_DATE_FROM_NON_TEMPORAL
--echo #
CREATE TABLE t1 (
  col_time_5_not_null_key time(5) NOT NULL,
  col_datetime_4 datetime(4) DEFAULT NULL,
  col_timestamp_1_not_null_key timestamp(1) NULL DEFAULT '0000-00-00 00:00:00.0',
  col_datetime_3_not_null_key datetime(3) NOT NULL,
  KEY col_time_5_not_null_key (col_time_5_not_null_key),
  KEY col_timestamp_1_not_null_key (col_timestamp_1_not_null_key),
  KEY col_datetime_3_not_null_key (col_datetime_3_not_null_key));
INSERT INTO t1 VALUES ('15:01:38.00004','0000-00-00 00:00:00.0000','2005-06-08 16:51:23.0','0000-00-00 00:00:00.000');
SELECT col_datetime_4 
FROM t1
WHERE col_datetime_3_not_null_key IN
(COALESCE (col_timestamp_1_not_null_key), col_time_5_not_null_key);
DROP TABLE t1;
CREATE TABLE t1 (a TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3));
INSERT INTO t1 VALUES ('2001-01-01 10:20:30.999');
SELECT CAST(COALESCE(a,a) AS CHAR) FROM t1;
SELECT CAST(COALESCE(a,a) AS SIGNED) FROM t1;
SELECT CAST(COALESCE(a,a) AS DECIMAL(25,3)) FROM t1;
SELECT CAST(COALESCE(a,a) AS DATETIME(6)) FROM t1;
SELECT CAST(COALESCE(a,a) AS TIME(6)) FROM t1;
SELECT ROUND(COALESCE(a,a)) FROM t1;
DROP TABLE t1;

--echo #
--echo # Bug#13391370 ASSERT FAILURE IN TIME_TO_LONGLONG_DATETIME_PACKED
--echo #
SET timestamp=UNIX_TIMESTAMP('2011-11-15 16:25:30');
CREATE TABLE t1 (
  col_time_2_not_null time(2) NOT NULL,  
  col_timestamp_1_not_null timestamp(1) NULL DEFAULT '0000-00-00 00:00:00.0',  
  col_datetime_1 datetime(1) DEFAULT NULL,
  col_timestamp_4_not_null timestamp(4) NULL DEFAULT '0000-00-00 00:00:00.0000',  
  col_timestamp_6_not_null_key timestamp(6) NULL DEFAULT '0000-00-00 00:00:00.000000',
  col_date date DEFAULT NULL,
  KEY col_timestamp_6_not_null_key (col_timestamp_6_not_null_key));
DELETE FROM t1;
INSERT INTO t1 VALUES ('00:20:09.00','2008-02-09 00:19:47.1','0000-00-00 00:00:00.0','2009-03-13 00:56:44.0401','2008-10-17 09:43:02.051040','2000-11-13');
INSERT INTO t1 VALUES ('00:20:04.00','0000-00-00 00:00:00.0','0000-00-00 00:00:00.0','0000-00-00 00:00:00.0000','0000-00-00 00:00:00.000000','2006-11-14');
INSERT INTO t1 VALUES ('15:54:14.06','2003-04-19 04:55:47.0',NULL,'0000-00-00 00:00:00.0000','2009-12-27 02:49:14.005722','0000-00-00');
INSERT INTO t1 VALUES ('04:58:16.02','2004-02-17 21:00:00.0',NULL,'0000-00-00 00:00:00.0000','2001-10-07 11:02:50.054375','2000-04-10');
INSERT INTO t1 VALUES ('10:34:28.04','2006-10-16 21:00:00.0',NULL,'0000-00-00 00:00:00.0000','2008-05-17 05:14:50.028683','0000-00-00');
INSERT INTO t1 VALUES ('14:17:38.04','2001-06-24 04:33:25.0','2008-09-05 21:25:39.0','0000-00-00 00:00:00.0000','0000-00-00 00:00:00.000000',NULL);
INSERT INTO t1 VALUES ('00:20:06.00','2005-06-26 22:52:46.0','0000-00-00 00:00:00.0','2000-09-11 02:51:17.0211','0000-00-00 00:00:00.000000','2006-10-10');
INSERT INTO t1 VALUES ('00:09:41.03','0000-00-00 00:00:00.0','2008-02-16 19:59:19.0','2000-10-21 21:00:00.0000','2006-11-14 10:22:27.009969','0000-00-00');
INSERT INTO t1 VALUES ('16:57:08.01','2009-05-13 21:03:39.0','2004-01-12 11:46:42.0','2000-02-28 10:51:01.0152','2001-05-10 14:21:38.022082',NULL);
INSERT INTO t1 VALUES ('19:02:15.03','0000-00-00 00:00:00.0','2009-07-16 20:15:38.1','0000-00-00 00:00:00.0000','0000-00-00 00:00:00.000000',NULL);
SELECT
    SUBTIME('2002-04-08 06:05:42.056589', '01:26:40.063385') AS c1
FROM
    t1
WHERE
  col_timestamp_6_not_null_key IN (
        LEAST(col_datetime_1, MAKETIME(24,8,0),
              ADDTIME(NOW(),'06:07:21.061946'), col_time_2_not_null),
        DATE ('2005-11-03'),
        CURTIME(),
        COALESCE(col_timestamp_4_not_null, TIMESTAMP('2003-09-10')))
ORDER BY
    col_date,
    col_timestamp_1_not_null;
DROP TABLE t1;
CREATE TABLE t1 (a datetime(1));
INSERT INTO t1 VALUES ('2006-11-14 10:22:27.009969');
SELECT * FROM t1
WHERE timestamp'2006-11-14 10:22:27.009969' IN (LEAST(a, MAKETIME(24,8,0)));
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # Bug#13384069 - EMBEDED UPDATE WITH CAST AND DATETIME WITH FSP DOES NOT WORK
--echo #
CREATE TABLE t1 (
  pk timestamp(2) NOT NULL DEFAULT '0000-00-00 00:00:00.00',
  col_datetime_6 datetime(6) DEFAULT NULL,
  PRIMARY KEY (pk));
INSERT INTO t1 VALUES ('2011-11-11 17:59:41.12','0000-00-00 00:00:00.000000');
SELECT col_datetime_6, CAST(col_datetime_6 AS DATETIME(2)) AS c1 FROM t1;
CREATE TABLE t2 AS SELECT CAST(col_datetime_6 AS DATETIME(2)) AS c1 FROM t1;
SELECT * FROM t2;
UPDATE t2 SET c1=(SELECT CAST(col_datetime_6 AS DATETIME(2)) AS c1 FROM t1);
SELECT * FROM t2;
DROP TABLE t1, t2;

CREATE TABLE t1 (a DATE);
INSERT INTO t1 VALUES ('0000-00-00');
SELECT CAST(COALESCE(a,a) AS SIGNED) FROM t1;
SELECT CAST(COALESCE(a,a) AS CHAR) FROM t1;
SELECT CAST(COALESCE(a,a) AS DECIMAL(25,3)) FROM t1;
SELECT CAST(COALESCE(a,a) AS DATETIME(6)) FROM t1;
SELECT CAST(COALESCE(a,a) AS TIME(6)) FROM t1;
SELECT ROUND(COALESCE(a,a)) FROM t1;
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME);
INSERT INTO t1 VALUES ('0000-00-00 00:00:00');
SELECT CAST(COALESCE(a,a) AS SIGNED) FROM t1;
SELECT CAST(COALESCE(a,a) AS CHAR) FROM t1;
SELECT CAST(COALESCE(a,a) AS DECIMAL(25,3)) FROM t1;
SELECT CAST(COALESCE(a,a) AS DATETIME(6)) FROM t1;
SELECT CAST(COALESCE(a,a) AS TIME(6)) FROM t1;
SELECT ROUND(COALESCE(a,a)) FROM t1;
DROP TABLE t1;

--echo #
--echo # Bug#13383838 - DELETE DOESN'T EMPTY RECORDS WHEN CAST WITH DATETIME TYPE IN EMBEDED SELECT
--echo #
CREATE TABLE t1 (
  pk timestamp(2) NOT NULL DEFAULT '0000-00-00 00:00:00.00',
  col_datetime_6 datetime(6) DEFAULT NULL,
  PRIMARY KEY (pk));
INSERT INTO t1 VALUES ('2011-11-11 17:59:41.12','0000-00-00 00:00:00.000000');
CREATE TABLE t2 SELECT CAST(col_datetime_6 AS DATETIME) AS c1 FROM t1;
UPDATE t2 SET c1=(SELECT CAST(col_datetime_6 AS DATETIME) AS c1 FROM t1)
WHERE c1 IN (SELECT CAST(col_datetime_6 AS DATETIME) AS c1 FROM t1);
SELECT * FROM t2 WHERE c1 IN  (SELECT CAST( col_datetime_6 AS DATETIME )  AS c1  FROM t1);
DELETE FROM t2 WHERE c1 IN  (SELECT CAST( col_datetime_6 AS DATETIME )  AS c1  FROM t1);
SELECT COUNT(*) FROM t2;
DROP TABLE t1, t2;
CREATE TABLE t1 (
  pk timestamp(2) NOT NULL DEFAULT '0000-00-00 00:00:00.00',
  col_date date DEFAULT NULL,
  PRIMARY KEY (pk));
INSERT INTO t1 VALUES ('2011-11-11 17:59:41.12','0000-00-00');
CREATE TABLE t2 SELECT CAST(col_date AS DATE) AS c1 FROM t1;
UPDATE t2 SET c1=(SELECT CAST(col_date AS DATE) AS c1 FROM t1)
WHERE c1 IN (SELECT CAST(col_date AS DATE) AS c1 FROM t1);
SELECT * FROM t2 WHERE c1 IN (SELECT CAST(col_date AS DATE) AS c1 FROM t1);
DELETE FROM t2 WHERE c1 IN (SELECT CAST(col_date AS DATE) AS c1  FROM t1);
SELECT COUNT(*) FROM t2;
DROP TABLE t1, t2;

--echo #
--echo # Bug#13392141 ASSERT FAILURE IN SEC_SINCE_EPOCH
--echo #
SELECT CONVERT_TZ(TIMESTAMP'2021-00-00 00:00:00', '+00:00', '+7:5');
SELECT CONVERT_TZ(DATE'2021-00-00', '+00:00', '+7:5');
SELECT CONVERT_TZ(GREATEST(DATE('2021-00-00'),DATE('2021-00-00')),'+00:00','+7:5');

#
# Bug 13392141 - ASSERT FAILURE IN SEC_SINCE_EPOCH
#
SET timestamp=UNIX_TIMESTAMP('2011-11-16 17:28:30');
DROP TABLE IF EXISTS t1;
CREATE TABLE t1 (
  col_timestamp_2_not_null_key timestamp(2) NULL DEFAULT '0000-00-00 00:00:00.00',
  pk time NOT NULL DEFAULT '00:00:00',
  PRIMARY KEY (pk),
  KEY col_timestamp_2_not_null_key (col_timestamp_2_not_null_key));
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:49:56');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:49:58');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:00');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:02');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:03');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:05');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:12');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:14');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:18');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:19');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:23');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:24');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:28');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:30');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:34');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:35');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:37');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:38');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:46');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:47');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:50');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.00','18:50:52');
INSERT INTO t1 VALUES ('2000-05-24 21:00:00.00','18:50:11');
INSERT INTO t1 VALUES ('2000-08-09 13:04:19.05','18:50:32');
INSERT INTO t1 VALUES ('2000-10-05 09:01:10.01','18:50:42');
INSERT INTO t1 VALUES ('2000-12-08 03:00:13.05','18:50:04');
INSERT INTO t1 VALUES ('2001-03-02 07:52:23.01','18:50:44');
INSERT INTO t1 VALUES ('2001-04-24 10:01:32.05','18:50:49');
INSERT INTO t1 VALUES ('2003-02-18 14:31:23.06','18:50:39');
INSERT INTO t1 VALUES ('2003-03-07 21:00:00.00','18:50:29');
INSERT INTO t1 VALUES ('2003-04-16 21:00:00.00','18:50:17');
INSERT INTO t1 VALUES ('2003-08-10 21:00:00.00','18:50:36');
INSERT INTO t1 VALUES ('2003-11-24 06:30:37.06','18:49:53');
INSERT INTO t1 VALUES ('2003-12-01 21:00:00.00','18:50:41');
INSERT INTO t1 VALUES ('2004-05-16 19:56:24.05','18:50:31');
INSERT INTO t1 VALUES ('2004-05-27 18:45:05.06','18:50:08');
INSERT INTO t1 VALUES ('2004-06-05 22:51:19.04','18:50:20');
INSERT INTO t1 VALUES ('2004-10-16 21:00:00.00','18:49:54');
INSERT INTO t1 VALUES ('2004-11-02 21:00:00.00','18:50:51');
INSERT INTO t1 VALUES ('2005-02-23 00:35:02.05','18:50:07');
INSERT INTO t1 VALUES ('2005-08-15 16:33:48.01','18:50:43');
INSERT INTO t1 VALUES ('2006-03-14 21:15:50.03','18:50:33');
INSERT INTO t1 VALUES ('2006-04-24 18:46:21.06','18:50:01');
INSERT INTO t1 VALUES ('2006-05-14 01:54:29.01','18:50:40');
INSERT INTO t1 VALUES ('2006-08-23 22:33:51.02','18:50:06');
INSERT INTO t1 VALUES ('2006-11-15 02:06:29.02','18:49:59');
INSERT INTO t1 VALUES ('2006-11-28 11:32:09.03','18:50:15');
INSERT INTO t1 VALUES ('2007-01-19 18:45:01.00','18:50:25');
INSERT INTO t1 VALUES ('2007-01-23 18:04:25.01','18:50:27');
INSERT INTO t1 VALUES ('2007-06-20 18:42:45.01','18:50:48');
INSERT INTO t1 VALUES ('2007-08-27 21:00:00.00','18:50:22');
INSERT INTO t1 VALUES ('2007-10-11 09:54:15.02','18:50:45');
INSERT INTO t1 VALUES ('2008-01-24 18:55:00.04','18:50:10');
INSERT INTO t1 VALUES ('2008-03-03 16:06:21.03','18:50:16');
INSERT INTO t1 VALUES ('2008-03-23 21:00:00.00','18:50:26');
INSERT INTO t1 VALUES ('2008-05-28 10:12:34.06','18:49:57');
INSERT INTO t1 VALUES ('2009-06-09 21:31:23.04','18:50:21');
INSERT INTO t1 VALUES ('2009-06-14 09:15:36.06','18:50:09');
INSERT INTO t1 VALUES ('2009-06-30 23:31:57.01','18:49:55');
INSERT INTO t1 VALUES ('2009-12-25 17:54:17.00','18:50:13');
INSERT INTO t1 VALUES ('2005-12-15 18:49:06.05','00:00:00');
SELECT col_timestamp_2_not_null_key
FROM t1
WHERE CONVERT_TZ(GREATEST('2005-11-05 18:16:50.055749', STR_TO_DATE(CURRENT_TIME, '%Y')),
              '+00:00', CONCAT('+', CONCAT_WS(':', 7, 5)));
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # Bug#13365131 - VALGRIND: CONDITIONAL JUMP OR MOVE DEPENDS ON UNINITIALISED IN MY_TIME_COMPARE
--echo #
CREATE TABLE t1 (
  col_time_not_null_key time not null,
  col_timestamp_6_not_null_key timestamp(6) not null NULL DEFAULT 0,
  col_datetime_6_not_null_key datetime(6) not null,
  col_datetime_6_key datetime(6),
  col_time_3_not_null_key time(3) not null,
  col_datetime_3_key  datetime(3),
key (col_datetime_6_not_null_key),
key (col_datetime_3_key)) ENGINE=innodb;
CREATE OR REPLACE VIEW v1 AS SELECT * FROM t1;
SELECT col_datetime_3_key AS c1 FROM v1
WHERE SYSDATE() IS NULL
OR col_datetime_6_not_null_key < TIMEDIFF('0000-00-00','2002-11-24' )
AND col_timestamp_6_not_null_key IS NULL;
DROP VIEW v1;
DROP TABLE t1;


--echo #
--echo # Bug#13401354 ITEM->FIELD_TYPE() == MYSQL_TYPE_TIME || ITEM->FIELD_TYPE() == MYSQL_TYPE_NULL
--echo #
CREATE TABLE t1 (col_time_not_null TIME NOT NULL);
SELECT * FROM t1
WHERE  col_time_not_null = DATE(NULL) AND col_time_not_null = TIME(NULL);
DROP TABLE t1;
CREATE TABLE t1 (
  col_date DATE,
  col_time_not_null TIME not null,
  pk TIMESTAMP(6),
  col_timestamp_3_not_null_key TIMESTAMP(3) NOT NULL,
  col_date_not_null DATE NOT NULL,
  col_time_3_key TIME(3),
  col_timestamp_6_key TIMESTAMP(6) NOT NULL,
  col_datetime DATETIME,
  col_datetime_6 DATETIME(6),
  col_timestamp_3_key TIMESTAMP(3) NOT NULL,
  col_time_6_not_null TIME(6) NOT NULL,
  col_time_6 TIME(6),
  col_time_6_not_null_key TIME(6) NOT NULL,
  col_datetime_not_null DATETIME NOT NULL,
  col_datetime_3_not_null_key DATETIME(3) NOT NULL,
  KEY (col_datetime_3_not_null_key));
SELECT col_time_6 AS c1, NOW() AS c2 FROM t1
WHERE
  col_datetime_not_null BETWEEN SUBTIME('0000-00-00','00:00:00') AND col_date_not_null
AND
  col_time_not_null=STR_TO_DATE(DATE_FORMAT(TIME('00:00:00.000000'),CONCAT_WS('-','%s','%k' )),CONCAT_WS(':','%i','%M'))
AND
  col_date BETWEEN UNIX_TIMESTAMP('0000-00-00 00:00:00') AND col_timestamp_3_key
AND
  col_datetime_6 NOT IN (col_time_6_not_null,'0000-00-00')
AND
  col_time_not_null = TIMEDIFF(CURDATE(),'00:00:00.000000')
AND
  col_time_3_key NOT IN (col_timestamp_6_key,pk)
AND
  col_timestamp_3_not_null_key < col_datetime; 
DROP TABLE t1;


--echo #
--echo # Bug#13399082 - ASSERTION `MON > 0 && MON < 13' FAILED IN TZTIME.CC | SEC_SINCE_EPOCH
--echo #
SET TIME_ZONE='+02:00';
SELECT UNIX_TIMESTAMP(STR_TO_DATE('2020','%Y'));
SET TIME_ZONE=DEFAULT;

--echo # This should return NULL
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES(NULL);
SELECT UNIX_TIMESTAMP(a) FROM t1;
DROP TABLE t1;
SELECT UNIX_TIMESTAMP(NULL);
SELECT UNIX_TIMESTAMP(DATE(NULL));
SELECT UNIX_TIMESTAMP(TIME(NULL));
SELECT UNIX_TIMESTAMP(TIMESTAMP(NULL));
SELECT CONCAT(UNIX_TIMESTAMP(NULL));
SELECT CAST(UNIX_TIMESTAMP(NULL) AS SIGNED);
SELECT CAST(UNIX_TIMESTAMP(NULL) AS DECIMAL(23,6));

--echo # This should return 0
CREATE TABLE t1 (a VARCHAR(30));
INSERT INTO t1 VALUES
('0000-01-01 00:00:00'),('2001-00-01 00:00:01'),('2001-01-00 00:00:00'),
('0000-00-00 00:00:01'),('0000-00-00 00:00:00.1');
SELECT UNIX_TIMESTAMP(a) FROM t1;
DROP TABLE t1;
SELECT UNIX_TIMESTAMP('0000-01-01 00:00:00');
SELECT UNIX_TIMESTAMP('2001-00-01 00:00:00');
SELECT UNIX_TIMESTAMP('2001-01-00 00:00:00');
SELECT UNIX_TIMESTAMP('0000-00-00 10:10:20');
SELECT UNIX_TIMESTAMP('0000-00-00 00:00:00.1');
SELECT UNIX_TIMESTAMP(TIMESTAMP'0000-00-00 10:10:20');
SELECT UNIX_TIMESTAMP(TIMESTAMP'0000-00-00 00:00:00.1');
SELECT UNIX_TIMESTAMP(TIMESTAMP('0000-00-00 10:10:20'));
SELECT UNIX_TIMESTAMP(TIMESTAMP('0000-00-00 00:00:00.1'));


--echo #
--echo # Bug#13354387 - CRASH IN IN MY_DECIMAL::OPERATOR FOR VIEW AND FUNCTION UNIX_TIMESTAMP
--echo #
SET timestamp=UNIX_TIMESTAMP('2011-11-18 14:36:00');
CREATE TABLE t1 (
  pk time(2) NOT NULL DEFAULT '00:00:00.00',  
  col_timestamp_3_not_null timestamp(3) NULL DEFAULT '0000-00-00 00:00:00.000',
  col_datetime_1_key datetime(1) DEFAULT NULL,
  col_datetime_6_not_null_key datetime(6) NOT NULL,
  col_datetime_2_not_null datetime(2) NOT NULL,
  PRIMARY KEY (pk),
  KEY col_datetime_1_key (col_datetime_1_key),
  KEY col_datetime_6_not_null_key (col_datetime_6_not_null_key))
  DEFAULT CHARSET=latin1;
INSERT INTO t1 VALUES ('19:14:35.36','0000-00-00 00:00:00.000','0000-00-00 00:00:00.0','2000-08-28 07:34:20.051690','2009-09-20 07:32:39.06');
INSERT INTO t1 VALUES ('19:14:37.36','2005-07-04 08:03:50.051','0000-00-00 00:00:00.0','0000-00-00 00:00:00.000000','0000-00-00 00:00:00.00');
INSERT INTO t1 VALUES ('19:14:39.36','0000-00-00 00:00:00.000',NULL,'2005-10-11 07:24:45.059064','2009-11-19 00:00:00.00');
INSERT INTO t1 VALUES ('19:14:41.36','2005-11-22 21:00:00.000',NULL,'0000-00-00 00:00:00.000000','0000-00-00 00:00:00.00');
INSERT INTO t1 VALUES ('19:14:43.36','2009-11-06 21:00:00.000','2007-07-03 11:13:08.0','2002-09-02 00:17:37.037583','2003-09-25 09:29:41.00');
INSERT INTO t1 VALUES ('19:14:36.36','2009-08-15 05:43:18.029','2009-01-18 00:00:00.0','0000-00-00 00:00:00.000000','2007-07-20 08:31:37.02');
INSERT INTO t1 VALUES ('19:14:38.36','0000-00-00 00:00:00.000','0000-00-00 00:00:00.0','0000-00-00 00:00:00.000000','2004-08-16 05:11:47.04');
INSERT INTO t1 VALUES ('19:14:40.36','2000-04-16 21:00:00.000',NULL,'2004-04-27 00:00:00.000000','2009-06-03 00:18:49.04');
INSERT INTO t1 VALUES ('19:14:42.36','2009-01-18 20:46:41.035','2003-03-23 11:37:04.0','0000-00-00 00:00:00.000000','2009-12-26 00:00:00.00');
INSERT INTO t1 VALUES ('19:14:44.36','0000-00-00 00:00:00.000','0000-00-00 00:00:00.0','2005-09-14 15:13:53.062460','0000-00-00 00:00:00.00');
CREATE OR REPLACE VIEW v1 AS SELECT * FROM t1;
SELECT UNIX_TIMESTAMP(col_datetime_2_not_null) AS c1
FROM  v1
WHERE col_datetime_6_not_null_key > col_datetime_1_key
AND col_timestamp_3_not_null IS NOT NULL
ORDER BY 1;
DROP VIEW v1;
DROP TABLE t1;
SET timestamp=DEFAULT;

--echo #
--echo # Bug#13394031 VALGRIND: CONDITIONAL JUMP/MOVE DEPENDS ON UNINITIALISED IN FIELD::IS_REAL_NULL
--echo #
CREATE TABLE t1 (
  pk time,
  c_timestamp timestamp NULL DEFAULT 0,
  c_time time,
  primary key (pk),
  key (c_timestamp)
) ENGINE=INNODB;  
INSERT INTO t1 VALUES
(SEC_TO_TIME(2),'20000627024305.040518','00:00:00.000000'),      
(SEC_TO_TIME(4),'0000-00-00','20010412233415.034269'),                  
(SEC_TO_TIME(46),'20030628121312.000376','20020323065409.039577'),            
(SEC_TO_TIME(48),'20071219173306.048009',NULL),            
(SEC_TO_TIME(50),'0000-00-00',NULL);                  
SELECT c_time FROM t1;
SELECT c_time AS c1 FROM t1
WHERE c_timestamp NOT IN (NULL, '0000-00-00 00:00:00');
DROP TABLE t1;

--echo #
--echo # Various tests for better gcov coverage
--echo #

--echo #
--echo # Comparing DATETIME value to TIME(NULL)
--echo #
SELECT TIMESTAMP'2001-01-01 00:00:00' = TIME(NULL);


## TS-TODO: SELECT CAST('00:00:00' AS DATETIME) -> should it use curdate?

## TS-TODO: reuse count_datetime_length()/count_string_result_length()
## in other hybrid functions: IF,CASE,GREATEST,LEAST
## CASE and COALESCE already use it.
## TS-TODO: use decimal_int_part() ?
## TS-TODO: check how DATE, TIME and DATETIME types compared to each other:
## insert into t1 values ('0000-01-01','00:01:01'); select * from t1 where a=b;

## TS-TODO-QQ: should all datetime functions honor sql_mode? return NULL+warning
## TS-TODO-QQ: should VARCHAR=TIME(6) compare as TIME(6)?
## TS-TODO-QQ: IFNULL does not support field_type well
## TS-TODO-QQ: unsigned_flag for DATE/DATETIME fields


## TS-TODO-QA: foreign keys
## TS-TODO-QA: complex queries with keys, JOIN, ORDER, many tables
## TS-TODO-QA: EXPLAIN, EXPLAIN, to fix that we correctly use keys for queries
## TS-TODO-QA: (and not only for SELECT).
## TS-TODO-QA: tests with auto_increment, auto update, other subselects



## Comparison DATETIME/TIME to VARCHAR
## PGSQL: requires explicit cast
## MSSQL: casts to time/datetime
## MSSQL: DATETIME2 vs VARCHAR compares as DATETIME2
##'select * from t1 where a=b'
##2011-04-09 15:17:29.7500000	2011-04-09 15:17:29.7500
## MSSQL: TIME vs VARCHAR
##'select * from t1 where a=b' compares as TIME
##11:22:33.123000	11:22:33.123
## MSSQL: VARCHAR vs CAST(TIME(6)) - compares as TIME
##select a, cast('11:12:13.123' as time(6)) from t1 where a=cast('11:12:13.123' as time(6));
##11:12:13.123	11:12:13.123000
## ORACLE: TIMESTAMP(6) vs VARCHAR - compares as TIMESTAMP
## alter session set nls_timestamp_format='YYYY-MM-DD HH24:MI:SS.FF';
##select * from t1 where a=b;
##2011-04-09 15:36:32.225000 2011-04-09 15:36:32.225


--echo #
--echo # BUG#13450867 - CRASH ON UPDATE A TIME COLUMN 
--echo #
CREATE TABLE t1 (a TIME(6), b TIME(6), c TIME(6), d TIME(6));
INSERT INTO t1 VALUES 
  ('-838:59:59.999999', '-838:59:59.000001',
   '838:59:59.999999', '838:59:59.000001');
SELECT * FROM t1;
UPDATE t1 SET 
  a= a - INTERVAL 999999 MICROSECOND, b= b - INTERVAL 1 MICROSECOND,
  c= c + INTERVAL 999999 MICROSECOND, d= d + INTERVAL 1 MICROSECOND;
SELECT * FROM t1;
DROP TABLE t1;

SELECT SEC_TO_TIME(3200399.999999);
SELECT SEC_TO_TIME(3200399.000001);
SELECT SEC_TO_TIME(-3200399.999999);
SELECT SEC_TO_TIME(-3200399.000001);


--echo #
--echo # BUG#13451866 - DIFFERENCE IN RESULTS WHEN USING
--echo # EXTRACT( MINUTE_MICROSECOND )
--echo #
CREATE TABLE t1 (a DATETIME(6) DEFAULT NULL);
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000000');
SELECT * FROM t1;
SELECT EXTRACT(MINUTE_MICROSECOND FROM a) FROM t1;
SELECT EXTRACT(MINUTE_MICROSECOND FROM '0000-00-00 00:00:00.000000');
DROP TABLE t1;


--echo #
--echo # BUG#13436875 - ASSERTION '!ARGS[0]->IS_TEMPORAL() ||
--echo # !ARGS[1]->IS_TEMPORAL()'
--echo #
CREATE TABLE t1 (a DATETIME NOT NULL);
SELECT 1 FROM t1 WHERE GREATEST(a, a) / a;
SELECT 1 FROM t1 WHERE LEAST(a, a) / a;
SELECT 1 FROM t1 WHERE COALESCE(a, a) / a;
SELECT 1 FROM t1 WHERE IF(0, a, a) / a;
SELECT 1 FROM t1 WHERE IFNULL(a, a) / a;
SELECT 1 FROM t1 WHERE CASE WHEN 0 then a ELSE a END / a;    
SELECT 1 FROM t1 WHERE (SELECT a FROM t1) / a;
DROP TABLE t1;


--echo #
--echo # BUG#13616434 ASSERTION FAILED: NANOSECONDS < 1000000000
--echo #
SELECT
  EXTRACT(DAY FROM (-0.1111111111)),
  EXTRACT(YEAR FROM (-0.1111111)),
  EXTRACT(YEAR_MONTH FROM (-0.1111111111)),
  EXTRACT(DAY FROM (-0.1111111)),
  EXTRACT(QUARTER FROM (-0.1111111111)),
  EXTRACT(MONTH FROM (-0.1111111)),
  EXTRACT(WEEK FROM (-0.1111111)),
  DATE(-0.1111111),
  QUARTER(-0.1111111111),
  TIMESTAMP(-0.1111111111),
  LAST_DAY(-0.1111111111),
  DAY(-0.1111111111),
  MONTH(-0.1111111111),
  YEAR(-0.1111111),
  DAYOFMONTH(-0.1111111111),
  TIMESTAMP((-0.1111111111),'{$x'),
  DATE_FORMAT(-0.1111111111,'+'),
  CONVERT(-0.1111111,DATE),
  CAST(-0.1111111111 AS DATE);


--echo #
--echo # Bug#13375823 - FSP(DECIMAL) RESULT DIFFERENCE WITH QUERY USING UNION ALL
--echo #
CREATE TABLE t1 (a DATETIME(3) NOT NULL);
INSERT INTO t1 VALUES ('2000-07-16 05:58:04.035');
INSERT INTO t1 VALUES ('2008-08-21 00:00:00.000');
INSERT INTO t1 VALUES ('0000-00-00 00:00:00.000');
--echo # Testing INT number
SELECT GREATEST(a, 20010101120000) FROM t1;
(SELECT GREATEST(a, 20010101120000) FROM t1) UNION ALL (SELECT GREATEST(a, 20010101120000) FROM t1 LIMIT 0);
SELECT LEAST(a, 20010101120000) FROM t1;
(SELECT LEAST(a, 20010101120000) FROM t1) UNION ALL (SELECT LEAST(a, 20010101120000) FROM t1 LIMIT 0);
--echo # Testing DECIMAL number 
SELECT GREATEST(a, 20010101120000.123456) FROM t1;
(SELECT GREATEST(a, 20010101120000.123456) FROM t1) UNION ALL (SELECT GREATEST(a, 20010101120000.123456) FROM t1 LIMIT 0);
SELECT LEAST(a, 20010101120000.123456) FROM t1;
(SELECT LEAST(a, 20010101120000.123456) FROM t1) UNION ALL (SELECT LEAST(a, 20010101120000.123456) FROM t1 LIMIT 0);
--echo # Testing REAL number
SELECT GREATEST(a, 20010101120000e0) FROM t1;
(SELECT GREATEST(a, 20010101120000e0) FROM t1) UNION ALL (SELECT GREATEST(a, 20010101120000e0) FROM t1 LIMIT 0);
SELECT LEAST(a, 20010101120000e0) FROM t1;
(SELECT LEAST(a, 20010101120000e0) FROM t1) UNION ALL (SELECT LEAST(a, 20010101120000e0) FROM t1 LIMIT 0);
DROP TABLE t1;

--echo # Make sure precision of 6 fractional digits does not get lost
CREATE TABLE t1 (a DATETIME(6));
INSERT INTO t1 VALUES ('2001-01-01 01:01:01.123456');
SELECT GREATEST(a,20010101120000), LEAST(a,20010101120000) FROM t1;
DROP TABLE t1;

--echo #
--echo # Bug#13976233 ASSERTION FAILED: !CHECK_TIME_MMSSFF_RANGE(LTIME), FILE SQL_TIME.CC, LINE 304
--echo #
SELECT SECOND(4.99999999991e0);
SELECT SECOND(-4.99999999991e0);
SELECT SECOND(TRUNCATE('5',180));


--echo #
--echo # Bug#17080703 INCONSISTENT DATETIME CONVERSIONS WITH FRACTIONAL SECONDS
--echo #

--echo # Test for TIMESTAMP datatype.
SELECT TIMESTAMP '20130710010203';
--error ER_WRONG_VALUE
SELECT TIMESTAMP '201307100102031';
--error ER_WRONG_VALUE
SELECT TIMESTAMP '2013071001020312';
--error ER_WRONG_VALUE
SELECT TIMESTAMP '20130710010203123';
--error ER_WRONG_VALUE
SELECT TIMESTAMP '201307100102031234';
--error ER_WRONG_VALUE
SELECT TIMESTAMP '2013071001020312345';
--error ER_WRONG_VALUE
SELECT TIMESTAMP '20130710010203123456';

--echo # Test non decimal point as delimiter for fractional seconds.
--error ER_WRONG_VALUE
SELECT TIMESTAMP '2013:07:10 01:02:03:04';

--echo # Test for invalid seconds >= 60 properly separated by decimal point with fractions.
--error ER_WRONG_VALUE
SELECT TIMESTAMP '2013:07:10 01.02.131';
--error ER_WRONG_VALUE
SELECT TIMESTAMP '2013:07:10 01.02.131.2';
--error ER_WRONG_VALUE
SELECT TIMESTAMP '2013:07:10 01.02.0312.3';
--error ER_WRONG_VALUE
SELECT TIMESTAMP '2013.07.10 01.02.131.23456';

--error ER_WRONG_VALUE
SELECT TIMESTAMP '201307100102031.';
--error ER_WRONG_VALUE
SELECT TIMESTAMP '2012071001021.1';


--echo # Test for valid seconds properly separated by decimal point with fractions.
SELECT TIMESTAMP '2013:07:10 01.02.0.31';
SELECT TIMESTAMP '20130710010203.';


--echo # Test for valid seconds properly separated by decimal point with fractions.
SELECT TIMESTAMP '20130710010203.1';
SELECT TIMESTAMP '20130710010203.12';
SELECT TIMESTAMP '20130710010203.123';
SELECT TIMESTAMP '20130710010203.1234';
SELECT TIMESTAMP '20130710010203.12345';
SELECT TIMESTAMP '20130710010203.123456';
SELECT TIMESTAMP '20130710010203.1234567';

--echo # Test for valid time literals in insert operation.
CREATE TABLE t(key1 INT UNIQUE, col TIMESTAMP(6));
INSERT INTO t VALUES (1, '20130710010203.1'), (2, '20130710010203.12'), (3, '20130710010203.123'), (4, '20130710010203.1234'),
(5,'20130710010203.12345'), (6, '20130710010203.123456'), (7, '201207100102');
SELECT * FROM t ORDER BY key1;
DELETE FROM t;

--echo # Test for invalid time literals in insert operation.
INSERT INTO t VALUES (1, '201307100102031'), (2, '2013071001020312'), (3, '20130710010203123'), (4, '201307100102031234'),
(5, '2013071001020312345'), (6, '20130710010203123456'), (7, '2013:07:10 01.02.031'), (8, '2013:07:10 01.02.031.2'),
(9, '2013:07:10 01.02.0312.3'), (10, '2012071001021.1'), (11, '2012071001021');
SELECT * FROM t ORDER BY key1;
DROP TABLE t;

--echo # Test for DATETIME datatype.

--echo # Test for valid time literals in insert operation.
CREATE TABLE t(key1 INT UNIQUE, col DATETIME(6));
INSERT INTO t VALUES (1, '20130710010203.1'), (2, '20130710010203.12'), (3, '20130710010203.123'), (4, '20130710010203.1234'),
(5, '20130710010203.12345'), (6, '20130710010203.123456'), (7, '201207100102');
SELECT * FROM t ORDER BY key1;
DELETE FROM t;
--echo # Test for invalid time literals in insert operation.
INSERT INTO t VALUES (1, '201307100102031'), (2, '2013071001020312'), (3, '20130710010203123'), (4, '201307100102031234'),
(5, '2013071001020312345'), (6, '20130710010203123456'), (7, '2013:07:10 01.02.031'), (8, '2013:07:10 01.02.031.2'),
(9, '2013:07:10 01.02.0312.3'), (10, '2012071001021.1'), (11, '2012071001021');
SELECT * FROM t ORDER BY key1;
DROP TABLE t;

--echo # Test for TIME datatype.
SELECT TIME '010203';
SELECT TIME '1010203';
SELECT TIME '01010203';

--error ER_WRONG_VALUE
SELECT TIME '10102031';
--error ER_WRONG_VALUE
SELECT TIME '101020312';
--error ER_WRONG_VALUE
SELECT TIME '1010203123';
--error ER_WRONG_VALUE
SELECT TIME '10102031234';
--error ER_WRONG_VALUE
SELECT TIME '101020312345';
--error ER_WRONG_VALUE
SELECT TIME '1010203123456';

--echo # Test non decimal point as delimiter for fractional seconds.
--error ER_WRONG_VALUE
SELECT TIME '01:02:03:04';

--echo # Test for invalid seconds >= 60 properly separated by decimal point with fractions.
--error ER_WRONG_VALUE
SELECT TIME '01:02:131';
--error ER_WRONG_VALUE
SELECT TIME '01:02:131.2';
--error ER_WRONG_VALUE
SELECT TIME '01:02:0312.3';
--error ER_WRONG_VALUE
SELECT TIME '01:02:131.23456';

--error ER_WRONG_VALUE
SELECT TIME '11102031.';

--echo # Test for valid seconds properly separated by decimal point with fractions.
SELECT TIME '01:02:0.31';
SELECT TIME '010203.';

--echo # Test for valid seconds properly separated by decimal point with fractions.
SELECT TIME '110203.1';
SELECT TIME '010203.12';
SELECT TIME '010203.123';
SELECT TIME '010203.1234';
SELECT TIME '010203.12345';
SELECT TIME '010203.123456';
SELECT TIME '010203.1234567';

SELECT TIME '01021.1';

--echo # Test for valid time literals in insert operation.
CREATE TABLE t(key1 INT UNIQUE, col TIME(6));
INSERT INTO t VALUES (1, '010203.1'), (2, '010203.12'), (3, '010203.123'), (4, '010203.1234'),
(5, '010203.12345'), (6, '010203.123456'), (7, '0203.123456'), (8, '03.123456'), (9, '0.123456');
SELECT * FROM t ORDER BY key1;
DELETE FROM t;

--echo # Test for invalid time literals in insert operation.
INSERT INTO t VALUES (1, '0102031'), (2, '01020312'), (3, '010203123'), (4, '0102031234'),
(5, '01020312345'), (6, '010203123456'), (7, '01:02:031'), (8, '01:02:031.2'),
(9, '01:02:0312.3');
SELECT * FROM t ORDER BY key1;
DROP TABLE t;
SET sql_mode = default;

--echo # End of 5.6 tests

--echo #
--echo # Bug#20565160 ASSERTION `SORT_FIELD->LENGTH >= LENGTH' FAILED
--echo #

SET NAMES utf8mb3;

CREATE TABLE t1 (
  c datetime(1) NOT NULL,
  KEY(c)
)
;

INSERT INTO t1 VALUES
('2002-08-12 22:00:00.0'),('2002-08-12 22:00:00.1'),('2002-08-12 22:00:00.2');

SELECT LEAST(c , '2005-05-14' ) AS c1
FROM t1
ORDER BY 1;

DROP TABLE t1;
SET NAMES default;

--echo # Bug#19900900 ASSERTION `!CHECK_DATETIME_RANGE(LTIME)' FAILED
--echo #              TIME_TO_LONGLONG_DATETIME_PACKED
--echo #

SET @@session.sql_buffer_result=TRUE;
CREATE TABLE t1(a DATETIME) engine=innodb;
INSERT INTO t1 VALUES('9999-12-31 23:59:58.000015');
SELECT ADDTIME(a, 100000) FROM t1;
SET @@session.sql_buffer_result=default;

DROP TABLE t1;

--echo #
--echo # Bug#29120569 SETTING OUT OF RANGE FRACTIONAL PART PRODUCES
--echo #              INCORRECT TIMESTAMPS
--echo #

SET time_zone='+00:00';

SET SESSION TIMESTAMP=1.9999996;
SELECT CURRENT_TIMESTAMP(6);

CREATE TABLE t0( ts TIMESTAMP(0) not null, dt DATETIME(0) not null);
CREATE TABLE t1( ts TIMESTAMP(1) not null, dt DATETIME(1) not null);
CREATE TABLE t2( ts TIMESTAMP(2) not null, dt DATETIME(2) not null);
CREATE TABLE t3( ts TIMESTAMP(3) not null, dt DATETIME(3) not null);
CREATE TABLE t4( ts TIMESTAMP(4) not null, dt DATETIME(4) not null);
CREATE TABLE t5( ts TIMESTAMP(5) not null, dt DATETIME(5) not null);
CREATE TABLE t6( ts TIMESTAMP(6) not null, dt DATETIME(6) not null);

INSERT INTO t0 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));
INSERT INTO t1 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));
INSERT INTO t2 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));
INSERT INTO t3 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));
INSERT INTO t4 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));
INSERT INTO t5 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));
INSERT INTO t6 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));

SELECT * FROM t0;
SELECT * FROM t1;
SELECT * FROM t2;
SELECT * FROM t3;
SELECT * FROM t4;
SELECT * FROM t5;
SELECT * FROM t6;

DROP TABLE t0, t1, t2, t3, t4, t5, t6;

SET sql_mode=time_truncate_fractional;

SET SESSION TIMESTAMP=1.9999996;

CREATE TABLE t0( ts TIMESTAMP(0) not null, dt DATETIME(0) not null);
CREATE TABLE t1( ts TIMESTAMP(1) not null, dt DATETIME(1) not null);
CREATE TABLE t2( ts TIMESTAMP(2) not null, dt DATETIME(2) not null);
CREATE TABLE t3( ts TIMESTAMP(3) not null, dt DATETIME(3) not null);
CREATE TABLE t4( ts TIMESTAMP(4) not null, dt DATETIME(4) not null);
CREATE TABLE t5( ts TIMESTAMP(5) not null, dt DATETIME(5) not null);
CREATE TABLE t6( ts TIMESTAMP(6) not null, dt DATETIME(6) not null);

INSERT INTO t0 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));
INSERT INTO t1 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));
INSERT INTO t2 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));
INSERT INTO t3 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));
INSERT INTO t4 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));
INSERT INTO t5 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));
INSERT INTO t6 values (CURRENT_TIMESTAMP(6), CURRENT_TIMESTAMP(6));

SELECT * FROM t0;
SELECT * FROM t1;
SELECT * FROM t2;
SELECT * FROM t3;
SELECT * FROM t4;
SELECT * FROM t5;
SELECT * FROM t6;

DROP TABLE t0, t1, t2, t3, t4, t5, t6;

SET sql_mode=default;
SET SESSION TIMESTAMP=default;
SET time_zone=default;

--echo #
--echo # Bug#29594951 DECIMAL2LLDIV_T REFERS TO UNINITIALIZED VALUES!
--echo #

CREATE TABLE t1 (c1 LONGTEXT);
INSERT INTO t1 VALUES('2018-10-15 01:29:55');

--error ER_INVALID_JSON_CHARSET
SELECT * FROM t1 WHERE
'2018-10-15 01:29:55'=(c1 + INTERVAL(json_keys(0xceedc36e,'$$')) SECOND);

DROP TABLE t1;

--echo #
--echo # Bug#29587536 UBSAN: SIGNED INTEGER OVERFLOW IN
--echo #              ITEM_DATE_ADD_INTERVAL::GET_TIME_INTERNAL
--echo #

do date_add(utc_time(), interval(oct(-11779)) microsecond);

--echo #
--echo # Bug #31054071 UBSAN: SIGNED INTEGER OVERFLOW IN
--echo #               ITEM_DATE_ADD_INTERVAL::GET_TIME_INTERNAL
--echo #

CREATE TABLE t(a TIME(1), b DECIMAL(55,13));
INSERT INTO t(a,b) VALUES
('-765:38:03.6',999999999999999999999999999999999999999999.9999999999999);
SELECT a - INTERVAL(b) MICROSECOND FROM t;
DROP TABLE t;

--echo #
--echo # Bug #30324587 	ASSERTION FAILED: (*REF)->IS_TEMPORAL()
--echo #

SELECT current_date IN ( MAX(NULL), 1 );
SELECT current_time IN ( MAX(NULL), 1 );
