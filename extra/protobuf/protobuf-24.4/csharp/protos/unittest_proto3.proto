// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Author: <EMAIL> (Kenton Varda)
//  Based on original Protocol Buffers design by
//  Sanjay Ghemawat, Jeff Dean, and others.
//
// A proto file we will use for unit testing.

syntax = "proto3";

option csharp_namespace = "Google.Protobuf.TestProtos";

// Only present so we can test that we can read it (as an example
// of a non-C# option)
option java_outer_classname = "UnittestProto";

import "csharp/protos/unittest_import_proto3.proto";

package protobuf_unittest3;

// This proto includes every type of field in both singular and repeated
// forms.
message TestAllTypes {
  message NestedMessage {
    // The field name "b" fails to compile in proto1 because it conflicts with
    // a local variable named "b" in one of the generated methods.  Doh.
    // This file needs to compile in proto1 to test backwards-compatibility.
    int32 bb = 1;
  }

  enum NestedEnum {
    NESTED_ENUM_UNSPECIFIED = 0;
    FOO = 1;
    BAR = 2;
    BAZ = 3;
    NEG = -1;  // Intentionally negative.
  }

  // Singular
  int32 single_int32 = 1;
  int64 single_int64 = 2;
  uint32 single_uint32 = 3;
  uint64 single_uint64 = 4;
  sint32 single_sint32 = 5;
  sint64 single_sint64 = 6;
  fixed32 single_fixed32 = 7;
  fixed64 single_fixed64 = 8;
  sfixed32 single_sfixed32 = 9;
  sfixed64 single_sfixed64 = 10;
  float single_float = 11;
  double single_double = 12;
  bool single_bool = 13;
  string single_string = 14;
  bytes single_bytes = 15;

  NestedMessage single_nested_message = 18;
  ForeignMessage single_foreign_message = 19;
  protobuf_unittest_import.ImportMessage single_import_message = 20;

  NestedEnum single_nested_enum = 21;
  ForeignEnum single_foreign_enum = 22;
  protobuf_unittest_import.ImportEnum single_import_enum = 23;

  // Defined in unittest_import_public.proto
  protobuf_unittest_import.PublicImportMessage single_public_import_message =
      26;

  // Repeated
  repeated int32 repeated_int32 = 31;
  repeated int64 repeated_int64 = 32;
  repeated uint32 repeated_uint32 = 33;
  repeated uint64 repeated_uint64 = 34;
  repeated sint32 repeated_sint32 = 35;
  repeated sint64 repeated_sint64 = 36;
  repeated fixed32 repeated_fixed32 = 37;
  repeated fixed64 repeated_fixed64 = 38;
  repeated sfixed32 repeated_sfixed32 = 39;
  repeated sfixed64 repeated_sfixed64 = 40;
  repeated float repeated_float = 41;
  repeated double repeated_double = 42;
  repeated bool repeated_bool = 43;
  repeated string repeated_string = 44;
  repeated bytes repeated_bytes = 45;

  repeated NestedMessage repeated_nested_message = 48;
  repeated ForeignMessage repeated_foreign_message = 49;
  repeated protobuf_unittest_import.ImportMessage repeated_import_message = 50;

  repeated NestedEnum repeated_nested_enum = 51;
  repeated ForeignEnum repeated_foreign_enum = 52;
  repeated protobuf_unittest_import.ImportEnum repeated_import_enum = 53;
  // Defined in unittest_import_public.proto
  repeated protobuf_unittest_import.PublicImportMessage
      repeated_public_import_message = 54;

  // For oneof test
  oneof oneof_field {
    uint32 oneof_uint32 = 111;
    NestedMessage oneof_nested_message = 112;
    string oneof_string = 113;
    bytes oneof_bytes = 114;
  }
}

// This proto includes a recursively nested message.
message NestedTestAllTypes {
  NestedTestAllTypes child = 1;
  TestAllTypes payload = 2;
  repeated NestedTestAllTypes repeated_child = 3;
}

message TestDeprecatedFields {
  int32 deprecated_int32 = 1 [deprecated = true];
}

// Define these after TestAllTypes to make sure the compiler can handle
// that.
message ForeignMessage {
  int32 c = 1;
}

enum ForeignEnum {
  FOREIGN_UNSPECIFIED = 0;
  FOREIGN_FOO = 4;
  FOREIGN_BAR = 5;
  FOREIGN_BAZ = 6;
}

message TestReservedFields {
  reserved 2, 15, 9 to 11;
  reserved "bar", "baz";
}

// Test that we can use NestedMessage from outside TestAllTypes.
message TestForeignNested {
  TestAllTypes.NestedMessage foreign_nested = 1;
}

// Test that really large tag numbers don't break anything.
message TestReallyLargeTagNumber {
  // The largest possible tag number is 2^28 - 1, since the wire format uses
  // three bits to communicate wire type.
  int32 a = 1;
  int32 bb = 268435455;
}

message TestRecursiveMessage {
  TestRecursiveMessage a = 1;
  int32 i = 2;
}

// Test that mutual recursion works.
message TestMutualRecursionA {
  TestMutualRecursionB bb = 1;
}

message TestMutualRecursionB {
  TestMutualRecursionA a = 1;
  int32 optional_int32 = 2;
}

message TestEnumAllowAlias {
  TestEnumWithDupValue value = 1;
}

// Test an enum that has multiple values with the same number.
enum TestEnumWithDupValue {
  option allow_alias = true;

  TEST_ENUM_WITH_DUP_VALUE_UNSPECIFIED = 0;

  FOO1 = 1;
  BAR1 = 2;
  BAZ = 3;
  FOO2 = 1;
  BAR2 = 2;
}

// Test an enum with large, unordered values.
enum TestSparseEnum {
  TEST_SPARSE_ENUM_UNSPECIFIED = 0;
  SPARSE_A = 123;
  SPARSE_B = 62374;
  SPARSE_C = 12589234;
  SPARSE_D = -15;
  SPARSE_E = -53452;
  // In proto3, value 0 must be the first one specified
  // SPARSE_F = 0;
  SPARSE_G = 2;
}

// Test message with CamelCase field names.  This violates Protocol Buffer
// standard style.
message TestCamelCaseFieldNames {
  int32 PrimitiveField = 1;
  string StringField = 2;
  ForeignEnum EnumField = 3;
  ForeignMessage MessageField = 4;

  repeated int32 RepeatedPrimitiveField = 7;
  repeated string RepeatedStringField = 8;
  repeated ForeignEnum RepeatedEnumField = 9;
  repeated ForeignMessage RepeatedMessageField = 10;
}

// We list fields out of order, to ensure that we're using field number and not
// field index to determine serialization order.
message TestFieldOrderings {
  string my_string = 11;
  int64 my_int = 1;
  float my_float = 101;
  message NestedMessage {
    int64 oo = 2;
    // The field name "b" fails to compile in proto1 because it conflicts with
    // a local variable named "b" in one of the generated methods.  Doh.
    // This file needs to compile in proto1 to test backwards-compatibility.
    int32 bb = 1;
  }

  NestedMessage single_nested_message = 200;
}

message SparseEnumMessage {
  TestSparseEnum sparse_enum = 1;
}

// Test String and Bytes: string is for valid UTF-8 strings
message OneString {
  string data = 1;
}

message MoreString {
  repeated string data = 1;
}

message OneBytes {
  bytes data = 1;
}

message MoreBytes {
  bytes data = 1;
}

// Test int32, uint32, int64, uint64, and bool are all compatible
message Int32Message {
  int32 data = 1;
}

message Uint32Message {
  uint32 data = 1;
}

message Int64Message {
  int64 data = 1;
}

message Uint64Message {
  uint64 data = 1;
}

message BoolMessage {
  bool data = 1;
}

// Test oneofs.
message TestOneof {
  oneof foo {
    int32 foo_int = 1;
    string foo_string = 2;
    TestAllTypes foo_message = 3;
  }
}

// Test messages for packed fields

message TestPackedTypes {
  repeated int32 packed_int32 = 90 [packed = true];
  repeated int64 packed_int64 = 91 [packed = true];
  repeated uint32 packed_uint32 = 92 [packed = true];
  repeated uint64 packed_uint64 = 93 [packed = true];
  repeated sint32 packed_sint32 = 94 [packed = true];
  repeated sint64 packed_sint64 = 95 [packed = true];
  repeated fixed32 packed_fixed32 = 96 [packed = true];
  repeated fixed64 packed_fixed64 = 97 [packed = true];
  repeated sfixed32 packed_sfixed32 = 98 [packed = true];
  repeated sfixed64 packed_sfixed64 = 99 [packed = true];
  repeated float packed_float = 100 [packed = true];
  repeated double packed_double = 101 [packed = true];
  repeated bool packed_bool = 102 [packed = true];
  repeated ForeignEnum packed_enum = 103 [packed = true];
}

// A message with the same fields as TestPackedTypes, but without packing. Used
// to test packed <-> unpacked wire compatibility.
message TestUnpackedTypes {
  repeated int32 unpacked_int32 = 90 [packed = false];
  repeated int64 unpacked_int64 = 91 [packed = false];
  repeated uint32 unpacked_uint32 = 92 [packed = false];
  repeated uint64 unpacked_uint64 = 93 [packed = false];
  repeated sint32 unpacked_sint32 = 94 [packed = false];
  repeated sint64 unpacked_sint64 = 95 [packed = false];
  repeated fixed32 unpacked_fixed32 = 96 [packed = false];
  repeated fixed64 unpacked_fixed64 = 97 [packed = false];
  repeated sfixed32 unpacked_sfixed32 = 98 [packed = false];
  repeated sfixed64 unpacked_sfixed64 = 99 [packed = false];
  repeated float unpacked_float = 100 [packed = false];
  repeated double unpacked_double = 101 [packed = false];
  repeated bool unpacked_bool = 102 [packed = false];
  repeated ForeignEnum unpacked_enum = 103 [packed = false];
}

message TestRepeatedScalarDifferentTagSizes {
  // Parsing repeated fixed size values used to fail. This message needs to be
  // used in order to get a tag of the right size; all of the repeated fields
  // in TestAllTypes didn't trigger the check.
  repeated fixed32 repeated_fixed32 = 12;
  // Check for a varint type, just for good measure.
  repeated int32 repeated_int32 = 13;

  // These have two-byte tags.
  repeated fixed64 repeated_fixed64 = 2046;
  repeated int64 repeated_int64 = 2047;

  // Three byte tags.
  repeated float repeated_float = 262142;
  repeated uint64 repeated_uint64 = 262143;
}

message TestCommentInjectionMessage {
  // */ <- This should not close the generated doc comment
  string a = 1;
}

// Test that RPC services work.
message FooRequest {}
message FooResponse {}

message FooClientMessage {}
message FooServerMessage {}

// This is a test service
service TestService {
  // This is a test method
  rpc Foo(FooRequest) returns (FooResponse);
  rpc Bar(BarRequest) returns (BarResponse);
}

message BarRequest {}
message BarResponse {}

message TestEmptyMessage {}

// This is leading detached comment 1

// This is leading detached comment 2

// This is a leading comment
message CommentMessage {
  // Leading nested message comment
  message NestedCommentMessage {
    // Leading nested message field comment
    string nested_text = 1;
  }

  // Leading nested enum comment
  enum NestedCommentEnum {
    // Zero value comment
    ZERO_VALUE = 0;
  }

  // Leading field comment
  string text = 1;  // Trailing field comment
}

// Leading enum comment
enum CommentEnum {
  // Zero value comment
  ZERO_VALUE = 0;
}
