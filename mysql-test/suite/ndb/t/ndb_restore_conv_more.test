-- source include/have_ndb.inc
-- source suite/ndb/include/backup_restore_setup.inc

--echo # bug#17275798
# use a canned backup with NULL char(10) log entries promoted to char(20)

create table t1 (
  a int unsigned primary key,
  b char(20) character set latin1,
  c int unsigned
) engine=ndb;

--echo # restore canned backup
--exec $NDB_RESTORE $NDB_RESTORE_NO_ENCR -b 1 -n 21 -r --promote-attributes $NDB_SAVED_BACKUPS/bug17275798 >> $NDB_TOOLS_OUTPUT

select sum(1),sum(isnull(b)),min(b),max(b) from t1;

drop table t1;

--echo # bug#17395298

create table t1 (
  a int primary key,
  b text,
  c int
) engine=ndb;

insert into t1 values (1,repeat('a',1000),10);
insert into t1 values (2,repeat('b',2000),20);
insert into t1 values (3,repeat('c',3000),30);

--echo # run backup
--source include/ndb_backup.inc

rename table t1 to t1old;

create table t1 (
  a int primary key,
  c int,
  b text
) engine=ndb;

--echo # run restore
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT

select count(*) from t1 x, t1old y
where x.a = y.a and x.b = y.b and x.c = y.c;

drop table t1old;
drop table t1;

--echo # bug#17235051 : char<->binary

--echo # plain restore

create table t1 (
  c1 char(10),
  c2 varchar(100),
  c3 varchar(1000),
  b1 binary(10),
  b2 varbinary(100),
  b3 varbinary(1000),
  d varchar(8000),
  a int unsigned primary key
) engine=ndb character set latin1;

let $ins =
insert into t1 values
  ('aaa1','bbb1','ccc1','ddd1','eee1','fff1',repeat('x',2000),1),
  ('aaa2','bbb2','ccc2','ddd2','eee2','fff2',repeat('y',4000),2),
  ('aaa3','bbb3','ccc3','ddd3','eee3','fff3',repeat('z',6000),3);
--eval $ins

let $qry =
select a,
trim(trailing char(0) from c1) as c1,c2,c3,
trim(trailing char(0) from b1) as b1,b2,b3
from t1 order by a;
--eval $qry

--echo # run backup
--source include/ndb_backup.inc

drop table t1;

create table t1 (
  b1 char(10),
  b2 varchar(100),
  b3 varchar(1000),
  c1 binary(10),
  c2 varbinary(100),
  c3 varbinary(1000),
  d varchar(8000),
  a int unsigned primary key
) engine=ndb character set latin1;

--echo # run restore
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r -A $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r -A $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT

--eval $qry

drop table t1;

--echo # staging restore

create table t1 (
  c1 char(10),
  c2 varchar(100),
  c3 varchar(1000),
  b1 binary(10),
  b2 varbinary(100),
  b3 varbinary(1000),
  d varchar(8000),
  a int unsigned primary key
) engine=ndb character set latin1;

--eval $ins
--eval $qry

--echo # run backup
--source include/ndb_backup.inc

drop table t1;

create table t1 (
  b1 char(10),
  b2 varchar(100),
  b3 varchar(1000),
  c1 binary(10),
  c2 varbinary(100),
  c3 varbinary(1000),
  d text,
  a int unsigned primary key
) engine=ndb character set latin1;

--echo # run restore
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r -A $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r -A $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT

--eval $qry

drop table t1;

--echo # create tablespace

create logfile group lg1
add undofile 'undofile1.dat'
initial_size 8M
undo_buffer_size 1M
engine=ndb;

create tablespace ts1
add datafile 'datafile1.dat'
use logfile group lg1
initial_size 96M
engine=ndb;

--echo # bug#17235051 : char<->text

create table t1 (
  b text,
  c varchar(8000) character set latin1,
  d text,
  a int unsigned primary key
) engine=ndb;

insert into t1 values (repeat('a',3333),repeat('b',5555),repeat('c',7777),1);
insert into t1 values (repeat('d',7777),repeat('e',5555),repeat('f',3333),2);
insert into t1 values (null,repeat('h',5555),repeat('i',3333),3);
insert into t1 values (repeat('j',7777),null,repeat('l',3333),4);
insert into t1 values (repeat('m',7777),repeat('n',5555),null,5);

--let $qry = select count(*),sum(crc32(concat(a,ifnull(b,'x'),ifnull(c,'y'),ifnull(d,'z')))) from t1
--eval $qry

--echo # run backup
--source include/ndb_backup.inc

drop table t1;

create table t1 (
  b varchar(8000) character set latin1,
  c text,
  d text,
  a int unsigned primary key
) engine=ndb;

--echo # run restore
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r -A -L $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r -A -L $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT

--eval $qry

drop table t1;

--echo # bug#17235051 : remap columns and missing columns

create table t1 (
  x int,
  a int primary key auto_increment,
  b int,
  c bigint,
  y int,
  d varchar(8000) character set latin1
) engine=ndb;

insert into t1 (x,b,c,d) values (1,10,100,repeat('d',30));
let $i=8;
while ($i)
{
  eval insert into t1 (x,b,c,d) select x*2,b*3,c*4,concat(d,d) from t1;
  dec $i;
}

select count(*),max(a),max(b),max(c),max(length(d)) from t1;

# non-deterministic??? try something else
#select sum(crc32(concat(a,b,c,d))) from t1;

--echo # run backup
--source include/ndb_backup.inc

rename table t1 to t1old;

create table t1 (
  d text,
  z char(10),
  b int,
  w char(10),
  c bigint,
  a int primary key auto_increment
) engine=ndb;

--echo # run restore
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r --exclude-missing-columns -A $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r --exclude-missing-columns -A $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT

select count(*),max(a),max(b),max(c),max(length(d)) from t1;

select count(*) from t1 x, t1old y
where x.a = y.a and x.b = y.b and x.c = y.c and x.d = y.d;

drop table t1old;
drop table t1;

--echo # bug#17235051 : lossy conversions

create table t1 (
  a char(1) primary key,
  b text,
  c varchar(10) character set latin1
) engine=ndb;

insert into t1 values ('a',concat('x',repeat('a',1000)),'0123456789');
insert into t1 values ('b',concat('y',repeat('b',2000)),'012345');
insert into t1 values ('c',concat('z',repeat('c',3000)),'01234');

let $qry =
select a,length(b),trim(trailing a from b),c
from t1 order by a;
--eval $qry

--echo # run backup
--source include/ndb_backup.inc

drop table t1;

create table t1 (
  a char(1) primary key,
  b varchar(2001) character set latin1,
  c varchar(5) character set latin1
) engine=ndb;

--echo # run restore
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r -L $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r -L $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT

--eval $qry

drop table t1;

--echo # bug#17235051 : char<->text + disk data

create table t1old (
  a int unsigned primary key auto_increment,
  b text,
  c varchar(8000) character set latin1,
  d text,
  e varchar(20),
  f int
) engine=ndb;

insert into t1old (b,c,d,e,f)
  values (repeat('b',20),repeat('c',30),repeat('d',40),'e',1);
let $i=8;
while ($i)
{
  eval insert into t1old (b,c,d,e,f)
    select concat(b,c),concat(c,d),concat(d,b),concat(e,'e'),2*f
    from t1old;
  dec $i;
}
select count(*),max(length(b)),max(length(c)),max(length(d)),max(length(e)),max(f)
from t1old;

let $qry =
select count(*) from t1 x, t1old y
where x.a = y.a and x.b = y.b and x.c = y.c and x.d = y.d and x.e = y.e and x.f = y.f; 
let $cre1 =
create table t1 (
  a int unsigned primary key auto_increment,
  b text,
  c varchar(8000) character set latin1,
  d text,
  e varchar(20),
  f int
) engine=ndb;

let $cre2 =
create table t1 (
  e varchar(20),
  f int,
  a int unsigned primary key auto_increment,
  b varchar(8000) character set latin1,
  c text,
  d text
) engine=ndb;

--echo # restore mem->mem

--eval $cre1
insert into t1 select * from t1old;
--eval $qry
--echo # run backup
--source include/ndb_backup.inc
drop table t1;
--eval $cre2
--echo # run restore
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r -A -L $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r -A -L $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--eval $qry
drop table t1;

--echo # restore disk->disk

--eval $cre1 tablespace ts1 storage disk
insert into t1 select * from t1old;
--eval $qry
--echo # run backup
--source include/ndb_backup.inc
drop table t1;
--eval $cre2 tablespace ts1 storage disk
--echo # run restore
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r -A -L $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r -A -L $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--eval $qry
drop table t1;

drop table t1old;

--echo # bug#17235051 : hybrid failure
# would fail in obsolete hybrid solution with 738 (Record too big)

create table t1 (
  a int primary key,
  b varchar(5000),
  c varchar(5000)
) engine=ndb character set latin1;

insert into t1 values (1,null,null);
select * from t1;

--echo # run backup
--source include/ndb_backup.inc

drop table t1;

create table t1 (
  a int primary key,
  b text,
  c varchar(10000) character set latin1
) engine=ndb;

--echo # run restore
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r -A $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r -A $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT

select * from t1;
drop table t1;

--echo # Ignore dynamic column diffs between staging and 'main'
--echo # If backed-up had dynamic (due to online alter), and
--echo # new does not (due to lack of alter), staging table
--echo # should not care.

create table t1(
  a int unsigned primary key,
  b varchar(6144) character set latin1
) engine=ndb;

--echo # alter causing varchar to become 'dynamic'

alter table t1 add column c int;
show warnings;

insert into t1 values (1,repeat('b',2000), 10);
insert into t1 values (2,repeat('t',2000), 20);
insert into t1 values (3,repeat('g',2000), 30);

--echo # run backup
--source include/ndb_backup.inc

rename table t1 to t1old;

--echo # create t1 with text + without alter (not dynamic by default)

create table t1(
  a int unsigned primary key,
  b text,
  c int
) engine=ndb;

--echo # run restore

--exec $NDB_RESTORE -b $the_backup_id -n 1 -r --promote-attributes $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r --promote-attributes $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT

select count(*) from t1 x, t1old y
where x.a = y.a and x.b = y.b and x.c = y.c;

drop table t1old;
drop table t1;

--echo # bug#18875137: ndb_restore type conversions

--echo # run restore with lossy conversions
create table text2text (pk int primary key, 
                        t1 tinytext, t2 text, t3 mediumtext, t4 longtext, 
                        t5 tinytext, t6 text, t7 mediumtext, t8 longtext,
                        t9 tinytext, t10 text, t11 mediumtext, t12 longtext,
                        t13 tinytext, t14 text, t15 mediumtext, t16 longtext)engine=NDB;
insert into text2text values(1, 
                        REPEAT("A",255), REPEAT("A",5000), REPEAT("A",65536), REPEAT("A", 30000),
                        REPEAT("A",255), REPEAT("A",5000), REPEAT("A",65537), REPEAT("A", 30000),
                        REPEAT("A",255), REPEAT("A",5000), REPEAT("A",65540), REPEAT("A", 30000),
                        NULL, NULL, NULL, NULL);
insert into text2text values(2, "", "", "", "", "", "", "", "", "", "", "", "", NULL, NULL, NULL, NULL);
insert into text2text values(3, 
                        REPEAT(" -,.;",51), REPEAT(" -,.;",1000), REPEAT(" -,.;",2000), REPEAT(" -,.;", 6000),
                        REPEAT(" -,.;",51), REPEAT(" -,.;",1000), REPEAT(" -,.;",2001), REPEAT(" -,.;", 6000),
                        REPEAT(" -,.;",51), REPEAT(" -,.;",1000), REPEAT(" -,.;",2002), REPEAT(" -,.;", 6000),
                        NULL, NULL, NULL, NULL);
insert into text2text values(4, 
                        REPEAT("12345",51), REPEAT("12345",1000), REPEAT("12345",2000), REPEAT("12345", 6000),
                        REPEAT("12345",51), REPEAT("12345",1000), REPEAT("12345",2001), REPEAT("12345", 6000),
                        REPEAT("12345",51), REPEAT("12345",1000), REPEAT("12345",2002), REPEAT("12345", 6000),
                        NULL, NULL, NULL, NULL);
insert into text2text values(5, 
                        REPEAT("abcde",51), REPEAT("abcde",1000), REPEAT("abcde",2000), REPEAT("abcde", 6000),
                        REPEAT("abcde",51), REPEAT("abcde",1000), REPEAT("abcde",2001), REPEAT("abcde", 6000),
                        REPEAT("abcde",51), REPEAT("abcde",1000), REPEAT("abcde",2002), REPEAT("abcde", 6000),
                        NULL, NULL, NULL, NULL);
insert into text2text values(6, 
                        REPEAT("     ",51), REPEAT("     ",1000), REPEAT("     ",2000), REPEAT("     ", 6000),
                        REPEAT("     ",51), REPEAT("     ",1000), REPEAT("     ",2001), REPEAT("     ", 6000),
                        REPEAT("     ",51), REPEAT("     ",1000), REPEAT("     ",2002), REPEAT("     ", 6000),
                        NULL, NULL, NULL, NULL);
insert into text2text values(7, 
                        REPEAT("a%d4 ",51), REPEAT("a%d4 ",1000), REPEAT("a%d4 ",2000), REPEAT("a%d4 ", 6000),
                        REPEAT("a%d4 ",51), REPEAT("a%d4 ",1000), REPEAT("a%d4 ",2001), REPEAT("a%d4 ", 6000),
                        REPEAT("a%d4 ",51), REPEAT("a%d4 ",1000), REPEAT("a%d4 ",2002), REPEAT("a%d4 ", 6000),
                        NULL, NULL, NULL, NULL);
insert into text2text values(8, 
                        NULL, NULL, NULL, NULL,
                        NULL, NULL, NULL, NULL,
                        NULL, NULL, NULL, NULL,
                        NULL, NULL, NULL, NULL);

--echo # run backup
--source include/ndb_backup.inc

drop table text2text;
create table text2text (pk int primary key, 
                        t1 text, t2 mediumtext, t3 longtext, t4 tinytext,
                        t5 mediumtext, t6 longtext, t7 tinytext, t8 text,
                        t9 longtext, t10 tinytext, t11 text, t12 mediumtext,
                        t13 text, t14 mediumtext, t15 longtext, t16 tinytext)engine=NDB;

--echo # run restore
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r --promote-attributes --lossy-conversions $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r --promote-attributes --lossy-conversions $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT

--echo # run ndb_select_all to print restored table 
--exec $NDB_SELECT_ALL -d test text2text -o PRIMARY
--echo # run select query to see truncation in mysqld
select LENGTH(t1), LENGTH(t2), LENGTH(t3), LENGTH(t4), LENGTH(t5), LENGTH(t6), LENGTH(t7), LENGTH(t8), LENGTH(t9), LENGTH(t10), LENGTH(t11), LENGTH(t12) from text2text order by pk;

--echo # run restore with no lossy conversions
drop table text2text;
create table text2text (pk int primary key, 
                        t1 text, t2 mediumtext, t3 longtext, t4 tinytext,
                        t5 mediumtext, t6 longtext, t7 tinytext, t8 text,
                        t9 longtext, t10 tinytext, t11 text, t12 mediumtext,
                        t13 text, t14 mediumtext, t15 longtext, t16 tinytext)engine=NDB;

--echo # run restore
--error 1
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r --promote-attributes $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT 2>&1
--echo # ndb_restore fails with error 1
--error 1
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r --promote-attributes $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT 2>&1
--echo # ndb_restore fails with error 1


--echo # restore NULL values into non-nullable columns 
drop table text2text;
create table text2text (pk int primary key, 
                        t1 text not null, t2 mediumtext not null, t3 longtext not null, t4 tinytext not null,
                        t5 mediumtext not null, t6 longtext not null, t7 tinytext not null, t8 text not null,
                        t9 longtext not null, t10 tinytext not null, t11 text not null, t12 mediumtext not null,
                        t13 text not null, t14 mediumtext not null, t15 longtext not null, t16 tinytext not null)engine=NDB;
--echo # run restore
--error 1
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r --promote-attributes --lossy-conversions $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT 2>&1
--echo # ndb_restore fails with error 1, staging table suffixes 'ST1-<id>' depend upon total #ldms
--error 0,1
--exec $NDB_DROP_TABLE -d test text2text\$ST1-1-0 >> $NDB_TOOLS_OUTPUT
--error 0,1
--exec $NDB_DROP_TABLE -d test text2text\$ST1-2-0 >> $NDB_TOOLS_OUTPUT
--error 0,1
--exec $NDB_DROP_TABLE -d test text2text\$ST1-3-0 >> $NDB_TOOLS_OUTPUT
--error 0,1
--exec $NDB_DROP_TABLE -d test text2text\$ST1-4-0 >> $NDB_TOOLS_OUTPUT
--error 1
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r --promote-attributes --lossy-conversions $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT 2>&1
--echo # ndb_restore fails with error 1, staging table suffixes 'ST1-<id>' depend upon total #ldms
--error 0,1
--exec $NDB_DROP_TABLE -d test text2text\$ST2-1-0 >> $NDB_TOOLS_OUTPUT
--error 0,1
--exec $NDB_DROP_TABLE -d test text2text\$ST2-2-0 >> $NDB_TOOLS_OUTPUT
--error 0,1
--exec $NDB_DROP_TABLE -d test text2text\$ST2-3-0 >> $NDB_TOOLS_OUTPUT
--error 0,1
--exec $NDB_DROP_TABLE -d test text2text\$ST2-4-0 >> $NDB_TOOLS_OUTPUT

--echo # restore to text column with different charset 
drop table text2text;
create table text2text (pk int primary key, 
                        t1 text character set utf8mb3, t2 mediumtext character set utf8mb3, t3 longtext character set utf8mb3, t4 tinytext character set utf8mb3,
                        t5 mediumtext character set utf8mb3, t6 longtext character set utf8mb3, t7 tinytext character set utf8mb3, t8 text character set utf8mb3,
                        t9 longtext character set utf8mb3, t10 tinytext character set utf8mb3, t11 text character set utf8mb3, t12 mediumtext character set utf8mb3,
                        t13 text character set utf8mb3, t14 mediumtext character set utf8mb3, t15 longtext character set utf8mb3, t16 tinytext character set utf8mb3)engine=NDB;

--echo # run restore
--error 1
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r --promote-attributes --lossy-conversions $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT 2>&1
--echo # ndb_restore fails with error 1
--error 1
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r --promote-attributes --lossy-conversions $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT 2>&1
--echo # ndb_restore fails with error 1

--echo # restore with mix of staging and built-in conversions 
drop table text2text;
create table text2text (pk mediumint primary key, 
                        t1 text, t2 mediumtext, t3 longtext, t4 tinytext,
                        t5 mediumtext, t6 longtext, t7 tinytext, t8 text,
                        t9 longtext, t10 tinytext, t11 text, t12 mediumtext,
                        t13 text, t14 mediumtext, t15 longtext, t16 tinytext)engine=NDB;

--echo # run restore
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r --lossy-conversions --promote-attributes $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT 2>&1
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r --lossy-conversions --promote-attributes $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT 2>&1

--echo # run ndb_select_all to print restored table 
--exec $NDB_SELECT_ALL -d test text2text -o PRIMARY

drop table text2text;

--echo # restore with pk built-in conversion and blob staging conversion
create table text2text(id char(10) character set latin1 primary key, val text)engine=ndb;
insert into text2text values("1111111111", REPEAT("A", 500));
insert into text2text values("2222222222", REPEAT("B", 500));

--echo # run backup
--source include/ndb_backup.inc

drop table text2text;
create table text2text(id varchar(5) character set latin1 primary key, val mediumtext)engine=ndb;

--echo # run restore
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r --promote-attributes --lossy-conversions $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r --promote-attributes --lossy-conversions $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT

--echo # run ndb_select_all to print restored table 
--exec $NDB_SELECT_ALL -d test text2text -o PRIMARY

--echo # ndb_restore TEXT -> TINYTEXT truncation with utf8mb3
drop table text2text;
set character_set_client=utf8mb3;
create table text2text(id int primary key, val text character set utf8mb3)engine=ndb;
insert into text2text values(11111, REPEAT(UNHEX("E38182"), 100));
insert into text2text values(22222, CONCAT("A",REPEAT(UNHEX("E38182"), 100)));

--echo # run backup
--source include/ndb_backup.inc

drop table text2text;
create table text2text(id int primary key, val tinytext character set utf8mb3)engine=ndb;

--echo # run restore
--exec $NDB_RESTORE -b $the_backup_id -n 1 -r --promote-attributes --lossy-conversions $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT
--exec $NDB_RESTORE -b $the_backup_id -n 2 -r --promote-attributes --lossy-conversions $NDB_BACKUPS-$the_backup_id >> $NDB_TOOLS_OUTPUT

--echo # run ndb_select_all to see charset-aware truncation in ndb
--exec $NDB_SELECT_ALL -d test text2text -o PRIMARY

--echo # run mysql select to see charset-aware truncation in mysqld
--echo # truncation in pk-based read
select length(val), char_length(val) from text2text where id = 22222;
--echo # truncation in scan
select length(val), char_length(val) from text2text order by id;

drop table text2text;

--echo # drop tablespace

alter tablespace ts1
drop datafile 'datafile1.dat';

drop tablespace ts1;

drop logfile group lg1
engine=ndb;
--source suite/ndb/include/backup_restore_cleanup.inc
--remove_file $NDB_TOOLS_OUTPUT
