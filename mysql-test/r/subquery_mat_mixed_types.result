#
# Bug#13960580 SUBQUERY MATERIALIZATION IS TOO RESTRICTIVE ON DATA TYPES
#
CREATE TABLE t1 (c1 INT, KEY(c1));
CREATE TABLE t2 (c1 BIGINT, KEY(c1));
CREATE TABLE t3 (c1 DECIMAL(10,2), <PERSON>E<PERSON>(c1));
CREATE TABLE t4 (c1 FLOAT, KEY(c1));
CREATE TABLE t5 (c1 DOUBLE, KEY(c1));
CREATE TABLE t6 (c1 CHAR(60), KEY(c1));
CREATE TABLE t7 (c1 VARCHAR(60), KEY(c1));
CREATE TABLE t8 (c1 TIME, KEY(c1));
CREATE TABLE t9 (c1 TIMESTAMP, KEY(c1));
CREATE TABLE t10 (c1 DATE, KEY(c1));
CREATE TABLE t11 (c1 DATETIM<PERSON>, <PERSON><PERSON><PERSON>(c1));
CREATE TABLE t12 (c1 CHAR(10) CHARACTER SET UTF16, <PERSON><PERSON><PERSON>(c1));
CREATE TABLE t13 (c1 BIGINT UNSIGNED, <PERSON>E<PERSON>(c1));
INSERT INTO t1 VALUES (19910113), (20010514), (19930513), (19970416), (19960416),
(19950414);
INSERT INTO t2 VALUES (19930513), (19990419), (19950414), (-1), (-19950414);
INSERT INTO t3 VALUES (19930513.3), (19990519), (19950414.0), (19950414.1);
INSERT INTO t4 VALUES (19930513.3), (19990419.2), (19950414e0), (19950414.1e0);
INSERT INTO t5 VALUES (19930513.3), (19990419.2), (19950414e0), (19950414.1e0);
INSERT INTO t6 VALUES ('19910111'), ('20010513'), ('19930513'), ('19950414'),
('19950414.1');
INSERT INTO t7 VALUES ('19910111'), ('20010513'), ('19930513'), ('19950414'),
('19950414.1');
INSERT INTO t8 VALUES ('10:22:33'), ('12:34:56'), ('33:22:33');
INSERT INTO t9 VALUES (20150413102233), (19990102123456);
INSERT INTO t10 VALUES ('1998-01-01'), ('2015-04-13');
INSERT INTO t11 VALUES ('1999-08-14 01:00:00'), ('2015-04-13 10:22:33'),
('2015-04-14 09:22:33');
INSERT INTO t12 VALUES ('19910111'), ('19930513'), ('20010513'), ('19950414')
, ('19950414.1');
INSERT INTO t13 VALUES (19950414),(18446744073709551615);
ANALYZE TABLE t1, t2, t3, t4, t5, t6, t7, t8, t9, t10, t11, t12, t13;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
test.t4	analyze	status	OK
test.t5	analyze	status	OK
test.t6	analyze	status	OK
test.t7	analyze	status	OK
test.t8	analyze	status	OK
test.t9	analyze	status	OK
test.t10	analyze	status	OK
test.t11	analyze	status	OK
test.t12	analyze	status	OK
test.t13	analyze	status	OK
set optimizer_switch='semijoin=off,materialization=on,subquery_materialization_cost_based=off';
SET TIMESTAMP=UNIX_TIMESTAMP(20150413000000);
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t2 WHERE t1.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t2 WHERE t1.c1=t2.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t3 WHERE t1.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t3 WHERE t1.c1=t3.c1;
c1	c1
19950414	19950414.00
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t4 WHERE t1.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t4 WHERE t1.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t5 WHERE t1.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t5 WHERE t1.c1=t5.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t6 WHERE t1.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t6 WHERE t1.c1=t6.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t6);
c1
19930513
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t7 WHERE t1.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t7 WHERE t1.c1=t7.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t7);
c1
19930513
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t8 WHERE t1.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t8 WHERE t1.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t9 WHERE t1.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t9 WHERE t1.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t10 WHERE t1.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t10 WHERE t1.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t11 WHERE t1.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t11 WHERE t1.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t12 WHERE t1.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t12 WHERE t1.c1=t12.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t12);
c1
19930513
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t13 WHERE t1.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t13 WHERE t1.c1=t13.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t1 WHERE t2.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t1 WHERE t2.c1=t1.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t3 WHERE t2.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t3 WHERE t2.c1=t3.c1;
c1	c1
19950414	19950414.00
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t4 WHERE t2.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t4 WHERE t2.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t5 WHERE t2.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t5 WHERE t2.c1=t5.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t6 WHERE t2.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t6 WHERE t2.c1=t6.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t6);
c1
19930513
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t7 WHERE t2.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t7 WHERE t2.c1=t7.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t7);
c1
19930513
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t8 WHERE t2.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t8 WHERE t2.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t9 WHERE t2.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t9 WHERE t2.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t10 WHERE t2.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t10 WHERE t2.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t11 WHERE t2.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t11 WHERE t2.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t12 WHERE t2.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t12 WHERE t2.c1=t12.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t12);
c1
19930513
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t13 WHERE t2.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t13 WHERE t2.c1=t13.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t1 WHERE t3.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t1 WHERE t3.c1=t1.c1;
c1	c1
19950414.00	19950414
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t1);
c1
19950414.00
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19950414.00
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t2 WHERE t3.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t2 WHERE t3.c1=t2.c1;
c1	c1
19950414.00	19950414
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t2);
c1
19950414.00
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19950414.00
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t4 WHERE t3.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t4 WHERE t3.c1=t4.c1;
c1	c1
19950414.00	19950400
19950414.00	19950400
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414.00
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414.00
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t5 WHERE t3.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t5 WHERE t3.c1=t5.c1;
c1	c1
19930513.30	19930513.3
19950414.00	19950414
19950414.10	19950414.1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t5);
c1
19930513.30
19950414.00
19950414.10
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19930513.30
19950414.00
19950414.10
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t6 WHERE t3.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t6 WHERE t3.c1=t6.c1;
c1	c1
19950414.00	19950414
19950414.10	19950414.1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t6);
c1
19950414.00
19950414.10
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19950414.00
19950414.10
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t7 WHERE t3.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t7 WHERE t3.c1=t7.c1;
c1	c1
19950414.00	19950414
19950414.10	19950414.1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t7);
c1
19950414.00
19950414.10
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19950414.00
19950414.10
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t8 WHERE t3.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t8 WHERE t3.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t9 WHERE t3.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t9 WHERE t3.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t10 WHERE t3.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t10 WHERE t3.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t11 WHERE t3.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t11 WHERE t3.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t12 WHERE t3.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t12 WHERE t3.c1=t12.c1;
c1	c1
19950414.00	19950414
19950414.10	19950414.1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t12);
c1
19950414.00
19950414.10
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19950414.00
19950414.10
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t13 WHERE t3.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t13 WHERE t3.c1=t13.c1;
c1	c1
19950414.00	19950414
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414.00
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414.00
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t1 WHERE t4.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t1 WHERE t4.c1=t1.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t1);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t2 WHERE t4.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t2 WHERE t4.c1=t2.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t2);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t3 WHERE t4.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t3 WHERE t4.c1=t3.c1;
c1	c1
19950400	19950414.00
19950400	19950414.00
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t5 WHERE t4.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t5 WHERE t4.c1=t5.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t6 WHERE t4.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t6 WHERE t4.c1=t6.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t6);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t7 WHERE t4.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t7 WHERE t4.c1=t7.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t7);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t8 WHERE t4.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t8 WHERE t4.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t9 WHERE t4.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t9 WHERE t4.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t10 WHERE t4.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t10 WHERE t4.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t11 WHERE t4.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t11 WHERE t4.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t12 WHERE t4.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t12 WHERE t4.c1=t12.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t12);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t13 WHERE t4.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t13 WHERE t4.c1=t13.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t1 WHERE t5.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t1 WHERE t5.c1=t1.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t1);
c1
19950414
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19950414
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t2 WHERE t5.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t2 WHERE t5.c1=t2.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t2);
c1
19950414
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19950414
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t3 WHERE t5.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t3 WHERE t5.c1=t3.c1;
c1	c1
19930513.3	19930513.30
19950414	19950414.00
19950414.1	19950414.10
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t3);
c1
19930513.3
19950414
19950414.1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19930513.3
19950414
19950414.1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t4 WHERE t5.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t4 WHERE t5.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t6 WHERE t5.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t6 WHERE t5.c1=t6.c1;
c1	c1
19950414	19950414
19950414.1	19950414.1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t6);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t7 WHERE t5.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t7 WHERE t5.c1=t7.c1;
c1	c1
19950414	19950414
19950414.1	19950414.1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t7);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t8 WHERE t5.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t8 WHERE t5.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t9 WHERE t5.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t9 WHERE t5.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t10 WHERE t5.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t10 WHERE t5.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t11 WHERE t5.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t11 WHERE t5.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t12 WHERE t5.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t12 WHERE t5.c1=t12.c1;
c1	c1
19950414	19950414
19950414.1	19950414.1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t12);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t13 WHERE t5.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t13 WHERE t5.c1=t13.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t1 WHERE t6.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t1 WHERE t6.c1=t1.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t2 WHERE t6.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t2 WHERE t6.c1=t2.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t3 WHERE t6.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t3 WHERE t6.c1=t3.c1;
c1	c1
19950414	19950414.00
19950414.1	19950414.10
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t4 WHERE t6.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t4 WHERE t6.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t5 WHERE t6.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t5 WHERE t6.c1=t5.c1;
c1	c1
19950414	19950414
19950414.1	19950414.1
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t7 WHERE t6.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t7 WHERE t6.c1=t7.c1;
c1	c1
19910111	19910111
19930513	19930513
19950414	19950414
19950414.1	19950414.1
20010513	20010513
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t7);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t8 WHERE t6.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t8 WHERE t6.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t9 WHERE t6.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t9 WHERE t6.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t10 WHERE t6.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t10 WHERE t6.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t11 WHERE t6.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t11 WHERE t6.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t12 WHERE t6.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t12 WHERE t6.c1=t12.c1;
c1	c1
19910111	19910111
19930513	19930513
19950414	19950414
19950414.1	19950414.1
20010513	20010513
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t12);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t13 WHERE t6.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t13 WHERE t6.c1=t13.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t1 WHERE t7.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t1 WHERE t7.c1=t1.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t2 WHERE t7.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t2 WHERE t7.c1=t2.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t3 WHERE t7.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t3 WHERE t7.c1=t3.c1;
c1	c1
19950414	19950414.00
19950414.1	19950414.10
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t4 WHERE t7.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t4 WHERE t7.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t5 WHERE t7.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t5 WHERE t7.c1=t5.c1;
c1	c1
19950414	19950414
19950414.1	19950414.1
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t6 WHERE t7.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t6 WHERE t7.c1=t6.c1;
c1	c1
19910111	19910111
19930513	19930513
19950414	19950414
19950414.1	19950414.1
20010513	20010513
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t6);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t8 WHERE t7.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t8 WHERE t7.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t9 WHERE t7.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t9 WHERE t7.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t10 WHERE t7.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t10 WHERE t7.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t11 WHERE t7.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t11 WHERE t7.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t12 WHERE t7.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t12 WHERE t7.c1=t12.c1;
c1	c1
19910111	19910111
19930513	19930513
19950414	19950414
19950414.1	19950414.1
20010513	20010513
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t12);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t13 WHERE t7.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t13 WHERE t7.c1=t13.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t1 WHERE t8.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t1 WHERE t8.c1=t1.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t2 WHERE t8.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t2 WHERE t8.c1=t2.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t3 WHERE t8.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t3 WHERE t8.c1=t3.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t4 WHERE t8.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t4 WHERE t8.c1=t4.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t5 WHERE t8.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t5 WHERE t8.c1=t5.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t6 WHERE t8.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t6 WHERE t8.c1=t6.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t7 WHERE t8.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t7 WHERE t8.c1=t7.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t9 WHERE t8.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t9 WHERE t8.c1=t9.c1;
c1	c1
10:22:33	2015-04-13 10:22:33
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t9);
c1
10:22:33
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
10:22:33
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t10 WHERE t8.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t10 WHERE t8.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t11 WHERE t8.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t11 WHERE t8.c1=t11.c1;
c1	c1
10:22:33	2015-04-13 10:22:33
33:22:33	2015-04-14 09:22:33
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t11);
c1
10:22:33
33:22:33
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
10:22:33
33:22:33
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t12 WHERE t8.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t12 WHERE t8.c1=t12.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t13 WHERE t8.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t13 WHERE t8.c1=t13.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t1 WHERE t9.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t1 WHERE t9.c1=t1.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t2 WHERE t9.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t2 WHERE t9.c1=t2.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t3 WHERE t9.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t3 WHERE t9.c1=t3.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t4 WHERE t9.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t4 WHERE t9.c1=t4.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t5 WHERE t9.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t5 WHERE t9.c1=t5.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t6 WHERE t9.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t6 WHERE t9.c1=t6.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t7 WHERE t9.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t7 WHERE t9.c1=t7.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t8 WHERE t9.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t8 WHERE t9.c1=t8.c1;
c1	c1
2015-04-13 10:22:33	10:22:33
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t8);
c1
2015-04-13 10:22:33
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
2015-04-13 10:22:33
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t10 WHERE t9.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t10 WHERE t9.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t11 WHERE t9.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t11 WHERE t9.c1=t11.c1;
c1	c1
2015-04-13 10:22:33	2015-04-13 10:22:33
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t11);
c1
2015-04-13 10:22:33
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
2015-04-13 10:22:33
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t12 WHERE t9.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t12 WHERE t9.c1=t12.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t13 WHERE t9.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t13 WHERE t9.c1=t13.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t1 WHERE t10.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t1 WHERE t10.c1=t1.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t2 WHERE t10.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t2 WHERE t10.c1=t2.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t3 WHERE t10.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t3 WHERE t10.c1=t3.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t4 WHERE t10.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t4 WHERE t10.c1=t4.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t5 WHERE t10.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t5 WHERE t10.c1=t5.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t6 WHERE t10.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t6 WHERE t10.c1=t6.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t7 WHERE t10.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t7 WHERE t10.c1=t7.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t8 WHERE t10.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t8 WHERE t10.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t9 WHERE t10.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t9 WHERE t10.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t11 WHERE t10.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t11 WHERE t10.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t12 WHERE t10.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t12 WHERE t10.c1=t12.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t13 WHERE t10.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t13 WHERE t10.c1=t13.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t1 WHERE t11.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t1 WHERE t11.c1=t1.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
1
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t2 WHERE t11.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t2 WHERE t11.c1=t2.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t3 WHERE t11.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t3 WHERE t11.c1=t3.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t4 WHERE t11.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t4 WHERE t11.c1=t4.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t5 WHERE t11.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t5 WHERE t11.c1=t5.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t6 WHERE t11.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t6 WHERE t11.c1=t6.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
1
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t7 WHERE t11.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t7 WHERE t11.c1=t7.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
1
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t8 WHERE t11.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t8 WHERE t11.c1=t8.c1;
c1	c1
2015-04-13 10:22:33	10:22:33
2015-04-14 09:22:33	33:22:33
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t8);
c1
2015-04-13 10:22:33
2015-04-14 09:22:33
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
2015-04-13 10:22:33
2015-04-14 09:22:33
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t9 WHERE t11.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t9 WHERE t11.c1=t9.c1;
c1	c1
2015-04-13 10:22:33	2015-04-13 10:22:33
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t9);
c1
2015-04-13 10:22:33
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
2015-04-13 10:22:33
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
1
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t10 WHERE t11.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t10 WHERE t11.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
1
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t12 WHERE t11.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t12 WHERE t11.c1=t12.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t13 WHERE t11.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t13 WHERE t11.c1=t13.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t1 WHERE t12.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t1 WHERE t12.c1=t1.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t2 WHERE t12.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t2 WHERE t12.c1=t2.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t3 WHERE t12.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t3 WHERE t12.c1=t3.c1;
c1	c1
19950414	19950414.00
19950414.1	19950414.10
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t4 WHERE t12.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t4 WHERE t12.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t5 WHERE t12.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t5 WHERE t12.c1=t5.c1;
c1	c1
19950414	19950414
19950414.1	19950414.1
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t6 WHERE t12.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t6 WHERE t12.c1=t6.c1;
c1	c1
19910111	19910111
19930513	19930513
19950414	19950414
19950414.1	19950414.1
20010513	20010513
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t6);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t7 WHERE t12.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t7 WHERE t12.c1=t7.c1;
c1	c1
19910111	19910111
19930513	19930513
19950414	19950414
19950414.1	19950414.1
20010513	20010513
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t7);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t8 WHERE t12.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t8 WHERE t12.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t9 WHERE t12.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t9 WHERE t12.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t10 WHERE t12.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t10 WHERE t12.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t11 WHERE t12.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t11 WHERE t12.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t13 WHERE t12.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t13 WHERE t12.c1=t13.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t1 WHERE t13.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t1 WHERE t13.c1=t1.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t1);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t2 WHERE t13.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t2 WHERE t13.c1=t2.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t2);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t3 WHERE t13.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t3 WHERE t13.c1=t3.c1;
c1	c1
19950414	19950414.00
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t4 WHERE t13.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t4 WHERE t13.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t5 WHERE t13.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t5 WHERE t13.c1=t5.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t6 WHERE t13.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t6 WHERE t13.c1=t6.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t6);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t7 WHERE t13.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t7 WHERE t13.c1=t7.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t7);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t8 WHERE t13.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t8 WHERE t13.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t9 WHERE t13.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t9 WHERE t13.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t10 WHERE t13.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t10 WHERE t13.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t11 WHERE t13.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t11 WHERE t13.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t12 WHERE t13.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t12 WHERE t13.c1=t12.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t12);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
SET TIMESTAMP=UNIX_TIMESTAMP(20140413000000);
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t9 WHERE t8.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t9 WHERE t8.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t8 WHERE t9.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t8 WHERE t9.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t8);
c1
set optimizer_switch='semijoin=on,firstmatch=off,loosescan=off,duplicateweedout=off,materialization=on,subquery_materialization_cost_based=off';
SET TIMESTAMP=UNIX_TIMESTAMP(20150413000000);
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t2 WHERE t1.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t2 WHERE t1.c1=t2.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t3 WHERE t1.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t3 WHERE t1.c1=t3.c1;
c1	c1
19950414	19950414.00
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t4 WHERE t1.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t4 WHERE t1.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t5 WHERE t1.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t5 WHERE t1.c1=t5.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t6 WHERE t1.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t6 WHERE t1.c1=t6.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t6);
c1
19930513
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t7 WHERE t1.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t7 WHERE t1.c1=t7.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t7);
c1
19930513
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t8 WHERE t1.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t8 WHERE t1.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t9 WHERE t1.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t9 WHERE t1.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t10 WHERE t1.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t10 WHERE t1.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t11 WHERE t1.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t11 WHERE t1.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t12 WHERE t1.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t12 WHERE t1.c1=t12.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t12);
c1
19930513
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t1 STRAIGHT_JOIN t13 WHERE t1.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 STRAIGHT_JOIN t13 WHERE t1.c1=t13.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t1 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t1 WHERE (SELECT 1 FROM t1 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t1 WHERE t2.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t1 WHERE t2.c1=t1.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t3 WHERE t2.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t3 WHERE t2.c1=t3.c1;
c1	c1
19950414	19950414.00
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t4 WHERE t2.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t4 WHERE t2.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t5 WHERE t2.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t5 WHERE t2.c1=t5.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t6 WHERE t2.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t6 WHERE t2.c1=t6.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t6);
c1
19930513
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t7 WHERE t2.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t7 WHERE t2.c1=t7.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t7);
c1
19930513
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t8 WHERE t2.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t8 WHERE t2.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t9 WHERE t2.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t9 WHERE t2.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t10 WHERE t2.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t10 WHERE t2.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t11 WHERE t2.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t11 WHERE t2.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t12 WHERE t2.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t12 WHERE t2.c1=t12.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t12);
c1
19930513
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t2 STRAIGHT_JOIN t13 WHERE t2.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 STRAIGHT_JOIN t13 WHERE t2.c1=t13.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t2 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t2 WHERE (SELECT 1 FROM t2 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t1 WHERE t3.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t1 WHERE t3.c1=t1.c1;
c1	c1
19950414.00	19950414
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t1);
c1
19950414.00
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19950414.00
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t2 WHERE t3.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t2 WHERE t3.c1=t2.c1;
c1	c1
19950414.00	19950414
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t2);
c1
19950414.00
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19950414.00
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t4 WHERE t3.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t4 WHERE t3.c1=t4.c1;
c1	c1
19950414.00	19950400
19950414.00	19950400
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414.00
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414.00
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t5 WHERE t3.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t5 WHERE t3.c1=t5.c1;
c1	c1
19930513.30	19930513.3
19950414.00	19950414
19950414.10	19950414.1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t5);
c1
19930513.30
19950414.00
19950414.10
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19930513.30
19950414.00
19950414.10
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t6 WHERE t3.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t6 WHERE t3.c1=t6.c1;
c1	c1
19950414.00	19950414
19950414.10	19950414.1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t6);
c1
19950414.00
19950414.10
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19950414.00
19950414.10
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t7 WHERE t3.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t7 WHERE t3.c1=t7.c1;
c1	c1
19950414.00	19950414
19950414.10	19950414.1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t7);
c1
19950414.00
19950414.10
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19950414.00
19950414.10
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t8 WHERE t3.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t8 WHERE t3.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t9 WHERE t3.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t9 WHERE t3.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t10 WHERE t3.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t10 WHERE t3.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t11 WHERE t3.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t11 WHERE t3.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t12 WHERE t3.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t12 WHERE t3.c1=t12.c1;
c1	c1
19950414.00	19950414
19950414.10	19950414.1
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t12);
c1
19950414.00
19950414.10
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19950414.00
19950414.10
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t3 STRAIGHT_JOIN t13 WHERE t3.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 STRAIGHT_JOIN t13 WHERE t3.c1=t13.c1;
c1	c1
19950414.00	19950414
EXPLAIN SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414.00
EXPLAIN SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t3 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414.00
EXPLAIN SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t3 WHERE (SELECT 1 FROM t3 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t1 WHERE t4.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t1 WHERE t4.c1=t1.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t1);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t2 WHERE t4.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t2 WHERE t4.c1=t2.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t2);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t3 WHERE t4.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t3 WHERE t4.c1=t3.c1;
c1	c1
19950400	19950414.00
19950400	19950414.00
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t5 WHERE t4.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t5 WHERE t4.c1=t5.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t6 WHERE t4.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t6 WHERE t4.c1=t6.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t6);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t7 WHERE t4.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t7 WHERE t4.c1=t7.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t7);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t8 WHERE t4.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t8 WHERE t4.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t9 WHERE t4.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t9 WHERE t4.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t10 WHERE t4.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t10 WHERE t4.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t11 WHERE t4.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t11 WHERE t4.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t12 WHERE t4.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t12 WHERE t4.c1=t12.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t12);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN t13 WHERE t4.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 STRAIGHT_JOIN t13 WHERE t4.c1=t13.c1;
c1	c1
19950400	19950414
19950400	19950414
EXPLAIN SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950400
19950400
EXPLAIN SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t4 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950400
EXPLAIN SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t4 WHERE (SELECT 1 FROM t4 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t1 WHERE t5.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t1 WHERE t5.c1=t1.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t1);
c1
19950414
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19950414
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t2 WHERE t5.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t2 WHERE t5.c1=t2.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t2);
c1
19950414
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19950414
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t3 WHERE t5.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t3 WHERE t5.c1=t3.c1;
c1	c1
19930513.3	19930513.30
19950414	19950414.00
19950414.1	19950414.10
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t3);
c1
19930513.3
19950414
19950414.1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19930513.3
19950414
19950414.1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t4 WHERE t5.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t4 WHERE t5.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t6 WHERE t5.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t6 WHERE t5.c1=t6.c1;
c1	c1
19950414	19950414
19950414.1	19950414.1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t6);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t7 WHERE t5.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t7 WHERE t5.c1=t7.c1;
c1	c1
19950414	19950414
19950414.1	19950414.1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t7);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t8 WHERE t5.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t8 WHERE t5.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t9 WHERE t5.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t9 WHERE t5.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t10 WHERE t5.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t10 WHERE t5.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t11 WHERE t5.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t11 WHERE t5.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t12 WHERE t5.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t12 WHERE t5.c1=t12.c1;
c1	c1
19950414	19950414
19950414.1	19950414.1
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t12);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t5 STRAIGHT_JOIN t13 WHERE t5.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 STRAIGHT_JOIN t13 WHERE t5.c1=t13.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t5 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t5 WHERE (SELECT 1 FROM t5 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t1 WHERE t6.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t1 WHERE t6.c1=t1.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t2 WHERE t6.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t2 WHERE t6.c1=t2.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t3 WHERE t6.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t3 WHERE t6.c1=t3.c1;
c1	c1
19950414	19950414.00
19950414.1	19950414.10
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t4 WHERE t6.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t4 WHERE t6.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t5 WHERE t6.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t5 WHERE t6.c1=t5.c1;
c1	c1
19950414	19950414
19950414.1	19950414.1
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t7 WHERE t6.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t7 WHERE t6.c1=t7.c1;
c1	c1
19910111	19910111
19930513	19930513
19950414	19950414
19950414.1	19950414.1
20010513	20010513
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t7);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t8 WHERE t6.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t8 WHERE t6.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t9 WHERE t6.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t9 WHERE t6.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t10 WHERE t6.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t10 WHERE t6.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t11 WHERE t6.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t11 WHERE t6.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t12 WHERE t6.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t12 WHERE t6.c1=t12.c1;
c1	c1
19910111	19910111
19930513	19930513
19950414	19950414
19950414.1	19950414.1
20010513	20010513
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t12);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t6 STRAIGHT_JOIN t13 WHERE t6.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 STRAIGHT_JOIN t13 WHERE t6.c1=t13.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t6 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t6 WHERE (SELECT 1 FROM t6 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t1 WHERE t7.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t1 WHERE t7.c1=t1.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t2 WHERE t7.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t2 WHERE t7.c1=t2.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t3 WHERE t7.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t3 WHERE t7.c1=t3.c1;
c1	c1
19950414	19950414.00
19950414.1	19950414.10
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t4 WHERE t7.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t4 WHERE t7.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t5 WHERE t7.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t5 WHERE t7.c1=t5.c1;
c1	c1
19950414	19950414
19950414.1	19950414.1
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t6 WHERE t7.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t6 WHERE t7.c1=t6.c1;
c1	c1
19910111	19910111
19930513	19930513
19950414	19950414
19950414.1	19950414.1
20010513	20010513
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t6);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t8 WHERE t7.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t8 WHERE t7.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t9 WHERE t7.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t9 WHERE t7.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t10 WHERE t7.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t10 WHERE t7.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t11 WHERE t7.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t11 WHERE t7.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
1
1
1
1
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t12 WHERE t7.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t12 WHERE t7.c1=t12.c1;
c1	c1
19910111	19910111
19930513	19930513
19950414	19950414
19950414.1	19950414.1
20010513	20010513
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t12);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t7 STRAIGHT_JOIN t13 WHERE t7.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 STRAIGHT_JOIN t13 WHERE t7.c1=t13.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t7 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t7 WHERE (SELECT 1 FROM t7 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t1 WHERE t8.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t1 WHERE t8.c1=t1.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t2 WHERE t8.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t2 WHERE t8.c1=t2.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t3 WHERE t8.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t3 WHERE t8.c1=t3.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t4 WHERE t8.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t4 WHERE t8.c1=t4.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t5 WHERE t8.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t5 WHERE t8.c1=t5.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t6 WHERE t8.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t6 WHERE t8.c1=t6.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t7 WHERE t8.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t7 WHERE t8.c1=t7.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t9 WHERE t8.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t9 WHERE t8.c1=t9.c1;
c1	c1
10:22:33	2015-04-13 10:22:33
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t9);
c1
10:22:33
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
10:22:33
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t10 WHERE t8.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t10 WHERE t8.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t11 WHERE t8.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t11 WHERE t8.c1=t11.c1;
c1	c1
10:22:33	2015-04-13 10:22:33
33:22:33	2015-04-14 09:22:33
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t11);
c1
10:22:33
33:22:33
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
10:22:33
33:22:33
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t12 WHERE t8.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t12 WHERE t8.c1=t12.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t13 WHERE t8.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t13 WHERE t8.c1=t13.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t8 WHERE (SELECT 1 FROM t8 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t1 WHERE t9.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t1 WHERE t9.c1=t1.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t2 WHERE t9.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t2 WHERE t9.c1=t2.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t3 WHERE t9.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t3 WHERE t9.c1=t3.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t4 WHERE t9.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t4 WHERE t9.c1=t4.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t5 WHERE t9.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t5 WHERE t9.c1=t5.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t6 WHERE t9.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t6 WHERE t9.c1=t6.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t7 WHERE t9.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t7 WHERE t9.c1=t7.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t8 WHERE t9.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t8 WHERE t9.c1=t8.c1;
c1	c1
2015-04-13 10:22:33	10:22:33
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t8);
c1
2015-04-13 10:22:33
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
2015-04-13 10:22:33
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t10 WHERE t9.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t10 WHERE t9.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t11 WHERE t9.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t11 WHERE t9.c1=t11.c1;
c1	c1
2015-04-13 10:22:33	2015-04-13 10:22:33
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t11);
c1
2015-04-13 10:22:33
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
2015-04-13 10:22:33
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t12 WHERE t9.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t12 WHERE t9.c1=t12.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t13 WHERE t9.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t13 WHERE t9.c1=t13.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t9 WHERE (SELECT 1 FROM t9 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t1 WHERE t10.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t1 WHERE t10.c1=t1.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t2 WHERE t10.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t2 WHERE t10.c1=t2.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t3 WHERE t10.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t3 WHERE t10.c1=t3.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t4 WHERE t10.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t4 WHERE t10.c1=t4.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t5 WHERE t10.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t5 WHERE t10.c1=t5.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t6 WHERE t10.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t6 WHERE t10.c1=t6.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t7 WHERE t10.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t7 WHERE t10.c1=t7.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t8 WHERE t10.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t8 WHERE t10.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t9 WHERE t10.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t9 WHERE t10.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t11 WHERE t10.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t11 WHERE t10.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
1
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t12 WHERE t10.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t12 WHERE t10.c1=t12.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t10 STRAIGHT_JOIN t13 WHERE t10.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 STRAIGHT_JOIN t13 WHERE t10.c1=t13.c1;
c1	c1
EXPLAIN SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 WHERE c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t10 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t10 WHERE (SELECT 1 FROM t10 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t1 WHERE t11.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t1 WHERE t11.c1=t1.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t1);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
1
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t2 WHERE t11.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t2 WHERE t11.c1=t2.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t2);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t3 WHERE t11.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t3 WHERE t11.c1=t3.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t3);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t4 WHERE t11.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t4 WHERE t11.c1=t4.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t4);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t5 WHERE t11.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t5 WHERE t11.c1=t5.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t5);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t6 WHERE t11.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t6 WHERE t11.c1=t6.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t6);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
1
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t7 WHERE t11.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t7 WHERE t11.c1=t7.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t7);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
1
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t8 WHERE t11.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t8 WHERE t11.c1=t8.c1;
c1	c1
2015-04-13 10:22:33	10:22:33
2015-04-14 09:22:33	33:22:33
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t8);
c1
2015-04-13 10:22:33
2015-04-14 09:22:33
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
2015-04-13 10:22:33
2015-04-14 09:22:33
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t9 WHERE t11.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t9 WHERE t11.c1=t9.c1;
c1	c1
2015-04-13 10:22:33	2015-04-13 10:22:33
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t9);
c1
2015-04-13 10:22:33
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
2015-04-13 10:22:33
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
1
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t10 WHERE t11.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t10 WHERE t11.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
1
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t12 WHERE t11.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t12 WHERE t11.c1=t12.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t12);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t11 STRAIGHT_JOIN t13 WHERE t11.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 STRAIGHT_JOIN t13 WHERE t11.c1=t13.c1;
c1	c1
EXPLAIN SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 WHERE c1 IN (SELECT c1 FROM t13);
c1
EXPLAIN SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t11 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
DELETE FROM t11 WHERE c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t11 WHERE (SELECT 1 FROM t11 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
INSERT INTO t11 SET c1='2015-04-14 09:22:33';
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t1 WHERE t12.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t1 WHERE t12.c1=t1.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t2 WHERE t12.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t2 WHERE t12.c1=t2.c1;
c1	c1
19930513	19930513
19950414	19950414
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19930513
19950414
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t3 WHERE t12.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t3 WHERE t12.c1=t3.c1;
c1	c1
19950414	19950414.00
19950414.1	19950414.10
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t4 WHERE t12.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t4 WHERE t12.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t5 WHERE t12.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t5 WHERE t12.c1=t5.c1;
c1	c1
19950414	19950414
19950414.1	19950414.1
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950414
19950414.1
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950414
19950414.1
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t6 WHERE t12.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t6 WHERE t12.c1=t6.c1;
c1	c1
19910111	19910111
19930513	19930513
19950414	19950414
19950414.1	19950414.1
20010513	20010513
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t6);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t7 WHERE t12.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t7 WHERE t12.c1=t7.c1;
c1	c1
19910111	19910111
19930513	19930513
19950414	19950414
19950414.1	19950414.1
20010513	20010513
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t7);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19910111
19930513
19950414
19950414.1
20010513
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t8 WHERE t12.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t8 WHERE t12.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t9 WHERE t12.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t9 WHERE t12.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t10 WHERE t12.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t10 WHERE t12.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t11 WHERE t12.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t11 WHERE t12.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t12 STRAIGHT_JOIN t13 WHERE t12.c1=t13.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 STRAIGHT_JOIN t13 WHERE t12.c1=t13.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 WHERE c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t12 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t13);
c1
19950414
EXPLAIN SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t12 WHERE (SELECT 1 FROM t12 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t13 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t1 WHERE t13.c1=t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t1 WHERE t13.c1=t1.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t1);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t1);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t1 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t2 WHERE t13.c1=t2.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t2 WHERE t13.c1=t2.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t2);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t2);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t2 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t3 WHERE t13.c1=t3.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t3 WHERE t13.c1=t3.c1;
c1	c1
19950414	19950414.00
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t3);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t3);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t3 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t4 WHERE t13.c1=t4.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t4 WHERE t13.c1=t4.c1;
c1	c1
19950414	19950400
19950414	19950400
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t4);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t4 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t5 WHERE t13.c1=t5.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t5 WHERE t13.c1=t5.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t5);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t5);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t5 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t6 WHERE t13.c1=t6.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t6 WHERE t13.c1=t6.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t6);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t6);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t6 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t7 WHERE t13.c1=t7.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t7 WHERE t13.c1=t7.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t7);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t7);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t7 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t8 WHERE t13.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t8 WHERE t13.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t8);
c1
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t8 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t9 WHERE t13.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t9 WHERE t13.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t9 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t10 WHERE t13.c1=t10.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t10 WHERE t13.c1=t10.c1;
c1	c1
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t10);
c1
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t10 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t11 WHERE t13.c1=t11.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t11 WHERE t13.c1=t11.c1;
c1	c1
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t11);
c1
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t11 WHERE ASCII(c1) = 50);
1
EXPLAIN SELECT * FROM t13 STRAIGHT_JOIN t12 WHERE t13.c1=t12.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 STRAIGHT_JOIN t12 WHERE t13.c1=t12.c1;
c1	c1
19950414	19950414
EXPLAIN SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 WHERE c1 IN (SELECT c1 FROM t12);
c1
19950414
EXPLAIN SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	DEPENDENT SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t13 GROUP BY c1 HAVING c1 IN (SELECT c1 FROM t12);
c1
19950414
EXPLAIN SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	PRIMARY	#	#	#	#	#	#	#	#	#	#
#	SUBQUERY	#	#	#	#	#	#	#	#	#	#
SELECT 1 FROM t13 WHERE (SELECT 1 FROM t13 WHERE ASCII(c1) = 50) IN
(SELECT 1 FROM t12 WHERE ASCII(c1) = 50);
1
SET TIMESTAMP=UNIX_TIMESTAMP(20140413000000);
EXPLAIN SELECT * FROM t8 STRAIGHT_JOIN t9 WHERE t8.c1=t9.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 STRAIGHT_JOIN t9 WHERE t8.c1=t9.c1;
c1	c1
EXPLAIN SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t9);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	MATERIALIZED	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t8 WHERE c1 IN (SELECT c1 FROM t9);
c1
EXPLAIN SELECT * FROM t9 STRAIGHT_JOIN t8 WHERE t9.c1=t8.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 STRAIGHT_JOIN t8 WHERE t9.c1=t8.c1;
c1	c1
EXPLAIN SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
#	SIMPLE	#	#	#	#	#	#	#	#	#	#
SELECT * FROM t9 WHERE c1 IN (SELECT c1 FROM t8);
c1
DROP TABLE t1, t2, t3, t4, t5, t6, t7, t8, t9, t10, t11, t12, t13;
