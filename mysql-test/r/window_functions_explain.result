# Explain of SQL window functions.
# ----------------------------------------------------------------------
SET NAMES utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Single window function (plus ORDER BY).
CREATE TABLE t(i INT, j INT);
INSERT INTO t VALUES (1,1), (1,4), (1,2), (1,4);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
Single partition, no sorting
EXPLAIN FORMAT=JSON SELECT i, j, SUM(i+j) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.65"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "sum"
          ]
        }
      ],
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 4,
        "rows_produced_per_join": 4,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.40",
          "prefix_cost": "0.65",
          "data_read_per_join": "64"
        },
        "used_columns": [
          "i",
          "j"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t`
EXPLAIN FORMAT=TRADITIONAL SELECT i, j, SUM(i+j) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	NULL
Warnings:
Note	3598	To get information about window functions use EXPLAIN FORMAT=JSON
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT i, j, SUM(i+j) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) foo FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.65"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "sum"
          ]
        }
      ],
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 4,
        "rows_produced_per_join": 4,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.40",
          "prefix_cost": "0.65",
          "data_read_per_join": "64"
        },
        "used_columns": [
          "i",
          "j"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT i, j, SUM(i+j) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.65"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "4.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "functions": [
              "sum"
            ]
          }
        ],
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 4,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.40",
            "prefix_cost": "0.65",
            "data_read_per_join": "64"
          },
          "used_columns": [
            "i",
            "j"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t` order by `foo`
EXPLAIN FORMAT=JSON SELECT i, j, SUM(i+j) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo DESC;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.65"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "4.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "functions": [
              "sum"
            ]
          }
        ],
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 4,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.40",
            "prefix_cost": "0.65",
            "data_read_per_join": "64"
          },
          "used_columns": [
            "i",
            "j"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t` order by `foo` desc
With LIMIT
EXPLAIN FORMAT=JSON SELECT i, j, SUM(i+j) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo DESC LIMIT 3;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.65"
    },
    "ordering_operation": {
      "using_filesort": true,
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "functions": [
              "sum"
            ]
          }
        ],
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 4,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.40",
            "prefix_cost": "0.65",
            "data_read_per_join": "64"
          },
          "used_columns": [
            "i",
            "j"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t` order by `foo` desc limit 3
Single ordered partition
EXPLAIN FORMAT=JSON SELECT i, j, SUM(i+j) OVER (ORDER BY j ROWS UNBOUNDED PRECEDING) foo FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.65"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`j`"
          ],
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "4.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 4,
        "rows_produced_per_join": 4,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.40",
          "prefix_cost": "0.65",
          "data_read_per_join": "64"
        },
        "used_columns": [
          "i",
          "j"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ORDER BY `test`.`t`.`j` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT i, j, SUM(i+j) OVER (ORDER BY j ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "8.65"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "4.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`j`"
            ],
            "functions": [
              "sum"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "4.00"
        },
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 4,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.40",
            "prefix_cost": "0.65",
            "data_read_per_join": "64"
          },
          "used_columns": [
            "i",
            "j"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ORDER BY `test`.`t`.`j` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t` order by `foo`
EXPLAIN FORMAT=JSON SELECT i, j, SUM(i+j) OVER (ORDER BY j ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo DESC;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "8.65"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "4.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`j`"
            ],
            "functions": [
              "sum"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "4.00"
        },
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 4,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.40",
            "prefix_cost": "0.65",
            "data_read_per_join": "64"
          },
          "used_columns": [
            "i",
            "j"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ORDER BY `test`.`t`.`j` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t` order by `foo` desc
EXPLAIN FORMAT=JSON SELECT i, j, SUM(i+j) OVER (ORDER BY j DESC ROWS UNBOUNDED PRECEDING) foo FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.65"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`j` desc"
          ],
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "4.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 4,
        "rows_produced_per_join": 4,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.40",
          "prefix_cost": "0.65",
          "data_read_per_join": "64"
        },
        "used_columns": [
          "i",
          "j"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ORDER BY `test`.`t`.`j` desc ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t`
EXPLAIN FORMAT=TRADITIONAL SELECT i, j, SUM(i+j) OVER (ORDER BY j DESC ROWS UNBOUNDED PRECEDING) foo FROM t;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	Using filesort
Warnings:
Note	3598	To get information about window functions use EXPLAIN FORMAT=JSON
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ORDER BY `test`.`t`.`j` desc ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t`
View with window function
CREATE VIEW v AS
SELECT i, j, SUM(i+j) OVER (ORDER BY j DESC ROWS UNBOUNDED PRECEDING) foo FROM t;
SHOW CREATE VIEW v;
View	Create View	character_set_client	collation_connection
v	CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v` AS select `t`.`i` AS `i`,`t`.`j` AS `j`,sum((`t`.`i` + `t`.`j`)) OVER (ORDER BY `t`.`j` desc ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `t`	utf8mb3	utf8mb3_general_ci
EXPLAIN FORMAT=JSON SELECT * FROM v;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.95"
    },
    "table": {
      "table_name": "v",
      "access_type": "ALL",
      "rows_examined_per_scan": 4,
      "rows_produced_per_join": 4,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "2.55",
        "eval_cost": "0.40",
        "prefix_cost": "2.95",
        "data_read_per_join": "128"
      },
      "used_columns": [
        "i",
        "j",
        "foo"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "4.65"
          },
          "windowing": {
            "windows": [
              {
                "name": "<unnamed window>",
                "using_filesort": true,
                "filesort_key": [
                  "`j` desc"
                ],
                "functions": [
                  "sum"
                ]
              }
            ],
            "cost_info": {
              "sort_cost": "4.00"
            },
            "table": {
              "table_name": "t",
              "access_type": "ALL",
              "rows_examined_per_scan": 4,
              "rows_produced_per_join": 4,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.40",
                "prefix_cost": "0.65",
                "data_read_per_join": "64"
              },
              "used_columns": [
                "i",
                "j"
              ]
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`v`.`i` AS `i`,`test`.`v`.`j` AS `j`,`test`.`v`.`foo` AS `foo` from `test`.`v`
DROP VIEW v;
EXPLAIN FORMAT=JSON SELECT i, j, SUM(i+j) OVER (ORDER BY j DESC ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "8.65"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "4.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`j` desc"
            ],
            "functions": [
              "sum"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "4.00"
        },
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 4,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.40",
            "prefix_cost": "0.65",
            "data_read_per_join": "64"
          },
          "used_columns": [
            "i",
            "j"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ORDER BY `test`.`t`.`j` desc ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t` order by `foo`
EXPLAIN FORMAT=JSON SELECT i, j, SUM(i+j) OVER (ORDER BY j DESC ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo DESC;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "8.65"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "4.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`j` desc"
            ],
            "functions": [
              "sum"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "4.00"
        },
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 4,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.40",
            "prefix_cost": "0.65",
            "data_read_per_join": "64"
          },
          "used_columns": [
            "i",
            "j"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ORDER BY `test`.`t`.`j` desc ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t` order by `foo` desc
TRUNCATE TABLE t;
Check my_decimal bug: no warning if c=a+b and c is one of a,b... just fails over 9 digits
INSERT INTO t VALUES (999961560, DEFAULT), (44721, DEFAULT);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT SUM(i) OVER () FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 2,
        "rows_produced_per_join": 2,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.20",
          "prefix_cost": "0.45",
          "data_read_per_join": "32"
        },
        "used_columns": [
          "i"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sum(`test`.`t`.`i`) OVER ()  AS `SUM(i) OVER ()` from `test`.`t`
DROP TABLE t;
CREATE TABLE t(i INT, j INT, k INT);
INSERT INTO t VALUES (1,1,1), (1,4,1), (1,2,1), (1,4,1);
INSERT INTO t VALUES (1,1,2), (1,4,2), (1,2,2), (1,4,2);
INSERT INTO t VALUES (1,1,3), (1,4,3), (1,2,3), (1,4,3);
INSERT INTO t VALUES (1,1,4), (1,4,4), (1,2,4), (1,4,4);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
----------------------------------------------------------------------
-    Combination with GROUP BY
----------------------------------------------------------------------
EXPLAIN FORMAT=JSON SELECT k, SUM(k) OVER (ROWS UNBOUNDED PRECEDING) wf FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "sum"
          ]
        }
      ],
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 16,
        "rows_produced_per_join": 16,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.60",
          "prefix_cost": "1.85",
          "data_read_per_join": "256"
        },
        "used_columns": [
          "k"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`k` AS `k`,sum(`test`.`t`.`k`) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `wf` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT k, MIN(i), SUM(j), SUM(k) OVER (ROWS UNBOUNDED PRECEDING) wf FROM t GROUP BY (k);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "sum"
          ]
        }
      ],
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 16,
          "rows_produced_per_join": 16,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "1.60",
            "prefix_cost": "1.85",
            "data_read_per_join": "256"
          },
          "used_columns": [
            "i",
            "j",
            "k"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`k` AS `k`,min(`test`.`t`.`i`) AS `MIN(i)`,sum(`test`.`t`.`j`) AS `SUM(j)`,sum(`test`.`t`.`k`) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `wf` from `test`.`t` group by `test`.`t`.`k`
EXPLAIN FORMAT=JSON SELECT k, MIN(i), SUM(j), SUM(k) OVER (ROWS UNBOUNDED PRECEDING) wf FROM t GROUP BY (k) ORDER BY wf DESC;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.85"
    },
    "ordering_operation": {
      "using_filesort": true,
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "functions": [
              "sum"
            ]
          }
        ],
        "grouping_operation": {
          "using_temporary_table": true,
          "using_filesort": false,
          "table": {
            "table_name": "t",
            "access_type": "ALL",
            "rows_examined_per_scan": 16,
            "rows_produced_per_join": 16,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "1.60",
              "prefix_cost": "1.85",
              "data_read_per_join": "256"
            },
            "used_columns": [
              "i",
              "j",
              "k"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`k` AS `k`,min(`test`.`t`.`i`) AS `MIN(i)`,sum(`test`.`t`.`j`) AS `SUM(j)`,sum(`test`.`t`.`k`) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `wf` from `test`.`t` group by `test`.`t`.`k` order by `wf` desc
EXPLAIN FORMAT=JSON SELECT k, GROUP_CONCAT(j ORDER BY j), SUM(k) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t GROUP BY (k);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "17.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "sum"
          ]
        }
      ],
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": true,
        "cost_info": {
          "sort_cost": "16.00"
        },
        "buffer_result": {
          "using_temporary_table": true,
          "table": {
            "table_name": "t",
            "access_type": "ALL",
            "rows_examined_per_scan": 16,
            "rows_produced_per_join": 16,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "1.60",
              "prefix_cost": "1.85",
              "data_read_per_join": "256"
            },
            "used_columns": [
              "j",
              "k"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`k` AS `k`,group_concat(`test`.`t`.`j` order by `test`.`t`.`j` ASC separator ',') AS `GROUP_CONCAT(j ORDER BY j)`,sum(`test`.`t`.`k`) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t` group by `test`.`t`.`k`
EXPLAIN FORMAT=JSON SELECT k, AVG(DISTINCT j), SUM(k) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t GROUP BY (k);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "17.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "sum"
          ]
        }
      ],
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": true,
        "cost_info": {
          "sort_cost": "16.00"
        },
        "buffer_result": {
          "using_temporary_table": true,
          "table": {
            "table_name": "t",
            "access_type": "ALL",
            "rows_examined_per_scan": 16,
            "rows_produced_per_join": 16,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "1.60",
              "prefix_cost": "1.85",
              "data_read_per_join": "256"
            },
            "used_columns": [
              "j",
              "k"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`k` AS `k`,avg(distinct `test`.`t`.`j`) AS `AVG(DISTINCT j)`,sum(`test`.`t`.`k`) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t` group by `test`.`t`.`k`
EXPLAIN FORMAT=JSON SELECT k, GROUP_CONCAT(j ORDER BY j), SUM(k+1) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t GROUP BY (k);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "17.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "sum"
          ]
        }
      ],
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": true,
        "cost_info": {
          "sort_cost": "16.00"
        },
        "buffer_result": {
          "using_temporary_table": true,
          "table": {
            "table_name": "t",
            "access_type": "ALL",
            "rows_examined_per_scan": 16,
            "rows_produced_per_join": 16,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "1.60",
              "prefix_cost": "1.85",
              "data_read_per_join": "256"
            },
            "used_columns": [
              "j",
              "k"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`k` AS `k`,group_concat(`test`.`t`.`j` order by `test`.`t`.`j` ASC separator ',') AS `GROUP_CONCAT(j ORDER BY j)`,sum((`test`.`t`.`k` + 1)) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t` group by `test`.`t`.`k`
EXPLAIN FORMAT=JSON SELECT k, GROUP_CONCAT(j ORDER BY j), SUM(k+1) OVER (ORDER BY k DESC ROWS UNBOUNDED PRECEDING) foo FROM t GROUP BY (k);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "33.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`k` desc"
          ],
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "16.00"
      },
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": true,
        "cost_info": {
          "sort_cost": "16.00"
        },
        "buffer_result": {
          "using_temporary_table": true,
          "table": {
            "table_name": "t",
            "access_type": "ALL",
            "rows_examined_per_scan": 16,
            "rows_produced_per_join": 16,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "1.60",
              "prefix_cost": "1.85",
              "data_read_per_join": "256"
            },
            "used_columns": [
              "j",
              "k"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`k` AS `k`,group_concat(`test`.`t`.`j` order by `test`.`t`.`j` ASC separator ',') AS `GROUP_CONCAT(j ORDER BY j)`,sum((`test`.`t`.`k` + 1)) OVER (ORDER BY `test`.`t`.`k` desc ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t` group by `test`.`t`.`k`
DROP TABLE t;
----------------------------------------------------------------------
-    Some RANK, DENSE_RANK tests
----------------------------------------------------------------------
CREATE TABLE t1 (id INTEGER, sex CHAR(1));
INSERT INTO t1 VALUES (1, 'M'),
(2, 'F'),
(3, 'F'),
(4, 'F'),
(5, 'M');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
CREATE TABLE t2 (user_id INTEGER NOT NULL, date DATE);
INSERT INTO t2 VALUES (1, '2002-06-09'),
(2, '2002-06-09'),
(1, '2002-06-09'),
(3, '2002-06-09'),
(4, '2002-06-09'),
(4, '2002-06-09'),
(5, '2002-06-09');
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT RANK() OVER (ORDER BY user_id) r FROM t2;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`user_id`"
          ],
          "functions": [
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "user_id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select rank() OVER (ORDER BY `test`.`t2`.`user_id` )  AS `r` from `test`.`t2`
EXPLAIN FORMAT=JSON SELECT DENSE_RANK() OVER (ORDER BY user_id) r FROM t2;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`user_id`"
          ],
          "functions": [
            "dense_rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "user_id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select dense_rank() OVER (ORDER BY `test`.`t2`.`user_id` )  AS `r` from `test`.`t2`
EXPLAIN FORMAT=JSON SELECT sex, SUM(DISTINCT id) AS uids FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex ORDER BY uids;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.50"
    },
    "ordering_operation": {
      "using_filesort": true,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": true,
        "buffer_result": {
          "using_temporary_table": true,
          "nested_loop": [
            {
              "table": {
                "table_name": "u",
                "access_type": "ALL",
                "rows_examined_per_scan": 5,
                "rows_produced_per_join": 5,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.50",
                  "prefix_cost": "0.75",
                  "data_read_per_join": "80"
                },
                "used_columns": [
                  "id",
                  "sex"
                ]
              }
            },
            {
              "table": {
                "table_name": "t2",
                "access_type": "ALL",
                "rows_examined_per_scan": 7,
                "rows_produced_per_join": 5,
                "filtered": "14.29",
                "using_join_buffer": "hash join",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.50",
                  "prefix_cost": "4.50",
                  "data_read_per_join": "80"
                },
                "used_columns": [
                  "user_id"
                ],
                "attached_condition": "(`test`.`t2`.`user_id` = `test`.`u`.`id`)"
              }
            }
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`u`.`sex` AS `sex`,sum(distinct `test`.`t1`.`id`) AS `uids` from `test`.`t1` `u` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`u`.`id`) group by `test`.`u`.`sex` order by `uids`
EXPLAIN FORMAT=JSON SELECT id, sex, RANK() OVER (ORDER BY sex) FROM t1 ORDER BY id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.75"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "5.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`sex`"
            ],
            "functions": [
              "rank"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "5.00"
        },
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "80"
          },
          "used_columns": [
            "id",
            "sex"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`sex` AS `sex`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `RANK() OVER (ORDER BY sex)` from `test`.`t1` order by `test`.`t1`.`id`
EXPLAIN FORMAT=JSON SELECT id, sex, DENSE_RANK() OVER (ORDER BY sex) FROM t1 ORDER BY id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.75"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "5.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`sex`"
            ],
            "functions": [
              "dense_rank"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "5.00"
        },
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "80"
          },
          "used_columns": [
            "id",
            "sex"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`sex` AS `sex`,dense_rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `DENSE_RANK() OVER (ORDER BY sex)` from `test`.`t1` order by `test`.`t1`.`id`
EXPLAIN FORMAT=JSON SELECT sex, RANK() OVER (ORDER BY sex DESC) `rank`, AVG(DISTINCT id) AS uids FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex ORDER BY sex;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "19.50"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "5.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`sex` desc"
            ],
            "functions": [
              "rank"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "5.00"
        },
        "grouping_operation": {
          "using_temporary_table": true,
          "using_filesort": true,
          "cost_info": {
            "sort_cost": "5.00"
          },
          "buffer_result": {
            "using_temporary_table": true,
            "nested_loop": [
              {
                "table": {
                  "table_name": "u",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 5,
                  "rows_produced_per_join": 5,
                  "filtered": "100.00",
                  "cost_info": {
                    "read_cost": "0.25",
                    "eval_cost": "0.50",
                    "prefix_cost": "0.75",
                    "data_read_per_join": "80"
                  },
                  "used_columns": [
                    "id",
                    "sex"
                  ]
                }
              },
              {
                "table": {
                  "table_name": "t2",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 7,
                  "rows_produced_per_join": 5,
                  "filtered": "14.29",
                  "using_join_buffer": "hash join",
                  "cost_info": {
                    "read_cost": "0.25",
                    "eval_cost": "0.50",
                    "prefix_cost": "4.50",
                    "data_read_per_join": "80"
                  },
                  "used_columns": [
                    "user_id"
                  ],
                  "attached_condition": "(`test`.`t2`.`user_id` = `test`.`u`.`id`)"
                }
              }
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`u`.`sex` AS `sex`,rank() OVER (ORDER BY `test`.`u`.`sex` desc )  AS `rank`,avg(distinct `test`.`t1`.`id`) AS `uids` from `test`.`t1` `u` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`u`.`id`) group by `test`.`u`.`sex` order by `test`.`u`.`sex`
Explicit window definition, WINDOW DESC ordering from GROUP BY
EXPLAIN FORMAT=JSON SELECT  sex, AVG(id) AS uids, RANK() OVER w `rank` FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex
WINDOW w AS (ORDER BY AVG(id));
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "9.50"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "avg(`id`)"
          ],
          "functions": [
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "5.00"
      },
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "nested_loop": [
          {
            "table": {
              "table_name": "u",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.50",
                "prefix_cost": "0.75",
                "data_read_per_join": "80"
              },
              "used_columns": [
                "id",
                "sex"
              ]
            }
          },
          {
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 5,
              "filtered": "14.29",
              "using_join_buffer": "hash join",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.50",
                "prefix_cost": "4.50",
                "data_read_per_join": "80"
              },
              "used_columns": [
                "user_id"
              ],
              "attached_condition": "(`test`.`t2`.`user_id` = `test`.`u`.`id`)"
            }
          }
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`u`.`sex` AS `sex`,avg(`test`.`u`.`id`) AS `uids`,rank() OVER `w` AS `rank` from `test`.`t1` `u` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`u`.`id`) group by `test`.`u`.`sex` window `w` AS (ORDER BY avg(`test`.`u`.`id`) ) 
Explicit window definition, window ordering from DISTINCT GROUP BY
EXPLAIN FORMAT=JSON SELECT  sex, AVG(DISTINCT id) AS uids, RANK() OVER w `rank` FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex
WINDOW w AS (ORDER BY AVG(DISTINCT id) DESC) ORDER BY sex;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "19.50"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "5.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "w",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "avg(distinct `id`) desc"
            ],
            "functions": [
              "rank"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "5.00"
        },
        "grouping_operation": {
          "using_temporary_table": true,
          "using_filesort": true,
          "cost_info": {
            "sort_cost": "5.00"
          },
          "buffer_result": {
            "using_temporary_table": true,
            "nested_loop": [
              {
                "table": {
                  "table_name": "u",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 5,
                  "rows_produced_per_join": 5,
                  "filtered": "100.00",
                  "cost_info": {
                    "read_cost": "0.25",
                    "eval_cost": "0.50",
                    "prefix_cost": "0.75",
                    "data_read_per_join": "80"
                  },
                  "used_columns": [
                    "id",
                    "sex"
                  ]
                }
              },
              {
                "table": {
                  "table_name": "t2",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 7,
                  "rows_produced_per_join": 5,
                  "filtered": "14.29",
                  "using_join_buffer": "hash join",
                  "cost_info": {
                    "read_cost": "0.25",
                    "eval_cost": "0.50",
                    "prefix_cost": "4.50",
                    "data_read_per_join": "80"
                  },
                  "used_columns": [
                    "user_id"
                  ],
                  "attached_condition": "(`test`.`t2`.`user_id` = `test`.`u`.`id`)"
                }
              }
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`u`.`sex` AS `sex`,avg(distinct `test`.`t1`.`id`) AS `uids`,rank() OVER `w` AS `rank` from `test`.`t1` `u` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`u`.`id`) group by `test`.`u`.`sex` window `w` AS (ORDER BY avg(distinct `test`.`t1`.`id`) desc )  order by `test`.`u`.`sex`
Explicit window definition, window ordering from GROUP BY, final ORDER BY
EXPLAIN FORMAT=JSON SELECT  sex, AVG(id) AS uids, RANK() OVER w `rank` FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex
WINDOW w AS (ORDER BY AVG(id) DESC)
ORDER BY `rank` DESC;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "9.50"
    },
    "ordering_operation": {
      "using_filesort": true,
      "windowing": {
        "windows": [
          {
            "name": "w",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "avg(`id`) desc"
            ],
            "functions": [
              "rank"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "5.00"
        },
        "grouping_operation": {
          "using_temporary_table": true,
          "using_filesort": false,
          "nested_loop": [
            {
              "table": {
                "table_name": "u",
                "access_type": "ALL",
                "rows_examined_per_scan": 5,
                "rows_produced_per_join": 5,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.50",
                  "prefix_cost": "0.75",
                  "data_read_per_join": "80"
                },
                "used_columns": [
                  "id",
                  "sex"
                ]
              }
            },
            {
              "table": {
                "table_name": "t2",
                "access_type": "ALL",
                "rows_examined_per_scan": 7,
                "rows_produced_per_join": 5,
                "filtered": "14.29",
                "using_join_buffer": "hash join",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.50",
                  "prefix_cost": "4.50",
                  "data_read_per_join": "80"
                },
                "used_columns": [
                  "user_id"
                ],
                "attached_condition": "(`test`.`t2`.`user_id` = `test`.`u`.`id`)"
              }
            }
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`u`.`sex` AS `sex`,avg(`test`.`u`.`id`) AS `uids`,rank() OVER `w` AS `rank` from `test`.`t1` `u` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`u`.`id`) group by `test`.`u`.`sex` window `w` AS (ORDER BY avg(`test`.`u`.`id`) desc )  order by `rank` desc
EXPLAIN FORMAT=TRADITIONAL SELECT  sex, AVG(id) AS uids, RANK() OVER w `rank` FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex
WINDOW w AS (ORDER BY AVG(id) DESC)
ORDER BY `rank` DESC;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	u	NULL	ALL	NULL	NULL	NULL	NULL	5	100.00	Using temporary; Using filesort
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	7	14.29	Using where; Using join buffer (hash join)
Warnings:
Note	3598	To get information about window functions use EXPLAIN FORMAT=JSON
Note	1003	/* select#1 */ select `test`.`u`.`sex` AS `sex`,avg(`test`.`u`.`id`) AS `uids`,rank() OVER `w` AS `rank` from `test`.`t1` `u` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`u`.`id`) group by `test`.`u`.`sex` window `w` AS (ORDER BY avg(`test`.`u`.`id`) desc )  order by `rank` desc
With NULLs
INSERT INTO t1 VALUES (10, NULL), (11, NULL);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT id, sex, RANK() OVER w, DENSE_RANK() OVER w FROM t1
WINDOW w AS (ORDER BY sex) ORDER BY id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "14.95"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "7.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "w",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`sex`"
            ],
            "functions": [
              "rank",
              "dense_rank"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "7.00"
        },
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 7,
          "rows_produced_per_join": 7,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.70",
            "prefix_cost": "0.95",
            "data_read_per_join": "112"
          },
          "used_columns": [
            "id",
            "sex"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`sex` AS `sex`,rank() OVER `w` AS `RANK() OVER w`,dense_rank() OVER `w` AS `DENSE_RANK() OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`sex` )  order by `test`.`t1`.`id`
EXPLAIN FORMAT=JSON SELECT id, sex, RANK() OVER (ORDER BY sex DESC) FROM t1 ORDER BY id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "14.95"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "7.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`sex` desc"
            ],
            "functions": [
              "rank"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "7.00"
        },
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 7,
          "rows_produced_per_join": 7,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.70",
            "prefix_cost": "0.95",
            "data_read_per_join": "112"
          },
          "used_columns": [
            "id",
            "sex"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`sex` AS `sex`,rank() OVER (ORDER BY `test`.`t1`.`sex` desc )  AS `RANK() OVER (ORDER BY sex DESC)` from `test`.`t1` order by `test`.`t1`.`id`
EXPLAIN FORMAT=JSON SELECT id value,
SUM(id) OVER (ROWS UNBOUNDED PRECEDING)
FROM t1 u, t2 WHERE t2.user_id = u.id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.10"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "sum"
          ]
        }
      ],
      "nested_loop": [
        {
          "table": {
            "table_name": "u",
            "access_type": "ALL",
            "rows_examined_per_scan": 7,
            "rows_produced_per_join": 7,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.70",
              "prefix_cost": "0.95",
              "data_read_per_join": "112"
            },
            "used_columns": [
              "id"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 7,
            "rows_produced_per_join": 7,
            "filtered": "14.29",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.70",
              "prefix_cost": "6.10",
              "data_read_per_join": "112"
            },
            "used_columns": [
              "user_id"
            ],
            "attached_condition": "(`test`.`t2`.`user_id` = `test`.`u`.`id`)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`u`.`id` AS `value`,sum(`test`.`u`.`id`) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `SUM(id) OVER (ROWS UNBOUNDED PRECEDING)` from `test`.`t1` `u` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`u`.`id`)
Aggregate with GROUP BY arguments to window function
EXPLAIN FORMAT=JSON SELECT AVG(id) average,
SUM(AVG(id)) OVER (ROWS UNBOUNDED PRECEDING)
FROM t1 u, t2 WHERE t2.user_id = u.id GROUP BY sex;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.10"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "sum"
          ]
        }
      ],
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "nested_loop": [
          {
            "table": {
              "table_name": "u",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "0.95",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "id",
                "sex"
              ]
            }
          },
          {
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "14.29",
              "using_join_buffer": "hash join",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "6.10",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "user_id"
              ],
              "attached_condition": "(`test`.`t2`.`user_id` = `test`.`u`.`id`)"
            }
          }
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select avg(`test`.`u`.`id`) AS `average`,sum(avg(`test`.`u`.`id`)) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `SUM(AVG(id)) OVER (ROWS UNBOUNDED PRECEDING)` from `test`.`t1` `u` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`u`.`id`) group by `test`.`u`.`sex`
Aggregate with GROUP BY in window's ORDER BY clause, with aggregate present in
SELECT list or not.
EXPLAIN FORMAT=JSON SELECT sex, AVG(id), RANK() OVER (ORDER BY AVG(id) DESC) FROM t1 GROUP BY sex ORDER BY sex;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "14.95"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "7.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "avg(`id`) desc"
            ],
            "functions": [
              "rank"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "7.00"
        },
        "grouping_operation": {
          "using_temporary_table": true,
          "using_filesort": false,
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 7,
            "rows_produced_per_join": 7,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.70",
              "prefix_cost": "0.95",
              "data_read_per_join": "112"
            },
            "used_columns": [
              "id",
              "sex"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`sex` AS `sex`,avg(`test`.`t1`.`id`) AS `AVG(id)`,rank() OVER (ORDER BY avg(`test`.`t1`.`id`) desc )  AS `RANK() OVER (ORDER BY AVG(id) DESC)` from `test`.`t1` group by `test`.`t1`.`sex` order by `test`.`t1`.`sex`
EXPLAIN FORMAT=JSON SELECT sex, RANK() OVER (ORDER BY AVG(id) DESC) FROM t1 GROUP BY sex ORDER BY sex;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "14.95"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "7.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "avg(`id`) desc"
            ],
            "functions": [
              "rank"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "7.00"
        },
        "grouping_operation": {
          "using_temporary_table": true,
          "using_filesort": false,
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 7,
            "rows_produced_per_join": 7,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.70",
              "prefix_cost": "0.95",
              "data_read_per_join": "112"
            },
            "used_columns": [
              "id",
              "sex"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`sex` AS `sex`,rank() OVER (ORDER BY avg(`test`.`t1`.`id`) desc )  AS `RANK() OVER (ORDER BY AVG(id) DESC)` from `test`.`t1` group by `test`.`t1`.`sex` order by `test`.`t1`.`sex`
Implicit group aggregate arguments to window function and in
window's ORDER BY clause
EXPLAIN FORMAT=JSON SELECT          RANK() OVER (ORDER BY AVG(id)) FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.95"
    },
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 7,
      "rows_produced_per_join": 7,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.70",
        "prefix_cost": "0.95",
        "data_read_per_join": "112"
      },
      "used_columns": [
        "id"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select rank() OVER (ORDER BY avg(`test`.`t1`.`id`) )  AS `RANK() OVER (ORDER BY AVG(id))` from `test`.`t1`
EXPLAIN FORMAT=JSON SELECT AVG(id), RANK() OVER (ORDER BY AVG(id)) FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.95"
    },
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 7,
      "rows_produced_per_join": 7,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.70",
        "prefix_cost": "0.95",
        "data_read_per_join": "112"
      },
      "used_columns": [
        "id"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select avg(`test`.`t1`.`id`) AS `AVG(id)`,rank() OVER (ORDER BY avg(`test`.`t1`.`id`) )  AS `RANK() OVER (ORDER BY AVG(id))` from `test`.`t1`
EXPLAIN FORMAT=JSON SELECT AVG(id), SUM(AVG(id)) OVER (ORDER BY AVG(id) ROWS UNBOUNDED PRECEDING) FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.95"
    },
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 7,
      "rows_produced_per_join": 7,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.70",
        "prefix_cost": "0.95",
        "data_read_per_join": "112"
      },
      "used_columns": [
        "id"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select avg(`test`.`t1`.`id`) AS `AVG(id)`,sum(avg(`test`.`t1`.`id`)) OVER (ORDER BY avg(`test`.`t1`.`id`) ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `SUM(AVG(id)) OVER (ORDER BY AVG(id) ROWS UNBOUNDED PRECEDING)` from `test`.`t1`
Several partitions, several window functions over the same window
EXPLAIN FORMAT=JSON SELECT sex, id, RANK() OVER (PARTITION BY sex ORDER BY id DESC) FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id` desc"
          ],
          "functions": [
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`sex` AS `sex`,`test`.`t1`.`id` AS `id`,rank() OVER (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` desc )  AS `RANK() OVER (PARTITION BY sex ORDER BY id DESC)` from `test`.`t1`
EXPLAIN FORMAT=JSON SELECT sex, id, RANK() OVER (PARTITION BY sex ORDER BY id ASC) FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "functions": [
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`sex` AS `sex`,`test`.`t1`.`id` AS `id`,rank() OVER (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` )  AS `RANK() OVER (PARTITION BY sex ORDER BY id ASC)` from `test`.`t1`
EXPLAIN FORMAT=JSON SELECT sex, id, SUM(id) OVER w summ, RANK() OVER w `rank` FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id ASC ROWS UNBOUNDED PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "functions": [
            "sum",
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	3599	Window function 'rank' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	1003	/* select#1 */ select `test`.`t1`.`sex` AS `sex`,`test`.`t1`.`id` AS `id`,sum(`test`.`t1`.`id`) OVER `w` AS `summ`,rank() OVER `w` AS `rank` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT sex, id, SUM(id) OVER w summ, RANK() OVER w `rank` FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id ASC ROWS UNBOUNDED PRECEDING) ORDER BY summ;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "14.95"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "7.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "w",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`sex`",
              "`id`"
            ],
            "functions": [
              "sum",
              "rank"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "7.00"
        },
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 7,
          "rows_produced_per_join": 7,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.70",
            "prefix_cost": "0.95",
            "data_read_per_join": "112"
          },
          "used_columns": [
            "id",
            "sex"
          ]
        }
      }
    }
  }
}
Warnings:
Note	3599	Window function 'rank' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	1003	/* select#1 */ select `test`.`t1`.`sex` AS `sex`,`test`.`t1`.`id` AS `id`,sum(`test`.`t1`.`id`) OVER `w` AS `summ`,rank() OVER `w` AS `rank` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  order by `summ`
CREATE TABLE t(d decimal(10,2), date DATE);
INSERT INTO t values  (10.4, '2002-06-09'),
(20.5, '2002-06-09'),
(10.4, '2002-06-10'),
(3,    '2002-06-09'),
(40.2, '2015-08-01'),
(40.2, '2002-06-09'),
(5,    '2015-08-01');
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT * FROM (SELECT  RANK() OVER (ORDER BY d) AS `rank`, d, date FROM t) alias ORDER BY `rank`, d, date;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.29"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "alias",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "2.59",
          "eval_cost": "0.70",
          "prefix_cost": "3.29",
          "data_read_per_join": "168"
        },
        "used_columns": [
          "rank",
          "d",
          "date"
        ],
        "materialized_from_subquery": {
          "using_temporary_table": true,
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "7.95"
            },
            "windowing": {
              "windows": [
                {
                  "name": "<unnamed window>",
                  "using_filesort": true,
                  "filesort_key": [
                    "`d`"
                  ],
                  "functions": [
                    "rank"
                  ]
                }
              ],
              "cost_info": {
                "sort_cost": "7.00"
              },
              "table": {
                "table_name": "t",
                "access_type": "ALL",
                "rows_examined_per_scan": 7,
                "rows_produced_per_join": 7,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.70",
                  "prefix_cost": "0.95",
                  "data_read_per_join": "112"
                },
                "used_columns": [
                  "d",
                  "date"
                ]
              }
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `alias`.`rank` AS `rank`,`alias`.`d` AS `d`,`alias`.`date` AS `date` from (/* select#2 */ select rank() OVER (ORDER BY `test`.`t`.`d` )  AS `rank`,`test`.`t`.`d` AS `d`,`test`.`t`.`date` AS `date` from `test`.`t`) `alias` order by `alias`.`rank`,`alias`.`d`,`alias`.`date`
EXPLAIN FORMAT=JSON SELECT * FROM (SELECT RANK() OVER (ORDER BY date) AS `rank`, date, d FROM t) alias ORDER BY `rank`, d DESC;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.29"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "alias",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "2.59",
          "eval_cost": "0.70",
          "prefix_cost": "3.29",
          "data_read_per_join": "168"
        },
        "used_columns": [
          "rank",
          "date",
          "d"
        ],
        "materialized_from_subquery": {
          "using_temporary_table": true,
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "7.95"
            },
            "windowing": {
              "windows": [
                {
                  "name": "<unnamed window>",
                  "using_filesort": true,
                  "filesort_key": [
                    "`date`"
                  ],
                  "functions": [
                    "rank"
                  ]
                }
              ],
              "cost_info": {
                "sort_cost": "7.00"
              },
              "table": {
                "table_name": "t",
                "access_type": "ALL",
                "rows_examined_per_scan": 7,
                "rows_produced_per_join": 7,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.70",
                  "prefix_cost": "0.95",
                  "data_read_per_join": "112"
                },
                "used_columns": [
                  "d",
                  "date"
                ]
              }
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `alias`.`rank` AS `rank`,`alias`.`date` AS `date`,`alias`.`d` AS `d` from (/* select#2 */ select rank() OVER (ORDER BY `test`.`t`.`date` )  AS `rank`,`test`.`t`.`date` AS `date`,`test`.`t`.`d` AS `d` from `test`.`t`) `alias` order by `alias`.`rank`,`alias`.`d` desc
DROP TABLE t;
Check that SUM stays that same when it sees NULL values
CREATE TABLE t(i INT, j INT);
INSERT INTO t VALUES (1,NULL),
(1,NULL),
(1,1),
(1,NULL),
(1,2),
(2,1),
(2,2),
(2,NULL),
(2,NULL);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT i, j, SUM(j) OVER (PARTITION BY i  ORDER BY j ROWS UNBOUNDED PRECEDING) FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`i`",
            "`j`"
          ],
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "i",
          "j"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum(`test`.`t`.`j`) OVER (PARTITION BY `test`.`t`.`i` ORDER BY `test`.`t`.`j` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `SUM(j) OVER (PARTITION BY i  ORDER BY j ROWS UNBOUNDED PRECEDING)` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT SUM(id), SUM(SUM(id)) OVER (ORDER BY sex ROWS UNBOUNDED PRECEDING) FROM t1,t2 WHERE t1.id=t2.user_id GROUP BY sex;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.10"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`sex`"
          ],
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "nested_loop": [
          {
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "0.95",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "id",
                "sex"
              ]
            }
          },
          {
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "14.29",
              "using_join_buffer": "hash join",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "6.10",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "user_id"
              ],
              "attached_condition": "(`test`.`t2`.`user_id` = `test`.`t1`.`id`)"
            }
          }
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sum(`test`.`t1`.`id`) AS `SUM(id)`,sum(sum(`test`.`t1`.`id`)) OVER (ORDER BY `test`.`t1`.`sex` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `SUM(SUM(id)) OVER (ORDER BY sex ROWS UNBOUNDED PRECEDING)` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`t1`.`id`) group by `test`.`t1`.`sex`
EXPLAIN FORMAT=JSON SELECT RANK() OVER w FROM t1,t2 WHERE t1.id=t2.user_id WINDOW w AS (PARTITION BY id ORDER BY sex);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.10"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`",
            "`sex`"
          ],
          "functions": [
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "buffer_result": {
        "using_temporary_table": true,
        "nested_loop": [
          {
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "0.95",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "id",
                "sex"
              ]
            }
          },
          {
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "14.29",
              "using_join_buffer": "hash join",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "6.10",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "user_id"
              ],
              "attached_condition": "(`test`.`t2`.`user_id` = `test`.`t1`.`id`)"
            }
          }
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select rank() OVER `w` AS `RANK() OVER w` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`t1`.`id`) window `w` AS (PARTITION BY `test`.`t1`.`id` ORDER BY `test`.`t1`.`sex` ) 
EXPLAIN FORMAT=JSON SELECT RANK() OVER w FROM (SELECT * FROM t1,t2 WHERE t1.id=t2.user_id) t WINDOW w AS (PARTITION BY id ORDER BY sex);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.10"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`",
            "`sex`"
          ],
          "functions": [
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "buffer_result": {
        "using_temporary_table": true,
        "nested_loop": [
          {
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "0.95",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "id",
                "sex"
              ]
            }
          },
          {
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "14.29",
              "using_join_buffer": "hash join",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "6.10",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "user_id"
              ],
              "attached_condition": "(`test`.`t2`.`user_id` = `test`.`t1`.`id`)"
            }
          }
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select rank() OVER `w` AS `RANK() OVER w` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`t1`.`id`) window `w` AS (PARTITION BY `test`.`t1`.`id` ORDER BY `test`.`t1`.`sex` ) 
Check that aggregate window functions that reference columns not in the SELECT list work
EXPLAIN FORMAT=JSON SELECT  SUM(id) OVER (PARTITION BY sex ORDER BY id ROWS UNBOUNDED PRECEDING) summ, sex FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sum(`test`.`t1`.`id`) OVER (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `summ`,`test`.`t1`.`sex` AS `sex` from `test`.`t1`
----------------------------------------------------------------------
-    Some ROW_NUMBER tests
----------------------------------------------------------------------
EXPLAIN FORMAT=JSON SELECT user_id, ROW_NUMBER() OVER (PARTITION BY user_id) FROM t2;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`user_id`"
          ],
          "functions": [
            "row_number"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "user_id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`user_id` AS `user_id`,row_number() OVER (PARTITION BY `test`.`t2`.`user_id` )  AS `ROW_NUMBER() OVER (PARTITION BY user_id)` from `test`.`t2`
EXPLAIN FORMAT=JSON SELECT * FROM t1,t2 WHERE t1.id=t2.user_id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.10"
    },
    "nested_loop": [
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 7,
          "rows_produced_per_join": 7,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.70",
            "prefix_cost": "0.95",
            "data_read_per_join": "112"
          },
          "used_columns": [
            "id",
            "sex"
          ]
        }
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 7,
          "rows_produced_per_join": 7,
          "filtered": "14.29",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.70",
            "prefix_cost": "6.10",
            "data_read_per_join": "112"
          },
          "used_columns": [
            "user_id",
            "date"
          ],
          "attached_condition": "(`test`.`t2`.`user_id` = `test`.`t1`.`id`)"
        }
      }
    ]
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`sex` AS `sex`,`test`.`t2`.`user_id` AS `user_id`,`test`.`t2`.`date` AS `date` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`t1`.`id`)
EXPLAIN FORMAT=JSON SELECT sex, id, date, ROW_NUMBER() OVER w AS row_no, RANK() OVER w AS `rank` FROM t1,t2
WHERE t1.id=t2.user_id
WINDOW w AS (PARTITION BY id ORDER BY sex);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.10"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`",
            "`sex`"
          ],
          "functions": [
            "row_number",
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "buffer_result": {
        "using_temporary_table": true,
        "nested_loop": [
          {
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "0.95",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "id",
                "sex"
              ]
            }
          },
          {
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "14.29",
              "using_join_buffer": "hash join",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "6.10",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "user_id",
                "date"
              ],
              "attached_condition": "(`test`.`t2`.`user_id` = `test`.`t1`.`id`)"
            }
          }
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`sex` AS `sex`,`test`.`t1`.`id` AS `id`,`test`.`t2`.`date` AS `date`,row_number() OVER `w` AS `row_no`,rank() OVER `w` AS `rank` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`t1`.`id`) window `w` AS (PARTITION BY `test`.`t1`.`id` ORDER BY `test`.`t1`.`sex` ) 
EXPLAIN FORMAT=JSON SELECT sex, id, date, ROW_NUMBER() OVER w AS row_no, RANK() OVER w AS `rank` FROM t1,t2
WHERE t1.id=t2.user_id
WINDOW w AS (PARTITION BY date ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.10"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`date`",
            "`id`"
          ],
          "functions": [
            "row_number",
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "buffer_result": {
        "using_temporary_table": true,
        "nested_loop": [
          {
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "0.95",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "id",
                "sex"
              ]
            }
          },
          {
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "14.29",
              "using_join_buffer": "hash join",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "6.10",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "user_id",
                "date"
              ],
              "attached_condition": "(`test`.`t2`.`user_id` = `test`.`t1`.`id`)"
            }
          }
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`sex` AS `sex`,`test`.`t1`.`id` AS `id`,`test`.`t2`.`date` AS `date`,row_number() OVER `w` AS `row_no`,rank() OVER `w` AS `rank` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`t1`.`id`) window `w` AS (PARTITION BY `test`.`t2`.`date` ORDER BY `test`.`t1`.`id` ) 
----------------------------------------------------------------------
-    Window function in subquery
----------------------------------------------------------------------
EXPLAIN FORMAT=JSON SELECT  date,id, RANK() OVER w AS `rank` FROM t1,t2 WINDOW w AS (PARTITION BY date ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "55.10"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`date`",
            "`id`"
          ],
          "functions": [
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "49.00"
      },
      "buffer_result": {
        "using_temporary_table": true,
        "nested_loop": [
          {
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "0.95",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "id"
              ]
            }
          },
          {
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 49,
              "filtered": "100.00",
              "using_join_buffer": "hash join",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "4.90",
                "prefix_cost": "6.10",
                "data_read_per_join": "784"
              },
              "used_columns": [
                "date"
              ]
            }
          }
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`date` AS `date`,`test`.`t1`.`id` AS `id`,rank() OVER `w` AS `rank` from `test`.`t1` join `test`.`t2` window `w` AS (PARTITION BY `test`.`t2`.`date` ORDER BY `test`.`t1`.`id` ) 
EXPLAIN FORMAT=JSON SELECT * from (SELECT  date,id, RANK() OVER w AS `rank` FROM t1,t2 WINDOW w AS (PARTITION BY date ORDER BY id)) t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "8.01"
    },
    "table": {
      "table_name": "t",
      "access_type": "ALL",
      "rows_examined_per_scan": 49,
      "rows_produced_per_join": 49,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "3.11",
        "eval_cost": "4.90",
        "prefix_cost": "8.01",
        "data_read_per_join": "1K"
      },
      "used_columns": [
        "date",
        "id",
        "rank"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "55.10"
          },
          "windowing": {
            "windows": [
              {
                "name": "w",
                "using_filesort": true,
                "filesort_key": [
                  "`date`",
                  "`id`"
                ],
                "functions": [
                  "rank"
                ]
              }
            ],
            "cost_info": {
              "sort_cost": "49.00"
            },
            "buffer_result": {
              "using_temporary_table": true,
              "nested_loop": [
                {
                  "table": {
                    "table_name": "t1",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 7,
                    "rows_produced_per_join": 7,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "0.25",
                      "eval_cost": "0.70",
                      "prefix_cost": "0.95",
                      "data_read_per_join": "112"
                    },
                    "used_columns": [
                      "id"
                    ]
                  }
                },
                {
                  "table": {
                    "table_name": "t2",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 7,
                    "rows_produced_per_join": 49,
                    "filtered": "100.00",
                    "using_join_buffer": "hash join",
                    "cost_info": {
                      "read_cost": "0.25",
                      "eval_cost": "4.90",
                      "prefix_cost": "6.10",
                      "data_read_per_join": "784"
                    },
                    "used_columns": [
                      "date"
                    ]
                  }
                }
              ]
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `t`.`date` AS `date`,`t`.`id` AS `id`,`t`.`rank` AS `rank` from (/* select#2 */ select `test`.`t2`.`date` AS `date`,`test`.`t1`.`id` AS `id`,rank() OVER `w` AS `rank` from `test`.`t1` join `test`.`t2` window `w` AS (PARTITION BY `test`.`t2`.`date` ORDER BY `test`.`t1`.`id` ) ) `t`
----------------------------------------------------------------------
-    Window function in parent and subquery
----------------------------------------------------------------------
EXPLAIN FORMAT=JSON SELECT t.*, SUM(t.`rank`) OVER (ROWS UNBOUNDED PRECEDING) FROM
(SELECT sex, id, date, ROW_NUMBER() OVER w AS row_no, RANK() OVER w AS `rank` FROM t1,t2
WHERE t1.id=t2.user_id
WINDOW w AS (PARTITION BY date ORDER BY id)
) AS t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.29"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "sum"
          ]
        }
      ],
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "2.59",
          "eval_cost": "0.70",
          "prefix_cost": "3.29",
          "data_read_per_join": "280"
        },
        "used_columns": [
          "sex",
          "id",
          "date",
          "row_no",
          "rank"
        ],
        "materialized_from_subquery": {
          "using_temporary_table": true,
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "13.10"
            },
            "windowing": {
              "windows": [
                {
                  "name": "w",
                  "using_filesort": true,
                  "filesort_key": [
                    "`date`",
                    "`id`"
                  ],
                  "functions": [
                    "row_number",
                    "rank"
                  ]
                }
              ],
              "cost_info": {
                "sort_cost": "7.00"
              },
              "buffer_result": {
                "using_temporary_table": true,
                "nested_loop": [
                  {
                    "table": {
                      "table_name": "t1",
                      "access_type": "ALL",
                      "rows_examined_per_scan": 7,
                      "rows_produced_per_join": 7,
                      "filtered": "100.00",
                      "cost_info": {
                        "read_cost": "0.25",
                        "eval_cost": "0.70",
                        "prefix_cost": "0.95",
                        "data_read_per_join": "112"
                      },
                      "used_columns": [
                        "id",
                        "sex"
                      ]
                    }
                  },
                  {
                    "table": {
                      "table_name": "t2",
                      "access_type": "ALL",
                      "rows_examined_per_scan": 7,
                      "rows_produced_per_join": 7,
                      "filtered": "14.29",
                      "using_join_buffer": "hash join",
                      "cost_info": {
                        "read_cost": "0.25",
                        "eval_cost": "0.70",
                        "prefix_cost": "6.10",
                        "data_read_per_join": "112"
                      },
                      "used_columns": [
                        "user_id",
                        "date"
                      ],
                      "attached_condition": "(`test`.`t2`.`user_id` = `test`.`t1`.`id`)"
                    }
                  }
                ]
              }
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `t`.`sex` AS `sex`,`t`.`id` AS `id`,`t`.`date` AS `date`,`t`.`row_no` AS `row_no`,`t`.`rank` AS `rank`,sum(`t`.`rank`) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `SUM(t.``rank``) OVER (ROWS UNBOUNDED PRECEDING)` from (/* select#2 */ select `test`.`t1`.`sex` AS `sex`,`test`.`t1`.`id` AS `id`,`test`.`t2`.`date` AS `date`,row_number() OVER `w` AS `row_no`,rank() OVER `w` AS `rank` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`user_id` = `test`.`t1`.`id`) window `w` AS (PARTITION BY `test`.`t2`.`date` ORDER BY `test`.`t1`.`id` ) ) `t`
----------------------------------------------------------------------
-    Multiple windows
----------------------------------------------------------------------
EXPLAIN FORMAT=JSON SELECT t1.*, RANK() OVER (ORDER BY sex), SUM(id) OVER (ORDER BY sex,id ROWS UNBOUNDED PRECEDING) FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "14.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`sex`"
          ],
          "functions": [
            "rank"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "14.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`sex` AS `sex`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `RANK() OVER (ORDER BY sex)`,sum(`test`.`t1`.`id`) OVER (ORDER BY `test`.`t1`.`sex`,`test`.`t1`.`id` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `SUM(id) OVER (ORDER BY sex,id ROWS UNBOUNDED PRECEDING)` from `test`.`t1`
EXPLAIN FORMAT=JSON SELECT * from (SELECT t1.*, SUM(id) OVER (ROWS UNBOUNDED PRECEDING), RANK() OVER (ORDER BY sex) FROM t1) alias ORDER BY id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.29"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "alias",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "2.59",
          "eval_cost": "0.70",
          "prefix_cost": "3.29",
          "data_read_per_join": "280"
        },
        "used_columns": [
          "id",
          "sex",
          "SUM(id) OVER (ROWS UNBOUNDED PRECEDING)",
          "RANK() OVER (ORDER BY sex)"
        ],
        "materialized_from_subquery": {
          "using_temporary_table": true,
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "7.95"
            },
            "windowing": {
              "windows": [
                {
                  "name": "<unnamed window>",
                  "definition_position": 1,
                  "using_temporary_table": true,
                  "functions": [
                    "sum"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 2,
                  "last_executed_window": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`sex`"
                  ],
                  "functions": [
                    "rank"
                  ]
                }
              ],
              "cost_info": {
                "sort_cost": "7.00"
              },
              "table": {
                "table_name": "t1",
                "access_type": "ALL",
                "rows_examined_per_scan": 7,
                "rows_produced_per_join": 7,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.70",
                  "prefix_cost": "0.95",
                  "data_read_per_join": "112"
                },
                "used_columns": [
                  "id",
                  "sex"
                ]
              }
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `alias`.`id` AS `id`,`alias`.`sex` AS `sex`,`alias`.`SUM(id) OVER (ROWS UNBOUNDED PRECEDING)` AS `SUM(id) OVER (ROWS UNBOUNDED PRECEDING)`,`alias`.`RANK() OVER (ORDER BY sex)` AS `RANK() OVER (ORDER BY sex)` from (/* select#2 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`sex` AS `sex`,sum(`test`.`t1`.`id`) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `SUM(id) OVER (ROWS UNBOUNDED PRECEDING)`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `RANK() OVER (ORDER BY sex)` from `test`.`t1`) `alias` order by `alias`.`id`
EXPLAIN FORMAT=JSON SELECT t1.*, SUM(id) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING),
RANK() OVER (ORDER BY sex,id),
ROW_NUMBER() OVER (ORDER BY sex,id)
FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "14.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "functions": [
            "sum"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "functions": [
            "rank"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 3,
          "last_executed_window": true,
          "functions": [
            "row_number"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "14.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`sex` AS `sex`,sum(`test`.`t1`.`id`) OVER (ORDER BY `test`.`t1`.`id` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `SUM(id) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING)`,rank() OVER (ORDER BY `test`.`t1`.`sex`,`test`.`t1`.`id` )  AS `RANK() OVER (ORDER BY sex,id)`,row_number() OVER (ORDER BY `test`.`t1`.`sex`,`test`.`t1`.`id` )  AS `ROW_NUMBER() OVER (ORDER BY sex,id)` from `test`.`t1`
a little more windows + subquery
EXPLAIN FORMAT=JSON SELECT t.*, SUM(id + r00 + r01) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING) AS s FROM (
SELECT t1.*,
RANK() OVER (ORDER BY sex) AS r00,
RANK() OVER (ORDER BY sex DESC) AS r01,
RANK() OVER (ORDER BY sex, id DESC) AS r02,
RANK() OVER (PARTITION BY id ORDER BY sex) AS r03,
RANK() OVER (ORDER BY sex) AS r04,
RANK() OVER (ORDER BY sex) AS r05,
RANK() OVER (ORDER BY sex) AS r06,
RANK() OVER (ORDER BY sex) AS r07,
RANK() OVER (ORDER BY sex) AS r08,
RANK() OVER (ORDER BY sex) AS r09,
RANK() OVER (ORDER BY sex) AS r10,
RANK() OVER (ORDER BY sex) AS r11,
RANK() OVER (ORDER BY sex) AS r12,
RANK() OVER (ORDER BY sex) AS r13,
RANK() OVER (ORDER BY sex) AS r14
FROM t1) t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.29"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "2.59",
          "eval_cost": "0.70",
          "prefix_cost": "3.29",
          "data_read_per_join": "952"
        },
        "used_columns": [
          "id",
          "sex",
          "r00",
          "r01",
          "r02",
          "r03",
          "r04",
          "r05",
          "r06",
          "r07",
          "r08",
          "r09",
          "r10",
          "r11",
          "r12",
          "r13",
          "r14"
        ],
        "materialized_from_subquery": {
          "using_temporary_table": true,
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "28.95"
            },
            "windowing": {
              "windows": [
                {
                  "name": "<unnamed window>",
                  "definition_position": 1,
                  "using_temporary_table": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`sex`"
                  ],
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 5,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 6,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 7,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 8,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 9,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 10,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 11,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 12,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 13,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 14,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 15,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 4,
                  "using_temporary_table": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`id`",
                    "`sex`"
                  ],
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 2,
                  "using_temporary_table": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`sex` desc"
                  ],
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 3,
                  "last_executed_window": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`sex`",
                    "`id` desc"
                  ],
                  "functions": [
                    "rank"
                  ]
                }
              ],
              "cost_info": {
                "sort_cost": "28.00"
              },
              "table": {
                "table_name": "t1",
                "access_type": "ALL",
                "rows_examined_per_scan": 7,
                "rows_produced_per_join": 7,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.70",
                  "prefix_cost": "0.95",
                  "data_read_per_join": "112"
                },
                "used_columns": [
                  "id",
                  "sex"
                ]
              }
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `t`.`id` AS `id`,`t`.`sex` AS `sex`,`t`.`r00` AS `r00`,`t`.`r01` AS `r01`,`t`.`r02` AS `r02`,`t`.`r03` AS `r03`,`t`.`r04` AS `r04`,`t`.`r05` AS `r05`,`t`.`r06` AS `r06`,`t`.`r07` AS `r07`,`t`.`r08` AS `r08`,`t`.`r09` AS `r09`,`t`.`r10` AS `r10`,`t`.`r11` AS `r11`,`t`.`r12` AS `r12`,`t`.`r13` AS `r13`,`t`.`r14` AS `r14`,sum(((`t`.`id` + `t`.`r00`) + `t`.`r01`)) OVER (ORDER BY `t`.`id` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `s` from (/* select#2 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`sex` AS `sex`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r00`,rank() OVER (ORDER BY `test`.`t1`.`sex` desc )  AS `r01`,rank() OVER (ORDER BY `test`.`t1`.`sex`,`test`.`t1`.`id` desc )  AS `r02`,rank() OVER (PARTITION BY `test`.`t1`.`id` ORDER BY `test`.`t1`.`sex` )  AS `r03`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r04`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r05`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r06`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r07`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r08`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r09`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r10`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r11`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r12`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r13`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r14` from `test`.`t1`) `t`
With LIMIT
EXPLAIN FORMAT=JSON SELECT t.*, SUM(id + r00 + r01) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING) AS s FROM (
SELECT t1.*,
RANK() OVER (ORDER BY sex) AS r00,
RANK() OVER (ORDER BY sex DESC) AS r01,
RANK() OVER (ORDER BY sex, id DESC) AS r02,
RANK() OVER (PARTITION BY id ORDER BY sex) AS r03,
RANK() OVER (ORDER BY sex) AS r04,
RANK() OVER (ORDER BY sex) AS r05,
RANK() OVER (ORDER BY sex) AS r06,
RANK() OVER (ORDER BY sex) AS r07,
RANK() OVER (ORDER BY sex) AS r08,
RANK() OVER (ORDER BY sex) AS r09,
RANK() OVER (ORDER BY sex) AS r10,
RANK() OVER (ORDER BY sex) AS r11,
RANK() OVER (ORDER BY sex) AS r12,
RANK() OVER (ORDER BY sex) AS r13,
RANK() OVER (ORDER BY sex) AS r14
FROM t1 LIMIT 4) t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "4.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 4,
        "rows_produced_per_join": 4,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "2.55",
          "eval_cost": "0.40",
          "prefix_cost": "2.95",
          "data_read_per_join": "544"
        },
        "used_columns": [
          "id",
          "sex",
          "r00",
          "r01",
          "r02",
          "r03",
          "r04",
          "r05",
          "r06",
          "r07",
          "r08",
          "r09",
          "r10",
          "r11",
          "r12",
          "r13",
          "r14"
        ],
        "materialized_from_subquery": {
          "using_temporary_table": true,
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "28.95"
            },
            "windowing": {
              "windows": [
                {
                  "name": "<unnamed window>",
                  "definition_position": 1,
                  "using_temporary_table": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`sex`"
                  ],
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 5,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 6,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 7,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 8,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 9,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 10,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 11,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 12,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 13,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 14,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 15,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 4,
                  "using_temporary_table": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`id`",
                    "`sex`"
                  ],
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 2,
                  "using_temporary_table": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`sex` desc"
                  ],
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 3,
                  "last_executed_window": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`sex`",
                    "`id` desc"
                  ],
                  "functions": [
                    "rank"
                  ]
                }
              ],
              "cost_info": {
                "sort_cost": "28.00"
              },
              "table": {
                "table_name": "t1",
                "access_type": "ALL",
                "rows_examined_per_scan": 7,
                "rows_produced_per_join": 7,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.70",
                  "prefix_cost": "0.95",
                  "data_read_per_join": "112"
                },
                "used_columns": [
                  "id",
                  "sex"
                ]
              }
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `t`.`id` AS `id`,`t`.`sex` AS `sex`,`t`.`r00` AS `r00`,`t`.`r01` AS `r01`,`t`.`r02` AS `r02`,`t`.`r03` AS `r03`,`t`.`r04` AS `r04`,`t`.`r05` AS `r05`,`t`.`r06` AS `r06`,`t`.`r07` AS `r07`,`t`.`r08` AS `r08`,`t`.`r09` AS `r09`,`t`.`r10` AS `r10`,`t`.`r11` AS `r11`,`t`.`r12` AS `r12`,`t`.`r13` AS `r13`,`t`.`r14` AS `r14`,sum(((`t`.`id` + `t`.`r00`) + `t`.`r01`)) OVER (ORDER BY `t`.`id` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `s` from (/* select#2 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`sex` AS `sex`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r00`,rank() OVER (ORDER BY `test`.`t1`.`sex` desc )  AS `r01`,rank() OVER (ORDER BY `test`.`t1`.`sex`,`test`.`t1`.`id` desc )  AS `r02`,rank() OVER (PARTITION BY `test`.`t1`.`id` ORDER BY `test`.`t1`.`sex` )  AS `r03`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r04`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r05`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r06`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r07`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r08`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r09`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r10`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r11`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r12`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r13`,rank() OVER (ORDER BY `test`.`t1`.`sex` )  AS `r14` from `test`.`t1` limit 4) `t`
----------------------------------------------------------------------
-    SUM, AVG, COUNT with frames
----------------------------------------------------------------------
EXPLAIN FORMAT=JSON SELECT SUM(id) OVER w * 2, AVG(id) OVER w, COUNT(id) OVER w FROM t1
WINDOW w AS (PARTITION BY sex);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum",
            "avg",
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select (sum(`test`.`t1`.`id`) OVER `w` * 2) AS `SUM(id) OVER w * 2`,avg(`test`.`t1`.`id`) OVER `w` AS `AVG(id) OVER w`,count(`test`.`t1`.`id`) OVER `w` AS `COUNT(id) OVER w` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ) 
EXPLAIN FORMAT=JSON SELECT * FROM (
SELECT id, SUM(id) OVER w, COUNT(*) OVER w, sex FROM t1
WINDOW w AS (PARTITION BY sex)
) alias ORDER BY id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.29"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "alias",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "2.59",
          "eval_cost": "0.70",
          "prefix_cost": "3.29",
          "data_read_per_join": "280"
        },
        "used_columns": [
          "id",
          "SUM(id) OVER w",
          "COUNT(*) OVER w",
          "sex"
        ],
        "materialized_from_subquery": {
          "using_temporary_table": true,
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "7.95"
            },
            "windowing": {
              "windows": [
                {
                  "name": "w",
                  "using_filesort": true,
                  "filesort_key": [
                    "`sex`"
                  ],
                  "frame_buffer": {
                    "using_temporary_table": true,
                    "optimized_frame_evaluation": true
                  },
                  "functions": [
                    "sum",
                    "count"
                  ]
                }
              ],
              "cost_info": {
                "sort_cost": "7.00"
              },
              "table": {
                "table_name": "t1",
                "access_type": "ALL",
                "rows_examined_per_scan": 7,
                "rows_produced_per_join": 7,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.70",
                  "prefix_cost": "0.95",
                  "data_read_per_join": "112"
                },
                "used_columns": [
                  "id",
                  "sex"
                ]
              }
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `alias`.`id` AS `id`,`alias`.`SUM(id) OVER w` AS `SUM(id) OVER w`,`alias`.`COUNT(*) OVER w` AS `COUNT(*) OVER w`,`alias`.`sex` AS `sex` from (/* select#2 */ select `test`.`t1`.`id` AS `id`,sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w`,count(0) OVER `w` AS `COUNT(*) OVER w`,`test`.`t1`.`sex` AS `sex` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ) ) `alias` order by `alias`.`id`
EXPLAIN FORMAT=JSON SELECT SUM(id) OVER w FROM t1 WINDOW w AS (PARTITION BY sex);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ) 
EXPLAIN FORMAT=JSON SELECT id, SUM(id) OVER w, sex FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w`,`test`.`t1`.`sex` AS `sex` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING) 
try the same as a view
CREATE VIEW v AS
SELECT id, SUM(id) OVER w, sex FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING);
SHOW CREATE VIEW v;
View	Create View	character_set_client	collation_connection
v	CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v` AS select `t1`.`id` AS `id`,sum(`t1`.`id`) OVER `w` AS `SUM(id) OVER w`,`t1`.`sex` AS `sex` from `t1` window `w` AS (PARTITION BY `t1`.`sex` ORDER BY `t1`.`id` ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING) 	utf8mb3	utf8mb3_general_ci
EXPLAIN FORMAT=JSON SELECT * FROM v;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.29"
    },
    "table": {
      "table_name": "v",
      "access_type": "ALL",
      "rows_examined_per_scan": 7,
      "rows_produced_per_join": 7,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "2.59",
        "eval_cost": "0.70",
        "prefix_cost": "3.29",
        "data_read_per_join": "224"
      },
      "used_columns": [
        "id",
        "SUM(id) OVER w",
        "sex"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "7.95"
          },
          "windowing": {
            "windows": [
              {
                "name": "w",
                "using_filesort": true,
                "filesort_key": [
                  "`sex`",
                  "`id`"
                ],
                "frame_buffer": {
                  "using_temporary_table": true,
                  "optimized_frame_evaluation": true
                },
                "functions": [
                  "sum"
                ]
              }
            ],
            "cost_info": {
              "sort_cost": "7.00"
            },
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "0.95",
                "data_read_per_join": "112"
              },
              "used_columns": [
                "id",
                "sex"
              ]
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`v`.`id` AS `id`,`test`.`v`.`SUM(id) OVER w` AS `SUM(id) OVER w`,`test`.`v`.`sex` AS `sex` from `test`.`v`
DROP VIEW v;
EXPLAIN FORMAT=JSON SELECT SUM(id) OVER w FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING) 
EXPLAIN FORMAT=JSON SELECT id, SUM(id) OVER w, sex FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w`,`test`.`t1`.`sex` AS `sex` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT SUM(id) OVER w, COUNT(*) OVER w FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum",
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w`,count(0) OVER `w` AS `COUNT(*) OVER w` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT id, AVG(id) OVER (ROWS UNBOUNDED PRECEDING) FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "avg"
          ]
        }
      ],
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,avg(`test`.`t1`.`id`) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `AVG(id) OVER (ROWS UNBOUNDED PRECEDING)` from `test`.`t1`
EXPLAIN FORMAT=JSON SELECT id, AVG(id) OVER w, COUNT(id) OVER w FROM t1
WINDOW w AS (ORDER BY id ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg",
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,avg(`test`.`t1`.`id`) OVER `w` AS `AVG(id) OVER w`,count(`test`.`t1`.`id`) OVER `w` AS `COUNT(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING) 
AVG, SUM with double type
CREATE TABLE td(d DOUBLE);
INSERT INTO td VALUES (2),(2),(3),(1),(1.2),(NULL);
ANALYZE TABLE td;
Table	Op	Msg_type	Msg_text
test.td	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER (ORDER BY d), AVG(d) OVER (ORDER BY d) FROM td;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "96"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER (ORDER BY `test`.`td`.`d` )  AS `SUM(d) OVER (ORDER BY d)`,avg(`test`.`td`.`d`) OVER (ORDER BY `test`.`td`.`d` )  AS `AVG(d) OVER (ORDER BY d)` from `test`.`td`
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER (ORDER BY d), AVG(d) OVER () FROM td;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "96"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER (ORDER BY `test`.`td`.`d` )  AS `SUM(d) OVER (ORDER BY d)`,avg(`test`.`td`.`d`) OVER ()  AS `AVG(d) OVER ()` from `test`.`td`
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER (ORDER BY d), AVG(d) OVER (ORDER BY d ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING) FROM td;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "frame_buffer": {
            "using_temporary_table": true
          },
          "functions": [
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "96"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER (ORDER BY `test`.`td`.`d` )  AS `SUM(d) OVER (ORDER BY d)`,avg(`test`.`td`.`d`) OVER (ORDER BY `test`.`td`.`d` ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING)  AS `AVG(d) OVER (ORDER BY d ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING)` from `test`.`td`
Check system variable "windowing_use_high_precision"
TRUNCATE td;
INSERT INTO td VALUES (1.7976931348623157E+307), (1);
ANALYZE TABLE td;
Table	Op	Msg_type	Msg_text
test.td	analyze	status	OK
should be default off:
SHOW VARIABLES LIKE 'windowing_use_high_precision';
Variable_name	Value
windowing_use_high_precision	ON
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER (ROWS BETWEEN CURRENT ROW AND 1 FOLLOWING) FROM td;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "frame_buffer": {
            "using_temporary_table": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 2,
        "rows_produced_per_join": 2,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.20",
          "prefix_cost": "0.45",
          "data_read_per_join": "32"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER (ROWS BETWEEN CURRENT ROW AND 1 FOLLOWING)  AS `SUM(d) OVER (ROWS BETWEEN CURRENT ROW AND 1 FOLLOWING)` from `test`.`td`
allow unsafe optimization: result changes
SET SESSION windowing_use_high_precision=FALSE;
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER (ROWS BETWEEN CURRENT ROW AND 1 FOLLOWING) FROM td;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 2,
        "rows_produced_per_join": 2,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.20",
          "prefix_cost": "0.45",
          "data_read_per_join": "32"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER (ROWS BETWEEN CURRENT ROW AND 1 FOLLOWING)  AS `SUM(d) OVER (ROWS BETWEEN CURRENT ROW AND 1 FOLLOWING)` from `test`.`td`
SET SESSION windowing_use_high_precision=TRUE;
bugfix: AVG for moving range frame
TRUNCATE td;
INSERT INTO td VALUES (10),(1),(2),(3),(4),(5),(6),(7),(8),(9);
ANALYZE TABLE td;
Table	Op	Msg_type	Msg_text
test.td	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND CURRENT ROW);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "11.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true
          },
          "functions": [
            "sum",
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "10.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "rows_produced_per_join": 10,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.00",
          "prefix_cost": "1.25",
          "data_read_per_join": "160"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER `w` AS `SUM(d) OVER w`,avg(`test`.`td`.`d`) OVER `w` AS `AVG(d) OVER w` from `test`.`td` window `w` AS (ORDER BY `test`.`td`.`d` RANGE BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "11.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true
          },
          "functions": [
            "sum",
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "10.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "rows_produced_per_join": 10,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.00",
          "prefix_cost": "1.25",
          "data_read_per_join": "160"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER `w` AS `SUM(d) OVER w`,avg(`test`.`td`.`d`) OVER `w` AS `AVG(d) OVER w` from `test`.`td` window `w` AS (ORDER BY `test`.`td`.`d` RANGE BETWEEN 2 PRECEDING AND 2 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN CURRENT ROW AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "11.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true
          },
          "functions": [
            "sum",
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "10.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "rows_produced_per_join": 10,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.00",
          "prefix_cost": "1.25",
          "data_read_per_join": "160"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER `w` AS `SUM(d) OVER w`,avg(`test`.`td`.`d`) OVER `w` AS `AVG(d) OVER w` from `test`.`td` window `w` AS (ORDER BY `test`.`td`.`d` RANGE BETWEEN CURRENT ROW AND 2 FOLLOWING) 
SET SESSION windowing_use_high_precision=FALSE;
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND CURRENT ROW);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "11.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum",
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "10.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "rows_produced_per_join": 10,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.00",
          "prefix_cost": "1.25",
          "data_read_per_join": "160"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER `w` AS `SUM(d) OVER w`,avg(`test`.`td`.`d`) OVER `w` AS `AVG(d) OVER w` from `test`.`td` window `w` AS (ORDER BY `test`.`td`.`d` RANGE BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "11.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum",
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "10.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "rows_produced_per_join": 10,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.00",
          "prefix_cost": "1.25",
          "data_read_per_join": "160"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER `w` AS `SUM(d) OVER w`,avg(`test`.`td`.`d`) OVER `w` AS `AVG(d) OVER w` from `test`.`td` window `w` AS (ORDER BY `test`.`td`.`d` RANGE BETWEEN 2 PRECEDING AND 2 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN CURRENT ROW AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "11.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum",
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "10.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "rows_produced_per_join": 10,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.00",
          "prefix_cost": "1.25",
          "data_read_per_join": "160"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER `w` AS `SUM(d) OVER w`,avg(`test`.`td`.`d`) OVER `w` AS `AVG(d) OVER w` from `test`.`td` window `w` AS (ORDER BY `test`.`td`.`d` RANGE BETWEEN CURRENT ROW AND 2 FOLLOWING) 
SET SESSION windowing_use_high_precision=TRUE;
INSERT INTO td SELECT * FROM td;
ANALYZE TABLE td;
Table	Op	Msg_type	Msg_text
test.td	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND CURRENT ROW);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "22.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true
          },
          "functions": [
            "sum",
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "20.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 20,
        "rows_produced_per_join": 20,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.00",
          "prefix_cost": "2.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER `w` AS `SUM(d) OVER w`,avg(`test`.`td`.`d`) OVER `w` AS `AVG(d) OVER w` from `test`.`td` window `w` AS (ORDER BY `test`.`td`.`d` RANGE BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "22.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true
          },
          "functions": [
            "sum",
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "20.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 20,
        "rows_produced_per_join": 20,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.00",
          "prefix_cost": "2.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER `w` AS `SUM(d) OVER w`,avg(`test`.`td`.`d`) OVER `w` AS `AVG(d) OVER w` from `test`.`td` window `w` AS (ORDER BY `test`.`td`.`d` RANGE BETWEEN 2 PRECEDING AND 2 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN CURRENT ROW AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "22.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true
          },
          "functions": [
            "sum",
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "20.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 20,
        "rows_produced_per_join": 20,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.00",
          "prefix_cost": "2.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER `w` AS `SUM(d) OVER w`,avg(`test`.`td`.`d`) OVER `w` AS `AVG(d) OVER w` from `test`.`td` window `w` AS (ORDER BY `test`.`td`.`d` RANGE BETWEEN CURRENT ROW AND 2 FOLLOWING) 
SET SESSION windowing_use_high_precision=FALSE;
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND CURRENT ROW);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "22.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum",
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "20.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 20,
        "rows_produced_per_join": 20,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.00",
          "prefix_cost": "2.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER `w` AS `SUM(d) OVER w`,avg(`test`.`td`.`d`) OVER `w` AS `AVG(d) OVER w` from `test`.`td` window `w` AS (ORDER BY `test`.`td`.`d` RANGE BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "22.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum",
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "20.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 20,
        "rows_produced_per_join": 20,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.00",
          "prefix_cost": "2.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER `w` AS `SUM(d) OVER w`,avg(`test`.`td`.`d`) OVER `w` AS `AVG(d) OVER w` from `test`.`td` window `w` AS (ORDER BY `test`.`td`.`d` RANGE BETWEEN 2 PRECEDING AND 2 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN CURRENT ROW AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "22.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum",
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "20.00"
      },
      "table": {
        "table_name": "td",
        "access_type": "ALL",
        "rows_examined_per_scan": 20,
        "rows_produced_per_join": 20,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.00",
          "prefix_cost": "2.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td`.`d` AS `d`,sum(`test`.`td`.`d`) OVER `w` AS `SUM(d) OVER w`,avg(`test`.`td`.`d`) OVER `w` AS `AVG(d) OVER w` from `test`.`td` window `w` AS (ORDER BY `test`.`td`.`d` RANGE BETWEEN CURRENT ROW AND 2 FOLLOWING) 
SET SESSION windowing_use_high_precision=TRUE;
DROP TABLE td;
----------------------------------------------------------------------
-    NTILE (requires two passes over partition).
-    Currently suboptimal in that it causes N*N reads of tmp buffer
----------------------------------------------------------------------
EXPLAIN FORMAT=JSON SELECT id, NTILE(NULL) OVER w FROM t1 WINDOW w AS (ORDER BY id);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'NULL) OVER w FROM t1 WINDOW w AS (ORDER BY id)' at line 1
EXPLAIN FORMAT=JSON SELECT id, NTILE(1) OVER w FROM t1 WINDOW w AS (ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "ntile"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,ntile(1) OVER `w` AS `NTILE(1) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` ) 
EXPLAIN FORMAT=JSON SELECT id, NTILE(2) OVER w FROM t1 WINDOW w AS (ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "ntile"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,ntile(2) OVER `w` AS `NTILE(2) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` ) 
EXPLAIN FORMAT=JSON SELECT id, NTILE(5) OVER w FROM t1 WINDOW w AS (ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "ntile"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,ntile(5) OVER `w` AS `NTILE(5) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` ) 
EXPLAIN FORMAT=JSON SELECT id, NTILE(11) OVER w FROM t1 WINDOW w AS (ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "ntile"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,ntile(11) OVER `w` AS `NTILE(11) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` ) 
combo with frame
EXPLAIN FORMAT=JSON SELECT id, ROW_NUMBER() OVER w, NTILE(4) OVER w, SUM(id) OVER w FROM t1
WINDOW w AS (ORDER BY id ROWS 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "row_number",
            "ntile",
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	3599	Window function 'row_number' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	3599	Window function 'ntile' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,row_number() OVER `w` AS `ROW_NUMBER() OVER w`,ntile(4) OVER `w` AS `NTILE(4) OVER w`,sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` ROWS BETWEEN 1 PRECEDING AND CURRENT ROW) 
Try one where there are no extras
DELETE FROM t1 WHERE id=11;
EXPLAIN FORMAT=JSON SELECT id, NTILE(3) OVER w FROM t1 WINDOW w AS (ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "ntile"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "96"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,ntile(3) OVER `w` AS `NTILE(3) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` ) 
INSERT INTO t1 VALUES (11, NULL);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
Simulated NTILE via other SQL window functions. Exercises an
an expression containing window functions defined on different
windows
EXPLAIN FORMAT=JSON SELECT (ROW_NUMBER() OVER w1 * 5 - 1) DIV (COUNT(*) OVER w2) + 1 AS cnt
FROM t1 WINDOW w1 AS (ORDER BY id ASC),
w2 AS ();
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w1",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "functions": [
            "row_number"
          ]
        },
        {
          "name": "w2",
          "definition_position": 2,
          "last_executed_window": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select ((((```row_number() OVER w1``` * 5) - 1) DIV count(0) OVER `w2`) + 1) AS `cnt` from `test`.`t1` window `w1` AS (ORDER BY `test`.`t1`.`id` ) , `w2` AS () 
EXPLAIN FORMAT=JSON SELECT  (ROW_NUMBER() OVER w1 * 5 - 1) DIV (COUNT(*) OVER w2) + 1 AS ntile_manually,
COUNT(*) OVER w3
FROM t1 WINDOW w1 AS (ORDER BY id ASC),
w2 AS (), w3 AS ();
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w1",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "functions": [
            "row_number"
          ]
        },
        {
          "name": "w2",
          "definition_position": 2,
          "using_temporary_table": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count"
          ]
        },
        {
          "name": "w3",
          "definition_position": 3,
          "last_executed_window": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select ((((```row_number() OVER w1``` * 5) - 1) DIV ```count(0) OVER w2```) + 1) AS `ntile_manually`,count(0) OVER `w3` AS `COUNT(*) OVER w3` from `test`.`t1` window `w1` AS (ORDER BY `test`.`t1`.`id` ) , `w2` AS () , `w3` AS () 
NTILE in combination with a frame that doesn't cover current row (was bug)
EXPLAIN FORMAT=JSON SELECT id, ROW_NUMBER() OVER w, SUM(id) OVER w, NTILE(5) OVER w FROM t1
WINDOW w AS (ORDER BY id ROWS BETWEEN UNBOUNDED PRECEDING AND 2 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "row_number",
            "sum",
            "ntile"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	3599	Window function 'row_number' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	3599	Window function 'ntile' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,row_number() OVER `w` AS `ROW_NUMBER() OVER w`,sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w`,ntile(5) OVER `w` AS `NTILE(5) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` ROWS BETWEEN UNBOUNDED PRECEDING AND 2 PRECEDING) 
----------------------------------------------------------------------
-    SUM with frames in combination with non-framing window functions
-    ROW_NUMBER and RANK
----------------------------------------------------------------------
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER w, id, SUM(id) OVER w, sex FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "row_number",
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	3599	Window function 'row_number' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	1003	/* select#1 */ select row_number() OVER `w` AS `ROW_NUMBER() OVER w`,`test`.`t1`.`id` AS `id`,sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w`,`test`.`t1`.`sex` AS `sex` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER w, SUM(id) OVER w FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "row_number",
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	3599	Window function 'row_number' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	1003	/* select#1 */ select row_number() OVER `w` AS `ROW_NUMBER() OVER w`,sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING) 
INSERT INTO t1 VALUES (10, NULL);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT RANK() OVER w, id, SUM(id) OVER w, sex FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "9.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "rank",
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "8.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 8,
        "rows_produced_per_join": 8,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.80",
          "prefix_cost": "1.05",
          "data_read_per_join": "128"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select rank() OVER `w` AS `RANK() OVER w`,`test`.`t1`.`id` AS `id`,sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w`,`test`.`t1`.`sex` AS `sex` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` ) 
EXPLAIN FORMAT=JSON SELECT RANK() OVER w, SUM(id) OVER w FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "9.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "rank",
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "8.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 8,
        "rows_produced_per_join": 8,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.80",
          "prefix_cost": "1.05",
          "data_read_per_join": "128"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	3599	Window function 'rank' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	1003	/* select#1 */ select rank() OVER `w` AS `RANK() OVER w`,sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT id, sex, SUM(id) OVER w,
ROW_NUMBER() OVER w,
RANK() OVER w  FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "9.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum",
            "row_number",
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "8.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 8,
        "rows_produced_per_join": 8,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.80",
          "prefix_cost": "1.05",
          "data_read_per_join": "128"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	3599	Window function 'row_number' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	3599	Window function 'rank' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`sex` AS `sex`,sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w`,row_number() OVER `w` AS `ROW_NUMBER() OVER w`,rank() OVER `w` AS `RANK() OVER w` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING) 
----------------------------------------------------------------------
-    FIRST_VALUE
----------------------------------------------------------------------
INSERT INTO t1 VALUES (NULL, 'M');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS ();
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS () 
select id, FIRST_VALUE(id) OVER (ROWS UNBOUNDED PRECEDING) FROM t1;
id	FIRST_VALUE(id) OVER (ROWS UNBOUNDED PRECEDING)
1	1
2	1
3	1
4	1
5	1
10	1
11	1
10	1
NULL	1
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (PARTITION BY sex ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`sex` ORDER BY `test`.`t1`.`id` ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id DESC);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id` desc"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` desc ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id ROWS  2 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` ROWS BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id RANGE 2 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` RANGE BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 
CREATE VIEW v AS
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
SHOW CREATE VIEW v;
View	Create View	character_set_client	collation_connection
v	CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v` AS select `t1`.`id` AS `id`,first_value(`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `t1` window `w` AS (ORDER BY `t1`.`id` RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 	utf8mb3	utf8mb3_general_ci
EXPLAIN FORMAT=JSON SELECT * FROM v;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.51"
    },
    "table": {
      "table_name": "v",
      "access_type": "ALL",
      "rows_examined_per_scan": 9,
      "rows_produced_per_join": 9,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "2.61",
        "eval_cost": "0.90",
        "prefix_cost": "3.51",
        "data_read_per_join": "216"
      },
      "used_columns": [
        "id",
        "FIRST_VALUE(id) OVER w"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "10.15"
          },
          "windowing": {
            "windows": [
              {
                "name": "w",
                "using_filesort": true,
                "filesort_key": [
                  "`id`"
                ],
                "frame_buffer": {
                  "using_temporary_table": true,
                  "optimized_frame_evaluation": true
                },
                "functions": [
                  "first_value"
                ]
              }
            ],
            "cost_info": {
              "sort_cost": "9.00"
            },
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 9,
              "rows_produced_per_join": 9,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.90",
                "prefix_cost": "1.15",
                "data_read_per_join": "144"
              },
              "used_columns": [
                "id"
              ]
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`v`.`id` AS `id`,`test`.`v`.`FIRST_VALUE(id) OVER w` AS `FIRST_VALUE(id) OVER w` from `test`.`v`
DROP VIEW v;
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` ROWS BETWEEN 2 FOLLOWING AND 3 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id RANGE BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` RANGE BETWEEN 2 FOLLOWING AND 3 FOLLOWING) 
CREATE TABLE td1 (id DOUBLE, sex CHAR(1));
INSERT INTO td1 SELECT * FROM t1;
ANALYZE TABLE td1;
Table	Op	Msg_type	Msg_text
test.td1	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS ();
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "table": {
        "table_name": "td1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td1`.`id` AS `id`,first_value(`test`.`td1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td1` window `w` AS () 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td1`.`id` AS `id`,first_value(`test`.`td1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td1` window `w` AS (ORDER BY `test`.`td1`.`id` ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (PARTITION BY sex ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td1`.`id` AS `id`,first_value(`test`.`td1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td1` window `w` AS (PARTITION BY `test`.`td1`.`sex` ORDER BY `test`.`td1`.`id` ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id DESC);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id` desc"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td1`.`id` AS `id`,first_value(`test`.`td1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td1` window `w` AS (ORDER BY `test`.`td1`.`id` desc ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id ROWS  2 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td1`.`id` AS `id`,first_value(`test`.`td1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td1` window `w` AS (ORDER BY `test`.`td1`.`id` ROWS BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id RANGE 2 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td1`.`id` AS `id`,first_value(`test`.`td1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td1` window `w` AS (ORDER BY `test`.`td1`.`id` RANGE BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td1`.`id` AS `id`,first_value(`test`.`td1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td1` window `w` AS (ORDER BY `test`.`td1`.`id` ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td1`.`id` AS `id`,first_value(`test`.`td1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td1` window `w` AS (ORDER BY `test`.`td1`.`id` RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td1`.`id` AS `id`,first_value(`test`.`td1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td1` window `w` AS (ORDER BY `test`.`td1`.`id` ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td1`.`id` AS `id`,first_value(`test`.`td1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td1` window `w` AS (ORDER BY `test`.`td1`.`id` RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td1`.`id` AS `id`,first_value(`test`.`td1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td1` window `w` AS (ORDER BY `test`.`td1`.`id` ROWS BETWEEN 2 FOLLOWING AND 3 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id RANGE BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td1`.`id` AS `id`,first_value(`test`.`td1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td1` window `w` AS (ORDER BY `test`.`td1`.`id` RANGE BETWEEN 2 FOLLOWING AND 3 FOLLOWING) 
DROP TABLE td1;
CREATE TABLE td_dec (id DECIMAL(10,2), sex CHAR(1));
INSERT INTO td_dec SELECT * FROM t1;
ANALYZE TABLE td_dec;
Table	Op	Msg_type	Msg_text
test.td_dec	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS ();
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "table": {
        "table_name": "td_dec",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_dec`.`id` AS `id`,first_value(`test`.`td_dec`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_dec` window `w` AS () 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_dec",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_dec`.`id` AS `id`,first_value(`test`.`td_dec`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_dec` window `w` AS (ORDER BY `test`.`td_dec`.`id` ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (PARTITION BY sex ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_dec",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_dec`.`id` AS `id`,first_value(`test`.`td_dec`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_dec` window `w` AS (PARTITION BY `test`.`td_dec`.`sex` ORDER BY `test`.`td_dec`.`id` ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id DESC);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id` desc"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_dec",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_dec`.`id` AS `id`,first_value(`test`.`td_dec`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_dec` window `w` AS (ORDER BY `test`.`td_dec`.`id` desc ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id ROWS  2 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_dec",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_dec`.`id` AS `id`,first_value(`test`.`td_dec`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_dec` window `w` AS (ORDER BY `test`.`td_dec`.`id` ROWS BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id RANGE 2 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_dec",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_dec`.`id` AS `id`,first_value(`test`.`td_dec`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_dec` window `w` AS (ORDER BY `test`.`td_dec`.`id` RANGE BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_dec",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_dec`.`id` AS `id`,first_value(`test`.`td_dec`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_dec` window `w` AS (ORDER BY `test`.`td_dec`.`id` ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_dec",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_dec`.`id` AS `id`,first_value(`test`.`td_dec`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_dec` window `w` AS (ORDER BY `test`.`td_dec`.`id` RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_dec",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_dec`.`id` AS `id`,first_value(`test`.`td_dec`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_dec` window `w` AS (ORDER BY `test`.`td_dec`.`id` ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_dec",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_dec`.`id` AS `id`,first_value(`test`.`td_dec`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_dec` window `w` AS (ORDER BY `test`.`td_dec`.`id` RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_dec",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_dec`.`id` AS `id`,first_value(`test`.`td_dec`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_dec` window `w` AS (ORDER BY `test`.`td_dec`.`id` ROWS BETWEEN 2 FOLLOWING AND 3 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id RANGE BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_dec",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_dec`.`id` AS `id`,first_value(`test`.`td_dec`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_dec` window `w` AS (ORDER BY `test`.`td_dec`.`id` RANGE BETWEEN 2 FOLLOWING AND 3 FOLLOWING) 
DROP TABLE td_dec;
CREATE TABLE td_str (id VARCHAR(20), sex CHAR(1));
INSERT INTO td_str SELECT * FROM t1;
ANALYZE TABLE td_str;
Table	Op	Msg_type	Msg_text
test.td_str	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS ();
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "table": {
        "table_name": "td_str",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "792"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_str`.`id` AS `id`,first_value(`test`.`td_str`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_str` window `w` AS () 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_str",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "792"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_str`.`id` AS `id`,first_value(`test`.`td_str`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_str` window `w` AS (ORDER BY `test`.`td_str`.`id` ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (PARTITION BY sex ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`sex`",
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_str",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "792"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_str`.`id` AS `id`,first_value(`test`.`td_str`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_str` window `w` AS (PARTITION BY `test`.`td_str`.`sex` ORDER BY `test`.`td_str`.`id` ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id DESC);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id` desc"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_str",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "792"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_str`.`id` AS `id`,first_value(`test`.`td_str`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_str` window `w` AS (ORDER BY `test`.`td_str`.`id` desc ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id ROWS  2 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_str",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "792"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_str`.`id` AS `id`,first_value(`test`.`td_str`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_str` window `w` AS (ORDER BY `test`.`td_str`.`id` ROWS BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_str",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "792"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_str`.`id` AS `id`,first_value(`test`.`td_str`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_str` window `w` AS (ORDER BY `test`.`td_str`.`id` ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_str",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "792"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_str`.`id` AS `id`,first_value(`test`.`td_str`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_str` window `w` AS (ORDER BY `test`.`td_str`.`id` ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "td_str",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "792"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`td_str`.`id` AS `id`,first_value(`test`.`td_str`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`td_str` window `w` AS (ORDER BY `test`.`td_str`.`id` ROWS BETWEEN 2 FOLLOWING AND 3 FOLLOWING) 
DROP TABLE td_str;
CREATE TABLE t_date(id DATE);
INSERT INTO t_date VALUES ('2002-06-09'),
('2002-06-09'),
('2002-06-10'),
('2002-06-09'),
('2015-08-01'),
('2002-06-09'),
('2015-08-01');
ANALYZE TABLE t_date;
Table	Op	Msg_type	Msg_text
test.t_date	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS ();
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "table": {
        "table_name": "t_date",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "56"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_date`.`id` AS `id`,first_value(`test`.`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t_date` window `w` AS () 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t_date",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "56"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_date`.`id` AS `id`,first_value(`test`.`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t_date` window `w` AS (ORDER BY `test`.`t_date`.`id` ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id DESC);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id` desc"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t_date",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "56"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_date`.`id` AS `id`,first_value(`test`.`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t_date` window `w` AS (ORDER BY `test`.`t_date`.`id` desc ) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id ROWS  2 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t_date",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "56"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_date`.`id` AS `id`,first_value(`test`.`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t_date` window `w` AS (ORDER BY `test`.`t_date`.`id` ROWS BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id RANGE INTERVAL 2 DAY PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t_date",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "56"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_date`.`id` AS `id`,first_value(`test`.`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t_date` window `w` AS (ORDER BY `test`.`t_date`.`id` RANGE BETWEEN INTERVAL 2 day  PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t_date",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "56"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_date`.`id` AS `id`,first_value(`test`.`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t_date` window `w` AS (ORDER BY `test`.`t_date`.`id` ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id RANGE BETWEEN INTERVAL 2 DAY PRECEDING AND INTERVAL 1 DAY PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t_date",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "56"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_date`.`id` AS `id`,first_value(`test`.`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t_date` window `w` AS (ORDER BY `test`.`t_date`.`id` RANGE BETWEEN INTERVAL 2 day  PRECEDING AND INTERVAL 1 day  PRECEDING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t_date",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "56"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_date`.`id` AS `id`,first_value(`test`.`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t_date` window `w` AS (ORDER BY `test`.`t_date`.`id` ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t_date",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "56"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_date`.`id` AS `id`,first_value(`test`.`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t_date` window `w` AS (ORDER BY `test`.`t_date`.`id` RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 
CREATE VIEW v AS
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
SHOW CREATE VIEW v;
View	Create View	character_set_client	collation_connection
v	CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v` AS select `t_date`.`id` AS `id`,first_value(`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `t_date` window `w` AS (ORDER BY `t_date`.`id` RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 	utf8mb3	utf8mb3_general_ci
EXPLAIN FORMAT=JSON SELECT * FROM v;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.29"
    },
    "table": {
      "table_name": "v",
      "access_type": "ALL",
      "rows_examined_per_scan": 7,
      "rows_produced_per_join": 7,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "2.59",
        "eval_cost": "0.70",
        "prefix_cost": "3.29",
        "data_read_per_join": "112"
      },
      "used_columns": [
        "id",
        "FIRST_VALUE(id) OVER w"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "7.95"
          },
          "windowing": {
            "windows": [
              {
                "name": "w",
                "using_filesort": true,
                "filesort_key": [
                  "`id`"
                ],
                "frame_buffer": {
                  "using_temporary_table": true,
                  "optimized_frame_evaluation": true
                },
                "functions": [
                  "first_value"
                ]
              }
            ],
            "cost_info": {
              "sort_cost": "7.00"
            },
            "table": {
              "table_name": "t_date",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "0.95",
                "data_read_per_join": "56"
              },
              "used_columns": [
                "id"
              ]
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`v`.`id` AS `id`,`test`.`v`.`FIRST_VALUE(id) OVER w` AS `FIRST_VALUE(id) OVER w` from `test`.`v`
DROP VIEW v;
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t_date",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "56"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_date`.`id` AS `id`,first_value(`test`.`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t_date` window `w` AS (ORDER BY `test`.`t_date`.`id` ROWS BETWEEN 2 FOLLOWING AND 3 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id RANGE BETWEEN INTERVAL 2 DAY FOLLOWING AND INTERVAL 3 DAY FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t_date",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "56"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_date`.`id` AS `id`,first_value(`test`.`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t_date` window `w` AS (ORDER BY `test`.`t_date`.`id` RANGE BETWEEN INTERVAL 2 day  FOLLOWING AND INTERVAL 3 day  FOLLOWING) 
CREATE VIEW v AS
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id RANGE BETWEEN INTERVAL 2 DAY FOLLOWING AND INTERVAL 3 DAY FOLLOWING);
SHOW CREATE VIEW v;
View	Create View	character_set_client	collation_connection
v	CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v` AS select `t_date`.`id` AS `id`,first_value(`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `t_date` window `w` AS (ORDER BY `t_date`.`id` RANGE BETWEEN INTERVAL 2 day  FOLLOWING AND INTERVAL 3 day  FOLLOWING) 	utf8mb3	utf8mb3_general_ci
EXPLAIN FORMAT=JSON SELECT * FROM v;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.29"
    },
    "table": {
      "table_name": "v",
      "access_type": "ALL",
      "rows_examined_per_scan": 7,
      "rows_produced_per_join": 7,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "2.59",
        "eval_cost": "0.70",
        "prefix_cost": "3.29",
        "data_read_per_join": "112"
      },
      "used_columns": [
        "id",
        "FIRST_VALUE(id) OVER w"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "7.95"
          },
          "windowing": {
            "windows": [
              {
                "name": "w",
                "using_filesort": true,
                "filesort_key": [
                  "`id`"
                ],
                "frame_buffer": {
                  "using_temporary_table": true,
                  "optimized_frame_evaluation": true
                },
                "functions": [
                  "first_value"
                ]
              }
            ],
            "cost_info": {
              "sort_cost": "7.00"
            },
            "table": {
              "table_name": "t_date",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.70",
                "prefix_cost": "0.95",
                "data_read_per_join": "56"
              },
              "used_columns": [
                "id"
              ]
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`v`.`id` AS `id`,`test`.`v`.`FIRST_VALUE(id) OVER w` AS `FIRST_VALUE(id) OVER w` from `test`.`v`
DROP VIEW v;
DROP TABLE t_date;
CREATE TABLE t_time(t TIME, ts TIMESTAMP);
INSERT INTO t_time VALUES ('12:30', '2016-07-05 08:30:42');
INSERT INTO t_time VALUES ('22:30', '2015-07-05 08:30:43');
INSERT INTO t_time VALUES ('13:30', '2014-07-05 08:30:44');
INSERT INTO t_time VALUES ('01:30', '2013-07-05 08:30:45');
INSERT INTO t_time VALUES ('15:30', '2016-08-05 08:31:42');
INSERT INTO t_time VALUES ('20:30', '2016-09-05 08:32:42');
INSERT INTO t_time VALUES ('04:30', '2016-10-05 08:33:42');
INSERT INTO t_time VALUES ('06:30', '2016-11-05 08:34:42');
INSERT INTO t_time VALUES ('18:30', '2016-07-05 09:30:42');
INSERT INTO t_time VALUES ('21:30', '2016-07-06 10:30:42');
INSERT INTO t_time VALUES ('00:30', '2016-07-07 11:30:42');
INSERT INTO t_time VALUES ('00:31', '2016-07-08 12:30:42');
ANALYZE TABLE t_time;
Table	Op	Msg_type	Msg_text
test.t_time	analyze	status	OK
CREATE TABLE t_time2(t TIME, ts TIMESTAMP, p INTEGER DEFAULT 1);
INSERT INTO t_time2(t, ts) SELECT * FROM t_time;
UPDATE t_time2 SET p=p+1;
INSERT INTO t_time2(t, ts) SELECT * FROM t_time;
ANALYZE TABLE t_time2;
Table	Op	Msg_type	Msg_text
test.t_time2	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS ();
EXPLAIN
            "first_value"
            "optimized_frame_evaluation": true
            "using_temporary_table": true,
          "data_read_per_join": "192"
          "eval_cost": "1.20",
          "frame_buffer": {
          "functions": [
          "name": "w",
          "prefix_cost": "1.45",
          "read_cost": "0.25",
          "t"
          ]
          },
        "access_type": "ALL",
        "cost_info": {
        "filtered": "100.00",
        "rows_examined_per_scan": 12,
        "rows_produced_per_join": 12,
        "table_name": "t_time",
        "used_columns": [
        ]
        {
        }
        },
      "query_cost": "1.45"
      "table": {
      "windows": [
      ],
      }
    "cost_info": {
    "select_id": 1,
    "windowing": {
    }
    },
  "query_block": {
  }
Note	1003	/* select#1 */ select `test`.`t_time`.`t` AS `t`,first_value(`test`.`t_time`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time` window `w` AS () 
Warnings:
{
}
EXPLAIN FORMAT=JSON SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t);
EXPLAIN
            "`t`"
            "first_value"
            "optimized_frame_evaluation": true
            "using_temporary_table": true,
          "data_read_per_join": "192"
          "eval_cost": "1.20",
          "filesort_key": [
          "frame_buffer": {
          "functions": [
          "name": "w",
          "prefix_cost": "1.45",
          "read_cost": "0.25",
          "t"
          "using_filesort": true,
          ]
          ],
          },
        "access_type": "ALL",
        "cost_info": {
        "filtered": "100.00",
        "rows_examined_per_scan": 12,
        "rows_produced_per_join": 12,
        "sort_cost": "12.00"
        "table_name": "t_time",
        "used_columns": [
        ]
        {
        }
        },
      "cost_info": {
      "query_cost": "13.45"
      "table": {
      "windows": [
      ],
      }
      },
    "cost_info": {
    "select_id": 1,
    "windowing": {
    }
    },
  "query_block": {
  }
Note	1003	/* select#1 */ select `test`.`t_time`.`t` AS `t`,first_value(`test`.`t_time`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time` window `w` AS (ORDER BY `test`.`t_time`.`t` ) 
Warnings:
{
}
EXPLAIN FORMAT=JSON SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t DESC);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`t` desc"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "12.00"
      },
      "table": {
        "table_name": "t_time",
        "access_type": "ALL",
        "rows_examined_per_scan": 12,
        "rows_produced_per_join": 12,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.20",
          "prefix_cost": "1.45",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "t"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time`.`t` AS `t`,first_value(`test`.`t_time`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time` window `w` AS (ORDER BY `test`.`t_time`.`t` desc ) 
EXPLAIN FORMAT=JSON SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t ROWS  2 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "12.00"
      },
      "table": {
        "table_name": "t_time",
        "access_type": "ALL",
        "rows_examined_per_scan": 12,
        "rows_produced_per_join": 12,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.20",
          "prefix_cost": "1.45",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "t"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time`.`t` AS `t`,first_value(`test`.`t_time`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time` window `w` AS (ORDER BY `test`.`t_time`.`t` ROWS BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t RANGE INTERVAL 2 HOUR PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "12.00"
      },
      "table": {
        "table_name": "t_time",
        "access_type": "ALL",
        "rows_examined_per_scan": 12,
        "rows_produced_per_join": 12,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.20",
          "prefix_cost": "1.45",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "t"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time`.`t` AS `t`,first_value(`test`.`t_time`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time` window `w` AS (ORDER BY `test`.`t_time`.`t` RANGE BETWEEN INTERVAL 2 hour  PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "12.00"
      },
      "table": {
        "table_name": "t_time",
        "access_type": "ALL",
        "rows_examined_per_scan": 12,
        "rows_produced_per_join": 12,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.20",
          "prefix_cost": "1.45",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "t"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time`.`t` AS `t`,first_value(`test`.`t_time`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time` window `w` AS (ORDER BY `test`.`t_time`.`t` ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING) 
EXPLAIN FORMAT=JSON SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t RANGE BETWEEN INTERVAL 2 HOUR PRECEDING AND INTERVAL 1 HOUR PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "12.00"
      },
      "table": {
        "table_name": "t_time",
        "access_type": "ALL",
        "rows_examined_per_scan": 12,
        "rows_produced_per_join": 12,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.20",
          "prefix_cost": "1.45",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "t"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time`.`t` AS `t`,first_value(`test`.`t_time`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time` window `w` AS (ORDER BY `test`.`t_time`.`t` RANGE BETWEEN INTERVAL 2 hour  PRECEDING AND INTERVAL 1 hour  PRECEDING) 
EXPLAIN FORMAT=JSON SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "12.00"
      },
      "table": {
        "table_name": "t_time",
        "access_type": "ALL",
        "rows_examined_per_scan": 12,
        "rows_produced_per_join": 12,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.20",
          "prefix_cost": "1.45",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "t"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time`.`t` AS `t`,first_value(`test`.`t_time`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time` window `w` AS (ORDER BY `test`.`t_time`.`t` ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "12.00"
      },
      "table": {
        "table_name": "t_time",
        "access_type": "ALL",
        "rows_examined_per_scan": 12,
        "rows_produced_per_join": 12,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.20",
          "prefix_cost": "1.45",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "t"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time`.`t` AS `t`,first_value(`test`.`t_time`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time` window `w` AS (ORDER BY `test`.`t_time`.`t` RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "12.00"
      },
      "table": {
        "table_name": "t_time",
        "access_type": "ALL",
        "rows_examined_per_scan": 12,
        "rows_produced_per_join": 12,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.20",
          "prefix_cost": "1.45",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "t"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time`.`t` AS `t`,first_value(`test`.`t_time`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time` window `w` AS (ORDER BY `test`.`t_time`.`t` ROWS BETWEEN 2 FOLLOWING AND 3 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t RANGE BETWEEN INTERVAL 2 HOUR FOLLOWING AND INTERVAL 3 HOUR FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "12.00"
      },
      "table": {
        "table_name": "t_time",
        "access_type": "ALL",
        "rows_examined_per_scan": 12,
        "rows_produced_per_join": 12,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.20",
          "prefix_cost": "1.45",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "t"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time`.`t` AS `t`,first_value(`test`.`t_time`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time` window `w` AS (ORDER BY `test`.`t_time`.`t` RANGE BETWEEN INTERVAL 2 hour  FOLLOWING AND INTERVAL 3 hour  FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t);
EXPLAIN
            "`p`",
            "`t`"
            "first_value"
            "optimized_frame_evaluation": true
            "using_temporary_table": true,
          "data_read_per_join": "384"
          "eval_cost": "2.40",
          "filesort_key": [
          "frame_buffer": {
          "functions": [
          "name": "w",
          "p"
          "prefix_cost": "2.65",
          "read_cost": "0.25",
          "t",
          "using_filesort": true,
          ]
          ],
          },
        "access_type": "ALL",
        "cost_info": {
        "filtered": "100.00",
        "rows_examined_per_scan": 24,
        "rows_produced_per_join": 24,
        "sort_cost": "24.00"
        "table_name": "t_time2",
        "used_columns": [
        ]
        {
        }
        },
      "cost_info": {
      "query_cost": "26.65"
      "table": {
      "windows": [
      ],
      }
      },
    "cost_info": {
    "select_id": 1,
    "windowing": {
    }
    },
  "query_block": {
  }
Note	1003	/* select#1 */ select `test`.`t_time2`.`p` AS `p`,`test`.`t_time2`.`t` AS `t`,first_value(`test`.`t_time2`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time2` window `w` AS (PARTITION BY `test`.`t_time2`.`p` ORDER BY `test`.`t_time2`.`t` ) 
Warnings:
{
}
EXPLAIN FORMAT=JSON SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t DESC);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "26.65"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`p`",
            "`t` desc"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "24.00"
      },
      "table": {
        "table_name": "t_time2",
        "access_type": "ALL",
        "rows_examined_per_scan": 24,
        "rows_produced_per_join": 24,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.40",
          "prefix_cost": "2.65",
          "data_read_per_join": "384"
        },
        "used_columns": [
          "t",
          "p"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time2`.`p` AS `p`,`test`.`t_time2`.`t` AS `t`,first_value(`test`.`t_time2`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time2` window `w` AS (PARTITION BY `test`.`t_time2`.`p` ORDER BY `test`.`t_time2`.`t` desc ) 
EXPLAIN FORMAT=JSON SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t ROWS  2 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "26.65"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`p`",
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "24.00"
      },
      "table": {
        "table_name": "t_time2",
        "access_type": "ALL",
        "rows_examined_per_scan": 24,
        "rows_produced_per_join": 24,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.40",
          "prefix_cost": "2.65",
          "data_read_per_join": "384"
        },
        "used_columns": [
          "t",
          "p"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time2`.`p` AS `p`,`test`.`t_time2`.`t` AS `t`,first_value(`test`.`t_time2`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time2` window `w` AS (PARTITION BY `test`.`t_time2`.`p` ORDER BY `test`.`t_time2`.`t` ROWS BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t RANGE INTERVAL 2 HOUR PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "26.65"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`p`",
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "24.00"
      },
      "table": {
        "table_name": "t_time2",
        "access_type": "ALL",
        "rows_examined_per_scan": 24,
        "rows_produced_per_join": 24,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.40",
          "prefix_cost": "2.65",
          "data_read_per_join": "384"
        },
        "used_columns": [
          "t",
          "p"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time2`.`p` AS `p`,`test`.`t_time2`.`t` AS `t`,first_value(`test`.`t_time2`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time2` window `w` AS (PARTITION BY `test`.`t_time2`.`p` ORDER BY `test`.`t_time2`.`t` RANGE BETWEEN INTERVAL 2 hour  PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "26.65"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`p`",
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "24.00"
      },
      "table": {
        "table_name": "t_time2",
        "access_type": "ALL",
        "rows_examined_per_scan": 24,
        "rows_produced_per_join": 24,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.40",
          "prefix_cost": "2.65",
          "data_read_per_join": "384"
        },
        "used_columns": [
          "t",
          "p"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time2`.`p` AS `p`,`test`.`t_time2`.`t` AS `t`,first_value(`test`.`t_time2`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time2` window `w` AS (PARTITION BY `test`.`t_time2`.`p` ORDER BY `test`.`t_time2`.`t` ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING) 
EXPLAIN FORMAT=JSON SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t RANGE BETWEEN INTERVAL 2 HOUR PRECEDING AND INTERVAL 1 HOUR PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "26.65"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`p`",
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "24.00"
      },
      "table": {
        "table_name": "t_time2",
        "access_type": "ALL",
        "rows_examined_per_scan": 24,
        "rows_produced_per_join": 24,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.40",
          "prefix_cost": "2.65",
          "data_read_per_join": "384"
        },
        "used_columns": [
          "t",
          "p"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time2`.`p` AS `p`,`test`.`t_time2`.`t` AS `t`,first_value(`test`.`t_time2`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time2` window `w` AS (PARTITION BY `test`.`t_time2`.`p` ORDER BY `test`.`t_time2`.`t` RANGE BETWEEN INTERVAL 2 hour  PRECEDING AND INTERVAL 1 hour  PRECEDING) 
EXPLAIN FORMAT=JSON SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "26.65"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`p`",
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "24.00"
      },
      "table": {
        "table_name": "t_time2",
        "access_type": "ALL",
        "rows_examined_per_scan": 24,
        "rows_produced_per_join": 24,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.40",
          "prefix_cost": "2.65",
          "data_read_per_join": "384"
        },
        "used_columns": [
          "t",
          "p"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time2`.`p` AS `p`,`test`.`t_time2`.`t` AS `t`,first_value(`test`.`t_time2`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time2` window `w` AS (PARTITION BY `test`.`t_time2`.`p` ORDER BY `test`.`t_time2`.`t` ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "26.65"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`p`",
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "24.00"
      },
      "table": {
        "table_name": "t_time2",
        "access_type": "ALL",
        "rows_examined_per_scan": 24,
        "rows_produced_per_join": 24,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.40",
          "prefix_cost": "2.65",
          "data_read_per_join": "384"
        },
        "used_columns": [
          "t",
          "p"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time2`.`p` AS `p`,`test`.`t_time2`.`t` AS `t`,first_value(`test`.`t_time2`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time2` window `w` AS (PARTITION BY `test`.`t_time2`.`p` ORDER BY `test`.`t_time2`.`t` RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "26.65"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`p`",
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "24.00"
      },
      "table": {
        "table_name": "t_time2",
        "access_type": "ALL",
        "rows_examined_per_scan": 24,
        "rows_produced_per_join": 24,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.40",
          "prefix_cost": "2.65",
          "data_read_per_join": "384"
        },
        "used_columns": [
          "t",
          "p"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time2`.`p` AS `p`,`test`.`t_time2`.`t` AS `t`,first_value(`test`.`t_time2`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time2` window `w` AS (PARTITION BY `test`.`t_time2`.`p` ORDER BY `test`.`t_time2`.`t` ROWS BETWEEN 2 FOLLOWING AND 3 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t RANGE BETWEEN INTERVAL 2 HOUR FOLLOWING AND INTERVAL 3 HOUR FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "26.65"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`p`",
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "24.00"
      },
      "table": {
        "table_name": "t_time2",
        "access_type": "ALL",
        "rows_examined_per_scan": 24,
        "rows_produced_per_join": 24,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "2.40",
          "prefix_cost": "2.65",
          "data_read_per_join": "384"
        },
        "used_columns": [
          "t",
          "p"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t_time2`.`p` AS `p`,`test`.`t_time2`.`t` AS `t`,first_value(`test`.`t_time2`.`t`) OVER `w` AS `FIRST_VALUE(t) OVER w` from `test`.`t_time2` window `w` AS (PARTITION BY `test`.`t_time2`.`p` ORDER BY `test`.`t_time2`.`t` RANGE BETWEEN INTERVAL 2 hour  FOLLOWING AND INTERVAL 3 hour  FOLLOWING) 
DROP TABLE t_time, t_time2;
----------------------------------------------------------------------
-    Aggregates with RANGE frame specification
----------------------------------------------------------------------
EXPLAIN FORMAT=JSON SELECT * FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.15"
    },
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 9,
      "rows_produced_per_join": 9,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.90",
        "prefix_cost": "1.15",
        "data_read_per_join": "144"
      },
      "used_columns": [
        "id",
        "sex"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`sex` AS `sex` from `test`.`t1`
Make t11 a clone of t1 but with an extra partitioning column, but other values
repeated, so we can test it the same frames work on more than one partition
CREATE TABLE t11 (id INTEGER, sex CHAR(1), p INTEGER DEFAULT 1);
INSERT INTO t11(id, sex) SELECT * FROM t1;
UPDATE t11 SET p=p+1;
INSERT INTO t11(id, sex) SELECT * FROM t1;
ANALYZE TABLE t11;
Table	Op	Msg_type	Msg_text
test.t11	analyze	status	OK
Make t22 a clone of t2 but with an extra partitioning column, but other values
repeated, so we can test it the same frames work on more than one partition
CREATE TABLE t22 (user_id INTEGER NOT NULL, date DATE, p INTEGER DEFAULT 1);
INSERT INTO t22(user_id, date) SELECT * FROM t2;
UPDATE t22 SET p=p+1;
INSERT INTO t22(user_id, date) SELECT * FROM t2;
ANALYZE TABLE t22;
Table	Op	Msg_type	Msg_text
test.t22	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT id, SUM(id) OVER (ORDER BY id RANGE 2 PRECEDING) FROM t1 ORDER BY id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "19.15"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "9.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`id`"
            ],
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "sum"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "9.00"
        },
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 9,
          "rows_produced_per_join": 9,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.90",
            "prefix_cost": "1.15",
            "data_read_per_join": "144"
          },
          "used_columns": [
            "id"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,sum(`test`.`t1`.`id`) OVER (ORDER BY `test`.`t1`.`id` RANGE BETWEEN 2 PRECEDING AND CURRENT ROW)  AS `SUM(id) OVER (ORDER BY id RANGE 2 PRECEDING)` from `test`.`t1` order by `test`.`t1`.`id`
EXPLAIN FORMAT=JSON SELECT id, SUM(id) OVER (ORDER BY id RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING) FROM t1 ORDER BY id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "19.15"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "9.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`id`"
            ],
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "sum"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "9.00"
        },
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 9,
          "rows_produced_per_join": 9,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.90",
            "prefix_cost": "1.15",
            "data_read_per_join": "144"
          },
          "used_columns": [
            "id"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,sum(`test`.`t1`.`id`) OVER (ORDER BY `test`.`t1`.`id` RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING)  AS `SUM(id) OVER (ORDER BY id RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING)` from `test`.`t1` order by `test`.`t1`.`id`
EXPLAIN FORMAT=JSON SELECT id, SUM(id) OVER (ORDER BY id RANGE UNBOUNDED PRECEDING) FROM t1 ORDER BY id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "19.15"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "9.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`id`"
            ],
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "sum"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "9.00"
        },
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 9,
          "rows_produced_per_join": 9,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.90",
            "prefix_cost": "1.15",
            "data_read_per_join": "144"
          },
          "used_columns": [
            "id"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,sum(`test`.`t1`.`id`) OVER (ORDER BY `test`.`t1`.`id` RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `SUM(id) OVER (ORDER BY id RANGE UNBOUNDED PRECEDING)` from `test`.`t1` order by `test`.`t1`.`id`
EXPLAIN FORMAT=JSON SELECT p, id, SUM(id) OVER (PARTITION BY p ORDER BY id RANGE 2 PRECEDING) FROM t11 ORDER BY p,id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "38.05"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "18.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`p`",
              "`id`"
            ],
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "sum"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "18.00"
        },
        "table": {
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 18,
          "rows_produced_per_join": 18,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "1.80",
            "prefix_cost": "2.05",
            "data_read_per_join": "288"
          },
          "used_columns": [
            "id",
            "p"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t11`.`p` AS `p`,`test`.`t11`.`id` AS `id`,sum(`test`.`t11`.`id`) OVER (PARTITION BY `test`.`t11`.`p` ORDER BY `test`.`t11`.`id` RANGE BETWEEN 2 PRECEDING AND CURRENT ROW)  AS `SUM(id) OVER (PARTITION BY p ORDER BY id RANGE 2 PRECEDING)` from `test`.`t11` order by `test`.`t11`.`p`,`test`.`t11`.`id`
EXPLAIN FORMAT=JSON SELECT p, id, SUM(id) OVER (PARTITION BY p ORDER BY id RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING) FROM t11 ORDER BY p,id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "38.05"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "18.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`p`",
              "`id`"
            ],
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "sum"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "18.00"
        },
        "table": {
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 18,
          "rows_produced_per_join": 18,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "1.80",
            "prefix_cost": "2.05",
            "data_read_per_join": "288"
          },
          "used_columns": [
            "id",
            "p"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t11`.`p` AS `p`,`test`.`t11`.`id` AS `id`,sum(`test`.`t11`.`id`) OVER (PARTITION BY `test`.`t11`.`p` ORDER BY `test`.`t11`.`id` RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING)  AS `SUM(id) OVER (PARTITION BY p ORDER BY id RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING)` from `test`.`t11` order by `test`.`t11`.`p`,`test`.`t11`.`id`
EXPLAIN FORMAT=JSON SELECT p, id, SUM(id) OVER (PARTITION BY p ORDER BY id RANGE UNBOUNDED PRECEDING) FROM t11 ORDER BY p,id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "38.05"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "18.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`p`",
              "`id`"
            ],
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "sum"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "18.00"
        },
        "table": {
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 18,
          "rows_produced_per_join": 18,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "1.80",
            "prefix_cost": "2.05",
            "data_read_per_join": "288"
          },
          "used_columns": [
            "id",
            "p"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t11`.`p` AS `p`,`test`.`t11`.`id` AS `id`,sum(`test`.`t11`.`id`) OVER (PARTITION BY `test`.`t11`.`p` ORDER BY `test`.`t11`.`id` RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `SUM(id) OVER (PARTITION BY p ORDER BY id RANGE UNBOUNDED PRECEDING)` from `test`.`t11` order by `test`.`t11`.`p`,`test`.`t11`.`id`
Implicit frame due to ORDER BY, with last in peer group as upper bound
EXPLAIN FORMAT=JSON SELECT user_id, SUM(user_id) OVER w, AVG(user_id) OVER w FROM t2 WINDOW w AS (ORDER BY user_id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`user_id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum",
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "user_id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`user_id` AS `user_id`,sum(`test`.`t2`.`user_id`) OVER `w` AS `SUM(user_id) OVER w`,avg(`test`.`t2`.`user_id`) OVER `w` AS `AVG(user_id) OVER w` from `test`.`t2` window `w` AS (ORDER BY `test`.`t2`.`user_id` ) 
EXPLAIN FORMAT=JSON SELECT p, user_id, SUM(user_id) OVER w, AVG(user_id) OVER w FROM t22 WINDOW w AS (PARTITION BY p ORDER BY user_id) ORDER BY p;
EXPLAIN
              "`p`",
              "`user_id`"
              "avg"
              "optimized_frame_evaluation": true
              "sum",
              "using_temporary_table": true,
            "data_read_per_join": "224"
            "eval_cost": "1.40",
            "filesort_key": [
            "frame_buffer": {
            "functions": [
            "name": "w",
            "p"
            "prefix_cost": "1.65",
            "read_cost": "0.25",
            "user_id",
            "using_filesort": true,
            "using_temporary_table": true,
            ]
            ],
            },
          "access_type": "ALL",
          "cost_info": {
          "filtered": "100.00",
          "rows_examined_per_scan": 14,
          "rows_produced_per_join": 14,
          "sort_cost": "14.00"
          "table_name": "t22",
          "used_columns": [
          ]
          {
          }
          },
        "cost_info": {
        "sort_cost": "14.00"
        "table": {
        "windows": [
        ],
        }
        },
      "cost_info": {
      "query_cost": "29.65"
      "using_filesort": true,
      "windowing": {
      }
      },
    "cost_info": {
    "ordering_operation": {
    "select_id": 1,
    }
    },
  "query_block": {
  }
Note	1003	/* select#1 */ select `test`.`t22`.`p` AS `p`,`test`.`t22`.`user_id` AS `user_id`,sum(`test`.`t22`.`user_id`) OVER `w` AS `SUM(user_id) OVER w`,avg(`test`.`t22`.`user_id`) OVER `w` AS `AVG(user_id) OVER w` from `test`.`t22` window `w` AS (PARTITION BY `test`.`t22`.`p` ORDER BY `test`.`t22`.`user_id` )  order by `test`.`t22`.`p`
Warnings:
{
}
Window function use of same field in different windows, both of which
need buffering. In this case we need subsequent rewrites of arg fields
Field pointer in tmp files for window 2..n The intervening internal
window buffering in each step used to mess that up.
EXPLAIN FORMAT=JSON SELECT user_id, SUM(user_id) OVER w, AVG(user_id) OVER w1 FROM t2
WINDOW w AS (ORDER BY user_id), w1 AS (ORDER BY user_id);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`user_id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        },
        {
          "name": "w1",
          "definition_position": 2,
          "last_executed_window": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "user_id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`user_id` AS `user_id`,sum(`test`.`t2`.`user_id`) OVER `w` AS `SUM(user_id) OVER w`,avg(`test`.`t2`.`user_id`) OVER `w1` AS `AVG(user_id) OVER w1` from `test`.`t2` window `w` AS (ORDER BY `test`.`t2`.`user_id` ) , `w1` AS (ORDER BY `test`.`t2`.`user_id` ) 
Check descending order by with RANGE: 2 PRECEDING in this case means larger than
current row.
EXPLAIN FORMAT=JSON SELECT NTILE(5) OVER w, ROW_NUMBER() OVER w, id, SUM(id) OVER w FROM t1
WINDOW w AS (ORDER BY id DESC RANGE 2 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id` desc"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "ntile",
            "row_number",
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	3599	Window function 'ntile' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	3599	Window function 'row_number' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	1003	/* select#1 */ select ntile(5) OVER `w` AS `NTILE(5) OVER w`,row_number() OVER `w` AS `ROW_NUMBER() OVER w`,`test`.`t1`.`id` AS `id`,sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` desc RANGE BETWEEN 2 PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT p, NTILE(5) OVER w, ROW_NUMBER() OVER w, id, SUM(id) OVER w FROM t11
WINDOW w AS (PARTITION BY p ORDER BY id DESC RANGE 2 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "20.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`p`",
            "`id` desc"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "ntile",
            "row_number",
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "18.00"
      },
      "table": {
        "table_name": "t11",
        "access_type": "ALL",
        "rows_examined_per_scan": 18,
        "rows_produced_per_join": 18,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.80",
          "prefix_cost": "2.05",
          "data_read_per_join": "288"
        },
        "used_columns": [
          "id",
          "p"
        ]
      }
    }
  }
}
Warnings:
Note	3599	Window function 'ntile' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	3599	Window function 'row_number' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	1003	/* select#1 */ select `test`.`t11`.`p` AS `p`,ntile(5) OVER `w` AS `NTILE(5) OVER w`,row_number() OVER `w` AS `ROW_NUMBER() OVER w`,`test`.`t11`.`id` AS `id`,sum(`test`.`t11`.`id`) OVER `w` AS `SUM(id) OVER w` from `test`.`t11` window `w` AS (PARTITION BY `test`.`t11`.`p` ORDER BY `test`.`t11`.`id` desc RANGE BETWEEN 2 PRECEDING AND CURRENT ROW) 
update t2 set date=date + user_id;
EXPLAIN FORMAT=JSON SELECT user_id, date, COUNT(*) OVER (ORDER BY date RANGE INTERVAL 1 DAY PRECEDING) FROM t2;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`date`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "user_id",
          "date"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`user_id` AS `user_id`,`test`.`t2`.`date` AS `date`,count(0) OVER (ORDER BY `test`.`t2`.`date` RANGE BETWEEN INTERVAL 1 day  PRECEDING AND CURRENT ROW)  AS `COUNT(*) OVER (ORDER BY date RANGE INTERVAL 1 DAY PRECEDING)` from `test`.`t2`
CREATE TABLE t3(d DOUBLE);
INSERT INTO t3
VALUES (1.1),(1.9),(4.0),(8.3),(16.0),(24.0),(20.1),(22.0),(23.0);
ANALYZE TABLE t3;
Table	Op	Msg_type	Msg_text
test.t3	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT d, SUM(d) OVER w, COUNT(*) OVER w FROM t3 WINDOW w AS (ORDER BY d RANGE BETWEEN 2.1 PRECEDING AND 1.1 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true
          },
          "functions": [
            "sum",
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t3",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`d` AS `d`,sum(`test`.`t3`.`d`) OVER `w` AS `SUM(d) OVER w`,count(0) OVER `w` AS `COUNT(*) OVER w` from `test`.`t3` window `w` AS (ORDER BY `test`.`t3`.`d` RANGE BETWEEN 2.1 PRECEDING AND 1.1 FOLLOWING) 
----------------------------------------------------------------------
-    wf over JSON
----------------------------------------------------------------------
CREATE TABLE tj(j JSON, i INT DEFAULT 7);
INSERT INTO tj(j) VALUES ('1'), ('2'), ('3'), ('4'), ('5'), (NULL);
INSERT INTO tj(j) VALUES ('3.14');
INSERT INTO tj(j) VALUES ('[1,2,3]');
ANALYZE TABLE tj;
Table	Op	Msg_type	Msg_text
test.tj	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT CAST(SUM(j) OVER () AS JSON) FROM tj;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "table": {
        "table_name": "tj",
        "access_type": "ALL",
        "rows_examined_per_scan": 8,
        "rows_produced_per_join": 8,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.80",
          "prefix_cost": "1.05",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "j"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select cast(sum(`test`.`tj`.`j`) OVER ()  as json) AS `CAST(SUM(j) OVER () AS JSON)` from `test`.`tj`
----------------------------------------------------------------------
-    SELECT DISTINCT
----------------------------------------------------------------------
One window
EXPLAIN FORMAT=JSON SELECT DISTINCT i,COUNT(*) OVER () FROM tj;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.05"
    },
    "duplicates_removal": {
      "using_filesort": false,
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "count"
            ]
          }
        ],
        "table": {
          "table_name": "tj",
          "access_type": "ALL",
          "rows_examined_per_scan": 8,
          "rows_produced_per_join": 8,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.80",
            "prefix_cost": "1.05",
            "data_read_per_join": "192"
          },
          "used_columns": [
            "i"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`tj`.`i` AS `i`,count(0) OVER ()  AS `COUNT(*) OVER ()` from `test`.`tj`
Several windows with final ORDER BY also
EXPLAIN FORMAT=JSON SELECT DISTINCT i,NTILE(3) OVER (ORDER BY i), SUM(i) OVER (), COUNT(*) OVER () FROM tj ORDER BY NTILE(3) OVER (ORDER BY i);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "17.05"
    },
    "ordering_operation": {
      "using_filesort": true,
      "duplicates_removal": {
        "using_filesort": false,
        "cost_info": {
          "sort_cost": "8.00"
        },
        "windowing": {
          "windows": [
            {
              "name": "<unnamed window>",
              "definition_position": 1,
              "using_temporary_table": true,
              "using_filesort": true,
              "filesort_key": [
                "`i`"
              ],
              "frame_buffer": {
                "using_temporary_table": true,
                "optimized_frame_evaluation": true
              },
              "functions": [
                "ntile"
              ]
            },
            {
              "name": "<unnamed window>",
              "definition_position": 4,
              "using_temporary_table": true,
              "frame_buffer": {
                "using_temporary_table": true,
                "optimized_frame_evaluation": true
              },
              "functions": [
                "ntile"
              ]
            },
            {
              "name": "<unnamed window>",
              "definition_position": 3,
              "using_temporary_table": true,
              "frame_buffer": {
                "using_temporary_table": true,
                "optimized_frame_evaluation": true
              },
              "functions": [
                "count"
              ]
            },
            {
              "name": "<unnamed window>",
              "definition_position": 2,
              "last_executed_window": true,
              "using_temporary_table": true,
              "frame_buffer": {
                "using_temporary_table": true,
                "optimized_frame_evaluation": true
              },
              "functions": [
                "sum"
              ]
            }
          ],
          "cost_info": {
            "sort_cost": "8.00"
          },
          "table": {
            "table_name": "tj",
            "access_type": "ALL",
            "rows_examined_per_scan": 8,
            "rows_produced_per_join": 8,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.80",
              "prefix_cost": "1.05",
              "data_read_per_join": "192"
            },
            "used_columns": [
              "i"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`tj`.`i` AS `i`,ntile(3) OVER (ORDER BY `test`.`tj`.`i` )  AS `NTILE(3) OVER (ORDER BY i)`,sum(`test`.`tj`.`i`) OVER ()  AS `SUM(i) OVER ()`,count(0) OVER ()  AS `COUNT(*) OVER ()` from `test`.`tj` order by ntile(3) OVER (ORDER BY `test`.`tj`.`i` ) 
UPDATE tj SET i=i+CASE WHEN JSON_TYPE(j) = 'ARRAY' THEN 1 ELSE j END;
UPDATE tj SET i=7 where i=8 AND JSON_TYPE(j) != 'ARRAY';
CREATE TABLE tj2 AS SELECT * FROM tj;
UPDATE tj2 SET i=MOD(i,3);
EXPLAIN FORMAT=JSON SELECT * FROM tj2;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.05"
    },
    "table": {
      "table_name": "tj2",
      "access_type": "ALL",
      "rows_examined_per_scan": 8,
      "rows_produced_per_join": 8,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.80",
        "prefix_cost": "1.05",
        "data_read_per_join": "192"
      },
      "used_columns": [
        "j",
        "i"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`tj2`.`j` AS `j`,`test`.`tj2`.`i` AS `i` from `test`.`tj2`
With GROUP BY
EXPLAIN FORMAT=JSON SELECT          COUNT(*) OVER (), MOD(SUM(i),2) FROM tj2 GROUP BY i;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count"
          ]
        }
      ],
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "tj2",
          "access_type": "ALL",
          "rows_examined_per_scan": 8,
          "rows_produced_per_join": 8,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.80",
            "prefix_cost": "1.05",
            "data_read_per_join": "192"
          },
          "used_columns": [
            "i"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select count(0) OVER ()  AS `COUNT(*) OVER ()`,(sum(`test`.`tj2`.`i`) % 2) AS `MOD(SUM(i),2)` from `test`.`tj2` group by `test`.`tj2`.`i`
EXPLAIN FORMAT=JSON SELECT DISTINCT COUNT(*) OVER (), MOD(SUM(i),2) FROM tj2 GROUP BY i;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.05"
    },
    "duplicates_removal": {
      "using_temporary_table": true,
      "using_filesort": false,
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "count"
            ]
          }
        ],
        "grouping_operation": {
          "using_temporary_table": true,
          "using_filesort": false,
          "table": {
            "table_name": "tj2",
            "access_type": "ALL",
            "rows_examined_per_scan": 8,
            "rows_produced_per_join": 8,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.80",
              "prefix_cost": "1.05",
              "data_read_per_join": "192"
            },
            "used_columns": [
              "i"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct count(0) OVER ()  AS `COUNT(*) OVER ()`,(sum(`test`.`tj2`.`i`) % 2) AS `MOD(SUM(i),2)` from `test`.`tj2` group by `test`.`tj2`.`i`
Bug fix GROUP BY with window function referring column used in grouping expression
EXPLAIN FORMAT=JSON SELECT i, SUM(i) OVER (), MOD(SUM(i),2) FROM tj2 GROUP BY i;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "tj2",
          "access_type": "ALL",
          "rows_examined_per_scan": 8,
          "rows_produced_per_join": 8,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.80",
            "prefix_cost": "1.05",
            "data_read_per_join": "192"
          },
          "used_columns": [
            "i"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`tj2`.`i` AS `i`,sum(`test`.`tj2`.`i`) OVER ()  AS `SUM(i) OVER ()`,(sum(`test`.`tj2`.`i`) % 2) AS `MOD(SUM(i),2)` from `test`.`tj2` group by `test`.`tj2`.`i`
EXPLAIN FORMAT=JSON SELECT i, SUM(SUM(i)) OVER (), SUM(i) OVER (ORDER BY i), MOD(SUM(i),2), SUM(i) FROM tj2 GROUP BY i;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "9.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "using_filesort": true,
          "filesort_key": [
            "`i`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "8.00"
      },
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "tj2",
          "access_type": "ALL",
          "rows_examined_per_scan": 8,
          "rows_produced_per_join": 8,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.80",
            "prefix_cost": "1.05",
            "data_read_per_join": "192"
          },
          "used_columns": [
            "i"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`tj2`.`i` AS `i`,sum(```sum(tj2.i)```) OVER ()  AS `SUM(SUM(i)) OVER ()`,sum(`test`.`tj2`.`i`) OVER (ORDER BY `test`.`tj2`.`i` )  AS `SUM(i) OVER (ORDER BY i)`,(sum(`test`.`tj2`.`i`) % 2) AS `MOD(SUM(i),2)`,sum(`test`.`tj2`.`i`) AS `SUM(i)` from `test`.`tj2` group by `test`.`tj2`.`i`
DROP TABLE tj2;
----------------------------------------------------------------------
-    Bug fixes
----------------------------------------------------------------------
Bug fix for FIRST_VALUE, LAST_VALUE when not buffered processing
EXPLAIN FORMAT=JSON SELECT LAST_VALUE(j) OVER w, FIRST_VALUE(j) OVER w FROM tj WINDOW w AS (PARTITION BY i ORDER BY j ROWS UNBOUNDED PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "9.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`i`",
            "`j`"
          ],
          "functions": [
            "last_value",
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "8.00"
      },
      "table": {
        "table_name": "tj",
        "access_type": "ALL",
        "rows_examined_per_scan": 8,
        "rows_produced_per_join": 8,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.80",
          "prefix_cost": "1.05",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "j",
          "i"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select last_value(`test`.`tj`.`j`) OVER `w` AS `LAST_VALUE(j) OVER w`,first_value(`test`.`tj`.`j`) OVER `w` AS `FIRST_VALUE(j) OVER w` from `test`.`tj` window `w` AS (PARTITION BY `test`.`tj`.`i` ORDER BY `test`.`tj`.`j` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) 
Bug missing hidden column (j) induction to select list: FIRST_VALUE/LAST_VALUE
EXPLAIN FORMAT=JSON SELECT i, LAST_VALUE((CAST(j AS UNSIGNED))) OVER w, FIRST_VALUE(CAST(j AS UNSIGNED)) OVER w FROM tj
WINDOW w AS (PARTITION BY i ORDER BY CAST(j AS UNSIGNED) RANGE UNBOUNDED PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "9.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`i`",
            "cast(`j` as unsigned)"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "last_value",
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "8.00"
      },
      "table": {
        "table_name": "tj",
        "access_type": "ALL",
        "rows_examined_per_scan": 8,
        "rows_produced_per_join": 8,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.80",
          "prefix_cost": "1.05",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "j",
          "i"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`tj`.`i` AS `i`,last_value(cast(`test`.`tj`.`j` as unsigned)) OVER `w` AS `LAST_VALUE((CAST(j AS UNSIGNED))) OVER w`,first_value(cast(`test`.`tj`.`j` as unsigned)) OVER `w` AS `FIRST_VALUE(CAST(j AS UNSIGNED)) OVER w` from `test`.`tj` window `w` AS (PARTITION BY `test`.`tj`.`i` ORDER BY cast(`test`.`tj`.`j` as unsigned) RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) 
Fix for lineno in warnings buffered and unbuffered windows
EXPLAIN FORMAT=JSON SELECT j,CAST(SUM(j) OVER (PARTITION BY i) AS JSON), CAST(SUM(j) OVER () AS JSON) FROM tj;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "9.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`i`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "8.00"
      },
      "table": {
        "table_name": "tj",
        "access_type": "ALL",
        "rows_examined_per_scan": 8,
        "rows_produced_per_join": 8,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.80",
          "prefix_cost": "1.05",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "j",
          "i"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`tj`.`j` AS `j`,cast(```sum(tj.j) OVER (PARTITION BY tj.i ) ``` as json) AS `CAST(SUM(j) OVER (PARTITION BY i) AS JSON)`,cast(sum(`test`.`tj`.`j`) OVER ()  as json) AS `CAST(SUM(j) OVER () AS JSON)` from `test`.`tj`
EXPLAIN FORMAT=JSON SELECT j,CAST(SUM(j) OVER (PARTITION BY i ROWS UNBOUNDED PRECEDING) AS JSON), CAST(SUM(j) OVER (PARTITION BY i ROWS UNBOUNDED PRECEDING) AS JSON) FROM tj;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "9.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`i`"
          ],
          "functions": [
            "sum"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "8.00"
      },
      "table": {
        "table_name": "tj",
        "access_type": "ALL",
        "rows_examined_per_scan": 8,
        "rows_produced_per_join": 8,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.80",
          "prefix_cost": "1.05",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "j",
          "i"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`tj`.`j` AS `j`,cast(sum(`test`.`tj`.`j`) OVER (PARTITION BY `test`.`tj`.`i` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  as json) AS `CAST(SUM(j) OVER (PARTITION BY i ROWS UNBOUNDED PRECEDING) AS JSON)`,cast(sum(`test`.`tj`.`j`) OVER (PARTITION BY `test`.`tj`.`i` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  as json) AS `CAST(SUM(j) OVER (PARTITION BY i ROWS UNBOUNDED PRECEDING) AS JSON)` from `test`.`tj`
Bug fix for UNION
EXPLAIN FORMAT=JSON SELECT i, ROW_NUMBER() OVER () FROM tj UNION ALL SELECT i, ROW_NUMBER() OVER () FROM tj;
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": false,
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "1.05"
            },
            "windowing": {
              "windows": [
                {
                  "name": "<unnamed window>",
                  "functions": [
                    "row_number"
                  ]
                }
              ],
              "table": {
                "table_name": "tj",
                "access_type": "ALL",
                "rows_examined_per_scan": 8,
                "rows_produced_per_join": 8,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.80",
                  "prefix_cost": "1.05",
                  "data_read_per_join": "192"
                },
                "used_columns": [
                  "i"
                ]
              }
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.05"
            },
            "windowing": {
              "windows": [
                {
                  "name": "<unnamed window>",
                  "functions": [
                    "row_number"
                  ]
                }
              ],
              "table": {
                "table_name": "tj",
                "access_type": "ALL",
                "rows_examined_per_scan": 8,
                "rows_produced_per_join": 8,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.80",
                  "prefix_cost": "1.05",
                  "data_read_per_join": "192"
                },
                "used_columns": [
                  "i"
                ]
              }
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`tj`.`i` AS `i`,row_number() OVER ()  AS `ROW_NUMBER() OVER ()` from `test`.`tj` union all /* select#2 */ select `test`.`tj`.`i` AS `i`,row_number() OVER ()  AS `ROW_NUMBER() OVER ()` from `test`.`tj`
EXPLAIN FORMAT=JSON SELECT * FROM (SELECT i, j, ROW_NUMBER() OVER (ORDER BY j) FROM tj UNION SELECT i, j, ROW_NUMBER() OVER (ORDER BY j) FROM tj) alias;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.30"
    },
    "table": {
      "table_name": "alias",
      "access_type": "ALL",
      "rows_examined_per_scan": 16,
      "rows_produced_per_join": 16,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "2.70",
        "eval_cost": "1.60",
        "prefix_cost": "4.30",
        "data_read_per_join": "640"
      },
      "used_columns": [
        "<hash_field>",
        "i",
        "j",
        "ROW_NUMBER() OVER (ORDER BY j)"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "union_result": {
            "using_temporary_table": true,
            "select_id": 4,
            "table_name": "<union2,3>",
            "access_type": "ALL",
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 2,
                  "cost_info": {
                    "query_cost": "9.05"
                  },
                  "windowing": {
                    "windows": [
                      {
                        "name": "<unnamed window>",
                        "using_filesort": true,
                        "filesort_key": [
                          "`j`"
                        ],
                        "functions": [
                          "row_number"
                        ]
                      }
                    ],
                    "cost_info": {
                      "sort_cost": "8.00"
                    },
                    "table": {
                      "table_name": "tj",
                      "access_type": "ALL",
                      "rows_examined_per_scan": 8,
                      "rows_produced_per_join": 8,
                      "filtered": "100.00",
                      "cost_info": {
                        "read_cost": "0.25",
                        "eval_cost": "0.80",
                        "prefix_cost": "1.05",
                        "data_read_per_join": "192"
                      },
                      "used_columns": [
                        "j",
                        "i"
                      ]
                    }
                  }
                }
              },
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 3,
                  "cost_info": {
                    "query_cost": "9.05"
                  },
                  "windowing": {
                    "windows": [
                      {
                        "name": "<unnamed window>",
                        "using_filesort": true,
                        "filesort_key": [
                          "`j`"
                        ],
                        "functions": [
                          "row_number"
                        ]
                      }
                    ],
                    "cost_info": {
                      "sort_cost": "8.00"
                    },
                    "table": {
                      "table_name": "tj",
                      "access_type": "ALL",
                      "rows_examined_per_scan": 8,
                      "rows_produced_per_join": 8,
                      "filtered": "100.00",
                      "cost_info": {
                        "read_cost": "0.25",
                        "eval_cost": "0.80",
                        "prefix_cost": "1.05",
                        "data_read_per_join": "192"
                      },
                      "used_columns": [
                        "j",
                        "i"
                      ]
                    }
                  }
                }
              }
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `alias`.`i` AS `i`,`alias`.`j` AS `j`,`alias`.`ROW_NUMBER() OVER (ORDER BY j)` AS `ROW_NUMBER() OVER (ORDER BY j)` from (/* select#2 */ select `test`.`tj`.`i` AS `i`,`test`.`tj`.`j` AS `j`,row_number() OVER (ORDER BY `test`.`tj`.`j` )  AS `ROW_NUMBER() OVER (ORDER BY j)` from `test`.`tj` union /* select#3 */ select `test`.`tj`.`i` AS `i`,`test`.`tj`.`j` AS `j`,row_number() OVER (ORDER BY `test`.`tj`.`j` )  AS `ROW_NUMBER() OVER (ORDER BY j)` from `test`.`tj`) `alias`
DROP TABLE tj;
----------------------------------------------------------------------
-    More JSON
----------------------------------------------------------------------
CREATE TABLE tj(j JSON);
INSERT INTO tj VALUES ('1'), ('2'), ('3'), ('4'), ('5'), (NULL);
EXPLAIN FORMAT=JSON SELECT j, JSON_TYPE(j), SUM(j) OVER (ORDER BY j ROWS 3 PRECEDING) FROM tj;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`j`"
          ],
          "frame_buffer": {
            "using_temporary_table": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "tj",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "96"
        },
        "used_columns": [
          "j"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`tj`.`j` AS `j`,json_type(`test`.`tj`.`j`) AS `JSON_TYPE(j)`,sum(`test`.`tj`.`j`) OVER (ORDER BY `test`.`tj`.`j` ROWS BETWEEN 3 PRECEDING AND CURRENT ROW)  AS `SUM(j) OVER (ORDER BY j ROWS 3 PRECEDING)` from `test`.`tj`
INSERT INTO tj VALUES ('3.14');
EXPLAIN FORMAT=JSON SELECT j, JSON_TYPE(j), SUM(j) OVER (ORDER BY j ROWS 3 PRECEDING) FROM tj;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`j`"
          ],
          "frame_buffer": {
            "using_temporary_table": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "tj",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "j"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`tj`.`j` AS `j`,json_type(`test`.`tj`.`j`) AS `JSON_TYPE(j)`,sum(`test`.`tj`.`j`) OVER (ORDER BY `test`.`tj`.`j` ROWS BETWEEN 3 PRECEDING AND CURRENT ROW)  AS `SUM(j) OVER (ORDER BY j ROWS 3 PRECEDING)` from `test`.`tj`
INSERT INTO tj VALUES ('[1,2,3]');
ANALYZE TABLE tj;
Table	Op	Msg_type	Msg_text
test.tj	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT j,
JSON_TYPE(j),
SUM(CASE WHEN JSON_TYPE(j) = 'ARRAY' THEN j->"$[0]" ELSE j END)
OVER (ORDER BY j ROWS 3 PRECEDING)
FROM tj;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "9.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`j`"
          ],
          "frame_buffer": {
            "using_temporary_table": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "8.00"
      },
      "table": {
        "table_name": "tj",
        "access_type": "ALL",
        "rows_examined_per_scan": 8,
        "rows_produced_per_join": 8,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.80",
          "prefix_cost": "1.05",
          "data_read_per_join": "128"
        },
        "used_columns": [
          "j"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`tj`.`j` AS `j`,json_type(`test`.`tj`.`j`) AS `JSON_TYPE(j)`,sum((case when (json_type(`test`.`tj`.`j`) = 'ARRAY') then json_extract(`test`.`tj`.`j`,'$[0]') else `test`.`tj`.`j` end)) OVER (ORDER BY `test`.`tj`.`j` ROWS BETWEEN 3 PRECEDING AND CURRENT ROW)  AS `SUM(CASE WHEN JSON_TYPE(j) = 'ARRAY' THEN j->"$[0]" ELSE j END)
OVER (ORDER BY j ROWS 3 PRECEDING)` from `test`.`tj`
CREATE TABLE t5(b BIGINT UNSIGNED);
INSERT INTO t5 VALUES (1), (2), (3), (4), (5), (6), (7);
ANALYZE TABLE t5;
Table	Op	Msg_type	Msg_text
test.t5	analyze	status	OK
last row should have COUNT(*) == 0 , not 1 (bug fix)
EXPLAIN FORMAT=JSON SELECT b, COUNT(*) OVER (ORDER BY b RANGE BETWEEN 1 FOLLOWING AND  100 FOLLOWING) bb FROM t5;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "7.95"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`b`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t5",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.70",
          "prefix_cost": "0.95",
          "data_read_per_join": "112"
        },
        "used_columns": [
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`b` AS `b`,count(0) OVER (ORDER BY `test`.`t5`.`b` RANGE BETWEEN 1 FOLLOWING AND 100 FOLLOWING)  AS `bb` from `test`.`t5`
CREATE TABLE t6(t TIME, ts TIMESTAMP);
INSERT INTO t6 VALUES ('12:30', '2016-07-05 08:30:42');
INSERT INTO t6 VALUES ('22:30', '2015-07-05 08:30:43');
INSERT INTO t6 VALUES ('13:30', '2014-07-05 08:30:44');
INSERT INTO t6 VALUES ('01:30', '2013-07-05 08:30:45');
INSERT INTO t6 VALUES ('15:30', '2016-08-05 08:31:42');
INSERT INTO t6 VALUES ('20:30', '2016-09-05 08:32:42');
INSERT INTO t6 VALUES ('04:30', '2016-10-05 08:33:42');
INSERT INTO t6 VALUES ('06:30', '2016-11-05 08:34:42');
INSERT INTO t6 VALUES ('18:30', '2016-07-05 09:30:42');
INSERT INTO t6 VALUES ('21:30', '2016-07-06 10:30:42');
INSERT INTO t6 VALUES ('00:30', '2016-07-07 11:30:42');
INSERT INTO t6 VALUES ('00:31', '2016-07-08 12:30:42');
ANALYZE TABLE t6;
Table	Op	Msg_type	Msg_text
test.t6	analyze	status	OK
INTERVAL specified with string as below failed
EXPLAIN FORMAT=JSON SELECT t, COUNT(*) OVER (ORDER BY t RANGE
BETWEEN INTERVAL 1 HOUR PRECEDING AND INTERVAL '2:2' MINUTE_SECOND FOLLOWING) AS cnt FROM t6;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "12.00"
      },
      "table": {
        "table_name": "t6",
        "access_type": "ALL",
        "rows_examined_per_scan": 12,
        "rows_produced_per_join": 12,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.20",
          "prefix_cost": "1.45",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "t"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t6`.`t` AS `t`,count(0) OVER (ORDER BY `test`.`t6`.`t` RANGE BETWEEN INTERVAL 1 hour  PRECEDING AND INTERVAL '2:2' minute_second  FOLLOWING)  AS `cnt` from `test`.`t6`
----------------------------------------------------------------------
-    Window spec inheritance
----------------------------------------------------------------------
EXPLAIN FORMAT=JSON SELECT COUNT(*) OVER w0,
COUNT(*) OVER w,
COUNT(*) OVER w1 FROM t6
WINDOW w0 AS (),
w AS (w0 ORDER BY t),
w1 AS (w RANGE BETWEEN INTERVAL 24 HOUR  PRECEDING AND INTERVAL '2:2' MINUTE_SECOND FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "13.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "w0",
          "definition_position": 1,
          "using_temporary_table": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count"
          ]
        },
        {
          "name": "w",
          "definition_position": 2,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`t`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count"
          ]
        },
        {
          "name": "w1",
          "definition_position": 3,
          "last_executed_window": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "12.00"
      },
      "table": {
        "table_name": "t6",
        "access_type": "ALL",
        "rows_examined_per_scan": 12,
        "rows_produced_per_join": 12,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.20",
          "prefix_cost": "1.45",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "t"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select count(0) OVER `w0` AS `COUNT(*) OVER w0`,count(0) OVER `w` AS `COUNT(*) OVER w`,count(0) OVER `w1` AS `COUNT(*) OVER w1` from `test`.`t6` window `w0` AS () , `w` AS (`w0` ORDER BY `test`.`t6`.`t` ) , `w1` AS (`w` RANGE BETWEEN INTERVAL 24 hour  PRECEDING AND INTERVAL '2:2' minute_second  FOLLOWING) 
CREATE VIEW v AS
SELECT COUNT(*) OVER w0,
COUNT(*) OVER w,
COUNT(*) OVER w1 FROM t6
WINDOW w0 AS (),
w AS (w0 ORDER BY t),
w1 AS (w RANGE BETWEEN INTERVAL 24 HOUR  PRECEDING AND INTERVAL '2:2' MINUTE_SECOND FOLLOWING);
SHOW CREATE VIEW v;
View	Create View	character_set_client	collation_connection
v	CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v` AS select count(0) OVER `w0` AS `COUNT(*) OVER w0`,count(0) OVER `w` AS `COUNT(*) OVER w`,count(0) OVER `w1` AS `COUNT(*) OVER w1` from `t6` window `w0` AS () , `w` AS (`w0` ORDER BY `t6`.`t` ) , `w1` AS (`w` RANGE BETWEEN INTERVAL 24 hour  PRECEDING AND INTERVAL '2:2' minute_second  FOLLOWING) 	utf8mb3	utf8mb3_general_ci
EXPLAIN FORMAT=JSON SELECT * FROM v;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.85"
    },
    "table": {
      "table_name": "v",
      "access_type": "ALL",
      "rows_examined_per_scan": 12,
      "rows_produced_per_join": 12,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "2.65",
        "eval_cost": "1.20",
        "prefix_cost": "3.85",
        "data_read_per_join": "384"
      },
      "used_columns": [
        "COUNT(*) OVER w0",
        "COUNT(*) OVER w",
        "COUNT(*) OVER w1"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "13.45"
          },
          "windowing": {
            "windows": [
              {
                "name": "w0",
                "definition_position": 1,
                "using_temporary_table": true,
                "frame_buffer": {
                  "using_temporary_table": true,
                  "optimized_frame_evaluation": true
                },
                "functions": [
                  "count"
                ]
              },
              {
                "name": "w",
                "definition_position": 2,
                "using_temporary_table": true,
                "using_filesort": true,
                "filesort_key": [
                  "`t`"
                ],
                "frame_buffer": {
                  "using_temporary_table": true,
                  "optimized_frame_evaluation": true
                },
                "functions": [
                  "count"
                ]
              },
              {
                "name": "w1",
                "definition_position": 3,
                "last_executed_window": true,
                "frame_buffer": {
                  "using_temporary_table": true,
                  "optimized_frame_evaluation": true
                },
                "functions": [
                  "count"
                ]
              }
            ],
            "cost_info": {
              "sort_cost": "12.00"
            },
            "table": {
              "table_name": "t6",
              "access_type": "ALL",
              "rows_examined_per_scan": 12,
              "rows_produced_per_join": 12,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "1.20",
                "prefix_cost": "1.45",
                "data_read_per_join": "192"
              },
              "used_columns": [
                "t"
              ]
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`v`.`COUNT(*) OVER w0` AS `COUNT(*) OVER w0`,`test`.`v`.`COUNT(*) OVER w` AS `COUNT(*) OVER w`,`test`.`v`.`COUNT(*) OVER w1` AS `COUNT(*) OVER w1` from `test`.`v`
DROP VIEW v;
----------------------------------------------------------------------
- Bugs with induction of hidden fields from window function also used
- in ORDER BY/PARTITION BY
----------------------------------------------------------------------
EXPLAIN FORMAT=JSON SELECT id, AVG(id) OVER (PARTITION BY id) summ FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,avg(`test`.`t1`.`id`) OVER (PARTITION BY `test`.`t1`.`id` )  AS `summ` from `test`.`t1`
EXPLAIN FORMAT=JSON SELECT     AVG(id) OVER (PARTITION BY id) summ FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select avg(`test`.`t1`.`id`) OVER (PARTITION BY `test`.`t1`.`id` )  AS `summ` from `test`.`t1`
EXPLAIN FORMAT=JSON SELECT id, AVG(id) OVER (PARTITION BY id) summ,
AVG(id) OVER (PARTITION BY id) summ2 FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,avg(`test`.`t1`.`id`) OVER (PARTITION BY `test`.`t1`.`id` )  AS `summ`,avg(`test`.`t1`.`id`) OVER (PARTITION BY `test`.`t1`.`id` )  AS `summ2` from `test`.`t1`
EXPLAIN FORMAT=JSON SELECT     AVG(id) OVER (PARTITION BY id) summ,
AVG(id) OVER (PARTITION BY id) summ2 FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "144"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select avg(`test`.`t1`.`id`) OVER (PARTITION BY `test`.`t1`.`id` )  AS `summ`,avg(`test`.`t1`.`id`) OVER (PARTITION BY `test`.`t1`.`id` )  AS `summ2` from `test`.`t1`
Bug for AVG in presence of several NULLs
INSERT INTO t1 VALUES (NULL, 'F');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT COUNT(id) OVER w, id, AVG(id) OVER w, SUM(id) OVER w, FIRST_VALUE(id) OVER w FROM t1
WINDOW w AS (ORDER BY id RANGE 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "11.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count",
            "avg",
            "sum",
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "10.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "rows_produced_per_join": 10,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.00",
          "prefix_cost": "1.25",
          "data_read_per_join": "160"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select count(`test`.`t1`.`id`) OVER `w` AS `COUNT(id) OVER w`,`test`.`t1`.`id` AS `id`,avg(`test`.`t1`.`id`) OVER `w` AS `AVG(id) OVER w`,sum(`test`.`t1`.`id`) OVER `w` AS `SUM(id) OVER w`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` RANGE BETWEEN 1 PRECEDING AND CURRENT ROW) 
Check frame size, COUNT(*) vs COUNT(<column>) in frames with NULLs
EXPLAIN FORMAT=JSON SELECT id, count(id) over w, count(*) over w, FIRST_VALUE(id) OVER w FROM t1
WINDOW w AS (ORDER BY id ASC  RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "11.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count",
            "count",
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "10.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "rows_produced_per_join": 10,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.00",
          "prefix_cost": "1.25",
          "data_read_per_join": "160"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,count(`test`.`t1`.`id`) OVER `w` AS `count(id) over w`,count(0) OVER `w` AS `count(*) over w`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING) 
EXPLAIN FORMAT=JSON SELECT id, count(id) over w, count(*) over w, FIRST_VALUE(id) OVER w FROM t1
WINDOW w AS (ORDER BY id DESC RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "11.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id` desc"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count",
            "count",
            "first_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "10.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "rows_produced_per_join": 10,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.00",
          "prefix_cost": "1.25",
          "data_read_per_join": "160"
        },
        "used_columns": [
          "id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,count(`test`.`t1`.`id`) OVER `w` AS `count(id) over w`,count(0) OVER `w` AS `count(*) over w`,first_value(`test`.`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` desc RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING) 
CREATE TABLE ss(c CHAR(1));
INSERT INTO ss VALUES ('M');
ANALYZE TABLE ss;
Table	Op	Msg_type	Msg_text
test.ss	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT sex, NTILE(2) OVER w, SUM(ASCII(sex)) OVER w s FROM t1
HAVING sex=(SELECT c FROM ss LIMIT 1)
WINDOW w AS (ORDER BY id ROWS UNBOUNDED PRECEDING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "11.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "ntile",
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "10.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "rows_produced_per_join": 10,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.00",
          "prefix_cost": "1.25",
          "data_read_per_join": "160"
        },
        "used_columns": [
          "id",
          "sex"
        ]
      },
      "having_subqueries": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.35"
            },
            "table": {
              "table_name": "ss",
              "access_type": "ALL",
              "rows_examined_per_scan": 1,
              "rows_produced_per_join": 1,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.10",
                "prefix_cost": "0.35",
                "data_read_per_join": "8"
              },
              "used_columns": [
                "c"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	3599	Window function 'ntile' ignores the frame clause of window 'w' and aggregates over the whole partition
Note	1003	/* select#1 */ select `test`.`t1`.`sex` AS `sex`,ntile(2) OVER `w` AS `NTILE(2) OVER w`,sum(ascii(`test`.`t1`.`sex`)) OVER `w` AS `s` from `test`.`t1` having (`test`.`t1`.`sex` = (/* select#2 */ select `test`.`ss`.`c` from `test`.`ss` limit 1)) window `w` AS (ORDER BY `test`.`t1`.`id` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) 
EXPLAIN FORMAT=JSON SELECT sex, AVG(id), ROW_NUMBER() OVER w FROM t1
GROUP BY sex HAVING sex='M' OR sex IS NULL
WINDOW w AS (ORDER BY AVG(id)) ORDER BY sex DESC;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "21.25"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "10.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "w",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "avg(`id`)"
            ],
            "functions": [
              "row_number"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "10.00"
        },
        "grouping_operation": {
          "using_temporary_table": true,
          "using_filesort": false,
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 10,
            "rows_produced_per_join": 10,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "1.00",
              "prefix_cost": "1.25",
              "data_read_per_join": "160"
            },
            "used_columns": [
              "id",
              "sex"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`sex` AS `sex`,avg(`test`.`t1`.`id`) AS `AVG(id)`,row_number() OVER `w` AS `ROW_NUMBER() OVER w` from `test`.`t1` group by `test`.`t1`.`sex` desc having ((`test`.`t1`.`sex` = 'M') or (`test`.`t1`.`sex` is null)) window `w` AS (ORDER BY avg(`test`.`t1`.`id`) )  order by `test`.`t1`.`sex` desc
DROP TABLE t, t1, t11, t2, t22, t3, t5, t6, tj, ss;
----------------------------------------------------------------------
-    ORDER BY + RANK with more than one ordering expression
----------------------------------------------------------------------
CREATE TABLE t(i INT, j INT, k INT);
INSERT INTO t VALUES (1,1,1),
(1,1,2),
(1,1,2),
(1,2,1),
(1,2,2),
(2,1,1),
(2,1,1),
(2,1,2),
(2,2,1),
(2,2,2);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT *, RANK() OVER (ORDER BY i,j,k) AS O_IJK,
RANK() OVER (ORDER BY j) AS O_J,
RANK() OVER (ORDER BY k,j) AS O_KJ FROM t ORDER BY i,j,k;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "41.25"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "10.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "definition_position": 1,
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`i`",
              "`j`",
              "`k`"
            ],
            "functions": [
              "rank"
            ]
          },
          {
            "name": "<unnamed window>",
            "definition_position": 2,
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`j`"
            ],
            "functions": [
              "rank"
            ]
          },
          {
            "name": "<unnamed window>",
            "definition_position": 3,
            "last_executed_window": true,
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`k`",
              "`j`"
            ],
            "functions": [
              "rank"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "30.00"
        },
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 10,
          "rows_produced_per_join": 10,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "1.00",
            "prefix_cost": "1.25",
            "data_read_per_join": "160"
          },
          "used_columns": [
            "i",
            "j",
            "k"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,`test`.`t`.`k` AS `k`,rank() OVER (ORDER BY `test`.`t`.`i`,`test`.`t`.`j`,`test`.`t`.`k` )  AS `O_IJK`,rank() OVER (ORDER BY `test`.`t`.`j` )  AS `O_J`,rank() OVER (ORDER BY `test`.`t`.`k`,`test`.`t`.`j` )  AS `O_KJ` from `test`.`t` order by `test`.`t`.`i`,`test`.`t`.`j`,`test`.`t`.`k`
DROP TABLE t;
----------------------------------------------------------------------
-    Gulutzan's sanity tests in
-    http://ocelot.ca/blog/blog/2016/04/18/mariadb-10-2-window-functions/
-    His comments are quoted.
----------------------------------------------------------------------
CREATE TABLE t1 (s1 INT, s2 CHAR(5));
INSERT INTO t1 VALUES (1, 'a');
INSERT INTO t1 VALUES (NULL, NULL);
INSERT INTO t1 VALUES (1, NULL);
INSERT INTO t1 VALUES (NULL, 'a');
INSERT INTO t1 VALUES (2, 'b');
INSERT INTO t1 VALUES (-1, '');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
"The following statements all cause the MariaDB server to crash"
MySQL doesn't crash
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER ();
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "No tables used"
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER ()  AS `ROW_NUMBER() OVER ()`
EXPLAIN FORMAT=JSON SELECT *, ABS(ROW_NUMBER() OVER (ORDER BY s1,s2))
- ROW_NUMBER() OVER (ORDER BY s1,s2) AS X FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`s1`",
            "`s2`"
          ],
          "functions": [
            "row_number"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "functions": [
            "row_number"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "s1",
          "s2"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`s1` AS `s1`,`test`.`t1`.`s2` AS `s2`,(abs(row_number() OVER (ORDER BY `test`.`t1`.`s1`,`test`.`t1`.`s2` ) ) - row_number() OVER (ORDER BY `test`.`t1`.`s1`,`test`.`t1`.`s2` ) ) AS `X` from `test`.`t1`
EXPLAIN FORMAT=JSON SELECT RANK() OVER (ORDER BY AVG(s1)) FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.85"
    },
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 6,
      "rows_produced_per_join": 6,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.60",
        "prefix_cost": "0.85",
        "data_read_per_join": "192"
      },
      "used_columns": [
        "s1"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select rank() OVER (ORDER BY avg(`test`.`t1`.`s1`) )  AS `RANK() OVER (ORDER BY AVG(s1))` from `test`.`t1`
"The following statements all give the wrong answers with MariaDB"
Correct with MySQL.
EXPLAIN FORMAT=JSON SELECT COUNT(*) OVER (ORDER BY s2) FROM t1 WHERE s2 IS NULL;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`s2`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "1.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 1,
        "filtered": "16.67",
        "cost_info": {
          "read_cost": "0.75",
          "eval_cost": "0.10",
          "prefix_cost": "0.85",
          "data_read_per_join": "32"
        },
        "used_columns": [
          "s2"
        ],
        "attached_condition": "(`test`.`t1`.`s2` is null)"
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select count(0) OVER (ORDER BY `test`.`t1`.`s2` )  AS `COUNT(*) OVER (ORDER BY s2)` from `test`.`t1` where (`test`.`t1`.`s2` is null)
EXPLAIN FORMAT=JSON SELECT * FROM (
SELECT *,DENSE_RANK() OVER (ORDER BY s2 DESC),
DENSE_RANK() OVER (ORDER BY s2) FROM t1
) alias ORDER BY s1,s2;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "9.17"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "alias",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "2.58",
          "eval_cost": "0.60",
          "prefix_cost": "3.18",
          "data_read_per_join": "288"
        },
        "used_columns": [
          "s1",
          "s2",
          "DENSE_RANK() OVER (ORDER BY s2 DESC)",
          "DENSE_RANK() OVER (ORDER BY s2)"
        ],
        "materialized_from_subquery": {
          "using_temporary_table": true,
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "12.85"
            },
            "windowing": {
              "windows": [
                {
                  "name": "<unnamed window>",
                  "definition_position": 1,
                  "using_temporary_table": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`s2` desc"
                  ],
                  "functions": [
                    "dense_rank"
                  ]
                },
                {
                  "name": "<unnamed window>",
                  "definition_position": 2,
                  "last_executed_window": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`s2`"
                  ],
                  "functions": [
                    "dense_rank"
                  ]
                }
              ],
              "cost_info": {
                "sort_cost": "12.00"
              },
              "table": {
                "table_name": "t1",
                "access_type": "ALL",
                "rows_examined_per_scan": 6,
                "rows_produced_per_join": 6,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.60",
                  "prefix_cost": "0.85",
                  "data_read_per_join": "192"
                },
                "used_columns": [
                  "s1",
                  "s2"
                ]
              }
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `alias`.`s1` AS `s1`,`alias`.`s2` AS `s2`,`alias`.`DENSE_RANK() OVER (ORDER BY s2 DESC)` AS `DENSE_RANK() OVER (ORDER BY s2 DESC)`,`alias`.`DENSE_RANK() OVER (ORDER BY s2)` AS `DENSE_RANK() OVER (ORDER BY s2)` from (/* select#2 */ select `test`.`t1`.`s1` AS `s1`,`test`.`t1`.`s2` AS `s2`,dense_rank() OVER (ORDER BY `test`.`t1`.`s2` desc )  AS `DENSE_RANK() OVER (ORDER BY s2 DESC)`,dense_rank() OVER (ORDER BY `test`.`t1`.`s2` )  AS `DENSE_RANK() OVER (ORDER BY s2)` from `test`.`t1`) `alias` order by `alias`.`s1`,`alias`.`s2`
EXPLAIN FORMAT=JSON SELECT * FROM (
SELECT *, SUM(s1) OVER (ORDER BY s1) FROM t1 ORDER BY s1
) alias ORDER BY s1,s2;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "9.17"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "alias",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "2.58",
          "eval_cost": "0.60",
          "prefix_cost": "3.18",
          "data_read_per_join": "288"
        },
        "used_columns": [
          "s1",
          "s2",
          "SUM(s1) OVER (ORDER BY s1)"
        ],
        "materialized_from_subquery": {
          "using_temporary_table": true,
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "12.85"
            },
            "ordering_operation": {
              "using_filesort": true,
              "cost_info": {
                "sort_cost": "6.00"
              },
              "windowing": {
                "windows": [
                  {
                    "name": "<unnamed window>",
                    "using_temporary_table": true,
                    "using_filesort": true,
                    "filesort_key": [
                      "`s1`"
                    ],
                    "frame_buffer": {
                      "using_temporary_table": true,
                      "optimized_frame_evaluation": true
                    },
                    "functions": [
                      "sum"
                    ]
                  }
                ],
                "cost_info": {
                  "sort_cost": "6.00"
                },
                "table": {
                  "table_name": "t1",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 6,
                  "rows_produced_per_join": 6,
                  "filtered": "100.00",
                  "cost_info": {
                    "read_cost": "0.25",
                    "eval_cost": "0.60",
                    "prefix_cost": "0.85",
                    "data_read_per_join": "192"
                  },
                  "used_columns": [
                    "s1",
                    "s2"
                  ]
                }
              }
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `alias`.`s1` AS `s1`,`alias`.`s2` AS `s2`,`alias`.`SUM(s1) OVER (ORDER BY s1)` AS `SUM(s1) OVER (ORDER BY s1)` from (/* select#2 */ select `test`.`t1`.`s1` AS `s1`,`test`.`t1`.`s2` AS `s2`,sum(`test`.`t1`.`s1`) OVER (ORDER BY `test`.`t1`.`s1` )  AS `SUM(s1) OVER (ORDER BY s1)` from `test`.`t1` order by `test`.`t1`.`s1`) `alias` order by `alias`.`s1`,`alias`.`s2`
EXPLAIN FORMAT=JSON SELECT AVG(s1), RANK() OVER (ORDER BY s1) FROM t1;
ERROR 42000: In aggregated query without GROUP BY, expression #1 of PARTITION BY or ORDER BY clause of window '<unnamed window>' contains nonaggregated column 'test.t1.s1'; this is incompatible with sql_mode=only_full_group_by
"The following statement causes the client to hang (it loops in
mysql_store_result, I think this is the first time I've seen this type of
error)"
No issue with MySQL
EXPLAIN FORMAT=JSON SELECT *, AVG(s1) OVER () FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg"
          ]
        }
      ],
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "s1",
          "s2"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`s1` AS `s1`,`test`.`t1`.`s2` AS `s2`,avg(`test`.`t1`.`s1`) OVER ()  AS `AVG(s1) OVER ()` from `test`.`t1`
EXPLAIN FORMAT=JSON SELECT *, AVG(s1) OVER (ROWS UNBOUNDED PRECEDING) FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "avg"
          ]
        }
      ],
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "192"
        },
        "used_columns": [
          "s1",
          "s2"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`s1` AS `s1`,`test`.`t1`.`s2` AS `s2`,avg(`test`.`t1`.`s1`) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `AVG(s1) OVER (ROWS UNBOUNDED PRECEDING)` from `test`.`t1`
DROP TABLE t1;
Some negative tests (from Srikanth)
CREATE TABLE t (a INT, b INT, c INT);
INSERT INTO t VALUES (1,1,1), (1,1,2), (1,1,3),
(1,2,1), (1,2,2), (1,2,3),
(1,3,1), (1,3,2), (1,3,3),
(2,1,1), (2,1,2), (2,1,3),
(2,2,1), (2,2,2), (2,2,3),
(2,3,1), (2,3,2), (2,3,3);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
Wfs OK in ORDER BY, but not in WHERE or HAVING clauses
EXPLAIN FORMAT=JSON SELECT * FROM t ORDER BY RANK() OVER (ORDER BY a DESC,b,c);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "38.05"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "18.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`a` desc",
              "`b`",
              "`c`"
            ],
            "functions": [
              "rank"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "18.00"
        },
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 18,
          "rows_produced_per_join": 18,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "1.80",
            "prefix_cost": "2.05",
            "data_read_per_join": "288"
          },
          "used_columns": [
            "a",
            "b",
            "c"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`a` AS `a`,`test`.`t`.`b` AS `b`,`test`.`t`.`c` AS `c` from `test`.`t` order by rank() OVER (ORDER BY `test`.`t`.`a` desc,`test`.`t`.`b`,`test`.`t`.`c` ) 
EXPLAIN FORMAT=JSON SELECT *, RANK() OVER (ORDER BY a DESC,b,c) AS `rank` FROM t ORDER BY `rank`;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "38.05"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "18.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`a` desc",
              "`b`",
              "`c`"
            ],
            "functions": [
              "rank"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "18.00"
        },
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 18,
          "rows_produced_per_join": 18,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "1.80",
            "prefix_cost": "2.05",
            "data_read_per_join": "288"
          },
          "used_columns": [
            "a",
            "b",
            "c"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`a` AS `a`,`test`.`t`.`b` AS `b`,`test`.`t`.`c` AS `c`,rank() OVER (ORDER BY `test`.`t`.`a` desc,`test`.`t`.`b`,`test`.`t`.`c` )  AS `rank` from `test`.`t` order by `rank`
Windows should only be allowed in order by of a simple table query
This is legal, though:
(select a from t) union (select a from t order by (row_number() over ()));
a
1
2
Non constants as frame bounds
Non-unique window name
Illegal legacy position indication in window's ORDER BY clause
EXPLAIN FORMAT=JSON SELECT * FROM (
SELECT a,b,c, RANK() OVER (ORDER BY 1*1) FROM t
) alias ORDER BY a,b,c;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "22.52"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "18.00"
      },
      "table": {
        "table_name": "alias",
        "access_type": "ALL",
        "rows_examined_per_scan": 18,
        "rows_produced_per_join": 18,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "2.73",
          "eval_cost": "1.80",
          "prefix_cost": "4.53",
          "data_read_per_join": "576"
        },
        "used_columns": [
          "a",
          "b",
          "c",
          "RANK() OVER (ORDER BY 1*1)"
        ],
        "materialized_from_subquery": {
          "using_temporary_table": true,
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "20.05"
            },
            "windowing": {
              "windows": [
                {
                  "name": "<unnamed window>",
                  "using_filesort": true,
                  "filesort_key": [
                    "(1 * 1)"
                  ],
                  "functions": [
                    "rank"
                  ]
                }
              ],
              "cost_info": {
                "sort_cost": "18.00"
              },
              "table": {
                "table_name": "t",
                "access_type": "ALL",
                "rows_examined_per_scan": 18,
                "rows_produced_per_join": 18,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "1.80",
                  "prefix_cost": "2.05",
                  "data_read_per_join": "288"
                },
                "used_columns": [
                  "a",
                  "b",
                  "c"
                ]
              }
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `alias`.`a` AS `a`,`alias`.`b` AS `b`,`alias`.`c` AS `c`,`alias`.`RANK() OVER (ORDER BY 1*1)` AS `RANK() OVER (ORDER BY 1*1)` from (/* select#2 */ select `test`.`t`.`a` AS `a`,`test`.`t`.`b` AS `b`,`test`.`t`.`c` AS `c`,rank() OVER (ORDER BY (1 * 1) )  AS `RANK() OVER (ORDER BY 1*1)` from `test`.`t`) `alias` order by `alias`.`a`,`alias`.`b`,`alias`.`c`
Crashed: more than one window in subquery
EXPLAIN FORMAT=JSON SELECT * FROM (SELECT count(*) OVER (), sum(c) OVER () AS sum1, a from t) as alias;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.52"
    },
    "table": {
      "table_name": "alias",
      "access_type": "ALL",
      "rows_examined_per_scan": 18,
      "rows_produced_per_join": 18,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "2.73",
        "eval_cost": "1.80",
        "prefix_cost": "4.53",
        "data_read_per_join": "720"
      },
      "used_columns": [
        "count(*) OVER ()",
        "sum1",
        "a"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "2.05"
          },
          "windowing": {
            "windows": [
              {
                "name": "<unnamed window>",
                "definition_position": 1,
                "using_temporary_table": true,
                "frame_buffer": {
                  "using_temporary_table": true,
                  "optimized_frame_evaluation": true
                },
                "functions": [
                  "count"
                ]
              },
              {
                "name": "<unnamed window>",
                "definition_position": 2,
                "last_executed_window": true,
                "frame_buffer": {
                  "using_temporary_table": true,
                  "optimized_frame_evaluation": true
                },
                "functions": [
                  "sum"
                ]
              }
            ],
            "table": {
              "table_name": "t",
              "access_type": "ALL",
              "rows_examined_per_scan": 18,
              "rows_produced_per_join": 18,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "1.80",
                "prefix_cost": "2.05",
                "data_read_per_join": "288"
              },
              "used_columns": [
                "a",
                "c"
              ]
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `alias`.`count(*) OVER ()` AS `count(*) OVER ()`,`alias`.`sum1` AS `sum1`,`alias`.`a` AS `a` from (/* select#2 */ select count(0) OVER ()  AS `count(*) OVER ()`,sum(`test`.`t`.`c`) OVER ()  AS `sum1`,`test`.`t`.`a` AS `a` from `test`.`t`) `alias`
Crashed: expression containing window function(s) in subquery
EXPLAIN FORMAT=JSON SELECT * FROM (SELECT count(*) OVER () + sum(c) OVER () AS sum1, a from t) as alias;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.52"
    },
    "table": {
      "table_name": "alias",
      "access_type": "ALL",
      "rows_examined_per_scan": 18,
      "rows_produced_per_join": 18,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "2.73",
        "eval_cost": "1.80",
        "prefix_cost": "4.53",
        "data_read_per_join": "576"
      },
      "used_columns": [
        "sum1",
        "a"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "2.05"
          },
          "windowing": {
            "windows": [
              {
                "name": "<unnamed window>",
                "definition_position": 1,
                "using_temporary_table": true,
                "frame_buffer": {
                  "using_temporary_table": true,
                  "optimized_frame_evaluation": true
                },
                "functions": [
                  "count"
                ]
              },
              {
                "name": "<unnamed window>",
                "definition_position": 2,
                "last_executed_window": true,
                "frame_buffer": {
                  "using_temporary_table": true,
                  "optimized_frame_evaluation": true
                },
                "functions": [
                  "sum"
                ]
              }
            ],
            "table": {
              "table_name": "t",
              "access_type": "ALL",
              "rows_examined_per_scan": 18,
              "rows_produced_per_join": 18,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "1.80",
                "prefix_cost": "2.05",
                "data_read_per_join": "288"
              },
              "used_columns": [
                "a",
                "c"
              ]
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `alias`.`sum1` AS `sum1`,`alias`.`a` AS `a` from (/* select#2 */ select (```count(0) OVER () ``` + sum(`test`.`t`.`c`) OVER () ) AS `sum1`,`test`.`t`.`a` AS `a` from `test`.`t`) `alias`
Wrong result if subquery window function referenced another column in the select list
This was OK, but:
EXPLAIN FORMAT=JSON SELECT * FROM (SELECT SUM(b) OVER (), a FROM t) AS alias;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.52"
    },
    "table": {
      "table_name": "alias",
      "access_type": "ALL",
      "rows_examined_per_scan": 18,
      "rows_produced_per_join": 18,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "2.73",
        "eval_cost": "1.80",
        "prefix_cost": "4.53",
        "data_read_per_join": "576"
      },
      "used_columns": [
        "SUM(b) OVER ()",
        "a"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "2.05"
          },
          "windowing": {
            "windows": [
              {
                "name": "<unnamed window>",
                "frame_buffer": {
                  "using_temporary_table": true,
                  "optimized_frame_evaluation": true
                },
                "functions": [
                  "sum"
                ]
              }
            ],
            "table": {
              "table_name": "t",
              "access_type": "ALL",
              "rows_examined_per_scan": 18,
              "rows_produced_per_join": 18,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "1.80",
                "prefix_cost": "2.05",
                "data_read_per_join": "288"
              },
              "used_columns": [
                "a",
                "b"
              ]
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `alias`.`SUM(b) OVER ()` AS `SUM(b) OVER ()`,`alias`.`a` AS `a` from (/* select#2 */ select sum(`test`.`t`.`b`) OVER ()  AS `SUM(b) OVER ()`,`test`.`t`.`a` AS `a` from `test`.`t`) `alias`
this one failed with NULL as sum
EXPLAIN FORMAT=JSON SELECT * FROM (SELECT SUM(b) OVER (), b FROM t) AS alias;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.52"
    },
    "table": {
      "table_name": "alias",
      "access_type": "ALL",
      "rows_examined_per_scan": 18,
      "rows_produced_per_join": 18,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "2.73",
        "eval_cost": "1.80",
        "prefix_cost": "4.53",
        "data_read_per_join": "576"
      },
      "used_columns": [
        "SUM(b) OVER ()",
        "b"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "2.05"
          },
          "windowing": {
            "windows": [
              {
                "name": "<unnamed window>",
                "frame_buffer": {
                  "using_temporary_table": true,
                  "optimized_frame_evaluation": true
                },
                "functions": [
                  "sum"
                ]
              }
            ],
            "table": {
              "table_name": "t",
              "access_type": "ALL",
              "rows_examined_per_scan": 18,
              "rows_produced_per_join": 18,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "1.80",
                "prefix_cost": "2.05",
                "data_read_per_join": "288"
              },
              "used_columns": [
                "b"
              ]
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `alias`.`SUM(b) OVER ()` AS `SUM(b) OVER ()`,`alias`.`b` AS `b` from (/* select#2 */ select sum(`test`.`t`.`b`) OVER ()  AS `SUM(b) OVER ()`,`test`.`t`.`b` AS `b` from `test`.`t`) `alias`
Window function having subquery in argument:
CREATE TABLE u(d INT);
SELECT AVG(a * (SELECT a*d FROM u)) OVER
(PARTITION BY (SELECT a+d FROM u) ORDER BY (SELECT d FROM u)) FROM t;
AVG(a * (SELECT a*d FROM u)) OVER
(PARTITION BY (SELECT a+d FROM u) ORDER BY (SELECT d FROM u))
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
EXPLAIN FORMAT=JSON SELECT AVG(a * (SELECT a*d FROM u)) OVER
(PARTITION BY (SELECT a+d FROM u) ORDER BY (SELECT d FROM u)) FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "20.05"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "(select (`a` + `d`) from `u`)",
            "(select `d` from `u`)"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "18.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 18,
        "rows_produced_per_join": 18,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.80",
          "prefix_cost": "2.05",
          "data_read_per_join": "288"
        },
        "used_columns": [
          "a"
        ]
      },
      "select_list_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 4,
            "cost_info": {
              "query_cost": "0.35"
            },
            "table": {
              "table_name": "u",
              "access_type": "ALL",
              "rows_examined_per_scan": 1,
              "rows_produced_per_join": 1,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.10",
                "prefix_cost": "0.35",
                "data_read_per_join": "8"
              },
              "used_columns": [
                "d"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 3,
            "cost_info": {
              "query_cost": "0.35"
            },
            "table": {
              "table_name": "u",
              "access_type": "ALL",
              "rows_examined_per_scan": 1,
              "rows_produced_per_join": 1,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.10",
                "prefix_cost": "0.35",
                "data_read_per_join": "8"
              },
              "used_columns": [
                "d"
              ]
            }
          }
        },
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.35"
            },
            "table": {
              "table_name": "u",
              "access_type": "ALL",
              "rows_examined_per_scan": 1,
              "rows_produced_per_join": 1,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.10",
                "prefix_cost": "0.35",
                "data_read_per_join": "8"
              },
              "used_columns": [
                "d"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1276	Field or reference 'test.t.a' of SELECT #4 was resolved in SELECT #1
Note	1276	Field or reference 'test.t.a' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select avg((`test`.`t`.`a` * (/* select#4 */ select (`test`.`t`.`a` * `test`.`u`.`d`) from `test`.`u`))) OVER (PARTITION BY (/* select#2 */ select (`test`.`t`.`a` + `test`.`u`.`d`) from `test`.`u`) ORDER BY (/* select#3 */ select `test`.`u`.`d` from `test`.`u`) )  AS `AVG(a * (SELECT a*d FROM u)) OVER
(PARTITION BY (SELECT a+d FROM u) ORDER BY (SELECT d FROM u))` from `test`.`t`
DROP TABLE u;
Crash due to unguarded access for window name string for an unnamed
window while producing the error message
Check that DISTINCT is not allowed in wfs
Check that GROUPS bounds unit is not supported yet
Check that EXCLUDE in frames is not supported yet
Check Nested wfs
DROP TABLE t;
Crash report (Srikanth)
CREATE TABLE t(a int, b int);
INSERT INTO t VALUES (1,1),(2,1),(3,2),(4,2),(5,3),(6,3);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT SUM(a) OVER (ORDER BY b) FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`b`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "96"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sum(`test`.`t`.`a`) OVER (ORDER BY `test`.`t`.`b` )  AS `SUM(a) OVER (ORDER BY b)` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT COUNT(*) OVER (ORDER BY b) FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`b`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "96"
        },
        "used_columns": [
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select count(0) OVER (ORDER BY `test`.`t`.`b` )  AS `COUNT(*) OVER (ORDER BY b)` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT AVG(b) OVER (ORDER BY b) FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`b`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "96"
        },
        "used_columns": [
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select avg(`test`.`t`.`b`) OVER (ORDER BY `test`.`t`.`b` )  AS `AVG(b) OVER (ORDER BY b)` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT a,b,LAST_VALUE(a) OVER (ORDER BY b,a) FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`b`",
            "`a`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "last_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "96"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`a` AS `a`,`test`.`t`.`b` AS `b`,last_value(`test`.`t`.`a`) OVER (ORDER BY `test`.`t`.`b`,`test`.`t`.`a` )  AS `LAST_VALUE(a) OVER (ORDER BY b,a)` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT NTILE(2) OVER (ORDER BY b) FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`b`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "ntile"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "96"
        },
        "used_columns": [
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select ntile(2) OVER (ORDER BY `test`.`t`.`b` )  AS `NTILE(2) OVER (ORDER BY b)` from `test`.`t`
DROP TABLE t;
Wrong result (Srikanth)
CREATE TABLE t1(a INT, b INT);
INSERT INTO t1 VALUES (1,2),
(1,3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT a, b, COUNT(a) OVER w count,
SUM(a) OVER w sum,
AVG(a) over w average,
LAST_VALUE(a) OVER w lastval FROM t1
WINDOW w as (PARTITION BY a ORDER BY b ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.45"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`a`",
            "`b`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count",
            "sum",
            "avg",
            "last_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "2.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 2,
        "rows_produced_per_join": 2,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.20",
          "prefix_cost": "0.45",
          "data_read_per_join": "32"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,count(`test`.`t1`.`a`) OVER `w` AS `count`,sum(`test`.`t1`.`a`) OVER `w` AS `sum`,avg(`test`.`t1`.`a`) OVER `w` AS `average`,last_value(`test`.`t1`.`a`) OVER `w` AS `lastval` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`a` ORDER BY `test`.`t1`.`b` ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING) 
INSERT INTO t1 VALUES (1,3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT a, b, COUNT(a) OVER w count,
SUM(a) OVER w sum,
AVG(a) OVER w average,
LAST_VALUE(a) OVER w lastval FROM t1
WINDOW w as (PARTITION BY a ORDER BY b ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.55"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`a`",
            "`b`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count",
            "sum",
            "avg",
            "last_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "3.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 3,
        "rows_produced_per_join": 3,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.30",
          "prefix_cost": "0.55",
          "data_read_per_join": "48"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,count(`test`.`t1`.`a`) OVER `w` AS `count`,sum(`test`.`t1`.`a`) OVER `w` AS `sum`,avg(`test`.`t1`.`a`) OVER `w` AS `average`,last_value(`test`.`t1`.`a`) OVER `w` AS `lastval` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`a` ORDER BY `test`.`t1`.`b` ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING) 
EXPLAIN FORMAT=JSON SELECT a, b, COUNT(a) OVER w count,
SUM(a) OVER w sum,
AVG(a) OVER w average,
LAST_VALUE(a) OVER w lastval FROM t1
WINDOW w as (PARTITION BY a ORDER BY b ROWS BETWEEN 1 PRECEDING AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.55"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`a`",
            "`b`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "count",
            "sum",
            "avg",
            "last_value"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "3.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 3,
        "rows_produced_per_join": 3,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.30",
          "prefix_cost": "0.55",
          "data_read_per_join": "48"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,count(`test`.`t1`.`a`) OVER `w` AS `count`,sum(`test`.`t1`.`a`) OVER `w` AS `sum`,avg(`test`.`t1`.`a`) OVER `w` AS `average`,last_value(`test`.`t1`.`a`) OVER `w` AS `lastval` from `test`.`t1` window `w` AS (PARTITION BY `test`.`t1`.`a` ORDER BY `test`.`t1`.`b` ROWS BETWEEN 1 PRECEDING AND 2 FOLLOWING) 
DROP TABLE t1;
frame buffer navigation assert
CREATE TABLE ta (a INT(11) DEFAULT NULL, b INT(11) DEFAULT NULL);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO  ta VALUES (1,1), (1,2), (1,3), (2,1), (2,2), (2,3);
ANALYZE TABLE ta;
Table	Op	Msg_type	Msg_text
test.ta	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT last_value(b) OVER (ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) FROM ta;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "last_value"
          ]
        }
      ],
      "table": {
        "table_name": "ta",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "96"
        },
        "used_columns": [
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select last_value(`test`.`ta`.`b`) OVER (ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING)  AS `last_value(b) OVER (ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING)` from `test`.`ta`
DROP TABLE ta;
Nullability fix bug for COUNT OVER in non optimized eval strategy
CREATE TABLE t(d DOUBLE);
INSERT INTO t VALUES (1.0), (2.0), (3.0);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT SUM(d) OVER w, COUNT(*) OVER w FROM t WINDOW W AS (ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.55"
    },
    "windowing": {
      "windows": [
        {
          "name": "W",
          "frame_buffer": {
            "using_temporary_table": true
          },
          "functions": [
            "sum",
            "count"
          ]
        }
      ],
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 3,
        "rows_produced_per_join": 3,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.30",
          "prefix_cost": "0.55",
          "data_read_per_join": "48"
        },
        "used_columns": [
          "d"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sum(`test`.`t`.`d`) OVER `W` AS `SUM(d) OVER w`,count(0) OVER `W` AS `COUNT(*) OVER w` from `test`.`t` window `W` AS (ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING) 
DROP TABLE t;
Bug in inverse logic with initial NULL and RANGE BETWEEN N FOLLOWING AND M FOLLOWING
CREATE TABLE t1 (d DOUBLE, id INT, sex CHAR(1), n INT NOT NULL AUTO_INCREMENT, PRIMARY KEY(n));
INSERT INTO t1(d, id, sex) VALUES (1.0, 1, 'M'),
(2.0, 2, 'F'),
(3.0, 3, 'F'),
(4.0, 4, 'F'),
(5.0, 5, 'M'),
(NULL, NULL, 'M'),
(10.0, 10, NULL),
(10.0, 10, NULL),
(11.0, 11, NULL);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT id, AVG(id) over w `avg`, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt FROM t1 WINDOW w as (ORDER BY id RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg",
            "sum",
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "216"
        },
        "used_columns": [
          "id",
          "n"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,avg(`test`.`t1`.`id`) OVER `w` AS `avg`,sum(`test`.`t1`.`id`) OVER `w` AS `sum`,count(0) OVER `w` AS `cnt` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`id` RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING) 
SET windowing_use_high_precision= OFF;
EXPLAIN FORMAT=JSON SELECT d, AVG(d) over w `avg`, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt FROM t1 WINDOW w as (ORDER BY d RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.15"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`d`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "avg",
            "sum",
            "count"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "9.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.90",
          "prefix_cost": "1.15",
          "data_read_per_join": "216"
        },
        "used_columns": [
          "d",
          "n"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`d` AS `d`,avg(`test`.`t1`.`d`) OVER `w` AS `avg`,sum(`test`.`t1`.`d`) OVER `w` AS `sum`,count(0) OVER `w` AS `cnt` from `test`.`t1` window `w` AS (ORDER BY `test`.`t1`.`d` RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING) 
SET windowing_use_high_precision= ON;
DROP TABLE t1;
Bug in inverse logic with e.g. ROWS BETWEEN UNBOUNDED PRECEDING AND 1
FOLLOWING: at end of partition, when no rows are removed or added we
lacked initialization of aggregates in optimized mode.
CREATE TABLE t (i char(10), j int);
INSERT INTO t VALUES('A', 1);
INSERT INTO t VALUES('A', 3);
INSERT INTO t VALUES('A', 5);
INSERT INTO t VALUES('B', 1);
INSERT INTO t VALUES('B', 7);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT i, j, SUM(j) OVER w FROM t
WINDOW w AS (PARTITION BY i ORDER BY j
ROWS BETWEEN UNBOUNDED PRECEDING AND 1 FOLLOWING);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "5.75"
    },
    "windowing": {
      "windows": [
        {
          "name": "w",
          "using_filesort": true,
          "filesort_key": [
            "`i`",
            "`j`"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "5.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 5,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.50",
          "prefix_cost": "0.75",
          "data_read_per_join": "240"
        },
        "used_columns": [
          "i",
          "j"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum(`test`.`t`.`j`) OVER `w` AS `SUM(j) OVER w` from `test`.`t` window `w` AS (PARTITION BY `test`.`t`.`i` ORDER BY `test`.`t`.`j` ROWS BETWEEN UNBOUNDED PRECEDING AND 1 FOLLOWING) 
Test definition_position: references don't count, only definitions,
thus we have an unnamed, then another unnamed (based on w but not
exactly w) then w then two unnamed.
EXPLAIN FORMAT=JSON SELECT SUM(j) OVER w, COUNT(j) OVER (),
AVG(j) OVER (w ORDER BY j), FIRST_VALUE(j) OVER w
FROM t WINDOW w AS (PARTITION BY i)
ORDER BY LAST_VALUE(j) OVER w, NTH_VALUE(j,1) OVER (),
ROW_NUMBER() OVER (PARTITION BY j);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "20.75"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "5.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "w",
            "definition_position": 3,
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`i`"
            ],
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "sum",
              "first_value",
              "last_value"
            ]
          },
          {
            "name": "<unnamed window>",
            "definition_position": 1,
            "using_temporary_table": true,
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "count"
            ]
          },
          {
            "name": "<unnamed window>",
            "definition_position": 2,
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`i`",
              "`j`"
            ],
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "avg"
            ]
          },
          {
            "name": "<unnamed window>",
            "definition_position": 4,
            "using_temporary_table": true,
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "nth_value"
            ]
          },
          {
            "name": "<unnamed window>",
            "definition_position": 5,
            "last_executed_window": true,
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`j`"
            ],
            "functions": [
              "row_number"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "15.00"
        },
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "240"
          },
          "used_columns": [
            "i",
            "j"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sum(`test`.`t`.`j`) OVER `w` AS `SUM(j) OVER w`,count(`test`.`t`.`j`) OVER ()  AS `COUNT(j) OVER ()`,avg(`test`.`t`.`j`) OVER (`w` ORDER BY `test`.`t`.`j` )  AS `AVG(j) OVER (w ORDER BY j)`,first_value(`test`.`t`.`j`) OVER `w` AS `FIRST_VALUE(j) OVER w` from `test`.`t` window `w` AS (PARTITION BY `test`.`t`.`i` )  order by last_value(`test`.`t`.`j`) OVER `w`,nth_value(`test`.`t`.`j`,1) OVER () ,row_number() OVER (PARTITION BY `test`.`t`.`j` ) 
DROP TABLE t;
#
# Bug#26114396 WL#9603: SIG11 AT OPT_EXPLAIN_JSON_NAMESPACE::WINDOW_CTX::FORMAT_BODY
#
CREATE TABLE t1(a int,b int);
CREATE TABLE t2(a int,b int);
INSERT INTO t1 VALUES (0,1);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
INSERT INTO t2 VALUES
(2,8),(81,0),(6,7),(8,1),(4,0),(0,2),(6,5),(5,4),(0,6),(9,3),
(5,0),(6,254),(6,0),(2,7),(8,73),(9,7),(3,5),(0,5),(7,75),(2,1);
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
EXPLAIN FORMAT=JSON
SELECT ROW_NUMBER() OVER () AS rn
FROM ( t1 LEFT JOIN t2 ON (t2.a <= t1 . a ) )
WHERE t1.a = 3
GROUP BY t1.a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.60"
    },
    "grouping_operation": {
      "using_filesort": false,
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 1,
            "rows_produced_per_join": 1,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.10",
              "prefix_cost": "0.35",
              "data_read_per_join": "16"
            },
            "used_columns": [
              "a"
            ],
            "attached_condition": "(`test`.`t1`.`a` = 3)"
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 20,
            "rows_produced_per_join": 20,
            "filtered": "100.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "2.00",
              "prefix_cost": "2.60",
              "data_read_per_join": "320"
            },
            "used_columns": [
              "a"
            ],
            "attached_condition": "<if>(is_not_null_compl(t2), (`test`.`t2`.`a` <= 3), true)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER ()  AS `rn` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`a` <= 3)) where (`test`.`t1`.`a` = 3) group by `test`.`t1`.`a`
DROP TABLE t1,t2;
#
# Printing the true number of "using temporary table"
#
CREATE TABLE t1(a INT, b INT);
INSERT INTO t1 VALUES(1, 1);
FLUSH STATUS;
SELECT t1.a, SUM(t2.b) OVER(ORDER BY t1.a) FROM t1, t1 AS t2 ORDER BY t2.a;
a	SUM(t2.b) OVER(ORDER BY t1.a)
1	1
SHOW STATUS LIKE 'Created_tmp_tables';
Variable_name	Value
Created_tmp_tables	2
EXPLAIN FORMAT=JSON SELECT t1.a, SUM(t2.b) OVER(ORDER BY t1.a) FROM t1, t1 AS t2 ORDER BY t2.a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.70"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "1.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`a`"
            ],
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "sum"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "1.00"
        },
        "buffer_result": {
          "using_temporary_table": true,
          "nested_loop": [
            {
              "table": {
                "table_name": "t1",
                "access_type": "ALL",
                "rows_examined_per_scan": 1,
                "rows_produced_per_join": 1,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.10",
                  "prefix_cost": "0.35",
                  "data_read_per_join": "16"
                },
                "used_columns": [
                  "a"
                ]
              }
            },
            {
              "table": {
                "table_name": "t2",
                "access_type": "ALL",
                "rows_examined_per_scan": 1,
                "rows_produced_per_join": 1,
                "filtered": "100.00",
                "using_join_buffer": "hash join",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.10",
                  "prefix_cost": "0.70",
                  "data_read_per_join": "16"
                },
                "used_columns": [
                  "a",
                  "b"
                ]
              }
            }
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,sum(`test`.`t2`.`b`) OVER (ORDER BY `test`.`t1`.`a` )  AS `SUM(t2.b) OVER(ORDER BY t1.a)` from `test`.`t1` join `test`.`t1` `t2` order by `test`.`t2`.`a`
DROP TABLE t1;
#
# Bug#26612356 WINDOW FUNCTIONS: FIX COST ESTIMATES
#
CREATE TABLE t(i INT);
INSERT INTO t VALUES
(2), (3), (1), (5), (8), (4), (6), (2), (10), (16), (4), (6), (2),
(10), (16), (8), (12), (4), (20), (32), (19), (29), (9), (49), (79),
(39), (59), (19), (99), (159), (39), (59), (19), (99), (159), (79),
(119), (39), (199), (319);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT * FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.25"
    },
    "table": {
      "table_name": "t",
      "access_type": "ALL",
      "rows_examined_per_scan": 40,
      "rows_produced_per_join": 40,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "4.00",
        "prefix_cost": "4.25",
        "data_read_per_join": "320"
      },
      "used_columns": [
        "i"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT * FROM t ORDER BY i;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "44.25"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "40.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 40,
        "rows_produced_per_join": 40,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "4.00",
          "prefix_cost": "4.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "i"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i` from `test`.`t` order by `test`.`t`.`i`
EXPLAIN FORMAT=JSON SELECT * FROM t GROUP BY i;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.25"
    },
    "grouping_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 40,
        "rows_produced_per_join": 40,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "4.00",
          "prefix_cost": "4.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "i"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i` from `test`.`t` group by `test`.`t`.`i`
EXPLAIN FORMAT=JSON SELECT * FROM t GROUP BY i ORDER BY i DESC;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "44.25"
    },
    "ordering_operation": {
      "using_filesort": false,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": true,
        "cost_info": {
          "sort_cost": "40.00"
        },
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 40,
          "rows_produced_per_join": 40,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "4.00",
            "prefix_cost": "4.25",
            "data_read_per_join": "320"
          },
          "used_columns": [
            "i"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i` from `test`.`t` group by `test`.`t`.`i` desc order by `test`.`t`.`i` desc
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER () FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "row_number"
          ]
        }
      ],
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 40,
        "rows_produced_per_join": 40,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "4.00",
          "prefix_cost": "4.25",
          "data_read_per_join": "320"
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER ()  AS `ROW_NUMBER() OVER ()` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT DISTINCT i FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.25"
    },
    "duplicates_removal": {
      "using_temporary_table": true,
      "using_filesort": false,
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 40,
        "rows_produced_per_join": 40,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "4.00",
          "prefix_cost": "4.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "i"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`t`.`i` AS `i` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT DISTINCT ROW_NUMBER() OVER () FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.25"
    },
    "duplicates_removal": {
      "using_filesort": false,
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "functions": [
              "row_number"
            ]
          }
        ],
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 40,
          "rows_produced_per_join": 40,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "4.00",
            "prefix_cost": "4.25",
            "data_read_per_join": "320"
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct row_number() OVER ()  AS `ROW_NUMBER() OVER ()` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER () FROM t ORDER BY i;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "44.25"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "40.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "functions": [
              "row_number"
            ]
          }
        ],
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 40,
          "rows_produced_per_join": 40,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "4.00",
            "prefix_cost": "4.25",
            "data_read_per_join": "320"
          },
          "used_columns": [
            "i"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER ()  AS `ROW_NUMBER() OVER ()` from `test`.`t` order by `test`.`t`.`i`
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER () FROM t GROUP BY i;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "row_number"
          ]
        }
      ],
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 40,
          "rows_produced_per_join": 40,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "4.00",
            "prefix_cost": "4.25",
            "data_read_per_join": "320"
          },
          "used_columns": [
            "i"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER ()  AS `ROW_NUMBER() OVER ()` from `test`.`t` group by `test`.`t`.`i`
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (PARTITION BY i) FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "44.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`i`"
          ],
          "functions": [
            "row_number"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "40.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 40,
        "rows_produced_per_join": 40,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "4.00",
          "prefix_cost": "4.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "i"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (PARTITION BY `test`.`t`.`i` )  AS `ROW_NUMBER() OVER (PARTITION BY i)` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT DISTINCT ROW_NUMBER() OVER (PARTITION BY i) FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "44.25"
    },
    "duplicates_removal": {
      "using_filesort": false,
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`i`"
            ],
            "functions": [
              "row_number"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "40.00"
        },
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 40,
          "rows_produced_per_join": 40,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "4.00",
            "prefix_cost": "4.25",
            "data_read_per_join": "320"
          },
          "used_columns": [
            "i"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct row_number() OVER (PARTITION BY `test`.`t`.`i` )  AS `ROW_NUMBER() OVER (PARTITION BY i)` from `test`.`t`
Final ORDER BY i could be eliminated
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (PARTITION BY i) FROM t ORDER BY i;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "84.25"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "40.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`i`"
            ],
            "functions": [
              "row_number"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "40.00"
        },
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 40,
          "rows_produced_per_join": 40,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "4.00",
            "prefix_cost": "4.25",
            "data_read_per_join": "320"
          },
          "used_columns": [
            "i"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (PARTITION BY `test`.`t`.`i` )  AS `ROW_NUMBER() OVER (PARTITION BY i)` from `test`.`t` order by `test`.`t`.`i`
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (PARTITION BY i) FROM t ORDER BY i DESC;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "84.25"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "40.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`i`"
            ],
            "functions": [
              "row_number"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "40.00"
        },
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 40,
          "rows_produced_per_join": 40,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "4.00",
            "prefix_cost": "4.25",
            "data_read_per_join": "320"
          },
          "used_columns": [
            "i"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (PARTITION BY `test`.`t`.`i` )  AS `ROW_NUMBER() OVER (PARTITION BY i)` from `test`.`t` order by `test`.`t`.`i` desc
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (PARTITION BY i), SUM(i) OVER () FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "44.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`i`"
          ],
          "functions": [
            "row_number"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "40.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 40,
        "rows_produced_per_join": 40,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "4.00",
          "prefix_cost": "4.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "i"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (PARTITION BY `test`.`t`.`i` )  AS `ROW_NUMBER() OVER (PARTITION BY i)`,sum(`test`.`t`.`i`) OVER ()  AS `SUM(i) OVER ()` from `test`.`t`
Sorting for 2nd window redundant and skipped
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (PARTITION BY i), SUM(i) OVER (ORDER BY i) FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "44.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`i`"
          ],
          "functions": [
            "row_number"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "40.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 40,
        "rows_produced_per_join": 40,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "4.00",
          "prefix_cost": "4.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "i"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (PARTITION BY `test`.`t`.`i` )  AS `ROW_NUMBER() OVER (PARTITION BY i)`,sum(`test`.`t`.`i`) OVER (ORDER BY `test`.`t`.`i` )  AS `SUM(i) OVER (ORDER BY i)` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (PARTITION BY i), SUM(i) OVER (ORDER BY i DESC) FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "84.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`i`"
          ],
          "functions": [
            "row_number"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "using_filesort": true,
          "filesort_key": [
            "`i` desc"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "80.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 40,
        "rows_produced_per_join": 40,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "4.00",
          "prefix_cost": "4.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "i"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (PARTITION BY `test`.`t`.`i` )  AS `ROW_NUMBER() OVER (PARTITION BY i)`,sum(`test`.`t`.`i`) OVER (ORDER BY `test`.`t`.`i` desc )  AS `SUM(i) OVER (ORDER BY i DESC)` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (PARTITION BY i), SUM(i) OVER (ORDER BY i DESC) FROM t GROUP BY i;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "84.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`i`"
          ],
          "functions": [
            "row_number"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "using_filesort": true,
          "filesort_key": [
            "`i` desc"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "80.00"
      },
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 40,
          "rows_produced_per_join": 40,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "4.00",
            "prefix_cost": "4.25",
            "data_read_per_join": "320"
          },
          "used_columns": [
            "i"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (PARTITION BY `test`.`t`.`i` )  AS `ROW_NUMBER() OVER (PARTITION BY i)`,sum(`test`.`t`.`i`) OVER (ORDER BY `test`.`t`.`i` desc )  AS `SUM(i) OVER (ORDER BY i DESC)` from `test`.`t` group by `test`.`t`.`i`
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (PARTITION BY i), SUM(i) OVER (ORDER BY i DESC) FROM t GROUP BY i;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "84.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`i`"
          ],
          "functions": [
            "row_number"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "using_filesort": true,
          "filesort_key": [
            "`i` desc"
          ],
          "frame_buffer": {
            "using_temporary_table": true,
            "optimized_frame_evaluation": true
          },
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "80.00"
      },
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 40,
          "rows_produced_per_join": 40,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "4.00",
            "prefix_cost": "4.25",
            "data_read_per_join": "320"
          },
          "used_columns": [
            "i"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (PARTITION BY `test`.`t`.`i` )  AS `ROW_NUMBER() OVER (PARTITION BY i)`,sum(`test`.`t`.`i`) OVER (ORDER BY `test`.`t`.`i` desc )  AS `SUM(i) OVER (ORDER BY i DESC)` from `test`.`t` group by `test`.`t`.`i`
Could be optimized further to drop final ordering
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (PARTITION BY i), SUM(i) OVER (ORDER BY i DESC) FROM t GROUP BY i ORDER BY i DESC;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "124.25"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "40.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "definition_position": 1,
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`i`"
            ],
            "functions": [
              "row_number"
            ]
          },
          {
            "name": "<unnamed window>",
            "definition_position": 2,
            "last_executed_window": true,
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`i` desc"
            ],
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "sum"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "80.00"
        },
        "grouping_operation": {
          "using_temporary_table": true,
          "using_filesort": false,
          "table": {
            "table_name": "t",
            "access_type": "ALL",
            "rows_examined_per_scan": 40,
            "rows_produced_per_join": 40,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "4.00",
              "prefix_cost": "4.25",
              "data_read_per_join": "320"
            },
            "used_columns": [
              "i"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (PARTITION BY `test`.`t`.`i` )  AS `ROW_NUMBER() OVER (PARTITION BY i)`,sum(`test`.`t`.`i`) OVER (ORDER BY `test`.`t`.`i` desc )  AS `SUM(i) OVER (ORDER BY i DESC)` from `test`.`t` group by `test`.`t`.`i` desc order by `test`.`t`.`i` desc
Reordering of windows could have made it possible to eliminate final ORDER BY
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (PARTITION BY i), SUM(i) OVER (ORDER BY i DESC) FROM t GROUP BY i ORDER BY i;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "124.25"
    },
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "40.00"
      },
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "definition_position": 1,
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`i`"
            ],
            "functions": [
              "row_number"
            ]
          },
          {
            "name": "<unnamed window>",
            "definition_position": 2,
            "last_executed_window": true,
            "using_temporary_table": true,
            "using_filesort": true,
            "filesort_key": [
              "`i` desc"
            ],
            "frame_buffer": {
              "using_temporary_table": true,
              "optimized_frame_evaluation": true
            },
            "functions": [
              "sum"
            ]
          }
        ],
        "cost_info": {
          "sort_cost": "80.00"
        },
        "grouping_operation": {
          "using_temporary_table": true,
          "using_filesort": false,
          "table": {
            "table_name": "t",
            "access_type": "ALL",
            "rows_examined_per_scan": 40,
            "rows_produced_per_join": 40,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "4.00",
              "prefix_cost": "4.25",
              "data_read_per_join": "320"
            },
            "used_columns": [
              "i"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (PARTITION BY `test`.`t`.`i` )  AS `ROW_NUMBER() OVER (PARTITION BY i)`,sum(`test`.`t`.`i`) OVER (ORDER BY `test`.`t`.`i` desc )  AS `SUM(i) OVER (ORDER BY i DESC)` from `test`.`t` group by `test`.`t`.`i` order by `test`.`t`.`i`
Implicit grouping should eliminate windowing ordering costs: only one row
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (ORDER BY AVG(i)) AS rn FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.25"
    },
    "table": {
      "table_name": "t",
      "access_type": "ALL",
      "rows_examined_per_scan": 40,
      "rows_produced_per_join": 40,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "4.00",
        "prefix_cost": "4.25",
        "data_read_per_join": "320"
      },
      "used_columns": [
        "i"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (ORDER BY avg(`test`.`t`.`i`) )  AS `rn` from `test`.`t`
Used to miss printing the sorting key
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (PARTITION BY i) FROM t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "44.25"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`i`"
          ],
          "functions": [
            "row_number"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "40.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 40,
        "rows_produced_per_join": 40,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "4.00",
          "prefix_cost": "4.25",
          "data_read_per_join": "320"
        },
        "used_columns": [
          "i"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (PARTITION BY `test`.`t`.`i` )  AS `ROW_NUMBER() OVER (PARTITION BY i)` from `test`.`t`
Test optimizer trace with window costs
SET optimizer_trace="enabled=on";
SELECT ROW_NUMBER() OVER (PARTITION BY i), SUM(i) OVER (ORDER BY i DESC) FROM t GROUP BY i ORDER BY i;
ROW_NUMBER() OVER (PARTITION BY i)	SUM(i) OVER (ORDER BY i DESC)
1	1297
1	1296
1	1294
1	1291
1	1287
1	1282
1	1276
1	1268
1	1259
1	1249
1	1237
1	1221
1	1202
1	1182
1	1153
1	1121
1	1082
1	1033
1	974
1	895
1	796
1	677
1	518
1	319
SELECT REGEXP_SUBSTR(TRACE, 'join_optimization.*', 1, 1, 'n') FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
REGEXP_SUBSTR(TRACE, 'join_optimization.*', 1, 1, 'n')
join_optimization": {
        "select#": 1,
        "steps": [
          {
            "substitute_generated_columns": {
            }
          },
          {
            "table_dependencies": [
              {
                "table": "`t`",
                "row_may_be_null": false,
                "map_bit": 0,
                "depends_on_map_bits": [
                ]
              }
            ]
          },
          {
            "rows_estimation": [
              {
                "table": "`t`",
                "table_scan": {
                  "rows": 40,
                  "cost": 0.25
                }
              }
            ]
          },
          {
            "considered_execution_plans": [
              {
                "plan_prefix": [
                ],
                "table": "`t`",
                "best_access_path": {
                  "considered_access_paths": [
                    {
                      "rows_to_scan": 40,
                      "access_type": "scan",
                      "resulting_rows": 40,
                      "cost": 4.25,
                      "chosen": true,
                      "use_tmp_table": true
                    }
                  ]
                },
                "condition_filtering_pct": 100,
                "rows_for_plan": 40,
                "cost_for_plan": 4.25,
                "sort_cost": 40,
                "new_cost_for_plan": 44.25,
                "chosen": true,
                "windowing_sort_cost": 80,
                "new_cost_for_plan": 124.25
              }
            ]
          },
          {
            "attaching_conditions_to_tables": {
              "original_condition": null,
              "attached_conditions_computation": [
              ],
              "attached_conditions_summary": [
                {
                  "table": "`t`",
                  "attached": null
                }
              ]
            }
          },
          {
            "optimizing_distinct_group_by_order_by": {
              "simplifying_order_by": {
                "original_clause": "`t`.`i`",
                "items": [
                  {
                    "item": "`t`.`i`"
                  }
                ],
                "resulting_clause_is_simple": true,
                "resulting_clause": "`t`.`i`"
              },
              "simplifying_group_by": {
                "original_clause": "`t`.`i`",
                "items": [
                  {
                    "item": "`t`.`i`"
                  }
                ],
                "resulting_clause_is_simple": false,
                "resulting_clause": "`t`.`i`"
              }
            }
          },
          {
            "finalizing_table_conditions": [
            ]
          },
          {
            "refine_plan": [
              {
                "table": "`t`"
              }
            ]
          },
          {
            "considering_tmp_tables": [
              {
                "adding_tmp_table_in_plan_at_position": 1,
                "write_method": "write_all_rows"
              },
              {
                "adding_tmp_table_in_plan_at_position": 2,
                "cause": "output_for_window_functions",
                "with_buffer": false,
                "write_method": "write_all_rows"
              },
              {
                "filesort": {
                  "adding_sort_to_table": ""
                }
              },
              {
                "adding_tmp_table_in_plan_at_position": 3,
                "cause": "output_for_window_functions",
                "with_buffer": true,
                "write_method": "write_all_rows"
              },
              {
                "creating_tmp_table": {
                  "tmp_table_info": {
                    "table": "window frame buffer",
                    "columns": 6,
                    "row_length": 29,
                    "key_length": 0,
                    "unique_constraint": false,
                    "makes_grouped_rows": false,
                    "cannot_insert_duplicates": false,
                    "location": "TempTable"
                  }
                }
              },
              {
                "filesort": {
                  "adding_sort_to_table": "window output buffer"
                }
              },
              {
                "adding_sort_to_table": "window output buffer"
              }
            ]
          }
        ]
      }
    },
    {
      "join_execution": {
        "select#": 1,
        "steps": [
          {
            "creating_tmp_table": {
              "tmp_table_info": {
                "table": "<temporary>",
                "in_plan_at_position": 3,
                "columns": 7,
                "row_length": 45,
                "key_length": 0,
                "unique_constraint": false,
                "makes_grouped_rows": false,
                "cannot_insert_duplicates": false,
                "location": "TempTable"
              }
            }
          },
          {
            "materialize": {
              "select#": 1,
              "steps": [
                {
                  "no de-duplication": {
                    "steps": [
                      {
                        "creating_tmp_table": {
                          "tmp_table_info": {
                            "table": "<temporary>",
                            "in_plan_at_position": 2,
                            "columns": 6,
                            "row_length": 29,
                            "key_length": 0,
                            "unique_constraint": false,
                            "makes_grouped_rows": false,
                            "cannot_insert_duplicates": false,
                            "location": "TempTable"
                          }
                        }
                      },
                      {
                        "materialize": {
                          "select#": 1,
                          "steps": [
                            {
                              "no de-duplication": {
                                "steps": [
                                  {
                                    "creating_tmp_table": {
                                      "tmp_table_info": {
                                        "table": "<temporary>",
                                        "in_plan_at_position": 1,
                                        "columns": 5,
                                        "row_length": 22,
                                        "key_length": 5,
                                        "unique_constraint": false,
                                        "makes_grouped_rows": true,
                                        "cannot_insert_duplicates": false,
                                        "location": "TempTable"
                                      }
                                    }
                                  },
                                  {
                                    "materialize": {
                                      "select#": 1,
                                      "steps": [
                                        {
                                          "de-duplicate with index": {
                                            "steps": [
                                            ]
                                          }
                                        }
                                      ]
                                    }
                                  },
                                  {
                                    "sorting_table": "<temporary>",
                                    "filesort_information": [
                                      {
                                        "direction": "asc",
                                        "expression": "`t`.`i`"
                                      }
                                    ],
                                    "filesort_priority_queue_optimization": {
                                      "usable": false,
                                      "cause": "not applicable (no LIMIT)"
                                    },
                                    "filesort_execution": [
                                    ],
                                    "filesort_summary": {
                                      "memory_available": 262144,
                                      "key_size": "XXX",
                                      "row_size": "XXX",
                                      "max_rows_per_buffer": "NNN",
                                      "num_rows_estimate": 40,
                                      "num_rows_found": 24,
                                      "num_initial_chunks_spilled_to_disk": 0,
                                      "peak_memory_used": "NNN",
                                      "sort_algorithm": "std::sort",
                                      "sort_mode": "<fixed_sort_key, packed_additional_fields>"
                                    }
                                  }
                                ]
                              }
                            }
                          ]
                        }
                      },
                      {
                        "sorting_table": "<temporary>",
                        "filesort_information": [
                          {
                            "direction": "desc",
                            "expression": "`t`.`i`"
                          }
                        ],
                        "filesort_priority_queue_optimization": {
                          "usable": false,
                          "cause": "not applicable (no LIMIT)"
                        },
                        "filesort_execution": [
                        ],
                        "filesort_summary": {
                          "memory_available": 262144,
                          "key_size": "XXX",
                          "row_size": "XXX",
                          "max_rows_per_buffer": "NNN",
                          "num_rows_estimate": 15,
                          "num_rows_found": 24,
                          "num_initial_chunks_spilled_to_disk": 0,
                          "peak_memory_used": "NNN",
                          "sort_algorithm": "std::sort",
                          "sort_mode": "<fixed_sort_key, packed_additional_fields>"
                        }
                      }
                    ]
                  }
                }
              ]
            }
          },
          {
            "sorting_table": "<temporary>",
            "filesort_information": [
              {
                "direction": "asc",
                "expression": "`t`.`i`"
              }
            ],
            "filesort_priority_queue_optimization": {
              "usable": false,
              "cause": "not applicable (no LIMIT)"
            },
            "filesort_execution": [
            ],
            "filesort_summary": {
              "memory_available": 262144,
              "key_size": "XXX",
              "row_size": "XXX",
              "max_rows_per_buffer": "NNN",
              "num_rows_estimate": 15,
              "num_rows_found": 24,
              "num_initial_chunks_spilled_to_disk": 0,
              "peak_memory_used": "NNN",
              "sort_algorithm": "std::sort",
              "sort_mode": "<fixed_sort_key, packed_additional_fields>"
            }
          }
        ]
      }
    }
  ]
}
SET optimizer_trace="enabled=off";
DROP TABLE t;
