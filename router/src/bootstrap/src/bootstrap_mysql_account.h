/*
  Copyright (c) 2022, 2025, Oracle and/or its affiliates.

  This program is free software; you can redistribute it and/or modify
  it under the terms of the GNU General Public License, version 2.0,
  as published by the Free Software Foundation.

  This program is designed to work with certain software (including
  but not limited to OpenSSL) that is licensed under separate terms,
  as designated in a particular file or component or in included license
  documentation.  The authors of MySQL hereby grant you an additional
  permission to link the program and your derivative works with the
  separately licensed software that they have either included with
  the program or referenced in the documentation.

  This program is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License for more details.

  You should have received a copy of the GNU General Public License
  along with this program; if not, write to the Free Software
  Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
*/

#ifndef ROUTER_SRC_BOOTSTRAP_SRC_BOOTSTRAP_MYSQL_ACCOUNT_H_
#define ROUTER_SRC_BOOTSTRAP_SRC_BOOTSTRAP_MYSQL_ACCOUNT_H_

#include <map>
#include <set>
#include <string>

#include "mysqlrouter/mysql_session.h"

static constexpr unsigned kMaxPasswordRetries = 10000;
using Strings = std::vector<std::string>;

struct UserOptions {
  std::string account;
  uint64_t password_retries{kMaxPasswordRetries};
  std::string account_create{"if-not-exists"};
  bool force_password_validation{false};

  Strings grant_role;
  bool autogenerated{false};
};

class BootstrapMySQLAccount {
 public:
  BootstrapMySQLAccount(mysqlrouter::MySQLSession *session) : mysql_{session} {}

  std::string create_router_accounts(const UserOptions &user_options,
                                     const std::set<std::string> &hostnames,
                                     const std::string &username,
                                     const std::string &password,
                                     bool password_change_ok);

 private:
  std::string create_accounts_with_compliant_password(
      const UserOptions &user_options, const std::string &username,
      const std::set<std::string> &hostnames, const std::string &password,
      bool password_change_ok, bool if_not_exists);

  void create_accounts(const UserOptions &user_options,
                       const std::string &username,
                       const std::set<std::string> &hostnames,
                       const std::string &password, bool if_not_exists = false);

  void create_users(const std::string &username,
                    const std::set<std::string> &hostnames,
                    const std::string &password, bool if_not_exists);

  void give_grants_to_users(const UserOptions &user_options,
                            const std::string &new_accounts);
  std::set<std::string> get_hostnames_of_created_accounts(
      const std::string &username, const std::set<std::string> &hostnames,
      bool if_not_exists);

  struct UndoCreateAccountList {
    enum {
      kNotSet = 1,  // =1 is not a requirement, just defensive programming
      kAllAccounts,
      kNewAccounts
    } type = kNotSet;
    std::string accounts;
  } undo_create_account_list_;
  mysqlrouter::MySQLSession *mysql_;
};

#endif  // ROUTER_SRC_BOOTSTRAP_SRC_BOOTSTRAP_MYSQL_ACCOUNT_H_
