include/only_with_option.inc [GLOBAL.replica_parallel_workers > 1]
include/group_replication.inc [rpl_server_count=4]
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection server1]

############################################################
# 1. Bootstrap group on server1. Configure servers.
#    Start an inbound channel that replicates from server4.
[connection server1]
include/start_and_bootstrap_group_replication.inc
SET SESSION sql_log_bin= 0;
call mtr.add_suppression("The transaction '[a-z0-9\-]*:[0-9]*' will commit out of order with respect to its source to follow the group global order.");
SET SESSION sql_log_bin= 1;
# Adding debug point 'simulate_bgct_rpco_deadlock' to @@GLOBAL.debug
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_USER='root', SOURCE_AUTO_POSITION=1, SOURCE_PORT=SERVER_4_PORT FOR CHANNEL 'ch1';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
include/rpl/start_replica.inc [FOR CHANNEL 'ch1']
[connection server2]
include/start_group_replication.inc
[connection server3]
include/start_group_replication.inc

############################################################
# 2. Schedule transactions in inbound replication channel
[connection server4]
CREATE TABLE t1 (c1 INT PRIMARY KEY, c2 LONGTEXT);
DROP TABLE t1;

############################################################
# 3. There must be 0 applier threads on server1 with the state
#    'Waiting for Binlog Group Commit ticket'.
[connection server1]
include/assert_grep.inc [There were transactions that did commit out of order with respect to its source to follow the group global order]

############################################################
# 4. Clean up.
[connection server4]
include/rpl/sync_to_replica.inc
include/rpl/sync.inc
[connection server3]
include/stop_group_replication.inc
[connection server2]
include/stop_group_replication.inc
[connection server1]
# Removing debug point 'simulate_bgct_rpco_deadlock' from @@GLOBAL.debug
include/rpl/stop_replica.inc [FOR CHANNEL 'ch1']
RESET REPLICA ALL FOR CHANNEL 'ch1';
include/stop_group_replication.inc
include/group_replication_end.inc
