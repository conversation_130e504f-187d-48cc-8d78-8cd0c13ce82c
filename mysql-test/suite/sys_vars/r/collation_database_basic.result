SET @global_start_value = @@global.collation_database;
SELECT @global_start_value;
@global_start_value
utf8mb4_0900_ai_ci
SET @session_start_value = @@collation_database;
SELECT @session_start_value;
@session_start_value
utf8mb4_0900_ai_ci
SET @session_start_value = @@local.collation_database;
SELECT @session_start_value;
@session_start_value
utf8mb4_0900_ai_ci
SET @session_start_value = @@session.collation_database;
SELECT @session_start_value;
@session_start_value
utf8mb4_0900_ai_ci
'#--------------------FN_DYNVARS_016_01------------------#'
SELECT collation_database;
ERROR 42S22: Unknown column 'collation_database' in 'field list'
SET collation_database=utf8mb3_unicode_ci;
Warnings:
Warning	3778	'utf8mb3_unicode_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_unicode_ci
SET global.collation_database=utf8mb3_unicode_ci;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'global.collation_database=utf8mb3_unicode_ci' at line 1
SET session collation_database=utf8mb3_unicode_ci;
Warnings:
Warning	3778	'utf8mb3_unicode_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT session collation_database;
ERROR 42S22: Unknown column 'session' in 'field list'
SET global collation_database=utf8mb3_unicode_ci;
Warnings:
Warning	3778	'utf8mb3_unicode_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT global collation_database;
ERROR 42S22: Unknown column 'global' in 'field list'
'#--------------------FN_DYNVARS_016_02-------------------------#'
SET @@collation_database = latin1_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SET @@collation_database = DEFAULT;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT DEFAULT_COLLATION_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME=DATABASE();
DEFAULT_COLLATION_NAME
utf8mb4_0900_ai_ci
SELECT @@collation_database AS DEFAULT_VALUE;
DEFAULT_VALUE
utf8mb4_0900_ai_ci
SET @@global.collation_database = latin1_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SET @@global.collation_database = DEFAULT;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb4_0900_ai_ci
'#--------------------FN_DYNVARS_016_03-------------------------#'
SET @@session.collation_database = utf8mb3_polish_ci;
Warnings:
Warning	3778	'utf8mb3_polish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_polish_ci
SET @@global.collation_database = latin7_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin7_general_ci
SELECT @@session.collation_database AS res_is_utf8mb3_polish_ci;
res_is_utf8mb3_polish_ci
utf8mb3_polish_ci
SET @@session.collation_database = latin7_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin7_bin
SELECT @@global.collation_database AS res_is_latin7_general_ci;
res_is_latin7_general_ci
latin7_general_ci
SELECT @@global.collation_database=@@session.collation_database AS res_is_false;
res_is_false
0
'#--------------------FN_DYNVARS_016_04-------------------------#'
SELECT @@collation_database = @@session.collation_database AS res;
res
1
SELECT @@collation_database = @@local.collation_database AS res;
res
1
'#--------------------FN_DYNVARS_016_05-------------------------#'
SET @@collation_database = latin7_general_ci + latin7_general_cs;
ERROR 42S22: Unknown column 'latin7_general_ci' in 'field list'
'#--------------------FN_DYNVARS_016_06-------------------------#'
SET @@session.collation_database = big5_chinese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
big5_chinese_ci
SET @@session.collation_database = big5_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
big5_bin
SET @@session.collation_database = dec8_swedish_ci;
Warnings:
Warning	4079	'dec8_swedish_ci' is a collation of the deprecated character set dec8. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
dec8_swedish_ci
SET @@session.collation_database = dec8_bin;
Warnings:
Warning	4079	'dec8_bin' is a collation of the deprecated character set dec8. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
dec8_bin
SET @@session.collation_database = cp850_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp850_general_ci
SET @@session.collation_database = cp850_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp850_bin
SET @@session.collation_database = hp8_english_ci;
Warnings:
Warning	4079	'hp8_english_ci' is a collation of the deprecated character set hp8. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
hp8_english_ci
SET @@session.collation_database = hp8_bin;
Warnings:
Warning	4079	'hp8_bin' is a collation of the deprecated character set hp8. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
hp8_bin
SET @@session.collation_database = koi8r_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
koi8r_general_ci
SET @@session.collation_database = koi8r_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
koi8r_bin
SET @@session.collation_database = latin1_german1_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin1_german1_ci
SET @@session.collation_database = latin1_swedish_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin1_swedish_ci
SET @@session.collation_database = latin1_danish_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin1_danish_ci
SET @@session.collation_database = latin1_german2_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin1_german2_ci
SET @@session.collation_database = latin1_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin1_bin
SET @@session.collation_database = latin1_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin1_general_ci
SET @@session.collation_database = latin1_general_cs;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin1_general_cs
SET @@session.collation_database = latin1_spanish_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin1_spanish_ci
SET @@session.collation_database = latin2_czech_cs;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin2_czech_cs
SET @@session.collation_database = latin2_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin2_general_ci
SET @@session.collation_database = latin2_hungarian_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin2_hungarian_ci
SET @@session.collation_database = latin2_croatian_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin2_croatian_ci
SET @@session.collation_database = latin2_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin2_bin
SET @@session.collation_database = swe7_swedish_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
swe7_swedish_ci
SET @@session.collation_database = swe7_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
swe7_bin
SET @@session.collation_database = ascii_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ascii_general_ci
SET @@session.collation_database = ascii_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ascii_bin
SET @@session.collation_database = ujis_japanese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ujis_japanese_ci
SET @@session.collation_database = ujis_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ujis_bin
SET @@session.collation_database = sjis_japanese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
sjis_japanese_ci
SET @@session.collation_database = sjis_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
sjis_bin
SET @@session.collation_database = hebrew_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
hebrew_general_ci
SET @@session.collation_database = hebrew_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
hebrew_bin
SET @@session.collation_database = tis620_thai_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
tis620_thai_ci
SET @@session.collation_database = tis620_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
tis620_bin
SET @@session.collation_database = euckr_korean_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
euckr_korean_ci
SET @@session.collation_database = euckr_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
euckr_bin
SET @@session.collation_database = koi8u_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
koi8u_general_ci
SET @@session.collation_database = koi8u_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
koi8u_bin
SET @@session.collation_database = gb2312_chinese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
gb2312_chinese_ci
SET @@session.collation_database = gb2312_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
gb2312_bin
SET @@session.collation_database = greek_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
greek_general_ci
SET @@session.collation_database = greek_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
greek_bin
SET @@session.collation_database = cp1250_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1250_general_ci
SET @@session.collation_database = cp1250_czech_cs;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1250_czech_cs
SET @@session.collation_database = cp1250_croatian_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1250_croatian_ci
SET @@session.collation_database = cp1250_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1250_bin
SET @@session.collation_database = cp1250_polish_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1250_polish_ci
SET @@session.collation_database = gbk_chinese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
gbk_chinese_ci
SET @@session.collation_database = gbk_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
gbk_bin
SET @@session.collation_database = gb18030_chinese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
gb18030_chinese_ci
SET @@session.collation_database = gb18030_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
gb18030_bin
SET @@session.collation_database = latin5_turkish_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin5_turkish_ci
SET @@session.collation_database = latin5_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin5_bin
SET @@session.collation_database = armscii8_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
armscii8_general_ci
SET @@session.collation_database = armscii8_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
armscii8_bin
SET @@session.collation_database = utf8mb3_general_ci;
Warnings:
Warning	3778	'utf8mb3_general_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_general_ci
SET @@session.collation_database = utf8mb3_bin;
Warnings:
Warning	3778	'utf8mb3_bin' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_bin
SET @@session.collation_database = utf8mb3_unicode_ci;
Warnings:
Warning	3778	'utf8mb3_unicode_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_unicode_ci
SET @@session.collation_database = utf8mb3_icelandic_ci;
Warnings:
Warning	3778	'utf8mb3_icelandic_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_icelandic_ci
SET @@session.collation_database = utf8mb3_latvian_ci;
Warnings:
Warning	3778	'utf8mb3_latvian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_latvian_ci
SET @@session.collation_database = utf8mb3_romanian_ci;
Warnings:
Warning	3778	'utf8mb3_romanian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_romanian_ci
SET @@session.collation_database = utf8mb3_slovenian_ci;
Warnings:
Warning	3778	'utf8mb3_slovenian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_slovenian_ci
SET @@session.collation_database = utf8mb3_polish_ci;
Warnings:
Warning	3778	'utf8mb3_polish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_polish_ci
SET @@session.collation_database = utf8mb3_estonian_ci;
Warnings:
Warning	3778	'utf8mb3_estonian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_estonian_ci
SET @@session.collation_database = utf8mb3_spanish_ci;
Warnings:
Warning	3778	'utf8mb3_spanish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_spanish_ci
SET @@session.collation_database = utf8mb3_swedish_ci;
Warnings:
Warning	3778	'utf8mb3_swedish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_swedish_ci
SET @@session.collation_database = utf8mb3_turkish_ci;
Warnings:
Warning	3778	'utf8mb3_turkish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_turkish_ci
SET @@session.collation_database = utf8mb3_czech_ci;
Warnings:
Warning	3778	'utf8mb3_czech_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_czech_ci
SET @@session.collation_database = utf8mb3_danish_ci;
Warnings:
Warning	3778	'utf8mb3_danish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_danish_ci
SET @@session.collation_database = utf8mb3_lithuanian_ci;
Warnings:
Warning	3778	'utf8mb3_lithuanian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_lithuanian_ci
SET @@session.collation_database = utf8mb3_slovak_ci;
Warnings:
Warning	3778	'utf8mb3_slovak_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_slovak_ci
SET @@session.collation_database = utf8mb3_spanish2_ci;
Warnings:
Warning	3778	'utf8mb3_spanish2_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_spanish2_ci
SET @@session.collation_database = utf8mb3_roman_ci;
Warnings:
Warning	3778	'utf8mb3_roman_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_roman_ci
SET @@session.collation_database = utf8mb3_persian_ci;
Warnings:
Warning	3778	'utf8mb3_persian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_persian_ci
SET @@session.collation_database = utf8mb3_esperanto_ci;
Warnings:
Warning	3778	'utf8mb3_esperanto_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_esperanto_ci
SET @@session.collation_database = utf8mb3_hungarian_ci;
Warnings:
Warning	3778	'utf8mb3_hungarian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_hungarian_ci
SET @@session.collation_database = utf8mb3_unicode_520_ci;
Warnings:
Warning	3778	'utf8mb3_unicode_520_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb3_unicode_520_ci
SET @@session.collation_database = ucs2_general_ci;
Warnings:
Warning	4079	'ucs2_general_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_general_ci
SET @@session.collation_database = ucs2_bin;
Warnings:
Warning	4079	'ucs2_bin' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_bin
SET @@session.collation_database = ucs2_unicode_ci;
Warnings:
Warning	4079	'ucs2_unicode_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_unicode_ci
SET @@session.collation_database = ucs2_icelandic_ci;
Warnings:
Warning	4079	'ucs2_icelandic_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_icelandic_ci
SET @@session.collation_database = ucs2_latvian_ci;
Warnings:
Warning	4079	'ucs2_latvian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_latvian_ci
SET @@session.collation_database = ucs2_romanian_ci;
Warnings:
Warning	4079	'ucs2_romanian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_romanian_ci
SET @@session.collation_database = ucs2_slovenian_ci;
Warnings:
Warning	4079	'ucs2_slovenian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_slovenian_ci
SET @@session.collation_database = ucs2_polish_ci;
Warnings:
Warning	4079	'ucs2_polish_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_polish_ci
SET @@session.collation_database = ucs2_estonian_ci;
Warnings:
Warning	4079	'ucs2_estonian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_estonian_ci
SET @@session.collation_database = ucs2_spanish_ci;
Warnings:
Warning	4079	'ucs2_spanish_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_spanish_ci
SET @@session.collation_database = ucs2_swedish_ci;
Warnings:
Warning	4079	'ucs2_swedish_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_swedish_ci
SET @@session.collation_database = ucs2_turkish_ci;
Warnings:
Warning	4079	'ucs2_turkish_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_turkish_ci
SET @@session.collation_database = ucs2_czech_ci;
Warnings:
Warning	4079	'ucs2_czech_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_czech_ci
SET @@session.collation_database = ucs2_danish_ci;
Warnings:
Warning	4079	'ucs2_danish_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_danish_ci
SET @@session.collation_database = ucs2_lithuanian_ci;
Warnings:
Warning	4079	'ucs2_lithuanian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_lithuanian_ci
SET @@session.collation_database = ucs2_slovak_ci;
Warnings:
Warning	4079	'ucs2_slovak_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_slovak_ci
SET @@session.collation_database = ucs2_spanish2_ci;
Warnings:
Warning	4079	'ucs2_spanish2_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_spanish2_ci
SET @@session.collation_database = ucs2_roman_ci;
Warnings:
Warning	4079	'ucs2_roman_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_roman_ci
SET @@session.collation_database = ucs2_persian_ci;
Warnings:
Warning	4079	'ucs2_persian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_persian_ci
SET @@session.collation_database = ucs2_esperanto_ci;
Warnings:
Warning	4079	'ucs2_esperanto_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_esperanto_ci
SET @@session.collation_database = ucs2_hungarian_ci;
Warnings:
Warning	4079	'ucs2_hungarian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_hungarian_ci
SET @@session.collation_database = ucs2_unicode_520_ci;
Warnings:
Warning	4079	'ucs2_unicode_520_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
ucs2_unicode_520_ci
SET @@session.collation_database = cp866_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp866_general_ci
SET @@session.collation_database = cp866_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp866_bin
SET @@session.collation_database = keybcs2_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
keybcs2_general_ci
SET @@session.collation_database = keybcs2_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
keybcs2_bin
SET @@session.collation_database = macce_general_ci;
Warnings:
Warning	4079	'macce_general_ci' is a collation of the deprecated character set macce. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
macce_general_ci
SET @@session.collation_database = macce_bin;
Warnings:
Warning	4079	'macce_bin' is a collation of the deprecated character set macce. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
macce_bin
SET @@session.collation_database = macroman_general_ci;
Warnings:
Warning	4079	'macroman_general_ci' is a collation of the deprecated character set macroman. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
macroman_general_ci
SET @@session.collation_database = macroman_bin;
Warnings:
Warning	4079	'macroman_bin' is a collation of the deprecated character set macroman. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
macroman_bin
SET @@session.collation_database = cp852_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp852_general_ci
SET @@session.collation_database = cp852_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp852_bin
SET @@session.collation_database = latin7_estonian_cs;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin7_estonian_cs
SET @@session.collation_database = latin7_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin7_general_ci
SET @@session.collation_database = latin7_general_cs;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin7_general_cs
SET @@session.collation_database = latin7_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
latin7_bin
SET @@session.collation_database = cp1251_bulgarian_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1251_bulgarian_ci
SET @@session.collation_database = cp1251_ukrainian_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1251_ukrainian_ci
SET @@session.collation_database = cp1251_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1251_bin
SET @@session.collation_database = cp1251_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1251_general_ci
SET @@session.collation_database = cp1251_general_cs;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1251_general_cs
SET @@session.collation_database = cp1256_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1256_general_ci
SET @@session.collation_database = cp1256_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1256_bin
SET @@session.collation_database = cp1257_lithuanian_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1257_lithuanian_ci
SET @@session.collation_database = cp1257_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1257_bin
SET @@session.collation_database = cp1257_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp1257_general_ci
SET @@session.collation_database = binary;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
binary
SET @@session.collation_database = geostd8_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
geostd8_general_ci
SET @@session.collation_database = geostd8_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
geostd8_bin
SET @@session.collation_database = cp932_japanese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp932_japanese_ci
SET @@session.collation_database = cp932_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
cp932_bin
SET @@session.collation_database = eucjpms_japanese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
eucjpms_japanese_ci
SET @@session.collation_database = eucjpms_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
eucjpms_bin
SET @@session.collation_database = utf8mb4_0900_ai_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb4_0900_ai_ci
'#--------------------FN_DYNVARS_016_07-------------------------#'
SET @@global.collation_database = big5_chinese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
big5_chinese_ci
SET @@global.collation_database = big5_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
big5_bin
SET @@global.collation_database = dec8_swedish_ci;
Warnings:
Warning	4079	'dec8_swedish_ci' is a collation of the deprecated character set dec8. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
dec8_swedish_ci
SET @@global.collation_database = dec8_bin;
Warnings:
Warning	4079	'dec8_bin' is a collation of the deprecated character set dec8. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
dec8_bin
SET @@global.collation_database = cp850_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp850_general_ci
SET @@global.collation_database = cp850_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp850_bin
SET @@global.collation_database = hp8_english_ci;
Warnings:
Warning	4079	'hp8_english_ci' is a collation of the deprecated character set hp8. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
hp8_english_ci
SET @@global.collation_database = hp8_bin;
Warnings:
Warning	4079	'hp8_bin' is a collation of the deprecated character set hp8. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
hp8_bin
SET @@global.collation_database = koi8r_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
koi8r_general_ci
SET @@global.collation_database = koi8r_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
koi8r_bin
SET @@global.collation_database = latin1_german1_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin1_german1_ci
SET @@global.collation_database = latin1_swedish_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin1_swedish_ci
SET @@global.collation_database = latin1_danish_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin1_danish_ci
SET @@global.collation_database = latin1_german2_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin1_german2_ci
SET @@global.collation_database = latin1_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin1_bin
SET @@global.collation_database = latin1_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin1_general_ci
SET @@global.collation_database = latin1_general_cs;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin1_general_cs
SET @@global.collation_database = latin1_spanish_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin1_spanish_ci
SET @@global.collation_database = latin2_czech_cs;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin2_czech_cs
SET @@global.collation_database = latin2_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin2_general_ci
SET @@global.collation_database = latin2_hungarian_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin2_hungarian_ci
SET @@global.collation_database = latin2_croatian_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin2_croatian_ci
SET @@global.collation_database = latin2_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin2_bin
SET @@global.collation_database = swe7_swedish_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
swe7_swedish_ci
SET @@global.collation_database = swe7_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
swe7_bin
SET @@global.collation_database = ascii_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ascii_general_ci
SET @@global.collation_database = ascii_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ascii_bin
SET @@global.collation_database = ujis_japanese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ujis_japanese_ci
SET @@global.collation_database = ujis_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ujis_bin
SET @@global.collation_database = sjis_japanese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
sjis_japanese_ci
SET @@global.collation_database = sjis_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
sjis_bin
SET @@global.collation_database = hebrew_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
hebrew_general_ci
SET @@global.collation_database = hebrew_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
hebrew_bin
SET @@global.collation_database = tis620_thai_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
tis620_thai_ci
SET @@global.collation_database = tis620_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
tis620_bin
SET @@global.collation_database = euckr_korean_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
euckr_korean_ci
SET @@global.collation_database = euckr_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
euckr_bin
SET @@global.collation_database = koi8u_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
koi8u_general_ci
SET @@global.collation_database = koi8u_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
koi8u_bin
SET @@global.collation_database = gb2312_chinese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
gb2312_chinese_ci
SET @@global.collation_database = gb2312_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
gb2312_bin
SET @@global.collation_database = greek_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
greek_general_ci
SET @@global.collation_database = greek_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
greek_bin
SET @@global.collation_database = cp1250_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1250_general_ci
SET @@global.collation_database = cp1250_czech_cs;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1250_czech_cs
SET @@global.collation_database = cp1250_croatian_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1250_croatian_ci
SET @@global.collation_database = cp1250_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1250_bin
SET @@global.collation_database = cp1250_polish_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1250_polish_ci
SET @@global.collation_database = gbk_chinese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
gbk_chinese_ci
SET @@global.collation_database = gbk_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
gbk_bin
SET @@global.collation_database = gb18030_chinese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
gb18030_chinese_ci
SET @@global.collation_database = gb18030_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
gb18030_bin
SET @@global.collation_database = latin5_turkish_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin5_turkish_ci
SET @@global.collation_database = latin5_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin5_bin
SET @@global.collation_database = armscii8_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
armscii8_general_ci
SET @@global.collation_database = armscii8_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
armscii8_bin
SET @@global.collation_database = utf8mb3_general_ci;
Warnings:
Warning	3778	'utf8mb3_general_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_general_ci
SET @@global.collation_database = utf8mb3_bin;
Warnings:
Warning	3778	'utf8mb3_bin' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_bin
SET @@global.collation_database = utf8mb3_unicode_ci;
Warnings:
Warning	3778	'utf8mb3_unicode_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_unicode_ci
SET @@global.collation_database = utf8mb3_icelandic_ci;
Warnings:
Warning	3778	'utf8mb3_icelandic_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_icelandic_ci
SET @@global.collation_database = utf8mb3_latvian_ci;
Warnings:
Warning	3778	'utf8mb3_latvian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_latvian_ci
SET @@global.collation_database = utf8mb3_romanian_ci;
Warnings:
Warning	3778	'utf8mb3_romanian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_romanian_ci
SET @@global.collation_database = utf8mb3_slovenian_ci;
Warnings:
Warning	3778	'utf8mb3_slovenian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_slovenian_ci
SET @@global.collation_database = utf8mb3_polish_ci;
Warnings:
Warning	3778	'utf8mb3_polish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_polish_ci
SET @@global.collation_database = utf8mb3_estonian_ci;
Warnings:
Warning	3778	'utf8mb3_estonian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_estonian_ci
SET @@global.collation_database = utf8mb3_spanish_ci;
Warnings:
Warning	3778	'utf8mb3_spanish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_spanish_ci
SET @@global.collation_database = utf8mb3_swedish_ci;
Warnings:
Warning	3778	'utf8mb3_swedish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_swedish_ci
SET @@global.collation_database = utf8mb3_turkish_ci;
Warnings:
Warning	3778	'utf8mb3_turkish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_turkish_ci
SET @@global.collation_database = utf8mb3_czech_ci;
Warnings:
Warning	3778	'utf8mb3_czech_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_czech_ci
SET @@global.collation_database = utf8mb3_danish_ci;
Warnings:
Warning	3778	'utf8mb3_danish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_danish_ci
SET @@global.collation_database = utf8mb3_lithuanian_ci;
Warnings:
Warning	3778	'utf8mb3_lithuanian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_lithuanian_ci
SET @@global.collation_database = utf8mb3_slovak_ci;
Warnings:
Warning	3778	'utf8mb3_slovak_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_slovak_ci
SET @@global.collation_database = utf8mb3_spanish2_ci;
Warnings:
Warning	3778	'utf8mb3_spanish2_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_spanish2_ci
SET @@global.collation_database = utf8mb3_roman_ci;
Warnings:
Warning	3778	'utf8mb3_roman_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_roman_ci
SET @@global.collation_database = utf8mb3_persian_ci;
Warnings:
Warning	3778	'utf8mb3_persian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_persian_ci
SET @@global.collation_database = utf8mb3_esperanto_ci;
Warnings:
Warning	3778	'utf8mb3_esperanto_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_esperanto_ci
SET @@global.collation_database = utf8mb3_hungarian_ci;
Warnings:
Warning	3778	'utf8mb3_hungarian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_hungarian_ci
SET @@global.collation_database = utf8mb3_unicode_520_ci;
Warnings:
Warning	3778	'utf8mb3_unicode_520_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb3_unicode_520_ci
SET @@global.collation_database = ucs2_general_ci;
Warnings:
Warning	4079	'ucs2_general_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_general_ci
SET @@global.collation_database = ucs2_bin;
Warnings:
Warning	4079	'ucs2_bin' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_bin
SET @@global.collation_database = ucs2_unicode_ci;
Warnings:
Warning	4079	'ucs2_unicode_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_unicode_ci
SET @@global.collation_database = ucs2_icelandic_ci;
Warnings:
Warning	4079	'ucs2_icelandic_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_icelandic_ci
SET @@global.collation_database = ucs2_latvian_ci;
Warnings:
Warning	4079	'ucs2_latvian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_latvian_ci
SET @@global.collation_database = ucs2_romanian_ci;
Warnings:
Warning	4079	'ucs2_romanian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_romanian_ci
SET @@global.collation_database = ucs2_slovenian_ci;
Warnings:
Warning	4079	'ucs2_slovenian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_slovenian_ci
SET @@global.collation_database = ucs2_polish_ci;
Warnings:
Warning	4079	'ucs2_polish_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_polish_ci
SET @@global.collation_database = ucs2_estonian_ci;
Warnings:
Warning	4079	'ucs2_estonian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_estonian_ci
SET @@global.collation_database = ucs2_spanish_ci;
Warnings:
Warning	4079	'ucs2_spanish_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_spanish_ci
SET @@global.collation_database = ucs2_swedish_ci;
Warnings:
Warning	4079	'ucs2_swedish_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_swedish_ci
SET @@global.collation_database = ucs2_turkish_ci;
Warnings:
Warning	4079	'ucs2_turkish_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_turkish_ci
SET @@global.collation_database = ucs2_czech_ci;
Warnings:
Warning	4079	'ucs2_czech_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_czech_ci
SET @@global.collation_database = ucs2_danish_ci;
Warnings:
Warning	4079	'ucs2_danish_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_danish_ci
SET @@global.collation_database = ucs2_lithuanian_ci;
Warnings:
Warning	4079	'ucs2_lithuanian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_lithuanian_ci
SET @@global.collation_database = ucs2_slovak_ci;
Warnings:
Warning	4079	'ucs2_slovak_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_slovak_ci
SET @@global.collation_database = ucs2_spanish2_ci;
Warnings:
Warning	4079	'ucs2_spanish2_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_spanish2_ci
SET @@global.collation_database = ucs2_roman_ci;
Warnings:
Warning	4079	'ucs2_roman_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_roman_ci
SET @@global.collation_database = ucs2_persian_ci;
Warnings:
Warning	4079	'ucs2_persian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_persian_ci
SET @@global.collation_database = ucs2_esperanto_ci;
Warnings:
Warning	4079	'ucs2_esperanto_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_esperanto_ci
SET @@global.collation_database = ucs2_hungarian_ci;
Warnings:
Warning	4079	'ucs2_hungarian_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_hungarian_ci
SET @@global.collation_database = ucs2_unicode_520_ci;
Warnings:
Warning	4079	'ucs2_unicode_520_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
ucs2_unicode_520_ci
SET @@global.collation_database = cp866_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp866_general_ci
SET @@global.collation_database = cp866_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp866_bin
SET @@global.collation_database = keybcs2_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
keybcs2_general_ci
SET @@global.collation_database = keybcs2_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
keybcs2_bin
SET @@global.collation_database = macce_general_ci;
Warnings:
Warning	4079	'macce_general_ci' is a collation of the deprecated character set macce. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
macce_general_ci
SET @@global.collation_database = macce_bin;
Warnings:
Warning	4079	'macce_bin' is a collation of the deprecated character set macce. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
macce_bin
SET @@global.collation_database = macroman_general_ci;
Warnings:
Warning	4079	'macroman_general_ci' is a collation of the deprecated character set macroman. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
macroman_general_ci
SET @@global.collation_database = macroman_bin;
Warnings:
Warning	4079	'macroman_bin' is a collation of the deprecated character set macroman. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
macroman_bin
SET @@global.collation_database = cp852_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp852_general_ci
SET @@global.collation_database = cp852_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp852_bin
SET @@global.collation_database = latin7_estonian_cs;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin7_estonian_cs
SET @@global.collation_database = latin7_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin7_general_ci
SET @@global.collation_database = latin7_general_cs;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin7_general_cs
SET @@global.collation_database = latin7_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin7_bin
SET @@global.collation_database = cp1251_bulgarian_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1251_bulgarian_ci
SET @@global.collation_database = cp1251_ukrainian_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1251_ukrainian_ci
SET @@global.collation_database = cp1251_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1251_bin
SET @@global.collation_database = cp1251_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1251_general_ci
SET @@global.collation_database = cp1251_general_cs;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1251_general_cs
SET @@global.collation_database = cp1256_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1256_general_ci
SET @@global.collation_database = cp1256_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1256_bin
SET @@global.collation_database = cp1257_lithuanian_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1257_lithuanian_ci
SET @@global.collation_database = cp1257_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1257_bin
SET @@global.collation_database = cp1257_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1257_general_ci
SET @@global.collation_database = binary;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
binary
SET @@global.collation_database = geostd8_general_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
geostd8_general_ci
SET @@global.collation_database = geostd8_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
geostd8_bin
SET @@global.collation_database = cp932_japanese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp932_japanese_ci
SET @@global.collation_database = cp932_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp932_bin
SET @@global.collation_database = eucjpms_japanese_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
eucjpms_japanese_ci
SET @@global.collation_database = eucjpms_bin;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
eucjpms_bin
SET @@global.collation_database = utf8mb4_0900_ai_ci;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb4_0900_ai_ci
'#--------------------FN_DYNVARS_016_08-------------------------#'
SET @@collation_database = LATIN7_GENERAL_CS;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@collation_database;
@@collation_database
latin7_general_cs
SET @@collation_database = latin7_general_cs;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@collation_database;
@@collation_database
latin7_general_cs
SET @@global.collation_database = Latin7_GeneRal_cS;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin7_general_cs
'#--------------------FN_DYNVARS_016_09-------------------------#'
SET @@collation_database = 1;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@collation_database;
@@collation_database
big5_chinese_ci
SET @@collation_database = 2;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@collation_database;
@@collation_database
latin2_czech_cs
SET @@collation_database = 3;
Warnings:
Warning	4079	'dec8_swedish_ci' is a collation of the deprecated character set dec8. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@collation_database;
@@collation_database
dec8_swedish_ci
SET @@collation_database = 99;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@collation_database;
@@collation_database
cp1250_polish_ci
SET @@collation_database = 100;
ERROR HY000: Unknown collation: '100'
SET @@global.collation_database = 1;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
big5_chinese_ci
SET @@global.collation_database = 2;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
latin2_czech_cs
SET @@global.collation_database = 3;
Warnings:
Warning	4079	'dec8_swedish_ci' is a collation of the deprecated character set dec8. Please consider using utf8mb4 with an appropriate collation instead.
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
dec8_swedish_ci
SET @@global.collation_database = 99;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
cp1250_polish_ci
SET @@global.collation_database = 100;
ERROR HY000: Unknown collation: '100'
SET @total_collations = (SELECT count(*) FROM INFORMATION_SCHEMA.COLLATIONS);
SELECT @total_collations > 120;
@total_collations > 120
1
'#--------------------FN_DYNVARS_016_10-------------------------#'
SET @@collation_database = latin7_binary;
ERROR HY000: Unknown collation: 'latin7_binary'
SET @@collation_database = 'eucjpms_japanese_cs';
ERROR HY000: Unknown collation: 'eucjpms_japanese_cs'
SET @@collation_database = 0;
ERROR HY000: Unknown collation: '0'
SET @@collation_database = 1.01;
ERROR 42000: Incorrect argument type to variable 'collation_database'
SET @@collation_database = -1;
ERROR HY000: Unknown collation: '-1'
SET @@collation_database = '';
ERROR HY000: Unknown collation: ''
SET @@collation_database = ' eucjpms_bin';
ERROR HY000: Unknown collation: ' eucjpms_bin'
SET @@collation_database = true;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@collation_database AS res_with_true;
res_with_true
big5_chinese_ci
SET @@collation_database = ON;
ERROR HY000: Unknown collation: 'ON'
SET @@collation_database = repeat('e', 256);
ERROR HY000: Unknown collation: 'eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee'
SET @@collation_database = repeat('e', 1024);
ERROR HY000: Unknown collation: 'eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee'
'#--------------------FN_DYNVARS_016_11-------------------------#'
SET @@global.collation_database = latin7_binary;
ERROR HY000: Unknown collation: 'latin7_binary'
SET @@global.collation_database = 'eucjpms_japanese_cs';
ERROR HY000: Unknown collation: 'eucjpms_japanese_cs'
SET @@global.collation_database = 0;
ERROR HY000: Unknown collation: '0'
SET @@global.collation_database = 1.1;
ERROR 42000: Incorrect argument type to variable 'collation_database'
SET @@global.collation_database = -1;
ERROR HY000: Unknown collation: '-1'
SET @@global.collation_database = "";
ERROR HY000: Unknown collation: ''
SET @@global.collation_database = ' eucjpms_bin';
ERROR HY000: Unknown collation: ' eucjpms_bin'
SET @@global.collation_database = true;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SET @@global.collation_database = ON;
ERROR HY000: Unknown collation: 'ON'
SET @@global.collation_database = repeat('e', 256);
ERROR HY000: Unknown collation: 'eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee'
SET @@global.collation_database = repeat('e', 1024);
ERROR HY000: Unknown collation: 'eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee'
'#--------------------FN_DYNVARS_016_12-------------------------#'
SELECT @@global.collation_database =
(SELECT VARIABLE_VALUE FROM performance_schema.global_variables
WHERE VARIABLE_NAME='collation_database') AS res;
res
1
SET @@global.collation_database = 1;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
big5_chinese_ci
SELECT @@global.collation_database =
(SELECT VARIABLE_VALUE FROM performance_schema.global_variables
WHERE VARIABLE_NAME='collation_database') AS res;
res
1
'#--------------------FN_DYNVARS_016_13-------------------------#'
SELECT @@collation_database =
(SELECT VARIABLE_VALUE FROM performance_schema.session_variables
WHERE VARIABLE_NAME='collation_database') AS res;
res
1
SELECT @@local.collation_database =
(SELECT VARIABLE_VALUE FROM performance_schema.session_variables
WHERE VARIABLE_NAME='collation_database') AS res;
res
1
SELECT @@session.collation_database =
(SELECT VARIABLE_VALUE FROM performance_schema.session_variables
WHERE VARIABLE_NAME='collation_database') AS res;
res
1
SET @@global.collation_database = @global_start_value;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@global.collation_database;
@@global.collation_database
utf8mb4_0900_ai_ci
SET @@session.collation_database = @session_start_value;
Warnings:
Warning	1681	Updating 'collation_database' is deprecated. It will be made read-only in a future release.
SELECT @@session.collation_database;
@@session.collation_database
utf8mb4_0900_ai_ci
