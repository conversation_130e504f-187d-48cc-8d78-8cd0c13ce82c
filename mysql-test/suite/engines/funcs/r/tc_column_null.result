DROP TABLE IF EXISTS t1;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DEC NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FIXED NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE PRECISION NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NULL DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATETIME NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` datetime DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyblob
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` blob
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumblob
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` longblob
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinytext
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` text
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumtext
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` longtext
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
