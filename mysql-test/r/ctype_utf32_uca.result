DROP TABLE IF EXISTS t1;
#
# Start of 5.5 tests
#
set names utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
set collation_connection=utf32_unicode_ci;
select hex('a'), hex('a ');
hex('a')	hex('a ')
00000061	0000006100000020
select 'a' = 'a', 'a' = 'a ', 'a ' = 'a';
'a' = 'a'	'a' = 'a '	'a ' = 'a'
1	1	1
select 'a\0' = 'a', 'a\0' < 'a', 'a\0' > 'a';
'a\0' = 'a'	'a\0' < 'a'	'a\0' > 'a'
1	0	0
select 'a' = 'a\0', 'a' < 'a\0', 'a' > 'a\0';
'a' = 'a\0'	'a' < 'a\0'	'a' > 'a\0'
1	0	0
select 'a\0' = 'a ', 'a\0' < 'a ', 'a\0' > 'a ';
'a\0' = 'a '	'a\0' < 'a '	'a\0' > 'a '
1	0	0
select 'a ' = 'a\0', 'a ' < 'a\0', 'a ' > 'a\0';
'a ' = 'a\0'	'a ' < 'a\0'	'a ' > 'a\0'
1	0	0
select 'a  a' > 'a', 'a  \0' < 'a';
'a  a' > 'a'	'a  \0' < 'a'
1	0
select binary 'a  a' > 'a', binary 'a  \0' > 'a', binary 'a\0' > 'a';
binary 'a  a' > 'a'	binary 'a  \0' > 'a'	binary 'a\0' > 'a'
1	1	1
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select 'c' like '\_' as want0;
want0
0
create table t1 (c1 char(10) character set utf32 collate utf32_bin);
insert into t1 values ('A'),('a');
insert into t1 values ('B'),('b');
insert into t1 values ('C'),('c');
insert into t1 values ('D'),('d');
insert into t1 values ('E'),('e');
insert into t1 values ('F'),('f');
insert into t1 values ('G'),('g');
insert into t1 values ('H'),('h');
insert into t1 values ('I'),('i');
insert into t1 values ('J'),('j');
insert into t1 values ('K'),('k');
insert into t1 values ('L'),('l');
insert into t1 values ('M'),('m');
insert into t1 values ('N'),('n');
insert into t1 values ('O'),('o');
insert into t1 values ('P'),('p');
insert into t1 values ('Q'),('q');
insert into t1 values ('R'),('r');
insert into t1 values ('S'),('s');
insert into t1 values ('T'),('t');
insert into t1 values ('U'),('u');
insert into t1 values ('V'),('v');
insert into t1 values ('W'),('w');
insert into t1 values ('X'),('x');
insert into t1 values ('Y'),('y');
insert into t1 values ('Z'),('z');
insert into t1 values (_ucs2 0x00e0),(_ucs2 0x00c0);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e1),(_ucs2 0x00c1);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e2),(_ucs2 0x00c2);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e3),(_ucs2 0x00c3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e4),(_ucs2 0x00c4);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e5),(_ucs2 0x00c5);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e6),(_ucs2 0x00c6);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e7),(_ucs2 0x00c7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e8),(_ucs2 0x00c8);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e9),(_ucs2 0x00c9);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ea),(_ucs2 0x00ca);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00eb),(_ucs2 0x00cb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ec),(_ucs2 0x00cc);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ed),(_ucs2 0x00cd);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ee),(_ucs2 0x00ce);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ef),(_ucs2 0x00cf);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f0),(_ucs2 0x00d0);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f1),(_ucs2 0x00d1);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f2),(_ucs2 0x00d2);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f3),(_ucs2 0x00d3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f4),(_ucs2 0x00d4);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f5),(_ucs2 0x00d5);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f6),(_ucs2 0x00d6);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f7),(_ucs2 0x00d7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f8),(_ucs2 0x00d8);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f9),(_ucs2 0x00d9);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fa),(_ucs2 0x00da);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fb),(_ucs2 0x00db);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fc),(_ucs2 0x00dc);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fd),(_ucs2 0x00dd);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fe),(_ucs2 0x00de);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ff),(_ucs2 0x00df);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0100),(_ucs2 0x0101),(_ucs2 0x0102),(_ucs2 0x0103);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0104),(_ucs2 0x0105),(_ucs2 0x0106),(_ucs2 0x0107);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0108),(_ucs2 0x0109),(_ucs2 0x010a),(_ucs2 0x010b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x010c),(_ucs2 0x010d),(_ucs2 0x010e),(_ucs2 0x010f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0110),(_ucs2 0x0111),(_ucs2 0x0112),(_ucs2 0x0113);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0114),(_ucs2 0x0115),(_ucs2 0x0116),(_ucs2 0x0117);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0118),(_ucs2 0x0119),(_ucs2 0x011a),(_ucs2 0x011b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x011c),(_ucs2 0x011d),(_ucs2 0x011e),(_ucs2 0x011f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0120),(_ucs2 0x0121),(_ucs2 0x0122),(_ucs2 0x0123);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0124),(_ucs2 0x0125),(_ucs2 0x0126),(_ucs2 0x0127);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0128),(_ucs2 0x0129),(_ucs2 0x012a),(_ucs2 0x012b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x012c),(_ucs2 0x012d),(_ucs2 0x012e),(_ucs2 0x012f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0130),(_ucs2 0x0131),(_ucs2 0x0132),(_ucs2 0x0133);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0134),(_ucs2 0x0135),(_ucs2 0x0136),(_ucs2 0x0137);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0138),(_ucs2 0x0139),(_ucs2 0x013a),(_ucs2 0x013b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x013c),(_ucs2 0x013d),(_ucs2 0x013e),(_ucs2 0x013f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0140),(_ucs2 0x0141),(_ucs2 0x0142),(_ucs2 0x0143);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0144),(_ucs2 0x0145),(_ucs2 0x0146),(_ucs2 0x0147);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0148),(_ucs2 0x0149),(_ucs2 0x014a),(_ucs2 0x014b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x014c),(_ucs2 0x014d),(_ucs2 0x014e),(_ucs2 0x014f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0150),(_ucs2 0x0151),(_ucs2 0x0152),(_ucs2 0x0153);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0154),(_ucs2 0x0155),(_ucs2 0x0156),(_ucs2 0x0157);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0158),(_ucs2 0x0159),(_ucs2 0x015a),(_ucs2 0x015b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x015c),(_ucs2 0x015d),(_ucs2 0x015e),(_ucs2 0x015f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0160),(_ucs2 0x0161),(_ucs2 0x0162),(_ucs2 0x0163);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0164),(_ucs2 0x0165),(_ucs2 0x0166),(_ucs2 0x0167);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0168),(_ucs2 0x0169),(_ucs2 0x016a),(_ucs2 0x016b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x016c),(_ucs2 0x016d),(_ucs2 0x016e),(_ucs2 0x016f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0170),(_ucs2 0x0171),(_ucs2 0x0172),(_ucs2 0x0173);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0174),(_ucs2 0x0175),(_ucs2 0x0176),(_ucs2 0x0177);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0178),(_ucs2 0x0179),(_ucs2 0x017a),(_ucs2 0x017b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x017c),(_ucs2 0x017d),(_ucs2 0x017e),(_ucs2 0x017f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0180),(_ucs2 0x0181),(_ucs2 0x0182),(_ucs2 0x0183);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0184),(_ucs2 0x0185),(_ucs2 0x0186),(_ucs2 0x0187);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0188),(_ucs2 0x0189),(_ucs2 0x018a),(_ucs2 0x018b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x018c),(_ucs2 0x018d),(_ucs2 0x018e),(_ucs2 0x018f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0190),(_ucs2 0x0191),(_ucs2 0x0192),(_ucs2 0x0193);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0194),(_ucs2 0x0195),(_ucs2 0x0196),(_ucs2 0x0197);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0198),(_ucs2 0x0199),(_ucs2 0x019a),(_ucs2 0x019b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x019c),(_ucs2 0x019d),(_ucs2 0x019e),(_ucs2 0x019f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01a0),(_ucs2 0x01a1),(_ucs2 0x01a2),(_ucs2 0x01a3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01a4),(_ucs2 0x01a5),(_ucs2 0x01a6),(_ucs2 0x01a7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01a8),(_ucs2 0x01a9),(_ucs2 0x01aa),(_ucs2 0x01ab);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01ac),(_ucs2 0x01ad),(_ucs2 0x01ae),(_ucs2 0x01af);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01b0),(_ucs2 0x01b1),(_ucs2 0x01b2),(_ucs2 0x01b3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01b4),(_ucs2 0x01b5),(_ucs2 0x01b6),(_ucs2 0x01b7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01b8),(_ucs2 0x01b9),(_ucs2 0x01ba),(_ucs2 0x01bb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01bc),(_ucs2 0x01bd),(_ucs2 0x01be),(_ucs2 0x01bf);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01c0),(_ucs2 0x01c1),(_ucs2 0x01c2),(_ucs2 0x01c3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01c4),(_ucs2 0x01c5),(_ucs2 0x01c6),(_ucs2 0x01c7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01c8),(_ucs2 0x01c9),(_ucs2 0x01ca),(_ucs2 0x01cb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01cc),(_ucs2 0x01cd),(_ucs2 0x01ce),(_ucs2 0x01cf);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01d0),(_ucs2 0x01d1),(_ucs2 0x01d2),(_ucs2 0x01d3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01d4),(_ucs2 0x01d5),(_ucs2 0x01d6),(_ucs2 0x01d7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01d8),(_ucs2 0x01d9),(_ucs2 0x01da),(_ucs2 0x01db);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01dc),(_ucs2 0x01dd),(_ucs2 0x01de),(_ucs2 0x01df);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01e0),(_ucs2 0x01e1),(_ucs2 0x01e2),(_ucs2 0x01e3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01e4),(_ucs2 0x01e5),(_ucs2 0x01e6),(_ucs2 0x01e7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01e8),(_ucs2 0x01e9),(_ucs2 0x01ea),(_ucs2 0x01eb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01ec),(_ucs2 0x01ed),(_ucs2 0x01ee),(_ucs2 0x01ef);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01f0),(_ucs2 0x01f1),(_ucs2 0x01f2),(_ucs2 0x01f3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01f4),(_ucs2 0x01f5),(_ucs2 0x01f6),(_ucs2 0x01f7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01f8),(_ucs2 0x01f9),(_ucs2 0x01fa),(_ucs2 0x01fb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01fc),(_ucs2 0x01fd),(_ucs2 0x01fe),(_ucs2 0x01ff);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EA0),(_ucs2 0x1EA1),(_ucs2 0x1EA2),(_ucs2 0x1EA3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EA4),(_ucs2 0x1EA5),(_ucs2 0x1EA6),(_ucs2 0x1EA7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EA8),(_ucs2 0x1EA9),(_ucs2 0x1EAA),(_ucs2 0x1EAB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EAC),(_ucs2 0x1EAD),(_ucs2 0x1EAE),(_ucs2 0x1EAF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EB0),(_ucs2 0x1EB1),(_ucs2 0x1EB2),(_ucs2 0x1EB3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EB4),(_ucs2 0x1EB5),(_ucs2 0x1EB6),(_ucs2 0x1EB7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EB8),(_ucs2 0x1EB9),(_ucs2 0x1EBA),(_ucs2 0x1EBB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EBC),(_ucs2 0x1EBD),(_ucs2 0x1EBE),(_ucs2 0x1EBF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EC0),(_ucs2 0x1EC1),(_ucs2 0x1EC2),(_ucs2 0x1EC3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EC4),(_ucs2 0x1EC5),(_ucs2 0x1EC6),(_ucs2 0x1EC7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EC8),(_ucs2 0x1EC9),(_ucs2 0x1ECA),(_ucs2 0x1ECB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1ECC),(_ucs2 0x1ECD),(_ucs2 0x1ECE),(_ucs2 0x1ECF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1ED0),(_ucs2 0x1ED1),(_ucs2 0x1ED2),(_ucs2 0x1ED3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1ED4),(_ucs2 0x1ED5),(_ucs2 0x1ED6),(_ucs2 0x1ED7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1ED8),(_ucs2 0x1ED9),(_ucs2 0x1EDA),(_ucs2 0x1EDB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EDC),(_ucs2 0x1EDD),(_ucs2 0x1EDE),(_ucs2 0x1EDF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EE0),(_ucs2 0x1EE1),(_ucs2 0x1EE2),(_ucs2 0x1EE3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EE4),(_ucs2 0x1EE5),(_ucs2 0x1EE6),(_ucs2 0x1EE7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EE8),(_ucs2 0x1EE9),(_ucs2 0x1EEA),(_ucs2 0x1EEB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EEC),(_ucs2 0x1EED),(_ucs2 0x1EEE),(_ucs2 0x1EEF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EF0),(_ucs2 0x1EF1);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values ('AA'),('Aa'),('aa'),('aA');
insert into t1 values ('AE'),('Ae'),('ae'),('aE');
insert into t1 values ('CH'),('Ch'),('ch'),('cH');
insert into t1 values ('DZ'),('Dz'),('dz'),('dZ');
insert into t1 values ('DŽ'),('Dž'),('dž'),('dŽ');
insert into t1 values ('IJ'),('Ij'),('ij'),('iJ');
insert into t1 values ('LJ'),('Lj'),('lj'),('lJ');
insert into t1 values ('LL'),('Ll'),('ll'),('lL');
insert into t1 values ('NJ'),('Nj'),('nj'),('nJ');
insert into t1 values ('OE'),('Oe'),('oe'),('oE');
insert into t1 values ('SS'),('Ss'),('ss'),('sS');
insert into t1 values ('RR'),('Rr'),('rr'),('rR');
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_unicode_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċČč
CHChcHch
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕÖòóôõöŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşŠšſ
SSSssSssß
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻżŽž
ƍ
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_icelandic_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÂÃàâãĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
Áá
ǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċČč
CHChcHch
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Ðð
Đđ
Ɖ
Ɗ
Ƌƌ
EeÈÊËèêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Éé
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÎÏìîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
Íí
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÔÕòôõŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
Óó
Ǿǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşŠšſ
SSSssSssß
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÛÜùûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Úú
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÿŶŷŸ
Ýý
Ƴƴ
ZzŹźŻżŽž
ƍ
Þþ
ÄÆäæ
ÖØöø
Åå
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_latvian_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċ
CHChcHch
Čč
Ƈƈ
DdĎď
DZDzdZdzǄǅǆǱǲǳ
DŽDždŽdž
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġǦǧǴǵ
Ģģ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
Yy
ı
Ɨ
Ɩ
JjĴĵǰ
KkǨǩ
Ķķ
Ƙƙ
LlĹĺĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Ļļ
Łł
ƚ
ƛ
Mm
NnÑñŃńŇňǸǹ
NJNjnJnjǊǋǌ
Ņņ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕÖòóôõöŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŘř
RRRrrRrr
Ŗŗ
Ʀ
SsŚśŜŝŞşſ
SSSssSssß
Šš
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
ÝýÿŶŷŸ
Ƴƴ
ZzŹźŻż
ƍ
Žž
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_romanian_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÃÄÅàáãäåĀāĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
Ăă
Ââ
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċČč
CHChcHch
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÏìíïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
Îî
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕÖòóôõöŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŠšſ
SSSssSssß
Şş
Ʃ
ƪ
TtŤť
ƾ
Ţţ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻżŽž
ƍ
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_slovenian_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċ
CHChcHch
Čč
Ƈƈ
DdĎď
DZDzdZdzǄǅǆǱǲǳ
DŽDždŽdž
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕÖòóôõöŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşſ
SSSssSssß
Šš
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻż
ƍ
Žž
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_polish_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
Ąą
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĈĉĊċČč
CHChcHch
Ćć
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ęę
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ńń
Ɲ
ƞ
Ŋŋ
OoÒÔÕÖòôõöŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
Óó
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŜŝŞşŠšſ
SSSssSssß
Śś
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŽž
ƍ
Źź
Żż
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_estonian_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÅàáâãåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċČč
CHChcHch
Ƈƈ
DdĎď
DZDzdZdz
DŽDždŽdž
ǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔòóôŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşſ
SSSssSssß
Šš
Zz
Žž
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛùúûŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Õõ
Ää
Öö
Üü
Xx
YyÝýÿŶŷŸ
Ƴƴ
ŹźŻż
ƍ
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_spanish_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċČč
CHChcHch
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ññ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕÖòóôõöŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşŠšſ
SSSssSssß
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻżŽž
ƍ
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_swedish_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃàáâãĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċČč
CHChcHch
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕòóôõŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
Ǿǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşŠšſ
SSSssSssß
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛùúûŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÜÝüýÿŶŷŸ
Ƴƴ
ZzŹźŻżŽž
ƍ
Åå
ÄÆäæ
ÖØöø
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_turkish_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcĆćĈĉĊċČč
CHChcHch
Çç
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĠġĢģǦǧǴǵ
Ğğ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
Iı
IJIj
ƕǶ
Ħħ
iÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
iJijĲĳ
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕòóôõŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
Öö
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŠšſ
SSSssSssß
Şş
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛùúûŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Üü
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻżŽž
ƍ
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_czech_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċ
cH
Čč
Ƈƈ
DdĎď
DZDzdZdzǄǅǆǱǲǳ
DŽDždŽdž
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
CHChch
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕÖòóôõöŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗ
RRRrrRrr
Řř
Ʀ
SsŚśŜŝŞşſ
SSSssSssß
Šš
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻż
ƍ
Žž
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_danish_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃàáâãĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
aA
AEAeaEae
ǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċČč
CHChcHch
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕòóôõŌōŎŏƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
Ǿǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşŠšſ
SSSssSssß
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛùúûŨũŪūŬŭŮůŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÜÝüýÿŰűŶŷŸ
Ƴƴ
ZzŹźŻżŽž
ƍ
ÄÆäæ
ÖØöøŐő
AAAaaaÅå
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_lithuanian_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CCHChcchÇçĆćĈĉĊċ
cH
Čč
Ƈƈ
DdĎď
DZDzdZdzǄǅǆǱǲǳ
DŽDždŽdž
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IYiyÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕÖòóôõöŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşſ
SSSssSssß
Šš
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
ÝýÿŶŷŸ
Ƴƴ
ZzŹźŻż
ƍ
Žž
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_slovak_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÅàáâãåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
Ää
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċ
cH
Čč
Ƈƈ
DdĎď
DZDzdZdzǄǅǆǱǲǳ
DŽDždŽdž
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
CHChch
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÕÖòóõöŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
Ôô
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşſ
SSSssSssß
Šš
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻż
ƍ
Žž
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_spanish2_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċČč
cH
CHChch
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
lL
LLLlll
Łł
ƚ
ƛ
Mm
NnŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ññ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕÖòóôõöŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşŠšſ
SSSssSssß
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻżŽž
ƍ
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_roman_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċČč
CHChcHch
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IJijÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJij
Ĳĳ
ı
Ɨ
Ɩ
Ĵĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJlj
Ǉǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnj
Ǌǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕÖòóôõöŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşŠšſ
SSSssSssß
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
ÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
UVuv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻżŽž
ƍ
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_esperanto_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĊċČč
CHChcHch
Ĉĉ
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĞğĠġĢģǦǧǴǵ
Ĝĝ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
Hh
Ĥĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
Jjǰ
Ĵĵ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕÖòóôõöŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŞşŠšſ
SSSssSssß
Ŝŝ
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ŭŭ
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻżŽž
ƍ
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_hungarian_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċČč
CHChcHch
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕòóôõŌōŎŏƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
ÖöŐő
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşŠšſ
SSSssSssß
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛùúûŨũŪūŬŭŮůŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
ÜüŰű
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻżŽž
ƍ
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_croatian_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEae
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĈĉĊċ
CHChcHch
Čč
Ćć
Ƈƈ
DdĎď
DZDzdZdzǱǲǳ
DŽDždŽdžǄǅǆ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LLLllLll
LJLjlJljǇǈǉ
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕÖòóôõöŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşſ
SSSssSssß
Šš
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻż
ƍ
Žž
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_german2_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÅàáâãåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEaeÄÆäæ
ǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċČč
CHChcHch
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕòóôõŌōŎŏŐőƠơǑǒǪǫǬǭỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeÖöŒœ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşŠšſ
SSSssSssß
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛùúûŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Üü
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻżŽž
ƍ
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_unicode_520_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặ
AAAaaAaa
AEAeaEaeÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċČč
CHChcHch
Ƈƈ
DdÐðĎďĐđ
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Ɖ
Ɗ
Ƌƌ
EeÈÉÊËèéêëĒēĔĕĖėĘęĚěẸẹẺẻẼẽẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥĦħ
ƕǶ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľĿŀŁł
LJLjlJljǇǈǉ
LLLllLll
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÔÕÖØòóôõöøŌōŎŏŐőƠơǑǒǪǫǬǭǾǿỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợ
OEOeoEoeŒœ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşŠšſ
SSSssSssß
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜỤụỦủỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻżŽž
ƍ
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(c1 order by binary c1 separator '') from t1 group by c1 collate utf32_vietnamese_ci;
group_concat(c1 order by binary c1 separator '')
÷
×
AaÀÁÃÄÅàáãäåĀāĄąǍǎǞǟǠǡǺǻẠạẢả
AAAaaAaa
AEAeaEae
ĂăẮắẰằẲẳẴẵẶặ
ÂâẤấẦầẨẩẪẫẬậ
ÆæǢǣǼǽ
Bb
ƀ
Ɓ
Ƃƃ
CcÇçĆćĈĉĊċČč
CHChcHch
Ƈƈ
DdĎď
DZDzDŽDždZdzdŽdžǄǅǆǱǲǳ
Đđ
Ɖ
Ɗ
Ƌƌ
Ðð
EeÈÉËèéëĒēĔĕĖėĘęĚěẸẹẺẻẼẽ
ÊêẾếỀềỂểỄễỆệ
Ǝǝ
Ə
Ɛ
Ff
Ƒƒ
GgĜĝĞğĠġĢģǦǧǴǵ
Ǥǥ
Ɠ
Ɣ
Ƣƣ
HhĤĥ
ƕǶ
Ħħ
IiÌÍÎÏìíîïĨĩĪīĬĭĮįİǏǐỈỉỊị
IJIjiJijĲĳ
ı
Ɨ
Ɩ
JjĴĵǰ
KkĶķǨǩ
Ƙƙ
LlĹĺĻļĽľ
Ŀŀ
LJLjlJljǇǈǉ
LLLllLll
Łł
ƚ
ƛ
Mm
NnÑñŃńŅņŇňǸǹ
NJNjnJnjǊǋǌ
Ɲ
ƞ
Ŋŋ
OoÒÓÕÖòóõöŌōŎŏŐőǑǒǪǫǬǭỌọỎỏ
OEOeoEoeŒœ
ÔôỐốỒồỔổỖỗỘộ
ƠơỚớỜờỞởỠỡỢợ
ØøǾǿ
Ɔ
Ɵ
Pp
Ƥƥ
Qq
ĸ
RrŔŕŖŗŘř
RRRrrRrr
Ʀ
SsŚśŜŝŞşŠšſ
SSSssSssß
Ʃ
ƪ
TtŢţŤť
ƾ
Ŧŧ
ƫ
Ƭƭ
Ʈ
UuÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųǓǔǕǖǗǘǙǚǛǜỤụỦủ
ƯưỨứỪừỬửỮữỰự
Ɯ
Ʊ
Vv
Ʋ
WwŴŵ
Xx
YyÝýÿŶŷŸ
Ƴƴ
ZzŹźŻżŽž
ƍ
Ƶƶ
ƷǮǯ
Ƹƹ
ƺ
Þþ
ƿǷ
ƻ
Ƨƨ
Ƽƽ
Ƅƅ
ŉ
ǀ
ǁ
ǂ
ǃ
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
drop table t1;
SET NAMES utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
CREATE TABLE t1 (c varchar(150) CHARACTER SET utf32 COLLATE utf32_general_ci NOT NULL, INDEX (c));
INSERT INTO t1 VALUES (_ucs2 0x039C03C903B403B11F770308);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT * FROM t1 WHERE c LIKE _utf32 0x0000039C00000025 COLLATE utf32_general_ci;
c
Μωδαί̈
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x039C03C903B4 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT * FROM t1 WHERE c LIKE _utf32 0x0000039C00000025
COLLATE utf32_general_ci ORDER BY c;
c
Μωδ
Μωδαί̈
DROP TABLE t1;
CREATE TABLE t1 (c varchar(150) CHARACTER SET utf32 COLLATE utf32_unicode_ci NOT NULL, INDEX (c));
INSERT INTO t1 VALUES (_ucs2 0x039C03C903B403B11F770308);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT * FROM t1 WHERE c LIKE _utf32 0x0000039C00000025 COLLATE utf32_unicode_ci;
c
Μωδαί̈
INSERT INTO t1 VALUES (_ucs2 0x039C03C903B4);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT * FROM t1 WHERE c LIKE _utf32 0x0000039C00000025
COLLATE utf32_unicode_ci ORDER BY c;
c
Μωδ
Μωδαί̈
DROP TABLE t1;
CREATE TABLE t1 (c varchar(150) CHARACTER SET utf32 COLLATE utf32_unicode_ci NOT NULL, INDEX (c));
INSERT INTO t1 VALUES (_ucs2 0x039C03C903B403B11F770308);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT * FROM t1 WHERE c LIKE CONVERT(_ucs2 0x039C0025 USING utf32) COLLATE utf32_unicode_ci;
c
Μωδαί̈
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x039C03C903B4 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT * FROM t1 WHERE c LIKE CONVERT(_ucs2 0x039C0025 USING utf32)
COLLATE utf32_unicode_ci ORDER BY c;
c
Μωδ
Μωδαί̈
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
DROP TABLE t1;
SET NAMES utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SET @test_character_set='utf32';
SET @test_collation='utf32_swedish_ci';
SET @safe_character_set_server= @@character_set_server;
SET @safe_collation_server= @@collation_server;
SET @safe_character_set_client= @@character_set_client;
SET @safe_character_set_results= @@character_set_results;
SET character_set_server= @test_character_set;
SET collation_server= @test_collation;
CREATE DATABASE d1;
USE d1;
CREATE TABLE t1 (c CHAR(10), KEY(c));
SHOW FULL COLUMNS FROM t1;
Field	Type	Collation	Null	Key	Default	Extra	Privileges	Comment
c	char(10)	utf32_swedish_ci	YES	MUL	NULL			
INSERT INTO t1 VALUES ('aaa'),('aaaa'),('aaaaa');
SELECT c as want3results FROM t1 WHERE c LIKE 'aaa%';
want3results
aaa
aaaa
aaaaa
DROP TABLE t1;
CREATE TABLE t1 (c1 varchar(15), KEY c1 (c1(2)));
SHOW FULL COLUMNS FROM t1;
Field	Type	Collation	Null	Key	Default	Extra	Privileges	Comment
c1	varchar(15)	utf32_swedish_ci	YES	MUL	NULL			
INSERT INTO t1 VALUES ('location'),('loberge'),('lotre'),('boabab');
SELECT c1 as want3results from t1 where c1 like 'l%';
want3results
location
loberge
lotre
SELECT c1 as want3results from t1 where c1 like 'lo%';
want3results
location
loberge
lotre
SELECT c1 as want1result  from t1 where c1 like 'loc%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'loca%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'locat%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'locati%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'locatio%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'location%';
want1result
location
DROP TABLE t1;
create table t1 (a set('a') not null);
insert ignore into t1 values (),();
Warnings:
Warning	1364	Field 'a' doesn't have a default value
select cast(a as char(1)) from t1;
cast(a as char(1))


select a sounds like a from t1;
a sounds like a
1
1
select 1 from t1 order by cast(a as char(1));
1
1
1
drop table t1;
set names utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
create table t1 (
name varchar(10),
level smallint unsigned);
show create table t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `name` varchar(10) COLLATE utf32_swedish_ci DEFAULT NULL,
  `level` smallint unsigned DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf32 COLLATE=utf32_swedish_ci
insert into t1 values ('string',1);
select concat(name,space(level)), concat(name, repeat(' ',level)) from t1;
concat(name,space(level))	concat(name, repeat(' ',level))
string 	string 
drop table t1;
DROP DATABASE d1;
USE test;
SET character_set_server= @safe_character_set_server;
SET collation_server= @safe_collation_server;
SET character_set_client= @safe_character_set_client;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SET character_set_results= @safe_character_set_results;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SET collation_connection='utf32_unicode_ci';
create table t1 select repeat('a',4000) a;
delete from t1;
insert into t1 values ('a'), ('a '), ('a\t');
select collation(a),hex(a) from t1 order by a;
collation(a)	hex(a)
utf32_unicode_ci	0000006100000009
utf32_unicode_ci	00000061
utf32_unicode_ci	0000006100000020
drop table t1;
select @@collation_connection;
@@collation_connection
utf32_unicode_ci
create table t1 ROW_FORMAT=DYNAMIC select repeat('a',50) as c1 ;
insert into t1 values('abcdef');
insert into t1 values('_bcdef');
insert into t1 values('a_cdef');
insert into t1 values('ab_def');
insert into t1 values('abc_ef');
insert into t1 values('abcd_f');
insert into t1 values('abcde_');
select c1 as c1u from t1 where c1 like 'ab\_def';
c1u
ab_def
select c1 as c2h from t1 where c1 like 'ab#_def' escape '#';
c2h
ab_def
drop table t1;
End of 4.1 tests
CREATE TABLE t1 (id int, a varchar(30) character set utf32);
INSERT INTO t1 VALUES (1, _ucs2 0x01310069), (2, _ucs2 0x01310131);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (3, _ucs2 0x00690069), (4, _ucs2 0x01300049);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (5, _ucs2 0x01300130), (6, _ucs2 0x00490049);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT a, length(a) la, @l:=lower(a) l, length(@l) ll, @u:=upper(a) u, length(@u) lu
FROM t1 ORDER BY id;
a	la	l	ll	u	lu
ıi	8	ıi	8	II	8
ıı	8	ıı	8	II	8
ii	8	ii	8	II	8
İI	8	ii	8	İI	8
İİ	8	ii	8	İİ	8
II	8	ii	8	II	8
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
ALTER TABLE t1 MODIFY a VARCHAR(30) character set utf32 collate utf32_turkish_ci;
SELECT a, length(a) la, @l:=lower(a) l, length(@l) ll, @u:=upper(a) u, length(@u) lu
FROM t1 ORDER BY id;
a	la	l	ll	u	lu
ıi	8	ıi	8	Iİ	8
ıı	8	ıı	8	II	8
ii	8	ii	8	İİ	8
İI	8	iı	8	İI	8
İİ	8	ii	8	İİ	8
II	8	ıı	8	II	8
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
DROP TABLE t1;
set collation_connection=utf32_unicode_ci;
drop table if exists t1;
create table t1 as
select repeat(' ', 64) as s1, repeat(' ',64) as s2
union
select null, null;
show create table t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `s1` varchar(64) CHARACTER SET utf32 COLLATE utf32_unicode_ci DEFAULT NULL,
  `s2` varchar(64) CHARACTER SET utf32 COLLATE utf32_unicode_ci DEFAULT NULL
) ENGINE=default_engine DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
delete from t1;
insert into t1 values('aaa','aaa');
insert into t1 values('aaa|qqq','qqq');
insert into t1 values('gheis','^[^a-dXYZ]+$');
insert into t1 values('aab','^aa?b');
insert into t1 values('Baaan','^Ba*n');
insert into t1 values('aaa','qqq|aaa');
insert into t1 values('qqq','qqq|aaa');
insert into t1 values('bbb','qqq|aaa');
insert into t1 values('bbb','qqq');
insert into t1 values('aaa','aba');
insert into t1 values(null,'abc');
insert into t1 values('def',null);
insert into t1 values(null,null);
select HIGH_PRIORITY s1 regexp s2 from t1;
s1 regexp s2
1
1
1
1
1
1
1
0
0
0
NULL
NULL
NULL
SELECT 'ghi' REGEXP 'ghi[';
ERROR HY000: The regular expression contains an unclosed bracket expression.
drop table t1;
SET collation_connection=utf32_czech_ci;
SELECT @@collation_connection;
@@collation_connection
utf32_czech_ci
#
# Bug#57737 Character sets: search fails with like, contraction, index
#
CREATE TABLE t1 AS SELECT REPEAT(' ', 10) AS s1 LIMIT 0;
INSERT INTO t1 VALUES ('c'),('ce'),('cé'),('ch');
SELECT * FROM t1 WHERE s1 LIKE 'c%';
s1
c
ce
cé
ch
ALTER TABLE t1 ADD KEY s1 (s1);
SELECT * FROM t1 WHERE s1 LIKE 'c%';
s1
c
ce
cé
ch
ALTER TABLE t1 DROP KEY s1, ADD KEY(s1(1));
SELECT * FROM t1 WHERE s1 LIKE 'ch';
s1
ch
DROP TABLE t1;
SELECT @@collation_connection;
@@collation_connection
utf32_czech_ci
#
# Bug#57737 Character sets: search fails with like, contraction, index
# Part#2 - ignorable characters
#
CREATE TABLE t1 AS SELECT REPEAT(' ', 10) AS s1 LIMIT 0;
INSERT INTO t1 VALUES ('a\0\0\0\0\0\t'),('a'),('b'),('c'),('d'),('e');
SELECT HEX(s1) FROM t1 WHERE s1 LIKE 'a%';
HEX(s1)
00000061000000000000000000000000000000000000000000000009
00000061
ALTER TABLE t1 ADD KEY s1 (s1);
SELECT HEX(s1) FROM t1 WHERE s1 LIKE 'a%';
HEX(s1)
00000061000000000000000000000000000000000000000000000009
00000061
DROP TABLE t1;
#
# Bug #12319710 : INVALID MEMORY READ AND/OR CRASH IN 
#   MY_UCA_CHARCMP WITH UTF32
#
SET collation_connection=utf32_unicode_ci;
CREATE TABLE t1 (a TEXT CHARACTER SET utf32 COLLATE utf32_turkish_ci NOT NULL);
INSERT INTO t1 VALUES ('a'), ('b');
CREATE TABLE t2 (b VARBINARY(5) NOT NULL);
#insert chars outside of BMP
INSERT INTO t2 VALUEs (0x082837),(0x082837);
#test for read-out-of-bounds with non-BMP chars as a LIKE pattern
SELECT * FROM t1,t2 WHERE a LIKE b;
a	b
#test the original statement
SELECT 1 FROM t1 AS t1_0 NATURAL LEFT OUTER JOIN t2 AS t2_0
RIGHT JOIN t1 AS t1_1 ON t1_0.a LIKE t2_0.b;
1
1
1
DROP TABLE t1,t2;
#
# End of 5.5 tests
#
#
# Start of 5.6 tests
#
#
# WL#3664 WEIGHT_STRING
#
set collation_connection=utf32_unicode_ci;
select @@collation_connection;
@@collation_connection
utf32_unicode_ci
select hex(weight_string('a'));
hex(weight_string('a'))
0E33
select hex(weight_string('A'));
hex(weight_string('A'))
0E33
select hex(weight_string('abc'));
hex(weight_string('abc'))
0E330E4A0E60
select hex(weight_string('abc' as char(2)));
hex(weight_string('abc' as char(2)))
0E330E4A
select hex(weight_string('abc' as char(3)));
hex(weight_string('abc' as char(3)))
0E330E4A0E60
select hex(weight_string('abc' as char(5)));
hex(weight_string('abc' as char(5)))
0E330E4A0E6002090209
select hex(weight_string('abc', 1, 2, 0xC0));
hex(weight_string('abc', 1, 2, 0xC0))
0E
select hex(weight_string('abc', 2, 2, 0xC0));
hex(weight_string('abc', 2, 2, 0xC0))
0E33
select hex(weight_string('abc', 3, 2, 0xC0));
hex(weight_string('abc', 3, 2, 0xC0))
0E330E
select hex(weight_string('abc', 4, 2, 0xC0));
hex(weight_string('abc', 4, 2, 0xC0))
0E330E4A
select hex(weight_string('abc', 5, 2, 0xC0));
hex(weight_string('abc', 5, 2, 0xC0))
0E330E4A02
select hex(weight_string('abc',25, 2, 0xC0));
hex(weight_string('abc',25, 2, 0xC0))
0E330E4A020902090209020902090209020902090209020902
select hex(weight_string('abc', 1, 3, 0xC0));
hex(weight_string('abc', 1, 3, 0xC0))
0E
select hex(weight_string('abc', 2, 3, 0xC0));
hex(weight_string('abc', 2, 3, 0xC0))
0E33
select hex(weight_string('abc', 3, 3, 0xC0));
hex(weight_string('abc', 3, 3, 0xC0))
0E330E
select hex(weight_string('abc', 4, 3, 0xC0));
hex(weight_string('abc', 4, 3, 0xC0))
0E330E4A
select hex(weight_string('abc', 5, 3, 0xC0));
hex(weight_string('abc', 5, 3, 0xC0))
0E330E4A0E
select hex(weight_string('abc',25, 3, 0xC0));
hex(weight_string('abc',25, 3, 0xC0))
0E330E4A0E6002090209020902090209020902090209020902
select hex(weight_string('abc', 1, 4, 0xC0));
hex(weight_string('abc', 1, 4, 0xC0))
0E
select hex(weight_string('abc', 2, 4, 0xC0));
hex(weight_string('abc', 2, 4, 0xC0))
0E33
select hex(weight_string('abc', 3, 4, 0xC0));
hex(weight_string('abc', 3, 4, 0xC0))
0E330E
select hex(weight_string('abc', 4, 4, 0xC0));
hex(weight_string('abc', 4, 4, 0xC0))
0E330E4A
select hex(weight_string('abc', 5, 4, 0xC0));
hex(weight_string('abc', 5, 4, 0xC0))
0E330E4A0E
select hex(weight_string('abc',25, 4, 0xC0));
hex(weight_string('abc',25, 4, 0xC0))
0E330E4A0E6002090209020902090209020902090209020902
select @@collation_connection;
@@collation_connection
utf32_unicode_ci
select hex(weight_string(cast(_latin1 0x80 as char)));
hex(weight_string(cast(_latin1 0x80 as char)))
0E23
select hex(weight_string(cast(_latin1 0x808080 as char)));
hex(weight_string(cast(_latin1 0x808080 as char)))
0E230E230E23
select hex(weight_string(cast(_latin1 0x808080 as char) as char(2)));
hex(weight_string(cast(_latin1 0x808080 as char) as char(2)))
0E230E23
select hex(weight_string(cast(_latin1 0x808080 as char) as char(3)));
hex(weight_string(cast(_latin1 0x808080 as char) as char(3)))
0E230E230E23
select hex(weight_string(cast(_latin1 0x808080 as char) as char(5)));
hex(weight_string(cast(_latin1 0x808080 as char) as char(5)))
0E230E230E2302090209
select hex(weight_string(cast(_latin1 0x808080 as char), 1, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 1, 2, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x808080 as char), 2, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 2, 2, 0xC0))
0E23
select hex(weight_string(cast(_latin1 0x808080 as char), 3, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 3, 2, 0xC0))
0E230E
select hex(weight_string(cast(_latin1 0x808080 as char), 4, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 4, 2, 0xC0))
0E230E23
select hex(weight_string(cast(_latin1 0x808080 as char), 5, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 5, 2, 0xC0))
0E230E2302
select hex(weight_string(cast(_latin1 0x808080 as char),25, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char),25, 2, 0xC0))
0E230E23020902090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0x808080 as char), 1, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 1, 3, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x808080 as char), 2, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 2, 3, 0xC0))
0E23
select hex(weight_string(cast(_latin1 0x808080 as char), 3, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 3, 3, 0xC0))
0E230E
select hex(weight_string(cast(_latin1 0x808080 as char), 4, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 4, 3, 0xC0))
0E230E23
select hex(weight_string(cast(_latin1 0x808080 as char), 5, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 5, 3, 0xC0))
0E230E230E
select hex(weight_string(cast(_latin1 0x808080 as char),25, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char),25, 3, 0xC0))
0E230E230E2302090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0x808080 as char), 1, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 1, 4, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x808080 as char), 2, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 2, 4, 0xC0))
0E23
select hex(weight_string(cast(_latin1 0x808080 as char), 3, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 3, 4, 0xC0))
0E230E
select hex(weight_string(cast(_latin1 0x808080 as char), 4, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 4, 4, 0xC0))
0E230E23
select hex(weight_string(cast(_latin1 0x808080 as char), 5, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 5, 4, 0xC0))
0E230E230E
select hex(weight_string(cast(_latin1 0x808080 as char),25, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char),25, 4, 0xC0))
0E230E230E2302090209020902090209020902090209020902
select hex(weight_string(_utf32 0x10000 collate utf32_unicode_ci));
hex(weight_string(_utf32 0x10000 collate utf32_unicode_ci))
FFFD
select hex(weight_string(_utf32 0x10001 collate utf32_unicode_ci));
hex(weight_string(_utf32 0x10001 collate utf32_unicode_ci))
FFFD
set @@collation_connection=utf32_czech_ci;
select @@collation_connection;
@@collation_connection
utf32_czech_ci
select collation(cast(_latin1 0xDF as char));
collation(cast(_latin1 0xDF as char))
utf32_czech_ci
select hex(weight_string('s'));
hex(weight_string('s'))
0FEA
select hex(weight_string(cast(_latin1 0xDF as char)));
hex(weight_string(cast(_latin1 0xDF as char)))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF as char) as char(1)));
hex(weight_string(cast(_latin1 0xDF as char) as char(1)))
0FEA0FEA
select hex(weight_string('c'));
hex(weight_string('c'))
0E60
select hex(weight_string('h'));
hex(weight_string('h'))
0EE1
select hex(weight_string('ch'));
hex(weight_string('ch'))
0EE2
select hex(weight_string('i'));
hex(weight_string('i'))
0EFB
select hex(weight_string(cast(_latin1 0x6368DF as char)));
hex(weight_string(cast(_latin1 0x6368DF as char)))
0EE20FEA0FEA
select hex(weight_string(cast(_latin1 0x6368DF as char) as char(1)));
hex(weight_string(cast(_latin1 0x6368DF as char) as char(1)))
0E60
select hex(weight_string(cast(_latin1 0x6368DF as char) as char(2)));
hex(weight_string(cast(_latin1 0x6368DF as char) as char(2)))
0EE2
select hex(weight_string(cast(_latin1 0x6368DF as char) as char(3)));
hex(weight_string(cast(_latin1 0x6368DF as char) as char(3)))
0EE20FEA0FEA
select hex(weight_string(cast(_latin1 0x6368DF as char) as char(4)));
hex(weight_string(cast(_latin1 0x6368DF as char) as char(4)))
0EE20FEA0FEA0209
select hex(weight_string(cast(_latin1 0xDF6368 as char)));
hex(weight_string(cast(_latin1 0xDF6368 as char)))
0FEA0FEA0EE2
select hex(weight_string(cast(_latin1 0xDF6368 as char) as char(1)));
hex(weight_string(cast(_latin1 0xDF6368 as char) as char(1)))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char) as char(2)));
hex(weight_string(cast(_latin1 0xDF6368 as char) as char(2)))
0FEA0FEA0E60
select hex(weight_string(cast(_latin1 0xDF6368 as char) as char(3)));
hex(weight_string(cast(_latin1 0xDF6368 as char) as char(3)))
0FEA0FEA0EE2
select hex(weight_string(cast(_latin1 0xDF6368 as char) as char(4)));
hex(weight_string(cast(_latin1 0xDF6368 as char) as char(4)))
0FEA0FEA0EE20209
select hex(weight_string(cast(_latin1 0x6368DF as char), 1, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 1, 2, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x6368DF as char), 2, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 2, 2, 0xC0))
0EE2
select hex(weight_string(cast(_latin1 0x6368DF as char), 3, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 3, 2, 0xC0))
0EE202
select hex(weight_string(cast(_latin1 0x6368DF as char), 4, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 4, 2, 0xC0))
0EE20209
select hex(weight_string(cast(_latin1 0x6368DF as char),25, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char),25, 2, 0xC0))
0EE20209020902090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0x6368DF as char), 1, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 1, 3, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x6368DF as char), 2, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 2, 3, 0xC0))
0EE2
select hex(weight_string(cast(_latin1 0x6368DF as char), 3, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 3, 3, 0xC0))
0EE20F
select hex(weight_string(cast(_latin1 0x6368DF as char), 4, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 4, 3, 0xC0))
0EE20FEA
select hex(weight_string(cast(_latin1 0x6368DF as char),25, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char),25, 3, 0xC0))
0EE20FEA0FEA02090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0x6368DF as char), 1, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 1, 4, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x6368DF as char), 2, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 2, 4, 0xC0))
0EE2
select hex(weight_string(cast(_latin1 0x6368DF as char), 3, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 3, 4, 0xC0))
0EE20F
select hex(weight_string(cast(_latin1 0x6368DF as char), 4, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 4, 4, 0xC0))
0EE20FEA
select hex(weight_string(cast(_latin1 0x6368DF as char),25, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char),25, 4, 0xC0))
0EE20FEA0FEA02090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 2,0xC0))
0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 2,0xC0))
0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 2,0xC0))
0FEA0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 2,0xC0))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char),25, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char),25, 2,0xC0))
0FEA0FEA0E6002090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 3,0xC0))
0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 3,0xC0))
0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 3,0xC0))
0FEA0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 3,0xC0))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char),25, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char),25, 3,0xC0))
0FEA0FEA0EE202090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 4,0xC0))
0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 4,0xC0))
0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 4,0xC0))
0FEA0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 4,0xC0))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char),25, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char),25, 4,0xC0))
0FEA0FEA0EE202090209020902090209020902090209020902
SET NAMES utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SET collation_connection=utf32_german2_ci;
"BEGIN ctype_german.inc"
drop table if exists t1;
create table t1 as select repeat(' ', 64) as s1;
select collation(s1) from t1;
collation(s1)
utf32_german2_ci
delete from t1;
INSERT INTO t1 VALUES ('ud'),('uf');
INSERT INTO t1 VALUES ('od'),('of');
INSERT INTO t1 VALUES ('e');
INSERT INTO t1 VALUES ('ad'),('af');
insert into t1 values ('a'),('ae'),(_latin1 0xE4);
insert into t1 values ('o'),('oe'),(_latin1 0xF6);
insert into t1 values ('s'),('ss'),(_latin1 0xDF);
insert into t1 values ('u'),('ue'),(_latin1 0xFC);
INSERT INTO t1 VALUES (_latin1 0xE6), (_latin1 0xC6);
INSERT INTO t1 VALUES (_latin1 0x9C), (_latin1 0x8C);
select s1, hex(s1) from t1 order by s1, binary s1;
s1	hex(s1)
a	00000061
ad	0000006100000064
ae	0000006100000065
Æ	000000C6
ä	000000E4
æ	000000E6
af	0000006100000066
e	00000065
o	0000006F
od	0000006F00000064
oe	0000006F00000065
ö	000000F6
Œ	00000152
œ	00000153
of	0000006F00000066
s	00000073
ss	0000007300000073
ß	000000DF
u	00000075
ud	0000007500000064
ue	0000007500000065
ü	000000FC
uf	0000007500000066
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(s1 order by binary s1) from t1 group by s1;
group_concat(s1 order by binary s1)
a
ad
ae,Æ,ä,æ
af
e
o
od
oe,ö,Œ,œ
of
s
ss,ß
u
ud
ue,ü
uf
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
SELECT s1, hex(s1), hex(weight_string(s1)) FROM t1 ORDER BY s1, BINARY(s1);
s1	hex(s1)	hex(weight_string(s1))
a	00000061	0E33
ad	0000006100000064	0E330E6D
ae	0000006100000065	0E330E8B
Æ	000000C6	0E330E8B
ä	000000E4	0E330E8B
æ	000000E6	0E330E8B
af	0000006100000066	0E330EB9
e	00000065	0E8B
o	0000006F	0F82
od	0000006F00000064	0F820E6D
oe	0000006F00000065	0F820E8B
ö	000000F6	0F820E8B
Œ	00000152	0F820E8B
œ	00000153	0F820E8B
of	0000006F00000066	0F820EB9
s	00000073	0FEA
ss	0000007300000073	0FEA0FEA
ß	000000DF	0FEA0FEA
u	00000075	101F
ud	0000007500000064	101F0E6D
ue	0000007500000065	101F0E8B
ü	000000FC	101F0E8B
uf	0000007500000066	101F0EB9
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
SELECT s1, hex(s1) FROM t1 WHERE s1='ae' ORDER BY s1, BINARY(s1);
s1	hex(s1)
ae	0000006100000065
Æ	000000C6
ä	000000E4
æ	000000E6
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
drop table t1;
"END ctype_german.inc"
#
# WL#2673 Unicode Collation Algorithm new version
#
SET NAMES utf8mb4;
SET collation_connection=utf32_unicode_520_ci;
CREATE TABLE t1 AS SELECT repeat('a', 10) as c LIMIT 0;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c` varchar(10) CHARACTER SET utf32 COLLATE utf32_unicode_520_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES (_utf32 0x0180),(_utf32 0x023A);
INSERT INTO t1 VALUES (_utf32 0x023B),(_utf32 0x023C);
INSERT INTO t1 VALUES (_utf32 0x023D),(_utf32 0x023E);
INSERT INTO t1 VALUES (_utf32 0x0241),(_utf32 0x0242);
INSERT INTO t1 VALUES (_utf32 0x0243),(_utf32 0x0244);
INSERT INTO t1 VALUES (_utf32 0x0245),(_utf32 0x0246);
INSERT INTO t1 VALUES (_utf32 0x0247),(_utf32 0x0248);
INSERT INTO t1 VALUES (_utf32 0x0249),(_utf32 0x024A);
INSERT INTO t1 VALUES (_utf32 0x024B),(_utf32 0x024C);
INSERT INTO t1 VALUES (_utf32 0x024D),(_utf32 0x024E);
INSERT INTO t1 VALUES (_utf32 0x024F),(_utf32 0x026B);
INSERT INTO t1 VALUES (_utf32 0x027D),(_utf32 0x0289);
INSERT INTO t1 VALUES (_utf32 0x028C);
INSERT INTO t1 VALUES (_utf32 0x037B), (_utf32 0x037C);
INSERT INTO t1 VALUES (_utf32 0x037D), (_utf32 0x03FD);
INSERT INTO t1 VALUES (_utf32 0x03FE), (_utf32 0x03FF);
INSERT INTO t1 VALUES (_utf32 0x04C0), (_utf32 0x04CF);
INSERT INTO t1 VALUES (_utf32 0x04F6), (_utf32 0x04F7);
INSERT INTO t1 VALUES (_utf32 0x04FA), (_utf32 0x04FB);
INSERT INTO t1 VALUES (_utf32 0x04FC), (_utf32 0x04FD);
INSERT INTO t1 VALUES (_utf32 0x04FE), (_utf32 0x04FF);
INSERT INTO t1 VALUES (_utf32 0x0510), (_utf32 0x0511);
INSERT INTO t1 VALUES (_utf32 0x0512), (_utf32 0x0513);
INSERT INTO t1 VALUES (_utf32 0x10A0), (_utf32 0x10A1);
INSERT INTO t1 VALUES (_utf32 0x10A2), (_utf32 0x10A3);
INSERT INTO t1 VALUES (_utf32 0x10A4), (_utf32 0x10A5);
INSERT INTO t1 VALUES (_utf32 0x10A6), (_utf32 0x10A7);
INSERT INTO t1 VALUES (_utf32 0x2D00), (_utf32 0x2D01);
INSERT INTO t1 VALUES (_utf32 0x2D02), (_utf32 0x2D03);
INSERT INTO t1 VALUES (_utf32 0x2D04), (_utf32 0x2D05);
INSERT INTO t1 VALUES (_utf32 0x2D06), (_utf32 0x2D07);
INSERT INTO t1 VALUES (_utf32 0x1D7D);
INSERT INTO t1 VALUES (_utf32 0x2132),(_utf32 0x214E);
INSERT INTO t1 VALUES (_utf32 0x2183),(_utf32 0x2184);
INSERT INTO t1 VALUES (_utf32 0x2C80), (_utf32 0x2C81);
INSERT INTO t1 VALUES (_utf32 0x2C82), (_utf32 0x2C83);
INSERT INTO t1 VALUES (_utf32 0x2C84), (_utf32 0x2C85);
INSERT INTO t1 VALUES (_utf32 0x2C86), (_utf32 0x2C87);
INSERT INTO t1 VALUES (_utf32 0x2C88), (_utf32 0x2C89);
INSERT INTO t1 VALUES (_utf32 0x2C8A), (_utf32 0x2C8B);
INSERT INTO t1 VALUES (_utf32 0x2C8C), (_utf32 0x2C8D);
INSERT INTO t1 VALUES (_utf32 0x2C8E), (_utf32 0x2C8F);
INSERT INTO t1 VALUES (_utf32 0x2C60), (_utf32 0x2C61);
INSERT INTO t1 VALUES (_utf32 0x2C62), (_utf32 0x2C63);
INSERT INTO t1 VALUES (_utf32 0x2C64), (_utf32 0x2C65);
INSERT INTO t1 VALUES (_utf32 0x2C66), (_utf32 0x2C67);
INSERT INTO t1 VALUES (_utf32 0x2C68), (_utf32 0x2C69);
INSERT INTO t1 VALUES (_utf32 0x2C6A), (_utf32 0x2C6B);
INSERT INTO t1 VALUES (_utf32 0x2C6C), (_utf32 0x2C75);
INSERT INTO t1 VALUES (_utf32 0x2C76);
INSERT INTO t1 VALUES (_utf32 0x2C00), (_utf32 0x2C01);
INSERT INTO t1 VALUES (_utf32 0x2C02), (_utf32 0x2C03);
INSERT INTO t1 VALUES (_utf32 0x2C04), (_utf32 0x2C05);
INSERT INTO t1 VALUES (_utf32 0x2C06), (_utf32 0x2C07);
INSERT INTO t1 VALUES (_utf32 0x2C30), (_utf32 0x2C31);
INSERT INTO t1 VALUES (_utf32 0x2C32), (_utf32 0x2C33);
INSERT INTO t1 VALUES (_utf32 0x2C34), (_utf32 0x2C35);
INSERT INTO t1 VALUES (_utf32 0x2C36), (_utf32 0x2C37);
INSERT INTO t1 VALUES (_utf32 0x10400), (_utf32 0x10401);
INSERT INTO t1 VALUES (_utf32 0x10402), (_utf32 0x10403);
INSERT INTO t1 VALUES (_utf32 0x10404), (_utf32 0x10405);
INSERT INTO t1 VALUES (_utf32 0x10406), (_utf32 0x10407);
INSERT INTO t1 VALUES (_utf32 0x10428), (_utf32 0x10429);
INSERT INTO t1 VALUES (_utf32 0x1042A), (_utf32 0x1042B);
INSERT INTO t1 VALUES (_utf32 0x1042C), (_utf32 0x1042D);
INSERT INTO t1 VALUES (_utf32 0x1042E), (_utf32 0x1042F);
INSERT INTO t1 VALUES (_utf32 0x0370);
INSERT INTO t1 VALUES (_utf32 0x0371);
INSERT INTO t1 VALUES (_utf32 0x0372);
INSERT INTO t1 VALUES (_utf32 0x0373);
INSERT INTO t1 VALUES (_utf32 0x0514);
INSERT INTO t1 VALUES (_utf32 0x0515);
INSERT INTO t1 VALUES (_utf32 0x0516);
INSERT INTO t1 VALUES (_utf32 0x0517);
INSERT INTO t1 VALUES (_utf32 0xA640);
INSERT INTO t1 VALUES (_utf32 0xA641);
INSERT INTO t1 VALUES (_utf32 0xA642);
INSERT INTO t1 VALUES (_utf32 0xA643);
INSERT INTO t1 VALUES (_utf32 0xA722);
INSERT INTO t1 VALUES (_utf32 0xA723);
INSERT INTO t1 VALUES (_utf32 0xA724);
INSERT INTO t1 VALUES (_utf32 0xA725);
INSERT INTO t1 VALUES (_utf32 0xA726);
INSERT INTO t1 VALUES (_utf32 0xA727);
INSERT INTO t1 VALUES (_utf32 0xA728);
INSERT INTO t1 VALUES (_utf32 0xA729);
INSERT INTO t1 VALUES (_utf32 0xA72A);
INSERT INTO t1 VALUES (_utf32 0xA72B);
INSERT INTO t1 VALUES (_utf32 0x2CEB);
INSERT INTO t1 VALUES (_utf32 0x2CEC);
INSERT INTO t1 VALUES (_utf32 0x2CED);
INSERT INTO t1 VALUES (_utf32 0x2CEE);
SELECT hex(c), hex(lower(c)), hex(upper(c)), hex(weight_string(c)), c
FROM t1 ORDER BY c, BINARY c;
hex(c)	hex(lower(c))	hex(upper(c))	hex(weight_string(c))	c
0000023A	00002C65	0000023A	1214	Ⱥ
00002C65	00002C65	0000023A	1214	ⱥ
00000180	00000180	00000243	122D	ƀ
00000243	00000180	00000243	122D	Ƀ
0000023B	0000023C	0000023B	1242	Ȼ
0000023C	0000023C	0000023B	1242	ȼ
00002183	00002184	00002183	124E	Ↄ
00002184	00002184	00002183	124E	ↄ
00000246	00000247	00000246	1270	Ɇ
00000247	00000247	00000246	1270	ɇ
00002132	0000214E	00002132	12AE	Ⅎ
0000214E	0000214E	00002132	12AE	ⅎ
00002C67	00002C68	00002C67	12E3	Ⱨ
00002C68	00002C68	00002C67	12E3	ⱨ
00002C75	00002C76	00002C75	12E4	Ⱶ
00002C76	00002C76	00002C75	12E4	ⱶ
0000A726	0000A727	0000A726	12E5	Ꜧ
0000A727	0000A727	0000A726	12E5	ꜧ
00000248	00000249	00000248	130E	Ɉ
00000249	00000249	00000248	130E	ɉ
00002C69	00002C6A	00002C69	1328	Ⱪ
00002C6A	00002C6A	00002C69	1328	ⱪ
0000023D	0000019A	0000023D	133B	Ƚ
00002C60	00002C61	00002C60	133F	Ⱡ
00002C61	00002C61	00002C60	133F	ⱡ
0000026B	0000026B	00002C62	1340	ɫ
00002C62	0000026B	00002C62	1340	Ɫ
00001D7D	00001D7D	00002C63	13B8	ᵽ
00002C63	00001D7D	00002C63	13B8	Ᵽ
0000024A	0000024B	0000024A	13D2	Ɋ
0000024B	0000024B	0000024A	13D2	ɋ
0000024C	0000024D	0000024C	13E4	Ɍ
0000024D	0000024D	0000024C	13E4	ɍ
0000027D	0000027D	00002C64	13FC	ɽ
00002C64	0000027D	00002C64	13FC	Ɽ
0000A728	0000A729	0000A728	143314AD	Ꜩ
0000A729	0000A729	0000A728	143314AD	ꜩ
0000023E	00002C66	0000023E	143C	Ⱦ
00002C66	00002C66	0000023E	143C	ⱦ
00000244	00000289	00000244	145B	Ʉ
00000289	00000289	00000244	145B	ʉ
00000245	0000028C	00000245	1489	Ʌ
0000028C	0000028C	00000245	1489	ʌ
0000024E	0000024F	0000024E	14A4	Ɏ
0000024F	0000024F	0000024E	14A4	ɏ
00002C6B	00002C6C	00002C6B	14C8	Ⱬ
00002C6C	00002C6C	00002C6B	14C8	ⱬ
0000A72A	0000A72B	0000A72A	14F3	Ꜫ
0000A72B	0000A72B	0000A72A	14F3	ꜫ
00000241	00000242	00000241	1506	Ɂ
00000242	00000242	00000241	1506	ɂ
0000A722	0000A723	0000A722	150E	Ꜣ
0000A723	0000A723	0000A722	150E	ꜣ
0000A724	0000A725	0000A724	1518	Ꜥ
0000A725	0000A725	0000A724	1518	ꜥ
00000370	00000371	00000370	154F	Ͱ
00000371	00000371	00000370	154F	ͱ
0000037C	0000037C	000003FE	1564	ͼ
000003FE	0000037C	000003FE	1564	Ͼ
0000037B	0000037B	000003FD	1565	ͻ
000003FD	0000037B	000003FD	1565	Ͻ
0000037D	0000037D	000003FF	1566	ͽ
000003FF	0000037D	000003FF	1566	Ͽ
00000372	00000373	00000372	156F	Ͳ
00000373	00000373	00000372	156F	ͳ
00002C80	00002C81	00002C80	1571	Ⲁ
00002C81	00002C81	00002C80	1571	ⲁ
00002C82	00002C83	00002C82	1572	Ⲃ
00002C83	00002C83	00002C82	1572	ⲃ
00002C84	00002C85	00002C84	1573	Ⲅ
00002C85	00002C85	00002C84	1573	ⲅ
00002C86	00002C87	00002C86	1574	Ⲇ
00002C87	00002C87	00002C86	1574	ⲇ
00002C88	00002C89	00002C88	1575	Ⲉ
00002C89	00002C89	00002C88	1575	ⲉ
00002C8A	00002C8B	00002C8A	1577	Ⲋ
00002C8B	00002C8B	00002C8A	1577	ⲋ
00002C8C	00002C8D	00002C8C	1578	Ⲍ
00002C8D	00002C8D	00002C8C	1578	ⲍ
00002C8E	00002C8F	00002C8E	1579	Ⲏ
00002C8F	00002C8F	00002C8E	1579	ⲏ
00002CEB	00002CEC	00002CEB	1591	Ⳬ
00002CEC	00002CEC	00002CEB	1591	ⳬ
00002CED	00002CEE	00002CED	15A0	Ⳮ
00002CEE	00002CEE	00002CED	15A0	ⳮ
000004FA	000004FB	000004FA	15D4	Ӻ
000004FB	000004FB	000004FA	15D4	ӻ
000004F6	000004F7	000004F6	15DC	Ӷ
000004F7	000004F7	000004F6	15DC	ӷ
0000A640	0000A641	0000A640	1611	Ꙁ
0000A641	0000A641	0000A640	1611	ꙁ
00000510	00000511	00000510	1613	Ԑ
00000511	00000511	00000510	1613	ԑ
0000A642	0000A643	0000A642	1618	Ꙃ
0000A643	0000A643	0000A642	1618	ꙃ
00000512	00000513	00000512	1666	Ԓ
00000513	00000513	00000512	1666	ԓ
00000514	00000515	00000514	166E	Ԕ
00000515	00000515	00000514	166E	ԕ
00000516	00000517	00000516	16B7	Ԗ
00000517	00000517	00000516	16B7	ԗ
000004FC	000004FD	000004FC	16F9	Ӽ
000004FD	000004FD	000004FC	16F9	ӽ
000004FE	000004FF	000004FE	16FD	Ӿ
000004FF	000004FF	000004FE	16FD	ӿ
000004C0	000004CF	000004C0	17B1	Ӏ
000004CF	000004CF	000004C0	17B1	ӏ
00002C00	00002C30	00002C00	17B5	Ⰰ
00002C30	00002C30	00002C00	17B5	ⰰ
00002C01	00002C31	00002C01	17B6	Ⰱ
00002C31	00002C31	00002C01	17B6	ⰱ
00002C02	00002C32	00002C02	17B7	Ⰲ
00002C32	00002C32	00002C02	17B7	ⰲ
00002C03	00002C33	00002C03	17B8	Ⰳ
00002C33	00002C33	00002C03	17B8	ⰳ
00002C04	00002C34	00002C04	17B9	Ⰴ
00002C34	00002C34	00002C04	17B9	ⰴ
00002C05	00002C35	00002C05	17BA	Ⰵ
00002C35	00002C35	00002C05	17BA	ⰵ
00002C06	00002C36	00002C06	17BB	Ⰶ
00002C36	00002C36	00002C06	17BB	ⰶ
00002C07	00002C37	00002C07	17BC	Ⰷ
00002C37	00002C37	00002C07	17BC	ⰷ
000010A0	00002D00	000010A0	17E5	Ⴀ
00002D00	00002D00	000010A0	17E5	ⴀ
000010A1	00002D01	000010A1	17E7	Ⴁ
00002D01	00002D01	000010A1	17E7	ⴁ
000010A2	00002D02	000010A2	17E9	Ⴂ
00002D02	00002D02	000010A2	17E9	ⴂ
000010A3	00002D03	000010A3	17EB	Ⴃ
00002D03	00002D03	000010A3	17EB	ⴃ
000010A4	00002D04	000010A4	17ED	Ⴄ
00002D04	00002D04	000010A4	17ED	ⴄ
000010A5	00002D05	000010A5	17EF	Ⴅ
00002D05	00002D05	000010A5	17EF	ⴅ
000010A6	00002D06	000010A6	17F1	Ⴆ
00002D06	00002D06	000010A6	17F1	ⴆ
000010A7	00002D07	000010A7	17F5	Ⴇ
00002D07	00002D07	000010A7	17F5	ⴇ
00010400	00010428	00010400	30D2	𐐀
00010428	00010428	00010400	30D2	𐐨
00010401	00010429	00010401	30D3	𐐁
00010429	00010429	00010401	30D3	𐐩
00010402	0001042A	00010402	30D4	𐐂
0001042A	0001042A	00010402	30D4	𐐪
00010403	0001042B	00010403	30D5	𐐃
0001042B	0001042B	00010403	30D5	𐐫
00010404	0001042C	00010404	30D6	𐐄
0001042C	0001042C	00010404	30D6	𐐬
00010405	0001042D	00010405	30D7	𐐅
0001042D	0001042D	00010405	30D7	𐐭
00010406	0001042E	00010406	30D8	𐐆
0001042E	0001042E	00010406	30D8	𐐮
00010407	0001042F	00010407	30D9	𐐇
0001042F	0001042F	00010407	30D9	𐐯
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
INSERT INTO t1 VALUES ('a');
INSERT INTO t1 VALUES (concat(_utf32 0x61, _utf32 0xFFFF));
INSERT INTO t1 VALUES (concat(_utf32 0x61, _utf32 0x10FFFF));
INSERT INTO t1 VALUES (concat(_utf32 0x61, _utf32 0x10400));
SELECT hex(c), hex(weight_string(c)) FROM t1 WHERE c LIKE 'a%' ORDER BY c;
hex(c)	hex(weight_string(c))
00000061	120F
0000006100010400	120F30D2
000000610000FFFF	120FFBC1FFFF
000000610010FFFF	120FFBE1FFFF
SELECT hex(c), hex(weight_string(c)), c FROM t1 WHERE c LIKE _utf32 0x10400 ORDER BY c, BINARY c;
hex(c)	hex(weight_string(c))	c
00010400	30D2	𐐀
00010428	30D2	𐐨
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
SELECT hex(c), hex(weight_string(c)), c FROM t1 WHERE c LIKE _utf32 0x10428 ORDER BY c, BINARY c;
hex(c)	hex(weight_string(c))	c
00010400	30D2	𐐀
00010428	30D2	𐐨
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
ALTER TABLE t1 ADD KEY(c);
EXPLAIN SELECT hex(c) FROM t1 WHERE c LIKE 'a%' ORDER BY c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	c	c	43	NULL	4	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select hex(`test`.`t1`.`c`) AS `hex(c)` from `test`.`t1` where (`test`.`t1`.`c` like 'a%') order by `test`.`t1`.`c`
SELECT hex(c), hex(weight_string(c)) FROM t1 WHERE c LIKE 'a%' ORDER BY c;
hex(c)	hex(weight_string(c))
00000061	120F
0000006100010400	120F30D2
000000610000FFFF	120FFBC1FFFF
000000610010FFFF	120FFBE1FFFF
SELECT hex(c), hex(weight_string(c)), c FROM t1 WHERE c LIKE _utf32 0x10400 ORDER BY c, BINARY c;
hex(c)	hex(weight_string(c))	c
00010400	30D2	𐐀
00010428	30D2	𐐨
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
SELECT hex(c), hex(weight_string(c)), c FROM t1 WHERE c LIKE _utf32 0x10428 ORDER BY c, BINARY c;
hex(c)	hex(weight_string(c))	c
00010400	30D2	𐐀
00010428	30D2	𐐨
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
DROP TABLE t1;
#
# End of 5.6 tests
#
