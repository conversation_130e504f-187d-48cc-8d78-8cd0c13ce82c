// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: csharp/protos/map_unittest_proto3.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Google.Protobuf.TestProtos {

  /// <summary>Holder for reflection information generated from csharp/protos/map_unittest_proto3.proto</summary>
  public static partial class MapUnittestProto3Reflection {

    #region Descriptor
    /// <summary>File descriptor for csharp/protos/map_unittest_proto3.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static MapUnittestProto3Reflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cidjc2hhcnAvcHJvdG9zL21hcF91bml0dGVzdF9wcm90bzMucHJvdG8SEnBy",
            "b3RvYnVmX3VuaXR0ZXN0MxojY3NoYXJwL3Byb3Rvcy91bml0dGVzdF9wcm90",
            "bzMucHJvdG8iqRIKB1Rlc3RNYXASRwoPbWFwX2ludDMyX2ludDMyGAEgAygL",
            "Mi4ucHJvdG9idWZfdW5pdHRlc3QzLlRlc3RNYXAuTWFwSW50MzJJbnQzMkVu",
            "dHJ5EkcKD21hcF9pbnQ2NF9pbnQ2NBgCIAMoCzIuLnByb3RvYnVmX3VuaXR0",
            "ZXN0My5UZXN0TWFwLk1hcEludDY0SW50NjRFbnRyeRJLChFtYXBfdWludDMy",
            "X3VpbnQzMhgDIAMoCzIwLnByb3RvYnVmX3VuaXR0ZXN0My5UZXN0TWFwLk1h",
            "cFVpbnQzMlVpbnQzMkVudHJ5EksKEW1hcF91aW50NjRfdWludDY0GAQgAygL",
            "MjAucHJvdG9idWZfdW5pdHRlc3QzLlRlc3RNYXAuTWFwVWludDY0VWludDY0",
            "RW50cnkSSwoRbWFwX3NpbnQzMl9zaW50MzIYBSADKAsyMC5wcm90b2J1Zl91",
            "bml0dGVzdDMuVGVzdE1hcC5NYXBTaW50MzJTaW50MzJFbnRyeRJLChFtYXBf",
            "c2ludDY0X3NpbnQ2NBgGIAMoCzIwLnByb3RvYnVmX3VuaXR0ZXN0My5UZXN0",
            "TWFwLk1hcFNpbnQ2NFNpbnQ2NEVudHJ5Ek8KE21hcF9maXhlZDMyX2ZpeGVk",
            "MzIYByADKAsyMi5wcm90b2J1Zl91bml0dGVzdDMuVGVzdE1hcC5NYXBGaXhl",
            "ZDMyRml4ZWQzMkVudHJ5Ek8KE21hcF9maXhlZDY0X2ZpeGVkNjQYCCADKAsy",
            "Mi5wcm90b2J1Zl91bml0dGVzdDMuVGVzdE1hcC5NYXBGaXhlZDY0Rml4ZWQ2",
            "NEVudHJ5ElMKFW1hcF9zZml4ZWQzMl9zZml4ZWQzMhgJIAMoCzI0LnByb3Rv",
            "YnVmX3VuaXR0ZXN0My5UZXN0TWFwLk1hcFNmaXhlZDMyU2ZpeGVkMzJFbnRy",
            "eRJTChVtYXBfc2ZpeGVkNjRfc2ZpeGVkNjQYCiADKAsyNC5wcm90b2J1Zl91",
            "bml0dGVzdDMuVGVzdE1hcC5NYXBTZml4ZWQ2NFNmaXhlZDY0RW50cnkSRwoP",
            "bWFwX2ludDMyX2Zsb2F0GAsgAygLMi4ucHJvdG9idWZfdW5pdHRlc3QzLlRl",
            "c3RNYXAuTWFwSW50MzJGbG9hdEVudHJ5EkkKEG1hcF9pbnQzMl9kb3VibGUY",
            "DCADKAsyLy5wcm90b2J1Zl91bml0dGVzdDMuVGVzdE1hcC5NYXBJbnQzMkRv",
            "dWJsZUVudHJ5EkMKDW1hcF9ib29sX2Jvb2wYDSADKAsyLC5wcm90b2J1Zl91",
            "bml0dGVzdDMuVGVzdE1hcC5NYXBCb29sQm9vbEVudHJ5EksKEW1hcF9zdHJp",
            "bmdfc3RyaW5nGA4gAygLMjAucHJvdG9idWZfdW5pdHRlc3QzLlRlc3RNYXAu",
            "TWFwU3RyaW5nU3RyaW5nRW50cnkSRwoPbWFwX2ludDMyX2J5dGVzGA8gAygL",
            "Mi4ucHJvdG9idWZfdW5pdHRlc3QzLlRlc3RNYXAuTWFwSW50MzJCeXRlc0Vu",
            "dHJ5EkUKDm1hcF9pbnQzMl9lbnVtGBAgAygLMi0ucHJvdG9idWZfdW5pdHRl",
            "c3QzLlRlc3RNYXAuTWFwSW50MzJFbnVtRW50cnkSWgoZbWFwX2ludDMyX2Zv",
            "cmVpZ25fbWVzc2FnZRgRIAMoCzI3LnByb3RvYnVmX3VuaXR0ZXN0My5UZXN0",
            "TWFwLk1hcEludDMyRm9yZWlnbk1lc3NhZ2VFbnRyeRo0ChJNYXBJbnQzMklu",
            "dDMyRW50cnkSCwoDa2V5GAEgASgFEg0KBXZhbHVlGAIgASgFOgI4ARo0ChJN",
            "YXBJbnQ2NEludDY0RW50cnkSCwoDa2V5GAEgASgDEg0KBXZhbHVlGAIgASgD",
            "OgI4ARo2ChRNYXBVaW50MzJVaW50MzJFbnRyeRILCgNrZXkYASABKA0SDQoF",
            "dmFsdWUYAiABKA06AjgBGjYKFE1hcFVpbnQ2NFVpbnQ2NEVudHJ5EgsKA2tl",
            "eRgBIAEoBBINCgV2YWx1ZRgCIAEoBDoCOAEaNgoUTWFwU2ludDMyU2ludDMy",
            "RW50cnkSCwoDa2V5GAEgASgREg0KBXZhbHVlGAIgASgROgI4ARo2ChRNYXBT",
            "aW50NjRTaW50NjRFbnRyeRILCgNrZXkYASABKBISDQoFdmFsdWUYAiABKBI6",
            "AjgBGjgKFk1hcEZpeGVkMzJGaXhlZDMyRW50cnkSCwoDa2V5GAEgASgHEg0K",
            "BXZhbHVlGAIgASgHOgI4ARo4ChZNYXBGaXhlZDY0Rml4ZWQ2NEVudHJ5EgsK",
            "A2tleRgBIAEoBhINCgV2YWx1ZRgCIAEoBjoCOAEaOgoYTWFwU2ZpeGVkMzJT",
            "Zml4ZWQzMkVudHJ5EgsKA2tleRgBIAEoDxINCgV2YWx1ZRgCIAEoDzoCOAEa",
            "OgoYTWFwU2ZpeGVkNjRTZml4ZWQ2NEVudHJ5EgsKA2tleRgBIAEoEBINCgV2",
            "YWx1ZRgCIAEoEDoCOAEaNAoSTWFwSW50MzJGbG9hdEVudHJ5EgsKA2tleRgB",
            "IAEoBRINCgV2YWx1ZRgCIAEoAjoCOAEaNQoTTWFwSW50MzJEb3VibGVFbnRy",
            "eRILCgNrZXkYASABKAUSDQoFdmFsdWUYAiABKAE6AjgBGjIKEE1hcEJvb2xC",
            "b29sRW50cnkSCwoDa2V5GAEgASgIEg0KBXZhbHVlGAIgASgIOgI4ARo2ChRN",
            "YXBTdHJpbmdTdHJpbmdFbnRyeRILCgNrZXkYASABKAkSDQoFdmFsdWUYAiAB",
            "KAk6AjgBGjQKEk1hcEludDMyQnl0ZXNFbnRyeRILCgNrZXkYASABKAUSDQoF",
            "dmFsdWUYAiABKAw6AjgBGlAKEU1hcEludDMyRW51bUVudHJ5EgsKA2tleRgB",
            "IAEoBRIqCgV2YWx1ZRgCIAEoDjIbLnByb3RvYnVmX3VuaXR0ZXN0My5NYXBF",
            "bnVtOgI4ARphChtNYXBJbnQzMkZvcmVpZ25NZXNzYWdlRW50cnkSCwoDa2V5",
            "GAEgASgFEjEKBXZhbHVlGAIgASgLMiIucHJvdG9idWZfdW5pdHRlc3QzLkZv",
            "cmVpZ25NZXNzYWdlOgI4ASJCChFUZXN0TWFwU3VibWVzc2FnZRItCgh0ZXN0",
            "X21hcBgBIAEoCzIbLnByb3RvYnVmX3VuaXR0ZXN0My5UZXN0TWFwIr4BCg5U",
            "ZXN0TWVzc2FnZU1hcBJSChFtYXBfaW50MzJfbWVzc2FnZRgBIAMoCzI3LnBy",
            "b3RvYnVmX3VuaXR0ZXN0My5UZXN0TWVzc2FnZU1hcC5NYXBJbnQzMk1lc3Nh",
            "Z2VFbnRyeRpYChRNYXBJbnQzMk1lc3NhZ2VFbnRyeRILCgNrZXkYASABKAUS",
            "LwoFdmFsdWUYAiABKAsyIC5wcm90b2J1Zl91bml0dGVzdDMuVGVzdEFsbFR5",
            "cGVzOgI4ASLlAQoPVGVzdFNhbWVUeXBlTWFwEjsKBG1hcDEYASADKAsyLS5w",
            "cm90b2J1Zl91bml0dGVzdDMuVGVzdFNhbWVUeXBlTWFwLk1hcDFFbnRyeRI7",
            "CgRtYXAyGAIgAygLMi0ucHJvdG9idWZfdW5pdHRlc3QzLlRlc3RTYW1lVHlw",
            "ZU1hcC5NYXAyRW50cnkaKwoJTWFwMUVudHJ5EgsKA2tleRgBIAEoBRINCgV2",
            "YWx1ZRgCIAEoBToCOAEaKwoJTWFwMkVudHJ5EgsKA2tleRgBIAEoBRINCgV2",
            "YWx1ZRgCIAEoBToCOAEi9RAKDFRlc3RBcmVuYU1hcBJMCg9tYXBfaW50MzJf",
            "aW50MzIYASADKAsyMy5wcm90b2J1Zl91bml0dGVzdDMuVGVzdEFyZW5hTWFw",
            "Lk1hcEludDMySW50MzJFbnRyeRJMCg9tYXBfaW50NjRfaW50NjQYAiADKAsy",
            "My5wcm90b2J1Zl91bml0dGVzdDMuVGVzdEFyZW5hTWFwLk1hcEludDY0SW50",
            "NjRFbnRyeRJQChFtYXBfdWludDMyX3VpbnQzMhgDIAMoCzI1LnByb3RvYnVm",
            "X3VuaXR0ZXN0My5UZXN0QXJlbmFNYXAuTWFwVWludDMyVWludDMyRW50cnkS",
            "UAoRbWFwX3VpbnQ2NF91aW50NjQYBCADKAsyNS5wcm90b2J1Zl91bml0dGVz",
            "dDMuVGVzdEFyZW5hTWFwLk1hcFVpbnQ2NFVpbnQ2NEVudHJ5ElAKEW1hcF9z",
            "aW50MzJfc2ludDMyGAUgAygLMjUucHJvdG9idWZfdW5pdHRlc3QzLlRlc3RB",
            "cmVuYU1hcC5NYXBTaW50MzJTaW50MzJFbnRyeRJQChFtYXBfc2ludDY0X3Np",
            "bnQ2NBgGIAMoCzI1LnByb3RvYnVmX3VuaXR0ZXN0My5UZXN0QXJlbmFNYXAu",
            "TWFwU2ludDY0U2ludDY0RW50cnkSVAoTbWFwX2ZpeGVkMzJfZml4ZWQzMhgH",
            "IAMoCzI3LnByb3RvYnVmX3VuaXR0ZXN0My5UZXN0QXJlbmFNYXAuTWFwRml4",
            "ZWQzMkZpeGVkMzJFbnRyeRJUChNtYXBfZml4ZWQ2NF9maXhlZDY0GAggAygL",
            "MjcucHJvdG9idWZfdW5pdHRlc3QzLlRlc3RBcmVuYU1hcC5NYXBGaXhlZDY0",
            "Rml4ZWQ2NEVudHJ5ElgKFW1hcF9zZml4ZWQzMl9zZml4ZWQzMhgJIAMoCzI5",
            "LnByb3RvYnVmX3VuaXR0ZXN0My5UZXN0QXJlbmFNYXAuTWFwU2ZpeGVkMzJT",
            "Zml4ZWQzMkVudHJ5ElgKFW1hcF9zZml4ZWQ2NF9zZml4ZWQ2NBgKIAMoCzI5",
            "LnByb3RvYnVmX3VuaXR0ZXN0My5UZXN0QXJlbmFNYXAuTWFwU2ZpeGVkNjRT",
            "Zml4ZWQ2NEVudHJ5EkwKD21hcF9pbnQzMl9mbG9hdBgLIAMoCzIzLnByb3Rv",
            "YnVmX3VuaXR0ZXN0My5UZXN0QXJlbmFNYXAuTWFwSW50MzJGbG9hdEVudHJ5",
            "Ek4KEG1hcF9pbnQzMl9kb3VibGUYDCADKAsyNC5wcm90b2J1Zl91bml0dGVz",
            "dDMuVGVzdEFyZW5hTWFwLk1hcEludDMyRG91YmxlRW50cnkSSAoNbWFwX2Jv",
            "b2xfYm9vbBgNIAMoCzIxLnByb3RvYnVmX3VuaXR0ZXN0My5UZXN0QXJlbmFN",
            "YXAuTWFwQm9vbEJvb2xFbnRyeRJKCg5tYXBfaW50MzJfZW51bRgOIAMoCzIy",
            "LnByb3RvYnVmX3VuaXR0ZXN0My5UZXN0QXJlbmFNYXAuTWFwSW50MzJFbnVt",
            "RW50cnkSXwoZbWFwX2ludDMyX2ZvcmVpZ25fbWVzc2FnZRgPIAMoCzI8LnBy",
            "b3RvYnVmX3VuaXR0ZXN0My5UZXN0QXJlbmFNYXAuTWFwSW50MzJGb3JlaWdu",
            "TWVzc2FnZUVudHJ5GjQKEk1hcEludDMySW50MzJFbnRyeRILCgNrZXkYASAB",
            "KAUSDQoFdmFsdWUYAiABKAU6AjgBGjQKEk1hcEludDY0SW50NjRFbnRyeRIL",
            "CgNrZXkYASABKAMSDQoFdmFsdWUYAiABKAM6AjgBGjYKFE1hcFVpbnQzMlVp",
            "bnQzMkVudHJ5EgsKA2tleRgBIAEoDRINCgV2YWx1ZRgCIAEoDToCOAEaNgoU",
            "TWFwVWludDY0VWludDY0RW50cnkSCwoDa2V5GAEgASgEEg0KBXZhbHVlGAIg",
            "ASgEOgI4ARo2ChRNYXBTaW50MzJTaW50MzJFbnRyeRILCgNrZXkYASABKBES",
            "DQoFdmFsdWUYAiABKBE6AjgBGjYKFE1hcFNpbnQ2NFNpbnQ2NEVudHJ5EgsK",
            "A2tleRgBIAEoEhINCgV2YWx1ZRgCIAEoEjoCOAEaOAoWTWFwRml4ZWQzMkZp",
            "eGVkMzJFbnRyeRILCgNrZXkYASABKAcSDQoFdmFsdWUYAiABKAc6AjgBGjgK",
            "Fk1hcEZpeGVkNjRGaXhlZDY0RW50cnkSCwoDa2V5GAEgASgGEg0KBXZhbHVl",
            "GAIgASgGOgI4ARo6ChhNYXBTZml4ZWQzMlNmaXhlZDMyRW50cnkSCwoDa2V5",
            "GAEgASgPEg0KBXZhbHVlGAIgASgPOgI4ARo6ChhNYXBTZml4ZWQ2NFNmaXhl",
            "ZDY0RW50cnkSCwoDa2V5GAEgASgQEg0KBXZhbHVlGAIgASgQOgI4ARo0ChJN",
            "YXBJbnQzMkZsb2F0RW50cnkSCwoDa2V5GAEgASgFEg0KBXZhbHVlGAIgASgC",
            "OgI4ARo1ChNNYXBJbnQzMkRvdWJsZUVudHJ5EgsKA2tleRgBIAEoBRINCgV2",
            "YWx1ZRgCIAEoAToCOAEaMgoQTWFwQm9vbEJvb2xFbnRyeRILCgNrZXkYASAB",
            "KAgSDQoFdmFsdWUYAiABKAg6AjgBGlAKEU1hcEludDMyRW51bUVudHJ5EgsK",
            "A2tleRgBIAEoBRIqCgV2YWx1ZRgCIAEoDjIbLnByb3RvYnVmX3VuaXR0ZXN0",
            "My5NYXBFbnVtOgI4ARphChtNYXBJbnQzMkZvcmVpZ25NZXNzYWdlRW50cnkS",
            "CwoDa2V5GAEgASgFEjEKBXZhbHVlGAIgASgLMiIucHJvdG9idWZfdW5pdHRl",
            "c3QzLkZvcmVpZ25NZXNzYWdlOgI4ASLmAQofTWVzc2FnZUNvbnRhaW5pbmdF",
            "bnVtQ2FsbGVkVHlwZRJLCgR0eXBlGAEgAygLMj0ucHJvdG9idWZfdW5pdHRl",
            "c3QzLk1lc3NhZ2VDb250YWluaW5nRW51bUNhbGxlZFR5cGUuVHlwZUVudHJ5",
            "GmAKCVR5cGVFbnRyeRILCgNrZXkYASABKAUSQgoFdmFsdWUYAiABKAsyMy5w",
            "cm90b2J1Zl91bml0dGVzdDMuTWVzc2FnZUNvbnRhaW5pbmdFbnVtQ2FsbGVk",
            "VHlwZToCOAEiFAoEVHlwZRIMCghUWVBFX0ZPTxAAIp4BCh9NZXNzYWdlQ29u",
            "dGFpbmluZ01hcENhbGxlZEVudHJ5Ek0KBWVudHJ5GAEgAygLMj4ucHJvdG9i",
            "dWZfdW5pdHRlc3QzLk1lc3NhZ2VDb250YWluaW5nTWFwQ2FsbGVkRW50cnku",
            "RW50cnlFbnRyeRosCgpFbnRyeUVudHJ5EgsKA2tleRgBIAEoBRINCgV2YWx1",
            "ZRgCIAEoBToCOAEqPwoHTWFwRW51bRIQCgxNQVBfRU5VTV9GT08QABIQCgxN",
            "QVBfRU5VTV9CQVIQARIQCgxNQVBfRU5VTV9CQVoQAkIdqgIaR29vZ2xlLlBy",
            "b3RvYnVmLlRlc3RQcm90b3NiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Google.Protobuf.TestProtos.UnittestProto3Reflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Google.Protobuf.TestProtos.MapEnum), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Google.Protobuf.TestProtos.TestMap), global::Google.Protobuf.TestProtos.TestMap.Parser, new[]{ "MapInt32Int32", "MapInt64Int64", "MapUint32Uint32", "MapUint64Uint64", "MapSint32Sint32", "MapSint64Sint64", "MapFixed32Fixed32", "MapFixed64Fixed64", "MapSfixed32Sfixed32", "MapSfixed64Sfixed64", "MapInt32Float", "MapInt32Double", "MapBoolBool", "MapStringString", "MapInt32Bytes", "MapInt32Enum", "MapInt32ForeignMessage" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::Google.Protobuf.TestProtos.TestMapSubmessage), global::Google.Protobuf.TestProtos.TestMapSubmessage.Parser, new[]{ "TestMap" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Google.Protobuf.TestProtos.TestMessageMap), global::Google.Protobuf.TestProtos.TestMessageMap.Parser, new[]{ "MapInt32Message" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::Google.Protobuf.TestProtos.TestSameTypeMap), global::Google.Protobuf.TestProtos.TestSameTypeMap.Parser, new[]{ "Map1", "Map2" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::Google.Protobuf.TestProtos.TestArenaMap), global::Google.Protobuf.TestProtos.TestArenaMap.Parser, new[]{ "MapInt32Int32", "MapInt64Int64", "MapUint32Uint32", "MapUint64Uint64", "MapSint32Sint32", "MapSint64Sint64", "MapFixed32Fixed32", "MapFixed64Fixed64", "MapSfixed32Sfixed32", "MapSfixed64Sfixed64", "MapInt32Float", "MapInt32Double", "MapBoolBool", "MapInt32Enum", "MapInt32ForeignMessage" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::Google.Protobuf.TestProtos.MessageContainingEnumCalledType), global::Google.Protobuf.TestProtos.MessageContainingEnumCalledType.Parser, new[]{ "Type" }, null, new[]{ typeof(global::Google.Protobuf.TestProtos.MessageContainingEnumCalledType.Types.Type) }, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::Google.Protobuf.TestProtos.MessageContainingMapCalledEntry), global::Google.Protobuf.TestProtos.MessageContainingMapCalledEntry.Parser, new[]{ "Entry" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, })
          }));
    }
    #endregion

  }
  #region Enums
  public enum MapEnum {
    [pbr::OriginalName("MAP_ENUM_FOO")] Foo = 0,
    [pbr::OriginalName("MAP_ENUM_BAR")] Bar = 1,
    [pbr::OriginalName("MAP_ENUM_BAZ")] Baz = 2,
  }

  #endregion

  #region Messages
  /// <summary>
  /// Tests maps.
  /// </summary>
  public sealed partial class TestMap : pb::IMessage<TestMap>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TestMap> _parser = new pb::MessageParser<TestMap>(() => new TestMap());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TestMap> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Google.Protobuf.TestProtos.MapUnittestProto3Reflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestMap() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestMap(TestMap other) : this() {
      mapInt32Int32_ = other.mapInt32Int32_.Clone();
      mapInt64Int64_ = other.mapInt64Int64_.Clone();
      mapUint32Uint32_ = other.mapUint32Uint32_.Clone();
      mapUint64Uint64_ = other.mapUint64Uint64_.Clone();
      mapSint32Sint32_ = other.mapSint32Sint32_.Clone();
      mapSint64Sint64_ = other.mapSint64Sint64_.Clone();
      mapFixed32Fixed32_ = other.mapFixed32Fixed32_.Clone();
      mapFixed64Fixed64_ = other.mapFixed64Fixed64_.Clone();
      mapSfixed32Sfixed32_ = other.mapSfixed32Sfixed32_.Clone();
      mapSfixed64Sfixed64_ = other.mapSfixed64Sfixed64_.Clone();
      mapInt32Float_ = other.mapInt32Float_.Clone();
      mapInt32Double_ = other.mapInt32Double_.Clone();
      mapBoolBool_ = other.mapBoolBool_.Clone();
      mapStringString_ = other.mapStringString_.Clone();
      mapInt32Bytes_ = other.mapInt32Bytes_.Clone();
      mapInt32Enum_ = other.mapInt32Enum_.Clone();
      mapInt32ForeignMessage_ = other.mapInt32ForeignMessage_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestMap Clone() {
      return new TestMap(this);
    }

    /// <summary>Field number for the "map_int32_int32" field.</summary>
    public const int MapInt32Int32FieldNumber = 1;
    private static readonly pbc::MapField<int, int>.Codec _map_mapInt32Int32_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 10);
    private readonly pbc::MapField<int, int> mapInt32Int32_ = new pbc::MapField<int, int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> MapInt32Int32 {
      get { return mapInt32Int32_; }
    }

    /// <summary>Field number for the "map_int64_int64" field.</summary>
    public const int MapInt64Int64FieldNumber = 2;
    private static readonly pbc::MapField<long, long>.Codec _map_mapInt64Int64_codec
        = new pbc::MapField<long, long>.Codec(pb::FieldCodec.ForInt64(8, 0L), pb::FieldCodec.ForInt64(16, 0L), 18);
    private readonly pbc::MapField<long, long> mapInt64Int64_ = new pbc::MapField<long, long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<long, long> MapInt64Int64 {
      get { return mapInt64Int64_; }
    }

    /// <summary>Field number for the "map_uint32_uint32" field.</summary>
    public const int MapUint32Uint32FieldNumber = 3;
    private static readonly pbc::MapField<uint, uint>.Codec _map_mapUint32Uint32_codec
        = new pbc::MapField<uint, uint>.Codec(pb::FieldCodec.ForUInt32(8, 0), pb::FieldCodec.ForUInt32(16, 0), 26);
    private readonly pbc::MapField<uint, uint> mapUint32Uint32_ = new pbc::MapField<uint, uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<uint, uint> MapUint32Uint32 {
      get { return mapUint32Uint32_; }
    }

    /// <summary>Field number for the "map_uint64_uint64" field.</summary>
    public const int MapUint64Uint64FieldNumber = 4;
    private static readonly pbc::MapField<ulong, ulong>.Codec _map_mapUint64Uint64_codec
        = new pbc::MapField<ulong, ulong>.Codec(pb::FieldCodec.ForUInt64(8, 0UL), pb::FieldCodec.ForUInt64(16, 0UL), 34);
    private readonly pbc::MapField<ulong, ulong> mapUint64Uint64_ = new pbc::MapField<ulong, ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<ulong, ulong> MapUint64Uint64 {
      get { return mapUint64Uint64_; }
    }

    /// <summary>Field number for the "map_sint32_sint32" field.</summary>
    public const int MapSint32Sint32FieldNumber = 5;
    private static readonly pbc::MapField<int, int>.Codec _map_mapSint32Sint32_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForSInt32(8, 0), pb::FieldCodec.ForSInt32(16, 0), 42);
    private readonly pbc::MapField<int, int> mapSint32Sint32_ = new pbc::MapField<int, int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> MapSint32Sint32 {
      get { return mapSint32Sint32_; }
    }

    /// <summary>Field number for the "map_sint64_sint64" field.</summary>
    public const int MapSint64Sint64FieldNumber = 6;
    private static readonly pbc::MapField<long, long>.Codec _map_mapSint64Sint64_codec
        = new pbc::MapField<long, long>.Codec(pb::FieldCodec.ForSInt64(8, 0L), pb::FieldCodec.ForSInt64(16, 0L), 50);
    private readonly pbc::MapField<long, long> mapSint64Sint64_ = new pbc::MapField<long, long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<long, long> MapSint64Sint64 {
      get { return mapSint64Sint64_; }
    }

    /// <summary>Field number for the "map_fixed32_fixed32" field.</summary>
    public const int MapFixed32Fixed32FieldNumber = 7;
    private static readonly pbc::MapField<uint, uint>.Codec _map_mapFixed32Fixed32_codec
        = new pbc::MapField<uint, uint>.Codec(pb::FieldCodec.ForFixed32(13, 0), pb::FieldCodec.ForFixed32(21, 0), 58);
    private readonly pbc::MapField<uint, uint> mapFixed32Fixed32_ = new pbc::MapField<uint, uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<uint, uint> MapFixed32Fixed32 {
      get { return mapFixed32Fixed32_; }
    }

    /// <summary>Field number for the "map_fixed64_fixed64" field.</summary>
    public const int MapFixed64Fixed64FieldNumber = 8;
    private static readonly pbc::MapField<ulong, ulong>.Codec _map_mapFixed64Fixed64_codec
        = new pbc::MapField<ulong, ulong>.Codec(pb::FieldCodec.ForFixed64(9, 0UL), pb::FieldCodec.ForFixed64(17, 0UL), 66);
    private readonly pbc::MapField<ulong, ulong> mapFixed64Fixed64_ = new pbc::MapField<ulong, ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<ulong, ulong> MapFixed64Fixed64 {
      get { return mapFixed64Fixed64_; }
    }

    /// <summary>Field number for the "map_sfixed32_sfixed32" field.</summary>
    public const int MapSfixed32Sfixed32FieldNumber = 9;
    private static readonly pbc::MapField<int, int>.Codec _map_mapSfixed32Sfixed32_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForSFixed32(13, 0), pb::FieldCodec.ForSFixed32(21, 0), 74);
    private readonly pbc::MapField<int, int> mapSfixed32Sfixed32_ = new pbc::MapField<int, int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> MapSfixed32Sfixed32 {
      get { return mapSfixed32Sfixed32_; }
    }

    /// <summary>Field number for the "map_sfixed64_sfixed64" field.</summary>
    public const int MapSfixed64Sfixed64FieldNumber = 10;
    private static readonly pbc::MapField<long, long>.Codec _map_mapSfixed64Sfixed64_codec
        = new pbc::MapField<long, long>.Codec(pb::FieldCodec.ForSFixed64(9, 0L), pb::FieldCodec.ForSFixed64(17, 0L), 82);
    private readonly pbc::MapField<long, long> mapSfixed64Sfixed64_ = new pbc::MapField<long, long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<long, long> MapSfixed64Sfixed64 {
      get { return mapSfixed64Sfixed64_; }
    }

    /// <summary>Field number for the "map_int32_float" field.</summary>
    public const int MapInt32FloatFieldNumber = 11;
    private static readonly pbc::MapField<int, float>.Codec _map_mapInt32Float_codec
        = new pbc::MapField<int, float>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForFloat(21, 0F), 90);
    private readonly pbc::MapField<int, float> mapInt32Float_ = new pbc::MapField<int, float>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, float> MapInt32Float {
      get { return mapInt32Float_; }
    }

    /// <summary>Field number for the "map_int32_double" field.</summary>
    public const int MapInt32DoubleFieldNumber = 12;
    private static readonly pbc::MapField<int, double>.Codec _map_mapInt32Double_codec
        = new pbc::MapField<int, double>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForDouble(17, 0D), 98);
    private readonly pbc::MapField<int, double> mapInt32Double_ = new pbc::MapField<int, double>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, double> MapInt32Double {
      get { return mapInt32Double_; }
    }

    /// <summary>Field number for the "map_bool_bool" field.</summary>
    public const int MapBoolBoolFieldNumber = 13;
    private static readonly pbc::MapField<bool, bool>.Codec _map_mapBoolBool_codec
        = new pbc::MapField<bool, bool>.Codec(pb::FieldCodec.ForBool(8, false), pb::FieldCodec.ForBool(16, false), 106);
    private readonly pbc::MapField<bool, bool> mapBoolBool_ = new pbc::MapField<bool, bool>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<bool, bool> MapBoolBool {
      get { return mapBoolBool_; }
    }

    /// <summary>Field number for the "map_string_string" field.</summary>
    public const int MapStringStringFieldNumber = 14;
    private static readonly pbc::MapField<string, string>.Codec _map_mapStringString_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 114);
    private readonly pbc::MapField<string, string> mapStringString_ = new pbc::MapField<string, string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> MapStringString {
      get { return mapStringString_; }
    }

    /// <summary>Field number for the "map_int32_bytes" field.</summary>
    public const int MapInt32BytesFieldNumber = 15;
    private static readonly pbc::MapField<int, pb::ByteString>.Codec _map_mapInt32Bytes_codec
        = new pbc::MapField<int, pb::ByteString>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForBytes(18, pb::ByteString.Empty), 122);
    private readonly pbc::MapField<int, pb::ByteString> mapInt32Bytes_ = new pbc::MapField<int, pb::ByteString>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, pb::ByteString> MapInt32Bytes {
      get { return mapInt32Bytes_; }
    }

    /// <summary>Field number for the "map_int32_enum" field.</summary>
    public const int MapInt32EnumFieldNumber = 16;
    private static readonly pbc::MapField<int, global::Google.Protobuf.TestProtos.MapEnum>.Codec _map_mapInt32Enum_codec
        = new pbc::MapField<int, global::Google.Protobuf.TestProtos.MapEnum>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForEnum(16, x => (int) x, x => (global::Google.Protobuf.TestProtos.MapEnum) x, global::Google.Protobuf.TestProtos.MapEnum.Foo), 130);
    private readonly pbc::MapField<int, global::Google.Protobuf.TestProtos.MapEnum> mapInt32Enum_ = new pbc::MapField<int, global::Google.Protobuf.TestProtos.MapEnum>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, global::Google.Protobuf.TestProtos.MapEnum> MapInt32Enum {
      get { return mapInt32Enum_; }
    }

    /// <summary>Field number for the "map_int32_foreign_message" field.</summary>
    public const int MapInt32ForeignMessageFieldNumber = 17;
    private static readonly pbc::MapField<int, global::Google.Protobuf.TestProtos.ForeignMessage>.Codec _map_mapInt32ForeignMessage_codec
        = new pbc::MapField<int, global::Google.Protobuf.TestProtos.ForeignMessage>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForMessage(18, global::Google.Protobuf.TestProtos.ForeignMessage.Parser), 138);
    private readonly pbc::MapField<int, global::Google.Protobuf.TestProtos.ForeignMessage> mapInt32ForeignMessage_ = new pbc::MapField<int, global::Google.Protobuf.TestProtos.ForeignMessage>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, global::Google.Protobuf.TestProtos.ForeignMessage> MapInt32ForeignMessage {
      get { return mapInt32ForeignMessage_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TestMap);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TestMap other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!MapInt32Int32.Equals(other.MapInt32Int32)) return false;
      if (!MapInt64Int64.Equals(other.MapInt64Int64)) return false;
      if (!MapUint32Uint32.Equals(other.MapUint32Uint32)) return false;
      if (!MapUint64Uint64.Equals(other.MapUint64Uint64)) return false;
      if (!MapSint32Sint32.Equals(other.MapSint32Sint32)) return false;
      if (!MapSint64Sint64.Equals(other.MapSint64Sint64)) return false;
      if (!MapFixed32Fixed32.Equals(other.MapFixed32Fixed32)) return false;
      if (!MapFixed64Fixed64.Equals(other.MapFixed64Fixed64)) return false;
      if (!MapSfixed32Sfixed32.Equals(other.MapSfixed32Sfixed32)) return false;
      if (!MapSfixed64Sfixed64.Equals(other.MapSfixed64Sfixed64)) return false;
      if (!MapInt32Float.Equals(other.MapInt32Float)) return false;
      if (!MapInt32Double.Equals(other.MapInt32Double)) return false;
      if (!MapBoolBool.Equals(other.MapBoolBool)) return false;
      if (!MapStringString.Equals(other.MapStringString)) return false;
      if (!MapInt32Bytes.Equals(other.MapInt32Bytes)) return false;
      if (!MapInt32Enum.Equals(other.MapInt32Enum)) return false;
      if (!MapInt32ForeignMessage.Equals(other.MapInt32ForeignMessage)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= MapInt32Int32.GetHashCode();
      hash ^= MapInt64Int64.GetHashCode();
      hash ^= MapUint32Uint32.GetHashCode();
      hash ^= MapUint64Uint64.GetHashCode();
      hash ^= MapSint32Sint32.GetHashCode();
      hash ^= MapSint64Sint64.GetHashCode();
      hash ^= MapFixed32Fixed32.GetHashCode();
      hash ^= MapFixed64Fixed64.GetHashCode();
      hash ^= MapSfixed32Sfixed32.GetHashCode();
      hash ^= MapSfixed64Sfixed64.GetHashCode();
      hash ^= MapInt32Float.GetHashCode();
      hash ^= MapInt32Double.GetHashCode();
      hash ^= MapBoolBool.GetHashCode();
      hash ^= MapStringString.GetHashCode();
      hash ^= MapInt32Bytes.GetHashCode();
      hash ^= MapInt32Enum.GetHashCode();
      hash ^= MapInt32ForeignMessage.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      mapInt32Int32_.WriteTo(output, _map_mapInt32Int32_codec);
      mapInt64Int64_.WriteTo(output, _map_mapInt64Int64_codec);
      mapUint32Uint32_.WriteTo(output, _map_mapUint32Uint32_codec);
      mapUint64Uint64_.WriteTo(output, _map_mapUint64Uint64_codec);
      mapSint32Sint32_.WriteTo(output, _map_mapSint32Sint32_codec);
      mapSint64Sint64_.WriteTo(output, _map_mapSint64Sint64_codec);
      mapFixed32Fixed32_.WriteTo(output, _map_mapFixed32Fixed32_codec);
      mapFixed64Fixed64_.WriteTo(output, _map_mapFixed64Fixed64_codec);
      mapSfixed32Sfixed32_.WriteTo(output, _map_mapSfixed32Sfixed32_codec);
      mapSfixed64Sfixed64_.WriteTo(output, _map_mapSfixed64Sfixed64_codec);
      mapInt32Float_.WriteTo(output, _map_mapInt32Float_codec);
      mapInt32Double_.WriteTo(output, _map_mapInt32Double_codec);
      mapBoolBool_.WriteTo(output, _map_mapBoolBool_codec);
      mapStringString_.WriteTo(output, _map_mapStringString_codec);
      mapInt32Bytes_.WriteTo(output, _map_mapInt32Bytes_codec);
      mapInt32Enum_.WriteTo(output, _map_mapInt32Enum_codec);
      mapInt32ForeignMessage_.WriteTo(output, _map_mapInt32ForeignMessage_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      mapInt32Int32_.WriteTo(ref output, _map_mapInt32Int32_codec);
      mapInt64Int64_.WriteTo(ref output, _map_mapInt64Int64_codec);
      mapUint32Uint32_.WriteTo(ref output, _map_mapUint32Uint32_codec);
      mapUint64Uint64_.WriteTo(ref output, _map_mapUint64Uint64_codec);
      mapSint32Sint32_.WriteTo(ref output, _map_mapSint32Sint32_codec);
      mapSint64Sint64_.WriteTo(ref output, _map_mapSint64Sint64_codec);
      mapFixed32Fixed32_.WriteTo(ref output, _map_mapFixed32Fixed32_codec);
      mapFixed64Fixed64_.WriteTo(ref output, _map_mapFixed64Fixed64_codec);
      mapSfixed32Sfixed32_.WriteTo(ref output, _map_mapSfixed32Sfixed32_codec);
      mapSfixed64Sfixed64_.WriteTo(ref output, _map_mapSfixed64Sfixed64_codec);
      mapInt32Float_.WriteTo(ref output, _map_mapInt32Float_codec);
      mapInt32Double_.WriteTo(ref output, _map_mapInt32Double_codec);
      mapBoolBool_.WriteTo(ref output, _map_mapBoolBool_codec);
      mapStringString_.WriteTo(ref output, _map_mapStringString_codec);
      mapInt32Bytes_.WriteTo(ref output, _map_mapInt32Bytes_codec);
      mapInt32Enum_.WriteTo(ref output, _map_mapInt32Enum_codec);
      mapInt32ForeignMessage_.WriteTo(ref output, _map_mapInt32ForeignMessage_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += mapInt32Int32_.CalculateSize(_map_mapInt32Int32_codec);
      size += mapInt64Int64_.CalculateSize(_map_mapInt64Int64_codec);
      size += mapUint32Uint32_.CalculateSize(_map_mapUint32Uint32_codec);
      size += mapUint64Uint64_.CalculateSize(_map_mapUint64Uint64_codec);
      size += mapSint32Sint32_.CalculateSize(_map_mapSint32Sint32_codec);
      size += mapSint64Sint64_.CalculateSize(_map_mapSint64Sint64_codec);
      size += mapFixed32Fixed32_.CalculateSize(_map_mapFixed32Fixed32_codec);
      size += mapFixed64Fixed64_.CalculateSize(_map_mapFixed64Fixed64_codec);
      size += mapSfixed32Sfixed32_.CalculateSize(_map_mapSfixed32Sfixed32_codec);
      size += mapSfixed64Sfixed64_.CalculateSize(_map_mapSfixed64Sfixed64_codec);
      size += mapInt32Float_.CalculateSize(_map_mapInt32Float_codec);
      size += mapInt32Double_.CalculateSize(_map_mapInt32Double_codec);
      size += mapBoolBool_.CalculateSize(_map_mapBoolBool_codec);
      size += mapStringString_.CalculateSize(_map_mapStringString_codec);
      size += mapInt32Bytes_.CalculateSize(_map_mapInt32Bytes_codec);
      size += mapInt32Enum_.CalculateSize(_map_mapInt32Enum_codec);
      size += mapInt32ForeignMessage_.CalculateSize(_map_mapInt32ForeignMessage_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TestMap other) {
      if (other == null) {
        return;
      }
      mapInt32Int32_.MergeFrom(other.mapInt32Int32_);
      mapInt64Int64_.MergeFrom(other.mapInt64Int64_);
      mapUint32Uint32_.MergeFrom(other.mapUint32Uint32_);
      mapUint64Uint64_.MergeFrom(other.mapUint64Uint64_);
      mapSint32Sint32_.MergeFrom(other.mapSint32Sint32_);
      mapSint64Sint64_.MergeFrom(other.mapSint64Sint64_);
      mapFixed32Fixed32_.MergeFrom(other.mapFixed32Fixed32_);
      mapFixed64Fixed64_.MergeFrom(other.mapFixed64Fixed64_);
      mapSfixed32Sfixed32_.MergeFrom(other.mapSfixed32Sfixed32_);
      mapSfixed64Sfixed64_.MergeFrom(other.mapSfixed64Sfixed64_);
      mapInt32Float_.MergeFrom(other.mapInt32Float_);
      mapInt32Double_.MergeFrom(other.mapInt32Double_);
      mapBoolBool_.MergeFrom(other.mapBoolBool_);
      mapStringString_.MergeFrom(other.mapStringString_);
      mapInt32Bytes_.MergeFrom(other.mapInt32Bytes_);
      mapInt32Enum_.MergeFrom(other.mapInt32Enum_);
      mapInt32ForeignMessage_.MergeFrom(other.mapInt32ForeignMessage_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            mapInt32Int32_.AddEntriesFrom(input, _map_mapInt32Int32_codec);
            break;
          }
          case 18: {
            mapInt64Int64_.AddEntriesFrom(input, _map_mapInt64Int64_codec);
            break;
          }
          case 26: {
            mapUint32Uint32_.AddEntriesFrom(input, _map_mapUint32Uint32_codec);
            break;
          }
          case 34: {
            mapUint64Uint64_.AddEntriesFrom(input, _map_mapUint64Uint64_codec);
            break;
          }
          case 42: {
            mapSint32Sint32_.AddEntriesFrom(input, _map_mapSint32Sint32_codec);
            break;
          }
          case 50: {
            mapSint64Sint64_.AddEntriesFrom(input, _map_mapSint64Sint64_codec);
            break;
          }
          case 58: {
            mapFixed32Fixed32_.AddEntriesFrom(input, _map_mapFixed32Fixed32_codec);
            break;
          }
          case 66: {
            mapFixed64Fixed64_.AddEntriesFrom(input, _map_mapFixed64Fixed64_codec);
            break;
          }
          case 74: {
            mapSfixed32Sfixed32_.AddEntriesFrom(input, _map_mapSfixed32Sfixed32_codec);
            break;
          }
          case 82: {
            mapSfixed64Sfixed64_.AddEntriesFrom(input, _map_mapSfixed64Sfixed64_codec);
            break;
          }
          case 90: {
            mapInt32Float_.AddEntriesFrom(input, _map_mapInt32Float_codec);
            break;
          }
          case 98: {
            mapInt32Double_.AddEntriesFrom(input, _map_mapInt32Double_codec);
            break;
          }
          case 106: {
            mapBoolBool_.AddEntriesFrom(input, _map_mapBoolBool_codec);
            break;
          }
          case 114: {
            mapStringString_.AddEntriesFrom(input, _map_mapStringString_codec);
            break;
          }
          case 122: {
            mapInt32Bytes_.AddEntriesFrom(input, _map_mapInt32Bytes_codec);
            break;
          }
          case 130: {
            mapInt32Enum_.AddEntriesFrom(input, _map_mapInt32Enum_codec);
            break;
          }
          case 138: {
            mapInt32ForeignMessage_.AddEntriesFrom(input, _map_mapInt32ForeignMessage_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            mapInt32Int32_.AddEntriesFrom(ref input, _map_mapInt32Int32_codec);
            break;
          }
          case 18: {
            mapInt64Int64_.AddEntriesFrom(ref input, _map_mapInt64Int64_codec);
            break;
          }
          case 26: {
            mapUint32Uint32_.AddEntriesFrom(ref input, _map_mapUint32Uint32_codec);
            break;
          }
          case 34: {
            mapUint64Uint64_.AddEntriesFrom(ref input, _map_mapUint64Uint64_codec);
            break;
          }
          case 42: {
            mapSint32Sint32_.AddEntriesFrom(ref input, _map_mapSint32Sint32_codec);
            break;
          }
          case 50: {
            mapSint64Sint64_.AddEntriesFrom(ref input, _map_mapSint64Sint64_codec);
            break;
          }
          case 58: {
            mapFixed32Fixed32_.AddEntriesFrom(ref input, _map_mapFixed32Fixed32_codec);
            break;
          }
          case 66: {
            mapFixed64Fixed64_.AddEntriesFrom(ref input, _map_mapFixed64Fixed64_codec);
            break;
          }
          case 74: {
            mapSfixed32Sfixed32_.AddEntriesFrom(ref input, _map_mapSfixed32Sfixed32_codec);
            break;
          }
          case 82: {
            mapSfixed64Sfixed64_.AddEntriesFrom(ref input, _map_mapSfixed64Sfixed64_codec);
            break;
          }
          case 90: {
            mapInt32Float_.AddEntriesFrom(ref input, _map_mapInt32Float_codec);
            break;
          }
          case 98: {
            mapInt32Double_.AddEntriesFrom(ref input, _map_mapInt32Double_codec);
            break;
          }
          case 106: {
            mapBoolBool_.AddEntriesFrom(ref input, _map_mapBoolBool_codec);
            break;
          }
          case 114: {
            mapStringString_.AddEntriesFrom(ref input, _map_mapStringString_codec);
            break;
          }
          case 122: {
            mapInt32Bytes_.AddEntriesFrom(ref input, _map_mapInt32Bytes_codec);
            break;
          }
          case 130: {
            mapInt32Enum_.AddEntriesFrom(ref input, _map_mapInt32Enum_codec);
            break;
          }
          case 138: {
            mapInt32ForeignMessage_.AddEntriesFrom(ref input, _map_mapInt32ForeignMessage_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class TestMapSubmessage : pb::IMessage<TestMapSubmessage>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TestMapSubmessage> _parser = new pb::MessageParser<TestMapSubmessage>(() => new TestMapSubmessage());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TestMapSubmessage> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Google.Protobuf.TestProtos.MapUnittestProto3Reflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestMapSubmessage() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestMapSubmessage(TestMapSubmessage other) : this() {
      testMap_ = other.testMap_ != null ? other.testMap_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestMapSubmessage Clone() {
      return new TestMapSubmessage(this);
    }

    /// <summary>Field number for the "test_map" field.</summary>
    public const int TestMapFieldNumber = 1;
    private global::Google.Protobuf.TestProtos.TestMap testMap_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.TestProtos.TestMap TestMap {
      get { return testMap_; }
      set {
        testMap_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TestMapSubmessage);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TestMapSubmessage other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(TestMap, other.TestMap)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (testMap_ != null) hash ^= TestMap.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (testMap_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(TestMap);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (testMap_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(TestMap);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (testMap_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(TestMap);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TestMapSubmessage other) {
      if (other == null) {
        return;
      }
      if (other.testMap_ != null) {
        if (testMap_ == null) {
          TestMap = new global::Google.Protobuf.TestProtos.TestMap();
        }
        TestMap.MergeFrom(other.TestMap);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (testMap_ == null) {
              TestMap = new global::Google.Protobuf.TestProtos.TestMap();
            }
            input.ReadMessage(TestMap);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (testMap_ == null) {
              TestMap = new global::Google.Protobuf.TestProtos.TestMap();
            }
            input.ReadMessage(TestMap);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class TestMessageMap : pb::IMessage<TestMessageMap>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TestMessageMap> _parser = new pb::MessageParser<TestMessageMap>(() => new TestMessageMap());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TestMessageMap> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Google.Protobuf.TestProtos.MapUnittestProto3Reflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestMessageMap() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestMessageMap(TestMessageMap other) : this() {
      mapInt32Message_ = other.mapInt32Message_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestMessageMap Clone() {
      return new TestMessageMap(this);
    }

    /// <summary>Field number for the "map_int32_message" field.</summary>
    public const int MapInt32MessageFieldNumber = 1;
    private static readonly pbc::MapField<int, global::Google.Protobuf.TestProtos.TestAllTypes>.Codec _map_mapInt32Message_codec
        = new pbc::MapField<int, global::Google.Protobuf.TestProtos.TestAllTypes>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForMessage(18, global::Google.Protobuf.TestProtos.TestAllTypes.Parser), 10);
    private readonly pbc::MapField<int, global::Google.Protobuf.TestProtos.TestAllTypes> mapInt32Message_ = new pbc::MapField<int, global::Google.Protobuf.TestProtos.TestAllTypes>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, global::Google.Protobuf.TestProtos.TestAllTypes> MapInt32Message {
      get { return mapInt32Message_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TestMessageMap);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TestMessageMap other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!MapInt32Message.Equals(other.MapInt32Message)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= MapInt32Message.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      mapInt32Message_.WriteTo(output, _map_mapInt32Message_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      mapInt32Message_.WriteTo(ref output, _map_mapInt32Message_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += mapInt32Message_.CalculateSize(_map_mapInt32Message_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TestMessageMap other) {
      if (other == null) {
        return;
      }
      mapInt32Message_.MergeFrom(other.mapInt32Message_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            mapInt32Message_.AddEntriesFrom(input, _map_mapInt32Message_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            mapInt32Message_.AddEntriesFrom(ref input, _map_mapInt32Message_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Two map fields share the same entry default instance.
  /// </summary>
  public sealed partial class TestSameTypeMap : pb::IMessage<TestSameTypeMap>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TestSameTypeMap> _parser = new pb::MessageParser<TestSameTypeMap>(() => new TestSameTypeMap());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TestSameTypeMap> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Google.Protobuf.TestProtos.MapUnittestProto3Reflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestSameTypeMap() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestSameTypeMap(TestSameTypeMap other) : this() {
      map1_ = other.map1_.Clone();
      map2_ = other.map2_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestSameTypeMap Clone() {
      return new TestSameTypeMap(this);
    }

    /// <summary>Field number for the "map1" field.</summary>
    public const int Map1FieldNumber = 1;
    private static readonly pbc::MapField<int, int>.Codec _map_map1_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 10);
    private readonly pbc::MapField<int, int> map1_ = new pbc::MapField<int, int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> Map1 {
      get { return map1_; }
    }

    /// <summary>Field number for the "map2" field.</summary>
    public const int Map2FieldNumber = 2;
    private static readonly pbc::MapField<int, int>.Codec _map_map2_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 18);
    private readonly pbc::MapField<int, int> map2_ = new pbc::MapField<int, int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> Map2 {
      get { return map2_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TestSameTypeMap);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TestSameTypeMap other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!Map1.Equals(other.Map1)) return false;
      if (!Map2.Equals(other.Map2)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= Map1.GetHashCode();
      hash ^= Map2.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      map1_.WriteTo(output, _map_map1_codec);
      map2_.WriteTo(output, _map_map2_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      map1_.WriteTo(ref output, _map_map1_codec);
      map2_.WriteTo(ref output, _map_map2_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += map1_.CalculateSize(_map_map1_codec);
      size += map2_.CalculateSize(_map_map2_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TestSameTypeMap other) {
      if (other == null) {
        return;
      }
      map1_.MergeFrom(other.map1_);
      map2_.MergeFrom(other.map2_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            map1_.AddEntriesFrom(input, _map_map1_codec);
            break;
          }
          case 18: {
            map2_.AddEntriesFrom(input, _map_map2_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            map1_.AddEntriesFrom(ref input, _map_map1_codec);
            break;
          }
          case 18: {
            map2_.AddEntriesFrom(ref input, _map_map2_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class TestArenaMap : pb::IMessage<TestArenaMap>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TestArenaMap> _parser = new pb::MessageParser<TestArenaMap>(() => new TestArenaMap());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TestArenaMap> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Google.Protobuf.TestProtos.MapUnittestProto3Reflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestArenaMap() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestArenaMap(TestArenaMap other) : this() {
      mapInt32Int32_ = other.mapInt32Int32_.Clone();
      mapInt64Int64_ = other.mapInt64Int64_.Clone();
      mapUint32Uint32_ = other.mapUint32Uint32_.Clone();
      mapUint64Uint64_ = other.mapUint64Uint64_.Clone();
      mapSint32Sint32_ = other.mapSint32Sint32_.Clone();
      mapSint64Sint64_ = other.mapSint64Sint64_.Clone();
      mapFixed32Fixed32_ = other.mapFixed32Fixed32_.Clone();
      mapFixed64Fixed64_ = other.mapFixed64Fixed64_.Clone();
      mapSfixed32Sfixed32_ = other.mapSfixed32Sfixed32_.Clone();
      mapSfixed64Sfixed64_ = other.mapSfixed64Sfixed64_.Clone();
      mapInt32Float_ = other.mapInt32Float_.Clone();
      mapInt32Double_ = other.mapInt32Double_.Clone();
      mapBoolBool_ = other.mapBoolBool_.Clone();
      mapInt32Enum_ = other.mapInt32Enum_.Clone();
      mapInt32ForeignMessage_ = other.mapInt32ForeignMessage_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestArenaMap Clone() {
      return new TestArenaMap(this);
    }

    /// <summary>Field number for the "map_int32_int32" field.</summary>
    public const int MapInt32Int32FieldNumber = 1;
    private static readonly pbc::MapField<int, int>.Codec _map_mapInt32Int32_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 10);
    private readonly pbc::MapField<int, int> mapInt32Int32_ = new pbc::MapField<int, int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> MapInt32Int32 {
      get { return mapInt32Int32_; }
    }

    /// <summary>Field number for the "map_int64_int64" field.</summary>
    public const int MapInt64Int64FieldNumber = 2;
    private static readonly pbc::MapField<long, long>.Codec _map_mapInt64Int64_codec
        = new pbc::MapField<long, long>.Codec(pb::FieldCodec.ForInt64(8, 0L), pb::FieldCodec.ForInt64(16, 0L), 18);
    private readonly pbc::MapField<long, long> mapInt64Int64_ = new pbc::MapField<long, long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<long, long> MapInt64Int64 {
      get { return mapInt64Int64_; }
    }

    /// <summary>Field number for the "map_uint32_uint32" field.</summary>
    public const int MapUint32Uint32FieldNumber = 3;
    private static readonly pbc::MapField<uint, uint>.Codec _map_mapUint32Uint32_codec
        = new pbc::MapField<uint, uint>.Codec(pb::FieldCodec.ForUInt32(8, 0), pb::FieldCodec.ForUInt32(16, 0), 26);
    private readonly pbc::MapField<uint, uint> mapUint32Uint32_ = new pbc::MapField<uint, uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<uint, uint> MapUint32Uint32 {
      get { return mapUint32Uint32_; }
    }

    /// <summary>Field number for the "map_uint64_uint64" field.</summary>
    public const int MapUint64Uint64FieldNumber = 4;
    private static readonly pbc::MapField<ulong, ulong>.Codec _map_mapUint64Uint64_codec
        = new pbc::MapField<ulong, ulong>.Codec(pb::FieldCodec.ForUInt64(8, 0UL), pb::FieldCodec.ForUInt64(16, 0UL), 34);
    private readonly pbc::MapField<ulong, ulong> mapUint64Uint64_ = new pbc::MapField<ulong, ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<ulong, ulong> MapUint64Uint64 {
      get { return mapUint64Uint64_; }
    }

    /// <summary>Field number for the "map_sint32_sint32" field.</summary>
    public const int MapSint32Sint32FieldNumber = 5;
    private static readonly pbc::MapField<int, int>.Codec _map_mapSint32Sint32_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForSInt32(8, 0), pb::FieldCodec.ForSInt32(16, 0), 42);
    private readonly pbc::MapField<int, int> mapSint32Sint32_ = new pbc::MapField<int, int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> MapSint32Sint32 {
      get { return mapSint32Sint32_; }
    }

    /// <summary>Field number for the "map_sint64_sint64" field.</summary>
    public const int MapSint64Sint64FieldNumber = 6;
    private static readonly pbc::MapField<long, long>.Codec _map_mapSint64Sint64_codec
        = new pbc::MapField<long, long>.Codec(pb::FieldCodec.ForSInt64(8, 0L), pb::FieldCodec.ForSInt64(16, 0L), 50);
    private readonly pbc::MapField<long, long> mapSint64Sint64_ = new pbc::MapField<long, long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<long, long> MapSint64Sint64 {
      get { return mapSint64Sint64_; }
    }

    /// <summary>Field number for the "map_fixed32_fixed32" field.</summary>
    public const int MapFixed32Fixed32FieldNumber = 7;
    private static readonly pbc::MapField<uint, uint>.Codec _map_mapFixed32Fixed32_codec
        = new pbc::MapField<uint, uint>.Codec(pb::FieldCodec.ForFixed32(13, 0), pb::FieldCodec.ForFixed32(21, 0), 58);
    private readonly pbc::MapField<uint, uint> mapFixed32Fixed32_ = new pbc::MapField<uint, uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<uint, uint> MapFixed32Fixed32 {
      get { return mapFixed32Fixed32_; }
    }

    /// <summary>Field number for the "map_fixed64_fixed64" field.</summary>
    public const int MapFixed64Fixed64FieldNumber = 8;
    private static readonly pbc::MapField<ulong, ulong>.Codec _map_mapFixed64Fixed64_codec
        = new pbc::MapField<ulong, ulong>.Codec(pb::FieldCodec.ForFixed64(9, 0UL), pb::FieldCodec.ForFixed64(17, 0UL), 66);
    private readonly pbc::MapField<ulong, ulong> mapFixed64Fixed64_ = new pbc::MapField<ulong, ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<ulong, ulong> MapFixed64Fixed64 {
      get { return mapFixed64Fixed64_; }
    }

    /// <summary>Field number for the "map_sfixed32_sfixed32" field.</summary>
    public const int MapSfixed32Sfixed32FieldNumber = 9;
    private static readonly pbc::MapField<int, int>.Codec _map_mapSfixed32Sfixed32_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForSFixed32(13, 0), pb::FieldCodec.ForSFixed32(21, 0), 74);
    private readonly pbc::MapField<int, int> mapSfixed32Sfixed32_ = new pbc::MapField<int, int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> MapSfixed32Sfixed32 {
      get { return mapSfixed32Sfixed32_; }
    }

    /// <summary>Field number for the "map_sfixed64_sfixed64" field.</summary>
    public const int MapSfixed64Sfixed64FieldNumber = 10;
    private static readonly pbc::MapField<long, long>.Codec _map_mapSfixed64Sfixed64_codec
        = new pbc::MapField<long, long>.Codec(pb::FieldCodec.ForSFixed64(9, 0L), pb::FieldCodec.ForSFixed64(17, 0L), 82);
    private readonly pbc::MapField<long, long> mapSfixed64Sfixed64_ = new pbc::MapField<long, long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<long, long> MapSfixed64Sfixed64 {
      get { return mapSfixed64Sfixed64_; }
    }

    /// <summary>Field number for the "map_int32_float" field.</summary>
    public const int MapInt32FloatFieldNumber = 11;
    private static readonly pbc::MapField<int, float>.Codec _map_mapInt32Float_codec
        = new pbc::MapField<int, float>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForFloat(21, 0F), 90);
    private readonly pbc::MapField<int, float> mapInt32Float_ = new pbc::MapField<int, float>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, float> MapInt32Float {
      get { return mapInt32Float_; }
    }

    /// <summary>Field number for the "map_int32_double" field.</summary>
    public const int MapInt32DoubleFieldNumber = 12;
    private static readonly pbc::MapField<int, double>.Codec _map_mapInt32Double_codec
        = new pbc::MapField<int, double>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForDouble(17, 0D), 98);
    private readonly pbc::MapField<int, double> mapInt32Double_ = new pbc::MapField<int, double>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, double> MapInt32Double {
      get { return mapInt32Double_; }
    }

    /// <summary>Field number for the "map_bool_bool" field.</summary>
    public const int MapBoolBoolFieldNumber = 13;
    private static readonly pbc::MapField<bool, bool>.Codec _map_mapBoolBool_codec
        = new pbc::MapField<bool, bool>.Codec(pb::FieldCodec.ForBool(8, false), pb::FieldCodec.ForBool(16, false), 106);
    private readonly pbc::MapField<bool, bool> mapBoolBool_ = new pbc::MapField<bool, bool>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<bool, bool> MapBoolBool {
      get { return mapBoolBool_; }
    }

    /// <summary>Field number for the "map_int32_enum" field.</summary>
    public const int MapInt32EnumFieldNumber = 14;
    private static readonly pbc::MapField<int, global::Google.Protobuf.TestProtos.MapEnum>.Codec _map_mapInt32Enum_codec
        = new pbc::MapField<int, global::Google.Protobuf.TestProtos.MapEnum>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForEnum(16, x => (int) x, x => (global::Google.Protobuf.TestProtos.MapEnum) x, global::Google.Protobuf.TestProtos.MapEnum.Foo), 114);
    private readonly pbc::MapField<int, global::Google.Protobuf.TestProtos.MapEnum> mapInt32Enum_ = new pbc::MapField<int, global::Google.Protobuf.TestProtos.MapEnum>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, global::Google.Protobuf.TestProtos.MapEnum> MapInt32Enum {
      get { return mapInt32Enum_; }
    }

    /// <summary>Field number for the "map_int32_foreign_message" field.</summary>
    public const int MapInt32ForeignMessageFieldNumber = 15;
    private static readonly pbc::MapField<int, global::Google.Protobuf.TestProtos.ForeignMessage>.Codec _map_mapInt32ForeignMessage_codec
        = new pbc::MapField<int, global::Google.Protobuf.TestProtos.ForeignMessage>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForMessage(18, global::Google.Protobuf.TestProtos.ForeignMessage.Parser), 122);
    private readonly pbc::MapField<int, global::Google.Protobuf.TestProtos.ForeignMessage> mapInt32ForeignMessage_ = new pbc::MapField<int, global::Google.Protobuf.TestProtos.ForeignMessage>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, global::Google.Protobuf.TestProtos.ForeignMessage> MapInt32ForeignMessage {
      get { return mapInt32ForeignMessage_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TestArenaMap);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TestArenaMap other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!MapInt32Int32.Equals(other.MapInt32Int32)) return false;
      if (!MapInt64Int64.Equals(other.MapInt64Int64)) return false;
      if (!MapUint32Uint32.Equals(other.MapUint32Uint32)) return false;
      if (!MapUint64Uint64.Equals(other.MapUint64Uint64)) return false;
      if (!MapSint32Sint32.Equals(other.MapSint32Sint32)) return false;
      if (!MapSint64Sint64.Equals(other.MapSint64Sint64)) return false;
      if (!MapFixed32Fixed32.Equals(other.MapFixed32Fixed32)) return false;
      if (!MapFixed64Fixed64.Equals(other.MapFixed64Fixed64)) return false;
      if (!MapSfixed32Sfixed32.Equals(other.MapSfixed32Sfixed32)) return false;
      if (!MapSfixed64Sfixed64.Equals(other.MapSfixed64Sfixed64)) return false;
      if (!MapInt32Float.Equals(other.MapInt32Float)) return false;
      if (!MapInt32Double.Equals(other.MapInt32Double)) return false;
      if (!MapBoolBool.Equals(other.MapBoolBool)) return false;
      if (!MapInt32Enum.Equals(other.MapInt32Enum)) return false;
      if (!MapInt32ForeignMessage.Equals(other.MapInt32ForeignMessage)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= MapInt32Int32.GetHashCode();
      hash ^= MapInt64Int64.GetHashCode();
      hash ^= MapUint32Uint32.GetHashCode();
      hash ^= MapUint64Uint64.GetHashCode();
      hash ^= MapSint32Sint32.GetHashCode();
      hash ^= MapSint64Sint64.GetHashCode();
      hash ^= MapFixed32Fixed32.GetHashCode();
      hash ^= MapFixed64Fixed64.GetHashCode();
      hash ^= MapSfixed32Sfixed32.GetHashCode();
      hash ^= MapSfixed64Sfixed64.GetHashCode();
      hash ^= MapInt32Float.GetHashCode();
      hash ^= MapInt32Double.GetHashCode();
      hash ^= MapBoolBool.GetHashCode();
      hash ^= MapInt32Enum.GetHashCode();
      hash ^= MapInt32ForeignMessage.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      mapInt32Int32_.WriteTo(output, _map_mapInt32Int32_codec);
      mapInt64Int64_.WriteTo(output, _map_mapInt64Int64_codec);
      mapUint32Uint32_.WriteTo(output, _map_mapUint32Uint32_codec);
      mapUint64Uint64_.WriteTo(output, _map_mapUint64Uint64_codec);
      mapSint32Sint32_.WriteTo(output, _map_mapSint32Sint32_codec);
      mapSint64Sint64_.WriteTo(output, _map_mapSint64Sint64_codec);
      mapFixed32Fixed32_.WriteTo(output, _map_mapFixed32Fixed32_codec);
      mapFixed64Fixed64_.WriteTo(output, _map_mapFixed64Fixed64_codec);
      mapSfixed32Sfixed32_.WriteTo(output, _map_mapSfixed32Sfixed32_codec);
      mapSfixed64Sfixed64_.WriteTo(output, _map_mapSfixed64Sfixed64_codec);
      mapInt32Float_.WriteTo(output, _map_mapInt32Float_codec);
      mapInt32Double_.WriteTo(output, _map_mapInt32Double_codec);
      mapBoolBool_.WriteTo(output, _map_mapBoolBool_codec);
      mapInt32Enum_.WriteTo(output, _map_mapInt32Enum_codec);
      mapInt32ForeignMessage_.WriteTo(output, _map_mapInt32ForeignMessage_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      mapInt32Int32_.WriteTo(ref output, _map_mapInt32Int32_codec);
      mapInt64Int64_.WriteTo(ref output, _map_mapInt64Int64_codec);
      mapUint32Uint32_.WriteTo(ref output, _map_mapUint32Uint32_codec);
      mapUint64Uint64_.WriteTo(ref output, _map_mapUint64Uint64_codec);
      mapSint32Sint32_.WriteTo(ref output, _map_mapSint32Sint32_codec);
      mapSint64Sint64_.WriteTo(ref output, _map_mapSint64Sint64_codec);
      mapFixed32Fixed32_.WriteTo(ref output, _map_mapFixed32Fixed32_codec);
      mapFixed64Fixed64_.WriteTo(ref output, _map_mapFixed64Fixed64_codec);
      mapSfixed32Sfixed32_.WriteTo(ref output, _map_mapSfixed32Sfixed32_codec);
      mapSfixed64Sfixed64_.WriteTo(ref output, _map_mapSfixed64Sfixed64_codec);
      mapInt32Float_.WriteTo(ref output, _map_mapInt32Float_codec);
      mapInt32Double_.WriteTo(ref output, _map_mapInt32Double_codec);
      mapBoolBool_.WriteTo(ref output, _map_mapBoolBool_codec);
      mapInt32Enum_.WriteTo(ref output, _map_mapInt32Enum_codec);
      mapInt32ForeignMessage_.WriteTo(ref output, _map_mapInt32ForeignMessage_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += mapInt32Int32_.CalculateSize(_map_mapInt32Int32_codec);
      size += mapInt64Int64_.CalculateSize(_map_mapInt64Int64_codec);
      size += mapUint32Uint32_.CalculateSize(_map_mapUint32Uint32_codec);
      size += mapUint64Uint64_.CalculateSize(_map_mapUint64Uint64_codec);
      size += mapSint32Sint32_.CalculateSize(_map_mapSint32Sint32_codec);
      size += mapSint64Sint64_.CalculateSize(_map_mapSint64Sint64_codec);
      size += mapFixed32Fixed32_.CalculateSize(_map_mapFixed32Fixed32_codec);
      size += mapFixed64Fixed64_.CalculateSize(_map_mapFixed64Fixed64_codec);
      size += mapSfixed32Sfixed32_.CalculateSize(_map_mapSfixed32Sfixed32_codec);
      size += mapSfixed64Sfixed64_.CalculateSize(_map_mapSfixed64Sfixed64_codec);
      size += mapInt32Float_.CalculateSize(_map_mapInt32Float_codec);
      size += mapInt32Double_.CalculateSize(_map_mapInt32Double_codec);
      size += mapBoolBool_.CalculateSize(_map_mapBoolBool_codec);
      size += mapInt32Enum_.CalculateSize(_map_mapInt32Enum_codec);
      size += mapInt32ForeignMessage_.CalculateSize(_map_mapInt32ForeignMessage_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TestArenaMap other) {
      if (other == null) {
        return;
      }
      mapInt32Int32_.MergeFrom(other.mapInt32Int32_);
      mapInt64Int64_.MergeFrom(other.mapInt64Int64_);
      mapUint32Uint32_.MergeFrom(other.mapUint32Uint32_);
      mapUint64Uint64_.MergeFrom(other.mapUint64Uint64_);
      mapSint32Sint32_.MergeFrom(other.mapSint32Sint32_);
      mapSint64Sint64_.MergeFrom(other.mapSint64Sint64_);
      mapFixed32Fixed32_.MergeFrom(other.mapFixed32Fixed32_);
      mapFixed64Fixed64_.MergeFrom(other.mapFixed64Fixed64_);
      mapSfixed32Sfixed32_.MergeFrom(other.mapSfixed32Sfixed32_);
      mapSfixed64Sfixed64_.MergeFrom(other.mapSfixed64Sfixed64_);
      mapInt32Float_.MergeFrom(other.mapInt32Float_);
      mapInt32Double_.MergeFrom(other.mapInt32Double_);
      mapBoolBool_.MergeFrom(other.mapBoolBool_);
      mapInt32Enum_.MergeFrom(other.mapInt32Enum_);
      mapInt32ForeignMessage_.MergeFrom(other.mapInt32ForeignMessage_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            mapInt32Int32_.AddEntriesFrom(input, _map_mapInt32Int32_codec);
            break;
          }
          case 18: {
            mapInt64Int64_.AddEntriesFrom(input, _map_mapInt64Int64_codec);
            break;
          }
          case 26: {
            mapUint32Uint32_.AddEntriesFrom(input, _map_mapUint32Uint32_codec);
            break;
          }
          case 34: {
            mapUint64Uint64_.AddEntriesFrom(input, _map_mapUint64Uint64_codec);
            break;
          }
          case 42: {
            mapSint32Sint32_.AddEntriesFrom(input, _map_mapSint32Sint32_codec);
            break;
          }
          case 50: {
            mapSint64Sint64_.AddEntriesFrom(input, _map_mapSint64Sint64_codec);
            break;
          }
          case 58: {
            mapFixed32Fixed32_.AddEntriesFrom(input, _map_mapFixed32Fixed32_codec);
            break;
          }
          case 66: {
            mapFixed64Fixed64_.AddEntriesFrom(input, _map_mapFixed64Fixed64_codec);
            break;
          }
          case 74: {
            mapSfixed32Sfixed32_.AddEntriesFrom(input, _map_mapSfixed32Sfixed32_codec);
            break;
          }
          case 82: {
            mapSfixed64Sfixed64_.AddEntriesFrom(input, _map_mapSfixed64Sfixed64_codec);
            break;
          }
          case 90: {
            mapInt32Float_.AddEntriesFrom(input, _map_mapInt32Float_codec);
            break;
          }
          case 98: {
            mapInt32Double_.AddEntriesFrom(input, _map_mapInt32Double_codec);
            break;
          }
          case 106: {
            mapBoolBool_.AddEntriesFrom(input, _map_mapBoolBool_codec);
            break;
          }
          case 114: {
            mapInt32Enum_.AddEntriesFrom(input, _map_mapInt32Enum_codec);
            break;
          }
          case 122: {
            mapInt32ForeignMessage_.AddEntriesFrom(input, _map_mapInt32ForeignMessage_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            mapInt32Int32_.AddEntriesFrom(ref input, _map_mapInt32Int32_codec);
            break;
          }
          case 18: {
            mapInt64Int64_.AddEntriesFrom(ref input, _map_mapInt64Int64_codec);
            break;
          }
          case 26: {
            mapUint32Uint32_.AddEntriesFrom(ref input, _map_mapUint32Uint32_codec);
            break;
          }
          case 34: {
            mapUint64Uint64_.AddEntriesFrom(ref input, _map_mapUint64Uint64_codec);
            break;
          }
          case 42: {
            mapSint32Sint32_.AddEntriesFrom(ref input, _map_mapSint32Sint32_codec);
            break;
          }
          case 50: {
            mapSint64Sint64_.AddEntriesFrom(ref input, _map_mapSint64Sint64_codec);
            break;
          }
          case 58: {
            mapFixed32Fixed32_.AddEntriesFrom(ref input, _map_mapFixed32Fixed32_codec);
            break;
          }
          case 66: {
            mapFixed64Fixed64_.AddEntriesFrom(ref input, _map_mapFixed64Fixed64_codec);
            break;
          }
          case 74: {
            mapSfixed32Sfixed32_.AddEntriesFrom(ref input, _map_mapSfixed32Sfixed32_codec);
            break;
          }
          case 82: {
            mapSfixed64Sfixed64_.AddEntriesFrom(ref input, _map_mapSfixed64Sfixed64_codec);
            break;
          }
          case 90: {
            mapInt32Float_.AddEntriesFrom(ref input, _map_mapInt32Float_codec);
            break;
          }
          case 98: {
            mapInt32Double_.AddEntriesFrom(ref input, _map_mapInt32Double_codec);
            break;
          }
          case 106: {
            mapBoolBool_.AddEntriesFrom(ref input, _map_mapBoolBool_codec);
            break;
          }
          case 114: {
            mapInt32Enum_.AddEntriesFrom(ref input, _map_mapInt32Enum_codec);
            break;
          }
          case 122: {
            mapInt32ForeignMessage_.AddEntriesFrom(ref input, _map_mapInt32ForeignMessage_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Previously, message containing enum called Type cannot be used as value of
  /// map field.
  /// </summary>
  public sealed partial class MessageContainingEnumCalledType : pb::IMessage<MessageContainingEnumCalledType>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MessageContainingEnumCalledType> _parser = new pb::MessageParser<MessageContainingEnumCalledType>(() => new MessageContainingEnumCalledType());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MessageContainingEnumCalledType> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Google.Protobuf.TestProtos.MapUnittestProto3Reflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MessageContainingEnumCalledType() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MessageContainingEnumCalledType(MessageContainingEnumCalledType other) : this() {
      type_ = other.type_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MessageContainingEnumCalledType Clone() {
      return new MessageContainingEnumCalledType(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private static readonly pbc::MapField<int, global::Google.Protobuf.TestProtos.MessageContainingEnumCalledType>.Codec _map_type_codec
        = new pbc::MapField<int, global::Google.Protobuf.TestProtos.MessageContainingEnumCalledType>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForMessage(18, global::Google.Protobuf.TestProtos.MessageContainingEnumCalledType.Parser), 10);
    private readonly pbc::MapField<int, global::Google.Protobuf.TestProtos.MessageContainingEnumCalledType> type_ = new pbc::MapField<int, global::Google.Protobuf.TestProtos.MessageContainingEnumCalledType>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, global::Google.Protobuf.TestProtos.MessageContainingEnumCalledType> Type {
      get { return type_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MessageContainingEnumCalledType);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MessageContainingEnumCalledType other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!Type.Equals(other.Type)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= Type.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      type_.WriteTo(output, _map_type_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      type_.WriteTo(ref output, _map_type_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += type_.CalculateSize(_map_type_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MessageContainingEnumCalledType other) {
      if (other == null) {
        return;
      }
      type_.MergeFrom(other.type_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            type_.AddEntriesFrom(input, _map_type_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            type_.AddEntriesFrom(ref input, _map_type_codec);
            break;
          }
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the MessageContainingEnumCalledType message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      public enum Type {
        [pbr::OriginalName("TYPE_FOO")] Foo = 0,
      }

    }
    #endregion

  }

  /// <summary>
  /// Previously, message cannot contain map field called "entry".
  /// </summary>
  public sealed partial class MessageContainingMapCalledEntry : pb::IMessage<MessageContainingMapCalledEntry>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MessageContainingMapCalledEntry> _parser = new pb::MessageParser<MessageContainingMapCalledEntry>(() => new MessageContainingMapCalledEntry());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MessageContainingMapCalledEntry> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Google.Protobuf.TestProtos.MapUnittestProto3Reflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MessageContainingMapCalledEntry() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MessageContainingMapCalledEntry(MessageContainingMapCalledEntry other) : this() {
      entry_ = other.entry_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MessageContainingMapCalledEntry Clone() {
      return new MessageContainingMapCalledEntry(this);
    }

    /// <summary>Field number for the "entry" field.</summary>
    public const int EntryFieldNumber = 1;
    private static readonly pbc::MapField<int, int>.Codec _map_entry_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 10);
    private readonly pbc::MapField<int, int> entry_ = new pbc::MapField<int, int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> Entry {
      get { return entry_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MessageContainingMapCalledEntry);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MessageContainingMapCalledEntry other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!Entry.Equals(other.Entry)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= Entry.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      entry_.WriteTo(output, _map_entry_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      entry_.WriteTo(ref output, _map_entry_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += entry_.CalculateSize(_map_entry_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MessageContainingMapCalledEntry other) {
      if (other == null) {
        return;
      }
      entry_.MergeFrom(other.entry_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            entry_.AddEntriesFrom(input, _map_entry_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            entry_.AddEntriesFrom(ref input, _map_entry_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
