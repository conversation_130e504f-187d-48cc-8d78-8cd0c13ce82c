/*****************************************************************************

Copyright (c) 1996, 2025, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License, version 2.0, as published by the
Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON><PERSON>ITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0,
for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

*****************************************************************************/

/** @file include/dict0crea.ic
 Database object creation

 Created 1/8/1996 Heikki Tuuri
 *******************************************************/

#include "ha_prototypes.h"

#include "mem0mem.h"

/** Checks if a table name contains the string "/#sql" which denotes temporary
 tables in MySQL.
 @return true if temporary table */
[[nodiscard]] bool row_is_mysql_tmp_table_name(const char *name);
/*!< in: table name in the form
'database/tablename' */

/** Generate a foreign key constraint name when it was not named by the user.
 A generated constraint has a name of the format dbname/tablename_ibfk_NUMBER,
 where the numbers start from 1, and are given locally for this table, that is,
 the number is not global, as it used to be before MySQL 4.0.18.  */
static inline dberr_t dict_create_add_foreign_id(
    ulint *id_nr,            /*!< in/out: number to use in id generation;
                             incremented if used */
    const char *name,        /*!< in: table name */
    dict_foreign_t *foreign) /*!< in/out: foreign key */
{
  DBUG_TRACE;

  if (foreign->id == nullptr) {
    /* Generate a new constraint id */
    ulint namelen = strlen(name);
    char *id = static_cast<char *>(mem_heap_alloc(foreign->heap, namelen + 20));

    if (row_is_mysql_tmp_table_name(name)) {
      /* no overflow if number < 1e13 */
      sprintf(id, "%s_ibfk_%lu", name, (ulong)(*id_nr)++);
    } else {
      char table_name[MAX_TABLE_NAME_LEN + 20 + 1] = "";
      uint errors = 0;

      strncpy(table_name, name, MAX_TABLE_NAME_LEN + 20);

      innobase_convert_to_system_charset(strchr(table_name, '/') + 1,
                                         strchr(name, '/') + 1,
                                         MAX_TABLE_NAME_LEN, &errors);

      if (errors) {
        strncpy(table_name, name, MAX_TABLE_NAME_LEN + 20);
      }

      /* no overflow if number < 1e13 */
      sprintf(id, "%s_ibfk_%lu", table_name, (ulong)(*id_nr)++);

      if (innobase_check_identifier_length(strchr(id, '/') + 1)) {
        /* Server has already done this check,
        So assert here */
        ut_d(ut_error);
        ut_o(return DB_IDENTIFIER_TOO_LONG);
      }
    }
    foreign->id = id;

    DBUG_PRINT("dict_create_add_foreign_id", ("generated foreign id: %s", id));
  }

  return DB_SUCCESS;
}

/** Compose a column number for a virtual column, stored in the "POS" field
of Sys_columns. The column number includes both its virtual column sequence
(the "nth" virtual column) and its actual column position in original table
@param[in]      v_pos           virtual column sequence
@param[in]      col_pos         column position in original table definition
@return composed column position number */
static inline ulint dict_create_v_col_pos(ulint v_pos, ulint col_pos) {
  ut_ad(v_pos <= REC_MAX_N_FIELDS);
  ut_ad(col_pos <= REC_MAX_N_FIELDS);

  return (((v_pos + 1) << 16) + col_pos);
}

/** Get the column number for a virtual column (the column position in
original table), stored in the "POS" field of Sys_columns
@param[in]      pos             virtual column position
@return column position in original table */
static inline ulint dict_get_v_col_mysql_pos(ulint pos) {
  return (pos & 0xFFFF);
}

/** Get a virtual column sequence (the "nth" virtual column) for a
virtual column, stord in the "POS" field of Sys_columns
@param[in]      pos             virtual column position
@return virtual column sequence */
static inline ulint dict_get_v_col_pos(ulint pos) { return ((pos >> 16) - 1); }
