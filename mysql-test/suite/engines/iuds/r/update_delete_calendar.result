DROP TABLE IF EXISTS t1,t2;
CREATE TABLE t1(c1 DATE NOT NULL PRIMARY KEY, c2 DATE NULL, c3 INT, INDEX idx2(c2));
INSERT INTO t1 VALUE('1998-01-01','10-01-01',1),('2001-01-01','70-01-01',2);
INSERT INTO t1 VALUE('2001-01-03','2001-01-04 09:34:00',3);
SELECT * FROM t1;
c1	c2	c3
1998-01-01	2010-01-01	1
2001-01-01	1970-01-01	2
2001-01-03	2001-01-04	3
UPDATE t1 SET c1='69-02-02' WHERE c2='10-01-01';
SELECT year(c1),month(c1),day(c1),year(c2) FROM t1 WHERE c2='10-01-01';
year(c1)	month(c1)	day(c1)	year(c2)
2069	2	2	2010
UPDATE t1 SET c1='1969-02-02',c2='1910-02-02' WHERE c2='10-01-01';
SELECT * FROM t1 WHERE c1='1969-02-02' AND c2='1910-02-02';
c1	c2	c3
1969-02-02	1910-02-02	1
UPDATE t1 SET c1='01-02-02',c2='2070-02-02' WHERE c2='70-01-01';
SELECT year(c1),c2 FROM t1 WHERE c1='01-02-02' AND c2='2070-02-02';
year(c1)	c2
2001	2070-02-02
UPDATE t1 SET c1='2001-01-03 08:30:00',c2='2001-01-05' WHERE c2='2001-01-04';
Warnings:
Note	1292	Incorrect date value: '2001-01-03 08:30:00' for column 'c1' at row 1
SELECT c1,hour(c1),minute(c1),second(c1) FROM t1 WHERE c2='2001-01-05';
c1	hour(c1)	minute(c1)	second(c1)
2001-01-03	0	0	0
INSERT INTO t1 VALUES('2009-02-25','09-02-27',4);
SET TIMESTAMP=1235553613;
UPDATE t1 SET c2=NOW() WHERE c1 >=CURDATE();
Warnings:
Note	1292	Incorrect date value: '2009-02-25 12:20:13' for column 'c2' at row 1
UPDATE t1 SET c1=DATE_ADD(CURDATE(),INTERVAL '1 1:1:1' DAY_SECOND) WHERE c2=CURDATE();
Warnings:
Note	1292	Incorrect date value: '2009-02-26 01:01:01' for column 'c1' at row 1
SELECT * FROM t1;
c1	c2	c3
1969-02-02	1910-02-02	1
2001-01-03	2001-01-05	3
2001-02-02	2070-02-02	2
2009-02-26	2009-02-25	4
INSERT INTO t1 VALUES('2001-01-06','2001-01-07',5),('2001-01-08',NULL,6);
UPDATE t1 SET c2=NULL WHERE c1='2001-01-06';
SELECT * FROM t1 WHERE c2 IS NULL;
c1	c2	c3
2001-01-06	NULL	5
2001-01-08	NULL	6
UPDATE t1 SET c2='2001-01-10' WHERE c2 IS NULL;
SELECT * FROM t1 WHERE c2='2001-01-10';
c1	c2	c3
2001-01-06	2001-01-10	5
2001-01-08	2001-01-10	6
INSERT INTO t1 VALUES('2001-01-11','2001-01-11',7),('2001-01-12','2001-01-12',8),('2001-01-13','2001-01-13',9),('2001-01-14','2001-01-14',10),('2001-01-15','2001-01-15',11),('2001-01-16','2001-01-16',12);
UPDATE t1 SET c1='10:45:15',c2='2009-01-32' WHERE c1='2001-01-11';
ERROR 22007: Incorrect date value: '10:45:15' for column 'c1' at row 1
UPDATE t1 SET c2='2009-02-30' WHERE c2='2001-01-12';
ERROR 22007: Incorrect date value: '2009-02-30' for column 'c2' at row 1
UPDATE t1 SET c2='2009-04-31' WHERE c2='2001-01-13';
ERROR 22007: Incorrect date value: '2009-04-31' for column 'c2' at row 1
UPDATE t1 SET c2='2009-06-31' WHERE c2='2001-01-14';
ERROR 22007: Incorrect date value: '2009-06-31' for column 'c2' at row 1
UPDATE t1 SET c2='2009-09-31' WHERE c2='2001-01-15';
ERROR 22007: Incorrect date value: '2009-09-31' for column 'c2' at row 1
SELECT count(*) FROM t1 WHERE c2='10:45:15' OR c2='2009-01-32' OR c2='2009-02-30' OR c2='2009-04-31' OR c2='2009-06-31' OR c2='2009-09-31' /* returns 0 */;
ERROR HY000: Incorrect DATE value: '10:45:15'
UPDATE t1 SET c2='2009-11-31' WHERE c2='2001-01-16';
ERROR 22007: Incorrect date value: '2009-11-31' for column 'c2' at row 1
SELECT * FROM t1 WHERE c2='0000-00-00';
ERROR HY000: Incorrect DATE value: '0000-00-00'
SET SQL_MODE=ALLOW_INVALID_DATES;
SELECT * FROM t1 WHERE c2='0000-00-00';
c1	c2	c3
INSERT INTO t1 VALUES('2001-01-27','2001-01-28',19);
UPDATE t1 SET c1='2009-04-31',c2='2009-02-30' WHERE c3=19;
SELECT c1,c2,day(c1),day(c2) FROM t1 WHERE c3=19;
c1	c2	day(c1)	day(c2)
2009-04-31	2009-02-30	31	30
SET SQL_MODE=DEFAULT;
INSERT INTO t1 VALUES('2001-01-17','0000-00-00',13);
ERROR 22007: Incorrect date value: '0000-00-00' for column 'c2' at row 1
INSERT INTO t1 VALUES('2001-01-18','2001-01-18',14);
SET SQL_MODE=TRADITIONAL;
SELECT * FROM t1 WHERE c2='0000-00-00';
ERROR HY000: Incorrect DATE value: '0000-00-00'
UPDATE t1 SET c2='0000-00-00' WHERE c2='2001-01-18';
ERROR 22007: Incorrect date value: '0000-00-00' for column 'c2' at row 1
SET SQL_MODE=DEFAULT;
UPDATE t1 SET c2='0000-00-00' WHERE c2='2001-01-18';
ERROR 22007: Incorrect date value: '0000-00-00' for column 'c2' at row 1
UPDATE t1 SET c2='2001-01-18' WHERE c2='0000-00-00' AND c1='2001-01-17';
ERROR 22007: Incorrect date value: '0000-00-00' for column 'c2' at row 1
SELECT * FROM t1 WHERE c1='0000-00-00' OR c2='0000-00-00';
ERROR HY000: Incorrect DATE value: '0000-00-00'
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
INSERT INTO t1 VALUES('2001-01-19','2001-01-20',15);
UPDATE t1 SET c1='2010-00-01',c2='2010-10-00' WHERE c3=15;
SELECT *,month(c1),day(c2) from t1 WHERE c3=15;
c1	c2	c3	month(c1)	day(c2)
2010-00-01	2010-10-00	15	0	0
INSERT INTO t1 VALUES('2001-01-21','2001-01-22',16);
UPDATE t1 SET c1='1000-01-01',c2='9999-12-31' WHERE c1='2001-01-21';
SELECT * FROM t1 WHERE c1='1000-01-01' AND c2='9999-12-31';
c1	c2	c3
1000-01-01	9999-12-31	16
INSERT INTO t1 VALUES('2001-01-23','2001-01-24',17),('2001-01-25','2001-01-26',18);
UPDATE t1 SET c1='2001-01-23' WHERE c1='2001-01-25';
ERROR 23000: Duplicate entry '2001-01-23' for key 't1.PRIMARY'
UPDATE IGNORE t1 SET c1='2001-01-23' WHERE c1='2001-01-25';
Warnings:
Warning	1062	Duplicate entry '2001-01-23' for key 't1.PRIMARY'
SELECT count(*) FROM t1 WHERE c1='2001-01-23';
count(*)
1
UPDATE t1 SET c1=NULL WHERE c2='2001-01-24' /* updates to 0000-00-00 */;
ERROR 23000: Column 'c1' cannot be null
UPDATE IGNORE t1 SET c1=NULL WHERE c2='2001-01-24';
Warnings:
Warning	1048	Column 'c1' cannot be null
SELECT * FROM t1 WHERE c1='0000-00-00';
ERROR HY000: Incorrect DATE value: '0000-00-00'
TRUNCATE TABLE t1;
CREATE TABLE t2(c1 DATE NOT NULL PRIMARY KEY, c2 DATE, c3 INT);
INSERT INTO t1 VALUES('2001-01-02','2001-01-01',1),('2001-01-03','2001-01-02',2);
INSERT INTO t2 VALUES('2001-01-02','2001-01-03',1),('2001-01-04','2001-01-05',2);
SELECT * FROM t1,t2 WHERE t2.c1=t1.c1;
c1	c2	c3	c1	c2	c3
2001-01-02	2001-01-01	1	2001-01-02	2001-01-03	1
UPDATE t1,t2 SET t2.c1='2005-01-06',t1.c1='2005-01-06' WHERE t2.c1=t1.c1;
SELECT * FROM t1,t2 WHERE t2.c1=t1.c1;
c1	c2	c3	c1	c2	c3
2005-01-06	2001-01-01	1	2005-01-06	2001-01-03	1
INSERT INTO t1 VALUES('2002-01-01','2002-01-02',3),('2002-01-04','2002-01-02',4);
INSERT INTO t2 VALUES('2002-01-02','2002-01-03',3),('2002-01-03','2002-01-04',4);
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2002-01-01	2002-01-02	3	2002-01-02	2002-01-03	3
2002-01-04	2002-01-02	4	2002-01-02	2002-01-03	3
UPDATE t1,t2 SET t2.c1='2002-01-06',t1.c2='2002-01-06' WHERE t2.c1=t1.c2;
SELECT * FROM t1,t2 WHERE t2.c1='2002-01-06' AND t1.c2='2002-01-06';
c1	c2	c3	c1	c2	c3
2002-01-01	2002-01-06	3	2002-01-06	2002-01-03	3
2002-01-04	2002-01-06	4	2002-01-06	2002-01-03	3
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2002-01-01	2002-01-06	3	2002-01-06	2002-01-03	3
2002-01-04	2002-01-06	4	2002-01-06	2002-01-03	3
DELETE FROM t1 WHERE c2='2002-01-06' AND c3=3;
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2002-01-04	2002-01-06	4	2002-01-06	2002-01-03	3
DROP TABLE t2;
TRUNCATE TABLE t1;
ALTER TABLE t1 CHANGE c1 c1 DATE NOT NULL, DROP PRIMARY KEY;
ALTER TABLE t1 CHANGE c1 c1 DATE NOT NULL, ADD KEY(c1);
UPDATE t1 SET c1='2002-01-07' WHERE c2='2002-01-05';
SELECT * FROM t1 WHERE c1='2002-01-07';
c1	c2	c3
INSERT INTO t1 VALUES('2002-01-08','2002-01-09',4),('2002-01-10','2002-01-11',5),('2002-01-12','2002-01-13',6),('2002-01-14','2002-01-15',7),('2002-01-16','2002-01-17',8),('2002-01-18','2002-01-19',9),('2002-01-20','2002-01-21',10),('2002-01-22','2002-01-23',11),('2002-01-24','2002-01-25',12),('2002-01-26','2002-01-27',13),('2002-01-28','2002-01-29',14),('2002-01-30','2002-01-31',15),('2002-02-01','2002-02-02',16),('2002-02-03','2002-02-04',17),('2002-02-05','2002-02-06',18),('2002-02-07','2002-02-08',19),('2002-02-09','2002-02-10',20);
SELECT * FROM t1;
c1	c2	c3
2002-01-08	2002-01-09	4
2002-01-10	2002-01-11	5
2002-01-12	2002-01-13	6
2002-01-14	2002-01-15	7
2002-01-16	2002-01-17	8
2002-01-18	2002-01-19	9
2002-01-20	2002-01-21	10
2002-01-22	2002-01-23	11
2002-01-24	2002-01-25	12
2002-01-26	2002-01-27	13
2002-01-28	2002-01-29	14
2002-01-30	2002-01-31	15
2002-02-01	2002-02-02	16
2002-02-03	2002-02-04	17
2002-02-05	2002-02-06	18
2002-02-07	2002-02-08	19
2002-02-09	2002-02-10	20
UPDATE t1 SET c1='2003-01-01' WHERE c2 <> '2002-01-09' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <> '2002-01-09' ORDER BY c2;
c1	c2	c3
2003-01-01	2002-01-11	5
2003-01-01	2002-01-13	6
2002-01-14	2002-01-15	7
2002-01-16	2002-01-17	8
2002-01-18	2002-01-19	9
2002-01-20	2002-01-21	10
2002-01-22	2002-01-23	11
2002-01-24	2002-01-25	12
2002-01-26	2002-01-27	13
2002-01-28	2002-01-29	14
2002-01-30	2002-01-31	15
2002-02-01	2002-02-02	16
2002-02-03	2002-02-04	17
2002-02-05	2002-02-06	18
2002-02-07	2002-02-08	19
2002-02-09	2002-02-10	20
UPDATE t1 SET c1='2003-01-01' WHERE c2 >= '2002-01-13' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '2002-01-13' ORDER BY c2 DESC;
c1	c2	c3
2003-01-01	2002-02-10	20
2003-01-01	2002-02-08	19
2002-02-05	2002-02-06	18
2002-02-03	2002-02-04	17
2002-02-01	2002-02-02	16
2002-01-30	2002-01-31	15
2002-01-28	2002-01-29	14
2002-01-26	2002-01-27	13
2002-01-24	2002-01-25	12
2002-01-22	2002-01-23	11
2002-01-20	2002-01-21	10
2002-01-18	2002-01-19	9
2002-01-16	2002-01-17	8
2002-01-14	2002-01-15	7
2003-01-01	2002-01-13	6
UPDATE t1 SET c1='2003-01-01' WHERE c2 <='2002-01-21' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <='2002-01-21' ORDER BY c2;
c1	c2	c3
2003-01-01	2002-01-09	4
2003-01-01	2002-01-11	5
2003-01-01	2002-01-13	6
2002-01-14	2002-01-15	7
2002-01-16	2002-01-17	8
2002-01-18	2002-01-19	9
2002-01-20	2002-01-21	10
UPDATE t1 SET c1='2003-01-01' WHERE c2 <=> '2002-01-23' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '2002-01-23' ORDER BY c2 DESC;
c1	c2	c3
2003-01-01	2002-01-23	11
UPDATE t1 SET c1='2003-01-01' WHERE c2 BETWEEN '2002-01-25' AND '2002-01-29' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '2002-01-25' AND '2002-01-29' ORDER BY c2;
c1	c2	c3
2003-01-01	2002-01-25	12
2003-01-01	2002-01-27	13
2002-01-28	2002-01-29	14
UPDATE t1 SET c1='2003-01-01' WHERE c2 IN('2002-01-29','2002-01-31') ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IN('2002-01-29','2002-01-31') ORDER BY c2 DESC;
c1	c2	c3
2003-01-01	2002-01-31	15
2003-01-01	2002-01-29	14
UPDATE t1 SET c1='2003-01-01' WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
c1	c2	c3
UPDATE t1 SET c1='2003-01-01' WHERE c2>= '2002-02-06' AND c1 < '2002-02-09' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2>= '2002-02-06' AND c1 < '2002-02-09' ORDER BY c2;
c1	c2	c3
INSERT INTO t1 VALUES('2002-03-01','2002-03-01',1),('2002-03-03','2002-03-04',2),('2002-03-05','2002-03-06',3),('2005-01-01','2005-01-02',4),('2005-01-03','2005-01-04',5),('2005-01-05','2005-01-06',6),('2005-01-07','2005-01-08',7),('2005-01-09',NULL,8);
SELECT * FROM t1;
c1	c2	c3
2002-01-14	2002-01-15	7
2002-01-16	2002-01-17	8
2002-01-18	2002-01-19	9
2002-01-20	2002-01-21	10
2002-02-01	2002-02-02	16
2002-02-03	2002-02-04	17
2002-03-01	2002-03-01	1
2002-03-03	2002-03-04	2
2002-03-05	2002-03-06	3
2003-01-01	2002-01-09	4
2003-01-01	2002-01-11	5
2003-01-01	2002-01-13	6
2003-01-01	2002-01-23	11
2003-01-01	2002-01-25	12
2003-01-01	2002-01-27	13
2003-01-01	2002-01-29	14
2003-01-01	2002-01-31	15
2003-01-01	2002-02-06	18
2003-01-01	2002-02-08	19
2003-01-01	2002-02-10	20
2005-01-01	2005-01-02	4
2005-01-03	2005-01-04	5
2005-01-05	2005-01-06	6
2005-01-07	2005-01-08	7
2005-01-09	NULL	8
SELECT c1 FROM t1;
c1
2002-01-14
2002-01-16
2002-01-18
2002-01-20
2002-02-01
2002-02-03
2002-03-01
2002-03-03
2002-03-05
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2005-01-01
2005-01-03
2005-01-05
2005-01-07
2005-01-09
SELECT c1 FROM t1 ORDER BY c1 DESC;
c1
2005-01-09
2005-01-07
2005-01-05
2005-01-03
2005-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2003-01-01
2002-03-05
2002-03-03
2002-03-01
2002-02-03
2002-02-01
2002-01-20
2002-01-18
2002-01-16
2002-01-14
SELECT * FROM t1 WHERE c1='2003-01-01';
c1	c2	c3
2003-01-01	2002-01-09	4
2003-01-01	2002-01-11	5
2003-01-01	2002-01-13	6
2003-01-01	2002-01-23	11
2003-01-01	2002-01-25	12
2003-01-01	2002-01-27	13
2003-01-01	2002-01-29	14
2003-01-01	2002-01-31	15
2003-01-01	2002-02-06	18
2003-01-01	2002-02-08	19
2003-01-01	2002-02-10	20
SELECT * FROM t1 WHERE c1 <> '2003-01-01' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
2002-01-14	2002-01-15	7
2002-01-16	2002-01-17	8
SELECT * FROM t1 WHERE c1 <> '2003-01-01' ORDER BY c1,c2 DESC;
c1	c2	c3
2002-01-14	2002-01-15	7
2002-01-16	2002-01-17	8
2002-01-18	2002-01-19	9
2002-01-20	2002-01-21	10
2002-02-01	2002-02-02	16
2002-02-03	2002-02-04	17
2002-03-01	2002-03-01	1
2002-03-03	2002-03-04	2
2002-03-05	2002-03-06	3
2005-01-01	2005-01-02	4
2005-01-03	2005-01-04	5
2005-01-05	2005-01-06	6
2005-01-07	2005-01-08	7
2005-01-09	NULL	8
SELECT * FROM t1 WHERE c1 > '2003-01-01' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
2005-01-01	2005-01-02	4
2005-01-03	2005-01-04	5
SELECT * FROM t1 WHERE c1 >= '2003-01-01' ORDER BY c1,c2;
c1	c2	c3
2003-01-01	2002-01-09	4
2003-01-01	2002-01-11	5
2003-01-01	2002-01-13	6
2003-01-01	2002-01-23	11
2003-01-01	2002-01-25	12
2003-01-01	2002-01-27	13
2003-01-01	2002-01-29	14
2003-01-01	2002-01-31	15
2003-01-01	2002-02-06	18
2003-01-01	2002-02-08	19
2003-01-01	2002-02-10	20
2005-01-01	2005-01-02	4
2005-01-03	2005-01-04	5
2005-01-05	2005-01-06	6
2005-01-07	2005-01-08	7
2005-01-09	NULL	8
SELECT * FROM t1 WHERE c1 < '2003-01-01' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
2002-01-14	2002-01-15	7
2002-01-16	2002-01-17	8
SELECT * FROM t1 WHERE c1 <= '2003-01-01' ORDER BY c1,c2 DESC;
c1	c2	c3
2002-01-14	2002-01-15	7
2002-01-16	2002-01-17	8
2002-01-18	2002-01-19	9
2002-01-20	2002-01-21	10
2002-02-01	2002-02-02	16
2002-02-03	2002-02-04	17
2002-03-01	2002-03-01	1
2002-03-03	2002-03-04	2
2002-03-05	2002-03-06	3
2003-01-01	2002-02-10	20
2003-01-01	2002-02-08	19
2003-01-01	2002-02-06	18
2003-01-01	2002-01-31	15
2003-01-01	2002-01-29	14
2003-01-01	2002-01-27	13
2003-01-01	2002-01-25	12
2003-01-01	2002-01-23	11
2003-01-01	2002-01-13	6
2003-01-01	2002-01-11	5
2003-01-01	2002-01-09	4
SELECT * FROM t1 WHERE c1 <=> '2003-01-01' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
2003-01-01	2002-02-10	20
2003-01-01	2002-02-08	19
SELECT * FROM t1 WHERE c1 <=> '2003-01-01' ORDER BY c1,c2;
c1	c2	c3
2003-01-01	2002-01-09	4
2003-01-01	2002-01-11	5
2003-01-01	2002-01-13	6
2003-01-01	2002-01-23	11
2003-01-01	2002-01-25	12
2003-01-01	2002-01-27	13
2003-01-01	2002-01-29	14
2003-01-01	2002-01-31	15
2003-01-01	2002-02-06	18
2003-01-01	2002-02-08	19
2003-01-01	2002-02-10	20
SELECT * FROM t1 WHERE c1 BETWEEN '2002-03-01' AND '2003-01-01' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
2002-03-01	2002-03-01	1
2002-03-03	2002-03-04	2
SELECT * FROM t1 WHERE c1 BETWEEN '2002-03-01' AND '2003-01-01' ORDER BY c1,c2 DESC;
c1	c2	c3
2002-03-01	2002-03-01	1
2002-03-03	2002-03-04	2
2002-03-05	2002-03-06	3
2003-01-01	2002-02-10	20
2003-01-01	2002-02-08	19
2003-01-01	2002-02-06	18
2003-01-01	2002-01-31	15
2003-01-01	2002-01-29	14
2003-01-01	2002-01-27	13
2003-01-01	2002-01-25	12
2003-01-01	2002-01-23	11
2003-01-01	2002-01-13	6
2003-01-01	2002-01-11	5
2003-01-01	2002-01-09	4
SELECT * FROM t1 WHERE c1 IN('2003-01-01','2005-01-07') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
2003-01-01	2002-02-10	20
2003-01-01	2002-02-08	19
SELECT * FROM t1 WHERE c1 IN('2003-01-01','2005-01-07') ORDER BY c1,c2;
c1	c2	c3
2003-01-01	2002-01-09	4
2003-01-01	2002-01-11	5
2003-01-01	2002-01-13	6
2003-01-01	2002-01-23	11
2003-01-01	2002-01-25	12
2003-01-01	2002-01-27	13
2003-01-01	2002-01-29	14
2003-01-01	2002-01-31	15
2003-01-01	2002-02-06	18
2003-01-01	2002-02-08	19
2003-01-01	2002-02-10	20
2005-01-07	2005-01-08	7
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2,c1 LIMIT 2;
c1	c2	c3
2005-01-09	NULL	8
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2,c1 DESC;
c1	c2	c3
2005-01-09	NULL	8
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1 DESC LIMIT 2;
c1	c2	c3
2003-01-01	2002-01-09	4
2003-01-01	2002-01-11	5
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1;
c1	c2	c3
2003-01-01	2002-01-09	4
2003-01-01	2002-01-11	5
2003-01-01	2002-01-13	6
2002-01-14	2002-01-15	7
2002-01-16	2002-01-17	8
2002-01-18	2002-01-19	9
2002-01-20	2002-01-21	10
2003-01-01	2002-01-23	11
2003-01-01	2002-01-25	12
2003-01-01	2002-01-27	13
2003-01-01	2002-01-29	14
2003-01-01	2002-01-31	15
2002-02-01	2002-02-02	16
2002-02-03	2002-02-04	17
2003-01-01	2002-02-06	18
2003-01-01	2002-02-08	19
2003-01-01	2002-02-10	20
2002-03-01	2002-03-01	1
2002-03-03	2002-03-04	2
2002-03-05	2002-03-06	3
2005-01-01	2005-01-02	4
2005-01-03	2005-01-04	5
2005-01-05	2005-01-06	6
2005-01-07	2005-01-08	7
SELECT * FROM t1 WHERE c1>='2003-01-01' AND c1 <= '2005-01-07' ORDER BY c2,c1 DESC LIMIT 2;
c1	c2	c3
2003-01-01	2002-01-09	4
2003-01-01	2002-01-11	5
SELECT * FROM t1 WHERE c1>='2003-01-01' AND c1 <= '2005-01-07' ORDER BY c2,c1;
c1	c2	c3
2003-01-01	2002-01-09	4
2003-01-01	2002-01-11	5
2003-01-01	2002-01-13	6
2003-01-01	2002-01-23	11
2003-01-01	2002-01-25	12
2003-01-01	2002-01-27	13
2003-01-01	2002-01-29	14
2003-01-01	2002-01-31	15
2003-01-01	2002-02-06	18
2003-01-01	2002-02-08	19
2003-01-01	2002-02-10	20
2005-01-01	2005-01-02	4
2005-01-03	2005-01-04	5
2005-01-05	2005-01-06	6
2005-01-07	2005-01-08	7
SELECT * FROM t1 WHERE c1='2003-01-03' OR c2='2002-03-06';
c1	c2	c3
2002-03-05	2002-03-06	3
UPDATE t1 SET c1='2006-01-01' WHERE c1='2003-01-03' OR c2='2002-03-06';
SELECT * FROM t1 WHERE c1='2006-01-01' OR c2='2002-03-04';
c1	c2	c3
2002-03-03	2002-03-04	2
2006-01-01	2002-03-06	3
TRUNCATE TABLE t1;
ALTER TABLE t1 CHANGE c1 c1 DATE NOT NULL, ADD PRIMARY KEY(c1);
INSERT INTO t1 VALUES(NOW(),NOW(),3),(ADDTIME(NOW(),'1 01:01:01'),ADDTIME(NOW(),'1 01:01:01'),4),(ADDTIME(NOW(),'2 01:01:01'),ADDTIME(NOW(),'2 01:01:01'),5),(ADDTIME(NOW(),'3 01:01:01'),ADDTIME(NOW(),'3 01:01:01'),6);
INSERT INTO t1 VALUES('1000-01-01','1000-01-01',1),('9999-12-31','9999-12-31',2);
SELECT * FROM t1 WHERE c1 <='1000-01-01';
c1	c2	c3
1000-01-01	1000-01-01	1
DELETE FROM t1 WHERE c1 <='1000-01-01';
SELECT * FROM t1 WHERE c1 <='1000-01-01';
c1	c2	c3
SELECT * FROM t1 WHERE c1 >='9999-12-31';
c1	c2	c3
9999-12-31	9999-12-31	2
DELETE FROM t1 WHERE c1 >='9999-12-31';
SELECT * FROM t1 WHERE c1 >='9999-12-31';
c1	c2	c3
SELECT * FROM t1 WHERE c1 > ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
2009-02-28	2009-02-28	6
2009-02-27	2009-02-27	5
DELETE FROM t1 WHERE c1 > ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c1 <> CURDATE() ORDER BY c1;
c1	c2	c3
2009-02-26	2009-02-26	4
DELETE FROM t1 WHERE c1 <> CURDATE() ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <> CURDATE() ORDER BY c1;
c1	c2	c3
INSERT INTO t1 VALUES('2001-01-01',NOW(),7),(ADDTIME(NOW(),'1 01:01:01'),ADDTIME(NOW(),'1 01:01:01'),8),(ADDTIME(NOW(),'2 01:01:01'),ADDTIME(NOW(),'2 01:01:01'),9),(ADDTIME(NOW(),'3 01:01:01'),ADDTIME(NOW(),'3 01:01:01'),10);
SELECT * FROM t1 WHERE c1 < ADDTIME(NOW(),'2 01:01:01') ORDER BY c1;
c1	c2	c3
2001-01-01	2009-02-25	7
2009-02-25	2009-02-25	3
2009-02-26	2009-02-26	8
2009-02-27	2009-02-27	9
DELETE FROM t1 WHERE c1 < ADDTIME(NOW(),'2 01:01:01') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < ADDTIME(NOW(),'2 01:01:01') ORDER BY c1;
c1	c2	c3
2009-02-26	2009-02-26	8
2009-02-27	2009-02-27	9
SELECT * FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
Warnings:
Note	1292	Incorrect date value: '2009-02-26 13:21:14' for column 'c1' at row 1
DELETE FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
Warnings:
Note	1292	Incorrect date value: '2009-02-26 13:21:14' for column 'c1' at row 1
SELECT * FROM t1 WHERE c1 <=> ADDDATE(CURDATE(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
2009-02-26	2009-02-26	8
Warnings:
Warning	1292	Truncated incorrect INTEGER value: '1 01:01:01'
Warning	1292	Truncated incorrect INTEGER value: '1 01:01:01'
DELETE FROM t1 WHERE c1 <=> ADDDATE(CURDATE(),'1') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> ADDDATE(CURDATE(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
Warnings:
Warning	1292	Truncated incorrect INTEGER value: '1 01:01:01'
INSERT INTO t1 VALUES('2001-01-01',NOW(),11);
Warnings:
Note	1292	Incorrect date value: '2009-02-25 12:20:13' for column 'c2' at row 1
INSERT INTO t1 VALUES(ADDTIME(NOW(),'1 01:01:01'),ADDTIME(NOW(),'1 01:01:01'),12);
Warnings:
Note	1292	Incorrect date value: '2009-02-26 13:21:14' for column 'c1' at row 1
Note	1292	Incorrect date value: '2009-02-26 13:21:14' for column 'c2' at row 1
INSERT INTO t1 VALUES(ADDTIME(NOW(),'4 01:01:01'),NULL,13),(ADDTIME(NOW(),'5 01:01:01'),NULL,14);
SELECT * FROM t1 WHERE c1 BETWEEN CURDATE() AND ADDTIME(NOW(),'2 01:01:01') ORDER BY c1;
c1	c2	c3
2009-02-26	2009-02-26	12
2009-02-27	2009-02-27	9
DELETE FROM t1 WHERE c1 BETWEEN CURDATE() AND ADDTIME(NOW(),'2 01:01:01') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN CURDATE() AND ADDTIME(NOW(),'2 01:01:01') ORDER BY c1;
c1	c2	c3
SELECT * FROM t1 WHERE c1 IN(CURDATE(),ADDDATE(CURDATE(),'3 01:01:01')) ORDER BY c1 DESC;
c1	c2	c3
2009-02-28	2009-02-28	10
DELETE FROM t1 WHERE c1 IN(CURDATE(),ADDDATE(CURDATE(),'3')) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN(CURDATE(),ADDDATE(CURDATE(),'3 01:01:01')) ORDER BY c1 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2,c1;
c1	c2	c3
2009-03-01	NULL	13
2009-03-02	NULL	14
DELETE FROM t1 WHERE c2 IS NULL ORDER BY c2,c1 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2,c1;
c1	c2	c3
INSERT INTO t1 VALUES(ADDTIME(NOW(),'6 01:01:01'),ADDTIME(NOW(),'6 01:01:01'),15),(ADDTIME(NOW(),'7 01:01:01'),NULL,16),(ADDTIME(NOW(),'8 01:01:01'),ADDTIME(NOW(),'8 01:01:01'),17),(ADDTIME(NOW(),'9 01:01:01'),ADDTIME(NOW(),'9 01:01:01'),18),(ADDTIME(NOW(),'10 01:01:01'),ADDTIME(NOW(),'10 01:01:01'),19);
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1 DESC;
c1	c2	c3
2001-01-01	2009-02-25	11
2009-03-03	2009-03-03	15
2009-03-05	2009-03-05	17
2009-03-06	2009-03-06	18
2009-03-07	2009-03-07	19
DELETE FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1 DESC;
c1	c2	c3
2009-03-05	2009-03-05	17
2009-03-06	2009-03-06	18
2009-03-07	2009-03-07	19
SELECT * FROM t1 WHERE c1>=ADDTIME(NOW(),'5 01:01:01') AND c2 < ADDTIME(NOW(),'8 01:01:01') ORDER BY c1;
c1	c2	c3
2009-03-05	2009-03-05	17
DELETE FROM t1 WHERE c1>=ADDTIME(NOW(),'5 01:01:01') AND c2 < ADDTIME(NOW(),'8 01:01:01') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1>=ADDTIME(NOW(),'5 01:01:01') AND c2 < ADDTIME(NOW(),'8 01:01:01') ORDER BY c1;
c1	c2	c3
SELECT * FROM t1 WHERE c1=ADDDATE(CURDATE(),'9 01:01:01') OR c2=ADDDATE(CURDATE(),'10 01:01:01');
c1	c2	c3
2009-03-06	2009-03-06	18
2009-03-07	2009-03-07	19
DELETE FROM t1 WHERE c1=ADDDATE(CURDATE(),'9') OR c2=ADDDATE(CURDATE(),'10');
SELECT * FROM t1 WHERE c1=ADDDATE(CURDATE(),'9 01:01:01') OR c2=ADDDATE(CURDATE(),'10 01:01:01');
c1	c2	c3
SELECT count(*) FROM t1;
count(*)
1
TRUNCATE TABLE t1;
SELECT count(*) FROM t1;
count(*)
0
CREATE TABLE t2(c1 DATE NOT NULL PRIMARY KEY, c2 DATE, c3 INT);
INSERT INTO t1 VALUES('2001-01-02','2001-01-01',1),('2001-01-03','2001-01-02',2);
INSERT INTO t2 VALUES('2001-01-02','2001-01-03',1),('2001-01-04','2001-01-05',2);
SELECT * FROM t1,t2 WHERE t2.c1=t1.c1;
c1	c2	c3	c1	c2	c3
2001-01-02	2001-01-01	1	2001-01-02	2001-01-03	1
DELETE t1,t2 FROM t1,t2 WHERE t2.c1=t1.c1;
SELECT * FROM t1,t2 WHERE t2.c1=t1.c1;
c1	c2	c3	c1	c2	c3
TRUNCATE TABLE t1;
TRUNCATE TABLE t2;
INSERT INTO t1 VALUES('2001-01-01','2001-01-02',1),('2001-01-03','2001-01-02',2);
INSERT INTO t2 VALUES('2001-01-02','2001-01-03',1),('2001-01-04','2001-01-05',2);
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2001-01-01	2001-01-02	1	2001-01-02	2001-01-03	1
2001-01-03	2001-01-02	2	2001-01-02	2001-01-03	1
DELETE FROM a1, a2 USING t1 AS a1 INNER JOIN t2 AS a2 WHERE a2.c1=a1.c2;
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
DROP TABLE t1,t2;
CREATE TABLE t1(c1 DATETIME NOT NULL PRIMARY KEY, c2 DATETIME NULL, c3 INT, INDEX idx2(c2));
INSERT INTO t1 VALUE('1998-01-01 09:23:45','10-01-01 14:12:33',1);
UPDATE t1 SET c1='2001-01-01' WHERE c2='10-01-01 14:12:33';
SELECT c1,hour(c1),minute(c1),second(c1) FROM t1 WHERE c2='10-01-01 14:12:33';
c1	hour(c1)	minute(c1)	second(c1)
2001-01-01 00:00:00	0	0	0
INSERT INTO t1 VALUES('2009-01-01 08:42:42','2009-01-02 23:59:59',2);
UPDATE t1 SET c1='2009-01-01 1:2:3',c2='2009-01-02 23:59:59.999999' WHERE c2='2009-01-02 23:59:59';
SELECT c1,ADDTIME(c2,'1 1:1:1.000002') FROM t1 WHERE c3=2;
c1	ADDTIME(c2,'1 1:1:1.000002')
2009-01-01 01:02:03	2009-01-04 01:01:01.000002
INSERT INTO t1 VALUES('2009-02-25 15:45','09-02-27 15:46',3);
SET TIMESTAMP=1235553613;
UPDATE t1 SET c2=NOW() WHERE c1 >=CURDATE();
UPDATE t1 SET c1=DATE_ADD(CURDATE(),INTERVAL '1 1:1:1' DAY_SECOND) WHERE c2=CURRENT_TIMESTAMP();
SELECT * FROM t1;
c1	c2	c3
2001-01-01 00:00:00	2010-01-01 14:12:33	1
2009-01-01 01:02:03	2009-01-03 00:00:00	2
2009-02-26 01:01:01	2009-02-25 12:20:13	3
INSERT INTO t1 VALUES('2001-01-06','2001-01-07 09:30:00',4),('2001-01-08',NULL,5);
SELECT * FROM t1 WHERE c2 IS NULL;
c1	c2	c3
2001-01-08 00:00:00	NULL	5
UPDATE t1 SET c2=NULL WHERE c1='2001-01-06 00:00:00';
SELECT * FROM t1 WHERE c2 IS NULL;
c1	c2	c3
2001-01-06 00:00:00	NULL	4
2001-01-08 00:00:00	NULL	5
UPDATE t1 SET c2='2001-01-10 00:00:00' WHERE c2 IS NULL;
SELECT * FROM t1 WHERE c2='2001-01-10';
c1	c2	c3
2001-01-06 00:00:00	2001-01-10 00:00:00	4
2001-01-08 00:00:00	2001-01-10 00:00:00	5
INSERT INTO t1 VALUES('2001-01-09','2001-01-10',6),('2001-01-11','2001-01-12',7);
UPDATE t1 SET c1='2001-01-09 24:59:59',c2='2009-01-10 23:60:59' WHERE c1='2001-01-09';
ERROR 22007: Incorrect datetime value: '2001-01-09 24:59:59' for column 'c1' at row 1
UPDATE t1 SET c2='2001-01-11 23:59:60' WHERE c1='2001-01-11';
ERROR 22007: Incorrect datetime value: '2001-01-11 23:59:60' for column 'c2' at row 1
SELECT count(*) FROM t1 WHERE c1='2001-01-09 24:59:59' AND c2='2009-01-10 23:60:59';
ERROR HY000: Incorrect DATETIME value: '2001-01-09 24:59:59'
SELECT count(*) FROM t1 WHERE c2='2001-01-11 23:59:60' /* returns 0 */;
ERROR HY000: Incorrect DATETIME value: '2001-01-11 23:59:60'
SELECT * FROM t1 WHERE c1='0000-00-00 00:00:00' OR c2='0000-00-00 00:00:00';
ERROR HY000: Incorrect DATETIME value: '0000-00-00 00:00:00'
INSERT INTO t1 VALUES('2001-01-17','0000-00-00 00:00:00',8);
ERROR 22007: Incorrect datetime value: '0000-00-00 00:00:00' for column 'c2' at row 1
INSERT INTO t1 VALUES('2001-01-18','2001-01-18 07:35',9);
SET SQL_MODE=TRADITIONAL;
SELECT * FROM t1 WHERE c2='0000-00-00 00:00:00';
ERROR HY000: Incorrect DATETIME value: '0000-00-00 00:00:00'
UPDATE t1 SET c2='0000-00-00 00:00:00' WHERE c2='2001-01-18 07:35';
ERROR 22007: Incorrect datetime value: '0000-00-00 00:00:00' for column 'c2' at row 1
SET SQL_MODE=DEFAULT;
UPDATE t1 SET c2='0000-00-00 00:00:00' WHERE c2='2001-01-18 07:35';
ERROR 22007: Incorrect datetime value: '0000-00-00 00:00:00' for column 'c2' at row 1
UPDATE t1 SET c2='2001-01-17 12:34:00' WHERE c2='0000-00-00 00:00:00' AND c1='2001-01-17';
ERROR 22007: Incorrect datetime value: '0000-00-00 00:00:00' for column 'c2' at row 1
SET @OLD_SQL_MODE=@@SQL_MODE, @@SQL_MODE=ALLOW_INVALID_DATES;
SELECT * FROM t1 WHERE c1='0000-00-00 00:00:00' OR c2='0000-00-00 00:00:00';
c1	c2	c3
SET @@SQL_MODE=@OLD_SQL_MODE;
SELECT * FROM t1 WHERE c2='2001-01-17 12:34:00';
c1	c2	c3
INSERT INTO t1 VALUES('2001-01-21 05:43:43','2001-01-22 06:53:53',10);
UPDATE t1 SET c1='1000-01-01 00:00:00',c2='9999-12-31 23:59:59' WHERE c1='2001-01-21 05:43:43';
SELECT * FROM t1 WHERE c1='1000-01-01 00:00:00' AND c2='9999-12-31 23:59:59';
c1	c2	c3
1000-01-01 00:00:00	9999-12-31 23:59:59	10
INSERT INTO t1 VALUES('2001-01-23 02:33','2001-01-24 03:33',11),('2001-01-25 04:33','2001-01-26 05:33',12);
UPDATE t1 SET c1='2001-01-23 02:33' WHERE c1='2001-01-25 04:33';
ERROR 23000: Duplicate entry '2001-01-23 02:33:00' for key 't1.PRIMARY'
UPDATE IGNORE t1 SET c1='2001-01-23 02:33' WHERE c1='2001-01-25 04:33';
Warnings:
Warning	1062	Duplicate entry '2001-01-23 02:33:00' for key 't1.PRIMARY'
SELECT count(*) FROM t1 WHERE c1='2001-01-23 02:33';
count(*)
1
UPDATE t1 SET c1=NULL WHERE c2='2001-01-24 03:33' /* updates to 0000-00-00 00:00:00 */;
ERROR 23000: Column 'c1' cannot be null
UPDATE IGNORE t1 SET c1=NULL WHERE c2='2001-01-24 03:33';
Warnings:
Warning	1048	Column 'c1' cannot be null
SET @OLD_SQL_MODE=@@SQL_MODE, @@SQL_MODE=ALLOW_INVALID_DATES;
SELECT * FROM t1 WHERE c1='0000-00-00 00:00:00';
c1	c2	c3
0000-00-00 00:00:00	2001-01-24 03:33:00	11
SET @@SQL_MODE=@OLD_SQL_MODE;
TRUNCATE TABLE t1;
CREATE TABLE t2(c1 DATETIME NOT NULL PRIMARY KEY, c2 DATETIME, c3 INT);
INSERT INTO t1 VALUES('2001-01-02 2:30:45','2001-01-01 1:30',1),('2001-01-03 3:30','2001-01-02 2:30:45',2);
INSERT INTO t2 VALUES('2001-01-02 2:30:45','2001-01-03 3:30',1),('2001-01-04 4:30','2001-01-05 5:30',2);
SELECT * FROM t1,t2 WHERE t2.c1=t1.c1;
c1	c2	c3	c1	c2	c3
2001-01-02 02:30:45	2001-01-01 01:30:00	1	2001-01-02 02:30:45	2001-01-03 03:30:00	1
UPDATE t1,t2 SET t2.c1='2005-01-06 2:30:50',t1.c1='2005-01-06 2:30:50' WHERE t2.c1=t1.c1;
SELECT * FROM t1,t2 WHERE t2.c1=t1.c1;
c1	c2	c3	c1	c2	c3
2005-01-06 02:30:50	2001-01-01 01:30:00	1	2005-01-06 02:30:50	2001-01-03 03:30:00	1
INSERT INTO t1 VALUES('2002-01-01 3:30','2002-01-02 2:30:45',3),('2002-01-04 4:30','2002-01-02 2:30:45',4);
INSERT INTO t2 VALUES('2002-01-02 2:30:45','2002-01-03 3:30',3),('2002-01-03 3:30','2002-01-04 4:30',4);
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2002-01-01 03:30:00	2002-01-02 02:30:45	3	2002-01-02 02:30:45	2002-01-03 03:30:00	3
2002-01-04 04:30:00	2002-01-02 02:30:45	4	2002-01-02 02:30:45	2002-01-03 03:30:00	3
UPDATE t1,t2 SET t2.c1='2002-01-06 2:30:50',t1.c2='2002-01-06 2:30:50' WHERE t2.c1=t1.c2;
SELECT * FROM t1,t2 WHERE t2.c1='2002-01-06 2:30:50' AND t1.c2='2002-01-06 2:30:50';
c1	c2	c3	c1	c2	c3
2002-01-01 03:30:00	2002-01-06 02:30:50	3	2002-01-06 02:30:50	2002-01-03 03:30:00	3
2002-01-04 04:30:00	2002-01-06 02:30:50	4	2002-01-06 02:30:50	2002-01-03 03:30:00	3
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2002-01-01 03:30:00	2002-01-06 02:30:50	3	2002-01-06 02:30:50	2002-01-03 03:30:00	3
2002-01-04 04:30:00	2002-01-06 02:30:50	4	2002-01-06 02:30:50	2002-01-03 03:30:00	3
DELETE FROM t1 WHERE c2='2002-01-06 2:30:50' AND c3=3;
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2002-01-04 04:30:00	2002-01-06 02:30:50	4	2002-01-06 02:30:50	2002-01-03 03:30:00	3
DROP TABLE t2;
TRUNCATE TABLE t1;
ALTER TABLE t1 CHANGE c1 c1 DATETIME NOT NULL, DROP PRIMARY KEY;
ALTER TABLE t1 CHANGE c1 c1 DATETIME NOT NULL, ADD KEY(c1);
UPDATE t1 SET c1='2002-01-07 7:30' WHERE c2='2002-01-05 5:30';
SELECT * FROM t1 WHERE c1='2002-01-07 7:30';
c1	c2	c3
INSERT INTO t1 VALUES('2002-01-08 1:30','2002-01-09 1:30',4),('2002-01-08 1:30:5','2002-01-09 1:30:5',5),('2002-01-08 1:30:10','2002-01-09 1:30:10',6),('2002-01-08 1:30:15','2002-01-09 1:30:15',7),('2002-01-08 1:30:20','2002-01-09 1:30:20',8),('2002-01-08 1:30:25','2002-01-09 1:30:25',9),('2002-01-08 1:30:30','2002-01-09 1:30:30',10),('2002-01-08 1:30:35','2002-01-09 1:30:35',11),('2002-01-08 1:30:40','2002-01-09 1:30:40',12),('2002-01-08 1:30:45','2002-01-09 1:30:45',13),('2002-01-08 1:30:50','2002-01-09 1:30:50',14),('2002-01-08 1:30:55','2002-01-09 1:30:55',15),('2002-01-08 1:30:59','2002-01-09 1:30:59',16),('2002-01-08 2:00','2002-01-09 2:00',17),('2002-01-08 2:00:05','2002-01-09 2:00:05',18),('2002-01-08 2:00:10','2002-01-09 2:00:10',19),('2002-01-08 2:00:15','2002-01-09 2:00:15',20);
SELECT * FROM t1;
c1	c2	c3
2002-01-08 01:30:00	2002-01-09 01:30:00	4
2002-01-08 01:30:05	2002-01-09 01:30:05	5
2002-01-08 01:30:10	2002-01-09 01:30:10	6
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2002-01-08 01:30:30	2002-01-09 01:30:30	10
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2002-01-08 01:30:40	2002-01-09 01:30:40	12
2002-01-08 01:30:45	2002-01-09 01:30:45	13
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2002-01-08 01:30:55	2002-01-09 01:30:55	15
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2002-01-08 02:00:00	2002-01-09 02:00:00	17
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2002-01-08 02:00:10	2002-01-09 02:00:10	19
2002-01-08 02:00:15	2002-01-09 02:00:15	20
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 <> '2002-01-09 01:30:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <> '2002-01-09 01:30:00' ORDER BY c2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2002-01-08 01:30:30	2002-01-09 01:30:30	10
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2002-01-08 01:30:40	2002-01-09 01:30:40	12
2002-01-08 01:30:45	2002-01-09 01:30:45	13
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2002-01-08 01:30:55	2002-01-09 01:30:55	15
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2002-01-08 02:00:00	2002-01-09 02:00:00	17
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2002-01-08 02:00:10	2002-01-09 02:00:10	19
2002-01-08 02:00:15	2002-01-09 02:00:15	20
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 >= '20020109013010' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '20020109013010' ORDER BY c2 DESC;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2002-01-08 02:00:00	2002-01-09 02:00:00	17
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2002-01-08 01:30:55	2002-01-09 01:30:55	15
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2002-01-08 01:30:45	2002-01-09 01:30:45	13
2002-01-08 01:30:40	2002-01-09 01:30:40	12
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2002-01-08 01:30:30	2002-01-09 01:30:30	10
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2003-01-01 01:02:03	2002-01-09 01:30:10	6
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 <='02-01-09 1:30:20' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <='02-01-09 1:30:20' ORDER BY c2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 <=> '020109013030' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '020109013030' ORDER BY c2 DESC;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:30	10
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 BETWEEN '2002-01-09 01:30:40' AND '2002-01-09 01:30:50' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '20020109 01:30:40' AND '20020109 01:30:50' ORDER BY c2;
c1	c2	c3
Warnings:
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 IN('2002-01-09 1:30:55','2002-01-09 2:00') ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IN('2002-01-09 1:30:55','2002-01-09 2:00') ORDER BY c2 DESC;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 01:30:55	15
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
c1	c2	c3
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2>= '2002-02-09 2:00:5' AND c1 < '2002-02-09 2:00:15' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2>= '2002-02-09 2:00:5' AND c1 < '2002-02-09 2:00:15' ORDER BY c2;
c1	c2	c3
INSERT INTO t1 VALUES('20020301000001','20020302000001',1),('020303010030','020304010030',2),('050103000001','050104000001',3),('5-10-10 1:2:3','5-10-10 1:2:3',4);
INSERT INTO t1 VALUES('0000-00-00 00:00:01',NULL,5);
ERROR 22007: Incorrect datetime value: '0000-00-00 00:00:01' for column 'c1' at row 1
SELECT * FROM t1;
c1	c2	c3
0005-10-10 01:02:03	0005-10-10 01:02:03	4
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2002-03-01 00:00:01	2002-03-02 00:00:01	1
2002-03-03 01:00:30	2002-03-04 01:00:30	2
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2003-01-01 01:02:03	2002-01-09 01:30:40	12
2003-01-01 01:02:03	2002-01-09 01:30:45	13
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT c1 FROM t1;
c1
0005-10-10 01:02:03
2002-01-08 01:30:15
2002-01-08 01:30:20
2002-01-08 01:30:25
2002-01-08 01:30:35
2002-01-08 01:30:50
2002-01-08 01:30:59
2002-01-08 02:00:05
2002-03-01 00:00:01
2002-03-03 01:00:30
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2005-01-03 00:00:01
SELECT c1 FROM t1 ORDER BY c1 DESC;
c1
2005-01-03 00:00:01
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2002-03-03 01:00:30
2002-03-01 00:00:01
2002-01-08 02:00:05
2002-01-08 01:30:59
2002-01-08 01:30:50
2002-01-08 01:30:35
2002-01-08 01:30:25
2002-01-08 01:30:20
2002-01-08 01:30:15
0005-10-10 01:02:03
SELECT * FROM t1 WHERE c1='2003-01-01 1:2:3';
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2003-01-01 01:02:03	2002-01-09 01:30:40	12
2003-01-01 01:02:03	2002-01-09 01:30:45	13
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:15	20
SELECT * FROM t1 WHERE c1 <> '2003-01-01 1:2:3' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
0005-10-10 01:02:03	0005-10-10 01:02:03	4
2002-01-08 01:30:15	2002-01-09 01:30:15	7
SELECT * FROM t1 WHERE c1 <> '2003-01-01 1:2:3' ORDER BY c1,c2 DESC;
c1	c2	c3
0005-10-10 01:02:03	0005-10-10 01:02:03	4
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2002-03-01 00:00:01	2002-03-02 00:00:01	1
2002-03-03 01:00:30	2002-03-04 01:00:30	2
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT * FROM t1 WHERE c1 > '20030101010203' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT * FROM t1 WHERE c1 > '20030101010203' ORDER BY c1,c2;
c1	c2	c3
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT * FROM t1 WHERE c1 < '03-01-01 1:2:3' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
0005-10-10 01:02:03	0005-10-10 01:02:03	4
2002-01-08 01:30:15	2002-01-09 01:30:15	7
SELECT * FROM t1 WHERE c1 < '03-01-01 1:2:3' ORDER BY c1,c2 DESC;
c1	c2	c3
0005-10-10 01:02:03	0005-10-10 01:02:03	4
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2002-03-01 00:00:01	2002-03-02 00:00:01	1
2002-03-03 01:00:30	2002-03-04 01:00:30	2
SELECT * FROM t1 WHERE c1 <=> '2003-01-01 1:2:3' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2003-01-01 01:02:03	2002-01-09 02:00:10	19
SELECT * FROM t1 WHERE c1 <=> '2003-01-01 1:2:3' ORDER BY c1,c2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2003-01-01 01:02:03	2002-01-09 01:30:40	12
2003-01-01 01:02:03	2002-01-09 01:30:45	13
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:15	20
SELECT * FROM t1 WHERE c1 BETWEEN '20020301' AND '2003-01-01 1:2:3' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
2002-03-01 00:00:01	2002-03-02 00:00:01	1
2002-03-03 01:00:30	2002-03-04 01:00:30	2
SELECT * FROM t1 WHERE c1 BETWEEN '20020301' AND '2003-01-01 1:2:3' ORDER BY c1,c2 DESC;
c1	c2	c3
2002-03-01 00:00:01	2002-03-02 00:00:01	1
2002-03-03 01:00:30	2002-03-04 01:00:30	2
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2003-01-01 01:02:03	2002-01-09 01:30:45	13
2003-01-01 01:02:03	2002-01-09 01:30:40	12
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:00	4
SELECT * FROM t1 WHERE c1 IN('2003-01-01 1:2:3','2005-01-03 00:00:01') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2003-01-01 01:02:03	2002-01-09 02:00:10	19
SELECT * FROM t1 WHERE c1 IN('2003-01-01 1:2:3','2005-01-03 00:00:01') ORDER BY c1,c2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2003-01-01 01:02:03	2002-01-09 01:30:40	12
2003-01-01 01:02:03	2002-01-09 01:30:45	13
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2,c1 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2,c1 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1 DESC LIMIT 2;
c1	c2	c3
0005-10-10 01:02:03	0005-10-10 01:02:03	4
2003-01-01 01:02:03	2002-01-09 01:30:00	4
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1;
c1	c2	c3
0005-10-10 01:02:03	0005-10-10 01:02:03	4
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2003-01-01 01:02:03	2002-01-09 01:30:40	12
2003-01-01 01:02:03	2002-01-09 01:30:45	13
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2002-03-01 00:00:01	2002-03-02 00:00:01	1
2002-03-03 01:00:30	2002-03-04 01:00:30	2
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT * FROM t1 WHERE c1>='2003-01-01 01:02:03' AND c1 <= '20050104000001' ORDER BY c2,c1 DESC LIMIT 2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
SELECT * FROM t1 WHERE c1>='2003-01-01 01:02:03' AND c1 <= '20050104000001' ORDER BY c2,c1;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2003-01-01 01:02:03	2002-01-09 01:30:40	12
2003-01-01 01:02:03	2002-01-09 01:30:45	13
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT * FROM t1 WHERE c1='2003-01-03 1:2:3' OR c2='2002-03-02 00:00:01';
c1	c2	c3
2002-03-01 00:00:01	2002-03-02 00:00:01	1
UPDATE t1 SET c1='20060101000001' WHERE c1='2003-01-03 1:2:3' OR c2='2002-03-02 00:00:01';
SELECT * FROM t1 WHERE c1='20060101000001' OR c2='020304010030';
c1	c2	c3
2002-03-03 01:00:30	2002-03-04 01:00:30	2
2006-01-01 00:00:01	2002-03-02 00:00:01	1
TRUNCATE TABLE t1;
ALTER TABLE t1 CHANGE c1 c1 DATETIME NOT NULL, ADD PRIMARY KEY(c1);
INSERT INTO t1 VALUES(NOW(),NOW(),3),(ADDTIME(NOW(),'1 01:01:01'),ADDTIME(NOW(),'1 01:01:01'),4),(ADDTIME(NOW(),'2 01:01:01'),ADDTIME(NOW(),'2 01:01:01'),5),(ADDTIME(NOW(),'3 01:01:01'),ADDTIME(NOW(),'3 01:01:01'),6);
INSERT INTO t1 VALUES('1000-01-01 00:00:00','1000-01-01 00:00:00',1),('9999-12-31 23:59:59','9999-12-31 23:59:59',2);
SELECT * FROM t1 WHERE c1 <='1000-01-01 00:00:00';
c1	c2	c3
1000-01-01 00:00:00	1000-01-01 00:00:00	1
DELETE FROM t1 WHERE c1 <='1000-01-01 00:00:00';
SELECT * FROM t1 WHERE c1 <='1000-01-01 00:00:00';
c1	c2	c3
SELECT * FROM t1 WHERE c1 >='9999-12-31 23:59:59';
c1	c2	c3
9999-12-31 23:59:59	9999-12-31 23:59:59	2
DELETE FROM t1 WHERE c1 >='9999-12-31 23:59:59';
SELECT * FROM t1 WHERE c1 >='9999-12-31 23:59:59';
c1	c2	c3
SELECT * FROM t1 WHERE c1 > ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
2009-02-28 13:21:14	2009-02-28 13:21:14	6
2009-02-27 13:21:14	2009-02-27 13:21:14	5
DELETE FROM t1 WHERE c1 > ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c1 <> CURRENT_TIMESTAMP() ORDER BY c1;
c1	c2	c3
2009-02-26 13:21:14	2009-02-26 13:21:14	4
DELETE FROM t1 WHERE c1 <> CURRENT_TIMESTAMP() ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <> CURRENT_TIMESTAMP() ORDER BY c1;
c1	c2	c3
INSERT INTO t1 VALUES('2001-01-01',NOW(),7),(ADDTIME(NOW(),'1 01:01:01'),ADDTIME(NOW(),'1 01:01:01'),8),(ADDTIME(NOW(),'2 01:01:01'),ADDTIME(NOW(),'2 01:01:01'),9),(ADDTIME(NOW(),'3 01:01:01'),ADDTIME(NOW(),'3 01:01:01'),10);
SELECT * FROM t1 WHERE c1 < ADDTIME(NOW(),'2 01:01:01') ORDER BY c1;
c1	c2	c3
2001-01-01 00:00:00	2009-02-25 12:20:13	7
2009-02-25 12:20:13	2009-02-25 12:20:13	3
2009-02-26 13:21:14	2009-02-26 13:21:14	8
DELETE FROM t1 WHERE c1 < ADDTIME(NOW(),'2 01:01:01') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < ADDTIME(NOW(),'2 01:01:01') ORDER BY c1;
c1	c2	c3
2009-02-26 13:21:14	2009-02-26 13:21:14	8
SELECT * FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
2009-02-26 13:21:14	2009-02-26 13:21:14	8
DELETE FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
DELETE FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
INSERT INTO t1 VALUES('2001-01-01',NOW(),11),(ADDTIME(NOW(),'1 01:01:01'),ADDTIME(NOW(),'1 01:01:01'),12),(ADDTIME(NOW(),'4 01:01:01'),NULL,13),(ADDTIME(NOW(),'5 01:01:01'),NULL,14);
SELECT * FROM t1 WHERE c1 BETWEEN NOW() AND ADDTIME(NOW(),'2 01:01:01') ORDER BY c1;
c1	c2	c3
2009-02-26 13:21:14	2009-02-26 13:21:14	12
2009-02-27 13:21:14	2009-02-27 13:21:14	9
DELETE FROM t1 WHERE c1 BETWEEN NOW() AND ADDTIME(NOW(),'2 01:01:01') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN NOW() AND ADDTIME(NOW(),'2 01:01:01') ORDER BY c1;
c1	c2	c3
SELECT * FROM t1 WHERE c1 IN(NOW(),ADDTIME(NOW(),'3 01:01:01')) ORDER BY c1 DESC;
c1	c2	c3
2009-02-28 13:21:14	2009-02-28 13:21:14	10
DELETE FROM t1 WHERE c1 IN(NOW(),ADDTIME(NOW(),'3 01:01:01')) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN(NOW(),ADDTIME(NOW(),'3 01:01:01')) ORDER BY c1 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2,c1;
c1	c2	c3
2009-03-01 13:21:14	NULL	13
2009-03-02 13:21:14	NULL	14
DELETE FROM t1 WHERE c2 IS NULL ORDER BY c2,c1 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2,c1;
c1	c2	c3
INSERT INTO t1 VALUES(ADDTIME(NOW(),'6 01:01:01'),ADDTIME(NOW(),'6 01:01:01'),15),(ADDTIME(NOW(),'7 01:01:01'),NULL,16),(ADDTIME(NOW(),'8 01:01:01'),ADDTIME(NOW(),'8 01:01:01'),17),(ADDTIME(NOW(),'9 01:01:01'),ADDTIME(NOW(),'9 01:01:01'),18),(ADDTIME(NOW(),'10 01:01:01'),ADDTIME(NOW(),'10 01:01:01'),19);
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1 DESC;
c1	c2	c3
2001-01-01 00:00:00	2009-02-25 12:20:13	11
2009-03-03 13:21:14	2009-03-03 13:21:14	15
2009-03-05 13:21:14	2009-03-05 13:21:14	17
2009-03-06 13:21:14	2009-03-06 13:21:14	18
2009-03-07 13:21:14	2009-03-07 13:21:14	19
DELETE FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1 DESC;
c1	c2	c3
2009-03-05 13:21:14	2009-03-05 13:21:14	17
2009-03-06 13:21:14	2009-03-06 13:21:14	18
2009-03-07 13:21:14	2009-03-07 13:21:14	19
SELECT * FROM t1 WHERE c1>=ADDTIME(NOW(),'5 01:01:01') AND c2 < ADDTIME(NOW(),'8 13:25') ORDER BY c1;
c1	c2	c3
2009-03-05 13:21:14	2009-03-05 13:21:14	17
DELETE FROM t1 WHERE c1>=ADDTIME(NOW(),'5 01:01:01') AND c2 < ADDTIME(NOW(),'8 13:25') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1>=ADDTIME(NOW(),'5 01:01:01') AND c2 < ADDTIME(NOW(),'8 13:25') ORDER BY c1;
c1	c2	c3
SELECT * FROM t1 WHERE c1=ADDTIME(NOW(),'9 01:01:01') OR c2=ADDTIME(NOW(),'10 01:01:01');
c1	c2	c3
2009-03-06 13:21:14	2009-03-06 13:21:14	18
2009-03-07 13:21:14	2009-03-07 13:21:14	19
DELETE FROM t1 WHERE c1=ADDTIME(NOW(),'9 01:01:01') OR c2=ADDTIME(NOW(),'10 01:01:01');
SELECT * FROM t1 WHERE c1=ADDTIME(NOW(),'9 01:01:01') OR c2=ADDTIME(NOW(),'10 01:01:01');
c1	c2	c3
SELECT count(*) FROM t1;
count(*)
1
TRUNCATE TABLE t1;
SELECT count(*) FROM t1;
count(*)
0
CREATE TABLE t2(c1 DATETIME NOT NULL PRIMARY KEY, c2 DATETIME, c3 INT);
INSERT INTO t1 VALUES('2001-01-01 1:30','2001-01-02 2:30',1),('2001-01-03 3:30','2001-01-02 2:30',2);
INSERT INTO t2 VALUES('2001-01-02 2:30','2001-01-03 3:30',1),('2001-01-04 4:30','2001-01-05 5:30',2);
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2001-01-01 01:30:00	2001-01-02 02:30:00	1	2001-01-02 02:30:00	2001-01-03 03:30:00	1
2001-01-03 03:30:00	2001-01-02 02:30:00	2	2001-01-02 02:30:00	2001-01-03 03:30:00	1
DELETE t1,t2 FROM t1,t2 WHERE t2.c1=t1.c2;
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
TRUNCATE TABLE t1;
TRUNCATE TABLE t2;
INSERT INTO t1 VALUES('2001-01-01 1:30','2001-01-02 2:30',1),('2001-01-03 3:30','2001-01-02 2:30',2);
INSERT INTO t2 VALUES('2001-01-02 2:30','2001-01-03 3:30',1),('2001-01-04 4:30','2001-01-05 5:30',2);
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2001-01-01 01:30:00	2001-01-02 02:30:00	1	2001-01-02 02:30:00	2001-01-03 03:30:00	1
2001-01-03 03:30:00	2001-01-02 02:30:00	2	2001-01-02 02:30:00	2001-01-03 03:30:00	1
DELETE FROM a1, a2 USING t1 AS a1 INNER JOIN t2 AS a2 WHERE a2.c1=a1.c2;
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
DROP TABLE t1,t2;
CREATE TABLE mt1(id int(11) NOT NULL default '0', name varchar(10) default NULL, PRIMARY KEY  (id));
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO mt1 VALUES (1,'aaa'),(2,'aaa'),(3,'aaa');
CREATE TABLE mt2 (id int(11) NOT NULL default '0', name varchar(10) default NULL, PRIMARY KEY  (id));
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO mt2 VALUES (2,'bbb'),(3,'bbb'),(4,'bbb');
CREATE TABLE mt3 (id int(11) NOT NULL default '0', mydate datetime default NULL, PRIMARY KEY  (id));
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO mt3 VALUES (1,'2002-02-04 00:00:00'),(3,'2002-05-12 00:00:00'),(5,'2002-05-12 00:00:00'),(6,'2002-06-22 00:00:00'),(7,'2002-07-22 00:00:00');
delete mt1,mt2,mt3 from mt1,mt2,mt3 where to_days(now())-to_days(mt3.mydate)>=30 and mt3.id=mt1.id and mt3.id=mt2.id;
select * from mt3;
id	mydate
1	2002-02-04 00:00:00
5	2002-05-12 00:00:00
6	2002-06-22 00:00:00
7	2002-07-22 00:00:00
DROP TABLE mt1,mt2,mt3;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL PRIMARY KEY, c2 TIMESTAMP NULL, c3 INT, INDEX idx2(c2));
INSERT INTO t1 VALUE('1998-01-01 09:23:45','10-01-01 14:12:33',1);
UPDATE t1 SET c1='2001-01-01' WHERE c2='10-01-01 14:12:33';
SELECT c1,hour(c1),minute(c1),second(c1) FROM t1 WHERE c2='10-01-01 14:12:33';
c1	hour(c1)	minute(c1)	second(c1)
2001-01-01 00:00:00	0	0	0
INSERT INTO t1 VALUES('2009-01-01 08:42:42','2009-01-02 23:59:59',2);
UPDATE t1 SET c1='2009-01-01 1:2:3',c2='2009-01-02 23:59:59.999999' WHERE c2='2009-01-02 23:59:59';
SELECT c1,ADDTIME(c2,'1 1:1:1.000002') FROM t1 WHERE c3=2;
c1	ADDTIME(c2,'1 1:1:1.000002')
2009-01-01 01:02:03	2009-01-04 01:01:01.000002
INSERT INTO t1 VALUES('2009-02-25 15:45','09-02-27 15:46',3);
SET TIMESTAMP=1235553613;
UPDATE t1 SET c2=NOW() WHERE c1 >=CURDATE() /* updates c1 with current timestamp */;
UPDATE t1 SET c1=DATE_ADD(CURDATE(),INTERVAL '1 1:1:1' DAY_SECOND) WHERE c2=CURRENT_TIMESTAMP();
SELECT * FROM t1;
c1	c2	c3
2001-01-01 00:00:00	2010-01-01 14:12:33	1
2009-01-01 01:02:03	2009-01-03 00:00:00	2
2009-02-26 01:01:01	2009-02-25 12:20:13	3
INSERT INTO t1 VALUES('2001-01-06','2001-01-07 09:30:00',4),('2001-01-08',NULL,5);
UPDATE t1 SET c1='20010106010030',c2=NULL WHERE c1='2001-01-06 00:00:00';
SELECT c1,c2 FROM t1 WHERE c2 IS NULL;
c1	c2
2001-01-06 01:00:30	NULL
2001-01-08 00:00:00	NULL
SELECT * FROM t1;
c1	c2	c3
2001-01-01 00:00:00	2010-01-01 14:12:33	1
2001-01-06 01:00:30	NULL	4
2001-01-08 00:00:00	NULL	5
2009-01-01 01:02:03	2009-01-03 00:00:00	2
2009-02-26 01:01:01	2009-02-25 12:20:13	3
UPDATE t1 SET c1=c1,c2='2001-01-10 00:00:00' WHERE c2 IS NULL;
SELECT * FROM t1 WHERE c2='2001-01-10';
c1	c2	c3
2001-01-06 01:00:30	2001-01-10 00:00:00	4
2001-01-08 00:00:00	2001-01-10 00:00:00	5
INSERT INTO t1 VALUES('2001-01-09','2001-01-10',6),('2001-01-11','2001-01-12',7);
SELECT * FROM t1 ORDER BY c3;
c1	c2	c3
2001-01-01 00:00:00	2010-01-01 14:12:33	1
2009-01-01 01:02:03	2009-01-03 00:00:00	2
2009-02-26 01:01:01	2009-02-25 12:20:13	3
2001-01-06 01:00:30	2001-01-10 00:00:00	4
2001-01-08 00:00:00	2001-01-10 00:00:00	5
2001-01-09 00:00:00	2001-01-10 00:00:00	6
2001-01-11 00:00:00	2001-01-12 00:00:00	7
UPDATE t1 SET c1='2001-01-09 24:59:59',c2='2009-01-10 23:60:59' WHERE c1='2001-01-09';
ERROR 22007: Incorrect datetime value: '2001-01-09 24:59:59' for column 'c1' at row 1
UPDATE t1 SET c1='2001-01-11 23:59:59',c2='2001-01-11 23:59:60' WHERE c1='2001-01-11';
ERROR 22007: Incorrect datetime value: '2001-01-11 23:59:60' for column 'c2' at row 1
SELECT * FROM t1 ORDER BY c3;
c1	c2	c3
2001-01-01 00:00:00	2010-01-01 14:12:33	1
2009-01-01 01:02:03	2009-01-03 00:00:00	2
2009-02-26 01:01:01	2009-02-25 12:20:13	3
2001-01-06 01:00:30	2001-01-10 00:00:00	4
2001-01-08 00:00:00	2001-01-10 00:00:00	5
2001-01-09 00:00:00	2001-01-10 00:00:00	6
2001-01-11 00:00:00	2001-01-12 00:00:00	7
INSERT INTO t1 VALUES('2001-01-17','0000-00-00 00:00:00',8);
ERROR 22007: Incorrect datetime value: '0000-00-00 00:00:00' for column 'c2' at row 1
INSERT INTO t1 VALUES('2001-01-18','2001-01-18 07:35',9);
SET SQL_MODE=TRADITIONAL;
SELECT * FROM t1;
c1	c2	c3
2001-01-01 00:00:00	2010-01-01 14:12:33	1
2001-01-06 01:00:30	2001-01-10 00:00:00	4
2001-01-08 00:00:00	2001-01-10 00:00:00	5
2001-01-09 00:00:00	2001-01-10 00:00:00	6
2001-01-11 00:00:00	2001-01-12 00:00:00	7
2001-01-18 00:00:00	2001-01-18 07:35:00	9
2009-01-01 01:02:03	2009-01-03 00:00:00	2
2009-02-26 01:01:01	2009-02-25 12:20:13	3
UPDATE t1 SET c1='1972-01-01 07:35',c2='0000-00-00 00:00:00' WHERE c2='2001-01-18 07:35';
ERROR 22007: Incorrect datetime value: '0000-00-00 00:00:00' for column 'c2' at row 1
SET SQL_MODE=DEFAULT;
UPDATE t1 SET c1='1972-01-01 07:35',c2='0000-00-00 00:00:00' WHERE c2='2001-01-18 07:35';
ERROR 22007: Incorrect datetime value: '0000-00-00 00:00:00' for column 'c2' at row 1
UPDATE t1 SET c1='2001-01-17 12:34:00',c2='2001-01-17 12:34:00' WHERE c2='0000-00-00 00:00:00' AND c1='2001-01-17';
ERROR 22007: Incorrect datetime value: '0000-00-00 00:00:00' for column 'c2' at row 1
SELECT * FROM t1;
c1	c2	c3
2001-01-01 00:00:00	2010-01-01 14:12:33	1
2001-01-06 01:00:30	2001-01-10 00:00:00	4
2001-01-08 00:00:00	2001-01-10 00:00:00	5
2001-01-09 00:00:00	2001-01-10 00:00:00	6
2001-01-11 00:00:00	2001-01-12 00:00:00	7
2001-01-18 00:00:00	2001-01-18 07:35:00	9
2009-01-01 01:02:03	2009-01-03 00:00:00	2
2009-02-26 01:01:01	2009-02-25 12:20:13	3
SELECT * FROM t1 WHERE c1='2001-01-17 12:34:00' AND c2='2001-01-17 12:34:00';
c1	c2	c3
INSERT INTO t1 VALUES('2001-01-21 05:43:43','2001-01-22 06:53:53',10);
UPDATE t1 SET c1='1971-01-01 00:00:01',c2='2038-01-09 03:14:07' WHERE c1='2001-01-21 05:43:43';
SELECT * FROM t1 WHERE c1='1971-01-01 00:00:01' AND c2='2038-01-09 03:14:07';
c1	c2	c3
1971-01-01 00:00:01	2038-01-09 03:14:07	10
INSERT INTO t1 VALUES('2001-01-23 02:33','2001-01-24 03:33',11),('2001-01-25 04:33','2001-01-26 05:33',12);
UPDATE t1 SET c1='2001-01-23 02:33' WHERE c1='2001-01-25 04:33';
ERROR 23000: Duplicate entry '2001-01-23 02:33:00' for key 't1.PRIMARY'
UPDATE IGNORE t1 SET c1='2001-01-23 02:33' WHERE c1='2001-01-25 04:33';
Warnings:
Warning	1062	Duplicate entry '2001-01-23 02:33:00' for key 't1.PRIMARY'
SELECT count(*) FROM t1 WHERE c1='2001-01-23 02:33';
count(*)
1
UPDATE t1 SET c1=CURRENT_TIMESTAMP WHERE c2='2001-01-24 03:33' /* updates to current timestamp */;
UPDATE IGNORE t1 SET c1=NULL WHERE c2='2001-01-26 05:33';
Warnings:
Warning	1048	Column 'c1' cannot be null
SELECT * FROM t1 WHERE c1=CURRENT_TIMESTAMP();
c1	c2	c3
2009-02-25 12:20:13	2001-01-24 03:33:00	11
TRUNCATE TABLE t1;
CREATE TABLE t2(c1 TIMESTAMP NOT NULL PRIMARY KEY, c2 TIMESTAMP NULL, c3 INT);
INSERT INTO t1 VALUES('2001-01-02 2:30:45','2001-01-01 1:30',1),('2001-01-03 3:30','2001-01-02 2:30:45',2);
INSERT INTO t2 VALUES('2001-01-02 2:30:45','2001-01-03 3:30',1),('2001-01-04 4:30','2001-01-05 5:30',2);
SELECT * FROM t1,t2 WHERE t2.c1=t1.c1;
c1	c2	c3	c1	c2	c3
2001-01-02 02:30:45	2001-01-01 01:30:00	1	2001-01-02 02:30:45	2001-01-03 03:30:00	1
UPDATE t1,t2 SET t2.c1='2005-01-06 2:30:50',t1.c1='2005-01-06 2:30:50' WHERE t2.c1=t1.c1;
SELECT * FROM t1,t2 WHERE t2.c1=t1.c1;
c1	c2	c3	c1	c2	c3
2005-01-06 02:30:50	2001-01-01 01:30:00	1	2005-01-06 02:30:50	2001-01-03 03:30:00	1
INSERT INTO t1 VALUES('2002-01-01 3:30','2002-01-02 2:30:45',3),('2002-01-04 4:30','2002-01-02 2:30:45',4);
INSERT INTO t2 VALUES('2002-01-02 2:30:45','2002-01-03 3:30',3),('2002-01-03 3:30','2002-01-04 4:30',4);
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2002-01-01 03:30:00	2002-01-02 02:30:45	3	2002-01-02 02:30:45	2002-01-03 03:30:00	3
2002-01-04 04:30:00	2002-01-02 02:30:45	4	2002-01-02 02:30:45	2002-01-03 03:30:00	3
UPDATE t1,t2 SET t2.c1='2002-01-06 2:30:50',t1.c1=t1.c1,t1.c2='2002-01-06 2:30:50' WHERE t2.c1=t1.c2;
SELECT * FROM t1,t2 WHERE t2.c1='2002-01-06 2:30:50' AND t1.c2='2002-01-06 2:30:50';
c1	c2	c3	c1	c2	c3
2002-01-01 03:30:00	2002-01-06 02:30:50	3	2002-01-06 02:30:50	2002-01-03 03:30:00	3
2002-01-04 04:30:00	2002-01-06 02:30:50	4	2002-01-06 02:30:50	2002-01-03 03:30:00	3
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2002-01-01 03:30:00	2002-01-06 02:30:50	3	2002-01-06 02:30:50	2002-01-03 03:30:00	3
2002-01-04 04:30:00	2002-01-06 02:30:50	4	2002-01-06 02:30:50	2002-01-03 03:30:00	3
DELETE FROM t1 WHERE c2='2002-01-06 2:30:50' AND c3=3;
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2002-01-04 04:30:00	2002-01-06 02:30:50	4	2002-01-06 02:30:50	2002-01-03 03:30:00	3
DROP TABLE t2;
TRUNCATE TABLE t1;
ALTER TABLE t1 CHANGE c1 c1 TIMESTAMP NOT NULL, DROP PRIMARY KEY;
ALTER TABLE t1 CHANGE c1 c1 TIMESTAMP NOT NULL, ADD KEY(c1);
UPDATE t1 SET c1='2002-01-07 7:30' WHERE c2='2002-01-05 5:30';
SELECT * FROM t1 WHERE c1='2002-01-07 7:30';
c1	c2	c3
INSERT INTO t1 VALUES('2002-01-08 1:30','2002-01-09 1:30',4),('2002-01-08 1:30:5','2002-01-09 1:30:5',5),('2002-01-08 1:30:10','2002-01-09 1:30:10',6),('2002-01-08 1:30:15','2002-01-09 1:30:15',7),('2002-01-08 1:30:20','2002-01-09 1:30:20',8),('2002-01-08 1:30:25','2002-01-09 1:30:25',9),('2002-01-08 1:30:30','2002-01-09 1:30:30',10),('2002-01-08 1:30:35','2002-01-09 1:30:35',11),('2002-01-08 1:30:40','2002-01-09 1:30:40',12),('2002-01-08 1:30:45','2002-01-09 1:30:45',13),('2002-01-08 1:30:50','2002-01-09 1:30:50',14),('2002-01-08 1:30:55','2002-01-09 1:30:55',15),('2002-01-08 1:30:59','2002-01-09 1:30:59',16),('2002-01-08 2:00','2002-01-09 2:00',17),('2002-01-08 2:00:05','2002-01-09 2:00:05',18),('2002-01-08 2:00:10','2002-01-09 2:00:10',19),('2002-01-08 2:00:15','2002-01-09 2:00:15',20);
SELECT * FROM t1;
c1	c2	c3
2002-01-08 01:30:00	2002-01-09 01:30:00	4
2002-01-08 01:30:05	2002-01-09 01:30:05	5
2002-01-08 01:30:10	2002-01-09 01:30:10	6
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2002-01-08 01:30:30	2002-01-09 01:30:30	10
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2002-01-08 01:30:40	2002-01-09 01:30:40	12
2002-01-08 01:30:45	2002-01-09 01:30:45	13
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2002-01-08 01:30:55	2002-01-09 01:30:55	15
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2002-01-08 02:00:00	2002-01-09 02:00:00	17
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2002-01-08 02:00:10	2002-01-09 02:00:10	19
2002-01-08 02:00:15	2002-01-09 02:00:15	20
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 <> '2002-01-09 01:30:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <> '2002-01-09 01:30:00' ORDER BY c2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2002-01-08 01:30:30	2002-01-09 01:30:30	10
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2002-01-08 01:30:40	2002-01-09 01:30:40	12
2002-01-08 01:30:45	2002-01-09 01:30:45	13
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2002-01-08 01:30:55	2002-01-09 01:30:55	15
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2002-01-08 02:00:00	2002-01-09 02:00:00	17
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2002-01-08 02:00:10	2002-01-09 02:00:10	19
2002-01-08 02:00:15	2002-01-09 02:00:15	20
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 >= '20020109013010' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '20020109013010' ORDER BY c2 DESC;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2002-01-08 02:00:00	2002-01-09 02:00:00	17
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2002-01-08 01:30:55	2002-01-09 01:30:55	15
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2002-01-08 01:30:45	2002-01-09 01:30:45	13
2002-01-08 01:30:40	2002-01-09 01:30:40	12
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2002-01-08 01:30:30	2002-01-09 01:30:30	10
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2003-01-01 01:02:03	2002-01-09 01:30:10	6
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 <='02-01-09 1:30:20' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <='02-01-09 1:30:20' ORDER BY c2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 <=> '020109013030' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '020109013030' ORDER BY c2 DESC;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:30	10
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 BETWEEN '20020109 01:30:40' AND '20020109 01:30:50' ORDER BY c2 LIMIT 2;
ERROR 22007: Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
SELECT * FROM t1 WHERE c2 BETWEEN '20020109 01:30:40' AND '20020109 01:30:50' ORDER BY c2;
c1	c2	c3
Warnings:
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:40' for column 'c2' at row 1
Warning	1292	Incorrect datetime value: '20020109 01:30:50' for column 'c2' at row 1
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 IN('2002-01-09 1:30:55','2002-01-09 2:00') ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IN('2002-01-09 1:30:55','2002-01-09 2:00') ORDER BY c2 DESC;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 01:30:55	15
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
c1	c2	c3
UPDATE t1 SET c1='2003-01-01 1:2:3' WHERE c2>= '2002-02-09 2:00:5' AND c1 < '2002-02-09 2:00:15' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2>= '2002-02-09 2:00:5' AND c1 < '2002-02-09 2:00:15' ORDER BY c2;
c1	c2	c3
INSERT INTO t1 VALUES('20020301000001','20020302000001',1),('020303010030','020304010030',2),('050103000001','050104000001',3);
INSERT INTO t1 VALUES('5-10-10 1:2:3','5-10-10 1:2:3',4);
ERROR 22007: Incorrect datetime value: '5-10-10 1:2:3' for column 'c1' at row 1
INSERT INTO t1 VALUES('0000-00-00 00:00:01',NULL,5);
ERROR 22007: Incorrect datetime value: '0000-00-00 00:00:01' for column 'c1' at row 1
SELECT * FROM t1;
c1	c2	c3
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2002-01-08 01:30:40	2002-01-09 01:30:40	12
2002-01-08 01:30:45	2002-01-09 01:30:45	13
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2002-03-01 00:00:01	2002-03-02 00:00:01	1
2002-03-03 01:00:30	2002-03-04 01:00:30	2
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT c1 FROM t1;
c1
2002-01-08 01:30:15
2002-01-08 01:30:20
2002-01-08 01:30:25
2002-01-08 01:30:35
2002-01-08 01:30:40
2002-01-08 01:30:45
2002-01-08 01:30:50
2002-01-08 01:30:59
2002-01-08 02:00:05
2002-03-01 00:00:01
2002-03-03 01:00:30
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2005-01-03 00:00:01
SELECT c1 FROM t1 ORDER BY c1 DESC;
c1
2005-01-03 00:00:01
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2003-01-01 01:02:03
2002-03-03 01:00:30
2002-03-01 00:00:01
2002-01-08 02:00:05
2002-01-08 01:30:59
2002-01-08 01:30:50
2002-01-08 01:30:45
2002-01-08 01:30:40
2002-01-08 01:30:35
2002-01-08 01:30:25
2002-01-08 01:30:20
2002-01-08 01:30:15
SELECT * FROM t1 WHERE c1='2003-01-01 1:2:3';
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:15	20
SELECT * FROM t1 WHERE c1 <> '2003-01-01 1:2:3' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
SELECT * FROM t1 WHERE c1 <> '2003-01-01 1:2:3' ORDER BY c1,c2 DESC;
c1	c2	c3
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2002-01-08 01:30:40	2002-01-09 01:30:40	12
2002-01-08 01:30:45	2002-01-09 01:30:45	13
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2002-03-01 00:00:01	2002-03-02 00:00:01	1
2002-03-03 01:00:30	2002-03-04 01:00:30	2
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT * FROM t1 WHERE c1 > '20030101010203' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT * FROM t1 WHERE c1 > '20030101010203' ORDER BY c1,c2;
c1	c2	c3
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT * FROM t1 WHERE c1 < '03-01-01 1:2:3' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
SELECT * FROM t1 WHERE c1 < '03-01-01 1:2:3' ORDER BY c1,c2 DESC;
c1	c2	c3
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2002-01-08 01:30:40	2002-01-09 01:30:40	12
2002-01-08 01:30:45	2002-01-09 01:30:45	13
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2002-03-01 00:00:01	2002-03-02 00:00:01	1
2002-03-03 01:00:30	2002-03-04 01:00:30	2
SELECT * FROM t1 WHERE c1 <=> '2003-01-01 1:2:3' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2003-01-01 01:02:03	2002-01-09 02:00:10	19
SELECT * FROM t1 WHERE c1 <=> '2003-01-01 1:2:3' ORDER BY c1,c2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:15	20
SELECT * FROM t1 WHERE c1 BETWEEN '20020301' AND '2003-01-01 1:2:3' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
2002-03-01 00:00:01	2002-03-02 00:00:01	1
2002-03-03 01:00:30	2002-03-04 01:00:30	2
SELECT * FROM t1 WHERE c1 BETWEEN '20020301' AND '2003-01-01 1:2:3' ORDER BY c1,c2 DESC;
c1	c2	c3
2002-03-01 00:00:01	2002-03-02 00:00:01	1
2002-03-03 01:00:30	2002-03-04 01:00:30	2
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:00	4
SELECT * FROM t1 WHERE c1 IN('2003-01-01 1:2:3','2005-01-03 00:00:01') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2003-01-01 01:02:03	2002-01-09 02:00:10	19
SELECT * FROM t1 WHERE c1 IN('2003-01-01 1:2:3','2005-01-03 00:00:01') ORDER BY c1,c2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2,c1 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2,c1 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1 DESC LIMIT 2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2002-01-08 01:30:15	2002-01-09 01:30:15	7
2002-01-08 01:30:20	2002-01-09 01:30:20	8
2002-01-08 01:30:25	2002-01-09 01:30:25	9
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2002-01-08 01:30:35	2002-01-09 01:30:35	11
2002-01-08 01:30:40	2002-01-09 01:30:40	12
2002-01-08 01:30:45	2002-01-09 01:30:45	13
2002-01-08 01:30:50	2002-01-09 01:30:50	14
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2002-01-08 01:30:59	2002-01-09 01:30:59	16
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2002-01-08 02:00:05	2002-01-09 02:00:05	18
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2002-03-01 00:00:01	2002-03-02 00:00:01	1
2002-03-03 01:00:30	2002-03-04 01:00:30	2
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT * FROM t1 WHERE c1>='2003-01-01 01:02:03' AND c1 <= '20050104000001' ORDER BY c2,c1 DESC LIMIT 2;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
SELECT * FROM t1 WHERE c1>='2003-01-01 01:02:03' AND c1 <= '20050104000001' ORDER BY c2,c1;
c1	c2	c3
2003-01-01 01:02:03	2002-01-09 01:30:00	4
2003-01-01 01:02:03	2002-01-09 01:30:05	5
2003-01-01 01:02:03	2002-01-09 01:30:10	6
2003-01-01 01:02:03	2002-01-09 01:30:30	10
2003-01-01 01:02:03	2002-01-09 01:30:55	15
2003-01-01 01:02:03	2002-01-09 02:00:00	17
2003-01-01 01:02:03	2002-01-09 02:00:10	19
2003-01-01 01:02:03	2002-01-09 02:00:15	20
2005-01-03 00:00:01	2005-01-04 00:00:01	3
SELECT * FROM t1 WHERE c1='2003-01-03 1:2:3' OR c2='2002-03-02 00:00:01';
c1	c2	c3
2002-03-01 00:00:01	2002-03-02 00:00:01	1
UPDATE t1 SET c1='20060101000001' WHERE c1='2003-01-03 1:2:3' OR c2='2002-03-02 00:00:01';
SELECT * FROM t1 WHERE c1='20060101000001' OR c2='020304010030';
c1	c2	c3
2002-03-03 01:00:30	2002-03-04 01:00:30	2
2006-01-01 00:00:01	2002-03-02 00:00:01	1
TRUNCATE TABLE t1;
ALTER TABLE t1 CHANGE c1 c1 TIMESTAMP NOT NULL, ADD PRIMARY KEY(c1);
INSERT INTO t1 VALUES(NOW(),NOW(),3),(ADDTIME(NOW(),'1 01:01:01'),ADDTIME(NOW(),'1 01:01:01'),4),(ADDTIME(NOW(),'2 01:01:01'),ADDTIME(NOW(),'2 01:01:01'),5),(ADDTIME(NOW(),'3 01:01:01'),ADDTIME(NOW(),'3 01:01:01'),6);
INSERT INTO t1 VALUES('1971-01-01 00:00:01','1971-01-01 00:00:01',1),('2038-01-09 03:14:07','2038-01-09 03:14:07',2);
SELECT * FROM t1 WHERE c1 <='1971-01-01 00:00:01';
c1	c2	c3
1971-01-01 00:00:01	1971-01-01 00:00:01	1
DELETE FROM t1 WHERE c1 <='1971-01-01 00:00:01';
SELECT * FROM t1 WHERE c1 <='1971-01-01 00:00:01';
c1	c2	c3
SELECT * FROM t1 WHERE c1 >='2038-01-09 03:14:07';
c1	c2	c3
2038-01-09 03:14:07	2038-01-09 03:14:07	2
DELETE FROM t1 WHERE c1 >='2038-01-09 03:14:07';
SELECT * FROM t1 WHERE c1 >='2038-01-09 03:14:07';
c1	c2	c3
SELECT * FROM t1 WHERE c1 > ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
2009-02-28 13:21:14	2009-02-28 13:21:14	6
2009-02-27 13:21:14	2009-02-27 13:21:14	5
DELETE FROM t1 WHERE c1 > ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c1 <> CURRENT_TIMESTAMP() ORDER BY c1;
c1	c2	c3
2009-02-26 13:21:14	2009-02-26 13:21:14	4
DELETE FROM t1 WHERE c1 <> CURRENT_TIMESTAMP() ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <> CURRENT_TIMESTAMP() ORDER BY c1;
c1	c2	c3
INSERT INTO t1 VALUES('2001-01-01',NOW(),7),(ADDTIME(NOW(),'1 01:01:01'),ADDTIME(NOW(),'1 01:01:01'),8),(ADDTIME(NOW(),'2 01:01:01'),ADDTIME(NOW(),'2 01:01:01'),9),(ADDTIME(NOW(),'3 01:01:01'),ADDTIME(NOW(),'3 01:01:01'),10);
SELECT * FROM t1 WHERE c1 < ADDTIME(NOW(),'2 01:01:01') ORDER BY c1;
c1	c2	c3
2001-01-01 00:00:00	2009-02-25 12:20:13	7
2009-02-25 12:20:13	2009-02-25 12:20:13	3
2009-02-26 13:21:14	2009-02-26 13:21:14	8
DELETE FROM t1 WHERE c1 < ADDTIME(NOW(),'2 01:01:01') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < ADDTIME(NOW(),'2 01:01:01') ORDER BY c1;
c1	c2	c3
2009-02-26 13:21:14	2009-02-26 13:21:14	8
SELECT * FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
2009-02-26 13:21:14	2009-02-26 13:21:14	8
DELETE FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
DELETE FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> ADDTIME(NOW(),'1 01:01:01') ORDER BY c1 DESC;
c1	c2	c3
INSERT INTO t1 VALUES('2001-01-01',NOW(),11),(ADDTIME(NOW(),'1 01:01:01'),ADDTIME(NOW(),'1 01:01:01'),12),(ADDTIME(NOW(),'4 01:01:01'),NULL,13),(ADDTIME(NOW(),'5 01:01:01'),NULL,14);
SELECT * FROM t1 WHERE c1 BETWEEN NOW() AND ADDTIME(NOW(),'2 01:01:01') ORDER BY c1;
c1	c2	c3
2009-02-26 13:21:14	2009-02-26 13:21:14	12
2009-02-27 13:21:14	2009-02-27 13:21:14	9
DELETE FROM t1 WHERE c1 BETWEEN NOW() AND ADDTIME(NOW(),'2 01:01:01') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN NOW() AND ADDTIME(NOW(),'2 01:01:01') ORDER BY c1;
c1	c2	c3
SELECT * FROM t1 WHERE c1 IN(NOW(),ADDTIME(NOW(),'3 01:01:01')) ORDER BY c1 DESC;
c1	c2	c3
2009-02-28 13:21:14	2009-02-28 13:21:14	10
DELETE FROM t1 WHERE c1 IN(NOW(),ADDTIME(NOW(),'3 01:01:01')) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN(NOW(),ADDTIME(NOW(),'3 01:01:01')) ORDER BY c1 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2,c1;
c1	c2	c3
2009-03-01 13:21:14	NULL	13
2009-03-02 13:21:14	NULL	14
DELETE FROM t1 WHERE c2 IS NULL ORDER BY c2,c1 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2,c1;
c1	c2	c3
INSERT INTO t1 VALUES(ADDTIME(NOW(),'6 01:01:01'),ADDTIME(NOW(),'6 01:01:01'),15),(ADDTIME(NOW(),'7 01:01:01'),NULL,16),(ADDTIME(NOW(),'8 01:01:01'),ADDTIME(NOW(),'8 01:01:01'),17),(ADDTIME(NOW(),'9 01:01:01'),ADDTIME(NOW(),'9 01:01:01'),18),(ADDTIME(NOW(),'10 01:01:01'),ADDTIME(NOW(),'10 01:01:01'),19);
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1 DESC;
c1	c2	c3
2001-01-01 00:00:00	2009-02-25 12:20:13	11
2009-03-03 13:21:14	2009-03-03 13:21:14	15
2009-03-05 13:21:14	2009-03-05 13:21:14	17
2009-03-06 13:21:14	2009-03-06 13:21:14	18
2009-03-07 13:21:14	2009-03-07 13:21:14	19
DELETE FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2,c1 DESC;
c1	c2	c3
2009-03-05 13:21:14	2009-03-05 13:21:14	17
2009-03-06 13:21:14	2009-03-06 13:21:14	18
2009-03-07 13:21:14	2009-03-07 13:21:14	19
SELECT * FROM t1 WHERE c1>=ADDTIME(NOW(),'5 01:01:01') AND c2 < ADDTIME(NOW(),'8 13:25') ORDER BY c1;
c1	c2	c3
2009-03-05 13:21:14	2009-03-05 13:21:14	17
DELETE FROM t1 WHERE c1>=ADDTIME(NOW(),'5 01:01:01') AND c2 < ADDTIME(NOW(),'8 13:25') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1>=ADDTIME(NOW(),'5 01:01:01') AND c2 < ADDTIME(NOW(),'8 13:25') ORDER BY c1;
c1	c2	c3
SELECT * FROM t1 WHERE c1=ADDTIME(NOW(),'9 01:01:01') OR c2=ADDTIME(NOW(),'10 01:01:01');
c1	c2	c3
2009-03-06 13:21:14	2009-03-06 13:21:14	18
2009-03-07 13:21:14	2009-03-07 13:21:14	19
DELETE FROM t1 WHERE c1=ADDTIME(NOW(),'9 01:01:01') OR c2=ADDTIME(NOW(),'10 01:01:01');
SELECT * FROM t1 WHERE c1=ADDTIME(NOW(),'9 01:01:01') OR c2=ADDTIME(NOW(),'10 01:01:01');
c1	c2	c3
SELECT count(*) FROM t1;
count(*)
1
TRUNCATE TABLE t1;
SELECT count(*) FROM t1;
count(*)
0
CREATE TABLE t2(c1 TIMESTAMP NOT NULL PRIMARY KEY, c2 TIMESTAMP NULL, c3 INT);
INSERT INTO t1 VALUES('2001-01-01 1:30','2001-01-02 2:30',1),('2001-01-03 3:30','2001-01-02 2:30',2);
INSERT INTO t2 VALUES('2001-01-02 2:30','2001-01-03 3:30',1),('2001-01-04 4:30','2001-01-05 5:30',2);
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2001-01-01 01:30:00	2001-01-02 02:30:00	1	2001-01-02 02:30:00	2001-01-03 03:30:00	1
2001-01-03 03:30:00	2001-01-02 02:30:00	2	2001-01-02 02:30:00	2001-01-03 03:30:00	1
DELETE t1,t2 FROM t1,t2 WHERE t2.c1=t1.c2;
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
TRUNCATE TABLE t1;
TRUNCATE TABLE t2;
INSERT INTO t1 VALUES('2001-01-01 1:30','2001-01-02 2:30',1),('2001-01-03 3:30','2001-01-02 2:30',2);
INSERT INTO t2 VALUES('2001-01-02 2:30','2001-01-03 3:30',1),('2001-01-04 4:30','2001-01-05 5:30',2);
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
2001-01-01 01:30:00	2001-01-02 02:30:00	1	2001-01-02 02:30:00	2001-01-03 03:30:00	1
2001-01-03 03:30:00	2001-01-02 02:30:00	2	2001-01-02 02:30:00	2001-01-03 03:30:00	1
DELETE FROM a1, a2 USING t1 AS a1 INNER JOIN t2 AS a2 WHERE a2.c1=a1.c2;
SELECT * FROM t1,t2 WHERE t2.c1=t1.c2;
c1	c2	c3	c1	c2	c3
DROP TABLE t1,t2;
