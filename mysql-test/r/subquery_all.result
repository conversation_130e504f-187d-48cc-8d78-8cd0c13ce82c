CALL mtr.add_suppression("==[0-9]*== Warning: set address range perms: large range");
set optimizer_switch='semijoin=on,materialization=on,firstmatch=on,loosescan=on,index_condition_pushdown=on,mrr=on,mrr_cost_based=off';
set @old_opt_switch=@@optimizer_switch;
set optimizer_switch='subquery_materialization_cost_based=off';
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
drop table if exists t1,t2,t3,t4,t5,t6,t7,t8,t11,t12;
select (select 2);
(select 2)
2
explain select (select 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
Warnings:
Note	1249	Select 2 was reduced during optimization
Note	1003	/* select#1 */ select 2 AS `(select 2)`
SELECT (SELECT 1) UNION SELECT (SELECT 2);
(SELECT 1)
1
2
explain SELECT (SELECT 1) UNION SELECT (SELECT 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
3	UNION	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
5	UNION RESULT	<union1,3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1249	Select 2 was reduced during optimization
Note	1249	Select 4 was reduced during optimization
Note	1003	/* select#1 */ select 1 AS `(SELECT 1)` union /* select#3 */ select 2 AS `(SELECT 2)`
SELECT (SELECT (SELECT 0 UNION SELECT 0));
(SELECT (SELECT 0 UNION SELECT 0))
0
explain SELECT (SELECT (SELECT 0 UNION SELECT 0));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
3	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
4	UNION	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
5	UNION RESULT	<union3,4>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1249	Select 2 was reduced during optimization
Note	1003	/* select#1 */ select (/* select#3 */ select 0 union /* select#4 */ select 0) AS `(SELECT (SELECT 0 UNION SELECT 0))`
SELECT (SELECT 1 FROM (SELECT 1) as b HAVING a=1) as a;
ERROR 42S22: Reference 'a' not supported (forward reference in item list)
SELECT (SELECT 1 FROM (SELECT 1) as b HAVING b=1) as a,(SELECT 1 FROM (SELECT 1) as c HAVING a=1) as b;
ERROR 42S22: Reference 'b' not supported (forward reference in item list)
SELECT (SELECT 1),MAX(1) FROM (SELECT 1) as a;
(SELECT 1)	MAX(1)
1	1
SELECT (SELECT a) as a;
ERROR 42S22: Reference 'a' not supported (forward reference in item list)
EXPLAIN SELECT 1 FROM (SELECT 1 as a) as b  HAVING (SELECT a)=1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	<derived2>	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
2	DERIVED	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
Warnings:
Note	1276	Field or reference 'b.a' of SELECT #3 was resolved in SELECT #1
Note	1249	Select 3 was reduced during optimization
Note	1003	/* select#1 */ select 1 AS `1` from dual having true
SELECT 1 FROM (SELECT 1 as a) as b HAVING (SELECT a)=1;
1
1
SELECT (SELECT 1), a;
ERROR 42S22: Unknown column 'a' in 'field list'
SELECT 1 as a FROM (SELECT 1) as b HAVING (SELECT a)=1;
a
1
SELECT 1 FROM (SELECT (SELECT a) b) c;
ERROR 42S22: Unknown column 'a' in 'field list'
SELECT * FROM (SELECT 1 as id) b WHERE id IN (SELECT * FROM (SELECT 1 as id) c ORDER BY id);
id
1
SELECT * FROM (SELECT 1) a  WHERE 1 IN (SELECT 1,1);
ERROR 21000: Operand should contain 1 column(s)
SELECT 1 IN (SELECT 1);
1 IN (SELECT 1)
1
SELECT 1 FROM (SELECT 1 as a) b WHERE 1 IN (SELECT (SELECT a));
1
1
SELECT (SELECT 1) as a FROM (SELECT 1) b WHERE (SELECT a) IS NULL;
ERROR 42S22: Unknown column 'a' in 'field list'
SELECT (SELECT 1) as a FROM (SELECT 1) b WHERE (SELECT a) IS NOT NULL;
ERROR 42S22: Unknown column 'a' in 'field list'
SELECT (SELECT 1,2,3) = ROW(1,2,3);
(SELECT 1,2,3) = ROW(1,2,3)
1
SELECT (SELECT 1,2,3) = ROW(1,2,1);
(SELECT 1,2,3) = ROW(1,2,1)
0
SELECT (SELECT 1,2,3) < ROW(1,2,1);
(SELECT 1,2,3) < ROW(1,2,1)
0
SELECT (SELECT 1,2,3) > ROW(1,2,1);
(SELECT 1,2,3) > ROW(1,2,1)
1
SELECT (SELECT 1,2,3) = ROW(1,2,NULL);
(SELECT 1,2,3) = ROW(1,2,NULL)
NULL
SELECT ROW(1,2,3) = (SELECT 1,2,3);
ROW(1,2,3) = (SELECT 1,2,3)
1
SELECT ROW(1,2,3) = (SELECT 1,2,1);
ROW(1,2,3) = (SELECT 1,2,1)
0
SELECT ROW(1,2,3) < (SELECT 1,2,1);
ROW(1,2,3) < (SELECT 1,2,1)
0
SELECT ROW(1,2,3) > (SELECT 1,2,1);
ROW(1,2,3) > (SELECT 1,2,1)
1
SELECT ROW(1,2,3) = (SELECT 1,2,NULL);
ROW(1,2,3) = (SELECT 1,2,NULL)
NULL
SELECT (SELECT 1.5,2,'a') = ROW(1.5,2,'a');
(SELECT 1.5,2,'a') = ROW(1.5,2,'a')
1
SELECT (SELECT 1.5,2,'a') = ROW(1.5,2,'b');
(SELECT 1.5,2,'a') = ROW(1.5,2,'b')
0
SELECT (SELECT 1.5,2,'a') = ROW('1.5b',2,'b');
(SELECT 1.5,2,'a') = ROW('1.5b',2,'b')
0
Warnings:
Warning	1292	Truncated incorrect DOUBLE value: '1.5b'
SELECT (SELECT 'b',2,'a') = ROW(1.5,2,'a');
(SELECT 'b',2,'a') = ROW(1.5,2,'a')
0
SELECT (SELECT 1.5,2,'a') = ROW(1.5,'2','a');
(SELECT 1.5,2,'a') = ROW(1.5,'2','a')
1
SELECT (SELECT 1.5,'c','a') = ROW(1.5,2,'a');
(SELECT 1.5,'c','a') = ROW(1.5,2,'a')
0
SELECT (SELECT * FROM (SELECT 'test' a,'test' b) a);
ERROR 21000: Operand should contain 1 column(s)
SELECT 1 as a,(SELECT a+a) b,(SELECT b);
a	b	(SELECT b)
1	2	2
create table t1 (a int);
create table t2 (a int, b int);
create table t3 (a int);
create table t4 (a int not null, b int not null);
insert into t1 values (2);
insert into t2 values (1,7),(2,7);
insert into t4 values (4,8),(3,8),(5,9);
ANALYZE TABLE t1, t2, t4;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t4	analyze	status	OK
select (select a from t1 where t1.a = a1) as a2, (select b from t2 where t2.b=a2) as a1;
ERROR 42S22: Reference 'a1' not supported (forward reference in item list)
select (select a from t1 where t1.a=t2.a), a from t2;
(select a from t1 where t1.a=t2.a)	a
NULL	1
2	2
select (select a from t1 where t1.a=t2.b), a from t2;
(select a from t1 where t1.a=t2.b)	a
NULL	1
NULL	2
select (select a from t1), a, (select 1 union select 2 limit 1) from t2;
(select a from t1)	a	(select 1 union select 2 limit 1)
2	1	1
2	2	1
select (select a from t3), a from t2;
(select a from t3)	a
NULL	1
NULL	2
select * from t2 where t2.a=(select a from t1);
a	b
2	7
insert into t3 values (6),(7),(3);
ANALYZE TABLE t3;
Table	Op	Msg_type	Msg_text
test.t3	analyze	status	OK
select * from t2 where t2.b=(select a from t3 order by 1 desc limit 1);
a	b
1	7
2	7
(select * from t2 where t2.b=(select a from t3 order by 1 desc limit 1)) union (select * from t4 order by a limit 2) limit 3;
a	b
1	7
2	7
3	8
(select * from t2 where t2.b=(select a from t3 order by 1 desc limit 1)) union (select * from t4 where t4.b=(select max(t2.a)*4 from t2) order by a);
a	b
1	7
2	7
4	8
3	8
explain (select * from t2 where t2.b=(select a from t3 order by 1 desc limit 1)) union (select * from t4 where t4.b=(select max(t2.a)*4 from t2) order by a);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
2	SUBQUERY	t3	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using filesort
3	UNION	t4	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
4	SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
5	UNION RESULT	<union1,3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` where (`test`.`t2`.`b` = (/* select#2 */ select `test`.`t3`.`a` from `test`.`t3` order by `test`.`t3`.`a` desc limit 1)) union /* select#3 */ select `test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b` from `test`.`t4` where (`test`.`t4`.`b` = (/* select#4 */ select (max(`test`.`t2`.`a`) * 4) from `test`.`t2`))
select (select a from t3 where a<t2.a*4 order by 1 desc limit 1), a from t2;
(select a from t3 where a<t2.a*4 order by 1 desc limit 1)	a
3	1
7	2
select (select t3.a from t3 where a<8 order by 1 desc limit 1), a from
(select * from t2 where a>1) as tt;
(select t3.a from t3 where a<8 order by 1 desc limit 1)	a
7	2
explain select (select t3.a from t3 where a<8 order by 1 desc limit 1), a from
(select * from t2 where a>1) as tt;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
2	SUBQUERY	t3	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select (/* select#2 */ select `test`.`t3`.`a` from `test`.`t3` where (`test`.`t3`.`a` < 8) order by `test`.`t3`.`a` desc limit 1) AS `(select t3.a from t3 where a<8 order by 1 desc limit 1)`,`test`.`t2`.`a` AS `a` from `test`.`t2` where (`test`.`t2`.`a` > 1)
select * from t1 where t1.a=(select t2.a from t2 where t2.b=(select max(a) from t3) order by 1 desc limit 1);
a
2
select * from t1 where t1.a=(select t2.a from t2 where t2.b=(select max(a) from t3 where t3.a > t1.a) order by 1 desc limit 1);
a
2
select * from t1 where t1.a=(select t2.a from t2 where t2.b=(select max(a) from t3 where t3.a < t1.a) order by 1 desc limit 1);
a
select b,(select avg(t2.a+(select min(t3.a) from t3 where t3.a >= t4.a)) from t2) from t4;
b	(select avg(t2.a+(select min(t3.a) from t3 where t3.a >= t4.a)) from t2)
8	7.5000
8	4.5000
9	7.5000
explain select b,(select avg(t2.a+(select min(t3.a) from t3 where t3.a >= t4.a)) from t2) from t4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t4	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
3	DEPENDENT SUBQUERY	t3	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1276	Field or reference 'test.t4.a' of SELECT #3 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t4`.`b` AS `b`,(/* select#2 */ select avg((`test`.`t2`.`a` + (/* select#3 */ select min(`test`.`t3`.`a`) from `test`.`t3` where (`test`.`t3`.`a` >= `test`.`t4`.`a`)))) from `test`.`t2`) AS `(select avg(t2.a+(select min(t3.a) from t3 where t3.a >= t4.a)) from t2)` from `test`.`t4`
select * from t3 where exists (select * from t2 where t2.b=t3.a);
a
7
select * from t3 where not exists (select * from t2 where t2.b=t3.a);
a
6
3
select * from t3 where a in (select b from t2);
a
7
select * from t3 where a not in (select b from t2);
a
6
3
select * from t3 where a = some (select b from t2);
a
7
select * from t3 where a <> any (select b from t2);
a
6
3
select * from t3 where a = all (select b from t2);
a
7
select * from t3 where a <> all (select b from t2);
a
6
3
insert into t2 values (100, 5);
select * from t3 where a < any (select b from t2);
a
6
3
select * from t3 where a < all (select b from t2);
a
3
select * from t3 where a >= any (select b from t2);
a
6
7
explain select * from t3 where a >= any (select b from t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t3	NULL	ALL	NULL	NULL	NULL	NULL	3	66.67	Using where
2	SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where <nop>((`test`.`t3`.`a` >= (/* select#2 */ select min(`test`.`t2`.`b`) from `test`.`t2`)))
select * from t3 where a >= all (select b from t2);
a
7
delete from t2 where a=100;
select * from t3 where a in (select a,b from t2);
ERROR 21000: Operand should contain 1 column(s)
select * from t3 where a in (select * from t2);
ERROR 21000: Operand should contain 1 column(s)
insert into t4 values (12,7),(1,7),(10,9),(9,6),(7,6),(3,9),(1,10);
select b,max(a) as ma from t4 group by b having b < (select max(t2.a) from t2 where t2.b=t4.b);
b	ma
insert into t2 values (2,10);
select b,max(a) as ma from t4 group by b having ma < (select max(t2.a) from t2 where t2.b=t4.b);
b	ma
10	1
delete from t2 where a=2 and b=10;
select b,max(a) as ma from t4 group by b having b >= (select max(t2.a) from t2 where t2.b=t4.b);
b	ma
7	12
create table t5 (a int);
select (select a from t1 where t1.a=t2.a union select a from t5 where t5.a=t2.a), a from t2;
(select a from t1 where t1.a=t2.a union select a from t5 where t5.a=t2.a)	a
NULL	1
2	2
insert into t5 values (5);
select (select a from t1 where t1.a=t2.a union select a from t5 where t5.a=t2.a), a from t2;
(select a from t1 where t1.a=t2.a union select a from t5 where t5.a=t2.a)	a
NULL	1
2	2
insert into t5 values (2);
ANALYZE TABLE t2, t5;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
test.t5	analyze	status	OK
select (select a from t1 where t1.a=t2.a union select a from t5 where t5.a=t2.a), a from t2;
(select a from t1 where t1.a=t2.a union select a from t5 where t5.a=t2.a)	a
NULL	1
2	2
explain select (select a from t1 where t1.a=t2.a union select a from t5 where t5.a=t2.a), a from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
3	DEPENDENT UNION	t5	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
4	UNION RESULT	<union2,3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1276	Field or reference 'test.t2.a' of SELECT #2 was resolved in SELECT #1
Note	1276	Field or reference 'test.t2.a' of SELECT #3 was resolved in SELECT #1
Note	1003	/* select#1 */ select (/* select#2 */ select `test`.`t1`.`a` from `test`.`t1` where (`test`.`t1`.`a` = `test`.`t2`.`a`) union /* select#3 */ select `test`.`t5`.`a` from `test`.`t5` where (`test`.`t5`.`a` = `test`.`t2`.`a`)) AS `(select a from t1 where t1.a=t2.a union select a from t5 where t5.a=t2.a)`,`test`.`t2`.`a` AS `a` from `test`.`t2`
select (select a from t1 where t1.a=t2.a union all select a from t5 where t5.a=t2.a), a from t2;
ERROR 21000: Subquery returns more than 1 row
create table t6 (patient_uq int, clinic_uq int, index i1 (clinic_uq));
create table t7( uq int primary key, name char(25));
insert into t7 values(1,"Oblastnaia bolnitsa"),(2,"Bolnitsa Krasnogo Kresta");
insert into t6 values (1,1),(1,2),(2,2),(1,3);
ANALYZE TABLE t6, t7;
Table	Op	Msg_type	Msg_text
test.t6	analyze	status	OK
test.t7	analyze	status	OK
select * from t6 where exists (select * from t7 where uq = clinic_uq);
patient_uq	clinic_uq
1	1
1	2
2	2
explain select * from t6 where exists (select * from t7 where uq = clinic_uq);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t7	NULL	index	PRIMARY	PRIMARY	4	NULL	2	100.00	Using index
1	SIMPLE	t6	NULL	ref	i1	i1	5	test.t7.uq	1	100.00	NULL
Warnings:
Note	1276	Field or reference 'test.t6.clinic_uq' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t6`.`patient_uq` AS `patient_uq`,`test`.`t6`.`clinic_uq` AS `clinic_uq` from `test`.`t7` join `test`.`t6` where (`test`.`t6`.`clinic_uq` = `test`.`t7`.`uq`)
select * from t1 where a= (select a from t2,t4 where t2.b=t4.b);
ERROR 23000: Column 'a' in field list is ambiguous
drop table t1,t2,t3;
CREATE TABLE t3 (a varchar(20),b char(1) NOT NULL default '0');
INSERT INTO t3 VALUES ('W','a'),('A','c'),('J','b');
CREATE TABLE t2 (a varchar(20),b int NOT NULL default '0');
INSERT INTO t2 VALUES ('W','1'),('A','3'),('J','2');
CREATE TABLE t1 (a varchar(20),b date NOT NULL default '0000-00-00');
INSERT INTO t1 VALUES ('W','1732-02-22'),('A','1735-10-30'),('J','1743-04-13');
SELECT * FROM t1 WHERE b = (SELECT MIN(b) FROM t1);
a	b
W	1732-02-22
SELECT * FROM t2 WHERE b = (SELECT MIN(b) FROM t2);
a	b
W	1
SELECT * FROM t3 WHERE b = (SELECT MIN(b) FROM t3);
a	b
W	a
CREATE TABLE `t8` (
`pseudo` varchar(35) character set latin1 NOT NULL default '',
`email` varchar(60) character set latin1 NOT NULL default '',
PRIMARY KEY  (`pseudo`),
UNIQUE KEY `email` (`email`)
) ENGINE=INNODB CHARSET=latin1 ROW_FORMAT=DYNAMIC;
INSERT INTO t8 (pseudo,email) VALUES ('joce','test');
INSERT INTO t8 (pseudo,email) VALUES ('joce1','test1');
INSERT INTO t8 (pseudo,email) VALUES ('2joce1','2test1');
ANALYZE TABLE t1, t2, t3, t8;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
test.t8	analyze	status	OK
EXPLAIN SELECT pseudo,(SELECT email FROM t8 WHERE pseudo=(SELECT pseudo FROM t8 WHERE pseudo='joce')) FROM t8 WHERE pseudo=(SELECT pseudo FROM t8 WHERE pseudo='joce');
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t8	NULL	const	PRIMARY	PRIMARY	37	const	1	100.00	Using index
4	SUBQUERY	t8	NULL	const	PRIMARY	PRIMARY	37	const	1	100.00	Using index
2	SUBQUERY	t8	NULL	const	PRIMARY	PRIMARY	37	const	1	100.00	NULL
3	SUBQUERY	t8	NULL	const	PRIMARY	PRIMARY	37	const	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select 'joce' AS `pseudo`,(/* select#2 */ select 'test' from `test`.`t8` where true) AS `(SELECT email FROM t8 WHERE pseudo=(SELECT pseudo FROM t8 WHERE pseudo='joce'))` from `test`.`t8` where true
SELECT pseudo FROM t8 WHERE pseudo=(SELECT pseudo,email FROM
t8 WHERE pseudo='joce');
ERROR 21000: Operand should contain 1 column(s)
SELECT pseudo FROM t8 WHERE pseudo=(SELECT * FROM t8 WHERE
pseudo='joce');
ERROR 21000: Operand should contain 1 column(s)
SELECT pseudo FROM t8 WHERE pseudo=(SELECT pseudo FROM t8 WHERE pseudo='joce');
pseudo
joce
SELECT pseudo FROM t8 WHERE pseudo=(SELECT pseudo FROM t8 WHERE pseudo LIKE '%joce%');
ERROR 21000: Subquery returns more than 1 row
drop table if exists t1,t2,t3,t4,t5,t6,t7,t8;
CREATE TABLE `t1` (
`topic` mediumint(8) unsigned NOT NULL default '0',
`date` date NOT NULL default '0000-00-00',
`pseudo` varchar(35) character set latin1 NOT NULL default '',
PRIMARY KEY  (`pseudo`,`date`,`topic`),
KEY `topic` (`topic`)
) ENGINE=INNODB ROW_FORMAT=DYNAMIC;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 (topic,date,pseudo) VALUES
('43506','2002-10-02','joce'),('40143','2002-08-03','joce');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT DISTINCT date FROM t1 WHERE date='2002-08-03';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	PRIMARY,topic	topic	3	NULL	2	50.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`t1`.`date` AS `date` from `test`.`t1` where (`test`.`t1`.`date` = DATE'2002-08-03')
EXPLAIN SELECT (SELECT DISTINCT date FROM t1 WHERE date='2002-08-03');
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
2	SUBQUERY	t1	NULL	index	PRIMARY,topic	topic	3	NULL	2	50.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select (/* select#2 */ select distinct `test`.`t1`.`date` from `test`.`t1` where (`test`.`t1`.`date` = DATE'2002-08-03')) AS `(SELECT DISTINCT date FROM t1 WHERE date='2002-08-03')`
SELECT DISTINCT date FROM t1 WHERE date='2002-08-03';
date
2002-08-03
SELECT (SELECT DISTINCT date FROM t1 WHERE date='2002-08-03');
(SELECT DISTINCT date FROM t1 WHERE date='2002-08-03')
2002-08-03
SELECT 1 FROM t1 WHERE 1=(SELECT 1 UNION SELECT 1) UNION ALL SELECT 1;
1
1
1
1
SELECT 1 FROM t1 WHERE 1=(SELECT 1 UNION ALL SELECT 1) UNION SELECT 1;
ERROR 21000: Subquery returns more than 1 row
EXPLAIN SELECT 1 FROM t1 WHERE 1=(SELECT 1 UNION SELECT 1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	index	NULL	topic	3	NULL	2	100.00	Using index
2	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
3	UNION	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
4	UNION RESULT	<union2,3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` where true
drop table t1;
CREATE TABLE `t1` (
`numeropost` mediumint(8) unsigned NOT NULL auto_increment,
`maxnumrep` int(10) unsigned NOT NULL default '0',
PRIMARY KEY  (`numeropost`),
UNIQUE KEY `maxnumrep` (`maxnumrep`)
) ENGINE=INNODB ;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 (numeropost,maxnumrep) VALUES (40143,1),(43506,2);
CREATE TABLE `t2` (
`mot` varchar(30) NOT NULL default '',
`topic` mediumint(8) unsigned NOT NULL default '0',
`date` date NOT NULL default '0000-00-00',
`pseudo` varchar(35) NOT NULL default '',
PRIMARY KEY  (`mot`,`pseudo`,`date`,`topic`)
) ENGINE=INNODB ROW_FORMAT=DYNAMIC;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 (mot,topic,date,pseudo) VALUES ('joce','40143','2002-10-22','joce'), ('joce','43506','2002-10-22','joce');
select numeropost as a FROM t1 GROUP BY (SELECT 1 FROM t1 HAVING a=1);
a
40143
SELECT numeropost,maxnumrep FROM t1 WHERE exists (SELECT 1 FROM t2 WHERE (mot='joce') AND date >= '2002-10-21' AND t1.numeropost = t2.topic) ORDER BY maxnumrep DESC LIMIT 0, 20;
numeropost	maxnumrep
43506	2
40143	1
SELECT (SELECT 1) as a FROM (SELECT 1 FROM t1 HAVING a=1) b;
ERROR 42S22: Unknown column 'a' in 'having clause'
SELECT 1 IN (SELECT 1 FROM t2 HAVING a);
ERROR 42S22: Unknown column 'a' in 'having clause'
SELECT * from t2 where topic IN (SELECT topic FROM t2 GROUP BY topic);
mot	topic	date	pseudo
joce	40143	2002-10-22	joce
joce	43506	2002-10-22	joce
SELECT * from t2 where topic IN (SELECT topic FROM t2 GROUP BY topic HAVING topic < 4100);
mot	topic	date	pseudo
SELECT * from t2 where topic IN (SELECT SUM(topic) FROM t1);
mot	topic	date	pseudo
SELECT * from t2 where topic = any (SELECT topic FROM t2 GROUP BY topic);
mot	topic	date	pseudo
joce	40143	2002-10-22	joce
joce	43506	2002-10-22	joce
SELECT * from t2 where topic = any (SELECT topic FROM t2 GROUP BY topic HAVING topic < 4100);
mot	topic	date	pseudo
SELECT * from t2 where topic = any (SELECT SUM(topic) FROM t1);
mot	topic	date	pseudo
SELECT * from t2 where topic = all (SELECT topic FROM t2 GROUP BY topic);
mot	topic	date	pseudo
SELECT * from t2 where topic = all (SELECT topic FROM t2 GROUP BY topic HAVING topic < 4100);
mot	topic	date	pseudo
joce	40143	2002-10-22	joce
joce	43506	2002-10-22	joce
SELECT *, topic = all (SELECT topic FROM t2 GROUP BY topic HAVING topic < 4100) from t2;
mot	topic	date	pseudo	topic = all (SELECT topic FROM t2 GROUP BY topic HAVING topic < 4100)
joce	40143	2002-10-22	joce	1
joce	43506	2002-10-22	joce	1
SELECT * from t2 where topic = all (SELECT SUM(topic) FROM t2);
mot	topic	date	pseudo
SELECT * from t2 where topic <> any (SELECT SUM(topic) FROM t2);
mot	topic	date	pseudo
joce	40143	2002-10-22	joce
joce	43506	2002-10-22	joce
SELECT * from t2 where topic IN (SELECT topic FROM t2 GROUP BY topic HAVING topic < 41000);
mot	topic	date	pseudo
joce	40143	2002-10-22	joce
SELECT * from t2 where topic = any (SELECT topic FROM t2 GROUP BY topic HAVING topic < 41000);
mot	topic	date	pseudo
joce	40143	2002-10-22	joce
SELECT * from t2 where topic = all (SELECT topic FROM t2 GROUP BY topic HAVING topic < 41000);
mot	topic	date	pseudo
joce	40143	2002-10-22	joce
SELECT *, topic = all (SELECT topic FROM t2 GROUP BY topic HAVING topic < 41000) from t2;
mot	topic	date	pseudo	topic = all (SELECT topic FROM t2 GROUP BY topic HAVING topic < 41000)
joce	40143	2002-10-22	joce	1
joce	43506	2002-10-22	joce	0
drop table t1,t2;
CREATE TABLE `t1` (
`numeropost` mediumint(8) unsigned NOT NULL auto_increment,
`maxnumrep` int(10) unsigned NOT NULL default '0',
PRIMARY KEY  (`numeropost`),
UNIQUE KEY `maxnumrep` (`maxnumrep`)
) ENGINE=INNODB ;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 (numeropost,maxnumrep) VALUES (1,0),(2,1);
select numeropost as a FROM t1 GROUP BY (SELECT 1 FROM t1 HAVING a=1);
ERROR 21000: Subquery returns more than 1 row
select numeropost as a FROM t1 ORDER BY (SELECT 1 FROM t1 HAVING a=1);
ERROR 21000: Subquery returns more than 1 row
drop table t1;
create table t1 (a int);
insert into t1 values (1),(2),(3);
(select * from t1) union (select * from t1) order by (select a from t1 limit 1);
a
1
2
3
drop table t1;
CREATE TABLE t1 (field char(1) NOT NULL DEFAULT 'b');
INSERT INTO t1 VALUES ();
SELECT field FROM t1 WHERE 1=(SELECT 1 UNION ALL SELECT 1 FROM (SELECT 1) a HAVING field='b');
ERROR 21000: Subquery returns more than 1 row
drop table t1;
CREATE TABLE `t1` (
`numeropost` mediumint(8) unsigned NOT NULL default '0',
`numreponse` int(10) unsigned NOT NULL auto_increment,
`pseudo` varchar(35) NOT NULL default '',
PRIMARY KEY  (`numeropost`,`numreponse`),
UNIQUE KEY `numreponse` (`numreponse`),
KEY `pseudo` (`pseudo`,`numeropost`)
) ENGINE=INNODB;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
SELECT (SELECT numeropost FROM t1 HAVING numreponse=a),numreponse FROM (SELECT * FROM t1) as a;
ERROR 42S22: Reference 'numreponse' not supported (forward reference in item list)
SELECT numreponse, (SELECT numeropost FROM t1 HAVING numreponse=a) FROM (SELECT * FROM t1) as a;
ERROR 42S22: Unknown column 'a' in 'having clause'
SELECT numreponse, (SELECT numeropost FROM t1 HAVING numreponse=1) FROM (SELECT * FROM t1) as a;
numreponse	(SELECT numeropost FROM t1 HAVING numreponse=1)
INSERT INTO t1 (numeropost,numreponse,pseudo) VALUES (1,1,'joce'),(1,2,'joce'),(1,3,'test');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT numreponse FROM t1 WHERE numeropost='1' AND numreponse=(SELECT 1 FROM t1 WHERE numeropost='1');
ERROR 21000: Subquery returns more than 1 row
EXPLAIN SELECT MAX(numreponse) FROM t1 WHERE numeropost='1';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Select tables optimized away
Warnings:
Note	1003	/* select#1 */ select max(`test`.`t1`.`numreponse`) AS `MAX(numreponse)` from `test`.`t1` where (`test`.`t1`.`numeropost` = 1)
EXPLAIN SELECT numreponse FROM t1 WHERE numeropost='1' AND numreponse=(SELECT MAX(numreponse) FROM t1 WHERE numeropost='1');
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	const	PRIMARY,numreponse	PRIMARY	7	const,const	1	100.00	Using index
2	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Select tables optimized away
Warnings:
Note	1003	/* select#1 */ select '3' AS `numreponse` from `test`.`t1` where true
drop table t1;
CREATE TABLE t1 (a int(1));
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1);
SELECT 1 FROM (SELECT a FROM t1) b HAVING (SELECT b.a)=1;
1
1
drop table t1;
create table t1 (a int NOT NULL, b int, primary key (a));
create table t2 (a int NOT NULL, b int, primary key (a));
insert into t1 values (0, 10),(1, 11),(2, 12);
insert into t2 values (1, 21),(2, 22),(3, 23);
select * from t1;
a	b
0	10
1	11
2	12
update t1 set b= (select b from t1);
ERROR HY000: You can't specify target table 't1' for update in FROM clause
update t1 set b= (select b from t2);
ERROR 21000: Subquery returns more than 1 row
update t1 set b= (select b from t2 where t1.a = t2.a);
select * from t1;
a	b
0	NULL
1	21
2	22
drop table t1, t2;
create table t1 (a int NOT NULL, b int, primary key (a));
create table t2 (a int NOT NULL, b int, primary key (a));
insert into t1 values (0, 10),(1, 11),(2, 12);
insert into t2 values (1, 21),(2, 12),(3, 23);
select * from t1;
a	b
0	10
1	11
2	12
select * from t1 where b = (select b from t2 where t1.a = t2.a);
a	b
2	12
delete from t1 where b = (select b from t1);
ERROR HY000: You can't specify target table 't1' for update in FROM clause
delete from t1 where b = (select b from t2);
ERROR 21000: Subquery returns more than 1 row
delete from t1 where b = (select b from t2 where t1.a = t2.a);
select * from t1;
a	b
0	10
1	11
drop table t1, t2;
create table t11 (a int NOT NULL, b int, primary key (a));
create table t12 (a int NOT NULL, b int, primary key (a));
create table t2 (a int NOT NULL, b int, primary key (a));
insert into t11 values (0, 10),(1, 11),(2, 12);
insert into t12 values (33, 10),(22, 11),(2, 12);
insert into t2 values (1, 21),(2, 12),(3, 23);
select * from t11;
a	b
0	10
1	11
2	12
select * from t12;
a	b
2	12
22	11
33	10
delete t11.*, t12.* from t11,t12 where t11.a = t12.a and t11.b = (select b from t12 where t11.a = t12.a);
ERROR HY000: You can't specify target table 't12' for update in FROM clause
delete t11.*, t12.* from t11,t12 where t11.a = t12.a and t11.b = (select b from t2);
ERROR 21000: Subquery returns more than 1 row
delete t11.*, t12.* from t11,t12 where t11.a = t12.a and t11.b = (select b from t2 where t11.a = t2.a);
select * from t11;
a	b
0	10
1	11
select * from t12;
a	b
22	11
33	10
drop table t11, t12, t2;
CREATE TABLE t1 (x int);
create table t2 (a int);
create table t3 (b int);
insert into t2 values (1);
insert into t3 values (1),(2);
INSERT INTO t1 (x) VALUES ((SELECT x FROM t1));
ERROR HY000: You can't specify target table 't1' for update in FROM clause
INSERT INTO t1 (x) VALUES ((SELECT b FROM t3));
ERROR 21000: Subquery returns more than 1 row
INSERT INTO t1 (x) VALUES ((SELECT a FROM t2));
select * from t1;
x
1
insert into t2 values (1);
INSERT INTO t1 (x) select (SELECT SUM(a)+1 FROM t2) FROM t2;
select * from t1;
x
1
3
3
INSERT INTO t1 (x) select (SELECT SUM(x)+2 FROM t1) FROM t2;
select * from t1;
x
1
3
3
9
9
INSERT INTO t1 (x) VALUES ((SELECT SUM(x) FROM t2));
INSERT INTO t1 (x) SELECT (SELECT SUM(a)+b FROM t2) from t3;
select * from t1;
x
1
3
3
9
9
NULL
3
4
drop table t1, t2, t3;
CREATE TABLE t1 (x int not null, y int, primary key (x));
create table t2 (a int);
create table t3 (a int);
insert into t2 values (1);
insert into t3 values (1),(2);
select * from t1;
x	y
replace into t1 (x, y) VALUES ((SELECT x FROM t1), (SELECT a+1 FROM t2));
ERROR HY000: You can't specify target table 't1' for update in FROM clause
replace into t1 (x, y) VALUES ((SELECT a FROM t3), (SELECT a+1 FROM t2));
ERROR 21000: Subquery returns more than 1 row
replace into t1 (x, y) VALUES ((SELECT a FROM t2), (SELECT a+1 FROM t2));
select * from t1;
x	y
1	2
replace into t1 (x, y) VALUES ((SELECT a FROM t2), (SELECT a+2 FROM t2));
select * from t1;
x	y
1	3
replace LOW_PRIORITY into t1 (x, y) VALUES ((SELECT a+1 FROM t2), (SELECT a FROM t2));
select * from t1;
x	y
1	3
2	1
drop table t1, t2, t3;
SELECT * FROM (SELECT 1) b WHERE 1 IN (SELECT *);
ERROR HY000: No tables used
CREATE TABLE t2 (id int(11) default NULL, KEY id (id)) ENGINE=INNODB CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES (1),(2);
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
SELECT * FROM t2 WHERE id IN (SELECT 1);
id
1
EXPLAIN SELECT * FROM t2 WHERE id IN (SELECT 1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ref	id	id	5	const	1	100.00	Using index
Warnings:
Note	1249	Select 2 was reduced during optimization
Note	1003	/* select#1 */ select `test`.`t2`.`id` AS `id` from `test`.`t2` where (`test`.`t2`.`id` = 1)
SELECT * FROM t2 WHERE id IN (SELECT 1 UNION SELECT 3);
id
1
SELECT * FROM t2 WHERE id IN (SELECT 1+(select 1));
id
2
EXPLAIN SELECT * FROM t2 WHERE id IN (SELECT 1+(select 1));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ref	id	id	5	const	1	100.00	Using index
Warnings:
Note	1249	Select 3 was reduced during optimization
Note	1249	Select 2 was reduced during optimization
Note	1003	/* select#1 */ select `test`.`t2`.`id` AS `id` from `test`.`t2` where (`test`.`t2`.`id` = (1 + 1))
EXPLAIN SELECT * FROM t2 WHERE id IN (SELECT 1 UNION SELECT 3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	index	NULL	id	5	NULL	2	100.00	Using where; Using index
2	DEPENDENT SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
3	DEPENDENT UNION	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
4	UNION RESULT	<union2,3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`id` AS `id` from `test`.`t2` where <in_optimizer>(`test`.`t2`.`id`,<exists>(/* select#2 */ select 1 having (<cache>(`test`.`t2`.`id`) = <ref_null_helper>(1)) union /* select#3 */ select 3 having (<cache>(`test`.`t2`.`id`) = <ref_null_helper>(3))))
SELECT * FROM t2 WHERE id IN (SELECT 5 UNION SELECT 3);
id
SELECT * FROM t2 WHERE id IN (SELECT 5 UNION SELECT 2);
id
2
INSERT INTO t2 VALUES ((SELECT * FROM t2));
ERROR HY000: You can't specify target table 't2' for update in FROM clause
INSERT INTO t2 VALUES ((SELECT id FROM t2));
ERROR HY000: You can't specify target table 't2' for update in FROM clause
SELECT * FROM t2;
id
1
2
CREATE TABLE t1 (id int(11) default NULL, KEY id (id)) ENGINE=INNODB CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 values (1),(1);
UPDATE t2 SET id=(SELECT * FROM t1);
ERROR 21000: Subquery returns more than 1 row
drop table t2, t1;
create table t1 (a int);
insert into t1 values (1),(2),(3);
select 1 IN (SELECT * from t1);
1 IN (SELECT * from t1)
1
select 10 IN (SELECT * from t1);
10 IN (SELECT * from t1)
0
select NULL IN (SELECT * from t1);
NULL IN (SELECT * from t1)
NULL
update t1 set a=NULL where a=2;
select 1 IN (SELECT * from t1);
1 IN (SELECT * from t1)
1
select 3 IN (SELECT * from t1);
3 IN (SELECT * from t1)
1
select 10 IN (SELECT * from t1);
10 IN (SELECT * from t1)
NULL
select 1 > ALL (SELECT * from t1);
1 > ALL (SELECT * from t1)
0
select 10 > ALL (SELECT * from t1);
10 > ALL (SELECT * from t1)
NULL
select 1 > ANY (SELECT * from t1);
1 > ANY (SELECT * from t1)
NULL
select 10 > ANY (SELECT * from t1);
10 > ANY (SELECT * from t1)
1
drop table t1;
create table t1 (a varchar(20));
insert into t1 values ('A'),('BC'),('DEF');
select 'A' IN (SELECT * from t1);
'A' IN (SELECT * from t1)
1
select 'XYZS' IN (SELECT * from t1);
'XYZS' IN (SELECT * from t1)
0
select NULL IN (SELECT * from t1);
NULL IN (SELECT * from t1)
NULL
update t1 set a=NULL where a='BC';
select 'A' IN (SELECT * from t1);
'A' IN (SELECT * from t1)
1
select 'DEF' IN (SELECT * from t1);
'DEF' IN (SELECT * from t1)
1
select 'XYZS' IN (SELECT * from t1);
'XYZS' IN (SELECT * from t1)
NULL
select 'A' > ALL (SELECT * from t1);
'A' > ALL (SELECT * from t1)
0
select 'XYZS' > ALL (SELECT * from t1);
'XYZS' > ALL (SELECT * from t1)
NULL
select 'A' > ANY (SELECT * from t1);
'A' > ANY (SELECT * from t1)
NULL
select 'XYZS' > ANY (SELECT * from t1);
'XYZS' > ANY (SELECT * from t1)
1
drop table t1;
create table t1 (a float);
insert into t1 values (1.5),(2.5),(3.5);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
select 1.5 IN (SELECT * from t1);
1.5 IN (SELECT * from t1)
1
select 10.5 IN (SELECT * from t1);
10.5 IN (SELECT * from t1)
0
select NULL IN (SELECT * from t1);
NULL IN (SELECT * from t1)
NULL
update t1 set a=NULL where a=2.5;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
select 1.5 IN (SELECT * from t1);
1.5 IN (SELECT * from t1)
1
select 3.5 IN (SELECT * from t1);
3.5 IN (SELECT * from t1)
1
select 10.5 IN (SELECT * from t1);
10.5 IN (SELECT * from t1)
NULL
select 1.5 > ALL (SELECT * from t1);
1.5 > ALL (SELECT * from t1)
0
select 10.5 > ALL (SELECT * from t1);
10.5 > ALL (SELECT * from t1)
NULL
select 1.5 > ANY (SELECT * from t1);
1.5 > ANY (SELECT * from t1)
NULL
select 10.5 > ANY (SELECT * from t1);
10.5 > ANY (SELECT * from t1)
1
explain select (select a+1) from t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
Note	1249	Select 2 was reduced during optimization
Note	1003	/* select#1 */ select (`test`.`t1`.`a` + 1) AS `(select a+1)` from `test`.`t1`
select (select a+1) from t1;
(select a+1)
2.5
NULL
4.5
drop table t1;
CREATE TABLE t1 (a int(11) NOT NULL default '0', PRIMARY KEY  (a));
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE t2 (a int(11) default '0', INDEX (a));
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1),(2),(3),(4);
INSERT INTO t2 VALUES (1),(2),(3);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
SELECT t1.a, t1.a in (select t2.a from t2) FROM t1;
a	t1.a in (select t2.a from t2)
1	1
2	1
3	1
4	0
explain SELECT t1.a, t1.a in (select t2.a from t2) FROM t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	index	NULL	PRIMARY	4	NULL	4	100.00	Using index
2	SUBQUERY	t2	NULL	index	a	a	5	NULL	3	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,<in_optimizer>(`test`.`t1`.`a`,`test`.`t1`.`a` in ( <materialize> (/* select#2 */ select `test`.`t2`.`a` from `test`.`t2` where true having true ), <primary_index_lookup>(`test`.`t1`.`a` in <temporary table> on <auto_distinct_key> where ((`test`.`t1`.`a` = `<materialized_subquery>`.`a`))))) AS `t1.a in (select t2.a from t2)` from `test`.`t1`
CREATE TABLE t3 (a int(11) default '0');
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t3 VALUES (1),(2),(3);
ANALYZE TABLE t3;
Table	Op	Msg_type	Msg_text
test.t3	analyze	status	OK
SELECT t1.a, t1.a in (select t2.a from t2,t3 where t3.a=t2.a) FROM t1;
a	t1.a in (select t2.a from t2,t3 where t3.a=t2.a)
1	1
2	1
3	1
4	0
explain SELECT t1.a, t1.a in (select t2.a from t2,t3 where t3.a=t2.a) FROM t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	index	NULL	PRIMARY	4	NULL	4	100.00	Using index
2	SUBQUERY	t3	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
2	SUBQUERY	t2	NULL	ref	a	a	5	test.t3.a	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,<in_optimizer>(`test`.`t1`.`a`,`test`.`t1`.`a` in ( <materialize> (/* select#2 */ select `test`.`t2`.`a` from `test`.`t2` join `test`.`t3` where (`test`.`t2`.`a` = `test`.`t3`.`a`) having true ), <primary_index_lookup>(`test`.`t1`.`a` in <temporary table> on <auto_distinct_key> where ((`test`.`t1`.`a` = `<materialized_subquery>`.`a`))))) AS `t1.a in (select t2.a from t2,t3 where t3.a=t2.a)` from `test`.`t1`
drop table t1,t2,t3;
create table t1 (a float);
select 10.5 IN (SELECT * from t1 LIMIT 1);
ERROR 42000: This version of MySQL doesn't yet support 'LIMIT & IN/ALL/ANY/SOME subquery'
select 10.5 IN (SELECT * from t1 LIMIT 1 UNION SELECT 1.5);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'UNION SELECT 1.5)' at line 1
select 10.5 IN (SELECT * from t1 UNION SELECT 1.5 LIMIT 1);
ERROR 42000: This version of MySQL doesn't yet support 'LIMIT & IN/ALL/ANY/SOME subquery'
drop table t1;
create table t1 (a int, b int, c varchar(10));
create table t2 (a int);
insert into t1 values (1,2,'a'),(2,3,'b'),(3,4,'c');
insert into t2 values (1),(2),(NULL);
select a, (select a,b,c from t1 where t1.a=t2.a) = ROW(a,2,'a'),(select c from t1 where a=t2.a)  from t2;
a	(select a,b,c from t1 where t1.a=t2.a) = ROW(a,2,'a')	(select c from t1 where a=t2.a)
1	1	a
2	0	b
NULL	NULL	NULL
select a, (select a,b,c from t1 where t1.a=t2.a) = ROW(a,3,'b'),(select c from t1 where a=t2.a) from t2;
a	(select a,b,c from t1 where t1.a=t2.a) = ROW(a,3,'b')	(select c from t1 where a=t2.a)
1	0	a
2	1	b
NULL	NULL	NULL
select a, (select a,b,c from t1 where t1.a=t2.a) = ROW(a,4,'c'),(select c from t1 where a=t2.a) from t2;
a	(select a,b,c from t1 where t1.a=t2.a) = ROW(a,4,'c')	(select c from t1 where a=t2.a)
1	0	a
2	0	b
NULL	NULL	NULL
drop table t1,t2;
create table t1 (a int, b real, c varchar(10));
insert into t1 values (1, 1, 'a'), (2,2,'b'), (NULL, 2, 'b');
select ROW(1, 1, 'a') IN (select a,b,c from t1);
ROW(1, 1, 'a') IN (select a,b,c from t1)
1
select ROW(1, 2, 'a') IN (select a,b,c from t1);
ROW(1, 2, 'a') IN (select a,b,c from t1)
0
select ROW(1, 1, 'a') IN (select b,a,c from t1);
ROW(1, 1, 'a') IN (select b,a,c from t1)
1
select ROW(1, 1, 'a') IN (select a,b,c from t1 where a is not null);
ROW(1, 1, 'a') IN (select a,b,c from t1 where a is not null)
1
select ROW(1, 2, 'a') IN (select a,b,c from t1 where a is not null);
ROW(1, 2, 'a') IN (select a,b,c from t1 where a is not null)
0
select ROW(1, 1, 'a') IN (select b,a,c from t1 where a is not null);
ROW(1, 1, 'a') IN (select b,a,c from t1 where a is not null)
1
select ROW(1, 1, 'a') IN (select a,b,c from t1 where c='b' or c='a');
ROW(1, 1, 'a') IN (select a,b,c from t1 where c='b' or c='a')
1
select ROW(1, 2, 'a') IN (select a,b,c from t1 where c='b' or c='a');
ROW(1, 2, 'a') IN (select a,b,c from t1 where c='b' or c='a')
0
select ROW(1, 1, 'a') IN (select b,a,c from t1 where c='b' or c='a');
ROW(1, 1, 'a') IN (select b,a,c from t1 where c='b' or c='a')
1
select ROW(1, 1, 'a') IN (select b,a,c from t1 limit 2);
ERROR 42000: This version of MySQL doesn't yet support 'LIMIT & IN/ALL/ANY/SOME subquery'
drop table t1;
create table t1 (a int);
insert into t1 values (1);
do @a:=(SELECT a from t1);
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
select @a;
@a
1
set @a:=2;
set @a:=(SELECT a from t1);
select @a;
@a
1
drop table t1;
do (SELECT a from t1);
ERROR 42S02: Table 'test.t1' doesn't exist
set @a:=(SELECT a from t1);
ERROR 42S02: Table 'test.t1' doesn't exist
CREATE TABLE t1 (a int, KEY(a));
HANDLER t1 OPEN;
HANDLER t1 READ a=((SELECT 1));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '(SELECT 1))' at line 1
HANDLER t1 CLOSE;
drop table t1;
create table t1 (a int);
create table t2 (b int);
insert into t1 values (1),(2);
insert into t2 values (1);
select a from t1 where a in (select a from t1 where a in (select b from t2));
a
1
drop table t1, t2;
create table t1 (a int, b int);
create table t2 like t1;
insert into t1 values (1,2),(1,3),(1,4),(1,5);
insert into t2 values (1,2),(1,3);
select * from t1 where row(a,b) in (select a,b from t2);
a	b
1	2
1	3
drop table t1, t2;
CREATE TABLE `t1` (`i` int(11) NOT NULL default '0',PRIMARY KEY  (`i`)) ENGINE=INNODB CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1);
UPDATE t1 SET i=i+1 WHERE i=(SELECT MAX(i));
select * from t1;
i
2
drop table t1;
CREATE TABLE t1 (a int(1));
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
EXPLAIN SELECT (SELECT RAND() FROM t1) FROM t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
2	UNCACHEABLE SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select (/* select#2 */ select rand() from `test`.`t1`) AS `(SELECT RAND() FROM t1)` from `test`.`t1`
EXPLAIN SELECT (SELECT BENCHMARK(1,1) FROM t1) FROM t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
2	UNCACHEABLE SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select (/* select#2 */ select benchmark(1,1) from `test`.`t1`) AS `(SELECT BENCHMARK(1,1) FROM t1)` from `test`.`t1`
drop table t1;
CREATE TABLE `t1` (
`mot` varchar(30) character set latin1 NOT NULL default '',
`topic` mediumint(8) unsigned NOT NULL default '0',
`date` date NOT NULL default '0000-00-00',
`pseudo` varchar(35) character set latin1 NOT NULL default '',
PRIMARY KEY  (`mot`,`pseudo`,`date`,`topic`),
KEY `pseudo` (`pseudo`,`date`,`topic`),
KEY `topic` (`topic`)
) ENGINE=INNODB CHARSET=latin1 ROW_FORMAT=DYNAMIC;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE `t2` (
`mot` varchar(30) character set latin1 NOT NULL default '',
`topic` mediumint(8) unsigned NOT NULL default '0',
`date` date NOT NULL default '0000-00-00',
`pseudo` varchar(35) character set latin1 NOT NULL default '',
PRIMARY KEY  (`mot`,`pseudo`,`date`,`topic`),
KEY `pseudo` (`pseudo`,`date`,`topic`),
KEY `topic` (`topic`)
) ENGINE=INNODB CHARSET=latin1 ROW_FORMAT=DYNAMIC;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE `t3` (
`numeropost` mediumint(8) unsigned NOT NULL auto_increment,
`maxnumrep` int(10) unsigned NOT NULL default '0',
PRIMARY KEY  (`numeropost`),
UNIQUE KEY `maxnumrep` (`maxnumrep`)
) ENGINE=INNODB CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES ('joce','1','','joce'),('test','2','','test');
Warnings:
Warning	1265	Data truncated for column 'date' at row 1
Warning	1265	Data truncated for column 'date' at row 2
INSERT INTO t2 VALUES ('joce','1','','joce'),('test','2','','test');
Warnings:
Warning	1265	Data truncated for column 'date' at row 1
Warning	1265	Data truncated for column 'date' at row 2
INSERT INTO t3 VALUES (1,1);
SELECT DISTINCT topic FROM t2 WHERE NOT EXISTS(SELECT * FROM t3 WHERE
numeropost=topic);
topic
2
select * from t1;
mot	topic	date	pseudo
joce	1	0000-00-00	joce
test	2	0000-00-00	test
DELETE FROM t1 WHERE topic IN (SELECT DISTINCT topic FROM t2 WHERE NOT
EXISTS(SELECT * FROM t3 WHERE numeropost=topic));
select * from t1;
mot	topic	date	pseudo
joce	1	0000-00-00	joce
drop table t1, t2, t3;
SELECT * FROM (SELECT 1 as a,(SELECT a)) a;
a	(SELECT a)
1	1
CREATE TABLE t1 charset utf8mb4 SELECT * FROM (SELECT 1 as a,(SELECT 1)) a;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `a` int NOT NULL DEFAULT '0',
  `(SELECT 1)` int NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
drop table t1;
CREATE TABLE t1 charset utf8mb4 SELECT * FROM (SELECT 1 as a,(SELECT a)) a;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `a` int NOT NULL DEFAULT '0',
  `(SELECT a)` int NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
CREATE TABLE t1 charset utf8mb4 SELECT * FROM (SELECT 1 as a,(SELECT a+0)) a;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `a` int NOT NULL DEFAULT '0',
  `(SELECT a+0)` int NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
CREATE TABLE t1 charset utf8mb4 SELECT (SELECT 1 as a UNION SELECT 1+1 limit 1,1) as a;
select * from t1;
a
2
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `a` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
drop table t1;
create table t1 (a int);
insert into t1 values (1), (2), (3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select a,(select (select rand() from t1 limit 1)  from t1 limit 1)
from t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	UNCACHEABLE SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
3	UNCACHEABLE SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,(/* select#2 */ select (/* select#3 */ select rand() from `test`.`t1` limit 1) from `test`.`t1` limit 1) AS `(select (select rand() from t1 limit 1)  from t1 limit 1)` from `test`.`t1`
drop table t1;
select t1.Continent, t2.Name, t2.Population from t1 LEFT JOIN t2 ON t1.Code = t2.Country  where t2.Population IN (select max(t2.Population) AS Population from t2, t1 where t2.Country = t1.Code group by Continent);
ERROR 42S02: Table 'test.t1' doesn't exist
CREATE TABLE t1 (
ID int(11) NOT NULL auto_increment,
name char(35) NOT NULL default '',
t2 char(3) NOT NULL default '',
District char(20) NOT NULL default '',
Population int(11) NOT NULL default '0',
PRIMARY KEY  (ID)
) ENGINE=INNODB;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (130,'Sydney','AUS','New South Wales',3276207);
INSERT INTO t1 VALUES (131,'Melbourne','AUS','Victoria',2865329);
INSERT INTO t1 VALUES (132,'Brisbane','AUS','Queensland',1291117);
CREATE TABLE t2 (
Code char(3) NOT NULL default '',
Name char(52) NOT NULL default '',
Continent enum('Asia','Europe','North America','Africa','Oceania','Antarctica','South America') NOT NULL default 'Asia',
Region char(26) NOT NULL default '',
SurfaceArea float(10,2) NOT NULL default '0.00',
IndepYear smallint(6) default NULL,
Population int(11) NOT NULL default '0',
LifeExpectancy float(3,1) default NULL,
GNP float(10,2) default NULL,
GNPOld float(10,2) default NULL,
LocalName char(45) NOT NULL default '',
GovernmentForm char(45) NOT NULL default '',
HeadOfState char(60) default NULL,
Capital int(11) default NULL,
Code2 char(2) NOT NULL default '',
PRIMARY KEY  (Code)
) ENGINE=INNODB;
Warnings:
Warning	1681	Specifying number of digits for floating point data types is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Specifying number of digits for floating point data types is deprecated and will be removed in a future release.
Warning	1681	Specifying number of digits for floating point data types is deprecated and will be removed in a future release.
Warning	1681	Specifying number of digits for floating point data types is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES ('AUS','Australia','Oceania','Australia and New Zealand',7741220.00,1901,18886000,79.8,351182.00,392911.00,'Australia','Constitutional Monarchy, Federation','Elisabeth II',135,'AU');
INSERT INTO t2 VALUES ('AZE','Azerbaijan','Asia','Middle East',86600.00,1991,7734000,62.9,4127.00,4100.00,'Azärbaycan','Federal Republic','Heydär Äliyev',144,'AZ');
select t2.Continent, t1.Name, t1.Population from t2 LEFT JOIN t1 ON t2.Code = t1.t2  where t1.Population IN (select max(t1.Population) AS Population from t1, t2 where t1.t2 = t2.Code group by Continent);
Continent	Name	Population
Oceania	Sydney	3276207
drop table t1, t2;
CREATE TABLE `t1` (
`id` mediumint(8) unsigned NOT NULL auto_increment,
`pseudo` varchar(35) character set latin1 NOT NULL default '',
PRIMARY KEY  (`id`),
UNIQUE KEY `pseudo` (`pseudo`)
) ENGINE=INNODB PACK_KEYS=1 ROW_FORMAT=DYNAMIC;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 (pseudo) VALUES ('test');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT 0 IN (SELECT 1 FROM t1 a);
0 IN (SELECT 1 FROM t1 a)
0
EXPLAIN SELECT 0 IN (SELECT 1 FROM t1 a);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
2	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
Warnings:
Note	1003	/* select#1 */ select <in_optimizer>(0,<exists>(/* select#2 */ select 1 from `test`.`t1` `a` where false)) AS `0 IN (SELECT 1 FROM t1 a)`
INSERT INTO t1 (pseudo) VALUES ('test1');
SELECT 0 IN (SELECT 1 FROM t1 a);
0 IN (SELECT 1 FROM t1 a)
0
EXPLAIN SELECT 0 IN (SELECT 1 FROM t1 a);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
2	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
Warnings:
Note	1003	/* select#1 */ select <in_optimizer>(0,<exists>(/* select#2 */ select 1 from `test`.`t1` `a` where false)) AS `0 IN (SELECT 1 FROM t1 a)`
drop table t1;
CREATE TABLE `t1` (
`i` int(11) NOT NULL default '0',
PRIMARY KEY  (`i`)
) ENGINE=INNODB CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1);
UPDATE t1 SET i=i+(SELECT MAX(i) FROM (SELECT 1) t) WHERE i=(SELECT MAX(i));
UPDATE t1 SET i=i+1 WHERE i=(SELECT MAX(i));
UPDATE t1 SET t.i=i+(SELECT MAX(i) FROM (SELECT 1) t);
ERROR 42S22: Unknown column 't.i' in 'field list'
select * from t1;
i
3
drop table t1;
CREATE TABLE t1 (
id int(11) default NULL
) ENGINE=INNODB CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1),(1),(2),(2),(1),(3);
CREATE TABLE t2 (
id int(11) default NULL,
name varchar(15) default NULL
) ENGINE=INNODB CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES (4,'vita'), (1,'vita'), (2,'vita'), (1,'vita');
update t1, t2 set t2.name='lenka' where t2.id in (select id from t1);
select * from t2;
id	name
4	vita
1	lenka
2	lenka
1	lenka
drop table t1,t2;
create table t1 (a int, unique index indexa (a));
insert into t1 values (-1), (-4), (-2), (NULL);
select -10 IN (select a from t1 FORCE INDEX (indexa));
-10 IN (select a from t1 FORCE INDEX (indexa))
NULL
drop table t1;
create table t1 (id int not null auto_increment primary key, salary int, key(salary));
insert into t1 (salary) values (100),(1000),(10000),(10),(500),(5000),(50000);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain SELECT id FROM t1 where salary = (SELECT MAX(salary) FROM t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ref	salary	salary	5	const	1	100.00	Using index
2	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Select tables optimized away
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id` from `test`.`t1` where (`test`.`t1`.`salary` = (/* select#2 */ select max(`test`.`t1`.`salary`) from `test`.`t1`))
drop table t1;
CREATE TABLE t1 (
ID int(10) unsigned NOT NULL auto_increment,
SUB_ID int(3) unsigned NOT NULL default '0',
REF_ID int(10) unsigned default NULL,
REF_SUB int(3) unsigned default '0',
PRIMARY KEY (ID,SUB_ID),
UNIQUE KEY t1_PK (ID,SUB_ID),
KEY t1_FK (REF_ID,REF_SUB),
KEY t1_REFID (REF_ID)
) ENGINE=INNODB CHARSET=cp1251;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1,0,NULL,NULL),(2,0,NULL,NULL);
SELECT DISTINCT REF_ID FROM t1 WHERE ID= (SELECT DISTINCT REF_ID FROM t1 WHERE ID=2);
REF_ID
DROP TABLE t1;
create table t1 (a int, b int);
create table t2 (a int, b int);
insert into t1 values (1,0), (2,0), (3,0);
insert into t2 values (1,1), (2,1), (3,1), (2,2);
update ignore t1 set b=(select b from t2 where t1.a=t2.a);
ERROR 21000: Subquery returns more than 1 row
select * from t1;
a	b
1	0
2	0
3	0
drop table t1, t2;
CREATE TABLE `t1` (
`id` mediumint(8) unsigned NOT NULL auto_increment,
`pseudo` varchar(35) NOT NULL default '',
`email` varchar(60) NOT NULL default '',
PRIMARY KEY  (`id`),
UNIQUE KEY `email` (`email`),
UNIQUE KEY `pseudo` (`pseudo`)
) ENGINE=INNODB CHARSET=latin1 PACK_KEYS=1 ROW_FORMAT=DYNAMIC;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 (id,pseudo,email) VALUES (1,'test','test'),(2,'test1','test1');
SELECT pseudo as a, pseudo as b FROM t1 GROUP BY (SELECT a) ORDER BY (SELECT id*1);
a	b
test	test
test1	test1
drop table if exists t1;
(SELECT 1 as a) UNION (SELECT 1) ORDER BY (SELECT a+0);
a
1
create table t1 (a int, b int);
create table t2 (a int, b int);
create table t3 (a int, b int);
insert into t1 values (0,100),(1,2), (1,3), (2,2), (2,7), (2,-1), (3,10);
insert into t2 values (0,0), (1,1), (2,1), (3,1), (4,1);
insert into t3 values (3,3), (2,2), (1,1);
select a,(select count(distinct t1.b) as sum from t1,t2 where t1.a=t2.a and t2.b > 0 and t1.a <= t3.b group by t1.a order by sum limit 1) from t3;
a	(select count(distinct t1.b) as sum from t1,t2 where t1.a=t2.a and t2.b > 0 and t1.a <= t3.b group by t1.a order by sum limit 1)
3	1
2	2
1	2
drop table t1,t2,t3;
create table t1 (s1 int);
create table t2 (s1 int);
insert into t1 values (1);
insert into t2 values (1);
select * from t1 where exists (select s1 from t2 having max(t2.s1)=t1.s1);
s1
1
drop table t1,t2;
create table t1 (s1 int);
create table t2 (s1 int);
insert into t1 values (1);
insert into t2 values (1);
update t1 set  s1 = s1 + 1 where 1 = (select x.s1 as A from t2 WHERE t2.s1 > t1.s1 order by A);
ERROR 42S22: Unknown column 'x.s1' in 'field list'
DROP TABLE t1, t2;
CREATE TABLE t1 (s1 CHAR(5) COLLATE latin1_german1_ci,
s2 CHAR(5) COLLATE latin1_swedish_ci);
INSERT INTO t1 VALUES ('z','?');
select * from t1 where s1 > (select max(s2) from t1);
ERROR HY000: Illegal mix of collations (latin1_german1_ci,IMPLICIT) and (latin1_swedish_ci,IMPLICIT) for operation '>'
select * from t1 where s1 > any (select max(s2) from t1);
ERROR HY000: Illegal mix of collations (latin1_german1_ci,IMPLICIT) and (latin1_swedish_ci,IMPLICIT) for operation '>'
drop table t1;
create table t1(toid int,rd int);
create table t2(userid int,pmnew int,pmtotal int);
insert into t2 values(1,0,0),(2,0,0);
insert into t1 values(1,0),(1,0),(1,0),(1,12),(1,15),(1,123),(1,12312),(1,12312),(1,123),(2,0),(2,0),(2,1),(2,2);
select userid,pmtotal,pmnew, (select count(rd) from t1 where toid=t2.userid) calc_total, (select count(rd) from t1 where rd=0 and toid=t2.userid) calc_new from t2 where userid in (select distinct toid from t1);
userid	pmtotal	pmnew	calc_total	calc_new
1	0	0	9	3
2	0	0	4	2
drop table t1, t2;
create table t1 (s1 char(5));
select (select 'a','b' from t1 union select 'a','b' from t1) from t1;
ERROR 21000: Operand should contain 1 column(s)
insert into t1 values ('tttt');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
select * from t1 where ('a','b')=(select 'a','b' from t1 union select 'a','b' from t1);
s1
tttt
explain (select * from t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`s1` AS `s1` from `test`.`t1`
(select * from t1);
s1
tttt
drop table t1;
create table t1 (s1 char(5), index s1(s1));
create table t2 (s1 char(5), index s1(s1));
insert into t1 values ('a1'),('a2'),('a3');
insert into t2 values ('a1'),('a2');
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
select s1, s1 NOT IN (SELECT s1 FROM t2) from t1;
s1	s1 NOT IN (SELECT s1 FROM t2)
a1	0
a2	0
a3	1
select s1, s1 = ANY (SELECT s1 FROM t2) from t1;
s1	s1 = ANY (SELECT s1 FROM t2)
a1	1
a2	1
a3	0
select s1, s1 <> ALL (SELECT s1 FROM t2) from t1;
s1	s1 <> ALL (SELECT s1 FROM t2)
a1	0
a2	0
a3	1
select s1, s1 NOT IN (SELECT s1 FROM t2 WHERE s1 < 'a2') from t1;
s1	s1 NOT IN (SELECT s1 FROM t2 WHERE s1 < 'a2')
a1	0
a2	1
a3	1
explain select s1, s1 NOT IN (SELECT s1 FROM t2) from t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	index	NULL	s1	6	NULL	4	100.00	Using index
2	SUBQUERY	t2	NULL	index	s1	s1	6	NULL	4	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`s1` AS `s1`,<in_optimizer>(`test`.`t1`.`s1`,`test`.`t1`.`s1` not in ( <materialize> (/* select#2 */ select `test`.`t2`.`s1` from `test`.`t2` where true having true ), <primary_index_lookup>(`test`.`t1`.`s1` in <temporary table> on <auto_distinct_key> where ((`test`.`t1`.`s1` = `<materialized_subquery>`.`s1`))))) AS `s1 NOT IN (SELECT s1 FROM t2)` from `test`.`t1`
explain select s1, s1 = ANY (SELECT s1 FROM t2) from t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	index	NULL	s1	6	NULL	4	100.00	Using index
2	SUBQUERY	t2	NULL	index	s1	s1	6	NULL	4	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`s1` AS `s1`,<in_optimizer>(`test`.`t1`.`s1`,`test`.`t1`.`s1` in ( <materialize> (/* select#2 */ select `test`.`t2`.`s1` from `test`.`t2` where true having true ), <primary_index_lookup>(`test`.`t1`.`s1` in <temporary table> on <auto_distinct_key> where ((`test`.`t1`.`s1` = `<materialized_subquery>`.`s1`))))) AS `s1 = ANY (SELECT s1 FROM t2)` from `test`.`t1`
explain select s1, s1 <> ALL (SELECT s1 FROM t2) from t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	index	NULL	s1	6	NULL	4	100.00	Using index
2	SUBQUERY	t2	NULL	index	s1	s1	6	NULL	4	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`s1` AS `s1`,<in_optimizer>(`test`.`t1`.`s1`,`test`.`t1`.`s1` not in ( <materialize> (/* select#2 */ select `test`.`t2`.`s1` from `test`.`t2` where true having true ), <primary_index_lookup>(`test`.`t1`.`s1` in <temporary table> on <auto_distinct_key> where ((`test`.`t1`.`s1` = `<materialized_subquery>`.`s1`))))) AS `s1 <> ALL (SELECT s1 FROM t2)` from `test`.`t1`
explain select s1, s1 NOT IN (SELECT s1 FROM t2 WHERE s1 < 'a2') from t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	index	NULL	s1	6	NULL	4	100.00	Using index
2	SUBQUERY	t2	NULL	range	s1	s1	6	NULL	4	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`s1` AS `s1`,<in_optimizer>(`test`.`t1`.`s1`,`test`.`t1`.`s1` not in ( <materialize> (/* select#2 */ select `test`.`t2`.`s1` from `test`.`t2` where (`test`.`t2`.`s1` < 'a2') having true ), <primary_index_lookup>(`test`.`t1`.`s1` in <temporary table> on <auto_distinct_key> where ((`test`.`t1`.`s1` = `<materialized_subquery>`.`s1`))))) AS `s1 NOT IN (SELECT s1 FROM t2 WHERE s1 < 'a2')` from `test`.`t1`
drop table t1,t2;
create table t2 (a int, b int);
create table t3 (a int);
insert into t3 values (6),(7),(3);
ANALYZE TABLE t2, t3;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
test.t3	analyze	status	OK
select * from t3 where a >= all (select b from t2);
a
6
7
3
explain select * from t3 where a >= all (select b from t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t3	NULL	ALL	NULL	NULL	NULL	NULL	3	66.67	Using where
2	SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where <not>((`test`.`t3`.`a` < <max>(/* select#2 */ select `test`.`t2`.`b` from `test`.`t2`)))
select * from t3 where a >= some (select b from t2);
a
explain select * from t3 where a >= some (select b from t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t3	NULL	ALL	NULL	NULL	NULL	NULL	3	66.67	Using where
2	SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where <nop>((`test`.`t3`.`a` >= (/* select#2 */ select min(`test`.`t2`.`b`) from `test`.`t2`)))
select * from t3 where a >= all (select b from t2 group by 1);
a
6
7
3
explain select * from t3 where a >= all (select b from t2 group by 1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t3	NULL	ALL	NULL	NULL	NULL	NULL	3	66.67	Using where
2	SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where <not>((`test`.`t3`.`a` < <max>(/* select#2 */ select `test`.`t2`.`b` from `test`.`t2`)))
select * from t3 where a >= some (select b from t2 group by 1);
a
explain select * from t3 where a >= some (select b from t2 group by 1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t3	NULL	ALL	NULL	NULL	NULL	NULL	3	66.67	Using where
2	SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where <nop>((`test`.`t3`.`a` >= (/* select#2 */ select min(`test`.`t2`.`b`) from `test`.`t2`)))
select * from t3 where NULL >= any (select b from t2);
a
explain select * from t3 where NULL >= any (select b from t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
2	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Not optimized, outer query is empty
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where false
select * from t3 where NULL >= any (select b from t2 group by 1);
a
explain select * from t3 where NULL >= any (select b from t2 group by 1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
2	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Not optimized, outer query is empty
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where false
select * from t3 where NULL >= some (select b from t2);
a
explain select * from t3 where NULL >= some (select b from t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
2	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Not optimized, outer query is empty
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where false
select * from t3 where NULL >= some (select b from t2 group by 1);
a
explain select * from t3 where NULL >= some (select b from t2 group by 1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
2	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Not optimized, outer query is empty
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where false
insert into t2 values (2,2), (2,1), (3,3), (3,1);
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
select * from t3 where a > all (select max(b) from t2 group by a);
a
6
7
explain select * from t3 where a > all (select max(b) from t2 group by a);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t3	NULL	ALL	NULL	NULL	NULL	NULL	3	66.67	Using where
2	SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a` from `test`.`t3` where <not>((`test`.`t3`.`a` <= <max>(/* select#2 */ select max(`test`.`t2`.`b`) from `test`.`t2` group by `test`.`t2`.`a`)))
drop table t2, t3;
CREATE TABLE `t1` ( `id` mediumint(9) NOT NULL auto_increment, `taskid` bigint(20) NOT NULL default '0', `dbid` int(11) NOT NULL default '0', `create_date` datetime NOT NULL default '0000-00-00 00:00:00', `last_update` datetime NOT NULL default '0000-00-00 00:00:00', PRIMARY KEY  (`id`)) ENGINE=INNODB CHARSET=latin1 AUTO_INCREMENT=3 ;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `t1` (`id`, `taskid`, `dbid`, `create_date`,`last_update`) VALUES (1, 1, 15, '2003-09-29 10:31:36', '2003-09-29 10:31:36'), (2, 1, 21, now(), now());
CREATE TABLE `t2` (`db_id` int(11) NOT NULL auto_increment,`name` varchar(200) NOT NULL default '',`primary_uid` smallint(6) NOT NULL default '0',`secondary_uid` smallint(6) NOT NULL default '0',PRIMARY KEY  (`db_id`),UNIQUE KEY `name_2` (`name`),FULLTEXT KEY `name` (`name`)) ENGINE=INNODB CHARSET=latin1 AUTO_INCREMENT=2147483647;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `t2` (`db_id`, `name`, `primary_uid`, `secondary_uid`) VALUES (18, 'Not Set 1', 0, 0),(19, 'Valid', 1, 2),(20, 'Valid 2', 1, 2),(21, 'Should Not Return', 1, 2),(26, 'Not Set 2', 0, 0),(-1, 'ALL DB\'S', 0, 0);
CREATE TABLE `t3` (`taskgenid` mediumint(9) NOT NULL auto_increment,`dbid` int(11) NOT NULL default '0',`taskid` int(11) NOT NULL default '0',`mon` tinyint(4) NOT NULL default '1',`tues` tinyint(4) NOT NULL default '1',`wed` tinyint(4) NOT NULL default '1',`thur` tinyint(4) NOT NULL default '1',`fri` tinyint(4) NOT NULL default '1',`sat` tinyint(4) NOT NULL default '0',`sun` tinyint(4) NOT NULL default '0',`how_often` smallint(6) NOT NULL default '1',`userid` smallint(6) NOT NULL default '0',`active` tinyint(4) NOT NULL default '1',PRIMARY KEY  (`taskgenid`)) ENGINE=INNODB CHARSET=latin1 AUTO_INCREMENT=2 ;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `t3` (`taskgenid`, `dbid`, `taskid`, `mon`, `tues`,`wed`, `thur`, `fri`, `sat`, `sun`, `how_often`, `userid`, `active`) VALUES (1,-1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 1);
CREATE TABLE `t4` (`task_id` smallint(6) NOT NULL default '0',`description` varchar(200) NOT NULL default '') ENGINE=INNODB CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `t4` (`task_id`, `description`) VALUES (1, 'Daily Check List'),(2, 'Weekly Status');
select  dbid, name, (date_format(now() , '%Y-%m-%d') - INTERVAL how_often DAY) >= ifnull((SELECT date_format(max(create_date),'%Y-%m-%d') FROM t1 WHERE dbid = b.db_id AND taskid = a.taskgenid), '1950-01-01') from t3 a, t2 b, t4  WHERE dbid = - 1 AND primary_uid = '1' AND t4.task_id = taskid;
dbid	name	(date_format(now() , '%Y-%m-%d') - INTERVAL how_often DAY) >= ifnull((SELECT date_format(max(create_date),'%Y-%m-%d') FROM t1 WHERE dbid = b.db_id AND taskid = a.taskgenid), '1950-01-01')
-1	Should Not Return	0
-1	Valid	1
-1	Valid 2	1
SELECT dbid, name FROM t3 a, t2 b, t4 WHERE dbid = - 1 AND primary_uid = '1' AND ((date_format(now() , '%Y-%m-%d') - INTERVAL how_often DAY) >= ifnull((SELECT date_format(max(create_date),'%Y-%m-%d') FROM t1 WHERE dbid = b.db_id AND taskid = a.taskgenid), '1950-01-01')) AND t4.task_id = taskid;
dbid	name
-1	Valid
-1	Valid 2
drop table t1,t2,t3,t4;
CREATE TABLE t1 (id int(11) default NULL) ENGINE=INNODB CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1),(5);
CREATE TABLE t2 (id int(11) default NULL) ENGINE=INNODB CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES (2),(6);
select * from t1 where (1,2,6) in (select * from t2);
ERROR 21000: Operand should contain 3 column(s)
DROP TABLE t1,t2;
create table t1 (s1 int);
insert into t1 values (1);
insert into t1 values (2);
set sort_buffer_size = (select s1 from t1);
ERROR 21000: Subquery returns more than 1 row
do (select * from t1);
ERROR 21000: Subquery returns more than 1 row
drop table t1;
create table t1 (s1 char);
insert into t1 values ('e');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
select * from t1 where 'f' > any (select s1 from t1);
s1
e
select * from t1 where 'f' > any (select s1 from t1 union select s1 from t1);
s1
e
explain select * from t1 where 'f' > any (select s1 from t1 union select s1 from t1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
2	SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
3	UNION	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
4	UNION RESULT	<union2,3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`s1` AS `s1` from `test`.`t1` where true
drop table t1;
CREATE TABLE t1 (number char(11) NOT NULL default '') ENGINE=INNODB CHARSET=latin1;
INSERT INTO t1 VALUES ('69294728265'),('18621828126'),('89356874041'),('95895001874');
CREATE TABLE t2 (code char(5) NOT NULL default '',UNIQUE KEY code (code)) ENGINE=INNODB CHARSET=latin1;
INSERT INTO t2 VALUES ('1'),('1226'),('1245'),('1862'),('18623'),('1874'),('1967'),('6');
select c.number as phone,(select p.code from t2 p where c.number like concat(p.code, '%') order by length(p.code) desc limit 1) as code from t1 c;
phone	code
69294728265	6
18621828126	1862
89356874041	NULL
95895001874	NULL
drop table t1, t2;
create table t1 (s1 int);
create table t2 (s1 int);
select * from t1 where (select count(*) from t2 where t1.s2) = 1;
ERROR 42S22: Unknown column 't1.s2' in 'where clause'
select * from t1 where (select count(*) from t2 group by t1.s2) = 1;
ERROR 42S22: Unknown column 't1.s2' in 'group statement'
select count(*) from t2 group by t1.s2;
ERROR 42S22: Unknown column 't1.s2' in 'group statement'
drop table t1, t2;
CREATE TABLE t1(COLA FLOAT NOT NULL,COLB FLOAT NOT NULL,COLC VARCHAR(20) DEFAULT NULL,PRIMARY KEY (COLA, COLB));
CREATE TABLE t2(COLA FLOAT NOT NULL,COLB FLOAT NOT NULL,COLC CHAR(1) NOT NULL,PRIMARY KEY (COLA));
INSERT INTO t1 VALUES (1,1,'1A3240'), (1,2,'4W2365');
INSERT INTO t2 VALUES (100, 200, 'C');
SELECT DISTINCT COLC FROM t1 WHERE COLA = (SELECT COLA FROM t2 WHERE COLB = 200 AND COLC ='C' LIMIT 1);
COLC
DROP TABLE t1, t2;
CREATE TABLE t1 (a int(1));
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1),(1),(1),(1),(1),(2),(3),(4),(5);
SELECT DISTINCT (SELECT a) FROM t1 LIMIT 100;
(SELECT a)
1
2
3
4
5
DROP TABLE t1;
create table t1 (a int, b decimal(13, 3));
insert into t1 values (1, 0.123);
select a, (select max(b) from t1) into outfile "../../tmp/subselect.out.file.1" from t1;
delete from t1;
load data infile "../../tmp/subselect.out.file.1" into table t1;
select * from t1;
a	b
1	0.123
drop table t1;
CREATE TABLE `t1` (
`id` int(11) NOT NULL auto_increment,
`id_cns` tinyint(3) unsigned NOT NULL default '0',
`tipo` enum('','UNO','DUE') NOT NULL default '',
`anno_dep` smallint(4) unsigned zerofill NOT NULL default '0000',
`particolare` mediumint(8) unsigned NOT NULL default '0',
`generale` mediumint(8) unsigned NOT NULL default '0',
`bis` tinyint(3) unsigned NOT NULL default '0',
PRIMARY KEY  (`id`),
UNIQUE KEY `idx_cns_gen_anno` (`anno_dep`,`id_cns`,`generale`,`particolare`),
UNIQUE KEY `idx_cns_par_anno` (`id_cns`,`anno_dep`,`tipo`,`particolare`,`bis`)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	The ZEROFILL attribute is deprecated and will be removed in a future release. Use the LPAD function to zero-pad numbers, or store the formatted numbers in a CHAR column.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `t1` VALUES (1,16,'UNO',1987,2048,9681,0),(2,50,'UNO',1987,1536,13987,0),(3,16,'UNO',1987,2432,14594,0),(4,16,'UNO',1987,1792,13422,0),(5,16,'UNO',1987,1025,10240,0),(6,16,'UNO',1987,1026,7089,0);
CREATE TABLE `t2` (
`id` tinyint(3) unsigned NOT NULL auto_increment,
`max_anno_dep` smallint(6) unsigned NOT NULL default '0',
PRIMARY KEY  (`id`)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `t2` VALUES (16,1987),(50,1990),(51,1990);
SELECT cns.id, cns.max_anno_dep, cns.max_anno_dep = (SELECT s.anno_dep FROM t1 AS s WHERE s.id_cns = cns.id ORDER BY s.anno_dep DESC LIMIT 1) AS PIPPO FROM t2 AS cns;
id	max_anno_dep	PIPPO
16	1987	1
50	1990	0
51	1990	NULL
DROP TABLE t1, t2;
create table t1 (a int);
insert into t1 values (1), (2), (3);
SET SQL_SELECT_LIMIT=1;
select sum(a) from (select * from t1) as a;
sum(a)
6
select 2 in (select * from t1);
2 in (select * from t1)
1
SET SQL_SELECT_LIMIT=default;
drop table t1;
CREATE TABLE t1 (a int, b int, INDEX (a));
INSERT INTO t1 VALUES (1, 1), (1, 2), (1, 3);
SELECT * FROM t1 WHERE a = (SELECT MAX(a) FROM t1 WHERE a = 1) ORDER BY b;
a	b
1	1
1	2
1	3
DROP TABLE t1;
create table t1(val varchar(10));
insert into t1 values ('aaa'), ('bbb'),('eee'),('mmm'),('ppp');
select count(*) from t1 as w1 where w1.val in (select w2.val from t1 as w2 where w2.val like 'm%') and w1.val in (select w3.val from t1 as w3 where w3.val like 'e%');
count(*)
0
drop table t1;
create table t1 (id int not null, text varchar(20) not null default '', primary key (id));
insert into t1 (id, text) values (1, 'text1'), (2, 'text2'), (3, 'text3'), (4, 'text4'), (5, 'text5'), (6, 'text6'), (7, 'text7'), (8, 'text8'), (9, 'text9'), (10, 'text10'), (11, 'text11'), (12, 'text12');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
select * from t1 where id not in (select id from t1 where id < 8);
id	text
8	text8
9	text9
10	text10
11	text11
12	text12
select * from t1 as tt where not exists (select id from t1 where id < 8 and (id = tt.id or id is null) having id is not null);
id	text
8	text8
9	text9
10	text10
11	text11
12	text12
explain select * from t1 where id not in (select id from t1 where id < 8);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	12	100.00	NULL
1	SIMPLE	t1	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.id	1	100.00	Using where; Not exists; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`text` AS `text` from `test`.`t1` anti join (`test`.`t1`) on(((`test`.`t1`.`id` = `test`.`t1`.`id`) and (`test`.`t1`.`id` < 8))) where true
explain select * from t1 as tt where not exists (select id from t1 where id < 8 and (id = tt.id or id is null) having id is not null);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	tt	NULL	ALL	NULL	NULL	NULL	NULL	12	100.00	Using where
2	DEPENDENT SUBQUERY	t1	NULL	eq_ref	PRIMARY	PRIMARY	4	test.tt.id	1	100.00	Using where; Using index
Warnings:
Note	1276	Field or reference 'test.tt.id' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`tt`.`id` AS `id`,`test`.`tt`.`text` AS `text` from `test`.`t1` `tt` where (not exists(/* select#2 */ select `test`.`t1`.`id` from `test`.`t1` where ((`test`.`t1`.`id` < 8) and (`test`.`t1`.`id` = `test`.`tt`.`id`)) having true))
insert into t1 (id, text) values (1000, 'text1000'), (1001, 'text1001');
create table t2 (id int not null, text varchar(20) not null default '', primary key (id));
insert into t2 (id, text) values (1, 'text1'), (2, 'text2'), (3, 'text3'), (4, 'text4'), (5, 'text5'), (6, 'text6'), (7, 'text7'), (8, 'text8'), (9, 'text9'), (10, 'text10'), (11, 'text1'), (12, 'text2'), (13, 'text3'), (14, 'text4'), (15, 'text5'), (16, 'text6'), (17, 'text7'), (18, 'text8'), (19, 'text9'), (20, 'text10'),(21, 'text1'), (22, 'text2'), (23, 'text3'), (24, 'text4'), (25, 'text5'), (26, 'text6'), (27, 'text7'), (28, 'text8'), (29, 'text9'), (30, 'text10'), (31, 'text1'), (32, 'text2'), (33, 'text3'), (34, 'text4'), (35, 'text5'), (36, 'text6'), (37, 'text7'), (38, 'text8'), (39, 'text9'), (40, 'text10'), (41, 'text1'), (42, 'text2'), (43, 'text3'), (44, 'text4'), (45, 'text5'), (46, 'text6'), (47, 'text7'), (48, 'text8'), (49, 'text9'), (50, 'text10');
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
select * from t1 a left join t2 b on (a.id=b.id or b.id is null) join t1 c on (if(isnull(b.id), 1000, b.id)=c.id);
id	text	id	text	id	text
1	text1	1	text1	1	text1
10	text10	10	text10	10	text10
1000	text1000	NULL	NULL	1000	text1000
1001	text1001	NULL	NULL	1000	text1000
11	text11	11	text1	11	text11
12	text12	12	text2	12	text12
2	text2	2	text2	2	text2
3	text3	3	text3	3	text3
4	text4	4	text4	4	text4
5	text5	5	text5	5	text5
6	text6	6	text6	6	text6
7	text7	7	text7	7	text7
8	text8	8	text8	8	text8
9	text9	9	text9	9	text9
explain select * from t1 a left join t2 b on (a.id=b.id or b.id is null) join t1 c on (if(isnull(b.id), 1000, b.id)=c.id);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	NULL	ALL	NULL	NULL	NULL	NULL	14	100.00	NULL
1	SIMPLE	b	NULL	eq_ref	PRIMARY	PRIMARY	4	test.a.id	1	100.00	NULL
1	SIMPLE	c	NULL	eq_ref	PRIMARY	PRIMARY	4	func	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`id` AS `id`,`test`.`a`.`text` AS `text`,`test`.`b`.`id` AS `id`,`test`.`b`.`text` AS `text`,`test`.`c`.`id` AS `id`,`test`.`c`.`text` AS `text` from `test`.`t1` `a` left join `test`.`t2` `b` on(((`test`.`b`.`id` = `test`.`a`.`id`) or (`test`.`b`.`id` is null))) join `test`.`t1` `c` where (if((`test`.`b`.`id` is null),1000,`test`.`b`.`id`) = `test`.`c`.`id`)
drop table t1,t2;
create table t1 (a int);
insert into t1 values (1);
explain select benchmark(1000, (select a from t1 where a=sha(rand())));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
2	UNCACHEABLE SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select benchmark(1000,(/* select#2 */ select `test`.`t1`.`a` from `test`.`t1` where (cast(`test`.`t1`.`a` as double) = cast(sha(rand()) as double)))) AS `benchmark(1000, (select a from t1 where a=sha(rand())))`
drop table t1;
create table t1(id int);
create table t2(id int);
create table t3(flag int);
select (select * from t3 where id not null) from t1, t2;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'null) from t1, t2' at line 1
drop table t1,t2,t3;
CREATE TABLE t1 (id INT);
CREATE TABLE t2 (id INT);
INSERT INTO t1 VALUES (1), (2);
INSERT INTO t2 VALUES (1);
SELECT t1.id, ( SELECT COUNT(t.id) FROM t2 AS t WHERE t.id = t1.id ) AS c FROM t1 LEFT JOIN t2 USING (id);
id	c
1	1
2	0
SELECT id, ( SELECT COUNT(t.id) FROM t2 AS t WHERE t.id = t1.id ) AS c FROM t1 LEFT JOIN t2 USING (id);
id	c
1	1
2	0
SELECT t1.id, ( SELECT COUNT(t.id) FROM t2 AS t WHERE t.id = t1.id ) AS c FROM t1 LEFT JOIN t2 USING (id) ORDER BY t1.id;
id	c
1	1
2	0
SELECT id, ( SELECT COUNT(t.id) FROM t2 AS t WHERE t.id = t1.id ) AS c FROM t1 LEFT JOIN t2 USING (id) ORDER BY id;
id	c
1	1
2	0
DROP TABLE t1,t2;
CREATE TABLE t1 ( a int, b int );
INSERT INTO t1 VALUES (1,1),(2,2),(3,3);
SELECT a FROM t1 WHERE a > ANY (SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE NOT a > ANY (SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE NOT NOT a > ANY (SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE a < ANY (SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE NOT a < ANY (SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE NOT NOT a < ANY (SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE a = ANY (SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE NOT a = ANY (SELECT a FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE NOT NOT a = ANY (SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE a >= ANY (SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE NOT a >= ANY (SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE NOT NOT a >= ANY (SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE a <= ANY (SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE NOT a <= ANY (SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE NOT NOT a <= ANY (SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE a <> ANY (SELECT a FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE NOT a <> ANY (SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE NOT NOT a <> ANY (SELECT a FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE a > ALL (SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE NOT a > ALL (SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE NOT NOT a > ALL (SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE a < ALL (SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE NOT a < ALL (SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE NOT NOT a < ALL (SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE a = ALL (SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE NOT a = ALL (SELECT a FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE NOT NOT a = ALL (SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE a >= ALL (SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE NOT a >= ALL (SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE NOT NOT a >= ALL (SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE a <= ALL (SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE NOT a <= ALL (SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE NOT NOT a <= ALL (SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE a <> ALL (SELECT a FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE NOT a <> ALL (SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE NOT NOT a <> ALL (SELECT a FROM t1 WHERE b = 2);
a
1
3
ALTER TABLE t1 ADD INDEX (a);
SELECT a FROM t1 WHERE a > ANY (SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE NOT a > ANY (SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE NOT NOT a > ANY (SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE a < ANY (SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE NOT a < ANY (SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE NOT NOT a < ANY (SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE a = ANY (SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE NOT a = ANY (SELECT a FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE NOT NOT a = ANY (SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE a >= ANY (SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE NOT a >= ANY (SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE NOT NOT a >= ANY (SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE a <= ANY (SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE NOT a <= ANY (SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE NOT NOT a <= ANY (SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE a <> ANY (SELECT a FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE NOT a <> ANY (SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE NOT NOT a <> ANY (SELECT a FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE a > ALL (SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE NOT a > ALL (SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE NOT NOT a > ALL (SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE a < ALL (SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE NOT a < ALL (SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE NOT NOT a < ALL (SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE a = ALL (SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE NOT a = ALL (SELECT a FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE NOT NOT a = ALL (SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE a >= ALL (SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE NOT a >= ALL (SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE NOT NOT a >= ALL (SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE a <= ALL (SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE NOT a <= ALL (SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE NOT NOT a <= ALL (SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE a <> ALL (SELECT a FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE NOT a <> ALL (SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE NOT NOT a <> ALL (SELECT a FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE a > ANY (SELECT a FROM t1 HAVING a = 2);
a
3
SELECT a FROM t1 WHERE a < ANY (SELECT a FROM t1 HAVING a = 2);
a
1
SELECT a FROM t1 WHERE a = ANY (SELECT a FROM t1 HAVING a = 2);
a
2
SELECT a FROM t1 WHERE a >= ANY (SELECT a FROM t1 HAVING a = 2);
a
2
3
SELECT a FROM t1 WHERE a <= ANY (SELECT a FROM t1 HAVING a = 2);
a
1
2
SELECT a FROM t1 WHERE a <> ANY (SELECT a FROM t1 HAVING a = 2);
a
1
3
SELECT a FROM t1 WHERE a > ALL (SELECT a FROM t1 HAVING a = 2);
a
3
SELECT a FROM t1 WHERE a < ALL (SELECT a FROM t1 HAVING a = 2);
a
1
SELECT a FROM t1 WHERE a = ALL (SELECT a FROM t1 HAVING a = 2);
a
2
SELECT a FROM t1 WHERE a >= ALL (SELECT a FROM t1 HAVING a = 2);
a
2
3
SELECT a FROM t1 WHERE a <= ALL (SELECT a FROM t1 HAVING a = 2);
a
1
2
SELECT a FROM t1 WHERE a <> ALL (SELECT a FROM t1 HAVING a = 2);
a
1
3
SELECT a FROM t1 WHERE a > ANY (SELECT a FROM t1 WHERE b = 2 UNION SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE a < ANY (SELECT a FROM t1 WHERE b = 2 UNION SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE a = ANY (SELECT a FROM t1 WHERE b = 2 UNION SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE a >= ANY (SELECT a FROM t1 WHERE b = 2 UNION SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE a <= ANY (SELECT a FROM t1 WHERE b = 2 UNION SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE a <> ANY (SELECT a FROM t1 WHERE b = 2 UNION SELECT a FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE a > ALL (SELECT a FROM t1 WHERE b = 2 UNION SELECT a FROM t1 WHERE b = 2);
a
3
SELECT a FROM t1 WHERE a < ALL (SELECT a FROM t1 WHERE b = 2 UNION SELECT a FROM t1 WHERE b = 2);
a
1
SELECT a FROM t1 WHERE a = ALL (SELECT a FROM t1 WHERE b = 2 UNION SELECT a FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE a >= ALL (SELECT a FROM t1 WHERE b = 2 UNION SELECT a FROM t1 WHERE b = 2);
a
2
3
SELECT a FROM t1 WHERE a <= ALL (SELECT a FROM t1 WHERE b = 2 UNION SELECT a FROM t1 WHERE b = 2);
a
1
2
SELECT a FROM t1 WHERE a <> ALL (SELECT a FROM t1 WHERE b = 2 UNION SELECT a FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE a > ANY (SELECT a FROM t1 HAVING a = 2 UNION SELECT a FROM t1 HAVING a = 2);
a
3
SELECT a FROM t1 WHERE a < ANY (SELECT a FROM t1 HAVING a = 2 UNION SELECT a FROM t1 HAVING a = 2);
a
1
SELECT a FROM t1 WHERE a = ANY (SELECT a FROM t1 HAVING a = 2 UNION SELECT a FROM t1 HAVING a = 2);
a
2
SELECT a FROM t1 WHERE a >= ANY (SELECT a FROM t1 HAVING a = 2 UNION SELECT a FROM t1 HAVING a = 2);
a
2
3
SELECT a FROM t1 WHERE a <= ANY (SELECT a FROM t1 HAVING a = 2 UNION SELECT a FROM t1 HAVING a = 2);
a
1
2
SELECT a FROM t1 WHERE a <> ANY (SELECT a FROM t1 HAVING a = 2 UNION SELECT a FROM t1 HAVING a = 2);
a
1
3
SELECT a FROM t1 WHERE a > ALL (SELECT a FROM t1 HAVING a = 2 UNION SELECT a FROM t1 HAVING a = 2);
a
3
SELECT a FROM t1 WHERE a < ALL (SELECT a FROM t1 HAVING a = 2 UNION SELECT a FROM t1 HAVING a = 2);
a
1
SELECT a FROM t1 WHERE a = ALL (SELECT a FROM t1 HAVING a = 2 UNION SELECT a FROM t1 HAVING a = 2);
a
2
SELECT a FROM t1 WHERE a >= ALL (SELECT a FROM t1 HAVING a = 2 UNION SELECT a FROM t1 HAVING a = 2);
a
2
3
SELECT a FROM t1 WHERE a <= ALL (SELECT a FROM t1 HAVING a = 2 UNION SELECT a FROM t1 HAVING a = 2);
a
1
2
SELECT a FROM t1 WHERE a <> ALL (SELECT a FROM t1 HAVING a = 2 UNION SELECT a FROM t1 HAVING a = 2);
a
1
3
SELECT a FROM t1 WHERE (1,2) > ANY (SELECT a FROM t1 WHERE b = 2);
ERROR 21000: Operand should contain 2 column(s)
SELECT a FROM t1 WHERE a > ANY (SELECT a,2 FROM t1 WHERE b = 2);
ERROR 21000: Operand should contain 1 column(s)
SELECT a FROM t1 WHERE (1,2) > ANY (SELECT a,2 FROM t1 WHERE b = 2);
ERROR 21000: Operand should contain 1 column(s)
SELECT a FROM t1 WHERE (1,2) > ALL (SELECT a FROM t1 WHERE b = 2);
ERROR 21000: Operand should contain 2 column(s)
SELECT a FROM t1 WHERE a > ALL (SELECT a,2 FROM t1 WHERE b = 2);
ERROR 21000: Operand should contain 1 column(s)
SELECT a FROM t1 WHERE (1,2) > ALL (SELECT a,2 FROM t1 WHERE b = 2);
ERROR 21000: Operand should contain 1 column(s)
SELECT a FROM t1 WHERE (1,2) = ALL (SELECT a,2 FROM t1 WHERE b = 2);
ERROR 21000: Operand should contain 1 column(s)
SELECT a FROM t1 WHERE (1,2) <> ANY (SELECT a,2 FROM t1 WHERE b = 2);
ERROR 21000: Operand should contain 1 column(s)
SELECT a FROM t1 WHERE (1,2) = ANY (SELECT a FROM t1 WHERE b = 2);
ERROR 21000: Operand should contain 2 column(s)
SELECT a FROM t1 WHERE a = ANY (SELECT a,2 FROM t1 WHERE b = 2);
ERROR 21000: Operand should contain 1 column(s)
SELECT a FROM t1 WHERE (1,2) = ANY (SELECT a,2 FROM t1 WHERE b = 2);
a
SELECT a FROM t1 WHERE (1,2) <> ALL (SELECT a FROM t1 WHERE b = 2);
ERROR 21000: Operand should contain 2 column(s)
SELECT a FROM t1 WHERE a <> ALL (SELECT a,2 FROM t1 WHERE b = 2);
ERROR 21000: Operand should contain 1 column(s)
SELECT a FROM t1 WHERE (1,2) <> ALL (SELECT a,2 FROM t1 WHERE b = 2);
a
1
2
3
SELECT a FROM t1 WHERE (a,1) = ANY (SELECT a,1 FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE (a,1) <> ALL (SELECT a,1 FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE (a,1) = ANY (SELECT a,1 FROM t1 HAVING a = 2);
a
2
SELECT a FROM t1 WHERE (a,1) <> ALL (SELECT a,1 FROM t1 HAVING a = 2);
a
1
3
SELECT a FROM t1 WHERE (a,1) = ANY (SELECT a,1 FROM t1 WHERE b = 2 UNION SELECT a,1 FROM t1 WHERE b = 2);
a
2
SELECT a FROM t1 WHERE (a,1) <> ALL (SELECT a,1 FROM t1 WHERE b = 2 UNION SELECT a,1 FROM t1 WHERE b = 2);
a
1
3
SELECT a FROM t1 WHERE (a,1) = ANY (SELECT a,1 FROM t1 HAVING a = 2 UNION SELECT a,1 FROM t1 HAVING a = 2);
a
2
SELECT a FROM t1 WHERE (a,1) <> ALL (SELECT a,1 FROM t1 HAVING a = 2 UNION SELECT a,1 FROM t1 HAVING a = 2);
a
1
3
SELECT a FROM t1 WHERE a > ANY (SELECT a FROM t1 WHERE b = 2 group by a);
a
3
SELECT a FROM t1 WHERE a < ANY (SELECT a FROM t1 WHERE b = 2 group by a);
a
1
SELECT a FROM t1 WHERE a = ANY (SELECT a FROM t1 WHERE b = 2 group by a);
a
2
SELECT a FROM t1 WHERE a >= ANY (SELECT a FROM t1 WHERE b = 2 group by a);
a
2
3
SELECT a FROM t1 WHERE a <= ANY (SELECT a FROM t1 WHERE b = 2 group by a);
a
1
2
SELECT a FROM t1 WHERE a <> ANY (SELECT a FROM t1 WHERE b = 2 group by a);
a
1
3
SELECT a FROM t1 WHERE a > ALL (SELECT a FROM t1 WHERE b = 2 group by a);
a
3
SELECT a FROM t1 WHERE a < ALL (SELECT a FROM t1 WHERE b = 2 group by a);
a
1
SELECT a FROM t1 WHERE a = ALL (SELECT a FROM t1 WHERE b = 2 group by a);
a
2
SELECT a FROM t1 WHERE a >= ALL (SELECT a FROM t1 WHERE b = 2 group by a);
a
2
3
SELECT a FROM t1 WHERE a <= ALL (SELECT a FROM t1 WHERE b = 2 group by a);
a
1
2
SELECT a FROM t1 WHERE a <> ALL (SELECT a FROM t1 WHERE b = 2 group by a);
a
1
3
SELECT a FROM t1 WHERE a > ANY (SELECT a FROM t1 group by a HAVING a = 2);
a
3
SELECT a FROM t1 WHERE a < ANY (SELECT a FROM t1 group by a HAVING a = 2);
a
1
SELECT a FROM t1 WHERE a = ANY (SELECT a FROM t1 group by a HAVING a = 2);
a
2
SELECT a FROM t1 WHERE a >= ANY (SELECT a FROM t1 group by a HAVING a = 2);
a
2
3
SELECT a FROM t1 WHERE a <= ANY (SELECT a FROM t1 group by a HAVING a = 2);
a
1
2
SELECT a FROM t1 WHERE a <> ANY (SELECT a FROM t1 group by a HAVING a = 2);
a
1
3
SELECT a FROM t1 WHERE a > ALL (SELECT a FROM t1 group by a HAVING a = 2);
a
3
SELECT a FROM t1 WHERE a < ALL (SELECT a FROM t1 group by a HAVING a = 2);
a
1
SELECT a FROM t1 WHERE a = ALL (SELECT a FROM t1 group by a HAVING a = 2);
a
2
SELECT a FROM t1 WHERE a >= ALL (SELECT a FROM t1 group by a HAVING a = 2);
a
2
3
SELECT a FROM t1 WHERE a <= ALL (SELECT a FROM t1 group by a HAVING a = 2);
a
1
2
SELECT a FROM t1 WHERE a <> ALL (SELECT a FROM t1 group by a HAVING a = 2);
a
1
3
SELECT concat(EXISTS(SELECT a FROM t1 WHERE b = 2 and a.a > t1.a), '-') from t1 a;
concat(EXISTS(SELECT a FROM t1 WHERE b = 2 and a.a > t1.a), '-')
0-
0-
1-
SELECT concat(EXISTS(SELECT a FROM t1 WHERE b = 2 and a.a < t1.a), '-') from t1 a;
concat(EXISTS(SELECT a FROM t1 WHERE b = 2 and a.a < t1.a), '-')
1-
0-
0-
SELECT concat(EXISTS(SELECT a FROM t1 WHERE b = 2 and a.a = t1.a), '-') from t1 a;
concat(EXISTS(SELECT a FROM t1 WHERE b = 2 and a.a = t1.a), '-')
0-
1-
0-
DROP TABLE t1;
CREATE TABLE t1 ( a double, b double );
INSERT INTO t1 VALUES (1,1),(2,2),(3,3);
SELECT a FROM t1 WHERE a > ANY (SELECT a FROM t1 WHERE b = 2e0);
a
3
SELECT a FROM t1 WHERE a < ANY (SELECT a FROM t1 WHERE b = 2e0);
a
1
SELECT a FROM t1 WHERE a = ANY (SELECT a FROM t1 WHERE b = 2e0);
a
2
SELECT a FROM t1 WHERE a >= ANY (SELECT a FROM t1 WHERE b = 2e0);
a
2
3
SELECT a FROM t1 WHERE a <= ANY (SELECT a FROM t1 WHERE b = 2e0);
a
1
2
SELECT a FROM t1 WHERE a <> ANY (SELECT a FROM t1 WHERE b = 2e0);
a
1
3
SELECT a FROM t1 WHERE a > ALL (SELECT a FROM t1 WHERE b = 2e0);
a
3
SELECT a FROM t1 WHERE a < ALL (SELECT a FROM t1 WHERE b = 2e0);
a
1
SELECT a FROM t1 WHERE a = ALL (SELECT a FROM t1 WHERE b = 2e0);
a
2
SELECT a FROM t1 WHERE a >= ALL (SELECT a FROM t1 WHERE b = 2e0);
a
2
3
SELECT a FROM t1 WHERE a <= ALL (SELECT a FROM t1 WHERE b = 2e0);
a
1
2
SELECT a FROM t1 WHERE a <> ALL (SELECT a FROM t1 WHERE b = 2e0);
a
1
3
DROP TABLE t1;
CREATE TABLE t1 ( a char(1), b char(1));
INSERT INTO t1 VALUES ('1','1'),('2','2'),('3','3');
SELECT a FROM t1 WHERE a > ANY (SELECT a FROM t1 WHERE b = '2');
a
3
SELECT a FROM t1 WHERE a < ANY (SELECT a FROM t1 WHERE b = '2');
a
1
SELECT a FROM t1 WHERE a = ANY (SELECT a FROM t1 WHERE b = '2');
a
2
SELECT a FROM t1 WHERE a >= ANY (SELECT a FROM t1 WHERE b = '2');
a
2
3
SELECT a FROM t1 WHERE a <= ANY (SELECT a FROM t1 WHERE b = '2');
a
1
2
SELECT a FROM t1 WHERE a <> ANY (SELECT a FROM t1 WHERE b = '2');
a
1
3
SELECT a FROM t1 WHERE a > ALL (SELECT a FROM t1 WHERE b = '2');
a
3
SELECT a FROM t1 WHERE a < ALL (SELECT a FROM t1 WHERE b = '2');
a
1
SELECT a FROM t1 WHERE a = ALL (SELECT a FROM t1 WHERE b = '2');
a
2
SELECT a FROM t1 WHERE a >= ALL (SELECT a FROM t1 WHERE b = '2');
a
2
3
SELECT a FROM t1 WHERE a <= ALL (SELECT a FROM t1 WHERE b = '2');
a
1
2
SELECT a FROM t1 WHERE a <> ALL (SELECT a FROM t1 WHERE b = '2');
a
1
3
DROP TABLE t1;
create table t1 (a int, b int);
insert into t1 values (1,2),(3,4);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
select * from t1 up where exists (select * from t1 where t1.a=up.a);
a	b
1	2
3	4
explain select * from t1 up where exists (select * from t1 where t1.a=up.a);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	up	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where; FirstMatch(up); Using join buffer (hash join)
Warnings:
Note	1276	Field or reference 'test.up.a' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`up`.`a` AS `a`,`test`.`up`.`b` AS `b` from `test`.`t1` `up` semi join (`test`.`t1`) where (`test`.`t1`.`a` = `test`.`up`.`a`)
drop table t1;
CREATE TABLE t1 (t1_a int);
INSERT INTO t1 VALUES (1);
CREATE TABLE t2 (t2_a int, t2_b int, PRIMARY KEY (t2_a, t2_b));
INSERT INTO t2 VALUES (1, 1), (1, 2);
SELECT * FROM t1, t2 table2 WHERE t1_a = 1 AND table2.t2_a = 1
HAVING table2.t2_b = (SELECT MAX(t2_b) FROM t2 WHERE t2_a = table2.t2_a);
t1_a	t2_a	t2_b
1	1	2
DROP TABLE t1, t2;
CREATE TABLE t1 (id int(11) default NULL,name varchar(10) default NULL);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1,'Tim'),(2,'Rebecca'),(3,NULL);
CREATE TABLE t2 (id int(11) default NULL, pet varchar(10) default NULL);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES (1,'Fido'),(2,'Spot'),(3,'Felix');
SELECT a.*, b.* FROM (SELECT * FROM t1) AS a JOIN t2 as b on a.id=b.id;
id	name	id	pet
1	Tim	1	Fido
2	Rebecca	2	Spot
3	NULL	3	Felix
drop table t1,t2;
CREATE TABLE t1 ( a int, b int );
CREATE TABLE t2 ( c int, d int );
INSERT INTO t1 VALUES (1,2), (2,3), (3,4);
SELECT a AS abc, b FROM t1 outr WHERE b =
(SELECT MIN(b) FROM t1 WHERE a=outr.a);
abc	b
1	2
2	3
3	4
INSERT INTO t2 SELECT a AS abc, b FROM t1 outr WHERE b =
(SELECT MIN(b) FROM t1 WHERE a=outr.a);
select * from t2;
c	d
1	2
2	3
3	4
CREATE TABLE t3 SELECT a AS abc, b FROM t1 outr WHERE b =
(SELECT MIN(b) FROM t1 WHERE a=outr.a);
select * from t3;
abc	b
1	2
2	3
3	4
prepare stmt1 from "INSERT INTO t2 SELECT a AS abc, b FROM t1 outr WHERE b = (SELECT MIN(b) FROM t1 WHERE a=outr.a);";
execute stmt1;
deallocate prepare stmt1;
select * from t2;
c	d
1	2
2	3
3	4
1	2
2	3
3	4
drop table t3;
prepare stmt1 from "CREATE TABLE t3 SELECT a AS abc, b FROM t1 outr WHERE b = (SELECT MIN(b) FROM t1 WHERE a=outr.a);";
execute stmt1;
select * from t3;
abc	b
1	2
2	3
3	4
deallocate prepare stmt1;
DROP TABLE t1, t2, t3;
CREATE TABLE `t1` ( `a` int(11) default NULL) ENGINE=INNODB DEFAULT CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t1 values (1);
CREATE TABLE `t2` ( `b` int(11) default NULL, `a` int(11) default NULL) ENGINE=INNODB DEFAULT CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t2 values (1,2);
select t000.a, count(*) `C` FROM t1 t000 GROUP BY t000.a HAVING count(*) > ALL (SELECT count(*) FROM t2 t001 WHERE t001.a=1);
a	C
1	1
drop table t1,t2;
create table t1 (a int not null auto_increment primary key, b varchar(40), fulltext(b));
insert into t1 (b) values ('ball'),('ball games'), ('games'), ('foo'), ('foobar'), ('Serg'), ('Sergei'),('Georg'), ('Patrik'),('Hakan');
create table t2 (a int);
insert into t2 values (1),(3),(2),(7);
select a,b from t1 where match(b) against ('Ball') > 0;
a	b
1	ball
2	ball games
select a from t2 where a in (select a from t1 where match(b) against ('Ball') > 0);
a
1
2
drop table t1,t2;
CREATE TABLE t1(`IZAVORGANG_ID` VARCHAR(11) CHARACTER SET latin1 COLLATE latin1_bin,`KUERZEL` VARCHAR(10) CHARACTER SET latin1 COLLATE latin1_bin,`IZAANALYSEART_ID` VARCHAR(11) CHARACTER SET latin1 COLLATE latin1_bin,`IZAPMKZ_ID` VARCHAR(11) CHARACTER SET latin1 COLLATE latin1_bin);
CREATE INDEX AK01IZAVORGANG ON t1(izaAnalyseart_id,Kuerzel);
INSERT INTO t1(`IZAVORGANG_ID`,`KUERZEL`,`IZAANALYSEART_ID`,`IZAPMKZ_ID`)VALUES('D0000000001','601','D0000000001','I0000000001');
INSERT INTO t1(`IZAVORGANG_ID`,`KUERZEL`,`IZAANALYSEART_ID`,`IZAPMKZ_ID`)VALUES('D0000000002','602','D0000000001','I0000000001');
INSERT INTO t1(`IZAVORGANG_ID`,`KUERZEL`,`IZAANALYSEART_ID`,`IZAPMKZ_ID`)VALUES('D0000000003','603','D0000000001','I0000000001');
INSERT INTO t1(`IZAVORGANG_ID`,`KUERZEL`,`IZAANALYSEART_ID`,`IZAPMKZ_ID`)VALUES('D0000000004','101','D0000000001','I0000000001');
SELECT `IZAVORGANG_ID` FROM t1 WHERE `KUERZEL` IN(SELECT MIN(`KUERZEL`)`Feld1` FROM t1 WHERE `KUERZEL` LIKE'601%'And`IZAANALYSEART_ID`='D0000000001');
IZAVORGANG_ID
D0000000001
drop table t1;
CREATE TABLE `t1` ( `aid` int(11) NOT NULL default '0', `bid` int(11) NOT NULL default '0', PRIMARY KEY  (`aid`,`bid`));
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE `t2` ( `aid` int(11) NOT NULL default '0', `bid` int(11) NOT NULL default '0', PRIMARY KEY  (`aid`,`bid`));
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t1 values (1,1),(1,2),(2,1),(2,2);
insert into t2 values (1,2),(2,2);
select * from t1 where t1.aid not in (select aid from t2 where bid=t1.bid);
aid	bid
1	1
2	1
alter table t2 drop primary key;
alter table t2 add key KEY1 (aid, bid);
select * from t1 where t1.aid not in (select aid from t2 where bid=t1.bid);
aid	bid
1	1
2	1
alter table t2 drop key KEY1;
alter table t2 add primary key (bid, aid);
select * from t1 where t1.aid not in (select aid from t2 where bid=t1.bid);
aid	bid
1	1
2	1
drop table t1,t2;
CREATE TABLE t1 (howmanyvalues bigint, avalue int);
INSERT INTO t1 VALUES (1, 1),(2, 1),(2, 2),(3, 1),(3, 2),(3, 3),(4, 1),(4, 2),(4, 3),(4, 4);
SELECT howmanyvalues, count(*) from t1 group by howmanyvalues;
howmanyvalues	count(*)
1	1
2	2
3	3
4	4
SELECT a.howmanyvalues, (SELECT count(*) from t1 b where b.howmanyvalues = a.howmanyvalues) as mycount from t1 a group by a.howmanyvalues;
howmanyvalues	mycount
1	1
2	2
3	3
4	4
CREATE INDEX t1_howmanyvalues_idx ON t1 (howmanyvalues);
SELECT a.howmanyvalues, (SELECT count(*) from t1 b where b.howmanyvalues+1 = a.howmanyvalues+1) as mycount from t1 a group by a.howmanyvalues;
howmanyvalues	mycount
1	1
2	2
3	3
4	4
SELECT a.howmanyvalues, (SELECT count(*) from t1 b where b.howmanyvalues = a.howmanyvalues) as mycount from t1 a group by a.howmanyvalues;
howmanyvalues	mycount
1	1
2	2
3	3
4	4
SELECT a.howmanyvalues, (SELECT count(*) from t1 b where b.howmanyvalues = a.avalue) as mycount from t1 a group by a.howmanyvalues;
howmanyvalues	mycount
1	1
2	1
3	1
4	1
drop table t1;
create table t1 (x int);
select  (select b.x from t1 as b where b.x=a.x) from t1 as a where a.x=2 group by a.x;
(select b.x from t1 as b where b.x=a.x)
drop table t1;
CREATE TABLE `t1` ( `master` int(10) unsigned NOT NULL default '0', `map` smallint(6) unsigned NOT NULL default '0', `slave` int(10) unsigned NOT NULL default '0', `access` int(10) unsigned NOT NULL default '0', UNIQUE KEY `access_u` (`master`,`map`,`slave`));
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `t1` VALUES (1,0,0,700),(1,1,1,400),(1,5,5,400),(1,12,12,400),(1,12,32,400),(4,12,32,400);
CREATE TABLE `t2` ( `id` int(10) unsigned NOT NULL default '0', `pid` int(10) unsigned NOT NULL default '0', `map` smallint(6) unsigned NOT NULL default '0', `level` tinyint(4) unsigned NOT NULL default '0', `title` varchar(255) default NULL, PRIMARY KEY  (`id`,`pid`,`map`), KEY `level` (`level`), KEY `id` (`id`,`map`)) ;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `t2` VALUES (6,5,12,7,'a'),(12,0,0,7,'a'),(12,1,0,7,'a'),(12,5,5,7,'a'),(12,5,12,7,'a');
SELECT b.sc FROM (SELECT (SELECT a.access FROM t1 a WHERE a.map = op.map AND a.slave = op.pid AND a.master = 1) ac FROM t2 op WHERE op.id = 12 AND op.map = 0) b;
ERROR 42S22: Unknown column 'b.sc' in 'field list'
SELECT b.ac FROM (SELECT (SELECT a.access FROM t1 a WHERE a.map = op.map AND a.slave = op.pid AND a.master = 1) ac FROM t2 op WHERE op.id = 12 AND op.map = 0) b;
ac
700
NULL
drop tables t1,t2;
create table t1 (a int not null, b int not null, c int, primary key (a,b));
insert into t1 values (1,1,1), (2,2,2), (3,3,3);
set @b:= 0;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select sum(a) from t1 where b > @b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	PRIMARY	PRIMARY	8	NULL	3	33.33	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select sum(`test`.`t1`.`a`) AS `sum(a)` from `test`.`t1` where (`test`.`t1`.`b` > <cache>((@`b`)))
set @a:= (select sum(a) from t1 where b > @b);
explain select a from t1 where c=2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`c` = 2)
do @a:= (select sum(a) from t1 where b > @b);
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
explain select a from t1 where c=2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`c` = 2)
drop table t1;
set @got_val= (SELECT 1 FROM (SELECT 'A' as my_col) as T1 ) ;
create table t1 (a int, b int);
create table t2 (a int, b int);
insert into t1 values (1,1),(1,2),(1,3),(2,4),(2,5);
insert into t2 values (1,3),(2,1);
select distinct a,b, (select max(b) from t2 where t1.b=t2.a) from t1 order by t1.b;
a	b	(select max(b) from t2 where t1.b=t2.a)
1	1	3
1	2	1
1	3	NULL
2	4	NULL
2	5	NULL
drop table t1, t2;
create table t1 (id int);
create table t2 (id int, body text, fulltext (body));
insert into t1 values(1),(2),(3);
insert into t2 values (1,'test'), (2,'mysql'), (3,'test'), (4,'test');
select count(distinct id) from t1 where id in (select id from t2 where match(body) against ('mysql' in boolean mode));
count(distinct id)
1
drop table t2,t1;
create table t1 (s1 int,s2 int);
insert into t1 values (20,15);
select * from t1 where  (('a',null) <=> (select 'a',s2 from t1 where s1 = 0));
s1	s2
drop table t1;
create table t1 (s1 int);
insert into t1 values (1),(null);
select * from t1 where s1 < all (select s1 from t1);
s1
select s1, s1 < all (select s1 from t1) from t1;
s1	s1 < all (select s1 from t1)
1	0
NULL	NULL
drop table t1;
CREATE TABLE t1 (
Code char(3) NOT NULL default '',
Name char(52) NOT NULL default '',
Continent enum('Asia','Europe','North America','Africa','Oceania','Antarctica','South America') NOT NULL default 'Asia',
Region char(26) NOT NULL default '',
SurfaceArea float(10,2) NOT NULL default '0.00',
IndepYear smallint(6) default NULL,
Population int(11) NOT NULL default '0',
LifeExpectancy float(3,1) default NULL,
GNP float(10,2) default NULL,
GNPOld float(10,2) default NULL,
LocalName char(45) NOT NULL default '',
GovernmentForm char(45) NOT NULL default '',
HeadOfState char(60) default NULL,
Capital int(11) default NULL,
Code2 char(2) NOT NULL default ''
) ENGINE=INNODB;
Warnings:
Warning	1681	Specifying number of digits for floating point data types is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Specifying number of digits for floating point data types is deprecated and will be removed in a future release.
Warning	1681	Specifying number of digits for floating point data types is deprecated and will be removed in a future release.
Warning	1681	Specifying number of digits for floating point data types is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES ('XXX','Xxxxx','Oceania','Xxxxxx',26.00,0,0,0,0,0,'Xxxxx','Xxxxx','Xxxxx',NULL,'XX');
INSERT INTO t1 VALUES ('ASM','American Samoa','Oceania','Polynesia',199.00,0,68000,75.1,334.00,NULL,'Amerika Samoa','US Territory','George W. Bush',54,'AS');
INSERT INTO t1 VALUES ('ATF','French Southern territories','Antarctica','Antarctica',7780.00,0,0,NULL,0.00,NULL,'Terres australes françaises','Nonmetropolitan Territory of France','Jacques Chirac',NULL,'TF');
INSERT INTO t1 VALUES ('UMI','United States Minor Outlying Islands','Oceania','Micronesia/Caribbean',16.00,0,0,NULL,0.00,NULL,'United States Minor Outlying Islands','Dependent Territory of the US','George W. Bush',NULL,'UM');
/*!40000 ALTER TABLE t1 ENABLE KEYS */;
Warnings:
Note	1031	Table storage engine for 't1' doesn't have this option
SELECT DISTINCT Continent AS c FROM t1 outr WHERE
Code <> SOME ( SELECT Code FROM t1 WHERE Continent = outr.Continent AND
Population < 200);
c
Oceania
drop table t1;
create table t1 (a1 int);
create table t2 (b1 int);
select * from t1 where a2 > any(select b1 from t2);
ERROR 42S22: Unknown column 'a2' in 'IN/ALL/ANY subquery'
select * from t1 where a1 > any(select b1 from t2);
a1
drop table t1,t2;
create table t1 (a integer, b integer);
select (select * from t1) = (select 1,2);
(select * from t1) = (select 1,2)
NULL
select (select 1,2) = (select * from t1);
(select 1,2) = (select * from t1)
NULL
select  row(1,2) = ANY (select * from t1);
row(1,2) = ANY (select * from t1)
0
select  row(1,2) != ALL (select * from t1);
row(1,2) != ALL (select * from t1)
1
drop table t1;
create table t1 (a integer, b integer);
select row(1,(2,2)) in (select * from t1 );
ERROR 21000: Operand should contain 2 column(s)
select row(1,(2,2)) = (select * from t1 );
ERROR 21000: Operand should contain 2 column(s)
select (select * from t1) = row(1,(2,2));
ERROR 21000: Operand should contain 1 column(s)
drop table t1;
create  table t1 (a integer);
insert into t1 values (1);
select 1 = ALL (select 1 from t1 where 1 = xx ), 1 as xx ;
ERROR 42S22: Reference 'xx' not supported (forward reference in item list)
select 1 = ALL (select 1 from t1 where 1 = xx ), 1 as xx;
ERROR 42S22: Reference 'xx' not supported (forward reference in item list)
select 1 as xx, 1 = ALL (  select 1 from t1 where 1 = xx );
xx	1 = ALL (  select 1 from t1 where 1 = xx )
1	1
select 1 = ALL (select 1 from t1 where 1 = xx ), 1 as xx;
ERROR 42S22: Reference 'xx' not supported (forward reference in item list)
select 1 = ALL (select 1 from t1 where 1 = xx ), 1 as xx from DUAL;
ERROR 42S22: Reference 'xx' not supported (forward reference in item list)
drop table t1;
CREATE TABLE t1 (
categoryId int(11) NOT NULL,
courseId int(11) NOT NULL,
startDate datetime NOT NULL,
endDate datetime NOT NULL,
createDate datetime NOT NULL,
modifyDate timestamp NOT NULL,
attributes text NOT NULL
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1,41,'2004-02-09','2010-01-01','2004-02-09','2004-02-09',''),
(1,86,'2004-08-16','2004-08-16','2004-08-16','2004-08-16',''),
(1,87,'2004-08-16','2004-08-16','2004-08-16','2004-08-16',''),
(2,52,'2004-03-15','2004-10-01','2004-03-15','2004-09-17',''),
(2,53,'2004-03-16','2004-10-01','2004-03-16','2004-09-17',''),
(2,88,'2004-08-16','2004-08-16','2004-08-16','2004-08-16',''),
(2,89,'2004-08-16','2004-08-16','2004-08-16','2004-08-16',''),
(3,51,'2004-02-09','2010-01-01','2004-02-09','2004-02-09',''),
(5,12,'2004-02-18','2010-01-01','2004-02-18','2004-02-18','');
CREATE TABLE t2 (
userId int(11) NOT NULL,
courseId int(11) NOT NULL,
date datetime NOT NULL
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES (5141,71,'2003-11-18'),
(5141,72,'2003-11-25'),(5141,41,'2004-08-06'),
(5141,52,'2004-08-06'),(5141,53,'2004-08-06'),
(5141,12,'2004-08-06'),(5141,86,'2004-10-21'),
(5141,87,'2004-10-21'),(5141,88,'2004-10-21'),
(5141,89,'2004-10-22'),(5141,51,'2004-10-26');
CREATE TABLE t3 (
groupId int(11) NOT NULL,
parentId int(11) NOT NULL,
startDate datetime NOT NULL,
endDate datetime NOT NULL,
createDate datetime NOT NULL,
modifyDate timestamp NOT NULL,
ordering int(11)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t3 VALUES (12,9,'1000-01-01','3999-12-31','2004-01-29','2004-01-29',NULL);
CREATE TABLE t4 (
id int(11) NOT NULL,
groupTypeId int(11) NOT NULL,
groupKey varchar(50) NOT NULL,
name text,
ordering int(11),
description text,
createDate datetime NOT NULL,
modifyDate timestamp NOT NULL
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t4 VALUES (9,5,'stationer','stationer',0,'Stationer','2004-01-29','2004-01-29'),
(12,5,'group2','group2',0,'group2','2004-01-29','2004-01-29');
CREATE TABLE t5 (
userId int(11) NOT NULL,
groupId int(11) NOT NULL,
createDate datetime NOT NULL,
modifyDate timestamp NOT NULL
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t5 VALUES (5141,12,'2004-08-06','2004-08-06');
select
count(distinct t2.userid) pass,
groupstuff.*,
count(t2.courseid) crse,
t1.categoryid,
t2.courseid,
date_format(date, '%b%y') as colhead
from t2
join t1 on t2.courseid=t1.courseid
join
(
select
t5.userid,
parentid,
parentgroup,
childid,
groupname,
grouptypeid
from t5
join
(
select t4.id as parentid,
t4.name as parentgroup,
t4.id as childid,
t4.name as groupname,
t4.grouptypeid
from t4
) as gin on t5.groupid=gin.childid
) as groupstuff on t2.userid = groupstuff.userid
group by
groupstuff.groupname, colhead , t2.courseid;
pass	userid	parentid	parentgroup	childid	groupname	grouptypeid	crse	categoryid	courseid	colhead
1	5141	12	group2	12	group2	5	1	5	12	Aug04
1	5141	12	group2	12	group2	5	1	1	41	Aug04
1	5141	12	group2	12	group2	5	1	2	52	Aug04
1	5141	12	group2	12	group2	5	1	2	53	Aug04
1	5141	12	group2	12	group2	5	1	3	51	Oct04
1	5141	12	group2	12	group2	5	1	1	86	Oct04
1	5141	12	group2	12	group2	5	1	1	87	Oct04
1	5141	12	group2	12	group2	5	1	2	88	Oct04
1	5141	12	group2	12	group2	5	1	2	89	Oct04
drop table t1, t2, t3, t4, t5;
create table t1 (a int);
insert into t1 values (1), (2), (3);
SELECT 1 FROM t1 WHERE (SELECT 1) in (SELECT 1);
1
1
1
1
drop table t1;
create table t1 (a int);
create table t2 (a int);
insert into t1 values (1),(2);
insert into t2 values (0),(1),(2),(3);
select a from t2 where a in (select a from t1);
a
1
2
select a from t2 having a in (select a from t1);
a
1
2
prepare stmt1 from "select a from t2 where a in (select a from t1)";
execute stmt1;
a
1
2
execute stmt1;
a
1
2
deallocate prepare stmt1;
prepare stmt1 from "select a from t2 having a in (select a from t1)";
execute stmt1;
a
1
2
execute stmt1;
a
1
2
deallocate prepare stmt1;
drop table t1, t2;
create table t1 (a int, b int);
insert into t1 values (1,2);
select 1 = (select * from t1);
ERROR 21000: Operand should contain 1 column(s)
select (select * from t1) = 1;
ERROR 21000: Operand should contain 2 column(s)
select (1,2) = (select a from t1);
ERROR 21000: Operand should contain 2 column(s)
select (select a from t1) = (1,2);
ERROR 21000: Operand should contain 1 column(s)
select (1,2,3) = (select * from t1);
ERROR 21000: Operand should contain 3 column(s)
select (select * from t1) = (1,2,3);
ERROR 21000: Operand should contain 2 column(s)
drop table t1;
CREATE TABLE `t1` (
`itemid` bigint(20) unsigned NOT NULL auto_increment,
`sessionid` bigint(20) unsigned default NULL,
`time` int(10) unsigned NOT NULL default '0',
`type` set('A','D','E','F','G','I','L','N','U') collate latin1_general_ci NOT
NULL default '',
`data` text collate latin1_general_ci NOT NULL,
PRIMARY KEY  (`itemid`)
) DEFAULT CHARSET=latin1 COLLATE=latin1_general_ci;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `t1` VALUES (1, 1, 1, 'D', '');
CREATE TABLE `t2` (
`sessionid` bigint(20) unsigned NOT NULL auto_increment,
`pid` int(10) unsigned NOT NULL default '0',
`date` int(10) unsigned NOT NULL default '0',
`ip` varchar(15) collate latin1_general_ci NOT NULL default '',
PRIMARY KEY  (`sessionid`)
) DEFAULT CHARSET=latin1 COLLATE=latin1_general_ci;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `t2` VALUES (1, 1, 1, '**********');
SELECT s.ip, count( e.itemid ) FROM `t1` e JOIN t2 s ON s.sessionid = e.sessionid WHERE e.sessionid = ( SELECT sessionid FROM t2 ORDER BY sessionid DESC LIMIT 1 ) GROUP BY s.ip HAVING count( e.itemid ) >0 LIMIT 0 , 30;
ip	count( e.itemid )
**********	1
drop tables t1,t2;
create table t1 (fld enum('0','1'));
insert into t1 values ('1');
select * from (select max(fld) from t1) as foo;
max(fld)
1
drop table t1;
CREATE TABLE t1 (a int);
CREATE TABLE t2 (a int, b int);
CREATE TABLE t3 (b int NOT NULL);
INSERT INTO t1 VALUES (1), (2), (3), (4);
INSERT INTO t2 VALUES (1,10), (3,30);
SELECT * FROM t2 LEFT JOIN t3 ON t2.b=t3.b
WHERE t3.b IS NOT NULL OR t2.a > 10;
a	b	b
SELECT * FROM t1
WHERE t1.a NOT IN (SELECT a FROM t2 LEFT JOIN t3 ON t2.b=t3.b
WHERE t3.b IS NOT NULL OR t2.a > 10);
a
1
2
3
4
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (f1 INT);
CREATE TABLE t2 (f2 INT);
INSERT INTO t1 VALUES (1);
SELECT * FROM t1 WHERE f1 > ALL (SELECT f2 FROM t2);
f1
1
SELECT * FROM t1 WHERE f1 > ALL (SELECT f2 FROM t2 WHERE 1=0);
f1
1
INSERT INTO t2 VALUES (1);
INSERT INTO t2 VALUES (2);
SELECT * FROM t1 WHERE f1 > ALL (SELECT f2 FROM t2 WHERE f2=0);
f1
1
DROP TABLE t1, t2;
select 1 from dual where 1 < any (select 2);
1
1
select 1 from dual where 1 < all (select 2);
1
1
select 1 from dual where 2 > any (select 1);
1
1
select 1 from dual where 2 > all (select 1);
1
1
select 1 from dual where 1 < any (select 2 from dual);
1
1
select 1 from dual where 1 < all (select 2 from dual where 1!=1);
1
1
create table t1 (s1 char);
insert into t1 values (1),(2);
select * from t1 where (s1 < any (select s1 from t1));
s1
1
select * from t1 where not (s1 < any (select s1 from t1));
s1
2
select * from t1 where (s1 < ALL (select s1+1 from t1));
s1
1
select * from t1 where not(s1 < ALL (select s1+1 from t1));
s1
2
select * from t1 where (s1+1 = ANY (select s1 from t1));
s1
1
select * from t1 where NOT(s1+1 = ANY (select s1 from t1));
s1
2
select * from t1 where (s1 = ALL (select s1/s1 from t1));
s1
1
select * from t1 where NOT(s1 = ALL (select s1/s1 from t1));
s1
2
drop table t1;
create table t1 (
retailerID varchar(8) NOT NULL,
statusID   int(10) unsigned NOT NULL,
changed    datetime NOT NULL,
UNIQUE KEY retailerID (retailerID, statusID, changed)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES("0026", "1", "2005-12-06 12:18:56");
INSERT INTO t1 VALUES("0026", "2", "2006-01-06 12:25:53");
INSERT INTO t1 VALUES("0037", "1", "2005-12-06 12:18:56");
INSERT INTO t1 VALUES("0037", "2", "2006-01-06 12:25:53");
INSERT INTO t1 VALUES("0048", "1", "2006-01-06 12:37:50");
INSERT INTO t1 VALUES("0059", "1", "2006-01-06 12:37:50");
select * from t1 r1
where (r1.retailerID,(r1.changed)) in
(SELECT r2.retailerId,(max(changed)) from t1 r2
group by r2.retailerId);
retailerID	statusID	changed
0026	2	2006-01-06 12:25:53
0037	2	2006-01-06 12:25:53
0048	1	2006-01-06 12:37:50
0059	1	2006-01-06 12:37:50
drop table t1;
create table t1(a int, primary key (a));
insert into t1 values (10);
create table t2 (a int primary key, b varchar(32), c int, unique key b(c, b)) charset utf8mb4;
insert into t2(a, c, b) values (1,10,'359'), (2,10,'35988'), (3,10,'35989');
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
explain SELECT t1.a, r.a, r.b FROM t1 LEFT JOIN t2 r
ON r.a = (SELECT t2.a FROM t2 WHERE t2.c = t1.a AND t2.b <= '359899'
             ORDER BY t2.c DESC, t2.b DESC LIMIT 1) WHERE t1.a = 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	Using index
1	PRIMARY	r	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	NULL
2	DEPENDENT SUBQUERY	t2	NULL	index	b	b	136	NULL	1	100.00	Using where; Backward index scan; Using index
Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select '10' AS `a`,'3' AS `a`,'35989' AS `b` from `test`.`t1` left join `test`.`t2` `r` on(('3' = (/* select#2 */ select `test`.`t2`.`a` from `test`.`t2` where ((`test`.`t2`.`c` = '10') and (`test`.`t2`.`b` <= '359899')) order by `test`.`t2`.`c` desc,`test`.`t2`.`b` desc limit 1))) where true
SELECT t1.a, r.a, r.b FROM t1 LEFT JOIN t2 r
ON r.a = (SELECT t2.a FROM t2 WHERE t2.c = t1.a AND t2.b <= '359899'
            ORDER BY t2.c DESC, t2.b DESC LIMIT 1) WHERE t1.a = 10;
a	a	b
10	3	35989
explain SELECT t1.a, r.a, r.b FROM t1 LEFT JOIN t2 r
ON r.a = (SELECT t2.a FROM t2 WHERE t2.c = t1.a AND t2.b <= '359899'
            ORDER BY t2.c, t2.b LIMIT 1) WHERE t1.a = 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	Using index
1	PRIMARY	r	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	NULL
2	DEPENDENT SUBQUERY	t2	NULL	index	b	b	136	NULL	1	100.00	Using where; Using index
Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select '10' AS `a`,'1' AS `a`,'359' AS `b` from `test`.`t1` left join `test`.`t2` `r` on(('1' = (/* select#2 */ select `test`.`t2`.`a` from `test`.`t2` where ((`test`.`t2`.`c` = '10') and (`test`.`t2`.`b` <= '359899')) order by `test`.`t2`.`c`,`test`.`t2`.`b` limit 1))) where true
SELECT t1.a, r.a, r.b FROM t1 LEFT JOIN t2 r
ON r.a = (SELECT t2.a FROM t2 WHERE t2.c = t1.a AND t2.b <= '359899'
            ORDER BY t2.c, t2.b LIMIT 1) WHERE t1.a = 10;
a	a	b
10	1	359
drop table t1,t2;
CREATE TABLE t1 (
field1 int NOT NULL,
field2 int NOT NULL,
field3 int NOT NULL,
PRIMARY KEY  (field1,field2,field3)
);
CREATE TABLE t2 (
fieldA int NOT NULL,
fieldB int NOT NULL,
PRIMARY KEY  (fieldA,fieldB)
);
INSERT INTO t1 VALUES
(1,1,1), (1,1,2), (1,2,1), (1,2,2), (1,2,3), (1,3,1);
INSERT INTO t2 VALUES (1,1), (1,2), (1,3);
SELECT field1, field2, COUNT(*)
FROM t1 GROUP BY field1, field2;
field1	field2	COUNT(*)
1	1	2
1	2	3
1	3	1
SELECT field1, field2
FROM  t1
GROUP BY field1, field2
HAVING COUNT(*) >= ALL (SELECT fieldB
FROM t2 WHERE fieldA = field1);
field1	field2
1	2
SELECT field1, field2
FROM  t1
GROUP BY field1, field2
HAVING COUNT(*) < ANY (SELECT fieldB
FROM t2 WHERE fieldA = field1);
field1	field2
1	1
1	3
DROP TABLE t1, t2;
CREATE TABLE t1(a int, INDEX (a));
INSERT INTO t1 VALUES (1), (3), (5), (7);
INSERT INTO t1 VALUES (NULL);
CREATE TABLE t2(a int);
INSERT INTO t2 VALUES (1),(2),(3);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN SELECT a, a IN (SELECT a FROM t1) FROM t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	SUBQUERY	t1	NULL	index	a	a	5	NULL	5	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,<in_optimizer>(`test`.`t2`.`a`,`test`.`t2`.`a` in ( <materialize> (/* select#2 */ select `test`.`t1`.`a` from `test`.`t1` where true having true ), <primary_index_lookup>(`test`.`t2`.`a` in <temporary table> on <auto_distinct_key> where ((`test`.`t2`.`a` = `<materialized_subquery>`.`a`))))) AS `a IN (SELECT a FROM t1)` from `test`.`t2`
SELECT a, a IN (SELECT a FROM t1) FROM t2;
a	a IN (SELECT a FROM t1)
1	1
2	NULL
3	1
DROP TABLE t1,t2;
CREATE TABLE t1 (a DATETIME);
INSERT INTO t1 VALUES ('1998-09-23'), ('2003-03-25');
CREATE TABLE t2 charset utf8mb4 AS SELECT
(SELECT a FROM t1 WHERE a < '2000-01-01') AS sub_a
FROM t1 WHERE a > '2000-01-01';
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `sub_a` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
CREATE TABLE t3 charset utf8mb4 AS (SELECT a FROM t1 WHERE a < '2000-01-01') UNION (SELECT a FROM t1 WHERE a > '2000-01-01');
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `a` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (a int);
INSERT INTO t1 VALUES (1), (2);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT a FROM t1 WHERE (SELECT 1 FROM DUAL WHERE 1=0) > 0;
a
SELECT a FROM t1 WHERE (SELECT 1 FROM DUAL WHERE 1=0) IS NULL;
a
1
2
EXPLAIN SELECT a FROM t1 WHERE (SELECT 1 FROM DUAL WHERE 1=0) IS NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
2	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where true
DROP TABLE t1;
CREATE TABLE t1 (a int);
INSERT INTO t1 VALUES (2), (4), (1), (3);
CREATE TABLE t2 (b int, c int);
INSERT INTO t2 VALUES
(2,1), (1,3), (2,1), (4,4), (2,2), (1,4);
SELECT a FROM t1 ORDER BY (SELECT c FROM t2 WHERE b > 2 );
a
2
4
1
3
SELECT a FROM t1 ORDER BY (SELECT c FROM t2 WHERE b > 1);
ERROR 21000: Subquery returns more than 1 row
SELECT a FROM t1 ORDER BY (SELECT c FROM t2 WHERE b > 2), a;
a
1
2
3
4
SELECT a FROM t1 ORDER BY (SELECT c FROM t2 WHERE b > 1), a;
ERROR 21000: Subquery returns more than 1 row
SELECT b, MAX(c) FROM t2 GROUP BY b, (SELECT c FROM t2 WHERE b > 2);
b	MAX(c)
1	4
2	2
4	4
SELECT b, MAX(c) FROM t2 GROUP BY b, (SELECT c FROM t2 WHERE b > 1);
ERROR 21000: Subquery returns more than 1 row
SELECT a FROM t1 GROUP BY a
HAVING IFNULL((SELECT b FROM t2 WHERE b > 2),
(SELECT c FROM t2 WHERE c=a AND b > 2 ORDER BY b)) > 3;
a
1
2
3
4
SELECT a FROM t1 GROUP BY a
HAVING IFNULL((SELECT b FROM t2 WHERE b > 1),
(SELECT c FROM t2 WHERE c=a AND b > 2 ORDER BY b)) > 3;
ERROR 21000: Subquery returns more than 1 row
SELECT a FROM t1 GROUP BY a
HAVING IFNULL((SELECT b FROM t2 WHERE b > 4),
(SELECT c FROM t2 WHERE c=a AND b > 2 ORDER BY b)) > 3;
a
4
SELECT a FROM t1 GROUP BY a
HAVING IFNULL((SELECT b FROM t2 WHERE b > 4),
(SELECT c FROM t2 WHERE c=a AND b > 1 ORDER BY b)) > 3;
ERROR 21000: Subquery returns more than 1 row
SELECT a FROM t1
ORDER BY IFNULL((SELECT b FROM t2 WHERE b > 2),
(SELECT c FROM t2 WHERE c=a AND b > 2 ORDER BY b));
a
1
2
3
4
SELECT a FROM t1
ORDER BY IFNULL((SELECT b FROM t2 WHERE b > 1),
(SELECT c FROM t2 WHERE c=a AND b > 1 ORDER BY b));
ERROR 21000: Subquery returns more than 1 row
SELECT a FROM t1
ORDER BY IFNULL((SELECT b FROM t2 WHERE b > 4),
(SELECT c FROM t2 WHERE c=a AND b > 2 ORDER BY b));
a
1
2
3
4
SELECT a FROM t1
ORDER BY IFNULL((SELECT b FROM t2 WHERE b > 4),
(SELECT c FROM t2 WHERE c=a AND b > 1 ORDER BY b));
ERROR 21000: Subquery returns more than 1 row
DROP TABLE t1,t2;
create table t1 (df decimal(5,1));
insert into t1 values(1.1);
insert into t1 values(2.2);
select * from t1 where df <= all (select avg(df) from t1 group by df);
df
1.1
select * from t1 where df >= all (select avg(df) from t1 group by df);
df
2.2
drop table t1;
create table t1 (df decimal(5,1));
insert into t1 values(1.1);
select 1.1 * exists(select * from t1);
1.1 * exists(select * from t1)
1.1
drop table t1;
CREATE TABLE t1 (
grp int(11) default NULL,
a decimal(10,2) default NULL);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t1 values (1, 1), (2, 2), (2, 3), (3, 4), (3, 5), (3, 6), (NULL, NULL);
select * from t1;
grp	a
1	1.00
2	2.00
2	3.00
3	4.00
3	5.00
3	6.00
NULL	NULL
select min(a) from t1 group by grp;
min(a)
1.00
2.00
4.00
NULL
drop table t1;
CREATE table t1 ( c1 integer );
INSERT INTO t1 VALUES ( 1 );
INSERT INTO t1 VALUES ( 2 );
INSERT INTO t1 VALUES ( 3 );
CREATE TABLE t2 ( c2 integer );
INSERT INTO t2 VALUES ( 1 );
INSERT INTO t2 VALUES ( 4 );
INSERT INTO t2 VALUES ( 5 );
SELECT * FROM t1 LEFT JOIN t2 ON c1 = c2 WHERE c2 IN (1);
c1	c2
1	1
SELECT * FROM t1 LEFT JOIN t2 ON c1 = c2
WHERE c2 IN ( SELECT c2 FROM t2 WHERE c2 IN ( 1 ) );
c1	c2
1	1
DROP TABLE t1,t2;
CREATE TABLE t1 ( c1 integer );
INSERT INTO t1 VALUES ( 1 );
INSERT INTO t1 VALUES ( 2 );
INSERT INTO t1 VALUES ( 3 );
INSERT INTO t1 VALUES ( 6 );
CREATE TABLE t2 ( c2 integer );
INSERT INTO t2 VALUES ( 1 );
INSERT INTO t2 VALUES ( 4 );
INSERT INTO t2 VALUES ( 5 );
INSERT INTO t2 VALUES ( 6 );
CREATE TABLE t3 ( c3 integer );
INSERT INTO t3 VALUES ( 7 );
INSERT INTO t3 VALUES ( 8 );
SELECT c1,c2 FROM t1 LEFT JOIN t2 ON c1 = c2
WHERE EXISTS (SELECT c3 FROM t3 WHERE c2 IS NULL );
c1	c2
2	NULL
3	NULL
DROP TABLE t1,t2,t3;
CREATE TABLE `t1` (
`itemid` bigint(20) unsigned NOT NULL auto_increment,
`sessionid` bigint(20) unsigned default NULL,
`time` int(10) unsigned NOT NULL default '0',
`type` set('A','D','E','F','G','I','L','N','U') collate latin1_general_ci NOT
NULL default '',
`data` text collate latin1_general_ci NOT NULL,
PRIMARY KEY  (`itemid`)
) DEFAULT CHARSET=latin1 COLLATE=latin1_general_ci;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `t1` VALUES (1, 1, 1, 'D', '');
CREATE TABLE `t2` (
`sessionid` bigint(20) unsigned NOT NULL auto_increment,
`pid` int(10) unsigned NOT NULL default '0',
`date` int(10) unsigned NOT NULL default '0',
`ip` varchar(15) collate latin1_general_ci NOT NULL default '',
PRIMARY KEY  (`sessionid`)
) DEFAULT CHARSET=latin1 COLLATE=latin1_general_ci;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `t2` VALUES (1, 1, 1, '**********');
SELECT s.ip, count( e.itemid ) FROM `t1` e JOIN t2 s ON s.sessionid = e.sessionid WHERE e.sessionid = ( SELECT sessionid FROM t2 ORDER BY sessionid DESC LIMIT 1 ) GROUP BY s.ip HAVING count( e.itemid ) >0 LIMIT 0 , 30;
ip	count( e.itemid )
**********	1
drop tables t1,t2;
CREATE TABLE t1 (EMPNUM   CHAR(3));
CREATE TABLE t2 (EMPNUM   CHAR(3) );
INSERT INTO t1 VALUES ('E1'),('E2');
INSERT INTO t2 VALUES ('E1');
DELETE FROM t1
WHERE t1.EMPNUM NOT IN
(SELECT t2.EMPNUM
FROM t2
WHERE t1.EMPNUM = t2.EMPNUM);
select * from t1;
EMPNUM
E1
DROP TABLE t1,t2;
CREATE TABLE t1(select_id BIGINT, values_id BIGINT);
INSERT INTO t1 VALUES (1, 1);
CREATE TABLE t2 (select_id BIGINT, values_id BIGINT,
PRIMARY KEY(select_id,values_id));
INSERT INTO t2 VALUES (0, 1), (0, 2), (0, 3), (1, 5);
SELECT values_id FROM t1
WHERE values_id IN (SELECT values_id FROM t2
WHERE select_id IN (1, 0));
values_id
1
SELECT values_id FROM t1
WHERE values_id IN (SELECT values_id FROM t2
WHERE select_id BETWEEN 0 AND 1);
values_id
1
SELECT values_id FROM t1
WHERE values_id IN (SELECT values_id FROM t2
WHERE select_id = 0 OR select_id = 1);
values_id
1
DROP TABLE t1, t2;
create table t1 (fld enum('0','1'));
insert into t1 values ('1');
select * from (select max(fld) from t1) as foo;
max(fld)
1
drop table t1;
CREATE TABLE t1 (a int, b int);
CREATE TABLE t2 (c int, d int);
CREATE TABLE t3 (e int);
INSERT INTO t1 VALUES
(1,10), (2,10), (1,20), (2,20), (3,20), (2,30), (4,40);
INSERT INTO t2 VALUES
(2,10), (2,20), (4,10), (5,10), (3,20), (2,40);
INSERT INTO t3 VALUES (10), (30), (10), (20) ;
SELECT a, MAX(b), MIN(b) FROM t1 GROUP BY a;
a	MAX(b)	MIN(b)
1	20	10
2	30	10
3	20	20
4	40	40
SELECT * FROM t2;
c	d
2	10
2	20
4	10
5	10
3	20
2	40
SELECT * FROM t3;
e
10
30
10
20
SELECT a FROM t1 GROUP BY a
HAVING a IN (SELECT c FROM t2 WHERE MAX(b)>20);
a
2
4
SELECT a FROM t1 GROUP BY a
HAVING a IN (SELECT c FROM t2 WHERE MAX(b)<d);
a
2
SELECT a FROM t1 GROUP BY a
HAVING a IN (SELECT c FROM t2 WHERE MAX(b)>d);
a
2
4
SELECT a FROM t1 GROUP BY a
HAVING a IN (SELECT c FROM t2
WHERE d >= SOME(SELECT e FROM t3 WHERE MAX(b)=e));
a
2
3
SELECT a FROM t1 GROUP BY a
HAVING a IN (SELECT c FROM t2
WHERE  EXISTS(SELECT e FROM t3 WHERE MAX(b)=e AND e <= d));
a
2
3
SELECT a FROM t1 GROUP BY a
HAVING a IN (SELECT c FROM t2
WHERE d > SOME(SELECT e FROM t3 WHERE MAX(b)=e));
a
2
SELECT a FROM t1 GROUP BY a
HAVING a IN (SELECT c FROM t2
WHERE  EXISTS(SELECT e FROM t3 WHERE MAX(b)=e AND e < d));
a
2
SELECT a FROM t1 GROUP BY a
HAVING a IN (SELECT c FROM t2
WHERE MIN(b) < d AND
EXISTS(SELECT e FROM t3 WHERE MAX(b)=e AND e <= d));
a
2
SELECT a, SUM(a) FROM t1 GROUP BY a;
a	SUM(a)
1	2
2	6
3	3
4	4
SELECT a FROM t1
WHERE EXISTS(SELECT c FROM t2 GROUP BY c HAVING SUM(a) = c) GROUP BY a;
a
3
4
SELECT a FROM t1 GROUP BY a
HAVING EXISTS(SELECT c FROM t2 GROUP BY c HAVING SUM(a) = c);
a
1
3
4
SELECT a FROM t1
WHERE a < 3 AND
EXISTS(SELECT c FROM t2 GROUP BY c HAVING SUM(a) != c) GROUP BY a;
a
1
2
SELECT a FROM t1
WHERE a < 3 AND
EXISTS(SELECT c FROM t2 GROUP BY c HAVING SUM(a) != c);
a
1
2
1
2
2
SELECT t1.a FROM t1 GROUP BY t1.a
HAVING t1.a < ALL(SELECT t2.c FROM t2 GROUP BY t2.c
HAVING EXISTS(SELECT t3.e FROM t3 GROUP BY t3.e
HAVING SUM(t1.a+t2.c) < t3.e/4));
a
1
2
SELECT t1.a FROM t1 GROUP BY t1.a
HAVING t1.a > ALL(SELECT t2.c FROM t2
WHERE EXISTS(SELECT t3.e FROM t3 GROUP BY t3.e
HAVING SUM(t1.a+t2.c) < t3.e/4));
a
4
SELECT t1.a FROM t1 GROUP BY t1.a
HAVING t1.a > ALL(SELECT t2.c FROM t2
WHERE EXISTS(SELECT t3.e FROM t3
WHERE SUM(t1.a+t2.c) < t3.e/4));
ERROR HY000: Invalid use of group function
SELECT t1.a from t1 GROUP BY t1.a HAVING AVG(SUM(t1.b)) > 20;
ERROR HY000: Invalid use of group function
SELECT t1.a FROM t1 GROUP BY t1.a
HAVING t1.a IN (SELECT t2.c FROM t2 GROUP BY t2.c
HAVING AVG(t2.c+SUM(t1.b)) > 20);
a
2
3
4
SELECT t1.a FROM t1 GROUP BY t1.a
HAVING t1.a IN (SELECT t2.c FROM t2 GROUP BY t2.c
HAVING AVG(SUM(t1.b)) > 20);
a
2
4
SELECT t1.a, SUM(b) AS sum  FROM t1 GROUP BY t1.a
HAVING t1.a IN (SELECT t2.c FROM t2 GROUP BY t2.c
HAVING t2.c+sum > 20);
a	sum
2	60
3	20
4	40
DROP TABLE t1,t2,t3;
create table t1( f1 int,f2 int);
insert into t1 values (1,1),(2,2);
select tt.t from (select 'crash1' as t, f2 from t1) as tt left join t1 on tt.t = 'crash2' and tt.f2 = t1.f2 where tt.t = 'crash1';
t
crash1
crash1
drop table t1;
create table t1 (c int, key(c));
insert into t1 values (1142477582), (1142455969);
create table t2 (a int, b int);
insert into t2 values (2, 1), (1, 0);
delete from t1 where c <= 1140006215 and (select b from t2 where a = 2) = 1;
drop table t1, t2;
CREATE TABLE t1 (a INT);
CREATE VIEW v1 AS SELECT * FROM t1 WHERE no_such_column = ANY (SELECT 1);
ERROR 42S22: Unknown column 'no_such_column' in 'IN/ALL/ANY subquery'
CREATE VIEW v2 AS SELECT * FROM t1 WHERE no_such_column = (SELECT 1);
ERROR 42S22: Unknown column 'no_such_column' in 'where clause'
SELECT * FROM t1 WHERE no_such_column = ANY (SELECT 1);
ERROR 42S22: Unknown column 'no_such_column' in 'IN/ALL/ANY subquery'
DROP TABLE t1;
create table t1 (i int, j bigint);
insert into t1 values (1, 2), (2, 2), (3, 2);
select * from (select min(i) from t1 where j=(select * from (select min(j) from t1) t2)) t3;
min(i)
1
drop table t1;
CREATE TABLE t1 (i BIGINT UNSIGNED);
INSERT INTO t1 VALUES (10000000000000000000);
INSERT INTO t1 VALUES (1);
CREATE TABLE t2 (i BIGINT UNSIGNED);
INSERT INTO t2 VALUES (10000000000000000000);
INSERT INTO t2 VALUES (1);
/* simple test */
SELECT t1.i FROM t1 JOIN t2 ON t1.i = t2.i;
i
10000000000000000000
1
/* subquery test */
SELECT t1.i FROM t1 WHERE t1.i = (SELECT MAX(i) FROM t2);
i
10000000000000000000
/* subquery test with cast*/
SELECT t1.i FROM t1 WHERE t1.i = CAST((SELECT MAX(i) FROM t2) AS UNSIGNED);
i
10000000000000000000
DROP TABLE t1;
DROP TABLE t2;
CREATE TABLE t1 (
id bigint(20) unsigned NOT NULL auto_increment,
name varchar(255) NOT NULL,
PRIMARY KEY  (id)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES
(1, 'Balazs'), (2, 'Joe'), (3, 'Frank');
CREATE TABLE t2 (
id bigint(20) unsigned NOT NULL auto_increment,
mid bigint(20) unsigned NOT NULL,
date date NOT NULL,
PRIMARY KEY  (id)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES
(1, 1, '2006-03-30'), (2, 2, '2006-04-06'), (3, 3, '2006-04-13'),
(4, 2, '2006-04-20'), (5, 1, '2006-05-01');
SELECT *,
(SELECT date FROM t2 WHERE mid = t1.id
ORDER BY date DESC LIMIT 0, 1) AS date_last,
(SELECT date FROM t2 WHERE mid = t1.id
ORDER BY date DESC LIMIT 3, 1) AS date_next_to_last
FROM t1;
id	name	date_last	date_next_to_last
1	Balazs	2006-05-01	NULL
2	Joe	2006-04-20	NULL
3	Frank	2006-04-13	NULL
SELECT *,
(SELECT COUNT(*) FROM t2 WHERE mid = t1.id
ORDER BY date DESC LIMIT 1, 1) AS date_count
FROM t1;
id	name	date_count
1	Balazs	NULL
2	Joe	NULL
3	Frank	NULL
SELECT *,
(SELECT date FROM t2 WHERE mid = t1.id
ORDER BY date DESC LIMIT 0, 1) AS date_last,
(SELECT date FROM t2 WHERE mid = t1.id
ORDER BY date DESC LIMIT 1, 1) AS date_next_to_last
FROM t1;
id	name	date_last	date_next_to_last
1	Balazs	2006-05-01	2006-03-30
2	Joe	2006-04-20	2006-04-06
3	Frank	2006-04-13	NULL
DROP TABLE t1,t2;
CREATE TABLE t1 (
i1 int(11) NOT NULL default '0',
i2 int(11) NOT NULL default '0',
t datetime NOT NULL default '0000-00-00 00:00:00',
PRIMARY KEY  (i1,i2,t)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES
(24,1,'2005-03-03 16:31:31'),(24,1,'2005-05-27 12:40:07'),
(24,1,'2005-05-27 12:40:08'),(24,1,'2005-05-27 12:40:10'),
(24,1,'2005-05-27 12:40:25'),(24,1,'2005-05-27 12:40:30'),
(24,2,'2005-03-03 13:43:05'),(24,2,'2005-03-03 16:23:31'),
(24,2,'2005-03-03 16:31:30'),(24,2,'2005-05-27 12:37:02'),
(24,2,'2005-05-27 12:40:06');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
CREATE TABLE t2 (
i1 int(11) NOT NULL default '0',
i2 int(11) NOT NULL default '0',
t datetime default NULL,
PRIMARY KEY  (i1)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES (24,1,'2006-06-20 12:29:40');
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
EXPLAIN
SELECT * FROM t1,t2
WHERE t1.t = (SELECT t1.t FROM t1
WHERE t1.t < t2.t  AND t1.i2=1 AND t2.i1=t1.i1
ORDER BY t1.t DESC LIMIT 1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
1	PRIMARY	t1	NULL	index	NULL	PRIMARY	13	NULL	11	100.00	Using where; Using index; Using join buffer (hash join)
2	DEPENDENT SUBQUERY	t1	NULL	ref	PRIMARY	PRIMARY	8	test.t2.i1,const	5	33.33	Using where; Using index; Using filesort
Warnings:
Note	1276	Field or reference 'test.t2.t' of SELECT #2 was resolved in SELECT #1
Note	1276	Field or reference 'test.t2.i1' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`i1` AS `i1`,`test`.`t1`.`i2` AS `i2`,`test`.`t1`.`t` AS `t`,`test`.`t2`.`i1` AS `i1`,`test`.`t2`.`i2` AS `i2`,`test`.`t2`.`t` AS `t` from `test`.`t1` join `test`.`t2` where (`test`.`t1`.`t` = (/* select#2 */ select `test`.`t1`.`t` from `test`.`t1` where ((`test`.`t1`.`i2` = 1) and (`test`.`t1`.`t` < `test`.`t2`.`t`) and (`test`.`t2`.`i1` = `test`.`t1`.`i1`)) order by `test`.`t1`.`t` desc limit 1))
SELECT * FROM t1,t2
WHERE t1.t = (SELECT t1.t FROM t1
WHERE t1.t < t2.t  AND t1.i2=1 AND t2.i1=t1.i1
ORDER BY t1.t DESC LIMIT 1);
i1	i2	t	i1	i2	t
24	1	2005-05-27 12:40:30	24	1	2006-06-20 12:29:40
DROP TABLE t1, t2;
CREATE TABLE t1 (i INT);
(SELECT i FROM t1) UNION (SELECT i FROM t1);
i
SELECT * FROM t1 WHERE NOT EXISTS
(
(SELECT i FROM t1) UNION
(SELECT i FROM t1)
);
i
SELECT * FROM t1
WHERE NOT EXISTS (((SELECT i FROM t1) UNION (SELECT i FROM t1)));
i
explain select ((select t11.i from t1 t11) union (select t12.i from t1 t12))
from t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
2	SUBQUERY	t11	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
3	UNION	t12	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
4	UNION RESULT	<union2,3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select (/* select#2 */ select `test`.`t11`.`i` from `test`.`t1` `t11` union /* select#3 */ select `test`.`t12`.`i` from `test`.`t1` `t12`) AS `((select t11.i from t1 t11) union (select t12.i from t1 t12))` from `test`.`t1`
explain select * from t1 where not exists
((select t11.i from t1 t11) union (select t12.i from t1 t12));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
2	SUBQUERY	t11	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
3	UNION	t12	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
4	UNION RESULT	<union2,3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` where true
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(250), b INT auto_increment, PRIMARY KEY (b));
insert into t1 (a) values (FLOOR(rand() * 100));
insert into t1 (a) select FLOOR(rand() * 100) from t1;
insert into t1 (a) select FLOOR(rand() * 100) from t1;
insert into t1 (a) select FLOOR(rand() * 100) from t1;
insert into t1 (a) select FLOOR(rand() * 100) from t1;
insert into t1 (a) select FLOOR(rand() * 100) from t1;
insert into t1 (a) select FLOOR(rand() * 100) from t1;
insert into t1 (a) select FLOOR(rand() * 100) from t1;
insert into t1 (a) select FLOOR(rand() * 100) from t1;
insert into t1 (a) select FLOOR(rand() * 100) from t1;
insert into t1 (a) select FLOOR(rand() * 100) from t1;
insert into t1 (a) select FLOOR(rand() * 100) from t1;
insert into t1 (a) select FLOOR(rand() * 100) from t1;
insert into t1 (a) select FLOOR(rand() * 100) from t1;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT a,
(SELECT REPEAT(' ',250) FROM t1 i1
WHERE i1.b=t1.a ORDER BY RAND() LIMIT 1) AS a
FROM t1 ORDER BY a LIMIT 5;
a	a
#	NULL
#	NULL
#	NULL
#	NULL
#	NULL
DROP TABLE t1;
CREATE TABLE t1 (a INT, b INT);
CREATE TABLE t2 (a INT);
INSERT INTO t2 values (1);
INSERT INTO t1 VALUES (1,1),(1,2),(2,3),(3,4);
SELECT (SELECT COUNT(DISTINCT t1.b) from t2) FROM t1 GROUP BY t1.a;
(SELECT COUNT(DISTINCT t1.b) from t2)
2
1
1
SELECT (SELECT COUNT(DISTINCT t1.b) from t2 union select 1 from t2 where 12 < 3)
FROM t1 GROUP BY t1.a;
(SELECT COUNT(DISTINCT t1.b) from t2 union select 1 from t2 where 12 < 3)
2
1
1
SELECT COUNT(DISTINCT t1.b), (SELECT COUNT(DISTINCT t1.b)) FROM t1 GROUP BY t1.a;
COUNT(DISTINCT t1.b)	(SELECT COUNT(DISTINCT t1.b))
2	2
1	1
1	1
SELECT COUNT(DISTINCT t1.b),
(SELECT COUNT(DISTINCT t1.b) union select 1 from DUAL where 12 < 3)
FROM t1 GROUP BY t1.a;
COUNT(DISTINCT t1.b)	(SELECT COUNT(DISTINCT t1.b) union select 1 from DUAL where 12 < 3)
2	2
1	1
1	1
SELECT (
SELECT (
SELECT COUNT(DISTINCT t1.b)
)
)
FROM t1 GROUP BY t1.a;
(
SELECT (
SELECT COUNT(DISTINCT t1.b)
)
)
2
1
1
SELECT (
SELECT (
SELECT (
SELECT COUNT(DISTINCT t1.b)
)
)
FROM t1 GROUP BY t1.a LIMIT 1)
FROM t1 t2
GROUP BY t2.a;
(
SELECT (
SELECT (
SELECT COUNT(DISTINCT t1.b)
)
)
FROM t1 GROUP BY t1.a LIMIT 1)
2
2
2
DROP TABLE t1,t2;
CREATE TABLE t1 (a int, b int, PRIMARY KEY (b));
CREATE TABLE t2 (x int auto_increment, y int, z int,
PRIMARY KEY (x), FOREIGN KEY (y) REFERENCES t1 (b));
create table t3 (a int);
insert into t3 values (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
insert into t1 select RAND()*1000, A.a + 10*(B.a+10*(C.a+10*D.a))
from t3 A, t3 B, t3 C, t3 D where D.a<3;
insert into t2(y,z) select t1.b, RAND()*1000 from t1, t3;
SET SESSION sort_buffer_size = 32 * 1024;
SELECT COUNT(*)
FROM (SELECT  a, b, (SELECT x FROM t2 WHERE y=b ORDER BY z DESC LIMIT 1) c
FROM t1) t;
COUNT(*)
3000
SET SESSION sort_buffer_size = 8 * 1024 * 1024;
SELECT COUNT(*)
FROM (SELECT  a, b, (SELECT x FROM t2 WHERE y=b ORDER BY z DESC LIMIT 1) c
FROM t1) t;
COUNT(*)
3000
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (id char(4) PRIMARY KEY, c int);
CREATE TABLE t2 (c int);
INSERT INTO t1 VALUES ('aa', 1);
INSERT INTO t2 VALUES (1);
SELECT * FROM t1
WHERE EXISTS (SELECT c FROM t2 WHERE c=1
UNION
SELECT c from t2 WHERE c=t1.c);
id	c
aa	1
INSERT INTO t1 VALUES ('bb', 2), ('cc', 3), ('dd',1);
SELECT * FROM t1
WHERE EXISTS (SELECT c FROM t2 WHERE c=1
UNION
SELECT c from t2 WHERE c=t1.c);
id	c
aa	1
bb	2
cc	3
dd	1
INSERT INTO t2 VALUES (2);
CREATE TABLE t3 (c int);
INSERT INTO t3 VALUES (1);
SELECT * FROM t1
WHERE EXISTS (SELECT t2.c FROM t2 JOIN t3 ON t2.c=t3.c WHERE t2.c=1
UNION
SELECT c from t2 WHERE c=t1.c);
id	c
aa	1
bb	2
cc	3
dd	1
DROP TABLE t1,t2,t3;
CREATE TABLE t1(f1 int);
CREATE TABLE t2(f2 int, f21 int, f3 timestamp);
INSERT INTO t1 VALUES (1),(1),(2),(2);
INSERT INTO t2 VALUES (1,1,"2004-02-29 11:11:11"), (2,2,"2004-02-29 11:11:11");
SELECT ((SELECT f2 FROM t2 WHERE f21=f1 LIMIT 1) * COUNT(f1)) AS sq FROM t1 GROUP BY f1;
sq
2
4
SELECT (SELECT SUM(1) FROM t2 ttt GROUP BY t2.f3 LIMIT 1) AS tt FROM t2;
tt
2
2
PREPARE stmt1 FROM 'SELECT ((SELECT f2 FROM t2 WHERE f21=f1 LIMIT 1) * COUNT(f1)) AS sq FROM t1 GROUP BY f1';
EXECUTE stmt1;
sq
2
4
EXECUTE stmt1;
sq
2
4
DEALLOCATE PREPARE stmt1;
SELECT f2, AVG(f21),
(SELECT t.f3 FROM t2 AS t WHERE t2.f2=t.f2 AND t.f3=MAX(t2.f3)) AS test
FROM t2 GROUP BY f2;
f2	AVG(f21)	test
1	1.0000	2004-02-29 11:11:11
2	2.0000	2004-02-29 11:11:11
DROP TABLE t1,t2;
CREATE TABLE t1 (a int, b INT, c CHAR(10) NOT NULL);
INSERT INTO t1 VALUES
(1,1,'a'), (1,2,'b'), (1,3,'c'), (1,4,'d'), (1,5,'e'),
(2,1,'f'), (2,2,'g'), (2,3,'h'), (3,4,'i'), (3,3,'j'),
(3,2,'k'), (3,1,'l'), (1,9,'m');
SELECT a, MAX(b),
(SELECT t.c FROM t1 AS t WHERE t1.a=t.a AND t.b=MAX(t1.b)) AS test
FROM t1 GROUP BY a;
a	MAX(b)	test
1	9	m
2	3	h
3	4	i
DROP TABLE t1;
DROP TABLE IF EXISTS t1;
DROP TABLE IF EXISTS t2;
DROP TABLE IF EXISTS t1xt2;
CREATE TABLE t1 (
id_1 int(5) NOT NULL,
t varchar(4) DEFAULT NULL
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE t2 (
id_2 int(5) NOT NULL,
t varchar(4) DEFAULT NULL
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE t1xt2 (
id_1 int(5) NOT NULL,
id_2 int(5) NOT NULL
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1, 'a'), (2, 'b'), (3, 'c'), (4, 'd');
INSERT INTO t2 VALUES (2, 'bb'), (3, 'cc'), (4, 'dd'), (12, 'aa');
INSERT INTO t1xt2 VALUES (2, 2), (3, 3), (4, 4);
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 IN (SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1));
id_1
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 IN ((SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1)));
id_1
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 IN (((SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1))));
id_1
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 NOT IN (SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1));
id_1
1
2
3
4
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 NOT IN ((SELECT t1xt2.id_2 FROM t1xt2 where t1.id_1 = t1xt2.id_1)));
id_1
1
2
3
4
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 NOT IN (((SELECT t1xt2.id_2 FROM t1xt2 where t1.id_1 = t1xt2.id_1))));
id_1
1
2
3
4
insert INTO t1xt2 VALUES (1, 12);
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 IN (SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1));
id_1
1
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 IN ((SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1)));
id_1
1
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 IN (((SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1))));
id_1
1
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 NOT IN (SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1));
id_1
2
3
4
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 NOT IN ((SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1)));
id_1
2
3
4
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 NOT IN (((SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1))));
id_1
2
3
4
insert INTO t1xt2 VALUES (2, 12);
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 IN (SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1));
id_1
1
2
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 IN ((SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1)));
id_1
1
2
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 IN (((SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1))));
id_1
1
2
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 NOT IN (SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1));
id_1
3
4
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 NOT IN ((SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1)));
id_1
3
4
SELECT DISTINCT t1.id_1 FROM t1 WHERE
(12 NOT IN (((SELECT t1xt2.id_2 FROM t1xt2 WHERE t1.id_1 = t1xt2.id_1))));
id_1
3
4
DROP TABLE t1;
DROP TABLE t2;
DROP TABLE t1xt2;
CREATE TABLE t1 (a int);
INSERT INTO t1 VALUES (3), (1), (2);
SELECT 'this is ' 'a test.' AS col1, a AS col2 FROM t1;
col1	col2
this is a test.	3
this is a test.	1
this is a test.	2
SELECT * FROM (SELECT 'this is ' 'a test.' AS col1, a AS t2 FROM t1) t;
col1	t2
this is a test.	3
this is a test.	1
this is a test.	2
DROP table t1;
CREATE TABLE t1 (a int, b int);
CREATE TABLE t2 (m int, n int);
INSERT INTO t1 VALUES (2,2), (2,2), (3,3), (3,3), (3,3), (4,4);
INSERT INTO t2 VALUES (1,11), (2,22), (3,32), (4,44), (4,44);
SELECT COUNT(*), a,
(SELECT m FROM t2 WHERE m = count(*) LIMIT 1)
FROM t1 GROUP BY a;
COUNT(*)	a	(SELECT m FROM t2 WHERE m = count(*) LIMIT 1)
2	2	2
3	3	3
1	4	1
SELECT COUNT(*), a,
(SELECT MIN(m) FROM t2 WHERE m = count(*))
FROM t1 GROUP BY a;
COUNT(*)	a	(SELECT MIN(m) FROM t2 WHERE m = count(*))
2	2	2
3	3	3
1	4	1
SELECT COUNT(*), a
FROM t1 GROUP BY a
HAVING (SELECT MIN(m) FROM t2 WHERE m = count(*)) > 1;
COUNT(*)	a
2	2
3	3
DROP TABLE t1,t2;
CREATE TABLE t1 (a int, b int);
CREATE TABLE t2 (m int, n int);
INSERT INTO t1 VALUES (2,2), (2,2), (3,3), (3,3), (3,3), (4,4);
INSERT INTO t2 VALUES (1,11), (2,22), (3,32), (4,44), (4,44);
SELECT COUNT(*) c, a,
(SELECT GROUP_CONCAT(COUNT(a)) FROM t2 WHERE m = a)
FROM t1 GROUP BY a;
c	a	(SELECT GROUP_CONCAT(COUNT(a)) FROM t2 WHERE m = a)
2	2	2
3	3	3
1	4	1,1
SELECT COUNT(*) c, a,
(SELECT GROUP_CONCAT(COUNT(a)+1) FROM t2 WHERE m = a)
FROM t1 GROUP BY a;
c	a	(SELECT GROUP_CONCAT(COUNT(a)+1) FROM t2 WHERE m = a)
2	2	3
3	3	4
1	4	2,2
DROP table t1,t2;
CREATE TABLE t1 (a int, b INT, d INT, c CHAR(10) NOT NULL, PRIMARY KEY (a, b));
INSERT INTO t1 VALUES (1,1,0,'a'), (1,2,0,'b'), (1,3,0,'c'), (1,4,0,'d'),
(1,5,0,'e'), (2,1,0,'f'), (2,2,0,'g'), (2,3,0,'h'), (3,4,0,'i'), (3,3,0,'j'),
(3,2,0,'k'), (3,1,0,'l'), (1,9,0,'m'), (1,0,10,'n'), (2,0,5,'o'), (3,0,7,'p');
SELECT a, MAX(b),
(SELECT t.c FROM t1 AS t WHERE t1.a=t.a AND t.b=MAX(t1.b + 0)) as test
FROM t1 GROUP BY a;
a	MAX(b)	test
1	9	m
2	3	h
3	4	i
SELECT a, AVG(b),
(SELECT t.c FROM t1 AS t WHERE t1.a=t.a AND t.b=AVG(t1.b)) AS test
FROM t1 WHERE t1.d=0 GROUP BY a;
a	AVG(b)	test
1	4.0000	d
2	2.0000	g
3	2.5000	NULL
SELECT tt.a,
(SELECT (SELECT c FROM t1 as t WHERE t1.a=t.a AND t.d=MAX(t1.b + tt.a)
LIMIT 1) FROM t1 WHERE t1.a=tt.a GROUP BY a LIMIT 1) as test
FROM t1 as tt;
a	test
1	n
1	n
1	n
1	n
1	n
1	n
1	n
2	o
2	o
2	o
2	o
3	p
3	p
3	p
3	p
3	p
SELECT tt.a,
(SELECT (SELECT t.c FROM t1 AS t WHERE t1.a=t.a AND t.d=MAX(t1.b + tt.a)
LIMIT 1)
FROM t1 WHERE t1.a=tt.a GROUP BY a LIMIT 1) as test
FROM t1 as tt GROUP BY tt.a;
a	test
1	n
2	o
3	p
SELECT tt.a, MAX(
(SELECT (SELECT t.c FROM t1 AS t WHERE t1.a=t.a AND t.d=MAX(t1.b + tt.a)
LIMIT 1)
FROM t1 WHERE t1.a=tt.a GROUP BY a LIMIT 1)) as test
FROM t1 as tt GROUP BY tt.a;
a	test
1	n
2	o
3	p
DROP TABLE t1;
CREATE TABLE t1 (a int, b int);
INSERT INTO t1 VALUES (2,22),(1,11),(2,22);
SELECT a FROM t1 WHERE (SELECT COUNT(b) FROM DUAL) > 0 GROUP BY a;
a
1
2
SELECT a FROM t1 WHERE (SELECT COUNT(b) FROM DUAL) > 1 GROUP BY a;
a
SELECT a FROM t1 t0
WHERE (SELECT COUNT(t0.b) FROM t1 t WHERE t.b>20) GROUP BY a;
a
1
2
SET @@sql_mode='ansi';
SELECT a FROM t1 WHERE (SELECT COUNT(b) FROM DUAL) > 0 GROUP BY a;
ERROR HY000: Invalid use of group function
SELECT a FROM t1 WHERE (SELECT COUNT(b) FROM DUAL) > 1 GROUP BY a;
ERROR HY000: Invalid use of group function
SELECT a FROM t1 t0
WHERE (SELECT COUNT(t0.b) FROM t1 t WHERE t.b>20) GROUP BY a;
ERROR HY000: Invalid use of group function
SET @@sql_mode=default;
DROP TABLE t1;
CREATE TABLE t1 (a INT);
INSERT INTO t1 values (1),(1),(1),(1);
CREATE TABLE t2 (x INT);
INSERT INTO t1 values (1000),(1001),(1002);
SELECT SUM( (SELECT COUNT(a) FROM t2) ) FROM t1;
ERROR HY000: Invalid use of group function
SELECT SUM( (SELECT SUM(COUNT(a)) FROM t2) ) FROM t1;
ERROR HY000: Invalid use of group function
SELECT COUNT(1) FROM DUAL;
COUNT(1)
1
SELECT SUM( (SELECT AVG( (SELECT t1.a FROM t2) ) FROM DUAL) ) FROM t1;
ERROR HY000: Invalid use of group function
SELECT
SUM( (SELECT AVG( (SELECT COUNT(*) FROM t1 t HAVING t1.a < 12) ) FROM t2) )
FROM t1;
ERROR HY000: Invalid use of group function
SELECT t1.a as xxa,
SUM( (SELECT AVG( (SELECT COUNT(*) FROM t1 t HAVING xxa < 12) ) FROM t2) )
FROM t1;
ERROR HY000: Invalid use of group function
DROP TABLE t1,t2;
CREATE TABLE t1 (a int, b int, KEY (a));
INSERT INTO t1 VALUES (1,1),(2,1);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT 1 FROM t1 WHERE a = (SELECT COUNT(*) FROM t1 GROUP BY b);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ref	a	a	5	const	1	100.00	Using index
2	SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using temporary
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` where (`test`.`t1`.`a` = (/* select#2 */ select count(0) from `test`.`t1` group by `test`.`t1`.`b`))
DROP TABLE t1;
CREATE TABLE t1 (id int NOT NULL, st CHAR(2), UNIQUE INDEX idx(id));
INSERT INTO t1 VALUES
(3,'FL'), (2,'GA'), (4,'FL'), (1,'GA'), (5,'NY'), (7,'FL'), (6,'NY');
CREATE TABLE t2 (id int NOT NULL, INDEX idx(id));
INSERT INTO t2 VALUES (7), (5), (1), (3);
SELECT id, st FROM t1
WHERE st IN ('GA','FL') AND EXISTS(SELECT 1 FROM t2 WHERE t2.id=t1.id);
id	st
1	GA
3	FL
7	FL
SELECT id, st FROM t1
WHERE st IN ('GA','FL') AND EXISTS(SELECT 1 FROM t2 WHERE t2.id=t1.id)
GROUP BY id;
id	st
1	GA
3	FL
7	FL
SELECT id, st FROM t1
WHERE st IN ('GA','FL') AND NOT EXISTS(SELECT 1 FROM t2 WHERE t2.id=t1.id);
id	st
2	GA
4	FL
SELECT id, st FROM t1
WHERE st IN ('GA','FL') AND NOT EXISTS(SELECT 1 FROM t2 WHERE t2.id=t1.id)
GROUP BY id;
id	st
2	GA
4	FL
DROP TABLE t1,t2;
CREATE TABLE t1 (a int);
INSERT INTO t1 VALUES (1), (2);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN
SELECT * FROM (SELECT count(*) FROM t1 GROUP BY a) as res;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	<derived2>	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
2	DERIVED	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using temporary
Warnings:
Note	1003	/* select#1 */ select `res`.`count(*)` AS `count(*)` from (/* select#2 */ select count(0) AS `count(*)` from `test`.`t1` group by `test`.`t1`.`a`) `res`
DROP TABLE t1;
CREATE TABLE t1 (
a varchar(200) default NULL,
b timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
INDEX idx(a,b)
);
CREATE TABLE t2 (
a varchar(255) default NULL
);
INSERT INTO t1 VALUES ('abcdefghijk','2007-05-07 06:00:24');
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO `t1` VALUES ('asdf','2007-02-08 01:11:26');
INSERT INTO `t2` VALUES ('abcdefghijk');
INSERT INTO `t2` VALUES ('asdf');
SET session sort_buffer_size=8192;
Warnings:
Warning	1292	Truncated incorrect sort_buffer_size value: '8192'
SELECT (SELECT 1 FROM  t1 WHERE t1.a=t2.a ORDER BY t1.b LIMIT 1) AS d1 FROM t2;
d1
1
1
DROP TABLE t1,t2;
CREATE TABLE t1 (a INTEGER, b INTEGER);
CREATE TABLE t2 (x INTEGER);
INSERT INTO t1 VALUES (1,11), (2,22), (2,22);
INSERT INTO t2 VALUES (1), (2);
SELECT a, COUNT(b), (SELECT COUNT(b) FROM t2) FROM t1 GROUP BY a;
ERROR 21000: Subquery returns more than 1 row
SELECT a, COUNT(b), (SELECT COUNT(b)+0 FROM t2) FROM t1 GROUP BY a;
ERROR 21000: Subquery returns more than 1 row
SELECT (SELECT SUM(t1.a)/AVG(t2.x) FROM t2) FROM t1;
(SELECT SUM(t1.a)/AVG(t2.x) FROM t2)
3.3333
DROP TABLE t1,t2;
CREATE TABLE t1 (a INT, b INT);
INSERT INTO t1 VALUES (1, 2), (1,3), (1,4), (2,1), (2,2);
SELECT a1.a, COUNT(*) FROM t1 a1 WHERE a1.a = 1
AND EXISTS( SELECT a2.a FROM t1 a2 WHERE a2.a = a1.a)
GROUP BY a1.a;
a	COUNT(*)
1	3
DROP TABLE t1;
CREATE TABLE t1 (a INT);
CREATE TABLE t2 (a INT);
INSERT INTO t1 VALUES (1),(2);
INSERT INTO t2 VALUES (1),(2);
SELECT (SELECT SUM(t1.a) FROM t2 WHERE a=0) FROM t1;
(SELECT SUM(t1.a) FROM t2 WHERE a=0)
NULL
SELECT (SELECT SUM(t1.a) FROM t2 WHERE a!=0) FROM t1;
ERROR 21000: Subquery returns more than 1 row
SELECT (SELECT SUM(t1.a) FROM t2 WHERE a=1) FROM t1;
(SELECT SUM(t1.a) FROM t2 WHERE a=1)
3
DROP TABLE t1,t2;
CREATE TABLE t1 (a1 INT, a2 INT);
CREATE TABLE t2 (b1 INT, b2 INT);
INSERT INTO t1 VALUES (100, 200);
INSERT INTO t1 VALUES (101, 201);
INSERT INTO t2 VALUES (101, 201);
INSERT INTO t2 VALUES (103, 203);
SELECT ((a1,a2) IN (SELECT * FROM t2 WHERE b2 > 0)) IS NULL FROM t1;
((a1,a2) IN (SELECT * FROM t2 WHERE b2 > 0)) IS NULL
0
0
DROP TABLE t1, t2;
CREATE TABLE t1 (s1 BINARY(5), s2 VARBINARY(5));
INSERT INTO t1 VALUES (0x41,0x41), (0x42,0x42), (0x43,0x43);
SELECT s1, s2 FROM t1 WHERE s2 IN (SELECT s1 FROM t1);
s1	s2
SELECT s1, s2 FROM t1 WHERE (s2, 10) IN (SELECT s1, 10 FROM t1);
s1	s2
CREATE INDEX I1 ON t1 (s1);
CREATE INDEX I2 ON t1 (s2);
SELECT s1, s2 FROM t1 WHERE s2 IN (SELECT s1 FROM t1);
s1	s2
SELECT s1, s2 FROM t1 WHERE (s2, 10) IN (SELECT s1, 10 FROM t1);
s1	s2
TRUNCATE t1;
INSERT INTO t1 VALUES (0x41,0x41);
SELECT * FROM t1 WHERE s1 = (SELECT s2 FROM t1);
s1	s2
DROP TABLE t1;
CREATE TABLE t1 (a1 VARBINARY(2) NOT NULL DEFAULT '0', PRIMARY KEY (a1));
CREATE TABLE t2 (a2 BINARY(2) default '0', INDEX (a2));
CREATE TABLE t3 (a3 BINARY(2) default '0');
INSERT INTO t1 VALUES (1),(2),(3),(4);
INSERT INTO t2 VALUES (1),(2),(3);
INSERT INTO t3 VALUES (1),(2),(3);
SELECT LEFT(t2.a2, 1) FROM t2,t3 WHERE t3.a3=t2.a2;
LEFT(t2.a2, 1)
1
2
3
SELECT t1.a1, t1.a1 in (SELECT t2.a2 FROM t2,t3 WHERE t3.a3=t2.a2) FROM t1;
a1	t1.a1 in (SELECT t2.a2 FROM t2,t3 WHERE t3.a3=t2.a2)
1	0
2	0
3	0
4	0
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (a1 BINARY(3) PRIMARY KEY, b1 VARBINARY(3));
CREATE TABLE t2 (a2 VARBINARY(3) PRIMARY KEY);
CREATE TABLE t3 (a3 VARBINARY(3) PRIMARY KEY);
INSERT INTO t1 VALUES (1,10), (2,20), (3,30), (4,40);
INSERT INTO t2 VALUES (2), (3), (4), (5);
INSERT INTO t3 VALUES (10), (20), (30);
SELECT LEFT(t1.a1,1) FROM t1,t3 WHERE t1.b1=t3.a3;
LEFT(t1.a1,1)
1
2
3
SELECT a2 FROM t2 WHERE t2.a2 IN (SELECT t1.a1 FROM t1,t3 WHERE t1.b1=t3.a3);
a2
DROP TABLE t1, t2, t3;
CREATE TABLE t1(a INT, b INT);
INSERT INTO t1 VALUES (1,1), (1,2), (2,3), (2,4);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN
SELECT a AS out_a, MIN(b) FROM t1
WHERE b > (SELECT MIN(b) FROM t1 WHERE a = out_a)
GROUP BY a;
ERROR 42S22: Unknown column 'out_a' in 'where clause'
SELECT a AS out_a, MIN(b) FROM t1
WHERE b > (SELECT MIN(b) FROM t1 WHERE a = out_a)
GROUP BY a;
ERROR 42S22: Unknown column 'out_a' in 'where clause'
EXPLAIN
SELECT a AS out_a, MIN(b) FROM t1 t1_outer
WHERE b > (SELECT MIN(b) FROM t1 WHERE a = t1_outer.a)
GROUP BY a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1_outer	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	Using where; Using temporary
2	DEPENDENT SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	4	25.00	Using where
Warnings:
Note	1276	Field or reference 'test.t1_outer.a' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1_outer`.`a` AS `out_a`,min(`test`.`t1_outer`.`b`) AS `MIN(b)` from `test`.`t1` `t1_outer` where (`test`.`t1_outer`.`b` > (/* select#2 */ select min(`test`.`t1`.`b`) from `test`.`t1` where (`test`.`t1`.`a` = `test`.`t1_outer`.`a`))) group by `test`.`t1_outer`.`a`
SELECT a AS out_a, MIN(b) FROM t1 t1_outer
WHERE b > (SELECT MIN(b) FROM t1 WHERE a = t1_outer.a)
GROUP BY a;
out_a	MIN(b)
1	2
2	4
DROP TABLE t1;
CREATE TABLE t1 (a INT);
CREATE TABLE t2 (a INT);
INSERT INTO t1 VALUES (1),(2);
INSERT INTO t2 VALUES (1),(2);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
SELECT 2 FROM t1 WHERE EXISTS ((SELECT 1 FROM t2 WHERE t1.a=t2.a));
2
2
2
EXPLAIN
SELECT 2 FROM t1 WHERE EXISTS ((SELECT 1 FROM t2 WHERE t1.a=t2.a));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where; FirstMatch(t1); Using join buffer (hash join)
Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select 2 AS `2` from `test`.`t1` semi join (`test`.`t2`) where (`test`.`t2`.`a` = `test`.`t1`.`a`)
EXPLAIN
SELECT 2 FROM t1 WHERE EXISTS ((SELECT 1 FROM t2 WHERE t1.a=t2.a) UNION
(SELECT 1 FROM t2 WHERE t1.a = t2.a));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using where
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
3	DEPENDENT UNION	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
4	UNION RESULT	<union2,3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
Note	1276	Field or reference 'test.t1.a' of SELECT #3 was resolved in SELECT #1
Note	1003	/* select#1 */ select 2 AS `2` from `test`.`t1` where exists(/* select#2 */ select 1 from `test`.`t2` where (`test`.`t1`.`a` = `test`.`t2`.`a`) union /* select#3 */ select 1 from `test`.`t2` where (`test`.`t1`.`a` = `test`.`t2`.`a`))
DROP TABLE t1,t2;
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
create table t0(a int);
insert into t0 values (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
create table t1(f11 int, f12 int);
create table t2(f21 int unsigned not null, f22 int, f23 varchar(10));
insert into t1 values(1,1),(2,2), (3, 3);
insert into t2 
select -1 , (@a:=(A.a + 10 * (B.a + 10 * (C.a+10*D.a))))/5000 + 1, @a 
from t0 A, t0 B, t0 C, t0 D;
set session sort_buffer_size= 33*1024;
select count(*) from t1 where f12 =
(select f22 from t2 where f22 = f12 order by f21 desc, f22, f23 limit 1);
count(*)
3
drop table t0,t1,t2;
SET sql_mode = default;
CREATE TABLE t4 (
f7 varchar(32) COLLATE utf8mb3_bin NOT NULL default '',
f10 varchar(32) COLLATE utf8mb3_bin default NULL,
PRIMARY KEY  (f7)
);
Warnings:
Warning	3778	'utf8mb3_bin' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	3778	'utf8mb3_bin' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
INSERT INTO t4 VALUES(1,1), (2,null);
CREATE TABLE t2 (
f4 varchar(32) COLLATE utf8mb3_bin NOT NULL default '',
f2 varchar(50) COLLATE utf8mb3_bin default NULL,
f3 varchar(10) COLLATE utf8mb3_bin default NULL,
PRIMARY KEY  (f4),
UNIQUE KEY uk1 (f2)
);
Warnings:
Warning	3778	'utf8mb3_bin' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	3778	'utf8mb3_bin' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	3778	'utf8mb3_bin' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
INSERT INTO t2 VALUES(1,1,null), (2,2,null);
CREATE TABLE t1 (
f8 varchar(32) COLLATE utf8mb3_bin NOT NULL default '',
f1 varchar(10) COLLATE utf8mb3_bin default NULL,
f9 varchar(32) COLLATE utf8mb3_bin default NULL,
PRIMARY KEY  (f8)
);
Warnings:
Warning	3778	'utf8mb3_bin' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	3778	'utf8mb3_bin' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	3778	'utf8mb3_bin' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
INSERT INTO t1 VALUES (1,'P',1), (2,'P',1), (3,'R',2);
CREATE TABLE t3 (
f6 varchar(32) COLLATE utf8mb3_bin NOT NULL default '',
f5 varchar(50) COLLATE utf8mb3_bin default NULL,
PRIMARY KEY (f6)
);
Warnings:
Warning	3778	'utf8mb3_bin' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
Warning	3778	'utf8mb3_bin' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
INSERT INTO t3 VALUES (1,null), (2,null);
SELECT
IF(t1.f1 = 'R', a1.f2, t2.f2) AS a4,
IF(t1.f1 = 'R', a1.f3, t2.f3) AS f3,
SUM(
IF(
(SELECT VPC.f2
FROM t2 VPC, t4 a2, t2 a3
WHERE
VPC.f4 = a2.f10 AND a3.f2 = a4
LIMIT 1) IS NULL,
0,
t3.f5
)
) AS a6
FROM
t2, t3, t1 JOIN t2 a1 ON t1.f9 = a1.f4
GROUP BY a4;
a4	f3	a6
1	NULL	NULL
2	NULL	NULL
DROP TABLE t1, t2, t3, t4;
create table t1 (a float(5,4) zerofill);
Warnings:
Warning	1681	The ZEROFILL attribute is deprecated and will be removed in a future release. Use the LPAD function to zero-pad numbers, or store the formatted numbers in a CHAR column.
Warning	1681	Specifying number of digits for floating point data types is deprecated and will be removed in a future release.
create table t2 (a float(5,4),b float(2,0));
Warnings:
Warning	1681	Specifying number of digits for floating point data types is deprecated and will be removed in a future release.
Warning	1681	Specifying number of digits for floating point data types is deprecated and will be removed in a future release.
select t1.a from t1 where
t1.a= (select b from t2 limit 1) and not
t1.a= (select a from t2 limit 1) ;
a
drop table t1, t2;
#
# Bug#45061: Incorrectly market field caused wrong result.
#
CREATE TABLE `c` (
`int_nokey` int(11) NOT NULL,
`int_key` int(11) NOT NULL,
KEY `int_key` (`int_key`)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `c` VALUES (9,9), (0,0), (8,6), (3,6), (7,6), (0,4),
(1,7), (9,4), (0,8), (9,4), (0,7), (5,5), (0,0), (8,5), (8,7),
(5,2), (1,8), (7,0), (0,9), (9,5);
ANALYZE TABLE c;
Table	Op	Msg_type	Msg_text
test.c	analyze	status	OK
SELECT * FROM c WHERE `int_key` IN (SELECT `int_nokey`);
int_nokey	int_key
9	9
0	0
5	5
0	0
EXPLAIN SELECT * FROM c WHERE `int_key` IN (SELECT `int_nokey`);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	c	NULL	ALL	NULL	NULL	NULL	NULL	20	100.00	Using where
DROP TABLE c;
# End of test for bug#45061.
#
# Bug #46749: Segfault in add_key_fields() with outer subquery level 
#   field references
#
CREATE TABLE t1 (
a int,
b int,
UNIQUE (a), KEY (b)
);
INSERT INTO t1 VALUES (1,1), (2,1);
CREATE TABLE st1 like t1;
INSERT INTO st1 VALUES (1,1), (2,1);
CREATE TABLE st2 like t1;
INSERT INTO st2 VALUES (1,1), (2,1);
ANALYZE TABLE t1, st1, st2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.st1	analyze	status	OK
test.st2	analyze	status	OK
EXPLAIN
SELECT MAX(b), (SELECT COUNT(*) FROM st1,st2 WHERE st2.b <= t1.b)
FROM t1 
WHERE a = 230;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
2	DEPENDENT SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
Warnings:
Note	1276	Field or reference 'test.t1.b' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select max(NULL) AS `MAX(b)`,(/* select#2 */ select count(0) from `test`.`st1` join `test`.`st2` where (`test`.`st2`.`b` <= NULL)) AS `(SELECT COUNT(*) FROM st1,st2 WHERE st2.b <= t1.b)` from `test`.`t1` where multiple equal(230, NULL)
SELECT MAX(b), (SELECT COUNT(*) FROM st1,st2 WHERE st2.b <= t1.b)
FROM t1 
WHERE a = 230;
MAX(b)	(SELECT COUNT(*) FROM st1,st2 WHERE st2.b <= t1.b)
NULL	NULL
DROP TABLE t1, st1, st2;
#
# Bug #48709: Assertion failed in sql_select.cc:11782: 
#   int join_read_key(JOIN_TAB*)
#
CREATE TABLE t1 (pk int PRIMARY KEY, int_key int);
INSERT INTO t1 VALUES (10,1), (14,1);
CREATE TABLE t2 (pk int PRIMARY KEY, int_key int);
INSERT INTO t2 VALUES (3,3), (5,NULL), (7,3);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
# should have eq_ref for t1, unless subquery materialization is used
EXPLAIN
SELECT * FROM t2 outr
WHERE outr.int_key NOT IN (SELECT t1.pk FROM t1, t2)  
ORDER BY outr.pk;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	outr	NULL	index	x	x	x	x	x	100.00	x
x	x	t1	NULL	index	x	x	x	x	x	100.00	x
x	x	t2	NULL	index	x	x	x	x	x	100.00	x
Warnings:
x	x	/* select#1 */ select `test`.`outr`.`pk` AS `pk`,`test`.`outr`.`int_key` AS `int_key` from `test`.`t2` `outr` where <in_optimizer>(`test`.`outr`.`int_key`,`test`.`outr`.`int_key` in ( <materialize> (/* select#2 */ select `test`.`t1`.`pk` from `test`.`t1` join `test`.`t2` where true ), <primary_index_lookup>(`test`.`outr`.`int_key` in <temporary table> on <auto_distinct_key> where ((`test`.`outr`.`int_key` = `<materialized_subquery>`.`pk`)))) is false) order by `test`.`outr`.`pk`
# should not crash on debug binaries
SELECT * FROM t2 outr
WHERE outr.int_key NOT IN (SELECT t1.pk FROM t1, t2)  
ORDER BY outr.pk;
pk	int_key
3	3
7	3
DROP TABLE t1,t2;
create table t1 (oref int, grp int, ie int) ;
insert into t1 (oref, grp, ie) values
(1, 1, 1),
(1, 1, 1),
(1, 2, NULL),
(2, 1, 3),
(3, 1, 4),
(3, 2, NULL);
create table t2 (oref int, a int);
insert into t2 values 
(1, 1),
(2, 2),
(3, 3),
(4, NULL),
(2, NULL);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
select a, oref, a in (select max(ie) 
from t1 where oref=t2.oref group by grp) Z from t2;
a	oref	Z
1	1	1
2	2	0
3	3	NULL
NULL	4	0
NULL	2	NULL
explain
select a, oref, a in (select max(ie) 
from t1 where oref=t2.oref group by grp) z from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	5	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	6	16.67	Using where; Using temporary
Warnings:
Note	1276	Field or reference 'test.t2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`oref` AS `oref`,<in_optimizer>(`test`.`t2`.`a`,<exists>(/* select#2 */ select max(`test`.`t1`.`ie`) from `test`.`t1` where (`test`.`t1`.`oref` = `test`.`t2`.`oref`) group by `test`.`t1`.`grp` having <if>(outer_field_is_not_null, (<cache>(`test`.`t2`.`a`) = <ref_null_helper>(max(`test`.`t1`.`ie`))), true))) AS `z` from `test`.`t2`
explain
select a, oref from t2 
where a in (select max(ie) from t1 where oref=t2.oref group by grp);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	5	100.00	Using where
2	DEPENDENT SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	6	16.67	Using where; Using temporary
Warnings:
Note	1276	Field or reference 'test.t2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`oref` AS `oref` from `test`.`t2` where <in_optimizer>(`test`.`t2`.`a`,<exists>(/* select#2 */ select max(`test`.`t1`.`ie`) from `test`.`t1` where (`test`.`t1`.`oref` = `test`.`t2`.`oref`) group by `test`.`t1`.`grp` having (<cache>(`test`.`t2`.`a`) = <ref_null_helper>(max(`test`.`t1`.`ie`)))))
select a, oref, a in (
select max(ie) from t1 where oref=t2.oref group by grp union
select max(ie) from t1 where oref=t2.oref group by grp
) Z from t2;
a	oref	Z
1	1	1
2	2	0
3	3	NULL
NULL	4	0
NULL	2	NULL
create table t3 (a int);
insert into t3 values (NULL), (NULL);
flush status;
select a in (select max(ie) from t1 where oref=4 group by grp) from t3;
a in (select max(ie) from t1 where oref=4 group by grp)
0
0
show status like 'Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	11
select ' ^ This must show 11' Z;
Z
 ^ This must show 11
explain select a in (select max(ie) from t1 where oref=4 group by grp) from t3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t3	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
2	SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	6	16.67	Using where; Using temporary
Warnings:
Note	1003	/* select#1 */ select <in_optimizer>(`test`.`t3`.`a`,`test`.`t3`.`a` in ( <materialize> (/* select#2 */ select max(`test`.`t1`.`ie`) from `test`.`t1` where (`test`.`t1`.`oref` = 4) group by `test`.`t1`.`grp` having true ), <primary_index_lookup>(`test`.`t3`.`a` in <temporary table> on <auto_distinct_key> where ((`test`.`t3`.`a` = `<materialized_subquery>`.`max(ie)`))))) AS `a in (select max(ie) from t1 where oref=4 group by grp)` from `test`.`t3`
drop table t1, t2, t3;
create table t1 (a int, oref int, key(a));
insert into t1 values 
(1, 1),
(1, NULL),
(2, 3),
(2, NULL),
(3, NULL);
create table t2 (a int, oref int);
insert into t2 values (1, 1), (2,2), (NULL, 3), (NULL, 4);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
select oref, a, a in (select a from t1 where oref=t2.oref) Z from t2;
oref	a	Z
1	1	1
2	2	0
3	NULL	NULL
4	NULL	0
explain
select oref, a, a in (select a from t1 where oref=t2.oref) z from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	ALL	a	NULL	NULL	NULL	5	20.00	Using where
Warnings:
Note	1276	Field or reference 'test.t2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`oref` AS `oref`,`test`.`t2`.`a` AS `a`,<in_optimizer>(`test`.`t2`.`a`,<exists>(/* select#2 */ select `test`.`t1`.`a` from `test`.`t1` where ((`test`.`t1`.`oref` = `test`.`t2`.`oref`) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t2`.`a`) = `test`.`t1`.`a`) or (`test`.`t1`.`a` is null)), true)) having <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`a`), true))) AS `z` from `test`.`t2`
flush status;
select oref, a from t2 where a in (select a from t1 where oref=t2.oref);
oref	a
1	1
show status like '%Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	11
delete from t2;
insert into t2 values (NULL, 0),(NULL, 0), (NULL, 0), (NULL, 0);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
FLUSH STATUS;
select oref, a, a in (select a from t1 where oref=t2.oref) Z from t2;
oref	a	Z
0	NULL	0
0	NULL	0
0	NULL	0
0	NULL	0
show status like '%Handler_read%';
Variable_name	Value
Handler_read_first	5
Handler_read_key	5
Handler_read_last	0
Handler_read_next	0
Handler_read_prev	0
Handler_read_rnd	0
Handler_read_rnd_next	29
select 'No key lookups, seq reads: 29= 5 reads from t2 + 4 * 6 reads from t1.' Z;
Z
No key lookups, seq reads: 29= 5 reads from t2 + 4 * 6 reads from t1.
drop table t1, t2;
create table t1 (a int, b int, primary key (a));
insert into t1 values (1,1), (3,1),(100,1);
create table t2 (a int, b int);
insert into t2 values (1,1),(2,1),(NULL,1),(NULL,0);
select a,b, a in (select a from t1 where t1.b = t2.b) Z from t2 ;
a	b	Z
1	1	1
2	1	0
NULL	1	NULL
NULL	0	0
drop table t1, t2;
create table t1 (a int, b int, key(a));
insert into t1 values 
(0,0),(1,1),(2,2),(3,3),(4,4),(5,5),(6,6),(7,7),(8,8),(9,9);
create table t2 like t1;
insert into t2 select * from t1;
update t2 set b=1;
create table t3 (a int, oref int);
insert into t3 values (1, 1), (NULL,1), (NULL,0);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
select a, oref, 
t3.a in (select t1.a from t1, t2 where t1.b=t2.a and t2.b=t3.oref) Z 
from t3;
a	oref	Z
1	1	1
NULL	1	NULL
NULL	0	0
explain
select a, oref, 
t3.a in (select t1.a from t1, t2 where t1.b=t2.a and t2.b=t3.oref) z 
from t3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t3	NULL	ALL	NULL	NULL	NULL	NULL	3	1	NULL
2	DEPENDENT SUBQUERY	t1	NULL	ref_or_null	a	a	5	func	2	1	Using where; Full scan on NULL key
2	DEPENDENT SUBQUERY	t2	NULL	ref	a	a	5	test.t1.b	1	1	Using where
Warnings:
Note	1276	Field or reference 'test.t3.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a`,`test`.`t3`.`oref` AS `oref`,<in_optimizer>(`test`.`t3`.`a`,<exists>(/* select#2 */ select `test`.`t1`.`a` from `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t2`.`b` = `test`.`t3`.`oref`) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t3`.`a`) = `test`.`t1`.`a`) or (`test`.`t1`.`a` is null)), true)) having <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`a`), true))) AS `z` from `test`.`t3`
drop table t1, t2, t3;
create table t1 (a int NOT NULL, b int NOT NULL, key(a));
insert into t1 values 
(0,0),(1,1),(2,2),(3,3),(4,4),(5,5),(6,6),(7,7),(8,8),(9,9);
create table t2 like t1;
insert into t2 select * from t1;
update t2 set b=1;
create table t3 (a int, oref int);
insert into t3 values (1, 1), (NULL,1), (NULL,0);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
select a, oref, 
t3.a in (select t1.a from t1, t2 where t1.b=t2.a and t2.b=t3.oref) Z 
from t3;
a	oref	Z
1	1	1
NULL	1	NULL
NULL	0	0
This must show a trig_cond:
explain
select a, oref, 
t3.a in (select t1.a from t1, t2 where t1.b=t2.a and t2.b=t3.oref) z 
from t3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t3	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	ref	a	a	4	func	1	100.00	Using where; Full scan on NULL key
2	DEPENDENT SUBQUERY	t2	NULL	ref	a	a	4	test.t1.b	1	10.00	Using where
Warnings:
Note	1276	Field or reference 'test.t3.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t3`.`a` AS `a`,`test`.`t3`.`oref` AS `oref`,<in_optimizer>(`test`.`t3`.`a`,<exists>(/* select#2 */ select `test`.`t1`.`a` from `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t2`.`b` = `test`.`t3`.`oref`) and <if>(outer_field_is_not_null, (<cache>(`test`.`t3`.`a`) = `test`.`t1`.`a`), true)))) AS `z` from `test`.`t3`
drop table t1,t2,t3;
create table t1 (oref int, grp int);
insert into t1 (oref, grp) values
(1, 1),
(1, 1);
create table t2 (oref int, a int);
insert into t2 values 
(1, NULL),
(2, NULL);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
select a, oref, 
a in (select count(*) from t1 group by grp having grp=t2.oref) Z from t2;
a	oref	Z
NULL	1	NULL
NULL	2	0
This must show a trig_cond:
explain
select a, oref, 
a in (select count(*) from t1 group by grp having grp=t2.oref) z from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using temporary
Warnings:
Note	1276	Field or reference 't2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`oref` AS `oref`,<in_optimizer>(`test`.`t2`.`a`,<exists>(/* select#2 */ select count(0) from `test`.`t1` group by `test`.`t1`.`grp` having ((`test`.`t1`.`grp` = `test`.`t2`.`oref`) and <if>(outer_field_is_not_null, (<cache>(`test`.`t2`.`a`) = <ref_null_helper>(count(0))), true)))) AS `z` from `test`.`t2`
drop table t1, t2;
create table t1 (a int, b int, primary key (a));
insert into t1 values (1,1), (3,1),(100,1);
create table t2 (a int, b int);
insert into t2 values (1,1),(2,1),(NULL,1),(NULL,0);
select a,b, a in (select a from t1 where t1.b = t2.b union select a from
t1 where t1.b = t2.b) Z from t2 ;
a	b	Z
1	1	1
2	1	0
NULL	1	NULL
NULL	0	0
select a,b, a in (select a from t1 where t1.b = t2.b) Z from t2 ;
a	b	Z
1	1	1
2	1	0
NULL	1	NULL
NULL	0	0
drop table t1, t2;
create table t3 (a int);
insert into t3 values (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
create table t2 (a int, b int, oref int);
insert into t2 values (NULL,1, 100), (NULL,2, 100);
create table t1 (a int, b int, c int, key(a,b));
insert into t1 select 2*A, 2*A, 100 from t3;
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
explain select a,b, oref, (a,b) in (select a,b from t1 where c=t2.oref) z from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	index_subquery	a	a	5	func	2	10.00	Using where; Full scan on NULL key
Warnings:
Note	1276	Field or reference 'test.t2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`oref` AS `oref`,<in_optimizer>((`test`.`t2`.`a`,`test`.`t2`.`b`),<exists>(<index_lookup>(<cache>(`test`.`t2`.`a`) in t1 on a checking NULL where ((`test`.`t1`.`c` = `test`.`t2`.`oref`) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t2`.`a`) = `test`.`t1`.`a`) or (`test`.`t1`.`a` is null)), true) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t2`.`b`) = `test`.`t1`.`b`) or (`test`.`t1`.`b` is null)), true)) having (<if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`a`), true) and <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`b`), true))))) AS `z` from `test`.`t2`
select a,b, oref, (a,b) in (select a,b from t1 where c=t2.oref) z from t2;
a	b	oref	z
NULL	1	100	0
NULL	2	100	NULL
create table t4 (x int);
insert into t4 select A.a + 10*B.a from t1 A, t1 B;
ANALYZE TABLE t4;
Table	Op	Msg_type	Msg_text
test.t4	analyze	status	OK
explain
select a,b, oref, 
(a,b) in (select a,b from t1,t4 where c=t2.oref) z 
from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	ref_or_null	a	a	5	func	2	10.00	Using where; Full scan on NULL key
2	DEPENDENT SUBQUERY	t4	NULL	ALL	NULL	NULL	NULL	NULL	100	100.00	Using join buffer (hash join)
Warnings:
Note	1276	Field or reference 'test.t2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`oref` AS `oref`,<in_optimizer>((`test`.`t2`.`a`,`test`.`t2`.`b`),<exists>(/* select#2 */ select `test`.`t1`.`a`,`test`.`t1`.`b` from `test`.`t1` join `test`.`t4` where ((`test`.`t1`.`c` = `test`.`t2`.`oref`) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t2`.`a`) = `test`.`t1`.`a`) or (`test`.`t1`.`a` is null)), true) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t2`.`b`) = `test`.`t1`.`b`) or (`test`.`t1`.`b` is null)), true)) having (<if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`a`), true) and <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`b`), true)))) AS `z` from `test`.`t2`
select a,b, oref, 
(a,b) in (select a,b from t1,t4 where c=t2.oref) z 
from t2;
a	b	oref	z
NULL	1	100	0
NULL	2	100	NULL
drop table t1,t2,t3,t4;
create table t1 (oref char(4), grp int, ie1 int, ie2 int);
insert into t1 (oref, grp, ie1, ie2) values
('aa', 10, 2, 1),
('aa', 10, 1, 1),
('aa', 20, 2, 1),
('bb', 10, 3, 1),
('cc', 10, 4, 2),
('cc', 20, 3, 2),
('ee', 10, 2, 1),
('ee', 10, 1, 2),
('ff', 20, 2, 2),
('ff', 20, 1, 2);
create table t2 (oref char(4), a int, b int);
insert into t2 values 
('ee', NULL, 1),
('bb', 2, 1),
('ff', 2, 2),
('cc', 3, NULL),
('bb', NULL, NULL),
('aa', 1, 1),
('dd', 1, NULL);
alter table t1 add index idx(ie1,ie2);
select oref, a, b, (a,b) in (select ie1,ie2 from t1 where oref=t2.oref) Z from t2 where a=3 and b is null ;
oref	a	b	Z
cc	3	NULL	NULL
insert into t2 values ('new1', 10,10);
insert into t1 values ('new1', 1234, 10, NULL);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
select oref, a, b, (a,b) in (select ie1,ie2 from t1 where oref=t2.oref) Z from t2 where a=10 and b=10;
oref	a	b	Z
new1	10	10	NULL
explain
select oref, a, b, (a,b) in (select ie1,ie2 from t1 where oref=t2.oref) z from t2 where a=10 and b=10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	8	12.50	Using where
2	DEPENDENT SUBQUERY	t1	NULL	index_subquery	idx	idx	5	func	4	10.00	Using where; Full scan on NULL key
Warnings:
Note	1276	Field or reference 'test.t2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`oref` AS `oref`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,<in_optimizer>((`test`.`t2`.`a`,`test`.`t2`.`b`),<exists>(<index_lookup>(<cache>(`test`.`t2`.`a`) in t1 on idx checking NULL where ((`test`.`t1`.`oref` = `test`.`t2`.`oref`) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t2`.`a`) = `test`.`t1`.`ie1`) or (`test`.`t1`.`ie1` is null)), true) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t2`.`b`) = `test`.`t1`.`ie2`) or (`test`.`t1`.`ie2` is null)), true)) having (<if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`ie1`), true) and <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`ie2`), true))))) AS `z` from `test`.`t2` where ((`test`.`t2`.`b` = 10) and (`test`.`t2`.`a` = 10))
drop table t1, t2;
create table t1 (oref char(4), grp int, ie int) charset utf8mb4;
insert into t1 (oref, grp, ie) values
('aa', 10, 2),
('aa', 10, 1),
('aa', 20, NULL),
('bb', 10, 3),
('cc', 10, 4),
('cc', 20, NULL),
('ee', 10, NULL),
('ee', 10, NULL),
('ff', 20, 2),
('ff', 20, 1);
create table t2 (oref char(4), a int) charset utf8mb4;
insert into t2 values 
('ee', NULL),
('bb', 2),
('ff', 2),
('cc', 3),
('aa', 1),
('dd', NULL),
('bb', NULL);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
select oref, a, a in (select ie from t1 where oref=t2.oref) Z from t2;
oref	a	Z
ee	NULL	NULL
bb	2	0
ff	2	1
cc	3	NULL
aa	1	1
dd	NULL	0
bb	NULL	NULL
select oref, a from t2 where a in (select ie from t1 where oref=t2.oref);
oref	a
aa	1
ff	2
select oref, a from t2 where a not in (select ie from t1 where oref=t2.oref);
oref	a
bb	2
dd	NULL
select oref, a, a in (select min(ie) from t1 where oref=t2.oref group by grp) Z from t2;
oref	a	Z
ee	NULL	NULL
bb	2	0
ff	2	0
cc	3	NULL
aa	1	1
dd	NULL	0
bb	NULL	NULL
select oref, a from t2 where 
a in (select min(ie) from t1 where oref=t2.oref group by grp);
oref	a
aa	1
select oref, a from t2 where 
a not in (select min(ie) from t1 where oref=t2.oref group by grp);
oref	a
bb	2
ff	2
dd	NULL
update t1 set ie=3 where oref='ff' and ie=1;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
select oref, a, a in (select min(ie) from t1 where oref=t2.oref group by
grp) Z from t2;
oref	a	Z
ee	NULL	NULL
bb	2	0
ff	2	1
cc	3	NULL
aa	1	1
dd	NULL	0
bb	NULL	NULL
select oref, a from t2 where a in (select min(ie) from t1 where
oref=t2.oref group by grp);
oref	a
ff	2
aa	1
select oref, a from t2 where a not in (select min(ie) from t1 where
oref=t2.oref group by grp);
oref	a
bb	2
dd	NULL
select oref, a, a in (select min(ie) from t1 where oref=t2.oref group by
grp having min(ie) > 1) Z from t2;
oref	a	Z
ee	NULL	0
bb	2	0
ff	2	1
cc	3	0
aa	1	0
dd	NULL	0
bb	NULL	NULL
select oref, a from t2 where a in (select min(ie) from t1 where
oref=t2.oref group by grp having min(ie) > 1);
oref	a
ff	2
select oref, a from t2 where a not in (select min(ie) from t1 where
oref=t2.oref group by grp having min(ie) > 1);
oref	a
ee	NULL
bb	2
cc	3
aa	1
dd	NULL
alter table t1 add index idx(ie);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select oref, a, a in (select ie from t1 where oref=t2.oref) z from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	index_subquery	idx	idx	5	func	4	10.00	Using where; Full scan on NULL key
Warnings:
Note	1276	Field or reference 'test.t2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`oref` AS `oref`,`test`.`t2`.`a` AS `a`,<in_optimizer>(`test`.`t2`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t2`.`a`) in t1 on idx checking NULL where ((`test`.`t1`.`oref` = `test`.`t2`.`oref`) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t2`.`a`) = `test`.`t1`.`ie`) or (`test`.`t1`.`ie` is null)), true)) having <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`ie`), true)))) AS `z` from `test`.`t2`
select oref, a, a in (select ie from t1 where oref=t2.oref) z from t2;
oref	a	z
ee	NULL	NULL
bb	2	0
ff	2	1
cc	3	NULL
aa	1	1
dd	NULL	0
bb	NULL	NULL
select oref, a from t2 where a in (select ie from t1 where oref=t2.oref);
oref	a
aa	1
ff	2
select oref, a from t2 where a not in (select ie from t1 where oref=t2.oref);
oref	a
bb	2
dd	NULL
alter table t1 drop index idx;
alter table t1 add index idx(oref,ie);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select oref, a, a in (select ie from t1 where oref=t2.oref) z from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	ref_or_null	idx	idx	22	test.t2.oref,func	2	100.00	Using where; Using index; Full scan on NULL key
Warnings:
Note	1276	Field or reference 'test.t2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`oref` AS `oref`,`test`.`t2`.`a` AS `a`,<in_optimizer>(`test`.`t2`.`a`,<exists>(/* select#2 */ select `test`.`t1`.`ie` from `test`.`t1` where ((`test`.`t1`.`oref` = `test`.`t2`.`oref`) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t2`.`a`) = `test`.`t1`.`ie`) or (`test`.`t1`.`ie` is null)), true)) having <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`ie`), true))) AS `z` from `test`.`t2`
select oref, a, a in (select ie from t1 where oref=t2.oref) z from t2;
oref	a	z
ee	NULL	NULL
bb	2	0
ff	2	1
cc	3	NULL
aa	1	1
dd	NULL	0
bb	NULL	NULL
select oref, a from t2 where a in (select ie from t1 where oref=t2.oref);
oref	a
ff	2
aa	1
select oref, a from t2 where a not in (select ie from t1 where oref=t2.oref);
oref	a
bb	2
dd	NULL
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
explain 
select oref, a, 
a in (select min(ie) from t1 where oref=t2.oref 
group by grp having min(ie) > 1) z 
from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	ref	idx	idx	17	test.t2.oref	2	100.00	Using temporary
Warnings:
Note	1276	Field or reference 'test.t2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`oref` AS `oref`,`test`.`t2`.`a` AS `a`,<in_optimizer>(`test`.`t2`.`a`,<exists>(/* select#2 */ select min(`test`.`t1`.`ie`) from `test`.`t1` where (`test`.`t1`.`oref` = `test`.`t2`.`oref`) group by `test`.`t1`.`grp` having ((min(`test`.`t1`.`ie`) > 1) and <if>(outer_field_is_not_null, (<cache>(`test`.`t2`.`a`) = <ref_null_helper>(min(`test`.`t1`.`ie`))), true)))) AS `z` from `test`.`t2`
select oref, a, 
a in (select min(ie) from t1 where oref=t2.oref 
group by grp having min(ie) > 1) z 
from t2;
oref	a	z
ee	NULL	0
bb	2	0
ff	2	1
cc	3	0
aa	1	0
dd	NULL	0
bb	NULL	NULL
select oref, a from t2 where a in (select min(ie) from t1 where oref=t2.oref 
group by grp having min(ie) > 1);
oref	a
ff	2
select oref, a from t2 where a not in (select min(ie) from t1 where oref=t2.oref 
group by grp having min(ie) > 1);
oref	a
ee	NULL
bb	2
cc	3
aa	1
dd	NULL
drop table t1,t2;
create table t1 (oref char(4), grp int, ie1 int, ie2 int);
insert into t1 (oref, grp, ie1, ie2) values
('aa', 10, 2, 1),
('aa', 10, 1, 1),
('aa', 20, 2, 1),
('bb', 10, 3, 1),
('cc', 10, 4, 2),
('cc', 20, 3, 2),
('ee', 10, 2, 1),
('ee', 10, 1, 2),
('ff', 20, 2, 2),
('ff', 20, 1, 2);
create table t2 (oref char(4), a int, b int);
insert into t2 values 
('ee', NULL, 1),
('bb', 2, 1),
('ff', 2, 2),
('cc', 3, NULL),
('bb', NULL, NULL),
('aa', 1, 1),
('dd', 1, NULL);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
select oref, a, b, (a,b) in (select ie1,ie2 from t1 where oref=t2.oref) Z from t2;
oref	a	b	Z
ee	NULL	1	NULL
bb	2	1	0
ff	2	2	1
cc	3	NULL	NULL
bb	NULL	NULL	NULL
aa	1	1	1
dd	1	NULL	0
select oref, a, b from t2 where (a,b) in (select ie1,ie2 from t1 where oref=t2.oref);
oref	a	b
aa	1	1
ff	2	2
select oref, a, b from t2 where (a,b) not in (select ie1,ie2 from t1 where oref=t2.oref);
oref	a	b
bb	2	1
dd	1	NULL
select oref, a, b, 
(a,b) in (select min(ie1),max(ie2) from t1 
where oref=t2.oref group by grp) Z 
from t2;
oref	a	b	Z
ee	NULL	1	0
bb	2	1	0
ff	2	2	0
cc	3	NULL	NULL
bb	NULL	NULL	NULL
aa	1	1	1
dd	1	NULL	0
select oref, a, b from t2 where 
(a,b) in (select min(ie1), max(ie2) from t1 where oref=t2.oref group by grp);
oref	a	b
aa	1	1
select oref, a, b from t2 where
(a,b) not in (select min(ie1), max(ie2) from t1 where oref=t2.oref group by grp);
oref	a	b
ee	NULL	1
bb	2	1
ff	2	2
dd	1	NULL
alter table t1 add index idx(ie1,ie2);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select oref, a, b, (a,b) in (select ie1,ie2 from t1 where oref=t2.oref) z from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	index_subquery	idx	idx	5	func	5	10.00	Using where; Full scan on NULL key
Warnings:
Note	1276	Field or reference 'test.t2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`oref` AS `oref`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,<in_optimizer>((`test`.`t2`.`a`,`test`.`t2`.`b`),<exists>(<index_lookup>(<cache>(`test`.`t2`.`a`) in t1 on idx checking NULL where ((`test`.`t1`.`oref` = `test`.`t2`.`oref`) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t2`.`a`) = `test`.`t1`.`ie1`) or (`test`.`t1`.`ie1` is null)), true) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t2`.`b`) = `test`.`t1`.`ie2`) or (`test`.`t1`.`ie2` is null)), true)) having (<if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`ie1`), true) and <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`ie2`), true))))) AS `z` from `test`.`t2`
select oref, a, b, (a,b) in (select ie1,ie2 from t1 where oref=t2.oref) z from t2;
oref	a	b	z
ee	NULL	1	NULL
bb	2	1	0
ff	2	2	1
cc	3	NULL	NULL
bb	NULL	NULL	NULL
aa	1	1	1
dd	1	NULL	0
select oref, a, b from t2 where (a,b) in (select ie1,ie2 from t1 where oref=t2.oref);
oref	a	b
aa	1	1
ff	2	2
select oref, a, b from t2 where (a,b) not in (select ie1,ie2 from t1 where oref=t2.oref);
oref	a	b
bb	2	1
dd	1	NULL
explain
select oref, a, b, (a,b) in (select ie1,ie2 from t1 where oref=t2.oref) z from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	index_subquery	idx	idx	5	func	5	10.00	Using where; Full scan on NULL key
Warnings:
Note	1276	Field or reference 'test.t2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`oref` AS `oref`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,<in_optimizer>((`test`.`t2`.`a`,`test`.`t2`.`b`),<exists>(<index_lookup>(<cache>(`test`.`t2`.`a`) in t1 on idx checking NULL where ((`test`.`t1`.`oref` = `test`.`t2`.`oref`) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t2`.`a`) = `test`.`t1`.`ie1`) or (`test`.`t1`.`ie1` is null)), true) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t2`.`b`) = `test`.`t1`.`ie2`) or (`test`.`t1`.`ie2` is null)), true)) having (<if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`ie1`), true) and <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`ie2`), true))))) AS `z` from `test`.`t2`
drop table t1,t2;
create table t1 (oref char(4), grp int, ie int primary key);
insert into t1 (oref, grp, ie) values
('aa', 10, 2),
('aa', 10, 1),
('bb', 10, 3),
('cc', 10, 4),
('cc', 20, 5),
('cc', 10, 6);
create table t2 (oref char(4), a int);
insert into t2 values 
('ee', NULL),
('bb', 2),
('cc', 5),
('cc', 2),
('cc', NULL),
('aa', 1),
('bb', NULL);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
explain select oref, a, a in (select ie from t1 where oref=t2.oref) z from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	unique_subquery	PRIMARY	PRIMARY	4	func	1	16.67	Using where; Full scan on NULL key
Warnings:
Note	1276	Field or reference 'test.t2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`oref` AS `oref`,`test`.`t2`.`a` AS `a`,<in_optimizer>(`test`.`t2`.`a`,<exists>(<primary_index_lookup>(<cache>(`test`.`t2`.`a`) in t1 on PRIMARY where ((`test`.`t1`.`oref` = `test`.`t2`.`oref`) and <if>(outer_field_is_not_null, (<cache>(`test`.`t2`.`a`) = `test`.`t1`.`ie`), true))))) AS `z` from `test`.`t2`
select oref, a, a in (select ie from t1 where oref=t2.oref) z from t2;
oref	a	z
ee	NULL	0
bb	2	0
cc	5	1
cc	2	0
cc	NULL	NULL
aa	1	1
bb	NULL	NULL
select oref, a from t2 where a in (select ie from t1 where oref=t2.oref);
oref	a
aa	1
cc	5
select oref, a from t2 where a not in (select ie from t1 where oref=t2.oref);
oref	a
ee	NULL
bb	2
cc	2
explain 
select oref, a, a in (select min(ie) from t1 where oref=t2.oref group by grp) z from t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	6	16.67	Using where; Using temporary
Warnings:
Note	1276	Field or reference 'test.t2.oref' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`oref` AS `oref`,`test`.`t2`.`a` AS `a`,<in_optimizer>(`test`.`t2`.`a`,<exists>(/* select#2 */ select min(`test`.`t1`.`ie`) from `test`.`t1` where (`test`.`t1`.`oref` = `test`.`t2`.`oref`) group by `test`.`t1`.`grp` having <if>(outer_field_is_not_null, (<cache>(`test`.`t2`.`a`) = <ref_null_helper>(min(`test`.`t1`.`ie`))), true))) AS `z` from `test`.`t2`
select oref, a, a in (select min(ie) from t1 where oref=t2.oref group by grp) z from t2;
oref	a	z
ee	NULL	0
bb	2	0
cc	5	1
cc	2	0
cc	NULL	NULL
aa	1	1
bb	NULL	NULL
drop table t1,t2;
create table t1 (a int, b int);
insert into t1 values (0,0), (2,2), (3,3);
create table t2 (a int, b int);
insert into t2 values (1,1), (3,3);
select a, b, (a,b) in (select a, min(b) from t2 group by a) Z from t1;
a	b	Z
0	0	0
2	2	0
3	3	1
insert into t2 values (NULL,4);
select a, b, (a,b) in (select a, min(b) from t2 group by a) Z from t1;
a	b	Z
0	0	0
2	2	0
3	3	1
drop table t1,t2;
CREATE TABLE t1 (a int, b INT, c CHAR(10) NOT NULL, PRIMARY KEY (a, b));
INSERT INTO t1 VALUES (1,1,'a'), (1,2,'b'), (1,3,'c'), (1,4,'d'), (1,5,'e'),
(2,1,'f'), (2,2,'g'), (2,3,'h'), (3,4,'i'),(3,3,'j'), (3,2,'k'), (3,1,'l'),
(1,9,'m');
CREATE TABLE t2 (a int, b INT, c CHAR(10) NOT NULL, PRIMARY KEY (a, b));
INSERT INTO t2 SELECT * FROM t1;
SELECT a, MAX(b), (SELECT t.c FROM t1 AS t WHERE t1.a=t.a AND t.b=MAX(t1.b))
as test FROM t1 GROUP BY a;
a	MAX(b)	test
1	9	m
2	3	h
3	4	i
SELECT * FROM t1 GROUP by t1.a
HAVING (MAX(t1.b) > (SELECT MAX(t2.b) FROM t2 WHERE t2.c < t1.c
HAVING MAX(t2.b+t1.a) < 10));
a	b	c
SELECT a,b,c FROM t1 WHERE b in (9,3,4) ORDER BY b,c;
a	b	c
1	3	c
2	3	h
3	3	j
1	4	d
3	4	i
1	9	m
SELECT a, MAX(b),
(SELECT COUNT(DISTINCT t.c) FROM t1 AS t WHERE t1.a=t.a AND t.b=MAX(t1.b) 
LIMIT 1) 
as cnt, 
(SELECT t.b FROM t1 AS t WHERE t1.a=t.a AND t.b=MAX(t1.b) LIMIT 1) 
as t_b,
(SELECT t.c FROM t1 AS t WHERE t1.a=t.a AND t.b=MAX(t1.b) LIMIT 1) 
as t_b,
(SELECT t.c FROM t1 AS t WHERE t1.a=t.a AND t.b=MAX(t1.b) ORDER BY t.c LIMIT 1)
as t_b
FROM t1 GROUP BY a;
a	MAX(b)	cnt	t_b	t_b	t_b
1	9	1	9	m	m
2	3	1	3	h	h
3	4	1	4	i	i
SELECT a, MAX(b),
(SELECT t.c FROM t1 AS t WHERE t1.a=t.a AND t.b=MAX(t1.b) LIMIT 1) as test 
FROM t1 GROUP BY a;
a	MAX(b)	test
1	9	m
2	3	h
3	4	i
DROP TABLE t1, t2;
CREATE TABLE t1 (a int);
CREATE TABLE t2 (b int, PRIMARY KEY(b));
INSERT INTO t1 VALUES (1), (NULL), (4);
INSERT INTO t2 VALUES (3), (1),(2), (5), (4), (7), (6);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN
SELECT a FROM t1, t2 WHERE a=b AND (b NOT IN (SELECT a FROM t1));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
1	PRIMARY	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.a	1	100.00	Using where; Using index
2	SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`b` = `test`.`t1`.`a`) and <in_optimizer>(`test`.`t2`.`b`,`test`.`t2`.`b` in ( <materialize> (/* select#2 */ select `test`.`t1`.`a` from `test`.`t1` where true having true ), <primary_index_lookup>(`test`.`t2`.`b` in <temporary table> on <auto_distinct_key> where ((`test`.`t2`.`b` = `<materialized_subquery>`.`a`)))) is false))
SELECT a FROM t1, t2 WHERE a=b AND (b NOT IN (SELECT a FROM t1));
a
SELECT a FROM t1, t2 WHERE a=b AND (b NOT IN (SELECT a FROM t1 WHERE a > 4));
a
1
4
DROP TABLE t1,t2;
CREATE TABLE t1 (id int);
CREATE TABLE t2 (id int PRIMARY KEY);
CREATE TABLE t3 (id int PRIMARY KEY, name varchar(10));
INSERT INTO t1 VALUES (2), (NULL), (3), (1);
INSERT INTO t2 VALUES (234), (345), (457);
INSERT INTO t3 VALUES (222,'bbb'), (333,'ccc'), (111,'aaa');
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
EXPLAIN
SELECT * FROM t1
WHERE t1.id NOT IN (SELECT t2.id FROM t2,t3 
WHERE t3.name='xxx' AND t2.id=t3.id);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	Using where
2	SUBQUERY	t3	NULL	ALL	PRIMARY	NULL	NULL	NULL	3	33.33	Using where
2	SUBQUERY	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.id	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`id`,`test`.`t1`.`id` in ( <materialize> (/* select#2 */ select `test`.`t2`.`id` from `test`.`t2` join `test`.`t3` where ((`test`.`t3`.`name` = 'xxx') and (`test`.`t2`.`id` = `test`.`t3`.`id`)) ), <primary_index_lookup>(`test`.`t1`.`id` in <temporary table> on <auto_distinct_key> where ((`test`.`t1`.`id` = `<materialized_subquery>`.`id`)))) is false)
SELECT * FROM t1
WHERE t1.id NOT IN (SELECT t2.id FROM t2,t3 
WHERE t3.name='xxx' AND t2.id=t3.id);
id
2
NULL
3
1
SELECT (t1.id IN (SELECT t2.id FROM t2,t3 
WHERE t3.name='xxx' AND t2.id=t3.id)) AS x
FROM t1;
x
0
0
0
0
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (a INT NOT NULL);
INSERT INTO t1 VALUES (1),(-1), (65),(66);
CREATE TABLE t2 (a INT UNSIGNED NOT NULL PRIMARY KEY);
INSERT INTO t2 VALUES (65),(66);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
SELECT a FROM t1 WHERE a NOT IN (65,66);
a
1
-1
SELECT a FROM t1 WHERE a NOT IN (SELECT a FROM t2);
a
1
-1
EXPLAIN SELECT a FROM t1 WHERE a NOT IN (SELECT a FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.a	1	100.00	Using where; Not exists; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` anti join (`test`.`t2`) on((`test`.`t1`.`a` = `test`.`t2`.`a`)) where true
DROP TABLE t1, t2;
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES(1);
CREATE TABLE t2 (placeholder CHAR(11));
INSERT INTO t2 VALUES("placeholder");
SELECT ROW(1, 2) IN (SELECT t1.a, 2)         FROM t1 GROUP BY t1.a;
ROW(1, 2) IN (SELECT t1.a, 2)
1
SELECT ROW(1, 2) IN (SELECT t1.a, 2 FROM t2) FROM t1 GROUP BY t1.a;
ROW(1, 2) IN (SELECT t1.a, 2 FROM t2)
1
DROP TABLE t1, t2;
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1),(2),(3);
CREATE TABLE t2 SELECT * FROM t1;
SELECT 1 FROM t1 WHERE t1.a NOT IN (SELECT 1 FROM t1, t2 WHERE 0);
1
1
1
1
DROP TABLE t1, t2;
create table t1 (a int, b decimal(13, 3));
insert into t1 values (1, 0.123);
select a, (select max(b) from t1) into outfile "subselect.out.file.1" from t1;
delete from t1;
load data infile "subselect.out.file.1" into table t1;
select * from t1;
a	b
1	0.123
drop table t1;
CREATE TABLE t1 (
pk INT PRIMARY KEY,
int_key INT,
varchar_key VARCHAR(5) UNIQUE,
varchar_nokey VARCHAR(5)
);
INSERT INTO t1 VALUES (9, 7,NULL,NULL), (10,8,'p' ,'p');
SELECT varchar_nokey
FROM t1
WHERE NULL NOT IN (
SELECT INNR.pk FROM t1 AS INNR2
LEFT JOIN t1 AS INNR ON ( INNR2.int_key = INNR.int_key )
WHERE INNR.varchar_key > 'n{'
);
varchar_nokey
DROP TABLE t1;
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (11);
# 2nd and 3rd columns should be same
SELECT a, ROW(11, 12) = (SELECT a, 22), ROW(11, 12) IN (SELECT a, 22) FROM t1 GROUP BY t1.a;
a	ROW(11, 12) = (SELECT a, 22)	ROW(11, 12) IN (SELECT a, 22)
1	0	0
2	0	0
11	0	0
SELECT a, ROW(11, 12) = (SELECT a, 12), ROW(11, 12) IN (SELECT a, 12) FROM t1 GROUP BY t1.a;
a	ROW(11, 12) = (SELECT a, 12)	ROW(11, 12) IN (SELECT a, 12)
1	0	0
2	0	0
11	1	1
SELECT a, ROW(11, 12) = (SELECT a, 22), ROW(11, 12) IN (SELECT a, 22) FROM t1;
a	ROW(11, 12) = (SELECT a, 22)	ROW(11, 12) IN (SELECT a, 22)
1	0	0
2	0	0
11	0	0
SELECT a, ROW(11, 12) = (SELECT a, 12), ROW(11, 12) IN (SELECT a, 12) FROM t1;
a	ROW(11, 12) = (SELECT a, 12)	ROW(11, 12) IN (SELECT a, 12)
1	0	0
2	0	0
11	1	1
SELECT a AS x, ROW(11, 12) = (SELECT MAX(x), 22), ROW(11, 12) IN (SELECT MAX(x), 22) FROM t1;
x	ROW(11, 12) = (SELECT MAX(x), 22)	ROW(11, 12) IN (SELECT MAX(x), 22)
1	0	0
2	0	0
11	0	0
SELECT a AS x, ROW(11, 12) = (SELECT MAX(x), 12), ROW(11, 12) IN (SELECT MAX(x), 12) FROM t1;
x	ROW(11, 12) = (SELECT MAX(x), 12)	ROW(11, 12) IN (SELECT MAX(x), 12)
1	0	0
2	0	0
11	1	1
DROP TABLE t1;
# both columns should be same
SELECT ROW(1,2) = (SELECT NULL, NULL), ROW(1,2) IN (SELECT NULL, NULL);
ROW(1,2) = (SELECT NULL, NULL)	ROW(1,2) IN (SELECT NULL, NULL)
NULL	NULL
SELECT ROW(1,2) = (SELECT   1,  NULL), ROW(1,2) IN (SELECT    1, NULL);
ROW(1,2) = (SELECT   1,  NULL)	ROW(1,2) IN (SELECT    1, NULL)
NULL	NULL
SELECT ROW(1,2) = (SELECT NULL,    2), ROW(1,2) IN (SELECT NULL,    2);
ROW(1,2) = (SELECT NULL,    2)	ROW(1,2) IN (SELECT NULL,    2)
NULL	NULL
SELECT ROW(1,2) = (SELECT NULL,    1), ROW(1,2) IN (SELECT NULL,    1);
ROW(1,2) = (SELECT NULL,    1)	ROW(1,2) IN (SELECT NULL,    1)
0	0
SELECT ROW(1,2) = (SELECT    1,    1), ROW(1,2) IN (SELECT    1,    1);
ROW(1,2) = (SELECT    1,    1)	ROW(1,2) IN (SELECT    1,    1)
0	0
SELECT ROW(1,2) = (SELECT    1,    2), ROW(1,2) IN (SELECT    1,    2);
ROW(1,2) = (SELECT    1,    2)	ROW(1,2) IN (SELECT    1,    2)
1	1
CREATE TABLE t1 (a INT, b INT, c INT);
INSERT INTO t1 VALUES (1,1,1), (1,1,1);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN
SELECT cc FROM 
( SELECT 
(SELECT COUNT(a) FROM 
(SELECT COUNT(b) FROM t1) AS x GROUP BY c
) AS cc FROM t1 GROUP BY b
) AS y;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SHOW WARNINGS;
Level	Code	Message
Note	1276	Field or reference 'test.t1.a' of SELECT #3 was resolved in SELECT #2
Note	1276	Field or reference 'test.t1.c' of SELECT #3 was resolved in SELECT #2
Error	1055	Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
DROP TABLE t1;
#
# Bug #46791: Assertion failed:(table->key_read==0),function unknown
#    function,file sql_base.cc
#
CREATE TABLE t1 (a INT, b INT, KEY(a));
INSERT INTO t1 VALUES (1,1),(2,2);
CREATE TABLE t2 LIKE t1;
INSERT INTO t2 VALUES (1,1),(2,2);
CREATE TABLE t3 LIKE t1;
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
# should have 1 impossible where and 2 dependent subqueries
EXPLAIN
SELECT COUNT(*) FROM t1
WHERE NOT EXISTS (SELECT 1 FROM t2 WHERE 1 = (SELECT MIN(t2.b) FROM t3))
ORDER BY COUNT(*);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	index	NULL	a	5	NULL	2	100.00	Using index
2	SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using where
3	DEPENDENT SUBQUERY	t3	NULL	index	NULL	a	5	NULL	1	100.00	Using index
Warnings:
Note	1276	Field or reference 'test.t2.b' of SELECT #3 was resolved in SELECT #2
Note	1003	/* select#1 */ select count(0) AS `COUNT(*)` from `test`.`t1` where true
# should not crash the next statement
SELECT COUNT(*) FROM t1
WHERE NOT EXISTS (SELECT 1 FROM t2 WHERE 1 = (SELECT MIN(t2.b) FROM t3))
ORDER BY COUNT(*);
COUNT(*)
2
# should not crash: the crash is caused by the previous statement
SELECT 1;
1
1
DROP TABLE t1,t2,t3;
#
# Bug #47106: Crash / segfault on adding EXPLAIN to a non-crashing 
# query
#
CREATE TABLE t1 (
a INT,
b INT,
PRIMARY KEY (a),
KEY b (b)
);
INSERT INTO t1 VALUES (1, 1), (2, 1);
CREATE TABLE t2 LIKE t1;
INSERT INTO t2 SELECT * FROM t1;
CREATE TABLE t3 LIKE t1;
INSERT INTO t3 SELECT * FROM t1;
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
# Should not crash.
# Should have 1 impossible where and 2 dependent subqs.
EXPLAIN
SELECT
(SELECT 1 FROM t1,t2 WHERE t2.b > t3.b)
FROM t3 WHERE 1 = 0 GROUP BY 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
2	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Not optimized, outer query is empty
Warnings:
Note	1276	Field or reference 'test.t3.b' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select (/* select#2 */ select 1 from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`b` > `test`.`t3`.`b`)) AS `(SELECT 1 FROM t1,t2 WHERE t2.b > t3.b)` from `test`.`t3` where false group by (/* select#2 */ select 1 from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`b` > `test`.`t3`.`b`))
# should return 0 rows
SELECT
(SELECT 1 FROM t1,t2 WHERE t2.b > t3.b)
FROM t3 WHERE 1 = 0 GROUP BY 1;
(SELECT 1 FROM t1,t2 WHERE t2.b > t3.b)
DROP TABLE t1,t2,t3;
#
# Bug#12329653 
# EXPLAIN, UNION, PREPARED STATEMENT, CRASH, SQL_FULL_GROUP_BY
#
CREATE TABLE t1(a1 int);
INSERT INTO t1 VALUES (1),(2);
SELECT @@session.sql_mode INTO @old_sql_mode;
SET SESSION sql_mode='ONLY_FULL_GROUP_BY';
SELECT 1 FROM t1 WHERE 1 < SOME (SELECT a1 FROM t1);
1
1
1
PREPARE stmt FROM 
'SELECT 1 UNION ALL 
SELECT 1 FROM t1
ORDER BY
(SELECT 1 FROM t1 AS t1_0  
  WHERE 1 < SOME (SELECT a1 FROM t1)
)' ;
EXECUTE stmt ;
ERROR 21000: Subquery returns more than 1 row
EXECUTE stmt ;
ERROR 21000: Subquery returns more than 1 row
SET SESSION sql_mode=@old_sql_mode;
DEALLOCATE PREPARE stmt;
DROP TABLE t1;
#
# Bug#12763207 - ASSERT IN SUBSELECT::SINGLE_VALUE_TRANSFORMER
#
CREATE TABLE t1(a1 int);
INSERT INTO t1 VALUES (1),(2);
CREATE TABLE t2(a1 int);
INSERT INTO t2 VALUES (3);
SELECT @@session.sql_mode INTO @old_sql_mode;
SET SESSION sql_mode='ONLY_FULL_GROUP_BY';
SELECT 1 FROM t1 WHERE 1 < SOME (SELECT 2 FROM t2);
1
1
1
SELECT 1 FROM t1 WHERE 1 < SOME (SELECT 2.0 FROM t2);
1
1
1
SELECT 1 FROM t1 WHERE 1 < SOME (SELECT 'a' FROM t2);
1
SELECT 1 FROM t1 WHERE 1 < SOME (SELECT a1 FROM t2);
1
1
1
SET SESSION sql_mode=@old_sql_mode;
DROP TABLE t1, t2;
#
# Bug#12763207 - ASSERT IN SUBSELECT::SINGLE_VALUE_TRANSFORMER
#
create table t2(i int);
insert into t2 values(0);
SELECT @@session.sql_mode INTO @old_sql_mode;
SET SESSION sql_mode='ONLY_FULL_GROUP_BY';
CREATE VIEW v1 AS  
SELECT 'f' FROM t2 UNION SELECT 'x' FROM t2
;
CREATE TABLE t1 (
pk int NOT NULL,
col_varchar_key varchar(1) DEFAULT NULL,
PRIMARY KEY (pk),
KEY col_varchar_key (col_varchar_key)
);
SELECT t1.pk
FROM t1
WHERE t1.col_varchar_key < ALL ( SELECT * FROM v1 )
;
pk
SET SESSION sql_mode=@old_sql_mode;
drop table t2, t1;
drop view v1;
End of 5.0 tests.
create table t_out (subcase char(3),
a1 char(2), b1 char(2), c1 char(2));
create table t_in  (a2 char(2), b2 char(2), c2 char(2));
insert into t_out values ('A.1','2a', NULL, '2a');
insert into t_out values ('A.3', '2a', NULL, '2a');
insert into t_out values ('A.4', '2a', NULL, 'xx');
insert into t_out values ('B.1', '2a', '2a', '2a');
insert into t_out values ('B.2', '2a', '2a', '2a');
insert into t_out values ('B.3', '3a', 'xx', '3a');
insert into t_out values ('B.4', 'xx', '3a', '3a');
insert into t_in values ('1a', '1a', '1a');
insert into t_in values ('2a', '2a', '2a');
insert into t_in values (NULL, '2a', '2a');
insert into t_in values ('3a', NULL, '3a');

Test general IN semantics (not top-level)

case A.1
select subcase,
(a1, b1, c1)     IN (select * from t_in where a2 = 'no_match') pred_in,
(a1, b1, c1) NOT IN (select * from t_in where a2 = 'no_match') pred_not_in
from t_out where subcase = 'A.1';
subcase	pred_in	pred_not_in
A.1	0	1
case A.2 - impossible
case A.3
select subcase,
(a1, b1, c1)     IN (select * from t_in) pred_in,
(a1, b1, c1) NOT IN (select * from t_in) pred_not_in
from t_out where subcase = 'A.3';
subcase	pred_in	pred_not_in
A.3	NULL	NULL
case A.4
select subcase,
(a1, b1, c1)     IN (select * from t_in) pred_in,
(a1, b1, c1) NOT IN (select * from t_in) pred_not_in
from t_out where subcase = 'A.4';
subcase	pred_in	pred_not_in
A.4	0	1
case B.1
select subcase,
(a1, b1, c1)     IN (select * from t_in where a2 = 'no_match') pred_in,
(a1, b1, c1) NOT IN (select * from t_in where a2 = 'no_match') pred_not_in
from t_out where subcase = 'B.1';
subcase	pred_in	pred_not_in
B.1	0	1
case B.2
select subcase,
(a1, b1, c1)     IN (select * from t_in) pred_in,
(a1, b1, c1) NOT IN (select * from t_in) pred_not_in
from t_out where subcase = 'B.2';
subcase	pred_in	pred_not_in
B.2	1	0
case B.3
select subcase,
(a1, b1, c1)     IN (select * from t_in) pred_in,
(a1, b1, c1) NOT IN (select * from t_in) pred_not_in
from t_out where subcase = 'B.3';
subcase	pred_in	pred_not_in
B.3	NULL	NULL
case B.4
select subcase,
(a1, b1, c1)     IN (select * from t_in) pred_in,
(a1, b1, c1) NOT IN (select * from t_in) pred_not_in
from t_out where subcase = 'B.4';
subcase	pred_in	pred_not_in
B.4	0	1

Test IN as top-level predicate, and
as non-top level for cases A.3, B.3 (the only cases with NULL result).

case A.1
select case when count(*) > 0 then 'T' else 'F' end as pred_in from t_out
where subcase = 'A.1' and
(a1, b1, c1) IN (select * from t_in where a1 = 'no_match');
pred_in
F
select case when count(*) > 0 then 'T' else 'F' end as pred_not_in from t_out
where subcase = 'A.1' and
(a1, b1, c1) NOT IN (select * from t_in where a1 = 'no_match');
pred_not_in
T
select case when count(*) > 0 then 'T' else 'F' end as not_pred_in from t_out
where subcase = 'A.1' and
NOT((a1, b1, c1) IN (select * from t_in where a1 = 'no_match'));
not_pred_in
T
case A.3
select case when count(*) > 0 then 'T' else 'F' end as pred_in from t_out
where subcase = 'A.3' and
(a1, b1, c1) IN (select * from t_in);
pred_in
F
select case when count(*) > 0 then 'T' else 'F' end as pred_not_in from t_out
where subcase = 'A.3' and
(a1, b1, c1) NOT IN (select * from t_in);
pred_not_in
F
select case when count(*) > 0 then 'T' else 'F' end as not_pred_in from t_out
where subcase = 'A.3' and
NOT((a1, b1, c1) IN (select * from t_in));
not_pred_in
F
test non-top level result indirectly
select case when count(*) > 0 then 'N' else 'wrong result' end as pred_in from t_out
where subcase = 'A.3' and
((a1, b1, c1) IN (select * from t_in)) is NULL and
((a1, b1, c1) NOT IN (select * from t_in)) is NULL;
pred_in
N
case A.4
select case when count(*) > 0 then 'T' else 'F' end as pred_in from t_out
where subcase = 'A.4' and
(a1, b1, c1) IN (select * from t_in);
pred_in
F
select case when count(*) > 0 then 'T' else 'F' end as pred_not_in from t_out
where subcase = 'A.4' and
(a1, b1, c1) NOT IN (select * from t_in);
pred_not_in
T
select case when count(*) > 0 then 'T' else 'F' end as not_pred_in from t_out
where subcase = 'A.4' and
NOT((a1, b1, c1) IN (select * from t_in));
not_pred_in
T
case B.1
select case when count(*) > 0 then 'T' else 'F' end as pred_in from t_out
where subcase = 'B.1' and
(a1, b1, c1) IN (select * from t_in where a1 = 'no_match');
pred_in
F
select case when count(*) > 0 then 'T' else 'F' end as pred_not_in from t_out
where subcase = 'B.1' and
(a1, b1, c1) NOT IN (select * from t_in where a1 = 'no_match');
pred_not_in
T
select case when count(*) > 0 then 'T' else 'F' end as not_pred_in from t_out
where subcase = 'B.1' and
NOT((a1, b1, c1) IN (select * from t_in where a1 = 'no_match'));
not_pred_in
T
case B.2
select case when count(*) > 0 then 'T' else 'F' end as pred_in from t_out
where subcase = 'B.2' and
(a1, b1, c1) IN (select * from t_in);
pred_in
T
select case when count(*) > 0 then 'T' else 'F' end as pred_not_in from t_out
where subcase = 'B.2' and
(a1, b1, c1) NOT IN (select * from t_in);
pred_not_in
F
select case when count(*) > 0 then 'T' else 'F' end as not_pred_in from t_out
where subcase = 'B.2' and
NOT((a1, b1, c1) IN (select * from t_in));
not_pred_in
F
case B.3
select case when count(*) > 0 then 'T' else 'F' end as pred_in from t_out
where subcase = 'B.3' and
(a1, b1, c1) IN (select * from t_in);
pred_in
F
select case when count(*) > 0 then 'T' else 'F' end as pred_not_in from t_out
where subcase = 'B.3' and
(a1, b1, c1) NOT IN (select * from t_in);
pred_not_in
F
select case when count(*) > 0 then 'T' else 'F' end as not_pred_in from t_out
where subcase = 'B.3' and
NOT((a1, b1, c1) IN (select * from t_in));
not_pred_in
F
test non-top level result indirectly
select case when count(*) > 0 then 'N' else 'wrong result' end as pred_in from t_out
where subcase = 'B.3' and
((a1, b1, c1) IN (select * from t_in)) is NULL and
((a1, b1, c1) NOT IN (select * from t_in)) is NULL;
pred_in
N
case B.4
select case when count(*) > 0 then 'T' else 'F' end as pred_in from t_out
where subcase = 'B.4' and
(a1, b1, c1) IN (select * from t_in);
pred_in
F
select case when count(*) > 0 then 'T' else 'F' end as pred_not_in from t_out
where subcase = 'B.4' and
(a1, b1, c1) NOT IN (select * from t_in);
pred_not_in
T
select case when count(*) > 0 then 'T' else 'F' end as not_pred_in from t_out
where subcase = 'B.4' and
NOT((a1, b1, c1) IN (select * from t_in));
not_pred_in
T
drop table t_out;
drop table t_in;
CREATE TABLE t1 (a INT, b INT);
INSERT INTO t1 VALUES (2,22),(1,11),(2,22);
SELECT a FROM t1 WHERE (SELECT COUNT(b) FROM DUAL) > 0 GROUP BY a;
a
1
2
SELECT a FROM t1 WHERE (SELECT COUNT(b) FROM DUAL) > 1 GROUP BY a;
a
SELECT a FROM t1 t0
WHERE (SELECT COUNT(t0.b) FROM t1 t WHERE t.b>20) GROUP BY a;
a
1
2
SET @@sql_mode='ansi';
SELECT a FROM t1 WHERE (SELECT COUNT(b) FROM DUAL) > 0 GROUP BY a;
ERROR HY000: Invalid use of group function
SELECT a FROM t1 WHERE (SELECT COUNT(b) FROM DUAL) > 1 GROUP BY a;
ERROR HY000: Invalid use of group function
SELECT a FROM t1 t0
WHERE (SELECT COUNT(t0.b) FROM t1 t WHERE t.b>20) GROUP BY a;
ERROR HY000: Invalid use of group function
SET @@sql_mode=default;
DROP TABLE t1;
CREATE TABLE t1 (s1 CHAR(1));
INSERT INTO t1 VALUES ('a');
SELECT * FROM t1 WHERE _utf8mb3'a' = ANY (SELECT s1 FROM t1);
s1
a
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
DROP TABLE t1;
CREATE TABLE t1(c INT, KEY(c));
CREATE TABLE t2(a INT, b INT);
INSERT INTO t2 VALUES (1, 10), (2, NULL);
INSERT INTO t1 VALUES (1), (3);
SELECT * FROM t2 WHERE b NOT IN (SELECT max(t.c) FROM t1, t1 t WHERE t.c>10);
a	b
DROP TABLE t1,t2;
CREATE TABLE t1(pk INT PRIMARY KEY, a INT, INDEX idx(a));
INSERT INTO t1 VALUES (1, 10), (3, 30), (2, 20);
CREATE TABLE t2(pk INT PRIMARY KEY, a INT, b INT, INDEX idxa(a));
INSERT INTO t2 VALUES (2, 20, 700), (1, 10, 200), (4, 10, 100);
SELECT * FROM t1
WHERE EXISTS (SELECT DISTINCT a FROM t2 WHERE t1.a < t2.a ORDER BY b);
pk	a
1	10
DROP TABLE t1,t2;
CREATE TABLE t1 (a INT, b INT, PRIMARY KEY (a), KEY b (b));
INSERT INTO t1 VALUES (1,NULL), (9,NULL);
CREATE TABLE t2 (
a INT,
b INT,
c INT,
d INT,
PRIMARY KEY (a),
UNIQUE KEY b (b,c,d),
KEY b_2 (b),
KEY c (c),
KEY d (d)
);
INSERT INTO t2 VALUES
(43, 2, 11 ,30),
(44, 2, 12 ,30),
(45, 1, 1  ,10000),
(46, 1, 2  ,10000),
(556,1, 32 ,10000);
CREATE TABLE t3 (
a INT,
b INT,
c INT,
PRIMARY KEY (a),
UNIQUE KEY b (b,c),
KEY c (c),
KEY b_2 (b)
);
INSERT INTO t3 VALUES (1,1,1), (2,32,1);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
explain
SELECT t1.a, (SELECT 1 FROM t2 WHERE t2.b=t3.c AND t2.c=t1.a ORDER BY t2.d LIMIT 1) AS incorrect FROM t1, t3 WHERE t3.b=t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	index	PRIMARY	b	5	NULL	2	100.00	Using index
1	PRIMARY	t3	NULL	ref	b,b_2	b	5	test.t1.a	1	100.00	Using index
2	DEPENDENT SUBQUERY	t2	NULL	ref	b,b_2,c	b	10	test.t3.c,test.t1.a	1	100.00	Using index; Using filesort
Warnings:
Note	1276	Field or reference 'test.t3.c' of SELECT #2 was resolved in SELECT #1
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,(/* select#2 */ select 1 from `test`.`t2` where ((`test`.`t2`.`b` = `test`.`t3`.`c`) and (`test`.`t2`.`c` = `test`.`t1`.`a`)) order by `test`.`t2`.`d` limit 1) AS `incorrect` from `test`.`t1` join `test`.`t3` where (`test`.`t3`.`b` = `test`.`t1`.`a`)
SELECT t1.a, (SELECT 1 FROM t2 WHERE t2.b=t3.c AND t2.c=t1.a ORDER BY t2.d LIMIT 1) AS incorrect FROM t1, t3 WHERE t3.b=t1.a;
a	incorrect
1	1
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (id int);
CREATE TABLE t2 (id int, c int);
INSERT INTO t1 (id) VALUES (1);
INSERT INTO t2 (id) VALUES (1);
INSERT INTO t1 (id) VALUES (1);
INSERT INTO t2 (id) VALUES (1);
CREATE VIEW v1 AS
SELECT t2.c AS c FROM t1, t2
WHERE t1.id=t2.id AND 1 IN (SELECT id FROM t1) WITH CHECK OPTION;
UPDATE v1 SET c=1;
CREATE VIEW v2 (a,b) AS
SELECT t2.id, t2.c AS c FROM t1, t2
WHERE t1.id=t2.id AND 1 IN (SELECT id FROM t1) WITH CHECK OPTION;
INSERT INTO v2(a,b) VALUES (2,2);
ERROR HY000: CHECK OPTION failed 'test.v2'
INSERT INTO v2(a,b) VALUES (1,2);
SELECT * FROM v1;
c
1
1
1
1
2
2
CREATE VIEW v3 AS
SELECT t2.c AS c FROM t2
WHERE 1 IN (SELECT id FROM t1) WITH CHECK OPTION;
DELETE FROM v3;
DROP VIEW v1,v2,v3;
DROP TABLE t1,t2;
#
# Bug#37822 Correlated subquery with IN and IS UNKNOWN provides wrong result
#
create table t1(id integer primary key, g integer, v integer, s char(1));
create table t2(id integer primary key, g integer, v integer, s char(1));
insert into t1 values
(10, 10, 10,   'l'),
(20, 20, 20,   'l'),
(40, 40, 40,   'l'),
(41, 40, null, 'l'),
(50, 50, 50,   'l'),
(51, 50, null, 'l'),
(60, 60, 60,   'l'),
(61, 60, null, 'l'),
(70, 70, 70,   'l'),
(90, 90, null, 'l');
insert into t2 values
(10, 10, 10,   'r'),
(30, 30, 30,   'r'),
(50, 50, 50,   'r'),
(60, 60, 60,   'r'),
(61, 60, null, 'r'),
(70, 70, 70,   'r'),
(71, 70, null, 'r'),
(80, 80, 80,   'r'),
(81, 80, null, 'r'),
(100,100,null, 'r');
select *
from t1
where v in(select v
from t2
where t1.g=t2.g) is unknown;
id	g	v	s
51	50	NULL	l
61	60	NULL	l
drop table t1, t2;
#
# Bug#33204: INTO is allowed in subselect, causing inconsistent results
#
CREATE TABLE t1( a INT );
INSERT INTO t1 VALUES (1),(2);
CREATE TABLE t2( a INT, b INT );
SELECT * 
FROM (SELECT a INTO @var FROM t1 WHERE a = 2) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * 
FROM (SELECT a INTO OUTFILE 'file' FROM t1 WHERE a = 2) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * 
FROM (SELECT a INTO DUMPFILE 'file' FROM t1 WHERE a = 2) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM ( 
SELECT 1 a 
UNION 
SELECT a INTO @var FROM t1 WHERE a = 2 
) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM ( 
SELECT 1 a 
UNION 
SELECT a INTO OUTFILE 'file' FROM t1 WHERE a = 2 
) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM ( 
SELECT 1 a 
UNION 
SELECT a INTO DUMPFILE 'file' FROM t1 WHERE a = 2 
) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM (SELECT a FROM t1 WHERE a = 2) t1a;
a
2
SELECT * FROM ( 
SELECT a FROM t1 WHERE a = 2 
UNION 
SELECT a FROM t1 WHERE a = 2 
) t1a;
a
2
SELECT * FROM ( 
SELECT 1 a 
UNION 
SELECT a FROM t1 WHERE a = 2 
UNION 
SELECT a FROM t1 WHERE a = 2 
) t1a;
a
1
2
SELECT * FROM ((SELECT 1 a) UNION SELECT 1 a);
ERROR 42000: Every derived table must have its own alias
SELECT * FROM (SELECT 1 a UNION (SELECT 1 a)) alias;
a
1
SELECT * FROM (SELECT 1 UNION SELECT 1) t1a;
1
1
SELECT * FROM ((SELECT 1 a INTO @a)) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM ((SELECT 1 a INTO OUTFILE 'file' )) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM ((SELECT 1 a INTO DUMPFILE 'file' )) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM (SELECT 1 a UNION (SELECT 1 a INTO @a)) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM (SELECT 1 a UNION (SELECT 1 a INTO DUMPFILE 'file' )) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM (SELECT 1 a UNION (SELECT 1 a INTO OUTFILE 'file' )) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM (SELECT 1 a UNION ((SELECT 1 a INTO @a))) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM (SELECT 1 a UNION ((SELECT 1 a INTO DUMPFILE 'file' ))) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM (SELECT 1 a UNION ((SELECT 1 a INTO OUTFILE 'file' ))) t1a;
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM (SELECT 1 a ORDER BY a) t1a;
a
1
SELECT * FROM (SELECT 1 a UNION SELECT 1 a ORDER BY a) t1a;
a
1
SELECT * FROM (SELECT 1 a UNION SELECT 1 a LIMIT 1) t1a;
a
1
SELECT * FROM (SELECT 1 a UNION SELECT 1 a ORDER BY a LIMIT 1) t1a;
a
1
SELECT * FROM t1 JOIN  (SELECT 1 UNION SELECT 1)  alias ON 1;
a	1
1	1
2	1
SELECT * FROM t1 JOIN ((SELECT 1 UNION SELECT 1)) alias ON 1;
a	1
1	1
2	1
SELECT * FROM t1 JOIN  (SELECT 1 UNION SELECT 1)  ON 1;
ERROR 42000: Every derived table must have its own alias
SELECT * FROM t1 JOIN ((SELECT 1 UNION SELECT 1)) ON 1;
ERROR 42000: Every derived table must have its own alias
SELECT * FROM t1 JOIN  (t1 t1a UNION SELECT 1)  ON 1;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'UNION SELECT 1)  ON 1' at line 1
SELECT * FROM t1 JOIN ((t1 t1a UNION SELECT 1)) ON 1;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'UNION SELECT 1)) ON 1' at line 1
SELECT * FROM t1 JOIN  (t1 t1a)  t1a ON 1;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 't1a ON 1' at line 1
SELECT * FROM t1 JOIN ((t1 t1a)) t1a ON 1;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 't1a ON 1' at line 1
SELECT * FROM t1 JOIN  (t1 t1a)  ON 1;
a	a
1	1
1	2
2	1
2	2
SELECT * FROM t1 JOIN ((t1 t1a)) ON 1;
a	a
1	1
1	2
2	1
2	2
SELECT * FROM (t1 t1a);
a
1
2
SELECT * FROM ((t1 t1a));
a
1
2
SELECT * FROM t1 JOIN  (SELECT 1 t1a) alias ON 1;
a	t1a
1	1
2	1
SELECT * FROM t1 JOIN ((SELECT 1 t1a)) alias ON 1;
a	t1a
1	1
2	1
SELECT * FROM t1 JOIN  (SELECT 1 a)  a ON 1;
a	a
1	1
2	1
SELECT * FROM t1 JOIN ((SELECT 1 a)) a ON 1;
a	a
1	1
2	1
SELECT * FROM (t1 JOIN (SELECT 1) t1a1 ON 1) t1a2;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 't1a2' at line 1
SELECT * FROM t1 WHERE a = ALL ( SELECT 1 );
a
1
SELECT * FROM t1 WHERE a = ALL ( SELECT 1 UNION SELECT 1 );
a
1
SELECT * FROM t1 WHERE a = ANY ( SELECT 3 UNION SELECT 1 );
a
1
SELECT * FROM t1 WHERE a = ANY ( SELECT 1 UNION SELECT 1 INTO @a);
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM t1 WHERE a = ANY ( SELECT 1 UNION SELECT 1 INTO OUTFILE 'file' );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM t1 WHERE a = ANY ( SELECT 1 UNION SELECT 1 INTO DUMPFILE 'file' );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM t1 WHERE a = ( SELECT 1 );
a
1
SELECT * FROM t1 WHERE a = ( SELECT 1 UNION SELECT 1 );
a
1
SELECT * FROM t1 WHERE a = ( SELECT 1 INTO @a);
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM t1 WHERE a = ( SELECT 1 INTO OUTFILE 'file' );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM t1 WHERE a = ( SELECT 1 INTO DUMPFILE 'file' );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM t1 WHERE a = ( SELECT 1 UNION SELECT 1 INTO @a);
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM t1 WHERE a = ( SELECT 1 UNION SELECT 1 INTO OUTFILE 'file' );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM t1 WHERE a = ( SELECT 1 UNION SELECT 1 INTO DUMPFILE 'file' );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT ( SELECT 1 INTO @v );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT ( SELECT 1 INTO OUTFILE 'file' );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT ( SELECT 1 INTO DUMPFILE 'file' );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT ( SELECT 1 UNION SELECT 1 INTO @v );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT ( SELECT 1 UNION SELECT 1 INTO OUTFILE 'file' );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT ( SELECT 1 UNION SELECT 1 INTO DUMPFILE 'file' );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT ( SELECT a FROM t1 WHERE a = 1 ), a FROM t1;
( SELECT a FROM t1 WHERE a = 1 )	a
1	1
1	2
SELECT ( SELECT a FROM t1 WHERE a = 1 UNION SELECT 1 ), a FROM t1;
( SELECT a FROM t1 WHERE a = 1 UNION SELECT 1 )	a
1	1
1	2
SELECT * FROM t2 WHERE (a, b) IN (SELECT a, b FROM t2);
a	b
SELECT 1 UNION ( SELECT 1 UNION SELECT 1 );
1
1
( SELECT 1 UNION SELECT 1 ) UNION SELECT 1;
1
1
SELECT ( SELECT 1 UNION ( SELECT 1 UNION SELECT 1 ) );
( SELECT 1 UNION ( SELECT 1 UNION SELECT 1 ) )
1
SELECT ( ( SELECT 1 UNION SELECT 1 ) UNION SELECT 1;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1
SELECT ( SELECT 1 UNION SELECT 1 UNION SELECT 1 );
( SELECT 1 UNION SELECT 1 UNION SELECT 1 )
1
SELECT ((SELECT 1 UNION SELECT 1 UNION SELECT 1));
((SELECT 1 UNION SELECT 1 UNION SELECT 1))
1
SELECT * FROM ( SELECT 1 UNION ( SELECT 1 UNION SELECT 1 ) );
ERROR 42000: Every derived table must have its own alias
SELECT * FROM ( ( SELECT 1 UNION SELECT 1 ) UNION SELECT 1 );
ERROR 42000: Every derived table must have its own alias
SELECT * FROM ( ( SELECT 1 UNION SELECT 1 ) UNION SELECT 1 ) a;
1
1
SELECT * FROM ( SELECT 1 UNION SELECT 1 UNION SELECT 1 ) a;
1
1
SELECT * FROM t1 WHERE a =     ( SELECT 1 UNION ( SELECT 1 UNION SELECT 1 ) );
a
1
SELECT * FROM t1 WHERE a = ALL ( SELECT 1 UNION ( SELECT 1 UNION SELECT 1 ) );
a
1
SELECT * FROM t1 WHERE a = ANY ( SELECT 1 UNION ( SELECT 1 UNION SELECT 1 ) );
a
1
SELECT * FROM t1 WHERE a IN    ( SELECT 1 UNION ( SELECT 1 UNION SELECT 1 ) );
a
1
SELECT * FROM t1 WHERE a =     ( ( SELECT 1 UNION SELECT 1 )  UNION SELECT 1 );
a
1
SELECT * FROM t1 WHERE a = ALL ( ( SELECT 1 UNION SELECT 1 )  UNION SELECT 1 );
a
1
SELECT * FROM t1 WHERE a = ANY ( ( SELECT 1 UNION SELECT 1 )  UNION SELECT 1 );
a
1
SELECT * FROM t1 WHERE a IN    ( ( SELECT 1 UNION SELECT 1 )  UNION SELECT 1 );
a
1
SELECT * FROM t1 WHERE a =     ( SELECT 1 UNION SELECT 1 UNION SELECT 1 );
a
1
SELECT * FROM t1 WHERE a = ALL ( SELECT 1 UNION SELECT 1 UNION SELECT 1 );
a
1
SELECT * FROM t1 WHERE a = ANY ( SELECT 1 UNION SELECT 1 UNION SELECT 1 );
a
1
SELECT * FROM t1 WHERE a IN    ( SELECT 1 UNION SELECT 1 UNION SELECT 1 );
a
1
SELECT * FROM t1 WHERE EXISTS ( SELECT 1 UNION SELECT 1 INTO @v );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT EXISTS(SELECT 1+1);
EXISTS(SELECT 1+1)
1
SELECT EXISTS(SELECT 1+1 INTO @test);
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM t1 WHERE a IN ( SELECT 1 UNION SELECT 1 INTO @v );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM t1 WHERE EXISTS ( SELECT 1 INTO @v );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
SELECT * FROM t1 WHERE a IN ( SELECT 1 INTO @v );
ERROR HY000: Misplaced INTO clause, INTO is not allowed inside subqueries, and must be placed at end of UNION clauses.
DROP TABLE t1, t2;
CREATE TABLE t1 (a ENUM('rainbow'));
INSERT INTO t1 VALUES (),(),(),(),();
SELECT 1
FROM t1 GROUP BY (SELECT COUNT(*) FROM t1 ORDER BY AVG(LAST_INSERT_ID()));
1
1
DROP TABLE t1;
CREATE TABLE t1 (a LONGBLOB);
INSERT INTO t1 SET a = 'aaaa';
INSERT INTO t1 SET a = 'aaaa';
SELECT 1 FROM t1 GROUP BY
(SELECT LAST_INSERT_ID() FROM t1 ORDER BY MIN(a) ASC LIMIT 1);
ERROR HY000: Expression #1 of ORDER BY contains aggregate function and applies to the result of a non-aggregated query
DROP TABLE t1;
#
# Bug #49512 : subquery with aggregate function crash 
#   subselect_single_select_engine::exec()
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES();
# should not crash
SELECT 1 FROM t1 WHERE a <> SOME
(
SELECT MAX((SELECT a FROM t1 LIMIT 1)) AS d
FROM t1,t1 a
);
1
DROP TABLE t1;
#
# Bug #45989 take 2 : memory leak after explain encounters an 
# error in the query
#
CREATE TABLE t1(a LONGTEXT);
INSERT INTO t1 VALUES (repeat('a',@@global.max_allowed_packet));
INSERT INTO t1 VALUES (repeat('b',@@global.max_allowed_packet));
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT DISTINCT 1 FROM t1,
(SELECT a AS away FROM t1 GROUP BY a WITH ROLLUP) AS d1  
WHERE t1.a = d1.a;
ERROR 42S22: Unknown column 'd1.a' in 'where clause'
EXPLAIN SELECT DISTINCT 1 FROM t1,
(SELECT DISTINCTROW a AS away FROM t1 GROUP BY a WITH ROLLUP) AS d1  
WHERE t1.a = d1.a;
ERROR 42S22: Unknown column 'd1.a' in 'where clause'
DROP TABLE t1;
create table t0 (a int);
insert into t0 values (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
create table t1 (
a int(11) default null,
b int(11) default null,
key (a)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t1 select A.a+10*(B.a+10*C.a),A.a+10*(B.a+10*C.a) from t0 A, t0 B, t0 C;
create table t2 (a int(11) default null);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t2 values (0),(1);
create table t3 (a int(11) default null);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t3 values (0),(1);
create table t4 (a int(11) default null);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t4 values (0),(1);
create table t5 (a int(11) default null);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t5 values (0),(1),(0),(1);
select * from t2, t3 
where
t2.a < 10 and
t3.a+1 = 2 and
t3.a in (select t1.b from t1
where t1.a+1=t1.a+1 and
t1.a < (select t4.a+10                                  
from t4, t5 limit 2));
ERROR 21000: Subquery returns more than 1 row
drop table t0, t1, t2, t3, t4, t5;
# 
# BUG#48177 - SELECTs with NOT IN subqueries containing NULL 
#             values return too many records
# 
CREATE TABLE t1 (
i1 int DEFAULT NULL,
i2 int DEFAULT NULL
) ;
INSERT INTO t1 VALUES (1,    NULL);
INSERT INTO t1 VALUES (2,    3);
INSERT INTO t1 VALUES (4,    NULL);
INSERT INTO t1 VALUES (4,    0);
INSERT INTO t1 VALUES (NULL, NULL);
CREATE TABLE t2 (
i1 int DEFAULT NULL,
i2 int DEFAULT NULL
) ;
INSERT INTO t2 VALUES (4, NULL);
INSERT INTO t2 VALUES (5, 0);

Data in t1
SELECT i1, i2 FROM t1;
i1	i2
1	NULL
2	3
4	NULL
4	0
NULL	NULL

Data in subquery (should be filtered out)
SELECT i1, i2 FROM t2 ORDER BY i1;
i1	i2
4	NULL
5	0
FLUSH STATUS;

SELECT i1, i2
FROM t1
WHERE (i1, i2) 
NOT IN (SELECT i1, i2 FROM t2);
i1	i2
1	NULL
2	3

# Check that the subquery only has to be evaluated once 
# for all-NULL values even though there are two (NULL,NULL) records
# Baseline:
SHOW STATUS LIKE '%Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	17

INSERT INTO t1 VALUES (NULL, NULL);
FLUSH STATUS;

SELECT i1, i2
FROM t1
WHERE (i1, i2) 
NOT IN (SELECT i1, i2 FROM t2);
i1	i2
1	NULL
2	3

# Handler_read_rnd_next should be one more than baseline 
# (read record from t1, but do not read from t2)
SHOW STATUS LIKE '%Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	18
DROP TABLE t1,t2;
#
# Bug#54568: create view cause Assertion failed: 0, 
# file .\item_subselect.cc, line 836
#
EXPLAIN SELECT 1 LIKE ( 1 IN ( SELECT 1 ) );
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
Warnings:
Note	1249	Select 2 was reduced during optimization
Note	1003	/* select#1 */ select (1 like (1 = 1)) AS `1 LIKE ( 1 IN ( SELECT 1 ) )`
DESCRIBE SELECT 1 LIKE ( 1 IN ( SELECT 1 ) );
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
Warnings:
Note	1249	Select 2 was reduced during optimization
Note	1003	/* select#1 */ select (1 like (1 = 1)) AS `1 LIKE ( 1 IN ( SELECT 1 ) )`
# None of the below should crash
CREATE VIEW v1 AS SELECT 1 LIKE ( 1 IN ( SELECT 1 ) );
CREATE VIEW v2 AS SELECT 1 LIKE '%' ESCAPE ( 1 IN ( SELECT 1 ) );
DROP VIEW v1, v2;
Set up test tables.
CREATE TABLE t1 (
t1_id INT UNSIGNED,
PRIMARY KEY(t1_id)
) Engine=INNODB;
INSERT INTO t1 (t1_id) VALUES (1), (2), (3), (4), (5);
CREATE TABLE t2 SELECT * FROM t1;
CREATE TABLE t3 (
t3_id INT UNSIGNED AUTO_INCREMENT,
t1_id INT UNSIGNED,
amount DECIMAL(16,2),
PRIMARY KEY(t3_id),
KEY(t1_id)
) Engine=INNODB;
INSERT INTO t3 (t1_id, t3_id, amount) 
VALUES (1, 1, 100.00), (2, 2, 200.00), (4, 4, 400.00);
This is the 'inner query' running by itself.
Produces correct results.
SELECT
t1.t1_id,
IFNULL((SELECT SUM(amount) FROM t3 WHERE t3.t1_id=t1.t1_id), 0) AS total_amount
FROM
t1
LEFT JOIN t2 ON t2.t1_id=t1.t1_id
GROUP BY
t1.t1_id
;
t1_id	total_amount
1	100.00
2	200.00
3	0.00
4	400.00
5	0.00
SELECT * FROM (the same inner query)
Produces correct results.
SELECT * FROM (
SELECT
t1.t1_id,
IFNULL((SELECT SUM(amount) FROM t3 WHERE t3.t1_id=t1.t1_id), 0) AS total_amount
FROM
t1
LEFT JOIN t2 ON t2.t1_id=t1.t1_id
GROUP BY
t1.t1_id
) AS t;
t1_id	total_amount
1	100.00
2	200.00
3	0.00
4	400.00
5	0.00
Now make t2.t1_id part of a key.
ALTER TABLE t2 ADD PRIMARY KEY(t1_id);
Same inner query by itself.
Still correct results.
SELECT
t1.t1_id,
IFNULL((SELECT SUM(amount) FROM t3 WHERE t3.t1_id=t1.t1_id), 0) AS total_amount
FROM
t1
LEFT JOIN t2 ON t2.t1_id=t1.t1_id
GROUP BY
t1.t1_id;
t1_id	total_amount
1	100.00
2	200.00
3	0.00
4	400.00
5	0.00
SELECT * FROM (the same inner query), now with indexes on the LEFT JOIN
SELECT * FROM (
SELECT
t1.t1_id,
IFNULL((SELECT SUM(amount) FROM t3 WHERE t3.t1_id=t1.t1_id), 0) AS total_amount
FROM
t1
LEFT JOIN t2 ON t2.t1_id=t1.t1_id
GROUP BY
t1.t1_id
) AS t;
t1_id	total_amount
1	100.00
2	200.00
3	0.00
4	400.00
5	0.00
DROP TABLE t3;
DROP TABLE t2;
DROP TABLE t1;
create table t0 (a int);
insert into t0 values (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
create table t1 (
a int(11) default null,
b int(11) default null,
key (a)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t1 select A.a+10*(B.a+10*C.a),A.a+10*(B.a+10*C.a) from t0 A, t0 B, t0 C;
create table t2 (a int(11) default null);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t2 values (0),(1);
create table t3 (a int(11) default null);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t3 values (0),(1);
create table t4 (a int(11) default null);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t4 values (0),(1);
create table t5 (a int(11) default null);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t5 values (0),(1),(0),(1);
select * from t2, t3 
where
t2.a < 10 and
t3.a+1 = 2 and
t3.a in (select t1.b from t1
where t1.a+1=t1.a+1 and
t1.a < (select t4.a+10                                  
from t4, t5 limit 2));
ERROR 21000: Subquery returns more than 1 row
drop table t0, t1, t2, t3, t4, t5;
# 
# BUG#48177 - SELECTs with NOT IN subqueries containing NULL 
#             values return too many records
# 
CREATE TABLE t1 (
i1 int DEFAULT NULL,
i2 int DEFAULT NULL
) ;
INSERT INTO t1 VALUES (1,    NULL);
INSERT INTO t1 VALUES (2,    3);
INSERT INTO t1 VALUES (4,    NULL);
INSERT INTO t1 VALUES (4,    0);
INSERT INTO t1 VALUES (NULL, NULL);
CREATE TABLE t2 (
i1 int DEFAULT NULL,
i2 int DEFAULT NULL
) ;
INSERT INTO t2 VALUES (4, NULL);
INSERT INTO t2 VALUES (5, 0);

Data in t1
SELECT i1, i2 FROM t1;
i1	i2
1	NULL
2	3
4	NULL
4	0
NULL	NULL

Data in subquery (should be filtered out)
SELECT i1, i2 FROM t2 ORDER BY i1;
i1	i2
4	NULL
5	0
FLUSH STATUS;

SELECT i1, i2
FROM t1
WHERE (i1, i2) 
NOT IN (SELECT i1, i2 FROM t2);
i1	i2
1	NULL
2	3

# Check that the subquery only has to be evaluated once 
# for all-NULL values even though there are two (NULL,NULL) records
# Baseline:
SHOW STATUS LIKE '%Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	17

INSERT INTO t1 VALUES (NULL, NULL);
FLUSH STATUS;

SELECT i1, i2
FROM t1
WHERE (i1, i2) 
NOT IN (SELECT i1, i2 FROM t2);
i1	i2
1	NULL
2	3

# Handler_read_rnd_next should be one more than baseline 
# (read record from t1, but do not read from t2)
SHOW STATUS LIKE '%Handler_read_rnd_next';
Variable_name	Value
Handler_read_rnd_next	18
DROP TABLE t1,t2;
#
# Bug #52711: Segfault when doing EXPLAIN SELECT with 
#  union...order by (select... where...)
#
CREATE TABLE t1 (a VARCHAR(10), FULLTEXT KEY a (a));
INSERT INTO t1 VALUES (1),(2);
CREATE TABLE t2 (b INT);
INSERT INTO t2 VALUES (1),(2);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
# Should not crash
EXPLAIN
SELECT * FROM t2 UNION SELECT * FROM t2
ORDER BY (SELECT * FROM t1 WHERE MATCH(a) AGAINST ('+abc' IN BOOLEAN MODE));
# Should not crash
SELECT * FROM t2 UNION SELECT * FROM t2
ORDER BY (SELECT * FROM t1 WHERE MATCH(a) AGAINST ('+abc' IN BOOLEAN MODE));
DROP TABLE t1,t2;
#
# Bug #58818: Incorrect result for IN/ANY subquery
# with HAVING condition 
#
CREATE TABLE t1(i INT);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t1s(i INT);
INSERT INTO t1s VALUES (10), (20), (30);
CREATE TABLE t2s(i INT);
INSERT INTO t2s VALUES (100), (200), (300);
SELECT * FROM t1
WHERE t1.i NOT IN
(
SELECT STRAIGHT_JOIN t2s.i 
FROM
t1s LEFT OUTER JOIN t2s ON t2s.i = t1s.i
HAVING t2s.i = 999
);
i
1
2
3
SELECT * FROM t1
WHERE t1.I IN
(
SELECT STRAIGHT_JOIN t2s.i 
FROM
t1s LEFT OUTER JOIN t2s ON t2s.i = t1s.i
HAVING t2s.i = 999
) IS UNKNOWN;
i
SELECT * FROM t1
WHERE NOT t1.I = ANY
(
SELECT STRAIGHT_JOIN t2s.i 
FROM
t1s LEFT OUTER JOIN t2s ON t2s.i = t1s.i
HAVING t2s.i = 999
);
i
1
2
3
SELECT * FROM t1
WHERE t1.i = ANY (
SELECT STRAIGHT_JOIN t2s.i 
FROM
t1s LEFT OUTER JOIN t2s ON t2s.i = t1s.i
HAVING t2s.i = 999
) IS UNKNOWN;
i
DROP TABLE t1,t1s,t2s;
#
# Bug #56690  Wrong results with subquery with 
# GROUP BY inside < ANY clause
#
CREATE TABLE t1 (
pk INT NOT NULL PRIMARY KEY,
number INT,
KEY key_number (number)
);
INSERT INTO t1 VALUES (8,8);
CREATE TABLE t2 (
pk INT NOT NULL PRIMARY KEY,
number INT,
KEY key_number (number)
);
INSERT INTO t2 VALUES (1,2);
INSERT INTO t2 VALUES (2,8);
INSERT INTO t2 VALUES (3,NULL);
INSERT INTO t2 VALUES (4,166);
SELECT * FROM t1 WHERE t1.number < ANY(SELECT number FROM t2 GROUP BY number);
pk	number
8	8
SELECT * FROM t1 WHERE t1.number < ANY(SELECT number FROM t2);
pk	number
8	8
DROP TABLE t1,t2;
purge binary logs before (select adddate(current_timestamp(), interval -4 day));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '(select adddate(current_timestamp(), interval -4 day))' at line 1
purge binary logs before adddate(current_timestamp(), interval -4 day);
create table t1(a int,b int,key(a),key(b));
insert into t1(a,b) values (1,2),(2,1),(2,3),(3,4),(5,4),(5,5),
(6,7),(7,4),(5,3);
4
3
2
1
26
25
24
23
22
21
20
19
18
17
16
15
14
13
12
11
10
9
8
7
6
5
4
3
2
1
drop table t1;
End of 5.1 tests
#
# BUG#50257: Missing info in REF column of the EXPLAIN 
#            lines for subselects
#
CREATE TABLE t1 (a INT, b INT, INDEX (a));
INSERT INTO t1 VALUES (3, 10), (2, 20), (7, 10), (5, 20);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK

EXPLAIN SELECT * FROM (SELECT * FROM t1 WHERE a=7) t;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	a	a	5	const	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`a` = 7)

EXPLAIN SELECT * FROM t1 WHERE EXISTS (SELECT * FROM t1 WHERE a=7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	a	a	5	const	1	100.00	Using index; FirstMatch
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` semi join (`test`.`t1`) where (`test`.`t1`.`a` = 7)

DROP TABLE t1;
#
# BUG#52317: Assertion failing in Field_varstring::store() 
# 	    at field.cc:6833
#
CREATE TABLE t1 (i INTEGER);
INSERT INTO t1 VALUES (1);
CREATE TABLE t2 (i INTEGER, KEY k(i));
INSERT INTO t2 VALUES (1), (2);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN  
SELECT i FROM t1 WHERE (1) NOT IN (SELECT i FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
2	SUBQUERY	t2	NULL	index_subquery	k	k	5	const	2	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` where false
DROP TABLE t2;
DROP TABLE t1;
#
# Bug #11765713 58705:
# OPTIMIZER LET ENGINE DEPEND ON UNINITIALIZED VALUES
# CREATED BY OPT_SUM_QUERY
#
CREATE TABLE t1(a INT NOT NULL, KEY (a));
INSERT INTO t1 VALUES (0), (1);
SELECT 1 as foo FROM t1 WHERE a < SOME
(SELECT a FROM t1 WHERE a <=>
(SELECT a FROM t1)
);
ERROR 21000: Subquery returns more than 1 row
SELECT 1 as foo FROM t1 WHERE a < SOME
(SELECT a FROM t1 WHERE a <=>
(SELECT a FROM t1 where a is null)
);
foo
DROP TABLE t1;
#
# Bug #57704: Cleanup code dies with void TABLE::set_keyread(bool): 
#             Assertion `file' failed.
#
CREATE TABLE t1 (a INT);
SELECT 1 FROM 
(SELECT ROW(
(SELECT 1 FROM t1 RIGHT JOIN 
(SELECT 1 FROM t1, t1 t2) AS d ON 1),
1) FROM t1) AS e;
ERROR 21000: Operand should contain 1 column(s)
DROP TABLE t1;
#
# Bug#11764086: Null left operand to NOT IN in WHERE clause
# behaves differently than real NULL
#
CREATE TABLE parent (id int);
INSERT INTO parent VALUES (1), (2);
CREATE TABLE child (parent_id int, other int);
INSERT INTO child VALUES (1,NULL);
# Offending query (c.parent_id is NULL for null-complemented rows only)
SELECT    p.id, c.parent_id
FROM      parent p
LEFT JOIN child  c
ON        p.id = c.parent_id
WHERE     c.parent_id NOT IN (
SELECT parent_id 
FROM   child
WHERE  parent_id = 3
);
id	parent_id
1	1
2	NULL
# Some syntactic variations with IS FALSE and IS NOT TRUE
SELECT    p.id, c.parent_id
FROM      parent p
LEFT JOIN child  c
ON        p.id = c.parent_id
WHERE     c.parent_id IN (
SELECT parent_id 
FROM   child
WHERE  parent_id = 3
) IS NOT TRUE;
id	parent_id
1	1
2	NULL
SELECT    p.id, c.parent_id
FROM      parent p
LEFT JOIN child  c
ON        p.id = c.parent_id
WHERE     c.parent_id IN (
SELECT parent_id 
FROM   child
WHERE  parent_id = 3
) IS FALSE;
id	parent_id
1	1
2	NULL
DROP TABLE parent, child;
# End of test for bug#11764086.
End of 5.5 tests.
#
# BUG#48920: COUNT DISTINCT returns 1 for NULL values when in a subquery 
#            in the select list
#

CREATE TABLE t1 (
i int(11) DEFAULT NULL,
v varchar(1) DEFAULT NULL
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.

INSERT INTO t1 VALUES (8,'v');
INSERT INTO t1 VALUES (9,'r');
INSERT INTO t1 VALUES (NULL,'y');

CREATE TABLE t2 (
i int(11) DEFAULT NULL,
v varchar(1) DEFAULT NULL,
KEY i_key (i)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.

INSERT INTO t2 VALUES (NULL,'r');
INSERT INTO t2 VALUES (0,'c');
INSERT INTO t2 VALUES (0,'o');
INSERT INTO t2 VALUES (2,'v');
INSERT INTO t2 VALUES (7,'c');
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK

SELECT i, v, (SELECT COUNT(DISTINCT i)
FROM t1
WHERE v  = t2.v) as subsel
FROM t2;
i	v	subsel
NULL	r	1
0	c	0
0	o	0
2	v	1
7	c	0

EXPLAIN
SELECT i, v, (SELECT COUNT(DISTINCT i)
FROM t1
WHERE v  = t2.v) as subsel
FROM t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	5	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1276	Field or reference 'test.t2.v' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t2`.`i` AS `i`,`test`.`t2`.`v` AS `v`,(/* select#2 */ select count(distinct `test`.`t1`.`i`) from `test`.`t1` where (`test`.`t1`.`v` = `test`.`t2`.`v`)) AS `subsel` from `test`.`t2`
DROP TABLE t1,t2;
#
# BUG#50257: Missing info in REF column of the EXPLAIN 
#            lines for subselects
#
CREATE TABLE t1 (a INT, b INT, INDEX (a));
INSERT INTO t1 VALUES (3, 10), (2, 20), (7, 10), (5, 20);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK

EXPLAIN SELECT * FROM (SELECT * FROM t1 WHERE a=7) t;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	a	a	5	const	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`a` = 7)

EXPLAIN SELECT * FROM t1 WHERE EXISTS (SELECT * FROM t1 WHERE a=7);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	a	a	5	const	1	100.00	Using index; FirstMatch
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` semi join (`test`.`t1`) where (`test`.`t1`.`a` = 7)

DROP TABLE t1;
#
# BUG#58561: Server Crash with correlated subquery and MyISAM tables 
#
CREATE TABLE cc (
pk INT,
col_int_key INT,
col_varchar_key VARCHAR(1),
PRIMARY KEY (pk),
KEY col_int_key (col_int_key),
KEY col_varchar_key (col_varchar_key,col_int_key)
) ENGINE=INNODB;
INSERT INTO cc VALUES (10,7,'v');
INSERT INTO cc VALUES (11,1,'r');
CREATE TABLE bb (
pk INT,
col_date_key DATE,
PRIMARY KEY (pk),
KEY col_date_key (col_date_key)
) ENGINE=INNODB;
INSERT INTO bb VALUES (10,'2002-02-21');
CREATE TABLE c (
pk INT,
col_int_key INT,
col_varchar_key VARCHAR(1),
PRIMARY KEY (pk),
KEY col_int_key (col_int_key),
KEY col_varchar_key (col_varchar_key,col_int_key)
) ENGINE=INNODB;
INSERT INTO c VALUES (1,NULL,'w');
INSERT INTO c VALUES (19,NULL,'f');
CREATE TABLE b (
pk INT,
col_int_key INT,
col_varchar_key VARCHAR(1),
PRIMARY KEY (pk),
KEY col_int_key (col_int_key),
KEY col_varchar_key (col_varchar_key,col_int_key)
) ENGINE=INNODB;
INSERT INTO b VALUES (1,7,'f');
SELECT col_int_key
FROM b granparent1
WHERE (col_int_key, col_int_key) IN (
SELECT parent1.pk, parent1.pk
FROM bb parent1 JOIN cc parent2
ON parent2.col_varchar_key = parent2.col_varchar_key
WHERE granparent1.col_varchar_key IN (
SELECT col_varchar_key
FROM c)
AND parent1.pk = granparent1.col_int_key
ORDER BY parent1.col_date_key 
);
col_int_key
DROP TABLE bb, b, cc, c;
End of 5.6 tests
#
# BUG#46743 "Azalea processing correlated, aggregate SELECT
# subqueries incorrectly"
#
CREATE TABLE t1 (c int);
INSERT INTO t1 VALUES (NULL);
CREATE TABLE t2 (d int , KEY (d));
INSERT INTO t2 VALUES (NULL),(NULL);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
0 rows in subquery
SELECT 1 AS RESULT FROM t2,t1 WHERE d = c;
RESULT
base query
SELECT (SELECT 1 FROM t2 WHERE d = c) AS RESULT FROM t1 ;
RESULT
NULL
EXPLAIN SELECT (SELECT 1 FROM t2 WHERE d = c) AS RESULT FROM t1 ;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
2	DEPENDENT SUBQUERY	t2	NULL	ref	d	d	5	test.t1.c	2	100.00	Using index
Warnings:
Note	1276	Field or reference 'test.t1.c' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select (/* select#2 */ select 1 from `test`.`t2` where (`test`.`t2`.`d` = `test`.`t1`.`c`)) AS `RESULT` from `test`.`t1`
first equivalent variant
SELECT (SELECT 1 FROM t2 WHERE d = IFNULL(c,NULL)) AS RESULT FROM t1 GROUP BY c ;
RESULT
NULL
EXPLAIN SELECT (SELECT 1 FROM t2 WHERE d = IFNULL(c,NULL)) AS RESULT FROM t1 GROUP BY c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using temporary
2	DEPENDENT SUBQUERY	t2	NULL	ref	d	d	5	func	2	100.00	Using where; Using index
Warnings:
Note	1276	Field or reference 'test.t1.c' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select (/* select#2 */ select 1 from `test`.`t2` where (`test`.`t2`.`d` = ifnull(`test`.`t1`.`c`,NULL))) AS `RESULT` from `test`.`t1` group by `test`.`t1`.`c`
second equivalent variant
SELECT (SELECT 1 FROM t2 WHERE d = c) AS RESULT FROM t1 GROUP BY c ;
RESULT
NULL
EXPLAIN SELECT (SELECT 1 FROM t2 WHERE d = c) AS RESULT FROM t1 GROUP BY c ;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using temporary
2	DEPENDENT SUBQUERY	t2	NULL	ref	d	d	5	func	2	100.00	Using index
Warnings:
Note	1276	Field or reference 'test.t1.c' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select (/* select#2 */ select 1 from `test`.`t2` where (`test`.`t2`.`d` = `test`.`t1`.`c`)) AS `RESULT` from `test`.`t1` group by `test`.`t1`.`c`
DROP TABLE t1,t2;

BUG#37842: Assertion in DsMrr_impl::dsmrr_init, at handler.cc:4307

CREATE TABLE t1 (
`pk` int(11) NOT NULL AUTO_INCREMENT,
`int_key` int(11) DEFAULT NULL,
PRIMARY KEY (`pk`),
KEY `int_key` (`int_key`)
) ENGINE=INNODB;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1,9),(2,3),(3,8),(4,6),(5,9),(6,5),(7,5),(8,9),(9,1),(10,10);
SELECT `pk` FROM t1 AS OUTR WHERE `int_key` = ALL (
SELECT `int_key` FROM t1 AS INNR WHERE INNR . `pk` >= 9
);
pk
DROP TABLE t1;
#
# Bug#53236 Segfault in DTCollation::set(DTCollation&)
#
CREATE TABLE t1 (
pk INTEGER AUTO_INCREMENT,
col_varchar VARCHAR(1),
PRIMARY KEY (pk)
)
;
INSERT INTO t1 (col_varchar) 
VALUES
('w'),
('m')
;
SELECT  table1.pk
FROM ( t1 AS table1 JOIN t1 AS table2 ON (table1.col_varchar =
table2.col_varchar) ) 
WHERE ( 1, 2 ) IN ( SELECT SUBQUERY1_t1.pk AS SUBQUERY1_field1,
SUBQUERY1_t1.pk AS SUBQUERY1_field2
FROM ( t1 AS SUBQUERY1_t1 JOIN t1 AS SUBQUERY1_t2
ON (SUBQUERY1_t2.col_varchar =
SUBQUERY1_t1.col_varchar) ) ) 
;
pk
drop table t1;
#
# Bug#58207: invalid memory reads when using default column value and 
# tmptable needed
#
CREATE TABLE t(a VARCHAR(245) DEFAULT
'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa');
INSERT INTO t VALUES (''),(''),(''),(''),(''),(''),(''),(''),(''),(''),('');
SELECT * FROM (SELECT default(a) FROM t GROUP BY a) d;
default(a)
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
DROP TABLE t;
#
# Bug 11765699 - 58690: !TABLE || (!TABLE->READ_SET || 
#                BITMAP_IS_SET(TABLE->READ_SET, FIELD_INDEX
#
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES (0), (1);
CREATE TABLE t2(
b TEXT, 
c INT, 
PRIMARY KEY (b(1))
);
INSERT INTO t2 VALUES ('a', 2), ('b', 3);
SELECT 1 FROM t1 WHERE a = 
(SELECT 1 FROM t2 WHERE b = 
(SELECT 1 FROM t1 t11 WHERE c = 1 OR t1.a = 1 AND 1 = 2)
ORDER BY b
);
1
SELECT 1 FROM t1 WHERE a = 
(SELECT 1 FROM t2 WHERE b = 
(SELECT 1 FROM t1 t11 WHERE c = 1 OR t1.a = 1 AND 1 = 2)
GROUP BY b
);
1
DROP TABLE t1, t2;
#
# BUG#12616253 - WRONG RESULT WITH EXISTS(SUBQUERY) (MISSING ROWS)
#
CREATE TABLE t1 (f1 varchar(1)) charset utf8mb4;
INSERT INTO t1 VALUES ('v'),('s');
CREATE TABLE t2 (f1_key varchar(1), KEY (f1_key)) charset utf8mb4;
INSERT INTO t2 VALUES ('j'),('v'),('c'),('m'),('d'),
('d'),('y'),('t'),('d'),('s');
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
SELECT table1.f1, table2.f1_key
FROM t1 AS table1, t2 AS table2
WHERE EXISTS 
( 
SELECT DISTINCT f1_key 
FROM t2 
WHERE f1_key != table2.f1_key AND f1_key >= table1.f1 );
f1	f1_key
s	c
s	d
s	d
s	d
s	j
s	m
s	s
s	t
s	v
s	y
v	c
v	d
v	d
v	d
v	j
v	m
v	s
v	t
v	v
v	y
explain SELECT table1.f1, table2.f1_key
FROM t1 AS table1, t2 AS table2
WHERE EXISTS 
( 
SELECT DISTINCT f1_key 
FROM t2 
WHERE f1_key != table2.f1_key AND f1_key >= table1.f1 );
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	table1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Start temporary
1	SIMPLE	t2	NULL	ALL	f1_key	NULL	NULL	NULL	10	33.33	Range checked for each record (index map: 0x1)
1	SIMPLE	table2	NULL	index	NULL	f1_key	7	NULL	10	90.00	Using where; Using index; End temporary; Using join buffer (hash join)
Warnings:
Note	1276	Field or reference 'test.table2.f1_key' of SELECT #2 was resolved in SELECT #1
Note	1276	Field or reference 'test.table1.f1' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`table1`.`f1` AS `f1`,`test`.`table2`.`f1_key` AS `f1_key` from `test`.`t1` `table1` join `test`.`t2` `table2` semi join (`test`.`t2`) where ((`test`.`t2`.`f1_key` <> `test`.`table2`.`f1_key`) and (`test`.`t2`.`f1_key` >= `test`.`table1`.`f1`))
DROP TABLE t1,t2;
#
# BUG#12616477 - 0 VS NULL DIFFERENCES WITH OUTER JOIN, SUBQUERY
#
CREATE TABLE t1 (
col_int_key int,
col_varchar_key varchar(1),
col_varchar_nokey varchar(1),
KEY (col_int_key)
) charset latin1;
INSERT INTO t1 VALUES (224,'p','p'),(9,'e','e');
CREATE TABLE t3 (
col_int_key int,
col_varchar_key varchar(1),
KEY col_int_key (col_int_key),
KEY col_varchar_key (col_varchar_key,col_int_key))
charset latin1;
INSERT INTO t3 VALUES (4,'p'),(8,'e'),(10,'a');
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	Error	Table 'test.t2' doesn't exist
test.t2	analyze	status	Operation failed
test.t3	analyze	status	OK
SELECT t1f.*,t3f.*,(
SELECT MIN(t3s.col_int_key)
FROM t3 AS t3s JOIN
t1 AS t1s ON t1s.col_int_key = 9 and
t1s.col_varchar_key = 'e'
WHERE 'e' <> t1f.col_varchar_nokey )
FROM
t1 AS t1f STRAIGHT_JOIN t3 AS t3f;
col_int_key	col_varchar_key	col_varchar_nokey	col_int_key	col_varchar_key	(
224	p	p	10	a	4
224	p	p	4	p	4
224	p	p	8	e	4
9	e	e	10	a	NULL
9	e	e	4	p	NULL
9	e	e	8	e	NULL
FROM t3 AS t3s JOIN
SELECT MIN(t3s.col_int_key)
WHERE 'e' <> t1f.col_varchar_nokey )
t1 AS t1s ON t1s.col_int_key = 9 and
t1s.col_varchar_key = 'e'
explain SELECT t1f.*,t3f.*,(
SELECT MIN(t3s.col_int_key)
FROM t3 AS t3s JOIN
t1 AS t1s ON t1s.col_int_key = 9 and
t1s.col_varchar_key = 'e'
WHERE 'e' <> t1f.col_varchar_nokey )
FROM
t1 AS t1f STRAIGHT_JOIN t3 AS t3f;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1f	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
1	PRIMARY	t3f	NULL	index	NULL	col_varchar_key	9	NULL	3	100.00	Using index; Using join buffer (hash join)
2	DEPENDENT SUBQUERY	t1s	NULL	ref	col_int_key	col_int_key	5	const	1	50.00	Using where
2	DEPENDENT SUBQUERY	t3s	NULL	index	NULL	col_int_key	5	NULL	3	100.00	Using index; Using join buffer (hash join)
Warnings:
Note	1276	Field or reference 'test.t1f.col_varchar_nokey' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1f`.`col_int_key` AS `col_int_key`,`test`.`t1f`.`col_varchar_key` AS `col_varchar_key`,`test`.`t1f`.`col_varchar_nokey` AS `col_varchar_nokey`,`test`.`t3f`.`col_int_key` AS `col_int_key`,`test`.`t3f`.`col_varchar_key` AS `col_varchar_key`,(/* select#2 */ select min(`test`.`t3s`.`col_int_key`) from `test`.`t3` `t3s` join `test`.`t1` `t1s` where ((`test`.`t1s`.`col_varchar_key` = 'e') and (`test`.`t1s`.`col_int_key` = 9) and ('e' <> `test`.`t1f`.`col_varchar_nokey`))) AS `(
SELECT MIN(t3s.col_int_key)
FROM t3 AS t3s JOIN
t1 AS t1s ON t1s.col_int_key = 9 and
t1s.col_varchar_key = 'e'
WHERE 'e' <> t1f.col_varchar_nokey )` from `test`.`t1` `t1f` straight_join `test`.`t3` `t3f`
DROP TABLE t1,t3;
#
# Bug#12795555: Missing rows with ALL/ANY subquery
#
CREATE TABLE t1 (f1 INT);
INSERT INTO t1 VAlUES (NULL),(1),(NULL),(2);
SELECT f1 FROM t1 WHERE f1 < ALL (SELECT 1 FROM DUAL WHERE 0);
f1
NULL
1
NULL
2
DROP TABLE t1;
CREATE TABLE t1 (k VARCHAR(1), KEY k(k));
INSERT INTO t1 VALUES ('r'), (NULL), (NULL);
CREATE TABLE t2 (c VARCHAR(1));
INSERT INTO t2 VALUES ('g'), (NULL);
CREATE TABLE t3 (c VARCHAR(1));
SELECT COUNT(*)
FROM t1 JOIN t2
WHERE t1.k < ALL(
SELECT c
FROM t3
);
COUNT(*)
6
DROP TABLE t1, t2, t3;
#
# Bug#12838171: 51VS56: TRANSFORMED IN()+SUBQ QUERY 
#               PRODUCES EMPTY RESULT ON 5.6, 1 ROW ON 5.1
#
CREATE TABLE ot (
col_int_nokey int(11), 
col_varchar_nokey varchar(1)
) charset latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO ot VALUES (1,'x');
CREATE TABLE it (
col_int_key int(11), 
col_varchar_key varchar(1), 
KEY idx_cvk_cik (col_varchar_key,col_int_key)
) charset latin1 ;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO it VALUES (NULL,'x'), (NULL,'f');
ANALYZE TABLE it;
Table	Op	Msg_type	Msg_text
test.it	analyze	status	OK

SELECT col_int_nokey 
FROM ot 
WHERE col_varchar_nokey IN
(SELECT col_varchar_key
FROM it 
WHERE col_int_key IS NULL);
col_int_nokey
1

EXPLAIN
SELECT col_int_nokey 
FROM ot 
WHERE col_varchar_nokey IN
(SELECT col_varchar_key
FROM it 
WHERE col_int_key IS NULL);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	ot	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
1	SIMPLE	it	NULL	ref	idx_cvk_cik	idx_cvk_cik	9	test.ot.col_varchar_nokey,const	1	100.00	Using where; Using index; FirstMatch(ot)
Warnings:
Note	1003	/* select#1 */ select `test`.`ot`.`col_int_nokey` AS `col_int_nokey` from `test`.`ot` semi join (`test`.`it`) where ((`test`.`it`.`col_varchar_key` = `test`.`ot`.`col_varchar_nokey`) and (`test`.`it`.`col_int_key` is null))

SELECT col_int_nokey 
FROM ot 
WHERE col_varchar_nokey IN
(SELECT col_varchar_key
FROM it 
WHERE coalesce(col_int_nokey, 1) );
col_int_nokey
1

EXPLAIN
SELECT col_int_nokey 
FROM ot 
WHERE col_varchar_nokey IN
(SELECT col_varchar_key
FROM it 
WHERE coalesce(col_int_nokey, 1) );
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	ot	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
1	SIMPLE	it	NULL	ref	idx_cvk_cik	idx_cvk_cik	4	test.ot.col_varchar_nokey	1	100.00	Using index; FirstMatch(ot)
Warnings:
Note	1276	Field or reference 'test.ot.col_int_nokey' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`ot`.`col_int_nokey` AS `col_int_nokey` from `test`.`ot` semi join (`test`.`it`) where ((`test`.`it`.`col_varchar_key` = `test`.`ot`.`col_varchar_nokey`) and (0 <> coalesce(`test`.`ot`.`col_int_nokey`,1)))
DROP TABLE it;
CREATE TABLE it (
col_int_key int(11),
col_varchar_key varchar(1),
col_varchar_key2 varchar(1),
KEY idx_cvk_cvk2_cik (col_varchar_key, col_varchar_key2, col_int_key),
KEY idx_cvk_cik (col_varchar_key, col_int_key)
) charset latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO it VALUES (NULL,'x','x'), (NULL,'f','f');
ANALYZE TABLE it;
Table	Op	Msg_type	Msg_text
test.it	analyze	status	OK
SELECT col_int_nokey
FROM ot
WHERE (col_varchar_nokey, 'x') IN
(SELECT col_varchar_key, col_varchar_key2
FROM it
WHERE col_int_key IS NULL);
col_int_nokey
1

EXPLAIN
SELECT col_int_nokey
FROM ot
WHERE (col_varchar_nokey, 'x') IN
(SELECT col_varchar_key, col_varchar_key2
FROM it
WHERE col_int_key IS NULL);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	ot	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
1	SIMPLE	it	NULL	ref	idx_cvk_cvk2_cik,idx_cvk_cik	idx_cvk_cvk2_cik	13	test.ot.col_varchar_nokey,const,const	1	100.00	Using where; Using index; FirstMatch(ot)
Warnings:
Note	1003	/* select#1 */ select `test`.`ot`.`col_int_nokey` AS `col_int_nokey` from `test`.`ot` semi join (`test`.`it`) where ((`test`.`it`.`col_varchar_key2` = 'x') and (`test`.`it`.`col_varchar_key` = `test`.`ot`.`col_varchar_nokey`) and (`test`.`it`.`col_int_key` is null))

DROP TABLE it, ot;
#
# Bug #11829691: Pure virtual method called in Item_bool_func2::fix...()
#
CREATE TABLE t1(a INTEGER);
CREATE TABLE t2(b INTEGER);
PREPARE stmt FROM "
SELECT SUM(b) FROM t2 GROUP BY b HAVING b IN (SELECT b FROM t1)";
EXECUTE stmt;
SUM(b)
EXECUTE stmt;
SUM(b)
DEALLOCATE PREPARE stmt;
DROP TABLE t1, t2;
#
# Bug #13595212 EXTRA ROWS RETURNED ON RIGHT JOIN WITH VIEW AND
# IN-SUBQUERY IN WHERE
#
CREATE TABLE t1 (
pk int(11) NOT NULL AUTO_INCREMENT,
col_int_key int(11) NOT NULL,
col_varchar_key varchar(1) NOT NULL,
col_varchar_nokey varchar(1) NOT NULL,
PRIMARY KEY (pk),
KEY col_int_key (col_int_key),
KEY col_varchar_key (col_varchar_key,col_int_key)
) charset latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1,0,'j','j'),(2,8,'v','v'),
(3,1,'c','c'),(4,8,'m','m'),(5,9,'d','d');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
CREATE VIEW v1 AS SELECT * FROM t1;
EXPLAIN SELECT alias2.col_varchar_nokey
FROM v1 AS alias1
RIGHT JOIN t1 AS alias2 ON 1
WHERE alias2.col_varchar_key IN (
SELECT sq2_alias1.col_varchar_nokey
FROM v1 AS sq2_alias1
LEFT JOIN t1 AS sq2_alias2
ON (sq2_alias2.col_int_key = sq2_alias1.pk)
WHERE sq2_alias1.pk != alias1.col_int_key
AND sq2_alias1.col_varchar_key > alias1.col_varchar_key
)
;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	col_varchar_key	NULL	NULL	NULL	5	100.00	Start temporary
1	SIMPLE	alias2	NULL	ref	col_varchar_key	col_varchar_key	3	test.t1.col_varchar_nokey	1	100.00	NULL
1	SIMPLE	t1	NULL	ALL	col_varchar_key	NULL	NULL	NULL	5	26.66	Range checked for each record (index map: 0x4)
1	SIMPLE	sq2_alias2	NULL	ref	col_int_key	col_int_key	4	func	1	100.00	Using where; Using index; End temporary
Warnings:
Note	1276	Field or reference 'alias1.col_int_key' of SELECT #2 was resolved in SELECT #1
Note	1276	Field or reference 'alias1.col_varchar_key' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`alias2`.`col_varchar_nokey` AS `col_varchar_nokey` from `test`.`t1` `alias2` join `test`.`t1` semi join (`test`.`t1` left join `test`.`t1` `sq2_alias2` on((`test`.`sq2_alias2`.`col_int_key` = `test`.`t1`.`pk`))) where ((`test`.`alias2`.`col_varchar_key` = `test`.`t1`.`col_varchar_nokey`) and (`test`.`t1`.`pk` <> `test`.`t1`.`col_int_key`) and (`test`.`t1`.`col_varchar_key` > `test`.`t1`.`col_varchar_key`))
SELECT alias2.col_varchar_nokey
FROM v1 AS alias1
RIGHT JOIN t1 AS alias2 ON 1
WHERE alias2.col_varchar_key IN (
SELECT sq2_alias1.col_varchar_nokey
FROM v1 AS sq2_alias1
LEFT JOIN t1 AS sq2_alias2
ON (sq2_alias2.col_int_key = sq2_alias1.pk)
WHERE sq2_alias1.pk != alias1.col_int_key
AND sq2_alias1.col_varchar_key > alias1.col_varchar_key
)
;
col_varchar_nokey
d
j
m
m
m
v
v
v
v
CREATE VIEW v2 AS SELECT alias2.col_varchar_nokey
FROM v1 AS alias1
RIGHT JOIN t1 AS alias2 ON 1
WHERE alias2.col_varchar_key IN (
SELECT sq2_alias1.col_varchar_nokey
FROM v1 AS sq2_alias1
LEFT JOIN t1 AS sq2_alias2
ON (sq2_alias2.col_int_key = sq2_alias1.pk)
WHERE sq2_alias1.pk != alias1.col_int_key
AND sq2_alias1.col_varchar_key > alias1.col_varchar_key
)
;
EXPLAIN SELECT * FROM v2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	col_varchar_key	NULL	NULL	NULL	#	#	Start temporary
1	SIMPLE	alias2	NULL	ref	col_varchar_key	col_varchar_key	3	test.t1.col_varchar_nokey	#	#	NULL
1	SIMPLE	t1	NULL	ALL	col_varchar_key	NULL	NULL	NULL	#	#	Range checked for each record (index map: 0x4)
1	SIMPLE	sq2_alias2	NULL	ref	col_int_key	col_int_key	4	func	#	#	Using where; Using index; End temporary
Warnings:
Note	1276	Field or reference 'test.alias1.col_int_key' of SELECT #3 was resolved in SELECT #2
Note	1276	Field or reference 'test.alias1.col_varchar_key' of SELECT #3 was resolved in SELECT #2
Note	1003	/* select#1 */ select `test`.`alias2`.`col_varchar_nokey` AS `col_varchar_nokey` from `test`.`t1` `alias2` join `test`.`t1` semi join (`test`.`t1` left join `test`.`t1` `sq2_alias2` on((`test`.`sq2_alias2`.`col_int_key` = `test`.`t1`.`pk`))) where ((`test`.`alias2`.`col_varchar_key` = `test`.`t1`.`col_varchar_nokey`) and (`test`.`t1`.`pk` <> `test`.`t1`.`col_int_key`) and (`test`.`t1`.`col_varchar_key` > `test`.`t1`.`col_varchar_key`))
SELECT * FROM v2;
col_varchar_nokey
d
j
m
m
m
v
v
v
v
EXPLAIN SELECT alias2.col_varchar_nokey
FROM t1 AS alias2
LEFT JOIN v1 AS alias1 ON 1
WHERE alias2.col_varchar_key IN (
SELECT sq2_alias1.col_varchar_nokey
FROM v1 AS sq2_alias1
LEFT JOIN t1 AS sq2_alias2
ON (sq2_alias2.col_int_key = sq2_alias1.pk)
WHERE sq2_alias1.pk != alias1.col_int_key
AND sq2_alias1.col_varchar_key > alias1.col_varchar_key
);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	col_varchar_key	NULL	NULL	NULL	5	100.00	Start temporary
1	SIMPLE	alias2	NULL	ref	col_varchar_key	col_varchar_key	3	test.t1.col_varchar_nokey	1	100.00	NULL
1	SIMPLE	t1	NULL	ALL	col_varchar_key	NULL	NULL	NULL	5	26.66	Range checked for each record (index map: 0x4)
1	SIMPLE	sq2_alias2	NULL	ref	col_int_key	col_int_key	4	func	1	100.00	Using where; Using index; End temporary
Warnings:
Note	1276	Field or reference 'alias1.col_int_key' of SELECT #2 was resolved in SELECT #1
Note	1276	Field or reference 'alias1.col_varchar_key' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`alias2`.`col_varchar_nokey` AS `col_varchar_nokey` from `test`.`t1` `alias2` join `test`.`t1` semi join (`test`.`t1` left join `test`.`t1` `sq2_alias2` on((`test`.`sq2_alias2`.`col_int_key` = `test`.`t1`.`pk`))) where ((`test`.`alias2`.`col_varchar_key` = `test`.`t1`.`col_varchar_nokey`) and (`test`.`t1`.`pk` <> `test`.`t1`.`col_int_key`) and (`test`.`t1`.`col_varchar_key` > `test`.`t1`.`col_varchar_key`))
SELECT alias2.col_varchar_nokey
FROM t1 AS alias2
LEFT JOIN v1 AS alias1 ON 1
WHERE alias2.col_varchar_key IN (
SELECT sq2_alias1.col_varchar_nokey
FROM v1 AS sq2_alias1
LEFT JOIN t1 AS sq2_alias2
ON (sq2_alias2.col_int_key = sq2_alias1.pk)
WHERE sq2_alias1.pk != alias1.col_int_key
AND sq2_alias1.col_varchar_key > alias1.col_varchar_key
);
col_varchar_nokey
d
j
m
m
m
v
v
v
v
DROP TABLE t1;
DROP VIEW v1,v2;
#
# Bug#13651009 WRONG RESULT FROM DERIVED TABLE IF THE SUBQUERY
# HAS AN EMPTY RESULT
#
CREATE TABLE t1 (
pk int NOT NULL,
col_int_nokey int NOT NULL,
col_int_key int NOT NULL,
col_time_key time NOT NULL,
col_varchar_key varchar(1) NOT NULL,
col_varchar_nokey varchar(1) NOT NULL,
PRIMARY KEY (pk),
KEY col_int_key (col_int_key),
KEY col_time_key (col_time_key),
KEY col_varchar_key (col_varchar_key,col_int_key)
) ENGINE=INNODB;
CREATE TABLE t2 (
pk int NOT NULL AUTO_INCREMENT,
col_int_nokey int NOT NULL,
col_int_key int NOT NULL,
col_time_key time NOT NULL,
col_varchar_key varchar(1) NOT NULL,
col_varchar_nokey varchar(1) NOT NULL,
PRIMARY KEY (pk),
KEY col_int_key (col_int_key),
KEY col_time_key (col_time_key),
KEY col_varchar_key (col_varchar_key,col_int_key)
) ENGINE=INNODB;
INSERT INTO t2 VALUES (1,4,4,'00:00:00','b','b');
SET @var2:=4, @var3:=8;

Testcase without inner subquery
EXPLAIN SELECT @var3:=12, sq4_alias1.*
FROM t1 AS sq4_alias1
WHERE (sq4_alias1.col_varchar_key + NULL) IS NULL OR
sq4_alias1.col_varchar_key = @var3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	sq4_alias1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
Note	1003	/* select#1 */ select (@var3:=12) AS `@var3:=12`,`test`.`sq4_alias1`.`pk` AS `pk`,`test`.`sq4_alias1`.`col_int_nokey` AS `col_int_nokey`,`test`.`sq4_alias1`.`col_int_key` AS `col_int_key`,`test`.`sq4_alias1`.`col_time_key` AS `col_time_key`,`test`.`sq4_alias1`.`col_varchar_key` AS `col_varchar_key`,`test`.`sq4_alias1`.`col_varchar_nokey` AS `col_varchar_nokey` from `test`.`t1` `sq4_alias1` where (((`test`.`sq4_alias1`.`col_varchar_key` + NULL) is null) or (cast(`test`.`sq4_alias1`.`col_varchar_key` as double) = cast((@`var3`) as double)))
SELECT @var3:=12, sq4_alias1.*
FROM t1 AS sq4_alias1
WHERE (sq4_alias1.col_varchar_key + NULL) IS NULL OR
sq4_alias1.col_varchar_key = @var3;
@var3:=12	pk	col_int_nokey	col_int_key	col_time_key	col_varchar_key	col_varchar_nokey
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
SELECT @var3;
@var3
8
EXPLAIN SELECT * FROM ( SELECT @var3:=12, sq4_alias1.*
FROM t1 AS sq4_alias1
WHERE (sq4_alias1.col_varchar_key + NULL) IS NULL OR
sq4_alias1.col_varchar_key = @var3 ) AS alias3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	<derived2>	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
2	DERIVED	sq4_alias1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
Note	1003	/* select#1 */ select `alias3`.`@var3:=12` AS `@var3:=12`,`alias3`.`pk` AS `pk`,`alias3`.`col_int_nokey` AS `col_int_nokey`,`alias3`.`col_int_key` AS `col_int_key`,`alias3`.`col_time_key` AS `col_time_key`,`alias3`.`col_varchar_key` AS `col_varchar_key`,`alias3`.`col_varchar_nokey` AS `col_varchar_nokey` from (/* select#2 */ select (@var3:=12) AS `@var3:=12`,`test`.`sq4_alias1`.`pk` AS `pk`,`test`.`sq4_alias1`.`col_int_nokey` AS `col_int_nokey`,`test`.`sq4_alias1`.`col_int_key` AS `col_int_key`,`test`.`sq4_alias1`.`col_time_key` AS `col_time_key`,`test`.`sq4_alias1`.`col_varchar_key` AS `col_varchar_key`,`test`.`sq4_alias1`.`col_varchar_nokey` AS `col_varchar_nokey` from `test`.`t1` `sq4_alias1` where (((`test`.`sq4_alias1`.`col_varchar_key` + NULL) is null) or (cast(`test`.`sq4_alias1`.`col_varchar_key` as double) = cast((@`var3`) as double)))) `alias3`
SELECT * FROM ( SELECT @var3:=12, sq4_alias1.*
FROM t1 AS sq4_alias1
WHERE (sq4_alias1.col_varchar_key + NULL) IS NULL OR
sq4_alias1.col_varchar_key = @var3 ) AS alias3;
@var3:=12	pk	col_int_nokey	col_int_key	col_time_key	col_varchar_key	col_varchar_nokey
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
SELECT @var3;
@var3
8

Testcase with inner subquery; crashed WL#6095
SET @var3=8;
EXPLAIN SELECT sq4_alias1.*
FROM t1 AS sq4_alias1
WHERE (sq4_alias1.col_varchar_key , sq4_alias1.col_varchar_nokey)
NOT IN
(SELECT c_sq1_alias1.col_varchar_key AS c_sq1_field1,
c_sq1_alias1.col_varchar_nokey AS c_sq1_field2
FROM t2 AS c_sq1_alias1
WHERE (c_sq1_alias1.col_int_nokey != @var2
OR c_sq1_alias1.pk != @var3));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	sq4_alias1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
1	SIMPLE	c_sq1_alias1	NULL	ref	PRIMARY,col_varchar_key	col_varchar_key	6	test.sq4_alias1.col_varchar_key	1	100.00	Using where; Not exists
Warnings:
Note	1003	/* select#1 */ select `test`.`sq4_alias1`.`pk` AS `pk`,`test`.`sq4_alias1`.`col_int_nokey` AS `col_int_nokey`,`test`.`sq4_alias1`.`col_int_key` AS `col_int_key`,`test`.`sq4_alias1`.`col_time_key` AS `col_time_key`,`test`.`sq4_alias1`.`col_varchar_key` AS `col_varchar_key`,`test`.`sq4_alias1`.`col_varchar_nokey` AS `col_varchar_nokey` from `test`.`t1` `sq4_alias1` anti join (`test`.`t2` `c_sq1_alias1`) on(((`test`.`c_sq1_alias1`.`col_varchar_nokey` = `test`.`sq4_alias1`.`col_varchar_nokey`) and (`test`.`c_sq1_alias1`.`col_varchar_key` = `test`.`sq4_alias1`.`col_varchar_key`) and ((`test`.`c_sq1_alias1`.`col_int_nokey` <> <cache>((@`var2`))) or (`test`.`c_sq1_alias1`.`pk` <> <cache>((@`var3`)))))) where true
SELECT sq4_alias1.*
FROM t1 AS sq4_alias1
WHERE (sq4_alias1.col_varchar_key , sq4_alias1.col_varchar_nokey)
NOT IN
(SELECT c_sq1_alias1.col_varchar_key AS c_sq1_field1,
c_sq1_alias1.col_varchar_nokey AS c_sq1_field2
FROM t2 AS c_sq1_alias1
WHERE (c_sq1_alias1.col_int_nokey != @var2
OR c_sq1_alias1.pk != @var3));
pk	col_int_nokey	col_int_key	col_time_key	col_varchar_key	col_varchar_nokey
EXPLAIN SELECT * FROM ( SELECT sq4_alias1.*
FROM t1 AS sq4_alias1
WHERE (sq4_alias1.col_varchar_key , sq4_alias1.col_varchar_nokey)
NOT IN
(SELECT c_sq1_alias1.col_varchar_key AS c_sq1_field1,
c_sq1_alias1.col_varchar_nokey AS c_sq1_field2
FROM t2 AS c_sq1_alias1
WHERE (c_sq1_alias1.col_int_nokey != @var2
OR c_sq1_alias1.pk != @var3)) ) AS alias3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	sq4_alias1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
1	SIMPLE	c_sq1_alias1	NULL	ref	PRIMARY,col_varchar_key	col_varchar_key	6	test.sq4_alias1.col_varchar_key	1	100.00	Using where; Not exists
Warnings:
Note	1003	/* select#1 */ select `test`.`sq4_alias1`.`pk` AS `pk`,`test`.`sq4_alias1`.`col_int_nokey` AS `col_int_nokey`,`test`.`sq4_alias1`.`col_int_key` AS `col_int_key`,`test`.`sq4_alias1`.`col_time_key` AS `col_time_key`,`test`.`sq4_alias1`.`col_varchar_key` AS `col_varchar_key`,`test`.`sq4_alias1`.`col_varchar_nokey` AS `col_varchar_nokey` from `test`.`t1` `sq4_alias1` anti join (`test`.`t2` `c_sq1_alias1`) on(((`test`.`c_sq1_alias1`.`col_varchar_nokey` = `test`.`sq4_alias1`.`col_varchar_nokey`) and (`test`.`c_sq1_alias1`.`col_varchar_key` = `test`.`sq4_alias1`.`col_varchar_key`) and ((`test`.`c_sq1_alias1`.`col_int_nokey` <> <cache>((@`var2`))) or (`test`.`c_sq1_alias1`.`pk` <> <cache>((@`var3`)))))) where true
SELECT * FROM ( SELECT sq4_alias1.*
FROM t1 AS sq4_alias1
WHERE (sq4_alias1.col_varchar_key , sq4_alias1.col_varchar_nokey)
NOT IN
(SELECT c_sq1_alias1.col_varchar_key AS c_sq1_field1,
c_sq1_alias1.col_varchar_nokey AS c_sq1_field2
FROM t2 AS c_sq1_alias1
WHERE (c_sq1_alias1.col_int_nokey != @var2
OR c_sq1_alias1.pk != @var3)) ) AS alias3;
pk	col_int_nokey	col_int_key	col_time_key	col_varchar_key	col_varchar_nokey
DROP TABLE t1,t2;
#
# Test that indexsubquery_engine only does one lookup if
# the technique is unique_subquery: does not try to read the
# next row if the first row failed the subquery's WHERE
# condition (here: b=3).
#
create table t1(a int);
insert into t1 values(1),(2);
create table t2(a int primary key, b int);
insert into t2 values(1,10),(2,10);
explain select * from t1 where a in (select a from t2 where b=3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	2	50.00	Using where
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t2` join `test`.`t1` where ((`test`.`t1`.`a` = `test`.`t2`.`a`) and (`test`.`t2`.`b` = 3))
flush status;
select * from t1 where a in (select a from t2 where b=3);
a
show status like "handler_read%";
Variable_name	Value
Handler_read_first	1
Handler_read_key	1
Handler_read_last	0
Handler_read_next	0
Handler_read_prev	0
Handler_read_rnd	0
Handler_read_rnd_next	3
drop table t1,t2;
#
# Bug#13735980 Difference in number of rows when using subqueries
#
CREATE TABLE m (c1 VARCHAR(1), c2 INTEGER, c3 INTEGER);
INSERT INTO m VALUES ('',6,8), ('',75,NULL);
CREATE TABLE o (c1 VARCHAR(1));
INSERT INTO o VALUES ('S'), ('S'), ('S');
CREATE VIEW v1 AS
SELECT m.c1 AS c1,m.c2 AS c2,m.c3 AS c3 FROM m;
CREATE VIEW v2 AS
SELECT o.c1 AS c1 FROM o;
SELECT t1.c3
FROM v1 AS t1 JOIN v2 AS t2 ON t1.c1 < t2.c1
WHERE t1.c3 BETWEEN 2 AND 6 OR
(t1.c3 IN(4) AND t1.c3 <> 2) OR t1.c2 >= 8;
c3
NULL
NULL
NULL
CREATE TABLE integers (i1 INTEGER);
INSERT IGNORE INTO integers VALUES (2),(4),(6),(8);
SELECT t1.c3
FROM v1 AS t1 JOIN v2 AS t2 ON t1.c1 < t2.c1
WHERE t1.c3 BETWEEN (SELECT i1 FROM integers WHERE i1 = 2) AND
(SELECT i1 FROM integers WHERE i1 = 6)  OR
t1.c3 IN((SELECT i1 FROM integers WHERE i1 = 4)) AND
t1.c3 <>(SELECT i1 FROM integers WHERE i1 = 2) OR
t1.c2 >=(SELECT i1 FROM integers WHERE i1 = 8);
c3
NULL
NULL
NULL
SELECT t1.c3
FROM m AS t1 JOIN o AS t2 ON t1.c1 < t2.c1
WHERE t1.c3 BETWEEN (SELECT i1 FROM integers WHERE i1 = 2) AND
(SELECT i1 FROM integers WHERE i1 = 6)  OR
t1.c3 IN((SELECT i1 FROM integers WHERE i1 = 4)) AND
t1.c3 <>(SELECT i1 FROM integers WHERE i1 = 2) OR
t1.c2 >=(SELECT i1 FROM integers WHERE i1 = 8);
c3
NULL
NULL
NULL
DROP VIEW v1, v2;
DROP TABLE m, o, integers;
#
# Bug#13721076 CRASH WITH TIME TYPE/TIMESTAMP() AND WARNINGS IN SUBQUERY
#
CREATE TABLE t1(a TIME NOT NULL);
INSERT INTO t1 VALUES ('00:00:32');
SELECT 1 FROM t1 WHERE a >
(SELECT timestamp(a) AS a FROM t1);
1
DROP TABLE t1;
#
# Bug #13736664 RIGHT JOIN+WHERE QUERY GIVES DIFF RESULTS WHEN USING
# SELECT * SUBQ FOR TABLES
#
CREATE TABLE t1(a INT, b CHAR(1));
INSERT INTO t1 VALUES (NULL, 'x');
CREATE TABLE t2(c INT, d CHAR(1));
INSERT INTO t2 VALUES (NULL, 'y'), (9, 'z');
SELECT d
FROM t2 AS a2 RIGHT JOIN t1 AS a1 ON c = a
WHERE d LIKE '_'
ORDER BY d;
d
SELECT d
FROM (SELECT * FROM t2) AS a2 RIGHT JOIN (SELECT * FROM t1) AS a1 ON c = a
WHERE d LIKE '_'
ORDER BY d;
d
DROP TABLE t1, t2;
#
# Bug#13468414 Query shows different results when literals are selected
#              from separate table
#
CREATE TABLE t1 (
col_varchar_key varchar(1) NOT NULL,
col_varchar_nokey varchar(1) NOT NULL,
KEY col_varchar_key (col_varchar_key)
) charset utf8mb4 ENGINE=InnoDB;
INSERT INTO t1 VALUES
('v','v'), ('s','s'), ('l','l'), ('y','y'), ('c','c'),
('i','i'), ('h','h'), ('q','q'), ('a','a'), ('v','v'),
('u','u'), ('s','s'), ('y','y'), ('z','z'), ('h','h'),
('p','p'), ('e','e'), ('i','i'), ('y','y'), ('w','w');
CREATE TABLE t2 (
col_int_nokey INT NOT NULL,
col_varchar_nokey varchar(1) NOT NULL
) charset utf8mb4 ENGINE=InnoDB;
INSERT INTO t2 VALUES
(4,'j'), (6,'v'), (3,'c'), (5,'m'), (3,'d'), (246,'d'), (2,'y'), (9,'t'),
(3,'d'), (8,'s'), (1,'r'), (8,'m'), (8,'b'), (5,'x'), (7,'g'), (5,'p'),
(1,'q'), (6,'w'), (2,'d'), (9,'e');
CREATE TABLE t0 (
i1 INTEGER NOT NULL PRIMARY KEY
);
INSERT INTO t0 VALUES (7);
SELECT col_varchar_nokey
FROM t1
WHERE (7) NOT IN
(SELECT it2.col_int_nokey
FROM t2 AS it2 LEFT JOIN t1 AS it1
ON it2.col_varchar_nokey = it1.col_varchar_key
);
col_varchar_nokey
SELECT col_varchar_nokey
FROM t1
WHERE (SELECT i1 FROM t0 WHERE i1 = 7) NOT IN
(SELECT it2.col_int_nokey
FROM t2 AS it2 LEFT JOIN t1 AS it1
ON it2.col_varchar_nokey = it1.col_varchar_key
);
col_varchar_nokey
explain SELECT col_varchar_nokey
FROM t1
WHERE (SELECT i1 FROM t0 WHERE i1 = 7) NOT IN
(SELECT it2.col_int_nokey
FROM t2 AS it2 LEFT JOIN t1 AS it1
ON it2.col_varchar_nokey = it1.col_varchar_key
);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
3	DEPENDENT SUBQUERY	it2	NULL	ALL	NULL	NULL	NULL	NULL	20	100.00	Using where
3	DEPENDENT SUBQUERY	it1	NULL	ref	col_varchar_key	col_varchar_key	6	test.it2.col_varchar_nokey	1	100.00	Using index
2	SUBQUERY	t0	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`col_varchar_nokey` AS `col_varchar_nokey` from `test`.`t1` where false
DROP TABLE t0, t1, t2;
#
# Bug#13735704 Memory engine + NOT IN + subquery produces different
# result set
#
CREATE TABLE t1(
col_varchar_key varchar(1),
KEY col_varchar_key(col_varchar_key)
) engine=MEMORY;
INSERT INTO t1 VALUES
('v'), ('s'), ('y'), ('z'), ('h'), ('p');
CREATE TABLE t2(
col_int_nokey int,
col_int_key int,
KEY col_int_key(col_int_key)
) engine=MEMORY;
INSERT INTO t2 VALUES
(4,4), (2,7), (2,5), (2,3), (197,188), (4,4), (6,2), (1,4),
(156,231), (2,1), (1,2), (80,194), (3,2), (8,3), (91,98), (6,3),
(7,1), (3,0), (161,189), (7,8), (7,3), (213,120), (248,25), (1,1),
(6,3), (6,1), (3,3), (140,153), (0,5), (7,9), (6,1), (1,2),
(1,7), (9,2), (0,1), (6,5);
CREATE TABLE t0(i1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t0 VALUES(7),(2);
SELECT col_varchar_key
FROM t1
WHERE (7,2) NOT IN
(SELECT col_int_key,col_int_nokey FROM t2);
col_varchar_key
explain SELECT col_varchar_key
FROM t1
WHERE ((SELECT i1 FROM t0 WHERE i1 = 7),
(SELECT i1 FROM t0 WHERE i1 = 2)) NOT IN
(SELECT col_int_key,col_int_nokey FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
4	DEPENDENT SUBQUERY	t2	NULL	index_subquery	col_int_key	col_int_key	5	func	5	100.00	Using where; Full scan on NULL key
3	SUBQUERY	t0	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	Using index
2	SUBQUERY	t0	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`col_varchar_key` AS `col_varchar_key` from `test`.`t1` where false
SELECT col_varchar_key
FROM t1
WHERE ((SELECT i1 FROM t0 WHERE i1 = 7),
(SELECT i1 FROM t0 WHERE i1 = 2)) NOT IN
(SELECT col_int_key,col_int_nokey FROM t2);
col_varchar_key
DROP TABLE t0, t1, t2;
#
# Bug #13639204 64111: CRASH ON SELECT SUBQUERY WITH NON UNIQUE
# INDEX
#
CREATE TABLE t1 (
id int
) ENGINE=InnoDB;
INSERT INTO t1 (id) VALUES (11);
CREATE TABLE t2 (
t1_id int,
position int,
KEY t1_id (t1_id),
KEY t1_id_position (t1_id,position)
) ENGINE=InnoDB;
EXPLAIN SELECT
(SELECT position FROM t2
WHERE t2.t1_id = t1.id
ORDER BY t2.t1_id , t2.position
LIMIT 10,1
) AS maxkey
FROM t1
LIMIT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
2	DEPENDENT SUBQUERY	t2	NULL	ref	t1_id,t1_id_position	t1_id_position	5	test.t1.id	1	100.00	Using index
Warnings:
Note	1276	Field or reference 'test.t1.id' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select (/* select#2 */ select `test`.`t2`.`position` from `test`.`t2` where (`test`.`t2`.`t1_id` = `test`.`t1`.`id`) order by `test`.`t2`.`t1_id`,`test`.`t2`.`position` limit 10,1) AS `maxkey` from `test`.`t1` limit 1
SELECT
(SELECT position FROM t2
WHERE t2.t1_id = t1.id
ORDER BY t2.t1_id , t2.position
LIMIT 10,1
) AS maxkey
FROM t1
LIMIT 1;
maxkey
NULL
DROP TABLE t1,t2;
#
# Bug#13731417 WRONG RESULT WITH NOT IN (SUBQUERY) AND
# COMPOSITE INDEX ON SUBQUERY'S TABLE
#
CREATE TABLE t1 (a int, b int);
CREATE TABLE t2 (a int, b int, KEY a_b (a,b));
CREATE TABLE t4 (a int);
INSERT INTO t1 VALUES(0,1);
INSERT INTO t2 VALUES(NULL,1),(NULL,1);
INSERT INTO t4 VALUES(1);
ANALYZE TABLE t1, t2, t4;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t4	analyze	status	OK
SELECT ((0,1) NOT IN (SELECT NULL,1)) IS NULL;
((0,1) NOT IN (SELECT NULL,1)) IS NULL
1
EXPLAIN SELECT * FROM t1
WHERE ((a,b) NOT IN (SELECT t2.a,t2.b FROM
t4 STRAIGHT_JOIN t2 WHERE t4.a=t2.b)) IS NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
2	DEPENDENT SUBQUERY	t4	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
2	DEPENDENT SUBQUERY	t2	NULL	ref_or_null	a_b	a_b	10	func,test.t4.a	4	100.00	Using where; Using index; Full scan on NULL key
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (<in_optimizer>((`test`.`t1`.`a`,`test`.`t1`.`b`),not <exists>(/* select#2 */ select `test`.`t2`.`a`,`test`.`t2`.`b` from `test`.`t4` straight_join `test`.`t2` where ((`test`.`t2`.`b` = `test`.`t4`.`a`) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t1`.`a`) = `test`.`t2`.`a`) or (`test`.`t2`.`a` is null)), true) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t1`.`b`) = `test`.`t4`.`a`) or (`test`.`t4`.`a` is null)), true)) having (<if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t2`.`a`), true) and <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t2`.`b`), true)))) is null)
SELECT * FROM t1
WHERE ((a,b) NOT IN (SELECT t2.a,t2.b FROM
t4 STRAIGHT_JOIN t2 WHERE t4.a=t2.b)) IS NULL;
a	b
0	1
ALTER TABLE t2 DROP INDEX a_b;
EXPLAIN SELECT * FROM t1
WHERE ((a,b) NOT IN (SELECT t2.a,t2.b FROM
t4 STRAIGHT_JOIN t2 WHERE t4.a=t2.b)) IS NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
2	DEPENDENT SUBQUERY	t4	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (<in_optimizer>((`test`.`t1`.`a`,`test`.`t1`.`b`),not <exists>(/* select#2 */ select `test`.`t2`.`a`,`test`.`t2`.`b` from `test`.`t4` straight_join `test`.`t2` where ((`test`.`t2`.`b` = `test`.`t4`.`a`) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t1`.`a`) = `test`.`t2`.`a`) or (`test`.`t2`.`a` is null)), true) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t1`.`b`) = `test`.`t4`.`a`) or (`test`.`t4`.`a` is null)), true)) having (<if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t2`.`a`), true) and <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t2`.`b`), true)))) is null)
SELECT * FROM t1
WHERE ((a,b) NOT IN (SELECT t2.a,t2.b FROM
t4 STRAIGHT_JOIN t2 WHERE t4.a=t2.b)) IS NULL;
a	b
0	1
DROP TABLE t1,t2,t4;
#
# Bug#13725821 ASSERT NULL_REF_KEY == __NULL FAILED IN
# CREATE_REF_FOR_KEY() IN SQL_SELECT.CC
#
CREATE TABLE t1 (
pk INT NOT NULL,
col_int_nokey INT,
col_int_key INT,
col_varchar_key VARCHAR(1),
col_varchar_nokey VARCHAR(1),
PRIMARY KEY (pk),
KEY col_int_key (col_int_key),
KEY col_varchar_key (col_varchar_key,col_int_key)
) ENGINE=INNODB;
INSERT INTO t1 VALUES (17,NULL,9,NULL,NULL),
(18,2,2,'o','o'),(19,NULL,9,'w','w'),(20,6,2,'m','m'),(21,7,4,'q','q'),
(22,2,0,NULL,NULL),(23,5,4,'d','d'),(24,7,8,'g','g'),(25,6,NULL,'x','x'),
(26,6,NULL,'f','f'),(27,2,0,'p','p'),(28,9,NULL,'j','j'),(29,6,8,'c','c');
ALTER TABLE t1 DISABLE KEYS;
Warnings:
Note	1031	Table storage engine for 't1' doesn't have this option
ALTER TABLE t1 ENABLE KEYS;
Warnings:
Note	1031	Table storage engine for 't1' doesn't have this option
CREATE TABLE t2 SELECT table1.col_varchar_key AS field1,
table1.col_int_nokey AS field2,
table2.col_varchar_key AS field3,
table1.col_int_nokey AS field4,
table2.col_int_nokey AS field5,
table1.col_varchar_nokey AS field6
FROM ( t1 AS table1
INNER JOIN t1 AS table2
ON (( table2.col_int_nokey = table1.col_int_key )
AND ( table2.col_int_key = table1.col_int_key )
) )
WHERE ( table1.col_varchar_key = table2.col_varchar_key OR table1.pk = 154 )
OR table1.pk != 201
AND ( table1.col_varchar_key LIKE '%a%' OR table1.col_varchar_key LIKE
'%b%')
 ;
SELECT *
FROM t2
WHERE (field1, field2, field3, field4, field5, field6) NOT IN ( SELECT table1.col_varchar_key AS field1,
table1.col_int_nokey AS field2,
table2.col_varchar_key AS field3,
table1.col_int_nokey AS field4,
table2.col_int_nokey AS field5,
table1.col_varchar_nokey AS field6
FROM ( t1 AS table1
INNER JOIN t1 AS table2
ON (( table2.col_int_nokey = table1.col_int_key )
AND ( table2.col_int_key = table1.col_int_key )
) )
WHERE ( table1.col_varchar_key = table2.col_varchar_key OR table1.pk = 154 )
OR table1.pk != 201
AND ( table1.col_varchar_key LIKE '%a%' OR table1.col_varchar_key LIKE
'%b%')

);
field1	field2	field3	field4	field5	field6
DROP TABLE t2,t1;
#
# Bug #13735712 SELECT W/ SUBQUERY PRODUCES MORE ROWS WHEN USING
# VARIABLES
#
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES(9);
CREATE TABLE t2(b INT);
INSERT INTO t2 VALUES(8);
CREATE TABLE t3(c INT);
INSERT INTO t3 VALUES(3);
SELECT *
FROM t2 RIGHT JOIN t3 ON(c = b)
WHERE b < ALL(SELECT a FROM t1 WHERE a <= 7);
b	c
NULL	3
SET @var = 7;
SELECT *
FROM t2 RIGHT JOIN t3 ON(c = b)
WHERE b < ALL(SELECT a FROM t1 WHERE a <= @var);
b	c
NULL	3
DROP TABLE t1, t2, t3;
#
# Bug #13330886 TOO MANY ROWS WITH ALL|ANY
#
CREATE TABLE t1 (a INT, b INT);
INSERT INTO t1 VALUES (NULL,8), (8,7);
CREATE TABLE t2 (c INT);
INSERT INTO t2 VALUES (10);
SELECT 1 FROM t2 WHERE c > ALL (SELECT a FROM t1 WHERE b >= 3);
1
SET @var = 3;
SELECT 1 FROM t2 WHERE c > ALL (SELECT a FROM t1 WHERE b >= @var);
1
DROP TABLE t1, t2;
CREATE TABLE t1 (a INT NOT NULL, b INT NOT NULL);
INSERT INTO t1 VALUES (0,8), (8,7);
CREATE TABLE t2 (c INT NOT NULL);
INSERT INTO t2 VALUES (10);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN
SELECT 1 FROM t2 WHERE c > ANY (SELECT a FROM t1 WHERE b >= 3) IS TRUE;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
2	SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t2` where (<nop>((`test`.`t2`.`c` > (/* select#2 */ select min(`test`.`t1`.`a`) from `test`.`t1` where (`test`.`t1`.`b` >= 3)))) is true)
SELECT 1 FROM t2 WHERE c > ANY (SELECT a FROM t1 WHERE b >= 3) IS TRUE;
1
1
DROP TABLE t1, t2;
# Bug #18014565 - WRONG RESULT COMPUTATION USING ALL() AND GROUP BY
#
CREATE TABLE t1 (
f1 INTEGER,
f2 INTEGER
);
INSERT INTO t1 VALUES (1,3),(1,6),(2,3),(2,6);
SELECT f1, SUM(f2) AS sum FROM t1 GROUP BY f1 HAVING sum > ALL (SELECT 1);
f1	sum
1	9
2	9
DROP TABLE t1;
# End of test for Bug#18014565
set @@optimizer_switch=@old_opt_switch;
#
# Bug #18486607 ASSERTION FAILED:
# IN_SUBQ_PRED->LEFT_EXPR->FIXED IN CONVERT_SUBQUERY_TO_SEMIJOIN 
#
CREATE TABLE t1 (a INT);
CREATE TABLE t2 (b INT);
SELECT (SELECT 1 FROM t2
WHERE a IN (SELECT 1 FROM t2)
) AS scalar
FROM t1
GROUP BY a;
scalar
DROP TABLE t1,t2;
SET sql_mode = default;
#
# Bug #19179529 SEGMENTATION FAULT IN ADD_KEY_FIELD FOR ENGINES MYISAM AND MEMORY
#
CREATE TABLE t1 (
pk INTEGER PRIMARY KEY,
col_int_key INTEGER NOT NULL,
KEY (col_int_key)
) ENGINE=INNODB;
SELECT
( SELECT MIN(  1 ) AS SQ1_field1
FROM t1 AS SQ1_alias1 , t1 AS SQ1_alias2
WHERE  SQ1_alias2 . `pk` > alias1 . `col_int_key`  ) AS field1
FROM t1 AS alias1 GROUP BY field1;
field1
DROP TABLE t1;
#
# Bug#19224430 	ASSERT FAILED IN JOIN::REPLACE_INDEX_SUBQUERY
#
CREATE TABLE t1 (
pk INTEGER,
col_int_nokey INTEGER,
col_int_key INTEGER,
col_datetime_key DATETIME,
PRIMARY KEY (pk),
KEY (col_int_key),
KEY (col_datetime_key)
)  ENGINE=MEMORY;
INSERT INTO t1 VALUES (1, 1, 7, '2001-11-04 19:07:55.051133');
CREATE TABLE t2(field1 INT, field2 INT) ENGINE=MEMORY;
EXPLAIN SELECT * FROM t2 WHERE (field1, field2) IN (
SELECT MAX(col_datetime_key), col_int_key
FROM t1
WHERE col_int_key > col_int_nokey
GROUP BY col_int_key);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
2	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Not optimized, outer query is empty
Warnings:
Note	1003	/* select#1 */ select NULL AS `field1`,NULL AS `field2` from `test`.`t2` where <in_optimizer>((NULL,NULL),<exists>(/* select#2 */ select max(`test`.`t1`.`col_datetime_key`),`test`.`t1`.`col_int_key` from `test`.`t1` where (`test`.`t1`.`col_int_key` > `test`.`t1`.`col_int_nokey`) group by `test`.`t1`.`col_int_key` having (((<cache>(NULL) = max(`test`.`t1`.`col_datetime_key`)) or (max(`test`.`t1`.`col_datetime_key`) is null)) and ((<cache>(NULL) = `test`.`t1`.`col_int_key`) or (`test`.`t1`.`col_int_key` is null)) and <is_not_null_test>(max(`test`.`t1`.`col_datetime_key`)) and <is_not_null_test>(`test`.`t1`.`col_int_key`))))
SELECT * FROM t2 WHERE (field1, field2) IN (
SELECT MAX(col_datetime_key), col_int_key
FROM t1
WHERE col_int_key > col_int_nokey
GROUP BY col_int_key);
field1	field2
DROP TABLE t1,t2;
#
# Bug#19416826 Assert failed in JOIN::make_tmp_tables_info
#
CREATE TABLE t1 (
pk int NOT NULL,
col_int_nokey int DEFAULT NULL,
col_int_key int DEFAULT NULL,
col_varchar_key varchar(1) DEFAULT NULL,
col_varchar_nokey varchar(1) DEFAULT NULL,
PRIMARY KEY(pk),
KEY col_int_key(col_int_key),
KEY col_varchar_key(col_varchar_key, col_int_key)
);
INSERT INTO t1 VALUES
(1,2,4,'v','v'),
(2,150,62,'v','v'),
(3,NULL,7,'c','c'),
(4,2,1,NULL,NULL),
(5,5,0,'x','x'),
(6,3,7,'i','i'),
(7,1,7,'e','e'),
(8,4,1,'p','p'),
(9,NULL,7,'s','s'),
(10,2,1,'j','j'),
(11,6,5,'z','z'),
(12,6,2,'c','c'),
(13,8,0,'a','a'),
(14,2,1,'q','q'),
(15,6,8,'y','y'),
(16,8,1,NULL,NULL),
(17,3,1,'r','r'),
(18,3,9,'v','v'),
(19,9,1,NULL,NULL),
(20,6,5,'r','r');
CREATE TABLE t2 (
pk int NOT NULL,
col_int_nokey int DEFAULT NULL,
col_int_key int DEFAULT NULL,
col_varchar_key varchar(1) DEFAULT NULL,
col_varchar_nokey varchar(1) DEFAULT NULL,
PRIMARY KEY(pk),
KEY col_int_key(col_int_key),
KEY col_varchar_key(col_varchar_key, col_int_key)
);
INSERT INTO t2 VALUES
(10,NULL,8,'x','x'),
(11,8,7,'d','d'),
(12,1,1,'r','r'),
(13,9,7,'f','f'),
(14,4,9,'y','y'),
(15,3,NULL,'u','u'),
(16,2,1,'m','m'),
(17,NULL,9,NULL,NULL),
(18,2,2,'o','o'),
(19,NULL,9,'w','w'),
(20,6,2,'m','m'),
(21,7,4,'q','q'),
(22,2,0,NULL,NULL),
(23,5,4,'d','d'),
(24,7,8,'g','g'),
(25,6,NULL,'x','x'),
(26,6,NULL,'f','f'),
(27,2,0,'p','p'),
(28,9,NULL,'j','j'),
(29,6,8,'c','c');
SELECT alias1.col_varchar_nokey AS field1
FROM (SELECT sq1_alias1.*
FROM t1 AS sq1_alias1 INNER JOIN t2 AS sq1_alias2
ON sq1_alias2.col_varchar_key = sq1_alias1.col_varchar_nokey) AS alias1
INNER JOIN t1 AS alias2
ON alias2.col_int_key = alias1.col_int_key
WHERE (alias1.col_int_key, alias2.col_int_nokey) NOT IN
(SELECT sq2_alias1.pk AS sq2_field1, COUNT(sq2_alias2.pk) AS sq2_field2
FROM t2 AS sq2_alias1, t2 AS sq2_alias2
GROUP BY sq2_field1) AND
alias1.col_varchar_key <= 's'
HAVING field1 != 'r'
ORDER BY CONCAT(alias2.col_varchar_nokey, alias2.col_varchar_nokey) DESC, field1;
field1
c
c
c
c
c
j
j
j
j
j
j
j
p
p
p
p
p
p
p
q
q
q
q
q
q
q
DROP TABLE t1, t2;
# Bug#19778967: Assert failed in JOIN::optimize
CREATE TABLE t1(a INTEGER);
CREATE VIEW v1 AS SELECT * FROM t1;
INSERT INTO t1 VALUES(0), (1);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT * FROM t1 WHERE a <ANY (SELECT a FROM v1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
2	SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where <nop>((`test`.`t1`.`a` < (/* select#2 */ select max(`test`.`t1`.`a`) from `test`.`t1`)))
PREPARE s FROM "SELECT * FROM t1 WHERE a <ANY (SELECT a FROM v1)";
EXECUTE s;
a
0
EXECUTE s;
a
0
DEALLOCATE PREPARE s;
DROP VIEW v1;
DROP TABLE t1;
#
# Bug#20615023 SIGNAL 11 IN ITEM_FIELD::RESULT_TYPE DURING 1ST EXECUTION OF PREPARED STMT
#
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES(1),(2);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT 1 as col FROM t1 WHERE a IN (SELECT a FROM DUAL WHERE 0);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using where
2	DEPENDENT SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select 1 AS `col` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t1`.`a` from DUAL  where false))
SELECT 1 as col FROM t1 WHERE a IN (SELECT a FROM DUAL WHERE 0);
col
EXPLAIN SELECT 1 as col FROM t1 WHERE a IN (SELECT a FROM DUAL HAVING 0);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using where
2	DEPENDENT SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible HAVING
Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select 1 AS `col` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t1`.`a` having false))
SELECT 1 as col FROM t1 WHERE a IN (SELECT a FROM DUAL HAVING 0);
col
SELECT 1 as col FROM t1 WHERE a IN (SELECT a FROM DUAL LIMIT 0);
ERROR 42000: This version of MySQL doesn't yet support 'LIMIT & IN/ALL/ANY/SOME subquery'
EXPLAIN SELECT 1 as col FROM t1
WHERE a IN (SELECT a FROM DUAL WHERE 3 IN (SELECT a FROM t1));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using where
2	DEPENDENT SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
3	SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select 1 AS `col` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t1`.`a` from DUAL  where false))
PREPARE s FROM 'SELECT 1 as col FROM t1
WHERE a IN (SELECT a FROM DUAL WHERE 3 IN (SELECT a FROM t1))';
EXECUTE s;
col
EXECUTE s;
col
DROP TABLE t1;
#
# Bug#21038929 SINGLE-TABLE SCALAR SUBQUERY WITH LIMIT AND ORDER BY GIVES WRONG RESULTS
#
CREATE TABLE t1 (t1_id INT PRIMARY KEY);
INSERT INTO t1 VALUES(1),(2),(3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT * FROM t1 GROUP BY t1_id ORDER BY t1_id DESC LIMIT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	PRIMARY	PRIMARY	4	NULL	1	100.00	Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`t1_id` AS `t1_id` from `test`.`t1` group by `test`.`t1`.`t1_id` order by `test`.`t1`.`t1_id` desc limit 1
SELECT * FROM t1 GROUP BY t1_id ORDER BY t1_id DESC LIMIT 1;
t1_id
3
EXPLAIN SELECT (SELECT * FROM t1 GROUP BY t1_id ORDER BY t1_id DESC LIMIT 1) AS C FROM DUAL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
2	SUBQUERY	t1	NULL	index	PRIMARY	PRIMARY	4	NULL	1	100.00	Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select (/* select#2 */ select `test`.`t1`.`t1_id` from `test`.`t1` group by `test`.`t1`.`t1_id` order by `test`.`t1`.`t1_id` desc limit 1) AS `C`
SELECT (SELECT * FROM t1 GROUP BY t1_id ORDER BY t1_id DESC LIMIT 1) AS C FROM DUAL;
C
3
# Without LIMIT, ORDER BY is removed from subquery
EXPLAIN SELECT * FROM t1 GROUP BY t1_id ORDER BY t1_id DESC;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	PRIMARY	PRIMARY	4	NULL	3	100.00	Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`t1_id` AS `t1_id` from `test`.`t1` group by `test`.`t1`.`t1_id` order by `test`.`t1`.`t1_id` desc
SELECT * FROM t1 GROUP BY t1_id ORDER BY t1_id DESC;
t1_id
3
2
1
EXPLAIN SELECT (SELECT * FROM t1 GROUP BY t1_id ORDER BY t1_id DESC) AS C FROM DUAL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
2	SUBQUERY	t1	NULL	index	PRIMARY	PRIMARY	4	NULL	3	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select (/* select#2 */ select `test`.`t1`.`t1_id` from `test`.`t1` group by `test`.`t1`.`t1_id`) AS `C`
SELECT (SELECT * FROM t1 GROUP BY t1_id ORDER BY t1_id DESC) AS C FROM DUAL;
ERROR 21000: Subquery returns more than 1 row
DROP TABLE t1;
#
# Bug#21251627 ASSERT `ORDER == __NULL && !SELECT_DISTINCT' AT JOIN::REPLACE_INDEX_SUBQUERY
#
CREATE TABLE t1 (
pk INT NOT NULL PRIMARY KEY,
col_varchar VARCHAR(1)
) ENGINE=INNODB;
INSERT INTO t1 VALUES (1, 'v');
CREATE TABLE t2 ENGINE=INNODB SELECT
col_varchar AS field1,
pk AS field2
FROM t1
WHERE col_varchar = col_varchar
GROUP BY field2
HAVING field1 >= 'nw';
SELECT * FROM t2;
field1	field2
v	1
SELECT * FROM t2 WHERE (field1, field2) IN (
SELECT
col_varchar AS field1,
pk AS field2
FROM t1
WHERE col_varchar = col_varchar
GROUP BY field2
HAVING field1 >= 'nw'
);
field1	field2
v	1
DROP TABLE t1,t2;
#
# Bug#21211187 CRASH IN JOIN::UPDATE_DEPEND_MAP
#
CREATE TABLE t1 (a INT PRIMARY KEY) ENGINE=INNODB;
CREATE TABLE t0(a INT);
INSERT INTO t0 VALUES();
SELECT 1 FROM t1
WHERE 1 IN
(
SELECT 1 FROM t1
WHERE
(
SELECT a
FROM (SELECT  1 FROM t1) q
ORDER BY (SELECT (SELECT  1 FROM t1) FROM t0 HAVING a) DESC
LIMIT 1
)
)
;
1
DROP TABLE t0,t1;
#
# Bug#21292102 CRASH IN GET_SORT_BY_TABLE()
#
set sql_mode="";
CREATE TABLE t (a INT) ENGINE=INNODB;
SELECT 1 FROM t WHERE 1 IN
(
SELECT 1 FROM t WHERE 1 <=>
(
EXISTS
(
SELECT a FROM (SELECT 1) s
GROUP BY 1 <= ANY
(
SELECT 1 FROM t HAVING a
)
HAVING 1
)
)
);
1
SET @@SQL_MODE=DEFAULT;
DROP TABLE t;
SET DEFAULT_STORAGE_ENGINE=INNODB;
#
# Bug#21205577 ASSERTION FAILED: (INT)IDX >= 0 && IDX < PARENT_JOIN->TABLES
#
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES(1);
CREATE TABLE t2(a INT);
INSERT INTO t2 VALUES(1),(1),(1),(1),(1);
SELECT
(
(SELECT 1 IN (SELECT 1 FROM t1 AS x1))
IN
(
SELECT 1 FROM t2
WHERE a IN (SELECT 4 FROM t1 AS x2)
)
) AS result
FROM t1 AS x3;
result
0
SELECT
(
(36, (SELECT 1 IN (SELECT 1 FROM t1 AS x1)))
IN
(
SELECT 36, 1 FROM t2
WHERE a IN (SELECT 4 FROM t1 AS x2)
)
) AS result
FROM t1 AS x3;
result
0
DROP TABLE t1,t2;
# Bug#21574933 Extra rows with derived table in subquery + XOR
CREATE TABLE t1 (
pk INT NOT NULL,
col_date DATE,
PRIMARY KEY (pk)
);
INSERT INTO t1 VALUES (1,'2008-06-18'), (4,NULL);
CREATE TABLE t2 (
col_date DATE,
pk INT NOT NULL,
PRIMARY KEY (pk)
);
INSERT INTO t2 VALUES ('2002-02-02',1);
CREATE TABLE t3 (
pk INT NOT NULL,
col_varchar VARCHAR(1),
col_int_key INT,
col_int INT,
PRIMARY KEY (pk),
KEY col_int_key (col_int_key)
);
INSERT INTO t3 VALUES (4,'l',2,2);
explain SELECT pk,
col_int IN
(SELECT innr1.pk
FROM t1 AS innr2
LEFT JOIN (SELECT pk, col_date FROM t2) AS innr1
ON innr2.col_date <> innr1.col_date
WHERE outr.col_int_key < 6) AS x
FROM t3 AS outr;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	outr	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
2	DEPENDENT SUBQUERY	innr2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using where
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1276	Field or reference 'test.outr.col_int_key' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`outr`.`pk` AS `pk`,<in_optimizer>(`test`.`outr`.`col_int`,<exists>(/* select#2 */ select `test`.`t2`.`pk` from `test`.`t1` `innr2` left join (`test`.`t2`) on((`test`.`innr2`.`col_date` <> `test`.`t2`.`col_date`)) where ((`test`.`outr`.`col_int_key` < 6) and <if>(outer_field_is_not_null, ((<cache>(`test`.`outr`.`col_int`) = `test`.`t2`.`pk`) or (`test`.`t2`.`pk` is null)), true)) having <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t2`.`pk`), true))) AS `x` from `test`.`t3` `outr`
SELECT pk,
col_int IN
(SELECT innr1.pk
FROM t1 AS innr2
LEFT JOIN (SELECT pk, col_date FROM t2) AS innr1
ON innr2.col_date <> innr1.col_date
WHERE outr.col_int_key < 6) AS x
FROM t3 AS outr;
pk	x
4	NULL
SELECT pk,
col_int IN
(SELECT innr1.pk
FROM t1 AS innr2
LEFT JOIN (SELECT pk, date'2015-01-01' AS d FROM t2) AS innr1
ON innr2.col_date <> innr1.d
WHERE outr.col_int_key < 6) AS x
FROM t3 AS outr;
pk	x
4	NULL
DROP TABLE t1, t2, t3;
#
# Bug#22090717 WRONG RESULT FOR SELECT NULL IN (<SUBQUERY>)
#
CREATE TABLE t1(a MEDIUMTEXT CHARSET utf8mb3) ENGINE=INNODB;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES(0x6F76786E7875);
SELECT NULL IN (SELECT 1 FROM t1);
NULL IN (SELECT 1 FROM t1)
NULL
SELECT (NULL AND 1) IN (SELECT 1 FROM t1);
(NULL AND 1) IN (SELECT 1 FROM t1)
NULL
SELECT (NULL, 1) IN (SELECT 1,1 FROM t1);
(NULL, 1) IN (SELECT 1,1 FROM t1)
NULL
SELECT (NULL, NULL) IN (SELECT 1,1 FROM t1);
(NULL, NULL) IN (SELECT 1,1 FROM t1)
NULL
SELECT (NULL OR 1) IN (SELECT 1 FROM t1);
(NULL OR 1) IN (SELECT 1 FROM t1)
1
SELECT ISNULL(NULL) IN  (SELECT 1 FROM t1);
ISNULL(NULL) IN  (SELECT 1 FROM t1)
1
SELECT (NULL IS NULL) IN  (SELECT 1 FROM t1);
(NULL IS NULL) IN  (SELECT 1 FROM t1)
1
DELETE FROM t1;
SELECT NULL IN (SELECT 1 FROM t1);
NULL IN (SELECT 1 FROM t1)
0
SELECT (NULL AND 1) IN (SELECT 1 FROM t1);
(NULL AND 1) IN (SELECT 1 FROM t1)
0
SELECT (NULL, 1) IN (SELECT 1,1 FROM t1);
(NULL, 1) IN (SELECT 1,1 FROM t1)
0
SELECT (NULL, NULL) IN (SELECT 1,1 FROM t1);
(NULL, NULL) IN (SELECT 1,1 FROM t1)
0
SELECT (NULL OR 1) IN (SELECT 1 FROM t1);
(NULL OR 1) IN (SELECT 1 FROM t1)
0
SELECT ISNULL(NULL) IN  (SELECT 1 FROM t1);
ISNULL(NULL) IN  (SELECT 1 FROM t1)
0
SELECT (NULL IS NULL) IN  (SELECT 1 FROM t1);
(NULL IS NULL) IN  (SELECT 1 FROM t1)
0
DROP TABLE t1;
#
# Bug#23049975: GROUP BY SENDING DIFFERENT RESULTS BETWEEN 5.6 AND 5.7
#
CREATE TABLE t1 (
t1_id int NOT NULL auto_increment,
join_val int default NULL,
PRIMARY KEY (t1_id)
);
INSERT INTO t1 VALUES (1,5);
CREATE TABLE t2 (
t2_id int NOT NULL auto_increment,
t2_val int default NULL,
join_val int NOT NULL,
PRIMARY KEY (t2_id)
);
INSERT INTO t2 VALUES (1,1,5);
CREATE TABLE t3 (
t3_id int NOT NULL auto_increment,
t3_t1_val int default NULL,
t3_val double(15,2) default NULL,
PRIMARY KEY  (t3_id),
KEY t3_t1_id_idx (t3_t1_val)
);
Warnings:
Warning	1681	Specifying number of digits for floating point data types is deprecated and will be removed in a future release.
INSERT INTO t3 VALUES (1,1,2.50);
EXPLAIN SELECT (SELECT t3.t3_val
FROM t3
WHERE t3.t3_t1_val = t1.t1_id) as CMS, t2.t2_val, t1.t1_id
FROM t1 INNER JOIN t2 ON t1.join_val = t2.join_val
WHERE t1.t1_id = 1 GROUP BY t2.t2_val;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	Using temporary
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
2	DEPENDENT SUBQUERY	t3	NULL	ref	t3_t1_id_idx	t3_t1_id_idx	5	const	1	100.00	NULL
Warnings:
Note	1276	Field or reference 'test.t1.t1_id' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select (/* select#2 */ select `test`.`t3`.`t3_val` from `test`.`t3` where (`test`.`t3`.`t3_t1_val` = '1')) AS `CMS`,`test`.`t2`.`t2_val` AS `t2_val`,'1' AS `t1_id` from `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`join_val` = '5')) group by `test`.`t2`.`t2_val`
SELECT (SELECT t3.t3_val
FROM t3
WHERE t3.t3_t1_val = t1.t1_id) as CMS, t2.t2_val, t1.t1_id
FROM t1 INNER JOIN t2 ON t1.join_val = t2.join_val
WHERE t1.t1_id = 1 GROUP BY t2.t2_val;
CMS	t2_val	t1_id
2.50	1	1
EXPLAIN SELECT (SELECT t3.t3_val
FROM t3
WHERE t3.t3_t1_val = t1.t1_id * t1.t1_id) as CMS, t2.t2_val, t1.t1_id
FROM t1 INNER JOIN t2 ON t1.join_val = t2.join_val
WHERE t1.t1_id = 1 GROUP BY t2.t2_val;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	Using temporary
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
2	DEPENDENT SUBQUERY	t3	NULL	ref	t3_t1_id_idx	t3_t1_id_idx	5	const	1	100.00	NULL
Warnings:
Note	1276	Field or reference 'test.t1.t1_id' of SELECT #2 was resolved in SELECT #1
Note	1276	Field or reference 'test.t1.t1_id' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select (/* select#2 */ select `test`.`t3`.`t3_val` from `test`.`t3` where (`test`.`t3`.`t3_t1_val` = ('1' * '1'))) AS `CMS`,`test`.`t2`.`t2_val` AS `t2_val`,'1' AS `t1_id` from `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`join_val` = '5')) group by `test`.`t2`.`t2_val`
SELECT (SELECT t3.t3_val
FROM t3
WHERE t3.t3_t1_val = t1.t1_id * t1.t1_id) as CMS, t2.t2_val, t1.t1_id
FROM t1 INNER JOIN t2 ON t1.join_val = t2.join_val
WHERE t1.t1_id = 1 GROUP BY t2.t2_val;
CMS	t2_val	t1_id
2.50	1	1
DROP TABLE t1, t2, t3;
#
# Bug#13915291 NOT IN (SUBQUERY) GIVES WRONG RESULT WHEN PREFIX INDEX OF INNER TABLE IS USED
#
CREATE TABLE t1 (
a1 char(8) DEFAULT NULL,
a2 char(8) DEFAULT NULL,
KEY a1 (a1(2))
) charset latin1;
CREATE TABLE t2 (
b1 char(8) DEFAULT NULL,
b2 char(8) DEFAULT NULL
) charset latin1;
INSERT INTO t1 VALUES
('1 - 00', '2 - 00'),('1 - 01', '2 - 01'),('1 - 02', '2 - 02');
INSERT INTO t2 VALUES
('1 - 01', '2 - 01'),('1 - 01', '2 - 01'),
('1 - 02', '2 - 02'),('1 - 02', '2 - 02'),('1 - 03', '2 - 03');
ANALYZE TABLE t1,t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
SELECT a1 FROM t1 WHERE a1 > '1';
a1
1 - 00
1 - 01
1 - 02
SELECT * FROM t2 WHERE b1 NOT IN ('1 - 00', '1 - 01', '1 - 02');
b1	b2
1 - 03	2 - 03
EXPLAIN SELECT * FROM t2 WHERE b1 NOT IN
(SELECT a1 FROM t1 FORCE INDEX(a1) WHERE a1 > '1');
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	5	100.00	Using where
2	SUBQUERY	t1	NULL	range	a1	a1	3	NULL	3	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`b1` AS `b1`,`test`.`t2`.`b2` AS `b2` from `test`.`t2` where <in_optimizer>(`test`.`t2`.`b1`,`test`.`t2`.`b1` in ( <materialize> (/* select#2 */ select `test`.`t1`.`a1` from `test`.`t1` FORCE INDEX (`a1`) where (`test`.`t1`.`a1` > '1') having true ), <primary_index_lookup>(`test`.`t2`.`b1` in <temporary table> on <auto_distinct_key> where ((`test`.`t2`.`b1` = `<materialized_subquery>`.`a1`)))) is false)
SELECT * FROM t2 WHERE b1 NOT IN
(SELECT a1 FROM t1 FORCE INDEX(a1) WHERE a1 > '1');
b1	b2
1 - 03	2 - 03
DROP TABLE t1,t2;
CREATE TABLE t1 (f1 INT NOT NULL);
CREATE VIEW v1 (a) AS SELECT f1 IN (SELECT f1 FROM t1) FROM t1;
SELECT * FROM v1;
a
drop view v1;
drop table t1;
#
# Bug #32773801: NOT IN CLAUSE GET WRONG RESULT
#
CREATE TABLE t1 (a INTEGER);
INSERT INTO t1 VALUES (1), (2), (3);
SELECT 2 IN ( SELECT 5 UNION SELECT NULL ) FROM t1;
2 IN ( SELECT 5 UNION SELECT NULL )
NULL
NULL
NULL
DROP TABLE t1;
set optimizer_switch=default;
