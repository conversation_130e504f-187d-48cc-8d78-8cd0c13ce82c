CREATE SCHEMA xtest;
CREATE TABLE xtest.xtable (doc <PERSON>);
INSERT INTO xtest.xtable (doc) VALUES
('{"scalar_10":10,
     "scalar_40":40,
     "scalar_red":"red",
     "scalar_banana":"banana",
     "scalar_true":true,
     "array_10_20_30":[10,20,30],
     "array_red_green_pear":["red","green","pear"],
     "array_true_false":[true, false],
     "object_scalar_10":{"scalar_10":10},
     "object_scalar_red":{"scalar_red":"red"}}');

Numeric literal in numeric literal
----------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 40
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 40
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Numeric literal in literal array
--------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 40
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 40
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Bool literal in literal array
-----------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: true
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: false
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: true
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: false
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 40
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: true
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: false
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 40
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: true
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: false
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

String literal in string literal
--------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "blue"
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "blue"
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "blue"
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "blue"
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "white"
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "blue"
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "white"
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "blue"
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

String literal in literal array
-------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "blue"
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "blue"
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "white"
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "white"
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Plain octets literal in literal array
-------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "pear"
              content_type: 0
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "apple"
                  content_type: 0
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "pear"
                  content_type: 0
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "plum"
                  content_type: 0
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "pear"
              content_type: 0
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "apple"
                  content_type: 0
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "pear"
                  content_type: 0
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "plum"
                  content_type: 0
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "banana"
              content_type: 0
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "apple"
                  content_type: 0
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "pear"
                  content_type: 0
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "plum"
                  content_type: 0
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "banana"
              content_type: 0
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "apple"
                  content_type: 0
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "pear"
                  content_type: 0
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "plum"
                  content_type: 0
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Plain octets literal in literal octets
--------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "pear"
              content_type: 0
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "pear"
              content_type: 0
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "pear"
              content_type: 0
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "pear"
              content_type: 0
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "banana"
              content_type: 0
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "pear"
              content_type: 0
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "banana"
              content_type: 0
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "pear"
              content_type: 0
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Literal bool literal in literal bool
------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: false
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: false
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Numeric literal in json octets array
------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[10, 20, 30]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[10, 20, 30]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 40
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[10, 20, 30]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 40
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[10, 20, 30]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Bool literal in json octets array
---------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[true, false]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[true, false]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[true, false]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[true, false]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

String literal in json octets array
-----------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "blue"
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[\"red\", \"green\", \"blue\"]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "blue"
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[\"red\", \"green\", \"blue\"]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "white"
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[\"red\", \"green\", \"blue\"]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "white"
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[\"red\", \"green\", \"blue\"]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Numeric literal in member
-------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 20
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 20
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Numeric member in literal scalar
--------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Numeric member in literal array
-------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Numeric member in json octets array
-----------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[10, 20, 30]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[10, 20, 30]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[10, 20, 30]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[10, 20, 30]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Bool literal in member
----------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_true"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_true"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: false
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_true"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: false
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_true"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Bool member in literal scalar
-----------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_true"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_true"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: true
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Bool member in literal array
----------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_true"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: true
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: false
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_true"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: true
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: false
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: true
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: false
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: true
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: false
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Bool member in json octets array
--------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_true"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[true, false]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_true"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[true, false]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[true, false]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[true, false]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

String literal in member
------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "white"
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "white"
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

String member in literal scalar
-------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

String member in literal array
------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

String member in json octets array
----------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[\"red\", \"green\", \"blue\"]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[\"red\", \"green\", \"blue\"]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[\"red\", \"green\", \"blue\"]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "[\"red\", \"green\", \"blue\"]"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Plain octets literal in member
------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "banana"
              content_type: 0
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "banana"
              content_type: 0
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "pear"
              content_type: 0
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "pear"
              content_type: 0
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

String member in plain octets literal
-------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Numeric literal in member array
-------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_10_20_30"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_10_20_30"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 40
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_10_20_30"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 40
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_10_20_30"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Numeric member in member array
------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_10_20_30"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_10_20_30"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_10_20_30"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_10_20_30"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Bool literal in member array
----------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: false
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_true_false"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_BOOL
            v_bool: false
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_true_false"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_true_false"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_true_false"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Bool member in member array
---------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_true"
            }
            name: "doc"
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_true_false"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_true"
            }
            name: "doc"
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_true_false"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_true_false"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_40"
            }
            name: "doc"
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_true_false"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

String literal in member array
------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_red_green_pear"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_red_green_pear"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "white"
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_red_green_pear"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "white"
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_red_green_pear"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

String member in member array
-----------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_red_green_pear"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_red_green_pear"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_red_green_pear"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "scalar_banana"
            }
            name: "doc"
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_red_green_pear"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Plain octets literal in member array
------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "pear"
              content_type: 0
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_red_green_pear"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "pear"
              content_type: 0
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_red_green_pear"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "banana"
              content_type: 0
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_red_green_pear"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "banana"
              content_type: 0
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "array_red_green_pear"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Json octets object in literal object
------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_blue\":\"blue\"}"
              content_type: 2
            }
          }
        }
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_blue\":\"blue\"}"
              content_type: 2
            }
          }
        }
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Literal object in json octets object
------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_blue"
              value {
                type: LITERAL
                literal {
                  type: V_STRING
                  v_string {
                    value: "blue"
                  }
                }
              }
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_blue"
              value {
                type: LITERAL
                literal {
                  type: V_STRING
                  v_string {
                    value: "blue"
                  }
                }
              }
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Json octets object in literal array
-----------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
            value {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "white"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
            value {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "white"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_blue\":\"blue\"}"
              content_type: 2
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
            value {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "white"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_blue\":\"blue\"}"
              content_type: 2
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
            value {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "white"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Literal object in literal array
-------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
            value {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "white"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
            value {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "white"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_blue"
              value {
                type: LITERAL
                literal {
                  type: V_STRING
                  v_string {
                    value: "blue"
                  }
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
            value {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "white"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_blue"
              value {
                type: LITERAL
                literal {
                  type: V_STRING
                  v_string {
                    value: "blue"
                  }
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
            value {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "white"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Literal object in document
--------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_blue"
              value {
                type: LITERAL
                literal {
                  type: V_STRING
                  v_string {
                    value: "blue"
                  }
                }
              }
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_blue"
              value {
                type: LITERAL
                literal {
                  type: V_STRING
                  v_string {
                    value: "blue"
                  }
                }
              }
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Json octets object in document
------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_blue\":\"blue\"}"
              content_type: 2
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_blue\":\"blue\"}"
              content_type: 2
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Literal object in member object
-------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_blue"
              value {
                type: LITERAL
                literal {
                  type: V_STRING
                  v_string {
                    value: "blue"
                  }
                }
              }
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_blue"
              value {
                type: LITERAL
                literal {
                  type: V_STRING
                  v_string {
                    value: "blue"
                  }
                }
              }
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Json octets object in member object
-----------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_blue\":\"blue\"}"
              content_type: 2
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_blue\":\"blue\"}"
              content_type: 2
            }
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Member object in literal object
-------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Member object in json octets object
-----------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "{\"scalar_10\":10}"
              content_type: 2
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Member object in literal array
------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
            value {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "white"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
            value {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "white"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
            value {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "white"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_red"
            }
            name: "doc"
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
            value {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "white"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

op+ directly to cont_in in literal array
----------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OPERATOR
          operator {
            name: "+"
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OPERATOR
          operator {
            name: "+"
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

Got expected error: CONT_IN expression requires operator that produce a JSON value. (code 5154)

op+ casted to uint in literal array
-----------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OPERATOR
          operator {
            name: "cast"
            param {
              type: OPERATOR
              operator {
                name: "+"
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "UNSIGNED INTEGER"
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OPERATOR
          operator {
            name: "cast"
            param {
              type: OPERATOR
              operator {
                name: "+"
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "UNSIGNED INTEGER"
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

Got expected error: CONT_IN expression requires operator that produce a JSON value. (code 5154)

literal uint in op+ casted to uint
----------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 30
          }
        }
        param {
          type: OPERATOR
          operator {
            name: "cast"
            param {
              type: OPERATOR
              operator {
                name: "+"
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "UNSIGNED INTEGER"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 30
          }
        }
        param {
          type: OPERATOR
          operator {
            name: "cast"
            param {
              type: OPERATOR
              operator {
                name: "+"
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "UNSIGNED INTEGER"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

Got expected error: CONT_IN expression requires operator that produce a JSON value. (code 5154)

op+ casted to json in literal array
-----------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OPERATOR
          operator {
            name: "cast"
            param {
              type: OPERATOR
              operator {
                name: "+"
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "JSON"
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OPERATOR
          operator {
            name: "cast"
            param {
              type: OPERATOR
              operator {
                name: "+"
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "JSON"
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

literal uint in op+ casted to json
----------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 30
          }
        }
        param {
          type: OPERATOR
          operator {
            name: "cast"
            param {
              type: OPERATOR
              operator {
                name: "+"
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "JSON"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 30
          }
        }
        param {
          type: OPERATOR
          operator {
            name: "cast"
            param {
              type: OPERATOR
              operator {
                name: "+"
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "JSON"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

Literal NULL literal in literal NULL
------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

literal NULL in literal array
-----------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_NULL
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_NULL
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

literal NULL in literal object
------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_null"
              value {
                type: LITERAL
                literal {
                  type: V_NULL
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_null"
              value {
                type: LITERAL
                literal {
                  type: V_NULL
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

literal NULL in member object 
------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
              value: "object_scalar_10"
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

literal NULL in root document 
------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
            }
            name: "doc"
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_NULL
          }
        }
        param {
          type: IDENT
          identifier {
            document_path {
              type: MEMBER
            }
            name: "doc"
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

Function concat in string literal
---------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "concat"
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "concat"
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

Got expected error: CONT_IN expression requires function that produce a JSON value. (code 5154)

String literal in function concat
---------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "concat"
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "concat"
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

Got expected error: CONT_IN expression requires function that produce a JSON value. (code 5154)

Quoted function concat in string literal
----------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_quote"
            }
            param {
              type: FUNC_CALL
              function_call {
                name {
                  name: "concat"
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "red"
                    }
                  }
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "green"
                    }
                  }
                }
              }
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_quote"
            }
            param {
              type: FUNC_CALL
              function_call {
                name {
                  name: "concat"
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "red"
                    }
                  }
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "green"
                    }
                  }
                }
              }
            }
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

String literal in quoted function concat
----------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_quote"
            }
            param {
              type: FUNC_CALL
              function_call {
                name {
                  name: "concat"
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "red"
                    }
                  }
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "green"
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_quote"
            }
            param {
              type: FUNC_CALL
              function_call {
                name {
                  name: "concat"
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "red"
                    }
                  }
                }
                param {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "green"
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

JSON_ARRAY - Create JSON array
------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_array"
            }
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: true
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_array"
            }
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_BOOL
                v_bool: true
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

JSON_ARRAY_APPEND - Append data to JSON document
------------------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_array_append"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_array_append"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

JSON_ARRAY_INSERT - Insert into JSON array
------------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_array_insert"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$[1]"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_array_insert"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$[1]"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

JSON_CONTAINS - Whether JSON document contains specific object at path
----------------------------------------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_contains"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_contains"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

Got expected error: CONT_IN expression requires function that produce a JSON value. (code 5154)

JSON_CONTAINS_PATH - Whether JSON document contains any data at path
--------------------------------------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_contains_path"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$[1]"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_contains_path"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$[1]"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

Got expected error: CONT_IN expression requires function that produce a JSON value. (code 5154)

JSON_DEPTH - Maximum depth of JSON document
-------------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_depth"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_STRING
            v_string {
              value: "red"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_depth"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

Got expected error: CONT_IN expression requires function that produce a JSON value. (code 5154)

JSON_EXTRACT - Return data from JSON document
---------------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_extract"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
                value {
                  type: OBJECT
                  object {
                    fld {
                      key: "scalar_10"
                      value {
                        type: LITERAL
                        literal {
                          type: V_SINT
                          v_signed_int: 10
                        }
                      }
                    }
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "white"
                    }
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$[1]"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_extract"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
                value {
                  type: OBJECT
                  object {
                    fld {
                      key: "scalar_10"
                      value {
                        type: LITERAL
                        literal {
                          type: V_SINT
                          v_signed_int: 10
                        }
                      }
                    }
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "white"
                    }
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$[1]"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

JSON_INSERT - Insert data into JSON document
--------------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_insert"
            }
            param {
              type: OBJECT
              object {
                fld {
                  key: "scalar_blue"
                  value {
                    type: LITERAL
                    literal {
                      type: V_STRING
                      v_string {
                        value: "blue"
                      }
                    }
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$.scalar_10"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_insert"
            }
            param {
              type: OBJECT
              object {
                fld {
                  key: "scalar_blue"
                  value {
                    type: LITERAL
                    literal {
                      type: V_STRING
                      v_string {
                        value: "blue"
                      }
                    }
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$.scalar_10"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

JSON_KEYS - Array of keys from JSON document
--------------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "scalar_10"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_keys"
            }
            param {
              type: IDENT
              identifier {
                document_path {
                  type: MEMBER
                }
                name: "doc"
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "scalar_10"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_keys"
            }
            param {
              type: IDENT
              identifier {
                document_path {
                  type: MEMBER
                }
                name: "doc"
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

JSON_LENGTH - Number of elements in JSON document
-------------------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_length"
            }
            param {
              type: IDENT
              identifier {
                document_path {
                  type: MEMBER
                }
                name: "doc"
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_length"
            }
            param {
              type: IDENT
              identifier {
                document_path {
                  type: MEMBER
                }
                name: "doc"
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

Got expected error: CONT_IN expression requires function that produce a JSON value. (code 5154)

JSON_MERGE - Merge JSON documents
---------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_blue"
              value {
                type: LITERAL
                literal {
                  type: V_STRING
                  v_string {
                    value: "blue"
                  }
                }
              }
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_merge"
            }
            param {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            param {
              type: OBJECT
              object {
                fld {
                  key: "scalar_blue"
                  value {
                    type: LITERAL
                    literal {
                      type: V_STRING
                      v_string {
                        value: "blue"
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_blue"
              value {
                type: LITERAL
                literal {
                  type: V_STRING
                  v_string {
                    value: "blue"
                  }
                }
              }
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_merge"
            }
            param {
              type: OBJECT
              object {
                fld {
                  key: "scalar_10"
                  value {
                    type: LITERAL
                    literal {
                      type: V_SINT
                      v_signed_int: 10
                    }
                  }
                }
              }
            }
            param {
              type: OBJECT
              object {
                fld {
                  key: "scalar_blue"
                  value {
                    type: LITERAL
                    literal {
                      type: V_STRING
                      v_string {
                        value: "blue"
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Warnings generated:
WARNING | 1287 | 'JSON_MERGE' is deprecated and will be removed in a future release. Please use JSON_MERGE_PRESERVE/JSON_MERGE_PATCH instead
WARNING | 1287 | 'JSON_MERGE' is deprecated and will be removed in a future release. Please use JSON_MERGE_PRESERVE/JSON_MERGE_PATCH instead
Got expected error (code 0)

JSON_OBJECT - Create JSON object
--------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_object"
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "scalar_10"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_object"
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "scalar_10"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

JSON_QUOTE - Quote JSON document
--------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_quote"
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_quote"
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

JSON_REMOVE - Remove data from JSON document
--------------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 20
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_remove"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$[1]"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 20
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_remove"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$[1]"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
0	1
command ok
Got expected error (code 0)

JSON_REPLACE - Replace values in JSON document
----------------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 40
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_replace"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$[1]"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 40
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 40
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_replace"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 10
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 20
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_SINT
                    v_signed_int: 30
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$[1]"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 40
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

JSON_SEARCH - Path to value within JSON document
------------------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "$[1]"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_search"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "red"
                    }
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "green"
                    }
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "blue"
                    }
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "one"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: LITERAL
          literal {
            type: V_OCTETS
            v_octets {
              value: "$[1]"
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_search"
            }
            param {
              type: ARRAY
              array {
                value {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "red"
                    }
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "green"
                    }
                  }
                }
                value {
                  type: LITERAL
                  literal {
                    type: V_STRING
                    v_string {
                      value: "blue"
                    }
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "one"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

JSON_SET - Insert data into JSON document
-----------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_set"
            }
            param {
              type: OBJECT
              object {
                fld {
                  key: "scalar_blue"
                  value {
                    type: LITERAL
                    literal {
                      type: V_STRING
                      v_string {
                        value: "blue"
                      }
                    }
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$.scalar_10"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: OBJECT
          object {
            fld {
              key: "scalar_10"
              value {
                type: LITERAL
                literal {
                  type: V_SINT
                  v_signed_int: 10
                }
              }
            }
          }
        }
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_set"
            }
            param {
              type: OBJECT
              object {
                fld {
                  key: "scalar_blue"
                  value {
                    type: LITERAL
                    literal {
                      type: V_STRING
                      v_string {
                        value: "blue"
                      }
                    }
                  }
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_OCTETS
                v_octets {
                  value: "$.scalar_10"
                }
              }
            }
            param {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

cont_in	not_cont_in
1	0
command ok
Got expected error (code 0)

JSON_TYPE - Type of JSON value
------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_type"
            }
            param {
              type: OBJECT
              object {
                fld {
                  key: "scalar_blue"
                  value {
                    type: LITERAL
                    literal {
                      type: V_STRING
                      v_string {
                        value: "blue"
                      }
                    }
                  }
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_type"
            }
            param {
              type: OBJECT
              object {
                fld {
                  key: "scalar_blue"
                  value {
                    type: LITERAL
                    literal {
                      type: V_STRING
                      v_string {
                        value: "blue"
                      }
                    }
                  }
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

Got expected error: CONT_IN expression requires function that produce a JSON value. (code 5154)

JSON_UNQUOTE - Unquote JSON value
---------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_unquote"
            }
            param {
              type: IDENT
              identifier {
                document_path {
                  type: MEMBER
                  value: "scalar_red"
                }
                name: "doc"
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_unquote"
            }
            param {
              type: IDENT
              identifier {
                document_path {
                  type: MEMBER
                  value: "scalar_red"
                }
                name: "doc"
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "red"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "green"
                }
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_STRING
                v_string {
                  value: "blue"
                }
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

Got expected error: CONT_IN expression requires function that produce a JSON value. (code 5154)

JSON_VALID - Whether JSON value is valid
----------------------------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_valid"
            }
            param {
              type: OBJECT
              object {
                fld {
                  key: "scalar_blue"
                  value {
                    type: LITERAL
                    literal {
                      type: V_STRING
                      v_string {
                        value: "blue"
                      }
                    }
                  }
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "cont_in"
  }
  projection {
    source {
      type: OPERATOR
      operator {
        name: "not_cont_in"
        param {
          type: FUNC_CALL
          function_call {
            name {
              name: "json_valid"
            }
            param {
              type: OBJECT
              object {
                fld {
                  key: "scalar_blue"
                  value {
                    type: LITERAL
                    literal {
                      type: V_STRING
                      v_string {
                        value: "blue"
                      }
                    }
                  }
                }
              }
            }
          }
        }
        param {
          type: ARRAY
          array {
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 10
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 20
              }
            }
            value {
              type: LITERAL
              literal {
                type: V_SINT
                v_signed_int: 30
              }
            }
          }
        }
      }
    }
    alias: "not_cont_in"
  }
}

Got expected error: CONT_IN expression requires function that produce a JSON value. (code 5154)

Number of parameters
--------------------
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
      }
    }
    alias: "cont_in"
  }
}

Got expected error: CONT_IN expression requires two parameters. (code 5151)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
      }
    }
    alias: "cont_in"
  }
}

Got expected error: CONT_IN expression requires two parameters. (code 5151)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 20
          }
        }
      }
    }
    alias: "cont_in"
  }
}

cont_in
0
command ok
Got expected error (code 0)
send Mysqlx.Crud.Find {
  collection {
    name: "xtable"
    schema: "xtest"
  }
  data_model: TABLE
  projection {
    source {
      type: OPERATOR
      operator {
        name: "cont_in"
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 10
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 20
          }
        }
        param {
          type: LITERAL
          literal {
            type: V_SINT
            v_signed_int: 30
          }
        }
      }
    }
    alias: "cont_in"
  }
}

Got expected error: CONT_IN expression requires two parameters. (code 5151)
Mysqlx.Ok {
  msg: "bye!"
}
ok
