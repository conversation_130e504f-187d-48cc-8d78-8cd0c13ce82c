/*****************************************************************************

Copyright (c) 1995, 2025, Oracle and/or its affiliates.
Copyright (c) 2008, Google Inc.

Portions of this file contain modifications contributed and copyrighted by
Google, Inc. Those modifications are gratefully acknowledged and are described
briefly in the InnoDB documentation. The contributions by Google are
incorporated with their permission, and subject to the conditions contained in
the file COPYING.Google.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License, version 2.0, as published by the
Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0,
for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

*****************************************************************************/

/** @file include/sync0rw.ic
 The read-write lock (for threads)

 Created 9/11/1995 Heikki Tuuri
 *******************************************************/

#include "os0event.h"

/** Lock an rw-lock in shared mode for the current thread. If the rw-lock is
 locked in exclusive mode, or there is an exclusive lock request waiting,
 the function spins a preset time (controlled by srv_n_spin_wait_rounds),
 waiting for the lock before suspending the thread.
 @param[in] lock pointer to rw-lock
 @param[in] pass pass value; !=0, if the lock will be passed to another thread
 to unlock
 @param[in] location location where requested */
void rw_lock_s_lock_spin(rw_lock_t *lock, ulint pass, ut::Location location);
#ifdef UNIV_DEBUG
/** Inserts the debug information for an rw-lock.
@param[in] lock rw-lock
@param[in] pass pass value
@param[in] lock_type lock type
@param[in] location location where requested */
void rw_lock_add_debug_info(rw_lock_t *lock, ulint pass, ulint lock_type,
                            ut::Location location);
/** Removes a debug information struct for an rw-lock. */
void rw_lock_remove_debug_info(rw_lock_t *lock,  /*!< in: rw-lock */
                               ulint pass,       /*!< in: pass value */
                               ulint lock_type); /*!< in: lock type */
#endif                                           /* UNIV_DEBUG */

/** Check if there are threads waiting for the rw-lock.
 @return true if waiters, false otherwise */
static inline bool rw_lock_get_waiters(
    const rw_lock_t *lock) /*!< in: rw-lock */
{
  return lock->waiters.load();
}

/** Sets lock->waiters to true. It is not an error if lock->waiters is already
 true. */
static inline void rw_lock_set_waiter_flag(
    rw_lock_t *lock) /*!< in/out: rw-lock */
{
  bool zero = false;
  lock->waiters.compare_exchange_strong(zero, true);
}

/** Resets lock->waiters to false. It is not an error if lock->waiters is
 already false. */
static inline void rw_lock_reset_waiter_flag(
    rw_lock_t *lock) /*!< in/out: rw-lock */
{
  bool one = true;
  lock->waiters.compare_exchange_strong(one, false);
}

/** Returns the write-status of the lock - this function made more sense
 with the old rw_lock implementation.
 @return RW_LOCK_NOT_LOCKED, RW_LOCK_X, RW_LOCK_X_WAIT, RW_LOCK_SX */
static inline ulint rw_lock_get_writer(
    const rw_lock_t *lock) /*!< in: rw-lock */
{
  lint lock_word = lock->lock_word;

  ut_ad(lock_word <= X_LOCK_DECR);
  if (lock_word > X_LOCK_HALF_DECR) {
    /* return NOT_LOCKED in s-lock state, like the writer
    member of the old lock implementation. */
    return (RW_LOCK_NOT_LOCKED);
  } else if (lock_word > 0) {
    /* sx-locked, no x-locks */
    return (RW_LOCK_SX);
  } else if (lock_word == 0 || lock_word == -X_LOCK_HALF_DECR ||
             lock_word <= -X_LOCK_DECR) {
    /* x-lock with sx-lock is also treated as RW_LOCK_EX */
    return (RW_LOCK_X);
  } else {
    /* x-waiter with sx-lock is also treated as RW_LOCK_WAIT_EX
    e.g. -X_LOCK_HALF_DECR < lock_word < 0 : without sx
         -X_LOCK_DECR < lock_word < -X_LOCK_HALF_DECR : with sx */
    return (RW_LOCK_X_WAIT);
  }
}

/** Returns the number of readers (s-locks).
 @return number of readers */
static inline ulint rw_lock_get_reader_count(
    const rw_lock_t *lock) /*!< in: rw-lock */
{
  lint lock_word = lock->lock_word;
  ut_ad(lock_word <= X_LOCK_DECR);

  if (lock_word > X_LOCK_HALF_DECR) {
    /* s-locked, no x-waiter */
    return (X_LOCK_DECR - lock_word);
  } else if (lock_word > 0) {
    /* s-locked, with sx-locks only */
    return (X_LOCK_HALF_DECR - lock_word);
  } else if (lock_word == 0) {
    /* x-locked */
    return (0);
  } else if (lock_word > -X_LOCK_HALF_DECR) {
    /* s-locked, with x-waiter */
    return ((ulint)(-lock_word));
  } else if (lock_word == -X_LOCK_HALF_DECR) {
    /* x-locked with sx-locks */
    return (0);
  } else if (lock_word > -X_LOCK_DECR) {
    /* s-locked, with x-waiter and sx-lock */
    return ((ulint)(-(lock_word + X_LOCK_HALF_DECR)));
  }
  /* no s-locks */
  return (0);
}

/** Returns the value of writer_count for the lock. Does not reserve the lock
 mutex, so the caller must be sure it is not changed during the call.
 @return value of writer_count */
static inline ulint rw_lock_get_x_lock_count(
    const rw_lock_t *lock) /*!< in: rw-lock */
{
  lint lock_copy = lock->lock_word;
  ut_ad(lock_copy <= X_LOCK_DECR);

  if (lock_copy == 0 || lock_copy == -X_LOCK_HALF_DECR) {
    /* "1 x-lock" or "1 x-lock + sx-locks" */
    return (1);
  } else if (lock_copy > -X_LOCK_DECR) {
    /* s-locks, one or more sx-locks if > 0, or x-waiter if < 0 */
    return (0);
  } else if (lock_copy > -(X_LOCK_DECR + X_LOCK_HALF_DECR)) {
    /* no s-lock, no sx-lock, 2 or more x-locks.
    First 2 x-locks are set with -X_LOCK_DECR,
    all other recursive x-locks are set with -1 */
    return (2 - (lock_copy + X_LOCK_DECR));
  } else {
    /* no s-lock, 1 or more sx-lock, 2 or more x-locks.
    First 2 x-locks are set with -(X_LOCK_DECR + X_LOCK_HALF_DECR),
    all other recursive x-locks are set with -1 */
    return (2 - (lock_copy + X_LOCK_DECR + X_LOCK_HALF_DECR));
  }
}

/** Returns the number of sx-lock for the lock. Does not reserve the lock
 mutex, so the caller must be sure it is not changed during the call.
 @return value of sx-lock count */
static inline ulint rw_lock_get_sx_lock_count(
    const rw_lock_t *lock) /*!< in: rw-lock */
{
#ifdef UNIV_DEBUG
  lint lock_copy = lock->lock_word;

  ut_ad(lock_copy <= X_LOCK_DECR);

  while (lock_copy < 0) {
    lock_copy += X_LOCK_DECR;
  }

  if (lock_copy > 0 && lock_copy <= X_LOCK_HALF_DECR) {
    return (lock->sx_recursive);
  }

  return (0);
#else  /* UNIV_DEBUG */
  return (lock->sx_recursive);
#endif /* UNIV_DEBUG */
}

/** Two different implementations for decrementing the lock_word of a rw_lock:
 one for systems supporting atomic operations, one for others. This does
 does not support recursive x-locks: they should be handled by the caller and
 need not be atomic since they are performed by the current lock holder.
 Returns true if the decrement was made, false if not.
 @return true if decr occurs */
ALWAYS_INLINE
bool rw_lock_lock_word_decr(rw_lock_t *lock, /*!< in/out: rw-lock */
                            ulint amount,    /*!< in: amount to decrement */
                            lint threshold)  /*!< in: threshold of judgement */
{
  int32_t local_lock_word;

  os_rmb;
  local_lock_word = lock->lock_word;
  while (local_lock_word > threshold) {
    if (lock->lock_word.compare_exchange_strong(local_lock_word,
                                                local_lock_word - amount)) {
      return (true);
    }
  }
  return (false);
}

/** Increments lock_word the specified amount and returns new value.
 @return lock->lock_word after increment */
static inline lint rw_lock_lock_word_incr(
    rw_lock_t *lock, /*!< in/out: rw-lock */
    ulint amount)    /*!< in: amount of increment */
{
  return (lock->lock_word.fetch_add(amount) + amount);
}

static inline void rw_lock_set_writer_id_and_recursion_flag(rw_lock_t *lock,
                                                            bool recursive) {
  lock->writer_thread.store(std::this_thread::get_id(),
                            std::memory_order_relaxed);
  lock->recursive.store(recursive, std::memory_order_release);
}

ALWAYS_INLINE
bool rw_lock_s_lock_low(rw_lock_t *lock, ulint pass [[maybe_unused]],
                        ut::Location location) {
  if (!rw_lock_lock_word_decr(lock, 1, 0)) {
    /* Locking did not succeed */
    return false;
  }

  ut_d(rw_lock_add_debug_info(lock, pass, RW_LOCK_S, location));

  /* These debugging values are not set safely: they may be incorrect
  or even refer to a line that is invalid for the file name. */
  lock->last_s_file_name = location.filename;
  /* last_s_line is uint16_t */
  ut_ad(location.line <=
        std::numeric_limits<decltype(lock->last_s_line)>::max());
  lock->last_s_line = location.line;
  lock->reader_thread.xor_thing(std::this_thread::get_id());

  return true; /* locking succeeded */
}

/** NOTE! Use the corresponding macro, not directly this function! Lock an
 rw-lock in shared mode for the current thread. If the rw-lock is locked
 in exclusive mode, or there is an exclusive lock request waiting, the
 function spins a preset time (controlled by srv_n_spin_wait_rounds), waiting
 for the lock, before suspending the thread.
 @param[in] lock pointer to rw-lock
 @param[in] pass pass value; != 0, if the lock will be passed to another thread
 to unlock
 @param[in] location location where requested*/
static inline void rw_lock_s_lock_func(rw_lock_t *lock, ulint pass,
                                       ut::Location location) {
  /* NOTE: As we do not know the thread ids for threads which have
  s-locked a latch, and s-lockers will be served only after waiting
  x-lock requests have been fulfilled, then if this thread already
  owns an s-lock here, it may end up in a deadlock with another thread
  which requests an x-lock here. Therefore, we will forbid recursive
  s-locking of a latch: the following assert will warn the programmer
  of the possibility of this kind of a deadlock. If we want to implement
  safe recursive s-locking, we should keep in a list the thread ids of
  the threads which have s-locked a latch. This would use some CPU
  time. */

  ut_ad(!rw_lock_own(lock, RW_LOCK_S)); /* see NOTE above */
  ut_ad(!rw_lock_own(lock, RW_LOCK_X));

  if (!rw_lock_s_lock_low(lock, pass, location)) {
    /* Did not succeed, try spin wait */

    rw_lock_s_lock_spin(lock, pass, location);
  }
}

/** NOTE! Use the corresponding macro, not directly this function! Lock an
 rw-lock in exclusive mode for the current thread if the lock can be
 obtained immediately.
 @param[in] lock pointer to rw-lock
 @param[in] location location where requested
 @return true if success */
static inline bool rw_lock_x_lock_func_nowait(rw_lock_t *lock,
                                              ut::Location location) {
  int32_t x_lock_decr = X_LOCK_DECR;

  if (lock->lock_word.compare_exchange_strong(x_lock_decr, 0)) {
    rw_lock_set_writer_id_and_recursion_flag(lock, true);
  } else if (lock->recursive.load(std::memory_order_acquire) &&
             lock->writer_thread.load(std::memory_order_relaxed) ==
                 std::this_thread::get_id()) {
    /* Relock: this lock_word modification is safe since no other
    threads can modify (lock, unlock, or reserve) lock_word while
    there is an exclusive writer and this is the writer thread. */
    if (lock->lock_word == 0 || lock->lock_word == -X_LOCK_HALF_DECR) {
      /* There are 1 x-locks */
      lock->lock_word -= X_LOCK_DECR;
    } else if (lock->lock_word <= -X_LOCK_DECR) {
      /* There are 2 or more x-locks */
      lock->lock_word--;
    } else {
      /* Failure */
      return false;
    }

    /* Watch for too many recursive locks */
    ut_ad(lock->lock_word < 0);
  } else {
    /* Failure */
    return false;
  }

  ut_d(rw_lock_add_debug_info(lock, 0, RW_LOCK_X, location));

  lock->last_x_file_name = location.filename;
  /* last_x_line is uint16_t */
  ut_ad(location.line <=
        std::numeric_limits<decltype(lock->last_x_line)>::max());
  lock->last_x_line = location.line;

  ut_ad(rw_lock_validate(lock));

  return true;
}

/** Releases a shared mode lock.
@param[in] pass pass value; != 0, if the lock may have been passed to another
thread to unlock
@param[in,out] lock  rw-lock */
static inline void rw_lock_s_unlock_func(IF_DEBUG(ulint pass, )
                                             rw_lock_t *lock) {
  ut_ad(lock->lock_word > -X_LOCK_DECR);
  ut_ad(lock->lock_word != 0);
  ut_ad(lock->lock_word < X_LOCK_DECR);
  lock->reader_thread.xor_thing(std::this_thread::get_id());

  ut_d(rw_lock_remove_debug_info(lock, pass, RW_LOCK_S));

  /* Increment lock_word to indicate 1 less reader */
  lint lock_word = rw_lock_lock_word_incr(lock, 1);
  if (lock_word == 0 || lock_word == -X_LOCK_HALF_DECR) {
    /* wait_ex waiter exists. It may not be asleep, but we signal
    anyway. We do not wake other waiters, because they can't
    exist without wait_ex waiter and wait_ex waiter goes first.*/
    os_event_set(lock->wait_ex_event);
    sync_array_object_signalled();
  }

  ut_ad(rw_lock_validate(lock));
}

/** Releases an exclusive mode lock.
@param[in] pass  pass value; != 0, if the lock may have been passed to
another thread to unlock
@param[in,out] lock lock rw-lock */
static inline void rw_lock_x_unlock_func(IF_DEBUG(ulint pass, )
                                             rw_lock_t *lock) {
  ut_ad(lock->lock_word == 0 || lock->lock_word == -X_LOCK_HALF_DECR ||
        lock->lock_word <= -X_LOCK_DECR);

  /* lock->recursive == true implies that the lock->writer_thread is the
  current writer. If we are the last of the recursive callers then we must unset
  lock->recursive flag to indicate that the lock->writer_thread is now
  stale. Otherwise if our thread tried to reacquire the lock it would wrongly
  believe it already has it.
  Note that since we still hold the x-lock we can safely read the lock_word. */
  if (lock->lock_word == 0) {
    /* Last caller in a possible recursive chain. */
    lock->recursive.store(false, std::memory_order_relaxed);
  }

  ut_d(rw_lock_remove_debug_info(lock, pass, RW_LOCK_X));

  if (lock->lock_word == 0 || lock->lock_word == -X_LOCK_HALF_DECR) {
    /* There is 1 x-lock */
    /* atomic increment is needed, because it is last */
    if (rw_lock_lock_word_incr(lock, X_LOCK_DECR) <= 0) {
      ut_error;
    }

    /* This no longer has an X-lock but it may still have
    an SX-lock. So it is now free for S-locks by other threads.
    We need to signal read/write waiters.
    We do not need to signal wait_ex waiters, since they cannot
    exist when there is a writer. */
    if (lock->waiters) {
      rw_lock_reset_waiter_flag(lock);
      os_event_set(lock->event);
      sync_array_object_signalled();
    }
  } else if (lock->lock_word == -X_LOCK_DECR ||
             lock->lock_word == -(X_LOCK_DECR + X_LOCK_HALF_DECR)) {
    /* There are 2 x-locks */
    lock->lock_word += X_LOCK_DECR;
  } else {
    /* There are more than 2 x-locks. */
    ut_ad(lock->lock_word < -X_LOCK_DECR);
    lock->lock_word += 1;
  }

  ut_ad(rw_lock_validate(lock));
}

/** Releases a sx mode lock.
 @param[in] pass pass value; != 0, if the lock may have been passed to another
thread to unlock
 @param[in,out] lock rw-lock */
static inline void rw_lock_sx_unlock_func(IF_DEBUG(ulint pass, )
                                              rw_lock_t *lock) {
  ut_ad(rw_lock_get_sx_lock_count(lock));
  ut_ad(lock->sx_recursive > 0);

  lock->decrement_sx_recursive();

  ut_d(rw_lock_remove_debug_info(lock, pass, RW_LOCK_SX));

  if (lock->sx_recursive == 0) {
    /* Last caller in a possible recursive chain. */
    if (lock->lock_word > 0) {
      lock->recursive.store(false, std::memory_order_relaxed);

      if (rw_lock_lock_word_incr(lock, X_LOCK_HALF_DECR) <= X_LOCK_HALF_DECR) {
        ut_error;
      }
      /* Lock is now free. May have to signal read/write
      waiters. We do not need to signal wait_ex waiters,
      since they cannot exist when there is an sx-lock
      holder. */
      if (lock->waiters) {
        rw_lock_reset_waiter_flag(lock);
        os_event_set(lock->event);
        sync_array_object_signalled();
      }
    } else {
      /* still has x-lock */
      ut_ad(lock->lock_word == -X_LOCK_HALF_DECR ||
            lock->lock_word <= -(X_LOCK_DECR + X_LOCK_HALF_DECR));
      lock->lock_word += X_LOCK_HALF_DECR;
    }
  }

  ut_ad(rw_lock_validate(lock));
}

#ifdef UNIV_PFS_RWLOCK

static inline void pfs_rw_lock_create_func(mysql_pfs_key_t key, rw_lock_t *lock,
                                           IF_DEBUG(latch_id_t id, )
                                               ut::Location clocation) {
  new (lock) rw_lock_t{};

  /* Initialize the rwlock for performance schema */
  lock->pfs_psi = PSI_RWLOCK_CALL(init_rwlock)(key.m_value, lock);

  /* The actual function to initialize an rwlock */
  rw_lock_create_func(lock, IF_DEBUG(id, ) clocation);
}
/** Performance schema instrumented wrap function for rw_lock_x_lock_func()
 NOTE! Please use the corresponding macro rw_lock_x_lock(), not directly
 this function!
 @param[in] lock pointer to rw-lock
 @param[in] pass pass value; != 0, if the lock will be passed to another thread
 to unlock
 @param[in] location location where requested */
static inline void pfs_rw_lock_x_lock_func(rw_lock_t *lock, ulint pass,
                                           ut::Location location) {
  if (lock->pfs_psi != nullptr) {
    if (lock->pfs_psi->m_enabled) {
      PSI_rwlock_locker *locker;
      PSI_rwlock_locker_state state;

      /* Record the acquisition of a read-write lock in exclusive
      mode in performance schema */

      locker = PSI_RWLOCK_CALL(start_rwlock_wrwait)(
          &state, lock->pfs_psi, PSI_RWLOCK_EXCLUSIVELOCK, location.filename,
          static_cast<uint>(location.line));

      rw_lock_x_lock_func(lock, pass, location);

      if (locker != nullptr) {
        PSI_RWLOCK_CALL(end_rwlock_wrwait)(locker, 0);
      }
      return;
    }
  }

  rw_lock_x_lock_func(lock, pass, location);
}
/** Performance schema instrumented wrap function for
 rw_lock_x_lock_func_nowait()
 NOTE! Please use the corresponding macro rw_lock_x_lock_func(),
 not directly this function!
 @return true if success */
[[nodiscard]] static inline bool pfs_rw_lock_x_lock_func_nowait(
    rw_lock_t *lock,       /*!< in: pointer to rw-lock */
    ut::Location location) /*!< in: line where requested */
{
  bool ret;

  if (lock->pfs_psi != nullptr) {
    if (lock->pfs_psi->m_enabled) {
      PSI_rwlock_locker *locker;
      PSI_rwlock_locker_state state;

      /* Record the acquisition of a read-write trylock in exclusive
      mode in performance schema */

      locker = PSI_RWLOCK_CALL(start_rwlock_wrwait)(
          &state, lock->pfs_psi, PSI_RWLOCK_TRYEXCLUSIVELOCK, location.filename,
          static_cast<uint>(location.line));

      ret = rw_lock_x_lock_func_nowait(lock, location);

      if (locker != nullptr) {
        const int rc = (ret ? 0 : 1);
        PSI_RWLOCK_CALL(end_rwlock_wrwait)(locker, rc);
      }

      return ret;
    }
  }

  ret = rw_lock_x_lock_func_nowait(lock, location);

  return ret;
}
/** Performance schema instrumented wrap function for rw_lock_free_func()
 NOTE! Please use the corresponding macro rw_lock_free(), not directly
 this function! */
static inline void pfs_rw_lock_free_func(
    rw_lock_t *lock) /*!< in: pointer to rw-lock */
{
  if (lock->pfs_psi != nullptr) {
    PSI_RWLOCK_CALL(destroy_rwlock)(lock->pfs_psi);
    lock->pfs_psi = nullptr;
  }

  rw_lock_free_func(lock);
}
/** Performance schema instrumented wrap function for rw_lock_s_lock_func()
 NOTE! Please use the corresponding macro rw_lock_s_lock(), not
 directly this function!
@param[in] lock pointer to rw-lock
@param[in] pass pass value; != 0, if the lock will be passed to another thread
to unlock
@param[in] location location where requested */
ALWAYS_INLINE
void pfs_rw_lock_s_lock_func(rw_lock_t *lock, ulint pass,
                             ut::Location location) {
  if (lock->pfs_psi != nullptr) {
    if (lock->pfs_psi->m_enabled) {
      PSI_rwlock_locker *locker;
      PSI_rwlock_locker_state state;

      /* Instrumented to inform we are acquiring a shared rwlock */
      locker = PSI_RWLOCK_CALL(start_rwlock_rdwait)(
          &state, lock->pfs_psi, PSI_RWLOCK_SHAREDLOCK, location.filename,
          static_cast<uint>(location.line));

      rw_lock_s_lock_func(lock, pass, location);

      if (locker != nullptr) {
        PSI_RWLOCK_CALL(end_rwlock_rdwait)(locker, 0);
      }

      return;
    }
  }

  rw_lock_s_lock_func(lock, pass, location);
}
/** Performance schema instrumented wrap function for rw_lock_sx_lock_func()
 NOTE! Please use the corresponding macro rw_lock_sx_lock(), not
 directly this function!
 @param[in] lock pointer to rw-lock
 @param[in] pass pass value; != 0, if the lock will be passed to another thread
 to unlock
 @param[in] location location where requested */
static inline void pfs_rw_lock_sx_lock_func(rw_lock_t *lock, ulint pass,
                                            ut::Location location) {
  if (lock->pfs_psi != nullptr) {
    if (lock->pfs_psi->m_enabled) {
      PSI_rwlock_locker *locker;
      PSI_rwlock_locker_state state;

      /* Instrumented to inform we are acquiring a shared rwlock */
      locker = PSI_RWLOCK_CALL(start_rwlock_wrwait)(
          &state, lock->pfs_psi, PSI_RWLOCK_SHAREDEXCLUSIVELOCK,
          location.filename, static_cast<uint>(location.line));

      rw_lock_sx_lock_func(lock, pass, location);

      if (locker != nullptr) {
        PSI_RWLOCK_CALL(end_rwlock_wrwait)(locker, 0);
      }

      return;
    }
  }

  rw_lock_sx_lock_func(lock, pass, location);
}
[[nodiscard]] static inline bool pfs_rw_lock_s_lock_low(rw_lock_t *lock,
                                                        ulint pass,
                                                        ut::Location location) {
  bool ret;

  if (lock->pfs_psi != nullptr) {
    if (lock->pfs_psi->m_enabled) {
      PSI_rwlock_locker *locker;
      PSI_rwlock_locker_state state;

      /* Instrumented to inform we are acquiring a shared rwlock */
      locker = PSI_RWLOCK_CALL(start_rwlock_rdwait)(
          &state, lock->pfs_psi, PSI_RWLOCK_TRYSHAREDLOCK, location.filename,
          static_cast<uint>(location.line));

      ret = rw_lock_s_lock_low(lock, pass, location);

      if (locker != nullptr) {
        const int rc = (ret ? 0 : 1);
        PSI_RWLOCK_CALL(end_rwlock_rdwait)(locker, rc);
      }

      return ret;
    }
  }

  ret = rw_lock_s_lock_low(lock, pass, location);

  return ret;
}
/** Performance schema instrumented wrap function for rw_lock_sx_lock_nowait()
 NOTE! Please use the corresponding macro, not
 directly this function!
 @param[in] lock pointer to rw-loxk
 @param[in] pass pass value; != 0 if the lock will be passed to another thread
 to unlock
 @param[in] location location where requested
 @return true if success */
[[nodiscard]] static inline bool pfs_rw_lock_sx_lock_low(
    rw_lock_t *lock, ulint pass, ut::Location location) {
  bool ret;

  if (lock->pfs_psi != nullptr) {
    if (lock->pfs_psi->m_enabled) {
      PSI_rwlock_locker *locker;
      PSI_rwlock_locker_state state;

      /* Instrumented to inform we are acquiring a shared
      exclusive rwlock */
      locker = PSI_RWLOCK_CALL(start_rwlock_rdwait)(
          &state, lock->pfs_psi, PSI_RWLOCK_TRYSHAREDEXCLUSIVELOCK,
          location.filename, static_cast<uint>(location.line));

      ret = rw_lock_sx_lock_low(lock, pass, location);

      if (locker != nullptr) {
        const int rc = (ret ? 0 : 1);
        PSI_RWLOCK_CALL(end_rwlock_rdwait)(locker, rc);
      }

      return (ret);
    }
  }

  ret = rw_lock_sx_lock_low(lock, pass, location);

  return ret;
}
/** Performance schema instrumented wrap function for rw_lock_x_unlock_func()
 NOTE! Please use the corresponding macro rw_lock_x_unlock(), not directly
 this function! */
static inline void pfs_rw_lock_x_unlock_func(IF_DEBUG(ulint pass, )
                                                 rw_lock_t *lock) {
  /* Inform performance schema we are unlocking the lock */
  if (lock->pfs_psi != nullptr) {
    if (lock->pfs_psi->m_enabled) {
      PSI_RWLOCK_CALL(unlock_rwlock)(lock->pfs_psi, PSI_RWLOCK_EXCLUSIVEUNLOCK);
    }
  }

  rw_lock_x_unlock_func(IF_DEBUG(pass, ) lock);
}

static inline void pfs_rw_lock_sx_unlock_func(IF_DEBUG(ulint pass, )
                                                  rw_lock_t *lock) {
  /* Inform performance schema we are unlocking the lock */
  if (lock->pfs_psi != nullptr) {
    if (lock->pfs_psi->m_enabled) {
      PSI_RWLOCK_CALL(unlock_rwlock)
      (lock->pfs_psi, PSI_RWLOCK_SHAREDEXCLUSIVEUNLOCK);
    }
  }

  rw_lock_sx_unlock_func(IF_DEBUG(pass, ) lock);
}

ALWAYS_INLINE
void pfs_rw_lock_s_unlock_func(IF_DEBUG(ulint pass, ) rw_lock_t *lock) {
  /* Inform performance schema we are unlocking the lock */
  if (lock->pfs_psi != nullptr) {
    if (lock->pfs_psi->m_enabled) {
      PSI_RWLOCK_CALL(unlock_rwlock)(lock->pfs_psi, PSI_RWLOCK_SHAREDUNLOCK);
    }
  }

  rw_lock_s_unlock_func(IF_DEBUG(pass, ) lock);
}
#endif /* UNIV_PFS_RWLOCK */
