# Copyright (c) 2022, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

---
# This is legacy code, so disable some warnings.
Checks:
  -bugprone-assignment-in-if-condition,
  -bugprone-implicit-widening-of-multiplication-result,
  -cppcoreguidelines-avoid-c-arrays,
  -cppcoreguidelines-avoid-goto,
  -cppcoreguidelines-avoid-magic-numbers,
  -cppcoreguidelines-avoid-non-const-global-variables,
  -cppcoreguidelines-interfaces-global-init,
  -cppcoreguidelines-macro-usage,
  -cppcoreguidelines-no-malloc,
  -cppcoreguidelines-non-private-member-variables-in-classes,
  -cppcoreguidelines-owning-memory,
  -cppcoreguidelines-pro-bounds-array-to-pointer-decay,
  -cppcoreguidelines-pro-bounds-pointer-arithmetic,
  -cppcoreguidelines-pro-type-union-access,
  -cppcoreguidelines-pro-type-vararg,
  -cppcoreguidelines-pro-bounds-array-to-pointer-decay,
  -cppcoreguidelines-special-member-functions,
  -misc-definitions-in-headers,
  -misc-non-private-member-variables-in-classes,
  -readability-function-cognitive-complexity,
  -readability-implicit-bool-conversion,
  -readability-magic-numbers,
  -readability-named-parameter,
  -readability-non-const-parameter,
  -readability-redundant-declaration

InheritParentConfig: true


CheckOptions:
  - key:        readability-magic-numbers.IgnoredIntegerValues
    value:      "1;2;3;4;9;32;1114111"
  # Allow things like CHARSET_INFO cs;
  - key:        readability-identifier-length.MinimumParameterNameLength
    value:      1
  - key:        readability-identifier-length.MinimumVariableNameLength
    value:      2
