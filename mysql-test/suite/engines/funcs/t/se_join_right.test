#Want to skip this test from daily Valgrind execution
--source include/no_valgrind_without_big.inc
--disable_warnings
DROP TABLE IF EXISTS t1,t2,t3,t4,t5,t6;
--enable_warnings
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 < 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 = 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 >= 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 != 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1;
-- sorted_result
SELECT t1.c1,t2.c1 FROM t1 RIGHT JOIN t2 ON t1.c1 = t2.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1;
-- sorted_result
SELECT t1.c1,t3.c1 FROM t1 RIGHT JOIN t3 ON t1.c1 = t3.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1;
-- sorted_result
SELECT t1.c1,t4.c1 FROM t1 RIGHT JOIN t4 ON t1.c1 = t4.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1;
-- sorted_result
SELECT t1.c1,t5.c1 FROM t1 RIGHT JOIN t5 ON t1.c1 = t5.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1;
-- sorted_result
SELECT t1.c1,t6.c1 FROM t1 RIGHT JOIN t6 ON t1.c1 = t6.c1 WHERE t1.c1 <=> 5;
DROP TABLE t1,t2,t3,t4,t5,t6;

