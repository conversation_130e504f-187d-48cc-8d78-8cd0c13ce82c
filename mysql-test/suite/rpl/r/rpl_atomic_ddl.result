include/rpl/init_source_replica.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection master]
[connection slave]
call mtr.add_suppression("Operation CREATE USER failed");
call mtr.add_suppression("Operation RENAME USER failed");
call mtr.add_suppression("The replica coordinator and worker threads are stopped");
call mtr.add_suppression("Replica worker thread has failed to apply an event");
call mtr.add_suppression("Error writing relay log configuration");
call mtr.add_suppression("Error 'Unknown error' on query");
[connection master]
CREATE DATABASE master_db;
USE master_db;
CREATE TABLE IF NOT EXISTS t_checks (id INT AUTO_INCREMENT NOT NULL PRIMARY KEY,  ddl_query TEXT,  pre_binlog_check TEXT, post_binlog_check TEXT);;
USE test;
CREATE TABLE t_1 (a int);
include/rpl/sync_to_replica.inc
include/rpl/stop_applier.inc
[connection master]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
CREATE USER user1 IDENTIFIED WITH 'caching_sha2_password';
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
GRANT SELECT ON *.* TO user1;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
REVOKE SELECT ON *.* FROM user1;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
ALTER USER user1 IDENTIFIED BY '123456';
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
SET PASSWORD FOR user1='abcdef';
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
RENAME USER user1 TO user2;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
CREATE ROLE roleA,roleB;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
GRANT roleA TO roleB;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
REVOKE roleA FROM roleB;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
GRANT roleA TO user2;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
ALTER USER user2 DEFAULT ROLE roleA;;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
DROP ROLE roleA,roleB;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
DROP USER user2;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
PREPARE stmt FROM 'CREATE USER user3';
DEALLOCATE PREPARE stmt;
PREPARE stmt FROM 'DROP USER user3';
DEALLOCATE PREPARE stmt;
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
CREATE TRIGGER tr_0 BEFORE INSERT ON t_1 FOR EACH ROW SET @a:=1;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
DROP TRIGGER tr_0;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
CREATE FUNCTION f_0() RETURNS INT RETURN 5;;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
ALTER FUNCTION f_0 COMMENT 'comment';
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
DROP FUNCTION f_0;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
CREATE PROCEDURE p_0() BEGIN END;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
ALTER PROCEDURE p_0 COMMENT 'comment';
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
DROP PROCEDURE p_0;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
CREATE EVENT e_0 ON SCHEDULE EVERY 2 DAY DO SELECT 2;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
ALTER EVENT e_0 ON SCHEDULE EVERY 2 day DO SELECT 1;;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
DROP EVENT e_0;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
CREATE VIEW v_0 AS SELECT * FROM t_1;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
ALTER VIEW v_0 AS SELECT * FROM t_1;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
DROP VIEW v_0;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
CREATE FUNCTION metaphon RETURNS STRING SONAME 'udf_example_LIB';
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
DROP FUNCTION metaphon;;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
CREATE TABLE test.t1(a int);
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
ALTER TABLE test.t1 ADD COLUMN t1_new_col varchar(100);;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
ALTER TABLE test.t1 ADD index new_t1_idx(t1_new_col);;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
ALTER TABLE test.t1 RENAME TO test.t2;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
ALTER TABLE test.t2 DROP COLUMN a;;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
TRUNCATE TABLE test.t2;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
ALTER TABLE test.t2 ADD fulltext index full_t2_idx(t1_new_col);;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
ALTER TABLE test.t2 DROP index full_t2_idx;;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
DROP TABLE test.t2;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
CREATE TABLESPACE ts ADD DATAFILE 'ts_0.ibd';
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
DROP TABLESPACE ts;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
CREATE TABLE test.t1(a int);
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
CREATE TABLE test.t2 AS SELECT * FROM t1;;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
DROP TABLE test.t1;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
include/rpl/gtid_step_reset.inc
FLUSH LOGS;
DROP TABLE test.t2;
include/rpl/gtid_step_assert.inc [count=1, only_count=0]
[connection slave]
include/rpl/start_receiver.inc
Warnings:
Note	3083	Replication thread(s) for channel '' are already runnning.
[connection master]
include/rpl/sync_to_replica_received.inc
[connection slave]
CREATE USER user1;
START REPLICA SQL_THREAD;
include/rpl/wait_for_applier_error.inc [errno=1396]
DROP USER user1;
SET @@GLOBAL.debug="+d,rli_pre_commit_error";
START REPLICA SQL_THREAD;
include/rpl/wait_for_applier_error.inc [errno=1105]
SET @@GLOBAL.debug="-d,rli_pre_commit_error";
[connection master]
Loop: 1 out of 44; current query: 'CREATE USER user1 IDENTIFIED WITH 'caching_sha2_password''; pre-binlog check: 'SELECT count(*) = 0 FROM mysql.user WHERE user = 'user1''; pre-commit check: 'SELECT count(*) = 1 FROM mysql.user WHERE user = 'user1'';
[connection slave]
*** Query: 'CREATE USER user1 IDENTIFIED WITH 'caching_sha2_password''. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=0]
*** Query: 'CREATE USER user1 IDENTIFIED WITH 'caching_sha2_password''. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 2 out of 44; current query: 'GRANT SELECT ON *.* TO user1'; pre-binlog check: 'SELECT select_priv = <non-deterministic hash> FROM mysql.user WHERE user = 'user1''; pre-commit check: 'SELECT select_priv = 'Y' FROM mysql.user WHERE user = 'user1'';
[connection slave]
*** Query: 'GRANT SELECT ON *.* TO user1'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'GRANT SELECT ON *.* TO user1'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 3 out of 44; current query: 'REVOKE SELECT ON *.* FROM user1'; pre-binlog check: 'SELECT select_priv = <non-deterministic hash> FROM mysql.user WHERE user = 'user1''; pre-commit check: 'SELECT select_priv = 'N' FROM mysql.user WHERE user = 'user1'';
[connection slave]
*** Query: 'REVOKE SELECT ON *.* FROM user1'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'REVOKE SELECT ON *.* FROM user1'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 4 out of 44; current query: 'ALTER USER user1 IDENTIFIED BY '123456''; pre-binlog check: 'SELECT HEX(authentication_string) = <non-deterministic hash> FROM mysql.user WHERE user LIKE 'user1''; pre-commit check: 'SELECT HEX(authentication_string) != <non-deterministic hash> FROM mysql.user WHERE user LIKE 'user1'';
[connection slave]
*** Query: 'ALTER USER user1 IDENTIFIED BY '123456''. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'ALTER USER user1 IDENTIFIED BY '123456''. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 5 out of 44; current query: 'SET PASSWORD FOR user1='abcdef''; pre-binlog check: 'SELECT HEX(authentication_string) = <non-deterministic hash> FROM mysql.user WHERE user LIKE 'user1''; pre-commit check: 'SELECT HEX(authentication_string) != <non-deterministic hash> FROM mysql.user WHERE user LIKE 'user1'';
[connection slave]
*** Query: 'SET PASSWORD FOR user1='abcdef''. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'SET PASSWORD FOR user1='abcdef''. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 6 out of 44; current query: 'RENAME USER user1 TO user2'; pre-binlog check: 'SELECT count(*) = 1 FROM mysql.user WHERE user = 'user1''; pre-commit check: 'SELECT count(*) = 0 FROM mysql.user WHERE user = 'user1'';
[connection slave]
*** Query: 'RENAME USER user1 TO user2'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'RENAME USER user1 TO user2'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 7 out of 44; current query: 'CREATE ROLE roleA,roleB'; pre-binlog check: 'SELECT COUNT(*) = 0 FROM mysql.user WHERE user LIKE 'roleB' OR user LIKE 'roleA';'; pre-commit check: 'SELECT COUNT(*) = 2 FROM mysql.user WHERE user LIKE 'roleB' OR user LIKE 'roleA';';
[connection slave]
*** Query: 'CREATE ROLE roleA,roleB'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'CREATE ROLE roleA,roleB'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 8 out of 44; current query: 'GRANT roleA TO roleB'; pre-binlog check: 'SELECT count(*) = 0 FROM mysql.role_edges WHERE from_user = 'roleA''; pre-commit check: 'SELECT count(*) = 1 FROM mysql.role_edges WHERE from_user = 'roleA'';
[connection slave]
*** Query: 'GRANT roleA TO roleB'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'GRANT roleA TO roleB'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 9 out of 44; current query: 'REVOKE roleA FROM roleB'; pre-binlog check: 'SELECT count(*) = 1 FROM mysql.role_edges WHERE from_user = 'roleA''; pre-commit check: 'SELECT count(*) = 0 FROM mysql.role_edges WHERE from_user = 'roleA'';
[connection slave]
*** Query: 'REVOKE roleA FROM roleB'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'REVOKE roleA FROM roleB'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 10 out of 44; current query: 'GRANT roleA TO user2'; pre-binlog check: 'SELECT count(*) = 0 FROM mysql.role_edges WHERE from_user = 'roleA''; pre-commit check: 'SELECT count(*) = 1 FROM mysql.role_edges WHERE from_user = 'roleA'';
[connection slave]
*** Query: 'GRANT roleA TO user2'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'GRANT roleA TO user2'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 11 out of 44; current query: 'ALTER USER user2 DEFAULT ROLE roleA;'; pre-binlog check: 'SELECT count(*) = 0 FROM mysql.default_roles WHERE user = 'user2''; pre-commit check: 'SELECT count(*) = 1 FROM mysql.default_roles WHERE user = 'user2'';
[connection slave]
*** Query: 'ALTER USER user2 DEFAULT ROLE roleA;'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'ALTER USER user2 DEFAULT ROLE roleA;'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 12 out of 44; current query: 'DROP ROLE roleA,roleB'; pre-binlog check: 'SELECT count(*) = 2 FROM mysql.user WHERE user LIKE 'roleA' OR user LIKE 'roleB''; pre-commit check: 'SELECT count(*) = 0 FROM mysql.user WHERE user LIKE 'roleA' OR user LIKE 'roleB'';
[connection slave]
*** Query: 'DROP ROLE roleA,roleB'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'DROP ROLE roleA,roleB'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 13 out of 44; current query: 'DROP USER user2'; pre-binlog check: 'SELECT count(*) = 1 FROM mysql.user WHERE user = 'user2''; pre-commit check: 'SELECT count(*) = 0 FROM mysql.user WHERE user = 'user2'';
[connection slave]
*** Query: 'DROP USER user2'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'DROP USER user2'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 14 out of 44; current query: 'CREATE TRIGGER tr_0 BEFORE INSERT ON t_1 FOR EACH ROW SET @a:=1'; pre-binlog check: 'SELECT count(*) = 0 FROM information_schema.TRIGGERS where TRIGGER_NAME = 'tr_0''; pre-commit check: 'SELECT count(*) = 1 FROM information_schema.TRIGGERS where TRIGGER_NAME = 'tr_0'';
[connection slave]
*** Query: 'CREATE TRIGGER tr_0 BEFORE INSERT ON t_1 FOR EACH ROW SET @a:=1'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'CREATE TRIGGER tr_0 BEFORE INSERT ON t_1 FOR EACH ROW SET @a:=1'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 15 out of 44; current query: 'DROP TRIGGER tr_0'; pre-binlog check: 'SELECT count(*) = 1 FROM information_schema.TRIGGERS where TRIGGER_NAME = 'tr_0''; pre-commit check: 'SELECT count(*) = 0 FROM information_schema.TRIGGERS where TRIGGER_NAME = 'tr_0'';
[connection slave]
*** Query: 'DROP TRIGGER tr_0'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'DROP TRIGGER tr_0'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 16 out of 44; current query: 'CREATE FUNCTION f_0() RETURNS INT RETURN 5;'; pre-binlog check: 'SELECT count(*) = 0 FROM information_schema.ROUTINES where ROUTINE_NAME = 'f_0''; pre-commit check: 'SELECT count(*) = 1 FROM information_schema.ROUTINES where ROUTINE_NAME = 'f_0'';
[connection slave]
*** Query: 'CREATE FUNCTION f_0() RETURNS INT RETURN 5;'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'CREATE FUNCTION f_0() RETURNS INT RETURN 5;'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 17 out of 44; current query: 'ALTER FUNCTION f_0 COMMENT 'comment''; pre-binlog check: 'SELECT count(*) = 1 FROM information_schema.ROUTINES where ROUTINE_NAME = 'f_0''; pre-commit check: 'SELECT count(*) = 1 FROM information_schema.ROUTINES where ROUTINE_NAME = 'f_0'';
[connection slave]
*** Query: 'ALTER FUNCTION f_0 COMMENT 'comment''. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'ALTER FUNCTION f_0 COMMENT 'comment''. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 18 out of 44; current query: 'DROP FUNCTION f_0'; pre-binlog check: 'SELECT count(*) = 1 FROM information_schema.ROUTINES where ROUTINE_NAME = 'f_0''; pre-commit check: 'SELECT count(*) = 0 FROM information_schema.ROUTINES where ROUTINE_NAME = 'f_0'';
[connection slave]
*** Query: 'DROP FUNCTION f_0'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'DROP FUNCTION f_0'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 19 out of 44; current query: 'CREATE PROCEDURE p_0() BEGIN END'; pre-binlog check: 'SELECT count(*) = 0 FROM information_schema.ROUTINES where ROUTINE_NAME = 'p_0''; pre-commit check: 'SELECT count(*) = 1 FROM information_schema.ROUTINES where ROUTINE_NAME = 'p_0'';
[connection slave]
*** Query: 'CREATE PROCEDURE p_0() BEGIN END'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'CREATE PROCEDURE p_0() BEGIN END'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 20 out of 44; current query: 'ALTER PROCEDURE p_0 COMMENT 'comment''; pre-binlog check: 'SELECT count(*) = 1 FROM information_schema.ROUTINES where ROUTINE_NAME = 'p_0''; pre-commit check: 'SELECT count(*) = 1 FROM information_schema.ROUTINES where ROUTINE_NAME = 'p_0'';
[connection slave]
*** Query: 'ALTER PROCEDURE p_0 COMMENT 'comment''. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'ALTER PROCEDURE p_0 COMMENT 'comment''. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 21 out of 44; current query: 'DROP PROCEDURE p_0'; pre-binlog check: 'SELECT count(*) = 1 FROM information_schema.ROUTINES where ROUTINE_NAME = 'p_0''; pre-commit check: 'SELECT count(*) = 0 FROM information_schema.ROUTINES where ROUTINE_NAME = 'p_0'';
[connection slave]
*** Query: 'DROP PROCEDURE p_0'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'DROP PROCEDURE p_0'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 22 out of 44; current query: 'CREATE EVENT e_0 ON SCHEDULE EVERY 2 DAY DO SELECT 2'; pre-binlog check: 'SELECT count(*) = 0 FROM information_schema.EVENTS where EVENT_NAME = 'e_0''; pre-commit check: 'SELECT count(*) = 1 FROM information_schema.EVENTS where EVENT_NAME = 'e_0'';
[connection slave]
*** Query: 'CREATE EVENT e_0 ON SCHEDULE EVERY 2 DAY DO SELECT 2'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'CREATE EVENT e_0 ON SCHEDULE EVERY 2 DAY DO SELECT 2'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 23 out of 44; current query: 'ALTER EVENT e_0 ON SCHEDULE EVERY 2 day DO SELECT 1;'; pre-binlog check: 'SELECT count(*) = 1 FROM information_schema.EVENTS where EVENT_NAME = 'e_0''; pre-commit check: 'SELECT count(*) = 1 FROM information_schema.EVENTS where EVENT_NAME = 'e_0'';
[connection slave]
*** Query: 'ALTER EVENT e_0 ON SCHEDULE EVERY 2 day DO SELECT 1;'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'ALTER EVENT e_0 ON SCHEDULE EVERY 2 day DO SELECT 1;'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 24 out of 44; current query: 'DROP EVENT e_0'; pre-binlog check: 'SELECT count(*) = 1 FROM information_schema.EVENTS where EVENT_NAME = 'e_0''; pre-commit check: 'SELECT count(*) = 0 FROM information_schema.EVENTS where EVENT_NAME = 'e_0'';
[connection slave]
*** Query: 'DROP EVENT e_0'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'DROP EVENT e_0'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 25 out of 44; current query: 'CREATE VIEW v_0 AS SELECT * FROM t_1'; pre-binlog check: 'SELECT count(*) = 0 FROM information_schema.VIEWS where TABLE_NAME = 'v_0''; pre-commit check: 'SELECT count(*) = 1 FROM information_schema.VIEWS where TABLE_NAME = 'v_0'';
[connection slave]
*** Query: 'CREATE VIEW v_0 AS SELECT * FROM t_1'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'CREATE VIEW v_0 AS SELECT * FROM t_1'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 26 out of 44; current query: 'ALTER VIEW v_0 AS SELECT * FROM t_1'; pre-binlog check: 'SELECT count(*) = 1 FROM information_schema.VIEWS where TABLE_NAME = 'v_0''; pre-commit check: 'SELECT count(*) = 1 FROM information_schema.VIEWS where TABLE_NAME = 'v_0'';
[connection slave]
*** Query: 'ALTER VIEW v_0 AS SELECT * FROM t_1'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'ALTER VIEW v_0 AS SELECT * FROM t_1'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 27 out of 44; current query: 'DROP VIEW v_0'; pre-binlog check: 'SELECT count(*) = 1 FROM information_schema.VIEWS where TABLE_NAME = 'v_0''; pre-commit check: 'SELECT count(*) = 0 FROM information_schema.VIEWS where TABLE_NAME = 'v_0'';
[connection slave]
*** Query: 'DROP VIEW v_0'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'DROP VIEW v_0'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 28 out of 44; current query: 'CREATE FUNCTION metaphon RETURNS STRING SONAME 'udf_example_LIB''; pre-binlog check: 'SELECT count(*) = 0 FROM mysql.func where NAME = 'metaphon''; pre-commit check: 'SELECT count(*) = 1 FROM mysql.func where NAME = 'metaphon'';
[connection slave]
*** Query: 'CREATE FUNCTION metaphon RETURNS STRING SONAME 'udf_example_LIB''. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'CREATE FUNCTION metaphon RETURNS STRING SONAME 'udf_example_LIB''. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 29 out of 44; current query: 'DROP FUNCTION metaphon;'; pre-binlog check: 'SELECT count(*) = 1 FROM mysql.func where NAME = 'metaphon''; pre-commit check: 'SELECT count(*) = 0 FROM mysql.func where NAME = 'metaphon'';
[connection slave]
*** Query: 'DROP FUNCTION metaphon;'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'DROP FUNCTION metaphon;'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 30 out of 44; current query: 'CREATE TABLE test.t1(a int)'; pre-binlog check: 'SELECT count(*)=0 FROM information_schema.tables where table_name='t1''; pre-commit check: 'SELECT count(*)=1 FROM information_schema.tables where table_name='t1'';
[connection slave]
*** Query: 'CREATE TABLE test.t1(a int)'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'CREATE TABLE test.t1(a int)'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 31 out of 44; current query: 'ALTER TABLE test.t1 ADD COLUMN t1_new_col varchar(100);'; pre-binlog check: 'SELECT count(*)=1 FROM information_schema.columns where table_name='t1''; pre-commit check: 'SELECT count(*)=2 FROM information_schema.columns where table_name='t1'';
[connection slave]
*** Query: 'ALTER TABLE test.t1 ADD COLUMN t1_new_col varchar(100);'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'ALTER TABLE test.t1 ADD COLUMN t1_new_col varchar(100);'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 32 out of 44; current query: 'ALTER TABLE test.t1 ADD index new_t1_idx(t1_new_col);'; pre-binlog check: 'SELECT count(*)=0 FROM information_schema.innodb_indexes where name='new_t1_idx''; pre-commit check: 'SELECT count(*)=1 FROM information_schema.innodb_indexes where name='new_t1_idx'';
[connection slave]
*** Query: 'ALTER TABLE test.t1 ADD index new_t1_idx(t1_new_col);'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'ALTER TABLE test.t1 ADD index new_t1_idx(t1_new_col);'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 33 out of 44; current query: 'ALTER TABLE test.t1 RENAME TO test.t2'; pre-binlog check: 'SELECT count(*)=0 FROM information_schema.tables where table_name='t2''; pre-commit check: 'SELECT count(*)=1 FROM information_schema.tables where table_name='t2'';
[connection slave]
*** Query: 'ALTER TABLE test.t1 RENAME TO test.t2'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'ALTER TABLE test.t1 RENAME TO test.t2'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 34 out of 44; current query: 'ALTER TABLE test.t2 DROP COLUMN a;'; pre-binlog check: 'SELECT count(*)=2 FROM information_schema.columns where table_name='t2''; pre-commit check: 'SELECT count(*)=1 FROM information_schema.columns where table_name='t2'';
[connection slave]
*** Query: 'ALTER TABLE test.t2 DROP COLUMN a;'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'ALTER TABLE test.t2 DROP COLUMN a;'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 35 out of 44; current query: 'TRUNCATE TABLE test.t2'; pre-binlog check: 'SELECT count(*)=1 FROM information_schema.tables where table_name='t2''; pre-commit check: 'SELECT count(*)=1 FROM information_schema.tables where table_name='t2'';
[connection slave]
*** Query: 'TRUNCATE TABLE test.t2'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'TRUNCATE TABLE test.t2'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 36 out of 44; current query: 'ALTER TABLE test.t2 ADD fulltext index full_t2_idx(t1_new_col);'; pre-binlog check: 'SELECT count(*)=0 FROM information_schema.innodb_indexes where name='full_t2_idx''; pre-commit check: 'SELECT count(*)=1 FROM information_schema.innodb_indexes where name='full_t2_idx'';
[connection slave]
*** Query: 'ALTER TABLE test.t2 ADD fulltext index full_t2_idx(t1_new_col);'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'ALTER TABLE test.t2 ADD fulltext index full_t2_idx(t1_new_col);'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 37 out of 44; current query: 'ALTER TABLE test.t2 DROP index full_t2_idx;'; pre-binlog check: 'SELECT count(*)=1 FROM information_schema.innodb_indexes where name='full_t2_idx''; pre-commit check: 'SELECT count(*)=0 FROM information_schema.innodb_indexes where name='full_t2_idx'';
[connection slave]
*** Query: 'ALTER TABLE test.t2 DROP index full_t2_idx;'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'ALTER TABLE test.t2 DROP index full_t2_idx;'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 38 out of 44; current query: 'DROP TABLE test.t2'; pre-binlog check: 'SELECT count(*)=1 FROM information_schema.tables where table_name='t2''; pre-commit check: 'SELECT count(*)=0 FROM information_schema.tables where table_name='t2'';
[connection slave]
*** Query: 'DROP TABLE test.t2'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'DROP TABLE test.t2'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 39 out of 44; current query: 'CREATE TABLESPACE ts ADD DATAFILE 'ts_0.ibd''; pre-binlog check: 'SELECT count(*)=0 FROM information_schema.INNODB_TABLESPACES WHERE name = 'ts''; pre-commit check: 'SELECT count(*)=1 FROM information_schema.INNODB_TABLESPACES WHERE name = 'ts'';
[connection slave]
*** Query: 'CREATE TABLESPACE ts ADD DATAFILE 'ts_0.ibd''. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'CREATE TABLESPACE ts ADD DATAFILE 'ts_0.ibd''. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 40 out of 44; current query: 'DROP TABLESPACE ts'; pre-binlog check: 'SELECT count(*)=1 FROM information_schema.INNODB_TABLESPACES WHERE name = 'ts''; pre-commit check: 'SELECT count(*)=0 FROM information_schema.INNODB_TABLESPACES WHERE name = 'ts'';
[connection slave]
*** Query: 'DROP TABLESPACE ts'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'DROP TABLESPACE ts'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 41 out of 44; current query: 'CREATE TABLE test.t1(a int)'; pre-binlog check: 'SELECT count(*)=0 FROM information_schema.tables where table_name='t1''; pre-commit check: 'SELECT count(*)=1 FROM information_schema.tables where table_name='t1'';
[connection slave]
*** Query: 'CREATE TABLE test.t1(a int)'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'CREATE TABLE test.t1(a int)'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 42 out of 44; current query: 'CREATE TABLE test.t2 AS SELECT * FROM t1;'; pre-binlog check: 'SELECT count(*)=0 FROM information_schema.tables where table_name='t2''; pre-commit check: 'SELECT count(*)=1 FROM information_schema.tables where table_name='t2'';
[connection slave]
*** Query: 'CREATE TABLE test.t2 AS SELECT * FROM t1;'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'CREATE TABLE test.t2 AS SELECT * FROM t1;'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 43 out of 44; current query: 'DROP TABLE test.t1'; pre-binlog check: 'SELECT count(*)=1 FROM information_schema.tables where table_name='t1''; pre-commit check: 'SELECT count(*)=0 FROM information_schema.tables where table_name='t1'';
[connection slave]
*** Query: 'DROP TABLE test.t1'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'DROP TABLE test.t1'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection master]
Loop: 44 out of 44; current query: 'DROP TABLE test.t2'; pre-binlog check: 'SELECT count(*)=1 FROM information_schema.tables where table_name='t2''; pre-commit check: 'SELECT count(*)=0 FROM information_schema.tables where table_name='t2'';
[connection slave]
*** Query: 'DROP TABLE test.t2'. Crash right after the query has been prepared in the engine before being logged. ***
SET @@GLOBAL.debug="+d,crash_commit_before_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
include/rpl/gtid_step_assert.inc [count=0, only_count=1]
*** Query: 'DROP TABLE test.t2'. Crash right after the query has been binary-logged before committed in the engine. ***
SET @@GLOBAL.debug="+d,crash_commit_after_log";
include/rpl/gtid_step_reset.inc
START REPLICA SQL_THREAD;
include/rpl/start_server.inc [server_number=2]
[connection slave]
include/rpl/gtid_step_assert.inc [count=1, only_count=1]
[connection slave]
include/rpl/start_replica.inc
[connection master]
DROP table t_1;
DROP DATABASE master_db;
include/rpl/sync_to_replica.inc
include/rpl/deinit.inc
