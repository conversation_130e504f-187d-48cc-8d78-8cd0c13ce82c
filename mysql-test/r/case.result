drop table if exists t1, t2;
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
select CASE "b" when "a" then 1 when "b" then 2 END;
CASE "b" when "a" then 1 when "b" then 2 END
2
select CASE "c" when "a" then 1 when "b" then 2 END;
CASE "c" when "a" then 1 when "b" then 2 END
NULL
select CASE "c" when "a" then 1 when "b" then 2 ELSE 3 END;
CASE "c" when "a" then 1 when "b" then 2 ELSE 3 END
3
select CASE BINARY "b" when "a" then 1 when "B" then 2 WHEN "b" then "ok" END;
CASE BINARY "b" when "a" then 1 when "B" then 2 WHEN "b" then "ok" END
ok
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select CASE "b" when "a" then 1 when binary "B" then 2 WHEN "b" then "ok" END;
CASE "b" when "a" then 1 when binary "B" then 2 WHEN "b" then "ok" END
ok
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select CASE concat("a","b") when concat("ab","") then "a" when "b" then "b" end;
CASE concat("a","b") when concat("ab","") then "a" when "b" then "b" end
a
select CASE when 1=0 then "true" else "false" END;
CASE when 1=0 then "true" else "false" END
false
select CASE 1 when 1 then "one" WHEN 2 then "two" ELSE "more" END;
CASE 1 when 1 then "one" WHEN 2 then "two" ELSE "more" END
one
explain select CASE 1 when 1 then "one" WHEN 2 then "two" ELSE "more" END;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
Warnings:
Note	1003	/* select#1 */ select (case 1 when 1 then 'one' when 2 then 'two' else 'more' end) AS `CASE 1 when 1 then "one" WHEN 2 then "two" ELSE "more" END`
select CASE 2.0 when 1 then "one" WHEN 2.0 then "two" ELSE "more" END;
CASE 2.0 when 1 then "one" WHEN 2.0 then "two" ELSE "more" END
two
select (CASE "two" when "one" then "1" WHEN "two" then "2" END) | 0;
(CASE "two" when "one" then "1" WHEN "two" then "2" END) | 0
2
select (CASE "two" when "one" then 1.00 WHEN "two" then 2.00 END) +0.0;
(CASE "two" when "one" then 1.00 WHEN "two" then 2.00 END) +0.0
2.00
select case 1/0 when "a" then "true" else "false" END;
case 1/0 when "a" then "true" else "false" END
false
select case 1/0 when "a" then "true" END;
case 1/0 when "a" then "true" END
NULL
select (case 1/0 when "a" then "true" END) | 0;
(case 1/0 when "a" then "true" END) | 0
NULL
select (case 1/0 when "a" then "true" END) + 0.0;
(case 1/0 when "a" then "true" END) + 0.0
NULL
select case when 1>0 then "TRUE" else "FALSE" END;
case when 1>0 then "TRUE" else "FALSE" END
TRUE
select case when 1<0 then "TRUE" else "FALSE" END;
case when 1<0 then "TRUE" else "FALSE" END
FALSE
create table t1 (a int);
insert into t1 values(1),(2),(3),(4);
select case a when 1 then 2 when 2 then 3 else 0 end as fcase, count(*) from t1 group by fcase;
fcase	count(*)
0	2
2	1
3	1
analyze table t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select case a when 1 then 2 when 2 then 3 else 0 end as fcase, count(*) from t1 group by fcase;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	Using temporary
Warnings:
Note	1003	/* select#1 */ select (case `test`.`t1`.`a` when 1 then 2 when 2 then 3 else 0 end) AS `fcase`,count(0) AS `count(*)` from `test`.`t1` group by `fcase`
select case a when 1 then "one" when 2 then "two" else "nothing" end as fcase, count(*) from t1 group by fcase;
fcase	count(*)
nothing	2
one	1
two	1
drop table t1;
create table t1 (`row` int not null, col int not null, val varchar(255) not null);
insert into t1 values (1,1,'orange'),(1,2,'large'),(2,1,'yellow'),(2,2,'medium'),(3,1,'green'),(3,2,'small');
select max(case col when 1 then val else null end) as color from t1 group by `row`;
color
orange
yellow
green
drop table t1;
SET NAMES latin1;
CREATE TABLE t1 SELECT 
CASE WHEN 1 THEN _latin1'a' COLLATE latin1_danish_ci ELSE _latin1'a' END AS c1,
CASE WHEN 1 THEN _latin1'a' ELSE _latin1'a' COLLATE latin1_danish_ci END AS c2,
CASE WHEN 1 THEN 'a' ELSE  1  END AS c3,
CASE WHEN 1 THEN  1  ELSE 'a' END AS c4,
CASE WHEN 1 THEN 'a' ELSE 1.0 END AS c5,
CASE WHEN 1 THEN 1.0 ELSE 'a' END AS c6,
CASE WHEN 1 THEN  1  ELSE 1.0 END AS c7,
CASE WHEN 1 THEN 1.0 ELSE  1  END AS c8,
CASE WHEN 1 THEN 1.0 END AS c9,
CASE WHEN 1 THEN 0.1e1 else 0.1 END AS c10,
CASE WHEN 1 THEN 0.1e1 else 1 END AS c11,
CASE WHEN 1 THEN 0.1e1 else '1' END AS c12
;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(1) CHARACTER SET latin1 COLLATE latin1_danish_ci NOT NULL DEFAULT '',
  `c2` varchar(1) CHARACTER SET latin1 COLLATE latin1_danish_ci NOT NULL DEFAULT '',
  `c3` varchar(2) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `c4` varchar(2) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `c5` varchar(4) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `c6` varchar(4) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `c7` decimal(2,1) NOT NULL DEFAULT '0.0',
  `c8` decimal(2,1) NOT NULL DEFAULT '0.0',
  `c9` decimal(2,1) DEFAULT NULL,
  `c10` double NOT NULL DEFAULT '0',
  `c11` double NOT NULL DEFAULT '0',
  `c12` varchar(22) CHARACTER SET latin1 NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SELECT CASE 
WHEN 1 
THEN _latin1'a' COLLATE latin1_danish_ci 
ELSE _latin1'a' COLLATE latin1_swedish_ci
END;
ERROR HY000: Illegal mix of collations (latin1_danish_ci,EXPLICIT) and (latin1_swedish_ci,EXPLICIT) for operation 'case'
SELECT CASE _latin1'a' COLLATE latin1_general_ci
WHEN _latin1'a' COLLATE latin1_danish_ci  THEN 1
WHEN _latin1'a' COLLATE latin1_swedish_ci THEN 2
END;
ERROR HY000: Illegal mix of collations (latin1_general_ci,EXPLICIT), (latin1_danish_ci,EXPLICIT), (latin1_swedish_ci,EXPLICIT) for operation 'case'
SELECT 
CASE _latin1'a' COLLATE latin1_general_ci  WHEN _latin1'A' THEN '1' ELSE 2 END,
CASE _latin1'a' COLLATE latin1_bin         WHEN _latin1'A' THEN '1' ELSE 2 END,
CASE _latin1'a' WHEN _latin1'A' COLLATE latin1_swedish_ci THEN '1' ELSE 2 END,
CASE _latin1'a' WHEN _latin1'A' COLLATE latin1_bin        THEN '1' ELSE 2 END
;
CASE _latin1'a' COLLATE latin1_general_ci  WHEN _latin1'A' THEN '1' ELSE 2 END	CASE _latin1'a' COLLATE latin1_bin         WHEN _latin1'A' THEN '1' ELSE 2 END	CASE _latin1'a' WHEN _latin1'A' COLLATE latin1_swedish_ci THEN '1' ELSE 2 END	CASE _latin1'a' WHEN _latin1'A' COLLATE latin1_bin        THEN '1' ELSE 2 END
1	2	1	2
CREATE TABLE t1 SELECT COALESCE(_latin1'a',_latin2'a');
ERROR HY000: Illegal mix of collations (latin1_swedish_ci,COERCIBLE) and (latin2_general_ci,COERCIBLE) for operation 'coalesce'
CREATE TABLE t1 SELECT COALESCE('a' COLLATE latin1_swedish_ci,'b' COLLATE latin1_bin);
ERROR HY000: Illegal mix of collations (latin1_swedish_ci,EXPLICIT) and (latin1_bin,EXPLICIT) for operation 'coalesce'
CREATE TABLE t1 SELECT 
COALESCE(1), COALESCE(1.0),COALESCE('a'),
COALESCE(1,1.0), COALESCE(1,'1'),COALESCE(1.1,'1'),
COALESCE('a' COLLATE latin1_bin,'b');
explain SELECT 
COALESCE(1), COALESCE(1.0),COALESCE('a'),
COALESCE(1,1.0), COALESCE(1,'1'),COALESCE(1.1,'1'),
COALESCE('a' COLLATE latin1_bin,'b');
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
Warnings:
Note	1003	/* select#1 */ select coalesce(1) AS `COALESCE(1)`,coalesce(1.0) AS `COALESCE(1.0)`,coalesce('a') AS `COALESCE('a')`,coalesce(1,1.0) AS `COALESCE(1,1.0)`,coalesce(1,'1') AS `COALESCE(1,'1')`,coalesce(1.1,'1') AS `COALESCE(1.1,'1')`,coalesce(('a' collate latin1_bin),'b') AS `COALESCE('a' COLLATE latin1_bin,'b')`
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `COALESCE(1)` int NOT NULL DEFAULT '0',
  `COALESCE(1.0)` decimal(2,1) NOT NULL DEFAULT '0.0',
  `COALESCE('a')` varchar(1) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `COALESCE(1,1.0)` decimal(2,1) NOT NULL DEFAULT '0.0',
  `COALESCE(1,'1')` varchar(2) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `COALESCE(1.1,'1')` varchar(4) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `COALESCE('a' COLLATE latin1_bin,'b')` varchar(1) CHARACTER SET latin1 COLLATE latin1_bin NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
CREATE TABLE t1 SELECT IFNULL('a' COLLATE latin1_swedish_ci, 'b' COLLATE latin1_bin);
ERROR HY000: Illegal mix of collations (latin1_swedish_ci,EXPLICIT) and (latin1_bin,EXPLICIT) for operation 'ifnull'
SELECT 'case+union+test'
UNION 
SELECT CASE LOWER('1') WHEN LOWER('2') THEN 'BUG' ELSE 'nobug' END;
case+union+test
case+union+test
nobug
SELECT CASE LOWER('1') WHEN LOWER('2') THEN 'BUG' ELSE 'nobug' END;
CASE LOWER('1') WHEN LOWER('2') THEN 'BUG' ELSE 'nobug' END
nobug
SELECT 'case+union+test'
UNION 
SELECT CASE '1' WHEN '2' THEN 'BUG' ELSE 'nobug' END;
case+union+test
case+union+test
nobug
create table t1(a float, b int default 3);
insert into t1 (a) values (2), (11), (8);
select min(a), min(case when 1=1 then a else NULL end),
min(case when 1!=1 then NULL else a end) 
from t1 where b=3 group by b;
min(a)	min(case when 1=1 then a else NULL end)	min(case when 1!=1 then NULL else a end)
2	2	2
drop table t1;
CREATE TABLE t1 (EMPNUM INT);
INSERT INTO t1 VALUES (0), (2);
CREATE TABLE t2 (EMPNUM DECIMAL (4, 2));
INSERT INTO t2 VALUES (0.0), (9.0);
SELECT COALESCE(t2.EMPNUM,t1.EMPNUM) AS CEMPNUM,
t1.EMPNUM AS EMPMUM1, t2.EMPNUM AS EMPNUM2
FROM t1 LEFT JOIN t2 ON t1.EMPNUM=t2.EMPNUM;
CEMPNUM	EMPMUM1	EMPNUM2
0.00	0	0.00
2.00	2	NULL
SELECT IFNULL(t2.EMPNUM,t1.EMPNUM) AS CEMPNUM,
t1.EMPNUM AS EMPMUM1, t2.EMPNUM AS EMPNUM2
FROM t1 LEFT JOIN t2 ON t1.EMPNUM=t2.EMPNUM;
CEMPNUM	EMPMUM1	EMPNUM2
0.00	0	0.00
2.00	2	NULL
DROP TABLE t1,t2;
End of 4.1 tests
create table t1 (a int, b bigint unsigned);
create table t2 (c int);
insert into t1 (a, b) values (1,4572794622775114594), (2,18196094287899841997),
(3,11120436154190595086);
insert into t2 (c) values (1), (2), (3);
select t1.a, (case t1.a when 0 then 0 else t1.b end) d from t1 
join t2 on t1.a=t2.c order by d;
a	d
1	4572794622775114594
3	11120436154190595086
2	18196094287899841997
select t1.a, (case t1.a when 0 then 0 else t1.b end) d from t1 
join t2 on t1.a=t2.c where b=11120436154190595086 order by d;
a	d
3	11120436154190595086
drop table t1, t2;
End of 5.0 tests
CREATE TABLE t1(a YEAR);
SELECT 1 FROM t1 WHERE a=1 AND CASE 1 WHEN a THEN 1 ELSE 1 END;
1
DROP TABLE t1;
SET sql_mode = default;
#
# Bug#19875294 ASSERTION `SRC' FAILED IN MY_STRNXFRM_UNICODE
#              (SIG 6 -STRINGS/CTYPE-utf8mb3.C:5151)
#
set @@sql_mode='';
CREATE TABLE t1(c1 SET('','')CHARACTER SET ucs2) engine=innodb;
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Note	1291	Column 'c1' has duplicated value '' in SET
INSERT INTO t1 VALUES(990101.102);
Warnings:
Warning	1265	Data truncated for column 'c1' at row 1
SELECT COALESCE(c1)FROM t1 ORDER BY 1;
COALESCE(c1)

DROP TABLE t1;
set @@sql_mode=default;
# Bug#24733658: IF and friends give wrong type for signed and unsigned
CREATE TABLE source(bt INTEGER, bf INTEGER, i8u BIGINT UNSIGNED, i8s BIGINT);
INSERT INTO source VALUES
(1,0,0,-9223372036854775808), (1,0,18446744073709551615,9223372036854775807);
CREATE TABLE target
SELECT IF(bt,i8u,i8s) AS u, IF(bf,i8u,i8s) AS s
FROM source;
SHOW CREATE TABLE target;
Table	Create Table
target	CREATE TABLE `target` (
  `u` decimal(20,0) DEFAULT NULL,
  `s` decimal(20,0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SELECT IF(bt,i8u,i8s) AS u, IF(bf,i8u,i8s) AS s
FROM source;
u	s
0	-9223372036854775808
18446744073709551615	9223372036854775807
SELECT * FROM target;
u	s
0	-9223372036854775808
18446744073709551615	9223372036854775807
DROP TABLE target;
CREATE TABLE target
SELECT CASE WHEN bt THEN i8u ELSE i8s END AS u,
CASE WHEN bf THEN i8u ELSE i8s END AS s
FROM source;
SHOW CREATE TABLE target;
Table	Create Table
target	CREATE TABLE `target` (
  `u` decimal(20,0) DEFAULT NULL,
  `s` decimal(20,0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SELECT CASE WHEN bt THEN i8u ELSE i8s END AS u,
CASE WHEN bf THEN i8u ELSE i8s END AS s
FROM source;
u	s
0	-9223372036854775808
18446744073709551615	9223372036854775807
SELECT * FROM target;
u	s
0	-9223372036854775808
18446744073709551615	9223372036854775807
DROP TABLE target;
CREATE TABLE target
SELECT CASE bt WHEN TRUE THEN i8u WHEN FALSE THEN i8s END AS u,
CASE bf WHEN TRUE THEN i8u WHEN FALSE THEN i8s END AS s
FROM source;
SHOW CREATE TABLE target;
Table	Create Table
target	CREATE TABLE `target` (
  `u` decimal(20,0) DEFAULT NULL,
  `s` decimal(20,0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SELECT CASE bt WHEN TRUE THEN i8u WHEN FALSE THEN i8s END AS u,
CASE bf WHEN TRUE THEN i8u WHEN FALSE THEN i8s END AS s
FROM source;
u	s
0	-9223372036854775808
18446744073709551615	9223372036854775807
SELECT * FROM target;
u	s
0	-9223372036854775808
18446744073709551615	9223372036854775807
DROP TABLE target;
CREATE TABLE target
SELECT COALESCE(i8u, i8s) AS u, COALESCE(i8s, i8u) AS s
FROM source;
SHOW CREATE TABLE target;
Table	Create Table
target	CREATE TABLE `target` (
  `u` decimal(20,0) DEFAULT NULL,
  `s` decimal(20,0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SELECT COALESCE(i8u, i8s) AS u, COALESCE(i8s, i8u) AS s
FROM source;
u	s
0	-9223372036854775808
18446744073709551615	9223372036854775807
SELECT * FROM target;
u	s
0	-9223372036854775808
18446744073709551615	9223372036854775807
DROP TABLE source, target;
# Bug#25139420: BIT(n) data type confused when used with IF, IFNULL
CREATE TABLE t (a bit(5));
INSERT INTO t VALUES
(0),(1),(2),(3),(4),(5),(6),(7),(8),(9),
(10),(11),(12),(19),(20),(21),(29),(30),(31);
SELECT HEX(a),
IFNULL(a,a) AS b,
IFNULL(a,a)+0 AS c,
IFNULL(a+0,a+0) AS d,
IFNULL(a+0,a) AS e,
IFNULL(a,a+0) AS f
FROM t;
HEX(a)	b	c	d	e	f
0	0	0	0	0	0
1	1	1	1	1	1
2	2	2	2	2	2
3	3	3	3	3	3
4	4	4	4	4	4
5	5	5	5	5	5
6	6	6	6	6	6
7	7	7	7	7	7
8	8	8	8	8	8
9	9	9	9	9	9
A	10	10	10	10	10
B	11	11	11	11	11
C	12	12	12	12	12
13	19	19	19	19	19
14	20	20	20	20	20
15	21	21	21	21	21
1D	29	29	29	29	29
1E	30	30	30	30	30
1F	31	31	31	31	31
CREATE TABLE u AS SELECT HEX(a),
IFNULL(a,a) AS b,
IFNULL(a,a)+0 AS c,
IFNULL(a+0,a+0) AS d,
IFNULL(a+0,a) AS e,
IFNULL(a,a+0) AS f
FROM t;
SHOW CREATE TABLE u;
Table	Create Table
u	CREATE TABLE `u` (
  `HEX(a)` varchar(16) CHARACTER SET latin1 DEFAULT NULL,
  `b` bit(5) DEFAULT NULL,
  `c` int unsigned DEFAULT NULL,
  `d` bigint unsigned DEFAULT NULL,
  `e` bigint unsigned DEFAULT NULL,
  `f` bigint unsigned DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t, u;
# Bug#26389402: Outer join optimized away with user-defined functions
CREATE TABLE t00(a INTEGER);
INSERT INTO t00 VALUES (1),(2);
CREATE TABLE t01(a INTEGER);
INSERT INTO t01 VALUES (1);
CREATE VIEW v0 AS
SELECT t00.a, t01.a AS b, IFNULL(t01.a, 666) AS c
FROM t00 LEFT JOIN t01 USING(a);
SELECT * FROM v0
WHERE c >= 0;
a	b	c
1	1	1
2	NULL	666
CREATE FUNCTION f(a INTEGER) RETURNS INTEGER DETERMINISTIC
RETURN IFNULL(a, 666);
CREATE VIEW v1 AS
SELECT t00.a, t01.a AS b, f(t01.a) AS c
FROM t00 LEFT JOIN t01 USING(a);
SELECT * FROM v1
WHERE c >= 0;
a	b	c
1	1	1
2	NULL	666
CREATE VIEW v2 AS
SELECT t00.a, t01.a AS b, f(IFNULL(t01.a, NULL)) AS c
FROM t00 LEFT JOIN t01 USING(a);
SELECT * FROM v2
WHERE c >= 0;
a	b	c
1	1	1
2	NULL	666
SELECT t00.a, t01.a AS b, f(t01.a) AS c
FROM t00 LEFT JOIN t01 USING(a)
WHERE f(t01.a) >= 0;
a	b	c
1	1	1
2	NULL	666
DROP FUNCTION f;
DROP VIEW v0, v1, v2;
DROP TABLE t00, t01;
#
# Bug #25051195 Wrong calculation of decimals after point for IFNULL w/ GROUP BY using
#               temporary table.
CREATE TABLE t (i1 INT,
d1 DOUBLE,
e2 DECIMAL(5,2));
INSERT INTO t VALUES ( 6,    6.0,  10.0/3),
( null, 9.0,  10.0/3),
( 1,    null, 10.0/3),
( 2,    2.0,  null  );
Warnings:
Note	1265	Data truncated for column 'e2' at row 1
Note	1265	Data truncated for column 'e2' at row 2
Note	1265	Data truncated for column 'e2' at row 3
IFNULL and IF should yield same number for decimals after point here
SELECT IFNULL(e2,i1) nullif_c,
IF(e2 IS NULL,i1,e2) if_c,
SUM(d1) FROM t
GROUP BY e2,i1 ORDER BY nullif_c, SUM(d1);
nullif_c	if_c	SUM(d1)
2.00	2.00	2
3.33	3.33	NULL
3.33	3.33	6
3.33	3.33	9
DROP TABLE t;
# Bug#29463760 In cases like ifnull(datetime(2), time(3), wrong answer
SET TIMESTAMP=UNIX_TIMESTAMP('2019-03-11 12:00:00');
CREATE TABLE t1 (dt2 DATETIME(2), t3 TIME(3), d DATE);
INSERT INTO t1 VALUES ('2001-01-01 00:00:00.12', '00:00:00.567', '2002-01-01');
SELECT CONCAT(IFNULL(t3, d)) AS col1 FROM t1;
col1
2019-03-11 00:00:00.567
SELECT CONCAT(IFNULL(t3, d)) AS col1 FROM t1 GROUP BY col1;
col1
2019-03-11 00:00:00.567
SET TIMESTAMP=DEFAULT;
DROP TABLE t1;
#
# Test for a memory leak in WL#6570
#
CREATE TABLE t1 (a VARCHAR(110));
INSERT INTO t1 VALUES (REPEAT("a",100));
SELECT (t1.a,t1.a) IN (('a','c'),('a','b')) END FROM t1;
END
0
SELECT CASE t1.a WHEN 'a' THEN 'c' ELSE 'd' END FROM t1;
CASE t1.a WHEN 'a' THEN 'c' ELSE 'd' END
d
DROP TABLE t1;
#
# Bug#32591239: VAL_JSON(JSON_WRAPPER*):ASSERTION
#               `!CURRENT_THD->IS_ERROR() && HAS_VALUE' FAILED
#
CREATE TABLE t1 (col_int int, col_double double, j json);
INSERT INTO t1 VALUES (382218415, -36452.389, '{"key1": 220655528}');
SELECT col_int FROM t1
WHERE CASE WHEN POWER(col_double, col_int) THEN j ELSE j END;
ERROR 22003: DOUBLE value is out of range in 'pow(`test`.`t1`.`col_double`,`test`.`t1`.`col_int`)'
DROP TABLE t1;
# Bug#32865008: GROUP_CONCAT: setup_fields: assertion `!thd->is_error()' failed
DO GROUP_CONCAT(DISTINCT NULLIF(1, PERIOD_ADD(0x6f09c5f8 ,'7451-01-27')));
ERROR HY000: Incorrect arguments to period_add
# Bug#35513196: Assertion failed: this_type != enum_json_type::J_ERROR
DO IFNULL(multipolygon(
multilinestring(
linestring(point(8117,-31186), point(31282,20992)),
linestring(point(-10280,-15814), point(13662,-12122),
point(12677,16556)))),
benchmark(2, version() >> (NOT json_objectagg('{"ab":2}',
'[{"a":"3"},{"a":2},{"b":1},{"a":0},{"a":[1,2]}]'))));
ERROR HY000: Incorrect arguments to multipolygon
DO COALESCE(multipolygon(
multilinestring(
linestring(point(8117,-31186), point(31282,20992)),
linestring(point(-10280,-15814), point(13662,-12122),
point(12677,16556)))),
benchmark(2, version() >> (NOT json_objectagg('{"ab":2}',
'[{"a":"3"},{"a":2},{"b":1},{"a":0},{"a":[1,2]}]'))));
ERROR HY000: Incorrect arguments to multipolygon
#
# Bug#36421511 Assertion `digits >= count_digits(number)' failed.
#
CREATE TABLE t (
c3 DATETIME(6) NOT NULL,
c4 DATE NOT NULL
);
INSERT INTO t VALUES ('2024-08-17 22:27:27.000000','2024-11-27');
SELECT
CASE(c3->'')
WHEN(FROM_UNIXTIME(36))
THEN(c4)
ELSE(c3)
END
FROM t;
ERROR 22032: Invalid data type for JSON data in argument 1 to function json_extract; a JSON string or JSON type is required.
DROP TABLE t;
# Bug#36570439: Assertion failed: other_type != enum_json_type::J_ERROR
#               Json_wrapper::compare()
CREATE TABLE t (a INT, b JSON, c VARCHAR(1));
INSERT INTO t VALUES (0, '{}', NULL);
SELECT b
FROM t
WHERE (CASE
WHEN a THEN NOW(2)
WHEN b->'w' THEN a < b
ELSE b <= 1
END
) <=> c;
ERROR 42000: Invalid JSON path expression. The error is around character position 1.
DROP TABLE t;
#
# Bug#36844420: Assertion failed: tmp >= tmp_value.ptr() in
#               Item_func_reverse::val_str()
#
SELECT REVERSE(IFNULL(DENSE_RANK() OVER(),
CONVERT('abc' USING utf32))) AS c;
c
1
