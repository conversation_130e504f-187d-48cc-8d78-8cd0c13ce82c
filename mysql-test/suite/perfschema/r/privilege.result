SHOW GRANTS;
Grants for root@localhost
GRANT <ALL_STATIC_PRIVILEGES> ON *.* TO `root`@`localhost` WITH GRANT OPTION
GRANT <ALL_DYNAMIC_PRIVILEGES> ON *.* TO `root`@`localhost` WITH GRANT OPTION
GRANT PROXY ON ``@`` TO `root`@`localhost` WITH GRANT OPTION
create user 'pfs_user_1'@localhost;
create user 'pfs_user_2'@localhost;
create user 'pfs_user_3'@localhost;
grant ALL on *.* to 'pfs_user_1'@localhost with GRANT OPTION;
grant ALL on performance_schema.* to 'pfs_user_2'@localhost
with GRANT OPTION;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant CREATE on performance_schema.* to 'pfs_user_2'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant REFERENCES on performance_schema.* to 'pfs_user_2'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant INDEX on performance_schema.* to 'pfs_user_2'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant ALTER on performance_schema.* to 'pfs_user_2'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant CREATE TEMPORARY TABLES on performance_schema.* to 'pfs_user_2'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant EXECUTE on performance_schema.* to 'pfs_user_2'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant CREATE VIEW on performance_schema.* to 'pfs_user_2'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant SHOW VIEW on performance_schema.* to 'pfs_user_2'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant CREATE ROUTINE on performance_schema.* to 'pfs_user_2'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant ALTER ROUTINE on performance_schema.* to 'pfs_user_2'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant EVENT on performance_schema.* to 'pfs_user_2'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant TRIGGER on performance_schema.* to 'pfs_user_2'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant SELECT on performance_schema.* to 'pfs_user_2'@localhost;
grant INSERT on performance_schema.* to 'pfs_user_2'@localhost;
grant UPDATE on performance_schema.* to 'pfs_user_2'@localhost;
grant DELETE on performance_schema.* to 'pfs_user_2'@localhost;
grant DROP on performance_schema.* to 'pfs_user_2'@localhost;
grant LOCK TABLES on performance_schema.* to 'pfs_user_2'@localhost;
grant ALL on performance_schema.setup_instruments to 'pfs_user_3'@localhost
with GRANT OPTION;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant CREATE on performance_schema.setup_instruments to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant DROP on performance_schema.setup_instruments to 'pfs_user_3'@localhost;
ERROR 42000: DROP, GRANT command denied to user 'root'@'localhost' for table 'setup_instruments'
grant REFERENCES on performance_schema.setup_instruments to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant INDEX on performance_schema.setup_instruments to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant ALTER on performance_schema.setup_instruments to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant CREATE VIEW on performance_schema.setup_instruments to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant SHOW VIEW on performance_schema.setup_instruments to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant TRIGGER on performance_schema.setup_instruments to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant INSERT on performance_schema.setup_instruments to 'pfs_user_3'@localhost;
ERROR 42000: INSERT, GRANT command denied to user 'root'@'localhost' for table 'setup_instruments'
grant DELETE on performance_schema.setup_instruments to 'pfs_user_3'@localhost;
ERROR 42000: DELETE, GRANT command denied to user 'root'@'localhost' for table 'setup_instruments'
grant SELECT on performance_schema.setup_instruments to 'pfs_user_3'@localhost
with GRANT OPTION;
grant UPDATE on performance_schema.setup_instruments to 'pfs_user_3'@localhost
with GRANT OPTION;
grant ALL on performance_schema.events_waits_current to 'pfs_user_3'@localhost
with GRANT OPTION;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant CREATE on performance_schema.events_waits_current to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant DROP on performance_schema.events_waits_current to 'pfs_user_3'@localhost;
grant REFERENCES on performance_schema.events_waits_current to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant INDEX on performance_schema.events_waits_current to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant ALTER on performance_schema.events_waits_current to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant CREATE VIEW on performance_schema.events_waits_current to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant SHOW VIEW on performance_schema.events_waits_current to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant TRIGGER on performance_schema.events_waits_current to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant INSERT on performance_schema.events_waits_current to 'pfs_user_3'@localhost;
ERROR 42000: INSERT, GRANT command denied to user 'root'@'localhost' for table 'events_waits_current'
grant UPDATE on performance_schema.events_waits_current to 'pfs_user_3'@localhost;
ERROR 42000: UPDATE, GRANT command denied to user 'root'@'localhost' for table 'events_waits_current'
grant DELETE on performance_schema.events_waits_current to 'pfs_user_3'@localhost;
ERROR 42000: DELETE, GRANT command denied to user 'root'@'localhost' for table 'events_waits_current'
grant SELECT on performance_schema.events_waits_current to 'pfs_user_3'@localhost
with GRANT OPTION;
grant ALL on performance_schema.file_instances to 'pfs_user_3'@localhost
with GRANT OPTION;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant CREATE on performance_schema.file_instances to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant DROP on performance_schema.file_instances to 'pfs_user_3'@localhost;
ERROR 42000: DROP, GRANT command denied to user 'root'@'localhost' for table 'file_instances'
grant REFERENCES on performance_schema.file_instances to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant INDEX on performance_schema.file_instances to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant ALTER on performance_schema.file_instances to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant CREATE VIEW on performance_schema.file_instances to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant SHOW VIEW on performance_schema.file_instances to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant TRIGGER on performance_schema.file_instances to 'pfs_user_3'@localhost;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
grant INSERT on performance_schema.file_instances to 'pfs_user_3'@localhost;
ERROR 42000: INSERT, GRANT command denied to user 'root'@'localhost' for table 'file_instances'
grant UPDATE on performance_schema.file_instances to 'pfs_user_3'@localhost;
ERROR 42000: UPDATE, GRANT command denied to user 'root'@'localhost' for table 'file_instances'
grant DELETE on performance_schema.file_instances to 'pfs_user_3'@localhost;
ERROR 42000: DELETE, GRANT command denied to user 'root'@'localhost' for table 'file_instances'
grant SELECT on performance_schema.file_instances to 'pfs_user_3'@localhost
with GRANT OPTION;
grant LOCK TABLES on performance_schema.* to 'pfs_user_3'@localhost
with GRANT OPTION;
flush privileges;
Warnings:
Warning	1681	'FLUSH PRIVILEGES' is deprecated and will be removed in a future release.
drop table if exists test.t1;
rename table performance_schema.setup_instruments to test.t1;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
rename table performance_schema.events_waits_current to test.t1;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
rename table performance_schema.file_instances to test.t1;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
rename table performance_schema.setup_instruments to performance_schema.t1;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
rename table performance_schema.events_waits_current to performance_schema.t1;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
rename table performance_schema.file_instances to performance_schema.t1;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
rename table performance_schema.setup_instruments
to performance_schema.events_waits_current;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
rename table performance_schema.events_waits_current
to performance_schema.setup_instruments;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
create procedure performance_schema.my_proc() begin end;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
create function performance_schema.my_func() returns int return 0;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
create event performance_schema.my_event on schedule every 15 minute
do begin end;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
create trigger performance_schema.bi_setup_instruments
before insert on performance_schema.setup_instruments
for each row begin end;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
create trigger performance_schema.bi_events_waits_current
before insert on performance_schema.events_waits_current
for each row begin end;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
create trigger performance_schema.bi_file_instances
before insert on performance_schema.file_instances
for each row begin end;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
create table test.t1(a int) engine=PERFORMANCE_SCHEMA;
ERROR HY000: Invalid performance_schema usage.
create table test.t1 like performance_schema.setup_instruments;
ERROR HY000: Invalid performance_schema usage.
create table test.t1 like performance_schema.events_waits_current;
ERROR HY000: Invalid performance_schema usage.
create table test.t1 like performance_schema.file_instances;
ERROR HY000: Invalid performance_schema usage.
insert into performance_schema.setup_instruments
set name="foo";
ERROR 42000: INSERT command denied to user 'root'@'localhost' for table 'setup_instruments'
insert into performance_schema.events_waits_current
set name="foo";
ERROR 42000: INSERT command denied to user 'root'@'localhost' for table 'events_waits_current'
insert into performance_schema.file_instances
set name="foo";
ERROR 42000: INSERT command denied to user 'root'@'localhost' for table 'file_instances'
delete from performance_schema.setup_instruments;
ERROR 42000: DELETE command denied to user 'root'@'localhost' for table 'setup_instruments'
delete from performance_schema.events_waits_current;
ERROR 42000: DELETE command denied to user 'root'@'localhost' for table 'events_waits_current'
delete from performance_schema.file_instances;
ERROR 42000: DELETE command denied to user 'root'@'localhost' for table 'file_instances'
lock table performance_schema.setup_instruments read;
unlock tables;
lock table performance_schema.setup_instruments write;
unlock tables;
lock table performance_schema.events_waits_current read;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'root'@'localhost' for table 'events_waits_current'
unlock tables;
lock table performance_schema.events_waits_current write;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'root'@'localhost' for table 'events_waits_current'
unlock tables;
lock table performance_schema.file_instances read;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'root'@'localhost' for table 'file_instances'
unlock tables;
lock table performance_schema.file_instances write;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'root'@'localhost' for table 'file_instances'
unlock tables;
#
# WL#4818, NFS2: Can use grants to give normal user access
#                to view data from _current and _history tables
#
# Should work as pfs_user_1 and pfs_user_2, but not as pfs_user_3.
# (Except for events_waits_current, which is granted.)
SELECT "can select" FROM performance_schema.events_waits_history LIMIT 1;
can select
can select
SELECT "can select" FROM performance_schema.events_waits_history_long LIMIT 1;
can select
can select
SELECT "can select" FROM performance_schema.events_waits_current LIMIT 1;
can select
can select
SELECT "can select" FROM performance_schema.events_waits_summary_by_instance LIMIT 1;
can select
can select
SELECT "can select" FROM performance_schema.file_summary_by_instance LIMIT 1;
can select
can select
drop table if exists test.t1;
rename table performance_schema.setup_instruments to test.t1;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
rename table performance_schema.events_waits_current to test.t1;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
rename table performance_schema.file_instances to test.t1;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
rename table performance_schema.setup_instruments to performance_schema.t1;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
rename table performance_schema.events_waits_current to performance_schema.t1;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
rename table performance_schema.file_instances to performance_schema.t1;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
rename table performance_schema.setup_instruments
to performance_schema.events_waits_current;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
rename table performance_schema.events_waits_current
to performance_schema.setup_instruments;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
create procedure performance_schema.my_proc() begin end;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
create function performance_schema.my_func() returns int return 0;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
create event performance_schema.my_event on schedule every 15 minute
do begin end;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
create trigger performance_schema.bi_setup_instruments
before insert on performance_schema.setup_instruments
for each row begin end;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
create trigger performance_schema.bi_events_waits_current
before insert on performance_schema.events_waits_current
for each row begin end;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
create trigger performance_schema.bi_file_instances
before insert on performance_schema.file_instances
for each row begin end;
ERROR 42000: Access denied for user 'pfs_user_1'@'localhost' to database 'performance_schema'
create table test.t1(a int) engine=PERFORMANCE_SCHEMA;
ERROR HY000: Invalid performance_schema usage.
create table test.t1 like performance_schema.setup_instruments;
ERROR HY000: Invalid performance_schema usage.
create table test.t1 like performance_schema.events_waits_current;
ERROR HY000: Invalid performance_schema usage.
create table test.t1 like performance_schema.file_instances;
ERROR HY000: Invalid performance_schema usage.
insert into performance_schema.setup_instruments
set name="foo";
ERROR 42000: INSERT command denied to user 'pfs_user_1'@'localhost' for table 'setup_instruments'
insert into performance_schema.events_waits_current
set name="foo";
ERROR 42000: INSERT command denied to user 'pfs_user_1'@'localhost' for table 'events_waits_current'
insert into performance_schema.file_instances
set name="foo";
ERROR 42000: INSERT command denied to user 'pfs_user_1'@'localhost' for table 'file_instances'
delete from performance_schema.setup_instruments;
ERROR 42000: DELETE command denied to user 'pfs_user_1'@'localhost' for table 'setup_instruments'
delete from performance_schema.events_waits_current;
ERROR 42000: DELETE command denied to user 'pfs_user_1'@'localhost' for table 'events_waits_current'
delete from performance_schema.file_instances;
ERROR 42000: DELETE command denied to user 'pfs_user_1'@'localhost' for table 'file_instances'
lock table performance_schema.setup_instruments read;
unlock tables;
lock table performance_schema.setup_instruments write;
unlock tables;
lock table performance_schema.events_waits_current read;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'pfs_user_1'@'localhost' for table 'events_waits_current'
unlock tables;
lock table performance_schema.events_waits_current write;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'pfs_user_1'@'localhost' for table 'events_waits_current'
unlock tables;
lock table performance_schema.file_instances read;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'pfs_user_1'@'localhost' for table 'file_instances'
unlock tables;
lock table performance_schema.file_instances write;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'pfs_user_1'@'localhost' for table 'file_instances'
unlock tables;
#
# WL#4818, NFS2: Can use grants to give normal user access
#                to view data from _current and _history tables
#
# Should work as pfs_user_1 and pfs_user_2, but not as pfs_user_3.
# (Except for events_waits_current, which is granted.)
SELECT "can select" FROM performance_schema.events_waits_history LIMIT 1;
can select
can select
SELECT "can select" FROM performance_schema.events_waits_history_long LIMIT 1;
can select
can select
SELECT "can select" FROM performance_schema.events_waits_current LIMIT 1;
can select
can select
SELECT "can select" FROM performance_schema.events_waits_summary_by_instance LIMIT 1;
can select
can select
SELECT "can select" FROM performance_schema.file_summary_by_instance LIMIT 1;
can select
can select
drop table if exists test.t1;
rename table performance_schema.setup_instruments to test.t1;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
rename table performance_schema.events_waits_current to test.t1;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
rename table performance_schema.file_instances to test.t1;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
rename table performance_schema.setup_instruments to performance_schema.t1;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
rename table performance_schema.events_waits_current to performance_schema.t1;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
rename table performance_schema.file_instances to performance_schema.t1;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
rename table performance_schema.setup_instruments
to performance_schema.events_waits_current;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
rename table performance_schema.events_waits_current
to performance_schema.setup_instruments;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
create procedure performance_schema.my_proc() begin end;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
create function performance_schema.my_func() returns int return 0;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
create event performance_schema.my_event on schedule every 15 minute
do begin end;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
create trigger performance_schema.bi_setup_instruments
before insert on performance_schema.setup_instruments
for each row begin end;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
create trigger performance_schema.bi_events_waits_current
before insert on performance_schema.events_waits_current
for each row begin end;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
create trigger performance_schema.bi_file_instances
before insert on performance_schema.file_instances
for each row begin end;
ERROR 42000: Access denied for user 'pfs_user_2'@'localhost' to database 'performance_schema'
create table test.t1(a int) engine=PERFORMANCE_SCHEMA;
ERROR HY000: Invalid performance_schema usage.
create table test.t1 like performance_schema.setup_instruments;
ERROR HY000: Invalid performance_schema usage.
create table test.t1 like performance_schema.events_waits_current;
ERROR HY000: Invalid performance_schema usage.
create table test.t1 like performance_schema.file_instances;
ERROR HY000: Invalid performance_schema usage.
insert into performance_schema.setup_instruments
set name="foo";
ERROR 42000: INSERT command denied to user 'pfs_user_2'@'localhost' for table 'setup_instruments'
insert into performance_schema.events_waits_current
set name="foo";
ERROR 42000: INSERT command denied to user 'pfs_user_2'@'localhost' for table 'events_waits_current'
insert into performance_schema.file_instances
set name="foo";
ERROR 42000: INSERT command denied to user 'pfs_user_2'@'localhost' for table 'file_instances'
delete from performance_schema.setup_instruments;
ERROR 42000: DELETE command denied to user 'pfs_user_2'@'localhost' for table 'setup_instruments'
delete from performance_schema.events_waits_current;
ERROR 42000: DELETE command denied to user 'pfs_user_2'@'localhost' for table 'events_waits_current'
delete from performance_schema.file_instances;
ERROR 42000: DELETE command denied to user 'pfs_user_2'@'localhost' for table 'file_instances'
lock table performance_schema.setup_instruments read;
unlock tables;
lock table performance_schema.setup_instruments write;
unlock tables;
lock table performance_schema.events_waits_current read;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'pfs_user_2'@'localhost' for table 'events_waits_current'
unlock tables;
lock table performance_schema.events_waits_current write;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'pfs_user_2'@'localhost' for table 'events_waits_current'
unlock tables;
lock table performance_schema.file_instances read;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'pfs_user_2'@'localhost' for table 'file_instances'
unlock tables;
lock table performance_schema.file_instances write;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'pfs_user_2'@'localhost' for table 'file_instances'
unlock tables;
#
# WL#4818, NFS2: Can use grants to give normal user access
#                to view data from _current and _history tables
#
# Should work as pfs_user_1 and pfs_user_2, but not as pfs_user_3.
# (Except for events_waits_current, which is granted.)
SELECT "can select" FROM performance_schema.events_waits_history LIMIT 1;
can select
can select
SELECT "can select" FROM performance_schema.events_waits_history_long LIMIT 1;
can select
can select
SELECT "can select" FROM performance_schema.events_waits_current LIMIT 1;
can select
can select
SELECT "can select" FROM performance_schema.events_waits_summary_by_instance LIMIT 1;
can select
can select
SELECT "can select" FROM performance_schema.file_summary_by_instance LIMIT 1;
can select
can select
drop table if exists test.t1;
rename table performance_schema.setup_instruments to test.t1;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
rename table performance_schema.events_waits_current to test.t1;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
rename table performance_schema.file_instances to test.t1;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
rename table performance_schema.setup_instruments to performance_schema.t1;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
rename table performance_schema.events_waits_current to performance_schema.t1;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
rename table performance_schema.file_instances to performance_schema.t1;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
rename table performance_schema.setup_instruments
to performance_schema.events_waits_current;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
rename table performance_schema.events_waits_current
to performance_schema.setup_instruments;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
create procedure performance_schema.my_proc() begin end;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
create function performance_schema.my_func() returns int return 0;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
create event performance_schema.my_event on schedule every 15 minute
do begin end;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
create trigger performance_schema.bi_setup_instruments
before insert on performance_schema.setup_instruments
for each row begin end;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
create trigger performance_schema.bi_events_waits_current
before insert on performance_schema.events_waits_current
for each row begin end;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
create trigger performance_schema.bi_file_instances
before insert on performance_schema.file_instances
for each row begin end;
ERROR 42000: Access denied for user 'pfs_user_3'@'localhost' to database 'performance_schema'
create table test.t1(a int) engine=PERFORMANCE_SCHEMA;
ERROR HY000: Invalid performance_schema usage.
create table test.t1 like performance_schema.setup_instruments;
ERROR HY000: Invalid performance_schema usage.
create table test.t1 like performance_schema.events_waits_current;
ERROR HY000: Invalid performance_schema usage.
create table test.t1 like performance_schema.file_instances;
ERROR HY000: Invalid performance_schema usage.
insert into performance_schema.setup_instruments
set name="foo";
ERROR 42000: INSERT command denied to user 'pfs_user_3'@'localhost' for table 'setup_instruments'
insert into performance_schema.events_waits_current
set name="foo";
ERROR 42000: INSERT command denied to user 'pfs_user_3'@'localhost' for table 'events_waits_current'
insert into performance_schema.file_instances
set name="foo";
ERROR 42000: INSERT command denied to user 'pfs_user_3'@'localhost' for table 'file_instances'
delete from performance_schema.setup_instruments;
ERROR 42000: DELETE command denied to user 'pfs_user_3'@'localhost' for table 'setup_instruments'
delete from performance_schema.events_waits_current;
ERROR 42000: DELETE command denied to user 'pfs_user_3'@'localhost' for table 'events_waits_current'
delete from performance_schema.file_instances;
ERROR 42000: DELETE command denied to user 'pfs_user_3'@'localhost' for table 'file_instances'
lock table performance_schema.setup_instruments read;
unlock tables;
lock table performance_schema.setup_instruments write;
unlock tables;
lock table performance_schema.events_waits_current read;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'pfs_user_3'@'localhost' for table 'events_waits_current'
unlock tables;
lock table performance_schema.events_waits_current write;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'pfs_user_3'@'localhost' for table 'events_waits_current'
unlock tables;
lock table performance_schema.file_instances read;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'pfs_user_3'@'localhost' for table 'file_instances'
unlock tables;
lock table performance_schema.file_instances write;
ERROR 42000: SELECT, LOCK TABLES command denied to user 'pfs_user_3'@'localhost' for table 'file_instances'
unlock tables;
#
# WL#4818, NFS2: Can use grants to give normal user access
#                to view data from _current and _history tables
#
# Should work as pfs_user_1 and pfs_user_2, but not as pfs_user_3.
# (Except for events_waits_current, which is granted.)
SELECT "can select" FROM performance_schema.events_waits_history LIMIT 1;
ERROR 42000: SELECT command denied to user 'pfs_user_3'@'localhost' for table 'events_waits_history'
SELECT "can select" FROM performance_schema.events_waits_history_long LIMIT 1;
ERROR 42000: SELECT command denied to user 'pfs_user_3'@'localhost' for table 'events_waits_history_long'
SELECT "can select" FROM performance_schema.events_waits_current LIMIT 1;
can select
can select
SELECT "can select" FROM performance_schema.events_waits_summary_by_instance LIMIT 1;
ERROR 42000: SELECT command denied to user 'pfs_user_3'@'localhost' for table 'events_waits_summary_by_instance'
SELECT "can select" FROM performance_schema.file_summary_by_instance LIMIT 1;
ERROR 42000: SELECT command denied to user 'pfs_user_3'@'localhost' for table 'file_summary_by_instance'
revoke all privileges, grant option from 'pfs_user_1'@localhost;
revoke all privileges, grant option from 'pfs_user_2'@localhost;
revoke all privileges, grant option from 'pfs_user_3'@localhost;
drop user 'pfs_user_1'@localhost;
drop user 'pfs_user_2'@localhost;
drop user 'pfs_user_3'@localhost;
flush privileges;
Warnings:
Warning	1681	'FLUSH PRIVILEGES' is deprecated and will be removed in a future release.
DROP SCHEMA performance_schema;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
# Test cases from WL#4818
# Setup user
CREATE user pfs_user_4;
#
# WL#4818, NFS4: Normal user does not have access to view data
#                without grants
#
# Select as pfs_user_4 should fail without grant
SELECT event_id FROM performance_schema.events_waits_history;
ERROR 42000: SELECT command denied to user 'pfs_user_4'@'localhost' for table 'events_waits_history'
SELECT event_id FROM performance_schema.events_waits_history_long;
ERROR 42000: SELECT command denied to user 'pfs_user_4'@'localhost' for table 'events_waits_history_long'
SELECT event_id FROM performance_schema.events_waits_current;
ERROR 42000: SELECT command denied to user 'pfs_user_4'@'localhost' for table 'events_waits_current'
SELECT event_name FROM performance_schema.events_waits_summary_by_instance;
ERROR 42000: SELECT command denied to user 'pfs_user_4'@'localhost' for table 'events_waits_summary_by_instance'
SELECT event_name FROM performance_schema.file_summary_by_instance;
ERROR 42000: SELECT command denied to user 'pfs_user_4'@'localhost' for table 'file_summary_by_instance'
#
# WL#4818, NFS3: Normal user does not have access to change what is
#                instrumented without grants
#
# User pfs_user_4 should not be allowed to tweak instrumentation without
# explicit grant
UPDATE performance_schema.setup_instruments SET enabled = 'NO', timed = 'YES';
ERROR 42000: UPDATE command denied to user 'pfs_user_4'@'localhost' for table 'setup_instruments'
UPDATE performance_schema.setup_instruments SET enabled = 'YES'
WHERE name LIKE 'wait/synch/mutex/%'
   OR name LIKE 'wait/synch/rwlock/%';
ERROR 42000: UPDATE command denied to user 'pfs_user_4'@'localhost' for table 'setup_instruments'
UPDATE performance_schema.setup_consumers SET enabled = 'YES';
ERROR 42000: UPDATE command denied to user 'pfs_user_4'@'localhost' for table 'setup_consumers'
TRUNCATE TABLE performance_schema.events_waits_history_long;
ERROR 42000: DROP command denied to user 'pfs_user_4'@'localhost' for table 'events_waits_history_long'
TRUNCATE TABLE performance_schema.events_waits_history;
ERROR 42000: DROP command denied to user 'pfs_user_4'@'localhost' for table 'events_waits_history'
TRUNCATE TABLE performance_schema.events_waits_current;
ERROR 42000: DROP command denied to user 'pfs_user_4'@'localhost' for table 'events_waits_current'
#
# WL#4814, NFS1: Can use grants to give normal user access
#                to turn on and off instrumentation
#
# Grant access to change tables with the root account
GRANT UPDATE ON performance_schema.setup_consumers TO pfs_user_4;
GRANT UPDATE, SELECT ON performance_schema.setup_instruments TO pfs_user_4;
GRANT DROP ON performance_schema.events_waits_current TO pfs_user_4;
GRANT DROP ON performance_schema.events_waits_history TO pfs_user_4;
GRANT DROP ON performance_schema.events_waits_history_long TO pfs_user_4;
# User pfs_user_4 should now be allowed to tweak instrumentation
UPDATE performance_schema.setup_instruments SET enabled = 'NO', timed = 'YES';
UPDATE performance_schema.setup_instruments SET enabled = 'YES'
WHERE name LIKE 'wait/synch/mutex/%'
   OR name LIKE 'wait/synch/rwlock/%';
UPDATE performance_schema.setup_consumers SET enabled = 'YES';
TRUNCATE TABLE performance_schema.events_waits_history_long;
TRUNCATE TABLE performance_schema.events_waits_history;
TRUNCATE TABLE performance_schema.events_waits_current;
# Clean up
REVOKE ALL PRIVILEGES, GRANT OPTION FROM pfs_user_4;
DROP USER pfs_user_4;
flush privileges;
Warnings:
Warning	1681	'FLUSH PRIVILEGES' is deprecated and will be removed in a future release.
UPDATE performance_schema.setup_instruments SET enabled = 'YES', timed = 'YES';
UPDATE performance_schema.setup_consumers SET enabled = 'YES';
#
# WL#2284: Increase the length of a user name
#
CREATE USER 'user_name_len_22_01234'@localhost;
GRANT ALL ON performance_schema.* TO 'user_name_len_22_01234'@localhost with GRANT OPTION;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'performance_schema'
REVOKE ALL PRIVILEGES, GRANT OPTION FROM 'user_name_len_22_01234'@localhost;
DROP USER 'user_name_len_22_01234'@localhost;
