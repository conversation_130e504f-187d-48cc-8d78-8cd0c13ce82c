# Copyright (c) 2010, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

[atrt]
basedir = CHOOSE_dir
baseport = 14000
clusters = .2node

[ndb_mgmd]

[mysqld]
innodb
skip-bdb

[cluster_config.2node]
ndb_mgmd = CHOOSE_host1
ndbd = CHOOSE_host2,CHOOSE_host3
ndbapi= CHOOSE_host1,CHOOSE_host1,CHOOSE_host1

NoOfReplicas = 2
DataMemory = 400M
BackupMemory = 64M
MaxNoOfConcurrentScans = 100
MaxNoOfSavedMessages= 5
SendBufferMemory = 2M
RedoBuffer = 32M

NoOfFragmentLogFiles = 6
FragmentLogFileSize = 128M
loose-CompressedLCP=1
loose-CompressedBackup=1
loose-ODirect=1
loose-ServerPort=14001

SharedGlobalMemory=256M
InitialLogfileGroup=undo_buffer_size=64M;undofile01.dat:256M;undofile02.dat:256M
InitialTablespace=datafile01.dat:256M;datafile02.dat:256M

[cluster_config.ndbd.1.2node]
FileSystemPath= /export/home/<USER>/ndb

[cluster_config.ndbd.2.2node]
FileSystemPath= /export/home/<USER>/ndb
