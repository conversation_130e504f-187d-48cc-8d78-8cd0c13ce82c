--disable_warnings
DROP TABLE IF EXISTS t1,t2,t3;
--enable_warnings
CREATE TABLE t1 (c1 TINYINT NOT NULL PRIMARY KEY, c2 TINYINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 TINYINT, new1 TINYINT, old2 TINYINT, new2 TINYINT);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t3 VALUES(1,0);
delimiter //;
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr2 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr3 BEFORE UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr4 AFTER UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr5 BEFORE DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr6 AFTER DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
UPDATE t1 SET c2=10 WHERE c1=1;
UPDATE t1 SET c2=10 WHERE c1=0;
UPDATE t1 SET c2=10 WHERE c1=NULL;
UPDATE t1 SET c2=10 WHERE c1=6 OR c1=7;
UPDATE t1 SET c2=10 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DELETE FROM t1 WHERE c1=1;
DELETE FROM t1 WHERE c1=0;
DELETE FROM t1 WHERE c1=NULL;
DELETE FROM t1 WHERE c1=6 OR c1=7;
DELETE FROM t1 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DROP TRIGGER tr1;
DROP TRIGGER tr2;
DROP TRIGGER tr3;
DROP TRIGGER tr4;
DROP TRIGGER tr5;
DROP TRIGGER tr6;
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 SMALLINT NOT NULL PRIMARY KEY, c2 SMALLINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 SMALLINT, new1 SMALLINT, old2 SMALLINT, new2 SMALLINT);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t3 VALUES(1,0);
delimiter //;
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr2 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr3 BEFORE UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr4 AFTER UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr5 BEFORE DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr6 AFTER DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
UPDATE t1 SET c2=10 WHERE c1=1;
UPDATE t1 SET c2=10 WHERE c1=0;
UPDATE t1 SET c2=10 WHERE c1=NULL;
UPDATE t1 SET c2=10 WHERE c1=6 OR c1=7;
UPDATE t1 SET c2=10 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DELETE FROM t1 WHERE c1=1;
DELETE FROM t1 WHERE c1=0;
DELETE FROM t1 WHERE c1=NULL;
DELETE FROM t1 WHERE c1=6 OR c1=7;
DELETE FROM t1 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DROP TRIGGER tr1;
DROP TRIGGER tr2;
DROP TRIGGER tr3;
DROP TRIGGER tr4;
DROP TRIGGER tr5;
DROP TRIGGER tr6;
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL PRIMARY KEY, c2 MEDIUMINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 MEDIUMINT, new1 MEDIUMINT, old2 MEDIUMINT, new2 MEDIUMINT);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t3 VALUES(1,0);
delimiter //;
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr2 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr3 BEFORE UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr4 AFTER UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr5 BEFORE DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr6 AFTER DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
UPDATE t1 SET c2=10 WHERE c1=1;
UPDATE t1 SET c2=10 WHERE c1=0;
UPDATE t1 SET c2=10 WHERE c1=NULL;
UPDATE t1 SET c2=10 WHERE c1=6 OR c1=7;
UPDATE t1 SET c2=10 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DELETE FROM t1 WHERE c1=1;
DELETE FROM t1 WHERE c1=0;
DELETE FROM t1 WHERE c1=NULL;
DELETE FROM t1 WHERE c1=6 OR c1=7;
DELETE FROM t1 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DROP TRIGGER tr1;
DROP TRIGGER tr2;
DROP TRIGGER tr3;
DROP TRIGGER tr4;
DROP TRIGGER tr5;
DROP TRIGGER tr6;
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 INT NOT NULL PRIMARY KEY, c2 INT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 INT, new1 INT, old2 INT, new2 INT);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t3 VALUES(1,0);
delimiter //;
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr2 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr3 BEFORE UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr4 AFTER UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr5 BEFORE DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr6 AFTER DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
UPDATE t1 SET c2=10 WHERE c1=1;
UPDATE t1 SET c2=10 WHERE c1=0;
UPDATE t1 SET c2=10 WHERE c1=NULL;
UPDATE t1 SET c2=10 WHERE c1=6 OR c1=7;
UPDATE t1 SET c2=10 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DELETE FROM t1 WHERE c1=1;
DELETE FROM t1 WHERE c1=0;
DELETE FROM t1 WHERE c1=NULL;
DELETE FROM t1 WHERE c1=6 OR c1=7;
DELETE FROM t1 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DROP TRIGGER tr1;
DROP TRIGGER tr2;
DROP TRIGGER tr3;
DROP TRIGGER tr4;
DROP TRIGGER tr5;
DROP TRIGGER tr6;
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 INTEGER, new1 INTEGER, old2 INTEGER, new2 INTEGER);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t3 VALUES(1,0);
delimiter //;
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr2 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr3 BEFORE UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr4 AFTER UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr5 BEFORE DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr6 AFTER DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
UPDATE t1 SET c2=10 WHERE c1=1;
UPDATE t1 SET c2=10 WHERE c1=0;
UPDATE t1 SET c2=10 WHERE c1=NULL;
UPDATE t1 SET c2=10 WHERE c1=6 OR c1=7;
UPDATE t1 SET c2=10 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DELETE FROM t1 WHERE c1=1;
DELETE FROM t1 WHERE c1=0;
DELETE FROM t1 WHERE c1=NULL;
DELETE FROM t1 WHERE c1=6 OR c1=7;
DELETE FROM t1 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DROP TRIGGER tr1;
DROP TRIGGER tr2;
DROP TRIGGER tr3;
DROP TRIGGER tr4;
DROP TRIGGER tr5;
DROP TRIGGER tr6;
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 BIGINT, new1 BIGINT, old2 BIGINT, new2 BIGINT);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t3 VALUES(1,0);
delimiter //;
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr2 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr3 BEFORE UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr4 AFTER UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr5 BEFORE DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr6 AFTER DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
UPDATE t1 SET c2=10 WHERE c1=1;
UPDATE t1 SET c2=10 WHERE c1=0;
UPDATE t1 SET c2=10 WHERE c1=NULL;
UPDATE t1 SET c2=10 WHERE c1=6 OR c1=7;
UPDATE t1 SET c2=10 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DELETE FROM t1 WHERE c1=1;
DELETE FROM t1 WHERE c1=0;
DELETE FROM t1 WHERE c1=NULL;
DELETE FROM t1 WHERE c1=6 OR c1=7;
DELETE FROM t1 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DROP TRIGGER tr1;
DROP TRIGGER tr2;
DROP TRIGGER tr3;
DROP TRIGGER tr4;
DROP TRIGGER tr5;
DROP TRIGGER tr6;
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 REAL NOT NULL PRIMARY KEY, c2 REAL);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 REAL, new1 REAL, old2 REAL, new2 REAL);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t3 VALUES(1,0);
delimiter //;
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr2 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr3 BEFORE UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr4 AFTER UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr5 BEFORE DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr6 AFTER DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
UPDATE t1 SET c2=10 WHERE c1=1;
UPDATE t1 SET c2=10 WHERE c1=0;
UPDATE t1 SET c2=10 WHERE c1=NULL;
UPDATE t1 SET c2=10 WHERE c1=6 OR c1=7;
UPDATE t1 SET c2=10 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DELETE FROM t1 WHERE c1=1;
DELETE FROM t1 WHERE c1=0;
DELETE FROM t1 WHERE c1=NULL;
DELETE FROM t1 WHERE c1=6 OR c1=7;
DELETE FROM t1 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DROP TRIGGER tr1;
DROP TRIGGER tr2;
DROP TRIGGER tr3;
DROP TRIGGER tr4;
DROP TRIGGER tr5;
DROP TRIGGER tr6;
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 DOUBLE NOT NULL PRIMARY KEY, c2 DOUBLE);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 DOUBLE, new1 DOUBLE, old2 DOUBLE, new2 DOUBLE);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t3 VALUES(1,0);
delimiter //;
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr2 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr3 BEFORE UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr4 AFTER UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr5 BEFORE DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr6 AFTER DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
UPDATE t1 SET c2=10 WHERE c1=1;
UPDATE t1 SET c2=10 WHERE c1=0;
UPDATE t1 SET c2=10 WHERE c1=NULL;
UPDATE t1 SET c2=10 WHERE c1=6 OR c1=7;
UPDATE t1 SET c2=10 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DELETE FROM t1 WHERE c1=1;
DELETE FROM t1 WHERE c1=0;
DELETE FROM t1 WHERE c1=NULL;
DELETE FROM t1 WHERE c1=6 OR c1=7;
DELETE FROM t1 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DROP TRIGGER tr1;
DROP TRIGGER tr2;
DROP TRIGGER tr3;
DROP TRIGGER tr4;
DROP TRIGGER tr5;
DROP TRIGGER tr6;
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 FLOAT NOT NULL PRIMARY KEY, c2 FLOAT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 FLOAT, new1 FLOAT, old2 FLOAT, new2 FLOAT);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t3 VALUES(1,0);
delimiter //;
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr2 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr3 BEFORE UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr4 AFTER UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr5 BEFORE DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr6 AFTER DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
UPDATE t1 SET c2=10 WHERE c1=1;
UPDATE t1 SET c2=10 WHERE c1=0;
UPDATE t1 SET c2=10 WHERE c1=NULL;
UPDATE t1 SET c2=10 WHERE c1=6 OR c1=7;
UPDATE t1 SET c2=10 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DELETE FROM t1 WHERE c1=1;
DELETE FROM t1 WHERE c1=0;
DELETE FROM t1 WHERE c1=NULL;
DELETE FROM t1 WHERE c1=6 OR c1=7;
DELETE FROM t1 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DROP TRIGGER tr1;
DROP TRIGGER tr2;
DROP TRIGGER tr3;
DROP TRIGGER tr4;
DROP TRIGGER tr5;
DROP TRIGGER tr6;
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 DECIMAL NOT NULL PRIMARY KEY, c2 DECIMAL);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 DECIMAL, new1 DECIMAL, old2 DECIMAL, new2 DECIMAL);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t3 VALUES(1,0);
delimiter //;
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr2 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr3 BEFORE UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr4 AFTER UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr5 BEFORE DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr6 AFTER DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
UPDATE t1 SET c2=10 WHERE c1=1;
UPDATE t1 SET c2=10 WHERE c1=0;
UPDATE t1 SET c2=10 WHERE c1=NULL;
UPDATE t1 SET c2=10 WHERE c1=6 OR c1=7;
UPDATE t1 SET c2=10 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DELETE FROM t1 WHERE c1=1;
DELETE FROM t1 WHERE c1=0;
DELETE FROM t1 WHERE c1=NULL;
DELETE FROM t1 WHERE c1=6 OR c1=7;
DELETE FROM t1 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DROP TRIGGER tr1;
DROP TRIGGER tr2;
DROP TRIGGER tr3;
DROP TRIGGER tr4;
DROP TRIGGER tr5;
DROP TRIGGER tr6;
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 NUMERIC NOT NULL PRIMARY KEY, c2 NUMERIC);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 NUMERIC, new1 NUMERIC, old2 NUMERIC, new2 NUMERIC);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t3 VALUES(1,0);
delimiter //;
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr2 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr3 BEFORE UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr4 AFTER UPDATE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,NEW.c1,OLD.c2,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr5 BEFORE DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
delimiter //;
CREATE TRIGGER tr6 AFTER DELETE ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(OLD.c1,0,OLD.c2,0);
UPDATE t3 SET c2 = c2 + OLD.c2 WHERE c1 = 1; 
END//
DELIMITER ;//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
UPDATE t1 SET c2=10 WHERE c1=1;
UPDATE t1 SET c2=10 WHERE c1=0;
UPDATE t1 SET c2=10 WHERE c1=NULL;
UPDATE t1 SET c2=10 WHERE c1=6 OR c1=7;
UPDATE t1 SET c2=10 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DELETE FROM t1 WHERE c1=1;
DELETE FROM t1 WHERE c1=0;
DELETE FROM t1 WHERE c1=NULL;
DELETE FROM t1 WHERE c1=6 OR c1=7;
DELETE FROM t1 WHERE c2=3;
SELECT * FROM t1 ORDER BY c1;
SELECT * FROM t2 ORDER BY c1;
SELECT * FROM t3 ORDER BY c1;
DROP TRIGGER tr1;
DROP TRIGGER tr2;
DROP TRIGGER tr3;
DROP TRIGGER tr4;
DROP TRIGGER tr5;
DROP TRIGGER tr6;
DROP TABLE t1,t2,t3;

