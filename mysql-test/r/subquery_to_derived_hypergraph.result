SET EXPLAIN_FORMAT=tree;
"Optimizer switch subquery_to_derived for subquery scalar to derived transformations."
SET optimizer_switch='subquery_to_derived=on';
TRUNCATE TABLE performance_schema.events_statements_summary_by_digest;
#
# Tests for WL#12885 Add ability to transform scalar subqueries to inner
# derived table
#
CREATE TABLE t1(a INT);
CREATE TABLE t2(a INT);
INSERT INTO t1 VALUES (1),(2),(3),(4);
INSERT INTO t2 VALUES (1),(2);
CREATE TABLE t0 AS SELECT * FROM t1;
CREATE TABLE t3(a INT, b INT);
INSERT INTO t3 VALUES (1,3), (2,3);
ANALYZE TABLE t1, t2, t0, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t0	analyze	status	OK
test.t3	analyze	status	OK
#
# 1. W H E R E  clause subqueries
#
# Implicitly grouped scalar subquery in WHERE clause.
# Automatic transformation to LEFT OUTER join. It is then
# transformed to inner join, and finally the derived table is
# evaluated at optimize or execution time, depending on
# optimization mode.
SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2);
a
3
4
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: count(t2.a)  (rows=1)
                    -> Table scan on t2  (rows=2)

# Equivalent manually recrafted query: same plan as previous
SELECT t1.* FROM t1 LEFT OUTER JOIN
(SELECT COUNT(a) AS cnt FROM t2) AS derived
ON TRUE
WHERE t1.a > derived.cnt;
a
3
4
explain SELECT t1.* FROM t1 LEFT OUTER JOIN
(SELECT COUNT(a) AS cnt FROM t2) AS derived
ON TRUE
WHERE t1.a > derived.cnt;
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived.cnt)  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: count(t2.a)  (rows=1)
                    -> Table scan on t2  (rows=2)

# Implicitly grouped scalar subquery in WHERE clause of joined
# query, here with LEFT JOIN.
# Query 1: Simplified to inner join
SELECT t0.*, t1.* FROM t0 LEFT OUTER JOIN t1 ON t0.a != t1.a
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2);
a	a
1	3
1	4
2	3
2	4
3	4
4	3
explain SELECT t0.*, t1.* FROM t0 LEFT OUTER JOIN t1 ON t0.a != t1.a
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t0.a <> t1.a)  (rows=4.8)
    -> Table scan on t0  (rows=4)
    -> Hash
        -> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.33)
            -> Table scan on t1  (rows=4)
            -> Hash
                -> Table scan on derived_1_2  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: count(t2.a)  (rows=1)
                            -> Table scan on t2  (rows=2)

# Query 2: Not simplified to inner join
SELECT * FROM t0 LEFT OUTER JOIN t1 on t0.a = t1.a
WHERE t0.a > (SELECT COUNT(a) AS cnt FROM t2);
a	a
3	3
4	4
explain SELECT * FROM t0 LEFT OUTER JOIN t1 on t0.a = t1.a
WHERE t0.a > (SELECT COUNT(a) AS cnt FROM t2);
EXPLAIN
-> Left hash join (t0.a = t1.a)  (rows=1.33)
    -> Inner hash join (no condition), extra conditions: (t0.a > derived_1_2.cnt)  (rows=1.33)
        -> Table scan on t0  (rows=4)
        -> Hash
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: count(t2.a)  (rows=1)
                        -> Table scan on t2  (rows=2)
    -> Hash
        -> Table scan on t1  (rows=4)

# Equivalent manually recrafted query: same plan as previous
SELECT t0.*, t1.* FROM (t0 LEFT OUTER JOIN t1 ON t0.a != t1.a) LEFT OUTER JOIN
(SELECT COUNT(a) AS cnt FROM t2) AS derived
ON TRUE
WHERE t1.a > derived.cnt;
a	a
1	3
1	4
2	3
2	4
3	4
4	3
explain SELECT t0.*, t1.* FROM (t0 LEFT OUTER JOIN t1 ON t0.a != t1.a) LEFT OUTER JOIN
(SELECT COUNT(a) AS cnt FROM t2) AS derived
ON TRUE
WHERE t1.a > derived.cnt;
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t0.a <> t1.a)  (rows=4.8)
    -> Table scan on t0  (rows=4)
    -> Hash
        -> Inner hash join (no condition), extra conditions: (t1.a > derived.cnt)  (rows=1.33)
            -> Table scan on t1  (rows=4)
            -> Hash
                -> Table scan on derived  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: count(t2.a)  (rows=1)
                            -> Table scan on t2  (rows=2)

# With deeper nested subquery (subquery inside an OR)
SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2) OR t1.a = 2;
a
2
3
4
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2) OR t1.a = 2;
EXPLAIN
-> Filter: ((t1.a > derived_1_2.cnt) or (t1.a = 2))  (rows=1.6)
    -> Left hash join (no condition)  (rows=4)
        -> Table scan on t1  (rows=4)
        -> Hash
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: count(t2.a)  (rows=1)
                        -> Table scan on t2  (rows=2)

# More than one subquery in the WHERE condition:
SELECT t1.* FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2) AND
t1.a < (SELECT MAX(a) * 4 AS mx FROM t2);
a
3
4
explain SELECT t1.* FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2) AND
t1.a < (SELECT MAX(a) * 4 AS mx FROM t2);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a < derived_1_3.mx)  (rows=0.444)
    -> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.33)
        -> Table scan on t1  (rows=4)
        -> Hash
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: count(t2.a)  (rows=1)
                        -> Table scan on t2  (rows=2)
    -> Hash
        -> Table scan on derived_1_3  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t2.a)  (rows=1)
                    -> Table scan on t2  (rows=2)

# Equivalent manually recrafted query: same plan as previous
SELECT t1.* FROM t1
LEFT JOIN (SELECT COUNT(a) AS cnt FROM t2) AS lj1 ON TRUE
LEFT JOIN (SELECT MAX(a) * 4 AS mx FROM t2) AS lj2 ON TRUE
WHERE t1.a > cnt AND t1.a < mx;
a
3
4
explain SELECT t1.* FROM t1
LEFT JOIN (SELECT COUNT(a) AS cnt FROM t2) AS lj1 ON TRUE
LEFT JOIN (SELECT MAX(a) * 4 AS mx FROM t2) AS lj2 ON TRUE
WHERE t1.a > cnt AND t1.a < mx;
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a < lj2.mx)  (rows=0.444)
    -> Inner hash join (no condition), extra conditions: (t1.a > lj1.cnt)  (rows=1.33)
        -> Table scan on t1  (rows=4)
        -> Hash
            -> Table scan on lj1  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: count(t2.a)  (rows=1)
                        -> Table scan on t2  (rows=2)
    -> Hash
        -> Table scan on lj2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t2.a)  (rows=1)
                    -> Table scan on t2  (rows=2)

# If we have an implicit grouping we we know that cardinality
# of result set is one, so no need for runtime checking of the
# cardinality of the derived tables
SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t3);
a
3
4
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t3);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: count(t3.a)  (rows=1)
                    -> Table scan on t3  (rows=2)

# If not, detect if we see more than one row in subquery
SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t3 GROUP BY a);
ERROR 21000: Subquery returns more than 1 row
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t3 GROUP BY a);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.89)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.41)
            -> Materialize  (rows=1.41)
                -> Group aggregate: count(t3.a)  (rows=1.41)
                    -> Sort: t3.a  (rows=2)
                        -> Table scan on t3  (rows=2)

SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 1);
ERROR 21000: Subquery returns more than 1 row
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 1);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=2.67)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=2)
            -> Union materialize with deduplication  (rows=2)
                -> Aggregate: count(t2.a)  (rows=1)
                    -> Table scan on t2  (rows=2)
                -> Rows fetched before execution  (rows=1)

# Should work, {2} \ {1} == {2}
SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 EXCEPT SELECT 1);
a
3
4
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 EXCEPT SELECT 1);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize with deduplication  (rows=1)
                -> Table scan on <except temporary>  (rows=1)
                    -> Except materialize with deduplication  (rows=1)
                        -> Aggregate: count(t2.a)  (rows=1)
                            -> Table scan on t2  (rows=2)
                        -> Rows fetched before execution  (rows=1)

# Should fail, {2} U {3} \ {1} == {2, 3}
SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 3 EXCEPT SELECT 1);
ERROR 21000: Subquery returns more than 1 row
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 3 EXCEPT SELECT 1);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=2.67)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=2)
            -> Materialize with deduplication  (rows=2)
                -> Table scan on <except temporary>  (rows=2)
                    -> Except materialize with deduplication  (rows=2)
                        -> Table scan on <union temporary>  (rows=2)
                            -> Union materialize with deduplication  (rows=2)
                                -> Aggregate: count(t2.a)  (rows=1)
                                    -> Table scan on t2  (rows=2)
                                -> Rows fetched before execution  (rows=1)
                        -> Rows fetched before execution  (rows=1)

# Should return no rows, since the intersection is the empty set
# {2} ∩ {1} == {}
SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 INTERSECT SELECT 1);
a
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 INTERSECT SELECT 1);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize with deduplication  (rows=1)
                -> Table scan on <intersect temporary>  (rows=1)
                    -> Intersect materialize with deduplication  (rows=1)
                        -> Aggregate: count(t2.a)  (rows=1)
                            -> Table scan on t2  (rows=2)
                        -> Rows fetched before execution  (rows=1)

# Should return no rows, since the result is the empty set
# {2} \ {2} == {}
SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 EXCEPT SELECT 2);
a
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 EXCEPT SELECT 2);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize with deduplication  (rows=1)
                -> Table scan on <except temporary>  (rows=1)
                    -> Except materialize with deduplication  (rows=1)
                        -> Aggregate: count(t2.a)  (rows=1)
                            -> Table scan on t2  (rows=2)
                        -> Rows fetched before execution  (rows=1)

# This should give one row: {2} ∩ {2} == {2}
SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 INTERSECT SELECT 2);
a
3
4
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 INTERSECT SELECT 2);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize with deduplication  (rows=1)
                -> Table scan on <intersect temporary>  (rows=1)
                    -> Intersect materialize with deduplication  (rows=1)
                        -> Aggregate: count(t2.a)  (rows=1)
                            -> Table scan on t2  (rows=2)
                        -> Rows fetched before execution  (rows=1)

# This should give one row: {2} ∩ {2} == {2}
SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 INTERSECT ALL SELECT 2);
a
3
4
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 INTERSECT ALL SELECT 2);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Table scan on <intersect temporary>  (rows=1)
                    -> Intersect all materialize  (rows=1)
                        -> Aggregate: count(t2.a)  (rows=1)
                            -> Table scan on t2  (rows=2)
                        -> Rows fetched before execution  (rows=1)

# This should remove the duplicate: {1, 1} ∩ALL {1} == {1}, so ok
SELECT * FROM t1
WHERE t1.a > ((SELECT COUNT(a) AS cnt FROM t2 GROUP BY a LIMIT 2) INTERSECT ALL SELECT 1);
a
2
3
4
explain SELECT * FROM t1
WHERE t1.a > ((SELECT COUNT(a) AS cnt FROM t2 GROUP BY a LIMIT 2) INTERSECT ALL SELECT 1);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Table scan on <intersect temporary>  (rows=1)
                    -> Intersect all materialize  (rows=1)
                        -> Limit: 2 row(s)  (rows=1.41)
                            -> Group aggregate: count(t2.a)  (rows=1.41)
                                -> Sort: t2.a  (rows=2)
                                    -> Table scan on t2  (rows=2)
                        -> Rows fetched before execution  (rows=1)

# ditto
SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 GROUP BY a  INTERSECT ALL SELECT 1);
a
2
3
4
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 GROUP BY a  INTERSECT ALL SELECT 1);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Table scan on <intersect temporary>  (rows=1)
                    -> Intersect all materialize  (rows=1)
                        -> Group aggregate: count(t2.a)  (rows=1.41)
                            -> Sort: t2.a  (rows=2)
                                -> Table scan on t2  (rows=2)
                        -> Rows fetched before execution  (rows=1)

# Empty scalar subquery
SELECT * FROM t1
WHERE t1.a > (SELECT a from t1 WHERE false);
a
explain SELECT * FROM t1
WHERE t1.a > (SELECT a from t1 WHERE false);
EXPLAIN
-> Zero rows (Impossible WHERE)  (rows=0)

SELECT a + (SELECT a from t1 WHERE false) FROM t1;
a + (SELECT a from t1 WHERE false)
NULL
NULL
NULL
NULL
explain SELECT a + (SELECT a from t1 WHERE false) FROM t1;
EXPLAIN
-> Nested loop left join  (rows=4)
    -> Table scan on t1  (rows=4)
    -> Zero rows (Impossible WHERE)  (rows=0)

# If we limit the cardinality, it should work:
SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 1 LIMIT 1);
a
3
4
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 1 LIMIT 1);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=2.67)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=2)
            -> Union materialize with deduplication  (rows=2)
                -> Limit table size: 1 unique row(s)
                    -> Aggregate: count(t2.a)  (rows=1)
                        -> Table scan on t2  (rows=2)
                -> Limit table size: 1 unique row(s)
                    -> Rows fetched before execution  (rows=1)

# Check that offset/limit doesn't fool us:
SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 1 LIMIT 1 OFFSET 1);
a
2
3
4
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 1 LIMIT 1 OFFSET 1);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Limit/Offset: 1/1 row(s)  (rows=1)
            -> Table scan on derived_1_2  (rows=2)
                -> Union materialize with deduplication  (rows=2)
                    -> Limit table size: 2 unique row(s)
                        -> Aggregate: count(t2.a)  (rows=1)
                            -> Table scan on t2  (rows=2)
                    -> Limit table size: 2 unique row(s)
                        -> Rows fetched before execution  (rows=1)

SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 1 LIMIT 1 OFFSET 0);
a
3
4
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 1 LIMIT 1 OFFSET 0);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=2.67)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=2)
            -> Union materialize with deduplication  (rows=2)
                -> Limit table size: 1 unique row(s)
                    -> Aggregate: count(t2.a)  (rows=1)
                        -> Table scan on t2  (rows=2)
                -> Limit table size: 1 unique row(s)
                    -> Rows fetched before execution  (rows=1)

SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 1 LIMIT 2 OFFSET 0);
ERROR 21000: Subquery returns more than 1 row
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 1 LIMIT 2 OFFSET 0);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=2.67)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=2)
            -> Union materialize with deduplication  (rows=2)
                -> Limit table size: 2 unique row(s)
                    -> Aggregate: count(t2.a)  (rows=1)
                        -> Table scan on t2  (rows=2)
                -> Limit table size: 2 unique row(s)
                    -> Rows fetched before execution  (rows=1)

SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 1 LIMIT 2 OFFSET 1);
a
2
3
4
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION SELECT 1 LIMIT 2 OFFSET 1);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Limit/Offset: 2/1 row(s)  (rows=1)
            -> Table scan on derived_1_2  (rows=2)
                -> Union materialize with deduplication  (rows=2)
                    -> Limit table size: 3 unique row(s)
                        -> Aggregate: count(t2.a)  (rows=1)
                            -> Table scan on t2  (rows=2)
                    -> Limit table size: 3 unique row(s)
                        -> Rows fetched before execution  (rows=1)

SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION (SELECT 1 LIMIT 1));
ERROR 21000: Subquery returns more than 1 row
explain SELECT * FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2 UNION (SELECT 1 LIMIT 1));
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.cnt)  (rows=2.67)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=2)
            -> Union materialize with deduplication  (rows=2)
                -> Aggregate: count(t2.a)  (rows=1)
                    -> Table scan on t2  (rows=2)
                -> Limit: 1 row(s)  (rows=1)
                    -> Rows fetched before execution  (rows=1)

Without aggregation
SELECT * FROM t1
WHERE t1.a > (SELECT a FROM t2 LIMIT 1);
a
2
3
4
explain SELECT * FROM t1
WHERE t1.a > (SELECT a FROM t2 LIMIT 1);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.a)  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Limit: 1 row(s)  (rows=1)
                    -> Table scan on t2  (rows=2)

SELECT * FROM t1
WHERE t1.a > (SELECT a FROM t2);
ERROR 21000: Subquery returns more than 1 row
explain SELECT * FROM t1
WHERE t1.a > (SELECT a FROM t2);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (t1.a > derived_1_2.a)  (rows=2.67)
    -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=2)
            -> Materialize  (rows=2)
                -> Table scan on t2  (rows=2)

In the below, the query block under consideration for transformation is
the outer subquery. It

a) contains a scalar subquery in its select list
b) is implicitly grouped, cf. SUM(a)

so we trigger an attempt to put the grouping into a separate derived
table.  But we also see that it contains an aggregate that has an outer
reference, SUM(t3.a), so we leave it untouched.
EXPLAIN
SELECT (SELECT SUM(a) + (SELECT SUM(t1.a) FROM t1) + SUM(t3.a) FROM t2) FROM t3;
EXPLAIN
-> Aggregate: sum(t3.a)  (rows=1)
    -> Table scan on t3  (rows=2)
-> Select #2 (subquery in projection; dependent)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on derived_2_4  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: sum(t2.a)  (rows=1)
                    -> Table scan on t2  (rows=2)
        -> Hash
            -> Table scan on derived_2_5  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: sum(t1.a)  (rows=1)
                        -> Table scan on t1  (rows=4)

Warnings:
Note	1276	Field or reference 'test.t3.a' of SELECT #2 was resolved in SELECT #1
Don't try transform grouping into a derived table if we have a reference
to the scalar subquery in the HAVING clause
EXPLAIN
SELECT SUM(a), (SELECT SUM(b) FROM t3) scalar FROM t1 HAVING SUM(a) > scalar;
EXPLAIN
-> Filter: (sum(t1.a) > scalar)  (rows=1)
    -> Aggregate: sum(t1.a), sum(t1.a)  (rows=1)
        -> Table scan on t1  (rows=4)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: sum(t3.b)  (rows=1)
            -> Table scan on t3  (rows=2)

#
# Check that we disable transform if we set a user variable in the query
# cf. WL#12885 requirement FR#6.
#
EXPLAIN SELECT t1.a + (@foo:=3) FROM t1
WHERE t1.a > (SELECT COUNT(a) AS cnt FROM t2);
EXPLAIN
-> Filter: (t1.a > (select #2))  (rows=1.33)
    -> Table scan on t1  (rows=4)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: count(t2.a)  (rows=1)
            -> Table scan on t2  (rows=2)

Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
#
# Check that RAND precludes transform
#
explain SELECT DISTINCT 3 + (SELECT COUNT(a) + RAND() FROM t1) FROM t1;
EXPLAIN
-> Sort row IDs with duplicate removal: 3 + (SELECT COUNT(a) + RAND() FROM t1)  (rows=2)
    -> Table scan on t1  (rows=4)
-> Select #2 (subquery in projection; uncacheable)
    -> Aggregate: count(t1.a)  (rows=1)
        -> Table scan on t1  (rows=4)

SELECT COUNT(*) > 1 FROM (SELECT DISTINCT 3 + (SELECT COUNT(a) + RAND() FROM t1) FROM t1) AS dt;
COUNT(*) > 1
1
#
# 1.1  J O I N   C O N D I T I O N  containing scalar subquery
#
SELECT t1.a, t2.a
FROM t1
JOIN t2
ON t1.a+t2.a = (SELECT COUNT(*) FROM t1);
a	a
2	2
3	1
explain SELECT t1.a, t2.a
FROM t1
JOIN t2
ON t1.a+t2.a = (SELECT COUNT(*) FROM t1);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((t1.a + t2.a) = derived_1_2.`COUNT(*)`)  (rows=8)
    -> Inner hash join (no condition)  (rows=4)
        -> Table scan on t1  (rows=4)
        -> Hash
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: count(0)  (rows=1)
                        -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on t2  (rows=2)

SELECT t1.a, t2.a, t3.a
FROM t1
JOIN t2
ON t1.a+t2.a = (SELECT COUNT(*) FROM t1)
JOIN t3
ON t1.a + (SELECT MIN(a) FROM t1) = t3.b;
a	a	a
2	2	1
2	2	2
explain SELECT t1.a, t2.a, t3.a
FROM t1
JOIN t2
ON t1.a+t2.a = (SELECT COUNT(*) FROM t1)
JOIN t3
ON t1.a + (SELECT MIN(a) FROM t1) = t3.b;
EXPLAIN
-> Inner hash join ((t1.a + derived_1_3.`MIN(a)`) = t3.b)  (rows=16)
    -> Inner hash join (no condition), extra conditions: ((t1.a + t2.a) = derived_1_2.`COUNT(*)`)  (rows=8)
        -> Inner hash join (no condition)  (rows=4)
            -> Table scan on t1  (rows=4)
            -> Hash
                -> Table scan on derived_1_3  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: min(t1.a)  (rows=1)
                            -> Table scan on t1  (rows=4)
        -> Hash
            -> Inner hash join (no condition)  (rows=2)
                -> Table scan on t2  (rows=2)
                -> Hash
                    -> Table scan on derived_1_2  (rows=1)
                        -> Materialize  (rows=1)
                            -> Aggregate: count(0)  (rows=1)
                                -> Table scan on t1  (rows=4)
    -> Hash
        -> Table scan on t3  (rows=2)

OUTER join
SELECT t1.a, t2.a, t3.a
FROM t1
LEFT JOIN ( t2
JOIN t3
ON t2.a = (SELECT COUNT(*) FROM t1) )
ON t1.a + (SELECT MIN(a) FROM t1) = t3.b;
a	a	a
1	NULL	NULL
2	NULL	NULL
3	NULL	NULL
4	NULL	NULL
explain SELECT t1.a, t2.a, t3.a
FROM t1
LEFT JOIN ( t2
JOIN t3
ON t2.a = (SELECT COUNT(*) FROM t1) )
ON t1.a + (SELECT MIN(a) FROM t1) = t3.b;
EXPLAIN
-> Left hash join ((t1.a + derived_1_3.`MIN(a)`) = t3.b)  (rows=4)
    -> Left hash join (no condition)  (rows=4)
        -> Table scan on t1  (rows=4)
        -> Hash
            -> Table scan on derived_1_3  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: min(t1.a)  (rows=1)
                        -> Table scan on t1  (rows=4)
    -> Hash
        -> Inner hash join (no condition)  (rows=0.4)
            -> Table scan on t3  (rows=2)
            -> Hash
                -> Inner hash join (t2.a = derived_1_2.`COUNT(*)`)  (rows=0.2)
                    -> Table scan on t2  (rows=2)
                    -> Hash
                        -> Table scan on derived_1_2  (rows=1)
                            -> Materialize  (rows=1)
                                -> Aggregate: count(0)  (rows=1)
                                    -> Table scan on t1  (rows=4)

SET optimizer_switch='subquery_to_derived=default';
SELECT t1.a, t2.a, t3.a
FROM t1
LEFT JOIN ( t2
JOIN t3
ON t2.a = (SELECT COUNT(*) FROM t1) )
ON t1.a + (SELECT MIN(a) FROM t1) = t3.b;
a	a	a
1	NULL	NULL
2	NULL	NULL
3	NULL	NULL
4	NULL	NULL
SET optimizer_switch='subquery_to_derived=on';
This is a manually transformed version of the above.
SELECT t1.a, t2.a, t3.a
FROM t1
LEFT JOIN (SELECT MIN(a) FROM t1) derived_1
ON TRUE
LEFT JOIN ( t2
LEFT JOIN (SELECT COUNT(*) FROM t1) AS derived_2
ON TRUE
JOIN t3
ON t2.a = derived_2.`COUNT(*)` )
ON t1.a + derived_1.`MIN(a)` = t3.b;
a	a	a
1	NULL	NULL
2	NULL	NULL
3	NULL	NULL
4	NULL	NULL
explain SELECT t1.a, t2.a, t3.a
FROM t1
LEFT JOIN (SELECT MIN(a) FROM t1) derived_1
ON TRUE
LEFT JOIN ( t2
LEFT JOIN (SELECT COUNT(*) FROM t1) AS derived_2
ON TRUE
JOIN t3
ON t2.a = derived_2.`COUNT(*)` )
ON t1.a + derived_1.`MIN(a)` = t3.b;
EXPLAIN
-> Left hash join ((t1.a + derived_1.`MIN(a)`) = t3.b)  (rows=4)
    -> Left hash join (no condition)  (rows=4)
        -> Table scan on t1  (rows=4)
        -> Hash
            -> Table scan on derived_1  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: min(t1.a)  (rows=1)
                        -> Table scan on t1  (rows=4)
    -> Hash
        -> Inner hash join (no condition)  (rows=0.4)
            -> Table scan on t3  (rows=2)
            -> Hash
                -> Inner hash join (t2.a = derived_2.`COUNT(*)`)  (rows=0.2)
                    -> Table scan on t2  (rows=2)
                    -> Hash
                        -> Table scan on derived_2  (rows=1)
                            -> Materialize  (rows=1)
                                -> Aggregate: count(0)  (rows=1)
                                    -> Table scan on t1  (rows=4)

SELECT t1.a, t2.a, t3.a
FROM t1
STRAIGHT_JOIN ( t2
STRAIGHT_JOIN t3
ON t2.a = (SELECT COUNT(*) FROM t1) )
ON t1.a + (SELECT MIN(a) FROM t1) = t3.b;
a	a	a
explain SELECT t1.a, t2.a, t3.a
FROM t1
STRAIGHT_JOIN ( t2
STRAIGHT_JOIN t3
ON t2.a = (SELECT COUNT(*) FROM t1) )
ON t1.a + (SELECT MIN(a) FROM t1) = t3.b;
EXPLAIN
-> Inner hash join ((t1.a + derived_1_3.`MIN(a)`) = t3.b)  (rows=1.6)
    -> Table scan on t3  (rows=2)
    -> Hash
        -> Inner hash join (no condition)  (rows=0.8)
            -> Inner hash join (no condition)  (rows=4)
                -> Table scan on t1  (rows=4)
                -> Hash
                    -> Table scan on derived_1_3  (rows=1)
                        -> Materialize  (rows=1)
                            -> Aggregate: min(t1.a)  (rows=1)
                                -> Table scan on t1  (rows=4)
            -> Hash
                -> Inner hash join (t2.a = derived_1_2.`COUNT(*)`)  (rows=0.2)
                    -> Table scan on t2  (rows=2)
                    -> Hash
                        -> Table scan on derived_1_2  (rows=1)
                            -> Materialize  (rows=1)
                                -> Aggregate: count(0)  (rows=1)
                                    -> Table scan on t1  (rows=4)

#
# 2. S E L E C T  list expression subqueries
#
SELECT a + (SELECT -SUM(a) FROM t1) AS cnt FROM t2;
cnt
-9
-8
explain SELECT a + (SELECT -SUM(a) FROM t1) AS cnt FROM t2;
EXPLAIN
-> Left hash join (no condition)  (rows=2)
    -> Table scan on t2  (rows=2)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: sum(t1.a)  (rows=1)
                    -> Table scan on t1  (rows=4)

# Equivalent manually recrafted query: same plan as previous
SELECT a + derived.cnt
FROM t2
LEFT OUTER JOIN (SELECT -SUM(a) AS cnt FROM t1) AS derived
ON TRUE;
a + derived.cnt
-8
-9
explain SELECT a + derived.cnt
FROM t2
LEFT OUTER JOIN (SELECT -SUM(a) AS cnt FROM t1) AS derived
ON TRUE;
EXPLAIN
-> Left hash join (no condition)  (rows=2)
    -> Table scan on t2  (rows=2)
    -> Hash
        -> Table scan on derived  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: sum(t1.a)  (rows=1)
                    -> Table scan on t1  (rows=4)

#
# 2.1  S E L E C T  list expression subqueries and UNION
#
SELECT a + (SELECT SUM(a) FROM t1) FROM t1 UNION ALL
SELECT a + (SELECT SUM(a) FROM t1) FROM t1;
a + (SELECT SUM(a) FROM t1)
11
12
13
14
11
12
13
14
explain SELECT a + (SELECT SUM(a) FROM t1) FROM t1 UNION ALL
SELECT a + (SELECT SUM(a) FROM t1) FROM t1;
EXPLAIN
-> Append  (rows=8)
    -> Stream results  (rows=4)
        -> Left hash join (no condition)  (rows=4)
            -> Table scan on t1  (rows=4)
            -> Hash
                -> Table scan on derived_1_2  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: sum(t1.a)  (rows=1)
                            -> Table scan on t1  (rows=4)
    -> Stream results  (rows=4)
        -> Left hash join (no condition)  (rows=4)
            -> Table scan on t1  (rows=4)
            -> Hash
                -> Table scan on derived_3_4  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: sum(t1.a)  (rows=1)
                            -> Table scan on t1  (rows=4)

#
# 3. N E S T E D  scalar subqueries
#
SELECT a + (SELECT SUM(a) + (SELECT COUNT(a) FROM t1) FROM t1) AS cnt FROM t2;
cnt
15
16
explain SELECT a + (SELECT SUM(a) + (SELECT COUNT(a) FROM t1) FROM t1) AS cnt FROM t2;
EXPLAIN
-> Left hash join (no condition)  (rows=2)
    -> Table scan on t2  (rows=2)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Left hash join (no condition)  (rows=1)
                    -> Table scan on derived_2_4  (rows=1)
                        -> Materialize  (rows=1)
                            -> Aggregate: sum(t1.a)  (rows=1)
                                -> Table scan on t1  (rows=4)
                    -> Hash
                        -> Table scan on derived_2_5  (rows=1)
                            -> Materialize  (rows=1)
                                -> Aggregate: count(t1.a)  (rows=1)
                                    -> Table scan on t1  (rows=4)

# Equivalent manually recrafted query: same plan as previous
SELECT (t2.a + derived_1_0.sum_plus_cnt) AS cnt
FROM t2
LEFT JOIN (SELECT (derived_2_0.tmp_aggr_1 + derived_2_1.count_a) AS sum_plus_cnt
FROM (SELECT STRAIGHT_JOIN SUM(t1.a) AS tmp_aggr_1 from t1) derived_2_0
LEFT JOIN (SELECT COUNT(t1.a) AS count_a from t1) derived_2_1
ON TRUE) derived_1_0
ON TRUE;
cnt
15
16
explain SELECT (t2.a + derived_1_0.sum_plus_cnt) AS cnt
FROM t2
LEFT JOIN (SELECT (derived_2_0.tmp_aggr_1 + derived_2_1.count_a) AS sum_plus_cnt
FROM (SELECT STRAIGHT_JOIN SUM(t1.a) AS tmp_aggr_1 from t1) derived_2_0
LEFT JOIN (SELECT COUNT(t1.a) AS count_a from t1) derived_2_1
ON TRUE) derived_1_0
ON TRUE;
EXPLAIN
-> Left hash join (no condition)  (rows=2)
    -> Table scan on t2  (rows=2)
    -> Hash
        -> Left hash join (no condition)  (rows=1)
            -> Table scan on derived_2_0  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: sum(t1.a)  (rows=1)
                        -> Table scan on t1  (rows=4)
            -> Hash
                -> Table scan on derived_2_1  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: count(t1.a)  (rows=1)
                            -> Table scan on t1  (rows=4)

SELECT a + (SELECT SUM(a) + (SELECT COUNT(a) FROM t1)
FROM (SELECT * from t1) t11) AS cnt FROM t2;
cnt
15
16
SELECT AVG(a) OVER () AS `avg`,
a + (SELECT SUM(a) + (SELECT COUNT(a) FROM t1)
FROM (SELECT * from t1) t11) AS cnt FROM t2;
avg	cnt
1.5000	15
1.5000	16
DROP TABLE t0, t1, t2, t3;
#
# 4. C O R R E L A T E D   query to derived with LATERAL
#
# Note: this feature has been disabled for WL#12885.
# The tests are kept in case we re-enable this transformation
#
CREATE TABLE t1(a INT, b INT);
INSERT INTO t1 (a) VALUES (1), (2);
CREATE TABLE t2 SELECT * FROM t1;
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
SELECT (WITH RECURSIVE dt AS (SELECT t1.a AS a UNION
SELECT a+1 FROM dt WHERE a<10)
SELECT t1.a * CONCAT(COUNT(*), '.', FLOOR(AVG(dt.a)))
FROM dt) AS subq
FROM t1;
subq
10.5
19.2
explain SELECT (WITH RECURSIVE dt AS (SELECT t1.a AS a UNION
SELECT a+1 FROM dt WHERE a<10)
SELECT t1.a * CONCAT(COUNT(*), '.', FLOOR(AVG(dt.a)))
FROM dt) AS subq
FROM t1;
EXPLAIN
-> Table scan on t1  (rows=2)
-> Select #2 (subquery in projection; dependent)
    -> Aggregate: avg(dt.a), count(0)  (rows=1)
        -> Table scan on dt  (rows=1)
            -> Materialize recursive CTE dt with deduplication  (rows=1)
                -> Rows fetched before execution  (rows=1)
                -> Repeat until convergence
                    -> Filter: (dt.a < 10)  (rows=0)
                        -> Scan new records on dt  (rows=1000)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #3 was resolved in SELECT #1
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
# Equivalent manually recrafted query: same plan as previous
SELECT derived0.cnct AS subq
FROM t1
LEFT JOIN LATERAL (WITH RECURSIVE dt AS (SELECT t1.a AS a UNION
SELECT (dt.a + 1)
FROM dt WHERE dt.a < 10)
SELECT t1.a * CONCAT(COUNT(0), '.', FLOOR(AVG(dt.a))) AS cnct
FROM dt) derived0
ON TRUE;
subq
10.5
19.2
explain SELECT derived0.cnct AS subq
FROM t1
LEFT JOIN LATERAL (WITH RECURSIVE dt AS (SELECT t1.a AS a UNION
SELECT (dt.a + 1)
FROM dt WHERE dt.a < 10)
SELECT t1.a * CONCAT(COUNT(0), '.', FLOOR(AVG(dt.a))) AS cnct
FROM dt) derived0
ON TRUE;
EXPLAIN
-> Nested loop left join  (rows=2)
    -> Table scan on t1  (rows=2)
    -> Stream results  (rows=1)
        -> Aggregate:   (rows=1)
            -> Table scan on dt  (rows=1)
                -> Materialize recursive CTE dt with deduplication  (rows=1)
                    -> Rows fetched before execution  (rows=1)
                    -> Repeat until convergence
                        -> Filter: (dt.a < 10)  (rows=0)
                            -> Scan new records on dt  (rows=1000)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #3 was resolved in SELECT #1
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
DROP TABLE t1, t2;
# Detect correlation outside of transformed query block which
# is not caught by the scalar aggregate function inspection in
# Item_singlerow_subselect::collect_scalar_subqueries. If there
# is such a subquery, we skip transformation of that block.
CREATE TABLE t1(i INT);
CREATE TABLE t2(a INT);
CREATE TABLE t3(x INT);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
EXPLAIN
SELECT (
SELECT (SELECT COUNT(*) FROM t2) +
(SELECT AVG(a)
FROM t2
WHERE t2.a = t3.x) AS aggs
FROM t1
) AS bignest
FROM t3;
EXPLAIN
-> Table scan on t3  (rows=1)
-> Select #2 (subquery in projection; dependent)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on t1  (rows=1)
        -> Hash
            -> Table scan on derived_2_3  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: count(0)  (rows=1)
                        -> Table scan on t2  (rows=1)
    -> Select #4 (subquery in projection; dependent)
        -> Aggregate: avg(t2.a)  (rows=1)
            -> Filter: (t2.a = t3.x)  (rows=0.1)
                -> Table scan on t2  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t3.x' of SELECT #4 was resolved in SELECT #1
# Without that subquery, we transform the level 2 query block,
# but the the outermost still contains its subquery.
EXPLAIN
SELECT (
SELECT (SELECT COUNT(*) FROM t2) AS aggs
FROM t1
) AS bignest
FROM t3;
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on t3  (rows=1)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Left hash join (no condition)  (rows=1)
                    -> Table scan on t1  (rows=1)
                    -> Hash
                        -> Table scan on derived_2_3  (rows=1)
                            -> Materialize  (rows=1)
                                -> Aggregate: count(0)  (rows=1)
                                    -> Table scan on t2  (rows=1)

DROP TABLE t1, t2, t3;
#
# 5.  S U B Q U E R Y   I N   S E L E C T   L I S T   +   G R O U P E D
#     O U T E R   Q U E R Y
#
CREATE TABLE t1 (a INT NOT NULL, b SMALLINT);
INSERT INTO t1 VALUES (12,12);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
# subquery is separate in SELECT list
SELECT (SELECT COUNT(*)
FROM t1
WHERE a=11725) AS tot,
IFNULL(MAX(b),0)+1 + 5 AS mx
FROM t1
WHERE false;
tot	mx
0	6
explain SELECT (SELECT COUNT(*)
FROM t1
WHERE a=11725) AS tot,
IFNULL(MAX(b),0)+1 + 5 AS mx
FROM t1
WHERE false;
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_3  (rows=1)
        -> Materialize  (rows=1)
            -> Zero input rows (Impossible WHERE), aggregated into one output row  (rows=1)
    -> Hash
        -> Table scan on derived_1_4  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: count(0)  (rows=1)
                    -> Filter: (t1.a = 11725)  (rows=0.1)
                        -> Table scan on t1  (rows=1)

# subquery is part of expression with aggregate in SELECT list
SELECT (SELECT COUNT(*)
FROM t1
WHERE a=11725) +
IFNULL(MAX(b),0)+1 + 5 AS mx
FROM t1
WHERE false;
mx
6
explain SELECT (SELECT COUNT(*)
FROM t1
WHERE a=11725) +
IFNULL(MAX(b),0)+1 + 5 AS mx
FROM t1
WHERE false;
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_3  (rows=1)
        -> Materialize  (rows=1)
            -> Zero input rows (Impossible WHERE), aggregated into one output row  (rows=1)
    -> Hash
        -> Table scan on derived_1_4  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: count(0)  (rows=1)
                    -> Filter: (t1.a = 11725)  (rows=0.1)
                        -> Table scan on t1  (rows=1)

INSERT INTO t1 VALUES (13, 12);
# outer query has DISTINCT, verify it is retained
SELECT DISTINCT (SELECT COUNT(*)
FROM t1) +
IFNULL(MAX(b),0)+1 + 5 AS mx
FROM t1
WHERE a > 5
GROUP BY a;
mx
20
explain SELECT DISTINCT (SELECT COUNT(*)
FROM t1) +
IFNULL(MAX(b),0)+1 + 5 AS mx
FROM t1
WHERE a > 5
GROUP BY a;
EXPLAIN
-> Sort with duplicate removal: mx  (rows=0.667)
    -> Stream results  (rows=0.667)
        -> Group aggregate: max(t1.b)  (rows=0.667)
            -> Sort: t1.a  (rows=0.667)
                -> Left hash join (no condition)  (rows=0.667)
                    -> Filter: (t1.a > 5)  (rows=0.667)
                        -> Table scan on t1  (rows=2)
                    -> Hash
                        -> Table scan on derived_1_2  (rows=1)
                            -> Materialize  (rows=1)
                                -> Aggregate: count(0)  (rows=1)
                                    -> Table scan on t1  (rows=2)

# outer query has LIMIT: verify it is retained
SELECT (SELECT COUNT(*)
FROM t1) +
IFNULL(MAX(b),0)+1 + 5 AS mx
FROM t1
GROUP BY a LIMIT 1;
mx
20
explain SELECT (SELECT COUNT(*)
FROM t1) +
IFNULL(MAX(b),0)+1 + 5 AS mx
FROM t1
GROUP BY a LIMIT 1;
EXPLAIN
-> Limit: 1 row(s)  (rows=1)
    -> Group aggregate: max(t1.b)  (rows=1.41)
        -> Sort: t1.a  (rows=2)
            -> Left hash join (no condition)  (rows=2)
                -> Table scan on t1  (rows=2)
                -> Hash
                    -> Table scan on derived_1_2  (rows=1)
                        -> Materialize  (rows=1)
                            -> Aggregate: count(0)  (rows=1)
                                -> Table scan on t1  (rows=2)

# The subquery under under consideration for transformation contains a
# aggregate function (MIN(t1_outer.a) and we have no explicit grouping,
# so it would a priori be a candidate putting in a derived table.  But
# here, the aggregation function is aggregated in a query block outer to
# the one being transformed.  This means we do not need to push it into a
# derived table.
SELECT
(SELECT (SELECT COUNT(*)
FROM t1) +
MAX(t1.b) + MIN(t1_outer.a) AS tot
FROM t1) FROM t1 AS t1_outer;
(SELECT (SELECT COUNT(*)
FROM t1) +
MAX(t1.b) + MIN(t1_outer.a) AS tot
FROM t1)
26
explain SELECT
(SELECT (SELECT COUNT(*)
FROM t1) +
MAX(t1.b) + MIN(t1_outer.a) AS tot
FROM t1) FROM t1 AS t1_outer;
EXPLAIN
-> Aggregate: min(t1_outer.a)  (rows=1)
    -> Table scan on t1_outer  (rows=2)
-> Select #2 (subquery in projection; dependent)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on derived_2_4  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t1.b)  (rows=1)
                    -> Table scan on t1  (rows=2)
        -> Hash
            -> Table scan on derived_2_5  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: count(0)  (rows=1)
                        -> Table scan on t1  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1_outer.a' of SELECT #2 was resolved in SELECT #1
# Aggregate aggregates in the transformation query block, but is
# contained in a subquery. Currently, this blocks transformation, but can
# be fixed if we let Item::transform visit subqueries.
SELECT (SELECT COUNT(*)
FROM t1) +
MAX(b) +
(SELECT MIN(a) + AVG(top.a) FROM t1)
AS tot
FROM t1 top;
tot
38.5000
explain SELECT (SELECT COUNT(*)
FROM t1) +
MAX(b) +
(SELECT MIN(a) + AVG(top.a) FROM t1)
AS tot
FROM t1 top;
EXPLAIN
-> Aggregate: avg(top.a), max(top.b)  (rows=1)
    -> Table scan on top  (rows=2)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: count(0)  (rows=1)
        -> Table scan on t1  (rows=2)
-> Select #3 (subquery in projection; dependent)
    -> Aggregate: min(t1.a)  (rows=1)
        -> Table scan on t1  (rows=2)

Warnings:
Note	1276	Field or reference 'test.top.a' of SELECT #3 was resolved in SELECT #1
# correlated version:
# explicit grouping, no need for moving the grouping down into a
# derived table
SELECT (SELECT COUNT(*) + `outer`.a
FROM t1) +
IFNULL(MAX(b),0)+1 + 5 AS mx
FROM t1 AS `outer`
GROUP BY a;
mx
32
33
explain SELECT (SELECT COUNT(*) + `outer`.a
FROM t1) +
IFNULL(MAX(b),0)+1 + 5 AS mx
FROM t1 AS `outer`
GROUP BY a;
EXPLAIN
-> Group aggregate: max(`outer`.b)  (rows=1.41)
    -> Sort: `outer`.a  (rows=2)
        -> Table scan on outer  (rows=2)
-> Select #2 (subquery in projection; dependent)
    -> Aggregate: count(0)  (rows=1)
        -> Table scan on t1  (rows=2)

Warnings:
Note	1276	Field or reference 'test.outer.a' of SELECT #2 was resolved in SELECT #1
Translated first step by hand
SELECT (SELECT COUNT(*) + derived_1.d_1 FROM t1) +
IFNULL(derived_1.`MAX(b)`,0) + 1 + 5 AS mx
FROM (SELECT STRAIGHT_JOIN MAX(outer_t.b) AS `MAX(b)`,
outer_t.a AS d_1
FROM t1 outer_t
GROUP BY outer_t.a) derived_1;
mx
32
33
Translated by hand
SELECT (derived_1.`COUNT(*) + outer_t.a` +
IFNULL(derived_0.`MAX(b)`,0)) + 1 + 5 AS mx
FROM (SELECT STRAIGHT_JOIN MAX(outer_t.b) AS `MAX(b)`,
outer_t.a AS d_1
FROM t1 outer_t
GROUP BY outer_t.a) derived_0
LEFT JOIN LATERAL (SELECT (COUNT(0) + derived_0.d_1)
AS `COUNT(*) + outer_t.a`
                        FROM t1) derived_1
ON(true)
WHERE true;
mx
32
33
# correlated version, with outer aggregate, not transformed
SELECT (SELECT COUNT(*) + MAX(outer_t.b)
FROM t1) +
IFNULL(MAX(b),0)+1 + 5 AS mx
FROM t1 AS outer_t
GROUP BY a;
mx
32
32
explain SELECT (SELECT COUNT(*) + MAX(outer_t.b)
FROM t1) +
IFNULL(MAX(b),0)+1 + 5 AS mx
FROM t1 AS outer_t
GROUP BY a;
EXPLAIN
-> Group aggregate: max(outer_t.b), max(outer_t.b)  (rows=1.41)
    -> Sort: outer_t.a  (rows=2)
        -> Table scan on outer_t  (rows=2)
-> Select #2 (subquery in projection; dependent)
    -> Aggregate: count(0)  (rows=1)
        -> Table scan on t1  (rows=2)

Warnings:
Note	1276	Field or reference 'test.outer_t.b' of SELECT #2 was resolved in SELECT #1
DROP TABLE t1;
#
# More complex example
#
CREATE VIEW events_digest AS
SELECT * FROM performance_schema.events_statements_summary_by_digest;
SELECT s2.avg_us avg_us,
IFNULL(SUM(s1.cnt)/NULLIF((SELECT COUNT(*) FROM
events_digest), 0), 0) percentile
FROM sys.`x$ps_digest_avg_latency_distribution` AS s1
JOIN sys.`x$ps_digest_avg_latency_distribution` AS s2
ON s1.avg_us <= s2.avg_us
GROUP BY s2.avg_us
HAVING IFNULL(SUM(s1.cnt)/ NULLIF((SELECT COUNT(*) FROM events_digest), 0), 0) > 0.95
ORDER BY percentile LIMIT 1;
avg_us	percentile
xxxxx	xxxxx
EXPLAIN
SELECT s2.avg_us avg_us,
IFNULL(SUM(s1.cnt)/NULLIF((SELECT COUNT(*) FROM
events_digest), 0), 0) percentile
FROM sys.`x$ps_digest_avg_latency_distribution` AS s1
JOIN sys.`x$ps_digest_avg_latency_distribution` AS s2
ON s1.avg_us <= s2.avg_us
GROUP BY s2.avg_us
HAVING IFNULL(SUM(s1.cnt)/ NULLIF((SELECT COUNT(*) FROM events_digest), 0), 0) > 0.95
ORDER BY percentile LIMIT 1;
EXPLAIN
-> Sort: percentile, limit input to 1 row(s) per chunk  (rows=1)
    -> Stream results  (rows=3.74)
        -> Filter: (ifnull((sum(sys.s1.cnt) / nullif(derived_1_3.`COUNT(*)`,0)),0) > 0.95)  (rows=3.74)
            -> Group aggregate: sum(sys.s1.cnt), sum(sys.s1.cnt)  (rows=3.74)
                -> Sort: sys.s2.avg_us  (rows=66.7)
                    -> Left hash join (no condition)  (rows=66.7)
                        -> Left hash join (no condition)  (rows=66.7)
                            -> Inner hash join (no condition), extra conditions: (sys.s1.avg_us <= sys.s2.avg_us)  (rows=66.7)
                                -> Table scan on s1  (rows=14.1)
                                    -> Materialize  (rows=14.1)
                                        -> Table scan on <temporary>  (rows=14.1)
                                            -> Aggregate using temporary table  (rows=14.1)
                                                -> Table scan on events_statements_summary_by_digest  (rows=200)
                                -> Hash
                                    -> Table scan on s2  (rows=14.1)
                                        -> Materialize  (rows=14.1)
                                            -> Table scan on <temporary>  (rows=14.1)
                                                -> Aggregate using temporary table  (rows=14.1)
                                                    -> Table scan on events_statements_summary_by_digest  (rows=200)
                            -> Hash
                                -> Table scan on derived_1_2  (rows=1)
                                    -> Materialize  (rows=1)
                                        -> Aggregate: count(0)  (rows=1)
                                            -> Table scan on events_statements_summary_by_digest  (rows=200)
                        -> Hash
                            -> Table scan on derived_1_3  (rows=1)
                                -> Materialize  (rows=1)
                                    -> Aggregate: count(0)  (rows=1)
                                        -> Table scan on events_statements_summary_by_digest  (rows=200)

# Manually translated, equivalent query (except we need to
# include the derived_1_1.`COUNT(*)` in the select list for it
# to be resolvable in the HAVING clause), and we need to
# disable strict group checking: safe, but not recognized as
# such by server..
SET sql_mode='';
EXPLAIN
SELECT s2.avg_us AS avg_us,
IFNULL((SUM(s1.cnt) / NULLIF(derived_1_0.`COUNT(*)`,0)),0) AS percentile,
derived_1_1.`COUNT(*)`
FROM sys.`x$ps_digest_avg_latency_distribution` s1 JOIN
sys.`x$ps_digest_avg_latency_distribution` s2 LEFT JOIN
(SELECT COUNT(0) AS `COUNT(*)`
      FROM performance_schema.events_statements_summary_by_digest) derived_1_0
ON(TRUE) LEFT JOIN
(SELECT COUNT(0) AS `COUNT(*)`
      FROM performance_schema.events_statements_summary_by_digest) derived_1_1
ON(TRUE)
WHERE (s1.avg_us <= s2.avg_us)
GROUP BY s2.avg_us
HAVING (IFNULL((SUM(s1.cnt) / NULLIF(derived_1_1.`COUNT(*)`,0)),0) > 0.95)
ORDER BY percentile LIMIT 1;
EXPLAIN
-> Sort: percentile, limit input to 1 row(s) per chunk  (rows=1)
    -> Stream results  (rows=3.74)
        -> Filter: (ifnull((sum(sys.s1.cnt) / nullif(derived_1_1.`COUNT(*)`,0)),0) > 0.95)  (rows=3.74)
            -> Group aggregate: sum(sys.s1.cnt), sum(sys.s1.cnt)  (rows=3.74)
                -> Sort: sys.s2.avg_us  (rows=66.7)
                    -> Left hash join (no condition)  (rows=66.7)
                        -> Left hash join (no condition)  (rows=66.7)
                            -> Inner hash join (no condition), extra conditions: (sys.s1.avg_us <= sys.s2.avg_us)  (rows=66.7)
                                -> Table scan on s1  (rows=14.1)
                                    -> Materialize  (rows=14.1)
                                        -> Table scan on <temporary>  (rows=14.1)
                                            -> Aggregate using temporary table  (rows=14.1)
                                                -> Table scan on events_statements_summary_by_digest  (rows=200)
                                -> Hash
                                    -> Table scan on s2  (rows=14.1)
                                        -> Materialize  (rows=14.1)
                                            -> Table scan on <temporary>  (rows=14.1)
                                                -> Aggregate using temporary table  (rows=14.1)
                                                    -> Table scan on events_statements_summary_by_digest  (rows=200)
                            -> Hash
                                -> Table scan on derived_1_0  (rows=1)
                                    -> Materialize  (rows=1)
                                        -> Aggregate: count(0)  (rows=1)
                                            -> Table scan on events_statements_summary_by_digest  (rows=200)
                        -> Hash
                            -> Table scan on derived_1_1  (rows=1)
                                -> Materialize  (rows=1)
                                    -> Aggregate: count(0)  (rows=1)
                                        -> Table scan on events_statements_summary_by_digest  (rows=200)

SET sql_mode=default;
# outer query has window: verify it is retained on outer level
SELECT AVG(s2.avg_us) OVER () + 3 AS avgsum,
s2.avg_us avg_us,
s2.avg_us avg_us2,
SUM(s2.avg_us) OVER () + 3 AS avgsum2,
IFNULL(SUM(s1.cnt)/NULLIF((SELECT COUNT(*) FROM
events_digest), 0), 0) percentile
FROM sys.`x$ps_digest_avg_latency_distribution` AS s1
JOIN sys.`x$ps_digest_avg_latency_distribution` AS s2
ON s1.avg_us <= s2.avg_us
GROUP BY s2.avg_us
HAVING IFNULL(SUM(s1.cnt)/ NULLIF((SELECT COUNT(*) FROM events_digest), 0), 0) > 0.95
ORDER BY percentile LIMIT 1;
avgsum	avg_us	avg_us2	avgsum2	percentile
xxxxx	xxxxx	xxxxx	xxxxx	xxxxx
DROP VIEW events_digest;
# An example with aggregates in ORDER BY and HAVING not seen in SELECT list
CREATE TABLE t1 (
school_name VARCHAR(45) NOT NULL,
country     VARCHAR(45) NOT NULL,
funds_requested FLOAT NOT NULL,
schooltype  VARCHAR(45) NOT NULL
);
INSERT INTO t1 VALUES ("the school", "USA", 1200, "Human");
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT COUNT(country) AS countrycount,
SUM(funds_requested) AS smcnt,
country,
(SELECT SUM(funds_requested) FROM t1) AS total_funds,
ROW_NUMBER() OVER (ORDER BY STDDEV_POP(funds_requested)) AS rn
FROM t1
GROUP BY country
HAVING AVG(funds_requested) > 0
ORDER BY SUM(ABS(funds_requested));
countrycount	smcnt	country	total_funds	rn
1	1200	USA	1200	1
EXPLAIN SELECT COUNT(country) AS countrycount,
SUM(funds_requested) AS smcnt,
country,
(SELECT SUM(funds_requested) FROM t1) AS total_funds,
ROW_NUMBER() OVER (ORDER BY STDDEV_POP(funds_requested)) AS rn
FROM t1
GROUP BY country
HAVING AVG(funds_requested) > 0
ORDER BY SUM(ABS(funds_requested));
EXPLAIN
-> Sort: `sum(abs(t1.funds_requested))`  (rows=1)
    -> Window aggregate: row_number() OVER (ORDER BY `std(t1.funds_requested)` )   (rows=1)
        -> Sort: `std(t1.funds_requested)`  (rows=1)
            -> Stream results  (rows=1)
                -> Filter: (avg(t1.funds_requested) > 0)  (rows=1)
                    -> Group aggregate: avg(t1.funds_requested), std(t1.funds_requested), sum(abs(t1.funds_requested)), count(t1.country), sum(t1.funds_requested)  (rows=1)
                        -> Sort: t1.country  (rows=1)
                            -> Left hash join (no condition)  (rows=1)
                                -> Table scan on t1  (rows=1)
                                -> Hash
                                    -> Table scan on derived_1_2  (rows=1)
                                        -> Materialize  (rows=1)
                                            -> Aggregate: sum(t1.funds_requested)  (rows=1)
                                                -> Table scan on t1  (rows=1)

SET sql_mode='';
SELECT COUNT(country) AS countrycount,
SUM(funds_requested) AS smcnt,
country,
(SELECT SUM(funds_requested) FROM t1) AS total_funds,
ROW_NUMBER() OVER (ORDER BY STDDEV_POP(funds_requested)) AS rn
FROM t1
HAVING AVG(funds_requested) > 0
ORDER BY SUM(ABS(funds_requested));
countrycount	smcnt	country	total_funds	rn
1	1200	USA	1200	1
EXPLAIN SELECT COUNT(country) AS countrycount,
SUM(funds_requested) AS smcnt,
country,
(SELECT SUM(funds_requested) FROM t1) AS total_funds,
ROW_NUMBER() OVER (ORDER BY STDDEV_POP(funds_requested)) AS rn
FROM t1
HAVING AVG(funds_requested) > 0
ORDER BY SUM(ABS(funds_requested));
EXPLAIN
-> Window aggregate: row_number() OVER (ORDER BY derived_1_3.tmp_aggr_2 )   (rows=1)
    -> Sort: derived_1_3.tmp_aggr_2  (rows=1)
        -> Left hash join (no condition)  (rows=1)
            -> Table scan on derived_1_3  (rows=1)
                -> Materialize  (rows=1)
                    -> Filter: (avg(t1.funds_requested) > 0)  (rows=1)
                        -> Aggregate: count(t1.country), sum(t1.funds_requested), avg(t1.funds_requested), std(t1.funds_requested)  (rows=1)
                            -> Table scan on t1  (rows=1)
            -> Hash
                -> Table scan on derived_1_4  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: sum(t1.funds_requested)  (rows=1)
                            -> Table scan on t1  (rows=1)

Manually translated
SELECT derived_1_0.countrycount AS countrycount,
derived_1_0.smcnt AS smcnt,
derived_1_0.d_1 AS country,
derived_1_1.`SUM(funds_requested)` AS total_funds,
row_number() OVER (ORDER BY derived_1_1.`SUM(funds_requested)` )  AS rn
FROM (SELECT COUNT(t1.country) AS countrycount,
SUM(t1.funds_requested) AS smcnt,
AVG(t1.funds_requested) AS tmp_aggr_1,
STD(t1.funds_requested) AS tmp_aggr_2,
t1.country AS d_1
FROM t1
HAVING (AVG(t1.funds_requested) > 0)) derived_1_0
LEFT JOIN
(SELECT SUM(t1.funds_requested) AS `SUM(funds_requested)`
      FROM t1) derived_1_1
ON(TRUE);
countrycount	smcnt	country	total_funds	rn
1	1200	USA	1200	1
SET sql_mode=default;
DROP TABLE t1;
# Cause for introducing TABLE_LIST::m_was_grouped2derived:
# EXECUTE would assert if we didn't have it.
CREATE TABLE cc (i INT);
INSERT INTO cc VALUES (1);
ANALYZE TABLE cc;
Table	Op	Msg_type	Msg_text
test.cc	analyze	status	OK
SELECT (SELECT COUNT(i) FROM cc AS cc_alias
WHERE (cc.i IN (SELECT cc_alias.i FROM cc))) AS cnt
FROM cc
GROUP BY i;
cnt
1
EXPLAIN SELECT (SELECT COUNT(i) FROM cc AS cc_alias
WHERE (cc.i IN (SELECT cc_alias.i FROM cc))) AS cnt
FROM cc
GROUP BY i;
EXPLAIN
-> Group (no aggregates)  (rows=1)
    -> Sort: cc.i  (rows=1)
        -> Nested loop left join  (rows=1)
            -> Table scan on cc  (rows=1)
            -> Index lookup on derived_1_2 using <auto_key0> (i = cc.i)  (rows=0.1)
                -> Materialize  (rows=1)
                    -> Group aggregate: count(cc_alias.i)  (rows=1)
                        -> Sort: cc_alias.i  (rows=1)
                            -> Nested loop inner join (FirstMatch)  (rows=1)
                                -> Limit: 1 row(s)  (rows=1)
                                    -> Table scan on cc  (rows=1)
                                -> Table scan on cc_alias  (rows=1)

Warnings:
Note	1276	Field or reference 'test.cc_alias.i' of SELECT #3 was resolved in SELECT #2
Note	1276	Field or reference 'test.cc.i' of SELECT #2 was resolved in SELECT #1
DROP TABLE cc;
# Test: detect correlated aggregates deep inside scalar subquery (was
# missed before we let has_correlated_aggregate walk subqueries
CREATE TABLE t (a INT);
INSERT INTO t VALUES (1);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
# should be left untouched, since COUNT(q.i) aggregates in the
# outermost SELECT
EXPLAIN SELECT (
SELECT GROUP_CONCAT((SELECT COUNT(q.i) FROM t))
FROM t) AS i
FROM (SELECT a AS i FROM t) q;
EXPLAIN
-> Aggregate: count(t.a)  (rows=1)
    -> Table scan on t  (rows=1)
-> Select #2 (subquery in projection; dependent)
    -> Aggregate: group_concat((select #3) separator ',')  (rows=1)
        -> Table scan on t  (rows=1)
    -> Select #3 (subquery in projection; dependent)
        -> Table scan on t  (rows=1)

Warnings:
Note	1276	Field or reference 'q.i' of SELECT #3 was resolved in SELECT #1
DROP TABLE t;

# IN to EXISTS transformation makes us skip subquery_to_derived.

CREATE TABLE t1 (a INT NOT NULL, b INT NOT NULL);
CREATE TABLE t2 (c INT NOT NULL, d INT NOT NULL);
CREATE TABLE t3 (e INT NOT NULL);
INSERT INTO t1 VALUES (1,10), (2,10), (1,20), (2,20), (3,20), (2,30), (4,40);
INSERT INTO t2 VALUES (2,10), (2,20), (4,10), (5,10), (3,20), (2,40);
INSERT INTO t3 VALUES (10), (30), (10), (20);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
SELECT * FROM t1 AS ta
WHERE ta.a IN (SELECT c FROM t2 AS tb
WHERE (SELECT MIN(e) FROM t3 as tc
WHERE tc.e IS NOT NULL) < SOME(SELECT e FROM t3 as tc
WHERE ta.b=tc.e));
a	b
2	20
2	30
3	20
EXPLAIN SELECT * FROM t1 AS ta
WHERE ta.a IN (SELECT c FROM t2 AS tb
WHERE (SELECT MIN(e) FROM t3 as tc
WHERE tc.e IS NOT NULL) < SOME(SELECT e FROM t3 as tc
WHERE ta.b=tc.e));
EXPLAIN
-> Hash semijoin (FirstMatch) (ta.a = tb.c), (ta.b = derived_2_4.Name_exp_3)  (rows=0.14)
    -> Table scan on ta  (rows=7)
    -> Hash
        -> Inner hash join (no condition)  (rows=4)
            -> Table scan on tb  (rows=6)
            -> Hash
                -> Inner hash join (no condition), extra conditions: (derived_2_3.`MIN(e)` < derived_2_4.Name_exp_1)  (rows=0.667)
                    -> Table scan on derived_2_4  (rows=2)
                        -> Materialize  (rows=2)
                            -> Filter: (coalesce(count(0),0) <> 0)  (rows=2)
                                -> Group aggregate: max(tc.e), count(0)  (rows=2)
                                    -> Sort: tc.e  (rows=4)
                                        -> Table scan on tc  (rows=4)
                    -> Hash
                        -> Table scan on derived_2_3  (rows=1)
                            -> Materialize  (rows=1)
                                -> Aggregate: min(tc.e)  (rows=1)
                                    -> Table scan on tc  (rows=4)

Warnings:
Note	1276	Field or reference 'test.ta.b' of SELECT #4 was resolved in SELECT #1
#
# Two parallel scalar subqueries + grouping to derived table
#
SELECT SUM(t1.a) + (SELECT SUM(t2.c)
FROM t2),
(SELECT COUNT(t3.e) FROM t3)
FROM t1;
SUM(t1.a) + (SELECT SUM(t2.c)
FROM t2)	(SELECT COUNT(t3.e) FROM t3)
33	4
EXPLAIN SELECT SUM(t1.a) + (SELECT SUM(t2.c)
FROM t2),
(SELECT COUNT(t3.e) FROM t3)
FROM t1;
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on derived_1_4  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: sum(t1.a)  (rows=1)
                    -> Table scan on t1  (rows=7)
        -> Hash
            -> Table scan on derived_1_6  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: sum(t2.c)  (rows=1)
                        -> Table scan on t2  (rows=6)
    -> Hash
        -> Table scan on derived_1_5  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: count(t3.e)  (rows=1)
                    -> Table scan on t3  (rows=4)

DROP TABLE t1, t2, t3;
# Bug discovered from running rapid.cp_i_subquery.test What is
# different here is that we have a combination of an implicitly
# grouped query + a scalar subquery in the select list + the top block
# has an IN subquery in the WHERE clause. The WHERE clause gets
# moved into the derived table for the grouping, but this move didn't
# account for the semijoin already determined for the top level, which
also has to go into the new derived table.
CREATE TABLE t1(
pedcompralote INT NOT NULL,
pedcompraseq SMALLINT
);
INSERT INTO t1 VALUES (12,12);
CREATE TABLE t2(
cod INT NOT NULL,
ped INT,
PRIMARY KEY (cod),
KEY ped (ped)
);
INSERT INTO t2 VALUES
(11724,1779), (11725,1779), (11726,1779), (11727,1779),
(11728,1779), (11729,1779), (11730,1779), (11731,1779);
SELECT (SELECT COUNT(*)
FROM t1
WHERE pedcompralote=11725) AS tot,
IFNULL(MAX(pedcompraseq),0)+1 AS newcode
FROM t1
WHERE pedcompralote IN (SELECT cod FROM t2 WHERE ped=1779);
tot	newcode
0	1
DROP TABLE t1, t2;
# outer query has DEFAULT: verify it is handled correctly
CREATE TABLE t(i INT DEFAULT 5);
INSERT INTO t VALUES (4);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
SELECT DEFAULT(i) AS def,
5 + DEFAULT(i) AS def2,
i AS any_v,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
ERROR 42000: In aggregated query without GROUP BY, expression #3 of SELECT list contains nonaggregated column 'test.t.i'; this is incompatible with sql_mode=only_full_group_by
SET SQL_MODE='';
SELECT DEFAULT(i) AS def,
5 + DEFAULT(i) AS def2,
i AS any_v,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
def	def2	any_v	subquery	summ
5	10	4	4	4
EXPLAIN SELECT DEFAULT(i) AS def,
5 + DEFAULT(i) AS def2,
i AS any_v,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_3  (rows=1)
        -> Materialize  (rows=1)
            -> Aggregate: sum(t.i)  (rows=1)
                -> Table scan on t  (rows=1)
    -> Hash
        -> Table scan on derived_1_4  (rows=1)
            -> Materialize  (rows=1)
                -> Table scan on t  (rows=1)

SET SQL_MODE=default;
# Bug#35150438 Unwarranted error message for ANY_VALUE with
#              scalar subquery transform
SELECT ANY_VALUE(i) AS i1,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
i1	subquery	summ
4	4	4
EXPLAIN SELECT ANY_VALUE(i) AS i1,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_3  (rows=1)
        -> Materialize  (rows=1)
            -> Aggregate: sum(t.i)  (rows=1)
                -> Table scan on t  (rows=1)
    -> Hash
        -> Table scan on derived_1_4  (rows=1)
            -> Materialize  (rows=1)
                -> Table scan on t  (rows=1)

SELECT i + ANY_VALUE(i) AS i1,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
ERROR 42000: In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'test.t.i'; this is incompatible with sql_mode=only_full_group_by
SELECT ANY_VALUE(i) + i AS i1,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
ERROR 42000: In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'test.t.i'; this is incompatible with sql_mode=only_full_group_by
SELECT ANY_VALUE(ANY_VALUE(i) + i) AS i1,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
i1	subquery	summ
8	4	4
EXPLAIN SELECT ANY_VALUE(ANY_VALUE(i) + i) AS i1,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_3  (rows=1)
        -> Materialize  (rows=1)
            -> Aggregate: sum(t.i)  (rows=1)
                -> Table scan on t  (rows=1)
    -> Hash
        -> Table scan on derived_1_4  (rows=1)
            -> Materialize  (rows=1)
                -> Table scan on t  (rows=1)

SELECT ANY_VALUE(i) AS i1, i as i2,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
ERROR 42000: In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'test.t.i'; this is incompatible with sql_mode=only_full_group_by
SELECT i as i2, ANY_VALUE(i) AS i1,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
ERROR 42000: In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'test.t.i'; this is incompatible with sql_mode=only_full_group_by
SELECT ANY_VALUE(i) as i2, ANY_VALUE(i) AS i1,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
i2	i1	subquery	summ
4	4	4	4
EXPLAIN SELECT ANY_VALUE(i) as i2, ANY_VALUE(i) AS i1,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_3  (rows=1)
        -> Materialize  (rows=1)
            -> Aggregate: sum(t.i)  (rows=1)
                -> Table scan on t  (rows=1)
    -> Hash
        -> Table scan on derived_1_4  (rows=1)
            -> Materialize  (rows=1)
                -> Table scan on t  (rows=1)

DROP TABLE t;
#
# 6.  S U B Q U E R Y   I N   H A V I N G   C L A U S E
#
CREATE TABLE t1(i int, j int);
CREATE TABLE t2(i int);
INSERT INTO t1 VALUES (1, 10);
INSERT INTO t1 VALUES (1, 20);
INSERT INTO t1 VALUES (1, 30);
INSERT INTO t1 VALUES (2, 11);
INSERT INTO t1 VALUES (2, 20);
INSERT INTO t1 VALUES (2, 30);
INSERT INTO t2 VALUES (25);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
Implicit grouping, HAVING: no transform
SELECT SUM(j) FROM t1
HAVING SUM(j) > (SELECT SUM(t2.i) FROM t2);
SUM(j)
121
EXPLAIN SELECT SUM(j) FROM t1
HAVING SUM(j) > (SELECT SUM(t2.i) FROM t2);
EXPLAIN
-> Filter: (sum(t1.j) > (select #2))  (rows=1)
    -> Aggregate: sum(t1.j), sum(t1.j)  (rows=1)
        -> Table scan on t1  (rows=6)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: sum(t2.i)  (rows=1)
            -> Table scan on t2  (rows=1)

Only HAVING is ok to transform, no implicit grouping
SELECT j FROM t1
HAVING j > (SELECT MIN(t2.i) FROM t2);
j
30
30
EXPLAIN SELECT j FROM t1
HAVING j > (SELECT MIN(t2.i) FROM t2);
EXPLAIN
-> Filter: (t1.j > derived_1_2.`MIN(t2.i)`)  (rows=2)
    -> Left hash join (no condition)  (rows=6)
        -> Table scan on t1  (rows=6)
        -> Hash
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: min(t2.i)  (rows=1)
                        -> Table scan on t2  (rows=1)

HAVING and GROUP BY, also ok
SELECT i, j FROM t1
GROUP BY i, j
HAVING SUM(j) > (SELECT SUM(t2.i) FROM t2);
i	j
1	30
2	30
EXPLAIN SELECT i, j FROM t1
GROUP BY i, j
HAVING SUM(j) > (SELECT SUM(t2.i) FROM t2);
EXPLAIN
-> Filter: (sum(t1.j) > derived_1_2.`SUM(t2.i)`)  (rows=2.45)
    -> Group aggregate: sum(t1.j)  (rows=2.45)
        -> Sort: t1.i, t1.j  (rows=6)
            -> Left hash join (no condition)  (rows=6)
                -> Table scan on t1  (rows=6)
                -> Hash
                    -> Table scan on derived_1_2  (rows=1)
                        -> Materialize  (rows=1)
                            -> Aggregate: sum(t2.i)  (rows=1)
                                -> Table scan on t2  (rows=1)

HAVING, GROUP BY and ROLLUP
SELECT i, j FROM t1
GROUP BY i, j WITH ROLLUP
HAVING SUM(j) > (SELECT SUM(t2.i) FROM t2);
i	j
1	30
1	NULL
2	30
2	NULL
NULL	NULL
EXPLAIN SELECT i, j FROM t1
GROUP BY i, j WITH ROLLUP
HAVING SUM(j) > (SELECT SUM(t2.i) FROM t2);
EXPLAIN
-> Filter: (rollup_sum_switcher(sum(t1.j)) > derived_1_2.`SUM(t2.i)`)  (rows=5.01)
    -> Group aggregate with rollup: sum(t1.j)  (rows=5.01)
        -> Sort: t1.i, t1.j  (rows=6)
            -> Left hash join (no condition)  (rows=6)
                -> Table scan on t1  (rows=6)
                -> Hash
                    -> Table scan on derived_1_2  (rows=1)
                        -> Materialize  (rows=1)
                            -> Aggregate: sum(t2.i)  (rows=1)
                                -> Table scan on t2  (rows=1)

DROP TABLE t1, t2;
Bigger example (TPC-H Q11) where we get a scalar transformation
in the grouping derived table also, i.e. nested scalar to
derived transformation.
CREATE TABLE supplier (
s_suppkey INT NOT NULL,
s_nationkey BIGINT NOT NULL,
PRIMARY KEY (s_suppkey)
);
CREATE TABLE nation (
n_nationkey INT NOT NULL,
n_name CHAR(25) DEFAULT NULL,
PRIMARY KEY (n_nationkey)
);
CREATE TABLE partsupp (
ps_partkey BIGINT NOT NULL,
ps_suppkey BIGINT NOT NULL,
ps_availqty INT DEFAULT NULL,
ps_supplycost DECIMAL(10,0) DEFAULT NULL,
PRIMARY KEY (ps_partkey, ps_suppkey)
);
INSERT INTO nation VALUES (1, 'germany'),
(2, 'norway'),
(3, 'u.k.');
INSERT INTO supplier VALUES (1, 1);
INSERT INTO partsupp VALUES
(1, 1, 10, 555),
(2, 1, 1, 2222),
(3, 1, 300, 700),
(4, 1, 259, 400),
(5, 1, 20,  400),
(6, 1, 1000, 300),
(7, 1, 30, 700);
ANALYZE TABLE supplier, nation, partsupp;
Table	Op	Msg_type	Msg_text
test.supplier	analyze	status	OK
test.nation	analyze	status	OK
test.partsupp	analyze	status	OK
SELECT
ps_partkey,
SUM(ps_supplycost * ps_availqty) AS value
FROM
partsupp,
supplier,
nation
WHERE
ps_suppkey = s_suppkey AND
s_nationkey = n_nationkey AND
n_name = 'germany'
GROUP BY
ps_partkey HAVING
SUM(ps_supplycost * ps_availqty) > (
SELECT
SUM(ps_supplycost * ps_availqty) * 0.1
FROM
partsupp,
supplier,
nation
WHERE
ps_suppkey = s_suppkey AND
s_nationkey = n_nationkey AND
n_name = 'germany'
        )
ORDER BY value DESC;
ps_partkey	value
6	300000
3	210000
4	103600
EXPLAIN SELECT
ps_partkey,
SUM(ps_supplycost * ps_availqty) AS value
FROM
partsupp,
supplier,
nation
WHERE
ps_suppkey = s_suppkey AND
s_nationkey = n_nationkey AND
n_name = 'germany'
GROUP BY
ps_partkey HAVING
SUM(ps_supplycost * ps_availqty) > (
SELECT
SUM(ps_supplycost * ps_availqty) * 0.1
FROM
partsupp,
supplier,
nation
WHERE
ps_suppkey = s_suppkey AND
s_nationkey = n_nationkey AND
n_name = 'germany'
        )
ORDER BY value DESC;
EXPLAIN
-> Sort: `value` DESC  (rows=0.7)
    -> Stream results  (rows=0.7)
        -> Filter: (sum((partsupp.ps_supplycost * partsupp.ps_availqty)) > derived_1_2.`SUM(ps_supplycost * ps_availqty) * 0.1`)  (rows=0.7)
            -> Group aggregate: sum((partsupp.ps_supplycost * partsupp.ps_availqty)), sum((partsupp.ps_supplycost * partsupp.ps_availqty))  (rows=0.7)
                -> Sort: partsupp.ps_partkey  (rows=0.7)
                    -> Left hash join (no condition)  (rows=0.7)
                        -> Inner hash join (partsupp.ps_suppkey = supplier.s_suppkey)  (rows=0.7)
                            -> Table scan on partsupp  (rows=7)
                            -> Hash
                                -> Nested loop inner join  (rows=0.1)
                                    -> Table scan on supplier  (rows=1)
                                    -> Filter: ((nation.n_name = 'germany') and (supplier.s_nationkey = nation.n_nationkey))  (rows=0.0333)
                                        -> Single-row index lookup on nation using PRIMARY (n_nationkey = supplier.s_nationkey)  (rows=1)
                        -> Hash
                            -> Table scan on derived_1_2  (rows=1)
                                -> Materialize  (rows=1)
                                    -> Aggregate: sum((partsupp.ps_supplycost * partsupp.ps_availqty))  (rows=1)
                                        -> Inner hash join (partsupp.ps_suppkey = supplier.s_suppkey)  (rows=0.7)
                                            -> Table scan on partsupp  (rows=7)
                                            -> Hash
                                                -> Nested loop inner join  (rows=0.1)
                                                    -> Table scan on supplier  (rows=1)
                                                    -> Filter: ((nation.n_name = 'germany') and (supplier.s_nationkey = nation.n_nationkey))  (rows=0.0333)
                                                        -> Single-row index lookup on nation using PRIMARY (n_nationkey = supplier.s_nationkey)  (rows=1)

DROP TABLE partsupp, nation, supplier;
Bugs discovered during full regression suite runs with
transformation enabled, selectively repeated here, since we
won't be running InnoDB with transformation enabled normally.
Bug 1
CREATE TABLE tbl1 (
login INT NOT NULL,
numb DECIMAL(15,2) NOT NULL DEFAULT '0.00',
PRIMARY KEY (login),
KEY numb (numb)
);
CREATE TABLE tbl2 (
login INT NOT NULL,
cmd TINYINT NOT NULL,
nump DECIMAL(15,2) NOT NULL DEFAULT '0.00',
KEY cmd (cmd),
KEY login (login)
);
SET autocommit = 0;
START TRANSACTION;
insert into tbl1 values(500, '500.');
insert into tbl1 values(499, '499.');
insert into tbl1 values(498, '498.');
insert into tbl1 values(497, '497.');
insert into tbl1 values(496, '496.');
insert into tbl1 values(495, '495.');
insert into tbl1 values(494, '494.');
insert into tbl1 values(493, '493.');
insert into tbl1 values(492, '492.');
insert into tbl1 values(491, '491.');
insert into tbl1 values(490, '490.');
insert into tbl1 values(489, '489.');
insert into tbl1 values(488, '488.');
insert into tbl1 values(487, '487.');
insert into tbl1 values(486, '486.');
insert into tbl1 values(485, '485.');
insert into tbl1 values(484, '484.');
insert into tbl1 values(483, '483.');
insert into tbl1 values(482, '482.');
insert into tbl1 values(481, '481.');
insert into tbl1 values(480, '480.');
insert into tbl1 values(479, '479.');
insert into tbl1 values(478, '478.');
insert into tbl1 values(477, '477.');
insert into tbl1 values(476, '476.');
insert into tbl1 values(475, '475.');
insert into tbl1 values(474, '474.');
insert into tbl1 values(473, '473.');
insert into tbl1 values(472, '472.');
insert into tbl1 values(471, '471.');
insert into tbl1 values(470, '470.');
insert into tbl1 values(469, '469.');
insert into tbl1 values(468, '468.');
insert into tbl1 values(467, '467.');
insert into tbl1 values(466, '466.');
insert into tbl1 values(465, '465.');
insert into tbl1 values(464, '464.');
insert into tbl1 values(463, '463.');
insert into tbl1 values(462, '462.');
insert into tbl1 values(461, '461.');
insert into tbl1 values(460, '460.');
insert into tbl1 values(459, '459.');
insert into tbl1 values(458, '458.');
insert into tbl1 values(457, '457.');
insert into tbl1 values(456, '456.');
insert into tbl1 values(455, '455.');
insert into tbl1 values(454, '454.');
insert into tbl1 values(453, '453.');
insert into tbl1 values(452, '452.');
insert into tbl1 values(451, '451.');
insert into tbl1 values(450, '450.');
insert into tbl1 values(449, '449.');
insert into tbl1 values(448, '448.');
insert into tbl1 values(447, '447.');
insert into tbl1 values(446, '446.');
insert into tbl1 values(445, '445.');
insert into tbl1 values(444, '444.');
insert into tbl1 values(443, '443.');
insert into tbl1 values(442, '442.');
insert into tbl1 values(441, '441.');
insert into tbl1 values(440, '440.');
insert into tbl1 values(439, '439.');
insert into tbl1 values(438, '438.');
insert into tbl1 values(437, '437.');
insert into tbl1 values(436, '436.');
insert into tbl1 values(435, '435.');
insert into tbl1 values(434, '434.');
insert into tbl1 values(433, '433.');
insert into tbl1 values(432, '432.');
insert into tbl1 values(431, '431.');
insert into tbl1 values(430, '430.');
insert into tbl1 values(429, '429.');
insert into tbl1 values(428, '428.');
insert into tbl1 values(427, '427.');
insert into tbl1 values(426, '426.');
insert into tbl1 values(425, '425.');
insert into tbl1 values(424, '424.');
insert into tbl1 values(423, '423.');
insert into tbl1 values(422, '422.');
insert into tbl1 values(421, '421.');
insert into tbl1 values(420, '420.');
insert into tbl1 values(419, '419.');
insert into tbl1 values(418, '418.');
insert into tbl1 values(417, '417.');
insert into tbl1 values(416, '416.');
insert into tbl1 values(415, '415.');
insert into tbl1 values(414, '414.');
insert into tbl1 values(413, '413.');
insert into tbl1 values(412, '412.');
insert into tbl1 values(411, '411.');
insert into tbl1 values(410, '410.');
insert into tbl1 values(409, '409.');
insert into tbl1 values(408, '408.');
insert into tbl1 values(407, '407.');
insert into tbl1 values(406, '406.');
insert into tbl1 values(405, '405.');
insert into tbl1 values(404, '404.');
insert into tbl1 values(403, '403.');
insert into tbl1 values(402, '402.');
insert into tbl1 values(401, '401.');
insert into tbl1 values(400, '400.');
insert into tbl1 values(399, '399.');
insert into tbl1 values(398, '398.');
insert into tbl1 values(397, '397.');
insert into tbl1 values(396, '396.');
insert into tbl1 values(395, '395.');
insert into tbl1 values(394, '394.');
insert into tbl1 values(393, '393.');
insert into tbl1 values(392, '392.');
insert into tbl1 values(391, '391.');
insert into tbl1 values(390, '390.');
insert into tbl1 values(389, '389.');
insert into tbl1 values(388, '388.');
insert into tbl1 values(387, '387.');
insert into tbl1 values(386, '386.');
insert into tbl1 values(385, '385.');
insert into tbl1 values(384, '384.');
insert into tbl1 values(383, '383.');
insert into tbl1 values(382, '382.');
insert into tbl1 values(381, '381.');
insert into tbl1 values(380, '380.');
insert into tbl1 values(379, '379.');
insert into tbl1 values(378, '378.');
insert into tbl1 values(377, '377.');
insert into tbl1 values(376, '376.');
insert into tbl1 values(375, '375.');
insert into tbl1 values(374, '374.');
insert into tbl1 values(373, '373.');
insert into tbl1 values(372, '372.');
insert into tbl1 values(371, '371.');
insert into tbl1 values(370, '370.');
insert into tbl1 values(369, '369.');
insert into tbl1 values(368, '368.');
insert into tbl1 values(367, '367.');
insert into tbl1 values(366, '366.');
insert into tbl1 values(365, '365.');
insert into tbl1 values(364, '364.');
insert into tbl1 values(363, '363.');
insert into tbl1 values(362, '362.');
insert into tbl1 values(361, '361.');
insert into tbl1 values(360, '360.');
insert into tbl1 values(359, '359.');
insert into tbl1 values(358, '358.');
insert into tbl1 values(357, '357.');
insert into tbl1 values(356, '356.');
insert into tbl1 values(355, '355.');
insert into tbl1 values(354, '354.');
insert into tbl1 values(353, '353.');
insert into tbl1 values(352, '352.');
insert into tbl1 values(351, '351.');
insert into tbl1 values(350, '350.');
insert into tbl1 values(349, '349.');
insert into tbl1 values(348, '348.');
insert into tbl1 values(347, '347.');
insert into tbl1 values(346, '346.');
insert into tbl1 values(345, '345.');
insert into tbl1 values(344, '344.');
insert into tbl1 values(343, '343.');
insert into tbl1 values(342, '342.');
insert into tbl1 values(341, '341.');
insert into tbl1 values(340, '340.');
insert into tbl1 values(339, '339.');
insert into tbl1 values(338, '338.');
insert into tbl1 values(337, '337.');
insert into tbl1 values(336, '336.');
insert into tbl1 values(335, '335.');
insert into tbl1 values(334, '334.');
insert into tbl1 values(333, '333.');
insert into tbl1 values(332, '332.');
insert into tbl1 values(331, '331.');
insert into tbl1 values(330, '330.');
insert into tbl1 values(329, '329.');
insert into tbl1 values(328, '328.');
insert into tbl1 values(327, '327.');
insert into tbl1 values(326, '326.');
insert into tbl1 values(325, '325.');
insert into tbl1 values(324, '324.');
insert into tbl1 values(323, '323.');
insert into tbl1 values(322, '322.');
insert into tbl1 values(321, '321.');
insert into tbl1 values(320, '320.');
insert into tbl1 values(319, '319.');
insert into tbl1 values(318, '318.');
insert into tbl1 values(317, '317.');
insert into tbl1 values(316, '316.');
insert into tbl1 values(315, '315.');
insert into tbl1 values(314, '314.');
insert into tbl1 values(313, '313.');
insert into tbl1 values(312, '312.');
insert into tbl1 values(311, '311.');
insert into tbl1 values(310, '310.');
insert into tbl1 values(309, '309.');
insert into tbl1 values(308, '308.');
insert into tbl1 values(307, '307.');
insert into tbl1 values(306, '306.');
insert into tbl1 values(305, '305.');
insert into tbl1 values(304, '304.');
insert into tbl1 values(303, '303.');
insert into tbl1 values(302, '302.');
insert into tbl1 values(301, '301.');
insert into tbl1 values(300, '300.');
insert into tbl1 values(299, '299.');
insert into tbl1 values(298, '298.');
insert into tbl1 values(297, '297.');
insert into tbl1 values(296, '296.');
insert into tbl1 values(295, '295.');
insert into tbl1 values(294, '294.');
insert into tbl1 values(293, '293.');
insert into tbl1 values(292, '292.');
insert into tbl1 values(291, '291.');
insert into tbl1 values(290, '290.');
insert into tbl1 values(289, '289.');
insert into tbl1 values(288, '288.');
insert into tbl1 values(287, '287.');
insert into tbl1 values(286, '286.');
insert into tbl1 values(285, '285.');
insert into tbl1 values(284, '284.');
insert into tbl1 values(283, '283.');
insert into tbl1 values(282, '282.');
insert into tbl1 values(281, '281.');
insert into tbl1 values(280, '280.');
insert into tbl1 values(279, '279.');
insert into tbl1 values(278, '278.');
insert into tbl1 values(277, '277.');
insert into tbl1 values(276, '276.');
insert into tbl1 values(275, '275.');
insert into tbl1 values(274, '274.');
insert into tbl1 values(273, '273.');
insert into tbl1 values(272, '272.');
insert into tbl1 values(271, '271.');
insert into tbl1 values(270, '270.');
insert into tbl1 values(269, '269.');
insert into tbl1 values(268, '268.');
insert into tbl1 values(267, '267.');
insert into tbl1 values(266, '266.');
insert into tbl1 values(265, '265.');
insert into tbl1 values(264, '264.');
insert into tbl1 values(263, '263.');
insert into tbl1 values(262, '262.');
insert into tbl1 values(261, '261.');
insert into tbl1 values(260, '260.');
insert into tbl1 values(259, '259.');
insert into tbl1 values(258, '258.');
insert into tbl1 values(257, '257.');
insert into tbl1 values(256, '256.');
insert into tbl1 values(255, '255.');
insert into tbl1 values(254, '254.');
insert into tbl1 values(253, '253.');
insert into tbl1 values(252, '252.');
insert into tbl1 values(251, '251.');
insert into tbl1 values(250, '250.');
insert into tbl1 values(249, '249.');
insert into tbl1 values(248, '248.');
insert into tbl1 values(247, '247.');
insert into tbl1 values(246, '246.');
insert into tbl1 values(245, '245.');
insert into tbl1 values(244, '244.');
insert into tbl1 values(243, '243.');
insert into tbl1 values(242, '242.');
insert into tbl1 values(241, '241.');
insert into tbl1 values(240, '240.');
insert into tbl1 values(239, '239.');
insert into tbl1 values(238, '238.');
insert into tbl1 values(237, '237.');
insert into tbl1 values(236, '236.');
insert into tbl1 values(235, '235.');
insert into tbl1 values(234, '234.');
insert into tbl1 values(233, '233.');
insert into tbl1 values(232, '232.');
insert into tbl1 values(231, '231.');
insert into tbl1 values(230, '230.');
insert into tbl1 values(229, '229.');
insert into tbl1 values(228, '228.');
insert into tbl1 values(227, '227.');
insert into tbl1 values(226, '226.');
insert into tbl1 values(225, '225.');
insert into tbl1 values(224, '224.');
insert into tbl1 values(223, '223.');
insert into tbl1 values(222, '222.');
insert into tbl1 values(221, '221.');
insert into tbl1 values(220, '220.');
insert into tbl1 values(219, '219.');
insert into tbl1 values(218, '218.');
insert into tbl1 values(217, '217.');
insert into tbl1 values(216, '216.');
insert into tbl1 values(215, '215.');
insert into tbl1 values(214, '214.');
insert into tbl1 values(213, '213.');
insert into tbl1 values(212, '212.');
insert into tbl1 values(211, '211.');
insert into tbl1 values(210, '210.');
insert into tbl1 values(209, '209.');
insert into tbl1 values(208, '208.');
insert into tbl1 values(207, '207.');
insert into tbl1 values(206, '206.');
insert into tbl1 values(205, '205.');
insert into tbl1 values(204, '204.');
insert into tbl1 values(203, '203.');
insert into tbl1 values(202, '202.');
insert into tbl1 values(201, '201.');
insert into tbl1 values(200, '200.');
insert into tbl1 values(199, '199.');
insert into tbl1 values(198, '198.');
insert into tbl1 values(197, '197.');
insert into tbl1 values(196, '196.');
insert into tbl1 values(195, '195.');
insert into tbl1 values(194, '194.');
insert into tbl1 values(193, '193.');
insert into tbl1 values(192, '192.');
insert into tbl1 values(191, '191.');
insert into tbl1 values(190, '190.');
insert into tbl1 values(189, '189.');
insert into tbl1 values(188, '188.');
insert into tbl1 values(187, '187.');
insert into tbl1 values(186, '186.');
insert into tbl1 values(185, '185.');
insert into tbl1 values(184, '184.');
insert into tbl1 values(183, '183.');
insert into tbl1 values(182, '182.');
insert into tbl1 values(181, '181.');
insert into tbl1 values(180, '180.');
insert into tbl1 values(179, '179.');
insert into tbl1 values(178, '178.');
insert into tbl1 values(177, '177.');
insert into tbl1 values(176, '176.');
insert into tbl1 values(175, '175.');
insert into tbl1 values(174, '174.');
insert into tbl1 values(173, '173.');
insert into tbl1 values(172, '172.');
insert into tbl1 values(171, '171.');
insert into tbl1 values(170, '170.');
insert into tbl1 values(169, '169.');
insert into tbl1 values(168, '168.');
insert into tbl1 values(167, '167.');
insert into tbl1 values(166, '166.');
insert into tbl1 values(165, '165.');
insert into tbl1 values(164, '164.');
insert into tbl1 values(163, '163.');
insert into tbl1 values(162, '162.');
insert into tbl1 values(161, '161.');
insert into tbl1 values(160, '160.');
insert into tbl1 values(159, '159.');
insert into tbl1 values(158, '158.');
insert into tbl1 values(157, '157.');
insert into tbl1 values(156, '156.');
insert into tbl1 values(155, '155.');
insert into tbl1 values(154, '154.');
insert into tbl1 values(153, '153.');
insert into tbl1 values(152, '152.');
insert into tbl1 values(151, '151.');
insert into tbl1 values(150, '150.');
insert into tbl1 values(149, '149.');
insert into tbl1 values(148, '148.');
insert into tbl1 values(147, '147.');
insert into tbl1 values(146, '146.');
insert into tbl1 values(145, '145.');
insert into tbl1 values(144, '144.');
insert into tbl1 values(143, '143.');
insert into tbl1 values(142, '142.');
insert into tbl1 values(141, '141.');
insert into tbl1 values(140, '140.');
insert into tbl1 values(139, '139.');
insert into tbl1 values(138, '138.');
insert into tbl1 values(137, '137.');
insert into tbl1 values(136, '136.');
insert into tbl1 values(135, '135.');
insert into tbl1 values(134, '134.');
insert into tbl1 values(133, '133.');
insert into tbl1 values(132, '132.');
insert into tbl1 values(131, '131.');
insert into tbl1 values(130, '130.');
insert into tbl1 values(129, '129.');
insert into tbl1 values(128, '128.');
insert into tbl1 values(127, '127.');
insert into tbl1 values(126, '126.');
insert into tbl1 values(125, '125.');
insert into tbl1 values(124, '124.');
insert into tbl1 values(123, '123.');
insert into tbl1 values(122, '122.');
insert into tbl1 values(121, '121.');
insert into tbl1 values(120, '120.');
insert into tbl1 values(119, '119.');
insert into tbl1 values(118, '118.');
insert into tbl1 values(117, '117.');
insert into tbl1 values(116, '116.');
insert into tbl1 values(115, '115.');
insert into tbl1 values(114, '114.');
insert into tbl1 values(113, '113.');
insert into tbl1 values(112, '112.');
insert into tbl1 values(111, '111.');
insert into tbl1 values(110, '110.');
insert into tbl1 values(109, '109.');
insert into tbl1 values(108, '108.');
insert into tbl1 values(107, '107.');
insert into tbl1 values(106, '106.');
insert into tbl1 values(105, '105.');
insert into tbl1 values(104, '104.');
insert into tbl1 values(103, '103.');
insert into tbl1 values(102, '102.');
insert into tbl1 values(101, '101.');
insert into tbl1 values(100, '100.');
insert into tbl1 values(99, '99.');
insert into tbl1 values(98, '98.');
insert into tbl1 values(97, '97.');
insert into tbl1 values(96, '96.');
insert into tbl1 values(95, '95.');
insert into tbl1 values(94, '94.');
insert into tbl1 values(93, '93.');
insert into tbl1 values(92, '92.');
insert into tbl1 values(91, '91.');
insert into tbl1 values(90, '90.');
insert into tbl1 values(89, '89.');
insert into tbl1 values(88, '88.');
insert into tbl1 values(87, '87.');
insert into tbl1 values(86, '86.');
insert into tbl1 values(85, '85.');
insert into tbl1 values(84, '84.');
insert into tbl1 values(83, '83.');
insert into tbl1 values(82, '82.');
insert into tbl1 values(81, '81.');
insert into tbl1 values(80, '80.');
insert into tbl1 values(79, '79.');
insert into tbl1 values(78, '78.');
insert into tbl1 values(77, '77.');
insert into tbl1 values(76, '76.');
insert into tbl1 values(75, '75.');
insert into tbl1 values(74, '74.');
insert into tbl1 values(73, '73.');
insert into tbl1 values(72, '72.');
insert into tbl1 values(71, '71.');
insert into tbl1 values(70, '70.');
insert into tbl1 values(69, '69.');
insert into tbl1 values(68, '68.');
insert into tbl1 values(67, '67.');
insert into tbl1 values(66, '66.');
insert into tbl1 values(65, '65.');
insert into tbl1 values(64, '64.');
insert into tbl1 values(63, '63.');
insert into tbl1 values(62, '62.');
insert into tbl1 values(61, '61.');
insert into tbl1 values(60, '60.');
insert into tbl1 values(59, '59.');
insert into tbl1 values(58, '58.');
insert into tbl1 values(57, '57.');
insert into tbl1 values(56, '56.');
insert into tbl1 values(55, '55.');
insert into tbl1 values(54, '54.');
insert into tbl1 values(53, '53.');
insert into tbl1 values(52, '52.');
insert into tbl1 values(51, '51.');
insert into tbl1 values(50, '50.');
insert into tbl1 values(49, '49.');
insert into tbl1 values(48, '48.');
insert into tbl1 values(47, '47.');
insert into tbl1 values(46, '46.');
insert into tbl1 values(45, '45.');
insert into tbl1 values(44, '44.');
insert into tbl1 values(43, '43.');
insert into tbl1 values(42, '42.');
insert into tbl1 values(41, '41.');
insert into tbl1 values(40, '40.');
insert into tbl1 values(39, '39.');
insert into tbl1 values(38, '38.');
insert into tbl1 values(37, '37.');
insert into tbl1 values(36, '36.');
insert into tbl1 values(35, '35.');
insert into tbl1 values(34, '34.');
insert into tbl1 values(33, '33.');
insert into tbl1 values(32, '32.');
insert into tbl1 values(31, '31.');
insert into tbl1 values(30, '30.');
insert into tbl1 values(29, '29.');
insert into tbl1 values(28, '28.');
insert into tbl1 values(27, '27.');
insert into tbl1 values(26, '26.');
insert into tbl1 values(25, '25.');
insert into tbl1 values(24, '24.');
insert into tbl1 values(23, '23.');
insert into tbl1 values(22, '22.');
insert into tbl1 values(21, '21.');
insert into tbl1 values(20, '20.');
insert into tbl1 values(19, '19.');
insert into tbl1 values(18, '18.');
insert into tbl1 values(17, '17.');
insert into tbl1 values(16, '16.');
insert into tbl1 values(15, '15.');
insert into tbl1 values(14, '14.');
insert into tbl1 values(13, '13.');
insert into tbl1 values(12, '12.');
insert into tbl1 values(11, '11.');
insert into tbl1 values(10, '10.');
insert into tbl1 values(9, '9.');
insert into tbl1 values(8, '8.');
insert into tbl1 values(7, '7.');
insert into tbl1 values(6, '6.');
insert into tbl1 values(5, '5.');
insert into tbl1 values(4, '4.');
insert into tbl1 values(3, '3.');
insert into tbl1 values(2, '2.');
insert into tbl1 values(1, '1.');
insert into tbl2 values(500, 500%127,'500.');
insert into tbl2 values(499, 499%127,'499.');
insert into tbl2 values(498, 498%127,'498.');
insert into tbl2 values(497, 497%127,'497.');
insert into tbl2 values(496, 496%127,'496.');
insert into tbl2 values(495, 495%127,'495.');
insert into tbl2 values(494, 494%127,'494.');
insert into tbl2 values(493, 493%127,'493.');
insert into tbl2 values(492, 492%127,'492.');
insert into tbl2 values(491, 491%127,'491.');
insert into tbl2 values(490, 490%127,'490.');
insert into tbl2 values(489, 489%127,'489.');
insert into tbl2 values(488, 488%127,'488.');
insert into tbl2 values(487, 487%127,'487.');
insert into tbl2 values(486, 486%127,'486.');
insert into tbl2 values(485, 485%127,'485.');
insert into tbl2 values(484, 484%127,'484.');
insert into tbl2 values(483, 483%127,'483.');
insert into tbl2 values(482, 482%127,'482.');
insert into tbl2 values(481, 481%127,'481.');
insert into tbl2 values(480, 480%127,'480.');
insert into tbl2 values(479, 479%127,'479.');
insert into tbl2 values(478, 478%127,'478.');
insert into tbl2 values(477, 477%127,'477.');
insert into tbl2 values(476, 476%127,'476.');
insert into tbl2 values(475, 475%127,'475.');
insert into tbl2 values(474, 474%127,'474.');
insert into tbl2 values(473, 473%127,'473.');
insert into tbl2 values(472, 472%127,'472.');
insert into tbl2 values(471, 471%127,'471.');
insert into tbl2 values(470, 470%127,'470.');
insert into tbl2 values(469, 469%127,'469.');
insert into tbl2 values(468, 468%127,'468.');
insert into tbl2 values(467, 467%127,'467.');
insert into tbl2 values(466, 466%127,'466.');
insert into tbl2 values(465, 465%127,'465.');
insert into tbl2 values(464, 464%127,'464.');
insert into tbl2 values(463, 463%127,'463.');
insert into tbl2 values(462, 462%127,'462.');
insert into tbl2 values(461, 461%127,'461.');
insert into tbl2 values(460, 460%127,'460.');
insert into tbl2 values(459, 459%127,'459.');
insert into tbl2 values(458, 458%127,'458.');
insert into tbl2 values(457, 457%127,'457.');
insert into tbl2 values(456, 456%127,'456.');
insert into tbl2 values(455, 455%127,'455.');
insert into tbl2 values(454, 454%127,'454.');
insert into tbl2 values(453, 453%127,'453.');
insert into tbl2 values(452, 452%127,'452.');
insert into tbl2 values(451, 451%127,'451.');
insert into tbl2 values(450, 450%127,'450.');
insert into tbl2 values(449, 449%127,'449.');
insert into tbl2 values(448, 448%127,'448.');
insert into tbl2 values(447, 447%127,'447.');
insert into tbl2 values(446, 446%127,'446.');
insert into tbl2 values(445, 445%127,'445.');
insert into tbl2 values(444, 444%127,'444.');
insert into tbl2 values(443, 443%127,'443.');
insert into tbl2 values(442, 442%127,'442.');
insert into tbl2 values(441, 441%127,'441.');
insert into tbl2 values(440, 440%127,'440.');
insert into tbl2 values(439, 439%127,'439.');
insert into tbl2 values(438, 438%127,'438.');
insert into tbl2 values(437, 437%127,'437.');
insert into tbl2 values(436, 436%127,'436.');
insert into tbl2 values(435, 435%127,'435.');
insert into tbl2 values(434, 434%127,'434.');
insert into tbl2 values(433, 433%127,'433.');
insert into tbl2 values(432, 432%127,'432.');
insert into tbl2 values(431, 431%127,'431.');
insert into tbl2 values(430, 430%127,'430.');
insert into tbl2 values(429, 429%127,'429.');
insert into tbl2 values(428, 428%127,'428.');
insert into tbl2 values(427, 427%127,'427.');
insert into tbl2 values(426, 426%127,'426.');
insert into tbl2 values(425, 425%127,'425.');
insert into tbl2 values(424, 424%127,'424.');
insert into tbl2 values(423, 423%127,'423.');
insert into tbl2 values(422, 422%127,'422.');
insert into tbl2 values(421, 421%127,'421.');
insert into tbl2 values(420, 420%127,'420.');
insert into tbl2 values(419, 419%127,'419.');
insert into tbl2 values(418, 418%127,'418.');
insert into tbl2 values(417, 417%127,'417.');
insert into tbl2 values(416, 416%127,'416.');
insert into tbl2 values(415, 415%127,'415.');
insert into tbl2 values(414, 414%127,'414.');
insert into tbl2 values(413, 413%127,'413.');
insert into tbl2 values(412, 412%127,'412.');
insert into tbl2 values(411, 411%127,'411.');
insert into tbl2 values(410, 410%127,'410.');
insert into tbl2 values(409, 409%127,'409.');
insert into tbl2 values(408, 408%127,'408.');
insert into tbl2 values(407, 407%127,'407.');
insert into tbl2 values(406, 406%127,'406.');
insert into tbl2 values(405, 405%127,'405.');
insert into tbl2 values(404, 404%127,'404.');
insert into tbl2 values(403, 403%127,'403.');
insert into tbl2 values(402, 402%127,'402.');
insert into tbl2 values(401, 401%127,'401.');
insert into tbl2 values(400, 400%127,'400.');
insert into tbl2 values(399, 399%127,'399.');
insert into tbl2 values(398, 398%127,'398.');
insert into tbl2 values(397, 397%127,'397.');
insert into tbl2 values(396, 396%127,'396.');
insert into tbl2 values(395, 395%127,'395.');
insert into tbl2 values(394, 394%127,'394.');
insert into tbl2 values(393, 393%127,'393.');
insert into tbl2 values(392, 392%127,'392.');
insert into tbl2 values(391, 391%127,'391.');
insert into tbl2 values(390, 390%127,'390.');
insert into tbl2 values(389, 389%127,'389.');
insert into tbl2 values(388, 388%127,'388.');
insert into tbl2 values(387, 387%127,'387.');
insert into tbl2 values(386, 386%127,'386.');
insert into tbl2 values(385, 385%127,'385.');
insert into tbl2 values(384, 384%127,'384.');
insert into tbl2 values(383, 383%127,'383.');
insert into tbl2 values(382, 382%127,'382.');
insert into tbl2 values(381, 381%127,'381.');
insert into tbl2 values(380, 380%127,'380.');
insert into tbl2 values(379, 379%127,'379.');
insert into tbl2 values(378, 378%127,'378.');
insert into tbl2 values(377, 377%127,'377.');
insert into tbl2 values(376, 376%127,'376.');
insert into tbl2 values(375, 375%127,'375.');
insert into tbl2 values(374, 374%127,'374.');
insert into tbl2 values(373, 373%127,'373.');
insert into tbl2 values(372, 372%127,'372.');
insert into tbl2 values(371, 371%127,'371.');
insert into tbl2 values(370, 370%127,'370.');
insert into tbl2 values(369, 369%127,'369.');
insert into tbl2 values(368, 368%127,'368.');
insert into tbl2 values(367, 367%127,'367.');
insert into tbl2 values(366, 366%127,'366.');
insert into tbl2 values(365, 365%127,'365.');
insert into tbl2 values(364, 364%127,'364.');
insert into tbl2 values(363, 363%127,'363.');
insert into tbl2 values(362, 362%127,'362.');
insert into tbl2 values(361, 361%127,'361.');
insert into tbl2 values(360, 360%127,'360.');
insert into tbl2 values(359, 359%127,'359.');
insert into tbl2 values(358, 358%127,'358.');
insert into tbl2 values(357, 357%127,'357.');
insert into tbl2 values(356, 356%127,'356.');
insert into tbl2 values(355, 355%127,'355.');
insert into tbl2 values(354, 354%127,'354.');
insert into tbl2 values(353, 353%127,'353.');
insert into tbl2 values(352, 352%127,'352.');
insert into tbl2 values(351, 351%127,'351.');
insert into tbl2 values(350, 350%127,'350.');
insert into tbl2 values(349, 349%127,'349.');
insert into tbl2 values(348, 348%127,'348.');
insert into tbl2 values(347, 347%127,'347.');
insert into tbl2 values(346, 346%127,'346.');
insert into tbl2 values(345, 345%127,'345.');
insert into tbl2 values(344, 344%127,'344.');
insert into tbl2 values(343, 343%127,'343.');
insert into tbl2 values(342, 342%127,'342.');
insert into tbl2 values(341, 341%127,'341.');
insert into tbl2 values(340, 340%127,'340.');
insert into tbl2 values(339, 339%127,'339.');
insert into tbl2 values(338, 338%127,'338.');
insert into tbl2 values(337, 337%127,'337.');
insert into tbl2 values(336, 336%127,'336.');
insert into tbl2 values(335, 335%127,'335.');
insert into tbl2 values(334, 334%127,'334.');
insert into tbl2 values(333, 333%127,'333.');
insert into tbl2 values(332, 332%127,'332.');
insert into tbl2 values(331, 331%127,'331.');
insert into tbl2 values(330, 330%127,'330.');
insert into tbl2 values(329, 329%127,'329.');
insert into tbl2 values(328, 328%127,'328.');
insert into tbl2 values(327, 327%127,'327.');
insert into tbl2 values(326, 326%127,'326.');
insert into tbl2 values(325, 325%127,'325.');
insert into tbl2 values(324, 324%127,'324.');
insert into tbl2 values(323, 323%127,'323.');
insert into tbl2 values(322, 322%127,'322.');
insert into tbl2 values(321, 321%127,'321.');
insert into tbl2 values(320, 320%127,'320.');
insert into tbl2 values(319, 319%127,'319.');
insert into tbl2 values(318, 318%127,'318.');
insert into tbl2 values(317, 317%127,'317.');
insert into tbl2 values(316, 316%127,'316.');
insert into tbl2 values(315, 315%127,'315.');
insert into tbl2 values(314, 314%127,'314.');
insert into tbl2 values(313, 313%127,'313.');
insert into tbl2 values(312, 312%127,'312.');
insert into tbl2 values(311, 311%127,'311.');
insert into tbl2 values(310, 310%127,'310.');
insert into tbl2 values(309, 309%127,'309.');
insert into tbl2 values(308, 308%127,'308.');
insert into tbl2 values(307, 307%127,'307.');
insert into tbl2 values(306, 306%127,'306.');
insert into tbl2 values(305, 305%127,'305.');
insert into tbl2 values(304, 304%127,'304.');
insert into tbl2 values(303, 303%127,'303.');
insert into tbl2 values(302, 302%127,'302.');
insert into tbl2 values(301, 301%127,'301.');
insert into tbl2 values(300, 300%127,'300.');
insert into tbl2 values(299, 299%127,'299.');
insert into tbl2 values(298, 298%127,'298.');
insert into tbl2 values(297, 297%127,'297.');
insert into tbl2 values(296, 296%127,'296.');
insert into tbl2 values(295, 295%127,'295.');
insert into tbl2 values(294, 294%127,'294.');
insert into tbl2 values(293, 293%127,'293.');
insert into tbl2 values(292, 292%127,'292.');
insert into tbl2 values(291, 291%127,'291.');
insert into tbl2 values(290, 290%127,'290.');
insert into tbl2 values(289, 289%127,'289.');
insert into tbl2 values(288, 288%127,'288.');
insert into tbl2 values(287, 287%127,'287.');
insert into tbl2 values(286, 286%127,'286.');
insert into tbl2 values(285, 285%127,'285.');
insert into tbl2 values(284, 284%127,'284.');
insert into tbl2 values(283, 283%127,'283.');
insert into tbl2 values(282, 282%127,'282.');
insert into tbl2 values(281, 281%127,'281.');
insert into tbl2 values(280, 280%127,'280.');
insert into tbl2 values(279, 279%127,'279.');
insert into tbl2 values(278, 278%127,'278.');
insert into tbl2 values(277, 277%127,'277.');
insert into tbl2 values(276, 276%127,'276.');
insert into tbl2 values(275, 275%127,'275.');
insert into tbl2 values(274, 274%127,'274.');
insert into tbl2 values(273, 273%127,'273.');
insert into tbl2 values(272, 272%127,'272.');
insert into tbl2 values(271, 271%127,'271.');
insert into tbl2 values(270, 270%127,'270.');
insert into tbl2 values(269, 269%127,'269.');
insert into tbl2 values(268, 268%127,'268.');
insert into tbl2 values(267, 267%127,'267.');
insert into tbl2 values(266, 266%127,'266.');
insert into tbl2 values(265, 265%127,'265.');
insert into tbl2 values(264, 264%127,'264.');
insert into tbl2 values(263, 263%127,'263.');
insert into tbl2 values(262, 262%127,'262.');
insert into tbl2 values(261, 261%127,'261.');
insert into tbl2 values(260, 260%127,'260.');
insert into tbl2 values(259, 259%127,'259.');
insert into tbl2 values(258, 258%127,'258.');
insert into tbl2 values(257, 257%127,'257.');
insert into tbl2 values(256, 256%127,'256.');
insert into tbl2 values(255, 255%127,'255.');
insert into tbl2 values(254, 254%127,'254.');
insert into tbl2 values(253, 253%127,'253.');
insert into tbl2 values(252, 252%127,'252.');
insert into tbl2 values(251, 251%127,'251.');
insert into tbl2 values(250, 250%127,'250.');
insert into tbl2 values(249, 249%127,'249.');
insert into tbl2 values(248, 248%127,'248.');
insert into tbl2 values(247, 247%127,'247.');
insert into tbl2 values(246, 246%127,'246.');
insert into tbl2 values(245, 245%127,'245.');
insert into tbl2 values(244, 244%127,'244.');
insert into tbl2 values(243, 243%127,'243.');
insert into tbl2 values(242, 242%127,'242.');
insert into tbl2 values(241, 241%127,'241.');
insert into tbl2 values(240, 240%127,'240.');
insert into tbl2 values(239, 239%127,'239.');
insert into tbl2 values(238, 238%127,'238.');
insert into tbl2 values(237, 237%127,'237.');
insert into tbl2 values(236, 236%127,'236.');
insert into tbl2 values(235, 235%127,'235.');
insert into tbl2 values(234, 234%127,'234.');
insert into tbl2 values(233, 233%127,'233.');
insert into tbl2 values(232, 232%127,'232.');
insert into tbl2 values(231, 231%127,'231.');
insert into tbl2 values(230, 230%127,'230.');
insert into tbl2 values(229, 229%127,'229.');
insert into tbl2 values(228, 228%127,'228.');
insert into tbl2 values(227, 227%127,'227.');
insert into tbl2 values(226, 226%127,'226.');
insert into tbl2 values(225, 225%127,'225.');
insert into tbl2 values(224, 224%127,'224.');
insert into tbl2 values(223, 223%127,'223.');
insert into tbl2 values(222, 222%127,'222.');
insert into tbl2 values(221, 221%127,'221.');
insert into tbl2 values(220, 220%127,'220.');
insert into tbl2 values(219, 219%127,'219.');
insert into tbl2 values(218, 218%127,'218.');
insert into tbl2 values(217, 217%127,'217.');
insert into tbl2 values(216, 216%127,'216.');
insert into tbl2 values(215, 215%127,'215.');
insert into tbl2 values(214, 214%127,'214.');
insert into tbl2 values(213, 213%127,'213.');
insert into tbl2 values(212, 212%127,'212.');
insert into tbl2 values(211, 211%127,'211.');
insert into tbl2 values(210, 210%127,'210.');
insert into tbl2 values(209, 209%127,'209.');
insert into tbl2 values(208, 208%127,'208.');
insert into tbl2 values(207, 207%127,'207.');
insert into tbl2 values(206, 206%127,'206.');
insert into tbl2 values(205, 205%127,'205.');
insert into tbl2 values(204, 204%127,'204.');
insert into tbl2 values(203, 203%127,'203.');
insert into tbl2 values(202, 202%127,'202.');
insert into tbl2 values(201, 201%127,'201.');
insert into tbl2 values(200, 200%127,'200.');
insert into tbl2 values(199, 199%127,'199.');
insert into tbl2 values(198, 198%127,'198.');
insert into tbl2 values(197, 197%127,'197.');
insert into tbl2 values(196, 196%127,'196.');
insert into tbl2 values(195, 195%127,'195.');
insert into tbl2 values(194, 194%127,'194.');
insert into tbl2 values(193, 193%127,'193.');
insert into tbl2 values(192, 192%127,'192.');
insert into tbl2 values(191, 191%127,'191.');
insert into tbl2 values(190, 190%127,'190.');
insert into tbl2 values(189, 189%127,'189.');
insert into tbl2 values(188, 188%127,'188.');
insert into tbl2 values(187, 187%127,'187.');
insert into tbl2 values(186, 186%127,'186.');
insert into tbl2 values(185, 185%127,'185.');
insert into tbl2 values(184, 184%127,'184.');
insert into tbl2 values(183, 183%127,'183.');
insert into tbl2 values(182, 182%127,'182.');
insert into tbl2 values(181, 181%127,'181.');
insert into tbl2 values(180, 180%127,'180.');
insert into tbl2 values(179, 179%127,'179.');
insert into tbl2 values(178, 178%127,'178.');
insert into tbl2 values(177, 177%127,'177.');
insert into tbl2 values(176, 176%127,'176.');
insert into tbl2 values(175, 175%127,'175.');
insert into tbl2 values(174, 174%127,'174.');
insert into tbl2 values(173, 173%127,'173.');
insert into tbl2 values(172, 172%127,'172.');
insert into tbl2 values(171, 171%127,'171.');
insert into tbl2 values(170, 170%127,'170.');
insert into tbl2 values(169, 169%127,'169.');
insert into tbl2 values(168, 168%127,'168.');
insert into tbl2 values(167, 167%127,'167.');
insert into tbl2 values(166, 166%127,'166.');
insert into tbl2 values(165, 165%127,'165.');
insert into tbl2 values(164, 164%127,'164.');
insert into tbl2 values(163, 163%127,'163.');
insert into tbl2 values(162, 162%127,'162.');
insert into tbl2 values(161, 161%127,'161.');
insert into tbl2 values(160, 160%127,'160.');
insert into tbl2 values(159, 159%127,'159.');
insert into tbl2 values(158, 158%127,'158.');
insert into tbl2 values(157, 157%127,'157.');
insert into tbl2 values(156, 156%127,'156.');
insert into tbl2 values(155, 155%127,'155.');
insert into tbl2 values(154, 154%127,'154.');
insert into tbl2 values(153, 153%127,'153.');
insert into tbl2 values(152, 152%127,'152.');
insert into tbl2 values(151, 151%127,'151.');
insert into tbl2 values(150, 150%127,'150.');
insert into tbl2 values(149, 149%127,'149.');
insert into tbl2 values(148, 148%127,'148.');
insert into tbl2 values(147, 147%127,'147.');
insert into tbl2 values(146, 146%127,'146.');
insert into tbl2 values(145, 145%127,'145.');
insert into tbl2 values(144, 144%127,'144.');
insert into tbl2 values(143, 143%127,'143.');
insert into tbl2 values(142, 142%127,'142.');
insert into tbl2 values(141, 141%127,'141.');
insert into tbl2 values(140, 140%127,'140.');
insert into tbl2 values(139, 139%127,'139.');
insert into tbl2 values(138, 138%127,'138.');
insert into tbl2 values(137, 137%127,'137.');
insert into tbl2 values(136, 136%127,'136.');
insert into tbl2 values(135, 135%127,'135.');
insert into tbl2 values(134, 134%127,'134.');
insert into tbl2 values(133, 133%127,'133.');
insert into tbl2 values(132, 132%127,'132.');
insert into tbl2 values(131, 131%127,'131.');
insert into tbl2 values(130, 130%127,'130.');
insert into tbl2 values(129, 129%127,'129.');
insert into tbl2 values(128, 128%127,'128.');
insert into tbl2 values(127, 127%127,'127.');
insert into tbl2 values(126, 126%127,'126.');
insert into tbl2 values(125, 125%127,'125.');
insert into tbl2 values(124, 124%127,'124.');
insert into tbl2 values(123, 123%127,'123.');
insert into tbl2 values(122, 122%127,'122.');
insert into tbl2 values(121, 121%127,'121.');
insert into tbl2 values(120, 120%127,'120.');
insert into tbl2 values(119, 119%127,'119.');
insert into tbl2 values(118, 118%127,'118.');
insert into tbl2 values(117, 117%127,'117.');
insert into tbl2 values(116, 116%127,'116.');
insert into tbl2 values(115, 115%127,'115.');
insert into tbl2 values(114, 114%127,'114.');
insert into tbl2 values(113, 113%127,'113.');
insert into tbl2 values(112, 112%127,'112.');
insert into tbl2 values(111, 111%127,'111.');
insert into tbl2 values(110, 110%127,'110.');
insert into tbl2 values(109, 109%127,'109.');
insert into tbl2 values(108, 108%127,'108.');
insert into tbl2 values(107, 107%127,'107.');
insert into tbl2 values(106, 106%127,'106.');
insert into tbl2 values(105, 105%127,'105.');
insert into tbl2 values(104, 104%127,'104.');
insert into tbl2 values(103, 103%127,'103.');
insert into tbl2 values(102, 102%127,'102.');
insert into tbl2 values(101, 101%127,'101.');
insert into tbl2 values(100, 100%127,'100.');
insert into tbl2 values(99, 99%127,'99.');
insert into tbl2 values(98, 98%127,'98.');
insert into tbl2 values(97, 97%127,'97.');
insert into tbl2 values(96, 96%127,'96.');
insert into tbl2 values(95, 95%127,'95.');
insert into tbl2 values(94, 94%127,'94.');
insert into tbl2 values(93, 93%127,'93.');
insert into tbl2 values(92, 92%127,'92.');
insert into tbl2 values(91, 91%127,'91.');
insert into tbl2 values(90, 90%127,'90.');
insert into tbl2 values(89, 89%127,'89.');
insert into tbl2 values(88, 88%127,'88.');
insert into tbl2 values(87, 87%127,'87.');
insert into tbl2 values(86, 86%127,'86.');
insert into tbl2 values(85, 85%127,'85.');
insert into tbl2 values(84, 84%127,'84.');
insert into tbl2 values(83, 83%127,'83.');
insert into tbl2 values(82, 82%127,'82.');
insert into tbl2 values(81, 81%127,'81.');
insert into tbl2 values(80, 80%127,'80.');
insert into tbl2 values(79, 79%127,'79.');
insert into tbl2 values(78, 78%127,'78.');
insert into tbl2 values(77, 77%127,'77.');
insert into tbl2 values(76, 76%127,'76.');
insert into tbl2 values(75, 75%127,'75.');
insert into tbl2 values(74, 74%127,'74.');
insert into tbl2 values(73, 73%127,'73.');
insert into tbl2 values(72, 72%127,'72.');
insert into tbl2 values(71, 71%127,'71.');
insert into tbl2 values(70, 70%127,'70.');
insert into tbl2 values(69, 69%127,'69.');
insert into tbl2 values(68, 68%127,'68.');
insert into tbl2 values(67, 67%127,'67.');
insert into tbl2 values(66, 66%127,'66.');
insert into tbl2 values(65, 65%127,'65.');
insert into tbl2 values(64, 64%127,'64.');
insert into tbl2 values(63, 63%127,'63.');
insert into tbl2 values(62, 62%127,'62.');
insert into tbl2 values(61, 61%127,'61.');
insert into tbl2 values(60, 60%127,'60.');
insert into tbl2 values(59, 59%127,'59.');
insert into tbl2 values(58, 58%127,'58.');
insert into tbl2 values(57, 57%127,'57.');
insert into tbl2 values(56, 56%127,'56.');
insert into tbl2 values(55, 55%127,'55.');
insert into tbl2 values(54, 54%127,'54.');
insert into tbl2 values(53, 53%127,'53.');
insert into tbl2 values(52, 52%127,'52.');
insert into tbl2 values(51, 51%127,'51.');
insert into tbl2 values(50, 50%127,'50.');
insert into tbl2 values(49, 49%127,'49.');
insert into tbl2 values(48, 48%127,'48.');
insert into tbl2 values(47, 47%127,'47.');
insert into tbl2 values(46, 46%127,'46.');
insert into tbl2 values(45, 45%127,'45.');
insert into tbl2 values(44, 44%127,'44.');
insert into tbl2 values(43, 43%127,'43.');
insert into tbl2 values(42, 42%127,'42.');
insert into tbl2 values(41, 41%127,'41.');
insert into tbl2 values(40, 40%127,'40.');
insert into tbl2 values(39, 39%127,'39.');
insert into tbl2 values(38, 38%127,'38.');
insert into tbl2 values(37, 37%127,'37.');
insert into tbl2 values(36, 36%127,'36.');
insert into tbl2 values(35, 35%127,'35.');
insert into tbl2 values(34, 34%127,'34.');
insert into tbl2 values(33, 33%127,'33.');
insert into tbl2 values(32, 32%127,'32.');
insert into tbl2 values(31, 31%127,'31.');
insert into tbl2 values(30, 30%127,'30.');
insert into tbl2 values(29, 29%127,'29.');
insert into tbl2 values(28, 28%127,'28.');
insert into tbl2 values(27, 27%127,'27.');
insert into tbl2 values(26, 26%127,'26.');
insert into tbl2 values(25, 25%127,'25.');
insert into tbl2 values(24, 24%127,'24.');
insert into tbl2 values(23, 23%127,'23.');
insert into tbl2 values(22, 22%127,'22.');
insert into tbl2 values(21, 21%127,'21.');
insert into tbl2 values(20, 20%127,'20.');
insert into tbl2 values(19, 19%127,'19.');
insert into tbl2 values(18, 18%127,'18.');
insert into tbl2 values(17, 17%127,'17.');
insert into tbl2 values(16, 16%127,'16.');
insert into tbl2 values(15, 15%127,'15.');
insert into tbl2 values(14, 14%127,'14.');
insert into tbl2 values(13, 13%127,'13.');
insert into tbl2 values(12, 12%127,'12.');
insert into tbl2 values(11, 11%127,'11.');
insert into tbl2 values(10, 10%127,'10.');
insert into tbl2 values(9, 9%127,'9.');
insert into tbl2 values(8, 8%127,'8.');
insert into tbl2 values(7, 7%127,'7.');
insert into tbl2 values(6, 6%127,'6.');
insert into tbl2 values(5, 5%127,'5.');
insert into tbl2 values(4, 4%127,'4.');
insert into tbl2 values(3, 3%127,'3.');
insert into tbl2 values(2, 2%127,'2.');
insert into tbl2 values(1, 1%127,'1.');
COMMIT;
SET autocommit = default;
ANALYZE TABLE tbl1, tbl2;
Table	Op	Msg_type	Msg_text
test.tbl1	analyze	status	OK
test.tbl2	analyze	status	OK
SELECT
t1.login AS tlogin,
numb -
IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0) -
IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0) as sp
FROM tbl1 t1, tbl2 t2
WHERE t1.login=t2.login
GROUP BY t1.login
LIMIT 5;
tlogin	sp
1	-1.00
2	-2.00
3	-3.00
4	-4.00
5	-5.00
EXPLAIN SELECT
t1.login AS tlogin,
numb -
IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0) -
IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0) as sp
FROM tbl1 t1, tbl2 t2
WHERE t1.login=t2.login
GROUP BY t1.login
LIMIT 5;
EXPLAIN
-> Limit: 5 row(s)  (rows=5)
    -> Group (no aggregates)  (rows=500)
        -> Nested loop inner join  (rows=500)
            -> Nested loop left join  (rows=500)
                -> Nested loop left join  (rows=500)
                    -> Covering index scan on t2 using login  (rows=500)
                    -> Index lookup on derived_1_2 using <auto_key0> (login = t2.login)  (rows=1)
                        -> Materialize  (rows=500)
                            -> Group aggregate: sum(tbl2.nump)  (rows=500)
                                -> Sort: tbl2.login  (rows=500)
                                    -> Table scan on tbl2  (rows=500)
                -> Index lookup on derived_1_3 using <auto_key0> (login = t2.login)  (rows=1)
                    -> Materialize  (rows=500)
                        -> Group aggregate: sum(tbl2.nump)  (rows=500)
                            -> Sort: tbl2.login  (rows=500)
                                -> Table scan on tbl2  (rows=500)
            -> Single-row index lookup on t1 using PRIMARY (login = t2.login)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.login' of SELECT #2 was resolved in SELECT #1
Note	1276	Field or reference 'test.t1.login' of SELECT #3 was resolved in SELECT #1
SELECT
t1.login AS tlogin,
numb -
IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0) -
IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0) as sp
FROM tbl1 t1, tbl2 t2
WHERE t1.login=t2.login
GROUP BY t1.login
ORDER BY sp
LIMIT 5;
tlogin	sp
500	-500.00
499	-499.00
498	-498.00
497	-497.00
496	-496.00
EXPLAIN SELECT
t1.login AS tlogin,
numb -
IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0) -
IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0) as sp
FROM tbl1 t1, tbl2 t2
WHERE t1.login=t2.login
GROUP BY t1.login
ORDER BY sp
LIMIT 5;
EXPLAIN
-> Sort: sp, limit input to 5 row(s) per chunk  (rows=5)
    -> Group (no aggregates)  (rows=500)
        -> Nested loop inner join  (rows=500)
            -> Sort: t2.login  (rows=500)
                -> Left hash join (t2.login = derived_1_3.login)  (rows=500)
                    -> Left hash join (t2.login = derived_1_2.login)  (rows=500)
                        -> Covering index scan on t2 using login  (rows=500)
                        -> Hash
                            -> Table scan on derived_1_2  (rows=500)
                                -> Materialize  (rows=500)
                                    -> Group aggregate: sum(tbl2.nump)  (rows=500)
                                        -> Sort: tbl2.login  (rows=500)
                                            -> Table scan on tbl2  (rows=500)
                    -> Hash
                        -> Table scan on derived_1_3  (rows=500)
                            -> Materialize  (rows=500)
                                -> Group aggregate: sum(tbl2.nump)  (rows=500)
                                    -> Sort: tbl2.login  (rows=500)
                                        -> Table scan on tbl2  (rows=500)
            -> Single-row index lookup on t1 using PRIMARY (login = t2.login)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.login' of SELECT #2 was resolved in SELECT #1
Note	1276	Field or reference 'test.t1.login' of SELECT #3 was resolved in SELECT #1
SELECT
t1.login AS tlogin,
numb -
IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0) -
IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0) as sp
FROM tbl1 t1, tbl2 t2
WHERE t1.login=t2.login
GROUP BY t1.login
ORDER BY numb - IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0)
- IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0)
LIMIT 5;
tlogin	sp
500	-500.00
499	-499.00
498	-498.00
497	-497.00
496	-496.00
EXPLAIN SELECT
t1.login AS tlogin,
numb -
IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0) -
IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0) as sp
FROM tbl1 t1, tbl2 t2
WHERE t1.login=t2.login
GROUP BY t1.login
ORDER BY numb - IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0)
- IFNULL((SELECT sum(nump) FROM tbl2 WHERE login=t1.login), 0)
LIMIT 5;
EXPLAIN
-> Sort: sp, limit input to 5 row(s) per chunk  (rows=5)
    -> Group (no aggregates)  (rows=500)
        -> Nested loop inner join  (rows=500)
            -> Sort: t2.login  (rows=500)
                -> Left hash join (t2.login = derived_1_3.login)  (rows=500)
                    -> Left hash join (t2.login = derived_1_2.login)  (rows=500)
                        -> Covering index scan on t2 using login  (rows=500)
                        -> Hash
                            -> Table scan on derived_1_2  (rows=500)
                                -> Materialize  (rows=500)
                                    -> Group aggregate: sum(tbl2.nump)  (rows=500)
                                        -> Sort: tbl2.login  (rows=500)
                                            -> Table scan on tbl2  (rows=500)
                    -> Hash
                        -> Table scan on derived_1_3  (rows=500)
                            -> Materialize  (rows=500)
                                -> Group aggregate: sum(tbl2.nump)  (rows=500)
                                    -> Sort: tbl2.login  (rows=500)
                                        -> Table scan on tbl2  (rows=500)
            -> Single-row index lookup on t1 using PRIMARY (login = t2.login)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.login' of SELECT #2 was resolved in SELECT #1
Note	1276	Field or reference 'test.t1.login' of SELECT #3 was resolved in SELECT #1
Note	1276	Field or reference 'test.t1.login' of SELECT #4 was resolved in SELECT #1
Note	1276	Field or reference 'test.t1.login' of SELECT #5 was resolved in SELECT #1
DROP TABLE tbl1, tbl2;
Bug 2
CREATE TABLE t2 (a INT, b INT);
CREATE TABLE t4 (a INT NOT NULL, b INT NOT NULL);
INSERT INTO t2 VALUES (1, 7), (2, 7), (2,10);
INSERT INTO t4 VALUES (4, 8), (3, 8), (5, 9), (12, 7), (1, 7),
(10, 9), (9, 6), (7, 6), (3, 9), (1, 10);
ANALYZE TABLE t2, t4;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
test.t4	analyze	status	OK
SELECT b, MAX(a) AS ma FROM t4
GROUP BY b HAVING ma < (SELECT MAX(t2.a) FROM t2 WHERE t2.b=t4.b);
b	ma
10	1
EXPLAIN SELECT b, MAX(a) AS ma FROM t4
GROUP BY b HAVING ma < (SELECT MAX(t2.a) FROM t2 WHERE t2.b=t4.b);
EXPLAIN
-> Filter: (ma < derived_1_2.`MAX(t2.a)`)  (rows=3.16)
    -> Group aggregate: max(t4.a)  (rows=3.16)
        -> Sort: t4.b  (rows=10)
            -> Left hash join (derived_1_2.b = t4.b)  (rows=10)
                -> Table scan on t4  (rows=10)
                -> Hash
                    -> Table scan on derived_1_2  (rows=1.73)
                        -> Materialize  (rows=1.73)
                            -> Group aggregate: max(t2.a)  (rows=1.73)
                                -> Sort: t2.b  (rows=3)
                                    -> Table scan on t2  (rows=3)

Warnings:
Note	1276	Field or reference 'test.t4.b' of SELECT #2 was resolved in SELECT #1
DROP TABLE t2, t4;
Bug 3

This query was caused an issue at one point, while we
still tried to moved explicit grouping into a derived table.
Should work fine now.
CREATE TEMPORARY TABLE tmp_digests (
schema_name VARCHAR(64) DEFAULT NULL,
digest VARCHAR(64) DEFAULT NULL,
digest_text LONGTEXT,
count_star BIGINT UNSIGNED NOT NULL,
sum_timer_wait BIGINT UNSIGNED NOT NULL,
min_timer_wait BIGINT UNSIGNED NOT NULL,
avg_timer_wait BIGINT UNSIGNED NOT NULL,
max_timer_wait BIGINT UNSIGNED NOT NULL,
sum_lock_time BIGINT UNSIGNED NOT NULL,
sum_errors BIGINT UNSIGNED NOT NULL,
sum_warnings BIGINT UNSIGNED NOT NULL,
sum_rows_affected BIGINT UNSIGNED NOT NULL,
sum_rows_sent BIGINT UNSIGNED NOT NULL,
sum_rows_examined BIGINT UNSIGNED NOT NULL,
sum_created_tmp_disk_tables BIGINT UNSIGNED NOT NULL,
sum_created_tmp_tables BIGINT UNSIGNED NOT NULL,
sum_select_full_join BIGINT UNSIGNED NOT NULL,
sum_select_full_range_join BIGINT UNSIGNED NOT NULL,
sum_select_range BIGINT UNSIGNED NOT NULL,
sum_select_range_check BIGINT UNSIGNED NOT NULL,
sum_select_scan BIGINT UNSIGNED NOT NULL,
sum_sort_merge_passes BIGINT UNSIGNED NOT NULL,
sum_sort_range BIGINT UNSIGNED NOT NULL,
sum_sort_rows BIGINT UNSIGNED NOT NULL,
sum_sort_scan BIGINT UNSIGNED NOT NULL,
sum_no_index_used BIGINT UNSIGNED NOT NULL,
sum_no_good_index_used BIGINT UNSIGNED NOT NULL,
sum_cpu_time BIGINT UNSIGNED NOT NULL,
max_controlled_memory BIGINT UNSIGNED NOT NULL,
max_total_memory BIGINT UNSIGNED NOT NULL,
count_secondary BIGINT UNSIGNED NOT NULL,
first_seen TIMESTAMP NULL DEFAULT NULL,
last_seen TIMESTAMP NULL DEFAULT NULL,
quantile_95 BIGINT UNSIGNED NOT NULL,
quantile_99 BIGINT UNSIGNED NOT NULL,
quantile_999 BIGINT UNSIGNED NOT NULL,
query_sample_text longtext,
query_sample_seen TIMESTAMP NULL DEFAULT NULL,
query_sample_timer_wait BIGINT UNSIGNED NOT NULL,
INDEX (schema_name, digest)
) DEFAULT CHARSET=utf8mb4;
INSERT INTO tmp_digests SELECT * FROM performance_schema.events_statements_summary_by_digest;
CREATE TEMPORARY TABLE tmp_digest_avg_latency_distribution1 (
cnt BIGINT UNSIGNED NOT NULL,
avg_us DECIMAL(21,0) NOT NULL,
PRIMARY KEY (avg_us)
) ENGINE=InnoDB;
CREATE TEMPORARY TABLE tmp_digest_avg_latency_distribution2 (
cnt BIGINT UNSIGNED NOT NULL,
avg_us DECIMAL(21,0) NOT NULL,
PRIMARY KEY (avg_us)
) ENGINE=InnoDB;
INSERT INTO tmp_digest_avg_latency_distribution1
SELECT COUNT(*) cnt,
ROUND(avg_timer_wait/1000000) AS avg_us
FROM tmp_digests
GROUP BY avg_us;
INSERT INTO tmp_digest_avg_latency_distribution2 SELECT * FROM tmp_digest_avg_latency_distribution1;
CREATE TEMPORARY TABLE tmp_digest_95th_percentile_by_avg_us (
avg_us decimal(21,0) NOT NULL,
percentile decimal(46,4) NOT NULL,
PRIMARY KEY (avg_us)
) ENGINE=InnoDB;
ANALYZE TABLE tmp_digests;
Table	Op	Msg_type	Msg_text
test.tmp_digests	analyze	status	OK
ANALYZE TABLE tmp_digest_avg_latency_distribution1;
Table	Op	Msg_type	Msg_text
test.tmp_digest_avg_latency_distribution1	analyze	status	OK
ANALYZE TABLE tmp_digest_avg_latency_distribution2;
Table	Op	Msg_type	Msg_text
test.tmp_digest_avg_latency_distribution2	analyze	status	OK
ANALYZE TABLE tmp_digest_95th_percentile_by_avg_us;
Table	Op	Msg_type	Msg_text
test.tmp_digest_95th_percentile_by_avg_us	analyze	status	OK
PREPARE p from 'INSERT INTO tmp_digest_95th_percentile_by_avg_us
SELECT s2.avg_us avg_us,
IFNULL(SUM(s1.cnt)/
NULLIF((SELECT COUNT(*) FROM tmp_digests), 0), 0) percentile
FROM tmp_digest_avg_latency_distribution1 AS s1
JOIN tmp_digest_avg_latency_distribution2 AS s2
ON s1.avg_us <= s2.avg_us
GROUP BY s2.avg_us
HAVING percentile > 0.95
ORDER BY percentile
LIMIT 1';
EXECUTE p;
SELECT * from tmp_digest_95th_percentile_by_avg_us;
avg_us	percentile
xxxxx	xxxxx
EXPLAIN INSERT INTO tmp_digest_95th_percentile_by_avg_us
SELECT s2.avg_us avg_us,
IFNULL(SUM(s1.cnt)/
NULLIF((SELECT COUNT(*) FROM tmp_digests), 0), 0) percentile
FROM tmp_digest_avg_latency_distribution1 AS s1
JOIN tmp_digest_avg_latency_distribution2 AS s2
ON s1.avg_us <= s2.avg_us
GROUP BY s2.avg_us
HAVING percentile > 0.95
ORDER BY percentile
LIMIT 1;
EXPLAIN
-> Insert into tmp_digest_95th_percentile_by_avg_us
    -> Sort: percentile, limit input to 1 row(s) per chunk
        -> Filter: (percentile > 0.95)
            -> Table scan on <temporary>
                -> Aggregate using temporary table
                    -> Left hash join (no condition)
                        -> Inner hash join (no condition), extra conditions: (s1.avg_us <= s2.avg_us)
                            -> Table scan on s1
                            -> Hash
                                -> Table scan on s2
                        -> Hash
                            -> Table scan on derived_1_2
                                -> Materialize
                                    -> Aggregate: count(0)
                                        -> Covering index scan on tmp_digests using schema_name

DROP PREPARE p;
DROP TEMPORARY TABLE tmp_digest_95th_percentile_by_avg_us;
DROP TEMPORARY TABLE tmp_digest_avg_latency_distribution2;
DROP TEMPORARY TABLE tmp_digest_avg_latency_distribution1;
DROP TEMPORARY TABLE tmp_digests;
Bug 4

Nested query with transformable scalar subquery at both levels:
interference with semijoin: 1) derived table name not unique
after flattening, 2) flattening needs to happen after
transforming scalar subqueries to derived tables (on the top
level here we got problems).
CREATE TABLE t1 (col_int_key int, KEY col_int_key (col_int_key));
INSERT INTO t1 VALUES (0),(8),(1),(8);
CREATE TABLE where_subselect_20070
SELECT table2 .col_int_key AS field1,
( SELECT COUNT( col_int_key )
FROM t1
)
FROM t1 AS table1
JOIN t1 AS table2
ON table2.col_int_key = table1.col_int_key;
ANALYZE TABLE t1, where_subselect_20070;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.where_subselect_20070	analyze	status	OK
SELECT *
FROM where_subselect_20070
WHERE (field1, ( SELECT COUNT( col_int_key ) FROM t1 )) IN (
SELECT table2 .col_int_key AS field1,
( SELECT COUNT( col_int_key )
FROM t1
)
FROM t1 AS table1
JOIN t1 AS table2
ON table2.col_int_key = table1.col_int_key
);
field1	( SELECT COUNT( col_int_key )
)
0	4
1	4
8	4
8	4
8	4
8	4
FROM t1
EXPLAIN SELECT *
FROM where_subselect_20070
WHERE (field1, ( SELECT COUNT( col_int_key ) FROM t1 )) IN (
SELECT table2 .col_int_key AS field1,
( SELECT COUNT( col_int_key )
FROM t1
)
FROM t1 AS table1
JOIN t1 AS table2
ON table2.col_int_key = table1.col_int_key
);
EXPLAIN
-> Hash semijoin (FirstMatch) (where_subselect_20070.field1 = table1.col_int_key), (derived_1_2.`COUNT( col_int_key )` = derived_3_4.`COUNT( col_int_key )`)  (rows=0.462)
    -> Inner hash join (no condition)  (rows=6)
        -> Table scan on where_subselect_20070  (rows=6)
        -> Hash
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: count(t1.col_int_key)  (rows=1)
                        -> Table scan on t1  (rows=4)
    -> Hash
        -> Inner hash join (no condition)  (rows=5.33)
            -> Nested loop inner join  (rows=5.33)
                -> Table scan on table2  (rows=4)
                -> Covering index lookup on table1 using col_int_key (col_int_key = table2.col_int_key)  (rows=1.33)
            -> Hash
                -> Table scan on derived_3_4  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: count(t1.col_int_key)  (rows=1)
                            -> Table scan on t1  (rows=4)

DROP TABLE t1, where_subselect_20070;
Bug 5

We used to not get the error: the GROUP BY transformation into a
derived table didn't see outer reference "outr.a"
CREATE TABLE t1 (a INT, b INT);
INSERT INTO t1 VALUES (1,1), (1,2), (1,3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT COUNT(*), (SELECT count(*) FROM t1 inr WHERE inr.a = outr.a)
FROM t1 outr;
ERROR 42000: In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'test.outr.a'; this is incompatible with sql_mode=only_full_group_by
DROP TABLE t1;
Bug, cf requirement FR#4 addendum: If a prepared statement was
prepared with the transformation enabled, it will also be
executed with the transformation.
CREATE TABLE t1(a DATETIME NOT NULL);
INSERT INTO t1 VALUES ('20060606155555');
PREPARE s FROM
'SELECT a FROM t1 WHERE a=(SELECT MAX(a) FROM t1) AND (a="20060606155555")';
SET optimizer_switch='subquery_to_derived=off';
EXECUTE s;
a
2006-06-06 15:55:55
Try the other way too. Transform will not happen on EXECUTE
as it can be performed only on PREPARE.
PREPARE s FROM
'SELECT a FROM t1 WHERE a=(SELECT MAX(a) FROM t1) AND (a="20060606155555")';
SET optimizer_switch='subquery_to_derived=on';
EXECUTE s;
a
2006-06-06 15:55:55
DROP TABLE t1;
#
# Bug fix: we transformed a query which cannot be transformed
#
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES (1),(2),(3),(4);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SET optimizer_switch='subquery_to_derived=off';
SELECT (SELECT MIN(a) FROM t1) a, MAX(a) AS mx
FROM t1
WHERE FALSE
HAVING (SELECT MIN(a) FROM t1) > 0;
a	mx
1	NULL
SELECT MAX(a) AS mx
FROM t1
WHERE FALSE
HAVING (SELECT MIN(a) FROM t1) > 0;
mx
NULL
SET optimizer_switch='subquery_to_derived=on';
SELECT (SELECT MIN(a) FROM t1) a, MAX(a) AS mx
FROM t1
WHERE FALSE
HAVING (SELECT MIN(a) FROM t1) > 0;
a	mx
1	NULL
EXPLAIN SELECT (SELECT MIN(a) FROM t1) a, MAX(a) AS mx
FROM t1
WHERE FALSE
HAVING (SELECT MIN(a) FROM t1) > 0;
EXPLAIN
-> Filter: ((select #3) > 0)  (rows=1)
    -> Zero input rows (Impossible WHERE), aggregated into one output row  (rows=1)
    -> Select #3 (subquery in condition; run only once)
        -> Aggregate: min(t1.a)  (rows=1)
            -> Table scan on t1  (rows=4)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: min(t1.a)  (rows=1)
        -> Table scan on t1  (rows=4)

SELECT MAX(a) AS mx
FROM t1
WHERE FALSE
HAVING (SELECT MIN(a) FROM t1) > 0;
mx
NULL
EXPLAIN SELECT MAX(a) AS mx
FROM t1
WHERE FALSE
HAVING (SELECT MIN(a) FROM t1) > 0;
EXPLAIN
-> Filter: ((select #2) > 0)  (rows=1)
    -> Zero input rows (Impossible WHERE), aggregated into one output row  (rows=1)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(t1.a)  (rows=1)
            -> Table scan on t1  (rows=4)

DROP TABLE t1;
#
# Bug#30616646 WL#12885: SIG 6 IN JOIN::MAKE_JOIN_PLAN() AT SQL/SQL_OPTIMIZER.CC
#
CREATE TABLE tab1(pk int PRIMARY KEY);
SELECT *
FROM tab1 AS table1
LEFT JOIN
( tab1 AS table2 JOIN
tab1 AS table3
ON 1 <= (SELECT COUNT(pk) FROM tab1) )
ON 1
WHERE (SELECT MIN(pk) FROM tab1);
pk	pk	pk
EXPLAIN SELECT *
FROM tab1 AS table1
LEFT JOIN
( tab1 AS table2 JOIN
tab1 AS table3
ON 1 <= (SELECT COUNT(pk) FROM tab1) )
ON 1
WHERE (SELECT MIN(pk) FROM tab1);
EXPLAIN
-> Inner hash join (no condition)  (rows=1)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on table1  (rows=1)
        -> Hash
            -> Inner hash join (no condition)  (rows=0.333)
                -> Table scan on table3  (rows=1)
                -> Hash
                    -> Inner hash join (no condition)  (rows=0.333)
                        -> Table scan on table2  (rows=1)
                        -> Hash
                            -> Filter: (1 <= derived_1_2.`COUNT(pk)`)  (rows=0.333)
                                -> Table scan on derived_1_2  (rows=1)
                                    -> Materialize  (rows=1)
                                        -> Aggregate: count(tab1.pk)  (rows=1)
                                            -> Table scan on tab1  (rows=1)
    -> Hash
        -> Table scan on derived_1_3  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (0 <> min(tab1.pk))  (rows=1)
                    -> Aggregate: min(tab1.pk)  (rows=1)
                        -> Table scan on tab1  (rows=1)

DROP TABLE tab1;
#
# Bug#30617216 WL#12885 SIG6 IN JOIN::ADD_HAVING_AS_TMP_TABLE_COND() AT SQL/SQL_SELECT.CC
#
# ANY/ALL/SOME transformation makes us skip subquery to derived
# transformation
#
CREATE TABLE c2 (col_varchar_key VARCHAR(1));
SELECT alias1.col_varchar_key
FROM c2 AS alias1
HAVING   alias1.col_varchar_key > SOME (SELECT col_varchar_key FROM c2)
ORDER BY alias1.col_varchar_key;
col_varchar_key
EXPLAIN SELECT alias1.col_varchar_key
FROM c2 AS alias1
HAVING   alias1.col_varchar_key > SOME (SELECT col_varchar_key FROM c2)
ORDER BY alias1.col_varchar_key;
EXPLAIN
-> Sort: alias1.col_varchar_key  (rows=0.667)
    -> Filter: <nop>((alias1.col_varchar_key > (select #2)))  (rows=0.667)
        -> Table scan on alias1  (rows=1)
        -> Select #2 (subquery in condition; run only once)
            -> Aggregate: min(c2.col_varchar_key)  (rows=1)
                -> Table scan on c2  (rows=1)

DROP TABLE c2;
#
# Bug#30622834 WL#12885: ASSERTION `SELECT_LEX->IS_RECURSIVE()' FAILED
# Update: these are no longer transformed after Bug#31566339
CREATE TABLE t1(col_int INT);
SELECT *
FROM ((t1 AS a2
LEFT JOIN
t1 AS a1
ON  1 <= SOME (SELECT COUNT(*) FROM t1))
LEFT JOIN
t1
ON true);
col_int	col_int	col_int
EXPLAIN SELECT *
FROM ((t1 AS a2
LEFT JOIN
t1 AS a1
ON  1 <= SOME (SELECT COUNT(*) FROM t1))
LEFT JOIN
t1
ON true);
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Left hash join (no condition), extra conditions: <cache>(<nop>((1 <= <max>(select #2))))  (rows=1)
        -> Table scan on a2  (rows=1)
        -> Hash
            -> Table scan on a1  (rows=1)
        -> Select #2 (subquery in extra conditions; run only once)
            -> Aggregate: count(0)  (rows=1)
                -> Table scan on t1  (rows=1)
    -> Hash
        -> Table scan on t1  (rows=1)

SELECT *
FROM ((t1 AS a2
LEFT JOIN
t1 AS a1
ON  1 <= ALL (SELECT COUNT(*) FROM t1))
LEFT JOIN
t1
ON true);
col_int	col_int	col_int
EXPLAIN SELECT *
FROM ((t1 AS a2
LEFT JOIN
t1 AS a1
ON  1 <= ALL (SELECT COUNT(*) FROM t1))
LEFT JOIN
t1
ON true);
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Left hash join (no condition), extra conditions: <cache>(<not>((1 > <min>(select #2))))  (rows=1)
        -> Table scan on a2  (rows=1)
        -> Hash
            -> Table scan on a1  (rows=1)
        -> Select #2 (subquery in extra conditions; run only once)
            -> Aggregate: count(0)  (rows=1)
                -> Table scan on t1  (rows=1)
    -> Hash
        -> Table scan on t1  (rows=1)

SELECT *
FROM (t1
RIGHT JOIN
(t1 AS a1
RIGHT JOIN
t1 AS a2
ON  1 <= SOME (SELECT COUNT(*) FROM t1))
ON true);
col_int	col_int	col_int
EXPLAIN SELECT *
FROM (t1
RIGHT JOIN
(t1 AS a1
RIGHT JOIN
t1 AS a2
ON  1 <= SOME (SELECT COUNT(*) FROM t1))
ON true);
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Left hash join (no condition), extra conditions: <cache>(<nop>((1 <= <max>(select #2))))  (rows=1)
        -> Table scan on a2  (rows=1)
        -> Hash
            -> Table scan on a1  (rows=1)
        -> Select #2 (subquery in extra conditions; run only once)
            -> Aggregate: count(0)  (rows=1)
                -> Table scan on t1  (rows=1)
    -> Hash
        -> Table scan on t1  (rows=1)

DROP TABLE t1;
#
# Bug#30626975 WL#12885: WL#12885: SIG 6 IN SELECT_LEX::SYNTHESIZE_DERIVED() AT SQL/SQL_RESOLVER.CC
# Update: ANY/ALL/SOME rejected.
CREATE TABLE t1(pk int PRIMARY KEY);
Simplified repro, requires -ps-protocol to fail before fix
SELECT t1.pk
FROM t1 LEFT JOIN ( SELECT t1.pk AS pk
FROM t1
WHERE (1 <= (SELECT MAX(t1.pk)
FROM t1)) ) alias2
ON true;
pk
EXPLAIN SELECT t1.pk
FROM t1 LEFT JOIN ( SELECT t1.pk AS pk
FROM t1
WHERE (1 <= (SELECT MAX(t1.pk)
FROM t1)) ) alias2
ON true;
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on t1  (rows=1)
    -> Hash
        -> Inner hash join (no condition)  (rows=0.333)
            -> Table scan on t1  (rows=1)
            -> Hash
                -> Filter: (1 <= derived_2_3.`MAX(t1.pk)`)  (rows=0.333)
                    -> Table scan on derived_2_3  (rows=1)
                        -> Materialize  (rows=1)
                            -> Aggregate: max(t1.pk)  (rows=1)
                                -> Table scan on t1  (rows=1)

Another mode of the bug: exercises has_scalar_to_derived_transform
which also needs to look inside join nest
PREPARE p FROM "SELECT t1.pk
FROM t1 LEFT JOIN ( SELECT t1.pk AS pk
FROM t1
WHERE (1 <= (SELECT MAX(t1.pk)
FROM t1)) ) alias2
ON true";
EXECUTE p;
pk
SET optimizer_switch='subquery_to_derived=off';
EXECUTE p;
pk
SET optimizer_switch='subquery_to_derived=on';
EXECUTE p;
pk
original repro, requires -ps-protocol to fail before fix)
SELECT alias1.pk
FROM t1 AS alias1 LEFT JOIN
t1 AS alias2 LEFT JOIN
(SELECT *
FROM t1
WHERE 1 <= ANY (SELECT c_sq1_alias1.pk
FROM t1 AS c_sq1_alias1 JOIN t1 AS c_sq1_alias2
ON TRUE
)
) AS alias3
ON TRUE
ON TRUE;
pk
EXPLAIN SELECT alias1.pk
FROM t1 AS alias1 LEFT JOIN
t1 AS alias2 LEFT JOIN
(SELECT *
FROM t1
WHERE 1 <= ANY (SELECT c_sq1_alias1.pk
FROM t1 AS c_sq1_alias1 JOIN t1 AS c_sq1_alias2
ON TRUE
)
) AS alias3
ON TRUE
ON TRUE;
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on alias1  (rows=1)
    -> Hash
        -> Left hash join (no condition)  (rows=1)
            -> Table scan on alias2  (rows=1)
            -> Hash
                -> Inner hash join (no condition)  (rows=0.3)
                    -> Table scan on t1  (rows=1)
                    -> Hash
                        -> Filter: ((1 <= derived_2_3.Name_exp_1) and (derived_2_3.Name_exp_2 <> 0))  (rows=0.3)
                            -> Table scan on derived_2_3  (rows=1)
                                -> Materialize  (rows=1)
                                    -> Aggregate: max(c_sq1_alias1.pk), count(0)  (rows=1)
                                        -> Inner hash join (no condition)  (rows=1)
                                            -> Table scan on c_sq1_alias1  (rows=1)
                                            -> Hash
                                                -> Table scan on c_sq1_alias2  (rows=1)

DROP TABLE t1;
#
# Bug#30627570 WL#12885 SIG11 IN ITEM_SUBSELECT::PLACE() AT SQL/ITEM_SUBSELECT.H
# Solved by fix for Bug#30626975. Included here for completeness.
#
CREATE TABLE X (col_varchar_key VARCHAR(1));
SET OPTIMIZER_SWITCH='subquery_to_derived=on';
PREPARE prep_stmt FROM
'SELECT col_varchar_key
 FROM (SELECT * FROM X
       WHERE X.col_varchar_key > (SELECT MIN(col_varchar_key)
                                  FROM X)) AS table1';
EXECUTE prep_stmt;
col_varchar_key
DROP TABLE X;
#
# Bug#30632595 WL#12885 SIG11 IN SELECT_LEX::NEST_DERIVED() AT SQL/SQL_RESOLVER.CC
#
CREATE TABLE n(col_int INT);
INSERT INTO n VALUES (1), (2), (3);
ANALYZE TABLE n;
Table	Op	Msg_type	Msg_text
test.n	analyze	status	OK
SELECT alias2.col_int
FROM (SELECT * FROM n) AS alias1
JOIN
(SELECT * FROM n) AS alias2
JOIN n
ON alias2.col_int < (SELECT MAX(col_int) FROM n)
ON TRUE;
col_int
1
1
1
1
1
1
1
1
1
2
2
2
2
2
2
2
2
2
EXPLAIN SELECT alias2.col_int
FROM (SELECT * FROM n) AS alias1
JOIN
(SELECT * FROM n) AS alias2
JOIN n
ON alias2.col_int < (SELECT MAX(col_int) FROM n)
ON TRUE;
EXPLAIN
-> Inner hash join (no condition)  (rows=9)
    -> Inner hash join (no condition)  (rows=9)
        -> Table scan on n  (rows=3)
        -> Hash
            -> Table scan on n  (rows=3)
    -> Hash
        -> Inner hash join (no condition), extra conditions: (n.col_int < derived_1_4.`MAX(col_int)`)  (rows=1)
            -> Table scan on n  (rows=3)
            -> Hash
                -> Table scan on derived_1_4  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: max(n.col_int)  (rows=1)
                            -> Table scan on n  (rows=3)

DROP TABLE n;
#
# Bug#30644900 WL#12885 SIG 11 IN SELECT_LEX::TRANSFORM_SCALAR_SUBQUERIES_TO_DERIVED()
#
CREATE TABLE x(col_int_key INT);
# Don't transform this due to user variable (normally this would be ok,
# but here we get RAND_TABLE_BIT due to user variable here due because
# engine->uncacheable() sets UNCACHEABLE_RAND for user variable read, and
# not just assigment. And engine->uncacheable() propagates into
# used_tables as RAND_TABLE_BIT.
SELECT table1.col_int_key AS field1
FROM ((SELECT * FROM x
WHERE col_int_key <= (SELECT SUM(col_int_key)
FROM x
WHERE col_int_key < @var1)) AS table1
JOIN
x AS table2);
field1
EXPLAIN SELECT table1.col_int_key AS field1
FROM ((SELECT * FROM x
WHERE col_int_key <= (SELECT SUM(col_int_key)
FROM x
WHERE col_int_key < @var1)) AS table1
JOIN
x AS table2);
EXPLAIN
-> Filter: (x.col_int_key <= (select #3))  (rows=1)
    -> Inner hash join (no condition)  (rows=1)
        -> Table scan on x  (rows=1)
        -> Hash
            -> Table scan on table2  (rows=1)
    -> Select #3 (subquery in condition; uncacheable)
        -> Aggregate: sum(x.col_int_key)  (rows=1)
            -> Filter: (x.col_int_key < <cache>((@var1)))  (rows=0.333)
                -> Table scan on x  (rows=1)

# Transform if no user variable
SELECT table1.col_int_key AS field1
FROM ((SELECT * FROM x
WHERE col_int_key <= (SELECT SUM(col_int_key)
FROM x
WHERE col_int_key < 1)) AS table1
JOIN
x AS table2);
field1
EXPLAIN SELECT table1.col_int_key AS field1
FROM ((SELECT * FROM x
WHERE col_int_key <= (SELECT SUM(col_int_key)
FROM x
WHERE col_int_key < 1)) AS table1
JOIN
x AS table2);
EXPLAIN
-> Inner hash join (no condition)  (rows=0.333)
    -> Table scan on table2  (rows=1)
    -> Hash
        -> Inner hash join (no condition), extra conditions: (x.col_int_key <= derived_2_3.`SUM(col_int_key)`)  (rows=0.333)
            -> Table scan on x  (rows=1)
            -> Hash
                -> Table scan on derived_2_3  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: sum(x.col_int_key)  (rows=1)
                            -> Filter: (x.col_int_key < 1)  (rows=0.333)
                                -> Table scan on x  (rows=1)

DROP TABLE x;
#
# Bug#30645426 WL#12885: SIG11 AT TABLE_LIST::CREATE_MATERIALIZED_TABLE() AT SQL/TABLE.H
# Used to fails with -ps-protocol
#
CREATE TABLE t1(col_varchar VARCHAR(1));
SELECT (SELECT COUNT(*)
FROM t1
WHERE 1 <> table1.col_varchar)
FROM ((SELECT a2.*
FROM (t1 AS a1
JOIN
t1 AS a2
ON (1 <> ( SELECT COUNT(*)
FROM t1)))) AS table1
JOIN
t1
ON 1);
(SELECT COUNT(*)
FROM t1
WHERE 1 <> table1.col_varchar)
EXPLAIN SELECT (SELECT COUNT(*)
FROM t1
WHERE 1 <> table1.col_varchar)
FROM ((SELECT a2.*
FROM (t1 AS a1
JOIN
t1 AS a2
ON (1 <> ( SELECT COUNT(*)
FROM t1)))) AS table1
JOIN
t1
ON 1);
EXPLAIN
-> Inner hash join (no condition)  (rows=1)
    -> Inner hash join (no condition)  (rows=1)
        -> Inner hash join (no condition)  (rows=1)
            -> Table scan on a1  (rows=1)
            -> Hash
                -> Table scan on derived_3_4  (rows=1)
                    -> Materialize  (rows=1)
                        -> Filter: (1 <> count(0))  (rows=1)
                            -> Aggregate: count(0)  (rows=1)
                                -> Table scan on t1  (rows=1)
        -> Hash
            -> Table scan on a2  (rows=1)
    -> Hash
        -> Table scan on t1  (rows=1)
-> Select #2 (subquery in projection; dependent)
    -> Aggregate: count(0)  (rows=1)
        -> Filter: (1 <> a2.col_varchar)  (rows=1)
            -> Table scan on t1  (rows=1)

Warnings:
Note	1276	Field or reference 'table1.col_varchar' of SELECT #2 was resolved in SELECT #1
DROP TABLE t1;
#
# Bug#30650326 WL#12885 SIG 11 IN ADD_KEY_FIELDS() AT SQL/SQL_OPTIMIZER.CC
#
CREATE TABLE a(i INT);
CREATE TABLE b(i INT);
CREATE TABLE c(i INT);
SELECT *
FROM b
WHERE EXISTS (SELECT *
FROM (b
JOIN
(a AS sq1_alias2
JOIN
c
ON (sq1_alias2.i >= (SELECT MAX(i)
FROM b)))
ON (6 IN (SELECT i
FROM b))));
i
EXPLAIN SELECT *
FROM b
WHERE EXISTS (SELECT *
FROM (b
JOIN
(a AS sq1_alias2
JOIN
c
ON (sq1_alias2.i >= (SELECT MAX(i)
FROM b)))
ON (6 IN (SELECT i
FROM b))));
EXPLAIN
-> Filter: <cache>(<in_optimizer>(6,<exists>(select #4)))  (rows=0.333)
    -> Nested loop inner join (FirstMatch)  (rows=0.333)
        -> Limit: 1 row(s)  (rows=0.333)
            -> Inner hash join (no condition)  (rows=0.333)
                -> Table scan on c  (rows=1)
                -> Hash
                    -> Inner hash join (no condition)  (rows=0.333)
                        -> Table scan on b  (rows=1)
                        -> Hash
                            -> Filter: (sq1_alias2.i >= (select #3))  (rows=0.333)
                                -> Table scan on sq1_alias2  (rows=1)
                                -> Select #3 (subquery in condition; run only once)
                                    -> Aggregate: max(b.i)  (rows=1)
                                        -> Table scan on b  (rows=1)
        -> Table scan on b  (rows=1)
    -> Select #4 (subquery in condition; run only once)
        -> Filter: (b.i = <cache>(6))  (rows=0.1)
            -> Table scan on b  (rows=1)

DROP TABLE a, b, c;
#
# Bug#30727021 WL#12885 SIG 6 IN SELECT_LEX::SYNTHESIZE_DERIVED() AT SQL/SQL_RESOLVER.CC
# Update: ANY/ALL/SOME rejected.
CREATE TABLE n(i INT);
SELECT (SELECT AVG(n.i)
FROM n) AS feild1,
SUM(table1.i)
FROM (n AS table1
JOIN
n AS table2
ON (table1.i <= ANY (SELECT i FROM n)))
WHERE (EXISTS ((SELECT i FROM n)));
feild1	SUM(table1.i)
NULL	NULL
EXPLAIN SELECT (SELECT AVG(n.i)
FROM n) AS feild1,
SUM(table1.i)
FROM (n AS table1
JOIN
n AS table2
ON (table1.i <= ANY (SELECT i FROM n)))
WHERE (EXISTS ((SELECT i FROM n)));
EXPLAIN
-> Aggregate: sum(table1.i)  (rows=1)
    -> Nested loop inner join (FirstMatch)  (rows=0.667)
        -> Limit: 1 row(s)  (rows=1)
            -> Table scan on n  (rows=1)
        -> Inner hash join (no condition)  (rows=0.667)
            -> Table scan on table2  (rows=1)
            -> Hash
                -> Filter: <nop>((table1.i <= (select #3)))  (rows=0.667)
                    -> Table scan on table1  (rows=1)
                    -> Select #3 (subquery in condition; run only once)
                        -> Aggregate: max(n.i)  (rows=1)
                            -> Table scan on n  (rows=1)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: avg(n.i)  (rows=1)
        -> Table scan on n  (rows=1)

These (manually transformed the ANY) trigger the error as
well. Semi-join complication.
SELECT (SELECT AVG(n.i)
FROM n) AS feild1,
SUM(table1.i)
FROM (n AS table1
JOIN
n AS table2
ON (table1.i <= (select MAX(`n`.`i`) from `n`)))
WHERE (EXISTS ((SELECT i FROM n)));
feild1	SUM(table1.i)
NULL	NULL
EXPLAIN SELECT (SELECT AVG(n.i)
FROM n) AS feild1,
SUM(table1.i)
FROM (n AS table1
JOIN
n AS table2
ON (table1.i <= (select MAX(`n`.`i`) from `n`)))
WHERE (EXISTS ((SELECT i FROM n)));
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_5  (rows=1)
        -> Materialize  (rows=1)
            -> Aggregate: sum(table1.i)  (rows=1)
                -> Nested loop inner join (FirstMatch)  (rows=0.333)
                    -> Limit: 1 row(s)  (rows=1)
                        -> Table scan on n  (rows=1)
                    -> Inner hash join (no condition)  (rows=0.333)
                        -> Table scan on table2  (rows=1)
                        -> Hash
                            -> Inner hash join (no condition), extra conditions: (table1.i <= derived_5_7.`MAX(``n``.``i``)`)  (rows=0.333)
                                -> Table scan on table1  (rows=1)
                                -> Hash
                                    -> Table scan on derived_5_7  (rows=1)
                                        -> Materialize  (rows=1)
                                            -> Aggregate: max(n.i)  (rows=1)
                                                -> Table scan on n  (rows=1)
    -> Hash
        -> Table scan on derived_1_8  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: avg(n.i)  (rows=1)
                    -> Table scan on n  (rows=1)

SELECT (SELECT AVG(n.i)
FROM n) AS feild1,
SUM(table1.i)
FROM (n AS table1
JOIN
n AS table2
ON  (table2.i <=  (select MAX(`n`.`i`) from `n`)))
JOIN
n AS table3
ON (table1.i <=  (select MAX(`n`.`i`) from `n`))
WHERE (EXISTS ((SELECT i FROM n)) AND
EXISTS ((SELECT i FROM n WHERE i = 5)) AND
EXISTS ((SELECT i FROM n WHERE i = 7)));
feild1	SUM(table1.i)
NULL	NULL
EXPLAIN SELECT (SELECT AVG(n.i)
FROM n) AS feild1,
SUM(table1.i)
FROM (n AS table1
JOIN
n AS table2
ON  (table2.i <=  (select MAX(`n`.`i`) from `n`)))
JOIN
n AS table3
ON (table1.i <=  (select MAX(`n`.`i`) from `n`))
WHERE (EXISTS ((SELECT i FROM n)) AND
EXISTS ((SELECT i FROM n WHERE i = 5)) AND
EXISTS ((SELECT i FROM n WHERE i = 7)));
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_8  (rows=1)
        -> Materialize  (rows=1)
            -> Aggregate: sum(table1.i)  (rows=1)
                -> Inner hash join (FirstMatch) (no condition)  (rows=0.00111)
                    -> Limit: 1 row(s)  (rows=0.1)
                        -> Filter: (n.i = 7)  (rows=0.1)
                            -> Table scan on n  (rows=1)
                    -> Hash
                        -> Nested loop inner join (FirstMatch)  (rows=0.0111)
                            -> Limit: 1 row(s)  (rows=0.1)
                                -> Filter: (n.i = 5)  (rows=0.1)
                                    -> Table scan on n  (rows=1)
                            -> Nested loop inner join (FirstMatch)  (rows=0.111)
                                -> Limit: 1 row(s)  (rows=1)
                                    -> Table scan on n  (rows=1)
                                -> Inner hash join (no condition)  (rows=0.111)
                                    -> Inner hash join (no condition)  (rows=0.333)
                                        -> Table scan on table3  (rows=1)
                                        -> Hash
                                            -> Inner hash join (no condition), extra conditions: (table1.i <= derived_8_12.`MAX(``n``.``i``)`)  (rows=0.333)
                                                -> Table scan on table1  (rows=1)
                                                -> Hash
                                                    -> Table scan on derived_8_12  (rows=1)
                                                        -> Materialize  (rows=1)
                                                            -> Aggregate: max(n.i)  (rows=1)
                                                                -> Table scan on n  (rows=1)
                                    -> Hash
                                        -> Inner hash join (no condition), extra conditions: (table2.i <= derived_8_13.`MAX(``n``.``i``)`)  (rows=0.333)
                                            -> Table scan on derived_8_13  (rows=1)
                                                -> Materialize  (rows=1)
                                                    -> Aggregate: max(n.i)  (rows=1)
                                                        -> Table scan on n  (rows=1)
                                            -> Hash
                                                -> Table scan on table2  (rows=1)
    -> Hash
        -> Table scan on derived_1_14  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: avg(n.i)  (rows=1)
                    -> Table scan on n  (rows=1)

Follow-up fix.
Anti-join complication.
SELECT (SELECT AVG(n.i)
FROM n) AS feild1,
SUM(table1.i)
FROM (n AS table1
JOIN
n AS table2
ON (table2.i <= (select MAX(`n`.`i`) from `n`)))
JOIN n AS table3
ON (table1.i <= (select MAX(`n`.`i`) from `n`))
WHERE (NOT EXISTS ((SELECT n1.i
FROM n n1, n n2
WHERE n1.i > n2.i)));
feild1	SUM(table1.i)
NULL	NULL
EXPLAIN SELECT (SELECT AVG(n.i)
FROM n) AS feild1,
SUM(table1.i)
FROM (n AS table1
JOIN
n AS table2
ON (table2.i <= (select MAX(`n`.`i`) from `n`)))
JOIN n AS table3
ON (table1.i <= (select MAX(`n`.`i`) from `n`))
WHERE (NOT EXISTS ((SELECT n1.i
FROM n n1, n n2
WHERE n1.i > n2.i)));
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_6  (rows=1)
        -> Materialize  (rows=1)
            -> Aggregate: sum(table1.i)  (rows=1)
                -> Filter: (derived_6_7.Name_exp_1 is null)  (rows=0.0111)
                    -> Left hash join (no condition)  (rows=0.111)
                        -> Inner hash join (no condition)  (rows=0.111)
                            -> Inner hash join (no condition)  (rows=0.333)
                                -> Table scan on table3  (rows=1)
                                -> Hash
                                    -> Inner hash join (no condition), extra conditions: (table1.i <= derived_6_8.`MAX(``n``.``i``)`)  (rows=0.333)
                                        -> Table scan on table1  (rows=1)
                                        -> Hash
                                            -> Table scan on derived_6_8  (rows=1)
                                                -> Materialize  (rows=1)
                                                    -> Aggregate: max(n.i)  (rows=1)
                                                        -> Table scan on n  (rows=1)
                            -> Hash
                                -> Inner hash join (no condition), extra conditions: (table2.i <= derived_6_9.`MAX(``n``.``i``)`)  (rows=0.333)
                                    -> Table scan on derived_6_9  (rows=1)
                                        -> Materialize  (rows=1)
                                            -> Aggregate: max(n.i)  (rows=1)
                                                -> Table scan on n  (rows=1)
                                    -> Hash
                                        -> Table scan on table2  (rows=1)
                        -> Hash
                            -> Table scan on derived_6_7  (rows=0.333)
                                -> Materialize  (rows=0.333)
                                    -> Limit: 1 row(s)  (rows=0.333)
                                        -> Inner hash join (no condition), extra conditions: (n1.i > n2.i)  (rows=0.333)
                                            -> Table scan on n1  (rows=1)
                                            -> Hash
                                                -> Table scan on n2  (rows=1)
    -> Hash
        -> Table scan on derived_1_10  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: avg(n.i)  (rows=1)
                    -> Table scan on n  (rows=1)

SELECT (SELECT AVG(n.i)
FROM n) AS feild1,
SUM(table1.i)
FROM (n AS table1
JOIN
n AS table2
ON  (table2.i <=  (select MAX(`n`.`i`) from `n`)))
JOIN
n AS table3
ON (table1.i <=  (select MAX(`n`.`i`) from `n`))
WHERE (EXISTS ((SELECT i FROM n)) AND
EXISTS ((SELECT i FROM n WHERE i = 5)) AND
EXISTS ((SELECT i FROM n WHERE i = 7)));
feild1	SUM(table1.i)
NULL	NULL
EXPLAIN SELECT (SELECT AVG(n.i)
FROM n) AS feild1,
SUM(table1.i)
FROM (n AS table1
JOIN
n AS table2
ON  (table2.i <=  (select MAX(`n`.`i`) from `n`)))
JOIN
n AS table3
ON (table1.i <=  (select MAX(`n`.`i`) from `n`))
WHERE (EXISTS ((SELECT i FROM n)) AND
EXISTS ((SELECT i FROM n WHERE i = 5)) AND
EXISTS ((SELECT i FROM n WHERE i = 7)));
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_8  (rows=1)
        -> Materialize  (rows=1)
            -> Aggregate: sum(table1.i)  (rows=1)
                -> Inner hash join (FirstMatch) (no condition)  (rows=0.00111)
                    -> Limit: 1 row(s)  (rows=0.1)
                        -> Filter: (n.i = 7)  (rows=0.1)
                            -> Table scan on n  (rows=1)
                    -> Hash
                        -> Nested loop inner join (FirstMatch)  (rows=0.0111)
                            -> Limit: 1 row(s)  (rows=0.1)
                                -> Filter: (n.i = 5)  (rows=0.1)
                                    -> Table scan on n  (rows=1)
                            -> Nested loop inner join (FirstMatch)  (rows=0.111)
                                -> Limit: 1 row(s)  (rows=1)
                                    -> Table scan on n  (rows=1)
                                -> Inner hash join (no condition)  (rows=0.111)
                                    -> Inner hash join (no condition)  (rows=0.333)
                                        -> Table scan on table3  (rows=1)
                                        -> Hash
                                            -> Inner hash join (no condition), extra conditions: (table1.i <= derived_8_12.`MAX(``n``.``i``)`)  (rows=0.333)
                                                -> Table scan on table1  (rows=1)
                                                -> Hash
                                                    -> Table scan on derived_8_12  (rows=1)
                                                        -> Materialize  (rows=1)
                                                            -> Aggregate: max(n.i)  (rows=1)
                                                                -> Table scan on n  (rows=1)
                                    -> Hash
                                        -> Inner hash join (no condition), extra conditions: (table2.i <= derived_8_13.`MAX(``n``.``i``)`)  (rows=0.333)
                                            -> Table scan on derived_8_13  (rows=1)
                                                -> Materialize  (rows=1)
                                                    -> Aggregate: max(n.i)  (rows=1)
                                                        -> Table scan on n  (rows=1)
                                            -> Hash
                                                -> Table scan on table2  (rows=1)
    -> Hash
        -> Table scan on derived_1_14  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: avg(n.i)  (rows=1)
                    -> Table scan on n  (rows=1)

Mix of semi-join and anti-join complications.
SELECT (SELECT AVG(n.i)
FROM n) AS feild1,
SUM(table1.i)
FROM (n AS table1
JOIN n AS table2
ON  (table2.i <=  (select MAX(`n`.`i`) from `n`)))
JOIN n AS table3
ON (table1.i <=  (select MAX(`n`.`i`) from `n`))
WHERE (EXISTS ((SELECT i FROM n)) AND
NOT EXISTS ((SELECT i FROM n WHERE i = 4)) AND
EXISTS ((SELECT i FROM n WHERE i = 5)) AND
EXISTS ((SELECT i FROM n WHERE i = 7)) AND
NOT EXISTS ((SELECT i FROM n WHERE i = 3)));
feild1	SUM(table1.i)
NULL	NULL
EXPLAIN SELECT (SELECT AVG(n.i)
FROM n) AS feild1,
SUM(table1.i)
FROM (n AS table1
JOIN n AS table2
ON  (table2.i <=  (select MAX(`n`.`i`) from `n`)))
JOIN n AS table3
ON (table1.i <=  (select MAX(`n`.`i`) from `n`))
WHERE (EXISTS ((SELECT i FROM n)) AND
NOT EXISTS ((SELECT i FROM n WHERE i = 4)) AND
EXISTS ((SELECT i FROM n WHERE i = 5)) AND
EXISTS ((SELECT i FROM n WHERE i = 7)) AND
NOT EXISTS ((SELECT i FROM n WHERE i = 3)));
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_10  (rows=1)
        -> Materialize  (rows=1)
            -> Aggregate: sum(table1.i)  (rows=1)
                -> Inner hash join (FirstMatch) (no condition)  (rows=11.1e-6)
                    -> Limit: 1 row(s)  (rows=0.1)
                        -> Filter: (n.i = 7)  (rows=0.1)
                            -> Table scan on n  (rows=1)
                    -> Hash
                        -> Inner hash join (FirstMatch) (no condition)  (rows=111e-6)
                            -> Limit: 1 row(s)  (rows=0.1)
                                -> Filter: (n.i = 5)  (rows=0.1)
                                    -> Table scan on n  (rows=1)
                            -> Hash
                                -> Inner hash join (FirstMatch) (no condition)  (rows=0.00111)
                                    -> Limit: 1 row(s)  (rows=1)
                                        -> Table scan on n  (rows=1)
                                    -> Hash
                                        -> Filter: (derived_10_11.Name_exp_1 is null)  (rows=0.00111)
                                            -> Left hash join (no condition)  (rows=0.0111)
                                                -> Filter: (derived_10_14.Name_exp_1 is null)  (rows=0.0111)
                                                    -> Left hash join (no condition)  (rows=0.111)
                                                        -> Inner hash join (no condition)  (rows=0.111)
                                                            -> Inner hash join (no condition)  (rows=0.333)
                                                                -> Table scan on table3  (rows=1)
                                                                -> Hash
                                                                    -> Inner hash join (no condition), extra conditions: (table1.i <= derived_10_16.`MAX(``n``.``i``)`)  (rows=0.333)
                                                                        -> Table scan on table1  (rows=1)
                                                                        -> Hash
                                                                            -> Table scan on derived_10_16  (rows=1)
                                                                                -> Materialize  (rows=1)
                                                                                    -> Aggregate: max(n.i)  (rows=1)
                                                                                        -> Table scan on n  (rows=1)
                                                            -> Hash
                                                                -> Inner hash join (no condition), extra conditions: (table2.i <= derived_10_17.`MAX(``n``.``i``)`)  (rows=0.333)
                                                                    -> Table scan on derived_10_17  (rows=1)
                                                                        -> Materialize  (rows=1)
                                                                            -> Aggregate: max(n.i)  (rows=1)
                                                                                -> Table scan on n  (rows=1)
                                                                    -> Hash
                                                                        -> Table scan on table2  (rows=1)
                                                        -> Hash
                                                            -> Table scan on derived_10_14  (rows=0.1)
                                                                -> Materialize  (rows=0.1)
                                                                    -> Limit: 1 row(s)  (rows=0.1)
                                                                        -> Filter: (n.i = 4)  (rows=0.1)
                                                                            -> Table scan on n  (rows=1)
                                                -> Hash
                                                    -> Table scan on derived_10_11  (rows=0.1)
                                                        -> Materialize  (rows=0.1)
                                                            -> Limit: 1 row(s)  (rows=0.1)
                                                                -> Filter: (n.i = 3)  (rows=0.1)
                                                                    -> Table scan on n  (rows=1)
    -> Hash
        -> Table scan on derived_1_18  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: avg(n.i)  (rows=1)
                    -> Table scan on n  (rows=1)

DROP TABLE n;
#
# Bug#30736475 WL#12885 SIG 6 IN ITEM_FIELD::FIX_FIELDS() AT SQL/ITEM.CC
#
CREATE TABLE m(pk INT);
CREATE VIEW view_m AS SELECT * FROM m;
PREPARE prep_stmt FROM
'SELECT (SELECT t2.pk FROM (m AS t1
                            JOIN
                            (m AS t2
                             JOIN m AS t3))),
        (SELECT SUM(pk) FROM m),
        MIN(table1.pk)
 FROM (m AS table1 JOIN
       ((view_m AS table2
         JOIN
         m AS table3))
       ON (table3.pk = table2.pk))';
EXECUTE prep_stmt;
(SELECT t2.pk FROM (m AS t1
                            JOIN
                            (m AS t2
                             JOIN m AS t3)))	(SELECT SUM(pk) FROM m)	MIN(table1.pk)
NULL	NULL	NULL
DROP VIEW view_m;
DROP TABLE m;
#
# Bug#30757306 WL#12885: ASSERTION `FILE' FAILED AT TABLE::SET_KEYREAD
# Issue with INSERT, tweaks the from list which is transformed by us.
CREATE TABLE t1(field1 INT, field2 VARCHAR(1));
SET optimizer_switch='subquery_to_derived=on';
CREATE TABLE cc1(pk INT NOT NULL,
col_varchar_key VARCHAR(1) DEFAULT NULL,
PRIMARY KEY (pk));
SELECT COUNT(table1.pk),
(SELECT MIN(col_varchar_key) FROM cc1 )
FROM (cc1 AS table1
JOIN (cc1 JOIN
cc1 AS table3
ON true)
ON true)
WHERE (1 <> (SELECT COUNT(*) FROM cc1));
COUNT(table1.pk)	(SELECT MIN(col_varchar_key) FROM cc1 )
0	NULL
EXPLAIN SELECT COUNT(table1.pk),
(SELECT MIN(col_varchar_key) FROM cc1 )
FROM (cc1 AS table1
JOIN (cc1 JOIN
cc1 AS table3
ON true)
ON true)
WHERE (1 <> (SELECT COUNT(*) FROM cc1));
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_4  (rows=1)
        -> Materialize  (rows=1)
            -> Aggregate: count(table1.pk)  (rows=1)
                -> Inner hash join (no condition)  (rows=1)
                    -> Inner hash join (no condition)  (rows=1)
                        -> Inner hash join (no condition)  (rows=1)
                            -> Table scan on table1  (rows=1)
                            -> Hash
                                -> Table scan on cc1  (rows=1)
                        -> Hash
                            -> Table scan on table3  (rows=1)
                    -> Hash
                        -> Table scan on derived_4_5  (rows=1)
                            -> Materialize  (rows=1)
                                -> Filter: (1 <> count(0))  (rows=1)
                                    -> Aggregate: count(0)  (rows=1)
                                        -> Table scan on cc1  (rows=1)
    -> Hash
        -> Table scan on derived_1_6  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(cc1.col_varchar_key)  (rows=1)
                    -> Table scan on cc1  (rows=1)

INSERT INTO t1 SELECT COUNT(table1.pk),
(SELECT MIN(col_varchar_key) FROM cc1 )
FROM (cc1 AS table1
JOIN (cc1 JOIN
cc1 AS table3
ON true)
ON true)
WHERE (1 <> (SELECT COUNT(*) FROM cc1));
EXPLAIN INSERT INTO t1 SELECT COUNT(table1.pk),
(SELECT MIN(col_varchar_key) FROM cc1 )
FROM (cc1 AS table1
JOIN (cc1 JOIN
cc1 AS table3
ON true)
ON true)
WHERE (1 <> (SELECT COUNT(*) FROM cc1));
EXPLAIN
-> Insert into t1
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on derived_1_4  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: count(table1.pk)  (rows=1)
                    -> Inner hash join (no condition)  (rows=1)
                        -> Inner hash join (no condition)  (rows=1)
                            -> Inner hash join (no condition)  (rows=1)
                                -> Table scan on table1  (rows=1)
                                -> Hash
                                    -> Table scan on cc1  (rows=1)
                            -> Hash
                                -> Table scan on table3  (rows=1)
                        -> Hash
                            -> Table scan on derived_4_5  (rows=1)
                                -> Materialize  (rows=1)
                                    -> Filter: (1 <> count(0))  (rows=1)
                                        -> Aggregate: count(0)  (rows=1)
                                            -> Table scan on cc1  (rows=1)
        -> Hash
            -> Table scan on derived_1_6  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: min(cc1.col_varchar_key)  (rows=1)
                        -> Table scan on cc1  (rows=1)

SELECT * from t1;
field1	field2
0	NULL
Test this also for coverage
CREATE TABLE t2 AS SELECT COUNT(table1.pk),
(SELECT MIN(col_varchar_key) FROM cc1 )
FROM (cc1 AS table1
JOIN (cc1 JOIN
cc1 AS table3
ON true)
ON true)
WHERE (1 <> (SELECT COUNT(*) FROM cc1));
DROP TABLE t1, cc1, t2;
#
# Bug#30755759 WL#12885 SIG6 IN HASH_JOIN_BUFFER::STOREFROMTABLEBUFFERS()
# Issue with covering indexes.
#
CREATE TABLE a (
pk INTEGER
);
CREATE TABLE bb (
col_varchar VARCHAR(1)
);
CREATE TABLE cc (
pk INTEGER,
col_int INTEGER,
col_int_key INTEGER,
col_time TIME,
col_time_key TIME,
col_datetime DATETIME,
col_datetime_key DATETIME,
col_varchar VARCHAR(1),
col_varchar_key VARCHAR(1),
PRIMARY KEY (pk)
);
CREATE INDEX idx_cc_col_varchar_key ON cc(col_varchar_key);
INSERT INTO cc VALUES (1,764578610,1400450503,'04:58:13','15:43:36',
'1977-07-20 14:44:30','1998-10-04 17:29:04','0','N');
INSERT INTO cc VALUES (2,-1430323290,761341340,'17:39:46','10:22:47',
'2027-06-26 01:50:30','1983-11-11 03:33:36','z','a');
ANALYZE TABLE a, bb, cc;
Table	Op	Msg_type	Msg_text
test.a	analyze	status	OK
test.bb	analyze	status	OK
test.cc	analyze	status	OK
Without the patch this plan would use an index scan on cc, but this
is not covering.
EXPLAIN FORMAT=tree
SELECT
AVG(cc.col_varchar_key),
(
SELECT SUM(cc.col_int_key)
FROM cc,a
)
FROM cc STRAIGHT_JOIN bb ON bb.col_varchar = cc.col_varchar_key
WHERE cc.col_varchar <> 'w';
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_3  (rows=1)
        -> Materialize  (rows=1)
            -> Aggregate: avg(cc.col_varchar_key)  (rows=1)
                -> Inner hash join (bb.col_varchar = cc.col_varchar_key)  (rows=0.9)
                    -> Table scan on bb  (rows=1)
                    -> Hash
                        -> Filter: (cc.col_varchar <> 'w')  (rows=1.8)
                            -> Table scan on cc  (rows=2)
    -> Hash
        -> Table scan on derived_1_4  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: sum(cc.col_int_key)  (rows=1)
                    -> Inner hash join (no condition)  (rows=2)
                        -> Table scan on cc  (rows=2)
                        -> Hash
                            -> Table scan on a  (rows=1)

DROP TABLE a, bb, cc;
#
# Bug#30774730 WL#12885 SIG 6 IN ITEM_FIELD::FIX_FIELDS() AT SQL/ITEM.CC
#
CREATE TABLE n(i INT);
CREATE VIEW view_n AS SELECT * FROM n;
PREPARE p FROM
'SELECT (SELECT MAX(i) FROM n) AS field2,
        COUNT(table1.i) AS field3 ,
        (SELECT AVG(i) FROM n) AS field4
 FROM (n AS table1
       JOIN
       ( view_n AS table2
         JOIN
         n AS table3
         ON true )
       ON (table2.i = table2.i))';
EXECUTE p;
field2	field3	field4
NULL	0	NULL
DROP VIEW view_n;
DROP TABLE n;
#
# Bug#30775902 WL#12885 SIG6 IN HASH_JOIN_BUFFER::STOREFROMTABLEBUFFERS()
#
CREATE TABLE cc (
pk int NOT NULL AUTO_INCREMENT,
col_int int DEFAULT NULL,
col_int_key int DEFAULT NULL,
col_varchar varchar(1) DEFAULT NULL,
col_varchar_key varchar(1) DEFAULT NULL,
PRIMARY KEY (pk),
KEY idx_cc_col_int_key (col_int_key),
KEY idx_cc_col_varchar_key (col_varchar_key)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
INSERT INTO cc VALUES
(1,   1375472775,   262188886, 'I', 'b'),
(2,  -1851648474,   130471446, 'o', '7'),
(3,    503688873,   259988235, 'L', 't'),
(4,    995143874,   -60832670, 'Q', 'K'),
(5,  -1440599261, -1669741488, 'k', '7'),
(6,  -1534014276,  1760407196, 'c', 'Z'),
(7,    808084535,   311457905, 'B', 'j'),
(8,    731883185,  -571871645, 'd', 'm'),
(9,   1445888442,  1903365311, 'w', 's'),
(10,   222313615,  -404576744, 'n', 'V'),
(11, -1320350569, -1496644593, 'y', 'o'),
(12,  2033205532,  1376480867, 'x', '4'),
(13,  -101883317,  -857422791, 'A', '7'),
(14,   867688302,  1410896813, 'J', 'c'),
(15, -1961088920, -2019664999, 'v', '1'),
(16, -1850585486, -1872043099, '1', 'o'),
(17,  -603486188,   901895823, 'G', 'q'),
(18, -1381157785, -1613624643, 'Z', 'E'),
(19,  -270976631,   288433409, 'r', 'Z'),
(20,  2113722977,   409698731, 'n', 'd');
ANALYZE TABLE cc;
Table	Op	Msg_type	Msg_text
test.cc	analyze	status	OK
CREATE VIEW view_cc AS SELECT * FROM cc;
SELECT AVG(table2.col_int) AS field1 ,
( SELECT COUNT(subquery1_t1.col_varchar_key ) AS subquery1_field1
FROM ( cc AS subquery1_t1
LEFT OUTER JOIN
( cc AS subquery1_t2
INNER JOIN view_cc AS subquery1_t3
ON ( subquery1_t3.col_varchar = subquery1_t2.col_varchar_key ) )
ON ( subquery1_t3.col_int_key = subquery1_t2.col_int  ) )
WHERE subquery1_t1.col_varchar_key != subquery1_t2.col_varchar ) AS field2
FROM ( cc AS table1
STRAIGHT_JOIN
cc AS table2
ON ( table1.col_varchar_key = table1.col_varchar_key ) )
WHERE ( table1.pk = 1 ) AND
( table1.col_varchar_key = 'D' OR
table1.col_varchar_key = table1.col_varchar_key) OR
table1.col_varchar_key < 'O'
ORDER BY table1.col_varchar ASC, field2, field1
LIMIT 1000 OFFSET 2;
field1	field2
EXPLAIN FORMAT=tree SELECT AVG(table2.col_int) AS field1 ,
( SELECT COUNT(subquery1_t1.col_varchar_key ) AS subquery1_field1
FROM ( cc AS subquery1_t1
LEFT OUTER JOIN
( cc AS subquery1_t2
INNER JOIN view_cc AS subquery1_t3
ON ( subquery1_t3.col_varchar = subquery1_t2.col_varchar_key ) )
ON ( subquery1_t3.col_int_key = subquery1_t2.col_int  ) )
WHERE subquery1_t1.col_varchar_key != subquery1_t2.col_varchar ) AS field2
FROM ( cc AS table1
STRAIGHT_JOIN
cc AS table2
ON ( table1.col_varchar_key = table1.col_varchar_key ) )
WHERE ( table1.pk = 1 ) AND
( table1.col_varchar_key = 'D' OR
table1.col_varchar_key = table1.col_varchar_key) OR
table1.col_varchar_key < 'O'
ORDER BY table1.col_varchar ASC, field2, field1
LIMIT 1000 OFFSET 2;
EXPLAIN
-> Limit/Offset: 1000/2 row(s)  (rows=0)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on derived_1_4  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: avg(table2.col_int)  (rows=1)
                    -> Inner hash join (no condition)  (rows=16.2)
                        -> Table scan on table2  (rows=20)
                        -> Hash
                            -> Filter: ((table1.col_varchar_key = table1.col_varchar_key) and ((((table1.col_varchar_key = 'D') or (table1.col_varchar_key = table1.col_varchar_key)) and (table1.pk = 1)) or (table1.col_varchar_key < 'O')))  (rows=0.812)
                                -> Table scan on table1  (rows=20)
        -> Hash
            -> Table scan on derived_1_5  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: count(subquery1_t1.col_varchar_key)  (rows=1)
                        -> Inner hash join (no condition), extra conditions: (subquery1_t1.col_varchar_key <> subquery1_t2.col_varchar)  (rows=23.4)
                            -> Table scan on subquery1_t1  (rows=20)
                            -> Hash
                                -> Inner hash join (cc.col_int_key = subquery1_t2.col_int), (cc.col_varchar = subquery1_t2.col_varchar_key)  (rows=1.25)
                                    -> Table scan on subquery1_t2  (rows=20)
                                    -> Hash
                                        -> Table scan on cc  (rows=20)

DROP VIEW view_cc;
DROP TABLE cc;
#
# Bug#30781925 WL#12885 SIG11 IN GETITERATORFORDERIVEDTABLE() AT SQL/SQL_EXECUTOR.CC
#
CREATE TABLE m(col_int INT);
SELECT MIN(table1.col_int)           AS field1,
( SELECT COUNT(col_int )
FROM m AS t1 )              AS field2,
AVG(table1.col_int)           AS field4,
( SELECT MAX(t1.col_int)
FROM ( m AS t1 JOIN
( m AS t2
JOIN
m AS t3 ) ) ) AS field5
FROM ( m AS table1
JOIN ( ( m AS table2
JOIN
( SELECT COUNT(col_int) FROM m ) AS table3 ) ) ) ;
field1	field2	field4	field5
NULL	0	NULL	NULL
DROP TABLE m;
#
# Bug#30786714 WL#12885 SIG6 IN ITEM_FIELD::FIX_FIELDS() AT SQL/ITEM.CC
#
# Missing treatment of view references in transformed block by
# transform_grouped_to_derived, e.g. table2.col_int below.
# Used to fail with prepared statement
CREATE TABLE n(col_int INT);
INSERT INTO n VALUES (1), (2), (3);
ANALYZE TABLE n;
Table	Op	Msg_type	Msg_text
test.n	analyze	status	OK
CREATE VIEW view_n AS SELECT * FROM n;
SET sql_mode="";
SELECT table_b.col_int               AS field_a,
(SELECT MAX(col_int) FROM n)  AS field_b,
COUNT(table_a.col_int)        AS field_c,
(SELECT AVG(col_int) FROM n)  AS field_d
FROM ( n AS table_a
JOIN ( view_n AS table_b
JOIN n AS table_c) );
field_a	field_b	field_c	field_d
1	1	27	2.0000
EXPLAIN SELECT table_b.col_int               AS field_a,
(SELECT MAX(col_int) FROM n)  AS field_b,
COUNT(table_a.col_int)        AS field_c,
(SELECT AVG(col_int) FROM n)  AS field_d
FROM ( n AS table_a
JOIN ( view_n AS table_b
JOIN n AS table_c) );
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on derived_1_5  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: count(table_a.col_int)  (rows=1)
                    -> Inner hash join (no condition)  (rows=27)
                        -> Inner hash join (no condition)  (rows=9)
                            -> Table scan on table_a  (rows=3)
                            -> Hash
                                -> Table scan on n  (rows=3)
                        -> Hash
                            -> Table scan on table_c  (rows=3)
        -> Hash
            -> Table scan on derived_1_7  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(n.col_int)  (rows=1)
                        -> Table scan on n  (rows=3)
    -> Hash
        -> Table scan on derived_1_6  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: avg(n.col_int)  (rows=1)
                    -> Table scan on n  (rows=3)

DROP VIEW view_n;
CREATE VIEW view_n(col_int2) AS SELECT col_int + 1 FROM n;
More complex view featuring an expression and now also
two equivalent view references
SELECT  table_b.col_int2              AS field_e,
table_a.col_int,
(SELECT MAX(col_int) FROM n)  AS field_a,
COUNT(table_a.col_int )       AS field_b,
(SELECT AVG(col_int) FROM n)  AS field_c,
table_b.col_int2              AS field_d
FROM ( n AS table_a
JOIN (view_n AS table_b
JOIN n AS table_c) );
field_e	col_int	field_a	field_b	field_c	field_d
2	1	3	27	2.0000	2
EXPLAIN SELECT  table_b.col_int2              AS field_e,
table_a.col_int,
(SELECT MAX(col_int) FROM n)  AS field_a,
COUNT(table_a.col_int )       AS field_b,
(SELECT AVG(col_int) FROM n)  AS field_c,
table_b.col_int2              AS field_d
FROM ( n AS table_a
JOIN (view_n AS table_b
JOIN n AS table_c) );
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on derived_1_5  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: count(table_a.col_int)  (rows=1)
                    -> Inner hash join (no condition)  (rows=27)
                        -> Inner hash join (no condition)  (rows=9)
                            -> Table scan on table_a  (rows=3)
                            -> Hash
                                -> Table scan on n  (rows=3)
                        -> Hash
                            -> Table scan on table_c  (rows=3)
        -> Hash
            -> Table scan on derived_1_7  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(n.col_int)  (rows=1)
                        -> Table scan on n  (rows=3)
    -> Hash
        -> Table scan on derived_1_6  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: avg(n.col_int)  (rows=1)
                    -> Table scan on n  (rows=3)

SET sql_mode=default;
DROP VIEW view_n;
DROP TABLE n;
#
# Bug#30786266 WL#12885 SIG 6 IN QEP_TAB::PUSH_INDEX_COND() AT SQL/SQL_SELECT.CC
#
CREATE TABLE c (pk INTEGER AUTO_INCREMENT,
col_int INT ,
col_int_key INT ,
col_varchar VARCHAR(1) ,
col_varchar_key VARCHAR(1) ,
PRIMARY KEY(pk));
CREATE INDEX idx_c_col_int_key ON c(col_int_key);
CREATE TABLE cc (pk INTEGER AUTO_INCREMENT,
col_int INT ,
col_int_key INT ,
col_varchar VARCHAR(1) ,
col_varchar_key VARCHAR(1) ,
PRIMARY KEY(pk));
INSERT INTO cc VALUES (DEFAULT,1750627978,-2052557260,'0','o');
INSERT INTO c values
(DEFAULT,809266110,-169779076,'C','O'),
(DEFAULT,3049998,1973362945,'2','O'),
(DEFAULT,912437770,-1109570817,'W','G'),
(DEFAULT,-1655291083,-1761323512,'q','9'),
(DEFAULT,-1276272795,-591291338,'3','O'),
(DEFAULT,-1297781203,-970713309,'q','r'),
(DEFAULT,-261602165,-2083959767,'7','O'),
(DEFAULT,357530836,-746109993,'6','i'),
(DEFAULT,1553746652,-1607882572,'G','Y'),
(DEFAULT,-1620551574,381511992,'5','n'),
(DEFAULT,-1221888549,-1127778040,'l','U'),
(DEFAULT,1048455957,-1830777487,'U','T'),
(DEFAULT,-541641553,-1731661529,'A','Q'),
(DEFAULT,1482963294,-1570976962,'0','s');
ANALYZE TABLES c, cc;
Table	Op	Msg_type	Msg_text
test.c	analyze	status	OK
test.cc	analyze	status	OK
EXPLAIN
SELECT MIN( table2.col_int ) AS field1 ,
SUM( table2.col_int ) AS field2 ,
( SELECT MAX( subquery1_t1.pk ) AS subquery1_field1
FROM ( cc AS subquery1_t1
INNER JOIN
cc AS subquery1_t2
ON ( subquery1_t2.col_varchar_key =
subquery1_t1.col_varchar_key ) ) ) AS field3
FROM ( c AS table1
RIGHT JOIN
( ( cc AS table2
STRAIGHT_JOIN
c AS table3
ON ( table2.pk = table2.col_int ) ) )
ON ( table2.col_varchar_key = table2.col_varchar AND
table1.col_int_key > ( SELECT 9 FROM cc ) ) )
WHERE ( EXISTS ( SELECT subquery3_t1.col_int AS subquery3_field1
FROM c AS subquery3_t1
WHERE subquery3_t1.col_int_key = table1.pk ) ) AND
table1.col_varchar_key <> table2.col_varchar;
EXPLAIN
-> Left hash join (no condition)  (rows=1)
    -> Table scan on derived_1_5  (rows=1)
        -> Materialize  (rows=1)
            -> Aggregate: min(table2.col_int), sum(table2.col_int)  (rows=1)
                -> Inner hash join (no condition), extra conditions: (table1.col_varchar_key <> table2.col_varchar_key)  (rows=58.8)
                    -> Inner hash join (no condition)  (rows=14)
                        -> Table scan on table3  (rows=14)
                        -> Hash
                            -> Filter: ((table2.col_varchar_key = table2.col_varchar) and (table2.pk = table2.col_int))  (rows=1)
                                -> Table scan on table2  (rows=1)
                    -> Hash
                        -> Nested loop semijoin (FirstMatch)  (rows=4.67)
                            -> Inner hash join (no condition), extra conditions: (table1.col_int_key > derived_5_7.`9`)  (rows=4.67)
                                -> Table scan on table1  (rows=14)
                                -> Hash
                                    -> Table scan on derived_5_7  (rows=1)
                                        -> Materialize  (rows=1)
                                            -> Table scan on cc  (rows=1)
                            -> Index lookup on subquery3_t1 using idx_c_col_int_key (col_int_key = table1.pk)  (rows=1)
    -> Hash
        -> Table scan on derived_1_8  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(subquery1_t1.pk)  (rows=1)
                    -> Inner hash join (subquery1_t2.col_varchar_key = subquery1_t1.col_varchar_key)  (rows=0.1)
                        -> Table scan on subquery1_t1  (rows=1)
                        -> Hash
                            -> Table scan on subquery1_t2  (rows=1)

Warnings:
Note	1276	Field or reference 'test.table1.pk' of SELECT #4 was resolved in SELECT #1
DROP TABLE c, cc;
#
# Bug#30818896 WL#12885: ASSERTION FAILURE IN TEMPTABLE::COLUMN::READ_STD_USER_DATA()
# Refinement of view references in transformed block by
# transform_grouped_to_derived. Cf. Bug#30786714. We replaced too many.
CREATE TABLE b (
pk int NOT NULL AUTO_INCREMENT,
col_int int DEFAULT NULL,
col_int_key int DEFAULT NULL,
col_varchar varchar(1) DEFAULT NULL,
col_varchar_key varchar(1) DEFAULT NULL,
PRIMARY KEY (pk),
KEY idx_b_col_int_key (col_int_key),
KEY idx_b_col_varchar_key (col_varchar_key)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
INSERT INTO b VALUES (1,-1155099828,-1879439976,'N','a');
CREATE TABLE c (
pk int NOT NULL AUTO_INCREMENT,
col_int int DEFAULT NULL,
col_int_key int DEFAULT NULL,
col_varchar varchar(1) DEFAULT NULL,
col_varchar_key varchar(1) DEFAULT NULL,
PRIMARY KEY (pk),
KEY idx_c_col_int_key (col_int_key),
KEY idx_c_col_varchar_key (col_varchar_key)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
INSERT INTO c VALUES
(1,     -3666739,   177583826, 'm', 'j'),
(2,   1904347123,  1743248268, '2', 'P'),
(3,   -469827848,  1376980829, 'i', 'A'),
(4,   1433595053,  1819090851, 'L', 'M'),
(5,    726547892,  1068584791, 'T', 'j'),
(6,   1439902652, -1277159531, 'S', 'r'),
(7,  -1897073668,  -282803609, 'x', '7'),
(8,   1220936946,   170773463, '8', 'z'),
(9,   2127527772,  1049703732, 'i', 'y'),
(10,   673031799,   609105572, 'h', 'a'),
(11,  -479585417,  1317141227, 'w', 'k'),
(12,  -688521145,  -684371590, 'S', 'y'),
(13,     2841986,  -721059140, 'E', 'I'),
(14,    58615730,   496153244, '2', 'U'),
(15,  1139572680,  1532132699, '2', 'n'),
(16,  -842003748,  1189460625, 'I', 'P'),
(17, -1177191130, -1717792127, 'y', 'n'),
(18, -1108396995,   313282977, 'N', 'a'),
(19,  -361562994,   419341930, 'd', 'C'),
(20,   743792160,   984757597, 'e', '2');
CREATE TABLE cc (
pk int NOT NULL AUTO_INCREMENT,
col_int int DEFAULT NULL,
col_int_key int DEFAULT NULL,
col_varchar varchar(1) DEFAULT NULL,
col_varchar_key varchar(1) DEFAULT NULL,
PRIMARY KEY (pk),
KEY idx_cc_col_int_key (col_int_key),
KEY idx_cc_col_varchar_key (col_varchar_key)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
INSERT INTO cc VALUES
(1,  1375472775,   262188886, 'I', 'b'),
(2, -1851648474,   130471446, 'o', '7'),
(3,   503688873,   259988235, 'L', 't'),
(4,   995143874,   -60832670, 'Q', 'K'),
(5, -1440599261, -1669741488, 'k', '7'),
(6, -1534014276,  1760407196, 'c', 'Z'),
(7,   808084535,   311457905, 'B', 'j'),
(8,   731883185,  -571871645, 'd', 'm'),
(9,  1445888442,  1903365311, 'w', 's'),
(10,  222313615,  -404576744, 'n', 'V'),
(11,-1320350569, -1496644593, 'y', 'o'),
(12, 2033205532,  1376480867, 'x', '4'),
(13, -101883317,  -857422791, 'A', '7'),
(14,  867688302,  1410896813, 'J', 'c'),
(15,-1961088920, -2019664999, 'v', '1'),
(16,-1850585486, -1872043099, '1', 'o'),
(17, -603486188,   901895823, 'G', 'q'),
(18,-1381157785, -1613624643, 'Z', 'E'),
(19, -270976631,   288433409, 'r', 'Z'),
(20, 2113722977,   409698731, 'n', 'd');
CREATE VIEW  view_cc AS
SELECT cc.col_int_key AS col_int_key,
cc.col_varchar AS col_varchar,
cc.col_varchar_key AS col_varchar_key from cc;
ANALYZE TABLES b, c, cc;
Table	Op	Msg_type	Msg_text
test.b	analyze	status	OK
test.c	analyze	status	OK
test.cc	analyze	status	OK
SET sql_mode='';
SELECT STRAIGHT_JOIN
( SELECT AVG(subquery1_t1.col_int) AS subquery1_field1
FROM c AS subquery1_t1
WHERE EXISTS ( SELECT subquery1_t1.pk AS child_subquery1_field1
FROM ( view_cc  AS child_subquery1_t1
LEFT JOIN
b AS child_subquery1_t2
ON child_subquery1_t2.pk = child_subquery1_t1.col_int_key )
WHERE child_subquery1_t1.col_varchar_key > subquery1_t1.col_varchar OR
child_subquery1_t1.col_varchar_key < child_subquery1_t1.col_varchar))
AS field1,
table1.col_int_key          AS field2,
SUM(table1.col_varchar_key) AS field3,
MAX(table2.col_int)         AS field4
FROM ( cc AS table1
INNER JOIN
( b AS table2
INNER JOIN
cc AS table3
ON table3.col_int = table2.col_int_key )
ON ( table3.col_varchar_key = table2.col_varchar_key ) )
WHERE ( NOT EXISTS ( ( SELECT subquery2_t1.col_varchar AS subquery2_field1
FROM c AS subquery2_t1 ) ) )   AND
table1.col_varchar_key = table2.col_varchar_key AND
( table2.col_varchar_key >= 'v'                 AND
table1.col_varchar <= table2.col_varchar_key )
ORDER BY field2 DESC, table1.col_int_key, table2 .pk ASC, field1, field2, field3, field4
LIMIT 1;
field1	field2	field3	field4
222144105.4500	NULL	NULL	NULL
EXPLAIN SELECT STRAIGHT_JOIN
( SELECT AVG(subquery1_t1.col_int) AS subquery1_field1
FROM c AS subquery1_t1
WHERE EXISTS ( SELECT subquery1_t1.pk AS child_subquery1_field1
FROM ( view_cc  AS child_subquery1_t1
LEFT JOIN
b AS child_subquery1_t2
ON child_subquery1_t2.pk = child_subquery1_t1.col_int_key )
WHERE child_subquery1_t1.col_varchar_key > subquery1_t1.col_varchar OR
child_subquery1_t1.col_varchar_key < child_subquery1_t1.col_varchar))
AS field1,
table1.col_int_key          AS field2,
SUM(table1.col_varchar_key) AS field3,
MAX(table2.col_int)         AS field4
FROM ( cc AS table1
INNER JOIN
( b AS table2
INNER JOIN
cc AS table3
ON table3.col_int = table2.col_int_key )
ON ( table3.col_varchar_key = table2.col_varchar_key ) )
WHERE ( NOT EXISTS ( ( SELECT subquery2_t1.col_varchar AS subquery2_field1
FROM c AS subquery2_t1 ) ) )   AND
table1.col_varchar_key = table2.col_varchar_key AND
( table2.col_varchar_key >= 'v'                 AND
table1.col_varchar <= table2.col_varchar_key )
ORDER BY field2 DESC, table1.col_int_key, table2 .pk ASC, field1, field2, field3, field4
LIMIT 1;
EXPLAIN
-> Limit: 1 row(s)  (rows=1)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on derived_1_6  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: sum(table1.col_varchar_key), max(table2.col_int)  (rows=1)
                    -> Filter: (derived_6_7.Name_exp_1 is null)  (rows=13.3)
                        -> Left hash join (no condition)  (rows=133)
                            -> Inner hash join (table2.col_varchar_key = table3.col_varchar_key), (table3.col_int = table2.col_int_key)  (rows=133)
                                -> Table scan on table3  (rows=20)
                                -> Hash
                                    -> Inner hash join (table1.col_varchar_key = table2.col_varchar_key)  (rows=6.67)
                                        -> Filter: (table1.col_varchar <= table1.col_varchar_key)  (rows=6.67)
                                            -> Table scan on table1  (rows=20)
                                        -> Hash
                                            -> Filter: (table2.col_varchar_key >= 'v')  (rows=1)
                                                -> Table scan on table2  (rows=1)
                            -> Hash
                                -> Table scan on derived_6_7  (rows=1)
                                    -> Materialize  (rows=1)
                                        -> Limit: 1 row(s)  (rows=1)
                                            -> Table scan on subquery2_t1  (rows=20)
        -> Hash
            -> Table scan on derived_1_8  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: avg(subquery1_t1.col_int)  (rows=1)
                        -> Hash semijoin (FirstMatch) (no condition), extra conditions: ((cc.col_varchar_key > subquery1_t1.col_varchar) or (cc.col_varchar_key < cc.col_varchar))  (rows=20)
                            -> Table scan on subquery1_t1  (rows=20)
                            -> Hash
                                -> Left hash join (child_subquery1_t2.pk = cc.col_int_key)  (rows=20)
                                    -> Table scan on cc  (rows=20)
                                    -> Hash
                                        -> Table scan on child_subquery1_t2  (rows=1)

Warnings:
Note	1276	Field or reference 'test.subquery1_t1.pk' of SELECT #3 was resolved in SELECT #2
Note	1276	Field or reference 'test.subquery1_t1.col_varchar' of SELECT #3 was resolved in SELECT #2
DROP VIEW view_cc;
DROP TABLES b, c, cc;
SET sql_mode=default;

Bug found while running RAPID mtr (rapid.view, rapid.subselect) with
PS-protocol

CREATE TABLE t1(a INTEGER, b INTEGER);
CREATE TABLE t2(a INTEGER);
INSERT INTO t1 VALUES
(1, 10),
(2, 20), (2, 21),
(3, NULL),
(4, 40), (4, 41), (4, 42), (4, 43), (4, 44);
INSERT INTO t2 VALUES (1), (2), (3), (4), (5), (NULL);
ANALYZE TABLE t1,t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
CREATE VIEW v1 AS SELECT a, b, (SELECT COUNT(*) FROM t2) AS c FROM t1;
SELECT * FROM v1;
a	b	c
1	10	6
2	20	6
2	21	6
3	NULL	6
4	40	6
4	41	6
4	42	6
4	43	6
4	44	6
EXPLAIN SELECT * FROM v1;
EXPLAIN
-> Left hash join (no condition)  (rows=9)
    -> Table scan on t1  (rows=9)
    -> Hash
        -> Table scan on derived_2_3  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: count(0)  (rows=1)
                    -> Table scan on t2  (rows=6)

The select does not reference the scalar subquery
SELECT a FROM v1;
a
1
2
2
3
4
4
4
4
4
EXPLAIN SELECT a FROM v1;
EXPLAIN
-> Left hash join (no condition)  (rows=9)
    -> Table scan on t1  (rows=9)
    -> Hash
        -> Table scan on derived_2_3  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: count(0)  (rows=1)
                    -> Table scan on t2  (rows=6)

set sql_mode='';
SELECT a,c FROM v1 GROUP BY b HAVING c > 0;
a	c
1	6
2	6
2	6
3	6
4	6
4	6
4	6
4	6
4	6
EXPLAIN SELECT a,c FROM v1 GROUP BY b HAVING c > 0;
EXPLAIN
-> Filter: (derived_2_3.`count(0)` > 0)  (rows=1)
    -> Group (no aggregates)  (rows=3)
        -> Sort: b  (rows=9)
            -> Left hash join (no condition)  (rows=9)
                -> Table scan on t1  (rows=9)
                -> Hash
                    -> Table scan on derived_2_3  (rows=1)
                        -> Materialize  (rows=1)
                            -> Aggregate: count(0)  (rows=1)
                                -> Table scan on t2  (rows=6)

set sql_mode=default;
The select references the scalar subquery from the view, but not in select list
SELECT a FROM v1 WHERE c > 0;
a
1
2
2
3
4
4
4
4
4
EXPLAIN SELECT a FROM v1 WHERE c > 0;
EXPLAIN
-> Inner hash join (no condition)  (rows=9)
    -> Table scan on t1  (rows=9)
    -> Hash
        -> Table scan on derived_2_3  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) > 0)  (rows=1)
                    -> Aggregate: count(0)  (rows=1)
                        -> Table scan on t2  (rows=6)

DROP VIEW v1;
DROP TABLE t1, t2;
#
# Bug#30922236 WL#13851 SIG 11 IN OPTIMIZE_KEYUSE() AT SQL/SQL_OPTIMIZER.CC
# Incomplete fix for commit 941056f2c "WL#12885: view bug 2"
CREATE TABLE c (
pk int NOT NULL AUTO_INCREMENT,
col_int int DEFAULT NULL,
col_int_key int DEFAULT NULL,
col_date date DEFAULT NULL,
col_date_key date DEFAULT NULL,
col_time time DEFAULT NULL,
col_time_key time DEFAULT NULL,
col_datetime datetime DEFAULT NULL,
col_datetime_key datetime DEFAULT NULL,
col_varchar varchar(1) DEFAULT NULL,
col_varchar_key varchar(1) DEFAULT NULL,
PRIMARY KEY (pk),
KEY idx_cc_col_int_key (col_int_key),
KEY idx_cc_col_date_key (col_date_key),
KEY idx_cc_col_time_key (col_time_key),
KEY idx_cc_col_datetime_key (col_datetime_key),
KEY idx_cc_col_varchar_key (col_varchar_key)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
CREATE VIEW view_c AS
SELECT alias1.col_int
FROM ( c AS alias1
JOIN
( ( c AS alias2
JOIN
c AS alias3
ON(1)) )
ON ( alias2.col_int >= ( SELECT MIN( sq1_alias1.col_int ) AS sq1_field1
FROM ( c AS sq1_alias1, c AS sq1_alias2 ) ) ) )
WHERE (  EXISTS ( ( SELECT sq2_alias1.col_int
FROM ( c AS sq2_alias1
JOIN
c AS sq2_alias2
ON ( sq2_alias2.col_int = sq2_alias1.pk ) )) ) ) ;
SELECT * FROM view_c;
col_int
EXPLAIN SELECT * FROM view_c;
EXPLAIN
-> Nested loop inner join (FirstMatch)  (rows=0.333)
    -> Limit: 1 row(s)  (rows=1)
        -> Inner hash join (sq2_alias2.col_int = sq2_alias1.pk)  (rows=1)
            -> Table scan on sq2_alias2  (rows=1)
            -> Hash
                -> Table scan on sq2_alias1  (rows=1)
    -> Inner hash join (no condition)  (rows=0.333)
        -> Inner hash join (no condition)  (rows=1)
            -> Table scan on alias1  (rows=1)
            -> Hash
                -> Table scan on alias3  (rows=1)
        -> Hash
            -> Inner hash join (no condition), extra conditions: (alias2.col_int >= derived_2_4.sq1_field1)  (rows=0.333)
                -> Table scan on derived_2_4  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: min(sq1_alias1.col_int)  (rows=1)
                            -> Inner hash join (no condition)  (rows=1)
                                -> Table scan on sq1_alias1  (rows=1)
                                -> Hash
                                    -> Table scan on sq1_alias2  (rows=1)
                -> Hash
                    -> Table scan on alias2  (rows=1)

DROP VIEW view_c;
DROP TABLE c;
#
# Bug#31535523 WL#13686: SIG6 HEADER::NUMBER_OF_USED_CHUNKS() == 0 AT TEMPTABLE/BLOCK.H
# This query used to fail during server shutdown, cf. bug issue for how to run.
# In a single mtr run, look in var/log/mysqld.1.err for the stack trace before the fix.
CREATE TABLE t1 (i int);
CREATE TABLE t2 (i int);
SELECT t2.i FROM t2
WHERE ( false ) AND
( t2.i  IN ( SELECT t1.i FROM t1
WHERE t1.i <= SOME ( SELECT 8 UNION  SELECT 3 ) ) );
i
DROP TABLE t1, t2;
#
# WL#13686 Transformation of constant scalar subquery with DUAL causes
#          assert.
#
CREATE TABLE t1 (a int);
INSERT INTO t1 VALUES (1), (2);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT a FROM t1 WHERE (SELECT 1 FROM DUAL WHERE 1=0) IS NULL;
a
1
2
EXPLAIN SELECT a FROM t1 WHERE (SELECT 1 FROM DUAL WHERE 1=0) IS NULL;
EXPLAIN
-> Table scan on t1  (rows=2)

DROP TABLE t1;
#
# Bug#31462120 ASSERTION `NESTED_JOIN_LIST->SIZE() == 2' FAILED AT SQL/SQL_RESOLVER.CC
#
CREATE TABLE a (col_varchar_key varchar(1));
SELECT table1.col_varchar_key
FROM ( SELECT sub1_t2.*
FROM (a
JOIN
(a AS sub1_t2)
ON sub1_t2.col_varchar_key IN (SELECT col_varchar_key FROM a))
WHERE EXISTS (SELECT sub2_t1.col_varchar_key
FROM a AS sub2_t1))  AS table1
JOIN
(a AS table2
JOIN
a
ON 1 >= (SELECT MIN( col_varchar_key) FROM a))
ON true;
col_varchar_key
EXPLAIN SELECT table1.col_varchar_key
FROM ( SELECT sub1_t2.*
FROM (a
JOIN
(a AS sub1_t2)
ON sub1_t2.col_varchar_key IN (SELECT col_varchar_key FROM a))
WHERE EXISTS (SELECT sub2_t1.col_varchar_key
FROM a AS sub2_t1))  AS table1
JOIN
(a AS table2
JOIN
a
ON 1 >= (SELECT MIN( col_varchar_key) FROM a))
ON true;
EXPLAIN
-> Filter: <cache>((1 >= (select #5)))  (rows=1)
    -> Inner hash join (no condition)  (rows=1)
        -> Inner hash join (no condition)  (rows=1)
            -> Nested loop inner join (FirstMatch)  (rows=1)
                -> Limit: 1 row(s)  (rows=1)
                    -> Table scan on sub2_t1  (rows=1)
                -> Inner hash join (no condition)  (rows=1)
                    -> Table scan on a  (rows=1)
                    -> Hash
                        -> Filter: <in_optimizer>(sub1_t2.col_varchar_key,<exists>(select #3))  (rows=1)
                            -> Table scan on sub1_t2  (rows=1)
                            -> Select #3 (subquery in condition; dependent)
                                -> Filter: (<cache>(sub1_t2.col_varchar_key) = a.col_varchar_key)  (rows=0.1)
                                    -> Table scan on a  (rows=1)
            -> Hash
                -> Table scan on table2  (rows=1)
        -> Hash
            -> Table scan on a  (rows=1)
    -> Select #5 (subquery in condition; run only once)
        -> Aggregate: min(a.col_varchar_key)  (rows=1)
            -> Table scan on a  (rows=1)

DROP TABLE a;
#
# Bug#31566339 WRONG RESULT WITH OPTIMIZER_SWITCH SUBQUERY_TO_DERIVED ON: ANY
#
CREATE TABLE t1(pk int primary key);
INSERT INTO t1 VALUES(1),(2),(3),(4),(5);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
# Used to return NULL
SELECT SUM(pk) FROM t1 WHERE ( pk >= ANY ( SELECT MAX(1) FROM DUAL) );
SUM(pk)
15
DROP TABLE t1;
#
# Bug #32859025: MYSQLD CRASH - ASSERTION `N < M_SIZE' FAILED|SQL/SQL_ARRAY.H
#
CREATE TABLE t1 ( pk INTEGER );
SELECT
(SELECT COUNT(*) FROM t1) AS f1,
(SELECT COUNT(*) FROM t1) AS f2
FROM t1
GROUP BY f1, f2 WITH ROLLUP;
f1	f2
NULL	NULL
DROP TABLE t1;
#
# Bug#33057397: FIELD NAMES ARE DISPLAYED INCORRECTLY WITH ROLLUP AND
#               SUBQUERY_TO_DERIVED
CREATE TABLE t1 ( f1 INTEGER);
INSERT INTO t1 VALUES (0);
SELECT (SELECT MIN(f1) AS min FROM t1 ) AS field1 FROM t1 GROUP BY
field1 WITH ROLLUP;
field1
0
NULL
DROP TABLE t1;
#
# Bug#33104036: ASSERTION FAILURE IN FIND_SUBQUERY_IN_SELECT_LIST AT
#               ../SQL/ITEM_SUBSELECT.CC
CREATE TABLE t1 (f1 INTEGER);
SELECT (SELECT SUM(f1) AS SQ1_field1 FROM t1) as field1
FROM t1 GROUP BY f1 WITH ROLLUP ORDER BY f1;
field1
NULL
DROP TABLE t1;
Original repro case
CREATE TABLE t1 (pk integer auto_increment,
col_int int ,
col_datetime datetime ,
col_char_255 char(255) ,
col_smallint smallint ,
col_decimal_10_8 decimal(10,8),
primary key(pk)) ;
set sql_mode='';
SELECT alias1.col_decimal_10_8 AS field1 ,
( SELECT SUM(table1.col_smallint ) AS SQ1_field1
FROM ( t1 as table1 RIGHT JOIN t1 as table2 ON 1 )
) AS field2 ,
GROUPING( LOG(alias1.col_int) ) AS field3
FROM ( t1 AS alias1
JOIN
t1 AS alias2 ON 1  )
WHERE alias2.pk IN ( SELECT col_char_255 FROM t1 )
GROUP BY field1, field2, LOG(alias1.col_int)
WITH ROLLUP
ORDER BY alias1.col_datetime, field1, field2, LOG(alias1.col_int) ;
field1	field2	field3
NULL	NULL	1
EXPLAIN SELECT alias1.col_decimal_10_8 AS field1 ,
( SELECT SUM(table1.col_smallint ) AS SQ1_field1
FROM ( t1 as table1 RIGHT JOIN t1 as table2 ON 1 )
) AS field2 ,
GROUPING( LOG(alias1.col_int) ) AS field3
FROM ( t1 AS alias1
JOIN
t1 AS alias2 ON 1  )
WHERE alias2.pk IN ( SELECT col_char_255 FROM t1 )
GROUP BY field1, field2, LOG(alias1.col_int)
WITH ROLLUP
ORDER BY alias1.col_datetime, field1, field2, LOG(alias1.col_int) ;
EXPLAIN
-> Sort: alias1.col_datetime, field1, field2, `rollup_group_item(log(alias1.col_int),2)`  (rows=4)
    -> Stream results  (rows=4)
        -> Group (no aggregates)  (rows=4)
            -> Sort: alias1.col_decimal_10_8, derived_1_2.SQ1_field1, log(alias1.col_int)  (rows=1)
                -> Left hash join (no condition)  (rows=1)
                    -> Inner hash join (no condition)  (rows=1)
                        -> Table scan on alias1  (rows=1)
                        -> Hash
                            -> Hash semijoin (FirstMatch) (cast(alias2.pk as double) = cast(t1.col_char_255 as double))  (rows=1)
                                -> Table scan on alias2  (rows=1)
                                -> Hash
                                    -> Table scan on t1  (rows=1)
                    -> Hash
                        -> Table scan on derived_1_2  (rows=1)
                            -> Materialize  (rows=1)
                                -> Aggregate: sum(table1.col_smallint)  (rows=1)
                                    -> Left hash join (no condition)  (rows=1)
                                        -> Table scan on table2  (rows=1)
                                        -> Hash
                                            -> Table scan on table1  (rows=1)

set sql_mode=default;
DROP TABLE t1;
#
# Bug#33079592: ASSERTION `SELECT->BASE_REF_ITEMS[ITEM_IDX] == ITEM' FAILED
#
CREATE TABLE t1 (f1 INTEGER);
SET optimizer_switch='subquery_to_derived=default';
SELECT SUM(f1), ROW_NUMBER() OVER (PARTITION BY f1), (SELECT MIN(f1) FROM t1) FROM t1;
ERROR 42000: In aggregated query without GROUP BY, expression #1 of PARTITION BY or ORDER BY clause of window '<unnamed window>' contains nonaggregated column 'test.t1.f1'; this is incompatible with sql_mode=only_full_group_by
SELECT SUM(f1), ROW_NUMBER() OVER (), (SELECT MIN(f1) FROM t1) FROM t1 ORDER BY f1;
SUM(f1)	ROW_NUMBER() OVER ()	(SELECT MIN(f1) FROM t1)
NULL	1	NULL
SELECT SUM(f1), SUM(f1) OVER (), f1, (SELECT MIN(f1) FROM t1) sq FROM t1 ORDER BY f1;
ERROR 42000: In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'test.t1.f1'; this is incompatible with sql_mode=only_full_group_by
SET optimizer_switch='subquery_to_derived=on';
SELECT SUM(f1), ROW_NUMBER() OVER (PARTITION BY f1), (SELECT MIN(f1) FROM t1) FROM t1;
ERROR 42000: In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'test.t1.f1'; this is incompatible with sql_mode=only_full_group_by
SELECT SUM(f1), ROW_NUMBER() OVER (), (SELECT MIN(f1) FROM t1) FROM t1 ORDER BY f1;
SUM(f1)	ROW_NUMBER() OVER ()	(SELECT MIN(f1) FROM t1)
NULL	1	NULL
SELECT SUM(f1), SUM(f1) OVER (), f1, (SELECT MIN(f1) FROM t1) sq FROM t1 ORDER BY f1;
ERROR 42000: In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'test.t1.f1'; this is incompatible with sql_mode=only_full_group_by
DROP TABLE t1;
#
# Bug#33910786 Scalar subquery transformation combined with
# WHERE clause optimization lead to reject_multiple_rows being
# ineffective
#
CREATE TABLE t(i INT);
INSERT INTO t VALUES (1);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
SELECT 1 AS one FROM t WHERE 1=(SELECT 1 UNION SELECT 2);
ERROR 21000: Subquery returns more than 1 row
EXPLAIN FORMAT=tree SELECT 1 AS one FROM t WHERE 1=(SELECT 1 UNION SELECT 2);
EXPLAIN
-> Inner hash join (no condition)  (rows=0.2)
    -> Table scan on t  (rows=1)
    -> Hash
        -> Filter: (derived_1_2.`1` = 1)  (rows=0.2)
            -> Table scan on derived_1_2  (rows=2)
                -> Union materialize with deduplication  (rows=2)
                    -> Rows fetched before execution  (rows=1)
                    -> Rows fetched before execution  (rows=1)

DROP TABLE t;
#
# Bug#34998721 Mysqld crash - Assertion `table_num_to_node_num[table_num] != -1' failed.
#              Only seen with hypergraph optimizer enabled.
#
CREATE TABLE t(x INT);
SET SQL_MODE='';
SELECT COUNT(*), (SELECT 1 FROM t)
FROM t AS t1,
(SELECT 1 FROM t) AS t2,
t AS t3
ORDER BY ROW_NUMBER() OVER (ORDER BY -t3.x);
COUNT(*)	(SELECT 1 FROM t)
0	NULL
SET SQL_MODE=default;
DROP TABLE t;
#
# Bug#35150085 Wrong item name in result set after transform
#
SET SQL_MODE='';
CREATE TABLE t(i INT);
INSERT INTO t VALUES (4);
SELECT i AS i1,  # used to get renamed to i2
i AS i2,
(SELECT i FROM t) AS subquery,
SUM(i) AS summ
FROM t;
i1	i2	subquery	summ
4	4	4	4
SET SQL_MODE=default;
DROP TABLE t;
#
# Bug#36314993 CUBE caused crash in Query_block::supported_correlated_scalar_subquery
#
CREATE VIEW v2df AS
SELECT 'a'
GROUP BY CUBE (TO_DAYS('6087-05-12'));
CREATE TABLE t24c (
c0 DATETIME,
c7 BINARY(172)
) ENGINE=innodb;
SET SQL_MODE='';
WITH cte1 AS
( SELECT MIN(c0) AS c7,
( SELECT c7
FROM v2df
GROUP BY c0
FOR UPDATE /*!80001 skip locked*/ ) AS c
FROM t24c
WHERE c0 )
SELECT c7 FROM cte1 WHERE t24c.c7<= 1 LIMIT 241;
ERROR 42S22: Unknown column 't24c.c7' in 'where clause'
DROP VIEW v2df;
DROP TABLE t24c;
CREATE TABLE t1 (f1 INTEGER, f2 INTEGER);
SELECT ( SELECT f2
FROM (SELECT 1
GROUP BY CUBE(1)) AS dt1
GROUP BY f1) AS a,
MIN(f1)
FROM t1;
ERROR HY000: Secondary engine operation failed. Reason: "You have not defined the secondary engine for at least one of the query tables.".
SELECT ( SELECT f2
FROM (SELECT 1
GROUP BY ROLLUP(1)) AS dt1
GROUP BY f1) AS a,
MIN(f1)
FROM t1;
a	MIN(f1)
NULL	NULL
DROP TABLE t1;
SET SQL_MODE=default;
#
# Bug#36079456 Assertion `having_cond->has_subquery() ||
#              !(having_cond->used_tables() & ~(1 | PSEUDO_TABLE_BITS))'
#              failed.
CREATE TABLE t(i INT);
SELECT i FROM t
HAVING i <> ( SELECT MIN(i)
FROM t)
ORDER BY i;
i
EXPLAIN SELECT i FROM t
HAVING i <> ( SELECT MIN(i)
FROM t)
ORDER BY i;
EXPLAIN
-> Sort: t.i  (rows=0.9)
    -> Filter: (t.i <> derived_1_2.`MIN(i)`)  (rows=0.9)
        -> Left hash join (no condition)  (rows=1)
            -> Table scan on t  (rows=1)
            -> Hash
                -> Table scan on derived_1_2  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: min(t.i)  (rows=1)
                            -> Table scan on t  (rows=1)

DROP TABLE t;
SET optimizer_switch='subquery_to_derived=default';
"Optimizer switch subquery_to_derived for subquery table to derived transformations."
# WL#13425 Transform IN and EXISTS predicates for RAPID
SET OPTIMIZER_SWITCH="subquery_to_derived=on";
CREATE TABLE t1 (a INT, b INT);
INSERT INTO t1 VALUES(1,10),(2,20),(3,30);
CREATE TABLE t2 (a INT, b INT);
INSERT INTO t2 VALUES(1,10),(2,20),(3,30),(1,110),(2,120),(3,130);
INSERT INTO t2 SELECT * FROM t2;
INSERT INTO t2 SELECT * FROM t2;
INSERT INTO t2 SELECT * FROM t2;
ANALYZE TABLE t1,t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN SELECT * FROM t1 ot
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it);
EXPLAIN
-> Filter: ((ot.b < 0) or (derived_1_2.Name_exp_1 is not null))  (rows=3)
    -> Nested loop left join  (rows=3)
        -> Table scan on ot  (rows=3)
        -> Filter: (ot.a = derived_1_2.Name_exp_1)  (rows=0.0693)
            -> Covering index lookup on derived_1_2 using <auto_key0> (Name_exp_1 = ot.a)  (rows=0.693)
                -> Materialize  (rows=6.93)
                    -> Table scan on <temporary>  (rows=6.93)
                        -> Temporary table with deduplication  (rows=6.93)
                            -> Table scan on it  (rows=48)

SELECT * FROM t1 ot
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it);
a	b
2	20
3	30
SET OPTIMIZER_SWITCH="subquery_to_derived=off";
EXPLAIN SELECT * FROM t1 ot
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it);
EXPLAIN
-> Filter: ((ot.b < 0) or <in_optimizer>(ot.a,ot.a in (select #2)))  (rows=3)
    -> Table scan on ot  (rows=3)
    -> Select #2 (subquery in condition; run only once)
        -> Filter: ((ot.a = `<materialized_subquery>`.`it.a+1`))  (rows=1)
            -> Limit: 1 row(s)  (rows=1)
                -> Index lookup on <materialized_subquery> using <auto_distinct_key> (it.a+1 = ot.a)
                    -> Materialize with deduplication  (rows=48)
                        -> Table scan on it  (rows=48)

SELECT * FROM t1 ot
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it);
a	b
2	20
3	30
SET OPTIMIZER_SWITCH="subquery_to_derived=on";
EXPLAIN SELECT * FROM t1 ot
WHERE ot.b<0 OR (ot.a,ot.a+1,ot.a+2)
IN (SELECT it.a+1 AS myname,it.a+2 AS myname,it.a+3 FROM t2 it
WHERE it.a+3=ot.a+2);
EXPLAIN
-> Filter: ((ot.b < 0) or (derived_1_2.Name_exp_1 is not null))  (rows=5.47)
    -> Nested loop left join  (rows=5.47)
        -> Table scan on ot  (rows=3)
        -> Filter: ((ot.a = derived_1_2.Name_exp_1) and ((ot.a + 1) = derived_1_2.Name_exp_2) and ((ot.a + 2) = derived_1_2.Name_exp_3) and ((ot.a + 2) = derived_1_2.Name_exp_4))  (rows=0.182)
            -> Covering index lookup on derived_1_2 using <auto_key0> (Name_exp_1 = ot.a, Name_exp_2 = (ot.a + 1), Name_exp_3 = (ot.a + 2), Name_exp_4 = (ot.a + 2))  (rows=1.82)
                -> Materialize  (rows=18.2)
                    -> Sort with duplicate removal: Name_exp_1, Name_exp_2, Name_exp_3  (rows=18.2)
                        -> Table scan on it  (rows=48)

Warnings:
Note	1276	Field or reference 'test.ot.a' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 ot
WHERE ot.b<0 OR (ot.a,ot.a+1,ot.a+2)
IN (SELECT it.a+1 AS myname,it.a+2 AS myname,it.a+3 FROM t2 it
WHERE it.a+3=ot.a+2);
a	b
2	20
3	30
EXPLAIN SELECT * FROM t1 ot
WHERE ot.b<0 OR EXISTS(SELECT * FROM t2 it WHERE ot.a=it.a+1);
EXPLAIN
-> Filter: ((ot.b < 0) or (derived_1_2.Name_exp_1 is not null))  (rows=3)
    -> Nested loop left join  (rows=3)
        -> Table scan on ot  (rows=3)
        -> Filter: (ot.a = derived_1_2.Name_exp_2)  (rows=0.0693)
            -> Index lookup on derived_1_2 using <auto_key0> (Name_exp_2 = ot.a)  (rows=0.693)
                -> Materialize  (rows=6.93)
                    -> Sort with duplicate removal: Name_exp_2  (rows=6.93)
                        -> Table scan on it  (rows=48)

Warnings:
Note	1276	Field or reference 'test.ot.a' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 ot
WHERE ot.b<0 OR EXISTS(SELECT * FROM t2 it WHERE ot.a=it.a+1);
a	b
2	20
3	30
EXPLAIN SELECT * FROM t1 ot
WHERE ot.b<0 OR EXISTS(SELECT it.b FROM t2 it WHERE ot.a=it.a+1);
EXPLAIN
-> Filter: ((ot.b < 0) or (derived_1_2.Name_exp_1 is not null))  (rows=3)
    -> Nested loop left join  (rows=3)
        -> Table scan on ot  (rows=3)
        -> Filter: (ot.a = derived_1_2.Name_exp_2)  (rows=0.0693)
            -> Index lookup on derived_1_2 using <auto_key0> (Name_exp_2 = ot.a)  (rows=0.693)
                -> Materialize  (rows=6.93)
                    -> Sort with duplicate removal: Name_exp_2  (rows=6.93)
                        -> Table scan on it  (rows=48)

Warnings:
Note	1276	Field or reference 'test.ot.a' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 ot
WHERE ot.b<0 OR EXISTS(SELECT it.b FROM t2 it WHERE ot.a=it.a+1);
a	b
2	20
3	30
EXPLAIN SELECT * FROM t1 ot
WHERE ot.b<0 OR EXISTS(SELECT it.b,ot.b FROM t2 it WHERE ot.a=it.a+1);
EXPLAIN
-> Filter: ((ot.b < 0) or (derived_1_2.Name_exp_1 is not null))  (rows=3)
    -> Nested loop left join  (rows=3)
        -> Table scan on ot  (rows=3)
        -> Filter: (ot.a = derived_1_2.Name_exp_3)  (rows=0.0693)
            -> Index lookup on derived_1_2 using <auto_key0> (Name_exp_3 = ot.a)  (rows=0.693)
                -> Materialize  (rows=6.93)
                    -> Sort with duplicate removal: Name_exp_3  (rows=6.93)
                        -> Table scan on it  (rows=48)

Warnings:
Note	1276	Field or reference 'test.ot.b' of SELECT #2 was resolved in SELECT #1
Note	1276	Field or reference 'test.ot.a' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 ot
WHERE ot.b<0 OR EXISTS(SELECT it.b,ot.b FROM t2 it WHERE ot.a=it.a+1);
a	b
2	20
3	30
CREATE view v2 AS SELECT * FROM t2;
PREPARE s FROM
"SELECT * FROM t1 ot
WHERE ot.b<0 OR EXISTS(SELECT it.b FROM v2 it WHERE ot.a=it.a+1)";
EXECUTE s;
a	b
2	20
3	30
EXPLAIN SELECT * FROM t1 ot
WHERE ot.b<0 OR (ot.b<0 AND (ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it)));
EXPLAIN
-> Nested loop left join  (rows=1)
    -> Filter: (ot.b < 0)  (rows=1)
        -> Table scan on ot  (rows=3)
    -> Filter: (ot.a = derived_1_2.Name_exp_1)  (rows=0.0693)
        -> Covering index lookup on derived_1_2 using <auto_key0> (Name_exp_1 = ot.a)  (rows=0.693)
            -> Materialize  (rows=6.93)
                -> Table scan on <temporary>  (rows=6.93)
                    -> Temporary table with deduplication  (rows=6.93)
                        -> Table scan on it  (rows=48)

SELECT * FROM t1 ot
WHERE ot.b<0 OR (ot.b<0 AND (ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it)));
a	b
EXPLAIN SELECT * FROM t1 ot
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM (SELECT * FROM t2 LIMIT 1) it
WHERE it.a+3=ot.a+1);
EXPLAIN
-> Filter: ((ot.b < 0) or (derived_1_2.Name_exp_1 is not null))  (rows=3)
    -> Left hash join (ot.a = derived_1_2.Name_exp_1), ((ot.a + 1) = derived_1_2.Name_exp_2)  (rows=3)
        -> Table scan on ot  (rows=3)
        -> Hash
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Sort with duplicate removal: Name_exp_1, Name_exp_2  (rows=1)
                        -> Table scan on it  (rows=1)
                            -> Materialize  (rows=1)
                                -> Limit: 1 row(s)  (rows=1)
                                    -> Table scan on t2  (rows=48)

Warnings:
Note	1276	Field or reference 'test.ot.a' of SELECT #2 was resolved in SELECT #1
BEGIN;
EXPLAIN UPDATE t1 ot SET a=a*100
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it);
EXPLAIN
-> Update ot (immediate)  (rows=3)
    -> Filter: ((ot.b < 0) or (derived_1_2.Name_exp_1 is not null))  (rows=3)
        -> Nested loop left join  (rows=3)
            -> Table scan on ot  (rows=3)
            -> Filter: (ot.a = derived_1_2.Name_exp_1)  (rows=0.0693)
                -> Covering index lookup on derived_1_2 using <auto_key0> (Name_exp_1 = ot.a)  (rows=0.693)
                    -> Materialize  (rows=6.93)
                        -> Table scan on <temporary>  (rows=6.93)
                            -> Temporary table with deduplication  (rows=6.93)
                                -> Table scan on it  (rows=48)

UPDATE t1 ot SET a=a*100
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it);
SELECT * FROM t1;
a	b
1	10
200	20
300	30
ROLLBACK;
BEGIN;
EXPLAIN UPDATE t1 ot, (SELECT 1) AS dummy
SET a=a*100
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it);
EXPLAIN
-> Update ot (buffered)  (rows=3)
    -> Filter: ((ot.b < 0) or (derived_1_3.Name_exp_1 is not null))  (rows=3)
        -> Inner hash join (no condition)  (rows=3)
            -> Nested loop left join  (rows=3)
                -> Table scan on ot  (rows=3)
                -> Filter: (ot.a = derived_1_3.Name_exp_1)  (rows=0.0693)
                    -> Covering index lookup on derived_1_3 using <auto_key0> (Name_exp_1 = ot.a)  (rows=0.693)
                        -> Materialize  (rows=6.93)
                            -> Table scan on <temporary>  (rows=6.93)
                                -> Temporary table with deduplication  (rows=6.93)
                                    -> Table scan on it  (rows=48)
            -> Hash
                -> Table scan on dummy  (rows=1)
                    -> Materialize  (rows=1)
                        -> Rows fetched before execution  (rows=1)

UPDATE t1 ot, (SELECT 1) AS dummy
SET a=a*100
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it);
SELECT * FROM t1;
a	b
1	10
200	20
300	30
ROLLBACK;
BEGIN;
EXPLAIN DELETE FROM t1 ot
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it);
EXPLAIN
-> Delete from ot (immediate)  (rows=3)
    -> Filter: ((ot.b < 0) or (derived_1_2.Name_exp_1 is not null))  (rows=3)
        -> Nested loop left join  (rows=3)
            -> Table scan on ot  (rows=3)
            -> Filter: (ot.a = derived_1_2.Name_exp_1)  (rows=0.0693)
                -> Covering index lookup on derived_1_2 using <auto_key0> (Name_exp_1 = ot.a)  (rows=0.693)
                    -> Materialize  (rows=6.93)
                        -> Table scan on <temporary>  (rows=6.93)
                            -> Temporary table with deduplication  (rows=6.93)
                                -> Table scan on it  (rows=48)

DELETE FROM t1 ot
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it);
SELECT * FROM t1;
a	b
1	10
ROLLBACK;
BEGIN;
EXPLAIN DELETE ot.* FROM t1 ot, (SELECT 1) AS dummy
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it);
EXPLAIN
-> Delete from ot (buffered)  (rows=1)
    -> Filter: ((ot.b < 0) or (derived_1_3.Name_exp_1 is not null))  (rows=1)
        -> Inner hash join (no condition)  (rows=1)
            -> Nested loop left join  (rows=1)
                -> Table scan on ot  (rows=1)
                -> Filter: (ot.a = derived_1_3.Name_exp_1)  (rows=0.0693)
                    -> Covering index lookup on derived_1_3 using <auto_key0> (Name_exp_1 = ot.a)  (rows=0.693)
                        -> Materialize  (rows=6.93)
                            -> Table scan on <temporary>  (rows=6.93)
                                -> Temporary table with deduplication  (rows=6.93)
                                    -> Table scan on it  (rows=48)
            -> Hash
                -> Table scan on dummy  (rows=1)
                    -> Materialize  (rows=1)
                        -> Rows fetched before execution  (rows=1)

DELETE ot.* FROM t1 ot, (SELECT 1) AS dummy
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it);
SELECT * FROM t1;
a	b
1	10
ROLLBACK;
CREATE PROCEDURE del()
DELETE ot.* FROM t1 ot, (SELECT 1) AS dummy
WHERE ot.b<0 OR ot.a IN (SELECT it.a+1 FROM t2 it);
SELECT * FROM t1;
a	b
1	10
2	20
3	30
BEGIN;
CALL del();
SELECT * FROM t1;
a	b
1	10
ROLLBACK;
BEGIN;
CALL del();
SELECT * FROM t1;
a	b
1	10
ROLLBACK;
SELECT * FROM t1;
a	b
1	10
2	20
3	30
DROP PROCEDURE del;
# Testing WL#12885 together with WL#13425
# WL#12885 with semijoin:
EXPLAIN SELECT * FROM t1 WHERE (SELECT SUM(a) FROM t1) IN (SELECT b FROM t1);
EXPLAIN
-> Inner hash join (no condition)  (rows=0.1)
    -> Table scan on t1  (rows=1)
    -> Hash
        -> Nested loop inner join (LooseScan)  (rows=0.1)
            -> Remove duplicates from input grouped on t1.b  (rows=1)
                -> Sort: t1.b  (rows=1)
                    -> Table scan on t1  (rows=1)
            -> Filter: (derived_1_2.`SUM(a)` = t1.b)  (rows=0.01)
                -> Covering index lookup on derived_1_2 using <auto_key0> (SUM(a) = t1.b)  (rows=0.1)
                    -> Materialize  (rows=1)
                        -> Aggregate: sum(t1.a)  (rows=1)
                            -> Table scan on t1  (rows=1)

# WL#12885 with WL#13425:
# Scalar subquery as left expr
EXPLAIN SELECT * FROM t1 WHERE (SELECT SUM(a) FROM t1) IN (SELECT b FROM t1) OR a>3;
EXPLAIN
-> Filter: ((derived_1_3.Name_exp_1 is not null) or (t1.a > 3))  (rows=1)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on t1  (rows=1)
        -> Hash
            -> Nested loop left join  (rows=1)
                -> Table scan on derived_1_2  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: sum(t1.a)  (rows=1)
                            -> Table scan on t1  (rows=1)
                -> Filter: (derived_1_2.`SUM(a)` = derived_1_3.Name_exp_1)  (rows=0.01)
                    -> Covering index lookup on derived_1_3 using <auto_key0> (Name_exp_1 = derived_1_2.`SUM(a)`)  (rows=0.1)
                        -> Materialize  (rows=1)
                            -> Sort with duplicate removal: t1.b  (rows=1)
                                -> Table scan on t1  (rows=1)

# Scalar subquery as right expr
EXPLAIN SELECT * FROM t1 WHERE 36 IN (SELECT (SELECT SUM(a) FROM t1)+b FROM t1) OR a>3;
EXPLAIN
-> Filter: ((derived_1_2.Name_exp_1 is not null) or (t1.a > 3))  (rows=1)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on t1  (rows=1)
        -> Hash
            -> Covering index lookup on derived_1_2 using <auto_key0> (Name_exp_1 = 36)  (rows=0.1)
                -> Materialize  (rows=1)
                    -> Sort with duplicate removal: Name_exp_1  (rows=1)
                        -> Left hash join (no condition)  (rows=1)
                            -> Table scan on t1  (rows=1)
                            -> Hash
                                -> Table scan on derived_2_3  (rows=1)
                                    -> Materialize  (rows=1)
                                        -> Aggregate: sum(t1.a)  (rows=1)
                                            -> Table scan on t1  (rows=1)

# Scalar subquery in EXISTS
EXPLAIN SELECT * FROM t1 WHERE EXISTS (SELECT * FROM t1 WHERE (SELECT SUM(a) FROM t1)=b) OR a>3;
EXPLAIN
-> Filter: ((derived_1_2.Name_exp_1 is not null) or (t1.a > 3))  (rows=1)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on t1  (rows=1)
        -> Hash
            -> Table scan on derived_1_2  (rows=0.1)
                -> Materialize  (rows=0.1)
                    -> Limit: 1 row(s)  (rows=0.1)
                        -> Nested loop inner join  (rows=0.1)
                            -> Table scan on t1  (rows=1)
                            -> Filter: (derived_2_3.`SUM(a)` = t1.b)  (rows=0.01)
                                -> Covering index lookup on derived_2_3 using <auto_key0> (SUM(a) = t1.b)  (rows=0.1)
                                    -> Materialize  (rows=1)
                                        -> Aggregate: sum(t1.a)  (rows=1)
                                            -> Table scan on t1  (rows=1)

# Scalar subquery in NOT EXISTS
EXPLAIN SELECT * FROM t1 WHERE NOT EXISTS (SELECT * FROM t1 WHERE (SELECT SUM(a) FROM t1)>b) OR a>3;
EXPLAIN
-> Filter: ((derived_1_2.Name_exp_1 is null) or (t1.a > 3))  (rows=1)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on t1  (rows=1)
        -> Hash
            -> Table scan on derived_1_2  (rows=0.333)
                -> Materialize  (rows=0.333)
                    -> Limit: 1 row(s)  (rows=0.333)
                        -> Inner hash join (no condition), extra conditions: (derived_2_3.`SUM(a)` > t1.b)  (rows=0.333)
                            -> Table scan on t1  (rows=1)
                            -> Hash
                                -> Table scan on derived_2_3  (rows=1)
                                    -> Materialize  (rows=1)
                                        -> Aggregate: sum(t1.a)  (rows=1)
                                            -> Table scan on t1  (rows=1)

# Scalar subquery in NOT IN and non-nullables (antijoin)
EXPLAIN SELECT * FROM t1 WHERE 36 NOT IN (SELECT COALESCE((SELECT SUM(a) FROM t1),b,55) FROM t1) OR a>3;
EXPLAIN
-> Filter: ((derived_1_2.Name_exp_1 is null) or (t1.a > 3))  (rows=1)
    -> Left hash join (no condition)  (rows=1)
        -> Table scan on t1  (rows=1)
        -> Hash
            -> Covering index lookup on derived_1_2 using <auto_key0> (Name_exp_1 = 36)  (rows=0.1)
                -> Materialize  (rows=1)
                    -> Sort with duplicate removal: Name_exp_1  (rows=1)
                        -> Left hash join (no condition)  (rows=1)
                            -> Table scan on t1  (rows=1)
                            -> Hash
                                -> Table scan on derived_2_3  (rows=1)
                                    -> Materialize  (rows=1)
                                        -> Aggregate: sum(t1.a)  (rows=1)
                                            -> Table scan on t1  (rows=1)

DROP TABLE t1,t2;
DROP view v2;
# Bug#30697743 WL#13425: ASSERT: !THD->IS_ERROR()' IN SELECT_LEX::PREPARE() AT SQL/SQL_RESOLVE
CREATE TABLE t1 (col_varchar_key VARCHAR(1));
CREATE TABLE t2
SELECT 1 FROM t1 WHERE
col_varchar_key IN (SELECT  1 FROM t1
WHERE ('f', 'f') IN (SELECT 1, COUNT(1) FROM t1));
ERROR 22007: Truncated incorrect DOUBLE value: 'f'
DROP TABLE t1;
# Bug#30709889 ASSERT: &SUBS_SELECT->FIELDS_LIST == &SUBS_SELECT->ITEM_LIST && SUBS_SELECT->IT
CREATE TABLE t1 (
field2 VARCHAR(2),
field3 BIGINT
);
CREATE TABLE t2 (
col_int INT,
pk INT
);
SELECT 1 FROM t1
WHERE (field2 ,field3) IN
(
SELECT STRAIGHT_JOIN
1 AS field2 ,
( SELECT 1 AS SQ1_field1 FROM t2 AS SQ1_alias1
WHERE SQ1_alias1.col_int != alias1.pk) AS field3
FROM t2 AS alias1 GROUP BY field2,field3
);
1
DROP TABLE t2,t1;
# Bug#31018642 RESULT MISMATCHES BETWEEN TRUNK & WORKLOG
# Verify that <> is decorrelated inside an AND-ed negated predicate
SET OPTIMIZER_SWITCH="semijoin=off";
CREATE TABLE t1(a INT);
CREATE TABLE t2(b INT);
INSERT INTO t1 VALUES(1);
INSERT INTO t2 VALUES(2),(3);
ANALYZE TABLE t1,t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN SELECT * FROM t1
WHERE NOT EXISTS(SELECT * FROM t2 WHERE t1.a<>t2.b);
EXPLAIN
-> Filter: (derived_1_2.Name_exp_1 is null)  (rows=0.127)
    -> Left hash join (no condition), extra conditions: (t1.a <> derived_1_2.Name_exp_2)  (rows=1.27)
        -> Table scan on t1  (rows=1)
        -> Hash
            -> Table scan on derived_1_2  (rows=1.41)
                -> Materialize  (rows=1.41)
                    -> Sort with duplicate removal: t2.b  (rows=1.41)
                        -> Table scan on t2  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1
WHERE NOT EXISTS(SELECT * FROM t2 WHERE t1.a<>t2.b);
a
EXPLAIN SELECT * FROM t1
WHERE NOT EXISTS(SELECT * FROM t2 WHERE t1.a<>t2.b) OR t1.a>0;
ERROR HY000: Statement requires a transform of a subquery to a non-SET operation (like IN2EXISTS, or subquery-to-LATERAL-derived-table). This is not allowed with optimizer switch 'subquery_to_derived' on.
EXPLAIN SELECT * FROM t1
WHERE NOT EXISTS(SELECT * FROM t2 WHERE t1.a<>t2.b) AND t1.a>0;
EXPLAIN
-> Filter: (derived_1_2.Name_exp_1 is null)  (rows=0.0424)
    -> Left hash join (no condition), extra conditions: (t1.a <> derived_1_2.Name_exp_2)  (rows=0.424)
        -> Filter: (t1.a > 0)  (rows=0.333)
            -> Table scan on t1  (rows=1)
        -> Hash
            -> Table scan on derived_1_2  (rows=1.41)
                -> Materialize  (rows=1.41)
                    -> Sort with duplicate removal: t2.b  (rows=1.41)
                        -> Table scan on t2  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1
WHERE NOT EXISTS(SELECT * FROM t2 WHERE t1.a<>t2.b) AND t1.a>0;
a
EXPLAIN
SELECT * FROM t1
WHERE EXISTS(SELECT * FROM t2 WHERE t1.a<>t2.b) AND t1.a>0;
ERROR HY000: Statement requires a transform of a subquery to a non-SET operation (like IN2EXISTS, or subquery-to-LATERAL-derived-table). This is not allowed with optimizer switch 'subquery_to_derived' on.
# Verify that >=, <=, >, < are also decorrelated.
EXPLAIN
SELECT * FROM t1
WHERE NOT EXISTS(SELECT * FROM t2 WHERE t1.a>=t2.b) AND t1.a>0;
EXPLAIN
-> Filter: (derived_1_2.Name_exp_1 is null)  (rows=0.0333)
    -> Left hash join (no condition), extra conditions: (t1.a >= derived_1_2.Name_exp_2)  (rows=0.333)
        -> Filter: (t1.a > 0)  (rows=0.333)
            -> Table scan on t1  (rows=1)
        -> Hash
            -> Table scan on derived_1_2  (rows=1.41)
                -> Materialize  (rows=1.41)
                    -> Sort with duplicate removal: t2.b  (rows=1.41)
                        -> Table scan on t2  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
EXPLAIN
SELECT * FROM t1
WHERE NOT EXISTS(SELECT * FROM t2 WHERE t1.a>t2.b) AND t1.a>0;
EXPLAIN
-> Filter: (derived_1_2.Name_exp_1 is null)  (rows=0.0333)
    -> Left hash join (no condition), extra conditions: (t1.a > derived_1_2.Name_exp_2)  (rows=0.333)
        -> Filter: (t1.a > 0)  (rows=0.333)
            -> Table scan on t1  (rows=1)
        -> Hash
            -> Table scan on derived_1_2  (rows=1.41)
                -> Materialize  (rows=1.41)
                    -> Sort with duplicate removal: t2.b  (rows=1.41)
                        -> Table scan on t2  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
EXPLAIN
SELECT * FROM t1
WHERE NOT EXISTS(SELECT * FROM t2 WHERE t1.a<=t2.b) AND t1.a>0;
EXPLAIN
-> Filter: (derived_1_2.Name_exp_1 is null)  (rows=0.0333)
    -> Left hash join (no condition), extra conditions: (t1.a <= derived_1_2.Name_exp_2)  (rows=0.333)
        -> Filter: (t1.a > 0)  (rows=0.333)
            -> Table scan on t1  (rows=1)
        -> Hash
            -> Table scan on derived_1_2  (rows=1.41)
                -> Materialize  (rows=1.41)
                    -> Sort with duplicate removal: t2.b  (rows=1.41)
                        -> Table scan on t2  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
EXPLAIN
SELECT * FROM t1
WHERE NOT EXISTS(SELECT * FROM t2 WHERE t1.a<t2.b) AND t1.a>0;
EXPLAIN
-> Filter: (derived_1_2.Name_exp_1 is null)  (rows=0.0333)
    -> Left hash join (no condition), extra conditions: (t1.a < derived_1_2.Name_exp_2)  (rows=0.333)
        -> Filter: (t1.a > 0)  (rows=0.333)
            -> Table scan on t1  (rows=1)
        -> Hash
            -> Table scan on derived_1_2  (rows=1.41)
                -> Materialize  (rows=1.41)
                    -> Sort with duplicate removal: t2.b  (rows=1.41)
                        -> Table scan on t2  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
# Reverse the order of arguments:
EXPLAIN
SELECT * FROM t1
WHERE NOT EXISTS(SELECT * FROM t2 WHERE t2.b<t1.a) AND t1.a>0;
EXPLAIN
-> Filter: (derived_1_2.Name_exp_1 is null)  (rows=0.0333)
    -> Left hash join (no condition), extra conditions: (t1.a > derived_1_2.Name_exp_2)  (rows=0.333)
        -> Filter: (t1.a > 0)  (rows=0.333)
            -> Table scan on t1  (rows=1)
        -> Hash
            -> Table scan on derived_1_2  (rows=1.41)
                -> Materialize  (rows=1.41)
                    -> Sort with duplicate removal: t2.b  (rows=1.41)
                        -> Table scan on t2  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
DROP TABLE t1,t2;
SET OPTIMIZER_SWITCH="semijoin=on";
#
# Bug #31941934: WL#14071: ASSERTION `JOIN == NULLPTR' FAILED|SQL/SQL_SELECT.CC
#
CREATE TABLE t1 ( pk INTEGER );
CREATE TABLE t2 ( a INTEGER );
CREATE TABLE t3 ( b INTEGER );
SELECT *
FROM t1 LEFT JOIN t2 ON 2 IN (
SELECT COUNT(*) FROM t1
WHERE NOT EXISTS ( SELECT b FROM t3 )
GROUP BY pk
);
pk	a
DROP TABLE t1, t2, t3;
#
# Bug#35014318 Exists query assertion error
# Bug#34746261 Forced secondary engine execution assertion error
#
# This issue showed deficiencies with table value constructor
# when used in a [NOT] EXISTS subquery
CREATE TABLE t1 (c0 INT);
INSERT INTO t1 VALUES (1), (2);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT 1 FROM t1 WHERE NOT EXISTS (VALUES ROW(1),ROW(2));
1
SELECT c0 FROM t1 WHERE NOT EXISTS (VALUES ROW(1),ROW(2));
c0
SELECT 1 FROM t1 WHERE EXISTS (VALUES ROW(1),ROW(2));
1
1
1
SELECT c0 FROM t1 WHERE EXISTS (VALUES ROW(1),ROW(2));
c0
1
2
SELECT 1 FROM (SELECT 5) t1(c0) WHERE EXISTS (VALUES ROW(1),ROW(2));
1
1
# This will do the transform in spite of the presence of
# LIMIT/OFFSET since we can compute at prepare time whether the
# result set will be empty or not
SELECT c0 FROM t1 WHERE EXISTS (VALUES ROW(1),ROW(2) LIMIT 1 OFFSET 0);
c0
1
2
SELECT c0 FROM t1 WHERE EXISTS (VALUES ROW(1),ROW(2) LIMIT 1 OFFSET 1);
c0
1
2
SELECT c0 FROM t1 WHERE EXISTS (VALUES ROW(1),ROW(2) LIMIT 1 OFFSET 2);
c0
SELECT c0 FROM t1 WHERE EXISTS (VALUES ROW(1),ROW(2) LIMIT 0);
c0
SELECT c0 FROM t1 WHERE EXISTS (VALUES ROW(1),ROW(2) LIMIT 1);
c0
1
2
# This does not use semi-join since we can't short-circuit due
# to dynamic parameter. Also fixed assert error due to missing
# type for ? (not transform dependent).
PREPARE p FROM 'SELECT c0 FROM t1
                WHERE EXISTS (VALUES ROW(1),ROW(2) LIMIT 1 OFFSET ?)';
SET @n=0;
EXECUTE p USING @n;
c0
1
2
SET @n=1;
EXECUTE p USING @n;
c0
1
2
SET @n=2;
EXECUTE p USING @n;
c0
PREPARE p FROM 'SELECT c0 FROM t1 WHERE EXISTS (VALUES ROW(1),ROW(2) LIMIT ?)';
SET @n=0;
EXECUTE p USING @n;
c0
SET @n=1;
EXECUTE p USING @n;
c0
1
2
DROP PREPARE p;
DROP TABLE t1;
# Bug#35804794: mysqld assertion failure in
#               Query_block::replace_subquery_in_expr
CREATE TABLE t1 (col varchar(1) CHARACTER SET ASCII);
SELECT col
FROM t1
WHERE col >= (SELECT MAX(CONCAT('å' COLLATE utf8mb4_la_0900_as_cs)) FROM t1);
col
DROP TABLE t1;
SET EXPLAIN_FORMAT=default;
