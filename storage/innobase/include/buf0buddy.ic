/*****************************************************************************

Copyright (c) 2006, 2025, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License, version 2.0, as published by the
Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON><PERSON>ITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0,
for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

*****************************************************************************/

/** @file include/buf0buddy.ic
 Binary buddy allocator for compressed pages

 Created December 2006 by Marko Makela
 *******************************************************/

#include "buf0buddy.h"
#include "buf0buf.h"

/** Allocate a block.
@param[in,out]  buf_pool        buffer pool instance
@param[in]      i               index of buf_pool->zip_free[]
                                or BUF_BUDDY_SIZES
@return allocated block, never NULL */
void *buf_buddy_alloc_low(buf_pool_t *buf_pool, ulint i) MY_ATTRIBUTE((malloc));

/** Deallocate a block.
@param[in]      buf_pool        buffer pool instance
@param[in]      buf             block to be freed, must not be pointed to
                                by the buffer pool
@param[in]      i               index of buf_pool->zip_free[],
                                or BUF_BUDDY_SIZES
@param[in]      has_zip_free    whether has zip_free_mutex */
void buf_buddy_free_low(buf_pool_t *buf_pool, void *buf, ulint i,
                        bool has_zip_free);

/** Get the index of buf_pool->zip_free[] for a given block size.
 @return index of buf_pool->zip_free[], or BUF_BUDDY_SIZES */
static inline ulint buf_buddy_get_slot(ulint size) /*!< in: block size */
{
  ulint i;
  ulint s;

  ut_ad(size >= UNIV_ZIP_SIZE_MIN);

  for (i = 0, s = BUF_BUDDY_LOW; s < size; i++, s <<= 1) {
  }

  ut_ad(i <= BUF_BUDDY_SIZES);
  return (i);
}

/** Allocate a block. This function should only be used for allocating
compressed page frames. The thread calling this function must hold
buf_pool->LRU_list_mutex and must not hold buf_pool->zip_mutex or any
block->mutex.
@param[in,out]  buf_pool        buffer pool in which the page resides
@param[in]      size            compressed page size, between
                                UNIV_ZIP_SIZE_MIN and UNIV_PAGE_SIZE
@return allocated block, never NULL */
static inline byte *buf_buddy_alloc(buf_pool_t *buf_pool, ulint size) {
  ut_ad(ut_is_2pow(size));
  ut_ad(size >= UNIV_ZIP_SIZE_MIN);
  ut_ad(size <= UNIV_PAGE_SIZE);

  return (static_cast<byte *>(
      buf_buddy_alloc_low(buf_pool, buf_buddy_get_slot(size))));
}

/** Deallocate a block.
@param[in,out]  buf_pool        buffer pool in which the block resides
@param[in]      buf             block to be freed, must not be pointed to
                                by the buffer pool
@param[in]      size            block size, up to UNIV_PAGE_SIZE */
static inline void buf_buddy_free(buf_pool_t *buf_pool, void *buf, ulint size) {
  ut_ad(ut_is_2pow(size));
  ut_ad(size >= UNIV_ZIP_SIZE_MIN);
  ut_ad(size <= UNIV_PAGE_SIZE);

  buf_buddy_free_low(buf_pool, buf, buf_buddy_get_slot(size), false);
}
