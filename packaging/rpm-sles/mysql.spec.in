# Copyright (c) 2000, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

%global mysql_vendor    Oracle and/or its affiliates
%global mysqldatadir    /var/lib/mysql

# Pass --define 'with_cluster 1' to build with cluster support
%{?with_cluster: %global cluster 1}

%if 0%{?cluster}
%global with_meb    0
%global with_router 0
%else
%if 0%{?commercial}
%global with_meb    1
%else
%global with_meb    0
%endif
%global with_router 1
%endif

# Pass path to mecab lib
%{?with_mecab: %global mecab_option -DWITH_MECAB=%{with_mecab}}
%{?with_mecab: %global mecab 1}

# Pass path to the AWS SDK install
%{?with_aws_sdk: %global aws_sdk_option -DWITH_KEYRING_AWS=ON -DWITH_AWS_SDK=%{with_aws_sdk}}
%{?with_aws_sdk: %global aws_sdk 1}
%{?with_aws_1_11: %global add_component_keyring_aws 1}

# Regression tests may take a long time, override the default to skip them
%{!?runselftest:%global runselftest 0}

%{!?product_suffix:              %global product_suffix community}
%{!?compilation_comment_release: %global compilation_comment_release MySQL Community - GPL}
%{!?compilation_comment_debug:   %global compilation_comment_debug MySQL Community - GPL - Debug}
%{!?compilation_comment_server_release: %global compilation_comment_server_release MySQL Community Server - GPL}
%{!?compilation_comment_server_debug:   %global compilation_comment_server_debug MySQL Community Server - GPL - Debug}
%{!?src_base:                    %global src_base mysql%{?cluster:-cluster-gpl}}

%global src_dir               %{src_base}-@VERSION_SRC@

# multiarch
%global multiarchs            ppc %{power64} %{ix86} x86_64 %{sparc}

%global license_files_server  %{src_dir}/LICENSE %{src_dir}/README

%if 0%{?commercial}
%global license_type          Commercial
%else
%global license_type          GPLv2
%endif

%if 0%{?suse_version} == 1500
%global dist                  .sl15
%global sles15                1
%global add_fido_plugins      1
%endif

%if 0%{?suse_version} == 1550
%global dist                  .sl16
%global sles16                1
%global add_fido_plugins      1
%endif

# https://en.opensuse.org/openSUSE:Systemd_packaging_guidelines
%{!?_tmpfilesdir:             %global _tmpfilesdir /usr/lib/tmpfiles.d}

%global min                   8.0.11

Name:           mysql%{?cluster:-cluster}-%{product_suffix}
Summary:        A very fast and reliable SQL database server
Group:          Applications/Databases
Version:        @MYSQL_NO_DASH_VERSION@
Release:        1@MYSQL_VERSION_EXTRA_DOT@%{?commercial:.1}%{?dist}
License:        Copyright (c) 2000, @MYSQL_COPYRIGHT_YEAR@, %{mysql_vendor}. Under %{?license_type} license as shown in the Description field.
Source0:        https://cdn.mysql.com/Downloads/MySQL-@MYSQL_BASE_VERSION@/%{src_dir}.tar.gz
URL:            http://www.mysql.com/
Packager:       MySQL Release Engineering <<EMAIL>>
Vendor:         %{mysql_vendor}
BuildRequires:  cmake >= 3.14.6
BuildRequires:  bison >= 3.0.4
BuildRequires:  elfutils
BuildRequires:  perl
BuildRequires:  perl(Config)
BuildRequires:  perl(Cwd)
BuildRequires:  perl(Data::Dumper)
BuildRequires:  perl(File::Basename)
BuildRequires:  perl(File::Copy)
BuildRequires:  perl(File::Find)
BuildRequires:  perl(File::Path)
BuildRequires:  perl(File::Spec)
BuildRequires:  perl(File::Spec::Functions)
BuildRequires:  perl(File::Temp)
BuildRequires:  perl(Getopt::Long)
BuildRequires:  perl(IO::File)
BuildRequires:  perl(IO::Select)
BuildRequires:  perl(IO::Socket)
BuildRequires:  perl(IO::Socket::INET)
BuildRequires:  perl(JSON)
BuildRequires:  perl(Sys::Hostname)
BuildRequires:  libaio-devel
%if 0%{?commercial}
BuildRequires:  cyrus-sasl-gssapi
BuildRequires:  krb5-devel
BuildRequires:  libcurl-devel
%endif
BuildRequires:  libnuma-devel
BuildRequires:  ncurses-devel
BuildRequires:  openssl-devel
BuildRequires:  zlib-devel
%{?sles15:BuildRequires: gcc13 gcc13-c++}
%{?sles15:BuildRequires: rpcgen pkgconfig(libtirpc)}
BuildRequires:  systemd
BuildRequires:  pkgconfig(systemd)
BuildRequires:  systemd-rpm-macros
BuildRequires:  cyrus-sasl-devel
BuildRequires:  openldap2-devel

%global _privatelibs libabsl_.*|libprotobuf.*|libmysqlharness\.so.*|libmysqlharness_stdx\.so.*|libmysqlharness_tls\.so.*|libmysqlrouter\.so\..*|libmysqlrouter_.*\.so\..*|libfido2.*|libpolyglot.*|libjitexecutor.*
%global _privateperl perl\\((GD|hostnames|lib::mtr|lib::v1|mtr_|My::)
%global __requires_exclude ^((%{_privatelibs})|(%{_privateperl}))
%global __provides_exclude_from ^(/usr/share/(mysql|mysql-test)/.*|%{_libdir}/mysql/plugin/.*\\.so|%{_libdir}/mysql/private/.*|%{_libdir}/mysqlrouter/.*|%{_libdir}/mysqlrouter/private/.*)$

%description
The MySQL(TM) software delivers a very fast, multi-threaded, multi-user,
and robust SQL (Structured Query Language) database server. MySQL Server
is intended for mission-critical, heavy-load production systems as well
as for embedding into mass-deployed software. MySQL is a trademark of
%{mysql_vendor}

The MySQL software has Dual Licensing, which means you can use the MySQL
software free of charge under the GNU General Public License
(http://www.gnu.org/licenses/). You can also purchase commercial MySQL
licenses from %{mysql_vendor} if you do not wish to be bound by the terms of
the GPL. See the chapter "Licensing and Support" in the manual for
further info.

The MySQL web site (http://www.mysql.com/) provides the latest
news and information about the MySQL software. Also please see the
documentation and the manual for more information.

%package        server
Summary:        A very fast and reliable SQL database server
Group:          Applications/Databases
Requires:       coreutils
Requires:       grep
Requires:       procps
Requires:       net-tools
Requires:       perl-base
Requires:       %{name}-client >= %{min}
Requires:       %{name}-common = %{version}-%{release}
Requires:       %{name}-icu-data-files = %{version}-%{release}
%if 0%{?commercial}
Obsoletes:      mysql-commercial-bench < 5.7.8
Provides:       MySQL-server-advanced = %{version}-%{release}
Obsoletes:      MySQL-server-advanced < %{version}-%{release}
Obsoletes:      mysql-community-server < %{version}-%{release}
Obsoletes:      MySQL-embedded-advanced < %{version}-%{release}
Obsoletes:      mysql-commercial-embedded < 8.0.1
Obsoletes:      mysql-commercial-embedded-devel < 8.0.1
%if 0%{?cluster}
Provides:       MySQL-Cluster-server-advanced = %{version}-%{release}
Obsoletes:      MySQL-Cluster-server-advanced < %{version}-%{release}
Obsoletes:      MySQL-Cluster-embedded-advanced < %{version}-%{release}
Obsoletes:      mysql-commercial-server < %{version}-%{release}
Obsoletes:      mysql-cluster-community-server < %{version}-%{release}
Obsoletes:      mysql-cluster-community-embedded < 8.0.1
Obsoletes:      mysql-cluster-community-embedded-devel < 8.0.1
%endif
%endif
%if 0%{?cluster}
Provides:       MySQL-Cluster-server-gpl%{?_isa} = %{version}-%{release}
Obsoletes:      MySQL-Cluster-server-gpl < %{version}-%{release}
Obsoletes:      MySQL-Cluster-embedded-gpl < %{version}-%{release}
%endif
Obsoletes:      mysql-community-bench < 5.7.8
Obsoletes:      mysql-community-embedded < 8.0.1
Obsoletes:      mysql-community-embedded-devel < 8.0.1
Obsoletes:      MySQL-embedded < %{version}-%{release}
Obsoletes:      mysql-embedded < %{version}-%{release}
Obsoletes:      mysql-embedded-devel < %{version}-%{release}
Obsoletes:      community-mysql-bench
Obsoletes:      mysql-bench
Provides:       MySQL-server = %{version}-%{release}
Obsoletes:      MySQL-server < %{version}-%{release}
Obsoletes:      mysql < %{version}-%{release}
Obsoletes:      mysql-tools < %{version}-%{release}
Obsoletes:      mariadb-bench
Obsoletes:      mariadb-embedded
Obsoletes:      mariadb-embedded-devel
Obsoletes:      mariadb-galera
Obsoletes:      mariadb-galera-server
Obsoletes:      mariadb-server
Obsoletes:      mariadb-tools
Provides:       mysql = %{version}-%{release}
Provides:       mysql-tools = %{version}-%{release}
Requires:       systemd >= 228
PreReq:         sed
PreReq:         pwdutils
Conflicts:      otherproviders(mysql)
Conflicts:      otherproviders(mysql-debug)
Conflicts:      otherproviders(mysql-tools)

%description    server
The MySQL(TM) software delivers a very fast, multi-threaded, multi-user,
and robust SQL (Structured Query Language) database server. MySQL Server
is intended for mission-critical, heavy-load production systems as well
as for embedding into mass-deployed software. MySQL is a trademark of
%{mysql_vendor}

The MySQL software has Dual Licensing, which means you can use the MySQL
software free of charge under the GNU General Public License
(http://www.gnu.org/licenses/). You can also purchase commercial MySQL
licenses from %{mysql_vendor} if you do not wish to be bound by the terms of
the GPL. See the chapter "Licensing and Support" in the manual for
further info.

The MySQL web site (http://www.mysql.com/) provides the latest news and
information about the MySQL software.  Also please see the documentation
and the manual for more information.

This package includes the MySQL server binary as well as related utilities
to run and administer a MySQL server.

%package        server-debug
Summary:        The debug version of MySQL server
Requires:       %{name}-server = %{version}-%{release}
%if 0%{?commercial}
Obsoletes:      mysql-community-server-debug < %{version}-%{release}
%if 0%{?cluster}
Obsoletes:      mysql-commercial-server-debug < %{version}-%{release}
Obsoletes:      mysql-cluster-community-server-debug < %{version}-%{release}
%endif # cluster
%else
%if 0%{?cluster}
Obsoletes:      mysql-community-server-debug < %{version}-%{release}
%endif # cluster
%endif # commercial

%description    server-debug
This packages contains the special debug build of MySQL server.

%package        client
Summary:        MySQL database client applications and tools
Group:          Applications/Databases
Requires:       %{name}-libs >= %{min}
Requires:       %{name}-client-plugins = %{version}-%{release}
%if 0%{?commercial}
Provides:       MySQL-client-advanced = %{version}-%{release}
Obsoletes:      MySQL-client-advanced < %{version}-%{release}
Obsoletes:      mysql-community-client < %{version}-%{release}
%if 0%{?cluster}
Provides:       MySQL-Cluster-client-advanced = %{version}-%{release}
Obsoletes:      MySQL-Cluster-client-advanced < %{version}-%{release}
Obsoletes:      mysql-commercial-client < %{version}-%{release}
Obsoletes:      mysql-cluster-community-client < %{version}-%{release}
%endif
%endif
%if 0%{?cluster}
Provides:       MySQL-Cluster-client-gpl = %{version}-%{release}
Obsoletes:      MySQL-Cluster-client-gpl < %{version}-%{release}
%endif
Provides:       MySQL-client = %{version}-%{release}
Obsoletes:      MySQL-client < %{version}-%{release}
Provides:       mysql-client = %{version}-%{release}
Obsoletes:      mysql-client < %{version}-%{release}
Obsoletes:      mariadb
Obsoletes:      mariadb-client
Conflicts:      otherproviders(mysql-client)

%description    client
This package contains the standard MySQL clients and administration
tools.

%package        icu-data-files
Summary:        MySQL packaging of ICU data files

%description    icu-data-files
This package contains ICU data files needer by MySQL regular expressions.

%package        common
Summary:        MySQL database common files for server and client libs
Group:          Applications/Databases
%if 0%{?commercial}
Obsoletes:      mysql-community-common < %{version}-%{release}
%if 0%{?cluster}
Obsoletes:      mysql-commercial-common < %{version}-%{release}
Obsoletes:      mysql-cluster-community-common < %{version}-%{release}
%endif
%endif
Provides:       mysql-common = %{version}-%{release}

%description    common
This packages contains common files needed by MySQL client library and
MySQL database server


%package        test
Summary:        Test suite for the MySQL database server
Group:          Applications/Databases
Requires:       %{name}-server >= %{min}
Requires:       perl(Config)
Requires:       perl(Cwd)
Requires:       perl(Data::Dumper)
Requires:       perl(File::Basename)
Requires:       perl(File::Copy)
Requires:       perl(File::Find)
Requires:       perl(File::Path)
Requires:       perl(File::Spec)
Requires:       perl(File::Spec::Functions)
Requires:       perl(File::Temp)
Requires:       perl(Getopt::Long)
Requires:       perl(IO::File)
Requires:       perl(IO::Select)
Requires:       perl(IO::Socket)
Requires:       perl(IO::Socket::INET)
Requires:       perl(JSON)
Requires:       perl(Sys::Hostname)
%if 0%{?cluster}
Requires:       %{name}-data-node%{?_isa} >= %{min}
Requires:       %{name}-management-server%{?_isa} >= %{min}
%endif
%if 0%{?commercial}
Provides:       MySQL-test-advanced = %{version}-%{release}
Obsoletes:      MySQL-test-advanced < %{version}-%{release}
Obsoletes:      mysql-community-test < %{version}-%{release}
%if 0%{?cluster}
Provides:       MySQL-Cluster-test-advanced = %{version}-%{release}
Obsoletes:      MySQL-Cluster-test-advanced < %{version}-%{release}
Obsoletes:      mysql-commercial-test < %{version}-%{release}
Obsoletes:      mysql-cluster-community-test < %{version}-%{release}
%endif
%endif
%if 0%{?cluster}
Provides:       MySQL-Cluster-test-gpl = %{version}-%{release}
Obsoletes:      MySQL-Cluster-test-gpl < %{version}-%{release}
%endif
Provides:       MySQL-test = %{version}-%{release}
Obsoletes:      MySQL-test < %{version}-%{release}
Obsoletes:      mysql-test < %{version}-%{release}
Obsoletes:      mariadb-test
Provides:       mysql-test = %{version}-%{release}
Conflicts:      otherproviders(mysql-test)

%description    test
This package contains the MySQL regression test suite for MySQL
database server.

%package        devel
Summary:        Development header files and libraries for MySQL database client applications
Group:          Applications/Databases
Requires:       %{name}-libs >= %{min}
Requires:       openssl-devel
%if 0%{?commercial}
Provides:       MySQL-devel-advanced = %{version}-%{release}
Obsoletes:      MySQL-devel-advanced < %{version}-%{release}
Obsoletes:      mysql-community-devel < %{version}-%{release}
%if 0%{?cluster}
Provides:       MySQL-Cluster-devel-advanced = %{version}-%{release}
Obsoletes:      MySQL-Cluster-devel-advanced < %{version}-%{release}
Obsoletes:      mysql-commercial-devel < %{version}-%{release}
Obsoletes:      mysql-cluster-community-devel < %{version}-%{release}
%endif
%endif
%if 0%{?cluster}
Provides:       MySQL-Cluster-devel-gpl = %{version}-%{release}
Obsoletes:      MySQL-Cluster-devel-gpl < %{version}-%{release}
%endif
Provides:       MySQL-devel = %{version}-%{release}
Obsoletes:      MySQL-devel < %{version}-%{release}
Obsoletes:      mysql-devel < %{version}-%{release}
Obsoletes:      mariadb-devel
Obsoletes:      libmysqlclient-devel
Obsoletes:      mysql-connector-c-devel < 6.2
Provides:       mysql-devel = %{version}-%{release}
Provides:       libmysqlclient-devel = %{version}-%{release}

%description    devel
This package contains the development header files and libraries necessary
to develop MySQL client applications.

%package        libs
Summary:        Shared libraries for MySQL database client applications
Group:          Applications/Databases
Requires:       %{name}-common >= %{min}
Requires:       %{name}-client-plugins = %{version}-%{release}
%if 0%{?commercial}
Provides:       MySQL-shared-advanced = %{version}-%{release}
Obsoletes:      MySQL-shared-advanced < %{version}-%{release}
Obsoletes:      mysql-community-libs < %{version}-%{release}
%if 0%{?cluster}
Provides:       MySQL-Cluster-shared-advanced = %{version}-%{release}
Obsoletes:      MySQL-Cluster-shared-advanced < %{version}-%{release}
Obsoletes:      MySQL-shared-advanced < %{version}-%{release}
Obsoletes:      mysql-cluster-community-libs < %{version}-%{release}
%endif
%endif
%if 0%{?cluster}
Provides:       MySQL-Cluster-shared-gpl = %{version}-%{release}
Obsoletes:      MySQL-Cluster-shared-gpl < %{version}-%{release}
%endif
Provides:       MySQL-shared = %{version}-%{release}
Obsoletes:      MySQL-shared < %{version}-%{release}
Obsoletes:      mysql-libs < %{version}-%{release}
Obsoletes:      mariadb-libs
Obsoletes:      libmysqlclient22 < %{version}-%{release}
Obsoletes:      mysql-connector-c-shared < 6.2
Provides:       mysql-libs = %{version}-%{release}
Provides:       libmysqlclient22 = %{version}-%{release}

%description    libs
This package contains the shared libraries for MySQL client
applications.

%package        client-plugins
Summary:        Shared plugins for MySQL client applications
Group:          Applications/Databases
Provides:       mysql-client-plugins = %{version}-%{release}
%if 0%{?commercial}
Conflicts:      mysql-commercial-server < 8.0.21
Conflicts:      mysql-commercial-client < 8.0.22
Obsoletes:      mysql-community-client-plugins < %{version}-%{release}
%if 0%{?cluster}
Obsoletes:      mysql-commercial-client-plugins < %{version}-%{release}
%endif
%else
Conflicts:      mysql-community-server < 8.0.21
Conflicts:      mysql-community-client < 8.0.22
%endif

%description    client-plugins
This package contains the client-plugins libraries used by MySQL client applications.

%if 0%{?cluster}
%package        management-server
Summary:        MySQL Cluster Management Server Daemon
Group:          Applications/Databases
%description    management-server
This package contains the MySQL Cluster Management Server Daemon,
which reads the cluster configuration file and distributes this
information to all nodes in the cluster.

%package        data-node
Summary:        MySQL Cluster Data Node Daemon
Group:          Applications/Databases
%description    data-node
This package contains MySQL Cluster Data Node Daemon, its the process
that is used to handle all the data in tables using the NDB Cluster
storage engine. It comes in two variants: ndbd and ndbmtd, the former
is single threaded while the latter is multi-threaded.

%package        ndbclient
Summary:        Shared libraries for MySQL NDB storage engine client applications
Group:          Applications/Databases
%description    ndbclient
This package contains the shared libraries for MySQL MySQL NDB storage
engine client applications.

%package        ndbclient-devel
Summary:        Development files for MySQL NDB storage engine client applications
Group:          Applications/Databases
Requires:       %{name}-devel%{?_isa} = %{version}-%{release}
Requires:       %{name}-ndbclient%{?_isa} = %{version}-%{release}
%description    ndbclient-devel
This package contains the development header files and libraries
necessary to develop client applications for MySQL NDB storage engine.

%package        java
Summary:        MySQL Cluster Connector for Java
Group:          Applications/Databases
%description    java
This package contains MySQL Cluster Connector for Java, which includes
ClusterJ and ClusterJPA, a plugin for use with OpenJPA.

ClusterJ is a high level database API that is similar in style and
concept to object-relational mapping persistence frameworks such as
Hibernate and JPA.

%endif # cluster

%if 0%{?with_meb}
%package        backup
Summary:        MySQL Enterprise Backup
Group:          Applications/Databases
Provides:       meb = %{version}-%{release}
Provides:       meb%{?_isa} = %{version}-%{release}
Obsoletes:      meb < %{version}-%{release}
%description    backup
Implementing proper database backup and disaster recovery plans to
protect against accidental loss of data, database corruption,
hardware/operating system crashes orany natural disasters has become
one of the most important responsibilities of the Database
Administrator (DBAs).

MySQL Enterprise Backup provides DBAs with a high-performance, online
"hot" backup solution with data compression technology to ensure your
data is protected in case of downtime or an outage. MySQL is a
trademark of %{mysql_vendor}
%endif # with_meb

%if 0%{?with_router}
%package  -n   mysql-router-%{product_suffix}
Summary:       MySQL Router
Group:         Applications/Databases
Provides:      mysql-router = %{version}-%{release}
Obsoletes:     mysql-router < %{version}-%{release}
%if 0%{?commercial}
Obsoletes:     mysql-router-community < %{version}-%{release}
%endif
%description -n mysql-router-%{product_suffix}
The MySQL(TM) Router software delivers a fast, multi-threaded way of
routing connections from MySQL Clients to MySQL Servers. MySQL is a
trademark of Oracle.

The MySQL software has Dual Licensing, which means you can use the
MySQL software free of charge under the GNU General Public License
(http://www.gnu.org/licenses/). You can also purchase commercial MySQL
licenses from Oracle and/or its affiliates if you do not wish to be
bound by the terms of the GPL. See the chapter "Licensing and Support"
in the manual for further info.

The MySQL web site (http://www.mysql.com/) provides the latest news
and information about the MySQL software. Also please see the
documentation and the manual for more information.

%package   -n   mysql-router-%{product_suffix}-devel
Summary:        Development header files and libraries for MySQL Router
Group:          Applications/Databases
Provides:       mysql-router-devel = %{version}-%{release}
Obsoletes:      mysql-router-devel < %{version}-%{release}
%if 0%{?commercial}
Obsoletes:      mysql-router-community-devel < %{version}-%{release}
Requires:       mysql-router-commercial = %{version}-%{release}
%else
Requires:       mysql-router-community = %{version}-%{release}
%endif
%description -n mysql-router-%{product_suffix}-devel
This package contains the development header files and libraries
necessary to develop MySQL Router applications.
%endif # with_router

%if 0%{?suse_version}
%debug_package
%endif

%prep
%setup -q -T -a 0 -c -n %{src_dir}
pushd %{src_dir}

%build
# Fail quickly and obviously if user tries to build as root
%if 0%{?runselftest}
if [ "x$(id -u)" = "x0" ] ; then
   echo "The MySQL regression tests may fail if run as root."
   echo "If you really need to build the RPM as root, use"
   echo "--define='runselftest 0' to skip the regression tests."
   exit 1
fi
%endif

# Build debug versions of mysqld
mkdir debug
(
  cd debug
  # Attempt to remove any optimisation flags from the debug build
  optflags=$(echo "%{optflags}" | sed -e 's/-O2 / /' -e 's/-D_FORTIFY_SOURCE=2/ /' -e 's/-Wp, / /')
  cmake ../%{src_dir} \
           -DBUILD_CONFIG=mysql_release \
           -DINSTALL_LAYOUT=RPM \
           -DPIDFILE_RPM="/var/run/mysql/mysqld.pid" \
           -DLOGFILE_RPM="/var/log/mysql/mysqld.log" \
           -DCMAKE_BUILD_TYPE=Debug \
           -DCMAKE_C_FLAGS="$optflags" \
           -DCMAKE_CXX_FLAGS="$optflags" \
           -DWITH_SYSTEMD=1 \
           -DSYSTEMD_SERVICE_NAME="mysql" \
           -DSYSTEMD_PID_DIR="/run/mysql" \
           -DWITH_MEB=%{with_meb} \
           -DWITH_MLE=1 \
           -DWITH_ROUTER=%{with_router} \
%if 0%{?cluster}
           -DWITH_NDB=1 \
%endif
%if 0%{?commercial}
           -DWITH_CURL=system \
           %{?aws_sdk_option} \
%endif
           -DMYSQL_UNIX_ADDR="%{mysqldatadir}/mysql.sock" \
           -DMYSQLX_UNIX_ADDR="/var/run/mysql/mysqlx.sock" \
           -DWITH_NUMA=ON \
           %{?mecab_option} \
%if 0%{?mrs_jit_executor_lib:1}
           -DMRS_JIT_EXECUTOR_LIB="%{mrs_jit_executor_lib}" \
%endif
           -DCOMPILATION_COMMENT="%{compilation_comment_debug}" \
           -DCOMPILATION_COMMENT_SERVER="%{compilation_comment_server_debug}" \
           -DMYSQL_SERVER_SUFFIX="%{?server_suffix}"
  echo BEGIN_DEBUG_CONFIG ; egrep '^#define' include/my_config.h ; echo END_DEBUG_CONFIG
  make %{?_smp_mflags} VERBOSE=1
)

# Build full release
mkdir release
(
  cd release
  cmake ../%{src_dir} \
           -DBUILD_CONFIG=mysql_release \
           -DINSTALL_LAYOUT=RPM \
           -DPIDFILE_RPM="/var/run/mysql/mysqld.pid" \
           -DLOGFILE_RPM="/var/log/mysql/mysqld.log" \
           -DCMAKE_BUILD_TYPE=RelWithDebInfo \
           -DCMAKE_C_FLAGS="%{optflags}" \
           -DCMAKE_CXX_FLAGS="%{optflags}" \
           -DWITH_SYSTEMD=1 \
           -DSYSTEMD_SERVICE_NAME="mysql" \
           -DSYSTEMD_PID_DIR="/run/mysql" \
           -DWITH_MEB=%{with_meb} \
           -DWITH_MLE=1 \
           -DWITH_ROUTER=%{with_router} \
%if 0%{?cluster}
           -DWITH_NDB=1 \
%endif
%if 0%{?commercial}
           -DWITH_CURL=system \
           %{?aws_sdk_option} \
%endif
           -DMYSQL_UNIX_ADDR="%{mysqldatadir}/mysql.sock" \
           -DMYSQLX_UNIX_ADDR="/var/run/mysql/mysqlx.sock" \
           -DWITH_NUMA=ON \
           %{?mecab_option} \
%if 0%{?mrs_jit_executor_lib:1}
           -DMRS_JIT_EXECUTOR_LIB="%{mrs_jit_executor_lib}" \
%endif
           -DCOMPILATION_COMMENT="%{compilation_comment_release}" \
           -DCOMPILATION_COMMENT_SERVER="%{compilation_comment_server_release}" \
           -DMYSQL_SERVER_SUFFIX="%{?server_suffix}"
  echo BEGIN_NORMAL_CONFIG ; egrep '^#define' include/my_config.h ; echo END_NORMAL_CONFIG
  make %{?_smp_mflags} VERBOSE=1
)

%install
MBD=$RPM_BUILD_DIR/%{src_dir}

# Ensure that needed directories exists
install -d -m 0751 %{buildroot}/var/lib/mysql
install -d -m 0755 %{buildroot}/var/run/mysql
install -d -m 0750 %{buildroot}/var/log/mysql
install -d -m 0750 %{buildroot}/var/lib/mysql-files
install -d -m 0750 %{buildroot}/var/lib/mysql-keyring


# Install all binaries
cd $MBD/release
make DESTDIR=%{buildroot} install

# Install logrotate and autostart
install -D -m 0644 packaging/rpm-common/mysql.logrotate %{buildroot}%{_sysconfdir}/logrotate.d/mysql
install -D -m 0644 packaging/rpm-common/my.cnf %{buildroot}%{_sysconfdir}/my.cnf
install -d %{buildroot}%{_sysconfdir}/my.cnf.d

# Make library links
install -d -m 0755 %{buildroot}%{_sysconfdir}/ld.so.conf.d
echo "%{_libdir}/mysql" > %{buildroot}%{_sysconfdir}/ld.so.conf.d/mysql-%{_arch}.conf

# Router directories
%if 0%{?with_router}
install -d -m 0755 %{buildroot}/var/log/mysqlrouter
install -d -m 0755 %{buildroot}/var/run/mysqlrouter

install -D -p -m 0644 packaging/rpm-common/mysqlrouter.conf %{buildroot}%{_sysconfdir}/mysqlrouter/mysqlrouter.conf
%endif

# rcmysql symlink
install -d %{buildroot}%{_sbindir}
ln -sf %{_sbindir}/service %{buildroot}%{_sbindir}/rcmysql

%check
%if 0%{?runselftest} || 0%{?with_unittests}
pushd release
make test-unit || true
%endif
%if 0%{?runselftest}
export MTR_BUILD_THREAD=auto
pushd mysql-test
./mtr \
    --mem --parallel=auto --force --retry=0 \
    --mysqld=--binlog-format=mixed \
    --suite-timeout=720 --testcase-timeout=30 \
    --clean-vardir
rm -r $(readlink var) var
%endif

%pre server
/usr/sbin/groupadd -r mysql >/dev/null 2>&1 || :
/usr/sbin/useradd -g mysql -o -r -d /var/lib/mysql -s /bin/false \
    -c "MySQL Server" -u 60 mysql >/dev/null 2>&1 || :
%service_add_pre mysql.service

%post server
[ -e /var/log/mysqld.log ] || install -m0640 -omysql -gmysql /dev/null /var/log/mysqld.log >/dev/null 2>&1 || :
[ -e /var/log/mysqld.log.00.json ] || install -m0640 -omysql -gmysql /dev/null /var/log/mysqld.log.00.json >/dev/null 2>&1 || :
%service_add_post mysql.service
/usr/bin/systemd-tmpfiles --create %{_tmpfilesdir}/mysql.conf >/dev/null 2>&1 || :
if [ $1 -eq 1 ]; then
    /bin/systemctl enable mysql.service >/dev/null 2>&1 || :
fi

%preun server
%service_del_preun mysql.service

%postun server
%service_del_postun mysql.service

%post libs -p /sbin/ldconfig

%postun libs -p /sbin/ldconfig

%if 0%{?compatlib}
%post libs-compat -p /sbin/ldconfig

%postun libs-compat -p /sbin/ldconfig
%endif

%if 0%{?with_router}
%pre -n mysql-router-%{product_suffix}
/usr/sbin/groupadd -r mysqlrouter >/dev/null 2>&1 || :
/usr/sbin/useradd -M -N -g mysqlrouter -r -d /var/lib/mysqlrouter -s /bin/false \
    -c "MySQL Router" mysqlrouter >/dev/null 2>&1 || :

%post -n mysql-router-%{product_suffix}
/sbin/ldconfig
%systemd_post mysqlrouter.service

%preun -n mysql-router-%{product_suffix}
%systemd_preun mysqlrouter.service

%postun -n mysql-router-%{product_suffix}
/sbin/ldconfig
%systemd_postun_with_restart mysqlrouter.service
%endif # with_router

%files server
%defattr(-, root, root, -)
%doc %{?license_files_server}
%doc %{src_dir}/Docs/INFO_SRC*
%doc release/Docs/INFO_BIN*
%attr(644, root, root) %{_mandir}/man1/innochecksum.1*
%attr(644, root, root) %{_mandir}/man1/ibd2sdi.1*
%attr(644, root, root) %{_mandir}/man1/my_print_defaults.1*
%attr(644, root, root) %{_mandir}/man1/myisam_ftdump.1*
%attr(644, root, root) %{_mandir}/man1/myisamchk.1*
%attr(644, root, root) %{_mandir}/man1/myisamlog.1*
%attr(644, root, root) %{_mandir}/man1/myisampack.1*
%attr(644, root, root) %{_mandir}/man8/mysqld.8*
%attr(644, root, root) %{_mandir}/man1/mysqldumpslow.1*
%attr(644, root, root) %{_mandir}/man1/mysql_secure_installation.1*
%attr(644, root, root) %{_mandir}/man1/mysqlman.1*
%attr(644, root, root) %{_mandir}/man1/mysql_tzinfo_to_sql.1*
%attr(644, root, root) %{_mandir}/man1/perror.1*

%config(noreplace) %{_sysconfdir}/my.cnf
%dir %{_sysconfdir}/my.cnf.d

%attr(755, root, root) %{_bindir}/innochecksum
%attr(755, root, root) %{_bindir}/ibd2sdi
%attr(755, root, root) %{_bindir}/my_print_defaults
%attr(755, root, root) %{_bindir}/myisam_ftdump
%attr(755, root, root) %{_bindir}/myisamchk
%attr(755, root, root) %{_bindir}/myisamlog
%attr(755, root, root) %{_bindir}/myisampack
%attr(755, root, root) %{_bindir}/mysql_secure_installation
%attr(755, root, root) %{_bindir}/mysql_tzinfo_to_sql
%attr(755, root, root) %{_bindir}/mysqldumpslow
%attr(755, root, root) %{_bindir}/perror
%attr(755, root, root) %{_bindir}/mysqld_pre_systemd
%attr(755, root, root) %{_sbindir}/mysqld
%attr(755, root, root) %{_sbindir}/mysqld-debug
%attr(755, root, root) %{_sbindir}/rcmysql

%dir %{_libdir}/mysql/private
%attr(755, root, root) %{_libdir}/mysql/private/libprotobuf-lite.so.24.4.0
%attr(755, root, root) %{_libdir}/mysql/private/libprotobuf.so.24.4.0
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_bad_any_cast_impl.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_bad_optional_access.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_bad_variant_access.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_base.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_city.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_civil_time.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_cord_internal.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_cord.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_cordz_functions.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_cordz_handle.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_cordz_info.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_cordz_sample_token.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_crc32c.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_crc_cord_state.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_crc_cpu_detect.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_crc_internal.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_debugging_internal.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_demangle_internal.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_die_if_null.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_examine_stack.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_exponential_biased.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_failure_signal_handler.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_flags_commandlineflag_internal.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_flags_commandlineflag.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_flags_config.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_flags_internal.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_flags_marshalling.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_flags_parse.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_flags_private_handle_accessor.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_flags_program_name.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_flags_reflection.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_flags.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_flags_usage_internal.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_flags_usage.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_graphcycles_internal.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_hash.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_hashtablez_sampler.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_int128.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_kernel_timeout_internal.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_leak_check.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_entry.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_flags.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_globals.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_initialize.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_internal_check_op.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_internal_conditions.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_internal_format.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_internal_globals.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_internal_log_sink_set.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_internal_message.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_internal_nullguard.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_internal_proto.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_severity.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_log_sink.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_low_level_hash.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_malloc_internal.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_periodic_sampler.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_random_distributions.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_random_internal_distribution_test_util.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_random_internal_platform.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_random_internal_pool_urbg.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_random_internal_randen_hwaes_impl.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_random_internal_randen_hwaes.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_random_internal_randen_slow.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_random_internal_randen.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_random_internal_seed_material.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_random_seed_gen_exception.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_random_seed_sequences.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_raw_hash_set.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_raw_logging_internal.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_scoped_set_env.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_spinlock_wait.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_stacktrace.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_statusor.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_status.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_strerror.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_str_format_internal.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_strings_internal.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_strings.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_string_view.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_symbolize.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_synchronization.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_throw_delegate.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_time.so
%attr(755, root, root) %{_libdir}/mysql/private/libabsl_time_zone.so
%if 0%{?commercial}
%attr(755, root, root) %{_libdir}/mysql/private/libpolyglot.so
%endif # commercial

%dir %{_libdir}/mysql/plugin
%attr(755, root, root) %{_libdir}/mysql/plugin/adt_null.so
%attr(755, root, root) %{_libdir}/mysql/plugin/auth_socket.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_audit_api_message_emit.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_keyring_file.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_log_filter_dragnet.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_log_sink_json.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_log_sink_syseventlog.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_mysqlbackup.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_query_attributes.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_reference_cache.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_validate_password.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_connection_control.so
%attr(755, root, root) %{_libdir}/mysql/plugin/connection_control.so
%attr(755, root, root) %{_libdir}/mysql/plugin/ddl_rewriter.so
%attr(755, root, root) %{_libdir}/mysql/plugin/group_replication.so
%attr(755, root, root) %{_libdir}/mysql/plugin/ha_example.so
%attr(755, root, root) %{_libdir}/mysql/plugin/ha_mock.so
%attr(755, root, root) %{_libdir}/mysql/plugin/keyring_udf.so
%attr(755, root, root) %{_libdir}/mysql/plugin/locking_service.so
%attr(755, root, root) %{_libdir}/mysql/plugin/mypluglib.so
%attr(755, root, root) %{_libdir}/mysql/plugin/mysql_clone.so
%attr(755, root, root) %{_libdir}/mysql/plugin/mysql_no_login.so
%attr(755, root, root) %{_libdir}/mysql/plugin/rewrite_example.so
%attr(755, root, root) %{_libdir}/mysql/plugin/rewriter.so
%attr(755, root, root) %{_libdir}/mysql/plugin/semisync_master.so
%attr(755, root, root) %{_libdir}/mysql/plugin/semisync_replica.so
%attr(755, root, root) %{_libdir}/mysql/plugin/semisync_slave.so
%attr(755, root, root) %{_libdir}/mysql/plugin/semisync_source.so
%attr(755, root, root) %{_libdir}/mysql/plugin/validate_password.so
%if 0%{?mecab}
%{_libdir}/mysql/mecab
%attr(755, root, root) %{_libdir}/mysql/plugin/libpluginmecab.so
%endif
%if 0%{?commercial}
%attr(755, root, root) %{_libdir}/mysql/plugin/audit_log.so
%attr(644, root, root) %{_datadir}/mysql-*/audit_log_filter_linux_install.sql
%attr(644, root, root) %{_datadir}/mysql-*/audit_log_filter_uninstall.sql
%attr(644, root, root) %{_datadir}/mysql-*/firewall_profile_migration.sql
%attr(644, root, root) %{_datadir}/mysql-*/linux_install_firewall.sql
%attr(644, root, root) %{_datadir}/mysql-*/uninstall_firewall.sql
%attr(644, root, root) %{_datadir}/mysql-*/masking_functions_install.sql
%attr(644, root, root) %{_datadir}/mysql-*/masking_functions_uninstall.sql
%attr(644, root, root) %{_datadir}/mysql-*/option_tracker_install.sql
%attr(644, root, root) %{_datadir}/mysql-*/option_tracker_uninstall.sql
%if 0%{?add_fido_plugins}
%attr(755, root, root) %{_libdir}/mysql/plugin/authentication_webauthn.so
%endif # add_fido_plugins
%attr(755, root, root) %{_libdir}/mysql/plugin/authentication_kerberos.so
%attr(755, root, root) %{_libdir}/mysql/plugin/authentication_openid_connect.so
%attr(755, root, root) %{_libdir}/mysql/plugin/authentication_ldap_sasl.so
%attr(755, root, root) %{_libdir}/mysql/plugin/authentication_ldap_simple.so
%attr(755, root, root) %{_libdir}/mysql/plugin/authentication_pam.so
%if 0%{?add_component_keyring_aws}
%attr(755, root, root) %{_libdir}/mysql/plugin/component_keyring_aws.so
%endif # add_component_keyring_aws
%attr(755, root, root) %{_libdir}/mysql/plugin/component_keyring_encrypted_file.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_keyring_hashicorp.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_keyring_oci.so
%attr(755, root, root) %{_libdir}/mysql/plugin/telemetry_client.so
%attr(755, root, root) %{_libdir}/mysql/plugin/data_masking.so
%attr(755, root, root) %{_libdir}/mysql/plugin/firewall.so
%attr(755, root, root) %{_libdir}/mysql/plugin/keyring_hashicorp.so
%attr(755, root, root) %{_libdir}/mysql/plugin/keyring_okv.so
%attr(755, root, root) %{_libdir}/mysql/plugin/thread_pool.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_enterprise_encryption.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_masking.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_masking_functions.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_mle.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_scheduler.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_telemetry.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_option_tracker.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_group_replication_flow_control_stats.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_group_replication_resource_manager.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_replication_applier_metrics.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_group_replication_elect_prefers_most_updated.so
%if 0%{?aws_sdk}
%attr(755, root, root) %{_libdir}/mysql/plugin/keyring_aws.so
%endif # aws
%endif # commercial

%attr(644, root, root) %{_datadir}/mysql-*/dictionary.txt
%attr(644, root, root) %{_datadir}/mysql-*/install_rewriter.sql
%attr(644, root, root) %{_datadir}/mysql-*/mysql-log-rotate
%attr(644, root, root) %{_datadir}/mysql-*/uninstall_rewriter.sql
%attr(644, root, root) %{_unitdir}/mysql.service
%attr(644, root, root) %{_unitdir}/mysql@.service
%attr(644, root, root) %{_tmpfilesdir}/mysql.conf
%attr(644, root, root) %config(noreplace,missingok) %{_sysconfdir}/logrotate.d/mysql
%dir %attr(751, mysql, mysql) /var/lib/mysql
%dir %attr(755, mysql, mysql) /var/run/mysql
%dir %attr(750, mysql, mysql) /var/log/mysql
%dir %attr(750, mysql, mysql) /var/lib/mysql-files
%dir %attr(750, mysql, mysql) /var/lib/mysql-keyring

%files server-debug
%doc %{?license_files_server}
%attr(755, root, root) %{_sbindir}/mysqld-debug
%dir %{_libdir}/mysql/plugin/debug
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/adt_null.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/auth_socket.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_audit_api_message_emit.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_keyring_file.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_log_filter_dragnet.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_log_sink_json.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_log_sink_syseventlog.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_mysqlbackup.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_query_attributes.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_reference_cache.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_validate_password.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_connection_control.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/connection_control.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/ddl_rewriter.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/group_replication.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/ha_example.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/ha_mock.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/keyring_udf.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/locking_service.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/mypluglib.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/mysql_clone.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/mysql_no_login.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/rewrite_example.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/rewriter.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/semisync_master.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/semisync_replica.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/semisync_slave.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/semisync_source.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/validate_password.so
%if 0%{?mecab}
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libpluginmecab.so
%endif # mecab
%if 0%{?commercial}
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/audit_log.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/authentication_pam.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/authentication_ldap_sasl.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/authentication_ldap_simple.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/authentication_kerberos.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/authentication_openid_connect.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/telemetry_client.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/data_masking.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/keyring_okv.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/keyring_hashicorp.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/thread_pool.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/firewall.so
%if 0%{?add_fido_plugins}
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/authentication_webauthn.so
%endif # add_fido_plugins
%if 0%{?add_component_keyring_aws}
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_keyring_aws.so
%endif # add_component_keyring_aws
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_keyring_encrypted_file.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_keyring_hashicorp.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_keyring_oci.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_enterprise_encryption.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_masking.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_masking_functions.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_mle.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_scheduler.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_telemetry.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_option_tracker.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_group_replication_flow_control_stats.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_group_replication_resource_manager.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_replication_applier_metrics.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_group_replication_elect_prefers_most_updated.so
%if 0%{?aws_sdk}
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/keyring_aws.so
%endif # aws_sdk
%endif # commercial

%files common
%defattr(-, root, root, -)
%doc %{?license_files_server}
%{_datadir}/mysql-*/charsets/
%{_datadir}/mysql-*/messages_to_error_log.txt
%{_datadir}/mysql-*/messages_to_clients.txt
%{_datadir}/mysql-*/bulgarian/
%{_datadir}/mysql-*/czech/
%{_datadir}/mysql-*/danish/
%{_datadir}/mysql-*/dutch/
%{_datadir}/mysql-*/english/
%{_datadir}/mysql-*/estonian/
%{_datadir}/mysql-*/french/
%{_datadir}/mysql-*/german/
%{_datadir}/mysql-*/greek/
%{_datadir}/mysql-*/hungarian/
%{_datadir}/mysql-*/italian/
%{_datadir}/mysql-*/japanese/
%{_datadir}/mysql-*/korean/
%{_datadir}/mysql-*/norwegian-ny/
%{_datadir}/mysql-*/norwegian/
%{_datadir}/mysql-*/polish/
%{_datadir}/mysql-*/portuguese/
%{_datadir}/mysql-*/romanian/
%{_datadir}/mysql-*/russian/
%{_datadir}/mysql-*/serbian/
%{_datadir}/mysql-*/slovak/
%{_datadir}/mysql-*/spanish/
%{_datadir}/mysql-*/swedish/
%{_datadir}/mysql-*/ukrainian/

%files icu-data-files
%defattr(-, root, root, -)
%doc %{?license_files_server}
%dir %attr(755, root, root) %{_libdir}/mysql/private/icudt73l
%{_libdir}/mysql/private/icudt73l/cnvalias.icu
%{_libdir}/mysql/private/icudt73l/uemoji.icu
%{_libdir}/mysql/private/icudt73l/ulayout.icu
%{_libdir}/mysql/private/icudt73l/unames.icu
%{_libdir}/mysql/private/icudt73l/brkitr

%files client
%defattr(-, root, root, -)
%doc %{?license_files_server}
%attr(755, root, root) %{_bindir}/mysql
%attr(755, root, root) %{_bindir}/mysqladmin
%attr(755, root, root) %{_bindir}/mysqlbinlog
%attr(755, root, root) %{_bindir}/mysqlcheck
%attr(755, root, root) %{_bindir}/mysqldump
%attr(755, root, root) %{_bindir}/mysqlimport
%attr(755, root, root) %{_bindir}/mysqlshow
%attr(755, root, root) %{_bindir}/mysqlslap
%attr(755, root, root) %{_bindir}/mysql_config_editor
%attr(755, root, root) %{_bindir}/mysql_migrate_keyring

%attr(644, root, root) %{_mandir}/man1/mysql.1*
%attr(644, root, root) %{_mandir}/man1/mysqladmin.1*
%attr(644, root, root) %{_mandir}/man1/mysqlbinlog.1*
%attr(644, root, root) %{_mandir}/man1/mysqlcheck.1*
%attr(644, root, root) %{_mandir}/man1/mysqldump.1*
%attr(644, root, root) %{_mandir}/man1/mysqlimport.1*
%attr(644, root, root) %{_mandir}/man1/mysqlshow.1*
%attr(644, root, root) %{_mandir}/man1/mysqlslap.1*
%attr(644, root, root) %{_mandir}/man1/mysql_config_editor.1*


%if 0%{?cluster}
%attr(755, root, root) %{_bindir}/ndb_blob_tool
%attr(755, root, root) %{_bindir}/ndb_config
%attr(755, root, root) %{_bindir}/ndb_delete_all
%attr(755, root, root) %{_bindir}/ndb_desc
%attr(755, root, root) %{_bindir}/ndb_drop_index
%attr(755, root, root) %{_bindir}/ndb_drop_table
%attr(755, root, root) %{_bindir}/ndb_error_reporter
%attr(755, root, root) %{_bindir}/ndb_index_stat
%attr(755, root, root) %{_bindir}/ndb_import
%attr(755, root, root) %{_bindir}/ndb_mgm
%attr(755, root, root) %{_bindir}/ndb_move_data
%attr(755, root, root) %{_bindir}/ndb_perror
%attr(755, root, root) %{_bindir}/ndb_print_backup_file
%attr(755, root, root) %{_bindir}/ndb_print_file
%attr(755, root, root) %{_bindir}/ndb_print_frag_file
%attr(755, root, root) %{_bindir}/ndb_print_schema_file
%attr(755, root, root) %{_bindir}/ndb_print_sys_file
%attr(755, root, root) %{_bindir}/ndb_redo_log_reader
%attr(755, root, root) %{_bindir}/ndb_restore
%attr(755, root, root) %{_bindir}/ndb_secretsfile_reader
%attr(755, root, root) %{_bindir}/ndb_select_all
%attr(755, root, root) %{_bindir}/ndb_select_count
%attr(755, root, root) %{_bindir}/ndb_show_tables
%attr(755, root, root) %{_bindir}/ndb_sign_keys
%attr(755, root, root) %{_bindir}/ndb_size.pl
%attr(755, root, root) %{_bindir}/ndb_top
%attr(755, root, root) %{_bindir}/ndb_waiter
%attr(755, root, root) %{_bindir}/ndbinfo_select_all
%attr(755, root, root) %{_bindir}/ndbxfrm

%attr(644, root, root) %{_mandir}/man1/ndb_blob_tool.1*
%attr(644, root, root) %{_mandir}/man1/ndb_config.1*
%attr(644, root, root) %{_mandir}/man1/ndb_cpcd.1*
%attr(644, root, root) %{_mandir}/man1/ndb_delete_all.1*
%attr(644, root, root) %{_mandir}/man1/ndb_desc.1*
%attr(644, root, root) %{_mandir}/man1/ndb_drop_index.1*
%attr(644, root, root) %{_mandir}/man1/ndb_drop_table.1*
%attr(644, root, root) %{_mandir}/man1/ndb_error_reporter.1*
%attr(644, root, root) %{_mandir}/man1/ndb_import.1*
%attr(644, root, root) %{_mandir}/man1/ndb_index_stat.1*
%attr(644, root, root) %{_mandir}/man1/ndb_mgm.1*
%attr(644, root, root) %{_mandir}/man1/ndb_move_data.1*
%attr(644, root, root) %{_mandir}/man1/ndb_perror.1*
%attr(644, root, root) %{_mandir}/man1/ndb_print_backup_file.1*
%attr(644, root, root) %{_mandir}/man1/ndb_print_file.1*
%attr(644, root, root) %{_mandir}/man1/ndb_print_frag_file.1*
%attr(644, root, root) %{_mandir}/man1/ndb_print_schema_file.1*
%attr(644, root, root) %{_mandir}/man1/ndb_print_sys_file.1*
%attr(644, root, root) %{_mandir}/man1/ndb_redo_log_reader.1*
%attr(644, root, root) %{_mandir}/man1/ndb_restore.1*
%attr(644, root, root) %{_mandir}/man1/ndb_secretsfile_reader.1*
%attr(644, root, root) %{_mandir}/man1/ndb_select_all.1*
%attr(644, root, root) %{_mandir}/man1/ndb_select_count.1*
%attr(644, root, root) %{_mandir}/man1/ndb_show_tables.1*
%attr(644, root, root) %{_mandir}/man1/ndb_sign_keys.1*
%attr(644, root, root) %{_mandir}/man1/ndb_size.pl.1*
%attr(644, root, root) %{_mandir}/man1/ndb_top.1*
%attr(644, root, root) %{_mandir}/man1/ndb_waiter.1*
%attr(644, root, root) %{_mandir}/man1/ndbinfo_select_all.1*
%attr(644, root, root) %{_mandir}/man1/ndbxfrm.1*
%endif #cluster

%files devel
%defattr(-, root, root, -)
%doc %{?license_files_server}
%attr(644, root, root) %{_mandir}/man1/mysql_config.1*
%attr(755, root, root) %{_bindir}/mysql_config
%{_includedir}/mysql
%if 0%{?cluster}
%exclude %{_includedir}/mysql/storage
%endif #cluster
%{_datadir}/aclocal/mysql.m4
%{_libdir}/mysql/libmysqlclient.a
%{_libdir}/mysql/libmysqlservices.a
%{_libdir}/mysql/libmysqlclient.so
%{_libdir}/pkgconfig/mysqlclient.pc

%files libs
%defattr(-, root, root, -)
%doc %{?license_files_server}
%dir %attr(755, root, root) %{_libdir}/mysql
%attr(644, root, root) %{_sysconfdir}/ld.so.conf.d/mysql-%{_arch}.conf
%{_libdir}/mysql/libmysqlclient.so.24*

%files client-plugins
%defattr(-, root, root, -)
%doc %{?license_files_server}
%attr(755, root, root) %{_libdir}/mysql/plugin/authentication_ldap_sasl_client.so
%if 0%{?add_fido_plugins}
%attr(755, root, root) %{_libdir}/mysql/plugin/authentication_webauthn_client.so
%attr(755, root, root) %{_libdir}/mysql/private/libfido2.so.1
%attr(755, root, root) %{_libdir}/mysql/private/libfido2.so.1.15.0
%endif # add_fido_plugins
%attr(755, root, root) %{_libdir}/mysql/plugin/authentication_kerberos_client.so
%attr(755, root, root) %{_libdir}/mysql/plugin/authentication_openid_connect_client.so
%attr(755, root, root) %{_libdir}/mysql/plugin/authentication_oci_client.so
%attr(755, root, root) %{_libdir}/mysql/plugin/mysql_native_password.so

%if 0%{?compatlib}
%files libs-compat
%defattr(-, root, root, -)
%doc %{?license_files_server}
%dir %attr(755, root, root) %{_libdir}/mysql
%attr(644, root, root) %{_sysconfdir}/ld.so.conf.d/mysql-%{_arch}.conf
%{_libdir}/mysql/libmysqlclient.so.%{compatlib}
%{_libdir}/mysql/libmysqlclient.so.%{compatlib}.0.0
%{_libdir}/mysql/libmysqlclient_r.so.%{compatlib}
%{_libdir}/mysql/libmysqlclient_r.so.%{compatlib}.0.0
%endif

%files test
%defattr(-, root, root, -)
%doc %{?license_files_server}
%attr(-, root, root) %{_datadir}/mysql-test
%attr(755, root, root) %{_bindir}/comp_err
%attr(755, root, root) %{_bindir}/mysql_client_test
%attr(755, root, root) %{_bindir}/mysqld_safe
%attr(755, root, root) %{_bindir}/mysqltest
%attr(755, root, root) %{_bindir}/mysqltest_safe_process
%attr(755, root, root) %{_bindir}/mysqlxtest
%attr(755, root, root) %{_bindir}/mysql_keyring_encryption_test
%attr(755, root, root) %{_bindir}/mysql_test_event_tracking
%attr(644, root, root) %{_mandir}/man1/comp_err.1*
%if 0%{?commercial}
%attr(755, root, root) %{_bindir}/mysql_test_jwt_generator
%endif # commercial

%attr(755, root, root) %{_libdir}/mysql/plugin/auth.so
%attr(755, root, root) %{_libdir}/mysql/plugin/auth_test_plugin.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_example_component1.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_example_component2.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_example_component3.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_log_sink_test.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_backup_lock_service.so
%if 0%{?commercial}
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_page_track_component.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_global_priv_registration.so
%endif
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_string_service_charset.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_string_service_long.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_string_service.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_pfs_example.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_pfs_example_component_population.so
%attr(755, root, root) %{_libdir}/mysql/plugin/pfs_example_plugin_employee.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_pfs_notification.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_pfs_resource_group.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_udf_registration.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_host_application_signal.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_mysql_current_thread_reader.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_mysql_runtime_error.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_mysql_system_variable_set.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_table_access.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_component_deinit.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_mysql_command_services.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_execute_prepared_statement.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_execute_regular_statement.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_mysql_signal_handler.so
%attr(755, root, root) %{_libdir}/mysql/plugin/test_services_command_services.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_udf_reg_3_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_udf_reg_avg_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_udf_reg_int_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_udf_reg_int_same_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_udf_reg_only_3_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_udf_reg_real_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_udf_unreg_3_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_udf_unreg_int_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_udf_unreg_real_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_sys_var_service_int.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_sys_var_service.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_sys_var_service_same.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_sys_var_service_str.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_session_var_service.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_status_var_service.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_status_var_service_int.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_status_var_service_reg_only.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_status_var_service_str.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_status_var_service_unreg_only.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_system_variable_source.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_audit_api_message.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_udf_aggregate.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_udf_services.so
%attr(755, root, root) %{_libdir}/mysql/plugin/conflicting_variables.so
%attr(644, root, root) %{_libdir}/mysql/plugin/daemon_example.ini
%attr(755, root, root) %{_libdir}/mysql/plugin/libdaemon_example.so
%attr(755, root, root) %{_libdir}/mysql/plugin/test_udf_services.so
%attr(755, root, root) %{_libdir}/mysql/plugin/udf_example.so
%attr(755, root, root) %{_libdir}/mysql/plugin/replication_observers_example_plugin.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_framework.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_services.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_services_threaded.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_session_detach.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_session_attach.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_session_in_thd.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_session_info.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_2_sessions.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_all_col_types.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_cmds_1.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_commit.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_complex.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_errors.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_lock.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_processlist.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_replication.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_shutdown.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_sleep_is_connected.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_stmt.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_sqlmode.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_stored_procedures_functions.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_views_triggers.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_sql_reset_connection.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_x_sessions_deinit.so
%attr(755, root, root) %{_libdir}/mysql/plugin/libtest_x_sessions_init.so
%attr(755, root, root) %{_libdir}/mysql/plugin/qa_auth_client.so
%attr(755, root, root) %{_libdir}/mysql/plugin/qa_auth_interface.so
%attr(755, root, root) %{_libdir}/mysql/plugin/qa_auth_server.so
%attr(755, root, root) %{_libdir}/mysql/plugin/test_security_context.so
%attr(755, root, root) %{_libdir}/mysql/plugin/test_services_plugin_registry.so
%attr(755, root, root) %{_libdir}/mysql/plugin/test_services_host_application_signal.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_mysqlx_global_reset.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_sensitive_system_variables.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_status_var_reader.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_server_telemetry_logs_client.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_server_telemetry_logs_export.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_server_telemetry_metrics.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_server_telemetry_traces.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_mysql_thd_store_service.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_event_tracking_consumer.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_event_tracking_producer_a.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_event_tracking_producer_b.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_event_tracking_consumer_a.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_event_tracking_consumer_b.so
%attr(755, root, root) %{_libdir}/mysql/plugin/component_test_event_tracking_consumer_c.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/auth.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/auth_test_plugin.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/authentication_ldap_sasl_client.so
%if 0%{?add_fido_plugins}
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/authentication_webauthn_client.so
%endif # add_fido_plugins
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/authentication_kerberos_client.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/authentication_openid_connect_client.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/authentication_oci_client.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/mysql_native_password.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_example_component1.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_example_component2.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_example_component3.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_log_sink_test.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_backup_lock_service.so
%if 0%{?commercial}
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_page_track_component.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_global_priv_registration.so
%endif
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_string_service_charset.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_string_service_long.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_string_service.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_pfs_example.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_pfs_example_component_population.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/pfs_example_plugin_employee.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_pfs_notification.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_pfs_resource_group.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_udf_registration.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_host_application_signal.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_mysql_current_thread_reader.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_mysql_runtime_error.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_udf_aggregate.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_mysql_system_variable_set.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_table_access.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_component_deinit.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_mysql_command_services.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/test_services_command_services.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_execute_prepared_statement.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_execute_regular_statement.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_mysql_signal_handler.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_udf_reg_3_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_udf_reg_avg_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_udf_reg_int_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_udf_reg_int_same_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_udf_reg_only_3_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_udf_reg_real_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_udf_unreg_3_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_udf_unreg_int_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_udf_unreg_real_func.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_sys_var_service_int.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_sys_var_service.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_sys_var_service_same.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_sys_var_service_str.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_session_var_service.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_status_var_service.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_status_var_service_int.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_status_var_service_reg_only.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_status_var_service_str.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_status_var_service_unreg_only.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_system_variable_source.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_audit_api_message.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_udf_services.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/conflicting_variables.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libdaemon_example.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/test_udf_services.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/udf_example.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/replication_observers_example_plugin.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_framework.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_services.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_services_threaded.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_session_detach.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_session_attach.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_session_in_thd.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_session_info.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_2_sessions.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_all_col_types.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_cmds_1.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_commit.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_complex.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_errors.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_lock.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_processlist.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_replication.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_shutdown.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_sleep_is_connected.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_stmt.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_sqlmode.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_stored_procedures_functions.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_views_triggers.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_sql_reset_connection.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_x_sessions_deinit.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/libtest_x_sessions_init.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/qa_auth_client.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/qa_auth_interface.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/qa_auth_server.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/test_security_context.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/test_services_plugin_registry.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/test_services_host_application_signal.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_mysqlx_global_reset.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_sensitive_system_variables.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_status_var_reader.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_server_telemetry_logs_client.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_server_telemetry_logs_export.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_server_telemetry_metrics.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_server_telemetry_traces.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_mysql_thd_store_service.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_event_tracking_consumer.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_event_tracking_producer_a.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_event_tracking_producer_b.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_event_tracking_consumer_a.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_event_tracking_consumer_b.so
%attr(755, root, root) %{_libdir}/mysql/plugin/debug/component_test_event_tracking_consumer_c.so

%if 0%{?cluster}
%files management-server
%defattr(-, root, root, -)
%doc %{?license_files_server}
%attr(755, root, root) %{_sbindir}/ndb_mgmd
%attr(644, root, root) %{_mandir}/man8/ndb_mgmd.8*

%files data-node
%defattr(-, root, root, -)
%doc %{?license_files_server}
%attr(755, root, root) %{_sbindir}/ndbd
%attr(755, root, root) %{_sbindir}/ndbmtd
%attr(644, root, root) %{_mandir}/man8/ndbd.8*
%attr(644, root, root) %{_mandir}/man8/ndbmtd.8*

%files ndbclient
%defattr(-, root, root, -)
%doc %{?license_files_server}
%attr(755, root, root) %{_libdir}/mysql/libndbclient.so.6.1.0

%files ndbclient-devel
%defattr(-, root, root, -)
%doc %{?license_files_server}
%attr(644, root, root) %{_libdir}/mysql/libndbclient_static.a
%{_libdir}/mysql/libndbclient.so
%{_includedir}/mysql/storage

%files java
%defattr(-, root, root, -)
%doc %{?license_files_server}
%{_datadir}/mysql-*/java/
%endif # cluster

%if 0%{?with_meb}
%files backup
%defattr(-, root, root, -)
%doc %{src_dir}/packaging/meb/LICENSE.meb
%doc %{src_dir}/packaging/meb/README.meb
%doc %{src_dir}/Docs/INFO_SRC*
%doc release/Docs/INFO_BIN*
%attr(755, root, root) %{_bindir}/mysqlbackup
%endif # with_meb

%if 0%{?with_router}
%files -n mysql-router-%{product_suffix}
%defattr(-, root, root, -)
%doc %{src_dir}/router/README.router  %{src_dir}/router/LICENSE.router
%doc %{src_dir}/Docs/INFO_SRC*
%doc release/Docs/INFO_BIN*
%dir %{_sysconfdir}/mysqlrouter
%config(noreplace) %{_sysconfdir}/mysqlrouter/mysqlrouter.conf
%attr(644, root, root) %config(noreplace,missingok) %{_sysconfdir}/logrotate.d/mysqlrouter
%{_bindir}/mysqlrouter
%{_bindir}/mysqlrouter_bootstrap
%{_bindir}/mysqlrouter_keyring
%{_bindir}/mysqlrouter_mrs_client
%{_bindir}/mysqlrouter_passwd
%{_bindir}/mysqlrouter_plugin_info
%attr(644, root, root) %{_mandir}/man1/mysqlrouter.1*
%attr(644, root, root) %{_mandir}/man1/mysqlrouter_passwd.1*
%attr(644, root, root) %{_mandir}/man1/mysqlrouter_plugin_info.1*
%{_unitdir}/mysqlrouter.service
%{_tmpfilesdir}/mysqlrouter.conf
%{_libdir}/mysqlrouter/private/libmysqlrouter_http_client.so.*
%{_libdir}/mysqlrouter/private/libmysqlharness.so.*
%{_libdir}/mysqlrouter/private/libmysqlharness_stdx.so.*
%{_libdir}/mysqlrouter/private/libmysqlharness_tls.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_cluster.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_connection_pool.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_destination_status.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_http.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_http_auth_backend.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_http_auth_realm.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_http_server.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_io_component.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_metadata_cache.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_mysqlclient.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_mysqlxclient.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_mysqlxmessages.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_mysql.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_routing.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_routing_connections.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_routing_guidelines.so.*
%{_libdir}/mysqlrouter/private/libmysqlrouter_utils.so.*
%{_libdir}/mysqlrouter/private/libprotobuf.so.24.4.0
%{_libdir}/mysqlrouter/private/libabsl_*.so
%if 0%{?mrs_jit_executor_lib:1}
  %{_libdir}/mysqlrouter/private/libjitexecutor*.so
%endif
%dir %{_libdir}/mysqlrouter
%dir %{_libdir}/mysqlrouter/private
%{_libdir}/mysqlrouter/*.so
%dir %attr(755, mysqlrouter, mysqlrouter) /var/log/mysqlrouter
%dir %attr(755, mysqlrouter, mysqlrouter) /var/run/mysqlrouter
%endif # with_router

%changelog
* Fri Jan 10 2025 Anibal Pinto <<EMAIL>> - 9.3.0-1
- Added component_group_replication_elect_prefers_most_updated

* Fri Nov 29 2024 Jaideep Karande <<EMAIL>> - 9.2.0-1
- Added component_group_replication_resource_manager

* Wed Nov 13 2024 Samar Pratap Singh <<EMAIL>> - 9.2.0-1
- Added component_connection_control

* Wed Jul 10 2024 Samar Pratap Singh <<EMAIL>> - 9.1.0-1
- Added authentication_openid_connect plugin
- Added mysql_test_jwt_generator tool

* Wed May 29 2024 Miroslav Rajcic <<EMAIL>> - 9.1.0-1
- Added component_test_server_telemetry_logs_* test components
- Added component_keyring_aws

* Mon May 27 2024 Georgi "Joro" Kodinov <<EMAIL>> - 9.0.0-1
- Added the option usage tracker component

* Mon May 20 2024 Murthy Sidagam <<EMAIL>> - 9.0
- Added component_test_session_var_service.so component

* Mon Feb 12 2024 Samar Pratap Singh <<EMAIL>> - 8.4.0-1
- Removed keyring_file plugin

* Tue Jan 30 2024 Samar Pratap Singh <<EMAIL>> - 8.4.0-1
- Removed keyring_encrypted_file plugin

* Thu Jan 25 2024 Georgi "Joro" Kodinov <<EMAIL>> - 8.4.0-1
- Removed mysql_ssl_rsa_setup

* Tue Jan 09 2024 Harin Vadodaria <<EMAIL>> - 8.4.0-1
- Removed openssl_udf plugin

* Tue Jan 02 2024 Harin Vadodaria <<EMAIL>> - 8.4.0-1
- Removed references to WITH_AUTHENTICATION_FIDO

* Mon Nov 20 2023 Samar Pratap Singh <<EMAIL>> - 8.4.0-1
- Removed keyring_oci plugin

* Fri Nov 17 2023 Miroslav Rajcic <<EMAIL>> - 8.4.0-1
- mysql_stmt_bind_param C API removed, so increase the libmysqlclient major version to 24.
- Removed mysqlpump, mysql_upgrade, zlib_decompress and lz4_decompress binaries

* Tue Sep 19 2023 Miroslav Rajcic <<EMAIL>> - 8.3.0-1
- Some C API removed, so increase the libmysqlclient major version to 23.

* Mon Jul 24 2023 Miroslav Rajcic <<EMAIL>> - 8.2.0-1
- Added component_test_server_telemetry_metrics

* Wed Apr 05 2023 Erlend Dahl <<EMAIL>> - 8.1.0-1
- Bump in the major version of MySQL, so increase the libmysqlclient major version to 22.

* Wed Mar 01 2023 Harin Vadodaria <<EMAIL>> - 8.0.34-1
- Added components, libraries and binaries related to event tracking

* Thu Oct 13 2022 Miroslav Rajcic <<EMAIL>> - 8.0.33-1
- Added component_test_server_telemetry_traces, component_test_mysql_thd_store_service

* Tue Oct 11 2022 Michal Jankowski <<EMAIL>> - 8.0.33-1
- Added component_masking.so component
- Added component_masking_functions.so component

* Tue Jun 28 2022 Samar Pratap Singh <<EMAIL>> - 8.0.31-1
- Added component_keyring_oci

* Mon Jun 20 2022 Murthy Sidagam <<EMAIL>> - 8.0.31-1
- Added component_test_mysql_command_services.so test component
- Added test_services_command_services.so test plugin

* Wed Feb 02 2022 Harin Vadodaria <<EMAIL>> - 8.0.30-1
- Added component_enterprise_encryption

* Mon Jan 03 2022 Harin Vadodaria <<EMAIL>> - 8.0.29-1
- Added component_test_sensitive_system_variables

* Tue Aug 31 2021 Harin Vadodaria <<EMAIL>> - 8.0.27-1
- Added authentication_oci_client

* Mon Sep 21 2020 Murthy Sidagam <<EMAIL>> - 8.0.23-1
- Added component_reference_cache.so component

* Wed Sep 09 2020 Joro Kodinov <<EMAIL>> - 8.0.23-1
- Add a new component_query_attributes

* Sun Jul 12 2020 Sreedhar S <<EMAIL>> - 8.0.22-1
- Add a new subpackage client-plugins
- Added component_keyring_file component
- Added component_keyring_encrypted_file component
- Added mysql_migrate_keyring

* Mon May 11 2020 Murthy Sidagam <<EMAIL>> - 8.0.15-1
- Added component_test_component_deinit.so test component

* Wed Aug 14 2019 Rahul Sisondia <<EMAIL>> - 8.0.19-1
- Added component_test_udf_services test component

* Wed Jun 26 2019 Maheedhar PV <<EMAIL>> - 8.0.18-1
- Add component_mysqlbackup component

* Mon May 13 2019 Bjorn Munch <<EMAIL>> - 8.0.17-1
- Add router man pages
- Remove support for System V init

* Mon Feb 18 2019 Bjorn Munch <<EMAIL>> - 8.0.16-1
- Add back support for alternative with_ssl

* Fri Dec 14 2018 Murthy Sidagam <<EMAIL>> - 8.0.15-1
- Added component_test_mysql_runtime_error.so test component

* Wed Nov 21 2018 Erlend Dahl <<EMAIL>> - 8.0.14-1
- Added specific compilation comment for the server
- Remove resolveip
- Remove resolve_stack_dump

* Mon Aug 20 2018 Georgi Kodinov <<EMAIL>> - 8.0.13-1
- Added test_services_host_application_signal test plugin
- Added component_test_host_application_signal.so test component
- Adapt to numbered share directory for NDB files
- Add support for SLES 15
- Add Router

* Mon Mar 12 2018 Erlend Dahl <<EMAIL>> - 8.0.12-1
- Move mysqlx to default plugin
- Add component_mysqlx_global_reset.so
- Change the global milestone to 'dmr'
- Add meb as sub package
- Remove obsoleted mysqltest man pages
- Include udf_example.so in test package
- Add component_validate_password component
- Add License Book, remove COPYING
- No longer need to remove obsoleted mysqltest man pages
- Add perl modules for test subpackage
- Add router as subpackage

* Fri Jul 28 2017 Horst Hunger <<EMAIL>> - 8.0.3-0.1
- Add component_test_status_var_service plugin

* Fri May 26 2017 Harin Vadodaria <<EMAIL>> - 8.0.2-0.1
- Add keyring_aws plugin and UDF components
- Add component_test_sys_var_service plugins

* Tue Sep 13 2016 Balasubramanian Kandasamy <<EMAIL>> - 8.0.1-0.1
- Add test_services_plugin_registry.so plugin
- Add connection_control.so to server subpackage

* Sun Jun 05 2016 Erlend Dahl <<EMAIL>> - 8.0.0-0.1
- Change the version number to 8.0.0
- Add manual page for ibd2sdi utility
- Adapt MySQL server 5.7 packaging to MySQL Cluster 7.5

* Fri Jun 03 2016 Balasubramanian Kandasamy <<EMAIL>> - 5.8.0-0.1
- Add example component to test package
- Add test_udf_services.so plugin
- Add keyring_udf.so plugin to server subpackage
- Add keyring_okv.so plugin to commercial server subpackage
- Purge man page for mysql_install_db in preparation for its removal
- Include mysql-keyring directory
- Provide keyring_file.so plugin

* Tue Nov 24 2015 Bjorn Munch <<EMAIL>> - 5.7.10-1
- Included man pages for lz4_decompress and zlib_decompress

* Thu Nov 12 2015 Bjorn Munch <<EMAIL>> - 5.7.10-1
- Added lines to remove man pages we are not ready to include yet

* Mon Oct 19 2015 Bharathy Satish <<EMAIL>> - 5.7.10-1
- Added new decompression utilities lz4_decompress and zlib_decompress binaries to
  client subpackage.

* Mon Oct 5 2015 Tor Didriksen <<EMAIL>>
- Added mysqlx.so

* Tue Sep 29 2015 Balasubramanian Kandasamy <<EMAIL>> - 5.7.9-1
- Updated for 5.7.9
- Added libtest_* plugins to test subpackage
- Add mysqlpump man page
- Obsolete mysql-connector-c-shared dependencies

* Mon Jul 06 2015 Murthy Narkedimilli <<EMAIL>> - 5.7.8-0.2.rc
- Bumped the libmysqlclient.so version from 20 -> 21.

* Thu Jun 25 2015 Balasubramanian Kandasamy <<EMAIL>> - 5.7.8-0.2.rc
- Add support for pkg-config

* Wed May 20 2015 Balasubramanian Kandasamy <<EMAIL>> - 5.7.8-0.2.rc
- Added libtest_framework.so, libtest_services.so, libtest_services_threaded.so plugins
- Build and ship mecab plugin

* Tue Feb 3 2015 Balasubramanian Kandasamy <<EMAIL>> - 5.7.6-0.2.m16
- Include boost sources
- Add license info in each subpackage
- Soname bump
- Added mysql_ssl_rsa_setup
- Include mysql-files directory

* Thu Sep 18 2014 Balasubramanian Kandasamy <<EMAIL>> - 5.7.6-0.2.m16
- Provide replication_observers_example_plugin.so plugin

* Tue Sep 02 2014 Bjorn Munch <<EMAIL>> - 5.7.6-0.1.m16

* Tue Sep 2 2014 Bjorn Munch <<EMAIL>> - 5.7.6-0.1.m16
- Updated for 5.7.6

* Mon Sep 01 2014 Balasubramanian Kandasamy <<EMAIL>> - 5.7.5-0.2.m15
- Added openssl_udf.so plugin to commercial packages

* Mon Jun 30 2014 Balasubramanian Kandasamy <<EMAIL>> - 5.7.5-0.1.m15
- Port to SLES

* Thu Jun 26 2014 Balasubramanian Kandasamy <<EMAIL>> - 5.7.5-0.3.m15
- Resolve embedded-devel conflict issue

* Wed Jun 25 2014 Balasubramanian Kandasamy <<EMAIL>> - 5.7.5-0.2.m15
- Add bench package
- Enable dtrace

* Thu Apr 24 2014 Balasubramanian Kandasamy <<EMAIL>> - 5.7.5-0.1.m15
- Updated for 5.7.5

* Mon Apr 07 2014 Balasubramanian Kandasamy <<EMAIL>> - 5.7.4-0.5.m14
- Fix Cflags for el7

* Mon Mar 31 2014 Balasubramanian Kandasamy <<EMAIL>> - 5.7.4-0.4.m14
- Support for enterprise packages
- Upgrade from MySQL-* packages

* Wed Mar 12 2014 Balasubramanian Kandasamy <<EMAIL>> - 5.7.4-0.3.m14
- Resolve conflict with mysql-libs-compat

* Thu Mar 06 2014 Balasubramanian Kandasamy <<EMAIL>> - 5.7.4-0.2.m14
- Resolve conflict issues during upgrade
- Add ha_example.so plugin which is now included

* Fri Feb 07 2014 Balasubramanian Kandasamy <<EMAIL>> - 5.7.4-0.1.m14
- 5.7.4
- Enable shared libmysqld by cmake option
- Move mysqltest and test plugins to test subpackage

* Mon Nov 18 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.7.3-0.3.m13
- Fixed isa_bits error

* Fri Oct 25 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.7.3-0.1.m13
- Initial 5.7 port

* Fri Oct 25 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.15-1
- Fixed uln advanced rpm libyassl.a error
- Updated to 5.6.15

* Wed Oct 16 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.14-3
- Fixed mysql_install_db usage
- Improved handling of plugin directory

* Fri Sep 27 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.14-2
- Refresh mysql-install patch and service renaming

* Mon Sep 16 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.14-1
- Updated to 5.6.14

* Wed Sep 04 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.13-5
- Support upgrade from 5.5 ULN packages to 5.6

* Tue Aug 27 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.13-4
- Enhanced perl filtering
- Added openssl-devel to buildreq

* Wed Aug 21 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.13-3
- Removed mysql_embedded binary to resolve multilib conflict issue

* Fri Aug 16 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.13-2
- Fixed Provides and Obsoletes issues in server, test packages

* Wed Aug 14 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.13-1
- Updated to 5.6.13

* Mon Aug 05 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.12-9
- Added files list to embedded packages

* Thu Aug 01 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.12-8
- Updated libmysqld.a with libmysqld.so in embedded package

* Mon Jul 29 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.12-7
- Updated test package dependency from client to server

* Wed Jul 24 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.12-6
- Added libs-compat dependency under libs package to resolve server
  installation conflicts issue.

* Wed Jul 17 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.12-5
- Removed libmysqlclient.so.16 from libs package

* Fri Jul 05 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.12-4
- Adjusted to work on OEL6

* Wed Jun 26 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.12-3
- Move libs to mysql/
- Basic multi arch support
- Fix changelog dates

* Thu Jun 20 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.12-2
- Major cleanup

* Tue Jun 04 2013 Balasubramanian Kandasamy <<EMAIL>> - 5.6.12-1
- Updated to 5.6.12

* Mon Nov 05 2012 Joerg Bruehe <<EMAIL>>

- Allow to override the default to use the bundled yaSSL by an option like
      --define="with_ssl /path/to/ssl"

* Wed Oct 10 2012 Bjorn Munch <<EMAIL>>

- Replace old my-*.cnf config file examples with template my-default.cnf

* Fri Oct 05 2012 Joerg Bruehe <<EMAIL>>

- Let the installation use the new option "--random-passwords" of "mysql_install_db".
  (Bug# 12794345 Ensure root password)
- Fix an inconsistency: "new install" vs "upgrade" are told from the (non)existence
  of "$mysql_datadir/mysql" (holding table "mysql.user" and other system stuff).

* Tue Jul 24 2012 Joerg Bruehe <<EMAIL>>

- Add a macro "runselftest":
  if set to 1 (default), the test suite will be run during the RPM build;
  this can be oveeridden via the command line by adding
      --define "runselftest 0"
  Failures of the test suite will NOT make the RPM build fail!

* Mon Jul 16 2012 Joerg Bruehe <<EMAIL>>

- Add the man page for the "mysql_config_editor".

* Mon Jun 11 2012 Joerg Bruehe <<EMAIL>>

- Make sure newly added "SPECIFIC-ULN/" directory does not disturb packaging.

* Wed Feb 29 2012 Brajmohan Saxena <<EMAIL>>

- Removal all traces of the readline library from mysql (BUG 13738013)

* Wed Sep 28 2011 Joerg Bruehe <<EMAIL>>

- Fix duplicate mentioning of "mysql_plugin" and its manual page,
  it is better to keep alphabetic order in the files list (merging!).

* Wed Sep 14 2011 Joerg Bruehe <<EMAIL>>

- Let the RPM capabilities ("obsoletes" etc) ensure that an upgrade may replace
  the RPMs of any configuration (of the current or the preceding release series)
  by the new ones. This is done by not using the implicitly generated capabilities
  (which include the configuration name) and relying on more generic ones which
  just list the function ("server", "client", ...).
  The implicit generation cannot be prevented, so all these capabilities must be
  explicitly listed in "Obsoletes:"

* Tue Sep 13 2011 Jonathan Perkin <<EMAIL>>

- Add support for Oracle Linux 6 and Red Hat Enterprise Linux 6.  Due to
  changes in RPM behaviour ($RPM_BUILD_ROOT is removed prior to install)
  this necessitated a move of the libmygcc.a installation to the install
  phase, which is probably where it belonged in the first place.

* Tue Sep 13 2011 Joerg Bruehe <<EMAIL>>

- "make_win_bin_dist" and its manual are dropped, cmake does it different.

* Thu Sep 08 2011 Daniel Fischer <<EMAIL>>

- Add mysql_plugin man page.

* Tue Aug 30 2011 Tor Didriksen <<EMAIL>>

- Set CXX=g++ by default to add a dependency on libgcc/libstdc++.
  Also, remove the use of the -fno-exceptions and -fno-rtti flags.
  TODO: update distro_buildreq/distro_requires

* Tue Aug 30 2011 Joerg Bruehe <<EMAIL>>

- Add the manual page for "mysql_plugin" to the server package.

* Fri Aug 19 2011 Joerg Bruehe <<EMAIL>>

- Null-upmerge the fix of bug#37165: This spec file is not affected.
- Replace "/var/lib/mysql" by the spec file variable "%%{mysqldatadir}".

* Fri Aug 12 2011 Daniel Fischer <<EMAIL>>

- Source plugin library files list from cmake-generated file.

* Mon Jul 25 2011 Chuck Bell <<EMAIL>>

- Added the mysql_plugin client - enables or disables plugins.

* Thu Jul 21 2011 Sunanda Menon <<EMAIL>>

- Fix bug#12561297: Added the MySQL embedded binary

* Thu Jul 07 2011 Joerg Bruehe <<EMAIL>>

- Fix bug#45415: "rpm upgrade recreates test database"
  Let the creation of the "test" database happen only during a new installation,
  not in an RPM upgrade.
  This affects both the "mkdir" and the call of "mysql_install_db".

* Wed Feb 09 2011 Joerg Bruehe <<EMAIL>>

- Fix bug#56581: If an installation deviates from the default file locations
  ("datadir" and "pid-file"), the mechanism to detect a running server (on upgrade)
  should still work, and use these locations.
  The problem was that the fix for bug#27072 did not check for local settings.

* Mon Jan 31 2011 Joerg Bruehe <<EMAIL>>

- Install the new "manifest" files: "INFO_SRC" and "INFO_BIN".

* Tue Nov 23 2010 Jonathan Perkin <<EMAIL>>

- EXCEPTIONS-CLIENT has been deleted, remove it from here too
- Support MYSQL_BUILD_MAKE_JFLAG environment variable for passing
  a '-j' argument to make.

* Mon Nov 1 2010 Georgi Kodinov <<EMAIL>>

- Added test authentication (WL#1054) plugin binaries

* Wed Oct 6 2010 Georgi Kodinov <<EMAIL>>

- Added example external authentication (WL#1054) plugin binaries

* Wed Aug 11 2010 Joerg Bruehe <<EMAIL>>

- With a recent spec file cleanup, names have changed: A "-community" part was dropped.
  Reflect that in the "Obsoletes" specifications.
- Add a "triggerpostun" to handle the uninstall of the "-community" server RPM.
- This fixes bug#55015 "MySQL server is not restarted properly after RPM upgrade".

* Tue Jun 15 2010 Joerg Bruehe <<EMAIL>>

- Change the behaviour on installation and upgrade:
  On installation, do not autostart the server.
  *Iff* the server was stopped before the upgrade is started, this is taken as a
  sign the administrator is handling that manually, and so the new server will
  not be started automatically at the end of the upgrade.
  The start/stop scripts will still be installed, so the server will be started
  on the next machine boot.
  This is the 5.5 version of fixing bug#27072 (RPM autostarting the server).

* Tue Jun 1 2010 Jonathan Perkin <<EMAIL>>

- Implement SELinux checks from distribution-specific spec file.

* Wed May 12 2010 Jonathan Perkin <<EMAIL>>

- Large number of changes to build using CMake
- Introduce distribution-specific RPMs
- Drop debuginfo, build all binaries with debug/symbols
- Remove __os_install_post, use native macro
- Remove _unpackaged_files_terminate_build, make it an error to have
  unpackaged files
- Remove cluster RPMs

* Wed Mar 24 2010 Joerg Bruehe <<EMAIL>>

- Add "--with-perfschema" to the configure options.

* Mon Mar 22 2010 Joerg Bruehe <<EMAIL>>

- User "usr/lib*" to allow for both "usr/lib" and "usr/lib64",
  mask "rmdir" return code 1.
- Remove "ha_example.*" files from the list, they aren't built.

* Wed Mar 17 2010 Joerg Bruehe <<EMAIL>>

- Fix a wrong path name in handling the debug plugins.

* Wed Mar 10 2010 Joerg Bruehe <<EMAIL>>

- Take the result of the debug plugin build and put it into the optimized tree,
  so that it becomes part of the final installation;
  include the files in the packlist. Part of the fixes for bug#49022.

* Mon Mar 01 2010 Joerg Bruehe <<EMAIL>>

- Set "Oracle and/or its affiliates" as the vendor and copyright owner,
  accept upgrading from packages showing MySQL or Sun as vendor.

* Fri Feb 12 2010 Joerg Bruehe <<EMAIL>>

- Formatting changes:
  Have a consistent structure of separator lines and of indentation
  (8 leading blanks => tab).
- Introduce the variable "src_dir".
- Give the environment variables "MYSQL_BUILD_CC(CXX)" precedence
  over "CC" ("CXX").
- Drop the old "with_static" argument analysis, this is not supported
  in 5.1 since ages.
- Introduce variables to control the handlers individually, as well
  as other options.
- Use the new "--with-plugin" notation for the table handlers.
- Drop handling "/etc/rc.d/init.d/mysql", the switch to "/etc/init.d/mysql"
  was done back in 2002 already.
- Make "--with-zlib-dir=bundled" the default, add an option to disable it.
- Add missing manual pages to the file list.
- Improve the runtime check for "libgcc.a", protect it against being tried
  with the Intel compiler "icc".

* Mon Jan 11 2010 Joerg Bruehe <<EMAIL>>

- Change RPM file naming:
  - Suffix like "-m2", "-rc" becomes part of version as "_m2", "_rc".
  - Release counts from 1, not 0.

* Wed Dec 23 2009 Joerg Bruehe <<EMAIL>>

- The "semisync" plugin file name has lost its introductory "lib",
  adapt the file lists for the subpackages.
  This is a part missing from the fix for bug#48351.
- Remove the "fix_privilege_tables" manual, it does not exist in 5.5
  (and likely, the whole script will go, too).

* Mon Nov 16 2009 Joerg Bruehe <<EMAIL>>

- remove erroneous traces of the InnoDB plugin (that is 5.1 only).

* Tue Oct 06 2009 Magnus Blaudd <<EMAIL>>

- Removed mysql_fix_privilege_tables

* Fri Oct 02 2009 Alexander Nozdrin <<EMAIL>>

- "mysqlmanager" got removed from version 5.4, all references deleted.

* Fri Aug 28 2009 Joerg Bruehe <<EMAIL>>

- Merge up from 5.1 to 5.4: Remove handling for the InnoDB plugin.

* Thu Aug 27 2009 Joerg Bruehe <<EMAIL>>

- This version does not contain the "Instance manager", "mysqlmanager":
  Remove it from the spec file so that packaging succeeds.

* Mon Aug 24 2009 Jonathan Perkin <<EMAIL>>

- Add conditionals for bundled zlib and innodb plugin

* Fri Aug 21 2009 Jonathan Perkin <<EMAIL>>

- Install plugin libraries in appropriate packages.
- Disable libdaemon_example and ftexample plugins.

* Thu Aug 20 2009 Jonathan Perkin <<EMAIL>>

- Update variable used for mysql-test suite location to match source.

* Fri Nov 07 2008 Joerg Bruehe <<EMAIL>>

- Correct yesterday's fix, so that it also works for the last flag,
  and fix a wrong quoting: un-quoted quote marks must not be escaped.

* Thu Nov 06 2008 Kent Boortz <<EMAIL>>

- Removed "mysql_upgrade_shell"
- Removed some copy/paste between debug and normal build

* Thu Nov 06 2008 Joerg Bruehe <<EMAIL>>

- Modify CFLAGS and CXXFLAGS such that a debug build is not optimized.
  This should cover both gcc and icc flags.  Fixes bug#40546.

* Fri Aug 29 2008 Kent Boortz <<EMAIL>>

- Removed the "Federated" storage engine option, and enabled in all

* Tue Aug 26 2008 Joerg Bruehe <<EMAIL>>

- Get rid of the "warning: Installed (but unpackaged) file(s) found:"
  Some generated files aren't needed in RPMs:
  - the "sql-bench/" subdirectory
  Some files were missing:
  - /usr/share/aclocal/mysql.m4  ("devel" subpackage)
  - Manual "mysqlbug" ("server" subpackage)
  - Program "innochecksum" and its manual ("server" subpackage)
  - Manual "mysql_find_rows" ("client" subpackage)
  - Script "mysql_upgrade_shell" ("client" subpackage)
  - Program "ndb_cpcd" and its manual ("ndb-extra" subpackage)
  - Manuals "ndb_mgm" + "ndb_restore" ("ndb-tools" subpackage)

* Mon Mar 31 2008 Kent Boortz <<EMAIL>>

- Made the "Federated" storage engine an option
- Made the "Cluster" storage engine and sub packages an option

* Wed Mar 19 2008 Joerg Bruehe <<EMAIL>>

- Add the man pages for "ndbd" and "ndb_mgmd".

* Mon Feb 18 2008 Timothy Smith <<EMAIL>>

- Require a manual upgrade if the alread-installed mysql-server is
  from another vendor, or is of a different major version.

* Wed May 02 2007 Joerg Bruehe <<EMAIL>>

- "ndb_size.tmpl" is not needed any more,
  "man1/mysql_install_db.1" lacked the trailing '*'.

* Sat Apr 07 2007 Kent Boortz <<EMAIL>>

- Removed man page for "mysql_create_system_tables"

* Wed Mar 21 2007 Daniel Fischer <<EMAIL>>

- Add debug server.

* Mon Mar 19 2007 Daniel Fischer <<EMAIL>>

- Remove Max RPMs; the server RPMs contain a mysqld compiled with all
  features that previously only were built into Max.

* Fri Mar 02 2007 Joerg Bruehe <<EMAIL>>

- Add several man pages for NDB which are now created.

* Fri Jan 05 2007 Kent Boortz <<EMAIL>>

- Put back "libmygcc.a", found no real reason it was removed.

- Add CFLAGS to gcc call with --print-libgcc-file, to make sure the
  correct "libgcc.a" path is returned for the 32/64 bit architecture.

* Mon Dec 18 2006 Joerg Bruehe <<EMAIL>>

- Fix the move of "mysqlmanager" to section 8: Directory name was wrong.

* Thu Dec 14 2006 Joerg Bruehe <<EMAIL>>

- Include the new man pages for "my_print_defaults" and "mysql_tzinfo_to_sql"
  in the server RPM.
- The "mysqlmanager" man page got moved from section 1 to 8.

* Thu Nov 30 2006 Joerg Bruehe <<EMAIL>>

- Call "make install" using "benchdir_root=%%{_datadir}",
  because that is affecting the regression test suite as well.

* Thu Nov 16 2006 Joerg Bruehe <<EMAIL>>

- Explicitly note that the "MySQL-shared" RPMs (as built by MySQL AB)
  replace "mysql-shared" (as distributed by SuSE) to allow easy upgrading
  (bug#22081).

* Mon Nov 13 2006 Joerg Bruehe <<EMAIL>>

- Add "--with-partition" t 2006 Joerg Bruehe <<EMAIL>>

- Use the Perl script to run the tests, because it will automatically check
  whether the server is configured with SSL.

* Tue Jun 27 2006 Joerg Bruehe <<EMAIL>>

- move "mysqldumpslow" from the client RPM to the server RPM (bug#20216)

- Revert all previous attempts to call "mysql_upgrade" during RPM upgrade,
  there are some more aspects which need to be solved before this is possible.
  For now, just ensure the binary "mysql_upgrade" is delivered and installysql.com>

- To run "mysql_upgrade", we need a running server;
  start it in isolation and skip password checks.

* Sat May 20 2006 Kent Boortz <<EMAIL>>

- Always compile for PIC, position independent code.

* Wed May 10 2006 Kent Boortz <<EMAIL>>

- Use character set "all" when compiling with Cluster, to make Cluster
  nodes independent on the character set directory, and the problem
  that two RPM sub packages both wants to install this directory.

* Mon May 01 2006 Kent Boortz <<EMAIL>>

- Use "./libtool --mode=execute" instead of searching for the
  executable in current directory and ".libs".

* Fri Apr 28 2006 Kent Boortz <<EMAIL>>

- Install and run "mysql_upgrade"

* Wed Apr 12 2006 Jim Winstead <<EMAIL>>

- Remove sql-bench, and MySQL-bench RPM (will be built as an independent
  project from the mysql-bench repository)

* Tue Apr 11 2006 Jim Winstead <<EMAIL>>

- Remove old mysqltestmanager and related programs
* Sat Apr 01 2006 Kent Boortz <<EMAIL>>

- Set $LDFLAGS from $MYSQL_BUILD_LDFLAGS

* Tue Mar 07 2006 Kent Boortz <<EMAIL>>

- Changed product name from "Community Edition" to "Community Server"

* Mon Mar 06 2006 Kent Boortz <<EMAIL>>

- Fast mutexes is now disabled by default, but should be
  used in Linux builds.

* Mon Feb 20 2006 Kent Boortz <<EMAIL>>

- Reintroduced a max build
- Limited testing of 'debug' and 'max' servers
- Berkeley DB only in 'max'

* Mon Feb 13 2006 Joerg Bruehe <<EMAIL>>

- Use "-i" on "make test-force";
  this is essential for later evaluation of this log file.

* Thu Feb 09 2006 Kent Boortz <<EMAIL>>

- Pass '-static' to libtool, link static with our own libraries, dynamic
  with system libraries.  Link with the bundled zlib.

* Wed Feb 08 2006 Kristian Nielsen <<EMAIL>>

- Modified RPM spec to match new 5.1 debug+max combined community packaging.

* Sun Dec 18 2005 Kent Boortz <<EMAIL>>

- Added "client/mysqlslap"

* Mon Dec 12 2005 Rodrigo Novo <<EMAIL>>

- Added zlib to the list of (static) libraries installed
- Added check against libtool wierdness (WRT: sql/mysqld || sql/.libs/mysqld)
- Compile MySQL with bundled zlib
- Fixed %%packager name to "MySQL Production Engineering Team"

* Mon Dec 05 2005 Joerg Bruehe <<EMAIL>>

- Avoid using the "bundled" zlib on "shared" builds:
  As it is not installed (on the build system), this gives dependency
  problems with "libtool" causing the build to fail.
  (Change was done on Nov 11, but left uncommented.)

* Tue Nov 22 2005 Joerg Bruehe <<EMAIL>>

- Extend the file existence check for "init.d/mysql" on un-install
  to also guard the call to "insserv"/"chkconfig".

* Thu Oct 27 2005 Lenz Grimmer <<EMAIL>>

- added more man pages

* Wed Oct 19 2005 Kent Boortz <<EMAIL>>

- Made yaSSL support an option (off by default)

* Wed Oct 19 2005 Kent Boortz <<EMAIL>>

- Enabled yaSSL support

* Sat Oct 15 2005 Kent Boortz <<EMAIL>>

- Give mode arguments the same way in all places
<EMAIL>>

- fixed the removing of the RPM_BUILD_ROOT in the %%clean section (the
  $RBR variable did not get expanded, thus leaving old build roots behind)

* Thu Aug 04 2005 Lenz Grimmer <<EMAIL>>

- Fixed the creation of the mysql user group account in the postinstall
  section (BUG 12348)
- Fixed enabling the Archive storage engine in the Max binary

* Tue Aug 02 2005 Lenz Grimmer <<EMAIL>>

- Fixed the Requires: tag for the server RPM (BUG 12233)

* Fri Jul 15 2005 Lenz Grimmer <<EMAIL>>

- create a "mysql" user group and assign the mysql user account to that group
  in the server postinstall section. (BUG 10984)

* Tue Jun 14 2005 Lenz Grimmer <<EMAIL>>

- Do not build statically on i386 by default, only when adding either "--with
  static" or "--define '_with_static 1'" to the RPM build options. Static
  linking really only makes sense when linking against the specially patched
  glibc 2.2.5.

* Mon Jun 06 2005 Lenz Grimmer <<EMAIL>>

- added mysql_client_test to the "bench" subpackage (BUG 10676)
- added the libndbclient static and shared libraries (BUG 10676)

* Wed Jun 01 2005 Lenz Grimmer <<EMAIL>>

- use "mysqldatadir" variable instead of hard-coding the path multiple times
- use the "mysqld_user" variable on all occasions a user name is referenced
- removed (incomplete) Brazilian translations
- removed redundant release tags from the subpackage descriptions

* Wed May 25 2005 Joerg Bruehe <<EMAIL>>

- Added a "make clean" between separate calls to "BuildMySQL".

* Thu May 12 2005 Guilhem Bichot <<EMAIL>>

- Removed the mysql_tableinfo script made obsolete by the information schema

* Wed Apr 20 2005 Lenz Grimmer <<EMAIL>>

- Enabled the "blackhole" storage engine for the Max RPM

* Wed Apr 13 2005 Lenz Grimmer <<EMAIL>>

- removed the MySQL manual files (html/ps/texi) - they have been removed
  from the MySQL sources and are now available separately.

* Mon Apr 4 2005 Petr Chardin <<EMAIL>>

- old mysqlmanager, mysq* Mon Feb 7 2005 Tomas Ulin <<EMAIL>>

- enabled the "Ndbcluster" storage engine for the max binary
- added extra make install in ndb subdir after Max build to get ndb binaries
- added packages for ndbcluster storage engine

* Fri Jan 14 2005 Lenz Grimmer <<EMAIL>>

- replaced obsoleted "BuildPrereq" with "BuildRequires" instead

* Thu Jan 13 2005 Lenz Grimmer <<EMAIL>>

- enabled the "Federated" storage engine for the max binary

* Tue Jan 04 2005 Petr Chardin <<EMAIL>>

- ISAM and merge storage engines were purged. As well as appropriate
  tools and manpages (isamchk and isamlog)

* Fri Dec 31 2004 Lenz Grimmer <<EMAIL>>

- enabled the "Archive" storage engine for the max binary
- enabled the "CSV" storage engine for the max binary
- enabled the "Example" storage engine for the max binary

* Thu Aug 26 2004 Lenz Grimmer <<EMAIL>>

- MySQL-Max now requires MySQL-server instead of MySQL (BUG 3860)

* Fri Aug 20 2004 Lenz Grimmer <<EMAIL>>

- do not link statically on IA64/AMD64 as these systems do not have
  a patched glibc installed

* Tue Aug 10 2004 Lenz Grimmer <<EMAIL>>

- Added libmygcc.a to the devel subpackage (required to link applications
  against the the embedded server libmysqld.a) (BUG 4921)

* Mon Aug 09 2004 Lenz Grimmer <<EMAIL>>

- Added EXCEPTIONS-CLIENT to the "devel" package

* Thu Jul 29 2004 Lenz Grimmer <<EMAIL>>

- disabled OpenSSL in the Max binaries again (the RPM packages were the
  only exception to this anyway) (BUG 1043)

* Wed Jun 30 2004 Lenz Grimmer <<EMAIL>>

- fixed server postinstall (mysql_install_db was called with the wrong
  parameter)

* Thu Jun 24 2004 Lenz Grimmer <<EMAIL>>

- added mysql_tzinfo_to_sql to the server subpackage
- run "make clean" instead of "make distclean"

* Mon Apr 05 2004 Lenz Grimmer <<EMAIL>>

- added ncurses-devel to the build prerequisites (BUG 3377)

* Thu Feb 12 2004 Lenz Grimmer <<EMAIL>>

- when using gcc, _always_ use CXX=gcc
- replaced Copyright with License field (Copyright is obsolete)

* Tue Feb 03 2004 Lenz Grimmer <<EMAIL>>

- added myisam_ftdump to the Server package

* Tue Jan 13 2004 Lenz Grimmer <<EMAIL>>

- link the mysql client against libreadline instead of libedit (BUG 2289)

* Mon Dec 22 2003 Lenz Grimmer <<EMAIL>>

- marked /etc/logrotate.d/mysql as a config file (BUG 2156)

* Sat Dec 13 2003 Lenz Grimmer <<EMAIL>>

- fixed file permissions (BUG 1672)

* Thu Dec 11 2003 Lenz Grimmer <<EMAIL>>

- made testing for gcc3 a bit more robust

* Fri Dec 05 2003 Lenz Grimmer <<EMAIL>>

- added missing file mysql_create_system_tables to the server subpackage

* Fri Nov 21 2003 Lenz Grimmer <<EMAIL>>

- removed dependency on MySQL-client from the MySQL-devel subpackage
  as it is not really required. (BUG 1610)

* Fri Aug 29 2003 Lenz Grimmer <<EMAIL>>

- Fixed BUG 1162 (removed macro names from the changelog)
- Really fixed BUG 998 (disable the checking for installed but
  unpackaged files)

* Tue Aug 05 2003 Lenz Grimmer <<EMAIL>>

- Fixed BUG 959 (libmysqld not being compiled properly)
- Fixed BUG 998 (RPM build errors): added missing files to the
  distribution (mysql_fix_extensions, mysql_tableinfo, mysqldumpslow,
  mysql_fix_privilege_tables.1), removed "-n" from install section.

* Wed Jul 09 2003 Lenz Grimmer <<EMAIL>>

- removed the GIF Icon (file was not included in the sources anyway)
- removed unused variable shared_lib_version
- do not run automake before building the standard binary
  (should not be necessary)
- add server suffix '-standard' to standard binary (to be in line
  with the binary tarball distributions)
- Use more RPM macros (_exec_prefix, _sbindir, _libdir, _sysconfdir,
  _datadir, _includedir) throughout the spec file.
- allow overriding CC and CXX (required when building with other compilers)

* Fri May 16 2003 Lenz Grimmer <<EMAIL>>

- re-enabled RAID again

* Wed Apr 30 2003 Lenz Grimmer <<EMAIL>>

- disabled MyISAM RAID (--with-raid)- it throws an assertion which
  needs to be investigated first.

* Mon Mar 10 2003 Lenz Grimmer <<EMAIL>>

- added missing file mysql_secure_installation to server subpackage
  (BUG 141)

* Tue Feb 11 2003 Lenz Grimmer <<EMAIL>>

- re-added missing pre- and post(un)install scripts to server subpackage
- added config file /etc/my.cnf to the file list (just for completeness)
- make sure to create the datadir with 755 permissions

* Mon Jan 27 2003 Lenz Grimmer <<EMAIL>>

- removed unusedql.com>

- Reworked the build steps a little bit: the Max binary is supposed
  to include OpenSSL, which cannot be linked statically, thus trying
  to statically link against a special glibc is futile anyway
- because of this, it is not required to make yet another build run
  just to compile the shared libs (saves a lot of time)
- updated package description of the Max subpackage
- clean up the BuildRoot directory afterwards

* Mon Jul 15 2002 Lenz Grimmer <<EMAIL>>

- Updated Packager information
- Fixed the build options: the regular package is supposed to
  include InnoDB and linked statically, while the Max package
  should include BDB and SSL support

* Fri May 03 2002 Lenz Grimmer <<EMAIL>>

- Use more RPM macros (e.g. infodir, mandir) to make the spec
  file more portable
- reorganized the installation of documentation files: let RPM
  take care of this
- reorganized the file list: actually install man pages along
  with the binaries of the respective subpackage
- do not include libmysqld.a in the devel subpackage as well, if we
  have a special "embedded" subpackage
- reworked the package descriptions

* Mon Oct  8 2001 Monty

- Added embedded server as a separate RPM

* Fri Apr 13 2001 Monty

- Added mysqld-max to the distribution

* Tue Jan 2  2001  Monty

- Added mysql-test to the bench package

* Fri Aug 18 2000 Tim Smith <<EMAIL>>

- Added separate libmysql_r directory; now both a threaded
  and non-threaded library is shipped.

* Tue Sep 28 1999 David Axmark <<EMAIL>>

- Added the support-files/my-example.cnf to the docs directory.

- Removed devel dependency on base since it is about client
  development.

* Wed Sep 8 1999 David Axmark <<EMAIL>>

- Cleaned up some for 3.23.

* Thu Jul 1 1999 David Axmark <<EMAIL>>

- Added support for shared libraries in a separate sub
  package. Original fix by David Fox (<EMAIL>)

- The --enable-assembler switch is now automatically disables on
  platforms there assembler code is unavailable. This should allow
  building this RPM on non i386 systems.

* Mon Feb 22 1999 David Axmark <<EMAIL>>

- Removed unportable cc switches from the spec file. The defaults can
  now be overridden with environment variables. This feature is used
  to compile the official RPM with optimal (but compiler version
  specific) switches.

- Removed the repetitive description parts for the sub rpms. Maybe add
  again if RPM gets a multiline macro capability.

- Added support for a pt_BR translation. Translation contributed by
  Jorge Godoy <<EMAIL>>.

* Wed Nov 4 1998 David Axmark <<EMAIL>>

- A lot of changes in all the rpm and install scripts. This may even
  be a working RPM :-)

* Sun Aug 16 1998 David Axmark <<EMAIL>>

- A developers changelog for MySQL is available in the source RPM. And
  there is a history of major user visible changed in the Reference
  Manual.  Only RPM specific changes will be documented here.
