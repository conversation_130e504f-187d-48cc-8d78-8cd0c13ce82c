load("@bazel_skylib//rules:build_test.bzl", "build_test")
load("@rules_pkg//:mappings.bzl", "pkg_filegroup", "pkg_files", "strip_prefix")
load("@rules_proto//proto:defs.bzl", "proto_lang_toolchain")
load("//conformance:defs.bzl", "conformance_test")
load("//java/internal:testing.bzl", "junit_tests")

exports_files(
    ["lite.awk"],
    visibility = ["//java/core:__pkg__"],
)

exports_files(
    ["pom_template.xml"],
    visibility = ["//java/core:__pkg__"],
)

alias(
    name = "lite",
    actual = "//java/core:lite",
    visibility = ["//visibility:public"],
)

proto_lang_toolchain(
    name = "toolchain",
    # keep this in sync w/ LITE_WELL_KNOWN_PROTO_MAP in //:BUILD
    blacklisted_protos = [
        "//:any_proto",
        "//:api_proto",
        "//:duration_proto",
        "//:empty_proto",
        "//:field_mask_proto",
        "//:source_context_proto",
        "//:struct_proto",
        "//:timestamp_proto",
        "//:type_proto",
        "//:wrappers_proto",
    ],
    command_line = "--java_out=lite:$(OUT)",
    runtime = ":lite",
    visibility = ["//visibility:public"],
)

test_suite(
    name = "tests",
    tests = [
        "conformance_test",
        "lite_build_test",
        "lite_tests",
        "//java/core:lite_tests",
    ],
)

build_test(
    name = "lite_build_test",
    targets = [
        ":lite",
    ],
)

conformance_test(
    name = "conformance_test",
    failure_list = "//conformance:failure_list_java_lite.txt",
    testee = "//conformance:conformance_java_lite",
    text_format_failure_list = "//conformance:text_format_failure_list_java_lite.txt",
)

junit_tests(
    name = "lite_tests",
    size = "small",
    srcs = glob(["src/test/**/*.java"]),
    deps = [
        ":lite",
        "//java/core:generic_test_protos_java_proto_lite",
        "//java/core:java_test_protos_java_proto_lite",
        "//java/core:lite_test_protos_java_proto_lite",
        "//java/core:test_util_lite",
        "@maven//:com_google_truth_truth",
        "@maven//:junit_junit",
    ],
)

pkg_files(
    name = "dist_files",
    srcs = glob([
        "**/*.java",
    ]) + [
        "BUILD.bazel",
        "generate-sources-build.xml",
        "generate-test-sources-build.xml",
        "lite.awk",
        "pom.xml",
        "pom_template.xml",
        "process-lite-sources-build.xml",
    ],
    strip_prefix = strip_prefix.from_root(""),
    visibility = ["//java:__pkg__"],
)
