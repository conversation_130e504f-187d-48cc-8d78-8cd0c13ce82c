DROP TABLE IF EXISTS t1,t2,t3,t4;
SET sql_mode='NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1(c1 YEAR NOT NULL, c2 YEAR NULL, c3 DATE, c4 DATETIME, PRIMARY KEY(c1), UNIQUE INDEX(c2));
CREATE TABLE t2(c1 YEAR NOT NULL, c2 YEAR NOT NULL, c3 DATE, c4 DATETIME, PRIMARY KEY(c1,c2));
CREATE TABLE t3(c1 YEAR NOT NULL, c2 YEAR NULL, c3 DATE, c4 DATETIME, UNIQUE INDEX idx(c1,c2));
CREATE TABLE t4(c1 YEAR NOT NULL, c2 YEAR NULL, c3 DATE, c4 DATETIME);
INSERT INTO t1 VALUES('1901','1901','98-12-31','98.12.31 11:30:45'),('1999','1999','98-12-30','98.12.30 11:30:45'),('2000','2000','98-12-29','98.12.29 11:30:45'),('2001','2001','98-12-28','98.12.28 11:30:45'),('2099','2099','98-12-27','98.12.27 11:30:45'),('2100','2100','98-12-26','98.12.26 11:30:45'),('2155','2155','98-12-26','98.12.26 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.31 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.30 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.29 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.28 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.27 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
INSERT INTO t2 VALUES('1901','1901','98-12-31','98.12.31 11:30:45'),('1999','1999','98-12-30','98.12.30 11:30:45'),('2000','2000','98-12-29','98.12.29 11:30:45'),('2001','2001','98-12-28','98.12.28 11:30:45'),('2099','2099','98-12-27','98.12.27 11:30:45'),('2100','2100','98-12-26','98.12.26 11:30:45'),('2155','2155','98-12-26','98.12.26 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.31 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.30 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.29 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.28 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.27 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
INSERT INTO t3 VALUES('1901','1901','98-12-31','98.12.31 11:30:45'),('1999','1999','98-12-30','98.12.30 11:30:45'),('2000','2000','98-12-29','98.12.29 11:30:45'),('2001','2001','98-12-28','98.12.28 11:30:45'),('2099','2099','98-12-27','98.12.27 11:30:45'),('2100','2100','98-12-26','98.12.26 11:30:45'),('2155','2155','98-12-26','98.12.26 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.31 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.30 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.29 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.28 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.27 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES('1901','1901','98-12-31','98.12.31 11:30:45'),('1999','1999','98-12-30','98.12.30 11:30:45'),('2000','2000','98-12-29','98.12.29 11:30:45'),('2001','2001','98-12-28','98.12.28 11:30:45'),('2099','2099','98-12-27','98.12.27 11:30:45'),('2100','2100','98-12-26','98.12.26 11:30:45'),('2155','2155','98-12-25','98.12.25 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.31 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.30 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.29 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.28 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.27 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.25 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES(1901,1901,'98-12-24','98.12.24 11:30:45'),(1999,1999,'98-12-23','98.12.23 11:30:45'),(2000,2000,'98-12-22','98.12.22 11:30:45'),(2001,2001,'98-12-21','98.12.21 11:30:45'),(2099,2099,'98-12-20','98.12.20 11:30:45'),(2100,2100,'98-12-19','98.12.19 11:30:45'),(2155,2155,'98-12-18','98.12.18 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.24 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.23 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.22 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.21 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.20 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.19 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.18 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
INSERT INTO t1 VALUES('20','20','98-12-16','98.12.16 11:30:45'),('40','40','98-12-15','98.12.15 11:30:45'),('60','60','98-12-14','98.12.14 11:30:45'),('69','69','98-12-13','98.12.13 11:30:45'),('70','70','98-12-12','98.12.12 11:30:45'),('90','90','98-12-11','98.12.11 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.16 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.15 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.14 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.13 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.12 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.11 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
INSERT INTO t2 VALUES('00','10','98-12-17','98.12.17 11:30:45'),('20','20','98-12-16','98.12.16 11:30:45'),('40','40','98-12-15','98.12.15 11:30:45'),('60','60','98-12-14','98.12.14 11:30:45'),('69','69','98-12-13','98.12.13 11:30:45'),('70','70','98-12-12','98.12.12 11:30:45'),('90','90','98-12-11','98.12.11 11:30:45'),('99','98','98-12-10','98.12.10 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.17 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.16 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.15 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.14 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.13 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.12 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.11 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.10 11:30:45' at row 8 is deprecated. Prefer the standard '-'.
INSERT INTO t3 VALUES('00','10','98-12-17','98.12.17 11:30:45'),('20','20','98-12-16','98.12.16 11:30:45'),('40','40','98-12-15','98.12.15 11:30:45'),('60','60','98-12-14','98.12.14 11:30:45'),('69','69','98-12-13','98.12.13 11:30:45'),('70','70','98-12-12','98.12.12 11:30:45'),('90','90','98-12-11','98.12.11 11:30:45'),('99','98','98-12-10','98.12.10 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.17 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.16 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.15 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.14 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.13 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.12 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.11 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.10 11:30:45' at row 8 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES('00','00','98-12-17','98.12.17 11:30:45'),('20','20','98-12-16','98.12.16 11:30:45'),('40','40','98-12-15','98.12.15 11:30:45'),('60','60','98-12-14','98.12.14 11:30:45'),('69','69','98-12-13','98.12.13 11:30:45'),('70','70','98-12-12','98.12.12 11:30:45'),('90','90','98-12-11','98.12.11 11:30:45'),('99','99','98-12-10','98.12.10 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.17 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.16 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.15 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.14 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.13 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.12 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.11 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.10 11:30:45' at row 8 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES(1,1,'98-12-9','98.12.9 11:30:45'),(20,20,'98-12-8','98.12.8 11:30:45'),(40,40,'98-12-7','98.12.7 11:30:45'),(60,60,'98-12-6','98.12.6 11:30:45'),(69,69,'98-12-5','98.12.5 11:30:45'),(70,70,'98-12-4','98.12.4 11:30:45'),(90,90,'98-12-3','98.12.3 11:30:45'),(99,99,'98-12-2','98.12.2 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.9 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.8 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.7 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.6 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.5 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.4 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.3 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.2 11:30:45' at row 8 is deprecated. Prefer the standard '-'.
SET TIMESTAMP=1233216687;
INSERT INTO t1 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
Warnings:
Note	1265	Data truncated for column 'c3' at row 1
INSERT INTO t2 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
Warnings:
Note	1265	Data truncated for column 'c3' at row 1
INSERT INTO t3 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
Warnings:
Note	1265	Data truncated for column 'c3' at row 1
INSERT INTO t4 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
Warnings:
Note	1265	Data truncated for column 'c3' at row 1
INSERT INTO t1(c1) VALUES('5');
INSERT INTO t2(c1) VALUES('5');
Warnings:
Warning	1364	Field 'c2' doesn't have a default value
INSERT INTO t3(c1) VALUES('5');
INSERT INTO t4(c1,c2) VALUES('0','000');
INSERT INTO t2 VALUES(1900,2156,'08-01-06','08/01/07');
Warnings:
Warning	1264	Out of range value for column 'c1' at row 1
Warning	1264	Out of range value for column 'c2' at row 1
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/07' at row 1 is deprecated. Prefer the standard '-'.
INSERT INTO t3 VALUES('1900','2156','08-01-08','08/1/9');
Warnings:
Warning	1264	Out of range value for column 'c1' at row 1
Warning	1264	Out of range value for column 'c2' at row 1
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 1 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES('1900','2156','08-01-08','08/1/9'),(1900,2156,'08-01-08','08/1/9'),(0,00,'08-01-08','08/1/9'),(000,000,'08-01-08','08/1/9'),('-20','100','08-01-08','08/1/9'),(-20,100,'08-01-08','08/1/9');
Warnings:
Warning	1264	Out of range value for column 'c1' at row 1
Warning	1264	Out of range value for column 'c2' at row 1
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 1 is deprecated. Prefer the standard '-'.
Warning	1264	Out of range value for column 'c1' at row 2
Warning	1264	Out of range value for column 'c2' at row 2
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 4 is deprecated. Prefer the standard '-'.
Warning	1264	Out of range value for column 'c1' at row 5
Warning	1264	Out of range value for column 'c2' at row 5
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 5 is deprecated. Prefer the standard '-'.
Warning	1264	Out of range value for column 'c1' at row 6
Warning	1264	Out of range value for column 'c2' at row 6
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 6 is deprecated. Prefer the standard '-'.
INSERT INTO t1 VALUES('','','08-01-04','08/01/05') /* Inserts zero dates for '' strings */;
Warnings:
Warning	1366	Incorrect integer value: '' for column 'c1' at row 1
Warning	1366	Incorrect integer value: '' for column 'c2' at row 1
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/05' at row 1 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES('abcd','abcd','08-01-10','08/01/11'),(1234,1234,'08-01-12','08/01/13') /* Inserts zero dates for absurd dates */;
Warnings:
Warning	1366	Incorrect integer value: 'abcd' for column 'c1' at row 1
Warning	1366	Incorrect integer value: 'abcd' for column 'c2' at row 1
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/11' at row 1 is deprecated. Prefer the standard '-'.
Warning	1264	Out of range value for column 'c1' at row 2
Warning	1264	Out of range value for column 'c2' at row 2
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/13' at row 2 is deprecated. Prefer the standard '-'.
INSERT INTO t2 VALUES('20','30','98-12-16','98.12.16 11:30:45'),('40','20','98-12-15','98.12.15 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.16 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.15 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
INSERT INTO t3 VALUES('00','20','98-12-17','98.12.17 11:30:45'),('20','40','98-12-10','98.12.10 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.17 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.10 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
INSERT INTO t1 VALUES(80,NULL,'08-01-02','08/01/03');
Warnings:
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/03' at row 1 is deprecated. Prefer the standard '-'.
INSERT INTO t3 VALUES(80,NULL,'08-01-02','08/01/03');
Warnings:
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/03' at row 1 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES(80,NULL,'08-01-02','08/01/03');
Warnings:
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/03' at row 1 is deprecated. Prefer the standard '-'.
INSERT INTO t1(c1,c2) VALUES(1992,NULL);
SELECT * FROM t1;
c1	c2	c3	c4
0000	0000	2008-01-04	2008-01-05 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1992	NULL	NULL	NULL
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t4;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
0000	0000	2008-01-08	2008-01-09 00:00:00
0000	0000	2008-01-08	2008-01-09 00:00:00
0000	0000	2008-01-08	2008-01-09 00:00:00
0000	0000	2008-01-08	2008-01-09 00:00:00
0000	0000	2008-01-08	2008-01-09 00:00:00
0000	0000	2008-01-10	2008-01-11 00:00:00
0000	0000	2008-01-12	2008-01-13 00:00:00
1901	1901	1998-12-24	1998-12-24 11:30:45
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-04	1998-12-04 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-03	1998-12-03 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-02	1998-12-02 11:30:45
1999	1999	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-23	1998-12-23 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-22	1998-12-22 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2000	NULL	NULL
2001	2001	1998-12-09	1998-12-09 11:30:45
2001	2001	1998-12-21	1998-12-21 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-08	1998-12-08 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-07	1998-12-07 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-06	1998-12-06 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-05	1998-12-05 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-20	1998-12-20 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-19	1998-12-19 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-18	1998-12-18 11:30:45
2155	2155	1998-12-25	1998-12-25 11:30:45
SELECT * FROM t2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
total_rows	min_value	max(c1)
20	0	2155
SELECT * FROM t2 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t2;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2001	2001
2005	0000
2009	2009
2020	2020
2020	2030
2040	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1999
1999	1998
2000	2010
2000	2000
2001	2001
2005	0000
2009	2009
2020	2030
2020	2020
2040	2040
2040	2020
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 = '1901' ORDER BY c1,c2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 = '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 = '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 = '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <> '1901' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 <> '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t2 WHERE c1 > '1901' ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 > '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT * FROM t2 WHERE c1 >= '1901' ORDER BY c1,c2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 >= '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t2 WHERE c1 < '1901' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
SELECT * FROM t2 WHERE c1 < '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
SELECT * FROM t2 WHERE c1 <= '1901' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <= '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <=> '1901' ORDER BY c1,c2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <=> '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 BETWEEN '1901' AND '2020' ORDER BY c1,c2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 BETWEEN '1901' AND '2020' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t2 WHERE c1 IN ('1901','2020') ORDER BY c1,c2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 IN ('1901','2020') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 >= '1901' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t2 WHERE c1 >= '1901' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <> '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 <> '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t2 WHERE c1 > '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 > '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT * FROM t2 WHERE c1 >= '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 >= '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t2 WHERE c1 < '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
SELECT * FROM t2 WHERE c1 < '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
SELECT * FROM t2 WHERE c1 <= '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <= '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <=> '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <=> '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 BETWEEN '1901' AND '2020' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 BETWEEN '1901' AND '2020' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t2 WHERE c1 IN ('1901','2020') ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 IN ('1901','2020') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 >= '1901' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t2 WHERE c1 >= '1901' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
total_rows	min_value	max(c1)
20	0	2155
SELECT * FROM t2 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t2;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2001	2001
2005	0000
2009	2009
2020	2020
2020	2030
2040	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1999
1999	1998
2000	2010
2000	2000
2001	2001
2005	0000
2009	2009
2020	2030
2020	2020
2040	2040
2040	2020
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 = '10' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 = '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 = '10' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 = '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 <> '10' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 <> '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 > '10' ORDER BY c1,c2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 > '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 >= '10' ORDER BY c1,c2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 >= '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 < '10' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t2 WHERE c1 < '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <= '10' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t2 WHERE c1 <= '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <=> '10' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 <=> '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 BETWEEN '10' AND '2020' ORDER BY c1,c2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 BETWEEN '10' AND '2020' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 IN ('10','2020') ORDER BY c1,c2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 IN ('10','2020') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 >= '10' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 >= '10' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <> '10' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 <> '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 > '10' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 > '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 >= '10' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 >= '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 < '10' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t2 WHERE c1 < '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <= '10' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t2 WHERE c1 <= '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <=> '10' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 <=> '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 BETWEEN '10' AND '2020' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 BETWEEN '10' AND '2020' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 IN ('10','2020') ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 IN ('10','2020') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 >= '10' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 >= '10' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
total_rows	min_value	max(c2)
20	0	2155
SELECT * FROM t2 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t2;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2001	2001
2005	0000
2009	2009
2020	2020
2020	2030
2040	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1999
1999	1998
2000	2010
2000	2000
2001	2001
2005	0000
2009	2009
2020	2030
2020	2020
2040	2040
2040	2020
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 = 2155 ORDER BY c1,c2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 = 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 = 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 = 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <> 2155 ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <> 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 > 2155 ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 > 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= 2155 ORDER BY c1,c2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 >= 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 < 2155 ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 < 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 <= 2155 ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <= 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 <=> 2155 ORDER BY c1,c2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <=> 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 BETWEEN '1970' AND 2155 ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 BETWEEN '1970' AND 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT * FROM t2 WHERE c2 IN ('1970',2155) ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 IN ('1970',2155) ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 >= '1970' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2;
c1	c2	c3	c4
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
SELECT * FROM t2 WHERE c2 >= '1970' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 <> 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <> 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 > 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 > 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 >= 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 < 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 < 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 <= 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <= 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 <=> 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <=> 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 BETWEEN '1970' AND 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 BETWEEN '1970' AND 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT * FROM t2 WHERE c2 IN ('1970',2155) ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 IN ('1970',2155) ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 >= '1970' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t2 WHERE c2 >= '1970' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
total_rows	min_value	max(c2)
20	0	2155
SELECT * FROM t2 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t2;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2001	2001
2005	0000
2009	2009
2020	2020
2020	2030
2040	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1999
1999	1998
2000	2010
2000	2000
2001	2001
2005	0000
2009	2009
2020	2030
2020	2020
2040	2040
2040	2020
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 = NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 = NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 = NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 = NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <> NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <> NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 > NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 > NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 < NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 < NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <= NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <= NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <=> NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <=> NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t2 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t2 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 <> NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <> NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 > NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 > NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 < NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 < NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <= NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <= NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <=> NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <=> NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t2 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t2 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
total_rows	min_value	max(c1)
21	0	2155
SELECT * FROM t3 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t3;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2000	2020
2001	2001
2005	NULL
2009	2009
2020	2020
2020	2040
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1999
1999	1998
2000	2020
2000	2010
2000	2000
2001	2001
2005	NULL
2009	2009
2020	2040
2020	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 = '1970' ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 = '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 = '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 = '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 <> '1970' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 <> '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 > '1970' ORDER BY c1,c2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 > '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT * FROM t3 WHERE c1 >= '1970' ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 >= '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
SELECT * FROM t3 WHERE c1 < '1970' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 < '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <= '1970' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 <= '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <=> '1970' ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 <=> '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 BETWEEN '1970' AND '2020' ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t3 WHERE c1 BETWEEN '1970' AND '2020' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
SELECT * FROM t3 WHERE c1 IN ('1970','2020') ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t3 WHERE c1 IN ('1970','2020') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t3 WHERE c1 >= '1970' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t3 WHERE c1 >= '1970' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <> '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 <> '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 > '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 > '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT * FROM t3 WHERE c1 >= '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 >= '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
SELECT * FROM t3 WHERE c1 < '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 < '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <= '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 <= '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <=> '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 <=> '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 BETWEEN '1970' AND '2020' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t3 WHERE c1 BETWEEN '1970' AND '2020' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
SELECT * FROM t3 WHERE c1 IN ('1970','2020') ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t3 WHERE c1 IN ('1970','2020') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t3 WHERE c1 >= '1970' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t3 WHERE c1 >= '1970' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
total_rows	min_value	max(c1)
21	0	2155
SELECT * FROM t3 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t3;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2000	2020
2001	2001
2005	NULL
2009	2009
2020	2020
2020	2040
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1999
1999	1998
2000	2020
2000	2010
2000	2000
2001	2001
2005	NULL
2009	2009
2020	2040
2020	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 = '69' ORDER BY c1,c2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 = '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 = '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 = '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 <> '69' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 <> '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 > '69' ORDER BY c1,c2;
c1	c2	c3	c4
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 > '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 >= '69' ORDER BY c1,c2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 >= '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
SELECT * FROM t3 WHERE c1 < '69' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
SELECT * FROM t3 WHERE c1 < '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <= '69' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 <= '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <=> '69' ORDER BY c1,c2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 <=> '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 BETWEEN 69 AND '2020' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 BETWEEN 69 AND '2020' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IN (69,'2020') ORDER BY c1,c2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t3 WHERE c1 IN (69,'2020') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t3 WHERE c1 >= 69 AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 >= 69 AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <> '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 <> '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 > '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 > '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 >= '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 >= '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
SELECT * FROM t3 WHERE c1 < '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
SELECT * FROM t3 WHERE c1 < '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <= '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 <= '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <=> '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 <=> '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 BETWEEN 69 AND '2020' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 BETWEEN 69 AND '2020' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IN (69,'2020') ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t3 WHERE c1 IN (69,'2020') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t3 WHERE c1 >= 69 AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 >= 69 AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
total_rows	min_value	max(c2)
21	0	2155
SELECT * FROM t3 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t3;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2000	2020
2001	2001
2005	NULL
2009	2009
2020	2020
2020	2040
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1999
1999	1998
2000	2020
2000	2010
2000	2000
2001	2001
2005	NULL
2009	2009
2020	2040
2020	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 = 2000 ORDER BY c1,c2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 = 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 = 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 = 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 <> 2000 ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 <> 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 > 2000 ORDER BY c1,c2;
c1	c2	c3	c4
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 > 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
SELECT * FROM t3 WHERE c2 >= 2000 ORDER BY c1,c2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 >= 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
SELECT * FROM t3 WHERE c2 < 2000 ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
SELECT * FROM t3 WHERE c2 < 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 <= 2000 ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 <= 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 <=> 2000 ORDER BY c1,c2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 <=> 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 BETWEEN '2000' AND 2155 ORDER BY c1,c2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 BETWEEN '2000' AND 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
SELECT * FROM t3 WHERE c2 IN ('2000',2155) ORDER BY c1,c2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 IN ('2000',2155) ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 >= '2000' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= '2000' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 <> 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 <> 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 > 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 > 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
SELECT * FROM t3 WHERE c2 >= 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 >= 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
SELECT * FROM t3 WHERE c2 < 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t3 WHERE c2 < 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 <= 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 <= 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 <=> 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 <=> 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 BETWEEN '2000' AND 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 BETWEEN '2000' AND 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
SELECT * FROM t3 WHERE c2 IN ('2000',2155) ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 IN ('2000',2155) ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 >= '2000' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= '2000' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
total_rows	min_value	max(c2)
21	0	2155
SELECT * FROM t3 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t3;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2000	2020
2001	2001
2005	NULL
2009	2009
2020	2020
2020	2040
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1999
1999	1998
2000	2020
2000	2010
2000	2000
2001	2001
2005	NULL
2009	2009
2020	2040
2020	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c1,c2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT c1 FROM t3 GROUP BY c1;
c1
0000
1901
1970
1980
1990
1999
2000
2001
2005
2009
2020
2040
2060
2069
2099
2100
2155
SELECT DISTINCT c1 FROM t3;
c1
0000
1901
1970
1980
1990
1999
2000
2001
2005
2009
2020
2040
2060
2069
2099
2100
2155
SELECT c1,MIN(c2) FROM t3 GROUP BY c1;
c1	MIN(c2)
0000	0
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1998
2000	2000
2001	2001
2005	NULL
2009	2009
2020	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t1 WHERE c1='1901' OR c2='2155';
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
INSERT INTO t1(c1,c2) VALUES(01,'99');
ERROR 23000: Duplicate entry '2001' for key 't1.PRIMARY'
INSERT INTO t2(c1,c2) VALUES(99,99);
ERROR 23000: Duplicate entry '1999-1999' for key 't2.PRIMARY'
INSERT INTO t1(c1,c2) VALUES('2098',69);
ERROR 23000: Duplicate entry '2069' for key 't1.c2'
INSERT INTO t3(c1,c2) VALUES(00,00);
ERROR 23000: Duplicate entry '0000-0000' for key 't3.idx'
INSERT IGNORE INTO t1(c1,c2) VALUES(01,'99');
Warnings:
Warning	1062	Duplicate entry '2001' for key 't1.PRIMARY'
INSERT IGNORE INTO t2(c1,c2) VALUES('1999','1999');
Warnings:
Warning	1062	Duplicate entry '1999-1999' for key 't2.PRIMARY'
INSERT IGNORE INTO t1(c1,c2) VALUES('2098','69');
Warnings:
Warning	1062	Duplicate entry '2069' for key 't1.c2'
INSERT IGNORE INTO t3(c1,c2) VALUES(00,00);
Warnings:
Warning	1062	Duplicate entry '0000-0000' for key 't3.idx'
SELECT * FROM t1 WHERE c1='01' /* Returns 1 row */;
c1	c2	c3	c4
2001	2001	1998-12-28	1998-12-28 11:30:45
SELECT * FROM t2 WHERE c1='1999' AND c2='1999' /* Returns 1 row */;
c1	c2	c3	c4
1999	1999	1998-12-30	1998-12-30 11:30:45
SELECT * FROM t1 WHERE c2='69' /* Returns 1 row */;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1=0 AND c2=0 /* Returns 1 row */;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
SELECT * FROM t1 WHERE c1='02' /* Returns no rows */;
c1	c2	c3	c4
INSERT INTO t1(c1) VALUES('00') ON DUPLICATE KEY UPDATE c1='02';
SELECT * FROM t1 WHERE c1='2002' /* Returns 1 row */;
c1	c2	c3	c4
2002	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t2 WHERE c1=69 AND c2=70 /* Returns no rows */;
c1	c2	c3	c4
INSERT INTO t2 VALUES(99,99,'98-12-20','98.12.20 11:30:45') ON DUPLICATE KEY UPDATE c1=69,c2=70;
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.20 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
SELECT * FROM t2 WHERE c1=2069 AND c2=1970 /* Returns 1 row */;
c1	c2	c3	c4
2069	1970	1998-12-30	1998-12-30 11:30:45
SELECT * FROM t1 WHERE c1=54 AND c2=53 /* Returns no rows */;
c1	c2	c3	c4
INSERT INTO t1 VALUES(69,69,'98-12-18','98.12.18 11:30:45') ON DUPLICATE KEY UPDATE c1=54,c2=53;
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.18 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
SELECT * FROM t1 WHERE c1=54 AND c2=53 /* Returns 1 row */;
c1	c2	c3	c4
2054	2053	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1=6 AND c2=1970 /* Returns no rows */;
c1	c2	c3	c4
INSERT INTO t3(c1,c2) VALUES('69','69') ON DUPLICATE KEY UPDATE c1=06,c2=70;
SELECT * FROM t3 WHERE c1=2006 AND c2=1970 /* Returns 1 row */;
c1	c2	c3	c4
2006	1970	1998-12-13	1998-12-13 11:30:45
CREATE TABLE t5(c1 YEAR NOT NULL, c2 YEAR NULL, c3 DATE, c4 DATETIME, INDEX idx(c1,c2));
INSERT INTO t5 SELECT * FROM t1;
SELECT * FROM t5;
c1	c2	c3	c4
0000	0000	2008-01-04	2008-01-05 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1992	NULL	NULL	NULL
1999	1999	1998-12-30	1998-12-30 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2002	2000	1998-12-29	1998-12-29 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2054	2053	1998-12-13	1998-12-13 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
TRUNCATE TABLE t5;
INSERT INTO t5 SELECT * FROM t2 WHERE c1 >=1970 AND c1 < 1999 AND c2 <> '1990';
SELECT * FROM t5;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
TRUNCATE TABLE t5;
INSERT INTO t5 SELECT * FROM t3 WHERE c2 BETWEEN '2000' AND '2155';
SELECT * FROM t5;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
TRUNCATE TABLE t5;
DROP TABLE t5;
INSERT INTO t1 SET c1='00';
INSERT INTO t2 SET c1=69;
Warnings:
Warning	1364	Field 'c2' doesn't have a default value
INSERT INTO t3 SET c1=70;
INSERT INTO t4 SET c2=99;
Warnings:
Warning	1364	Field 'c1' doesn't have a default value
SELECT * FROM t1 WHERE c1=0;
c1	c2	c3	c4
0000	0000	2008-01-04	2008-01-05 00:00:00
SELECT * FROM t2 WHERE c1=69;
c1	c2	c3	c4
2069	0000	NULL	NULL
2069	1970	1998-12-30	1998-12-30 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1=70;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1970	NULL	NULL	NULL
SELECT * FROM t4 WHERE c2=99;
c1	c2	c3	c4
0000	1999	NULL	NULL
1999	1999	1998-12-02	1998-12-02 11:30:45
1999	1999	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-23	1998-12-23 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 YEAR NULL, c3 DATE, c4 DATETIME, PRIMARY KEY(c1), UNIQUE INDEX(c2));
CREATE TABLE t2(c1 YEAR NOT NULL, c2 YEAR NOT NULL, c3 DATE, c4 DATETIME, PRIMARY KEY(c1,c2));
CREATE TABLE t3(c1 YEAR NOT NULL, c2 YEAR NULL, c3 DATE, c4 DATETIME, UNIQUE INDEX idx(c1,c2));
CREATE TABLE t4(c1 YEAR NOT NULL, c2 YEAR NULL, c3 DATE, c4 DATETIME);
INSERT INTO t1 VALUES('1901','1901','98-12-31','98.12.31 11:30:45'),('1999','1999','98-12-30','98.12.30 11:30:45'),('2000','2000','98-12-29','98.12.29 11:30:45'),('2001','2001','98-12-28','98.12.28 11:30:45'),('2099','2099','98-12-27','98.12.27 11:30:45'),('2100','2100','98-12-26','98.12.26 11:30:45'),('2155','2155','98-12-26','98.12.26 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.31 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.30 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.29 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.28 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.27 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
INSERT INTO t2 VALUES('1901','1901','98-12-31','98.12.31 11:30:45'),('1999','1999','98-12-30','98.12.30 11:30:45'),('2000','2000','98-12-29','98.12.29 11:30:45'),('2001','2001','98-12-28','98.12.28 11:30:45'),('2099','2099','98-12-27','98.12.27 11:30:45'),('2100','2100','98-12-26','98.12.26 11:30:45'),('2155','2155','98-12-26','98.12.26 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.31 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.30 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.29 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.28 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.27 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
INSERT INTO t3 VALUES('1901','1901','98-12-31','98.12.31 11:30:45'),('1999','1999','98-12-30','98.12.30 11:30:45'),('2000','2000','98-12-29','98.12.29 11:30:45'),('2001','2001','98-12-28','98.12.28 11:30:45'),('2099','2099','98-12-27','98.12.27 11:30:45'),('2100','2100','98-12-26','98.12.26 11:30:45'),('2155','2155','98-12-26','98.12.26 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.31 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.30 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.29 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.28 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.27 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES('1901','1901','98-12-31','98.12.31 11:30:45'),('1999','1999','98-12-30','98.12.30 11:30:45'),('2000','2000','98-12-29','98.12.29 11:30:45'),('2001','2001','98-12-28','98.12.28 11:30:45'),('2099','2099','98-12-27','98.12.27 11:30:45'),('2100','2100','98-12-26','98.12.26 11:30:45'),('2155','2155','98-12-25','98.12.25 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.31 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.30 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.29 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.28 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.27 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.26 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.25 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES(1901,1901,'98-12-24','98.12.24 11:30:45'),(1999,1999,'98-12-23','98.12.23 11:30:45'),(2000,2000,'98-12-22','98.12.22 11:30:45'),(2001,2001,'98-12-21','98.12.21 11:30:45'),(2099,2099,'98-12-20','98.12.20 11:30:45'),(2100,2100,'98-12-19','98.12.19 11:30:45'),(2155,2155,'98-12-18','98.12.18 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.24 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.23 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.22 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.21 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.20 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.19 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.18 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
INSERT INTO t1 VALUES('20','20','98-12-16','98.12.16 11:30:45'),('40','40','98-12-15','98.12.15 11:30:45'),('60','60','98-12-14','98.12.14 11:30:45'),('69','69','98-12-13','98.12.13 11:30:45'),('70','70','98-12-12','98.12.12 11:30:45'),('90','90','98-12-11','98.12.11 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.16 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.15 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.14 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.13 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.12 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.11 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
INSERT INTO t2 VALUES('00','10','98-12-17','98.12.17 11:30:45'),('20','20','98-12-16','98.12.16 11:30:45'),('40','40','98-12-15','98.12.15 11:30:45'),('60','60','98-12-14','98.12.14 11:30:45'),('69','69','98-12-13','98.12.13 11:30:45'),('70','70','98-12-12','98.12.12 11:30:45'),('90','90','98-12-11','98.12.11 11:30:45'),('99','98','98-12-10','98.12.10 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.17 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.16 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.15 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.14 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.13 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.12 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.11 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.10 11:30:45' at row 8 is deprecated. Prefer the standard '-'.
INSERT INTO t3 VALUES('00','10','98-12-17','98.12.17 11:30:45'),('20','20','98-12-16','98.12.16 11:30:45'),('40','40','98-12-15','98.12.15 11:30:45'),('60','60','98-12-14','98.12.14 11:30:45'),('69','69','98-12-13','98.12.13 11:30:45'),('70','70','98-12-12','98.12.12 11:30:45'),('90','90','98-12-11','98.12.11 11:30:45'),('99','98','98-12-10','98.12.10 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.17 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.16 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.15 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.14 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.13 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.12 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.11 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.10 11:30:45' at row 8 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES('00','00','98-12-17','98.12.17 11:30:45'),('20','20','98-12-16','98.12.16 11:30:45'),('40','40','98-12-15','98.12.15 11:30:45'),('60','60','98-12-14','98.12.14 11:30:45'),('69','69','98-12-13','98.12.13 11:30:45'),('70','70','98-12-12','98.12.12 11:30:45'),('90','90','98-12-11','98.12.11 11:30:45'),('99','99','98-12-10','98.12.10 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.17 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.16 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.15 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.14 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.13 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.12 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.11 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.10 11:30:45' at row 8 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES(1,1,'98-12-9','98.12.9 11:30:45'),(20,20,'98-12-8','98.12.8 11:30:45'),(40,40,'98-12-7','98.12.7 11:30:45'),(60,60,'98-12-6','98.12.6 11:30:45'),(69,69,'98-12-5','98.12.5 11:30:45'),(70,70,'98-12-4','98.12.4 11:30:45'),(90,90,'98-12-3','98.12.3 11:30:45'),(99,99,'98-12-2','98.12.2 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.9 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.8 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.7 11:30:45' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.6 11:30:45' at row 4 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.5 11:30:45' at row 5 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.4 11:30:45' at row 6 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.3 11:30:45' at row 7 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.2 11:30:45' at row 8 is deprecated. Prefer the standard '-'.
SET TIMESTAMP=1233216687;
INSERT INTO t1 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
Warnings:
Note	1265	Data truncated for column 'c3' at row 1
INSERT INTO t2 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
Warnings:
Note	1265	Data truncated for column 'c3' at row 1
INSERT INTO t3 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
Warnings:
Note	1265	Data truncated for column 'c3' at row 1
INSERT INTO t4 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
Warnings:
Note	1265	Data truncated for column 'c3' at row 1
INSERT INTO t1(c1) VALUES('5');
INSERT INTO t2(c1) VALUES('5');
Warnings:
Warning	1364	Field 'c2' doesn't have a default value
INSERT INTO t3(c1) VALUES('5');
INSERT INTO t4(c1,c2) VALUES('0','000');
INSERT INTO t2 VALUES(1900,2156,'08-01-06','08/01/07');
Warnings:
Warning	1264	Out of range value for column 'c1' at row 1
Warning	1264	Out of range value for column 'c2' at row 1
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/07' at row 1 is deprecated. Prefer the standard '-'.
INSERT INTO t3 VALUES('1900','2156','08-01-08','08/1/9');
Warnings:
Warning	1264	Out of range value for column 'c1' at row 1
Warning	1264	Out of range value for column 'c2' at row 1
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 1 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES('1900','2156','08-01-08','08/1/9'),(1900,2156,'08-01-08','08/1/9'),(0,00,'08-01-08','08/1/9'),(000,000,'08-01-08','08/1/9'),('-20','100','08-01-08','08/1/9'),(-20,100,'08-01-08','08/1/9');
Warnings:
Warning	1264	Out of range value for column 'c1' at row 1
Warning	1264	Out of range value for column 'c2' at row 1
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 1 is deprecated. Prefer the standard '-'.
Warning	1264	Out of range value for column 'c1' at row 2
Warning	1264	Out of range value for column 'c2' at row 2
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 2 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 3 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 4 is deprecated. Prefer the standard '-'.
Warning	1264	Out of range value for column 'c1' at row 5
Warning	1264	Out of range value for column 'c2' at row 5
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 5 is deprecated. Prefer the standard '-'.
Warning	1264	Out of range value for column 'c1' at row 6
Warning	1264	Out of range value for column 'c2' at row 6
Warning	4095	Delimiter '/' in position 2 in datetime value '08/1/9' at row 6 is deprecated. Prefer the standard '-'.
INSERT INTO t1 VALUES('','','08-01-04','08/01/05') /* Inserts zero dates for '' strings */;
Warnings:
Warning	1366	Incorrect integer value: '' for column 'c1' at row 1
Warning	1366	Incorrect integer value: '' for column 'c2' at row 1
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/05' at row 1 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES('abcd','abcd','08-01-10','08/01/11'),(1234,1234,'08-01-12','08/01/13') /* Inserts zero dates for absurd dates */;
Warnings:
Warning	1366	Incorrect integer value: 'abcd' for column 'c1' at row 1
Warning	1366	Incorrect integer value: 'abcd' for column 'c2' at row 1
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/11' at row 1 is deprecated. Prefer the standard '-'.
Warning	1264	Out of range value for column 'c1' at row 2
Warning	1264	Out of range value for column 'c2' at row 2
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/13' at row 2 is deprecated. Prefer the standard '-'.
INSERT INTO t2 VALUES('20','30','98-12-16','98.12.16 11:30:45'),('40','20','98-12-15','98.12.15 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.16 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.15 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
INSERT INTO t3 VALUES('00','20','98-12-17','98.12.17 11:30:45'),('20','40','98-12-10','98.12.10 11:30:45');
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.17 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.10 11:30:45' at row 2 is deprecated. Prefer the standard '-'.
INSERT INTO t1 VALUES(80,NULL,'08-01-02','08/01/03');
Warnings:
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/03' at row 1 is deprecated. Prefer the standard '-'.
INSERT INTO t3 VALUES(80,NULL,'08-01-02','08/01/03');
Warnings:
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/03' at row 1 is deprecated. Prefer the standard '-'.
INSERT INTO t4 VALUES(80,NULL,'08-01-02','08/01/03');
Warnings:
Warning	4095	Delimiter '/' in position 2 in datetime value '08/01/03' at row 1 is deprecated. Prefer the standard '-'.
INSERT INTO t1(c1,c2) VALUES(1992,NULL);
SELECT * FROM t1;
c1	c2	c3	c4
0000	0000	2008-01-04	2008-01-05 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1992	NULL	NULL	NULL
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t4;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
0000	0000	2008-01-08	2008-01-09 00:00:00
0000	0000	2008-01-08	2008-01-09 00:00:00
0000	0000	2008-01-08	2008-01-09 00:00:00
0000	0000	2008-01-08	2008-01-09 00:00:00
0000	0000	2008-01-08	2008-01-09 00:00:00
0000	0000	2008-01-10	2008-01-11 00:00:00
0000	0000	2008-01-12	2008-01-13 00:00:00
1901	1901	1998-12-24	1998-12-24 11:30:45
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-04	1998-12-04 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-03	1998-12-03 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-02	1998-12-02 11:30:45
1999	1999	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-23	1998-12-23 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-22	1998-12-22 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2000	NULL	NULL
2001	2001	1998-12-09	1998-12-09 11:30:45
2001	2001	1998-12-21	1998-12-21 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-08	1998-12-08 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-07	1998-12-07 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-06	1998-12-06 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-05	1998-12-05 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-20	1998-12-20 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-19	1998-12-19 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-18	1998-12-18 11:30:45
2155	2155	1998-12-25	1998-12-25 11:30:45
SELECT * FROM t2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
total_rows	min_value	max(c1)
20	0	2155
SELECT * FROM t2 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t2;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2001	2001
2005	0000
2009	2009
2020	2020
2020	2030
2040	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1999
1999	1998
2000	2010
2000	2000
2001	2001
2005	0000
2009	2009
2020	2030
2020	2020
2040	2040
2040	2020
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 = '1901' ORDER BY c1,c2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 = '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 = '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 = '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <> '1901' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 <> '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t2 WHERE c1 > '1901' ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 > '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT * FROM t2 WHERE c1 >= '1901' ORDER BY c1,c2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 >= '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t2 WHERE c1 < '1901' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
SELECT * FROM t2 WHERE c1 < '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
SELECT * FROM t2 WHERE c1 <= '1901' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <= '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <=> '1901' ORDER BY c1,c2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <=> '1901' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 BETWEEN '1901' AND '2020' ORDER BY c1,c2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 BETWEEN '1901' AND '2020' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t2 WHERE c1 IN ('1901','2020') ORDER BY c1,c2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 IN ('1901','2020') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 >= '1901' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t2 WHERE c1 >= '1901' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <> '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 <> '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t2 WHERE c1 > '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 > '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT * FROM t2 WHERE c1 >= '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 >= '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t2 WHERE c1 < '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
SELECT * FROM t2 WHERE c1 < '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
SELECT * FROM t2 WHERE c1 <= '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <= '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <=> '1901' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <=> '1901' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 BETWEEN '1901' AND '2020' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 BETWEEN '1901' AND '2020' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t2 WHERE c1 IN ('1901','2020') ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 IN ('1901','2020') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 >= '1901' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t2 WHERE c1 >= '1901' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
total_rows	min_value	max(c1)
20	0	2155
SELECT * FROM t2 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t2;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2001	2001
2005	0000
2009	2009
2020	2020
2020	2030
2040	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1999
1999	1998
2000	2010
2000	2000
2001	2001
2005	0000
2009	2009
2020	2030
2020	2020
2040	2040
2040	2020
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 = '10' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 = '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 = '10' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 = '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 <> '10' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 <> '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 > '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 >= '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 < '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <= '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <=> '10' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 <=> '10' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 BETWEEN '10' AND '2020' ORDER BY c1,c2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 BETWEEN '10' AND '2020' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 IN ('10','2020') ORDER BY c1,c2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 IN ('10','2020') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 >= '10' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 >= '10' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <> '10' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 <> '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 > '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 >= '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 < '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <= '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c1 <=> '10' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 <=> '10' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 BETWEEN '10' AND '2020' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 BETWEEN '10' AND '2020' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 IN ('10','2020') ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 IN ('10','2020') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t2 WHERE c1 >= '10' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 >= '10' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
total_rows	min_value	max(c2)
20	0	2155
SELECT * FROM t2 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t2;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2001	2001
2005	0000
2009	2009
2020	2020
2020	2030
2040	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1999
1999	1998
2000	2010
2000	2000
2001	2001
2005	0000
2009	2009
2020	2030
2020	2020
2040	2040
2040	2020
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 = 2155 ORDER BY c1,c2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 = 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 = 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 = 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <> 2155 ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <> 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 > 2155 ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 > 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= 2155 ORDER BY c1,c2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 >= 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 < 2155 ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 < 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 <= 2155 ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <= 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 <=> 2155 ORDER BY c1,c2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <=> 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 BETWEEN '1970' AND 2155 ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 BETWEEN '1970' AND 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT * FROM t2 WHERE c2 IN ('1970',2155) ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 IN ('1970',2155) ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 >= '1970' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2;
c1	c2	c3	c4
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
SELECT * FROM t2 WHERE c2 >= '1970' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 <> 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <> 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 > 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 > 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 >= 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 < 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 < 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 <= 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <= 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 <=> 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 <=> 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 BETWEEN '1970' AND 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 BETWEEN '1970' AND 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT * FROM t2 WHERE c2 IN ('1970',2155) ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 IN ('1970',2155) ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 >= '1970' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t2 WHERE c2 >= '1970' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
total_rows	min_value	max(c2)
20	0	2155
SELECT * FROM t2 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t2;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2001	2001
2005	0000
2009	2009
2020	2020
2020	2030
2040	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1990	1990
1999	1999
1999	1998
2000	2010
2000	2000
2001	2001
2005	0000
2009	2009
2020	2030
2020	2020
2040	2040
2040	2020
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 = NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 = NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 = NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 = NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <> NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <> NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 > NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 > NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 < NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 < NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <= NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <= NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <=> NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <=> NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t2 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t2 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2030	1998-12-16	1998-12-16 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t2 WHERE c2 <> NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <> NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 > NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 > NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 < NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 < NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <= NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <= NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <=> NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 <=> NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t2 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t2 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	0000	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2030	1998-12-16	1998-12-16 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2040	2020	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-06	2008-01-07 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
total_rows	min_value	max(c1)
21	0	2155
SELECT * FROM t3 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t3;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2000	2020
2001	2001
2005	NULL
2009	2009
2020	2020
2020	2040
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1999
1999	1998
2000	2020
2000	2010
2000	2000
2001	2001
2005	NULL
2009	2009
2020	2040
2020	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 = '1970' ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 = '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 = '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 = '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 <> '1970' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 <> '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 > '1970' ORDER BY c1,c2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 > '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT * FROM t3 WHERE c1 >= '1970' ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 >= '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
SELECT * FROM t3 WHERE c1 < '1970' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 < '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <= '1970' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 <= '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <=> '1970' ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 <=> '1970' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 BETWEEN '1970' AND '2020' ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t3 WHERE c1 BETWEEN '1970' AND '2020' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
SELECT * FROM t3 WHERE c1 IN ('1970','2020') ORDER BY c1,c2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t3 WHERE c1 IN ('1970','2020') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t3 WHERE c1 >= '1970' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t3 WHERE c1 >= '1970' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <> '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 <> '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 > '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 > '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT * FROM t3 WHERE c1 >= '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 >= '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
SELECT * FROM t3 WHERE c1 < '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 < '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <= '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 <= '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <=> '1970' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 <=> '1970' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
SELECT * FROM t3 WHERE c1 BETWEEN '1970' AND '2020' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t3 WHERE c1 BETWEEN '1970' AND '2020' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
SELECT * FROM t3 WHERE c1 IN ('1970','2020') ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t3 WHERE c1 IN ('1970','2020') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t3 WHERE c1 >= '1970' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t3 WHERE c1 >= '1970' AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2009	2009	2009-01-29	2009-01-29 00:00:00
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
total_rows	min_value	max(c1)
21	0	2155
SELECT * FROM t3 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t3;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2000	2020
2001	2001
2005	NULL
2009	2009
2020	2020
2020	2040
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1999
1999	1998
2000	2020
2000	2010
2000	2000
2001	2001
2005	NULL
2009	2009
2020	2040
2020	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 = '69' ORDER BY c1,c2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 = '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 = '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 = '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 <> '69' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 <> '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 > '69' ORDER BY c1,c2;
c1	c2	c3	c4
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 > '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 >= '69' ORDER BY c1,c2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 >= '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
SELECT * FROM t3 WHERE c1 < '69' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
SELECT * FROM t3 WHERE c1 < '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <= '69' ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 <= '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <=> '69' ORDER BY c1,c2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 <=> '69' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 BETWEEN 69 AND '2020' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 BETWEEN 69 AND '2020' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IN (69,'2020') ORDER BY c1,c2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t3 WHERE c1 IN (69,'2020') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t3 WHERE c1 >= 69 AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 >= 69 AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <> '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 <> '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 > '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 > '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 >= '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 >= '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
SELECT * FROM t3 WHERE c1 < '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
SELECT * FROM t3 WHERE c1 < '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <= '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 <= '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c1 <=> '69' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 <=> '69' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1 BETWEEN 69 AND '2020' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 BETWEEN 69 AND '2020' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IN (69,'2020') ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t3 WHERE c1 IN (69,'2020') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
SELECT * FROM t3 WHERE c1 >= 69 AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 >= 69 AND c1 < '2020' AND c2 = '2009' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
total_rows	min_value	max(c2)
21	0	2155
SELECT * FROM t3 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t3;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2000	2020
2001	2001
2005	NULL
2009	2009
2020	2020
2020	2040
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1999
1999	1998
2000	2020
2000	2010
2000	2000
2001	2001
2005	NULL
2009	2009
2020	2040
2020	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 = 2000 ORDER BY c1,c2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 = 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 = 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 = 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 <> 2000 ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 <> 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 > 2000 ORDER BY c1,c2;
c1	c2	c3	c4
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 > 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
SELECT * FROM t3 WHERE c2 >= 2000 ORDER BY c1,c2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 >= 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
SELECT * FROM t3 WHERE c2 < 2000 ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
SELECT * FROM t3 WHERE c2 < 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 <= 2000 ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 <= 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 <=> 2000 ORDER BY c1,c2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 <=> 2000 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 BETWEEN '2000' AND 2155 ORDER BY c1,c2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 BETWEEN '2000' AND 2155 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
SELECT * FROM t3 WHERE c2 IN ('2000',2155) ORDER BY c1,c2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 IN ('2000',2155) ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 >= '2000' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= '2000' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 <> 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 <> 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 > 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 > 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
SELECT * FROM t3 WHERE c2 >= 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 >= 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
SELECT * FROM t3 WHERE c2 < 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
SELECT * FROM t3 WHERE c2 < 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 <= 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 <= 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 <=> 2000 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 <=> 2000 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t3 WHERE c2 BETWEEN '2000' AND 2155 ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 BETWEEN '2000' AND 2155 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
SELECT * FROM t3 WHERE c2 IN ('2000',2155) ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 IN ('2000',2155) ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 >= '2000' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= '2000' AND c2 < 2155 AND c1 = '1999' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
total_rows	min_value	max(c2)
21	0	2155
SELECT * FROM t3 WHERE c3 = '1998-12-11';
c1	c2	c3	c4
1990	1990	1998-12-11	1998-12-11 11:30:45
SELECT c1,c2 FROM t3;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1998
1999	1999
2000	2000
2000	2010
2000	2020
2001	2001
2005	NULL
2009	2009
2020	2020
2020	2040
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;
c1	c2
0000	0000
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1999
1999	1998
2000	2020
2000	2010
2000	2000
2001	2001
2005	NULL
2009	2009
2020	2040
2020	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c1,c2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND '2069' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2 DESC;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c2 IN (NULL,'2069') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2 DESC;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < '2069' AND c1 = '2000' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
1980	NULL	2008-01-02	2008-01-03 00:00:00
2005	NULL	NULL	NULL
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1990	1990	1998-12-11	1998-12-11 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
1999	1998	1998-12-10	1998-12-10 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2000	1998-12-29	1998-12-29 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2040	1998-12-10	1998-12-10 11:30:45
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
SELECT c1 FROM t3 GROUP BY c1;
c1
0000
1901
1970
1980
1990
1999
2000
2001
2005
2009
2020
2040
2060
2069
2099
2100
2155
SELECT DISTINCT c1 FROM t3;
c1
0000
1901
1970
1980
1990
1999
2000
2001
2005
2009
2020
2040
2060
2069
2099
2100
2155
SELECT c1,MIN(c2) FROM t3 GROUP BY c1;
c1	MIN(c2)
0000	0
1901	1901
1970	1970
1980	NULL
1990	1990
1999	1998
2000	2000
2001	2001
2005	NULL
2009	2009
2020	2020
2040	2040
2060	2060
2069	2069
2099	2099
2100	2100
2155	2155
SELECT * FROM t1 WHERE c1='1901' OR c2='2155';
c1	c2	c3	c4
1901	1901	1998-12-31	1998-12-31 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
INSERT INTO t1(c1,c2) VALUES(01,'99');
ERROR 23000: Duplicate entry '2001' for key 't1.PRIMARY'
INSERT INTO t2(c1,c2) VALUES(99,99);
ERROR 23000: Duplicate entry '1999-1999' for key 't2.PRIMARY'
INSERT INTO t1(c1,c2) VALUES('2098',69);
ERROR 23000: Duplicate entry '2069' for key 't1.c2'
INSERT INTO t3(c1,c2) VALUES(00,00);
ERROR 23000: Duplicate entry '0000-0000' for key 't3.idx'
INSERT IGNORE INTO t1(c1,c2) VALUES(01,'99');
Warnings:
Warning	1062	Duplicate entry '2001' for key 't1.PRIMARY'
INSERT IGNORE INTO t2(c1,c2) VALUES('1999','1999');
Warnings:
Warning	1062	Duplicate entry '1999-1999' for key 't2.PRIMARY'
INSERT IGNORE INTO t1(c1,c2) VALUES('2098','69');
Warnings:
Warning	1062	Duplicate entry '2069' for key 't1.c2'
INSERT IGNORE INTO t3(c1,c2) VALUES(00,00);
Warnings:
Warning	1062	Duplicate entry '0000-0000' for key 't3.idx'
SELECT * FROM t1 WHERE c1='01' /* Returns 1 row */;
c1	c2	c3	c4
2001	2001	1998-12-28	1998-12-28 11:30:45
SELECT * FROM t2 WHERE c1='1999' AND c2='1999' /* Returns 1 row */;
c1	c2	c3	c4
1999	1999	1998-12-30	1998-12-30 11:30:45
SELECT * FROM t1 WHERE c2='69' /* Returns 1 row */;
c1	c2	c3	c4
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1=0 AND c2=0 /* Returns 1 row */;
c1	c2	c3	c4
0000	0000	2008-01-08	2008-01-09 00:00:00
SELECT * FROM t1 WHERE c1='02' /* Returns no rows */;
c1	c2	c3	c4
INSERT INTO t1(c1) VALUES('00') ON DUPLICATE KEY UPDATE c1='02';
SELECT * FROM t1 WHERE c1='2002' /* Returns 1 row */;
c1	c2	c3	c4
2002	2000	1998-12-29	1998-12-29 11:30:45
SELECT * FROM t2 WHERE c1=69 AND c2=70 /* Returns no rows */;
c1	c2	c3	c4
INSERT INTO t2 VALUES(99,99,'98-12-20','98.12.20 11:30:45') ON DUPLICATE KEY UPDATE c1=69,c2=70;
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.20 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
SELECT * FROM t2 WHERE c1=2069 AND c2=1970 /* Returns 1 row */;
c1	c2	c3	c4
2069	1970	1998-12-30	1998-12-30 11:30:45
SELECT * FROM t1 WHERE c1=54 AND c2=53 /* Returns no rows */;
c1	c2	c3	c4
INSERT INTO t1 VALUES(69,69,'98-12-18','98.12.18 11:30:45') ON DUPLICATE KEY UPDATE c1=54,c2=53;
Warnings:
Warning	4095	Delimiter '.' in position 2 in datetime value '98.12.18 11:30:45' at row 1 is deprecated. Prefer the standard '-'.
SELECT * FROM t1 WHERE c1=54 AND c2=53 /* Returns 1 row */;
c1	c2	c3	c4
2054	2053	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1=6 AND c2=1970 /* Returns no rows */;
c1	c2	c3	c4
INSERT INTO t3(c1,c2) VALUES('69','69') ON DUPLICATE KEY UPDATE c1=06,c2=70;
SELECT * FROM t3 WHERE c1=2006 AND c2=1970 /* Returns 1 row */;
c1	c2	c3	c4
2006	1970	1998-12-13	1998-12-13 11:30:45
CREATE TABLE t5(c1 YEAR NOT NULL, c2 YEAR NULL, c3 DATE, c4 DATETIME, INDEX idx(c1,c2));
INSERT INTO t5 SELECT * FROM t1;
SELECT * FROM t5;
c1	c2	c3	c4
0000	0000	2008-01-04	2008-01-05 00:00:00
1901	1901	1998-12-31	1998-12-31 11:30:45
1970	1970	1998-12-12	1998-12-12 11:30:45
1980	NULL	2008-01-02	2008-01-03 00:00:00
1990	1990	1998-12-11	1998-12-11 11:30:45
1992	NULL	NULL	NULL
1999	1999	1998-12-30	1998-12-30 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2002	2000	1998-12-29	1998-12-29 11:30:45
2005	NULL	NULL	NULL
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2054	2053	1998-12-13	1998-12-13 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
TRUNCATE TABLE t5;
INSERT INTO t5 SELECT * FROM t2 WHERE c1 >=1970 AND c1 < 1999 AND c2 <> '1990';
SELECT * FROM t5;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
TRUNCATE TABLE t5;
INSERT INTO t5 SELECT * FROM t3 WHERE c2 BETWEEN '2000' AND '2155';
SELECT * FROM t5;
c1	c2	c3	c4
2000	2000	1998-12-29	1998-12-29 11:30:45
2000	2010	1998-12-17	1998-12-17 11:30:45
2000	2020	1998-12-17	1998-12-17 11:30:45
2001	2001	1998-12-28	1998-12-28 11:30:45
2009	2009	2009-01-29	2009-01-29 00:00:00
2020	2020	1998-12-16	1998-12-16 11:30:45
2020	2040	1998-12-10	1998-12-10 11:30:45
2040	2040	1998-12-15	1998-12-15 11:30:45
2060	2060	1998-12-14	1998-12-14 11:30:45
2099	2099	1998-12-27	1998-12-27 11:30:45
2100	2100	1998-12-26	1998-12-26 11:30:45
2155	2155	1998-12-26	1998-12-26 11:30:45
TRUNCATE TABLE t5;
DROP TABLE t5;
INSERT INTO t1 SET c1='00';
INSERT INTO t2 SET c1=69;
Warnings:
Warning	1364	Field 'c2' doesn't have a default value
INSERT INTO t3 SET c1=70;
INSERT INTO t4 SET c2=99;
Warnings:
Warning	1364	Field 'c1' doesn't have a default value
SELECT * FROM t1 WHERE c1=0;
c1	c2	c3	c4
0000	0000	2008-01-04	2008-01-05 00:00:00
SELECT * FROM t2 WHERE c1=69;
c1	c2	c3	c4
2069	0000	NULL	NULL
2069	1970	1998-12-30	1998-12-30 11:30:45
2069	2069	1998-12-13	1998-12-13 11:30:45
SELECT * FROM t3 WHERE c1=70;
c1	c2	c3	c4
1970	1970	1998-12-12	1998-12-12 11:30:45
1970	NULL	NULL	NULL
SELECT * FROM t4 WHERE c2=99;
c1	c2	c3	c4
0000	1999	NULL	NULL
1999	1999	1998-12-02	1998-12-02 11:30:45
1999	1999	1998-12-10	1998-12-10 11:30:45
1999	1999	1998-12-23	1998-12-23 11:30:45
1999	1999	1998-12-30	1998-12-30 11:30:45
DROP TABLE t1,t2,t3,t4;
SET sql_mode=default;
