include/rpl/init_source_replica.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection master]
CREATE TABLE trans_table1 (i INT) ENGINE=INNODB;
INSERT INTO trans_table1 VALUES (1);
CREATE TABLE trans_table2 (i INT) ENGINE=INNODB;
INSERT INTO trans_table2 VALUES (1);
CREATE TABLE non_trans_table1 (i INT) ENGINE=MYISAM;
INSERT INTO non_trans_table1 VALUES (1);
CREATE TABLE non_trans_table2 (i INT) ENGINE=MYISAM;
INSERT INTO non_trans_table2 VALUES (1);
CREATE FUNCTION func1 () RETURNS varchar(30) CHARSET utf8mb3
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
RETURN 0;
END;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY.
#
SELECT func1();
func1()
0
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	COMMIT
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with commit.
#
START TRANSACTION;
SELECT func1();
func1()
0
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	COMMIT
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with rollback.
#
START TRANSACTION;
SELECT func1();
func1()
0
ROLLBACK;
Warnings:
Warning	1751	The creation of some temporary tables could not be rolled back.
Warning	1752	Some temporary tables were dropped, but these operations could not be rolled back.
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	ROLLBACK
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN INSERT/UPDATE/DELETE QUERY ON
# transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (func1());
UPDATE trans_table2 SET i=func1();
DELETE FROM trans_table2 WHERE i=func1();
INSERT INTO non_trans_table2 VALUES (func1());
UPDATE non_trans_table2 SET i=func1();
DELETE FROM non_trans_table2 WHERE i=func1();
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (func1())
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=func1()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2 WHERE i=func1()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (func1())
master-bin.000001	#	Query	#	#	COMMIT
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=func1()
master-bin.000001	#	Query	#	#	COMMIT
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2 WHERE i=func1()
master-bin.000001	#	Query	#	#	COMMIT
DROP FUNCTION func1;
CREATE TRIGGER trigger1
BEFORE INSERT ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (10)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (10)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=12
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=12
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (10)
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (10)
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=12
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=12
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE FUNCTION func1 () RETURNS varchar(30) CHARSET utf8mb3
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
RETURN 0;
END;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY.
#
SELECT func1();
func1()
0
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	COMMIT
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with commit.
#
START TRANSACTION;
SELECT func1();
func1()
0
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	COMMIT
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with rollback.
#
START TRANSACTION;
SELECT func1();
func1()
0
ROLLBACK;
Warnings:
Warning	1751	The creation of some temporary tables could not be rolled back.
Warning	1752	Some temporary tables were dropped, but these operations could not be rolled back.
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	ROLLBACK
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN INSERT/UPDATE/DELETE QUERY ON
# transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (func1());
UPDATE trans_table2 SET i=func1();
DELETE FROM trans_table2 WHERE i=func1();
INSERT INTO non_trans_table2 VALUES (func1());
UPDATE non_trans_table2 SET i=func1();
DELETE FROM non_trans_table2 WHERE i=func1();
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (func1())
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=func1()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2 WHERE i=func1()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (func1())
master-bin.000001	#	Query	#	#	COMMIT
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=func1()
master-bin.000001	#	Query	#	#	COMMIT
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2 WHERE i=func1()
master-bin.000001	#	Query	#	#	COMMIT
DROP FUNCTION func1;
CREATE TRIGGER trigger1
BEFORE INSERT ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (10)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (10)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=12
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=12
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (10)
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (10)
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=12
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=12
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE FUNCTION func1 () RETURNS varchar(30) CHARSET utf8mb3
BEGIN
DROP TEMPORARY TABLE IF EXISTS tt1;
RETURN 0;
END;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY.
#
SELECT func1();
func1()
0
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	COMMIT
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with commit.
#
START TRANSACTION;
SELECT func1();
func1()
0
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	COMMIT
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with rollback.
#
START TRANSACTION;
SELECT func1();
func1()
0
ROLLBACK;
include/rpl/deprecated/show_binlog_events.inc
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN INSERT/UPDATE/DELETE QUERY ON
# transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (func1());
UPDATE trans_table2 SET i=func1();
DELETE FROM trans_table2 WHERE i=func1();
INSERT INTO non_trans_table2 VALUES (func1());
UPDATE non_trans_table2 SET i=func1();
DELETE FROM non_trans_table2 WHERE i=func1();
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (func1())
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=func1()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2 WHERE i=func1()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (func1())
master-bin.000001	#	Query	#	#	COMMIT
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=func1()
master-bin.000001	#	Query	#	#	COMMIT
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2 WHERE i=func1()
master-bin.000001	#	Query	#	#	COMMIT
DROP FUNCTION func1;
CREATE TRIGGER trigger1
BEFORE INSERT ON trans_table2 FOR EACH ROW
BEGIN
DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (10)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON trans_table2 FOR EACH ROW
BEGIN
DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (10)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON trans_table2 FOR EACH ROW
BEGIN
DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=12
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON trans_table2 FOR EACH ROW
BEGIN
DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=12
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON trans_table2 FOR EACH ROW
BEGIN
DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON trans_table2 FOR EACH ROW
BEGIN
DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (10)
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (10)
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=12
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=12
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE FUNCTION func1 () RETURNS varchar(30) CHARSET utf8mb3
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; CREATE TEMPORARY TABLE tt2(i INT) ENGINE=Innodb; DROP TEMPORARY TABLE IF EXISTS tt1; DROP TEMPORARY TABLE IF EXISTS tt2;
RETURN 0;
END;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY.
#
SELECT func1();
func1()
0
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	COMMIT
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with commit.
#
START TRANSACTION;
SELECT func1();
func1()
0
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	COMMIT
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with rollback.
#
START TRANSACTION;
SELECT func1();
func1()
0
ROLLBACK;
Warnings:
Warning	1751	The creation of some temporary tables could not be rolled back.
Warning	1752	Some temporary tables were dropped, but these operations could not be rolled back.
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	ROLLBACK
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN INSERT/UPDATE/DELETE QUERY ON
# transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (func1());
UPDATE trans_table2 SET i=func1();
DELETE FROM trans_table2 WHERE i=func1();
INSERT INTO non_trans_table2 VALUES (func1());
UPDATE non_trans_table2 SET i=func1();
DELETE FROM non_trans_table2 WHERE i=func1();
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (func1())
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=func1()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2 WHERE i=func1()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (func1())
master-bin.000001	#	Query	#	#	COMMIT
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=func1()
master-bin.000001	#	Query	#	#	COMMIT
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2 WHERE i=func1()
master-bin.000001	#	Query	#	#	COMMIT
DROP FUNCTION func1;
CREATE TRIGGER trigger1
BEFORE INSERT ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; CREATE TEMPORARY TABLE tt2(i INT) ENGINE=Innodb; DROP TEMPORARY TABLE IF EXISTS tt1; DROP TEMPORARY TABLE IF EXISTS tt2;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (10)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; CREATE TEMPORARY TABLE tt2(i INT) ENGINE=Innodb; DROP TEMPORARY TABLE IF EXISTS tt1; DROP TEMPORARY TABLE IF EXISTS tt2;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (10)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; CREATE TEMPORARY TABLE tt2(i INT) ENGINE=Innodb; DROP TEMPORARY TABLE IF EXISTS tt1; DROP TEMPORARY TABLE IF EXISTS tt2;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=12
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; CREATE TEMPORARY TABLE tt2(i INT) ENGINE=Innodb; DROP TEMPORARY TABLE IF EXISTS tt1; DROP TEMPORARY TABLE IF EXISTS tt2;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=12
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; CREATE TEMPORARY TABLE tt2(i INT) ENGINE=Innodb; DROP TEMPORARY TABLE IF EXISTS tt1; DROP TEMPORARY TABLE IF EXISTS tt2;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; CREATE TEMPORARY TABLE tt2(i INT) ENGINE=Innodb; DROP TEMPORARY TABLE IF EXISTS tt1; DROP TEMPORARY TABLE IF EXISTS tt2;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; CREATE TEMPORARY TABLE tt2(i INT) ENGINE=Innodb; DROP TEMPORARY TABLE IF EXISTS tt1; DROP TEMPORARY TABLE IF EXISTS tt2;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (10)
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; CREATE TEMPORARY TABLE tt2(i INT) ENGINE=Innodb; DROP TEMPORARY TABLE IF EXISTS tt1; DROP TEMPORARY TABLE IF EXISTS tt2;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (10)
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; CREATE TEMPORARY TABLE tt2(i INT) ENGINE=Innodb; DROP TEMPORARY TABLE IF EXISTS tt1; DROP TEMPORARY TABLE IF EXISTS tt2;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=12
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; CREATE TEMPORARY TABLE tt2(i INT) ENGINE=Innodb; DROP TEMPORARY TABLE IF EXISTS tt1; DROP TEMPORARY TABLE IF EXISTS tt2;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=12
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; CREATE TEMPORARY TABLE tt2(i INT) ENGINE=Innodb; DROP TEMPORARY TABLE IF EXISTS tt1; DROP TEMPORARY TABLE IF EXISTS tt2;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; CREATE TEMPORARY TABLE tt2(i INT) ENGINE=Innodb; DROP TEMPORARY TABLE IF EXISTS tt1; DROP TEMPORARY TABLE IF EXISTS tt2;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE FUNCTION func1 () RETURNS varchar(30) CHARSET utf8mb3
BEGIN
INSERT INTO trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
RETURN 0;
END;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY.
#
SELECT func1();
func1()
0
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with commit.
#
START TRANSACTION;
SELECT func1();
func1()
0
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with rollback.
#
START TRANSACTION;
SELECT func1();
func1()
0
ROLLBACK;
Warnings:
Warning	1751	The creation of some temporary tables could not be rolled back.
Warning	1752	Some temporary tables were dropped, but these operations could not be rolled back.
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	ROLLBACK
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN INSERT/UPDATE/DELETE QUERY ON
# transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (func1());
UPDATE trans_table2 SET i=func1();
DELETE FROM trans_table2 WHERE i=func1();
INSERT INTO non_trans_table2 VALUES (func1());
UPDATE non_trans_table2 SET i=func1();
DELETE FROM non_trans_table2 WHERE i=func1();
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (func1())
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=func1()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2 WHERE i=func1()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP FUNCTION func1;
CREATE TRIGGER trigger1
BEFORE INSERT ON trans_table2 FOR EACH ROW
BEGIN
INSERT INTO trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (10)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON trans_table2 FOR EACH ROW
BEGIN
INSERT INTO trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (10)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON trans_table2 FOR EACH ROW
BEGIN
INSERT INTO trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=12
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON trans_table2 FOR EACH ROW
BEGIN
INSERT INTO trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=12
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON trans_table2 FOR EACH ROW
BEGIN
INSERT INTO trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON trans_table2 FOR EACH ROW
BEGIN
INSERT INTO trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
INSERT INTO trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
INSERT INTO trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
INSERT INTO trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
INSERT INTO trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
INSERT INTO trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
INSERT INTO trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE FUNCTION func1 () RETURNS varchar(30) CHARSET utf8mb3
BEGIN
INSERT INTO non_trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
RETURN 0;
END;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY.
#
SELECT func1();
func1()
0
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	COMMIT
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with commit.
#
START TRANSACTION;
SELECT func1();
func1()
0
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	COMMIT
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with rollback.
#
START TRANSACTION;
SELECT func1();
func1()
0
ROLLBACK;
Warnings:
Warning	1196	Some non-transactional changed tables couldn't be rolled back
Warning	1751	The creation of some temporary tables could not be rolled back.
Warning	1752	Some temporary tables were dropped, but these operations could not be rolled back.
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	ROLLBACK
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN INSERT/UPDATE/DELETE QUERY ON
# transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (func1());
UPDATE trans_table2 SET i=func1();
DELETE FROM trans_table2 WHERE i=func1();
INSERT INTO non_trans_table2 VALUES (func1());
UPDATE non_trans_table2 SET i=func1();
DELETE FROM non_trans_table2 WHERE i=func1();
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (func1())
master-bin.000001	#	Query	#	#	COMMIT
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=func1()
master-bin.000001	#	Query	#	#	COMMIT
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2 WHERE i=func1()
master-bin.000001	#	Query	#	#	COMMIT
DROP FUNCTION func1;
CREATE TRIGGER trigger1
BEFORE INSERT ON trans_table2 FOR EACH ROW
BEGIN
INSERT INTO non_trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON trans_table2 FOR EACH ROW
BEGIN
INSERT INTO non_trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON trans_table2 FOR EACH ROW
BEGIN
INSERT INTO non_trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON trans_table2 FOR EACH ROW
BEGIN
INSERT INTO non_trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON trans_table2 FOR EACH ROW
BEGIN
INSERT INTO non_trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON trans_table2 FOR EACH ROW
BEGIN
INSERT INTO non_trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
INSERT INTO non_trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (10)
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
INSERT INTO non_trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (10)
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
INSERT INTO non_trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=12
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
INSERT INTO non_trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=12
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
INSERT INTO non_trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
INSERT INTO non_trans_table1 VALUES (12); CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1;
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE FUNCTION func1 () RETURNS varchar(30) CHARSET utf8mb3
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO non_trans_table1 VALUES (12);
RETURN 0;
END;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY.
#
SELECT func1();
func1()
0
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	COMMIT
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with commit.
#
START TRANSACTION;
SELECT func1();
func1()
0
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	COMMIT
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with rollback.
#
START TRANSACTION;
SELECT func1();
func1()
0
ROLLBACK;
Warnings:
Warning	1196	Some non-transactional changed tables couldn't be rolled back
Warning	1751	The creation of some temporary tables could not be rolled back.
Warning	1752	Some temporary tables were dropped, but these operations could not be rolled back.
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	ROLLBACK
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN INSERT/UPDATE/DELETE QUERY ON
# transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (func1());
UPDATE trans_table2 SET i=func1();
DELETE FROM trans_table2 WHERE i=func1();
INSERT INTO non_trans_table2 VALUES (func1());
UPDATE non_trans_table2 SET i=func1();
DELETE FROM non_trans_table2 WHERE i=func1();
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (func1())
master-bin.000001	#	Query	#	#	COMMIT
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=func1()
master-bin.000001	#	Query	#	#	COMMIT
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2 WHERE i=func1()
master-bin.000001	#	Query	#	#	COMMIT
DROP FUNCTION func1;
CREATE TRIGGER trigger1
BEFORE INSERT ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO non_trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO non_trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO non_trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO non_trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO non_trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO non_trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO non_trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (10)
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO non_trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO non_trans_table2 VALUES (10)
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO non_trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=12
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO non_trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE non_trans_table2 SET i=12
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO non_trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=INNODB; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO non_trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM non_trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE FUNCTION func1 () RETURNS varchar(30) CHARSET utf8mb3
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO trans_table1 VALUES (12);
RETURN 0;
END;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY.
#
SELECT func1();
func1()
0
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with commit.
#
START TRANSACTION;
SELECT func1();
func1()
0
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN SELECT QUERY IN MULTI
# TRANSACTION STATEMENT with rollback.
#
START TRANSACTION;
SELECT func1();
func1()
0
ROLLBACK;
Warnings:
Warning	1751	The creation of some temporary tables could not be rolled back.
Warning	1752	Some temporary tables were dropped, but these operations could not be rolled back.
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`func1`()
master-bin.000001	#	Query	#	#	ROLLBACK
#
# Verify that CREATE/DROP TEMPORARY TABLE in a FUNCTION are not written
# into binlog when using the FUNCTION IN INSERT/UPDATE/DELETE QUERY ON
# transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (func1());
UPDATE trans_table2 SET i=func1();
DELETE FROM trans_table2 WHERE i=func1();
INSERT INTO non_trans_table2 VALUES (func1());
UPDATE non_trans_table2 SET i=func1();
DELETE FROM non_trans_table2 WHERE i=func1();
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (func1())
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=func1()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2 WHERE i=func1()
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP FUNCTION func1;
CREATE TRIGGER trigger1
BEFORE INSERT ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (10)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO trans_table2 VALUES (10)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=12
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; UPDATE trans_table2 SET i=12
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM trans_table2;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; DELETE FROM trans_table2
master-bin.000001	#	Query	#	#	COMMIT
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER INSERT ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# INSERT QUERY ON transactional/non-transactional table.
#
INSERT INTO non_trans_table2 VALUES (10);
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER UPDATE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# UPDATE QUERY ON transactional/non-transactional table.
#
UPDATE non_trans_table2 SET i=12;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
BEFORE DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
CREATE TRIGGER trigger1
AFTER DELETE ON non_trans_table2 FOR EACH ROW
BEGIN
CREATE TEMPORARY TABLE tt1(i INT) ENGINE=MyISAM; DROP TEMPORARY TABLE IF EXISTS tt1; INSERT INTO trans_table1 VALUES (12);
END;
#
# Verify that CREATE/DROP TEMPORARY TABLE in a TRIGGER are not written
# into binlog when the trigger is triggered before/after
# DELETE QUERY ON transactional/non-transactional table.
#
DELETE FROM non_trans_table2;
include/rpl/deprecated/show_binlog_events.inc
DROP TRIGGER trigger1;
include/rpl/sync_to_replica.inc
[connection master]
include/diff_tables.inc [master:trans_table1, slave:trans_table1]
include/diff_tables.inc [master:trans_table2, slave:trans_table2]
include/diff_tables.inc [master:non_trans_table1, slave:non_trans_table1]
include/diff_tables.inc [master:non_trans_table2, slave:non_trans_table2]
DROP TABLE trans_table1, trans_table2;
DROP TABLE non_trans_table1, non_trans_table2;
include/rpl/deinit.inc
