SELECT @@global.validate_password_check_user_name;
@@global.validate_password_check_user_name
1
# Should fail: not a session variable
SET @@session.validate_password_check_user_name= ON;
ERROR HY000: Variable 'validate_password_check_user_name' is a GLOBAL variable and should be set with SET GLOBAL
# Should fail: not a session variable
SET validate_password_check_user_name= ON;
ERROR HY000: Variable 'validate_password_check_user_name' is a GLOBAL variable and should be set with SET GLOBAL
SET @@global.validate_password_policy=LOW;
SET @@global.validate_password_mixed_case_count=0;
SET @@global.validate_password_number_count=0;
SET @@global.validate_password_special_char_count=0;
SET @@global.validate_password_length=0;
SET @@global.validate_password_check_user_name= ON;
# Must pass: password the same as the the user name, but run by root
CREATE USER "base_user"@localhost IDENTIFIED BY 'base_user';
GRANT ALL PRIVILEGES ON test.* TO "base_user"@localhost;
# Must pass: password the same as the user name, but run by root
SET PASSWORD FOR "base_user"@localhost = 'base_user';
# Must pass: password the same as the user name, but run by root
ALTER USER "base_user"@localhost IDENTIFIED BY 'base_user';
# Must fail: password is root
CREATE USER foo@localhost IDENTIFIED BY 'root';
ERROR HY000: Your password does not satisfy the current policy requirements
# Must return 0 : same as the user name
SELECT VALIDATE_PASSWORD_STRENGTH('root') = 0;
VALIDATE_PASSWORD_STRENGTH('root') = 0
1
# Must return 0 : same as the user name
SELECT VALIDATE_PASSWORD_STRENGTH('toor') = 0;
VALIDATE_PASSWORD_STRENGTH('toor') = 0
1
# Must return non-0: upper case in user name
SELECT VALIDATE_PASSWORD_STRENGTH('Root') <> 0;
VALIDATE_PASSWORD_STRENGTH('Root') <> 0
1
# Must return non-0: upper case in reverse user name
SELECT VALIDATE_PASSWORD_STRENGTH('Toor') <> 0;
VALIDATE_PASSWORD_STRENGTH('Toor') <> 0
1
# Must return non-0: different name
SELECT VALIDATE_PASSWORD_STRENGTH('fooHoHo%1') <> 0;
VALIDATE_PASSWORD_STRENGTH('fooHoHo%1') <> 0
1
# connect as base_user
# Should fail: password the same as the user name
SET PASSWORD='base_user';
ERROR HY000: Your password does not satisfy the current policy requirements
# Should pass: uppercase in U
SET PASSWORD='base_User';
# Should fail: password the same as the login user name
ALTER USER "base_user"@localhost IDENTIFIED BY 'base_user';
ERROR HY000: Your password does not satisfy the current policy requirements
# Must be 0: user name matches the password
SELECT VALIDATE_PASSWORD_STRENGTH('base_user') = 0;
VALIDATE_PASSWORD_STRENGTH('base_user') = 0
1
# Must be 0: reverse of user name matches the password
SELECT VALIDATE_PASSWORD_STRENGTH('resu_esab') = 0;
VALIDATE_PASSWORD_STRENGTH('resu_esab') = 0
1
# Must pass: empty password is ok
SET PASSWORD='';
# back to default connection
REVOKE ALL PRIVILEGES ON test.* FROM "base_user"@localhost;
DROP USER "base_user"@localhost;
# test effective user name
CREATE USER ""@localhost;
GRANT ALL PRIVILEGES ON test.* TO ""@localhost;
# connect as the login_user
SELECT USER(),CURRENT_USER();
USER()	CURRENT_USER()
login_user@localhost	@localhost
# Should return 0: login user id matches
SELECT VALIDATE_PASSWORD_STRENGTH('login_user') = 0;
VALIDATE_PASSWORD_STRENGTH('login_user') = 0
1
# Should return 0: reverse login user id matches
SELECT VALIDATE_PASSWORD_STRENGTH('resu_nigol') = 0;
VALIDATE_PASSWORD_STRENGTH('resu_nigol') = 0
1
# back to default connection
REVOKE ALL PRIVILEGES ON test.* FROM ""@localhost;
DROP USER ""@localhost;
SET @@global.validate_password_policy=default;
SET @@global.validate_password_length=default;
SET @@global.validate_password_mixed_case_count=default;
SET @@global.validate_password_number_count=default;
SET @@global.validate_password_special_char_count=default;
SET @@global.validate_password_check_user_name= default;
End of 5.7 tests
