// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: google/protobuf/unittest_retention.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace ProtobufUnittest {

  /// <summary>Holder for reflection information generated from google/protobuf/unittest_retention.proto</summary>
  public static partial class UnittestRetentionReflection {

    #region Descriptor
    /// <summary>File descriptor for google/protobuf/unittest_retention.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static UnittestRetentionReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cihnb29nbGUvcHJvdG9idWYvdW5pdHRlc3RfcmV0ZW50aW9uLnByb3RvEhFw",
            "cm90b2J1Zl91bml0dGVzdBogZ29vZ2xlL3Byb3RvYnVmL2Rlc2NyaXB0b3Iu",
            "cHJvdG8icAoOT3B0aW9uc01lc3NhZ2USEwoLcGxhaW5fZmllbGQYASABKAUS",
            "JAoXcnVudGltZV9yZXRlbnRpb25fZmllbGQYAiABKAVCA4gBARIjChZzb3Vy",
            "Y2VfcmV0ZW50aW9uX2ZpZWxkGAMgASgFQgOIAQIiFgoIRXh0ZW5kZWUqBAgB",
            "EAIqBAgCEAMi4wEKD1RvcExldmVsTWVzc2FnZRIVCgFmGAEgASgCQgqauu2E",
            "DwQIARACEgsKAWkYAiABKANIABobCg1OZXN0ZWRNZXNzYWdlOgqaxd6FDwQI",
            "ARACIiwKCk5lc3RlZEVudW0SEgoOTkVTVEVEX1VOS05PV04QABoK+p6qhA8E",
            "CAEQAioQCAoQZRoKopjfhQ8ECAEQAjIyCgFzEhsucHJvdG9idWZfdW5pdHRl",
            "c3QuRXh0ZW5kZWUYAiABKAlCCpq67YQPBAgBEAI6CprF3oUPBAgBEAJCDwoB",
            "bxIKitu3hA8ECAEQAio9CgxUb3BMZXZlbEVudW0SIQoRVE9QX0xFVkVMX1VO",
            "S05PV04QABoK0t2phA8ECAEQAhoK+p6qhA8ECAEQAjJ0CgdTZXJ2aWNlEl0K",
            "B0RvU3R1ZmYSIi5wcm90b2J1Zl91bml0dGVzdC5Ub3BMZXZlbE1lc3NhZ2Ua",
            "Ii5wcm90b2J1Zl91bml0dGVzdC5Ub3BMZXZlbE1lc3NhZ2UiCuKu+IMPBAgB",
            "EAIaCuqHi4QPBAgBEAI6NgoMcGxhaW5fb3B0aW9uEhwuZ29vZ2xlLnByb3Rv",
            "YnVmLkZpbGVPcHRpb25zGMa17PABIAEoBTpHChhydW50aW1lX3JldGVudGlv",
            "bl9vcHRpb24SHC5nb29nbGUucHJvdG9idWYuRmlsZU9wdGlvbnMYnJLp8AEg",
            "ASgFQgOIAQE6RgoXc291cmNlX3JldGVudGlvbl9vcHRpb24SHC5nb29nbGUu",
            "cHJvdG9idWYuRmlsZU9wdGlvbnMY1Kzf8AEgASgFQgOIAQI6WAoLZmlsZV9v",
            "cHRpb24SHC5nb29nbGUucHJvdG9idWYuRmlsZU9wdGlvbnMYgPLe8AEgASgL",
            "MiEucHJvdG9idWZfdW5pdHRlc3QuT3B0aW9uc01lc3NhZ2U6XQoQcmVwZWF0",
            "ZWRfb3B0aW9ucxIcLmdvb2dsZS5wcm90b2J1Zi5GaWxlT3B0aW9ucxiS/tvw",
            "ASADKAsyIS5wcm90b2J1Zl91bml0dGVzdC5PcHRpb25zTWVzc2FnZTptChZl",
            "eHRlbnNpb25fcmFuZ2Vfb3B0aW9uEiYuZ29vZ2xlLnByb3RvYnVmLkV4dGVu",
            "c2lvblJhbmdlT3B0aW9ucxiE89vwASABKAsyIS5wcm90b2J1Zl91bml0dGVz",
            "dC5PcHRpb25zTWVzc2FnZTpeCg5tZXNzYWdlX29wdGlvbhIfLmdvb2dsZS5w",
            "cm90b2J1Zi5NZXNzYWdlT3B0aW9ucxjT6NvwASABKAsyIS5wcm90b2J1Zl91",
            "bml0dGVzdC5PcHRpb25zTWVzc2FnZTpaCgxmaWVsZF9vcHRpb24SHS5nb29n",
            "bGUucHJvdG9idWYuRmllbGRPcHRpb25zGKPXzfABIAEoCzIhLnByb3RvYnVm",
            "X3VuaXR0ZXN0Lk9wdGlvbnNNZXNzYWdlOloKDG9uZW9mX29wdGlvbhIdLmdv",
            "b2dsZS5wcm90b2J1Zi5PbmVvZk9wdGlvbnMYsfvG8AEgASgLMiEucHJvdG9i",
            "dWZfdW5pdHRlc3QuT3B0aW9uc01lc3NhZ2U6WAoLZW51bV9vcHRpb24SHC5n",
            "b29nbGUucHJvdG9idWYuRW51bU9wdGlvbnMY76PF8AEgASgLMiEucHJvdG9i",
            "dWZfdW5pdHRlc3QuT3B0aW9uc01lc3NhZ2U6YwoRZW51bV9lbnRyeV9vcHRp",
            "b24SIS5nb29nbGUucHJvdG9idWYuRW51bVZhbHVlT3B0aW9ucxjam8XwASAB",
            "KAsyIS5wcm90b2J1Zl91bml0dGVzdC5PcHRpb25zTWVzc2FnZTpeCg5zZXJ2",
            "aWNlX29wdGlvbhIfLmdvb2dsZS5wcm90b2J1Zi5TZXJ2aWNlT3B0aW9ucxj9",
            "sMHwASABKAsyIS5wcm90b2J1Zl91bml0dGVzdC5PcHRpb25zTWVzc2FnZTpc",
            "Cg1tZXRob2Rfb3B0aW9uEh4uZ29vZ2xlLnByb3RvYnVmLk1ldGhvZE9wdGlv",
            "bnMY7IW/8AEgASgLMiEucHJvdG9idWZfdW5pdHRlc3QuT3B0aW9uc01lc3Nh",
            "Z2U6MgoBaRIbLnByb3RvYnVmX3VuaXR0ZXN0LkV4dGVuZGVlGAEgASgFQgqa",
            "uu2EDwQIARACQjOqAhBQcm90b2J1ZlVuaXR0ZXN0kvHfhQ8ECAEQAoKQ94UP",
            "BAgBEALgkcmGDwKwrOOGDwE="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Google.Protobuf.Reflection.DescriptorReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::ProtobufUnittest.TopLevelEnum), }, new pb::Extension[] { UnittestRetentionExtensions.PlainOption, UnittestRetentionExtensions.RuntimeRetentionOption, UnittestRetentionExtensions.SourceRetentionOption, UnittestRetentionExtensions.FileOption, UnittestRetentionExtensions.RepeatedOptions, UnittestRetentionExtensions.ExtensionRangeOption, UnittestRetentionExtensions.MessageOption, UnittestRetentionExtensions.FieldOption, UnittestRetentionExtensions.OneofOption, UnittestRetentionExtensions.EnumOption, UnittestRetentionExtensions.EnumEntryOption, UnittestRetentionExtensions.ServiceOption, UnittestRetentionExtensions.MethodOption, UnittestRetentionExtensions.I }, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufUnittest.OptionsMessage), global::ProtobufUnittest.OptionsMessage.Parser, new[]{ "PlainField", "RuntimeRetentionField", "SourceRetentionField" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufUnittest.Extendee), global::ProtobufUnittest.Extendee.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufUnittest.TopLevelMessage), global::ProtobufUnittest.TopLevelMessage.Parser, new[]{ "F", "I" }, new[]{ "O" }, new[]{ typeof(global::ProtobufUnittest.TopLevelMessage.Types.NestedEnum) }, new pb::Extension[] { global::ProtobufUnittest.TopLevelMessage.Extensions.S }, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufUnittest.TopLevelMessage.Types.NestedMessage), global::ProtobufUnittest.TopLevelMessage.Types.NestedMessage.Parser, null, null, null, null, null)})
          }));
    }
    #endregion

  }
  /// <summary>Holder for extension identifiers generated from the top level of google/protobuf/unittest_retention.proto</summary>
  public static partial class UnittestRetentionExtensions {
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.FileOptions, int> PlainOption =
      new pb::Extension<global::Google.Protobuf.Reflection.FileOptions, int>(505092806, pb::FieldCodec.ForInt32(4040742448, 0));
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.FileOptions, int> RuntimeRetentionOption =
      new pb::Extension<global::Google.Protobuf.Reflection.FileOptions, int>(505039132, pb::FieldCodec.ForInt32(4040313056, 0));
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.FileOptions, int> SourceRetentionOption =
      new pb::Extension<global::Google.Protobuf.Reflection.FileOptions, int>(504878676, pb::FieldCodec.ForInt32(4039029408, 0));
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.FileOptions, global::ProtobufUnittest.OptionsMessage> FileOption =
      new pb::Extension<global::Google.Protobuf.Reflection.FileOptions, global::ProtobufUnittest.OptionsMessage>(504871168, pb::FieldCodec.ForMessage(4038969346, global::ProtobufUnittest.OptionsMessage.Parser));
    public static readonly pb::RepeatedExtension<global::Google.Protobuf.Reflection.FileOptions, global::ProtobufUnittest.OptionsMessage> RepeatedOptions =
      new pb::RepeatedExtension<global::Google.Protobuf.Reflection.FileOptions, global::ProtobufUnittest.OptionsMessage>(504823570, pb::FieldCodec.ForMessage(4038588562, global::ProtobufUnittest.OptionsMessage.Parser));
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.ExtensionRangeOptions, global::ProtobufUnittest.OptionsMessage> ExtensionRangeOption =
      new pb::Extension<global::Google.Protobuf.Reflection.ExtensionRangeOptions, global::ProtobufUnittest.OptionsMessage>(504822148, pb::FieldCodec.ForMessage(4038577186, global::ProtobufUnittest.OptionsMessage.Parser));
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.MessageOptions, global::ProtobufUnittest.OptionsMessage> MessageOption =
      new pb::Extension<global::Google.Protobuf.Reflection.MessageOptions, global::ProtobufUnittest.OptionsMessage>(504820819, pb::FieldCodec.ForMessage(4038566554, global::ProtobufUnittest.OptionsMessage.Parser));
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.FieldOptions, global::ProtobufUnittest.OptionsMessage> FieldOption =
      new pb::Extension<global::Google.Protobuf.Reflection.FieldOptions, global::ProtobufUnittest.OptionsMessage>(504589219, pb::FieldCodec.ForMessage(4036713754, global::ProtobufUnittest.OptionsMessage.Parser));
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.OneofOptions, global::ProtobufUnittest.OptionsMessage> OneofOption =
      new pb::Extension<global::Google.Protobuf.Reflection.OneofOptions, global::ProtobufUnittest.OptionsMessage>(504479153, pb::FieldCodec.ForMessage(4035833226, global::ProtobufUnittest.OptionsMessage.Parser));
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.EnumOptions, global::ProtobufUnittest.OptionsMessage> EnumOption =
      new pb::Extension<global::Google.Protobuf.Reflection.EnumOptions, global::ProtobufUnittest.OptionsMessage>(504451567, pb::FieldCodec.ForMessage(4035612538, global::ProtobufUnittest.OptionsMessage.Parser));
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.EnumValueOptions, global::ProtobufUnittest.OptionsMessage> EnumEntryOption =
      new pb::Extension<global::Google.Protobuf.Reflection.EnumValueOptions, global::ProtobufUnittest.OptionsMessage>(504450522, pb::FieldCodec.ForMessage(4035604178, global::ProtobufUnittest.OptionsMessage.Parser));
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.ServiceOptions, global::ProtobufUnittest.OptionsMessage> ServiceOption =
      new pb::Extension<global::Google.Protobuf.Reflection.ServiceOptions, global::ProtobufUnittest.OptionsMessage>(504387709, pb::FieldCodec.ForMessage(4035101674, global::ProtobufUnittest.OptionsMessage.Parser));
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.MethodOptions, global::ProtobufUnittest.OptionsMessage> MethodOption =
      new pb::Extension<global::Google.Protobuf.Reflection.MethodOptions, global::ProtobufUnittest.OptionsMessage>(504349420, pb::FieldCodec.ForMessage(4034795362, global::ProtobufUnittest.OptionsMessage.Parser));
    public static readonly pb::Extension<global::ProtobufUnittest.Extendee, int> I =
      new pb::Extension<global::ProtobufUnittest.Extendee, int>(1, pb::FieldCodec.ForInt32(8, 0));
  }

  #region Enums
  public enum TopLevelEnum {
    [pbr::OriginalName("TOP_LEVEL_UNKNOWN")] TopLevelUnknown = 0,
  }

  #endregion

  #region Messages
  /// <summary>
  /// Retention attributes set on fields nested within a message
  /// </summary>
  public sealed partial class OptionsMessage : pb::IMessage<OptionsMessage>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<OptionsMessage> _parser = new pb::MessageParser<OptionsMessage>(() => new OptionsMessage());
    private pb::UnknownFieldSet _unknownFields;
    private int _hasBits0;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<OptionsMessage> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufUnittest.UnittestRetentionReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public OptionsMessage() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public OptionsMessage(OptionsMessage other) : this() {
      _hasBits0 = other._hasBits0;
      plainField_ = other.plainField_;
      runtimeRetentionField_ = other.runtimeRetentionField_;
      sourceRetentionField_ = other.sourceRetentionField_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public OptionsMessage Clone() {
      return new OptionsMessage(this);
    }

    /// <summary>Field number for the "plain_field" field.</summary>
    public const int PlainFieldFieldNumber = 1;
    private readonly static int PlainFieldDefaultValue = 0;

    private int plainField_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int PlainField {
      get { if ((_hasBits0 & 1) != 0) { return plainField_; } else { return PlainFieldDefaultValue; } }
      set {
        _hasBits0 |= 1;
        plainField_ = value;
      }
    }
    /// <summary>Gets whether the "plain_field" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasPlainField {
      get { return (_hasBits0 & 1) != 0; }
    }
    /// <summary>Clears the value of the "plain_field" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearPlainField() {
      _hasBits0 &= ~1;
    }

    /// <summary>Field number for the "runtime_retention_field" field.</summary>
    public const int RuntimeRetentionFieldFieldNumber = 2;
    private readonly static int RuntimeRetentionFieldDefaultValue = 0;

    private int runtimeRetentionField_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int RuntimeRetentionField {
      get { if ((_hasBits0 & 2) != 0) { return runtimeRetentionField_; } else { return RuntimeRetentionFieldDefaultValue; } }
      set {
        _hasBits0 |= 2;
        runtimeRetentionField_ = value;
      }
    }
    /// <summary>Gets whether the "runtime_retention_field" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRuntimeRetentionField {
      get { return (_hasBits0 & 2) != 0; }
    }
    /// <summary>Clears the value of the "runtime_retention_field" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRuntimeRetentionField() {
      _hasBits0 &= ~2;
    }

    /// <summary>Field number for the "source_retention_field" field.</summary>
    public const int SourceRetentionFieldFieldNumber = 3;
    private readonly static int SourceRetentionFieldDefaultValue = 0;

    private int sourceRetentionField_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int SourceRetentionField {
      get { if ((_hasBits0 & 4) != 0) { return sourceRetentionField_; } else { return SourceRetentionFieldDefaultValue; } }
      set {
        _hasBits0 |= 4;
        sourceRetentionField_ = value;
      }
    }
    /// <summary>Gets whether the "source_retention_field" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasSourceRetentionField {
      get { return (_hasBits0 & 4) != 0; }
    }
    /// <summary>Clears the value of the "source_retention_field" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearSourceRetentionField() {
      _hasBits0 &= ~4;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as OptionsMessage);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(OptionsMessage other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (PlainField != other.PlainField) return false;
      if (RuntimeRetentionField != other.RuntimeRetentionField) return false;
      if (SourceRetentionField != other.SourceRetentionField) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasPlainField) hash ^= PlainField.GetHashCode();
      if (HasRuntimeRetentionField) hash ^= RuntimeRetentionField.GetHashCode();
      if (HasSourceRetentionField) hash ^= SourceRetentionField.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasPlainField) {
        output.WriteRawTag(8);
        output.WriteInt32(PlainField);
      }
      if (HasRuntimeRetentionField) {
        output.WriteRawTag(16);
        output.WriteInt32(RuntimeRetentionField);
      }
      if (HasSourceRetentionField) {
        output.WriteRawTag(24);
        output.WriteInt32(SourceRetentionField);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasPlainField) {
        output.WriteRawTag(8);
        output.WriteInt32(PlainField);
      }
      if (HasRuntimeRetentionField) {
        output.WriteRawTag(16);
        output.WriteInt32(RuntimeRetentionField);
      }
      if (HasSourceRetentionField) {
        output.WriteRawTag(24);
        output.WriteInt32(SourceRetentionField);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasPlainField) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(PlainField);
      }
      if (HasRuntimeRetentionField) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RuntimeRetentionField);
      }
      if (HasSourceRetentionField) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SourceRetentionField);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(OptionsMessage other) {
      if (other == null) {
        return;
      }
      if (other.HasPlainField) {
        PlainField = other.PlainField;
      }
      if (other.HasRuntimeRetentionField) {
        RuntimeRetentionField = other.RuntimeRetentionField;
      }
      if (other.HasSourceRetentionField) {
        SourceRetentionField = other.SourceRetentionField;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            PlainField = input.ReadInt32();
            break;
          }
          case 16: {
            RuntimeRetentionField = input.ReadInt32();
            break;
          }
          case 24: {
            SourceRetentionField = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            PlainField = input.ReadInt32();
            break;
          }
          case 16: {
            RuntimeRetentionField = input.ReadInt32();
            break;
          }
          case 24: {
            SourceRetentionField = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class Extendee : pb::IExtendableMessage<Extendee>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Extendee> _parser = new pb::MessageParser<Extendee>(() => new Extendee());
    private pb::UnknownFieldSet _unknownFields;
    private pb::ExtensionSet<Extendee> _extensions;
    private pb::ExtensionSet<Extendee> _Extensions { get { return _extensions; } }
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Extendee> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufUnittest.UnittestRetentionReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Extendee() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Extendee(Extendee other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
      _extensions = pb::ExtensionSet.Clone(other._extensions);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Extendee Clone() {
      return new Extendee(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Extendee);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Extendee other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!Equals(_extensions, other._extensions)) {
        return false;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_extensions != null) {
        hash ^= _extensions.GetHashCode();
      }
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_extensions != null) {
        _extensions.WriteTo(output);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_extensions != null) {
        _extensions.WriteTo(ref output);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_extensions != null) {
        size += _extensions.CalculateSize();
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Extendee other) {
      if (other == null) {
        return;
      }
      pb::ExtensionSet.MergeFrom(ref _extensions, other._extensions);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            if (!pb::ExtensionSet.TryMergeFieldFrom(ref _extensions, input)) {
              _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            }
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            if (!pb::ExtensionSet.TryMergeFieldFrom(ref _extensions, ref input)) {
              _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            }
            break;
        }
      }
    }
    #endif

    public TValue GetExtension<TValue>(pb::Extension<Extendee, TValue> extension) {
      return pb::ExtensionSet.Get(ref _extensions, extension);
    }
    public pbc::RepeatedField<TValue> GetExtension<TValue>(pb::RepeatedExtension<Extendee, TValue> extension) {
      return pb::ExtensionSet.Get(ref _extensions, extension);
    }
    public pbc::RepeatedField<TValue> GetOrInitializeExtension<TValue>(pb::RepeatedExtension<Extendee, TValue> extension) {
      return pb::ExtensionSet.GetOrInitialize(ref _extensions, extension);
    }
    public void SetExtension<TValue>(pb::Extension<Extendee, TValue> extension, TValue value) {
      pb::ExtensionSet.Set(ref _extensions, extension, value);
    }
    public bool HasExtension<TValue>(pb::Extension<Extendee, TValue> extension) {
      return pb::ExtensionSet.Has(ref _extensions, extension);
    }
    public void ClearExtension<TValue>(pb::Extension<Extendee, TValue> extension) {
      pb::ExtensionSet.Clear(ref _extensions, extension);
    }
    public void ClearExtension<TValue>(pb::RepeatedExtension<Extendee, TValue> extension) {
      pb::ExtensionSet.Clear(ref _extensions, extension);
    }

  }

  public sealed partial class TopLevelMessage : pb::IExtendableMessage<TopLevelMessage>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TopLevelMessage> _parser = new pb::MessageParser<TopLevelMessage>(() => new TopLevelMessage());
    private pb::UnknownFieldSet _unknownFields;
    private pb::ExtensionSet<TopLevelMessage> _extensions;
    private pb::ExtensionSet<TopLevelMessage> _Extensions { get { return _extensions; } }
    private int _hasBits0;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TopLevelMessage> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufUnittest.UnittestRetentionReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TopLevelMessage() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TopLevelMessage(TopLevelMessage other) : this() {
      _hasBits0 = other._hasBits0;
      f_ = other.f_;
      switch (other.OCase) {
        case OOneofCase.I:
          I = other.I;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
      _extensions = pb::ExtensionSet.Clone(other._extensions);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TopLevelMessage Clone() {
      return new TopLevelMessage(this);
    }

    /// <summary>Field number for the "f" field.</summary>
    public const int FFieldNumber = 1;
    private readonly static float FDefaultValue = 0F;

    private float f_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float F {
      get { if ((_hasBits0 & 1) != 0) { return f_; } else { return FDefaultValue; } }
      set {
        _hasBits0 |= 1;
        f_ = value;
      }
    }
    /// <summary>Gets whether the "f" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasF {
      get { return (_hasBits0 & 1) != 0; }
    }
    /// <summary>Clears the value of the "f" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearF() {
      _hasBits0 &= ~1;
    }

    /// <summary>Field number for the "i" field.</summary>
    public const int IFieldNumber = 2;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long I {
      get { return HasI ? (long) o_ : 0L; }
      set {
        o_ = value;
        oCase_ = OOneofCase.I;
      }
    }
    /// <summary>Gets whether the "i" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasI {
      get { return oCase_ == OOneofCase.I; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "i" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearI() {
      if (HasI) {
        ClearO();
      }
    }

    private object o_;
    /// <summary>Enum of possible cases for the "o" oneof.</summary>
    public enum OOneofCase {
      None = 0,
      I = 2,
    }
    private OOneofCase oCase_ = OOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public OOneofCase OCase {
      get { return oCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearO() {
      oCase_ = OOneofCase.None;
      o_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TopLevelMessage);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TopLevelMessage other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(F, other.F)) return false;
      if (I != other.I) return false;
      if (OCase != other.OCase) return false;
      if (!Equals(_extensions, other._extensions)) {
        return false;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasF) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(F);
      if (HasI) hash ^= I.GetHashCode();
      hash ^= (int) oCase_;
      if (_extensions != null) {
        hash ^= _extensions.GetHashCode();
      }
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasF) {
        output.WriteRawTag(13);
        output.WriteFloat(F);
      }
      if (HasI) {
        output.WriteRawTag(16);
        output.WriteInt64(I);
      }
      if (_extensions != null) {
        _extensions.WriteTo(output);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasF) {
        output.WriteRawTag(13);
        output.WriteFloat(F);
      }
      if (HasI) {
        output.WriteRawTag(16);
        output.WriteInt64(I);
      }
      if (_extensions != null) {
        _extensions.WriteTo(ref output);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasF) {
        size += 1 + 4;
      }
      if (HasI) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(I);
      }
      if (_extensions != null) {
        size += _extensions.CalculateSize();
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TopLevelMessage other) {
      if (other == null) {
        return;
      }
      if (other.HasF) {
        F = other.F;
      }
      switch (other.OCase) {
        case OOneofCase.I:
          I = other.I;
          break;
      }

      pb::ExtensionSet.MergeFrom(ref _extensions, other._extensions);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            if (!pb::ExtensionSet.TryMergeFieldFrom(ref _extensions, input)) {
              _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            }
            break;
          case 13: {
            F = input.ReadFloat();
            break;
          }
          case 16: {
            I = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            if (!pb::ExtensionSet.TryMergeFieldFrom(ref _extensions, ref input)) {
              _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            }
            break;
          case 13: {
            F = input.ReadFloat();
            break;
          }
          case 16: {
            I = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

    public TValue GetExtension<TValue>(pb::Extension<TopLevelMessage, TValue> extension) {
      return pb::ExtensionSet.Get(ref _extensions, extension);
    }
    public pbc::RepeatedField<TValue> GetExtension<TValue>(pb::RepeatedExtension<TopLevelMessage, TValue> extension) {
      return pb::ExtensionSet.Get(ref _extensions, extension);
    }
    public pbc::RepeatedField<TValue> GetOrInitializeExtension<TValue>(pb::RepeatedExtension<TopLevelMessage, TValue> extension) {
      return pb::ExtensionSet.GetOrInitialize(ref _extensions, extension);
    }
    public void SetExtension<TValue>(pb::Extension<TopLevelMessage, TValue> extension, TValue value) {
      pb::ExtensionSet.Set(ref _extensions, extension, value);
    }
    public bool HasExtension<TValue>(pb::Extension<TopLevelMessage, TValue> extension) {
      return pb::ExtensionSet.Has(ref _extensions, extension);
    }
    public void ClearExtension<TValue>(pb::Extension<TopLevelMessage, TValue> extension) {
      pb::ExtensionSet.Clear(ref _extensions, extension);
    }
    public void ClearExtension<TValue>(pb::RepeatedExtension<TopLevelMessage, TValue> extension) {
      pb::ExtensionSet.Clear(ref _extensions, extension);
    }

    #region Nested types
    /// <summary>Container for nested types declared in the TopLevelMessage message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      public enum NestedEnum {
        [pbr::OriginalName("NESTED_UNKNOWN")] NestedUnknown = 0,
      }

      public sealed partial class NestedMessage : pb::IMessage<NestedMessage>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<NestedMessage> _parser = new pb::MessageParser<NestedMessage>(() => new NestedMessage());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<NestedMessage> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::ProtobufUnittest.TopLevelMessage.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public NestedMessage() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public NestedMessage(NestedMessage other) : this() {
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public NestedMessage Clone() {
          return new NestedMessage(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as NestedMessage);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(NestedMessage other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(NestedMessage other) {
          if (other == null) {
            return;
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
            }
          }
        }
        #endif

      }

    }
    #endregion

    #region Extensions
    /// <summary>Container for extensions for other messages declared in the TopLevelMessage message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Extensions {
      public static readonly pb::Extension<global::ProtobufUnittest.Extendee, string> S =
        new pb::Extension<global::ProtobufUnittest.Extendee, string>(2, pb::FieldCodec.ForString(18, ""));
    }
    #endregion

  }

  #endregion

}

#endregion Designer generated code
