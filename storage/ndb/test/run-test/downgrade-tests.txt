# Copyright (c) 2018, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
#
cmd: testDowngrade
args: -n ShowVersions T1
max-time: 600
type: bench  # Always record result

cmd: testDowngrade
args: -n Downgrade_NR1 T1
max-time: 600

cmd: testDowngrade
args: -n Downgrade_NR2 T1
max-time: 600

cmd: testDowngrade
args: -n Downgrade_NR3 T1
max-time: 600

cmd: testDowngrade
args: -n Downgrade_FS T1
max-time: 600

cmd: testDowngrade
args: -n Downgrade_Traffic T1
max-time: 1200

cmd: testDowngrade
args: -n Downgrade_Traffic_FS T1
max-time: 1200

cmd: testDowngrade
args: -n Downgrade_Bug14702377 T1
max-time: 600

cmd: testDowngrade
args: -n Downgrade_Api_Only T1
max-time: 1200

cmd: testDowngrade
args: -n Downgrade_Api_Before_NR1 T2
max-time: 1200

cmd: testDowngrade
args: -n Downgrade_Api_NDBD_MGMD T1
max-time: 1200

cmd: testDowngrade
args: -n Downgrade_Mixed_MGMD_API_NDBD T2
max-time: 1200

cmd: testDowngrade
args: -n Downgrade_SR_ManyTablesMaxFrag T1
max-time: 6000

#
# Downgrade tests tagged with "WithMGMDStart"
#
cmd: testDowngrade
args: -n Downgrade_NR1_WithMGMDStart T1
max-time: 600

cmd: testDowngrade
args: -n Downgrade_Api_Before_NR1_WithMGMDStart T2
max-time: 1200

#
# Downgrade tests tagged with "WithMGMDInitialStart"
#
cmd: testDowngrade
args: -n Downgrade_NR2_WithMGMDInitialStart T1
max-time: 600

cmd: testDowngrade
args: -n Downgrade_NR3_WithMGMDInitialStart T1
max-time: 600

cmd: testDowngrade
args: -n Downgrade_FS_WithMGMDInitialStart T1
max-time: 600

cmd: testDowngrade
args: -n Downgrade_Traffic_WithMGMDInitialStart T1
max-time: 1200

cmd: testDowngrade
args: -n Downgrade_Traffic_FS_WithMGMDInitialStart T1
max-time: 1200

cmd: testDowngrade
args: -n Downgrade_Bug14702377_WithMGMDInitialStart T1
max-time: 600

cmd: testDowngrade
args: -n Downgrade_Api_Only_WithMGMDInitialStart T1
max-time: 1200

cmd: testDowngrade
args: -n Downgrade_Api_NDBD_MGMD_WithMGMDInitialStart T1
max-time: 1200

cmd: testDowngrade
args: -n Downgrade_SR_ManyTablesMaxFrag_WithMGMDInitialStart T1
max-time: 6000
