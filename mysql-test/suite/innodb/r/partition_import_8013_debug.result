# Test to create schema for Upgrade in old version
# Extract data files to be imported
# Starting server with keyring plugin
# ----------------------------------------------------------------------
# Setup
# Creating custom global manifest file for MySQL server
# Creating custom global configuration file for keyring component: component_keyring_file
# Re-starting mysql server with manifest file
# ----------------------------------------------------------------------
# restart: --innodb_directories=/tmp/mysql_wl13352_data/ --plugin-dir=PLUGIN_DIR

# A. Create tables and fill data
Creating partitioned tables
---------------------------
CREATE TABLE table_part_1(
id int primary key, value int, name varchar(32), data BLOB,
key k1(value)) partition by hash (id) partitions 5;

CREATE TABLE table_part_2(
id int primary key, value int, name varchar(32), data BLOB,
key k1(value)) DATA DIRECTORY = '/tmp/mysql_wl13352_data/'
partition by hash (id) partitions 5;

CREATE TABLE table_part_sub_1(
id int, value int, name varchar(32), data BLOB, primary key pk(id, value),
key k1(value)) partition by range (value) subpartition by hash (id)
subpartitions 2 ( partition Part_1 values less than (101),
partition pArt_2 values less than (401),
partition part_3 values less than (701),
partition PART_4 values less than MAXVALUE);

CREATE TABLE table_part_sub_2(
id int, value int, name varchar(32), data BLOB, primary key pk(id, value),
key k1(value)) partition by range (value) subpartition by hash (id)
( partition Part_1 values less than (101)(
subpartition Sub_1,
subpartition sUb_2),
partition pArt_2 values less than (501)(
subpartition suB_3 DATA DIRECTORY = '/tmp/mysql_wl13352_data/',
subpartition SUB_4),
partition PART_3 values less than MAXVALUE(
subpartition SUB_5,
subpartition sub_6));

CREATE TABLE `table_part_\_special_/_sub_?_3`(
id int, value int, name varchar(32), data BLOB, primary key pk(id, value),
key k1(value)) partition by range (value) subpartition by hash (id)
( partition `Part_?_1` values less than (201)(
subpartition `Sub_?_1`,
subpartition `sUb_/_2`),
partition `pArt_\_2` values less than (501)(
subpartition `suB_\_3`,
subpartition `SUB_?\_4`),
partition `PART_/_3` values less than MAXVALUE(
subpartition `SUB_?/_5`,
subpartition `sub_\/?_6`));

Inserting 10 rows to table_part_1
---------------------------------------
insert into table_part_1 VALUES(0, 55, "Row - 0", REPEAT('Large Column Data - 0' , 256));
insert into table_part_1 VALUES(1, 155, "Row - 1", REPEAT('Large Column Data - 1' , 256));
insert into table_part_1 VALUES(2, 255, "Row - 2", REPEAT('Large Column Data - 2' , 256));
insert into table_part_1 VALUES(3, 355, "Row - 3", REPEAT('Large Column Data - 3' , 256));
insert into table_part_1 VALUES(4, 455, "Row - 4", REPEAT('Large Column Data - 4' , 256));
insert into table_part_1 VALUES(5, 555, "Row - 5", REPEAT('Large Column Data - 5' , 256));
insert into table_part_1 VALUES(6, 655, "Row - 6", REPEAT('Large Column Data - 6' , 256));
insert into table_part_1 VALUES(7, 755, "Row - 7", REPEAT('Large Column Data - 7' , 256));
insert into table_part_1 VALUES(8, 855, "Row - 8", REPEAT('Large Column Data - 8' , 256));
insert into table_part_1 VALUES(9, 955, "Row - 9", REPEAT('Large Column Data - 9' , 256));

Inserting 10 rows to table_part_sub_1
---------------------------------------
insert into table_part_sub_1 VALUES(0, 55, "Row - 0", REPEAT('Large Column Data - 0' , 256));
insert into table_part_sub_1 VALUES(1, 155, "Row - 1", REPEAT('Large Column Data - 1' , 256));
insert into table_part_sub_1 VALUES(2, 255, "Row - 2", REPEAT('Large Column Data - 2' , 256));
insert into table_part_sub_1 VALUES(3, 355, "Row - 3", REPEAT('Large Column Data - 3' , 256));
insert into table_part_sub_1 VALUES(4, 455, "Row - 4", REPEAT('Large Column Data - 4' , 256));
insert into table_part_sub_1 VALUES(5, 555, "Row - 5", REPEAT('Large Column Data - 5' , 256));
insert into table_part_sub_1 VALUES(6, 655, "Row - 6", REPEAT('Large Column Data - 6' , 256));
insert into table_part_sub_1 VALUES(7, 755, "Row - 7", REPEAT('Large Column Data - 7' , 256));
insert into table_part_sub_1 VALUES(8, 855, "Row - 8", REPEAT('Large Column Data - 8' , 256));
insert into table_part_sub_1 VALUES(9, 955, "Row - 9", REPEAT('Large Column Data - 9' , 256));

Inserting 10 rows to table_part_sub_2
---------------------------------------
insert into table_part_sub_2 VALUES(0, 55, "Row - 0", REPEAT('Large Column Data - 0' , 256));
insert into table_part_sub_2 VALUES(1, 155, "Row - 1", REPEAT('Large Column Data - 1' , 256));
insert into table_part_sub_2 VALUES(2, 255, "Row - 2", REPEAT('Large Column Data - 2' , 256));
insert into table_part_sub_2 VALUES(3, 355, "Row - 3", REPEAT('Large Column Data - 3' , 256));
insert into table_part_sub_2 VALUES(4, 455, "Row - 4", REPEAT('Large Column Data - 4' , 256));
insert into table_part_sub_2 VALUES(5, 555, "Row - 5", REPEAT('Large Column Data - 5' , 256));
insert into table_part_sub_2 VALUES(6, 655, "Row - 6", REPEAT('Large Column Data - 6' , 256));
insert into table_part_sub_2 VALUES(7, 755, "Row - 7", REPEAT('Large Column Data - 7' , 256));
insert into table_part_sub_2 VALUES(8, 855, "Row - 8", REPEAT('Large Column Data - 8' , 256));
insert into table_part_sub_2 VALUES(9, 955, "Row - 9", REPEAT('Large Column Data - 9' , 256));

Inserting 10 rows to `table_part_\_special_/_sub_?_3`
---------------------------------------
insert into `table_part_\_special_/_sub_?_3` VALUES(0, 55, "Row - 0", REPEAT('Large Column Data - 0' , 256));
insert into `table_part_\_special_/_sub_?_3` VALUES(1, 155, "Row - 1", REPEAT('Large Column Data - 1' , 256));
insert into `table_part_\_special_/_sub_?_3` VALUES(2, 255, "Row - 2", REPEAT('Large Column Data - 2' , 256));
insert into `table_part_\_special_/_sub_?_3` VALUES(3, 355, "Row - 3", REPEAT('Large Column Data - 3' , 256));
insert into `table_part_\_special_/_sub_?_3` VALUES(4, 455, "Row - 4", REPEAT('Large Column Data - 4' , 256));
insert into `table_part_\_special_/_sub_?_3` VALUES(5, 555, "Row - 5", REPEAT('Large Column Data - 5' , 256));
insert into `table_part_\_special_/_sub_?_3` VALUES(6, 655, "Row - 6", REPEAT('Large Column Data - 6' , 256));
insert into `table_part_\_special_/_sub_?_3` VALUES(7, 755, "Row - 7", REPEAT('Large Column Data - 7' , 256));
insert into `table_part_\_special_/_sub_?_3` VALUES(8, 855, "Row - 8", REPEAT('Large Column Data - 8' , 256));
insert into `table_part_\_special_/_sub_?_3` VALUES(9, 955, "Row - 9", REPEAT('Large Column Data - 9' , 256));

Inserting 10 rows to table_part_2
---------------------------------------
insert into table_part_2 VALUES(0, 55, "Row - 0", REPEAT('Large Column Data - 0' , 256));
insert into table_part_2 VALUES(1, 155, "Row - 1", REPEAT('Large Column Data - 1' , 256));
insert into table_part_2 VALUES(2, 255, "Row - 2", REPEAT('Large Column Data - 2' , 256));
insert into table_part_2 VALUES(3, 355, "Row - 3", REPEAT('Large Column Data - 3' , 256));
insert into table_part_2 VALUES(4, 455, "Row - 4", REPEAT('Large Column Data - 4' , 256));
insert into table_part_2 VALUES(5, 555, "Row - 5", REPEAT('Large Column Data - 5' , 256));
insert into table_part_2 VALUES(6, 655, "Row - 6", REPEAT('Large Column Data - 6' , 256));
insert into table_part_2 VALUES(7, 755, "Row - 7", REPEAT('Large Column Data - 7' , 256));
insert into table_part_2 VALUES(8, 855, "Row - 8", REPEAT('Large Column Data - 8' , 256));
insert into table_part_2 VALUES(9, 955, "Row - 9", REPEAT('Large Column Data - 9' , 256));
select id, value, name from table_part_sub_2 partition (sub_1) order by id;
id	value	name
0	55	Row - 0
select id, value, name from table_part_sub_2 partition (sub_2) order by id;
id	value	name
select id, value, name from table_part_sub_2 partition (sub_3) order by id;
id	value	name
2	255	Row - 2
4	455	Row - 4
select id, value, name from table_part_sub_2 partition (sub_4) order by id;
id	value	name
1	155	Row - 1
3	355	Row - 3
select id, value, name from table_part_sub_2 partition (sub_5) order by id;
id	value	name
6	655	Row - 6
8	855	Row - 8
select id, value, name from table_part_sub_2 partition (sub_6) order by id;
id	value	name
5	555	Row - 5
7	755	Row - 7
9	955	Row - 9

# Import table partitions
CREATE TABLE table_part_encrypt(
id int, value int, name varchar(32), data BLOB, primary key pk(id, value),
key k1(value)) ENCRYPTION = 'Y'
      partition by range (value) subpartition by hash (id)
( partition Part_1 values less than (101)(
subpartition Sub_1,
subpartition sUb_2),
partition pArt_2 values less than (501)(
subpartition suB_3 DATA DIRECTORY = '/tmp/mysql_wl13352_data/',
subpartition SUB_4),
partition PART_3 values less than MAXVALUE(
subpartition SUB_5,
subpartition sub_6));
INSERT INTO table_part_encrypt SELECT * FROM table_part_sub_2;
ALTER TABLE table_part_sub_2 DISCARD PARTITION sub_3, sub_5 tablespace;
ALTER TABLE table_part_encrypt DISCARD PARTITION sub_3, sub_5 tablespace;

List files before import
---------------------------
table_part_1#p#p0.ibd
table_part_1#p#p1.ibd
table_part_1#p#p2.ibd
table_part_1#p#p3.ibd
table_part_1#p#p4.ibd
table_part_@005c_special_@002f_sub_@003f_3#p#part_@002f_3#sp#sub_@003f@002f_5.ibd
table_part_@005c_special_@002f_sub_@003f_3#p#part_@002f_3#sp#sub_@005c@002f@003f_6.ibd
table_part_@005c_special_@002f_sub_@003f_3#p#part_@003f_1#sp#sub_@002f_2.ibd
table_part_@005c_special_@002f_sub_@003f_3#p#part_@003f_1#sp#sub_@003f_1.ibd
table_part_@005c_special_@002f_sub_@003f_3#p#part_@005c_2#sp#sub_@003f@005c_4.ibd
table_part_@005c_special_@002f_sub_@003f_3#p#part_@005c_2#sp#sub_@005c_3.ibd
table_part_encrypt#P#PART_3#SP#SUB_5.cfg
table_part_encrypt#P#PART_3#SP#SUB_5.cfp
table_part_encrypt#P#PART_3#SP#SUB_5.ibd
table_part_encrypt#p#part_1#sp#sub_1.ibd
table_part_encrypt#p#part_1#sp#sub_2.ibd
table_part_encrypt#p#part_2#sp#sub_4.ibd
table_part_encrypt#p#part_3#sp#sub_6.ibd
table_part_sub_1#p#part_1#sp#part_1sp0.ibd
table_part_sub_1#p#part_1#sp#part_1sp1.ibd
table_part_sub_1#p#part_2#sp#part_2sp0.ibd
table_part_sub_1#p#part_2#sp#part_2sp1.ibd
table_part_sub_1#p#part_3#sp#part_3sp0.ibd
table_part_sub_1#p#part_3#sp#part_3sp1.ibd
table_part_sub_1#p#part_4#sp#part_4sp0.ibd
table_part_sub_1#p#part_4#sp#part_4sp1.ibd
table_part_sub_2#P#PART_3#SP#SUB_5.cfg
table_part_sub_2#P#PART_3#SP#SUB_5.ibd
table_part_sub_2#p#part_1#sp#sub_1.ibd
table_part_sub_2#p#part_1#sp#sub_2.ibd
table_part_sub_2#p#part_2#sp#sub_4.ibd
table_part_sub_2#p#part_3#sp#sub_6.ibd

table_part_2#p#p0.ibd
table_part_2#p#p1.ibd
table_part_2#p#p2.ibd
table_part_2#p#p3.ibd
table_part_2#p#p4.ibd
table_part_encrypt#P#pArt_2#SP#suB_3.cfg
table_part_encrypt#P#pArt_2#SP#suB_3.cfp
table_part_encrypt#P#pArt_2#SP#suB_3.ibd
table_part_sub_2#P#pArt_2#SP#suB_3.cfg
table_part_sub_2#P#pArt_2#SP#suB_3.ibd

ALTER TABLE table_part_sub_2 IMPORT PARTITION sub_3, sub_5 tablespace;

ALTER TABLE table_part_encrypt IMPORT PARTITION sub_3, sub_5 tablespace;

List all files after import
---------------------------
table_part_1#p#p0.ibd
table_part_1#p#p1.ibd
table_part_1#p#p2.ibd
table_part_1#p#p3.ibd
table_part_1#p#p4.ibd
table_part_@005c_special_@002f_sub_@003f_3#p#part_@002f_3#sp#sub_@003f@002f_5.ibd
table_part_@005c_special_@002f_sub_@003f_3#p#part_@002f_3#sp#sub_@005c@002f@003f_6.ibd
table_part_@005c_special_@002f_sub_@003f_3#p#part_@003f_1#sp#sub_@002f_2.ibd
table_part_@005c_special_@002f_sub_@003f_3#p#part_@003f_1#sp#sub_@003f_1.ibd
table_part_@005c_special_@002f_sub_@003f_3#p#part_@005c_2#sp#sub_@003f@005c_4.ibd
table_part_@005c_special_@002f_sub_@003f_3#p#part_@005c_2#sp#sub_@005c_3.ibd
table_part_encrypt#p#part_1#sp#sub_1.ibd
table_part_encrypt#p#part_1#sp#sub_2.ibd
table_part_encrypt#p#part_2#sp#sub_4.ibd
table_part_encrypt#p#part_3#sp#sub_5.cfg
table_part_encrypt#p#part_3#sp#sub_5.cfp
table_part_encrypt#p#part_3#sp#sub_5.ibd
table_part_encrypt#p#part_3#sp#sub_6.ibd
table_part_sub_1#p#part_1#sp#part_1sp0.ibd
table_part_sub_1#p#part_1#sp#part_1sp1.ibd
table_part_sub_1#p#part_2#sp#part_2sp0.ibd
table_part_sub_1#p#part_2#sp#part_2sp1.ibd
table_part_sub_1#p#part_3#sp#part_3sp0.ibd
table_part_sub_1#p#part_3#sp#part_3sp1.ibd
table_part_sub_1#p#part_4#sp#part_4sp0.ibd
table_part_sub_1#p#part_4#sp#part_4sp1.ibd
table_part_sub_2#p#part_1#sp#sub_1.ibd
table_part_sub_2#p#part_1#sp#sub_2.ibd
table_part_sub_2#p#part_2#sp#sub_4.ibd
table_part_sub_2#p#part_3#sp#sub_5.cfg
table_part_sub_2#p#part_3#sp#sub_5.ibd
table_part_sub_2#p#part_3#sp#sub_6.ibd

table_part_2#p#p0.ibd
table_part_2#p#p1.ibd
table_part_2#p#p2.ibd
table_part_2#p#p3.ibd
table_part_2#p#p4.ibd
table_part_encrypt#p#part_2#sp#sub_3.cfg
table_part_encrypt#p#part_2#sp#sub_3.cfp
table_part_encrypt#p#part_2#sp#sub_3.ibd
table_part_sub_2#p#part_2#sp#sub_3.cfg
table_part_sub_2#p#part_2#sp#sub_3.ibd

# Check table metadata and data
Checking partitioned tables
---------------------------
SET NAMES utf8mb4 COLLATE utf8mb4_0900_as_cs;

CHECK TABLE table_part_1;
Table	Op	Msg_type	Msg_text
test.table_part_1	check	status	OK

SHOW CREATE TABLE table_part_1;
Table	Create Table
table_part_1	CREATE TABLE `table_part_1` (
  `id` int NOT NULL,
  `value` int DEFAULT NULL,
  `name` varchar(32) DEFAULT NULL,
  `data` blob,
  PRIMARY KEY (`id`),
  KEY `k1` (`value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY HASH (`id`)
PARTITIONS 5 */

SELECT id, value, name, SUBSTRING(data, 1024, 32) FROM table_part_1 order by id;
id	value	name	SUBSTRING(data, 1024, 32)
0	55	Row - 0	ta - 0Large Column Data - 0Large
1	155	Row - 1	ta - 1Large Column Data - 1Large
2	255	Row - 2	ta - 2Large Column Data - 2Large
3	355	Row - 3	ta - 3Large Column Data - 3Large
4	455	Row - 4	ta - 4Large Column Data - 4Large
5	555	Row - 5	ta - 5Large Column Data - 5Large
6	655	Row - 6	ta - 6Large Column Data - 6Large
7	755	Row - 7	ta - 7Large Column Data - 7Large
8	855	Row - 8	ta - 8Large Column Data - 8Large
9	955	Row - 9	ta - 9Large Column Data - 9Large

CHECK TABLE table_part_sub_1;
Table	Op	Msg_type	Msg_text
test.table_part_sub_1	check	status	OK

SHOW CREATE TABLE table_part_sub_1;
Table	Create Table
table_part_sub_1	CREATE TABLE `table_part_sub_1` (
  `id` int NOT NULL,
  `value` int NOT NULL,
  `name` varchar(32) DEFAULT NULL,
  `data` blob,
  PRIMARY KEY (`id`,`value`),
  KEY `k1` (`value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY RANGE (`value`)
SUBPARTITION BY HASH (`id`)
SUBPARTITIONS 2
(PARTITION Part_1 VALUES LESS THAN (101) ENGINE = InnoDB,
 PARTITION pArt_2 VALUES LESS THAN (401) ENGINE = InnoDB,
 PARTITION part_3 VALUES LESS THAN (701) ENGINE = InnoDB,
 PARTITION PART_4 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */

SELECT id, value, name, SUBSTRING(data, 1024, 32) FROM table_part_sub_1 order by id;
id	value	name	SUBSTRING(data, 1024, 32)
0	55	Row - 0	ta - 0Large Column Data - 0Large
1	155	Row - 1	ta - 1Large Column Data - 1Large
2	255	Row - 2	ta - 2Large Column Data - 2Large
3	355	Row - 3	ta - 3Large Column Data - 3Large
4	455	Row - 4	ta - 4Large Column Data - 4Large
5	555	Row - 5	ta - 5Large Column Data - 5Large
6	655	Row - 6	ta - 6Large Column Data - 6Large
7	755	Row - 7	ta - 7Large Column Data - 7Large
8	855	Row - 8	ta - 8Large Column Data - 8Large
9	955	Row - 9	ta - 9Large Column Data - 9Large

CHECK TABLE table_part_sub_2;
Table	Op	Msg_type	Msg_text
test.table_part_sub_2	check	status	OK

SHOW CREATE TABLE table_part_sub_2;
Table	Create Table
table_part_sub_2	CREATE TABLE `table_part_sub_2` (
  `id` int NOT NULL,
  `value` int NOT NULL,
  `name` varchar(32) DEFAULT NULL,
  `data` blob,
  PRIMARY KEY (`id`,`value`),
  KEY `k1` (`value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY RANGE (`value`)
SUBPARTITION BY HASH (`id`)
(PARTITION Part_1 VALUES LESS THAN (101)
 (SUBPARTITION Sub_1 ENGINE = InnoDB,
  SUBPARTITION sUb_2 ENGINE = InnoDB),
 PARTITION pArt_2 VALUES LESS THAN (501)
 (SUBPARTITION suB_3 DATA DIRECTORY = '/tmp/mysql_wl13352_data/' ENGINE = InnoDB,
  SUBPARTITION SUB_4 ENGINE = InnoDB),
 PARTITION PART_3 VALUES LESS THAN MAXVALUE
 (SUBPARTITION SUB_5 ENGINE = InnoDB,
  SUBPARTITION sub_6 ENGINE = InnoDB)) */

SELECT id, value, name, SUBSTRING(data, 1024, 32) FROM table_part_sub_2 order by id;
id	value	name	SUBSTRING(data, 1024, 32)
0	55	Row - 0	ta - 0Large Column Data - 0Large
1	155	Row - 1	ta - 1Large Column Data - 1Large
2	255	Row - 2	ta - 2Large Column Data - 2Large
3	355	Row - 3	ta - 3Large Column Data - 3Large
4	455	Row - 4	ta - 4Large Column Data - 4Large
5	555	Row - 5	ta - 5Large Column Data - 5Large
6	655	Row - 6	ta - 6Large Column Data - 6Large
7	755	Row - 7	ta - 7Large Column Data - 7Large
8	855	Row - 8	ta - 8Large Column Data - 8Large
9	955	Row - 9	ta - 9Large Column Data - 9Large

CHECK TABLE `table_part_\_special_/_sub_?_3`;
Table	Op	Msg_type	Msg_text
test.table_part_\_special_/_sub_?_3	check	status	OK

SHOW CREATE TABLE `table_part_\_special_/_sub_?_3`;
Table	Create Table
table_part_\_special_/_sub_?_3	CREATE TABLE `table_part_\_special_/_sub_?_3` (
  `id` int NOT NULL,
  `value` int NOT NULL,
  `name` varchar(32) DEFAULT NULL,
  `data` blob,
  PRIMARY KEY (`id`,`value`),
  KEY `k1` (`value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY RANGE (`value`)
SUBPARTITION BY HASH (`id`)
(PARTITION `Part_?_1` VALUES LESS THAN (201)
 (SUBPARTITION `Sub_?_1` ENGINE = InnoDB,
  SUBPARTITION `sUb_/_2` ENGINE = InnoDB),
 PARTITION `pArt_\_2` VALUES LESS THAN (501)
 (SUBPARTITION `suB_\_3` ENGINE = InnoDB,
  SUBPARTITION `SUB_?\_4` ENGINE = InnoDB),
 PARTITION `PART_/_3` VALUES LESS THAN MAXVALUE
 (SUBPARTITION `SUB_?/_5` ENGINE = InnoDB,
  SUBPARTITION `sub_\/?_6` ENGINE = InnoDB)) */

SELECT id, value, name, SUBSTRING(data, 1024, 32) FROM `table_part_\_special_/_sub_?_3` order by id;
id	value	name	SUBSTRING(data, 1024, 32)
0	55	Row - 0	ta - 0Large Column Data - 0Large
1	155	Row - 1	ta - 1Large Column Data - 1Large
2	255	Row - 2	ta - 2Large Column Data - 2Large
3	355	Row - 3	ta - 3Large Column Data - 3Large
4	455	Row - 4	ta - 4Large Column Data - 4Large
5	555	Row - 5	ta - 5Large Column Data - 5Large
6	655	Row - 6	ta - 6Large Column Data - 6Large
7	755	Row - 7	ta - 7Large Column Data - 7Large
8	855	Row - 8	ta - 8Large Column Data - 8Large
9	955	Row - 9	ta - 9Large Column Data - 9Large

CHECK TABLE table_part_2;
Table	Op	Msg_type	Msg_text
test.table_part_2	check	status	OK

SHOW CREATE TABLE table_part_2;
Table	Create Table
table_part_2	CREATE TABLE `table_part_2` (
  `id` int NOT NULL,
  `value` int DEFAULT NULL,
  `name` varchar(32) DEFAULT NULL,
  `data` blob,
  PRIMARY KEY (`id`),
  KEY `k1` (`value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY HASH (`id`)
PARTITIONS 5 */

SELECT id, value, name, SUBSTRING(data, 1024, 32) FROM table_part_2 order by id;
id	value	name	SUBSTRING(data, 1024, 32)
0	55	Row - 0	ta - 0Large Column Data - 0Large
1	155	Row - 1	ta - 1Large Column Data - 1Large
2	255	Row - 2	ta - 2Large Column Data - 2Large
3	355	Row - 3	ta - 3Large Column Data - 3Large
4	455	Row - 4	ta - 4Large Column Data - 4Large
5	555	Row - 5	ta - 5Large Column Data - 5Large
6	655	Row - 6	ta - 6Large Column Data - 6Large
7	755	Row - 7	ta - 7Large Column Data - 7Large
8	855	Row - 8	ta - 8Large Column Data - 8Large
9	955	Row - 9	ta - 9Large Column Data - 9Large

CHECK TABLE table_part_encrypt;
Table	Op	Msg_type	Msg_text
test.table_part_encrypt	check	status	OK

SHOW CREATE TABLE table_part_encrypt;
Table	Create Table
table_part_encrypt	CREATE TABLE `table_part_encrypt` (
  `id` int NOT NULL,
  `value` int NOT NULL,
  `name` varchar(32) DEFAULT NULL,
  `data` blob,
  PRIMARY KEY (`id`,`value`),
  KEY `k1` (`value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ENCRYPTION='Y'
/*!50100 PARTITION BY RANGE (`value`)
SUBPARTITION BY HASH (`id`)
(PARTITION Part_1 VALUES LESS THAN (101)
 (SUBPARTITION Sub_1 ENGINE = InnoDB,
  SUBPARTITION sUb_2 ENGINE = InnoDB),
 PARTITION pArt_2 VALUES LESS THAN (501)
 (SUBPARTITION suB_3 DATA DIRECTORY = '/tmp/mysql_wl13352_data/' ENGINE = InnoDB,
  SUBPARTITION SUB_4 ENGINE = InnoDB),
 PARTITION PART_3 VALUES LESS THAN MAXVALUE
 (SUBPARTITION SUB_5 ENGINE = InnoDB,
  SUBPARTITION sub_6 ENGINE = InnoDB)) */

SELECT id, value, name, SUBSTRING(data, 1024, 32) FROM table_part_encrypt order by id;
id	value	name	SUBSTRING(data, 1024, 32)
0	55	Row - 0	ta - 0Large Column Data - 0Large
1	155	Row - 1	ta - 1Large Column Data - 1Large
2	255	Row - 2	ta - 2Large Column Data - 2Large
3	355	Row - 3	ta - 3Large Column Data - 3Large
4	455	Row - 4	ta - 4Large Column Data - 4Large
5	555	Row - 5	ta - 5Large Column Data - 5Large
6	655	Row - 6	ta - 6Large Column Data - 6Large
7	755	Row - 7	ta - 7Large Column Data - 7Large
8	855	Row - 8	ta - 8Large Column Data - 8Large
9	955	Row - 9	ta - 9Large Column Data - 9Large

Checking IFS views
------------------
select table_schema, table_name from information_schema.tables
where table_name LIKE '%table_part%' order by table_schema, table_name;
TABLE_SCHEMA	TABLE_NAME
test	table_part_1
test	table_part_2
test	table_part_\_special_/_sub_?_3
test	table_part_encrypt
test	table_part_sub_1
test	table_part_sub_2

select name, space_type from information_schema.innodb_tables
where name LIKE '%table_part%' order by name, space_type;
name	space_type
test/table_part_1#p#p0	Single
test/table_part_1#p#p1	Single
test/table_part_1#p#p2	Single
test/table_part_1#p#p3	Single
test/table_part_1#p#p4	Single
test/table_part_2#p#p0	Single
test/table_part_2#p#p1	Single
test/table_part_2#p#p2	Single
test/table_part_2#p#p3	Single
test/table_part_2#p#p4	Single
test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@002f_3#sp#sub_@003f@002f_5	Single
test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@002f_3#sp#sub_@005c@002f@003f_6	Single
test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@003f_1#sp#sub_@002f_2	Single
test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@003f_1#sp#sub_@003f_1	Single
test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@005c_2#sp#sub_@003f@005c_4	Single
test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@005c_2#sp#sub_@005c_3	Single
test/table_part_encrypt#p#part_1#sp#sub_1	Single
test/table_part_encrypt#p#part_1#sp#sub_2	Single
test/table_part_encrypt#p#part_2#sp#sub_3	Single
test/table_part_encrypt#p#part_2#sp#sub_4	Single
test/table_part_encrypt#p#part_3#sp#sub_5	Single
test/table_part_encrypt#p#part_3#sp#sub_6	Single
test/table_part_sub_1#p#part_1#sp#part_1sp0	Single
test/table_part_sub_1#p#part_1#sp#part_1sp1	Single
test/table_part_sub_1#p#part_2#sp#part_2sp0	Single
test/table_part_sub_1#p#part_2#sp#part_2sp1	Single
test/table_part_sub_1#p#part_3#sp#part_3sp0	Single
test/table_part_sub_1#p#part_3#sp#part_3sp1	Single
test/table_part_sub_1#p#part_4#sp#part_4sp0	Single
test/table_part_sub_1#p#part_4#sp#part_4sp1	Single
test/table_part_sub_2#p#part_1#sp#sub_1	Single
test/table_part_sub_2#p#part_1#sp#sub_2	Single
test/table_part_sub_2#p#part_2#sp#sub_3	Single
test/table_part_sub_2#p#part_2#sp#sub_4	Single
test/table_part_sub_2#p#part_3#sp#sub_5	Single
test/table_part_sub_2#p#part_3#sp#sub_6	Single

select table_schema, table_name, partition_name, subpartition_name
from information_schema.partitions
where table_name LIKE '%table_part%'
  order by table_schema, table_name, partition_name, subpartition_name;
TABLE_SCHEMA	TABLE_NAME	PARTITION_NAME	SUBPARTITION_NAME
test	table_part_1	p0	NULL
test	table_part_1	p1	NULL
test	table_part_1	p2	NULL
test	table_part_1	p3	NULL
test	table_part_1	p4	NULL
test	table_part_2	p0	NULL
test	table_part_2	p1	NULL
test	table_part_2	p2	NULL
test	table_part_2	p3	NULL
test	table_part_2	p4	NULL
test	table_part_\_special_/_sub_?_3	PART_/_3	SUB_?/_5
test	table_part_\_special_/_sub_?_3	PART_/_3	sub_\/?_6
test	table_part_\_special_/_sub_?_3	Part_?_1	sUb_/_2
test	table_part_\_special_/_sub_?_3	Part_?_1	Sub_?_1
test	table_part_\_special_/_sub_?_3	pArt_\_2	SUB_?\_4
test	table_part_\_special_/_sub_?_3	pArt_\_2	suB_\_3
test	table_part_encrypt	Part_1	Sub_1
test	table_part_encrypt	Part_1	sUb_2
test	table_part_encrypt	pArt_2	suB_3
test	table_part_encrypt	pArt_2	SUB_4
test	table_part_encrypt	PART_3	SUB_5
test	table_part_encrypt	PART_3	sub_6
test	table_part_sub_1	Part_1	Part_1sp0
test	table_part_sub_1	Part_1	Part_1sp1
test	table_part_sub_1	pArt_2	pArt_2sp0
test	table_part_sub_1	pArt_2	pArt_2sp1
test	table_part_sub_1	part_3	part_3sp0
test	table_part_sub_1	part_3	part_3sp1
test	table_part_sub_1	PART_4	PART_4sp0
test	table_part_sub_1	PART_4	PART_4sp1
test	table_part_sub_2	Part_1	Sub_1
test	table_part_sub_2	Part_1	sUb_2
test	table_part_sub_2	pArt_2	suB_3
test	table_part_sub_2	pArt_2	SUB_4
test	table_part_sub_2	PART_3	SUB_5
test	table_part_sub_2	PART_3	sub_6

select name from information_schema.innodb_tablespaces
where name LIKE '%table_part%' order by name;
name
test/table_part_1#p#p0
test/table_part_1#p#p1
test/table_part_1#p#p2
test/table_part_1#p#p3
test/table_part_1#p#p4
test/table_part_2#p#p0
test/table_part_2#p#p1
test/table_part_2#p#p2
test/table_part_2#p#p3
test/table_part_2#p#p4
test/table_part_encrypt#p#part_1#sp#sub_1
test/table_part_encrypt#p#part_1#sp#sub_2
test/table_part_encrypt#p#part_2#sp#sub_3
test/table_part_encrypt#p#part_2#sp#sub_4
test/table_part_encrypt#p#part_3#sp#sub_5
test/table_part_encrypt#p#part_3#sp#sub_6
test/table_part_sub_1#p#part_1#sp#part_1sp0
test/table_part_sub_1#p#part_1#sp#part_1sp1
test/table_part_sub_1#p#part_2#sp#part_2sp0
test/table_part_sub_1#p#part_2#sp#part_2sp1
test/table_part_sub_1#p#part_3#sp#part_3sp0
test/table_part_sub_1#p#part_3#sp#part_3sp1
test/table_part_sub_1#p#part_4#sp#part_4sp0
test/table_part_sub_1#p#part_4#sp#part_4sp1
test/table_part_sub_2#p#part_1#sp#sub_1
test/table_part_sub_2#p#part_1#sp#sub_2
test/table_part_sub_2#p#part_2#sp#sub_3
test/table_part_sub_2#p#part_2#sp#sub_4
test/table_part_sub_2#p#part_3#sp#sub_5
test/table_part_sub_2#p#part_3#sp#sub_6
test/table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_?/_5
test/table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_\/?_6
test/table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_/_2
test/table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_?_1
test/table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_?\_4
test/table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_\_3

select file_type, status, file_name, tablespace_name from information_schema.files
where file_name LIKE '%table_part%'
  order by file_type, status, file_name, tablespace_name;
FILE_TYPE	STATUS	FILE_NAME	TABLESPACE_NAME
TABLESPACE	NORMAL	./test/table_part_1#p#p0.ibd	test/table_part_1#p#p0
TABLESPACE	NORMAL	./test/table_part_1#p#p1.ibd	test/table_part_1#p#p1
TABLESPACE	NORMAL	./test/table_part_1#p#p2.ibd	test/table_part_1#p#p2
TABLESPACE	NORMAL	./test/table_part_1#p#p3.ibd	test/table_part_1#p#p3
TABLESPACE	NORMAL	./test/table_part_1#p#p4.ibd	test/table_part_1#p#p4
TABLESPACE	NORMAL	./test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@002f_3#sp#sub_@003f@002f_5.ibd	test/table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_?/_5
TABLESPACE	NORMAL	./test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@002f_3#sp#sub_@005c@002f@003f_6.ibd	test/table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_\/?_6
TABLESPACE	NORMAL	./test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@003f_1#sp#sub_@002f_2.ibd	test/table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_/_2
TABLESPACE	NORMAL	./test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@003f_1#sp#sub_@003f_1.ibd	test/table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_?_1
TABLESPACE	NORMAL	./test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@005c_2#sp#sub_@003f@005c_4.ibd	test/table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_?\_4
TABLESPACE	NORMAL	./test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@005c_2#sp#sub_@005c_3.ibd	test/table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_\_3
TABLESPACE	NORMAL	./test/table_part_encrypt#p#part_1#sp#sub_1.ibd	test/table_part_encrypt#p#part_1#sp#sub_1
TABLESPACE	NORMAL	./test/table_part_encrypt#p#part_1#sp#sub_2.ibd	test/table_part_encrypt#p#part_1#sp#sub_2
TABLESPACE	NORMAL	./test/table_part_encrypt#p#part_2#sp#sub_4.ibd	test/table_part_encrypt#p#part_2#sp#sub_4
TABLESPACE	NORMAL	./test/table_part_encrypt#p#part_3#sp#sub_5.ibd	test/table_part_encrypt#p#part_3#sp#sub_5
TABLESPACE	NORMAL	./test/table_part_encrypt#p#part_3#sp#sub_6.ibd	test/table_part_encrypt#p#part_3#sp#sub_6
TABLESPACE	NORMAL	./test/table_part_sub_1#p#part_1#sp#part_1sp0.ibd	test/table_part_sub_1#p#part_1#sp#part_1sp0
TABLESPACE	NORMAL	./test/table_part_sub_1#p#part_1#sp#part_1sp1.ibd	test/table_part_sub_1#p#part_1#sp#part_1sp1
TABLESPACE	NORMAL	./test/table_part_sub_1#p#part_2#sp#part_2sp0.ibd	test/table_part_sub_1#p#part_2#sp#part_2sp0
TABLESPACE	NORMAL	./test/table_part_sub_1#p#part_2#sp#part_2sp1.ibd	test/table_part_sub_1#p#part_2#sp#part_2sp1
TABLESPACE	NORMAL	./test/table_part_sub_1#p#part_3#sp#part_3sp0.ibd	test/table_part_sub_1#p#part_3#sp#part_3sp0
TABLESPACE	NORMAL	./test/table_part_sub_1#p#part_3#sp#part_3sp1.ibd	test/table_part_sub_1#p#part_3#sp#part_3sp1
TABLESPACE	NORMAL	./test/table_part_sub_1#p#part_4#sp#part_4sp0.ibd	test/table_part_sub_1#p#part_4#sp#part_4sp0
TABLESPACE	NORMAL	./test/table_part_sub_1#p#part_4#sp#part_4sp1.ibd	test/table_part_sub_1#p#part_4#sp#part_4sp1
TABLESPACE	NORMAL	./test/table_part_sub_2#p#part_1#sp#sub_1.ibd	test/table_part_sub_2#p#part_1#sp#sub_1
TABLESPACE	NORMAL	./test/table_part_sub_2#p#part_1#sp#sub_2.ibd	test/table_part_sub_2#p#part_1#sp#sub_2
TABLESPACE	NORMAL	./test/table_part_sub_2#p#part_2#sp#sub_4.ibd	test/table_part_sub_2#p#part_2#sp#sub_4
TABLESPACE	NORMAL	./test/table_part_sub_2#p#part_3#sp#sub_5.ibd	test/table_part_sub_2#p#part_3#sp#sub_5
TABLESPACE	NORMAL	./test/table_part_sub_2#p#part_3#sp#sub_6.ibd	test/table_part_sub_2#p#part_3#sp#sub_6
TABLESPACE	NORMAL	/tmp/mysql_wl13352_data/test/table_part_2#p#p0.ibd	test/table_part_2#p#p0
TABLESPACE	NORMAL	/tmp/mysql_wl13352_data/test/table_part_2#p#p1.ibd	test/table_part_2#p#p1
TABLESPACE	NORMAL	/tmp/mysql_wl13352_data/test/table_part_2#p#p2.ibd	test/table_part_2#p#p2
TABLESPACE	NORMAL	/tmp/mysql_wl13352_data/test/table_part_2#p#p3.ibd	test/table_part_2#p#p3
TABLESPACE	NORMAL	/tmp/mysql_wl13352_data/test/table_part_2#p#p4.ibd	test/table_part_2#p#p4
TABLESPACE	NORMAL	/tmp/mysql_wl13352_data/test/table_part_encrypt#p#part_2#sp#sub_3.ibd	test/table_part_encrypt#p#part_2#sp#sub_3
TABLESPACE	NORMAL	/tmp/mysql_wl13352_data/test/table_part_sub_2#p#part_2#sp#sub_3.ibd	test/table_part_sub_2#p#part_2#sp#sub_3

Checking Innodb stat tables
---------------------------
select database_name, table_name from mysql.innodb_table_stats
where table_name like '%table_part%' order by database_name, table_name;
database_name	table_name
test	table_part_1#p#p0
test	table_part_1#p#p1
test	table_part_1#p#p2
test	table_part_1#p#p3
test	table_part_1#p#p4
test	table_part_2#p#p0
test	table_part_2#p#p1
test	table_part_2#p#p2
test	table_part_2#p#p3
test	table_part_2#p#p4
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_?/_5
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_\/?_6
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_/_2
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_?_1
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_?\_4
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_\_3
test	table_part_encrypt#p#part_1#sp#sub_1
test	table_part_encrypt#p#part_1#sp#sub_2
test	table_part_encrypt#p#part_2#sp#sub_3
test	table_part_encrypt#p#part_2#sp#sub_4
test	table_part_encrypt#p#part_3#sp#sub_5
test	table_part_encrypt#p#part_3#sp#sub_6
test	table_part_sub_1#p#part_1#sp#part_1sp0
test	table_part_sub_1#p#part_1#sp#part_1sp1
test	table_part_sub_1#p#part_2#sp#part_2sp0
test	table_part_sub_1#p#part_2#sp#part_2sp1
test	table_part_sub_1#p#part_3#sp#part_3sp0
test	table_part_sub_1#p#part_3#sp#part_3sp1
test	table_part_sub_1#p#part_4#sp#part_4sp0
test	table_part_sub_1#p#part_4#sp#part_4sp1
test	table_part_sub_2#p#part_1#sp#sub_1
test	table_part_sub_2#p#part_1#sp#sub_2
test	table_part_sub_2#p#part_2#sp#sub_3
test	table_part_sub_2#p#part_2#sp#sub_4
test	table_part_sub_2#p#part_3#sp#sub_5
test	table_part_sub_2#p#part_3#sp#sub_6

select database_name, table_name, index_name, stat_name from mysql.innodb_index_stats
where table_name like '%table_part%'
  order by database_name, table_name, index_name, stat_name;
database_name	table_name	index_name	stat_name
test	table_part_1#p#p0	PRIMARY	n_diff_pfx01
test	table_part_1#p#p0	PRIMARY	n_leaf_pages
test	table_part_1#p#p0	PRIMARY	size
test	table_part_1#p#p0	k1	n_diff_pfx01
test	table_part_1#p#p0	k1	n_diff_pfx02
test	table_part_1#p#p0	k1	n_leaf_pages
test	table_part_1#p#p0	k1	size
test	table_part_1#p#p1	PRIMARY	n_diff_pfx01
test	table_part_1#p#p1	PRIMARY	n_leaf_pages
test	table_part_1#p#p1	PRIMARY	size
test	table_part_1#p#p1	k1	n_diff_pfx01
test	table_part_1#p#p1	k1	n_diff_pfx02
test	table_part_1#p#p1	k1	n_leaf_pages
test	table_part_1#p#p1	k1	size
test	table_part_1#p#p2	PRIMARY	n_diff_pfx01
test	table_part_1#p#p2	PRIMARY	n_leaf_pages
test	table_part_1#p#p2	PRIMARY	size
test	table_part_1#p#p2	k1	n_diff_pfx01
test	table_part_1#p#p2	k1	n_diff_pfx02
test	table_part_1#p#p2	k1	n_leaf_pages
test	table_part_1#p#p2	k1	size
test	table_part_1#p#p3	PRIMARY	n_diff_pfx01
test	table_part_1#p#p3	PRIMARY	n_leaf_pages
test	table_part_1#p#p3	PRIMARY	size
test	table_part_1#p#p3	k1	n_diff_pfx01
test	table_part_1#p#p3	k1	n_diff_pfx02
test	table_part_1#p#p3	k1	n_leaf_pages
test	table_part_1#p#p3	k1	size
test	table_part_1#p#p4	PRIMARY	n_diff_pfx01
test	table_part_1#p#p4	PRIMARY	n_leaf_pages
test	table_part_1#p#p4	PRIMARY	size
test	table_part_1#p#p4	k1	n_diff_pfx01
test	table_part_1#p#p4	k1	n_diff_pfx02
test	table_part_1#p#p4	k1	n_leaf_pages
test	table_part_1#p#p4	k1	size
test	table_part_2#p#p0	PRIMARY	n_diff_pfx01
test	table_part_2#p#p0	PRIMARY	n_leaf_pages
test	table_part_2#p#p0	PRIMARY	size
test	table_part_2#p#p0	k1	n_diff_pfx01
test	table_part_2#p#p0	k1	n_diff_pfx02
test	table_part_2#p#p0	k1	n_leaf_pages
test	table_part_2#p#p0	k1	size
test	table_part_2#p#p1	PRIMARY	n_diff_pfx01
test	table_part_2#p#p1	PRIMARY	n_leaf_pages
test	table_part_2#p#p1	PRIMARY	size
test	table_part_2#p#p1	k1	n_diff_pfx01
test	table_part_2#p#p1	k1	n_diff_pfx02
test	table_part_2#p#p1	k1	n_leaf_pages
test	table_part_2#p#p1	k1	size
test	table_part_2#p#p2	PRIMARY	n_diff_pfx01
test	table_part_2#p#p2	PRIMARY	n_leaf_pages
test	table_part_2#p#p2	PRIMARY	size
test	table_part_2#p#p2	k1	n_diff_pfx01
test	table_part_2#p#p2	k1	n_diff_pfx02
test	table_part_2#p#p2	k1	n_leaf_pages
test	table_part_2#p#p2	k1	size
test	table_part_2#p#p3	PRIMARY	n_diff_pfx01
test	table_part_2#p#p3	PRIMARY	n_leaf_pages
test	table_part_2#p#p3	PRIMARY	size
test	table_part_2#p#p3	k1	n_diff_pfx01
test	table_part_2#p#p3	k1	n_diff_pfx02
test	table_part_2#p#p3	k1	n_leaf_pages
test	table_part_2#p#p3	k1	size
test	table_part_2#p#p4	PRIMARY	n_diff_pfx01
test	table_part_2#p#p4	PRIMARY	n_leaf_pages
test	table_part_2#p#p4	PRIMARY	size
test	table_part_2#p#p4	k1	n_diff_pfx01
test	table_part_2#p#p4	k1	n_diff_pfx02
test	table_part_2#p#p4	k1	n_leaf_pages
test	table_part_2#p#p4	k1	size
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_?/_5	PRIMARY	n_diff_pfx01
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_?/_5	PRIMARY	n_diff_pfx02
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_?/_5	PRIMARY	n_leaf_pages
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_?/_5	PRIMARY	size
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_?/_5	k1	n_diff_pfx01
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_?/_5	k1	n_diff_pfx02
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_?/_5	k1	n_leaf_pages
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_?/_5	k1	size
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_\/?_6	PRIMARY	n_diff_pfx01
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_\/?_6	PRIMARY	n_diff_pfx02
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_\/?_6	PRIMARY	n_leaf_pages
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_\/?_6	PRIMARY	size
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_\/?_6	k1	n_diff_pfx01
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_\/?_6	k1	n_diff_pfx02
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_\/?_6	k1	n_leaf_pages
test	table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_\/?_6	k1	size
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_/_2	PRIMARY	n_diff_pfx01
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_/_2	PRIMARY	n_diff_pfx02
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_/_2	PRIMARY	n_leaf_pages
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_/_2	PRIMARY	size
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_/_2	k1	n_diff_pfx01
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_/_2	k1	n_diff_pfx02
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_/_2	k1	n_leaf_pages
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_/_2	k1	size
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_?_1	PRIMARY	n_diff_pfx01
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_?_1	PRIMARY	n_diff_pfx02
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_?_1	PRIMARY	n_leaf_pages
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_?_1	PRIMARY	size
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_?_1	k1	n_diff_pfx01
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_?_1	k1	n_diff_pfx02
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_?_1	k1	n_leaf_pages
test	table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_?_1	k1	size
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_?\_4	PRIMARY	n_diff_pfx01
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_?\_4	PRIMARY	n_diff_pfx02
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_?\_4	PRIMARY	n_leaf_pages
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_?\_4	PRIMARY	size
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_?\_4	k1	n_diff_pfx01
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_?\_4	k1	n_diff_pfx02
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_?\_4	k1	n_leaf_pages
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_?\_4	k1	size
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_\_3	PRIMARY	n_diff_pfx01
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_\_3	PRIMARY	n_diff_pfx02
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_\_3	PRIMARY	n_leaf_pages
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_\_3	PRIMARY	size
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_\_3	k1	n_diff_pfx01
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_\_3	k1	n_diff_pfx02
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_\_3	k1	n_leaf_pages
test	table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_\_3	k1	size
test	table_part_encrypt#p#part_1#sp#sub_1	PRIMARY	n_diff_pfx01
test	table_part_encrypt#p#part_1#sp#sub_1	PRIMARY	n_diff_pfx02
test	table_part_encrypt#p#part_1#sp#sub_1	PRIMARY	n_leaf_pages
test	table_part_encrypt#p#part_1#sp#sub_1	PRIMARY	size
test	table_part_encrypt#p#part_1#sp#sub_1	k1	n_diff_pfx01
test	table_part_encrypt#p#part_1#sp#sub_1	k1	n_diff_pfx02
test	table_part_encrypt#p#part_1#sp#sub_1	k1	n_leaf_pages
test	table_part_encrypt#p#part_1#sp#sub_1	k1	size
test	table_part_encrypt#p#part_1#sp#sub_2	PRIMARY	n_diff_pfx01
test	table_part_encrypt#p#part_1#sp#sub_2	PRIMARY	n_diff_pfx02
test	table_part_encrypt#p#part_1#sp#sub_2	PRIMARY	n_leaf_pages
test	table_part_encrypt#p#part_1#sp#sub_2	PRIMARY	size
test	table_part_encrypt#p#part_1#sp#sub_2	k1	n_diff_pfx01
test	table_part_encrypt#p#part_1#sp#sub_2	k1	n_diff_pfx02
test	table_part_encrypt#p#part_1#sp#sub_2	k1	n_leaf_pages
test	table_part_encrypt#p#part_1#sp#sub_2	k1	size
test	table_part_encrypt#p#part_2#sp#sub_3	PRIMARY	n_diff_pfx01
test	table_part_encrypt#p#part_2#sp#sub_3	PRIMARY	n_diff_pfx02
test	table_part_encrypt#p#part_2#sp#sub_3	PRIMARY	n_leaf_pages
test	table_part_encrypt#p#part_2#sp#sub_3	PRIMARY	size
test	table_part_encrypt#p#part_2#sp#sub_3	k1	n_diff_pfx01
test	table_part_encrypt#p#part_2#sp#sub_3	k1	n_diff_pfx02
test	table_part_encrypt#p#part_2#sp#sub_3	k1	n_leaf_pages
test	table_part_encrypt#p#part_2#sp#sub_3	k1	size
test	table_part_encrypt#p#part_2#sp#sub_4	PRIMARY	n_diff_pfx01
test	table_part_encrypt#p#part_2#sp#sub_4	PRIMARY	n_diff_pfx02
test	table_part_encrypt#p#part_2#sp#sub_4	PRIMARY	n_leaf_pages
test	table_part_encrypt#p#part_2#sp#sub_4	PRIMARY	size
test	table_part_encrypt#p#part_2#sp#sub_4	k1	n_diff_pfx01
test	table_part_encrypt#p#part_2#sp#sub_4	k1	n_diff_pfx02
test	table_part_encrypt#p#part_2#sp#sub_4	k1	n_leaf_pages
test	table_part_encrypt#p#part_2#sp#sub_4	k1	size
test	table_part_encrypt#p#part_3#sp#sub_5	PRIMARY	n_diff_pfx01
test	table_part_encrypt#p#part_3#sp#sub_5	PRIMARY	n_diff_pfx02
test	table_part_encrypt#p#part_3#sp#sub_5	PRIMARY	n_leaf_pages
test	table_part_encrypt#p#part_3#sp#sub_5	PRIMARY	size
test	table_part_encrypt#p#part_3#sp#sub_5	k1	n_diff_pfx01
test	table_part_encrypt#p#part_3#sp#sub_5	k1	n_diff_pfx02
test	table_part_encrypt#p#part_3#sp#sub_5	k1	n_leaf_pages
test	table_part_encrypt#p#part_3#sp#sub_5	k1	size
test	table_part_encrypt#p#part_3#sp#sub_6	PRIMARY	n_diff_pfx01
test	table_part_encrypt#p#part_3#sp#sub_6	PRIMARY	n_diff_pfx02
test	table_part_encrypt#p#part_3#sp#sub_6	PRIMARY	n_leaf_pages
test	table_part_encrypt#p#part_3#sp#sub_6	PRIMARY	size
test	table_part_encrypt#p#part_3#sp#sub_6	k1	n_diff_pfx01
test	table_part_encrypt#p#part_3#sp#sub_6	k1	n_diff_pfx02
test	table_part_encrypt#p#part_3#sp#sub_6	k1	n_leaf_pages
test	table_part_encrypt#p#part_3#sp#sub_6	k1	size
test	table_part_sub_1#p#part_1#sp#part_1sp0	PRIMARY	n_diff_pfx01
test	table_part_sub_1#p#part_1#sp#part_1sp0	PRIMARY	n_diff_pfx02
test	table_part_sub_1#p#part_1#sp#part_1sp0	PRIMARY	n_leaf_pages
test	table_part_sub_1#p#part_1#sp#part_1sp0	PRIMARY	size
test	table_part_sub_1#p#part_1#sp#part_1sp0	k1	n_diff_pfx01
test	table_part_sub_1#p#part_1#sp#part_1sp0	k1	n_diff_pfx02
test	table_part_sub_1#p#part_1#sp#part_1sp0	k1	n_leaf_pages
test	table_part_sub_1#p#part_1#sp#part_1sp0	k1	size
test	table_part_sub_1#p#part_1#sp#part_1sp1	PRIMARY	n_diff_pfx01
test	table_part_sub_1#p#part_1#sp#part_1sp1	PRIMARY	n_diff_pfx02
test	table_part_sub_1#p#part_1#sp#part_1sp1	PRIMARY	n_leaf_pages
test	table_part_sub_1#p#part_1#sp#part_1sp1	PRIMARY	size
test	table_part_sub_1#p#part_1#sp#part_1sp1	k1	n_diff_pfx01
test	table_part_sub_1#p#part_1#sp#part_1sp1	k1	n_diff_pfx02
test	table_part_sub_1#p#part_1#sp#part_1sp1	k1	n_leaf_pages
test	table_part_sub_1#p#part_1#sp#part_1sp1	k1	size
test	table_part_sub_1#p#part_2#sp#part_2sp0	PRIMARY	n_diff_pfx01
test	table_part_sub_1#p#part_2#sp#part_2sp0	PRIMARY	n_diff_pfx02
test	table_part_sub_1#p#part_2#sp#part_2sp0	PRIMARY	n_leaf_pages
test	table_part_sub_1#p#part_2#sp#part_2sp0	PRIMARY	size
test	table_part_sub_1#p#part_2#sp#part_2sp0	k1	n_diff_pfx01
test	table_part_sub_1#p#part_2#sp#part_2sp0	k1	n_diff_pfx02
test	table_part_sub_1#p#part_2#sp#part_2sp0	k1	n_leaf_pages
test	table_part_sub_1#p#part_2#sp#part_2sp0	k1	size
test	table_part_sub_1#p#part_2#sp#part_2sp1	PRIMARY	n_diff_pfx01
test	table_part_sub_1#p#part_2#sp#part_2sp1	PRIMARY	n_diff_pfx02
test	table_part_sub_1#p#part_2#sp#part_2sp1	PRIMARY	n_leaf_pages
test	table_part_sub_1#p#part_2#sp#part_2sp1	PRIMARY	size
test	table_part_sub_1#p#part_2#sp#part_2sp1	k1	n_diff_pfx01
test	table_part_sub_1#p#part_2#sp#part_2sp1	k1	n_diff_pfx02
test	table_part_sub_1#p#part_2#sp#part_2sp1	k1	n_leaf_pages
test	table_part_sub_1#p#part_2#sp#part_2sp1	k1	size
test	table_part_sub_1#p#part_3#sp#part_3sp0	PRIMARY	n_diff_pfx01
test	table_part_sub_1#p#part_3#sp#part_3sp0	PRIMARY	n_diff_pfx02
test	table_part_sub_1#p#part_3#sp#part_3sp0	PRIMARY	n_leaf_pages
test	table_part_sub_1#p#part_3#sp#part_3sp0	PRIMARY	size
test	table_part_sub_1#p#part_3#sp#part_3sp0	k1	n_diff_pfx01
test	table_part_sub_1#p#part_3#sp#part_3sp0	k1	n_diff_pfx02
test	table_part_sub_1#p#part_3#sp#part_3sp0	k1	n_leaf_pages
test	table_part_sub_1#p#part_3#sp#part_3sp0	k1	size
test	table_part_sub_1#p#part_3#sp#part_3sp1	PRIMARY	n_diff_pfx01
test	table_part_sub_1#p#part_3#sp#part_3sp1	PRIMARY	n_diff_pfx02
test	table_part_sub_1#p#part_3#sp#part_3sp1	PRIMARY	n_leaf_pages
test	table_part_sub_1#p#part_3#sp#part_3sp1	PRIMARY	size
test	table_part_sub_1#p#part_3#sp#part_3sp1	k1	n_diff_pfx01
test	table_part_sub_1#p#part_3#sp#part_3sp1	k1	n_diff_pfx02
test	table_part_sub_1#p#part_3#sp#part_3sp1	k1	n_leaf_pages
test	table_part_sub_1#p#part_3#sp#part_3sp1	k1	size
test	table_part_sub_1#p#part_4#sp#part_4sp0	PRIMARY	n_diff_pfx01
test	table_part_sub_1#p#part_4#sp#part_4sp0	PRIMARY	n_diff_pfx02
test	table_part_sub_1#p#part_4#sp#part_4sp0	PRIMARY	n_leaf_pages
test	table_part_sub_1#p#part_4#sp#part_4sp0	PRIMARY	size
test	table_part_sub_1#p#part_4#sp#part_4sp0	k1	n_diff_pfx01
test	table_part_sub_1#p#part_4#sp#part_4sp0	k1	n_diff_pfx02
test	table_part_sub_1#p#part_4#sp#part_4sp0	k1	n_leaf_pages
test	table_part_sub_1#p#part_4#sp#part_4sp0	k1	size
test	table_part_sub_1#p#part_4#sp#part_4sp1	PRIMARY	n_diff_pfx01
test	table_part_sub_1#p#part_4#sp#part_4sp1	PRIMARY	n_diff_pfx02
test	table_part_sub_1#p#part_4#sp#part_4sp1	PRIMARY	n_leaf_pages
test	table_part_sub_1#p#part_4#sp#part_4sp1	PRIMARY	size
test	table_part_sub_1#p#part_4#sp#part_4sp1	k1	n_diff_pfx01
test	table_part_sub_1#p#part_4#sp#part_4sp1	k1	n_diff_pfx02
test	table_part_sub_1#p#part_4#sp#part_4sp1	k1	n_leaf_pages
test	table_part_sub_1#p#part_4#sp#part_4sp1	k1	size
test	table_part_sub_2#p#part_1#sp#sub_1	PRIMARY	n_diff_pfx01
test	table_part_sub_2#p#part_1#sp#sub_1	PRIMARY	n_diff_pfx02
test	table_part_sub_2#p#part_1#sp#sub_1	PRIMARY	n_leaf_pages
test	table_part_sub_2#p#part_1#sp#sub_1	PRIMARY	size
test	table_part_sub_2#p#part_1#sp#sub_1	k1	n_diff_pfx01
test	table_part_sub_2#p#part_1#sp#sub_1	k1	n_diff_pfx02
test	table_part_sub_2#p#part_1#sp#sub_1	k1	n_leaf_pages
test	table_part_sub_2#p#part_1#sp#sub_1	k1	size
test	table_part_sub_2#p#part_1#sp#sub_2	PRIMARY	n_diff_pfx01
test	table_part_sub_2#p#part_1#sp#sub_2	PRIMARY	n_diff_pfx02
test	table_part_sub_2#p#part_1#sp#sub_2	PRIMARY	n_leaf_pages
test	table_part_sub_2#p#part_1#sp#sub_2	PRIMARY	size
test	table_part_sub_2#p#part_1#sp#sub_2	k1	n_diff_pfx01
test	table_part_sub_2#p#part_1#sp#sub_2	k1	n_diff_pfx02
test	table_part_sub_2#p#part_1#sp#sub_2	k1	n_leaf_pages
test	table_part_sub_2#p#part_1#sp#sub_2	k1	size
test	table_part_sub_2#p#part_2#sp#sub_3	PRIMARY	n_diff_pfx01
test	table_part_sub_2#p#part_2#sp#sub_3	PRIMARY	n_diff_pfx02
test	table_part_sub_2#p#part_2#sp#sub_3	PRIMARY	n_leaf_pages
test	table_part_sub_2#p#part_2#sp#sub_3	PRIMARY	size
test	table_part_sub_2#p#part_2#sp#sub_3	k1	n_diff_pfx01
test	table_part_sub_2#p#part_2#sp#sub_3	k1	n_diff_pfx02
test	table_part_sub_2#p#part_2#sp#sub_3	k1	n_leaf_pages
test	table_part_sub_2#p#part_2#sp#sub_3	k1	size
test	table_part_sub_2#p#part_2#sp#sub_4	PRIMARY	n_diff_pfx01
test	table_part_sub_2#p#part_2#sp#sub_4	PRIMARY	n_diff_pfx02
test	table_part_sub_2#p#part_2#sp#sub_4	PRIMARY	n_leaf_pages
test	table_part_sub_2#p#part_2#sp#sub_4	PRIMARY	size
test	table_part_sub_2#p#part_2#sp#sub_4	k1	n_diff_pfx01
test	table_part_sub_2#p#part_2#sp#sub_4	k1	n_diff_pfx02
test	table_part_sub_2#p#part_2#sp#sub_4	k1	n_leaf_pages
test	table_part_sub_2#p#part_2#sp#sub_4	k1	size
test	table_part_sub_2#p#part_3#sp#sub_5	PRIMARY	n_diff_pfx01
test	table_part_sub_2#p#part_3#sp#sub_5	PRIMARY	n_diff_pfx02
test	table_part_sub_2#p#part_3#sp#sub_5	PRIMARY	n_leaf_pages
test	table_part_sub_2#p#part_3#sp#sub_5	PRIMARY	size
test	table_part_sub_2#p#part_3#sp#sub_5	k1	n_diff_pfx01
test	table_part_sub_2#p#part_3#sp#sub_5	k1	n_diff_pfx02
test	table_part_sub_2#p#part_3#sp#sub_5	k1	n_leaf_pages
test	table_part_sub_2#p#part_3#sp#sub_5	k1	size
test	table_part_sub_2#p#part_3#sp#sub_6	PRIMARY	n_diff_pfx01
test	table_part_sub_2#p#part_3#sp#sub_6	PRIMARY	n_diff_pfx02
test	table_part_sub_2#p#part_3#sp#sub_6	PRIMARY	n_leaf_pages
test	table_part_sub_2#p#part_3#sp#sub_6	PRIMARY	size
test	table_part_sub_2#p#part_3#sp#sub_6	k1	n_diff_pfx01
test	table_part_sub_2#p#part_3#sp#sub_6	k1	n_diff_pfx02
test	table_part_sub_2#p#part_3#sp#sub_6	k1	n_leaf_pages
test	table_part_sub_2#p#part_3#sp#sub_6	k1	size

Checking DD tables
------------------
SET DEBUG='+d,skip_dd_table_access_check';

select t.name as "table", p.name as "partition", p.number
from mysql.tables t, mysql.table_partitions p
where p.table_id = t.id
order by t.name, p.name, p.number;
table	partition	number
table_part_1	p0	0
table_part_1	p1	1
table_part_1	p2	2
table_part_1	p3	3
table_part_1	p4	4
table_part_2	p0	0
table_part_2	p1	1
table_part_2	p2	2
table_part_2	p3	3
table_part_2	p4	4
table_part_\_special_/_sub_?_3	PART_/_3	2
table_part_\_special_/_sub_?_3	Part_?_1	0
table_part_\_special_/_sub_?_3	pArt_\_2	1
table_part_\_special_/_sub_?_3	sUb_/_2	1
table_part_\_special_/_sub_?_3	SUB_?/_5	0
table_part_\_special_/_sub_?_3	SUB_?\_4	1
table_part_\_special_/_sub_?_3	Sub_?_1	0
table_part_\_special_/_sub_?_3	sub_\/?_6	1
table_part_\_special_/_sub_?_3	suB_\_3	0
table_part_encrypt	Part_1	0
table_part_encrypt	pArt_2	1
table_part_encrypt	PART_3	2
table_part_encrypt	Sub_1	0
table_part_encrypt	sUb_2	1
table_part_encrypt	suB_3	0
table_part_encrypt	SUB_4	1
table_part_encrypt	SUB_5	0
table_part_encrypt	sub_6	1
table_part_sub_1	Part_1	0
table_part_sub_1	Part_1sp0	0
table_part_sub_1	Part_1sp1	1
table_part_sub_1	pArt_2	1
table_part_sub_1	pArt_2sp0	0
table_part_sub_1	pArt_2sp1	1
table_part_sub_1	part_3	2
table_part_sub_1	part_3sp0	0
table_part_sub_1	part_3sp1	1
table_part_sub_1	PART_4	3
table_part_sub_1	PART_4sp0	0
table_part_sub_1	PART_4sp1	1
table_part_sub_2	Part_1	0
table_part_sub_2	pArt_2	1
table_part_sub_2	PART_3	2
table_part_sub_2	Sub_1	0
table_part_sub_2	sUb_2	1
table_part_sub_2	suB_3	0
table_part_sub_2	SUB_4	1
table_part_sub_2	SUB_5	0
table_part_sub_2	sub_6	1

select name from mysql.tablespaces
where name like '%table_part%' order by name;
name
test/table_part_1#p#p0
test/table_part_1#p#p1
test/table_part_1#p#p2
test/table_part_1#p#p3
test/table_part_1#p#p4
test/table_part_2#p#p0
test/table_part_2#p#p1
test/table_part_2#p#p2
test/table_part_2#p#p3
test/table_part_2#p#p4
test/table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_?/_5
test/table_part_\_special_/_sub_?_3#p#part_/_3#sp#sub_\/?_6
test/table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_/_2
test/table_part_\_special_/_sub_?_3#p#part_?_1#sp#sub_?_1
test/table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_?\_4
test/table_part_\_special_/_sub_?_3#p#part_\_2#sp#sub_\_3
test/table_part_encrypt#p#part_1#sp#sub_1
test/table_part_encrypt#p#part_1#sp#sub_2
test/table_part_encrypt#p#part_2#sp#sub_3
test/table_part_encrypt#p#part_2#sp#sub_4
test/table_part_encrypt#p#part_3#sp#sub_5
test/table_part_encrypt#p#part_3#sp#sub_6
test/table_part_sub_1#p#part_1#sp#part_1sp0
test/table_part_sub_1#p#part_1#sp#part_1sp1
test/table_part_sub_1#p#part_2#sp#part_2sp0
test/table_part_sub_1#p#part_2#sp#part_2sp1
test/table_part_sub_1#p#part_3#sp#part_3sp0
test/table_part_sub_1#p#part_3#sp#part_3sp1
test/table_part_sub_1#p#part_4#sp#part_4sp0
test/table_part_sub_1#p#part_4#sp#part_4sp1
test/table_part_sub_2#p#part_1#sp#sub_1
test/table_part_sub_2#p#part_1#sp#sub_2
test/table_part_sub_2#p#part_2#sp#sub_3
test/table_part_sub_2#p#part_2#sp#sub_4
test/table_part_sub_2#p#part_3#sp#sub_5
test/table_part_sub_2#p#part_3#sp#sub_6

select file_name from mysql.tablespace_files
where file_name like '%table_part%' order by file_name;
file_name
./test/table_part_1#p#p0.ibd
./test/table_part_1#p#p1.ibd
./test/table_part_1#p#p2.ibd
./test/table_part_1#p#p3.ibd
./test/table_part_1#p#p4.ibd
./test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@002f_3#sp#sub_@003f@002f_5.ibd
./test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@002f_3#sp#sub_@005c@002f@003f_6.ibd
./test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@003f_1#sp#sub_@002f_2.ibd
./test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@003f_1#sp#sub_@003f_1.ibd
./test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@005c_2#sp#sub_@003f@005c_4.ibd
./test/table_part_@005c_special_@002f_sub_@003f_3#p#part_@005c_2#sp#sub_@005c_3.ibd
./test/table_part_encrypt#p#part_1#sp#sub_1.ibd
./test/table_part_encrypt#p#part_1#sp#sub_2.ibd
./test/table_part_encrypt#p#part_2#sp#sub_4.ibd
./test/table_part_encrypt#p#part_3#sp#sub_5.ibd
./test/table_part_encrypt#p#part_3#sp#sub_6.ibd
./test/table_part_sub_1#p#part_1#sp#part_1sp0.ibd
./test/table_part_sub_1#p#part_1#sp#part_1sp1.ibd
./test/table_part_sub_1#p#part_2#sp#part_2sp0.ibd
./test/table_part_sub_1#p#part_2#sp#part_2sp1.ibd
./test/table_part_sub_1#p#part_3#sp#part_3sp0.ibd
./test/table_part_sub_1#p#part_3#sp#part_3sp1.ibd
./test/table_part_sub_1#p#part_4#sp#part_4sp0.ibd
./test/table_part_sub_1#p#part_4#sp#part_4sp1.ibd
./test/table_part_sub_2#p#part_1#sp#sub_1.ibd
./test/table_part_sub_2#p#part_1#sp#sub_2.ibd
./test/table_part_sub_2#p#part_2#sp#sub_4.ibd
./test/table_part_sub_2#p#part_3#sp#sub_5.ibd
./test/table_part_sub_2#p#part_3#sp#sub_6.ibd
/tmp/mysql_wl13352_data/test/table_part_2#p#p0.ibd
/tmp/mysql_wl13352_data/test/table_part_2#p#p1.ibd
/tmp/mysql_wl13352_data/test/table_part_2#p#p2.ibd
/tmp/mysql_wl13352_data/test/table_part_2#p#p3.ibd
/tmp/mysql_wl13352_data/test/table_part_2#p#p4.ibd
/tmp/mysql_wl13352_data/test/table_part_encrypt#p#part_2#sp#sub_3.ibd
/tmp/mysql_wl13352_data/test/table_part_sub_2#p#part_2#sp#sub_3.ibd
SET DEBUG='-d,skip_dd_table_access_check';
SET NAMES DEFAULT;

# Drop all tables created for import
Dropping partitioned tables
---------------------------
DROP TABLE table_part_1;
DROP TABLE table_part_sub_1;
DROP TABLE table_part_sub_2;
DROP TABLE `table_part_\_special_/_sub_?_3`;
DROP TABLE table_part_2;
DROP TABLE table_part_encrypt;

# Stop DB server
# Cleanup: Restart with default options.
# restart
# ----------------------------------------------------------------------
# Teardown
# Removing local keyring file for keyring component: component_keyring_file
# Removing global configuration file for keyring component: component_keyring_file
# Removing global manifest file for MySQL server
# Restarting server without the manifest file
# ----------------------------------------------------------------------
