/*
 Copyright (c) 2010, 2025, Oracle and/or its affiliates.

 This program is free software; you can redistribute it and/or modify
 it under the terms of the GNU General Public License, version 2.0,
 as published by the Free Software Foundation.

 This program is designed to work with certain software (including
 but not limited to OpenSSL) that is licensed under separate terms,
 as designated in a particular file or component or in included license
 documentation.  The authors of MySQL hereby grant you an additional
 permission to link the program and your derivative works with the
 separately licensed software that they have either included with
 the program or referenced in the documentation.

 This program is distributed in the hope that it will be useful,
 but WITHOUT ANY WARRANTY; without even the implied warranty of
 MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 GNU General Public License, version 2.0, for more details.

 You should have received a copy of the GNU General Public License
 along with this program; if not, write to the Free Software
 Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
*/
/*
 * myjapi_classes.hpp
 */

#ifndef myjapi_classes_hpp
#define myjapi_classes_hpp

// API to implement against
#include "MyApiWrapper.hpp"
#include "myapi.hpp"

// libraries
#include "helpers.hpp"
#include "jtie.hpp"

// ---------------------------------------------------------------------------
// API Type Definitions
// ---------------------------------------------------------------------------

JTIE_DEFINE_PEER_CLASS_MAPPING(A, myjapi_A)
JTIE_DEFINE_PEER_CLASS_MAPPING(B0, myjapi_B0)
JTIE_DEFINE_PEER_CLASS_MAPPING(B1, myjapi_B1)
JTIE_DEFINE_PEER_CLASS_MAPPING(C0, myjapi_CI_C0)
JTIE_DEFINE_PEER_CLASS_MAPPING(C1, myjapi_CI_C1)
JTIE_DEFINE_PEER_CLASS_MAPPING(C0, myjapi_CI_C0Array)
JTIE_DEFINE_PEER_CLASS_MAPPING(C1, myjapi_CI_C1Array)
JTIE_DEFINE_PEER_CLASS_MAPPING(D0, myjapi_D0)
JTIE_DEFINE_PEER_CLASS_MAPPING(D1, myjapi_D1)
JTIE_DEFINE_PEER_CLASS_MAPPING(D2, myjapi_D2)
JTIE_DEFINE_PEER_CLASS_MAPPING(E, myjapi_E)

JTIE_DEFINE_JINT_ENUM_TYPE_MAPPING(E::EE, E_EE)

// ---------------------------------------------------------------------------
// API JNI function stubs
// ---------------------------------------------------------------------------

// #define JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION 1

// The API stub functions in this file have mangled names that adhere
// to the JVM specification.  It is not necessary to include the
// function prototypes generated by the javah tool from the Java source
// if they are declared to receive "C" linkage here.
//
// A javah bug in JDK 5
//   http://forums.sun.com/thread.jspa?threadID=5115982&tstart=1499
// generates a wrong name for native methods in static nested classes.
// JDK 6 has this bug only partially fixed (nested classes as invocation
// targets but not as parameters).
//
// Outer$Inner is to be mangled as unicode escape: Outer_00024Inner, see:
//   http://java.sun.com/javase/6/docs/technotes/guides/jni/spec/design.html#wp615]Resolving%20Native%20Method%20Names
extern "C" {

// ---------------------------------------------------------------------------

// #include "myjapi_A.h"

JNIEXPORT jobject JNICALL Java_myjapi_A_create_1r__(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_A_create_1r__(JNIEnv *, jclass)");
  return gcreate<ttrait_myjapi_A_r>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_A_create_1r__I(JNIEnv *env, jclass cls,
                                                     jint p0) {
  TRACE("jobject Java_myjapi_A_create_1r__I(JNIEnv *, jclass, jint)");
  return gcreate<ttrait_myjapi_A_r, ttrait_int32>(env, cls, p0);
}

JNIEXPORT jobject JNICALL Java_myjapi_A_create_1p__(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_A_create_1p__(JNIEnv *, jclass)");
  return gcreate<ttrait_myjapi_A_p>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_A_create_1p__I(JNIEnv *env, jclass cls,
                                                     jint p0) {
  TRACE("jobject Java_myjapi_A_create_1p__I(JNIEnv *, jclass, jint)");
  return gcreate<ttrait_myjapi_A_p, ttrait_int32>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_A_delete_1r(JNIEnv *env, jclass cls,
                                               jobject p0) {
  TRACE("void Java_myjapi_A_delete_1r(JNIEnv *, jclass, jobject)");
  gdelete<ttrait_myjapi_A_r>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_A_delete_1p(JNIEnv *env, jclass cls,
                                               jobject p0) {
  TRACE("void Java_myjapi_A_delete_1p(JNIEnv *, jclass, jobject)");
  gdelete<ttrait_myjapi_A_p>(env, cls, p0);
}

JNIEXPORT jint JNICALL Java_myjapi_A_f0s(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_A_f0s(JNIEnv *, jclass)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_int32, &A::f0s>(env, cls);
#else
  return gcall_fr<ttrait_int32, &MyApiWrapper::A__f0s>(env, cls);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_f0n(JNIEnv *env, jclass cls, jobject obj) {
  TRACE("jint Java_myjapi_A_f0n(JNIEnv *, jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  (void)cls;
  return gcall_mfr<ttrait_myjapi_A_ct, ttrait_int32, &A::f0n>(env, obj);
#else
  (void)cls;
  return gcall_fr<ttrait_int32, ttrait_myjapi_A_cr, &MyApiWrapper::A__f0n>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_f0v(JNIEnv *env, jobject obj) {
  TRACE("jint Java_myjapi_A_f0v(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_A_ct, ttrait_int32, &A::f0v>(env, obj);
#else
  return gcall_fr<ttrait_int32, ttrait_myjapi_A_cr, &MyApiWrapper::A__f0v>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_d0sc(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_A_d0sc(JNIEnv *, jclass)");
  return gget<ttrait_int32_c, A::d0sc>(env, cls);
}

JNIEXPORT jint JNICALL Java_myjapi_A_d0s__(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_A_d0s__(JNIEnv *, jclass)");
  return gget<ttrait_int32, A::d0s>(env, cls);
}

JNIEXPORT void JNICALL Java_myjapi_A_d0s__I(JNIEnv *env, jclass cls, jint p0) {
  TRACE("void Java_myjapi_A_d0s__I(JNIEnv *, jclass, jint)");
  gset<ttrait_int32, A::d0s>(env, cls, p0);
}

JNIEXPORT jint JNICALL Java_myjapi_A_d0c(JNIEnv *env, jobject obj) {
  TRACE("jint Java_myjapi_A_d0c(JNIEnv *, jobject)");
  return gget<ttrait_myjapi_A_t, ttrait_int32_c, &A::d0c>(env, obj);
}

JNIEXPORT jint JNICALL Java_myjapi_A_d0__(JNIEnv *env, jobject obj) {
  TRACE("jint Java_myjapi_A_d0__(JNIEnv *, jobject)");
  return gget<ttrait_myjapi_A_t, ttrait_int32, &A::d0>(env, obj);
}

JNIEXPORT void JNICALL Java_myjapi_A_d0__I(JNIEnv *env, jobject obj, jint p0) {
  TRACE("void Java_myjapi_A_d0__I(JNIEnv *, jobject, jint)");
  return gset<ttrait_myjapi_A_t, ttrait_int32, &A::d0>(env, obj, p0);
}

JNIEXPORT jobject JNICALL Java_myjapi_A_newB0(JNIEnv *env, jobject obj) {
  TRACE("jobject Java_myjapi_A_newB0(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_A_ct, ttrait_myjapi_B0_p, &A::newB0>(env, obj);
#else
  return gcall_fr<ttrait_myjapi_B0_p, ttrait_myjapi_A_cr,
                  &MyApiWrapper::A__newB0>(env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_A_newB1(JNIEnv *env, jobject obj) {
  TRACE("jobject Java_myjapi_A_newB1(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_A_ct, ttrait_myjapi_B1_p, &A::newB1>(env, obj);
#else
  return gcall_fr<ttrait_myjapi_B1_p, ttrait_myjapi_A_cr,
                  &MyApiWrapper::A__newB1>(env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_del__Lmyjapi_B0_2(JNIEnv *env, jobject obj,
                                                       jobject p0) {
  TRACE("Java_myjapi_A_del__Lmyjapi_B0_2(JNIEnv *, jobject, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_A_t, ttrait_myjapi_B0_r, &A::del>(env, obj, p0);
#else
  gcall_fv<ttrait_myjapi_A_r, ttrait_myjapi_B0_r, &MyApiWrapper::A__del__0>(
      env, NULL, obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_del__Lmyjapi_B1_2(JNIEnv *env, jobject obj,
                                                       jobject p0) {
  TRACE("Java_myjapi_A_del__Lmyjapi_B1_2(JNIEnv *, jobject, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_A_t, ttrait_myjapi_B1_r, &A::del>(env, obj, p0);
#else
  gcall_fv<ttrait_myjapi_A_r, ttrait_myjapi_B1_r, &MyApiWrapper::A__del__1>(
      env, NULL, obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_A_deliver_1ptr(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_A_deliver_1ptr(JNIEnv *, jclass)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_myjapi_A_p, &A::deliver_ptr>(env, cls);
#else
  return gcall_fr<ttrait_myjapi_A_p, &MyApiWrapper::A__deliver_ptr>(env, cls);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_A_deliver_1null_1ptr(JNIEnv *env,
                                                           jclass cls) {
  TRACE("jobject Java_myjapi_A_deliver_1null_1ptr(JNIEnv *, jclass)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_myjapi_A_p, &A::deliver_null_ptr>(env, cls);
#else
  return gcall_fr<ttrait_myjapi_A_p, &MyApiWrapper::A__deliver_null_ptr>(env,
                                                                         cls);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_A_deliver_1ref(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_A_deliver_1ref(JNIEnv *, jclass)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_myjapi_A_r, &A::deliver_ref>(env, cls);
#else
  return gcall_fr<ttrait_myjapi_A_r, &MyApiWrapper::A__deliver_ref>(env, cls);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_take_1ptr(JNIEnv *env, jclass cls,
                                               jobject p0) {
  TRACE("void Java_myjapi_A_take_1ptr(JNIEnv *, jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_fv<ttrait_myjapi_A_p, &A::take_ptr>(env, cls, p0);
#else
  gcall_fv<ttrait_myjapi_A_p, &MyApiWrapper::A__take_ptr>(env, cls, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_take_1null_1ptr(JNIEnv *env, jclass cls,
                                                     jobject p0) {
  TRACE("void Java_myjapi_A_take_1null_1ptr(JNIEnv *, jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_fv<ttrait_myjapi_A_p, &A::take_null_ptr>(env, cls, p0);
#else
  gcall_fv<ttrait_myjapi_A_p, &MyApiWrapper::A__take_null_ptr>(env, cls, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_take_1ref(JNIEnv *env, jclass cls,
                                               jobject p0) {
  TRACE("void Java_myjapi_A_take_1ref(JNIEnv *, jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_fv<ttrait_myjapi_A_r, &A::take_ref>(env, cls, p0);
#else
  gcall_fv<ttrait_myjapi_A_r, &MyApiWrapper::A__take_ref>(env, cls, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_print(JNIEnv *env, jclass cls,
                                           jobject p0) {
  TRACE("void Java_myjapi_A_print(JNIEnv *, jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_fv<ttrait_myjapi_A_p, &A::print>(env, cls, p0);
#else
  gcall_fv<ttrait_myjapi_A_p, &MyApiWrapper::A__print>(env, cls, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_A_h0(JNIEnv *env, jclass cls) {
  TRACE("void Java_myjapi_A_h0(JNIEnv *, jclass)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_fv<&h0>(env, cls);
#else
  gcall_fv<&MyApiWrapper::h0>(env, cls);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_h1(JNIEnv *env, jclass cls, jbyte p0) {
  TRACE("void Java_myjapi_A_h1(JNIEnv *, jclass, jbyte)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_fv<ttrait_int8, &h1>(env, cls, p0);
#else
  gcall_fv<ttrait_int8, &MyApiWrapper::h1>(env, cls, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_h2(JNIEnv *env, jclass cls, jbyte p0,
                                        jshort p1) {
  TRACE("void Java_myjapi_A_h2(JNIEnv *, jclass, jbyte, jshort)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_fv<ttrait_int8, ttrait_int16, &h2>(env, cls, p0, p1);
#else
  gcall_fv<ttrait_int8, ttrait_int16, &MyApiWrapper::h2>(env, cls, p0, p1);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_h3(JNIEnv *env, jclass cls, jbyte p0,
                                        jshort p1, jint p2) {
  TRACE("void Java_myjapi_A_h3(JNIEnv *, jclass, jbyte, jshort, jint)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_fv<ttrait_int8, ttrait_int16, ttrait_int32, &h3>(env, cls, p0, p1, p2);
#else
  gcall_fv<ttrait_int8, ttrait_int16, ttrait_int32, &MyApiWrapper::h3>(
      env, cls, p0, p1, p2);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_h0r(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_A_h0r(JNIEnv *, jclass)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_int32, &h0r>(env, cls);
#else
  return gcall_fr<ttrait_int32, &MyApiWrapper::h0r>(env, cls);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_h1r(JNIEnv *env, jclass cls, jbyte p0) {
  TRACE("jint Java_myjapi_A_h1r(JNIEnv *, jclass, jbyte)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_int32, ttrait_int8, &h1r>(env, cls, p0);
#else
  return gcall_fr<ttrait_int32, ttrait_int8, &MyApiWrapper::h1r>(env, cls, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_h2r(JNIEnv *env, jclass cls, jbyte p0,
                                         jshort p1) {
  TRACE("jint Java_myjapi_A_h2r(JNIEnv *, jclass, jbyte, jshort)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_int32, ttrait_int8, ttrait_int16, &h2r>(env, cls, p0,
                                                                 p1);
#else
  return gcall_fr<ttrait_int32, ttrait_int8, ttrait_int16, &MyApiWrapper::h2r>(
      env, cls, p0, p1);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_h3r(JNIEnv *env, jclass cls, jbyte p0,
                                         jshort p1, jint p2) {
  TRACE("jint Java_myjapi_A_h3r(JNIEnv *, jclass, jbyte, jshort, jint)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_int32, ttrait_int8, ttrait_int16, ttrait_int32, &h3r>(
      env, cls, p0, p1, p2);
#else
  return gcall_fr<ttrait_int32, ttrait_int8, ttrait_int16, ttrait_int32,
                  &MyApiWrapper::h3r>(env, cls, p0, p1, p2);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_A_g0c(JNIEnv *env, jobject obj) {
  TRACE("void Java_myjapi_A_g0c(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_A_ct, &A::g0c>(env, obj);
#else
  gcall_fv<ttrait_myjapi_A_cr, &MyApiWrapper::A__g0c>(env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_g1c(JNIEnv *env, jobject obj, jbyte p0) {
  TRACE("void Java_myjapi_A_g1c(JNIEnv *, jobject, jbyte)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_A_ct, ttrait_int8, &A::g1c>(env, obj, p0);
#else
  gcall_fv<ttrait_myjapi_A_cr, ttrait_int8, &MyApiWrapper::A__g1c>(env, NULL,
                                                                   obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_g2c(JNIEnv *env, jobject obj, jbyte p0,
                                         jshort p1) {
  TRACE("void Java_myjapi_A_g2c(JNIEnv *, jobject, jbyte, jshort)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_A_ct, ttrait_int8, ttrait_int16, &A::g2c>(env, obj,
                                                                    p0, p1);
#else
  gcall_fv<ttrait_myjapi_A_cr, ttrait_int8, ttrait_int16,
           &MyApiWrapper::A__g2c>(env, NULL, obj, p0, p1);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_g3c(JNIEnv *env, jobject obj, jbyte p0,
                                         jshort p1, jint p2) {
  TRACE("void Java_myjapi_A_g3c(JNIEnv *, jobject, jbyte, jshort, jint)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_A_ct, ttrait_int8, ttrait_int16, ttrait_int32,
            &A::g3c>(env, obj, p0, p1, p2);
#else
  gcall_fv<ttrait_myjapi_A_cr, ttrait_int8, ttrait_int16, ttrait_int32,
           &MyApiWrapper::A__g3c>(env, NULL, obj, p0, p1, p2);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_g0(JNIEnv *env, jobject obj) {
  TRACE("void Java_myjapi_A_g0(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_A_t, &A::g0>(env, obj);
#else
  gcall_fv<ttrait_myjapi_A_r, &MyApiWrapper::A__g0>(env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_g1(JNIEnv *env, jobject obj, jbyte p0) {
  TRACE("void Java_myjapi_A_g1(JNIEnv *, jobject, jbyte)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_A_t, ttrait_int8, &A::g1>(env, obj, p0);
#else
  gcall_fv<ttrait_myjapi_A_r, ttrait_int8, &MyApiWrapper::A__g1>(env, NULL, obj,
                                                                 p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_g2(JNIEnv *env, jobject obj, jbyte p0,
                                        jshort p1) {
  TRACE("void Java_myjapi_A_g2(JNIEnv *, jobject, jbyte, jshort)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_A_t, ttrait_int8, ttrait_int16, &A::g2>(env, obj, p0,
                                                                  p1);
#else
  gcall_fv<ttrait_myjapi_A_r, ttrait_int8, ttrait_int16, &MyApiWrapper::A__g2>(
      env, NULL, obj, p0, p1);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_A_g3(JNIEnv *env, jobject obj, jbyte p0,
                                        jshort p1, jint p2) {
  TRACE("void Java_myjapi_A_g3(JNIEnv *, jobject, jbyte, jshort, jint)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_A_t, ttrait_int8, ttrait_int16, ttrait_int32, &A::g3>(
      env, obj, p0, p1, p2);
#else
  gcall_fv<ttrait_myjapi_A_r, ttrait_int8, ttrait_int16, ttrait_int32,
           &MyApiWrapper::A__g3>(env, NULL, obj, p0, p1, p2);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_g0rc(JNIEnv *env, jobject obj) {
  TRACE("jint Java_myjapi_A_g0rc(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_A_ct, ttrait_int32, &A::g0rc>(env, obj);
#else
  return gcall_fr<ttrait_int32, ttrait_myjapi_A_cr, &MyApiWrapper::A__g0rc>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_g1rc(JNIEnv *env, jobject obj, jbyte p0) {
  TRACE("jint Java_myjapi_A_g1rc(JNIEnv *, jobject, jbyte)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_A_ct, ttrait_int32, ttrait_int8, &A::g1rc>(
      env, obj, p0);
#else
  return gcall_fr<ttrait_int32, ttrait_myjapi_A_cr, ttrait_int8,
                  &MyApiWrapper::A__g1rc>(env, NULL, obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_g2rc(JNIEnv *env, jobject obj, jbyte p0,
                                          jshort p1) {
  TRACE("jint Java_myjapi_A_g2rc(JNIEnv *, jobject, jbyte, jshort)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_A_ct, ttrait_int32, ttrait_int8, ttrait_int16,
                   &A::g2rc>(env, obj, p0, p1);
#else
  return gcall_fr<ttrait_int32, ttrait_myjapi_A_cr, ttrait_int8, ttrait_int16,
                  &MyApiWrapper::A__g2rc>(env, NULL, obj, p0, p1);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_g3rc(JNIEnv *env, jobject obj, jbyte p0,
                                          jshort p1, jint p2) {
  TRACE("jint Java_myjapi_A_g3rc(JNIEnv *, jobject, jbyte, jshort, jint)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_A_ct, ttrait_int32, ttrait_int8, ttrait_int16,
                   ttrait_int32, &A::g3rc>(env, obj, p0, p1, p2);
#else
  return gcall_fr<ttrait_int32, ttrait_myjapi_A_cr, ttrait_int8, ttrait_int16,
                  ttrait_int32, &MyApiWrapper::A__g3rc>(env, NULL, obj, p0, p1,
                                                        p2);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_g0r(JNIEnv *env, jobject obj) {
  TRACE("jint Java_myjapi_A_g0r(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_A_t, ttrait_int32, &A::g0r>(env, obj);
#else
  return gcall_fr<ttrait_int32, ttrait_myjapi_A_r, &MyApiWrapper::A__g0r>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_g1r(JNIEnv *env, jobject obj, jbyte p0) {
  TRACE("jint Java_myjapi_A_g1r(JNIEnv *, jobject, jbyte)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_A_t, ttrait_int32, ttrait_int8, &A::g1r>(
      env, obj, p0);
#else
  return gcall_fr<ttrait_int32, ttrait_myjapi_A_r, ttrait_int8,
                  &MyApiWrapper::A__g1r>(env, NULL, obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_g2r(JNIEnv *env, jobject obj, jbyte p0,
                                         jshort p1) {
  TRACE("jint Java_myjapi_A_g2r(JNIEnv *, jobject, jbyte, jshort)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_A_t, ttrait_int32, ttrait_int8, ttrait_int16,
                   &A::g2r>(env, obj, p0, p1);
#else
  return gcall_fr<ttrait_int32, ttrait_myjapi_A_r, ttrait_int8, ttrait_int16,
                  &MyApiWrapper::A__g2r>(env, NULL, obj, p0, p1);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_A_g3r(JNIEnv *env, jobject obj, jbyte p0,
                                         jshort p1, jint p2) {
  TRACE("jint Java_myjapi_A_g3r(JNIEnv *, jobject, jbyte, jshort, jint)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_A_t, ttrait_int32, ttrait_int8, ttrait_int16,
                   ttrait_int32, &A::g3r>(env, obj, p0, p1, p2);
#else
  return gcall_fr<ttrait_int32, ttrait_myjapi_A_r, ttrait_int8, ttrait_int16,
                  ttrait_int32, &MyApiWrapper::A__g3r>(env, NULL, obj, p0, p1,
                                                       p2);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

// ---------------------------------------------------------------------------

// #include "myjapi_B0.h"
// #include "myjapi_B1.h"

JNIEXPORT jint JNICALL Java_myjapi_B0_f0s(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_B0_f0s(JNIEnv *, jclass)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_int32, &B0::f0s>(env, cls);
#else
  return gcall_fr<ttrait_int32, &MyApiWrapper::B0__f0s>(env, cls);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_B0_f0n(JNIEnv *env, jclass cls,
                                          jobject obj) {
  TRACE("jint Java_myjapi_B0_f0n(JNIEnv *, jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  (void)cls;
  return gcall_mfr<ttrait_myjapi_B0_ct, ttrait_int32, &B0::f0n>(env, obj);
#else
  (void)cls;
  return gcall_fr<ttrait_int32, ttrait_myjapi_B0_cr, &MyApiWrapper::B0__f0n>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_B0_f0v(JNIEnv *env, jobject obj) {
  TRACE("jint Java_myjapi_B0_f0v(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_B0_ct, ttrait_int32, &B0::f0v>(env, obj);
#else
  return gcall_fr<ttrait_int32, ttrait_myjapi_B0_cr, &MyApiWrapper::B0__f0v>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_B1_f0s(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_B1_f0s(JNIEnv *, jclass)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_int32, &B1::f0s>(env, cls);
#else
  return gcall_fr<ttrait_int32, &MyApiWrapper::B1__f0s>(env, cls);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_B1_f0n(JNIEnv *env, jclass cls,
                                          jobject obj) {
  TRACE("jint Java_myjapi_B1_f0n(JNIEnv *, jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  (void)cls;
  return gcall_mfr<ttrait_myjapi_B1_ct, ttrait_int32, &B1::f0n>(env, obj);
#else
  (void)cls;
  return gcall_fr<ttrait_int32, ttrait_myjapi_B1_cr, &MyApiWrapper::B1__f0n>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_B1_f0v(JNIEnv *env, jobject obj) {
  TRACE("jint Java_myjapi_B1_f0v(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_B1_ct, ttrait_int32, &B1::f0v>(env, obj);
#else
  return gcall_fr<ttrait_int32, ttrait_myjapi_B1_cr, &MyApiWrapper::B1__f0v>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_B0_d0sc(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_B0_d0sc(JNIEnv *, jclass)");
  return gget<ttrait_int32_c, B0::d0sc>(env, cls);
}

JNIEXPORT jint JNICALL Java_myjapi_B0_d0s__(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_B0_d0s__(JNIEnv *, jclass)");
  return gget<ttrait_int32, B0::d0s>(env, cls);
}

JNIEXPORT void JNICALL Java_myjapi_B0_d0s__I(JNIEnv *env, jclass cls, jint p0) {
  TRACE("void Java_myjapi_B0_d0s__I(JNIEnv *, jclass, jint)");
  gset<ttrait_int32, B0::d0s>(env, cls, p0);
}

JNIEXPORT jint JNICALL Java_myjapi_B0_d0c(JNIEnv *env, jclass cls,
                                          jobject obj) {
  TRACE("jint Java_myjapi_B0_d0c(JNIEnv *, jclass, jobject)");
  (void)cls;
  return gget<ttrait_myjapi_B0_t, ttrait_int32_c, &B0::d0c>(env, obj);
}

JNIEXPORT jint JNICALL Java_myjapi_B0_d0__Lmyjapi_B0_2(JNIEnv *env, jclass cls,
                                                       jobject obj) {
  TRACE("jint Java_myjapi_B0_d0__Lmyjapi_B0_2(JNIEnv *, jclass, jobject)");
  (void)cls;
  return gget<ttrait_myjapi_B0_t, ttrait_int32, &B0::d0>(env, obj);
}

JNIEXPORT void JNICALL Java_myjapi_B0_d0__Lmyjapi_B0_2I(JNIEnv *env, jclass cls,
                                                        jobject obj, jint p0) {
  TRACE(
      "void Java_myjapi_B0_d0__Lmyjapi_B0_2I(JNIEnv *, jclass, jobject, jint)");
  (void)cls;
  return gset<ttrait_myjapi_B0_t, ttrait_int32, &B0::d0>(env, obj, p0);
}

JNIEXPORT jint JNICALL Java_myjapi_B1_d0sc(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_B1_d0sc(JNIEnv *, jclass)");
  return gget<ttrait_int32_c, B1::d0sc>(env, cls);
}

JNIEXPORT jint JNICALL Java_myjapi_B1_d0s__(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_B1_d0s__(JNIEnv *, jclass)");
  return gget<ttrait_int32, B1::d0s>(env, cls);
}

JNIEXPORT void JNICALL Java_myjapi_B1_d0s__I(JNIEnv *env, jclass cls, jint p0) {
  TRACE("void Java_myjapi_B1_d0s__I(JNIEnv *, jclass, jint)");
  gset<ttrait_int32, B1::d0s>(env, cls, p0);
}

JNIEXPORT jint JNICALL Java_myjapi_B1_d0c(JNIEnv *env, jclass cls,
                                          jobject obj) {
  TRACE("jint Java_myjapi_B1_d0c(JNIEnv *, jclass, jobject)");
  (void)cls;
  return gget<ttrait_myjapi_B1_t, ttrait_int32_c, &B1::d0c>(env, obj);
}

JNIEXPORT jint JNICALL Java_myjapi_B1_d0__Lmyjapi_B1_2(JNIEnv *env, jclass cls,
                                                       jobject obj) {
  TRACE("jint Java_myjapi_B1_d0__Lmyjapi_B1_2(JNIEnv *, jclass, jobject)");
  (void)cls;
  return gget<ttrait_myjapi_B1_t, ttrait_int32, &B1::d0>(env, obj);
}

JNIEXPORT void JNICALL Java_myjapi_B1_d0__Lmyjapi_B1_2I(JNIEnv *env, jclass cls,
                                                        jobject obj, jint p0) {
  TRACE(
      "void Java_myjapi_B1_d0__Lmyjapi_B1_2I(JNIEnv *, jclass, jobject, jint)");
  (void)cls;
  return gset<ttrait_myjapi_B1_t, ttrait_int32, &B1::d0>(env, obj, p0);
}

// ---------------------------------------------------------------------------

// #include "myjapi_CI_C0.h"
// #include "myjapi_CI_C0C.h"

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C0_cc(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_CI_00024C0_cc(JNIEnv *, jclass)");
  return gget<ttrait_myjapi_CI_C0_cpc, C0::cc>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C0_c(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_CI_00024C0_c(JNIEnv *, jclass)");
  return gget<ttrait_myjapi_CI_C0_pc, C0::c>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C0_create(JNIEnv *env,
                                                        jclass cls) {
  TRACE("Java_myjapi_CI_00024C0_create(JNIEnv *, jclass)");
  return gcreate<ttrait_myjapi_CI_C0_r>(env, cls);
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C0_delete(JNIEnv *env, jclass cls,
                                                     jobject p0) {
  TRACE("Java_myjapi_CI_00024C0_delete(JNIEnv *, jclass, jobject)");
  gdelete<ttrait_myjapi_CI_C0_r>(env, cls, p0);
}

JNIEXPORT jobject JNICALL
Java_myjapi_CI_00024C0_pass__Lmyjapi_CI_00024C0Array_2(JNIEnv *env, jclass cls,
                                                       jobject p0) {
  TRACE(
      "Java_myjapi_CI_00024C0_pass__Lmyjapi_CI_00024C0Array_2(JNIEnv *, "
      "jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_myjapi_CI_C0Array_p, ttrait_myjapi_CI_C0Array_p,
                  &C0::pass>(env, cls, p0);
#else
  return gcall_fr<ttrait_myjapi_CI_C0Array_p, ttrait_myjapi_CI_C0Array_p,
                  &MyApiWrapper::C0__pass__0>(env, cls, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL
Java_myjapi_CI_00024C0_pass__Lmyjapi_CI_00024C0CArray_2(JNIEnv *env, jclass cls,
                                                        jobject p0) {
  TRACE(
      "Java_myjapi_CI_00024C0_pass__Lmyjapi_CI_00024C0CArray_2(JNIEnv *, "
      "jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_myjapi_CI_C0Array_cp, ttrait_myjapi_CI_C0Array_cp,
                  &C0::pass>(env, cls, p0);
#else
  return gcall_fr<ttrait_myjapi_CI_C0Array_cp, ttrait_myjapi_CI_C0Array_cp,
                  &MyApiWrapper::C0__pass__1>(env, cls, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jlong JNICALL Java_myjapi_CI_00024C0_hash(JNIEnv *env, jclass cls,
                                                    jobject p0, jint p1) {
  TRACE("Java_myjapi_CI_00024C0_hash(JNIEnv *, jclass, jobject, jint)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_int64, ttrait_myjapi_CI_C0Array_cp, ttrait_int32,
                  &C0::hash>(env, cls, p0, p1);
#else
  return gcall_fr<ttrait_int64, ttrait_myjapi_CI_C0Array_cp, ttrait_int32,
                  &MyApiWrapper::C0__hash>(env, cls, p0, p1);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jlong JNICALL Java_myjapi_CI_00024C0_id(JNIEnv *env, jobject obj) {
  TRACE("Java_myjapi_CI_00024C0_id(JNIEnv *, jobject)");
  return gget<ttrait_myjapi_CI_C0_t, ttrait_int64_c, &C0::id>(env, obj);
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C0_check(JNIEnv *env, jobject obj,
                                                    jlong p0) {
  TRACE("Java_myjapi_CI_00024C0_check(JNIEnv *, jobject, jlong)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_CI_C0_ct, ttrait_int64, &C0::check>(env, obj, p0);
#else
  gcall_fv<ttrait_myjapi_CI_C0_cr, ttrait_int64, &MyApiWrapper::C0__check>(
      env, NULL, obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C0_print(JNIEnv *env, jobject obj) {
  TRACE("Java_myjapi_CI_00024C0_print(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_CI_C0_ct, &C0::print>(env, obj);
#else
  gcall_fv<ttrait_myjapi_CI_C0_cr, &MyApiWrapper::C0__print>(env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C0_deliver_1C0Cp(JNIEnv *env,
                                                               jobject obj) {
  TRACE("jobject Java_myjapi_CI_00024C0_deliver_1C0Cp(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_CI_C0_ct, ttrait_myjapi_CI_C0_cp,
                   &C0::deliver_C0Cp>(env, obj);
#else
  return gcall_fr<ttrait_myjapi_CI_C0_cp, ttrait_myjapi_CI_C0_cr,
                  &MyApiWrapper::C0__deliver_C0Cp>(env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C0_deliver_1C0Cr(JNIEnv *env,
                                                               jobject obj) {
  TRACE("jobject Java_myjapi_CI_00024C0_deliver_1C0Cr(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_CI_C0_ct, ttrait_myjapi_CI_C0_cr,
                   &C0::deliver_C0Cr>(env, obj);
#else
  return gcall_fr<ttrait_myjapi_CI_C0_cr, ttrait_myjapi_CI_C0_cr,
                  &MyApiWrapper::C0__deliver_C0Cr>(env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C0_take_1C0Cp(JNIEnv *env,
                                                         jobject obj,
                                                         jobject p0) {
  TRACE("void Java_myjapi_CI_00024C0_take_1C0Cp(JNIEnv *, jobject, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_CI_C0_ct, ttrait_myjapi_CI_C0_cp, &C0::take_C0Cp>(
      env, obj, p0);
#else
  gcall_fv<ttrait_myjapi_CI_C0_cr, ttrait_myjapi_CI_C0_cp,
           &MyApiWrapper::C0__take_C0Cp>(env, NULL, obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C0_take_1C0Cr(JNIEnv *env,
                                                         jobject obj,
                                                         jobject p0) {
  TRACE("void Java_myjapi_CI_00024C0_take_1C0Cr(JNIEnv *, jobject, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_CI_C0_ct, ttrait_myjapi_CI_C0_cr, &C0::take_C0Cr>(
      env, obj, p0);
#else
  gcall_fv<ttrait_myjapi_CI_C0_cr, ttrait_myjapi_CI_C0_cr,
           &MyApiWrapper::C0__take_C0Cr>(env, NULL, obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C0_deliver_1C0p(JNIEnv *env,
                                                              jobject obj) {
  TRACE("jobject Java_myjapi_CI_00024C0_deliver_1C0p(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_CI_C0_t, ttrait_myjapi_CI_C0_p,
                   &C0::deliver_C0p>(env, obj);
#else
  return gcall_fr<ttrait_myjapi_CI_C0_p, ttrait_myjapi_CI_C0_r,
                  &MyApiWrapper::C0__deliver_C0p>(env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C0_deliver_1C0r(JNIEnv *env,
                                                              jobject obj) {
  TRACE("jobject Java_myjapi_CI_00024C0_deliver_1C0r(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_CI_C0_t, ttrait_myjapi_CI_C0_r,
                   &C0::deliver_C0r>(env, obj);
#else
  return gcall_fr<ttrait_myjapi_CI_C0_r, ttrait_myjapi_CI_C0_r,
                  &MyApiWrapper::C0__deliver_C0r>(env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C0_take_1C0p(JNIEnv *env,
                                                        jobject obj,
                                                        jobject p0) {
  TRACE("void Java_myjapi_CI_00024C0_take_1C0p(JNIEnv *, jobject, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_CI_C0_t, ttrait_myjapi_CI_C0_p, &C0::take_C0p>(
      env, obj, p0);
#else
  gcall_fv<ttrait_myjapi_CI_C0_r, ttrait_myjapi_CI_C0_p,
           &MyApiWrapper::C0__take_C0p>(env, NULL, obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C0_take_1C0r(JNIEnv *env,
                                                        jobject obj,
                                                        jobject p0) {
  TRACE("void Java_myjapi_CI_00024C0_take_1C0r(JNIEnv *, jobject, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_CI_C0_t, ttrait_myjapi_CI_C0_r, &C0::take_C0r>(
      env, obj, p0);
#else
  gcall_fv<ttrait_myjapi_CI_C0_r, ttrait_myjapi_CI_C0_r,
           &MyApiWrapper::C0__take_C0r>(env, NULL, obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

// ---------------------------------------------------------------------------

// #include "myjapi_CI_C1.h"
// #include "myjapi_CI_C1C.h"

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C1_cc(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_CI_00024C1_cc(JNIEnv *, jclass)");
  return gget<ttrait_myjapi_CI_C1_cpc, C1::cc>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C1_c(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_CI_00024C1_c(JNIEnv *, jclass)");
  return gget<ttrait_myjapi_CI_C1_pc, C1::c>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C1_create(JNIEnv *env,
                                                        jclass cls) {
  TRACE("Java_myjapi_CI_00024C1_create(JNIEnv *, jclass)");
  return gcreate<ttrait_myjapi_CI_C0_r>(env, cls);
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C1_delete(JNIEnv *env, jclass cls,
                                                     jobject p0) {
  TRACE("Java_myjapi_CI_00024C1_delete(JNIEnv *, jclass, jobject)");
  gdelete<ttrait_myjapi_CI_C0_r>(env, cls, p0);
}

JNIEXPORT jobject JNICALL
Java_myjapi_CI_00024C1_pass__Lmyjapi_CI_00024C1Array_2(JNIEnv *env, jclass cls,
                                                       jobject p0) {
  TRACE(
      "Java_myjapi_CI_00024C1_pass__Lmyjapi_CI_00024C1Array_2(JNIEnv *, "
      "jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_myjapi_CI_C1Array_p, ttrait_myjapi_CI_C1Array_p,
                  &C1::pass>(env, cls, p0);
#else
  return gcall_fr<ttrait_myjapi_CI_C1Array_p, ttrait_myjapi_CI_C1Array_p,
                  &MyApiWrapper::C1__pass__0>(env, cls, p0);
  return 0;
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL
Java_myjapi_CI_00024C1_pass__Lmyjapi_CI_00024C1CArray_2(JNIEnv *env, jclass cls,
                                                        jobject p0) {
  TRACE(
      "Java_myjapi_CI_00024C1_pass__Lmyjapi_CI_00024C1CArray_2(JNIEnv *, "
      "jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_myjapi_CI_C1Array_cp, ttrait_myjapi_CI_C1Array_cp,
                  &C1::pass>(env, cls, p0);
#else
  return gcall_fr<ttrait_myjapi_CI_C1Array_cp, ttrait_myjapi_CI_C1Array_cp,
                  &MyApiWrapper::C1__pass__1>(env, cls, p0);
  return 0;
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jlong JNICALL Java_myjapi_CI_00024C1_hash(JNIEnv *env, jclass cls,
                                                    jobject p0, jint p1) {
  TRACE("Java_myjapi_CI_00024C1_hash(JNIEnv *, jclass, jobject, jint)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_int64, ttrait_myjapi_CI_C1Array_cp, ttrait_int32,
                  &C1::hash>(env, cls, p0, p1);
#else
  return gcall_fr<ttrait_int64, ttrait_myjapi_CI_C1Array_cp, ttrait_int32,
                  &MyApiWrapper::C1__hash>(env, cls, p0, p1);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C1_deliver_1C1Cp(JNIEnv *env,
                                                               jobject obj) {
  TRACE("jobject Java_myjapi_CI_00024C1_deliver_1C1Cp(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_CI_C1_ct, ttrait_myjapi_CI_C1_cp,
                   &C1::deliver_C1Cp>(env, obj);
#else
  return gcall_fr<ttrait_myjapi_CI_C1_cp, ttrait_myjapi_CI_C1_cr,
                  &MyApiWrapper::C1__deliver_C1Cp>(env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C1_deliver_1C1Cr(JNIEnv *env,
                                                               jobject obj) {
  TRACE("jobject Java_myjapi_CI_00024C1_deliver_1C1Cr(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_CI_C1_ct, ttrait_myjapi_CI_C1_cr,
                   &C1::deliver_C1Cr>(env, obj);
#else
  return gcall_fr<ttrait_myjapi_CI_C1_cr, ttrait_myjapi_CI_C1_cr,
                  &MyApiWrapper::C1__deliver_C1Cr>(env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C1_take_1C1Cp(JNIEnv *env,
                                                         jobject obj,
                                                         jobject p0) {
  TRACE("void Java_myjapi_CI_00024C1_take_1C1Cp(JNIEnv *, jobject, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_CI_C1_ct, ttrait_myjapi_CI_C1_cp, &C1::take_C1Cp>(
      env, obj, p0);
#else
  gcall_fv<ttrait_myjapi_CI_C1_cr, ttrait_myjapi_CI_C1_cp,
           &MyApiWrapper::C1__take_C1Cp>(env, NULL, obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C1_take_1C1Cr(JNIEnv *env,
                                                         jobject obj,
                                                         jobject p0) {
  TRACE("void Java_myjapi_CI_00024C1_take_1C1Cr(JNIEnv *, jobject, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_CI_C1_ct, ttrait_myjapi_CI_C1_cr, &C1::take_C1Cr>(
      env, obj, p0);
#else
  gcall_fv<ttrait_myjapi_CI_C1_cr, ttrait_myjapi_CI_C1_cr,
           &MyApiWrapper::C1__take_C1Cr>(env, NULL, obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C1_deliver_1C1p(JNIEnv *env,
                                                              jobject obj) {
  TRACE("jobject Java_myjapi_CI_00024C1_deliver_1C1p(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_CI_C1_t, ttrait_myjapi_CI_C1_p,
                   &C1::deliver_C1p>(env, obj);
#else
  return gcall_fr<ttrait_myjapi_CI_C1_p, ttrait_myjapi_CI_C1_r,
                  &MyApiWrapper::C1__deliver_C1p>(env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C1_deliver_1C1r(JNIEnv *env,
                                                              jobject obj) {
  TRACE("jobject Java_myjapi_CI_00024C1_deliver_1C1r(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_CI_C1_t, ttrait_myjapi_CI_C1_r,
                   &C1::deliver_C1r>(env, obj);
#else
  return gcall_fr<ttrait_myjapi_CI_C1_r, ttrait_myjapi_CI_C1_r,
                  &MyApiWrapper::C1__deliver_C1r>(env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C1_take_1C1p(JNIEnv *env,
                                                        jobject obj,
                                                        jobject p0) {
  TRACE("void Java_myjapi_CI_00024C1_take_1C1p(JNIEnv *, jobject, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_CI_C1_t, ttrait_myjapi_CI_C1_p, &C1::take_C1p>(
      env, obj, p0);
#else
  gcall_fv<ttrait_myjapi_CI_C1_r, ttrait_myjapi_CI_C1_p,
           &MyApiWrapper::C1__take_C1p>(env, NULL, obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C1_take_1C1r(JNIEnv *env,
                                                        jobject obj,
                                                        jobject p0) {
  TRACE("void Java_myjapi_CI_00024C1_take_1C1r(JNIEnv *, jobject, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_mfv<ttrait_myjapi_CI_C1_t, ttrait_myjapi_CI_C1_r, &C1::take_C1r>(
      env, obj, p0);
#else
  gcall_fv<ttrait_myjapi_CI_C1_r, ttrait_myjapi_CI_C1_r,
           &MyApiWrapper::C1__take_C1r>(env, NULL, obj, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

// ---------------------------------------------------------------------------

// #include "myjapi_CI_C0Array.h"
// #include "myjapi_CI_C0CArray.h"

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C0Array_create(JNIEnv *env,
                                                             jclass cls,
                                                             jint p0) {
  TRACE("Java_myjapi_CI_00024C0Array_create(JNIEnv *, jclass, jint)");
  return gcreateArray<ttrait_myjapi_CI_C0Array_r, ttrait_int32>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C0Array_delete(JNIEnv *env,
                                                          jclass cls,
                                                          jobject p0) {
  TRACE("Java_myjapi_CI_00024C0Array_delete(JNIEnv *, jclass, jobject)");
  gdeleteArray<ttrait_myjapi_CI_C0Array_r>(env, cls, p0);
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C0Array_at(JNIEnv *env,
                                                         jobject obj, jint p0) {
  TRACE("Java_myjapi_CI_00024C0Array_at(JNIEnv *, jobject, jint)");
  return gat<ttrait_myjapi_CI_C0_r, ttrait_myjapi_CI_C0Array_r, ttrait_int32>(
      env, NULL, obj, p0);
}

// ---------------------------------------------------------------------------

// #include "myjapi_CI_C1Array.h"
// #include "myjapi_CI_C1CArray.h"

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C1Array_create(JNIEnv *env,
                                                             jclass cls,
                                                             jint p0) {
  TRACE("Java_myjapi_CI_00024C1Array_create(JNIEnv *, jclass, jint)");
  return gcreateArray<ttrait_myjapi_CI_C1Array_r, ttrait_int32>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_CI_00024C1Array_delete(JNIEnv *env,
                                                          jclass cls,
                                                          jobject p0) {
  TRACE("Java_myjapi_CI_00024C1Array_delete(JNIEnv *, jclass, jobject)");
  gdeleteArray<ttrait_myjapi_CI_C1Array_r>(env, cls, p0);
}

JNIEXPORT jobject JNICALL Java_myjapi_CI_00024C1Array_at(JNIEnv *env,
                                                         jobject obj, jint p0) {
  TRACE("Java_myjapi_CI_00024C1Array_at(JNIEnv *, jobject, jint)");
  return gat<ttrait_myjapi_CI_C1_r, ttrait_myjapi_CI_C1Array_r, ttrait_int32>(
      env, NULL, obj, p0);
}

// ---------------------------------------------------------------------------

// #include "myjapi_D0.h"
// #include "myjapi_D1.h"
// #include "myjapi_D2.h"

JNIEXPORT jint JNICALL Java_myjapi_D0_f_1d0(JNIEnv *env, jobject obj) {
  TRACE("jint Java_myjapi_D0_f_1d0(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_D0_t, ttrait_int, &D0::f_d0>(env, obj);
#else
  return gcall_fr<ttrait_int, ttrait_myjapi_D0_r, &MyApiWrapper::D0__f_d0>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_D0_f_1nv(JNIEnv *env, jclass cls,
                                            jobject obj) {
  TRACE("jint Java_myjapi_D0_f_1nv(JNIEnv *, jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  (void)cls;
  return gcall_mfr<ttrait_myjapi_D0_t, ttrait_int, &D0::f_nv>(env, obj);
#else
  (void)cls;
  return gcall_fr<ttrait_int, ttrait_myjapi_D0_r, &MyApiWrapper::D0__f_nv>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_D0_f_1v(JNIEnv *env, jobject obj) {
  TRACE("jint Java_myjapi_D0_f_1v(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_D0_t, ttrait_int, &D0::f_v>(env, obj);
#else
  return gcall_fr<ttrait_int, ttrait_myjapi_D0_r, &MyApiWrapper::D0__f_v>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_D0_sub(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_D0_sub(JNIEnv *, jclass)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_myjapi_D1_p, &D0::sub>(env, cls);
#else
  return gcall_fr<ttrait_myjapi_D1_p, &MyApiWrapper::D0__sub>(env, cls);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_D1_f_1d1(JNIEnv *env, jobject obj) {
  TRACE("jint Java_myjapi_D1_f_1d1(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_D1_t, ttrait_int, &D1::f_d1>(env, obj);
#else
  return gcall_fr<ttrait_int, ttrait_myjapi_D1_r, &MyApiWrapper::D1__f_d1>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_D1_f_1nv(JNIEnv *env, jclass cls,
                                            jobject obj) {
  TRACE("jint Java_myjapi_D1_f_1nv(JNIEnv *, jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  (void)cls;
  return gcall_mfr<ttrait_myjapi_D1_t, ttrait_int, &D1::f_nv>(env, obj);
#else
  (void)cls;
  return gcall_fr<ttrait_int, ttrait_myjapi_D1_r, &MyApiWrapper::D1__f_nv>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_D1_f_1v(JNIEnv *env, jobject obj) {
  TRACE("jint Java_myjapi_D1_f_1v(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_D1_t, ttrait_int, &D1::f_v>(env, obj);
#else
  return gcall_fr<ttrait_int, ttrait_myjapi_D1_r, &MyApiWrapper::D1__f_v>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_D1_sub(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_D1_sub(JNIEnv *, jclass)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_myjapi_D1_p, &D1::sub>(env, cls);
#else
  return gcall_fr<ttrait_myjapi_D1_p, &MyApiWrapper::D1__sub>(env, cls);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_D2_f_1d2(JNIEnv *env, jobject obj) {
  TRACE("jint Java_myjapi_D2_f_1d2(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_D2_t, ttrait_int, &D2::f_d2>(env, obj);
#else
  return gcall_fr<ttrait_int, ttrait_myjapi_D2_r, &MyApiWrapper::D2__f_d2>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_D2_f_1nv(JNIEnv *env, jclass cls,
                                            jobject obj) {
  TRACE("jint Java_myjapi_D2_f_1nv(JNIEnv *, jclass, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  (void)cls;
  return gcall_mfr<ttrait_myjapi_D2_t, ttrait_int, &D2::f_nv>(env, obj);
#else
  (void)cls;
  return gcall_fr<ttrait_int, ttrait_myjapi_D2_r, &MyApiWrapper::D2__f_nv>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jint JNICALL Java_myjapi_D2_f_1v(JNIEnv *env, jobject obj) {
  TRACE("jint Java_myjapi_D2_f_1v(JNIEnv *, jobject)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_mfr<ttrait_myjapi_D2_t, ttrait_int, &D2::f_v>(env, obj);
#else
  return gcall_fr<ttrait_int, ttrait_myjapi_D2_r, &MyApiWrapper::D2__f_v>(
      env, NULL, obj);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT jobject JNICALL Java_myjapi_D2_sub(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_D2_sub(JNIEnv *, jclass)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_myjapi_D1_p, &D2::sub>(env, cls);
#else
  return gcall_fr<ttrait_myjapi_D1_p, &MyApiWrapper::D2__sub>(env, cls);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

// ---------------------------------------------------------------------------

JNIEXPORT jint JNICALL Java_myjapi_E_deliver_1EE1(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_E_deliver_1EE1(JNIEnv *, jclass)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  return gcall_fr<ttrait_E_EE_iv, &E::deliver_EE1>(env, cls);
#else
  return gcall_fr<ttrait_E_EE_iv, &MyApiWrapper::E__deliver_EE1>(env, cls);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_E_take_1EE1(JNIEnv *env, jclass cls,
                                               jint p0) {
  TRACE("void Java_myjapi_E_take_1EE1(JNIEnv *, jclass, jint)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_fv<ttrait_E_EE_iv, &E::take_EE1>(env, cls, p0);
#else
  gcall_fv<ttrait_E_EE_iv, &MyApiWrapper::E__take_EE1>(env, cls, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

JNIEXPORT void JNICALL Java_myjapi_E_take_1EE1c(JNIEnv *env, jclass cls,
                                                jint p0) {
  TRACE("void Java_myjapi_E_take_1EE1c(JNIEnv *, jclass, jint)");
#ifndef JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
  gcall_fv<ttrait_E_EE_c_iv, E::take_EE1c>(env, cls, p0);
#else
  gcall_fv<ttrait_E_EE_c_iv, &MyApiWrapper::E__take_EE1c>(env, cls, p0);
#endif  // JTIE_USE_WRAPPED_VARIANT_FOR_FUNCTION
}

// ---------------------------------------------------------------------------

}  // extern "C"

#endif  // myjapi_classes_hpp
