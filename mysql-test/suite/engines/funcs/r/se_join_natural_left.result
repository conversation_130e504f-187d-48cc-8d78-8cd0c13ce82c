DROP TABLE IF EXISTS t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 < 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 < 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 < 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 < 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 < 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 < 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 < 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 < 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 < 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 < 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 < 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 < 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 < 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 < 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 < 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 < 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 < 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 < 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 < 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 < 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 < 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 < 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 < 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 < 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 < 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 < 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 < 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 < 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 < 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 < 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <= 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <= 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <= 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <= 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <= 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <= 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <= 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <= 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <= 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <= 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <= 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <= 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <= 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <= 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <= 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <= 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <= 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <= 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <= 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <= 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <= 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <= 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <= 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <= 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <= 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <= 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <= 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <= 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <= 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <= 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 = 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 = 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 = 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 = 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 = 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 = 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 = 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 = 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 = 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 = 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 = 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 = 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 = 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 = 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 = 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 = 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 = 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 = 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 = 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 = 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 = 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 = 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 = 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 = 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 = 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 = 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 = 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 = 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 = 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 = 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 >= 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 >= 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 >= 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 >= 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 >= 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 >= 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 >= 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 >= 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 >= 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 >= 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 >= 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 >= 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 >= 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 >= 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 >= 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 >= 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 >= 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 >= 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 >= 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 >= 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 >= 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 >= 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 >= 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 >= 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 >= 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 >= 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 >= 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 >= 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 >= 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 >= 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 != 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 != 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 != 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 != 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 != 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 != 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 != 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 != 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 != 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 != 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 != 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 != 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 != 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 != 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 != 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 != 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 != 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 != 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 != 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 != 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 != 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 != 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 != 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 != 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 != 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 != 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 != 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 != 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 != 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 != 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <> 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <> 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <> 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <> 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <> 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <> 5 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <> 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <> 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <> 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <> 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <> 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <> 5 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
6	NULL
7	7
8	NULL
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <> 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <> 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <> 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <> 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <> 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <> 5 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <> 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <> 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <> 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <> 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <> 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <> 5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <> 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <> 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <> 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <> 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <> 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <> 5 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
6	NULL
7	NULL
8	NULL
9	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2 ORDER BY t1.c1,t2.c1;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL LEFT JOIN t2  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t2.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3 ORDER BY t1.c1,t3.c1;
c1	c1
0	NULL
1	1
2	NULL
3	3
4	NULL
5	5
6	NULL
7	7
8	NULL
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL LEFT JOIN t3  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t3.c1;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4 ORDER BY t1.c1,t4.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t4.c1 FROM t1 NATURAL LEFT JOIN t4  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t4.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5 ORDER BY t1.c1,t5.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t5.c1 FROM t1 NATURAL LEFT JOIN t5  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t5.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6 ORDER BY t1.c1,t6.c1;
c1	c1
0	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
SELECT t1.c1,t6.c1 FROM t1 NATURAL LEFT JOIN t6  WHERE t1.c1 <=> 5 ORDER BY t1.c1,t6.c1;
c1	c1
5	NULL
DROP TABLE t1,t2,t3,t4,t5,t6;
