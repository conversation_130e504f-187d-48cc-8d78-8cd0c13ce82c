include/group_replication.inc [rpl_server_count=3]
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection server1]
#
# Setup a new group with three servers.
#

############################################################
# 01. Start server1
[connection server1]
SET SESSION sql_log_bin= 0;
CREATE USER "recovery_user" IDENTIFIED BY "recovery_password";
GRANT REPLICATION SLAVE ON *.* TO "recovery_user";
GRANT GROUP_REPLICATION_STREAM ON *.* TO 'recovery_user'@'%';
FLUSH PRIVILEGES;
Warnings:
Warning	1681	'FLUSH PRIVILEGES' is deprecated and will be removed in a future release.
SET SESSION sql_log_bin= 1;
SET @@GLOBAL.DEBUG= '+d,group_replication_version_with_vcle';
include/start_and_bootstrap_group_replication.inc

############################################################
# 02. Start server2
[connection server2]
SET SESSION sql_log_bin= 0;
CREATE USER "recovery_user" IDENTIFIED BY "recovery_password";
GRANT GROUP_REPLICATION_STREAM ON *.* TO 'recovery_user'@'%';
FLUSH PRIVILEGES;
Warnings:
Warning	1681	'FLUSH PRIVILEGES' is deprecated and will be removed in a future release.
SET SESSION sql_log_bin= 1;
SET @@GLOBAL.DEBUG= '+d,group_replication_version_with_vcle';
include/start_group_replication.inc

############################################################
# 03. Block T1 and T2 before message send after applier prepare
#     on server2.
[connection server2]
SET @debug_save= @@GLOBAL.DEBUG;
SET @@GLOBAL.DEBUG= '+d,group_replication_wait_before_message_send_after_applier_prepare';

############################################################
# 04. Execute T1 and T2 on server1.
[connection server1]
CREATE TABLE test.t1 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB;
[connection server_1_2]
[connection server_1_1]
CREATE TABLE test.t2 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB;
[connection server_1_2]

############################################################
# 05. Check wait condition on server2.
[connection server2]

############################################################
# 06. Execute T3 on server1
#     Make it wait on before transaction message broadcast.
[connection server_1_2]
SET @debug_save= @@GLOBAL.DEBUG;
SET @@GLOBAL.DEBUG= '+d,group_replication_before_message_broadcast';
CREATE TABLE test.t3 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB;

############################################################
# 07. Check wait condition on server1.
[connection server_1]

############################################################
# 08. Start server3
[connection server3]
SET SESSION sql_log_bin= 0;
CREATE USER "recovery_user" IDENTIFIED BY "recovery_password";
GRANT GROUP_REPLICATION_STREAM ON *.* TO 'recovery_user'@'%';
FLUSH PRIVILEGES;
Warnings:
Warning	1681	'FLUSH PRIVILEGES' is deprecated and will be removed in a future release.
SET SESSION sql_log_bin= 1;
CHANGE REPLICATION SOURCE TO SOURCE_USER= 'recovery_user', SOURCE_PASSWORD= 'recovery_password' FOR CHANNEL 'group_replication_recovery';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
SET SESSION sql_log_bin= 0;
call mtr.add_suppression("There was an error when connecting to the donor server*");
call mtr.add_suppression("For details please check performance_schema*");
call mtr.add_suppression("Replica I/O for channel 'group_replication_recovery': Source command COM_REGISTER_REPLICA failed*");
call mtr.add_suppression("Replica I/O thread couldn't register on source");
SET SESSION sql_log_bin= 1;
SET @@GLOBAL.DEBUG= '+d,group_replication_version_with_vcle';
include/start_group_replication.inc

############################################################
# 09. Signal to continue T3 and execute T4 on server1.
[connection server_1]
SET @@GLOBAL.DEBUG= @debug_save;
SET DEBUG_SYNC= 'now SIGNAL waiting';
[connection server_1_3]
CREATE TABLE test.t4 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB;

############################################################
# 10. Signal to continue T1 and T2 on server2.
[connection server2]
SET @@GLOBAL.DEBUG= @debug_save;
SET DEBUG_SYNC= 'now SIGNAL signal.after_before_message_send_after_applier_prepare_continue';

############################################################
# 11. Check members state on server1.
[connection server1]
[connection server_1_1]
[connection server_1_2]
[connection server_1_3]
include/assert.inc [Server1 MEMBER_STATE is ONLINE]
[connection server3]
include/gr_wait_for_member_state.inc
include/diff_tables.inc [server1:test.t1, server2:test.t1, server3:test.t1]
include/diff_tables.inc [server1:test.t2, server2:test.t2, server3:test.t2]
include/diff_tables.inc [server1:test.t3, server2:test.t3, server3:test.t3]

############################################################
# 12. Check all servers have the same transactions order on the binary log.
[connection server1]
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
server-binary-log.000001	#	Previous_gtids	#	#	
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	BEGIN
server-binary-log.000001	#	Query	#	#	COMMIT
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	BEGIN
server-binary-log.000001	#	Query	#	#	COMMIT
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	use `test`; CREATE TABLE test.t1 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	use `test`; CREATE TABLE test.t2 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	BEGIN
server-binary-log.000001	#	Query	#	#	COMMIT
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	use `test`; CREATE TABLE test.t3 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	use `test`; CREATE TABLE test.t4 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB
[connection server2]
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
server-binary-log.000001	#	Previous_gtids	#	#	
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	BEGIN
server-binary-log.000001	#	Query	#	#	COMMIT
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	BEGIN
server-binary-log.000001	#	Query	#	#	COMMIT
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	use `test`; CREATE TABLE test.t1 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	use `test`; CREATE TABLE test.t2 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	BEGIN
server-binary-log.000001	#	Query	#	#	COMMIT
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	use `test`; CREATE TABLE test.t3 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	use `test`; CREATE TABLE test.t4 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB
[connection server3]
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
server-binary-log.000001	#	Previous_gtids	#	#	
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	BEGIN
server-binary-log.000001	#	Query	#	#	COMMIT
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	BEGIN
server-binary-log.000001	#	Query	#	#	COMMIT
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	use `test`; CREATE TABLE test.t1 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	use `test`; CREATE TABLE test.t2 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	BEGIN
server-binary-log.000001	#	Query	#	#	COMMIT
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	use `test`; CREATE TABLE test.t3 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB
server-binary-log.000001	#	Gtid	#	#	SET @@SESSION.GTID_NEXT= 'Gtid_set'
server-binary-log.000001	#	Query	#	#	use `test`; CREATE TABLE test.t4 (c1 INT NOT NULL PRIMARY KEY) ENGINE=InnoDB

############################################################
# 13. Cleaning up
[connection server1]
DROP TABLE t1;
DROP TABLE t2;
DROP TABLE t3;
DROP TABLE t4;
SET SESSION sql_log_bin= 0;
DROP USER "recovery_user";
SET SESSION sql_log_bin= 1;
SET @@GLOBAL.DEBUG= '-d,group_replication_version_with_vcle';
[connection server2]
SET SESSION sql_log_bin= 0;
DROP USER "recovery_user";
SET SESSION sql_log_bin= 1;
SET @@GLOBAL.DEBUG= '-d,group_replication_version_with_vcle';
[connection server3]
SET SESSION sql_log_bin= 0;
DROP USER "recovery_user";
SET SESSION sql_log_bin= 1;
SET @@GLOBAL.DEBUG= '-d,group_replication_version_with_vcle';
include/group_replication_end.inc
