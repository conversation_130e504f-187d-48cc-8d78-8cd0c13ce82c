# Copyright (c) 2015, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
#
# GENERATED FILE
#
cmd: testBasic
args: -n CommitAsMuch626 T6 D1 D2
max-time: 180

cmd: testNdbApi
args: -n DeleteClusterConnectionWhileUsed T1
max-time: 180

cmd: testNdbApi
args: -n NdbErrorOperation T6
max-time: 180

cmd: testBasic
args: -n PkRead T1
max-time: 180

cmd: testBasic
args: -n Bug54944DATABUFFER T1
max-time: 180

cmd: testBasic
args: -n CommitDelete T1 T6 D1 D2
max-time: 180

cmd: testScan
args: -n OnlyOpenScanOnce T6 D1 D2
max-time: 180

cmd: testSystemRestart
args: -l 1 -n SR7 T1
max-time: 180

cmd: testScan
args: -n ScanUpdate2 T6 D1 D2
max-time: 180

cmd: testDict
args: -n FK_TRANS2 T1
max-time: 180

cmd: testIndex
args: -n Bug46069 T1
max-time: 360

cmd: testDataBuffers
args:
max-time: 180

cmd: testIndex
args: -n BuildDuring_O T6
max-time: 180

cmd: testNodeRestart
args: -n Bug24543 T1
max-time: 360

cmd: testBasic
args: -n PkRead WIDE_MAXKEY_HUGO WIDE_MAXATTR_HUGO WIDE_MAXKEYMAXCOLS_HUGO WIDE_MINKEYMAXCOLS_HUGO
max-time: 180

cmd: testNdbApi
args: -n NdbClusterConnectNR_slow T1
max-time: 180

cmd: testNodeRestart
args: -n Bug18044717 T1
max-time: 360

cmd: test_event
args: -n Apiv2EmptyEpochs T1
max-time: 180

cmd: testBasic
args: -n ReadWithLocksAndInserts T6 D1 D2
max-time: 540

cmd: testScan
args: -n ScanReadError5023 T1 D1 D2
max-time: 540

cmd: testBasic
args: -n PkUpdate WIDE_MAXKEY_HUGO WIDE_MAXATTR_HUGO WIDE_MAXKEYMAXCOLS_HUGO WIDE_MINKEYMAXCOLS_HUGO
max-time: 540

cmd: testBlobs
args: -rows 25
max-time: 540

cmd: testNodeRestart
args: -n Bug32922 T1
max-time: 720

cmd: testSystemRestart
args: -n Bug54611 T1
max-time: 720

cmd: testNodeRestart
args: -n Bug27466 T1
max-time: 900

cmd: testSystemRestart
args: -n basic T1
max-time: 1080

cmd: testDict
args: -n TemporaryTables T1 T6
max-time: 1260

cmd: testSRBank
args: -n SR -l 300 -r 15 T1
max-time: 1260

cmd: testSystemRestart
args: -n SR3 T6
max-time: 2880

cmd: testIndex
args: -l 2 -n SR1_O T6 T13
max-time: 3780

