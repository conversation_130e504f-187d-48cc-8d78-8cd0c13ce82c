--disable_warnings
let $ENGINE=`select variable_value from performance_schema.global_variables where variable_name='DEFAULT_STORAGE_ENGINE'`;
--enable_warnings

--disable_warnings
DROP TABLE IF EXISTS t4;
--enable_warnings
CREATE TABLE t4(c1 TINYINT   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 TINYINT   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 SMALLINT   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 SMALLINT   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 MEDIUMINT   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 MEDIUMINT   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INT   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 INT   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INTEGER   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 INTEGER   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 BIGINT   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 BIGINT   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 REAL   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 REAL   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DOUBLE   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 DOUBLE   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 FLOAT   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 FLOAT   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DECIMAL   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 DECIMAL   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 NUMERIC   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 NUMERIC   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 TINYINT   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 TINYINT   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 SMALLINT   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 SMALLINT   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 MEDIUMINT   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 MEDIUMINT   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INT   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 INT   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INTEGER   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 INTEGER   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 BIGINT   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 BIGINT   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 REAL   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 REAL   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DOUBLE   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 DOUBLE   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 FLOAT   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 FLOAT   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DECIMAL   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 DECIMAL   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 NUMERIC   NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 NUMERIC   ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 TINYINT UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 TINYINT UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 SMALLINT UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 SMALLINT UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 MEDIUMINT UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 MEDIUMINT UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INT UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 INT UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INTEGER UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 INTEGER UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 BIGINT UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 BIGINT UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 REAL UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 REAL UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DOUBLE UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 DOUBLE UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 FLOAT UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 FLOAT UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DECIMAL UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 DECIMAL UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 NUMERIC UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 NUMERIC UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 TINYINT UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 TINYINT UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 SMALLINT UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 SMALLINT UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 MEDIUMINT UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 MEDIUMINT UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INT UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 INT UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INTEGER UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 INTEGER UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 BIGINT UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 BIGINT UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 REAL UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 REAL UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DOUBLE UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 DOUBLE UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 FLOAT UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 FLOAT UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DECIMAL UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 DECIMAL UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 NUMERIC UNSIGNED NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 NUMERIC UNSIGNED ZEROFILL NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 TINYINT   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 TINYINT   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 SMALLINT   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 SMALLINT   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 MEDIUMINT   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 MEDIUMINT   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INT   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 INT   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INTEGER   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 INTEGER   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 BIGINT   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 BIGINT   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 REAL   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 REAL   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DOUBLE   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 DOUBLE   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 FLOAT   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 FLOAT   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DECIMAL   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 DECIMAL   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 NUMERIC   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 NUMERIC   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 TINYINT   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 TINYINT   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 SMALLINT   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 SMALLINT   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 MEDIUMINT   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 MEDIUMINT   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INT   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 INT   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INTEGER   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 INTEGER   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 BIGINT   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 BIGINT   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 REAL   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 REAL   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DOUBLE   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 DOUBLE   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 FLOAT   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 FLOAT   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DECIMAL   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 DECIMAL   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 NUMERIC   NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 NUMERIC   ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 TINYINT UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 TINYINT UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 SMALLINT UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 SMALLINT UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 MEDIUMINT UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 MEDIUMINT UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INT UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 INT UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INTEGER UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 INTEGER UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 BIGINT UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 BIGINT UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 REAL UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 REAL UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DOUBLE UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 DOUBLE UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 FLOAT UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 FLOAT UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DECIMAL UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 DECIMAL UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 NUMERIC UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 MODIFY c1 NUMERIC UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 TINYINT UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 TINYINT UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 SMALLINT UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 SMALLINT UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 MEDIUMINT UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 MEDIUMINT UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INT UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 INT UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 INTEGER UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 INTEGER UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 BIGINT UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 BIGINT UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 REAL UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 REAL UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DOUBLE UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 DOUBLE UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 FLOAT UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 FLOAT UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 DECIMAL UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 DECIMAL UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
CREATE TABLE t4(c1 NUMERIC UNSIGNED NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; 
ALTER TABLE t4 CHANGE c1 c1 NUMERIC UNSIGNED ZEROFILL NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t4; DROP TABLE t4; SHOW TABLES;
