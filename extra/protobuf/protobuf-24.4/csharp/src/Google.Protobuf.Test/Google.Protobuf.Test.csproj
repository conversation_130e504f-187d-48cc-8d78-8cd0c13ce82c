<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net462;net6.0</TargetFrameworks>
    <AssemblyOriginatorKeyFile>../../keys/Google.Protobuf.snk</AssemblyOriginatorKeyFile>
    <SignAssembly>true</SignAssembly>
    <IsPackable>False</IsPackable>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Google.Protobuf\Google.Protobuf.csproj" />
    <ProjectReference Include="..\Google.Protobuf.Test.TestProtos\Google.Protobuf.Test.TestProtos.csproj"/>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.5.0" />
    <PackageReference Include="NUnit" Version="3.13.3" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.2.2" />
  </ItemGroup>

  <!-- Needed for the netfx build to work on Unix. See https://github.com/dotnet/designs/pull/33 -->
  <ItemGroup>
    <PackageReference Include="Microsoft.NETFramework.ReferenceAssemblies" Version="1.0.2" PrivateAssets="All" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="testprotos.pb" />
  </ItemGroup>

</Project>
