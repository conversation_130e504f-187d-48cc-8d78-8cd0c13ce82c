--disable_warnings
let $ENGINE=`select variable_value from performance_schema.global_variables where variable_name='DEFAULT_STORAGE_ENGINE'`;
--enable_warnings

--disable_warnings
DROP TABLE IF EXISTS t1;
--enable_warnings
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX     (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX   USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD KEY i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1   (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING BTREE (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD INDEX i1 USING HASH (c1);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
