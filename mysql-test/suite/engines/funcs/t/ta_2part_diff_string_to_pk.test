--disable_warnings
let $ENGINE=`select variable_value from performance_schema.global_variables where variable_name='DEFAULT_STORAGE_ENGINE'`;
--enable_warnings

--disable_warnings
DROP TABLE IF EXISTS t1;
--enable_warnings
CREATE TABLE t1(c1 CHAR(30) NOT NULL, c2 CHAR(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(30) NOT NULL, c2 CHAR(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(30) NOT NULL, c2 CHAR(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(30) NOT NULL, c2 CHAR(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(30) NOT NULL, c2 VARCHAR(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(30) NOT NULL, c2 VARCHAR(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(30) NOT NULL, c2 VARCHAR(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(30) NOT NULL, c2 VARCHAR(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(30) NOT NULL, c2 BINARY(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(30) NOT NULL, c2 BINARY(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(30) NOT NULL, c2 BINARY(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(30) NOT NULL, c2 BINARY(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(30) NOT NULL, c2 VARBINARY(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(30) NOT NULL, c2 VARBINARY(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(30) NOT NULL, c2 VARBINARY(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(30) NOT NULL, c2 VARBINARY(20) NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
