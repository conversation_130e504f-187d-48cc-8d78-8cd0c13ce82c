################################################################################
# Test case to verify that binlog dump thread is stopped on:
# 1. STOP GR
# 2. Member leaves the group on error
#
# Test:
# 0. This test requires 3 members.
#    M1 and M2 are connected through GR.
#    M3 connects with M1 and M2 through asynchronous replication.
# 1. Start GR on M1(primary) and create table t1.
# 2. Start M2.
# 3. Create and start new channel ch3_1: M1(master), M3(slave)
#    and ch4_1: M1(master), M4(slave).
# 4. Insert row on M1 and table t1 with binlog off.
# 5. Insert same row in M2 so that M1 stops due to primary key coflict.
# 6. Wait for GR to ERROR out on M1.
# 7. Assert channels ch3_1 and ch4_1 have errored out.
#    Assert binlog dump thread is not present in threads table.
# 8. Restore M3 channel.
# 9. Start GR on M1 and channel ch3_1: <PERSON>(master), M3(slave)
#    and ch4_1: M1(master), M4(slave).
# 10. Stop GR on M1.
# 11. Assert channels ch3_1 and ch4_1 have errored out.
#     Assert binlog dump thread is not present in threads table.
# 12. Cleanup
################################################################################

--source include/big_test.inc
--source include/have_group_replication_plugin.inc
--let $rpl_server_count=4
--let $rpl_skip_group_replication_start= 1
--let $rpl_group_replication_single_primary_mode=1
--source include/group_replication.inc

--echo
--echo # 1. Start GR on M1(primary) and create table t1.
--let $rpl_connection_name= server1
--source include/connection.inc
--let $server1_uuid= query_get_value(SELECT @@SERVER_UUID, @@SERVER_UUID, 1)

set session sql_log_bin=0;
call mtr.add_suppression("Replica SQL for channel 'group_replication_applier': Could not execute Write_rows event on table .*");
call mtr.add_suppression("Replica SQL for channel 'group_replication_applier': Worker . failed executing transaction .*");
call mtr.add_suppression("Replica SQL for channel 'group_replication_applier': ... The replica coordinator and worker threads are stopped, .*");
call mtr.add_suppression("Replica: Duplicate entry '.' for key 't1.PRIMARY' Error_code: MY-001062");
call mtr.add_suppression("The applier thread execution was aborted. .*");
call mtr.add_suppression("Error running query, replica SQL thread aborted. .*");
call mtr.add_suppression("Fatal error during execution on the Applier process of Group Replication. The server will now leave the group.");
call mtr.add_suppression("The server was automatically set into read only mode after an error was detected.");
call mtr.add_suppression("Skipping leave operation: concurrent attempt to leave the group is on-going.");
set session sql_log_bin=1;

--source include/start_and_bootstrap_group_replication.inc
CREATE TABLE test.t1 (a INT PRIMARY KEY NOT NULL);
INSERT INTO test.t1 values (1);

--echo
--echo # 2. Start M2.
--let $rpl_connection_name= server2
--source include/connection.inc

--source include/start_group_replication.inc

--echo
--echo # 3. Create and start new channel ch3_1: M1(master), M3(slave)
--echo #    and ch4_1: M1(master), M4(slave).

--let $rpl_connection_name= server3
--source include/connection.inc

set session sql_log_bin=0;
call mtr.add_suppression("Error reading packet from server for channel 'ch3_1': .*");
set session sql_log_bin=1;

--replace_result $SERVER_MYPORT_1 SERVER_1_PORT
--eval CHANGE REPLICATION SOURCE TO SOURCE_HOST='localhost', SOURCE_USER='root', SOURCE_PORT=$SERVER_MYPORT_1 for channel 'ch3_1'

--let $rpl_channel_name='ch3_1'
--source include/rpl/start_replica.inc
--let $rpl_channel_name=

--let $wait_condition= SELECT COUNT(*)=1 FROM information_schema.tables WHERE TABLE_SCHEMA="test" AND TABLE_NAME="t1"
--source include/wait_condition.inc

--let $wait_condition= SELECT COUNT(*)=1 FROM test.t1 WHERE a=1
--source include/wait_condition.inc

--let $rpl_connection_name= server4
--source include/connection.inc

set session sql_log_bin=0;
call mtr.add_suppression("Error reading packet from server for channel 'ch4_1': .*");
set session sql_log_bin=1;

--replace_result $SERVER_MYPORT_1 SERVER_1_PORT
--eval CHANGE REPLICATION SOURCE TO SOURCE_HOST='localhost', SOURCE_USER='root', SOURCE_PORT=$SERVER_MYPORT_1 for channel 'ch4_1'

--let $rpl_channel_name='ch4_1'
--source include/rpl/start_replica.inc
--let $rpl_channel_name=

--let $wait_condition= SELECT COUNT(*)=1 FROM information_schema.tables WHERE TABLE_SCHEMA="test" AND TABLE_NAME="t1"
--source include/wait_condition.inc

--let $wait_condition= SELECT COUNT(*)=1 FROM test.t1 WHERE a=1
--source include/wait_condition.inc

--echo
--echo # 4. Insert row on M1 and table t1 with binlog off.
--let $rpl_connection_name= server1
--source include/connection.inc

SET sql_log_bin=0;
INSERT INTO test.t1 values (2);
SET sql_log_bin=1;

--echo
--echo # 5. Insert same row in M2 so that M1 stops due to primary key coflict.
--let $rpl_connection_name= server2
--source include/connection.inc

SET GLOBAL super_read_only= 0;
INSERT INTO test.t1 values (2);

--echo
--echo # 6. Wait for GR to ERROR out on M1.
--let $rpl_connection_name= server1
--source include/connection.inc

--let $group_replication_member_state= ERROR
--source include/gr_wait_for_member_state.inc

SET GLOBAL super_read_only= FALSE;
SET sql_log_bin=0;
INSERT INTO test.t1 values (3);
SET sql_log_bin=1;
SET GLOBAL super_read_only= TRUE;

--echo
--echo # 7. Assert channels ch3_1 and ch4_1 have errored out.
--echo #    Assert binlog dump thread is not present in threads table.

--let $assert_text= Thread Binlog Dump GTID is killed.
--let $assert_cond= "[SELECT COUNT(*) FROM information_schema.processlist WHERE COMMAND=\"Binlog Dump%\"]" = "0"
--source include/assert.inc

--let $rpl_connection_name= server3
--source include/connection.inc

--let $assert_file= $MYSQLTEST_VARDIR/log/mysqld.3.err
--let $assert_select= Lost connection to MySQL server during query \(server_errno=2013\)
--let $assert_count= 1
--let $assert_text= Master connection is lost.
--source include/assert_grep.inc

--let $assert_text= Slave connection is stopped.
--let $assert_cond= "[SELECT COUNT(*) FROM test.t1 WHERE a=3]" = "0"
--source include/assert.inc

--let $rpl_connection_name= server4
--source include/connection.inc

--let $assert_file= $MYSQLTEST_VARDIR/log/mysqld.4.err
--let $assert_select= Lost connection to MySQL server during query \(server_errno=2013\)
--let $assert_count= 1
--let $assert_text= Master connection is lost.
--source include/assert_grep.inc

--let $assert_text= Slave connection is stopped.
--let $assert_cond= "[SELECT COUNT(*) FROM test.t1 WHERE a=3]" = "0"
--source include/assert.inc

--echo
--echo # 8. Restore M3 channel.

--let $rpl_connection_name= server3
--source include/connection.inc

--let $rpl_channel_name='ch3_1'
--source include/rpl/stop_replica.inc
--let $rpl_channel_name=

--let $rpl_connection_name= server4
--source include/connection.inc

--let $rpl_channel_name='ch4_1'
--source include/rpl/stop_replica.inc
--let $rpl_channel_name=

--let $rpl_connection_name= server1
--source include/connection.inc

SET GLOBAL super_read_only= FALSE;
SET sql_log_bin=0;
DELETE FROM test.t1 WHERE a=3;
SET sql_log_bin=1;
SET GLOBAL super_read_only= TRUE;

--echo
--echo # 9. Start GR on M1 and channel ch3_1: M1(master), M3(slave)
--echo #    and ch4_1: M1(master), M4(slave).

--source include/stop_group_replication.inc
SET sql_log_bin=0;
DELETE FROM test.t1 WHERE a=2;
SET sql_log_bin=1;
--source include/start_group_replication.inc
--replace_result $server1_uuid SERVER1_UUID
--eval SELECT group_replication_set_as_primary("$server1_uuid")
INSERT INTO test.t1 values (3);

--let $rpl_connection_name= server3
--source include/connection.inc

--let $rpl_channel_name='ch3_1'
--source include/rpl/start_replica.inc
--let $rpl_channel_name=

--let $wait_condition= SELECT COUNT(*)=1 FROM test.t1 WHERE a=3
--source include/wait_condition.inc

--let $rpl_connection_name= server4
--source include/connection.inc

--let $rpl_channel_name='ch4_1'
--source include/rpl/start_replica.inc
--let $rpl_channel_name=

--let $wait_condition= SELECT COUNT(*)=1 FROM test.t1 WHERE a=3
--source include/wait_condition.inc

--echo
--echo # 10. Stop GR on M1.
--let $rpl_connection_name= server1
--source include/connection.inc

--source include/stop_group_replication.inc

SET GLOBAL super_read_only= FALSE;
SET sql_log_bin=0;
INSERT INTO test.t1 values (4);
SET sql_log_bin=1;
SET GLOBAL super_read_only= TRUE;

--echo
--echo # 11. Assert channels ch3_1 and ch4_1 have errored out.
--echo #     Assert binlog dump thread is not present in threads table.

--let $assert_text= Thread Binlog Dump GTID is killed.
--let $assert_cond= "[SELECT COUNT(*) FROM information_schema.processlist WHERE COMMAND=\"Binlog Dump%\"]" = "0"
--source include/assert.inc

--let $rpl_connection_name= server3
--source include/connection.inc

--let $assert_file= $MYSQLTEST_VARDIR/log/mysqld.3.err
--let $assert_select= Lost connection to MySQL server during query \(server_errno=2013\)
--let $assert_count= 2
--let $assert_text= Master connection is lost.
--source include/assert_grep.inc

--let $assert_text= Slave connection is stopped.
--let $assert_cond= "[SELECT COUNT(*) FROM test.t1 WHERE a=4]" = "0"
--source include/assert.inc

--let $rpl_connection_name= server4
--source include/connection.inc

--let $assert_file= $MYSQLTEST_VARDIR/log/mysqld.4.err
--let $assert_select= Lost connection to MySQL server during query \(server_errno=2013\)
--let $assert_count= 2
--let $assert_text= Master connection is lost.
--source include/assert_grep.inc

--let $assert_text= Slave connection is stopped.
--let $assert_cond= "[SELECT COUNT(*) FROM t1 WHERE a=4]" = "0"
--source include/assert.inc

--echo
--echo # 12. Cleanup
--let $rpl_connection_name= server1
--source include/connection.inc

SET GLOBAL super_read_only= FALSE;
SET sql_log_bin=0;
DELETE FROM test.t1 WHERE a=4;
SET sql_log_bin=1;
SET GLOBAL super_read_only= TRUE;

--source include/start_group_replication.inc

--let $rpl_connection_name= server2
--source include/connection.inc

DROP TABLE test.t1;

--let $rpl_connection_name= server3
--source include/connection.inc

--let $rpl_channel_name='ch3_1'
--source include/rpl/stop_replica.inc
--let $rpl_channel_name=
RESET REPLICA ALL FOR CHANNEL 'ch3_1';
--source include/start_group_replication.inc

--let $rpl_connection_name= server4
--source include/connection.inc

--let $rpl_channel_name='ch4_1'
--source include/rpl/stop_replica.inc
--let $rpl_channel_name=
RESET REPLICA ALL FOR CHANNEL 'ch4_1';
--source include/start_group_replication.inc

--source include/group_replication_end.inc
