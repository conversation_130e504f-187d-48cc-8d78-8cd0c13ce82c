# Test Case : With default row type
SET NAMES utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
# Create table : Test with row_type ROW_FORMAT=DEFAULT  with char,varchar,text columns
CREATE TABLE articles_1 (
seq INT ,
title_char VARCHAR(200),
title_varchar VARCHAR(300),
title_text VARCHAR(300)
) charset latin1 ENGINE=InnoDB ROW_FORMAT=DEFAULT ;
CREATE TABLE articles (
seq INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
c1 VARCHAR(200) CHARACTER SET big5 COLLATE big5_chinese_ci,
c2 CHAR(200) CHARACTER SET gbk COLLATE gbk_chinese_ci,
c3 CHAR(200) CHARACTER SET utf8mb3,
j1 TEXT CHARACTER SET ujis COLLATE ujis_japanese_ci,
j2 VARCHAR(200) CHARACTER SET sjis COLLATE sjis_japanese_ci,
j3 VARCHAR(200) CHARACTER SET ujis,
k1 CHAR(200) CHARACTER SET euckr COLLATE euckr_korean_ci,
k2 CHAR(200) CHARACTER SET utf8mb3,
e1 CHAR(200) CHARACTER SET latin1,
FULLTEXT KEY con1 (c1) WITH PARSER ngram,
FULLTEXT KEY con2 (j1,j3) WITH PARSER ngram,
FULLTEXT KEY con3 (k2) WITH PARSER ngram
) ENGINE=InnoDB ROW_FORMAT=DEFAULT ;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
CREATE TABLE 名字 (
seq INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
c1 VARCHAR(200) CHARACTER SET big5 COLLATE big5_chinese_ci,
c2 CHAR(200) CHARACTER SET gbk COLLATE gbk_chinese_ci,
c3 CHAR(200) CHARACTER SET utf8mb3,
さようなら TEXT CHARACTER SET ujis COLLATE ujis_japanese_ci,
j2 VARCHAR(200) CHARACTER SET sjis COLLATE sjis_japanese_ci,
j3 VARCHAR(200) CHARACTER SET ujis,
행운을빈다 CHAR(200) CHARACTER SET euckr COLLATE euckr_korean_ci,
k2 CHAR(200) CHARACTER SET utf8mb3,
e1 CHAR(200) CHARACTER SET latin1,
FULLTEXT KEY con1 (c1) WITH PARSER ngram,
FULLTEXT KEY con2 (さようなら,j3) WITH PARSER ngram,
FULLTEXT KEY con3 (k2) WITH PARSER ngram
) ENGINE=InnoDB ROW_FORMAT=DEFAULT ;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SET SESSION innodb_ft_enable_stopword=0;
# Case : run ngram on english text.
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_varchar) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_text) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char,title_varchar) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_text,title_varchar) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO articles_1 (seq,title_char,title_varchar,title_text) VALUES
(1,'English is a West Germanic language','It was first spoken in early medieval England','most widely used language in the world'),
(2,'spoken as a first language by the majority populations of several states','including the United Kingdom i*', ' and the United States, Canada, Australia, Ireland'),
(3,'English arose in the Anglo-Saxon kingdoms','its from of England and what is now southeast Scotland','Following the extensive influence of Great Britain and the United Kingdom'),
(5,'significant number of English words are constructed on the basis of roots from Latin', 'because Latin in some form was the lingua franca of the Christian Church','and of European intellectual life'),
(7,'spread across states by  United Kingdom ~ England army','The Oxford English Dictionary liSts over 250,000 distinct words+','it became the dominant language in the United States, Canada, Australia and New Zealand');
ANALYZE TABLE articles_1;
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('En');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('nd');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('50');
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('se');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('al');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('er');
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('West');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('first');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('United');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('in');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('St');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('En la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('+En -la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('-En +la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('+En +la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('-En -la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('in nc' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+in -nc' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+in <la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+la >la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('~in la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('Ca Ox' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('s+ Ox' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('s+ i*' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('"Un" "-i*"' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('la'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('po'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_text)
AGAINST ('"Ca da"@2' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1
WHERE MATCH (title_varchar,title_text)
AGAINST ('"Ca da"@5' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 t1,articles_1 t2
WHERE MATCH(t2.title_varchar,t2.title_text) AGAINST('Ca Ox' IN BOOLEAN MODE)
AND t1.seq = t2.seq ;
seq	title_char	title_varchar	title_text	seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland	2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life	5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand	7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT COUNT(*) FROM articles_1 t1,articles_1 t2
WHERE MATCH(t1.title_char) AGAINST('la')
AND t1.seq = t2.seq
AND MATCH(t2.title_varchar) AGAINST('-da' IN BOOLEAN MODE) ;
COUNT(*)
0
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char,title_text,title_varchar);
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('kingdom');
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('+"kingdom" -"States"' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('"United States"@2' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('States' WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SET SESSION innodb_ft_enable_stopword=1;
DROP TABLE articles_1;
# Test Case 2: CJK charset and collation on row_type
INSERT INTO articles (c1,c2,c3,j1,j2,j3,k1,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row row1 xy'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row row1 value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE articles;
SELECT @@ngram_token_size;
@@ngram_token_size
2
SELECT * FROM articles;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
ALTER TABLE articles ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE articles ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE articles ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE articles ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE articles ADD FULLTEXT INDEX con8 (k1) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`k1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE articles ADD FULLTEXT INDEX con9 (e1) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`k1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con9` (`e1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SELECT * FROM articles WHERE
MATCH(c1) AGAINST('救命');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST('我幾');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('やっ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('右さ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j3) AGAINST('右さ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k1) AGAINST('Ch');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('니다');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('row');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(c1) AGAINST("+啊! " IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("-我幾 -火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("<我幾 >火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
INSERT INTO articles (c1,c2,c3,j1,j2,j3,k1,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやてみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row row1 xy');
ANALYZE TABLE articles;
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST("つや +(てみ)" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
4	歡迎	你好	喂	日の長	言も言わずに	つやてみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
DELETE FROM articles WHERE seq = 4;
ANALYZE TABLE articles;
SELECT * FROM articles WHERE MATCH(c1) AGAINST('+ ! ' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST("右さ -の長" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('-Ch +해요');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(e1) AGAINST("row1 -row" IN BOOLEAN MODE) ;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c1) AGAINST('命啊' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j2) AGAINST('やっそう' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('右さ の長' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
SELECT * FROM articles WHERE
MATCH(j3) AGAINST('の長' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('testing' WITH QUERY EXPANSION) ;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j2) AGAINST('"やっそう"@5' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('"右さ -の長"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('"국말 Ch"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('"row row1"@1' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t2.j1,t2.j3) AGAINST("右さ +の長" IN BOOLEAN MODE)
AND t1.seq = t2.seq ;
COUNT(*)
1
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t1.c1) AGAINST("命啊")
AND t1.seq != t2.seq
AND MATCH(t2.k2) AGAINST("국말" IN BOOLEAN MODE) ;
COUNT(*)
1
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t1.c1) AGAINST("啊! " WITH QUERY EXPANSION)
AND t1.seq = t2.seq
AND MATCH(t2.k2) AGAINST("<국말" IN BOOLEAN MODE) ;
COUNT(*)
0
SELECT * FROM articles;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DROP TABLE articles;
# Test Case : CJK charset and collation with globalized table, column names
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE 名字;
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con9 (e1) WITH PARSER ngram;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST('row');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST("row -row1" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST('"row row"@1' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=DEFAULT  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
# Test Case : CJK charset and collation with  FTS + transactional statements
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`행운을빈다`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DELETE FROM 名字;
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ROLLBACK TO B;
COMMIT;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('啊! ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DELETE FROM 名字;
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ROLLBACK;
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=DEFAULT  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=DEFAULT  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
# Test Case : CJK charset and collation , FTS query in condition of update/delete
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
UPDATE 名字 SET c1 = '你好嗎?' WHERE MATCH(c2) AGAINST('啊!');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DELETE FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST("てみ -雨が" IN BOOLEAN MODE);
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('てみ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH (행운을빈다) AGAINST('니다');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DELETE FROM 名字 WHERE
MATCH (행운을빈다) AGAINST('니다');
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DROP TABLE 名字;
# Test Case : with compact row type
SET NAMES utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
# Create table : Test with row_type ROW_FORMAT=COMPACT  with char,varchar,text columns
CREATE TABLE articles_1 (
seq INT ,
title_char VARCHAR(200),
title_varchar VARCHAR(300),
title_text VARCHAR(300)
) charset latin1 ENGINE=InnoDB ROW_FORMAT=COMPACT ;
CREATE TABLE articles (
seq INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
c1 VARCHAR(200) CHARACTER SET big5 COLLATE big5_chinese_ci,
c2 CHAR(200) CHARACTER SET gbk COLLATE gbk_chinese_ci,
c3 CHAR(200) CHARACTER SET utf8mb3,
j1 TEXT CHARACTER SET ujis COLLATE ujis_japanese_ci,
j2 VARCHAR(200) CHARACTER SET sjis COLLATE sjis_japanese_ci,
j3 VARCHAR(200) CHARACTER SET ujis,
k1 CHAR(200) CHARACTER SET euckr COLLATE euckr_korean_ci,
k2 CHAR(200) CHARACTER SET utf8mb3,
e1 CHAR(200) CHARACTER SET latin1,
FULLTEXT KEY con1 (c1) WITH PARSER ngram,
FULLTEXT KEY con2 (j1,j3) WITH PARSER ngram,
FULLTEXT KEY con3 (k2) WITH PARSER ngram
) ENGINE=InnoDB ROW_FORMAT=COMPACT ;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
CREATE TABLE 名字 (
seq INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
c1 VARCHAR(200) CHARACTER SET big5 COLLATE big5_chinese_ci,
c2 CHAR(200) CHARACTER SET gbk COLLATE gbk_chinese_ci,
c3 CHAR(200) CHARACTER SET utf8mb3,
さようなら TEXT CHARACTER SET ujis COLLATE ujis_japanese_ci,
j2 VARCHAR(200) CHARACTER SET sjis COLLATE sjis_japanese_ci,
j3 VARCHAR(200) CHARACTER SET ujis,
행운을빈다 CHAR(200) CHARACTER SET euckr COLLATE euckr_korean_ci,
k2 CHAR(200) CHARACTER SET utf8mb3,
e1 CHAR(200) CHARACTER SET latin1,
FULLTEXT KEY con1 (c1) WITH PARSER ngram,
FULLTEXT KEY con2 (さようなら,j3) WITH PARSER ngram,
FULLTEXT KEY con3 (k2) WITH PARSER ngram
) ENGINE=InnoDB ROW_FORMAT=COMPACT ;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SET SESSION innodb_ft_enable_stopword=0;
# Case : run ngram on english text.
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_varchar) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_text) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char,title_varchar) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_text,title_varchar) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
INSERT INTO articles_1 (seq,title_char,title_varchar,title_text) VALUES
(1,'English is a West Germanic language','It was first spoken in early medieval England','most widely used language in the world'),
(2,'spoken as a first language by the majority populations of several states','including the United Kingdom i*', ' and the United States, Canada, Australia, Ireland'),
(3,'English arose in the Anglo-Saxon kingdoms','its from of England and what is now southeast Scotland','Following the extensive influence of Great Britain and the United Kingdom'),
(5,'significant number of English words are constructed on the basis of roots from Latin', 'because Latin in some form was the lingua franca of the Christian Church','and of European intellectual life'),
(7,'spread across states by  United Kingdom ~ England army','The Oxford English Dictionary liSts over 250,000 distinct words+','it became the dominant language in the United States, Canada, Australia and New Zealand');
ANALYZE TABLE articles_1;
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('En');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('nd');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('50');
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('se');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('al');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('er');
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('West');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('first');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('United');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('in');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('St');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('En la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('+En -la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('-En +la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('+En +la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('-En -la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('in nc' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+in -nc' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+in <la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+la >la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('~in la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('Ca Ox' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('s+ Ox' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('s+ i*' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('"Un" "-i*"' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('la'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('po'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_text)
AGAINST ('"Ca da"@2' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1
WHERE MATCH (title_varchar,title_text)
AGAINST ('"Ca da"@5' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 t1,articles_1 t2
WHERE MATCH(t2.title_varchar,t2.title_text) AGAINST('Ca Ox' IN BOOLEAN MODE)
AND t1.seq = t2.seq ;
seq	title_char	title_varchar	title_text	seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland	2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life	5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand	7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT COUNT(*) FROM articles_1 t1,articles_1 t2
WHERE MATCH(t1.title_char) AGAINST('la')
AND t1.seq = t2.seq
AND MATCH(t2.title_varchar) AGAINST('-da' IN BOOLEAN MODE) ;
COUNT(*)
0
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char,title_text,title_varchar);
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('kingdom');
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('+"kingdom" -"States"' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('"United States"@2' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('States' WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SET SESSION innodb_ft_enable_stopword=1;
DROP TABLE articles_1;
# Test Case 2: CJK charset and collation on row_type
INSERT INTO articles (c1,c2,c3,j1,j2,j3,k1,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row row1 xy'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row row1 value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE articles;
SELECT @@ngram_token_size;
@@ngram_token_size
2
SELECT * FROM articles;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
ALTER TABLE articles ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE articles ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE articles ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE articles ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE articles ADD FULLTEXT INDEX con8 (k1) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`k1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE articles ADD FULLTEXT INDEX con9 (e1) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`k1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con9` (`e1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
SELECT * FROM articles WHERE
MATCH(c1) AGAINST('救命');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST('我幾');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('やっ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('右さ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j3) AGAINST('右さ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k1) AGAINST('Ch');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('니다');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('row');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(c1) AGAINST("+啊! " IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("-我幾 -火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("<我幾 >火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
INSERT INTO articles (c1,c2,c3,j1,j2,j3,k1,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやてみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row row1 xy');
ANALYZE TABLE articles;
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST("つや +(てみ)" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
4	歡迎	你好	喂	日の長	言も言わずに	つやてみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
DELETE FROM articles WHERE seq = 4;
ANALYZE TABLE articles;
SELECT * FROM articles WHERE MATCH(c1) AGAINST('+ ! ' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST("右さ -の長" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('-Ch +해요');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(e1) AGAINST("row1 -row" IN BOOLEAN MODE) ;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c1) AGAINST('命啊' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j2) AGAINST('やっそう' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('右さ の長' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
SELECT * FROM articles WHERE
MATCH(j3) AGAINST('の長' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('testing' WITH QUERY EXPANSION) ;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j2) AGAINST('"やっそう"@5' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('"右さ -の長"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('"국말 Ch"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('"row row1"@1' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t2.j1,t2.j3) AGAINST("右さ +の長" IN BOOLEAN MODE)
AND t1.seq = t2.seq ;
COUNT(*)
1
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t1.c1) AGAINST("命啊")
AND t1.seq != t2.seq
AND MATCH(t2.k2) AGAINST("국말" IN BOOLEAN MODE) ;
COUNT(*)
1
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t1.c1) AGAINST("啊! " WITH QUERY EXPANSION)
AND t1.seq = t2.seq
AND MATCH(t2.k2) AGAINST("<국말" IN BOOLEAN MODE) ;
COUNT(*)
0
SELECT * FROM articles;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DROP TABLE articles;
# Test Case : CJK charset and collation with globalized table, column names
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE 名字;
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con9 (e1) WITH PARSER ngram;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST('row');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST("row -row1" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST('"row row"@1' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=COMPACT  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
# Test Case : CJK charset and collation with  FTS + transactional statements
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`행운을빈다`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DELETE FROM 名字;
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ROLLBACK TO B;
COMMIT;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('啊! ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DELETE FROM 名字;
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ROLLBACK;
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=COMPACT  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=COMPACT  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT
# Test Case : CJK charset and collation , FTS query in condition of update/delete
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
UPDATE 名字 SET c1 = '你好嗎?' WHERE MATCH(c2) AGAINST('啊!');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DELETE FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST("てみ -雨が" IN BOOLEAN MODE);
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('てみ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH (행운을빈다) AGAINST('니다');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DELETE FROM 名字 WHERE
MATCH (행운을빈다) AGAINST('니다');
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DROP TABLE 名字;
# Test Case : With redundant row type
SET NAMES utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
# Create table : Test with row_type ROW_FORMAT=REDUNDANT  with char,varchar,text columns
CREATE TABLE articles_1 (
seq INT ,
title_char VARCHAR(200),
title_varchar VARCHAR(300),
title_text VARCHAR(300)
) charset latin1 ENGINE=InnoDB ROW_FORMAT=REDUNDANT ;
CREATE TABLE articles (
seq INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
c1 VARCHAR(200) CHARACTER SET big5 COLLATE big5_chinese_ci,
c2 CHAR(200) CHARACTER SET gbk COLLATE gbk_chinese_ci,
c3 CHAR(200) CHARACTER SET utf8mb3,
j1 TEXT CHARACTER SET ujis COLLATE ujis_japanese_ci,
j2 VARCHAR(200) CHARACTER SET sjis COLLATE sjis_japanese_ci,
j3 VARCHAR(200) CHARACTER SET ujis,
k1 CHAR(200) CHARACTER SET euckr COLLATE euckr_korean_ci,
k2 CHAR(200) CHARACTER SET utf8mb3,
e1 CHAR(200) CHARACTER SET latin1,
FULLTEXT KEY con1 (c1) WITH PARSER ngram,
FULLTEXT KEY con2 (j1,j3) WITH PARSER ngram,
FULLTEXT KEY con3 (k2) WITH PARSER ngram
) ENGINE=InnoDB ROW_FORMAT=REDUNDANT ;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
CREATE TABLE 名字 (
seq INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
c1 VARCHAR(200) CHARACTER SET big5 COLLATE big5_chinese_ci,
c2 CHAR(200) CHARACTER SET gbk COLLATE gbk_chinese_ci,
c3 CHAR(200) CHARACTER SET utf8mb3,
さようなら TEXT CHARACTER SET ujis COLLATE ujis_japanese_ci,
j2 VARCHAR(200) CHARACTER SET sjis COLLATE sjis_japanese_ci,
j3 VARCHAR(200) CHARACTER SET ujis,
행운을빈다 CHAR(200) CHARACTER SET euckr COLLATE euckr_korean_ci,
k2 CHAR(200) CHARACTER SET utf8mb3,
e1 CHAR(200) CHARACTER SET latin1,
FULLTEXT KEY con1 (c1) WITH PARSER ngram,
FULLTEXT KEY con2 (さようなら,j3) WITH PARSER ngram,
FULLTEXT KEY con3 (k2) WITH PARSER ngram
) ENGINE=InnoDB ROW_FORMAT=REDUNDANT ;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SET SESSION innodb_ft_enable_stopword=0;
# Case : run ngram on english text.
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_varchar) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_text) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char,title_varchar) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_text,title_varchar) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
INSERT INTO articles_1 (seq,title_char,title_varchar,title_text) VALUES
(1,'English is a West Germanic language','It was first spoken in early medieval England','most widely used language in the world'),
(2,'spoken as a first language by the majority populations of several states','including the United Kingdom i*', ' and the United States, Canada, Australia, Ireland'),
(3,'English arose in the Anglo-Saxon kingdoms','its from of England and what is now southeast Scotland','Following the extensive influence of Great Britain and the United Kingdom'),
(5,'significant number of English words are constructed on the basis of roots from Latin', 'because Latin in some form was the lingua franca of the Christian Church','and of European intellectual life'),
(7,'spread across states by  United Kingdom ~ England army','The Oxford English Dictionary liSts over 250,000 distinct words+','it became the dominant language in the United States, Canada, Australia and New Zealand');
ANALYZE TABLE articles_1;
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('En');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('nd');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('50');
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('se');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('al');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('er');
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('West');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('first');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('United');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('in');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('St');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('En la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('+En -la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('-En +la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('+En +la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('-En -la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('in nc' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+in -nc' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+in <la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+la >la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('~in la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('Ca Ox' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('s+ Ox' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('s+ i*' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('"Un" "-i*"' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('la'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('po'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_text)
AGAINST ('"Ca da"@2' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1
WHERE MATCH (title_varchar,title_text)
AGAINST ('"Ca da"@5' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 t1,articles_1 t2
WHERE MATCH(t2.title_varchar,t2.title_text) AGAINST('Ca Ox' IN BOOLEAN MODE)
AND t1.seq = t2.seq ;
seq	title_char	title_varchar	title_text	seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland	2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life	5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand	7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT COUNT(*) FROM articles_1 t1,articles_1 t2
WHERE MATCH(t1.title_char) AGAINST('la')
AND t1.seq = t2.seq
AND MATCH(t2.title_varchar) AGAINST('-da' IN BOOLEAN MODE) ;
COUNT(*)
0
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char,title_text,title_varchar);
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('kingdom');
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('+"kingdom" -"States"' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('"United States"@2' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('States' WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SET SESSION innodb_ft_enable_stopword=1;
DROP TABLE articles_1;
# Test Case 2: CJK charset and collation on row_type
INSERT INTO articles (c1,c2,c3,j1,j2,j3,k1,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row row1 xy'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row row1 value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE articles;
SELECT @@ngram_token_size;
@@ngram_token_size
2
SELECT * FROM articles;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
ALTER TABLE articles ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE articles ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE articles ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE articles ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE articles ADD FULLTEXT INDEX con8 (k1) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`k1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE articles ADD FULLTEXT INDEX con9 (e1) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`k1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con9` (`e1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
SELECT * FROM articles WHERE
MATCH(c1) AGAINST('救命');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST('我幾');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('やっ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('右さ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j3) AGAINST('右さ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k1) AGAINST('Ch');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('니다');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('row');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(c1) AGAINST("+啊! " IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("-我幾 -火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("<我幾 >火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
INSERT INTO articles (c1,c2,c3,j1,j2,j3,k1,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやてみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row row1 xy');
ANALYZE TABLE articles;
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST("つや +(てみ)" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
4	歡迎	你好	喂	日の長	言も言わずに	つやてみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
DELETE FROM articles WHERE seq = 4;
ANALYZE TABLE articles;
SELECT * FROM articles WHERE MATCH(c1) AGAINST('+ ! ' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST("右さ -の長" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('-Ch +해요');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(e1) AGAINST("row1 -row" IN BOOLEAN MODE) ;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c1) AGAINST('命啊' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j2) AGAINST('やっそう' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('右さ の長' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
SELECT * FROM articles WHERE
MATCH(j3) AGAINST('の長' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('testing' WITH QUERY EXPANSION) ;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j2) AGAINST('"やっそう"@5' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('"右さ -の長"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('"국말 Ch"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('"row row1"@1' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t2.j1,t2.j3) AGAINST("右さ +の長" IN BOOLEAN MODE)
AND t1.seq = t2.seq ;
COUNT(*)
1
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t1.c1) AGAINST("命啊")
AND t1.seq != t2.seq
AND MATCH(t2.k2) AGAINST("국말" IN BOOLEAN MODE) ;
COUNT(*)
1
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t1.c1) AGAINST("啊! " WITH QUERY EXPANSION)
AND t1.seq = t2.seq
AND MATCH(t2.k2) AGAINST("<국말" IN BOOLEAN MODE) ;
COUNT(*)
0
SELECT * FROM articles;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DROP TABLE articles;
# Test Case : CJK charset and collation with globalized table, column names
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE 名字;
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con9 (e1) WITH PARSER ngram;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST('row');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST("row -row1" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST('"row row"@1' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=REDUNDANT  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
# Test Case : CJK charset and collation with  FTS + transactional statements
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`행운을빈다`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DELETE FROM 名字;
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ROLLBACK TO B;
COMMIT;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('啊! ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DELETE FROM 名字;
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ROLLBACK;
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=REDUNDANT  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=REDUNDANT  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=REDUNDANT
# Test Case : CJK charset and collation , FTS query in condition of update/delete
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
UPDATE 名字 SET c1 = '你好嗎?' WHERE MATCH(c2) AGAINST('啊!');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DELETE FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST("てみ -雨が" IN BOOLEAN MODE);
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('てみ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH (행운을빈다) AGAINST('니다');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DELETE FROM 名字 WHERE
MATCH (행운을빈다) AGAINST('니다');
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DROP TABLE 名字;
# Test Case : With Dynamic row type
SET NAMES utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
# Create table : Test with row_type ROW_FORMAT=DYNAMIC  with char,varchar,text columns
CREATE TABLE articles_1 (
seq INT ,
title_char VARCHAR(200),
title_varchar VARCHAR(300),
title_text VARCHAR(300)
) charset latin1 ENGINE=InnoDB ROW_FORMAT=DYNAMIC ;
CREATE TABLE articles (
seq INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
c1 VARCHAR(200) CHARACTER SET big5 COLLATE big5_chinese_ci,
c2 CHAR(200) CHARACTER SET gbk COLLATE gbk_chinese_ci,
c3 CHAR(200) CHARACTER SET utf8mb3,
j1 TEXT CHARACTER SET ujis COLLATE ujis_japanese_ci,
j2 VARCHAR(200) CHARACTER SET sjis COLLATE sjis_japanese_ci,
j3 VARCHAR(200) CHARACTER SET ujis,
k1 CHAR(200) CHARACTER SET euckr COLLATE euckr_korean_ci,
k2 CHAR(200) CHARACTER SET utf8mb3,
e1 CHAR(200) CHARACTER SET latin1,
FULLTEXT KEY con1 (c1) WITH PARSER ngram,
FULLTEXT KEY con2 (j1,j3) WITH PARSER ngram,
FULLTEXT KEY con3 (k2) WITH PARSER ngram
) ENGINE=InnoDB ROW_FORMAT=DYNAMIC ;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
CREATE TABLE 名字 (
seq INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
c1 VARCHAR(200) CHARACTER SET big5 COLLATE big5_chinese_ci,
c2 CHAR(200) CHARACTER SET gbk COLLATE gbk_chinese_ci,
c3 CHAR(200) CHARACTER SET utf8mb3,
さようなら TEXT CHARACTER SET ujis COLLATE ujis_japanese_ci,
j2 VARCHAR(200) CHARACTER SET sjis COLLATE sjis_japanese_ci,
j3 VARCHAR(200) CHARACTER SET ujis,
행운을빈다 CHAR(200) CHARACTER SET euckr COLLATE euckr_korean_ci,
k2 CHAR(200) CHARACTER SET utf8mb3,
e1 CHAR(200) CHARACTER SET latin1,
FULLTEXT KEY con1 (c1) WITH PARSER ngram,
FULLTEXT KEY con2 (さようなら,j3) WITH PARSER ngram,
FULLTEXT KEY con3 (k2) WITH PARSER ngram
) ENGINE=InnoDB ROW_FORMAT=DYNAMIC ;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SET SESSION innodb_ft_enable_stopword=0;
# Case : run ngram on english text.
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_varchar) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_text) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char,title_varchar) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_text,title_varchar) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
INSERT INTO articles_1 (seq,title_char,title_varchar,title_text) VALUES
(1,'English is a West Germanic language','It was first spoken in early medieval England','most widely used language in the world'),
(2,'spoken as a first language by the majority populations of several states','including the United Kingdom i*', ' and the United States, Canada, Australia, Ireland'),
(3,'English arose in the Anglo-Saxon kingdoms','its from of England and what is now southeast Scotland','Following the extensive influence of Great Britain and the United Kingdom'),
(5,'significant number of English words are constructed on the basis of roots from Latin', 'because Latin in some form was the lingua franca of the Christian Church','and of European intellectual life'),
(7,'spread across states by  United Kingdom ~ England army','The Oxford English Dictionary liSts over 250,000 distinct words+','it became the dominant language in the United States, Canada, Australia and New Zealand');
ANALYZE TABLE articles_1;
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('En');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('nd');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('50');
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('se');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('al');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('er');
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('West');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('first');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('United');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('in');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('St');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('En la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('+En -la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('-En +la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('+En +la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('-En -la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('in nc' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+in -nc' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+in <la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+la >la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('~in la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('Ca Ox' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('s+ Ox' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('s+ i*' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('"Un" "-i*"' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('la'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('po'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_text)
AGAINST ('"Ca da"@2' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1
WHERE MATCH (title_varchar,title_text)
AGAINST ('"Ca da"@5' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 t1,articles_1 t2
WHERE MATCH(t2.title_varchar,t2.title_text) AGAINST('Ca Ox' IN BOOLEAN MODE)
AND t1.seq = t2.seq ;
seq	title_char	title_varchar	title_text	seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland	2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life	5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand	7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT COUNT(*) FROM articles_1 t1,articles_1 t2
WHERE MATCH(t1.title_char) AGAINST('la')
AND t1.seq = t2.seq
AND MATCH(t2.title_varchar) AGAINST('-da' IN BOOLEAN MODE) ;
COUNT(*)
0
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char,title_text,title_varchar);
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('kingdom');
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('+"kingdom" -"States"' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('"United States"@2' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('States' WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SET SESSION innodb_ft_enable_stopword=1;
DROP TABLE articles_1;
# Test Case 2: CJK charset and collation on row_type
INSERT INTO articles (c1,c2,c3,j1,j2,j3,k1,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row row1 xy'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row row1 value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE articles;
SELECT @@ngram_token_size;
@@ngram_token_size
2
SELECT * FROM articles;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
ALTER TABLE articles ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE articles ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE articles ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE articles ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE articles ADD FULLTEXT INDEX con8 (k1) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`k1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE articles ADD FULLTEXT INDEX con9 (e1) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`k1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con9` (`e1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
SELECT * FROM articles WHERE
MATCH(c1) AGAINST('救命');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST('我幾');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('やっ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('右さ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j3) AGAINST('右さ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k1) AGAINST('Ch');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('니다');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('row');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(c1) AGAINST("+啊! " IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("-我幾 -火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("<我幾 >火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
INSERT INTO articles (c1,c2,c3,j1,j2,j3,k1,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやてみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row row1 xy');
ANALYZE TABLE articles;
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST("つや +(てみ)" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
4	歡迎	你好	喂	日の長	言も言わずに	つやてみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
DELETE FROM articles WHERE seq = 4;
ANALYZE TABLE articles;
SELECT * FROM articles WHERE MATCH(c1) AGAINST('+ ! ' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST("右さ -の長" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('-Ch +해요');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(e1) AGAINST("row1 -row" IN BOOLEAN MODE) ;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c1) AGAINST('命啊' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j2) AGAINST('やっそう' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('右さ の長' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
SELECT * FROM articles WHERE
MATCH(j3) AGAINST('の長' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('testing' WITH QUERY EXPANSION) ;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j2) AGAINST('"やっそう"@5' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('"右さ -の長"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('"국말 Ch"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('"row row1"@1' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t2.j1,t2.j3) AGAINST("右さ +の長" IN BOOLEAN MODE)
AND t1.seq = t2.seq ;
COUNT(*)
1
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t1.c1) AGAINST("命啊")
AND t1.seq != t2.seq
AND MATCH(t2.k2) AGAINST("국말" IN BOOLEAN MODE) ;
COUNT(*)
1
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t1.c1) AGAINST("啊! " WITH QUERY EXPANSION)
AND t1.seq = t2.seq
AND MATCH(t2.k2) AGAINST("<국말" IN BOOLEAN MODE) ;
COUNT(*)
0
SELECT * FROM articles;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DROP TABLE articles;
# Test Case : CJK charset and collation with globalized table, column names
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE 名字;
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con9 (e1) WITH PARSER ngram;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST('row');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST("row -row1" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST('"row row"@1' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=DYNAMIC  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
# Test Case : CJK charset and collation with  FTS + transactional statements
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`행운을빈다`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DELETE FROM 名字;
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ROLLBACK TO B;
COMMIT;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('啊! ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DELETE FROM 名字;
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ROLLBACK;
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=DYNAMIC  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=DYNAMIC  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC
# Test Case : CJK charset and collation , FTS query in condition of update/delete
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
UPDATE 名字 SET c1 = '你好嗎?' WHERE MATCH(c2) AGAINST('啊!');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DELETE FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST("てみ -雨が" IN BOOLEAN MODE);
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('てみ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH (행운을빈다) AGAINST('니다');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DELETE FROM 名字 WHERE
MATCH (행운을빈다) AGAINST('니다');
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DROP TABLE 名字;
# Test Case : With compressed row type
SET NAMES utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
# Create table : Test with row_type ROW_FORMAT=COMPRESSED  with char,varchar,text columns
CREATE TABLE articles_1 (
seq INT ,
title_char VARCHAR(200),
title_varchar VARCHAR(300),
title_text VARCHAR(300)
) charset latin1 ENGINE=InnoDB ROW_FORMAT=COMPRESSED ;
CREATE TABLE articles (
seq INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
c1 VARCHAR(200) CHARACTER SET big5 COLLATE big5_chinese_ci,
c2 CHAR(200) CHARACTER SET gbk COLLATE gbk_chinese_ci,
c3 CHAR(200) CHARACTER SET utf8mb3,
j1 TEXT CHARACTER SET ujis COLLATE ujis_japanese_ci,
j2 VARCHAR(200) CHARACTER SET sjis COLLATE sjis_japanese_ci,
j3 VARCHAR(200) CHARACTER SET ujis,
k1 CHAR(200) CHARACTER SET euckr COLLATE euckr_korean_ci,
k2 CHAR(200) CHARACTER SET utf8mb3,
e1 CHAR(200) CHARACTER SET latin1,
FULLTEXT KEY con1 (c1) WITH PARSER ngram,
FULLTEXT KEY con2 (j1,j3) WITH PARSER ngram,
FULLTEXT KEY con3 (k2) WITH PARSER ngram
) ENGINE=InnoDB ROW_FORMAT=COMPRESSED ;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
CREATE TABLE 名字 (
seq INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
c1 VARCHAR(200) CHARACTER SET big5 COLLATE big5_chinese_ci,
c2 CHAR(200) CHARACTER SET gbk COLLATE gbk_chinese_ci,
c3 CHAR(200) CHARACTER SET utf8mb3,
さようなら TEXT CHARACTER SET ujis COLLATE ujis_japanese_ci,
j2 VARCHAR(200) CHARACTER SET sjis COLLATE sjis_japanese_ci,
j3 VARCHAR(200) CHARACTER SET ujis,
행운을빈다 CHAR(200) CHARACTER SET euckr COLLATE euckr_korean_ci,
k2 CHAR(200) CHARACTER SET utf8mb3,
e1 CHAR(200) CHARACTER SET latin1,
FULLTEXT KEY con1 (c1) WITH PARSER ngram,
FULLTEXT KEY con2 (さようなら,j3) WITH PARSER ngram,
FULLTEXT KEY con3 (k2) WITH PARSER ngram
) ENGINE=InnoDB ROW_FORMAT=COMPRESSED ;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SET SESSION innodb_ft_enable_stopword=0;
# Case : run ngram on english text.
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_varchar) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_text) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char,title_varchar) WITH PARSER ngram;
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_text,title_varchar) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
INSERT INTO articles_1 (seq,title_char,title_varchar,title_text) VALUES
(1,'English is a West Germanic language','It was first spoken in early medieval England','most widely used language in the world'),
(2,'spoken as a first language by the majority populations of several states','including the United Kingdom i*', ' and the United States, Canada, Australia, Ireland'),
(3,'English arose in the Anglo-Saxon kingdoms','its from of England and what is now southeast Scotland','Following the extensive influence of Great Britain and the United Kingdom'),
(5,'significant number of English words are constructed on the basis of roots from Latin', 'because Latin in some form was the lingua franca of the Christian Church','and of European intellectual life'),
(7,'spread across states by  United Kingdom ~ England army','The Oxford English Dictionary liSts over 250,000 distinct words+','it became the dominant language in the United States, Canada, Australia and New Zealand');
ANALYZE TABLE articles_1;
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('En');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('nd');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('50');
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('se');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('al');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('er');
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('West');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('first');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('United');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('in');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('St');
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('En la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('+En -la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('-En +la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('+En +la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('-En -la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('in nc' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+in -nc' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+in <la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('+la >la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('~in la' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('Ca Ox' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('s+ Ox' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('s+ i*' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 WHERE
MATCH(title_varchar,title_text) AGAINST('"Un" "-i*"' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char) AGAINST('la'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_varchar) AGAINST('po'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_char,title_varchar) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1 WHERE
MATCH(title_text,title_varchar) AGAINST('Ca'  WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_text)
AGAINST ('"Ca da"@2' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1
WHERE MATCH (title_varchar,title_text)
AGAINST ('"Ca da"@5' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
SELECT * FROM articles_1 t1,articles_1 t2
WHERE MATCH(t2.title_varchar,t2.title_text) AGAINST('Ca Ox' IN BOOLEAN MODE)
AND t1.seq = t2.seq ;
seq	title_char	title_varchar	title_text	seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland	2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life	5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand	7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT COUNT(*) FROM articles_1 t1,articles_1 t2
WHERE MATCH(t1.title_char) AGAINST('la')
AND t1.seq = t2.seq
AND MATCH(t2.title_varchar) AGAINST('-da' IN BOOLEAN MODE) ;
COUNT(*)
0
ALTER TABLE articles_1 ADD FULLTEXT INDEX (title_char,title_text,title_varchar);
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('kingdom');
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('+"kingdom" -"States"' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('"United States"@2' IN BOOLEAN MODE);
seq	title_char	title_varchar	title_text
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SELECT * FROM articles_1
WHERE MATCH (title_char,title_text,title_varchar)
AGAINST ('States' WITH QUERY EXPANSION);
seq	title_char	title_varchar	title_text
1	English is a West Germanic language	It was first spoken in early medieval England	most widely used language in the world
2	spoken as a first language by the majority populations of several states	including the United Kingdom i*	 and the United States, Canada, Australia, Ireland
3	English arose in the Anglo-Saxon kingdoms	its from of England and what is now southeast Scotland	Following the extensive influence of Great Britain and the United Kingdom
5	significant number of English words are constructed on the basis of roots from Latin	because Latin in some form was the lingua franca of the Christian Church	and of European intellectual life
7	spread across states by  United Kingdom ~ England army	The Oxford English Dictionary liSts over 250,000 distinct words+	it became the dominant language in the United States, Canada, Australia and New Zealand
SET SESSION innodb_ft_enable_stopword=1;
DROP TABLE articles_1;
# Test Case 2: CJK charset and collation on row_type
INSERT INTO articles (c1,c2,c3,j1,j2,j3,k1,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row row1 xy'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row row1 value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE articles;
SELECT @@ngram_token_size;
@@ngram_token_size
2
SELECT * FROM articles;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
ALTER TABLE articles ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE articles ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE articles ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE articles ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE articles ADD FULLTEXT INDEX con8 (k1) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`k1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE articles ADD FULLTEXT INDEX con9 (e1) WITH PARSER ngram;
SHOW CREATE TABLE articles;
Table	Create Table
articles	CREATE TABLE `articles` (
  `seq` int NOT NULL AUTO_INCREMENT,
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `j1` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `k1` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  PRIMARY KEY (`seq`),
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`j1`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`k1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con9` (`e1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
SELECT * FROM articles WHERE
MATCH(c1) AGAINST('救命');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST('我幾');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('やっ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('右さ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j3) AGAINST('右さ');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k1) AGAINST('Ch');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('니다');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('row');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(c1) AGAINST("+啊! " IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("-我幾 -火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST("<我幾 >火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
INSERT INTO articles (c1,c2,c3,j1,j2,j3,k1,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやてみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row row1 xy');
ANALYZE TABLE articles;
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST("つや +(てみ)" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
4	歡迎	你好	喂	日の長	言も言わずに	つやてみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
DELETE FROM articles WHERE seq = 4;
ANALYZE TABLE articles;
SELECT * FROM articles WHERE MATCH(c1) AGAINST('+ ! ' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST("右さ -の長" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('-Ch +해요');
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT * FROM articles WHERE
MATCH(e1) AGAINST("row1 -row" IN BOOLEAN MODE) ;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(c1) AGAINST('命啊' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j2) AGAINST('やっそう' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('右さ の長' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
SELECT * FROM articles WHERE
MATCH(j3) AGAINST('の長' WITH QUERY EXPANSION);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('testing' WITH QUERY EXPANSION) ;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM articles WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j2) AGAINST('"やっそう"@5' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(j1,j3) AGAINST('"右さ -の長"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(k2) AGAINST('"국말 Ch"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
SELECT * FROM articles WHERE
MATCH(e1) AGAINST('"row row1"@1' IN BOOLEAN MODE);
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t2.j1,t2.j3) AGAINST("右さ +の長" IN BOOLEAN MODE)
AND t1.seq = t2.seq ;
COUNT(*)
1
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t1.c1) AGAINST("命啊")
AND t1.seq != t2.seq
AND MATCH(t2.k2) AGAINST("국말" IN BOOLEAN MODE) ;
COUNT(*)
1
SELECT COUNT(*) FROM articles t1,articles t2
WHERE MATCH(t1.c1) AGAINST("啊! " WITH QUERY EXPANSION)
AND t1.seq = t2.seq
AND MATCH(t2.k2) AGAINST("<국말" IN BOOLEAN MODE) ;
COUNT(*)
0
SELECT * FROM articles;
seq	c1	c2	c3	j1	j2	j3	k1	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row row1 xy
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row row1 value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DROP TABLE articles;
# Test Case : CJK charset and collation with globalized table, column names
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE 名字;
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con9 (e1) WITH PARSER ngram;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST('row');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
3	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST("row -row1" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
1	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
2	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(e1) AGAINST('"row row"@1' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=COMPRESSED  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
# Test Case : CJK charset and collation with  FTS + transactional statements
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con4` (`c3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con5` (`c2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con6` (`j2`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con7` (`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con8` (`행운을빈다`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DELETE FROM 名字;
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ROLLBACK TO B;
COMMIT;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('啊! ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DELETE FROM 名字;
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ROLLBACK;
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=COMPRESSED  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  FULLTEXT KEY `con1` (`c1`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con2` (`さようなら`,`j3`) /*!50100 WITH PARSER `ngram` */ ,
  FULLTEXT KEY `con3` (`k2`) /*!50100 WITH PARSER `ngram` */ 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
START TRANSACTION;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row');
SAVEPOINT A;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value');
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
SAVEPOINT B;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
COMMIT;
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('やっ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('제가');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	歡迎	你好	喂	日の長	言も言わずに	つやってみよう	제가 미국인입니다.	한국말을 공부합니다%	testing row
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST("-我幾 火啊" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j2) AGAINST("やっ-(そう)" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(j3) AGAINST(">の長" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST("니다 -제가" IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH(c2) AGAINST('"我幾 火啊"@3' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('"降り やっ"@4' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字 WHERE
MATCH(행운을빈다) AGAINST('"분은 어를"@2' IN BOOLEAN MODE);
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
CREATE TABLE tmp1 ROW_FORMAT=COMPRESSED  AS SELECT * FROM 名字 WHERE 1=2;
DROP TABLE 名字;
RENAME TABLE tmp1 TO 名字;
SHOW CREATE TABLE 名字;
Table	Create Table
名字	CREATE TABLE `名字` (
  `seq` int NOT NULL DEFAULT '0',
  `c1` varchar(200) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `c2` char(200) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT NULL,
  `c3` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `さようなら` text CHARACTER SET ujis COLLATE ujis_japanese_ci,
  `j2` varchar(200) CHARACTER SET sjis COLLATE sjis_japanese_ci DEFAULT NULL,
  `j3` varchar(200) CHARACTER SET ujis COLLATE ujis_japanese_ci DEFAULT NULL,
  `행운을빈다` char(200) CHARACTER SET euckr COLLATE euckr_korean_ci DEFAULT NULL,
  `k2` char(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `e1` char(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPRESSED
# Test Case : CJK charset and collation , FTS query in condition of update/delete
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
ALTER TABLE 名字 ADD FULLTEXT INDEX con1 (c1) WITH PARSER ngram;
Warnings:
Warning	124	InnoDB rebuilding table to add column FTS_DOC_ID
ALTER TABLE 名字 ADD FULLTEXT INDEX con2 (さようなら,j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con3 (k2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con4 (c3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con5 (c2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con6 (j2) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con7 (j3) WITH PARSER ngram;
ALTER TABLE 名字 ADD FULLTEXT INDEX con8 (행운을빈다) WITH PARSER ngram;
INSERT INTO 名字 (c1,c2,c3,さようなら,j2,j3,행운을빈다,k2,e1) VALUES
('歡迎','你好','喂 ','日の長','言も言わずに','つやってみよう','제가 미국인입니다.','한국말을 공부합니다%','testing row'),
('你好嗎?','我幾好，你呢','好耐冇見','左右','左右される','☆右折⇔左折','제 이름은 Charles입니다.','운동을 좋아해요*','row value'),
('救命啊!','火啊! 你好','生日快樂 ','雨季','雨が降りそう つやってみよう','雨雲','저분은 영어를 잘 합니다','저는 서울에서 살고 있습니다.','recording test');
ANALYZE TABLE 名字;
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
UPDATE 名字 SET c1 = '你好嗎?' WHERE MATCH(c2) AGAINST('啊!');
SELECT * FROM 名字 WHERE
MATCH(c1) AGAINST('命啊');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DELETE FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST("てみ -雨が" IN BOOLEAN MODE);
SELECT * FROM 名字 WHERE
MATCH(さようなら,j3) AGAINST('てみ');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
SELECT * FROM 名字 WHERE
MATCH (행운을빈다) AGAINST('니다');
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
0	你好嗎?	我幾好，你呢	好耐冇見	左右	左右される	☆右折⇔左折	제 이름은 Charles입니다.	운동을 좋아해요*	row value
0	救命啊!	火啊! 你好	生日快樂	雨季	雨が降りそう つやってみよう	雨雲	저분은 영어를 잘 합니다	저는 서울에서 살고 있습니다.	recording test
DELETE FROM 名字 WHERE
MATCH (행운을빈다) AGAINST('니다');
SELECT * FROM 名字;
seq	c1	c2	c3	さようなら	j2	j3	행운을빈다	k2	e1
DROP TABLE 名字;
SET GLOBAL innodb_file_per_table = 1;
SET GLOBAL innodb_ft_enable_stopword=OFF;
SET innodb_ft_enable_stopword=OFF;
CREATE TABLE ft_test3(
id INT NOT NULL,
contents TEXT,
PRIMARY KEY(id),
FULLTEXT INDEX fx_contents(contents) WITH PARSER NGRAM
) ENGINE=INNODB;
SET GLOBAL innodb_ft_aux_table='test/ft_test3';
INSERT INTO ft_test3 VALUES (1, 'xy,yz');
SELECT * FROM INFORMATION_SCHEMA.INNODB_FT_INDEX_CACHE ORDER BY position;
WORD	FIRST_DOC_ID	LAST_DOC_ID	DOC_COUNT	DOC_ID	POSITION
xy	2	2	1	2	0
yz	2	2	1	2	3
SELECT * FROM ft_test3 WHERE MATCH(contents) AGAINST('y,' IN BOOLEAN MODE);
id	contents
SELECT * FROM ft_test3 WHERE MATCH(contents) AGAINST('y,' IN NATURAl LANGUAGE MODE);
id	contents
SET GLOBAL innodb_ft_enable_stopword = ON;
SET GLOBAL innodb_ft_aux_table = @old_innodb_ft_aux_table;
DROP TABLE ft_test3;
