// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/api.proto

#include "google/protobuf/api.pb.h"

#include <algorithm>
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/extension_set.h"
#include "google/protobuf/wire_format_lite.h"
#include "google/protobuf/descriptor.h"
#include "google/protobuf/generated_message_reflection.h"
#include "google/protobuf/reflection_ops.h"
#include "google/protobuf/wire_format.h"
#include "google/protobuf/generated_message_tctable_impl.h"
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"
PROTOBUF_PRAGMA_INIT_SEG
namespace _pb = ::google::protobuf;
namespace _pbi = ::google::protobuf::internal;
namespace _fl = ::google::protobuf::internal::field_layout;
namespace google {
namespace protobuf {
        template <typename>
PROTOBUF_CONSTEXPR Api::Api(::_pbi::ConstantInitialized)
    : _impl_{
      /*decltype(_impl_._has_bits_)*/ {},
      /*decltype(_impl_._cached_size_)*/ {},
      /*decltype(_impl_.methods_)*/ {},
      /*decltype(_impl_.options_)*/ {},
      /*decltype(_impl_.mixins_)*/ {},
      /*decltype(_impl_.name_)*/ {
          &::_pbi::fixed_address_empty_string,
          ::_pbi::ConstantInitialized{},
      },
      /*decltype(_impl_.version_)*/ {
          &::_pbi::fixed_address_empty_string,
          ::_pbi::ConstantInitialized{},
      },
      /*decltype(_impl_.source_context_)*/ nullptr,
      /*decltype(_impl_.syntax_)*/ 0,
    } {}
struct ApiDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ApiDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ApiDefaultTypeInternal() {}
  union {
    Api _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_EXPORT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ApiDefaultTypeInternal _Api_default_instance_;
        template <typename>
PROTOBUF_CONSTEXPR Method::Method(::_pbi::ConstantInitialized)
    : _impl_{
      /*decltype(_impl_.options_)*/ {},
      /*decltype(_impl_.name_)*/ {
          &::_pbi::fixed_address_empty_string,
          ::_pbi::ConstantInitialized{},
      },
      /*decltype(_impl_.request_type_url_)*/ {
          &::_pbi::fixed_address_empty_string,
          ::_pbi::ConstantInitialized{},
      },
      /*decltype(_impl_.response_type_url_)*/ {
          &::_pbi::fixed_address_empty_string,
          ::_pbi::ConstantInitialized{},
      },
      /*decltype(_impl_.request_streaming_)*/ false,
      /*decltype(_impl_.response_streaming_)*/ false,
      /*decltype(_impl_.syntax_)*/ 0,
      /*decltype(_impl_._cached_size_)*/ {},
    } {}
struct MethodDefaultTypeInternal {
  PROTOBUF_CONSTEXPR MethodDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~MethodDefaultTypeInternal() {}
  union {
    Method _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_EXPORT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 MethodDefaultTypeInternal _Method_default_instance_;
        template <typename>
PROTOBUF_CONSTEXPR Mixin::Mixin(::_pbi::ConstantInitialized)
    : _impl_{
      /*decltype(_impl_.name_)*/ {
          &::_pbi::fixed_address_empty_string,
          ::_pbi::ConstantInitialized{},
      },
      /*decltype(_impl_.root_)*/ {
          &::_pbi::fixed_address_empty_string,
          ::_pbi::ConstantInitialized{},
      },
      /*decltype(_impl_._cached_size_)*/ {},
    } {}
struct MixinDefaultTypeInternal {
  PROTOBUF_CONSTEXPR MixinDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~MixinDefaultTypeInternal() {}
  union {
    Mixin _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_EXPORT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 MixinDefaultTypeInternal _Mixin_default_instance_;
}  // namespace protobuf
}  // namespace google
static ::_pb::Metadata file_level_metadata_google_2fprotobuf_2fapi_2eproto[3];
static constexpr const ::_pb::EnumDescriptor**
    file_level_enum_descriptors_google_2fprotobuf_2fapi_2eproto = nullptr;
static constexpr const ::_pb::ServiceDescriptor**
    file_level_service_descriptors_google_2fprotobuf_2fapi_2eproto = nullptr;
const ::uint32_t TableStruct_google_2fprotobuf_2fapi_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(
    protodesc_cold) = {
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Api, _impl_._has_bits_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Api, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Api, _impl_.name_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Api, _impl_.methods_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Api, _impl_.options_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Api, _impl_.version_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Api, _impl_.source_context_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Api, _impl_.mixins_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Api, _impl_.syntax_),
    ~0u,
    ~0u,
    ~0u,
    ~0u,
    0,
    ~0u,
    ~0u,
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Method, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Method, _impl_.name_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Method, _impl_.request_type_url_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Method, _impl_.request_streaming_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Method, _impl_.response_type_url_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Method, _impl_.response_streaming_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Method, _impl_.options_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Method, _impl_.syntax_),
    ~0u,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Mixin, _internal_metadata_),
    ~0u,  // no _extensions_
    ~0u,  // no _oneof_case_
    ~0u,  // no _weak_field_map_
    ~0u,  // no _inlined_string_donated_
    ~0u,  // no _split_
    ~0u,  // no sizeof(Split)
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Mixin, _impl_.name_),
    PROTOBUF_FIELD_OFFSET(::google::protobuf::Mixin, _impl_.root_),
};

static const ::_pbi::MigrationSchema
    schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
        {0, 15, -1, sizeof(::google::protobuf::Api)},
        {22, -1, -1, sizeof(::google::protobuf::Method)},
        {37, -1, -1, sizeof(::google::protobuf::Mixin)},
};

static const ::_pb::Message* const file_default_instances[] = {
    &::google::protobuf::_Api_default_instance_._instance,
    &::google::protobuf::_Method_default_instance_._instance,
    &::google::protobuf::_Mixin_default_instance_._instance,
};
const char descriptor_table_protodef_google_2fprotobuf_2fapi_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
    "\n\031google/protobuf/api.proto\022\017google.prot"
    "obuf\032$google/protobuf/source_context.pro"
    "to\032\032google/protobuf/type.proto\"\201\002\n\003Api\022\014"
    "\n\004name\030\001 \001(\t\022(\n\007methods\030\002 \003(\0132\027.google.p"
    "rotobuf.Method\022(\n\007options\030\003 \003(\0132\027.google"
    ".protobuf.Option\022\017\n\007version\030\004 \001(\t\0226\n\016sou"
    "rce_context\030\005 \001(\0132\036.google.protobuf.Sour"
    "ceContext\022&\n\006mixins\030\006 \003(\0132\026.google.proto"
    "buf.Mixin\022\'\n\006syntax\030\007 \001(\0162\027.google.proto"
    "buf.Syntax\"\325\001\n\006Method\022\014\n\004name\030\001 \001(\t\022\030\n\020r"
    "equest_type_url\030\002 \001(\t\022\031\n\021request_streami"
    "ng\030\003 \001(\010\022\031\n\021response_type_url\030\004 \001(\t\022\032\n\022r"
    "esponse_streaming\030\005 \001(\010\022(\n\007options\030\006 \003(\013"
    "2\027.google.protobuf.Option\022\'\n\006syntax\030\007 \001("
    "\0162\027.google.protobuf.Syntax\"#\n\005Mixin\022\014\n\004n"
    "ame\030\001 \001(\t\022\014\n\004root\030\002 \001(\tBv\n\023com.google.pr"
    "otobufB\010ApiProtoP\001Z,google.golang.org/pr"
    "otobuf/types/known/apipb\242\002\003GPB\252\002\036Google."
    "Protobuf.WellKnownTypesb\006proto3"
};
static const ::_pbi::DescriptorTable* const descriptor_table_google_2fprotobuf_2fapi_2eproto_deps[2] =
    {
        &::descriptor_table_google_2fprotobuf_2fsource_5fcontext_2eproto,
        &::descriptor_table_google_2fprotobuf_2ftype_2eproto,
};
static ::absl::once_flag descriptor_table_google_2fprotobuf_2fapi_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_google_2fprotobuf_2fapi_2eproto = {
    false,
    false,
    751,
    descriptor_table_protodef_google_2fprotobuf_2fapi_2eproto,
    "google/protobuf/api.proto",
    &descriptor_table_google_2fprotobuf_2fapi_2eproto_once,
    descriptor_table_google_2fprotobuf_2fapi_2eproto_deps,
    2,
    3,
    schemas,
    file_default_instances,
    TableStruct_google_2fprotobuf_2fapi_2eproto::offsets,
    file_level_metadata_google_2fprotobuf_2fapi_2eproto,
    file_level_enum_descriptors_google_2fprotobuf_2fapi_2eproto,
    file_level_service_descriptors_google_2fprotobuf_2fapi_2eproto,
};

// This function exists to be marked as weak.
// It can significantly speed up compilation by breaking up LLVM's SCC
// in the .pb.cc translation units. Large translation units see a
// reduction of more than 35% of walltime for optimized builds. Without
// the weak attribute all the messages in the file, including all the
// vtables and everything they use become part of the same SCC through
// a cycle like:
// GetMetadata -> descriptor table -> default instances ->
//   vtables -> GetMetadata
// By adding a weak function here we break the connection from the
// individual vtables back into the descriptor table.
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_google_2fprotobuf_2fapi_2eproto_getter() {
  return &descriptor_table_google_2fprotobuf_2fapi_2eproto;
}
// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2
static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_google_2fprotobuf_2fapi_2eproto(&descriptor_table_google_2fprotobuf_2fapi_2eproto);
namespace google {
namespace protobuf {
// ===================================================================

class Api::_Internal {
 public:
  using HasBits = decltype(std::declval<Api>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
    8 * PROTOBUF_FIELD_OFFSET(Api, _impl_._has_bits_);
  static const ::google::protobuf::SourceContext& source_context(const Api* msg);
  static void set_has_source_context(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::google::protobuf::SourceContext& Api::_Internal::source_context(const Api* msg) {
  return *msg->_impl_.source_context_;
}
void Api::clear_options() {
  _internal_mutable_options()->Clear();
}
void Api::clear_source_context() {
  if (_impl_.source_context_ != nullptr) _impl_.source_context_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
Api::Api(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:google.protobuf.Api)
}
Api::Api(const Api& from) : ::google::protobuf::Message() {
  Api* const _this = this;
  (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_._has_bits_){from._impl_._has_bits_},
      /*decltype(_impl_._cached_size_)*/ {},
      decltype(_impl_.methods_){from._impl_.methods_},
      decltype(_impl_.options_){from._impl_.options_},
      decltype(_impl_.mixins_){from._impl_.mixins_},
      decltype(_impl_.name_){},
      decltype(_impl_.version_){},
      decltype(_impl_.source_context_){nullptr},
      decltype(_impl_.syntax_){},
  };
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.name_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    _this->_impl_.name_.Set(from._internal_name(), _this->GetArenaForAllocation());
  }
  _impl_.version_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.version_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_version().empty()) {
    _this->_impl_.version_.Set(from._internal_version(), _this->GetArenaForAllocation());
  }
  if ((from._impl_._has_bits_[0] & 0x00000001u) != 0) {
    _this->_impl_.source_context_ = new ::google::protobuf::SourceContext(*from._impl_.source_context_);
  }
  _this->_impl_.syntax_ = from._impl_.syntax_;

  // @@protoc_insertion_point(copy_constructor:google.protobuf.Api)
}
inline void Api::SharedCtor(::_pb::Arena* arena) {
  (void)arena;
  new (&_impl_) Impl_{
      decltype(_impl_._has_bits_){},
      /*decltype(_impl_._cached_size_)*/ {},
      decltype(_impl_.methods_){arena},
      decltype(_impl_.options_){arena},
      decltype(_impl_.mixins_){arena},
      decltype(_impl_.name_){},
      decltype(_impl_.version_){},
      decltype(_impl_.source_context_){nullptr},
      decltype(_impl_.syntax_){0},
  };
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.name_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.version_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.version_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}
Api::~Api() {
  // @@protoc_insertion_point(destructor:google.protobuf.Api)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void Api::SharedDtor() {
  ABSL_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.methods_.~RepeatedPtrField();
  _impl_.options_.~RepeatedPtrField();
  _impl_.mixins_.~RepeatedPtrField();
  _impl_.name_.Destroy();
  _impl_.version_.Destroy();
  if (this != internal_default_instance()) delete _impl_.source_context_;
}
void Api::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

PROTOBUF_NOINLINE void Api::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.Api)
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_mutable_methods()->Clear();
  _internal_mutable_options()->Clear();
  _internal_mutable_mixins()->Clear();
  _impl_.name_.ClearToEmpty();
  _impl_.version_.ClearToEmpty();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.source_context_ != nullptr);
    _impl_.source_context_->Clear();
  }
  _impl_.syntax_ = 0;
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* Api::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 7, 4, 39, 2> Api::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(Api, _impl_._has_bits_),
    0, // no _extensions_
    7, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967168,  // skipmap
    offsetof(decltype(_table_), field_entries),
    7,  // num_field_entries
    4,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    &_Api_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // string name = 1;
    {::_pbi::TcParser::FastUS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(Api, _impl_.name_)}},
    // repeated .google.protobuf.Method methods = 2;
    {::_pbi::TcParser::FastMtR1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(Api, _impl_.methods_)}},
    // repeated .google.protobuf.Option options = 3;
    {::_pbi::TcParser::FastMtR1,
     {26, 63, 1, PROTOBUF_FIELD_OFFSET(Api, _impl_.options_)}},
    // string version = 4;
    {::_pbi::TcParser::FastUS1,
     {34, 63, 0, PROTOBUF_FIELD_OFFSET(Api, _impl_.version_)}},
    // .google.protobuf.SourceContext source_context = 5;
    {::_pbi::TcParser::FastMtS1,
     {42, 0, 2, PROTOBUF_FIELD_OFFSET(Api, _impl_.source_context_)}},
    // repeated .google.protobuf.Mixin mixins = 6;
    {::_pbi::TcParser::FastMtR1,
     {50, 63, 3, PROTOBUF_FIELD_OFFSET(Api, _impl_.mixins_)}},
    // .google.protobuf.Syntax syntax = 7;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(Api, _impl_.syntax_), 63>(),
     {56, 63, 0, PROTOBUF_FIELD_OFFSET(Api, _impl_.syntax_)}},
  }}, {{
    65535, 65535
  }}, {{
    // string name = 1;
    {PROTOBUF_FIELD_OFFSET(Api, _impl_.name_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // repeated .google.protobuf.Method methods = 2;
    {PROTOBUF_FIELD_OFFSET(Api, _impl_.methods_), -1, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // repeated .google.protobuf.Option options = 3;
    {PROTOBUF_FIELD_OFFSET(Api, _impl_.options_), -1, 1,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // string version = 4;
    {PROTOBUF_FIELD_OFFSET(Api, _impl_.version_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // .google.protobuf.SourceContext source_context = 5;
    {PROTOBUF_FIELD_OFFSET(Api, _impl_.source_context_), _Internal::kHasBitsOffset + 0, 2,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // repeated .google.protobuf.Mixin mixins = 6;
    {PROTOBUF_FIELD_OFFSET(Api, _impl_.mixins_), -1, 3,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // .google.protobuf.Syntax syntax = 7;
    {PROTOBUF_FIELD_OFFSET(Api, _impl_.syntax_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kOpenEnum)},
  }}, {{
    {::_pbi::TcParser::GetTable<::google::protobuf::Method>()},
    {::_pbi::TcParser::GetTable<::google::protobuf::Option>()},
    {::_pbi::TcParser::GetTable<::google::protobuf::SourceContext>()},
    {::_pbi::TcParser::GetTable<::google::protobuf::Mixin>()},
  }}, {{
    "\23\4\0\0\7\0\0\0"
    "google.protobuf.Api"
    "name"
    "version"
  }},
};

::uint8_t* Api::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.Api)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    const std::string& _s = this->_internal_name();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "google.protobuf.Api.name");
    target = stream->WriteStringMaybeAliased(1, _s, target);
  }

  // repeated .google.protobuf.Method methods = 2;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_methods_size()); i < n; i++) {
    const auto& repfield = this->_internal_methods().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(2, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .google.protobuf.Option options = 3;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_options_size()); i < n; i++) {
    const auto& repfield = this->_internal_options().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(3, repfield, repfield.GetCachedSize(), target, stream);
  }

  // string version = 4;
  if (!this->_internal_version().empty()) {
    const std::string& _s = this->_internal_version();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "google.protobuf.Api.version");
    target = stream->WriteStringMaybeAliased(4, _s, target);
  }

  cached_has_bits = _impl_._has_bits_[0];
  // .google.protobuf.SourceContext source_context = 5;
  if (cached_has_bits & 0x00000001u) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessage(5, _Internal::source_context(this),
        _Internal::source_context(this).GetCachedSize(), target, stream);
  }

  // repeated .google.protobuf.Mixin mixins = 6;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_mixins_size()); i < n; i++) {
    const auto& repfield = this->_internal_mixins().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(6, repfield, repfield.GetCachedSize(), target, stream);
  }

  // .google.protobuf.Syntax syntax = 7;
  if (this->_internal_syntax() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
        7, this->_internal_syntax(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.Api)
  return target;
}

::size_t Api::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.Api)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .google.protobuf.Method methods = 2;
  total_size += 1UL * this->_internal_methods_size();
  for (const auto& msg : this->_internal_methods()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // repeated .google.protobuf.Option options = 3;
  total_size += 1UL * this->_internal_options_size();
  for (const auto& msg : this->_internal_options()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // repeated .google.protobuf.Mixin mixins = 6;
  total_size += 1UL * this->_internal_mixins_size();
  for (const auto& msg : this->_internal_mixins()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_name());
  }

  // string version = 4;
  if (!this->_internal_version().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_version());
  }

  // .google.protobuf.SourceContext source_context = 5;
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *_impl_.source_context_);
  }

  // .google.protobuf.Syntax syntax = 7;
  if (this->_internal_syntax() != 0) {
    total_size += 1 +
                  ::_pbi::WireFormatLite::EnumSize(this->_internal_syntax());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData Api::_class_data_ = {
    ::google::protobuf::Message::CopyWithSourceCheck,
    Api::MergeImpl
};
const ::google::protobuf::Message::ClassData*Api::GetClassData() const { return &_class_data_; }


void Api::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<Api*>(&to_msg);
  auto& from = static_cast<const Api&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.Api)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_methods()->MergeFrom(from._internal_methods());
  _this->_internal_mutable_options()->MergeFrom(from._internal_options());
  _this->_internal_mutable_mixins()->MergeFrom(from._internal_mixins());
  if (!from._internal_name().empty()) {
    _this->_internal_set_name(from._internal_name());
  }
  if (!from._internal_version().empty()) {
    _this->_internal_set_version(from._internal_version());
  }
  if ((from._impl_._has_bits_[0] & 0x00000001u) != 0) {
    _this->_internal_mutable_source_context()->::google::protobuf::SourceContext::MergeFrom(
        from._internal_source_context());
  }
  if (from._internal_syntax() != 0) {
    _this->_internal_set_syntax(from._internal_syntax());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void Api::CopyFrom(const Api& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.Api)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool Api::IsInitialized() const {
  return true;
}

void Api::InternalSwap(Api* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.methods_.InternalSwap(&other->_impl_.methods_);
  _impl_.options_.InternalSwap(&other->_impl_.options_);
  _impl_.mixins_.InternalSwap(&other->_impl_.mixins_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.name_, lhs_arena,
                                       &other->_impl_.name_, rhs_arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.version_, lhs_arena,
                                       &other->_impl_.version_, rhs_arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Api, _impl_.syntax_)
      + sizeof(Api::_impl_.syntax_)
      - PROTOBUF_FIELD_OFFSET(Api, _impl_.source_context_)>(
          reinterpret_cast<char*>(&_impl_.source_context_),
          reinterpret_cast<char*>(&other->_impl_.source_context_));
}

::google::protobuf::Metadata Api::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2fapi_2eproto_getter, &descriptor_table_google_2fprotobuf_2fapi_2eproto_once,
      file_level_metadata_google_2fprotobuf_2fapi_2eproto[0]);
}
// ===================================================================

class Method::_Internal {
 public:
};

void Method::clear_options() {
  _internal_mutable_options()->Clear();
}
Method::Method(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:google.protobuf.Method)
}
Method::Method(const Method& from) : ::google::protobuf::Message() {
  Method* const _this = this;
  (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.options_){from._impl_.options_},
      decltype(_impl_.name_){},
      decltype(_impl_.request_type_url_){},
      decltype(_impl_.response_type_url_){},
      decltype(_impl_.request_streaming_){},
      decltype(_impl_.response_streaming_){},
      decltype(_impl_.syntax_){},
      /*decltype(_impl_._cached_size_)*/ {},
  };
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.name_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    _this->_impl_.name_.Set(from._internal_name(), _this->GetArenaForAllocation());
  }
  _impl_.request_type_url_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.request_type_url_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_request_type_url().empty()) {
    _this->_impl_.request_type_url_.Set(from._internal_request_type_url(), _this->GetArenaForAllocation());
  }
  _impl_.response_type_url_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.response_type_url_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_response_type_url().empty()) {
    _this->_impl_.response_type_url_.Set(from._internal_response_type_url(), _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.request_streaming_, &from._impl_.request_streaming_,
    static_cast<::size_t>(reinterpret_cast<char*>(&_impl_.syntax_) -
    reinterpret_cast<char*>(&_impl_.request_streaming_)) + sizeof(_impl_.syntax_));

  // @@protoc_insertion_point(copy_constructor:google.protobuf.Method)
}
inline void Method::SharedCtor(::_pb::Arena* arena) {
  (void)arena;
  new (&_impl_) Impl_{
      decltype(_impl_.options_){arena},
      decltype(_impl_.name_){},
      decltype(_impl_.request_type_url_){},
      decltype(_impl_.response_type_url_){},
      decltype(_impl_.request_streaming_){false},
      decltype(_impl_.response_streaming_){false},
      decltype(_impl_.syntax_){0},
      /*decltype(_impl_._cached_size_)*/ {},
  };
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.name_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.request_type_url_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.request_type_url_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.response_type_url_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.response_type_url_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}
Method::~Method() {
  // @@protoc_insertion_point(destructor:google.protobuf.Method)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void Method::SharedDtor() {
  ABSL_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.options_.~RepeatedPtrField();
  _impl_.name_.Destroy();
  _impl_.request_type_url_.Destroy();
  _impl_.response_type_url_.Destroy();
}
void Method::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

PROTOBUF_NOINLINE void Method::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.Method)
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_mutable_options()->Clear();
  _impl_.name_.ClearToEmpty();
  _impl_.request_type_url_.ClearToEmpty();
  _impl_.response_type_url_.ClearToEmpty();
  ::memset(&_impl_.request_streaming_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.syntax_) -
      reinterpret_cast<char*>(&_impl_.request_streaming_)) + sizeof(_impl_.syntax_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* Method::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 7, 1, 68, 2> Method::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    7, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967168,  // skipmap
    offsetof(decltype(_table_), field_entries),
    7,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    &_Method_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // string name = 1;
    {::_pbi::TcParser::FastUS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(Method, _impl_.name_)}},
    // string request_type_url = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(Method, _impl_.request_type_url_)}},
    // bool request_streaming = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<bool, offsetof(Method, _impl_.request_streaming_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(Method, _impl_.request_streaming_)}},
    // string response_type_url = 4;
    {::_pbi::TcParser::FastUS1,
     {34, 63, 0, PROTOBUF_FIELD_OFFSET(Method, _impl_.response_type_url_)}},
    // bool response_streaming = 5;
    {::_pbi::TcParser::SingularVarintNoZag1<bool, offsetof(Method, _impl_.response_streaming_), 63>(),
     {40, 63, 0, PROTOBUF_FIELD_OFFSET(Method, _impl_.response_streaming_)}},
    // repeated .google.protobuf.Option options = 6;
    {::_pbi::TcParser::FastMtR1,
     {50, 63, 0, PROTOBUF_FIELD_OFFSET(Method, _impl_.options_)}},
    // .google.protobuf.Syntax syntax = 7;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(Method, _impl_.syntax_), 63>(),
     {56, 63, 0, PROTOBUF_FIELD_OFFSET(Method, _impl_.syntax_)}},
  }}, {{
    65535, 65535
  }}, {{
    // string name = 1;
    {PROTOBUF_FIELD_OFFSET(Method, _impl_.name_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string request_type_url = 2;
    {PROTOBUF_FIELD_OFFSET(Method, _impl_.request_type_url_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // bool request_streaming = 3;
    {PROTOBUF_FIELD_OFFSET(Method, _impl_.request_streaming_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBool)},
    // string response_type_url = 4;
    {PROTOBUF_FIELD_OFFSET(Method, _impl_.response_type_url_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // bool response_streaming = 5;
    {PROTOBUF_FIELD_OFFSET(Method, _impl_.response_streaming_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBool)},
    // repeated .google.protobuf.Option options = 6;
    {PROTOBUF_FIELD_OFFSET(Method, _impl_.options_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // .google.protobuf.Syntax syntax = 7;
    {PROTOBUF_FIELD_OFFSET(Method, _impl_.syntax_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kOpenEnum)},
  }}, {{
    {::_pbi::TcParser::GetTable<::google::protobuf::Option>()},
  }}, {{
    "\26\4\20\0\21\0\0\0"
    "google.protobuf.Method"
    "name"
    "request_type_url"
    "response_type_url"
  }},
};

::uint8_t* Method::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.Method)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    const std::string& _s = this->_internal_name();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "google.protobuf.Method.name");
    target = stream->WriteStringMaybeAliased(1, _s, target);
  }

  // string request_type_url = 2;
  if (!this->_internal_request_type_url().empty()) {
    const std::string& _s = this->_internal_request_type_url();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "google.protobuf.Method.request_type_url");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  // bool request_streaming = 3;
  if (this->_internal_request_streaming() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(
        3, this->_internal_request_streaming(), target);
  }

  // string response_type_url = 4;
  if (!this->_internal_response_type_url().empty()) {
    const std::string& _s = this->_internal_response_type_url();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "google.protobuf.Method.response_type_url");
    target = stream->WriteStringMaybeAliased(4, _s, target);
  }

  // bool response_streaming = 5;
  if (this->_internal_response_streaming() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(
        5, this->_internal_response_streaming(), target);
  }

  // repeated .google.protobuf.Option options = 6;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_options_size()); i < n; i++) {
    const auto& repfield = this->_internal_options().Get(i);
    target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessage(6, repfield, repfield.GetCachedSize(), target, stream);
  }

  // .google.protobuf.Syntax syntax = 7;
  if (this->_internal_syntax() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
        7, this->_internal_syntax(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.Method)
  return target;
}

::size_t Method::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.Method)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .google.protobuf.Option options = 6;
  total_size += 1UL * this->_internal_options_size();
  for (const auto& msg : this->_internal_options()) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
  }
  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_name());
  }

  // string request_type_url = 2;
  if (!this->_internal_request_type_url().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_request_type_url());
  }

  // string response_type_url = 4;
  if (!this->_internal_response_type_url().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_response_type_url());
  }

  // bool request_streaming = 3;
  if (this->_internal_request_streaming() != 0) {
    total_size += 2;
  }

  // bool response_streaming = 5;
  if (this->_internal_response_streaming() != 0) {
    total_size += 2;
  }

  // .google.protobuf.Syntax syntax = 7;
  if (this->_internal_syntax() != 0) {
    total_size += 1 +
                  ::_pbi::WireFormatLite::EnumSize(this->_internal_syntax());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData Method::_class_data_ = {
    ::google::protobuf::Message::CopyWithSourceCheck,
    Method::MergeImpl
};
const ::google::protobuf::Message::ClassData*Method::GetClassData() const { return &_class_data_; }


void Method::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<Method*>(&to_msg);
  auto& from = static_cast<const Method&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.Method)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_options()->MergeFrom(from._internal_options());
  if (!from._internal_name().empty()) {
    _this->_internal_set_name(from._internal_name());
  }
  if (!from._internal_request_type_url().empty()) {
    _this->_internal_set_request_type_url(from._internal_request_type_url());
  }
  if (!from._internal_response_type_url().empty()) {
    _this->_internal_set_response_type_url(from._internal_response_type_url());
  }
  if (from._internal_request_streaming() != 0) {
    _this->_internal_set_request_streaming(from._internal_request_streaming());
  }
  if (from._internal_response_streaming() != 0) {
    _this->_internal_set_response_streaming(from._internal_response_streaming());
  }
  if (from._internal_syntax() != 0) {
    _this->_internal_set_syntax(from._internal_syntax());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void Method::CopyFrom(const Method& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.Method)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool Method::IsInitialized() const {
  return true;
}

void Method::InternalSwap(Method* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.options_.InternalSwap(&other->_impl_.options_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.name_, lhs_arena,
                                       &other->_impl_.name_, rhs_arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.request_type_url_, lhs_arena,
                                       &other->_impl_.request_type_url_, rhs_arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.response_type_url_, lhs_arena,
                                       &other->_impl_.response_type_url_, rhs_arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Method, _impl_.syntax_)
      + sizeof(Method::_impl_.syntax_)
      - PROTOBUF_FIELD_OFFSET(Method, _impl_.request_streaming_)>(
          reinterpret_cast<char*>(&_impl_.request_streaming_),
          reinterpret_cast<char*>(&other->_impl_.request_streaming_));
}

::google::protobuf::Metadata Method::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2fapi_2eproto_getter, &descriptor_table_google_2fprotobuf_2fapi_2eproto_once,
      file_level_metadata_google_2fprotobuf_2fapi_2eproto[1]);
}
// ===================================================================

class Mixin::_Internal {
 public:
};

Mixin::Mixin(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:google.protobuf.Mixin)
}
Mixin::Mixin(const Mixin& from) : ::google::protobuf::Message() {
  Mixin* const _this = this;
  (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.name_){},
      decltype(_impl_.root_){},
      /*decltype(_impl_._cached_size_)*/ {},
  };
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.name_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    _this->_impl_.name_.Set(from._internal_name(), _this->GetArenaForAllocation());
  }
  _impl_.root_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.root_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_root().empty()) {
    _this->_impl_.root_.Set(from._internal_root(), _this->GetArenaForAllocation());
  }

  // @@protoc_insertion_point(copy_constructor:google.protobuf.Mixin)
}
inline void Mixin::SharedCtor(::_pb::Arena* arena) {
  (void)arena;
  new (&_impl_) Impl_{
      decltype(_impl_.name_){},
      decltype(_impl_.root_){},
      /*decltype(_impl_._cached_size_)*/ {},
  };
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.name_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.root_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        _impl_.root_.Set("", GetArenaForAllocation());
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}
Mixin::~Mixin() {
  // @@protoc_insertion_point(destructor:google.protobuf.Mixin)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void Mixin::SharedDtor() {
  ABSL_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.name_.Destroy();
  _impl_.root_.Destroy();
}
void Mixin::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

PROTOBUF_NOINLINE void Mixin::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.Mixin)
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.name_.ClearToEmpty();
  _impl_.root_.ClearToEmpty();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

const char* Mixin::_InternalParse(
    const char* ptr, ::_pbi::ParseContext* ctx) {
  ptr = ::_pbi::TcParser::ParseLoop(this, ptr, ctx, &_table_.header);
  return ptr;
}


PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 0, 38, 2> Mixin::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_Mixin_default_instance_._instance,
    ::_pbi::TcParser::GenericFallback,  // fallback
  }, {{
    // string root = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(Mixin, _impl_.root_)}},
    // string name = 1;
    {::_pbi::TcParser::FastUS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(Mixin, _impl_.name_)}},
  }}, {{
    65535, 65535
  }}, {{
    // string name = 1;
    {PROTOBUF_FIELD_OFFSET(Mixin, _impl_.name_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string root = 2;
    {PROTOBUF_FIELD_OFFSET(Mixin, _impl_.root_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
  }},
  // no aux_entries
  {{
    "\25\4\4\0\0\0\0\0"
    "google.protobuf.Mixin"
    "name"
    "root"
  }},
};

::uint8_t* Mixin::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.Mixin)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    const std::string& _s = this->_internal_name();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "google.protobuf.Mixin.name");
    target = stream->WriteStringMaybeAliased(1, _s, target);
  }

  // string root = 2;
  if (!this->_internal_root().empty()) {
    const std::string& _s = this->_internal_root();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "google.protobuf.Mixin.root");
    target = stream->WriteStringMaybeAliased(2, _s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.Mixin)
  return target;
}

::size_t Mixin::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.Mixin)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_name());
  }

  // string root = 2;
  if (!this->_internal_root().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_root());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::google::protobuf::Message::ClassData Mixin::_class_data_ = {
    ::google::protobuf::Message::CopyWithSourceCheck,
    Mixin::MergeImpl
};
const ::google::protobuf::Message::ClassData*Mixin::GetClassData() const { return &_class_data_; }


void Mixin::MergeImpl(::google::protobuf::Message& to_msg, const ::google::protobuf::Message& from_msg) {
  auto* const _this = static_cast<Mixin*>(&to_msg);
  auto& from = static_cast<const Mixin&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.Mixin)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _this->_internal_set_name(from._internal_name());
  }
  if (!from._internal_root().empty()) {
    _this->_internal_set_root(from._internal_root());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void Mixin::CopyFrom(const Mixin& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.Mixin)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool Mixin::IsInitialized() const {
  return true;
}

void Mixin::InternalSwap(Mixin* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.name_, lhs_arena,
                                       &other->_impl_.name_, rhs_arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.root_, lhs_arena,
                                       &other->_impl_.root_, rhs_arena);
}

::google::protobuf::Metadata Mixin::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2fapi_2eproto_getter, &descriptor_table_google_2fprotobuf_2fapi_2eproto_once,
      file_level_metadata_google_2fprotobuf_2fapi_2eproto[2]);
}
// @@protoc_insertion_point(namespace_scope)
}  // namespace protobuf
}  // namespace google
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google
// @@protoc_insertion_point(global_scope)
#include "google/protobuf/port_undef.inc"
