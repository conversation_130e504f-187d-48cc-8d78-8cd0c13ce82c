// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: google/protobuf/test_messages_proto2.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace ProtobufTestMessages.Proto2 {

  /// <summary>Holder for reflection information generated from google/protobuf/test_messages_proto2.proto</summary>
  public static partial class TestMessagesProto2Reflection {

    #region Descriptor
    /// <summary>File descriptor for google/protobuf/test_messages_proto2.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static TestMessagesProto2Reflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cipnb29nbGUvcHJvdG9idWYvdGVzdF9tZXNzYWdlc19wcm90bzIucHJvdG8S",
            "HXByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8yIsQ+ChJUZXN0QWxsVHlw",
            "ZXNQcm90bzISFgoOb3B0aW9uYWxfaW50MzIYASABKAUSFgoOb3B0aW9uYWxf",
            "aW50NjQYAiABKAMSFwoPb3B0aW9uYWxfdWludDMyGAMgASgNEhcKD29wdGlv",
            "bmFsX3VpbnQ2NBgEIAEoBBIXCg9vcHRpb25hbF9zaW50MzIYBSABKBESFwoP",
            "b3B0aW9uYWxfc2ludDY0GAYgASgSEhgKEG9wdGlvbmFsX2ZpeGVkMzIYByAB",
            "KAcSGAoQb3B0aW9uYWxfZml4ZWQ2NBgIIAEoBhIZChFvcHRpb25hbF9zZml4",
            "ZWQzMhgJIAEoDxIZChFvcHRpb25hbF9zZml4ZWQ2NBgKIAEoEBIWCg5vcHRp",
            "b25hbF9mbG9hdBgLIAEoAhIXCg9vcHRpb25hbF9kb3VibGUYDCABKAESFQoN",
            "b3B0aW9uYWxfYm9vbBgNIAEoCBIXCg9vcHRpb25hbF9zdHJpbmcYDiABKAkS",
            "FgoOb3B0aW9uYWxfYnl0ZXMYDyABKAwSYAoXb3B0aW9uYWxfbmVzdGVkX21l",
            "c3NhZ2UYEiABKAsyPy5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5U",
            "ZXN0QWxsVHlwZXNQcm90bzIuTmVzdGVkTWVzc2FnZRJVChhvcHRpb25hbF9m",
            "b3JlaWduX21lc3NhZ2UYEyABKAsyMy5wcm90b2J1Zl90ZXN0X21lc3NhZ2Vz",
            "LnByb3RvMi5Gb3JlaWduTWVzc2FnZVByb3RvMhJaChRvcHRpb25hbF9uZXN0",
            "ZWRfZW51bRgVIAEoDjI8LnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8y",
            "LlRlc3RBbGxUeXBlc1Byb3RvMi5OZXN0ZWRFbnVtEk8KFW9wdGlvbmFsX2Zv",
            "cmVpZ25fZW51bRgWIAEoDjIwLnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJv",
            "dG8yLkZvcmVpZ25FbnVtUHJvdG8yEiEKFW9wdGlvbmFsX3N0cmluZ19waWVj",
            "ZRgYIAEoCUICCAISGQoNb3B0aW9uYWxfY29yZBgZIAEoCUICCAESTAoRcmVj",
            "dXJzaXZlX21lc3NhZ2UYGyABKAsyMS5wcm90b2J1Zl90ZXN0X21lc3NhZ2Vz",
            "LnByb3RvMi5UZXN0QWxsVHlwZXNQcm90bzISFgoOcmVwZWF0ZWRfaW50MzIY",
            "HyADKAUSFgoOcmVwZWF0ZWRfaW50NjQYICADKAMSFwoPcmVwZWF0ZWRfdWlu",
            "dDMyGCEgAygNEhcKD3JlcGVhdGVkX3VpbnQ2NBgiIAMoBBIXCg9yZXBlYXRl",
            "ZF9zaW50MzIYIyADKBESFwoPcmVwZWF0ZWRfc2ludDY0GCQgAygSEhgKEHJl",
            "cGVhdGVkX2ZpeGVkMzIYJSADKAcSGAoQcmVwZWF0ZWRfZml4ZWQ2NBgmIAMo",
            "BhIZChFyZXBlYXRlZF9zZml4ZWQzMhgnIAMoDxIZChFyZXBlYXRlZF9zZml4",
            "ZWQ2NBgoIAMoEBIWCg5yZXBlYXRlZF9mbG9hdBgpIAMoAhIXCg9yZXBlYXRl",
            "ZF9kb3VibGUYKiADKAESFQoNcmVwZWF0ZWRfYm9vbBgrIAMoCBIXCg9yZXBl",
            "YXRlZF9zdHJpbmcYLCADKAkSFgoOcmVwZWF0ZWRfYnl0ZXMYLSADKAwSYAoX",
            "cmVwZWF0ZWRfbmVzdGVkX21lc3NhZ2UYMCADKAsyPy5wcm90b2J1Zl90ZXN0",
            "X21lc3NhZ2VzLnByb3RvMi5UZXN0QWxsVHlwZXNQcm90bzIuTmVzdGVkTWVz",
            "c2FnZRJVChhyZXBlYXRlZF9mb3JlaWduX21lc3NhZ2UYMSADKAsyMy5wcm90",
            "b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5Gb3JlaWduTWVzc2FnZVByb3Rv",
            "MhJaChRyZXBlYXRlZF9uZXN0ZWRfZW51bRgzIAMoDjI8LnByb3RvYnVmX3Rl",
            "c3RfbWVzc2FnZXMucHJvdG8yLlRlc3RBbGxUeXBlc1Byb3RvMi5OZXN0ZWRF",
            "bnVtEk8KFXJlcGVhdGVkX2ZvcmVpZ25fZW51bRg0IAMoDjIwLnByb3RvYnVm",
            "X3Rlc3RfbWVzc2FnZXMucHJvdG8yLkZvcmVpZ25FbnVtUHJvdG8yEiEKFXJl",
            "cGVhdGVkX3N0cmluZ19waWVjZRg2IAMoCUICCAISGQoNcmVwZWF0ZWRfY29y",
            "ZBg3IAMoCUICCAESGAoMcGFja2VkX2ludDMyGEsgAygFQgIQARIYCgxwYWNr",
            "ZWRfaW50NjQYTCADKANCAhABEhkKDXBhY2tlZF91aW50MzIYTSADKA1CAhAB",
            "EhkKDXBhY2tlZF91aW50NjQYTiADKARCAhABEhkKDXBhY2tlZF9zaW50MzIY",
            "TyADKBFCAhABEhkKDXBhY2tlZF9zaW50NjQYUCADKBJCAhABEhoKDnBhY2tl",
            "ZF9maXhlZDMyGFEgAygHQgIQARIaCg5wYWNrZWRfZml4ZWQ2NBhSIAMoBkIC",
            "EAESGwoPcGFja2VkX3NmaXhlZDMyGFMgAygPQgIQARIbCg9wYWNrZWRfc2Zp",
            "eGVkNjQYVCADKBBCAhABEhgKDHBhY2tlZF9mbG9hdBhVIAMoAkICEAESGQoN",
            "cGFja2VkX2RvdWJsZRhWIAMoAUICEAESFwoLcGFja2VkX2Jvb2wYVyADKAhC",
            "AhABElwKEnBhY2tlZF9uZXN0ZWRfZW51bRhYIAMoDjI8LnByb3RvYnVmX3Rl",
            "c3RfbWVzc2FnZXMucHJvdG8yLlRlc3RBbGxUeXBlc1Byb3RvMi5OZXN0ZWRF",
            "bnVtQgIQARIaCg51bnBhY2tlZF9pbnQzMhhZIAMoBUICEAASGgoOdW5wYWNr",
            "ZWRfaW50NjQYWiADKANCAhAAEhsKD3VucGFja2VkX3VpbnQzMhhbIAMoDUIC",
            "EAASGwoPdW5wYWNrZWRfdWludDY0GFwgAygEQgIQABIbCg91bnBhY2tlZF9z",
            "aW50MzIYXSADKBFCAhAAEhsKD3VucGFja2VkX3NpbnQ2NBheIAMoEkICEAAS",
            "HAoQdW5wYWNrZWRfZml4ZWQzMhhfIAMoB0ICEAASHAoQdW5wYWNrZWRfZml4",
            "ZWQ2NBhgIAMoBkICEAASHQoRdW5wYWNrZWRfc2ZpeGVkMzIYYSADKA9CAhAA",
            "Eh0KEXVucGFja2VkX3NmaXhlZDY0GGIgAygQQgIQABIaCg51bnBhY2tlZF9m",
            "bG9hdBhjIAMoAkICEAASGwoPdW5wYWNrZWRfZG91YmxlGGQgAygBQgIQABIZ",
            "Cg11bnBhY2tlZF9ib29sGGUgAygIQgIQABJeChR1bnBhY2tlZF9uZXN0ZWRf",
            "ZW51bRhmIAMoDjI8LnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8yLlRl",
            "c3RBbGxUeXBlc1Byb3RvMi5OZXN0ZWRFbnVtQgIQABJdCg9tYXBfaW50MzJf",
            "aW50MzIYOCADKAsyRC5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5U",
            "ZXN0QWxsVHlwZXNQcm90bzIuTWFwSW50MzJJbnQzMkVudHJ5El0KD21hcF9p",
            "bnQ2NF9pbnQ2NBg5IAMoCzJELnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJv",
            "dG8yLlRlc3RBbGxUeXBlc1Byb3RvMi5NYXBJbnQ2NEludDY0RW50cnkSYQoR",
            "bWFwX3VpbnQzMl91aW50MzIYOiADKAsyRi5wcm90b2J1Zl90ZXN0X21lc3Nh",
            "Z2VzLnByb3RvMi5UZXN0QWxsVHlwZXNQcm90bzIuTWFwVWludDMyVWludDMy",
            "RW50cnkSYQoRbWFwX3VpbnQ2NF91aW50NjQYOyADKAsyRi5wcm90b2J1Zl90",
            "ZXN0X21lc3NhZ2VzLnByb3RvMi5UZXN0QWxsVHlwZXNQcm90bzIuTWFwVWlu",
            "dDY0VWludDY0RW50cnkSYQoRbWFwX3NpbnQzMl9zaW50MzIYPCADKAsyRi5w",
            "cm90b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5UZXN0QWxsVHlwZXNQcm90",
            "bzIuTWFwU2ludDMyU2ludDMyRW50cnkSYQoRbWFwX3NpbnQ2NF9zaW50NjQY",
            "PSADKAsyRi5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5UZXN0QWxs",
            "VHlwZXNQcm90bzIuTWFwU2ludDY0U2ludDY0RW50cnkSZQoTbWFwX2ZpeGVk",
            "MzJfZml4ZWQzMhg+IAMoCzJILnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJv",
            "dG8yLlRlc3RBbGxUeXBlc1Byb3RvMi5NYXBGaXhlZDMyRml4ZWQzMkVudHJ5",
            "EmUKE21hcF9maXhlZDY0X2ZpeGVkNjQYPyADKAsySC5wcm90b2J1Zl90ZXN0",
            "X21lc3NhZ2VzLnByb3RvMi5UZXN0QWxsVHlwZXNQcm90bzIuTWFwRml4ZWQ2",
            "NEZpeGVkNjRFbnRyeRJpChVtYXBfc2ZpeGVkMzJfc2ZpeGVkMzIYQCADKAsy",
            "Si5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5UZXN0QWxsVHlwZXNQ",
            "cm90bzIuTWFwU2ZpeGVkMzJTZml4ZWQzMkVudHJ5EmkKFW1hcF9zZml4ZWQ2",
            "NF9zZml4ZWQ2NBhBIAMoCzJKLnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJv",
            "dG8yLlRlc3RBbGxUeXBlc1Byb3RvMi5NYXBTZml4ZWQ2NFNmaXhlZDY0RW50",
            "cnkSXQoPbWFwX2ludDMyX2Zsb2F0GEIgAygLMkQucHJvdG9idWZfdGVzdF9t",
            "ZXNzYWdlcy5wcm90bzIuVGVzdEFsbFR5cGVzUHJvdG8yLk1hcEludDMyRmxv",
            "YXRFbnRyeRJfChBtYXBfaW50MzJfZG91YmxlGEMgAygLMkUucHJvdG9idWZf",
            "dGVzdF9tZXNzYWdlcy5wcm90bzIuVGVzdEFsbFR5cGVzUHJvdG8yLk1hcElu",
            "dDMyRG91YmxlRW50cnkSWQoNbWFwX2Jvb2xfYm9vbBhEIAMoCzJCLnByb3Rv",
            "YnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8yLlRlc3RBbGxUeXBlc1Byb3RvMi5N",
            "YXBCb29sQm9vbEVudHJ5EmEKEW1hcF9zdHJpbmdfc3RyaW5nGEUgAygLMkYu",
            "cHJvdG9idWZfdGVzdF9tZXNzYWdlcy5wcm90bzIuVGVzdEFsbFR5cGVzUHJv",
            "dG8yLk1hcFN0cmluZ1N0cmluZ0VudHJ5El8KEG1hcF9zdHJpbmdfYnl0ZXMY",
            "RiADKAsyRS5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5UZXN0QWxs",
            "VHlwZXNQcm90bzIuTWFwU3RyaW5nQnl0ZXNFbnRyeRJwChltYXBfc3RyaW5n",
            "X25lc3RlZF9tZXNzYWdlGEcgAygLMk0ucHJvdG9idWZfdGVzdF9tZXNzYWdl",
            "cy5wcm90bzIuVGVzdEFsbFR5cGVzUHJvdG8yLk1hcFN0cmluZ05lc3RlZE1l",
            "c3NhZ2VFbnRyeRJyChptYXBfc3RyaW5nX2ZvcmVpZ25fbWVzc2FnZRhIIAMo",
            "CzJOLnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8yLlRlc3RBbGxUeXBl",
            "c1Byb3RvMi5NYXBTdHJpbmdGb3JlaWduTWVzc2FnZUVudHJ5EmoKFm1hcF9z",
            "dHJpbmdfbmVzdGVkX2VudW0YSSADKAsySi5wcm90b2J1Zl90ZXN0X21lc3Nh",
            "Z2VzLnByb3RvMi5UZXN0QWxsVHlwZXNQcm90bzIuTWFwU3RyaW5nTmVzdGVk",
            "RW51bUVudHJ5EmwKF21hcF9zdHJpbmdfZm9yZWlnbl9lbnVtGEogAygLMksu",
            "cHJvdG9idWZfdGVzdF9tZXNzYWdlcy5wcm90bzIuVGVzdEFsbFR5cGVzUHJv",
            "dG8yLk1hcFN0cmluZ0ZvcmVpZ25FbnVtRW50cnkSFgoMb25lb2ZfdWludDMy",
            "GG8gASgNSAASXwoUb25lb2ZfbmVzdGVkX21lc3NhZ2UYcCABKAsyPy5wcm90",
            "b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5UZXN0QWxsVHlwZXNQcm90bzIu",
            "TmVzdGVkTWVzc2FnZUgAEhYKDG9uZW9mX3N0cmluZxhxIAEoCUgAEhUKC29u",
            "ZW9mX2J5dGVzGHIgASgMSAASFAoKb25lb2ZfYm9vbBhzIAEoCEgAEhYKDG9u",
            "ZW9mX3VpbnQ2NBh0IAEoBEgAEhUKC29uZW9mX2Zsb2F0GHUgASgCSAASFgoM",
            "b25lb2ZfZG91YmxlGHYgASgBSAASUgoKb25lb2ZfZW51bRh3IAEoDjI8LnBy",
            "b3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8yLlRlc3RBbGxUeXBlc1Byb3Rv",
            "Mi5OZXN0ZWRFbnVtSAASRQoEZGF0YRjJASABKAoyNi5wcm90b2J1Zl90ZXN0",
            "X21lc3NhZ2VzLnByb3RvMi5UZXN0QWxsVHlwZXNQcm90bzIuRGF0YRIiCg1k",
            "ZWZhdWx0X2ludDMyGPEBIAEoBToKLTEyMzQ1Njc4ORIsCg1kZWZhdWx0X2lu",
            "dDY0GPIBIAEoAzoULTkxMjM0NTY3ODkxMjM0NTY3ODkSIwoOZGVmYXVsdF91",
            "aW50MzIY8wEgASgNOgoyMTIzNDU2Nzg5Ei0KDmRlZmF1bHRfdWludDY0GPQB",
            "IAEoBDoUMTAxMjM0NTY3ODkxMjM0NTY3ODkSIwoOZGVmYXVsdF9zaW50MzIY",
            "9QEgASgROgotMTIzNDU2Nzg5Ei0KDmRlZmF1bHRfc2ludDY0GPYBIAEoEjoU",
            "LTkxMjM0NTY3ODkxMjM0NTY3ODkSJAoPZGVmYXVsdF9maXhlZDMyGPcBIAEo",
            "BzoKMjEyMzQ1Njc4ORIuCg9kZWZhdWx0X2ZpeGVkNjQY+AEgASgGOhQxMDEy",
            "MzQ1Njc4OTEyMzQ1Njc4ORIlChBkZWZhdWx0X3NmaXhlZDMyGPkBIAEoDzoK",
            "LTEyMzQ1Njc4ORIvChBkZWZhdWx0X3NmaXhlZDY0GPoBIAEoEDoULTkxMjM0",
            "NTY3ODkxMjM0NTY3ODkSHQoNZGVmYXVsdF9mbG9hdBj7ASABKAI6BTllKzA5",
            "Eh4KDmRlZmF1bHRfZG91YmxlGPwBIAEoAToFN2UrMjISGwoMZGVmYXVsdF9i",
            "b29sGP0BIAEoCDoEdHJ1ZRIgCg5kZWZhdWx0X3N0cmluZxj+ASABKAk6B1Jv",
            "c2VidWQSHgoNZGVmYXVsdF9ieXRlcxj/ASABKAw6Bmpvc2h1YRITCgpmaWVs",
            "ZG5hbWUxGJEDIAEoBRIUCgtmaWVsZF9uYW1lMhiSAyABKAUSFQoMX2ZpZWxk",
            "X25hbWUzGJMDIAEoBRIWCg1maWVsZF9fbmFtZTRfGJQDIAEoBRIUCgtmaWVs",
            "ZDBuYW1lNRiVAyABKAUSFgoNZmllbGRfMF9uYW1lNhiWAyABKAUSEwoKZmll",
            "bGROYW1lNxiXAyABKAUSEwoKRmllbGROYW1lOBiYAyABKAUSFAoLZmllbGRf",
            "TmFtZTkYmQMgASgFEhUKDEZpZWxkX05hbWUxMBiaAyABKAUSFQoMRklFTERf",
            "TkFNRTExGJsDIAEoBRIVCgxGSUVMRF9uYW1lMTIYnAMgASgFEhcKDl9fZmll",
            "bGRfbmFtZTEzGJ0DIAEoBRIXCg5fX0ZpZWxkX25hbWUxNBieAyABKAUSFgoN",
            "ZmllbGRfX25hbWUxNRifAyABKAUSFgoNZmllbGRfX05hbWUxNhigAyABKAUS",
            "FwoOZmllbGRfbmFtZTE3X18YoQMgASgFEhcKDkZpZWxkX25hbWUxOF9fGKID",
            "IAEoBRpiCg1OZXN0ZWRNZXNzYWdlEgkKAWEYASABKAUSRgoLY29yZWN1cnNp",
            "dmUYAiABKAsyMS5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5UZXN0",
            "QWxsVHlwZXNQcm90bzIaNAoSTWFwSW50MzJJbnQzMkVudHJ5EgsKA2tleRgB",
            "IAEoBRINCgV2YWx1ZRgCIAEoBToCOAEaNAoSTWFwSW50NjRJbnQ2NEVudHJ5",
            "EgsKA2tleRgBIAEoAxINCgV2YWx1ZRgCIAEoAzoCOAEaNgoUTWFwVWludDMy",
            "VWludDMyRW50cnkSCwoDa2V5GAEgASgNEg0KBXZhbHVlGAIgASgNOgI4ARo2",
            "ChRNYXBVaW50NjRVaW50NjRFbnRyeRILCgNrZXkYASABKAQSDQoFdmFsdWUY",
            "AiABKAQ6AjgBGjYKFE1hcFNpbnQzMlNpbnQzMkVudHJ5EgsKA2tleRgBIAEo",
            "ERINCgV2YWx1ZRgCIAEoEToCOAEaNgoUTWFwU2ludDY0U2ludDY0RW50cnkS",
            "CwoDa2V5GAEgASgSEg0KBXZhbHVlGAIgASgSOgI4ARo4ChZNYXBGaXhlZDMy",
            "Rml4ZWQzMkVudHJ5EgsKA2tleRgBIAEoBxINCgV2YWx1ZRgCIAEoBzoCOAEa",
            "OAoWTWFwRml4ZWQ2NEZpeGVkNjRFbnRyeRILCgNrZXkYASABKAYSDQoFdmFs",
            "dWUYAiABKAY6AjgBGjoKGE1hcFNmaXhlZDMyU2ZpeGVkMzJFbnRyeRILCgNr",
            "ZXkYASABKA8SDQoFdmFsdWUYAiABKA86AjgBGjoKGE1hcFNmaXhlZDY0U2Zp",
            "eGVkNjRFbnRyeRILCgNrZXkYASABKBASDQoFdmFsdWUYAiABKBA6AjgBGjQK",
            "Ek1hcEludDMyRmxvYXRFbnRyeRILCgNrZXkYASABKAUSDQoFdmFsdWUYAiAB",
            "KAI6AjgBGjUKE01hcEludDMyRG91YmxlRW50cnkSCwoDa2V5GAEgASgFEg0K",
            "BXZhbHVlGAIgASgBOgI4ARoyChBNYXBCb29sQm9vbEVudHJ5EgsKA2tleRgB",
            "IAEoCBINCgV2YWx1ZRgCIAEoCDoCOAEaNgoUTWFwU3RyaW5nU3RyaW5nRW50",
            "cnkSCwoDa2V5GAEgASgJEg0KBXZhbHVlGAIgASgJOgI4ARo1ChNNYXBTdHJp",
            "bmdCeXRlc0VudHJ5EgsKA2tleRgBIAEoCRINCgV2YWx1ZRgCIAEoDDoCOAEa",
            "fgobTWFwU3RyaW5nTmVzdGVkTWVzc2FnZUVudHJ5EgsKA2tleRgBIAEoCRJO",
            "CgV2YWx1ZRgCIAEoCzI/LnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8y",
            "LlRlc3RBbGxUeXBlc1Byb3RvMi5OZXN0ZWRNZXNzYWdlOgI4ARpzChxNYXBT",
            "dHJpbmdGb3JlaWduTWVzc2FnZUVudHJ5EgsKA2tleRgBIAEoCRJCCgV2YWx1",
            "ZRgCIAEoCzIzLnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8yLkZvcmVp",
            "Z25NZXNzYWdlUHJvdG8yOgI4ARp4ChhNYXBTdHJpbmdOZXN0ZWRFbnVtRW50",
            "cnkSCwoDa2V5GAEgASgJEksKBXZhbHVlGAIgASgOMjwucHJvdG9idWZfdGVz",
            "dF9tZXNzYWdlcy5wcm90bzIuVGVzdEFsbFR5cGVzUHJvdG8yLk5lc3RlZEVu",
            "dW06AjgBGm0KGU1hcFN0cmluZ0ZvcmVpZ25FbnVtRW50cnkSCwoDa2V5GAEg",
            "ASgJEj8KBXZhbHVlGAIgASgOMjAucHJvdG9idWZfdGVzdF9tZXNzYWdlcy5w",
            "cm90bzIuRm9yZWlnbkVudW1Qcm90bzI6AjgBGjMKBERhdGESFAoLZ3JvdXBf",
            "aW50MzIYygEgASgFEhUKDGdyb3VwX3VpbnQzMhjLASABKA0aIQoRTWVzc2Fn",
            "ZVNldENvcnJlY3QqCAgEEP////8HOgIIARrgAQobTWVzc2FnZVNldENvcnJl",
            "Y3RFeHRlbnNpb24xEgsKA3N0chgZIAEoCTKzAQoVbWVzc2FnZV9zZXRfZXh0",
            "ZW5zaW9uEkMucHJvdG9idWZfdGVzdF9tZXNzYWdlcy5wcm90bzIuVGVzdEFs",
            "bFR5cGVzUHJvdG8yLk1lc3NhZ2VTZXRDb3JyZWN0GPm7XiABKAsyTS5wcm90",
            "b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5UZXN0QWxsVHlwZXNQcm90bzIu",
            "TWVzc2FnZVNldENvcnJlY3RFeHRlbnNpb24xGt8BChtNZXNzYWdlU2V0Q29y",
            "cmVjdEV4dGVuc2lvbjISCQoBaRgJIAEoBTK0AQoVbWVzc2FnZV9zZXRfZXh0",
            "ZW5zaW9uEkMucHJvdG9idWZfdGVzdF9tZXNzYWdlcy5wcm90bzIuVGVzdEFs",
            "bFR5cGVzUHJvdG8yLk1lc3NhZ2VTZXRDb3JyZWN0GJCz/AEgASgLMk0ucHJv",
            "dG9idWZfdGVzdF9tZXNzYWdlcy5wcm90bzIuVGVzdEFsbFR5cGVzUHJvdG8y",
            "Lk1lc3NhZ2VTZXRDb3JyZWN0RXh0ZW5zaW9uMiI5CgpOZXN0ZWRFbnVtEgcK",
            "A0ZPTxAAEgcKA0JBUhABEgcKA0JBWhACEhAKA05FRxD///////////8BKgUI",
            "eBDJAUINCgtvbmVvZl9maWVsZEoGCOgHEJBOIiEKFEZvcmVpZ25NZXNzYWdl",
            "UHJvdG8yEgkKAWMYASABKAUiwQIKFVVua25vd25Ub1Rlc3RBbGxUeXBlcxIX",
            "Cg5vcHRpb25hbF9pbnQzMhjpByABKAUSGAoPb3B0aW9uYWxfc3RyaW5nGOoH",
            "IAEoCRJMCg5uZXN0ZWRfbWVzc2FnZRjrByABKAsyMy5wcm90b2J1Zl90ZXN0",
            "X21lc3NhZ2VzLnByb3RvMi5Gb3JlaWduTWVzc2FnZVByb3RvMhJaCg1vcHRp",
            "b25hbGdyb3VwGOwHIAEoCjJCLnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJv",
            "dG8yLlVua25vd25Ub1Rlc3RBbGxUeXBlcy5PcHRpb25hbEdyb3VwEhYKDW9w",
            "dGlvbmFsX2Jvb2wY7gcgASgIEhcKDnJlcGVhdGVkX2ludDMyGPMHIAMoBRoa",
            "Cg1PcHRpb25hbEdyb3VwEgkKAWEYASABKAUiFgoUTnVsbEh5cG90aGVzaXNQ",
            "cm90bzIiLwoORW51bU9ubHlQcm90bzIiHQoEQm9vbBIKCgZrRmFsc2UQABIJ",
            "CgVrVHJ1ZRABIh8KD09uZVN0cmluZ1Byb3RvMhIMCgRkYXRhGAEgASgJIkYK",
            "EVByb3RvV2l0aEtleXdvcmRzEg4KBmlubGluZRgBIAEoBRIPCgdjb25jZXB0",
            "GAIgASgJEhAKCHJlcXVpcmVzGAMgAygJIt4TChpUZXN0QWxsUmVxdWlyZWRU",
            "eXBlc1Byb3RvMhIWCg5yZXF1aXJlZF9pbnQzMhgBIAIoBRIWCg5yZXF1aXJl",
            "ZF9pbnQ2NBgCIAIoAxIXCg9yZXF1aXJlZF91aW50MzIYAyACKA0SFwoPcmVx",
            "dWlyZWRfdWludDY0GAQgAigEEhcKD3JlcXVpcmVkX3NpbnQzMhgFIAIoERIX",
            "Cg9yZXF1aXJlZF9zaW50NjQYBiACKBISGAoQcmVxdWlyZWRfZml4ZWQzMhgH",
            "IAIoBxIYChByZXF1aXJlZF9maXhlZDY0GAggAigGEhkKEXJlcXVpcmVkX3Nm",
            "aXhlZDMyGAkgAigPEhkKEXJlcXVpcmVkX3NmaXhlZDY0GAogAigQEhYKDnJl",
            "cXVpcmVkX2Zsb2F0GAsgAigCEhcKD3JlcXVpcmVkX2RvdWJsZRgMIAIoARIV",
            "Cg1yZXF1aXJlZF9ib29sGA0gAigIEhcKD3JlcXVpcmVkX3N0cmluZxgOIAIo",
            "CRIWCg5yZXF1aXJlZF9ieXRlcxgPIAIoDBJoChdyZXF1aXJlZF9uZXN0ZWRf",
            "bWVzc2FnZRgSIAIoCzJHLnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8y",
            "LlRlc3RBbGxSZXF1aXJlZFR5cGVzUHJvdG8yLk5lc3RlZE1lc3NhZ2USVQoY",
            "cmVxdWlyZWRfZm9yZWlnbl9tZXNzYWdlGBMgAigLMjMucHJvdG9idWZfdGVz",
            "dF9tZXNzYWdlcy5wcm90bzIuRm9yZWlnbk1lc3NhZ2VQcm90bzISYgoUcmVx",
            "dWlyZWRfbmVzdGVkX2VudW0YFSACKA4yRC5wcm90b2J1Zl90ZXN0X21lc3Nh",
            "Z2VzLnByb3RvMi5UZXN0QWxsUmVxdWlyZWRUeXBlc1Byb3RvMi5OZXN0ZWRF",
            "bnVtEk8KFXJlcXVpcmVkX2ZvcmVpZ25fZW51bRgWIAIoDjIwLnByb3RvYnVm",
            "X3Rlc3RfbWVzc2FnZXMucHJvdG8yLkZvcmVpZ25FbnVtUHJvdG8yEiEKFXJl",
            "cXVpcmVkX3N0cmluZ19waWVjZRgYIAIoCUICCAISGQoNcmVxdWlyZWRfY29y",
            "ZBgZIAIoCUICCAESVAoRcmVjdXJzaXZlX21lc3NhZ2UYGyACKAsyOS5wcm90",
            "b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5UZXN0QWxsUmVxdWlyZWRUeXBl",
            "c1Byb3RvMhJdChpvcHRpb25hbF9yZWN1cnNpdmVfbWVzc2FnZRgcIAEoCzI5",
            "LnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8yLlRlc3RBbGxSZXF1aXJl",
            "ZFR5cGVzUHJvdG8yEk0KBGRhdGEYyQEgAigKMj4ucHJvdG9idWZfdGVzdF9t",
            "ZXNzYWdlcy5wcm90bzIuVGVzdEFsbFJlcXVpcmVkVHlwZXNQcm90bzIuRGF0",
            "YRIiCg1kZWZhdWx0X2ludDMyGPEBIAIoBToKLTEyMzQ1Njc4ORIsCg1kZWZh",
            "dWx0X2ludDY0GPIBIAIoAzoULTkxMjM0NTY3ODkxMjM0NTY3ODkSIwoOZGVm",
            "YXVsdF91aW50MzIY8wEgAigNOgoyMTIzNDU2Nzg5Ei0KDmRlZmF1bHRfdWlu",
            "dDY0GPQBIAIoBDoUMTAxMjM0NTY3ODkxMjM0NTY3ODkSIwoOZGVmYXVsdF9z",
            "aW50MzIY9QEgAigROgotMTIzNDU2Nzg5Ei0KDmRlZmF1bHRfc2ludDY0GPYB",
            "IAIoEjoULTkxMjM0NTY3ODkxMjM0NTY3ODkSJAoPZGVmYXVsdF9maXhlZDMy",
            "GPcBIAIoBzoKMjEyMzQ1Njc4ORIuCg9kZWZhdWx0X2ZpeGVkNjQY+AEgAigG",
            "OhQxMDEyMzQ1Njc4OTEyMzQ1Njc4ORIlChBkZWZhdWx0X3NmaXhlZDMyGPkB",
            "IAIoDzoKLTEyMzQ1Njc4ORIvChBkZWZhdWx0X3NmaXhlZDY0GPoBIAIoEDoU",
            "LTkxMjM0NTY3ODkxMjM0NTY3ODkSHQoNZGVmYXVsdF9mbG9hdBj7ASACKAI6",
            "BTllKzA5Eh4KDmRlZmF1bHRfZG91YmxlGPwBIAIoAToFN2UrMjISGwoMZGVm",
            "YXVsdF9ib29sGP0BIAIoCDoEdHJ1ZRIgCg5kZWZhdWx0X3N0cmluZxj+ASAC",
            "KAk6B1Jvc2VidWQSHgoNZGVmYXVsdF9ieXRlcxj/ASACKAw6Bmpvc2h1YRrD",
            "AQoNTmVzdGVkTWVzc2FnZRIJCgFhGAEgAigFEk4KC2NvcmVjdXJzaXZlGAIg",
            "AigLMjkucHJvdG9idWZfdGVzdF9tZXNzYWdlcy5wcm90bzIuVGVzdEFsbFJl",
            "cXVpcmVkVHlwZXNQcm90bzISVwoUb3B0aW9uYWxfY29yZWN1cnNpdmUYAyAB",
            "KAsyOS5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5UZXN0QWxsUmVx",
            "dWlyZWRUeXBlc1Byb3RvMhozCgREYXRhEhQKC2dyb3VwX2ludDMyGMoBIAIo",
            "BRIVCgxncm91cF91aW50MzIYywEgAigNGiEKEU1lc3NhZ2VTZXRDb3JyZWN0",
            "KggIBBD/////BzoCCAEa8AEKG01lc3NhZ2VTZXRDb3JyZWN0RXh0ZW5zaW9u",
            "MRILCgNzdHIYGSACKAkywwEKFW1lc3NhZ2Vfc2V0X2V4dGVuc2lvbhJLLnBy",
            "b3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8yLlRlc3RBbGxSZXF1aXJlZFR5",
            "cGVzUHJvdG8yLk1lc3NhZ2VTZXRDb3JyZWN0GPm7XiABKAsyVS5wcm90b2J1",
            "Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5UZXN0QWxsUmVxdWlyZWRUeXBlc1By",
            "b3RvMi5NZXNzYWdlU2V0Q29ycmVjdEV4dGVuc2lvbjEa7wEKG01lc3NhZ2VT",
            "ZXRDb3JyZWN0RXh0ZW5zaW9uMhIJCgFpGAkgAigFMsQBChVtZXNzYWdlX3Nl",
            "dF9leHRlbnNpb24SSy5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMi5U",
            "ZXN0QWxsUmVxdWlyZWRUeXBlc1Byb3RvMi5NZXNzYWdlU2V0Q29ycmVjdBiQ",
            "s/wBIAEoCzJVLnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8yLlRlc3RB",
            "bGxSZXF1aXJlZFR5cGVzUHJvdG8yLk1lc3NhZ2VTZXRDb3JyZWN0RXh0ZW5z",
            "aW9uMiI5CgpOZXN0ZWRFbnVtEgcKA0ZPTxAAEgcKA0JBUhABEgcKA0JBWhAC",
            "EhAKA05FRxD///////////8BKgUIeBDJAUoGCOgHEJBOKkYKEUZvcmVpZ25F",
            "bnVtUHJvdG8yEg8KC0ZPUkVJR05fRk9PEAASDwoLRk9SRUlHTl9CQVIQARIP",
            "CgtGT1JFSUdOX0JBWhACOkoKD2V4dGVuc2lvbl9pbnQzMhIxLnByb3RvYnVm",
            "X3Rlc3RfbWVzc2FnZXMucHJvdG8yLlRlc3RBbGxUeXBlc1Byb3RvMhh4IAEo",
            "BUI4Cihjb20uZ29vZ2xlLnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8y",
            "SAH4AQGiAgZQcm90bzI="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::ProtobufTestMessages.Proto2.ForeignEnumProto2), }, new pb::Extension[] { TestMessagesProto2Extensions.ExtensionInt32 }, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.TestAllTypesProto2), global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Parser, new[]{ "OptionalInt32", "OptionalInt64", "OptionalUint32", "OptionalUint64", "OptionalSint32", "OptionalSint64", "OptionalFixed32", "OptionalFixed64", "OptionalSfixed32", "OptionalSfixed64", "OptionalFloat", "OptionalDouble", "OptionalBool", "OptionalString", "OptionalBytes", "OptionalNestedMessage", "OptionalForeignMessage", "OptionalNestedEnum", "OptionalForeignEnum", "OptionalStringPiece", "OptionalCord", "RecursiveMessage", "RepeatedInt32", "RepeatedInt64", "RepeatedUint32", "RepeatedUint64", "RepeatedSint32", "RepeatedSint64", "RepeatedFixed32", "RepeatedFixed64", "RepeatedSfixed32", "RepeatedSfixed64", "RepeatedFloat", "RepeatedDouble", "RepeatedBool", "RepeatedString", "RepeatedBytes", "RepeatedNestedMessage", "RepeatedForeignMessage", "RepeatedNestedEnum", "RepeatedForeignEnum", "RepeatedStringPiece", "RepeatedCord", "PackedInt32", "PackedInt64", "PackedUint32", "PackedUint64", "PackedSint32", "PackedSint64", "PackedFixed32", "PackedFixed64", "PackedSfixed32", "PackedSfixed64", "PackedFloat", "PackedDouble", "PackedBool", "PackedNestedEnum", "UnpackedInt32", "UnpackedInt64", "UnpackedUint32", "UnpackedUint64", "UnpackedSint32", "UnpackedSint64", "UnpackedFixed32", "UnpackedFixed64", "UnpackedSfixed32", "UnpackedSfixed64", "UnpackedFloat", "UnpackedDouble", "UnpackedBool", "UnpackedNestedEnum", "MapInt32Int32", "MapInt64Int64", "MapUint32Uint32", "MapUint64Uint64", "MapSint32Sint32", "MapSint64Sint64", "MapFixed32Fixed32", "MapFixed64Fixed64", "MapSfixed32Sfixed32", "MapSfixed64Sfixed64", "MapInt32Float", "MapInt32Double", "MapBoolBool", "MapStringString", "MapStringBytes", "MapStringNestedMessage", "MapStringForeignMessage", "MapStringNestedEnum", "MapStringForeignEnum", "OneofUint32", "OneofNestedMessage", "OneofString", "OneofBytes", "OneofBool", "OneofUint64", "OneofFloat", "OneofDouble", "OneofEnum", "Data", "DefaultInt32", "DefaultInt64", "DefaultUint32", "DefaultUint64", "DefaultSint32", "DefaultSint64", "DefaultFixed32", "DefaultFixed64", "DefaultSfixed32", "DefaultSfixed64", "DefaultFloat", "DefaultDouble", "DefaultBool", "DefaultString", "DefaultBytes", "Fieldname1", "FieldName2", "FieldName3", "FieldName4", "Field0Name5", "Field0Name6", "FieldName7", "FieldName8", "FieldName9", "FieldName10", "FIELDNAME11", "FIELDName12", "FieldName13", "FieldName14", "FieldName15", "FieldName16", "FieldName17", "FieldName18" }, new[]{ "OneofField" }, new[]{ typeof(global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum) }, null, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage), global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage.Parser, new[]{ "A", "Corecursive" }, null, null, null, null),
            null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.Data), global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.Data.Parser, new[]{ "GroupInt32", "GroupUint32" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrect), global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrect.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrectExtension1), global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrectExtension1.Parser, new[]{ "Str" }, null, null, new pb::Extension[] { global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrectExtension1.Extensions.MessageSetExtension }, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrectExtension2), global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrectExtension2.Parser, new[]{ "I" }, null, null, new pb::Extension[] { global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrectExtension2.Extensions.MessageSetExtension }, null)}),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.ForeignMessageProto2), global::ProtobufTestMessages.Proto2.ForeignMessageProto2.Parser, new[]{ "C" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.UnknownToTestAllTypes), global::ProtobufTestMessages.Proto2.UnknownToTestAllTypes.Parser, new[]{ "OptionalInt32", "OptionalString", "NestedMessage", "OptionalGroup", "OptionalBool", "RepeatedInt32" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.UnknownToTestAllTypes.Types.OptionalGroup), global::ProtobufTestMessages.Proto2.UnknownToTestAllTypes.Types.OptionalGroup.Parser, new[]{ "A" }, null, null, null, null)}),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.NullHypothesisProto2), global::ProtobufTestMessages.Proto2.NullHypothesisProto2.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.EnumOnlyProto2), global::ProtobufTestMessages.Proto2.EnumOnlyProto2.Parser, null, null, new[]{ typeof(global::ProtobufTestMessages.Proto2.EnumOnlyProto2.Types.Bool) }, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.OneStringProto2), global::ProtobufTestMessages.Proto2.OneStringProto2.Parser, new[]{ "Data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.ProtoWithKeywords), global::ProtobufTestMessages.Proto2.ProtoWithKeywords.Parser, new[]{ "Inline", "Concept", "Requires" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2), global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Parser, new[]{ "RequiredInt32", "RequiredInt64", "RequiredUint32", "RequiredUint64", "RequiredSint32", "RequiredSint64", "RequiredFixed32", "RequiredFixed64", "RequiredSfixed32", "RequiredSfixed64", "RequiredFloat", "RequiredDouble", "RequiredBool", "RequiredString", "RequiredBytes", "RequiredNestedMessage", "RequiredForeignMessage", "RequiredNestedEnum", "RequiredForeignEnum", "RequiredStringPiece", "RequiredCord", "RecursiveMessage", "OptionalRecursiveMessage", "Data", "DefaultInt32", "DefaultInt64", "DefaultUint32", "DefaultUint64", "DefaultSint32", "DefaultSint64", "DefaultFixed32", "DefaultFixed64", "DefaultSfixed32", "DefaultSfixed64", "DefaultFloat", "DefaultDouble", "DefaultBool", "DefaultString", "DefaultBytes" }, null, new[]{ typeof(global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedEnum) }, null, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedMessage), global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedMessage.Parser, new[]{ "A", "Corecursive", "OptionalCorecursive" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.Data), global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.Data.Parser, new[]{ "GroupInt32", "GroupUint32" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrect), global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrect.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrectExtension1), global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrectExtension1.Parser, new[]{ "Str" }, null, null, new pb::Extension[] { global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrectExtension1.Extensions.MessageSetExtension }, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrectExtension2), global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrectExtension2.Parser, new[]{ "I" }, null, null, new pb::Extension[] { global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrectExtension2.Extensions.MessageSetExtension }, null)})
          }));
    }
    #endregion

  }
  /// <summary>Holder for extension identifiers generated from the top level of google/protobuf/test_messages_proto2.proto</summary>
  public static partial class TestMessagesProto2Extensions {
    public static readonly pb::Extension<global::ProtobufTestMessages.Proto2.TestAllTypesProto2, int> ExtensionInt32 =
      new pb::Extension<global::ProtobufTestMessages.Proto2.TestAllTypesProto2, int>(120, pb::FieldCodec.ForInt32(960, 0));
  }

  #region Enums
  public enum ForeignEnumProto2 {
    [pbr::OriginalName("FOREIGN_FOO")] ForeignFoo = 0,
    [pbr::OriginalName("FOREIGN_BAR")] ForeignBar = 1,
    [pbr::OriginalName("FOREIGN_BAZ")] ForeignBaz = 2,
  }

  #endregion

  #region Messages
  /// <summary>
  /// This proto includes every type of field in both singular and repeated
  /// forms.
  ///
  /// Also, crucially, all messages and enums in this file are eventually
  /// submessages of this message.  So for example, a fuzz test of TestAllTypes
  /// could trigger bugs that occur in any message type in this file.  We verify
  /// this stays true in a unit test.
  /// </summary>
  public sealed partial class TestAllTypesProto2 : pb::IExtendableMessage<TestAllTypesProto2>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TestAllTypesProto2> _parser = new pb::MessageParser<TestAllTypesProto2>(() => new TestAllTypesProto2());
    private pb::UnknownFieldSet _unknownFields;
    private pb::ExtensionSet<TestAllTypesProto2> _extensions;
    private pb::ExtensionSet<TestAllTypesProto2> _Extensions { get { return _extensions; } }
    private int _hasBits0;
    private int _hasBits1;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TestAllTypesProto2> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufTestMessages.Proto2.TestMessagesProto2Reflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestAllTypesProto2() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestAllTypesProto2(TestAllTypesProto2 other) : this() {
      _hasBits0 = other._hasBits0;
      _hasBits1 = other._hasBits1;
      optionalInt32_ = other.optionalInt32_;
      optionalInt64_ = other.optionalInt64_;
      optionalUint32_ = other.optionalUint32_;
      optionalUint64_ = other.optionalUint64_;
      optionalSint32_ = other.optionalSint32_;
      optionalSint64_ = other.optionalSint64_;
      optionalFixed32_ = other.optionalFixed32_;
      optionalFixed64_ = other.optionalFixed64_;
      optionalSfixed32_ = other.optionalSfixed32_;
      optionalSfixed64_ = other.optionalSfixed64_;
      optionalFloat_ = other.optionalFloat_;
      optionalDouble_ = other.optionalDouble_;
      optionalBool_ = other.optionalBool_;
      optionalString_ = other.optionalString_;
      optionalBytes_ = other.optionalBytes_;
      optionalNestedMessage_ = other.optionalNestedMessage_ != null ? other.optionalNestedMessage_.Clone() : null;
      optionalForeignMessage_ = other.optionalForeignMessage_ != null ? other.optionalForeignMessage_.Clone() : null;
      optionalNestedEnum_ = other.optionalNestedEnum_;
      optionalForeignEnum_ = other.optionalForeignEnum_;
      optionalStringPiece_ = other.optionalStringPiece_;
      optionalCord_ = other.optionalCord_;
      recursiveMessage_ = other.recursiveMessage_ != null ? other.recursiveMessage_.Clone() : null;
      repeatedInt32_ = other.repeatedInt32_.Clone();
      repeatedInt64_ = other.repeatedInt64_.Clone();
      repeatedUint32_ = other.repeatedUint32_.Clone();
      repeatedUint64_ = other.repeatedUint64_.Clone();
      repeatedSint32_ = other.repeatedSint32_.Clone();
      repeatedSint64_ = other.repeatedSint64_.Clone();
      repeatedFixed32_ = other.repeatedFixed32_.Clone();
      repeatedFixed64_ = other.repeatedFixed64_.Clone();
      repeatedSfixed32_ = other.repeatedSfixed32_.Clone();
      repeatedSfixed64_ = other.repeatedSfixed64_.Clone();
      repeatedFloat_ = other.repeatedFloat_.Clone();
      repeatedDouble_ = other.repeatedDouble_.Clone();
      repeatedBool_ = other.repeatedBool_.Clone();
      repeatedString_ = other.repeatedString_.Clone();
      repeatedBytes_ = other.repeatedBytes_.Clone();
      repeatedNestedMessage_ = other.repeatedNestedMessage_.Clone();
      repeatedForeignMessage_ = other.repeatedForeignMessage_.Clone();
      repeatedNestedEnum_ = other.repeatedNestedEnum_.Clone();
      repeatedForeignEnum_ = other.repeatedForeignEnum_.Clone();
      repeatedStringPiece_ = other.repeatedStringPiece_.Clone();
      repeatedCord_ = other.repeatedCord_.Clone();
      packedInt32_ = other.packedInt32_.Clone();
      packedInt64_ = other.packedInt64_.Clone();
      packedUint32_ = other.packedUint32_.Clone();
      packedUint64_ = other.packedUint64_.Clone();
      packedSint32_ = other.packedSint32_.Clone();
      packedSint64_ = other.packedSint64_.Clone();
      packedFixed32_ = other.packedFixed32_.Clone();
      packedFixed64_ = other.packedFixed64_.Clone();
      packedSfixed32_ = other.packedSfixed32_.Clone();
      packedSfixed64_ = other.packedSfixed64_.Clone();
      packedFloat_ = other.packedFloat_.Clone();
      packedDouble_ = other.packedDouble_.Clone();
      packedBool_ = other.packedBool_.Clone();
      packedNestedEnum_ = other.packedNestedEnum_.Clone();
      unpackedInt32_ = other.unpackedInt32_.Clone();
      unpackedInt64_ = other.unpackedInt64_.Clone();
      unpackedUint32_ = other.unpackedUint32_.Clone();
      unpackedUint64_ = other.unpackedUint64_.Clone();
      unpackedSint32_ = other.unpackedSint32_.Clone();
      unpackedSint64_ = other.unpackedSint64_.Clone();
      unpackedFixed32_ = other.unpackedFixed32_.Clone();
      unpackedFixed64_ = other.unpackedFixed64_.Clone();
      unpackedSfixed32_ = other.unpackedSfixed32_.Clone();
      unpackedSfixed64_ = other.unpackedSfixed64_.Clone();
      unpackedFloat_ = other.unpackedFloat_.Clone();
      unpackedDouble_ = other.unpackedDouble_.Clone();
      unpackedBool_ = other.unpackedBool_.Clone();
      unpackedNestedEnum_ = other.unpackedNestedEnum_.Clone();
      mapInt32Int32_ = other.mapInt32Int32_.Clone();
      mapInt64Int64_ = other.mapInt64Int64_.Clone();
      mapUint32Uint32_ = other.mapUint32Uint32_.Clone();
      mapUint64Uint64_ = other.mapUint64Uint64_.Clone();
      mapSint32Sint32_ = other.mapSint32Sint32_.Clone();
      mapSint64Sint64_ = other.mapSint64Sint64_.Clone();
      mapFixed32Fixed32_ = other.mapFixed32Fixed32_.Clone();
      mapFixed64Fixed64_ = other.mapFixed64Fixed64_.Clone();
      mapSfixed32Sfixed32_ = other.mapSfixed32Sfixed32_.Clone();
      mapSfixed64Sfixed64_ = other.mapSfixed64Sfixed64_.Clone();
      mapInt32Float_ = other.mapInt32Float_.Clone();
      mapInt32Double_ = other.mapInt32Double_.Clone();
      mapBoolBool_ = other.mapBoolBool_.Clone();
      mapStringString_ = other.mapStringString_.Clone();
      mapStringBytes_ = other.mapStringBytes_.Clone();
      mapStringNestedMessage_ = other.mapStringNestedMessage_.Clone();
      mapStringForeignMessage_ = other.mapStringForeignMessage_.Clone();
      mapStringNestedEnum_ = other.mapStringNestedEnum_.Clone();
      mapStringForeignEnum_ = other.mapStringForeignEnum_.Clone();
      data_ = other.HasData ? other.data_.Clone() : null;
      defaultInt32_ = other.defaultInt32_;
      defaultInt64_ = other.defaultInt64_;
      defaultUint32_ = other.defaultUint32_;
      defaultUint64_ = other.defaultUint64_;
      defaultSint32_ = other.defaultSint32_;
      defaultSint64_ = other.defaultSint64_;
      defaultFixed32_ = other.defaultFixed32_;
      defaultFixed64_ = other.defaultFixed64_;
      defaultSfixed32_ = other.defaultSfixed32_;
      defaultSfixed64_ = other.defaultSfixed64_;
      defaultFloat_ = other.defaultFloat_;
      defaultDouble_ = other.defaultDouble_;
      defaultBool_ = other.defaultBool_;
      defaultString_ = other.defaultString_;
      defaultBytes_ = other.defaultBytes_;
      fieldname1_ = other.fieldname1_;
      fieldName2_ = other.fieldName2_;
      FieldName3_ = other.FieldName3_;
      fieldName4_ = other.fieldName4_;
      field0Name5_ = other.field0Name5_;
      field0Name6_ = other.field0Name6_;
      fieldName7_ = other.fieldName7_;
      fieldName8_ = other.fieldName8_;
      fieldName9_ = other.fieldName9_;
      fieldName10_ = other.fieldName10_;
      fIELDNAME11_ = other.fIELDNAME11_;
      fIELDName12_ = other.fIELDName12_;
      FieldName13_ = other.FieldName13_;
      FieldName14_ = other.FieldName14_;
      fieldName15_ = other.fieldName15_;
      fieldName16_ = other.fieldName16_;
      fieldName17_ = other.fieldName17_;
      fieldName18_ = other.fieldName18_;
      switch (other.OneofFieldCase) {
        case OneofFieldOneofCase.OneofUint32:
          OneofUint32 = other.OneofUint32;
          break;
        case OneofFieldOneofCase.OneofNestedMessage:
          OneofNestedMessage = other.OneofNestedMessage.Clone();
          break;
        case OneofFieldOneofCase.OneofString:
          OneofString = other.OneofString;
          break;
        case OneofFieldOneofCase.OneofBytes:
          OneofBytes = other.OneofBytes;
          break;
        case OneofFieldOneofCase.OneofBool:
          OneofBool = other.OneofBool;
          break;
        case OneofFieldOneofCase.OneofUint64:
          OneofUint64 = other.OneofUint64;
          break;
        case OneofFieldOneofCase.OneofFloat:
          OneofFloat = other.OneofFloat;
          break;
        case OneofFieldOneofCase.OneofDouble:
          OneofDouble = other.OneofDouble;
          break;
        case OneofFieldOneofCase.OneofEnum:
          OneofEnum = other.OneofEnum;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
      _extensions = pb::ExtensionSet.Clone(other._extensions);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestAllTypesProto2 Clone() {
      return new TestAllTypesProto2(this);
    }

    /// <summary>Field number for the "optional_int32" field.</summary>
    public const int OptionalInt32FieldNumber = 1;
    private readonly static int OptionalInt32DefaultValue = 0;

    private int optionalInt32_;
    /// <summary>
    /// Singular
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int OptionalInt32 {
      get { if ((_hasBits0 & 1) != 0) { return optionalInt32_; } else { return OptionalInt32DefaultValue; } }
      set {
        _hasBits0 |= 1;
        optionalInt32_ = value;
      }
    }
    /// <summary>Gets whether the "optional_int32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalInt32 {
      get { return (_hasBits0 & 1) != 0; }
    }
    /// <summary>Clears the value of the "optional_int32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalInt32() {
      _hasBits0 &= ~1;
    }

    /// <summary>Field number for the "optional_int64" field.</summary>
    public const int OptionalInt64FieldNumber = 2;
    private readonly static long OptionalInt64DefaultValue = 0L;

    private long optionalInt64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long OptionalInt64 {
      get { if ((_hasBits0 & 2) != 0) { return optionalInt64_; } else { return OptionalInt64DefaultValue; } }
      set {
        _hasBits0 |= 2;
        optionalInt64_ = value;
      }
    }
    /// <summary>Gets whether the "optional_int64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalInt64 {
      get { return (_hasBits0 & 2) != 0; }
    }
    /// <summary>Clears the value of the "optional_int64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalInt64() {
      _hasBits0 &= ~2;
    }

    /// <summary>Field number for the "optional_uint32" field.</summary>
    public const int OptionalUint32FieldNumber = 3;
    private readonly static uint OptionalUint32DefaultValue = 0;

    private uint optionalUint32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint OptionalUint32 {
      get { if ((_hasBits0 & 4) != 0) { return optionalUint32_; } else { return OptionalUint32DefaultValue; } }
      set {
        _hasBits0 |= 4;
        optionalUint32_ = value;
      }
    }
    /// <summary>Gets whether the "optional_uint32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalUint32 {
      get { return (_hasBits0 & 4) != 0; }
    }
    /// <summary>Clears the value of the "optional_uint32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalUint32() {
      _hasBits0 &= ~4;
    }

    /// <summary>Field number for the "optional_uint64" field.</summary>
    public const int OptionalUint64FieldNumber = 4;
    private readonly static ulong OptionalUint64DefaultValue = 0UL;

    private ulong optionalUint64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong OptionalUint64 {
      get { if ((_hasBits0 & 8) != 0) { return optionalUint64_; } else { return OptionalUint64DefaultValue; } }
      set {
        _hasBits0 |= 8;
        optionalUint64_ = value;
      }
    }
    /// <summary>Gets whether the "optional_uint64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalUint64 {
      get { return (_hasBits0 & 8) != 0; }
    }
    /// <summary>Clears the value of the "optional_uint64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalUint64() {
      _hasBits0 &= ~8;
    }

    /// <summary>Field number for the "optional_sint32" field.</summary>
    public const int OptionalSint32FieldNumber = 5;
    private readonly static int OptionalSint32DefaultValue = 0;

    private int optionalSint32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int OptionalSint32 {
      get { if ((_hasBits0 & 16) != 0) { return optionalSint32_; } else { return OptionalSint32DefaultValue; } }
      set {
        _hasBits0 |= 16;
        optionalSint32_ = value;
      }
    }
    /// <summary>Gets whether the "optional_sint32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalSint32 {
      get { return (_hasBits0 & 16) != 0; }
    }
    /// <summary>Clears the value of the "optional_sint32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalSint32() {
      _hasBits0 &= ~16;
    }

    /// <summary>Field number for the "optional_sint64" field.</summary>
    public const int OptionalSint64FieldNumber = 6;
    private readonly static long OptionalSint64DefaultValue = 0L;

    private long optionalSint64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long OptionalSint64 {
      get { if ((_hasBits0 & 32) != 0) { return optionalSint64_; } else { return OptionalSint64DefaultValue; } }
      set {
        _hasBits0 |= 32;
        optionalSint64_ = value;
      }
    }
    /// <summary>Gets whether the "optional_sint64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalSint64 {
      get { return (_hasBits0 & 32) != 0; }
    }
    /// <summary>Clears the value of the "optional_sint64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalSint64() {
      _hasBits0 &= ~32;
    }

    /// <summary>Field number for the "optional_fixed32" field.</summary>
    public const int OptionalFixed32FieldNumber = 7;
    private readonly static uint OptionalFixed32DefaultValue = 0;

    private uint optionalFixed32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint OptionalFixed32 {
      get { if ((_hasBits0 & 64) != 0) { return optionalFixed32_; } else { return OptionalFixed32DefaultValue; } }
      set {
        _hasBits0 |= 64;
        optionalFixed32_ = value;
      }
    }
    /// <summary>Gets whether the "optional_fixed32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalFixed32 {
      get { return (_hasBits0 & 64) != 0; }
    }
    /// <summary>Clears the value of the "optional_fixed32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalFixed32() {
      _hasBits0 &= ~64;
    }

    /// <summary>Field number for the "optional_fixed64" field.</summary>
    public const int OptionalFixed64FieldNumber = 8;
    private readonly static ulong OptionalFixed64DefaultValue = 0UL;

    private ulong optionalFixed64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong OptionalFixed64 {
      get { if ((_hasBits0 & 128) != 0) { return optionalFixed64_; } else { return OptionalFixed64DefaultValue; } }
      set {
        _hasBits0 |= 128;
        optionalFixed64_ = value;
      }
    }
    /// <summary>Gets whether the "optional_fixed64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalFixed64 {
      get { return (_hasBits0 & 128) != 0; }
    }
    /// <summary>Clears the value of the "optional_fixed64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalFixed64() {
      _hasBits0 &= ~128;
    }

    /// <summary>Field number for the "optional_sfixed32" field.</summary>
    public const int OptionalSfixed32FieldNumber = 9;
    private readonly static int OptionalSfixed32DefaultValue = 0;

    private int optionalSfixed32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int OptionalSfixed32 {
      get { if ((_hasBits0 & 256) != 0) { return optionalSfixed32_; } else { return OptionalSfixed32DefaultValue; } }
      set {
        _hasBits0 |= 256;
        optionalSfixed32_ = value;
      }
    }
    /// <summary>Gets whether the "optional_sfixed32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalSfixed32 {
      get { return (_hasBits0 & 256) != 0; }
    }
    /// <summary>Clears the value of the "optional_sfixed32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalSfixed32() {
      _hasBits0 &= ~256;
    }

    /// <summary>Field number for the "optional_sfixed64" field.</summary>
    public const int OptionalSfixed64FieldNumber = 10;
    private readonly static long OptionalSfixed64DefaultValue = 0L;

    private long optionalSfixed64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long OptionalSfixed64 {
      get { if ((_hasBits0 & 512) != 0) { return optionalSfixed64_; } else { return OptionalSfixed64DefaultValue; } }
      set {
        _hasBits0 |= 512;
        optionalSfixed64_ = value;
      }
    }
    /// <summary>Gets whether the "optional_sfixed64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalSfixed64 {
      get { return (_hasBits0 & 512) != 0; }
    }
    /// <summary>Clears the value of the "optional_sfixed64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalSfixed64() {
      _hasBits0 &= ~512;
    }

    /// <summary>Field number for the "optional_float" field.</summary>
    public const int OptionalFloatFieldNumber = 11;
    private readonly static float OptionalFloatDefaultValue = 0F;

    private float optionalFloat_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float OptionalFloat {
      get { if ((_hasBits0 & 1024) != 0) { return optionalFloat_; } else { return OptionalFloatDefaultValue; } }
      set {
        _hasBits0 |= 1024;
        optionalFloat_ = value;
      }
    }
    /// <summary>Gets whether the "optional_float" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalFloat {
      get { return (_hasBits0 & 1024) != 0; }
    }
    /// <summary>Clears the value of the "optional_float" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalFloat() {
      _hasBits0 &= ~1024;
    }

    /// <summary>Field number for the "optional_double" field.</summary>
    public const int OptionalDoubleFieldNumber = 12;
    private readonly static double OptionalDoubleDefaultValue = 0D;

    private double optionalDouble_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double OptionalDouble {
      get { if ((_hasBits0 & 2048) != 0) { return optionalDouble_; } else { return OptionalDoubleDefaultValue; } }
      set {
        _hasBits0 |= 2048;
        optionalDouble_ = value;
      }
    }
    /// <summary>Gets whether the "optional_double" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalDouble {
      get { return (_hasBits0 & 2048) != 0; }
    }
    /// <summary>Clears the value of the "optional_double" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalDouble() {
      _hasBits0 &= ~2048;
    }

    /// <summary>Field number for the "optional_bool" field.</summary>
    public const int OptionalBoolFieldNumber = 13;
    private readonly static bool OptionalBoolDefaultValue = false;

    private bool optionalBool_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool OptionalBool {
      get { if ((_hasBits0 & 4096) != 0) { return optionalBool_; } else { return OptionalBoolDefaultValue; } }
      set {
        _hasBits0 |= 4096;
        optionalBool_ = value;
      }
    }
    /// <summary>Gets whether the "optional_bool" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalBool {
      get { return (_hasBits0 & 4096) != 0; }
    }
    /// <summary>Clears the value of the "optional_bool" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalBool() {
      _hasBits0 &= ~4096;
    }

    /// <summary>Field number for the "optional_string" field.</summary>
    public const int OptionalStringFieldNumber = 14;
    private readonly static string OptionalStringDefaultValue = "";

    private string optionalString_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OptionalString {
      get { return optionalString_ ?? OptionalStringDefaultValue; }
      set {
        optionalString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "optional_string" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalString {
      get { return optionalString_ != null; }
    }
    /// <summary>Clears the value of the "optional_string" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalString() {
      optionalString_ = null;
    }

    /// <summary>Field number for the "optional_bytes" field.</summary>
    public const int OptionalBytesFieldNumber = 15;
    private readonly static pb::ByteString OptionalBytesDefaultValue = pb::ByteString.Empty;

    private pb::ByteString optionalBytes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString OptionalBytes {
      get { return optionalBytes_ ?? OptionalBytesDefaultValue; }
      set {
        optionalBytes_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "optional_bytes" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalBytes {
      get { return optionalBytes_ != null; }
    }
    /// <summary>Clears the value of the "optional_bytes" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalBytes() {
      optionalBytes_ = null;
    }

    /// <summary>Field number for the "optional_nested_message" field.</summary>
    public const int OptionalNestedMessageFieldNumber = 18;
    private global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage optionalNestedMessage_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage OptionalNestedMessage {
      get { return optionalNestedMessage_; }
      set {
        optionalNestedMessage_ = value;
      }
    }

    /// <summary>Field number for the "optional_foreign_message" field.</summary>
    public const int OptionalForeignMessageFieldNumber = 19;
    private global::ProtobufTestMessages.Proto2.ForeignMessageProto2 optionalForeignMessage_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.ForeignMessageProto2 OptionalForeignMessage {
      get { return optionalForeignMessage_; }
      set {
        optionalForeignMessage_ = value;
      }
    }

    /// <summary>Field number for the "optional_nested_enum" field.</summary>
    public const int OptionalNestedEnumFieldNumber = 21;
    private readonly static global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum OptionalNestedEnumDefaultValue = global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum.Foo;

    private global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum optionalNestedEnum_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum OptionalNestedEnum {
      get { if ((_hasBits0 & 8192) != 0) { return optionalNestedEnum_; } else { return OptionalNestedEnumDefaultValue; } }
      set {
        _hasBits0 |= 8192;
        optionalNestedEnum_ = value;
      }
    }
    /// <summary>Gets whether the "optional_nested_enum" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalNestedEnum {
      get { return (_hasBits0 & 8192) != 0; }
    }
    /// <summary>Clears the value of the "optional_nested_enum" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalNestedEnum() {
      _hasBits0 &= ~8192;
    }

    /// <summary>Field number for the "optional_foreign_enum" field.</summary>
    public const int OptionalForeignEnumFieldNumber = 22;
    private readonly static global::ProtobufTestMessages.Proto2.ForeignEnumProto2 OptionalForeignEnumDefaultValue = global::ProtobufTestMessages.Proto2.ForeignEnumProto2.ForeignFoo;

    private global::ProtobufTestMessages.Proto2.ForeignEnumProto2 optionalForeignEnum_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.ForeignEnumProto2 OptionalForeignEnum {
      get { if ((_hasBits0 & 16384) != 0) { return optionalForeignEnum_; } else { return OptionalForeignEnumDefaultValue; } }
      set {
        _hasBits0 |= 16384;
        optionalForeignEnum_ = value;
      }
    }
    /// <summary>Gets whether the "optional_foreign_enum" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalForeignEnum {
      get { return (_hasBits0 & 16384) != 0; }
    }
    /// <summary>Clears the value of the "optional_foreign_enum" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalForeignEnum() {
      _hasBits0 &= ~16384;
    }

    /// <summary>Field number for the "optional_string_piece" field.</summary>
    public const int OptionalStringPieceFieldNumber = 24;
    private readonly static string OptionalStringPieceDefaultValue = "";

    private string optionalStringPiece_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OptionalStringPiece {
      get { return optionalStringPiece_ ?? OptionalStringPieceDefaultValue; }
      set {
        optionalStringPiece_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "optional_string_piece" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalStringPiece {
      get { return optionalStringPiece_ != null; }
    }
    /// <summary>Clears the value of the "optional_string_piece" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalStringPiece() {
      optionalStringPiece_ = null;
    }

    /// <summary>Field number for the "optional_cord" field.</summary>
    public const int OptionalCordFieldNumber = 25;
    private readonly static string OptionalCordDefaultValue = "";

    private string optionalCord_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OptionalCord {
      get { return optionalCord_ ?? OptionalCordDefaultValue; }
      set {
        optionalCord_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "optional_cord" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalCord {
      get { return optionalCord_ != null; }
    }
    /// <summary>Clears the value of the "optional_cord" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalCord() {
      optionalCord_ = null;
    }

    /// <summary>Field number for the "recursive_message" field.</summary>
    public const int RecursiveMessageFieldNumber = 27;
    private global::ProtobufTestMessages.Proto2.TestAllTypesProto2 recursiveMessage_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.TestAllTypesProto2 RecursiveMessage {
      get { return recursiveMessage_; }
      set {
        recursiveMessage_ = value;
      }
    }

    /// <summary>Field number for the "repeated_int32" field.</summary>
    public const int RepeatedInt32FieldNumber = 31;
    private static readonly pb::FieldCodec<int> _repeated_repeatedInt32_codec
        = pb::FieldCodec.ForInt32(248);
    private readonly pbc::RepeatedField<int> repeatedInt32_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// Repeated
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> RepeatedInt32 {
      get { return repeatedInt32_; }
    }

    /// <summary>Field number for the "repeated_int64" field.</summary>
    public const int RepeatedInt64FieldNumber = 32;
    private static readonly pb::FieldCodec<long> _repeated_repeatedInt64_codec
        = pb::FieldCodec.ForInt64(256);
    private readonly pbc::RepeatedField<long> repeatedInt64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> RepeatedInt64 {
      get { return repeatedInt64_; }
    }

    /// <summary>Field number for the "repeated_uint32" field.</summary>
    public const int RepeatedUint32FieldNumber = 33;
    private static readonly pb::FieldCodec<uint> _repeated_repeatedUint32_codec
        = pb::FieldCodec.ForUInt32(264);
    private readonly pbc::RepeatedField<uint> repeatedUint32_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> RepeatedUint32 {
      get { return repeatedUint32_; }
    }

    /// <summary>Field number for the "repeated_uint64" field.</summary>
    public const int RepeatedUint64FieldNumber = 34;
    private static readonly pb::FieldCodec<ulong> _repeated_repeatedUint64_codec
        = pb::FieldCodec.ForUInt64(272);
    private readonly pbc::RepeatedField<ulong> repeatedUint64_ = new pbc::RepeatedField<ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> RepeatedUint64 {
      get { return repeatedUint64_; }
    }

    /// <summary>Field number for the "repeated_sint32" field.</summary>
    public const int RepeatedSint32FieldNumber = 35;
    private static readonly pb::FieldCodec<int> _repeated_repeatedSint32_codec
        = pb::FieldCodec.ForSInt32(280);
    private readonly pbc::RepeatedField<int> repeatedSint32_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> RepeatedSint32 {
      get { return repeatedSint32_; }
    }

    /// <summary>Field number for the "repeated_sint64" field.</summary>
    public const int RepeatedSint64FieldNumber = 36;
    private static readonly pb::FieldCodec<long> _repeated_repeatedSint64_codec
        = pb::FieldCodec.ForSInt64(288);
    private readonly pbc::RepeatedField<long> repeatedSint64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> RepeatedSint64 {
      get { return repeatedSint64_; }
    }

    /// <summary>Field number for the "repeated_fixed32" field.</summary>
    public const int RepeatedFixed32FieldNumber = 37;
    private static readonly pb::FieldCodec<uint> _repeated_repeatedFixed32_codec
        = pb::FieldCodec.ForFixed32(301);
    private readonly pbc::RepeatedField<uint> repeatedFixed32_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> RepeatedFixed32 {
      get { return repeatedFixed32_; }
    }

    /// <summary>Field number for the "repeated_fixed64" field.</summary>
    public const int RepeatedFixed64FieldNumber = 38;
    private static readonly pb::FieldCodec<ulong> _repeated_repeatedFixed64_codec
        = pb::FieldCodec.ForFixed64(305);
    private readonly pbc::RepeatedField<ulong> repeatedFixed64_ = new pbc::RepeatedField<ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> RepeatedFixed64 {
      get { return repeatedFixed64_; }
    }

    /// <summary>Field number for the "repeated_sfixed32" field.</summary>
    public const int RepeatedSfixed32FieldNumber = 39;
    private static readonly pb::FieldCodec<int> _repeated_repeatedSfixed32_codec
        = pb::FieldCodec.ForSFixed32(317);
    private readonly pbc::RepeatedField<int> repeatedSfixed32_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> RepeatedSfixed32 {
      get { return repeatedSfixed32_; }
    }

    /// <summary>Field number for the "repeated_sfixed64" field.</summary>
    public const int RepeatedSfixed64FieldNumber = 40;
    private static readonly pb::FieldCodec<long> _repeated_repeatedSfixed64_codec
        = pb::FieldCodec.ForSFixed64(321);
    private readonly pbc::RepeatedField<long> repeatedSfixed64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> RepeatedSfixed64 {
      get { return repeatedSfixed64_; }
    }

    /// <summary>Field number for the "repeated_float" field.</summary>
    public const int RepeatedFloatFieldNumber = 41;
    private static readonly pb::FieldCodec<float> _repeated_repeatedFloat_codec
        = pb::FieldCodec.ForFloat(333);
    private readonly pbc::RepeatedField<float> repeatedFloat_ = new pbc::RepeatedField<float>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<float> RepeatedFloat {
      get { return repeatedFloat_; }
    }

    /// <summary>Field number for the "repeated_double" field.</summary>
    public const int RepeatedDoubleFieldNumber = 42;
    private static readonly pb::FieldCodec<double> _repeated_repeatedDouble_codec
        = pb::FieldCodec.ForDouble(337);
    private readonly pbc::RepeatedField<double> repeatedDouble_ = new pbc::RepeatedField<double>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<double> RepeatedDouble {
      get { return repeatedDouble_; }
    }

    /// <summary>Field number for the "repeated_bool" field.</summary>
    public const int RepeatedBoolFieldNumber = 43;
    private static readonly pb::FieldCodec<bool> _repeated_repeatedBool_codec
        = pb::FieldCodec.ForBool(344);
    private readonly pbc::RepeatedField<bool> repeatedBool_ = new pbc::RepeatedField<bool>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<bool> RepeatedBool {
      get { return repeatedBool_; }
    }

    /// <summary>Field number for the "repeated_string" field.</summary>
    public const int RepeatedStringFieldNumber = 44;
    private static readonly pb::FieldCodec<string> _repeated_repeatedString_codec
        = pb::FieldCodec.ForString(354);
    private readonly pbc::RepeatedField<string> repeatedString_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> RepeatedString {
      get { return repeatedString_; }
    }

    /// <summary>Field number for the "repeated_bytes" field.</summary>
    public const int RepeatedBytesFieldNumber = 45;
    private static readonly pb::FieldCodec<pb::ByteString> _repeated_repeatedBytes_codec
        = pb::FieldCodec.ForBytes(362);
    private readonly pbc::RepeatedField<pb::ByteString> repeatedBytes_ = new pbc::RepeatedField<pb::ByteString>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<pb::ByteString> RepeatedBytes {
      get { return repeatedBytes_; }
    }

    /// <summary>Field number for the "repeated_nested_message" field.</summary>
    public const int RepeatedNestedMessageFieldNumber = 48;
    private static readonly pb::FieldCodec<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage> _repeated_repeatedNestedMessage_codec
        = pb::FieldCodec.ForMessage(386, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage.Parser);
    private readonly pbc::RepeatedField<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage> repeatedNestedMessage_ = new pbc::RepeatedField<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage> RepeatedNestedMessage {
      get { return repeatedNestedMessage_; }
    }

    /// <summary>Field number for the "repeated_foreign_message" field.</summary>
    public const int RepeatedForeignMessageFieldNumber = 49;
    private static readonly pb::FieldCodec<global::ProtobufTestMessages.Proto2.ForeignMessageProto2> _repeated_repeatedForeignMessage_codec
        = pb::FieldCodec.ForMessage(394, global::ProtobufTestMessages.Proto2.ForeignMessageProto2.Parser);
    private readonly pbc::RepeatedField<global::ProtobufTestMessages.Proto2.ForeignMessageProto2> repeatedForeignMessage_ = new pbc::RepeatedField<global::ProtobufTestMessages.Proto2.ForeignMessageProto2>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::ProtobufTestMessages.Proto2.ForeignMessageProto2> RepeatedForeignMessage {
      get { return repeatedForeignMessage_; }
    }

    /// <summary>Field number for the "repeated_nested_enum" field.</summary>
    public const int RepeatedNestedEnumFieldNumber = 51;
    private static readonly pb::FieldCodec<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum> _repeated_repeatedNestedEnum_codec
        = pb::FieldCodec.ForEnum(408, x => (int) x, x => (global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum) x);
    private readonly pbc::RepeatedField<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum> repeatedNestedEnum_ = new pbc::RepeatedField<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum> RepeatedNestedEnum {
      get { return repeatedNestedEnum_; }
    }

    /// <summary>Field number for the "repeated_foreign_enum" field.</summary>
    public const int RepeatedForeignEnumFieldNumber = 52;
    private static readonly pb::FieldCodec<global::ProtobufTestMessages.Proto2.ForeignEnumProto2> _repeated_repeatedForeignEnum_codec
        = pb::FieldCodec.ForEnum(416, x => (int) x, x => (global::ProtobufTestMessages.Proto2.ForeignEnumProto2) x);
    private readonly pbc::RepeatedField<global::ProtobufTestMessages.Proto2.ForeignEnumProto2> repeatedForeignEnum_ = new pbc::RepeatedField<global::ProtobufTestMessages.Proto2.ForeignEnumProto2>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::ProtobufTestMessages.Proto2.ForeignEnumProto2> RepeatedForeignEnum {
      get { return repeatedForeignEnum_; }
    }

    /// <summary>Field number for the "repeated_string_piece" field.</summary>
    public const int RepeatedStringPieceFieldNumber = 54;
    private static readonly pb::FieldCodec<string> _repeated_repeatedStringPiece_codec
        = pb::FieldCodec.ForString(434);
    private readonly pbc::RepeatedField<string> repeatedStringPiece_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> RepeatedStringPiece {
      get { return repeatedStringPiece_; }
    }

    /// <summary>Field number for the "repeated_cord" field.</summary>
    public const int RepeatedCordFieldNumber = 55;
    private static readonly pb::FieldCodec<string> _repeated_repeatedCord_codec
        = pb::FieldCodec.ForString(442);
    private readonly pbc::RepeatedField<string> repeatedCord_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> RepeatedCord {
      get { return repeatedCord_; }
    }

    /// <summary>Field number for the "packed_int32" field.</summary>
    public const int PackedInt32FieldNumber = 75;
    private static readonly pb::FieldCodec<int> _repeated_packedInt32_codec
        = pb::FieldCodec.ForInt32(602);
    private readonly pbc::RepeatedField<int> packedInt32_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// Packed
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> PackedInt32 {
      get { return packedInt32_; }
    }

    /// <summary>Field number for the "packed_int64" field.</summary>
    public const int PackedInt64FieldNumber = 76;
    private static readonly pb::FieldCodec<long> _repeated_packedInt64_codec
        = pb::FieldCodec.ForInt64(610);
    private readonly pbc::RepeatedField<long> packedInt64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> PackedInt64 {
      get { return packedInt64_; }
    }

    /// <summary>Field number for the "packed_uint32" field.</summary>
    public const int PackedUint32FieldNumber = 77;
    private static readonly pb::FieldCodec<uint> _repeated_packedUint32_codec
        = pb::FieldCodec.ForUInt32(618);
    private readonly pbc::RepeatedField<uint> packedUint32_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> PackedUint32 {
      get { return packedUint32_; }
    }

    /// <summary>Field number for the "packed_uint64" field.</summary>
    public const int PackedUint64FieldNumber = 78;
    private static readonly pb::FieldCodec<ulong> _repeated_packedUint64_codec
        = pb::FieldCodec.ForUInt64(626);
    private readonly pbc::RepeatedField<ulong> packedUint64_ = new pbc::RepeatedField<ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> PackedUint64 {
      get { return packedUint64_; }
    }

    /// <summary>Field number for the "packed_sint32" field.</summary>
    public const int PackedSint32FieldNumber = 79;
    private static readonly pb::FieldCodec<int> _repeated_packedSint32_codec
        = pb::FieldCodec.ForSInt32(634);
    private readonly pbc::RepeatedField<int> packedSint32_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> PackedSint32 {
      get { return packedSint32_; }
    }

    /// <summary>Field number for the "packed_sint64" field.</summary>
    public const int PackedSint64FieldNumber = 80;
    private static readonly pb::FieldCodec<long> _repeated_packedSint64_codec
        = pb::FieldCodec.ForSInt64(642);
    private readonly pbc::RepeatedField<long> packedSint64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> PackedSint64 {
      get { return packedSint64_; }
    }

    /// <summary>Field number for the "packed_fixed32" field.</summary>
    public const int PackedFixed32FieldNumber = 81;
    private static readonly pb::FieldCodec<uint> _repeated_packedFixed32_codec
        = pb::FieldCodec.ForFixed32(650);
    private readonly pbc::RepeatedField<uint> packedFixed32_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> PackedFixed32 {
      get { return packedFixed32_; }
    }

    /// <summary>Field number for the "packed_fixed64" field.</summary>
    public const int PackedFixed64FieldNumber = 82;
    private static readonly pb::FieldCodec<ulong> _repeated_packedFixed64_codec
        = pb::FieldCodec.ForFixed64(658);
    private readonly pbc::RepeatedField<ulong> packedFixed64_ = new pbc::RepeatedField<ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> PackedFixed64 {
      get { return packedFixed64_; }
    }

    /// <summary>Field number for the "packed_sfixed32" field.</summary>
    public const int PackedSfixed32FieldNumber = 83;
    private static readonly pb::FieldCodec<int> _repeated_packedSfixed32_codec
        = pb::FieldCodec.ForSFixed32(666);
    private readonly pbc::RepeatedField<int> packedSfixed32_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> PackedSfixed32 {
      get { return packedSfixed32_; }
    }

    /// <summary>Field number for the "packed_sfixed64" field.</summary>
    public const int PackedSfixed64FieldNumber = 84;
    private static readonly pb::FieldCodec<long> _repeated_packedSfixed64_codec
        = pb::FieldCodec.ForSFixed64(674);
    private readonly pbc::RepeatedField<long> packedSfixed64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> PackedSfixed64 {
      get { return packedSfixed64_; }
    }

    /// <summary>Field number for the "packed_float" field.</summary>
    public const int PackedFloatFieldNumber = 85;
    private static readonly pb::FieldCodec<float> _repeated_packedFloat_codec
        = pb::FieldCodec.ForFloat(682);
    private readonly pbc::RepeatedField<float> packedFloat_ = new pbc::RepeatedField<float>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<float> PackedFloat {
      get { return packedFloat_; }
    }

    /// <summary>Field number for the "packed_double" field.</summary>
    public const int PackedDoubleFieldNumber = 86;
    private static readonly pb::FieldCodec<double> _repeated_packedDouble_codec
        = pb::FieldCodec.ForDouble(690);
    private readonly pbc::RepeatedField<double> packedDouble_ = new pbc::RepeatedField<double>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<double> PackedDouble {
      get { return packedDouble_; }
    }

    /// <summary>Field number for the "packed_bool" field.</summary>
    public const int PackedBoolFieldNumber = 87;
    private static readonly pb::FieldCodec<bool> _repeated_packedBool_codec
        = pb::FieldCodec.ForBool(698);
    private readonly pbc::RepeatedField<bool> packedBool_ = new pbc::RepeatedField<bool>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<bool> PackedBool {
      get { return packedBool_; }
    }

    /// <summary>Field number for the "packed_nested_enum" field.</summary>
    public const int PackedNestedEnumFieldNumber = 88;
    private static readonly pb::FieldCodec<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum> _repeated_packedNestedEnum_codec
        = pb::FieldCodec.ForEnum(706, x => (int) x, x => (global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum) x);
    private readonly pbc::RepeatedField<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum> packedNestedEnum_ = new pbc::RepeatedField<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum> PackedNestedEnum {
      get { return packedNestedEnum_; }
    }

    /// <summary>Field number for the "unpacked_int32" field.</summary>
    public const int UnpackedInt32FieldNumber = 89;
    private static readonly pb::FieldCodec<int> _repeated_unpackedInt32_codec
        = pb::FieldCodec.ForInt32(712);
    private readonly pbc::RepeatedField<int> unpackedInt32_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// Unpacked
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> UnpackedInt32 {
      get { return unpackedInt32_; }
    }

    /// <summary>Field number for the "unpacked_int64" field.</summary>
    public const int UnpackedInt64FieldNumber = 90;
    private static readonly pb::FieldCodec<long> _repeated_unpackedInt64_codec
        = pb::FieldCodec.ForInt64(720);
    private readonly pbc::RepeatedField<long> unpackedInt64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> UnpackedInt64 {
      get { return unpackedInt64_; }
    }

    /// <summary>Field number for the "unpacked_uint32" field.</summary>
    public const int UnpackedUint32FieldNumber = 91;
    private static readonly pb::FieldCodec<uint> _repeated_unpackedUint32_codec
        = pb::FieldCodec.ForUInt32(728);
    private readonly pbc::RepeatedField<uint> unpackedUint32_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> UnpackedUint32 {
      get { return unpackedUint32_; }
    }

    /// <summary>Field number for the "unpacked_uint64" field.</summary>
    public const int UnpackedUint64FieldNumber = 92;
    private static readonly pb::FieldCodec<ulong> _repeated_unpackedUint64_codec
        = pb::FieldCodec.ForUInt64(736);
    private readonly pbc::RepeatedField<ulong> unpackedUint64_ = new pbc::RepeatedField<ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> UnpackedUint64 {
      get { return unpackedUint64_; }
    }

    /// <summary>Field number for the "unpacked_sint32" field.</summary>
    public const int UnpackedSint32FieldNumber = 93;
    private static readonly pb::FieldCodec<int> _repeated_unpackedSint32_codec
        = pb::FieldCodec.ForSInt32(744);
    private readonly pbc::RepeatedField<int> unpackedSint32_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> UnpackedSint32 {
      get { return unpackedSint32_; }
    }

    /// <summary>Field number for the "unpacked_sint64" field.</summary>
    public const int UnpackedSint64FieldNumber = 94;
    private static readonly pb::FieldCodec<long> _repeated_unpackedSint64_codec
        = pb::FieldCodec.ForSInt64(752);
    private readonly pbc::RepeatedField<long> unpackedSint64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> UnpackedSint64 {
      get { return unpackedSint64_; }
    }

    /// <summary>Field number for the "unpacked_fixed32" field.</summary>
    public const int UnpackedFixed32FieldNumber = 95;
    private static readonly pb::FieldCodec<uint> _repeated_unpackedFixed32_codec
        = pb::FieldCodec.ForFixed32(765);
    private readonly pbc::RepeatedField<uint> unpackedFixed32_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> UnpackedFixed32 {
      get { return unpackedFixed32_; }
    }

    /// <summary>Field number for the "unpacked_fixed64" field.</summary>
    public const int UnpackedFixed64FieldNumber = 96;
    private static readonly pb::FieldCodec<ulong> _repeated_unpackedFixed64_codec
        = pb::FieldCodec.ForFixed64(769);
    private readonly pbc::RepeatedField<ulong> unpackedFixed64_ = new pbc::RepeatedField<ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> UnpackedFixed64 {
      get { return unpackedFixed64_; }
    }

    /// <summary>Field number for the "unpacked_sfixed32" field.</summary>
    public const int UnpackedSfixed32FieldNumber = 97;
    private static readonly pb::FieldCodec<int> _repeated_unpackedSfixed32_codec
        = pb::FieldCodec.ForSFixed32(781);
    private readonly pbc::RepeatedField<int> unpackedSfixed32_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> UnpackedSfixed32 {
      get { return unpackedSfixed32_; }
    }

    /// <summary>Field number for the "unpacked_sfixed64" field.</summary>
    public const int UnpackedSfixed64FieldNumber = 98;
    private static readonly pb::FieldCodec<long> _repeated_unpackedSfixed64_codec
        = pb::FieldCodec.ForSFixed64(785);
    private readonly pbc::RepeatedField<long> unpackedSfixed64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> UnpackedSfixed64 {
      get { return unpackedSfixed64_; }
    }

    /// <summary>Field number for the "unpacked_float" field.</summary>
    public const int UnpackedFloatFieldNumber = 99;
    private static readonly pb::FieldCodec<float> _repeated_unpackedFloat_codec
        = pb::FieldCodec.ForFloat(797);
    private readonly pbc::RepeatedField<float> unpackedFloat_ = new pbc::RepeatedField<float>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<float> UnpackedFloat {
      get { return unpackedFloat_; }
    }

    /// <summary>Field number for the "unpacked_double" field.</summary>
    public const int UnpackedDoubleFieldNumber = 100;
    private static readonly pb::FieldCodec<double> _repeated_unpackedDouble_codec
        = pb::FieldCodec.ForDouble(801);
    private readonly pbc::RepeatedField<double> unpackedDouble_ = new pbc::RepeatedField<double>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<double> UnpackedDouble {
      get { return unpackedDouble_; }
    }

    /// <summary>Field number for the "unpacked_bool" field.</summary>
    public const int UnpackedBoolFieldNumber = 101;
    private static readonly pb::FieldCodec<bool> _repeated_unpackedBool_codec
        = pb::FieldCodec.ForBool(808);
    private readonly pbc::RepeatedField<bool> unpackedBool_ = new pbc::RepeatedField<bool>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<bool> UnpackedBool {
      get { return unpackedBool_; }
    }

    /// <summary>Field number for the "unpacked_nested_enum" field.</summary>
    public const int UnpackedNestedEnumFieldNumber = 102;
    private static readonly pb::FieldCodec<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum> _repeated_unpackedNestedEnum_codec
        = pb::FieldCodec.ForEnum(816, x => (int) x, x => (global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum) x);
    private readonly pbc::RepeatedField<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum> unpackedNestedEnum_ = new pbc::RepeatedField<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum> UnpackedNestedEnum {
      get { return unpackedNestedEnum_; }
    }

    /// <summary>Field number for the "map_int32_int32" field.</summary>
    public const int MapInt32Int32FieldNumber = 56;
    private static readonly pbc::MapField<int, int>.Codec _map_mapInt32Int32_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 450);
    private readonly pbc::MapField<int, int> mapInt32Int32_ = new pbc::MapField<int, int>();
    /// <summary>
    /// Map
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> MapInt32Int32 {
      get { return mapInt32Int32_; }
    }

    /// <summary>Field number for the "map_int64_int64" field.</summary>
    public const int MapInt64Int64FieldNumber = 57;
    private static readonly pbc::MapField<long, long>.Codec _map_mapInt64Int64_codec
        = new pbc::MapField<long, long>.Codec(pb::FieldCodec.ForInt64(8, 0L), pb::FieldCodec.ForInt64(16, 0L), 458);
    private readonly pbc::MapField<long, long> mapInt64Int64_ = new pbc::MapField<long, long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<long, long> MapInt64Int64 {
      get { return mapInt64Int64_; }
    }

    /// <summary>Field number for the "map_uint32_uint32" field.</summary>
    public const int MapUint32Uint32FieldNumber = 58;
    private static readonly pbc::MapField<uint, uint>.Codec _map_mapUint32Uint32_codec
        = new pbc::MapField<uint, uint>.Codec(pb::FieldCodec.ForUInt32(8, 0), pb::FieldCodec.ForUInt32(16, 0), 466);
    private readonly pbc::MapField<uint, uint> mapUint32Uint32_ = new pbc::MapField<uint, uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<uint, uint> MapUint32Uint32 {
      get { return mapUint32Uint32_; }
    }

    /// <summary>Field number for the "map_uint64_uint64" field.</summary>
    public const int MapUint64Uint64FieldNumber = 59;
    private static readonly pbc::MapField<ulong, ulong>.Codec _map_mapUint64Uint64_codec
        = new pbc::MapField<ulong, ulong>.Codec(pb::FieldCodec.ForUInt64(8, 0UL), pb::FieldCodec.ForUInt64(16, 0UL), 474);
    private readonly pbc::MapField<ulong, ulong> mapUint64Uint64_ = new pbc::MapField<ulong, ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<ulong, ulong> MapUint64Uint64 {
      get { return mapUint64Uint64_; }
    }

    /// <summary>Field number for the "map_sint32_sint32" field.</summary>
    public const int MapSint32Sint32FieldNumber = 60;
    private static readonly pbc::MapField<int, int>.Codec _map_mapSint32Sint32_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForSInt32(8, 0), pb::FieldCodec.ForSInt32(16, 0), 482);
    private readonly pbc::MapField<int, int> mapSint32Sint32_ = new pbc::MapField<int, int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> MapSint32Sint32 {
      get { return mapSint32Sint32_; }
    }

    /// <summary>Field number for the "map_sint64_sint64" field.</summary>
    public const int MapSint64Sint64FieldNumber = 61;
    private static readonly pbc::MapField<long, long>.Codec _map_mapSint64Sint64_codec
        = new pbc::MapField<long, long>.Codec(pb::FieldCodec.ForSInt64(8, 0L), pb::FieldCodec.ForSInt64(16, 0L), 490);
    private readonly pbc::MapField<long, long> mapSint64Sint64_ = new pbc::MapField<long, long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<long, long> MapSint64Sint64 {
      get { return mapSint64Sint64_; }
    }

    /// <summary>Field number for the "map_fixed32_fixed32" field.</summary>
    public const int MapFixed32Fixed32FieldNumber = 62;
    private static readonly pbc::MapField<uint, uint>.Codec _map_mapFixed32Fixed32_codec
        = new pbc::MapField<uint, uint>.Codec(pb::FieldCodec.ForFixed32(13, 0), pb::FieldCodec.ForFixed32(21, 0), 498);
    private readonly pbc::MapField<uint, uint> mapFixed32Fixed32_ = new pbc::MapField<uint, uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<uint, uint> MapFixed32Fixed32 {
      get { return mapFixed32Fixed32_; }
    }

    /// <summary>Field number for the "map_fixed64_fixed64" field.</summary>
    public const int MapFixed64Fixed64FieldNumber = 63;
    private static readonly pbc::MapField<ulong, ulong>.Codec _map_mapFixed64Fixed64_codec
        = new pbc::MapField<ulong, ulong>.Codec(pb::FieldCodec.ForFixed64(9, 0UL), pb::FieldCodec.ForFixed64(17, 0UL), 506);
    private readonly pbc::MapField<ulong, ulong> mapFixed64Fixed64_ = new pbc::MapField<ulong, ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<ulong, ulong> MapFixed64Fixed64 {
      get { return mapFixed64Fixed64_; }
    }

    /// <summary>Field number for the "map_sfixed32_sfixed32" field.</summary>
    public const int MapSfixed32Sfixed32FieldNumber = 64;
    private static readonly pbc::MapField<int, int>.Codec _map_mapSfixed32Sfixed32_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForSFixed32(13, 0), pb::FieldCodec.ForSFixed32(21, 0), 514);
    private readonly pbc::MapField<int, int> mapSfixed32Sfixed32_ = new pbc::MapField<int, int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> MapSfixed32Sfixed32 {
      get { return mapSfixed32Sfixed32_; }
    }

    /// <summary>Field number for the "map_sfixed64_sfixed64" field.</summary>
    public const int MapSfixed64Sfixed64FieldNumber = 65;
    private static readonly pbc::MapField<long, long>.Codec _map_mapSfixed64Sfixed64_codec
        = new pbc::MapField<long, long>.Codec(pb::FieldCodec.ForSFixed64(9, 0L), pb::FieldCodec.ForSFixed64(17, 0L), 522);
    private readonly pbc::MapField<long, long> mapSfixed64Sfixed64_ = new pbc::MapField<long, long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<long, long> MapSfixed64Sfixed64 {
      get { return mapSfixed64Sfixed64_; }
    }

    /// <summary>Field number for the "map_int32_float" field.</summary>
    public const int MapInt32FloatFieldNumber = 66;
    private static readonly pbc::MapField<int, float>.Codec _map_mapInt32Float_codec
        = new pbc::MapField<int, float>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForFloat(21, 0F), 530);
    private readonly pbc::MapField<int, float> mapInt32Float_ = new pbc::MapField<int, float>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, float> MapInt32Float {
      get { return mapInt32Float_; }
    }

    /// <summary>Field number for the "map_int32_double" field.</summary>
    public const int MapInt32DoubleFieldNumber = 67;
    private static readonly pbc::MapField<int, double>.Codec _map_mapInt32Double_codec
        = new pbc::MapField<int, double>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForDouble(17, 0D), 538);
    private readonly pbc::MapField<int, double> mapInt32Double_ = new pbc::MapField<int, double>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, double> MapInt32Double {
      get { return mapInt32Double_; }
    }

    /// <summary>Field number for the "map_bool_bool" field.</summary>
    public const int MapBoolBoolFieldNumber = 68;
    private static readonly pbc::MapField<bool, bool>.Codec _map_mapBoolBool_codec
        = new pbc::MapField<bool, bool>.Codec(pb::FieldCodec.ForBool(8, false), pb::FieldCodec.ForBool(16, false), 546);
    private readonly pbc::MapField<bool, bool> mapBoolBool_ = new pbc::MapField<bool, bool>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<bool, bool> MapBoolBool {
      get { return mapBoolBool_; }
    }

    /// <summary>Field number for the "map_string_string" field.</summary>
    public const int MapStringStringFieldNumber = 69;
    private static readonly pbc::MapField<string, string>.Codec _map_mapStringString_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 554);
    private readonly pbc::MapField<string, string> mapStringString_ = new pbc::MapField<string, string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> MapStringString {
      get { return mapStringString_; }
    }

    /// <summary>Field number for the "map_string_bytes" field.</summary>
    public const int MapStringBytesFieldNumber = 70;
    private static readonly pbc::MapField<string, pb::ByteString>.Codec _map_mapStringBytes_codec
        = new pbc::MapField<string, pb::ByteString>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForBytes(18, pb::ByteString.Empty), 562);
    private readonly pbc::MapField<string, pb::ByteString> mapStringBytes_ = new pbc::MapField<string, pb::ByteString>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, pb::ByteString> MapStringBytes {
      get { return mapStringBytes_; }
    }

    /// <summary>Field number for the "map_string_nested_message" field.</summary>
    public const int MapStringNestedMessageFieldNumber = 71;
    private static readonly pbc::MapField<string, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage>.Codec _map_mapStringNestedMessage_codec
        = new pbc::MapField<string, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForMessage(18, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage.Parser), 570);
    private readonly pbc::MapField<string, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage> mapStringNestedMessage_ = new pbc::MapField<string, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage> MapStringNestedMessage {
      get { return mapStringNestedMessage_; }
    }

    /// <summary>Field number for the "map_string_foreign_message" field.</summary>
    public const int MapStringForeignMessageFieldNumber = 72;
    private static readonly pbc::MapField<string, global::ProtobufTestMessages.Proto2.ForeignMessageProto2>.Codec _map_mapStringForeignMessage_codec
        = new pbc::MapField<string, global::ProtobufTestMessages.Proto2.ForeignMessageProto2>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForMessage(18, global::ProtobufTestMessages.Proto2.ForeignMessageProto2.Parser), 578);
    private readonly pbc::MapField<string, global::ProtobufTestMessages.Proto2.ForeignMessageProto2> mapStringForeignMessage_ = new pbc::MapField<string, global::ProtobufTestMessages.Proto2.ForeignMessageProto2>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::ProtobufTestMessages.Proto2.ForeignMessageProto2> MapStringForeignMessage {
      get { return mapStringForeignMessage_; }
    }

    /// <summary>Field number for the "map_string_nested_enum" field.</summary>
    public const int MapStringNestedEnumFieldNumber = 73;
    private static readonly pbc::MapField<string, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum>.Codec _map_mapStringNestedEnum_codec
        = new pbc::MapField<string, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForEnum(16, x => (int) x, x => (global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum) x, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum.Foo), 586);
    private readonly pbc::MapField<string, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum> mapStringNestedEnum_ = new pbc::MapField<string, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum> MapStringNestedEnum {
      get { return mapStringNestedEnum_; }
    }

    /// <summary>Field number for the "map_string_foreign_enum" field.</summary>
    public const int MapStringForeignEnumFieldNumber = 74;
    private static readonly pbc::MapField<string, global::ProtobufTestMessages.Proto2.ForeignEnumProto2>.Codec _map_mapStringForeignEnum_codec
        = new pbc::MapField<string, global::ProtobufTestMessages.Proto2.ForeignEnumProto2>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForEnum(16, x => (int) x, x => (global::ProtobufTestMessages.Proto2.ForeignEnumProto2) x, global::ProtobufTestMessages.Proto2.ForeignEnumProto2.ForeignFoo), 594);
    private readonly pbc::MapField<string, global::ProtobufTestMessages.Proto2.ForeignEnumProto2> mapStringForeignEnum_ = new pbc::MapField<string, global::ProtobufTestMessages.Proto2.ForeignEnumProto2>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::ProtobufTestMessages.Proto2.ForeignEnumProto2> MapStringForeignEnum {
      get { return mapStringForeignEnum_; }
    }

    /// <summary>Field number for the "oneof_uint32" field.</summary>
    public const int OneofUint32FieldNumber = 111;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint OneofUint32 {
      get { return HasOneofUint32 ? (uint) oneofField_ : 0; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = OneofFieldOneofCase.OneofUint32;
      }
    }
    /// <summary>Gets whether the "oneof_uint32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofUint32 {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofUint32; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_uint32" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofUint32() {
      if (HasOneofUint32) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_nested_message" field.</summary>
    public const int OneofNestedMessageFieldNumber = 112;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage OneofNestedMessage {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage ? (global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage) oneofField_ : null; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = value == null ? OneofFieldOneofCase.None : OneofFieldOneofCase.OneofNestedMessage;
      }
    }

    /// <summary>Field number for the "oneof_string" field.</summary>
    public const int OneofStringFieldNumber = 113;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OneofString {
      get { return HasOneofString ? (string) oneofField_ : ""; }
      set {
        oneofField_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
        oneofFieldCase_ = OneofFieldOneofCase.OneofString;
      }
    }
    /// <summary>Gets whether the "oneof_string" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofString {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofString; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_string" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofString() {
      if (HasOneofString) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_bytes" field.</summary>
    public const int OneofBytesFieldNumber = 114;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString OneofBytes {
      get { return HasOneofBytes ? (pb::ByteString) oneofField_ : pb::ByteString.Empty; }
      set {
        oneofField_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
        oneofFieldCase_ = OneofFieldOneofCase.OneofBytes;
      }
    }
    /// <summary>Gets whether the "oneof_bytes" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofBytes {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofBytes; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_bytes" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofBytes() {
      if (HasOneofBytes) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_bool" field.</summary>
    public const int OneofBoolFieldNumber = 115;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool OneofBool {
      get { return HasOneofBool ? (bool) oneofField_ : false; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = OneofFieldOneofCase.OneofBool;
      }
    }
    /// <summary>Gets whether the "oneof_bool" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofBool {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofBool; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_bool" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofBool() {
      if (HasOneofBool) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_uint64" field.</summary>
    public const int OneofUint64FieldNumber = 116;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong OneofUint64 {
      get { return HasOneofUint64 ? (ulong) oneofField_ : 0UL; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = OneofFieldOneofCase.OneofUint64;
      }
    }
    /// <summary>Gets whether the "oneof_uint64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofUint64 {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofUint64; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_uint64" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofUint64() {
      if (HasOneofUint64) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_float" field.</summary>
    public const int OneofFloatFieldNumber = 117;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float OneofFloat {
      get { return HasOneofFloat ? (float) oneofField_ : 0F; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = OneofFieldOneofCase.OneofFloat;
      }
    }
    /// <summary>Gets whether the "oneof_float" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofFloat {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofFloat; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_float" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofFloat() {
      if (HasOneofFloat) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_double" field.</summary>
    public const int OneofDoubleFieldNumber = 118;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double OneofDouble {
      get { return HasOneofDouble ? (double) oneofField_ : 0D; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = OneofFieldOneofCase.OneofDouble;
      }
    }
    /// <summary>Gets whether the "oneof_double" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofDouble {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofDouble; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_double" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofDouble() {
      if (HasOneofDouble) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_enum" field.</summary>
    public const int OneofEnumFieldNumber = 119;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum OneofEnum {
      get { return HasOneofEnum ? (global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum) oneofField_ : global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum.Foo; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = OneofFieldOneofCase.OneofEnum;
      }
    }
    /// <summary>Gets whether the "oneof_enum" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofEnum {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofEnum; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_enum" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofEnum() {
      if (HasOneofEnum) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int DataFieldNumber = 201;
    private global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.Data data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.Data Data {
      get { return data_; }
      set {
        data_ = value;
      }
    }
    /// <summary>Gets whether the data field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasData {
      get { return data_ != null; }
    }
    /// <summary>Clears the value of the data field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearData() {
      data_ = null;
    }

    /// <summary>Field number for the "default_int32" field.</summary>
    public const int DefaultInt32FieldNumber = 241;
    private readonly static int DefaultInt32DefaultValue = -123456789;

    private int defaultInt32_;
    /// <summary>
    /// default values
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int DefaultInt32 {
      get { if ((_hasBits0 & 32768) != 0) { return defaultInt32_; } else { return DefaultInt32DefaultValue; } }
      set {
        _hasBits0 |= 32768;
        defaultInt32_ = value;
      }
    }
    /// <summary>Gets whether the "default_int32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultInt32 {
      get { return (_hasBits0 & 32768) != 0; }
    }
    /// <summary>Clears the value of the "default_int32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultInt32() {
      _hasBits0 &= ~32768;
    }

    /// <summary>Field number for the "default_int64" field.</summary>
    public const int DefaultInt64FieldNumber = 242;
    private readonly static long DefaultInt64DefaultValue = -9123456789123456789L;

    private long defaultInt64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long DefaultInt64 {
      get { if ((_hasBits0 & 65536) != 0) { return defaultInt64_; } else { return DefaultInt64DefaultValue; } }
      set {
        _hasBits0 |= 65536;
        defaultInt64_ = value;
      }
    }
    /// <summary>Gets whether the "default_int64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultInt64 {
      get { return (_hasBits0 & 65536) != 0; }
    }
    /// <summary>Clears the value of the "default_int64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultInt64() {
      _hasBits0 &= ~65536;
    }

    /// <summary>Field number for the "default_uint32" field.</summary>
    public const int DefaultUint32FieldNumber = 243;
    private readonly static uint DefaultUint32DefaultValue = 2123456789;

    private uint defaultUint32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint DefaultUint32 {
      get { if ((_hasBits0 & 131072) != 0) { return defaultUint32_; } else { return DefaultUint32DefaultValue; } }
      set {
        _hasBits0 |= 131072;
        defaultUint32_ = value;
      }
    }
    /// <summary>Gets whether the "default_uint32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultUint32 {
      get { return (_hasBits0 & 131072) != 0; }
    }
    /// <summary>Clears the value of the "default_uint32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultUint32() {
      _hasBits0 &= ~131072;
    }

    /// <summary>Field number for the "default_uint64" field.</summary>
    public const int DefaultUint64FieldNumber = 244;
    private readonly static ulong DefaultUint64DefaultValue = 10123456789123456789UL;

    private ulong defaultUint64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong DefaultUint64 {
      get { if ((_hasBits0 & 262144) != 0) { return defaultUint64_; } else { return DefaultUint64DefaultValue; } }
      set {
        _hasBits0 |= 262144;
        defaultUint64_ = value;
      }
    }
    /// <summary>Gets whether the "default_uint64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultUint64 {
      get { return (_hasBits0 & 262144) != 0; }
    }
    /// <summary>Clears the value of the "default_uint64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultUint64() {
      _hasBits0 &= ~262144;
    }

    /// <summary>Field number for the "default_sint32" field.</summary>
    public const int DefaultSint32FieldNumber = 245;
    private readonly static int DefaultSint32DefaultValue = -123456789;

    private int defaultSint32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int DefaultSint32 {
      get { if ((_hasBits0 & 524288) != 0) { return defaultSint32_; } else { return DefaultSint32DefaultValue; } }
      set {
        _hasBits0 |= 524288;
        defaultSint32_ = value;
      }
    }
    /// <summary>Gets whether the "default_sint32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultSint32 {
      get { return (_hasBits0 & 524288) != 0; }
    }
    /// <summary>Clears the value of the "default_sint32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultSint32() {
      _hasBits0 &= ~524288;
    }

    /// <summary>Field number for the "default_sint64" field.</summary>
    public const int DefaultSint64FieldNumber = 246;
    private readonly static long DefaultSint64DefaultValue = -9123456789123456789L;

    private long defaultSint64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long DefaultSint64 {
      get { if ((_hasBits0 & 1048576) != 0) { return defaultSint64_; } else { return DefaultSint64DefaultValue; } }
      set {
        _hasBits0 |= 1048576;
        defaultSint64_ = value;
      }
    }
    /// <summary>Gets whether the "default_sint64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultSint64 {
      get { return (_hasBits0 & 1048576) != 0; }
    }
    /// <summary>Clears the value of the "default_sint64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultSint64() {
      _hasBits0 &= ~1048576;
    }

    /// <summary>Field number for the "default_fixed32" field.</summary>
    public const int DefaultFixed32FieldNumber = 247;
    private readonly static uint DefaultFixed32DefaultValue = 2123456789;

    private uint defaultFixed32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint DefaultFixed32 {
      get { if ((_hasBits0 & 2097152) != 0) { return defaultFixed32_; } else { return DefaultFixed32DefaultValue; } }
      set {
        _hasBits0 |= 2097152;
        defaultFixed32_ = value;
      }
    }
    /// <summary>Gets whether the "default_fixed32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultFixed32 {
      get { return (_hasBits0 & 2097152) != 0; }
    }
    /// <summary>Clears the value of the "default_fixed32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultFixed32() {
      _hasBits0 &= ~2097152;
    }

    /// <summary>Field number for the "default_fixed64" field.</summary>
    public const int DefaultFixed64FieldNumber = 248;
    private readonly static ulong DefaultFixed64DefaultValue = 10123456789123456789UL;

    private ulong defaultFixed64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong DefaultFixed64 {
      get { if ((_hasBits0 & 4194304) != 0) { return defaultFixed64_; } else { return DefaultFixed64DefaultValue; } }
      set {
        _hasBits0 |= 4194304;
        defaultFixed64_ = value;
      }
    }
    /// <summary>Gets whether the "default_fixed64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultFixed64 {
      get { return (_hasBits0 & 4194304) != 0; }
    }
    /// <summary>Clears the value of the "default_fixed64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultFixed64() {
      _hasBits0 &= ~4194304;
    }

    /// <summary>Field number for the "default_sfixed32" field.</summary>
    public const int DefaultSfixed32FieldNumber = 249;
    private readonly static int DefaultSfixed32DefaultValue = -123456789;

    private int defaultSfixed32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int DefaultSfixed32 {
      get { if ((_hasBits0 & 8388608) != 0) { return defaultSfixed32_; } else { return DefaultSfixed32DefaultValue; } }
      set {
        _hasBits0 |= 8388608;
        defaultSfixed32_ = value;
      }
    }
    /// <summary>Gets whether the "default_sfixed32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultSfixed32 {
      get { return (_hasBits0 & 8388608) != 0; }
    }
    /// <summary>Clears the value of the "default_sfixed32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultSfixed32() {
      _hasBits0 &= ~8388608;
    }

    /// <summary>Field number for the "default_sfixed64" field.</summary>
    public const int DefaultSfixed64FieldNumber = 250;
    private readonly static long DefaultSfixed64DefaultValue = -9123456789123456789L;

    private long defaultSfixed64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long DefaultSfixed64 {
      get { if ((_hasBits0 & 16777216) != 0) { return defaultSfixed64_; } else { return DefaultSfixed64DefaultValue; } }
      set {
        _hasBits0 |= 16777216;
        defaultSfixed64_ = value;
      }
    }
    /// <summary>Gets whether the "default_sfixed64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultSfixed64 {
      get { return (_hasBits0 & 16777216) != 0; }
    }
    /// <summary>Clears the value of the "default_sfixed64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultSfixed64() {
      _hasBits0 &= ~16777216;
    }

    /// <summary>Field number for the "default_float" field.</summary>
    public const int DefaultFloatFieldNumber = 251;
    private readonly static float DefaultFloatDefaultValue = 9e+09F;

    private float defaultFloat_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float DefaultFloat {
      get { if ((_hasBits0 & 33554432) != 0) { return defaultFloat_; } else { return DefaultFloatDefaultValue; } }
      set {
        _hasBits0 |= 33554432;
        defaultFloat_ = value;
      }
    }
    /// <summary>Gets whether the "default_float" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultFloat {
      get { return (_hasBits0 & 33554432) != 0; }
    }
    /// <summary>Clears the value of the "default_float" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultFloat() {
      _hasBits0 &= ~33554432;
    }

    /// <summary>Field number for the "default_double" field.</summary>
    public const int DefaultDoubleFieldNumber = 252;
    private readonly static double DefaultDoubleDefaultValue = 7e+22D;

    private double defaultDouble_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double DefaultDouble {
      get { if ((_hasBits0 & 67108864) != 0) { return defaultDouble_; } else { return DefaultDoubleDefaultValue; } }
      set {
        _hasBits0 |= 67108864;
        defaultDouble_ = value;
      }
    }
    /// <summary>Gets whether the "default_double" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultDouble {
      get { return (_hasBits0 & 67108864) != 0; }
    }
    /// <summary>Clears the value of the "default_double" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultDouble() {
      _hasBits0 &= ~67108864;
    }

    /// <summary>Field number for the "default_bool" field.</summary>
    public const int DefaultBoolFieldNumber = 253;
    private readonly static bool DefaultBoolDefaultValue = true;

    private bool defaultBool_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool DefaultBool {
      get { if ((_hasBits0 & 134217728) != 0) { return defaultBool_; } else { return DefaultBoolDefaultValue; } }
      set {
        _hasBits0 |= 134217728;
        defaultBool_ = value;
      }
    }
    /// <summary>Gets whether the "default_bool" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultBool {
      get { return (_hasBits0 & 134217728) != 0; }
    }
    /// <summary>Clears the value of the "default_bool" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultBool() {
      _hasBits0 &= ~134217728;
    }

    /// <summary>Field number for the "default_string" field.</summary>
    public const int DefaultStringFieldNumber = 254;
    private readonly static string DefaultStringDefaultValue = global::System.Text.Encoding.UTF8.GetString(global::System.Convert.FromBase64String("Um9zZWJ1ZA=="), 0, 7);

    private string defaultString_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string DefaultString {
      get { return defaultString_ ?? DefaultStringDefaultValue; }
      set {
        defaultString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "default_string" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultString {
      get { return defaultString_ != null; }
    }
    /// <summary>Clears the value of the "default_string" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultString() {
      defaultString_ = null;
    }

    /// <summary>Field number for the "default_bytes" field.</summary>
    public const int DefaultBytesFieldNumber = 255;
    private readonly static pb::ByteString DefaultBytesDefaultValue = pb::ByteString.FromBase64("am9zaHVh");

    private pb::ByteString defaultBytes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString DefaultBytes {
      get { return defaultBytes_ ?? DefaultBytesDefaultValue; }
      set {
        defaultBytes_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "default_bytes" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultBytes {
      get { return defaultBytes_ != null; }
    }
    /// <summary>Clears the value of the "default_bytes" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultBytes() {
      defaultBytes_ = null;
    }

    /// <summary>Field number for the "fieldname1" field.</summary>
    public const int Fieldname1FieldNumber = 401;
    private readonly static int Fieldname1DefaultValue = 0;

    private int fieldname1_;
    /// <summary>
    /// Test field-name-to-JSON-name convention.
    /// (protobuf says names can be any valid C/C++ identifier.)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Fieldname1 {
      get { if ((_hasBits0 & 268435456) != 0) { return fieldname1_; } else { return Fieldname1DefaultValue; } }
      set {
        _hasBits0 |= 268435456;
        fieldname1_ = value;
      }
    }
    /// <summary>Gets whether the "fieldname1" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldname1 {
      get { return (_hasBits0 & 268435456) != 0; }
    }
    /// <summary>Clears the value of the "fieldname1" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldname1() {
      _hasBits0 &= ~268435456;
    }

    /// <summary>Field number for the "field_name2" field.</summary>
    public const int FieldName2FieldNumber = 402;
    private readonly static int FieldName2DefaultValue = 0;

    private int fieldName2_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName2 {
      get { if ((_hasBits0 & 536870912) != 0) { return fieldName2_; } else { return FieldName2DefaultValue; } }
      set {
        _hasBits0 |= 536870912;
        fieldName2_ = value;
      }
    }
    /// <summary>Gets whether the "field_name2" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldName2 {
      get { return (_hasBits0 & 536870912) != 0; }
    }
    /// <summary>Clears the value of the "field_name2" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldName2() {
      _hasBits0 &= ~536870912;
    }

    /// <summary>Field number for the "_field_name3" field.</summary>
    public const int FieldName3FieldNumber = 403;
    private readonly static int FieldName3DefaultValue = 0;

    private int FieldName3_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName3 {
      get { if ((_hasBits0 & 1073741824) != 0) { return FieldName3_; } else { return FieldName3DefaultValue; } }
      set {
        _hasBits0 |= 1073741824;
        FieldName3_ = value;
      }
    }
    /// <summary>Gets whether the "_field_name3" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldName3 {
      get { return (_hasBits0 & 1073741824) != 0; }
    }
    /// <summary>Clears the value of the "_field_name3" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldName3() {
      _hasBits0 &= ~1073741824;
    }

    /// <summary>Field number for the "field__name4_" field.</summary>
    public const int FieldName4FieldNumber = 404;
    private readonly static int FieldName4DefaultValue = 0;

    private int fieldName4_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName4 {
      get { if ((_hasBits0 & -2147483648) != 0) { return fieldName4_; } else { return FieldName4DefaultValue; } }
      set {
        _hasBits0 |= -2147483648;
        fieldName4_ = value;
      }
    }
    /// <summary>Gets whether the "field__name4_" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldName4 {
      get { return (_hasBits0 & -2147483648) != 0; }
    }
    /// <summary>Clears the value of the "field__name4_" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldName4() {
      _hasBits0 &= ~-2147483648;
    }

    /// <summary>Field number for the "field0name5" field.</summary>
    public const int Field0Name5FieldNumber = 405;
    private readonly static int Field0Name5DefaultValue = 0;

    private int field0Name5_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Field0Name5 {
      get { if ((_hasBits1 & 1) != 0) { return field0Name5_; } else { return Field0Name5DefaultValue; } }
      set {
        _hasBits1 |= 1;
        field0Name5_ = value;
      }
    }
    /// <summary>Gets whether the "field0name5" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasField0Name5 {
      get { return (_hasBits1 & 1) != 0; }
    }
    /// <summary>Clears the value of the "field0name5" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearField0Name5() {
      _hasBits1 &= ~1;
    }

    /// <summary>Field number for the "field_0_name6" field.</summary>
    public const int Field0Name6FieldNumber = 406;
    private readonly static int Field0Name6DefaultValue = 0;

    private int field0Name6_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Field0Name6 {
      get { if ((_hasBits1 & 2) != 0) { return field0Name6_; } else { return Field0Name6DefaultValue; } }
      set {
        _hasBits1 |= 2;
        field0Name6_ = value;
      }
    }
    /// <summary>Gets whether the "field_0_name6" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasField0Name6 {
      get { return (_hasBits1 & 2) != 0; }
    }
    /// <summary>Clears the value of the "field_0_name6" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearField0Name6() {
      _hasBits1 &= ~2;
    }

    /// <summary>Field number for the "fieldName7" field.</summary>
    public const int FieldName7FieldNumber = 407;
    private readonly static int FieldName7DefaultValue = 0;

    private int fieldName7_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName7 {
      get { if ((_hasBits1 & 4) != 0) { return fieldName7_; } else { return FieldName7DefaultValue; } }
      set {
        _hasBits1 |= 4;
        fieldName7_ = value;
      }
    }
    /// <summary>Gets whether the "fieldName7" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldName7 {
      get { return (_hasBits1 & 4) != 0; }
    }
    /// <summary>Clears the value of the "fieldName7" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldName7() {
      _hasBits1 &= ~4;
    }

    /// <summary>Field number for the "FieldName8" field.</summary>
    public const int FieldName8FieldNumber = 408;
    private readonly static int FieldName8DefaultValue = 0;

    private int fieldName8_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName8 {
      get { if ((_hasBits1 & 8) != 0) { return fieldName8_; } else { return FieldName8DefaultValue; } }
      set {
        _hasBits1 |= 8;
        fieldName8_ = value;
      }
    }
    /// <summary>Gets whether the "FieldName8" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldName8 {
      get { return (_hasBits1 & 8) != 0; }
    }
    /// <summary>Clears the value of the "FieldName8" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldName8() {
      _hasBits1 &= ~8;
    }

    /// <summary>Field number for the "field_Name9" field.</summary>
    public const int FieldName9FieldNumber = 409;
    private readonly static int FieldName9DefaultValue = 0;

    private int fieldName9_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName9 {
      get { if ((_hasBits1 & 16) != 0) { return fieldName9_; } else { return FieldName9DefaultValue; } }
      set {
        _hasBits1 |= 16;
        fieldName9_ = value;
      }
    }
    /// <summary>Gets whether the "field_Name9" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldName9 {
      get { return (_hasBits1 & 16) != 0; }
    }
    /// <summary>Clears the value of the "field_Name9" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldName9() {
      _hasBits1 &= ~16;
    }

    /// <summary>Field number for the "Field_Name10" field.</summary>
    public const int FieldName10FieldNumber = 410;
    private readonly static int FieldName10DefaultValue = 0;

    private int fieldName10_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName10 {
      get { if ((_hasBits1 & 32) != 0) { return fieldName10_; } else { return FieldName10DefaultValue; } }
      set {
        _hasBits1 |= 32;
        fieldName10_ = value;
      }
    }
    /// <summary>Gets whether the "Field_Name10" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldName10 {
      get { return (_hasBits1 & 32) != 0; }
    }
    /// <summary>Clears the value of the "Field_Name10" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldName10() {
      _hasBits1 &= ~32;
    }

    /// <summary>Field number for the "FIELD_NAME11" field.</summary>
    public const int FIELDNAME11FieldNumber = 411;
    private readonly static int FIELDNAME11DefaultValue = 0;

    private int fIELDNAME11_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FIELDNAME11 {
      get { if ((_hasBits1 & 64) != 0) { return fIELDNAME11_; } else { return FIELDNAME11DefaultValue; } }
      set {
        _hasBits1 |= 64;
        fIELDNAME11_ = value;
      }
    }
    /// <summary>Gets whether the "FIELD_NAME11" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFIELDNAME11 {
      get { return (_hasBits1 & 64) != 0; }
    }
    /// <summary>Clears the value of the "FIELD_NAME11" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFIELDNAME11() {
      _hasBits1 &= ~64;
    }

    /// <summary>Field number for the "FIELD_name12" field.</summary>
    public const int FIELDName12FieldNumber = 412;
    private readonly static int FIELDName12DefaultValue = 0;

    private int fIELDName12_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FIELDName12 {
      get { if ((_hasBits1 & 128) != 0) { return fIELDName12_; } else { return FIELDName12DefaultValue; } }
      set {
        _hasBits1 |= 128;
        fIELDName12_ = value;
      }
    }
    /// <summary>Gets whether the "FIELD_name12" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFIELDName12 {
      get { return (_hasBits1 & 128) != 0; }
    }
    /// <summary>Clears the value of the "FIELD_name12" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFIELDName12() {
      _hasBits1 &= ~128;
    }

    /// <summary>Field number for the "__field_name13" field.</summary>
    public const int FieldName13FieldNumber = 413;
    private readonly static int FieldName13DefaultValue = 0;

    private int FieldName13_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName13 {
      get { if ((_hasBits1 & 256) != 0) { return FieldName13_; } else { return FieldName13DefaultValue; } }
      set {
        _hasBits1 |= 256;
        FieldName13_ = value;
      }
    }
    /// <summary>Gets whether the "__field_name13" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldName13 {
      get { return (_hasBits1 & 256) != 0; }
    }
    /// <summary>Clears the value of the "__field_name13" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldName13() {
      _hasBits1 &= ~256;
    }

    /// <summary>Field number for the "__Field_name14" field.</summary>
    public const int FieldName14FieldNumber = 414;
    private readonly static int FieldName14DefaultValue = 0;

    private int FieldName14_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName14 {
      get { if ((_hasBits1 & 512) != 0) { return FieldName14_; } else { return FieldName14DefaultValue; } }
      set {
        _hasBits1 |= 512;
        FieldName14_ = value;
      }
    }
    /// <summary>Gets whether the "__Field_name14" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldName14 {
      get { return (_hasBits1 & 512) != 0; }
    }
    /// <summary>Clears the value of the "__Field_name14" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldName14() {
      _hasBits1 &= ~512;
    }

    /// <summary>Field number for the "field__name15" field.</summary>
    public const int FieldName15FieldNumber = 415;
    private readonly static int FieldName15DefaultValue = 0;

    private int fieldName15_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName15 {
      get { if ((_hasBits1 & 1024) != 0) { return fieldName15_; } else { return FieldName15DefaultValue; } }
      set {
        _hasBits1 |= 1024;
        fieldName15_ = value;
      }
    }
    /// <summary>Gets whether the "field__name15" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldName15 {
      get { return (_hasBits1 & 1024) != 0; }
    }
    /// <summary>Clears the value of the "field__name15" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldName15() {
      _hasBits1 &= ~1024;
    }

    /// <summary>Field number for the "field__Name16" field.</summary>
    public const int FieldName16FieldNumber = 416;
    private readonly static int FieldName16DefaultValue = 0;

    private int fieldName16_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName16 {
      get { if ((_hasBits1 & 2048) != 0) { return fieldName16_; } else { return FieldName16DefaultValue; } }
      set {
        _hasBits1 |= 2048;
        fieldName16_ = value;
      }
    }
    /// <summary>Gets whether the "field__Name16" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldName16 {
      get { return (_hasBits1 & 2048) != 0; }
    }
    /// <summary>Clears the value of the "field__Name16" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldName16() {
      _hasBits1 &= ~2048;
    }

    /// <summary>Field number for the "field_name17__" field.</summary>
    public const int FieldName17FieldNumber = 417;
    private readonly static int FieldName17DefaultValue = 0;

    private int fieldName17_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName17 {
      get { if ((_hasBits1 & 4096) != 0) { return fieldName17_; } else { return FieldName17DefaultValue; } }
      set {
        _hasBits1 |= 4096;
        fieldName17_ = value;
      }
    }
    /// <summary>Gets whether the "field_name17__" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldName17 {
      get { return (_hasBits1 & 4096) != 0; }
    }
    /// <summary>Clears the value of the "field_name17__" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldName17() {
      _hasBits1 &= ~4096;
    }

    /// <summary>Field number for the "Field_name18__" field.</summary>
    public const int FieldName18FieldNumber = 418;
    private readonly static int FieldName18DefaultValue = 0;

    private int fieldName18_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName18 {
      get { if ((_hasBits1 & 8192) != 0) { return fieldName18_; } else { return FieldName18DefaultValue; } }
      set {
        _hasBits1 |= 8192;
        fieldName18_ = value;
      }
    }
    /// <summary>Gets whether the "Field_name18__" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasFieldName18 {
      get { return (_hasBits1 & 8192) != 0; }
    }
    /// <summary>Clears the value of the "Field_name18__" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearFieldName18() {
      _hasBits1 &= ~8192;
    }

    private object oneofField_;
    /// <summary>Enum of possible cases for the "oneof_field" oneof.</summary>
    public enum OneofFieldOneofCase {
      None = 0,
      OneofUint32 = 111,
      OneofNestedMessage = 112,
      OneofString = 113,
      OneofBytes = 114,
      OneofBool = 115,
      OneofUint64 = 116,
      OneofFloat = 117,
      OneofDouble = 118,
      OneofEnum = 119,
    }
    private OneofFieldOneofCase oneofFieldCase_ = OneofFieldOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public OneofFieldOneofCase OneofFieldCase {
      get { return oneofFieldCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofField() {
      oneofFieldCase_ = OneofFieldOneofCase.None;
      oneofField_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TestAllTypesProto2);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TestAllTypesProto2 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (OptionalInt32 != other.OptionalInt32) return false;
      if (OptionalInt64 != other.OptionalInt64) return false;
      if (OptionalUint32 != other.OptionalUint32) return false;
      if (OptionalUint64 != other.OptionalUint64) return false;
      if (OptionalSint32 != other.OptionalSint32) return false;
      if (OptionalSint64 != other.OptionalSint64) return false;
      if (OptionalFixed32 != other.OptionalFixed32) return false;
      if (OptionalFixed64 != other.OptionalFixed64) return false;
      if (OptionalSfixed32 != other.OptionalSfixed32) return false;
      if (OptionalSfixed64 != other.OptionalSfixed64) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(OptionalFloat, other.OptionalFloat)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(OptionalDouble, other.OptionalDouble)) return false;
      if (OptionalBool != other.OptionalBool) return false;
      if (OptionalString != other.OptionalString) return false;
      if (OptionalBytes != other.OptionalBytes) return false;
      if (!object.Equals(OptionalNestedMessage, other.OptionalNestedMessage)) return false;
      if (!object.Equals(OptionalForeignMessage, other.OptionalForeignMessage)) return false;
      if (OptionalNestedEnum != other.OptionalNestedEnum) return false;
      if (OptionalForeignEnum != other.OptionalForeignEnum) return false;
      if (OptionalStringPiece != other.OptionalStringPiece) return false;
      if (OptionalCord != other.OptionalCord) return false;
      if (!object.Equals(RecursiveMessage, other.RecursiveMessage)) return false;
      if(!repeatedInt32_.Equals(other.repeatedInt32_)) return false;
      if(!repeatedInt64_.Equals(other.repeatedInt64_)) return false;
      if(!repeatedUint32_.Equals(other.repeatedUint32_)) return false;
      if(!repeatedUint64_.Equals(other.repeatedUint64_)) return false;
      if(!repeatedSint32_.Equals(other.repeatedSint32_)) return false;
      if(!repeatedSint64_.Equals(other.repeatedSint64_)) return false;
      if(!repeatedFixed32_.Equals(other.repeatedFixed32_)) return false;
      if(!repeatedFixed64_.Equals(other.repeatedFixed64_)) return false;
      if(!repeatedSfixed32_.Equals(other.repeatedSfixed32_)) return false;
      if(!repeatedSfixed64_.Equals(other.repeatedSfixed64_)) return false;
      if(!repeatedFloat_.Equals(other.repeatedFloat_)) return false;
      if(!repeatedDouble_.Equals(other.repeatedDouble_)) return false;
      if(!repeatedBool_.Equals(other.repeatedBool_)) return false;
      if(!repeatedString_.Equals(other.repeatedString_)) return false;
      if(!repeatedBytes_.Equals(other.repeatedBytes_)) return false;
      if(!repeatedNestedMessage_.Equals(other.repeatedNestedMessage_)) return false;
      if(!repeatedForeignMessage_.Equals(other.repeatedForeignMessage_)) return false;
      if(!repeatedNestedEnum_.Equals(other.repeatedNestedEnum_)) return false;
      if(!repeatedForeignEnum_.Equals(other.repeatedForeignEnum_)) return false;
      if(!repeatedStringPiece_.Equals(other.repeatedStringPiece_)) return false;
      if(!repeatedCord_.Equals(other.repeatedCord_)) return false;
      if(!packedInt32_.Equals(other.packedInt32_)) return false;
      if(!packedInt64_.Equals(other.packedInt64_)) return false;
      if(!packedUint32_.Equals(other.packedUint32_)) return false;
      if(!packedUint64_.Equals(other.packedUint64_)) return false;
      if(!packedSint32_.Equals(other.packedSint32_)) return false;
      if(!packedSint64_.Equals(other.packedSint64_)) return false;
      if(!packedFixed32_.Equals(other.packedFixed32_)) return false;
      if(!packedFixed64_.Equals(other.packedFixed64_)) return false;
      if(!packedSfixed32_.Equals(other.packedSfixed32_)) return false;
      if(!packedSfixed64_.Equals(other.packedSfixed64_)) return false;
      if(!packedFloat_.Equals(other.packedFloat_)) return false;
      if(!packedDouble_.Equals(other.packedDouble_)) return false;
      if(!packedBool_.Equals(other.packedBool_)) return false;
      if(!packedNestedEnum_.Equals(other.packedNestedEnum_)) return false;
      if(!unpackedInt32_.Equals(other.unpackedInt32_)) return false;
      if(!unpackedInt64_.Equals(other.unpackedInt64_)) return false;
      if(!unpackedUint32_.Equals(other.unpackedUint32_)) return false;
      if(!unpackedUint64_.Equals(other.unpackedUint64_)) return false;
      if(!unpackedSint32_.Equals(other.unpackedSint32_)) return false;
      if(!unpackedSint64_.Equals(other.unpackedSint64_)) return false;
      if(!unpackedFixed32_.Equals(other.unpackedFixed32_)) return false;
      if(!unpackedFixed64_.Equals(other.unpackedFixed64_)) return false;
      if(!unpackedSfixed32_.Equals(other.unpackedSfixed32_)) return false;
      if(!unpackedSfixed64_.Equals(other.unpackedSfixed64_)) return false;
      if(!unpackedFloat_.Equals(other.unpackedFloat_)) return false;
      if(!unpackedDouble_.Equals(other.unpackedDouble_)) return false;
      if(!unpackedBool_.Equals(other.unpackedBool_)) return false;
      if(!unpackedNestedEnum_.Equals(other.unpackedNestedEnum_)) return false;
      if (!MapInt32Int32.Equals(other.MapInt32Int32)) return false;
      if (!MapInt64Int64.Equals(other.MapInt64Int64)) return false;
      if (!MapUint32Uint32.Equals(other.MapUint32Uint32)) return false;
      if (!MapUint64Uint64.Equals(other.MapUint64Uint64)) return false;
      if (!MapSint32Sint32.Equals(other.MapSint32Sint32)) return false;
      if (!MapSint64Sint64.Equals(other.MapSint64Sint64)) return false;
      if (!MapFixed32Fixed32.Equals(other.MapFixed32Fixed32)) return false;
      if (!MapFixed64Fixed64.Equals(other.MapFixed64Fixed64)) return false;
      if (!MapSfixed32Sfixed32.Equals(other.MapSfixed32Sfixed32)) return false;
      if (!MapSfixed64Sfixed64.Equals(other.MapSfixed64Sfixed64)) return false;
      if (!MapInt32Float.Equals(other.MapInt32Float)) return false;
      if (!MapInt32Double.Equals(other.MapInt32Double)) return false;
      if (!MapBoolBool.Equals(other.MapBoolBool)) return false;
      if (!MapStringString.Equals(other.MapStringString)) return false;
      if (!MapStringBytes.Equals(other.MapStringBytes)) return false;
      if (!MapStringNestedMessage.Equals(other.MapStringNestedMessage)) return false;
      if (!MapStringForeignMessage.Equals(other.MapStringForeignMessage)) return false;
      if (!MapStringNestedEnum.Equals(other.MapStringNestedEnum)) return false;
      if (!MapStringForeignEnum.Equals(other.MapStringForeignEnum)) return false;
      if (OneofUint32 != other.OneofUint32) return false;
      if (!object.Equals(OneofNestedMessage, other.OneofNestedMessage)) return false;
      if (OneofString != other.OneofString) return false;
      if (OneofBytes != other.OneofBytes) return false;
      if (OneofBool != other.OneofBool) return false;
      if (OneofUint64 != other.OneofUint64) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(OneofFloat, other.OneofFloat)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(OneofDouble, other.OneofDouble)) return false;
      if (OneofEnum != other.OneofEnum) return false;
      if (!object.Equals(Data, other.Data)) return false;
      if (DefaultInt32 != other.DefaultInt32) return false;
      if (DefaultInt64 != other.DefaultInt64) return false;
      if (DefaultUint32 != other.DefaultUint32) return false;
      if (DefaultUint64 != other.DefaultUint64) return false;
      if (DefaultSint32 != other.DefaultSint32) return false;
      if (DefaultSint64 != other.DefaultSint64) return false;
      if (DefaultFixed32 != other.DefaultFixed32) return false;
      if (DefaultFixed64 != other.DefaultFixed64) return false;
      if (DefaultSfixed32 != other.DefaultSfixed32) return false;
      if (DefaultSfixed64 != other.DefaultSfixed64) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(DefaultFloat, other.DefaultFloat)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(DefaultDouble, other.DefaultDouble)) return false;
      if (DefaultBool != other.DefaultBool) return false;
      if (DefaultString != other.DefaultString) return false;
      if (DefaultBytes != other.DefaultBytes) return false;
      if (Fieldname1 != other.Fieldname1) return false;
      if (FieldName2 != other.FieldName2) return false;
      if (FieldName3 != other.FieldName3) return false;
      if (FieldName4 != other.FieldName4) return false;
      if (Field0Name5 != other.Field0Name5) return false;
      if (Field0Name6 != other.Field0Name6) return false;
      if (FieldName7 != other.FieldName7) return false;
      if (FieldName8 != other.FieldName8) return false;
      if (FieldName9 != other.FieldName9) return false;
      if (FieldName10 != other.FieldName10) return false;
      if (FIELDNAME11 != other.FIELDNAME11) return false;
      if (FIELDName12 != other.FIELDName12) return false;
      if (FieldName13 != other.FieldName13) return false;
      if (FieldName14 != other.FieldName14) return false;
      if (FieldName15 != other.FieldName15) return false;
      if (FieldName16 != other.FieldName16) return false;
      if (FieldName17 != other.FieldName17) return false;
      if (FieldName18 != other.FieldName18) return false;
      if (OneofFieldCase != other.OneofFieldCase) return false;
      if (!Equals(_extensions, other._extensions)) {
        return false;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasOptionalInt32) hash ^= OptionalInt32.GetHashCode();
      if (HasOptionalInt64) hash ^= OptionalInt64.GetHashCode();
      if (HasOptionalUint32) hash ^= OptionalUint32.GetHashCode();
      if (HasOptionalUint64) hash ^= OptionalUint64.GetHashCode();
      if (HasOptionalSint32) hash ^= OptionalSint32.GetHashCode();
      if (HasOptionalSint64) hash ^= OptionalSint64.GetHashCode();
      if (HasOptionalFixed32) hash ^= OptionalFixed32.GetHashCode();
      if (HasOptionalFixed64) hash ^= OptionalFixed64.GetHashCode();
      if (HasOptionalSfixed32) hash ^= OptionalSfixed32.GetHashCode();
      if (HasOptionalSfixed64) hash ^= OptionalSfixed64.GetHashCode();
      if (HasOptionalFloat) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(OptionalFloat);
      if (HasOptionalDouble) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(OptionalDouble);
      if (HasOptionalBool) hash ^= OptionalBool.GetHashCode();
      if (HasOptionalString) hash ^= OptionalString.GetHashCode();
      if (HasOptionalBytes) hash ^= OptionalBytes.GetHashCode();
      if (optionalNestedMessage_ != null) hash ^= OptionalNestedMessage.GetHashCode();
      if (optionalForeignMessage_ != null) hash ^= OptionalForeignMessage.GetHashCode();
      if (HasOptionalNestedEnum) hash ^= OptionalNestedEnum.GetHashCode();
      if (HasOptionalForeignEnum) hash ^= OptionalForeignEnum.GetHashCode();
      if (HasOptionalStringPiece) hash ^= OptionalStringPiece.GetHashCode();
      if (HasOptionalCord) hash ^= OptionalCord.GetHashCode();
      if (recursiveMessage_ != null) hash ^= RecursiveMessage.GetHashCode();
      hash ^= repeatedInt32_.GetHashCode();
      hash ^= repeatedInt64_.GetHashCode();
      hash ^= repeatedUint32_.GetHashCode();
      hash ^= repeatedUint64_.GetHashCode();
      hash ^= repeatedSint32_.GetHashCode();
      hash ^= repeatedSint64_.GetHashCode();
      hash ^= repeatedFixed32_.GetHashCode();
      hash ^= repeatedFixed64_.GetHashCode();
      hash ^= repeatedSfixed32_.GetHashCode();
      hash ^= repeatedSfixed64_.GetHashCode();
      hash ^= repeatedFloat_.GetHashCode();
      hash ^= repeatedDouble_.GetHashCode();
      hash ^= repeatedBool_.GetHashCode();
      hash ^= repeatedString_.GetHashCode();
      hash ^= repeatedBytes_.GetHashCode();
      hash ^= repeatedNestedMessage_.GetHashCode();
      hash ^= repeatedForeignMessage_.GetHashCode();
      hash ^= repeatedNestedEnum_.GetHashCode();
      hash ^= repeatedForeignEnum_.GetHashCode();
      hash ^= repeatedStringPiece_.GetHashCode();
      hash ^= repeatedCord_.GetHashCode();
      hash ^= packedInt32_.GetHashCode();
      hash ^= packedInt64_.GetHashCode();
      hash ^= packedUint32_.GetHashCode();
      hash ^= packedUint64_.GetHashCode();
      hash ^= packedSint32_.GetHashCode();
      hash ^= packedSint64_.GetHashCode();
      hash ^= packedFixed32_.GetHashCode();
      hash ^= packedFixed64_.GetHashCode();
      hash ^= packedSfixed32_.GetHashCode();
      hash ^= packedSfixed64_.GetHashCode();
      hash ^= packedFloat_.GetHashCode();
      hash ^= packedDouble_.GetHashCode();
      hash ^= packedBool_.GetHashCode();
      hash ^= packedNestedEnum_.GetHashCode();
      hash ^= unpackedInt32_.GetHashCode();
      hash ^= unpackedInt64_.GetHashCode();
      hash ^= unpackedUint32_.GetHashCode();
      hash ^= unpackedUint64_.GetHashCode();
      hash ^= unpackedSint32_.GetHashCode();
      hash ^= unpackedSint64_.GetHashCode();
      hash ^= unpackedFixed32_.GetHashCode();
      hash ^= unpackedFixed64_.GetHashCode();
      hash ^= unpackedSfixed32_.GetHashCode();
      hash ^= unpackedSfixed64_.GetHashCode();
      hash ^= unpackedFloat_.GetHashCode();
      hash ^= unpackedDouble_.GetHashCode();
      hash ^= unpackedBool_.GetHashCode();
      hash ^= unpackedNestedEnum_.GetHashCode();
      hash ^= MapInt32Int32.GetHashCode();
      hash ^= MapInt64Int64.GetHashCode();
      hash ^= MapUint32Uint32.GetHashCode();
      hash ^= MapUint64Uint64.GetHashCode();
      hash ^= MapSint32Sint32.GetHashCode();
      hash ^= MapSint64Sint64.GetHashCode();
      hash ^= MapFixed32Fixed32.GetHashCode();
      hash ^= MapFixed64Fixed64.GetHashCode();
      hash ^= MapSfixed32Sfixed32.GetHashCode();
      hash ^= MapSfixed64Sfixed64.GetHashCode();
      hash ^= MapInt32Float.GetHashCode();
      hash ^= MapInt32Double.GetHashCode();
      hash ^= MapBoolBool.GetHashCode();
      hash ^= MapStringString.GetHashCode();
      hash ^= MapStringBytes.GetHashCode();
      hash ^= MapStringNestedMessage.GetHashCode();
      hash ^= MapStringForeignMessage.GetHashCode();
      hash ^= MapStringNestedEnum.GetHashCode();
      hash ^= MapStringForeignEnum.GetHashCode();
      if (HasOneofUint32) hash ^= OneofUint32.GetHashCode();
      if (oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage) hash ^= OneofNestedMessage.GetHashCode();
      if (HasOneofString) hash ^= OneofString.GetHashCode();
      if (HasOneofBytes) hash ^= OneofBytes.GetHashCode();
      if (HasOneofBool) hash ^= OneofBool.GetHashCode();
      if (HasOneofUint64) hash ^= OneofUint64.GetHashCode();
      if (HasOneofFloat) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(OneofFloat);
      if (HasOneofDouble) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(OneofDouble);
      if (HasOneofEnum) hash ^= OneofEnum.GetHashCode();
      if (HasData) hash ^= Data.GetHashCode();
      if (HasDefaultInt32) hash ^= DefaultInt32.GetHashCode();
      if (HasDefaultInt64) hash ^= DefaultInt64.GetHashCode();
      if (HasDefaultUint32) hash ^= DefaultUint32.GetHashCode();
      if (HasDefaultUint64) hash ^= DefaultUint64.GetHashCode();
      if (HasDefaultSint32) hash ^= DefaultSint32.GetHashCode();
      if (HasDefaultSint64) hash ^= DefaultSint64.GetHashCode();
      if (HasDefaultFixed32) hash ^= DefaultFixed32.GetHashCode();
      if (HasDefaultFixed64) hash ^= DefaultFixed64.GetHashCode();
      if (HasDefaultSfixed32) hash ^= DefaultSfixed32.GetHashCode();
      if (HasDefaultSfixed64) hash ^= DefaultSfixed64.GetHashCode();
      if (HasDefaultFloat) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(DefaultFloat);
      if (HasDefaultDouble) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(DefaultDouble);
      if (HasDefaultBool) hash ^= DefaultBool.GetHashCode();
      if (HasDefaultString) hash ^= DefaultString.GetHashCode();
      if (HasDefaultBytes) hash ^= DefaultBytes.GetHashCode();
      if (HasFieldname1) hash ^= Fieldname1.GetHashCode();
      if (HasFieldName2) hash ^= FieldName2.GetHashCode();
      if (HasFieldName3) hash ^= FieldName3.GetHashCode();
      if (HasFieldName4) hash ^= FieldName4.GetHashCode();
      if (HasField0Name5) hash ^= Field0Name5.GetHashCode();
      if (HasField0Name6) hash ^= Field0Name6.GetHashCode();
      if (HasFieldName7) hash ^= FieldName7.GetHashCode();
      if (HasFieldName8) hash ^= FieldName8.GetHashCode();
      if (HasFieldName9) hash ^= FieldName9.GetHashCode();
      if (HasFieldName10) hash ^= FieldName10.GetHashCode();
      if (HasFIELDNAME11) hash ^= FIELDNAME11.GetHashCode();
      if (HasFIELDName12) hash ^= FIELDName12.GetHashCode();
      if (HasFieldName13) hash ^= FieldName13.GetHashCode();
      if (HasFieldName14) hash ^= FieldName14.GetHashCode();
      if (HasFieldName15) hash ^= FieldName15.GetHashCode();
      if (HasFieldName16) hash ^= FieldName16.GetHashCode();
      if (HasFieldName17) hash ^= FieldName17.GetHashCode();
      if (HasFieldName18) hash ^= FieldName18.GetHashCode();
      hash ^= (int) oneofFieldCase_;
      if (_extensions != null) {
        hash ^= _extensions.GetHashCode();
      }
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasOptionalInt32) {
        output.WriteRawTag(8);
        output.WriteInt32(OptionalInt32);
      }
      if (HasOptionalInt64) {
        output.WriteRawTag(16);
        output.WriteInt64(OptionalInt64);
      }
      if (HasOptionalUint32) {
        output.WriteRawTag(24);
        output.WriteUInt32(OptionalUint32);
      }
      if (HasOptionalUint64) {
        output.WriteRawTag(32);
        output.WriteUInt64(OptionalUint64);
      }
      if (HasOptionalSint32) {
        output.WriteRawTag(40);
        output.WriteSInt32(OptionalSint32);
      }
      if (HasOptionalSint64) {
        output.WriteRawTag(48);
        output.WriteSInt64(OptionalSint64);
      }
      if (HasOptionalFixed32) {
        output.WriteRawTag(61);
        output.WriteFixed32(OptionalFixed32);
      }
      if (HasOptionalFixed64) {
        output.WriteRawTag(65);
        output.WriteFixed64(OptionalFixed64);
      }
      if (HasOptionalSfixed32) {
        output.WriteRawTag(77);
        output.WriteSFixed32(OptionalSfixed32);
      }
      if (HasOptionalSfixed64) {
        output.WriteRawTag(81);
        output.WriteSFixed64(OptionalSfixed64);
      }
      if (HasOptionalFloat) {
        output.WriteRawTag(93);
        output.WriteFloat(OptionalFloat);
      }
      if (HasOptionalDouble) {
        output.WriteRawTag(97);
        output.WriteDouble(OptionalDouble);
      }
      if (HasOptionalBool) {
        output.WriteRawTag(104);
        output.WriteBool(OptionalBool);
      }
      if (HasOptionalString) {
        output.WriteRawTag(114);
        output.WriteString(OptionalString);
      }
      if (HasOptionalBytes) {
        output.WriteRawTag(122);
        output.WriteBytes(OptionalBytes);
      }
      if (optionalNestedMessage_ != null) {
        output.WriteRawTag(146, 1);
        output.WriteMessage(OptionalNestedMessage);
      }
      if (optionalForeignMessage_ != null) {
        output.WriteRawTag(154, 1);
        output.WriteMessage(OptionalForeignMessage);
      }
      if (HasOptionalNestedEnum) {
        output.WriteRawTag(168, 1);
        output.WriteEnum((int) OptionalNestedEnum);
      }
      if (HasOptionalForeignEnum) {
        output.WriteRawTag(176, 1);
        output.WriteEnum((int) OptionalForeignEnum);
      }
      if (HasOptionalStringPiece) {
        output.WriteRawTag(194, 1);
        output.WriteString(OptionalStringPiece);
      }
      if (HasOptionalCord) {
        output.WriteRawTag(202, 1);
        output.WriteString(OptionalCord);
      }
      if (recursiveMessage_ != null) {
        output.WriteRawTag(218, 1);
        output.WriteMessage(RecursiveMessage);
      }
      repeatedInt32_.WriteTo(output, _repeated_repeatedInt32_codec);
      repeatedInt64_.WriteTo(output, _repeated_repeatedInt64_codec);
      repeatedUint32_.WriteTo(output, _repeated_repeatedUint32_codec);
      repeatedUint64_.WriteTo(output, _repeated_repeatedUint64_codec);
      repeatedSint32_.WriteTo(output, _repeated_repeatedSint32_codec);
      repeatedSint64_.WriteTo(output, _repeated_repeatedSint64_codec);
      repeatedFixed32_.WriteTo(output, _repeated_repeatedFixed32_codec);
      repeatedFixed64_.WriteTo(output, _repeated_repeatedFixed64_codec);
      repeatedSfixed32_.WriteTo(output, _repeated_repeatedSfixed32_codec);
      repeatedSfixed64_.WriteTo(output, _repeated_repeatedSfixed64_codec);
      repeatedFloat_.WriteTo(output, _repeated_repeatedFloat_codec);
      repeatedDouble_.WriteTo(output, _repeated_repeatedDouble_codec);
      repeatedBool_.WriteTo(output, _repeated_repeatedBool_codec);
      repeatedString_.WriteTo(output, _repeated_repeatedString_codec);
      repeatedBytes_.WriteTo(output, _repeated_repeatedBytes_codec);
      repeatedNestedMessage_.WriteTo(output, _repeated_repeatedNestedMessage_codec);
      repeatedForeignMessage_.WriteTo(output, _repeated_repeatedForeignMessage_codec);
      repeatedNestedEnum_.WriteTo(output, _repeated_repeatedNestedEnum_codec);
      repeatedForeignEnum_.WriteTo(output, _repeated_repeatedForeignEnum_codec);
      repeatedStringPiece_.WriteTo(output, _repeated_repeatedStringPiece_codec);
      repeatedCord_.WriteTo(output, _repeated_repeatedCord_codec);
      mapInt32Int32_.WriteTo(output, _map_mapInt32Int32_codec);
      mapInt64Int64_.WriteTo(output, _map_mapInt64Int64_codec);
      mapUint32Uint32_.WriteTo(output, _map_mapUint32Uint32_codec);
      mapUint64Uint64_.WriteTo(output, _map_mapUint64Uint64_codec);
      mapSint32Sint32_.WriteTo(output, _map_mapSint32Sint32_codec);
      mapSint64Sint64_.WriteTo(output, _map_mapSint64Sint64_codec);
      mapFixed32Fixed32_.WriteTo(output, _map_mapFixed32Fixed32_codec);
      mapFixed64Fixed64_.WriteTo(output, _map_mapFixed64Fixed64_codec);
      mapSfixed32Sfixed32_.WriteTo(output, _map_mapSfixed32Sfixed32_codec);
      mapSfixed64Sfixed64_.WriteTo(output, _map_mapSfixed64Sfixed64_codec);
      mapInt32Float_.WriteTo(output, _map_mapInt32Float_codec);
      mapInt32Double_.WriteTo(output, _map_mapInt32Double_codec);
      mapBoolBool_.WriteTo(output, _map_mapBoolBool_codec);
      mapStringString_.WriteTo(output, _map_mapStringString_codec);
      mapStringBytes_.WriteTo(output, _map_mapStringBytes_codec);
      mapStringNestedMessage_.WriteTo(output, _map_mapStringNestedMessage_codec);
      mapStringForeignMessage_.WriteTo(output, _map_mapStringForeignMessage_codec);
      mapStringNestedEnum_.WriteTo(output, _map_mapStringNestedEnum_codec);
      mapStringForeignEnum_.WriteTo(output, _map_mapStringForeignEnum_codec);
      packedInt32_.WriteTo(output, _repeated_packedInt32_codec);
      packedInt64_.WriteTo(output, _repeated_packedInt64_codec);
      packedUint32_.WriteTo(output, _repeated_packedUint32_codec);
      packedUint64_.WriteTo(output, _repeated_packedUint64_codec);
      packedSint32_.WriteTo(output, _repeated_packedSint32_codec);
      packedSint64_.WriteTo(output, _repeated_packedSint64_codec);
      packedFixed32_.WriteTo(output, _repeated_packedFixed32_codec);
      packedFixed64_.WriteTo(output, _repeated_packedFixed64_codec);
      packedSfixed32_.WriteTo(output, _repeated_packedSfixed32_codec);
      packedSfixed64_.WriteTo(output, _repeated_packedSfixed64_codec);
      packedFloat_.WriteTo(output, _repeated_packedFloat_codec);
      packedDouble_.WriteTo(output, _repeated_packedDouble_codec);
      packedBool_.WriteTo(output, _repeated_packedBool_codec);
      packedNestedEnum_.WriteTo(output, _repeated_packedNestedEnum_codec);
      unpackedInt32_.WriteTo(output, _repeated_unpackedInt32_codec);
      unpackedInt64_.WriteTo(output, _repeated_unpackedInt64_codec);
      unpackedUint32_.WriteTo(output, _repeated_unpackedUint32_codec);
      unpackedUint64_.WriteTo(output, _repeated_unpackedUint64_codec);
      unpackedSint32_.WriteTo(output, _repeated_unpackedSint32_codec);
      unpackedSint64_.WriteTo(output, _repeated_unpackedSint64_codec);
      unpackedFixed32_.WriteTo(output, _repeated_unpackedFixed32_codec);
      unpackedFixed64_.WriteTo(output, _repeated_unpackedFixed64_codec);
      unpackedSfixed32_.WriteTo(output, _repeated_unpackedSfixed32_codec);
      unpackedSfixed64_.WriteTo(output, _repeated_unpackedSfixed64_codec);
      unpackedFloat_.WriteTo(output, _repeated_unpackedFloat_codec);
      unpackedDouble_.WriteTo(output, _repeated_unpackedDouble_codec);
      unpackedBool_.WriteTo(output, _repeated_unpackedBool_codec);
      unpackedNestedEnum_.WriteTo(output, _repeated_unpackedNestedEnum_codec);
      if (HasOneofUint32) {
        output.WriteRawTag(248, 6);
        output.WriteUInt32(OneofUint32);
      }
      if (oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage) {
        output.WriteRawTag(130, 7);
        output.WriteMessage(OneofNestedMessage);
      }
      if (HasOneofString) {
        output.WriteRawTag(138, 7);
        output.WriteString(OneofString);
      }
      if (HasOneofBytes) {
        output.WriteRawTag(146, 7);
        output.WriteBytes(OneofBytes);
      }
      if (HasOneofBool) {
        output.WriteRawTag(152, 7);
        output.WriteBool(OneofBool);
      }
      if (HasOneofUint64) {
        output.WriteRawTag(160, 7);
        output.WriteUInt64(OneofUint64);
      }
      if (HasOneofFloat) {
        output.WriteRawTag(173, 7);
        output.WriteFloat(OneofFloat);
      }
      if (HasOneofDouble) {
        output.WriteRawTag(177, 7);
        output.WriteDouble(OneofDouble);
      }
      if (HasOneofEnum) {
        output.WriteRawTag(184, 7);
        output.WriteEnum((int) OneofEnum);
      }
      if (HasData) {
        output.WriteRawTag(203, 12);
        output.WriteGroup(Data);
        output.WriteRawTag(204, 12);
      }
      if (HasDefaultInt32) {
        output.WriteRawTag(136, 15);
        output.WriteInt32(DefaultInt32);
      }
      if (HasDefaultInt64) {
        output.WriteRawTag(144, 15);
        output.WriteInt64(DefaultInt64);
      }
      if (HasDefaultUint32) {
        output.WriteRawTag(152, 15);
        output.WriteUInt32(DefaultUint32);
      }
      if (HasDefaultUint64) {
        output.WriteRawTag(160, 15);
        output.WriteUInt64(DefaultUint64);
      }
      if (HasDefaultSint32) {
        output.WriteRawTag(168, 15);
        output.WriteSInt32(DefaultSint32);
      }
      if (HasDefaultSint64) {
        output.WriteRawTag(176, 15);
        output.WriteSInt64(DefaultSint64);
      }
      if (HasDefaultFixed32) {
        output.WriteRawTag(189, 15);
        output.WriteFixed32(DefaultFixed32);
      }
      if (HasDefaultFixed64) {
        output.WriteRawTag(193, 15);
        output.WriteFixed64(DefaultFixed64);
      }
      if (HasDefaultSfixed32) {
        output.WriteRawTag(205, 15);
        output.WriteSFixed32(DefaultSfixed32);
      }
      if (HasDefaultSfixed64) {
        output.WriteRawTag(209, 15);
        output.WriteSFixed64(DefaultSfixed64);
      }
      if (HasDefaultFloat) {
        output.WriteRawTag(221, 15);
        output.WriteFloat(DefaultFloat);
      }
      if (HasDefaultDouble) {
        output.WriteRawTag(225, 15);
        output.WriteDouble(DefaultDouble);
      }
      if (HasDefaultBool) {
        output.WriteRawTag(232, 15);
        output.WriteBool(DefaultBool);
      }
      if (HasDefaultString) {
        output.WriteRawTag(242, 15);
        output.WriteString(DefaultString);
      }
      if (HasDefaultBytes) {
        output.WriteRawTag(250, 15);
        output.WriteBytes(DefaultBytes);
      }
      if (HasFieldname1) {
        output.WriteRawTag(136, 25);
        output.WriteInt32(Fieldname1);
      }
      if (HasFieldName2) {
        output.WriteRawTag(144, 25);
        output.WriteInt32(FieldName2);
      }
      if (HasFieldName3) {
        output.WriteRawTag(152, 25);
        output.WriteInt32(FieldName3);
      }
      if (HasFieldName4) {
        output.WriteRawTag(160, 25);
        output.WriteInt32(FieldName4);
      }
      if (HasField0Name5) {
        output.WriteRawTag(168, 25);
        output.WriteInt32(Field0Name5);
      }
      if (HasField0Name6) {
        output.WriteRawTag(176, 25);
        output.WriteInt32(Field0Name6);
      }
      if (HasFieldName7) {
        output.WriteRawTag(184, 25);
        output.WriteInt32(FieldName7);
      }
      if (HasFieldName8) {
        output.WriteRawTag(192, 25);
        output.WriteInt32(FieldName8);
      }
      if (HasFieldName9) {
        output.WriteRawTag(200, 25);
        output.WriteInt32(FieldName9);
      }
      if (HasFieldName10) {
        output.WriteRawTag(208, 25);
        output.WriteInt32(FieldName10);
      }
      if (HasFIELDNAME11) {
        output.WriteRawTag(216, 25);
        output.WriteInt32(FIELDNAME11);
      }
      if (HasFIELDName12) {
        output.WriteRawTag(224, 25);
        output.WriteInt32(FIELDName12);
      }
      if (HasFieldName13) {
        output.WriteRawTag(232, 25);
        output.WriteInt32(FieldName13);
      }
      if (HasFieldName14) {
        output.WriteRawTag(240, 25);
        output.WriteInt32(FieldName14);
      }
      if (HasFieldName15) {
        output.WriteRawTag(248, 25);
        output.WriteInt32(FieldName15);
      }
      if (HasFieldName16) {
        output.WriteRawTag(128, 26);
        output.WriteInt32(FieldName16);
      }
      if (HasFieldName17) {
        output.WriteRawTag(136, 26);
        output.WriteInt32(FieldName17);
      }
      if (HasFieldName18) {
        output.WriteRawTag(144, 26);
        output.WriteInt32(FieldName18);
      }
      if (_extensions != null) {
        _extensions.WriteTo(output);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasOptionalInt32) {
        output.WriteRawTag(8);
        output.WriteInt32(OptionalInt32);
      }
      if (HasOptionalInt64) {
        output.WriteRawTag(16);
        output.WriteInt64(OptionalInt64);
      }
      if (HasOptionalUint32) {
        output.WriteRawTag(24);
        output.WriteUInt32(OptionalUint32);
      }
      if (HasOptionalUint64) {
        output.WriteRawTag(32);
        output.WriteUInt64(OptionalUint64);
      }
      if (HasOptionalSint32) {
        output.WriteRawTag(40);
        output.WriteSInt32(OptionalSint32);
      }
      if (HasOptionalSint64) {
        output.WriteRawTag(48);
        output.WriteSInt64(OptionalSint64);
      }
      if (HasOptionalFixed32) {
        output.WriteRawTag(61);
        output.WriteFixed32(OptionalFixed32);
      }
      if (HasOptionalFixed64) {
        output.WriteRawTag(65);
        output.WriteFixed64(OptionalFixed64);
      }
      if (HasOptionalSfixed32) {
        output.WriteRawTag(77);
        output.WriteSFixed32(OptionalSfixed32);
      }
      if (HasOptionalSfixed64) {
        output.WriteRawTag(81);
        output.WriteSFixed64(OptionalSfixed64);
      }
      if (HasOptionalFloat) {
        output.WriteRawTag(93);
        output.WriteFloat(OptionalFloat);
      }
      if (HasOptionalDouble) {
        output.WriteRawTag(97);
        output.WriteDouble(OptionalDouble);
      }
      if (HasOptionalBool) {
        output.WriteRawTag(104);
        output.WriteBool(OptionalBool);
      }
      if (HasOptionalString) {
        output.WriteRawTag(114);
        output.WriteString(OptionalString);
      }
      if (HasOptionalBytes) {
        output.WriteRawTag(122);
        output.WriteBytes(OptionalBytes);
      }
      if (optionalNestedMessage_ != null) {
        output.WriteRawTag(146, 1);
        output.WriteMessage(OptionalNestedMessage);
      }
      if (optionalForeignMessage_ != null) {
        output.WriteRawTag(154, 1);
        output.WriteMessage(OptionalForeignMessage);
      }
      if (HasOptionalNestedEnum) {
        output.WriteRawTag(168, 1);
        output.WriteEnum((int) OptionalNestedEnum);
      }
      if (HasOptionalForeignEnum) {
        output.WriteRawTag(176, 1);
        output.WriteEnum((int) OptionalForeignEnum);
      }
      if (HasOptionalStringPiece) {
        output.WriteRawTag(194, 1);
        output.WriteString(OptionalStringPiece);
      }
      if (HasOptionalCord) {
        output.WriteRawTag(202, 1);
        output.WriteString(OptionalCord);
      }
      if (recursiveMessage_ != null) {
        output.WriteRawTag(218, 1);
        output.WriteMessage(RecursiveMessage);
      }
      repeatedInt32_.WriteTo(ref output, _repeated_repeatedInt32_codec);
      repeatedInt64_.WriteTo(ref output, _repeated_repeatedInt64_codec);
      repeatedUint32_.WriteTo(ref output, _repeated_repeatedUint32_codec);
      repeatedUint64_.WriteTo(ref output, _repeated_repeatedUint64_codec);
      repeatedSint32_.WriteTo(ref output, _repeated_repeatedSint32_codec);
      repeatedSint64_.WriteTo(ref output, _repeated_repeatedSint64_codec);
      repeatedFixed32_.WriteTo(ref output, _repeated_repeatedFixed32_codec);
      repeatedFixed64_.WriteTo(ref output, _repeated_repeatedFixed64_codec);
      repeatedSfixed32_.WriteTo(ref output, _repeated_repeatedSfixed32_codec);
      repeatedSfixed64_.WriteTo(ref output, _repeated_repeatedSfixed64_codec);
      repeatedFloat_.WriteTo(ref output, _repeated_repeatedFloat_codec);
      repeatedDouble_.WriteTo(ref output, _repeated_repeatedDouble_codec);
      repeatedBool_.WriteTo(ref output, _repeated_repeatedBool_codec);
      repeatedString_.WriteTo(ref output, _repeated_repeatedString_codec);
      repeatedBytes_.WriteTo(ref output, _repeated_repeatedBytes_codec);
      repeatedNestedMessage_.WriteTo(ref output, _repeated_repeatedNestedMessage_codec);
      repeatedForeignMessage_.WriteTo(ref output, _repeated_repeatedForeignMessage_codec);
      repeatedNestedEnum_.WriteTo(ref output, _repeated_repeatedNestedEnum_codec);
      repeatedForeignEnum_.WriteTo(ref output, _repeated_repeatedForeignEnum_codec);
      repeatedStringPiece_.WriteTo(ref output, _repeated_repeatedStringPiece_codec);
      repeatedCord_.WriteTo(ref output, _repeated_repeatedCord_codec);
      mapInt32Int32_.WriteTo(ref output, _map_mapInt32Int32_codec);
      mapInt64Int64_.WriteTo(ref output, _map_mapInt64Int64_codec);
      mapUint32Uint32_.WriteTo(ref output, _map_mapUint32Uint32_codec);
      mapUint64Uint64_.WriteTo(ref output, _map_mapUint64Uint64_codec);
      mapSint32Sint32_.WriteTo(ref output, _map_mapSint32Sint32_codec);
      mapSint64Sint64_.WriteTo(ref output, _map_mapSint64Sint64_codec);
      mapFixed32Fixed32_.WriteTo(ref output, _map_mapFixed32Fixed32_codec);
      mapFixed64Fixed64_.WriteTo(ref output, _map_mapFixed64Fixed64_codec);
      mapSfixed32Sfixed32_.WriteTo(ref output, _map_mapSfixed32Sfixed32_codec);
      mapSfixed64Sfixed64_.WriteTo(ref output, _map_mapSfixed64Sfixed64_codec);
      mapInt32Float_.WriteTo(ref output, _map_mapInt32Float_codec);
      mapInt32Double_.WriteTo(ref output, _map_mapInt32Double_codec);
      mapBoolBool_.WriteTo(ref output, _map_mapBoolBool_codec);
      mapStringString_.WriteTo(ref output, _map_mapStringString_codec);
      mapStringBytes_.WriteTo(ref output, _map_mapStringBytes_codec);
      mapStringNestedMessage_.WriteTo(ref output, _map_mapStringNestedMessage_codec);
      mapStringForeignMessage_.WriteTo(ref output, _map_mapStringForeignMessage_codec);
      mapStringNestedEnum_.WriteTo(ref output, _map_mapStringNestedEnum_codec);
      mapStringForeignEnum_.WriteTo(ref output, _map_mapStringForeignEnum_codec);
      packedInt32_.WriteTo(ref output, _repeated_packedInt32_codec);
      packedInt64_.WriteTo(ref output, _repeated_packedInt64_codec);
      packedUint32_.WriteTo(ref output, _repeated_packedUint32_codec);
      packedUint64_.WriteTo(ref output, _repeated_packedUint64_codec);
      packedSint32_.WriteTo(ref output, _repeated_packedSint32_codec);
      packedSint64_.WriteTo(ref output, _repeated_packedSint64_codec);
      packedFixed32_.WriteTo(ref output, _repeated_packedFixed32_codec);
      packedFixed64_.WriteTo(ref output, _repeated_packedFixed64_codec);
      packedSfixed32_.WriteTo(ref output, _repeated_packedSfixed32_codec);
      packedSfixed64_.WriteTo(ref output, _repeated_packedSfixed64_codec);
      packedFloat_.WriteTo(ref output, _repeated_packedFloat_codec);
      packedDouble_.WriteTo(ref output, _repeated_packedDouble_codec);
      packedBool_.WriteTo(ref output, _repeated_packedBool_codec);
      packedNestedEnum_.WriteTo(ref output, _repeated_packedNestedEnum_codec);
      unpackedInt32_.WriteTo(ref output, _repeated_unpackedInt32_codec);
      unpackedInt64_.WriteTo(ref output, _repeated_unpackedInt64_codec);
      unpackedUint32_.WriteTo(ref output, _repeated_unpackedUint32_codec);
      unpackedUint64_.WriteTo(ref output, _repeated_unpackedUint64_codec);
      unpackedSint32_.WriteTo(ref output, _repeated_unpackedSint32_codec);
      unpackedSint64_.WriteTo(ref output, _repeated_unpackedSint64_codec);
      unpackedFixed32_.WriteTo(ref output, _repeated_unpackedFixed32_codec);
      unpackedFixed64_.WriteTo(ref output, _repeated_unpackedFixed64_codec);
      unpackedSfixed32_.WriteTo(ref output, _repeated_unpackedSfixed32_codec);
      unpackedSfixed64_.WriteTo(ref output, _repeated_unpackedSfixed64_codec);
      unpackedFloat_.WriteTo(ref output, _repeated_unpackedFloat_codec);
      unpackedDouble_.WriteTo(ref output, _repeated_unpackedDouble_codec);
      unpackedBool_.WriteTo(ref output, _repeated_unpackedBool_codec);
      unpackedNestedEnum_.WriteTo(ref output, _repeated_unpackedNestedEnum_codec);
      if (HasOneofUint32) {
        output.WriteRawTag(248, 6);
        output.WriteUInt32(OneofUint32);
      }
      if (oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage) {
        output.WriteRawTag(130, 7);
        output.WriteMessage(OneofNestedMessage);
      }
      if (HasOneofString) {
        output.WriteRawTag(138, 7);
        output.WriteString(OneofString);
      }
      if (HasOneofBytes) {
        output.WriteRawTag(146, 7);
        output.WriteBytes(OneofBytes);
      }
      if (HasOneofBool) {
        output.WriteRawTag(152, 7);
        output.WriteBool(OneofBool);
      }
      if (HasOneofUint64) {
        output.WriteRawTag(160, 7);
        output.WriteUInt64(OneofUint64);
      }
      if (HasOneofFloat) {
        output.WriteRawTag(173, 7);
        output.WriteFloat(OneofFloat);
      }
      if (HasOneofDouble) {
        output.WriteRawTag(177, 7);
        output.WriteDouble(OneofDouble);
      }
      if (HasOneofEnum) {
        output.WriteRawTag(184, 7);
        output.WriteEnum((int) OneofEnum);
      }
      if (HasData) {
        output.WriteRawTag(203, 12);
        output.WriteGroup(Data);
        output.WriteRawTag(204, 12);
      }
      if (HasDefaultInt32) {
        output.WriteRawTag(136, 15);
        output.WriteInt32(DefaultInt32);
      }
      if (HasDefaultInt64) {
        output.WriteRawTag(144, 15);
        output.WriteInt64(DefaultInt64);
      }
      if (HasDefaultUint32) {
        output.WriteRawTag(152, 15);
        output.WriteUInt32(DefaultUint32);
      }
      if (HasDefaultUint64) {
        output.WriteRawTag(160, 15);
        output.WriteUInt64(DefaultUint64);
      }
      if (HasDefaultSint32) {
        output.WriteRawTag(168, 15);
        output.WriteSInt32(DefaultSint32);
      }
      if (HasDefaultSint64) {
        output.WriteRawTag(176, 15);
        output.WriteSInt64(DefaultSint64);
      }
      if (HasDefaultFixed32) {
        output.WriteRawTag(189, 15);
        output.WriteFixed32(DefaultFixed32);
      }
      if (HasDefaultFixed64) {
        output.WriteRawTag(193, 15);
        output.WriteFixed64(DefaultFixed64);
      }
      if (HasDefaultSfixed32) {
        output.WriteRawTag(205, 15);
        output.WriteSFixed32(DefaultSfixed32);
      }
      if (HasDefaultSfixed64) {
        output.WriteRawTag(209, 15);
        output.WriteSFixed64(DefaultSfixed64);
      }
      if (HasDefaultFloat) {
        output.WriteRawTag(221, 15);
        output.WriteFloat(DefaultFloat);
      }
      if (HasDefaultDouble) {
        output.WriteRawTag(225, 15);
        output.WriteDouble(DefaultDouble);
      }
      if (HasDefaultBool) {
        output.WriteRawTag(232, 15);
        output.WriteBool(DefaultBool);
      }
      if (HasDefaultString) {
        output.WriteRawTag(242, 15);
        output.WriteString(DefaultString);
      }
      if (HasDefaultBytes) {
        output.WriteRawTag(250, 15);
        output.WriteBytes(DefaultBytes);
      }
      if (HasFieldname1) {
        output.WriteRawTag(136, 25);
        output.WriteInt32(Fieldname1);
      }
      if (HasFieldName2) {
        output.WriteRawTag(144, 25);
        output.WriteInt32(FieldName2);
      }
      if (HasFieldName3) {
        output.WriteRawTag(152, 25);
        output.WriteInt32(FieldName3);
      }
      if (HasFieldName4) {
        output.WriteRawTag(160, 25);
        output.WriteInt32(FieldName4);
      }
      if (HasField0Name5) {
        output.WriteRawTag(168, 25);
        output.WriteInt32(Field0Name5);
      }
      if (HasField0Name6) {
        output.WriteRawTag(176, 25);
        output.WriteInt32(Field0Name6);
      }
      if (HasFieldName7) {
        output.WriteRawTag(184, 25);
        output.WriteInt32(FieldName7);
      }
      if (HasFieldName8) {
        output.WriteRawTag(192, 25);
        output.WriteInt32(FieldName8);
      }
      if (HasFieldName9) {
        output.WriteRawTag(200, 25);
        output.WriteInt32(FieldName9);
      }
      if (HasFieldName10) {
        output.WriteRawTag(208, 25);
        output.WriteInt32(FieldName10);
      }
      if (HasFIELDNAME11) {
        output.WriteRawTag(216, 25);
        output.WriteInt32(FIELDNAME11);
      }
      if (HasFIELDName12) {
        output.WriteRawTag(224, 25);
        output.WriteInt32(FIELDName12);
      }
      if (HasFieldName13) {
        output.WriteRawTag(232, 25);
        output.WriteInt32(FieldName13);
      }
      if (HasFieldName14) {
        output.WriteRawTag(240, 25);
        output.WriteInt32(FieldName14);
      }
      if (HasFieldName15) {
        output.WriteRawTag(248, 25);
        output.WriteInt32(FieldName15);
      }
      if (HasFieldName16) {
        output.WriteRawTag(128, 26);
        output.WriteInt32(FieldName16);
      }
      if (HasFieldName17) {
        output.WriteRawTag(136, 26);
        output.WriteInt32(FieldName17);
      }
      if (HasFieldName18) {
        output.WriteRawTag(144, 26);
        output.WriteInt32(FieldName18);
      }
      if (_extensions != null) {
        _extensions.WriteTo(ref output);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasOptionalInt32) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(OptionalInt32);
      }
      if (HasOptionalInt64) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(OptionalInt64);
      }
      if (HasOptionalUint32) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(OptionalUint32);
      }
      if (HasOptionalUint64) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(OptionalUint64);
      }
      if (HasOptionalSint32) {
        size += 1 + pb::CodedOutputStream.ComputeSInt32Size(OptionalSint32);
      }
      if (HasOptionalSint64) {
        size += 1 + pb::CodedOutputStream.ComputeSInt64Size(OptionalSint64);
      }
      if (HasOptionalFixed32) {
        size += 1 + 4;
      }
      if (HasOptionalFixed64) {
        size += 1 + 8;
      }
      if (HasOptionalSfixed32) {
        size += 1 + 4;
      }
      if (HasOptionalSfixed64) {
        size += 1 + 8;
      }
      if (HasOptionalFloat) {
        size += 1 + 4;
      }
      if (HasOptionalDouble) {
        size += 1 + 8;
      }
      if (HasOptionalBool) {
        size += 1 + 1;
      }
      if (HasOptionalString) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(OptionalString);
      }
      if (HasOptionalBytes) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(OptionalBytes);
      }
      if (optionalNestedMessage_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(OptionalNestedMessage);
      }
      if (optionalForeignMessage_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(OptionalForeignMessage);
      }
      if (HasOptionalNestedEnum) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) OptionalNestedEnum);
      }
      if (HasOptionalForeignEnum) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) OptionalForeignEnum);
      }
      if (HasOptionalStringPiece) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(OptionalStringPiece);
      }
      if (HasOptionalCord) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(OptionalCord);
      }
      if (recursiveMessage_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(RecursiveMessage);
      }
      size += repeatedInt32_.CalculateSize(_repeated_repeatedInt32_codec);
      size += repeatedInt64_.CalculateSize(_repeated_repeatedInt64_codec);
      size += repeatedUint32_.CalculateSize(_repeated_repeatedUint32_codec);
      size += repeatedUint64_.CalculateSize(_repeated_repeatedUint64_codec);
      size += repeatedSint32_.CalculateSize(_repeated_repeatedSint32_codec);
      size += repeatedSint64_.CalculateSize(_repeated_repeatedSint64_codec);
      size += repeatedFixed32_.CalculateSize(_repeated_repeatedFixed32_codec);
      size += repeatedFixed64_.CalculateSize(_repeated_repeatedFixed64_codec);
      size += repeatedSfixed32_.CalculateSize(_repeated_repeatedSfixed32_codec);
      size += repeatedSfixed64_.CalculateSize(_repeated_repeatedSfixed64_codec);
      size += repeatedFloat_.CalculateSize(_repeated_repeatedFloat_codec);
      size += repeatedDouble_.CalculateSize(_repeated_repeatedDouble_codec);
      size += repeatedBool_.CalculateSize(_repeated_repeatedBool_codec);
      size += repeatedString_.CalculateSize(_repeated_repeatedString_codec);
      size += repeatedBytes_.CalculateSize(_repeated_repeatedBytes_codec);
      size += repeatedNestedMessage_.CalculateSize(_repeated_repeatedNestedMessage_codec);
      size += repeatedForeignMessage_.CalculateSize(_repeated_repeatedForeignMessage_codec);
      size += repeatedNestedEnum_.CalculateSize(_repeated_repeatedNestedEnum_codec);
      size += repeatedForeignEnum_.CalculateSize(_repeated_repeatedForeignEnum_codec);
      size += repeatedStringPiece_.CalculateSize(_repeated_repeatedStringPiece_codec);
      size += repeatedCord_.CalculateSize(_repeated_repeatedCord_codec);
      size += packedInt32_.CalculateSize(_repeated_packedInt32_codec);
      size += packedInt64_.CalculateSize(_repeated_packedInt64_codec);
      size += packedUint32_.CalculateSize(_repeated_packedUint32_codec);
      size += packedUint64_.CalculateSize(_repeated_packedUint64_codec);
      size += packedSint32_.CalculateSize(_repeated_packedSint32_codec);
      size += packedSint64_.CalculateSize(_repeated_packedSint64_codec);
      size += packedFixed32_.CalculateSize(_repeated_packedFixed32_codec);
      size += packedFixed64_.CalculateSize(_repeated_packedFixed64_codec);
      size += packedSfixed32_.CalculateSize(_repeated_packedSfixed32_codec);
      size += packedSfixed64_.CalculateSize(_repeated_packedSfixed64_codec);
      size += packedFloat_.CalculateSize(_repeated_packedFloat_codec);
      size += packedDouble_.CalculateSize(_repeated_packedDouble_codec);
      size += packedBool_.CalculateSize(_repeated_packedBool_codec);
      size += packedNestedEnum_.CalculateSize(_repeated_packedNestedEnum_codec);
      size += unpackedInt32_.CalculateSize(_repeated_unpackedInt32_codec);
      size += unpackedInt64_.CalculateSize(_repeated_unpackedInt64_codec);
      size += unpackedUint32_.CalculateSize(_repeated_unpackedUint32_codec);
      size += unpackedUint64_.CalculateSize(_repeated_unpackedUint64_codec);
      size += unpackedSint32_.CalculateSize(_repeated_unpackedSint32_codec);
      size += unpackedSint64_.CalculateSize(_repeated_unpackedSint64_codec);
      size += unpackedFixed32_.CalculateSize(_repeated_unpackedFixed32_codec);
      size += unpackedFixed64_.CalculateSize(_repeated_unpackedFixed64_codec);
      size += unpackedSfixed32_.CalculateSize(_repeated_unpackedSfixed32_codec);
      size += unpackedSfixed64_.CalculateSize(_repeated_unpackedSfixed64_codec);
      size += unpackedFloat_.CalculateSize(_repeated_unpackedFloat_codec);
      size += unpackedDouble_.CalculateSize(_repeated_unpackedDouble_codec);
      size += unpackedBool_.CalculateSize(_repeated_unpackedBool_codec);
      size += unpackedNestedEnum_.CalculateSize(_repeated_unpackedNestedEnum_codec);
      size += mapInt32Int32_.CalculateSize(_map_mapInt32Int32_codec);
      size += mapInt64Int64_.CalculateSize(_map_mapInt64Int64_codec);
      size += mapUint32Uint32_.CalculateSize(_map_mapUint32Uint32_codec);
      size += mapUint64Uint64_.CalculateSize(_map_mapUint64Uint64_codec);
      size += mapSint32Sint32_.CalculateSize(_map_mapSint32Sint32_codec);
      size += mapSint64Sint64_.CalculateSize(_map_mapSint64Sint64_codec);
      size += mapFixed32Fixed32_.CalculateSize(_map_mapFixed32Fixed32_codec);
      size += mapFixed64Fixed64_.CalculateSize(_map_mapFixed64Fixed64_codec);
      size += mapSfixed32Sfixed32_.CalculateSize(_map_mapSfixed32Sfixed32_codec);
      size += mapSfixed64Sfixed64_.CalculateSize(_map_mapSfixed64Sfixed64_codec);
      size += mapInt32Float_.CalculateSize(_map_mapInt32Float_codec);
      size += mapInt32Double_.CalculateSize(_map_mapInt32Double_codec);
      size += mapBoolBool_.CalculateSize(_map_mapBoolBool_codec);
      size += mapStringString_.CalculateSize(_map_mapStringString_codec);
      size += mapStringBytes_.CalculateSize(_map_mapStringBytes_codec);
      size += mapStringNestedMessage_.CalculateSize(_map_mapStringNestedMessage_codec);
      size += mapStringForeignMessage_.CalculateSize(_map_mapStringForeignMessage_codec);
      size += mapStringNestedEnum_.CalculateSize(_map_mapStringNestedEnum_codec);
      size += mapStringForeignEnum_.CalculateSize(_map_mapStringForeignEnum_codec);
      if (HasOneofUint32) {
        size += 2 + pb::CodedOutputStream.ComputeUInt32Size(OneofUint32);
      }
      if (oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(OneofNestedMessage);
      }
      if (HasOneofString) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(OneofString);
      }
      if (HasOneofBytes) {
        size += 2 + pb::CodedOutputStream.ComputeBytesSize(OneofBytes);
      }
      if (HasOneofBool) {
        size += 2 + 1;
      }
      if (HasOneofUint64) {
        size += 2 + pb::CodedOutputStream.ComputeUInt64Size(OneofUint64);
      }
      if (HasOneofFloat) {
        size += 2 + 4;
      }
      if (HasOneofDouble) {
        size += 2 + 8;
      }
      if (HasOneofEnum) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) OneofEnum);
      }
      if (HasData) {
        size += 4 + pb::CodedOutputStream.ComputeGroupSize(Data);
      }
      if (HasDefaultInt32) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(DefaultInt32);
      }
      if (HasDefaultInt64) {
        size += 2 + pb::CodedOutputStream.ComputeInt64Size(DefaultInt64);
      }
      if (HasDefaultUint32) {
        size += 2 + pb::CodedOutputStream.ComputeUInt32Size(DefaultUint32);
      }
      if (HasDefaultUint64) {
        size += 2 + pb::CodedOutputStream.ComputeUInt64Size(DefaultUint64);
      }
      if (HasDefaultSint32) {
        size += 2 + pb::CodedOutputStream.ComputeSInt32Size(DefaultSint32);
      }
      if (HasDefaultSint64) {
        size += 2 + pb::CodedOutputStream.ComputeSInt64Size(DefaultSint64);
      }
      if (HasDefaultFixed32) {
        size += 2 + 4;
      }
      if (HasDefaultFixed64) {
        size += 2 + 8;
      }
      if (HasDefaultSfixed32) {
        size += 2 + 4;
      }
      if (HasDefaultSfixed64) {
        size += 2 + 8;
      }
      if (HasDefaultFloat) {
        size += 2 + 4;
      }
      if (HasDefaultDouble) {
        size += 2 + 8;
      }
      if (HasDefaultBool) {
        size += 2 + 1;
      }
      if (HasDefaultString) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(DefaultString);
      }
      if (HasDefaultBytes) {
        size += 2 + pb::CodedOutputStream.ComputeBytesSize(DefaultBytes);
      }
      if (HasFieldname1) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(Fieldname1);
      }
      if (HasFieldName2) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName2);
      }
      if (HasFieldName3) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName3);
      }
      if (HasFieldName4) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName4);
      }
      if (HasField0Name5) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(Field0Name5);
      }
      if (HasField0Name6) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(Field0Name6);
      }
      if (HasFieldName7) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName7);
      }
      if (HasFieldName8) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName8);
      }
      if (HasFieldName9) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName9);
      }
      if (HasFieldName10) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName10);
      }
      if (HasFIELDNAME11) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FIELDNAME11);
      }
      if (HasFIELDName12) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FIELDName12);
      }
      if (HasFieldName13) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName13);
      }
      if (HasFieldName14) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName14);
      }
      if (HasFieldName15) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName15);
      }
      if (HasFieldName16) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName16);
      }
      if (HasFieldName17) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName17);
      }
      if (HasFieldName18) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName18);
      }
      if (_extensions != null) {
        size += _extensions.CalculateSize();
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TestAllTypesProto2 other) {
      if (other == null) {
        return;
      }
      if (other.HasOptionalInt32) {
        OptionalInt32 = other.OptionalInt32;
      }
      if (other.HasOptionalInt64) {
        OptionalInt64 = other.OptionalInt64;
      }
      if (other.HasOptionalUint32) {
        OptionalUint32 = other.OptionalUint32;
      }
      if (other.HasOptionalUint64) {
        OptionalUint64 = other.OptionalUint64;
      }
      if (other.HasOptionalSint32) {
        OptionalSint32 = other.OptionalSint32;
      }
      if (other.HasOptionalSint64) {
        OptionalSint64 = other.OptionalSint64;
      }
      if (other.HasOptionalFixed32) {
        OptionalFixed32 = other.OptionalFixed32;
      }
      if (other.HasOptionalFixed64) {
        OptionalFixed64 = other.OptionalFixed64;
      }
      if (other.HasOptionalSfixed32) {
        OptionalSfixed32 = other.OptionalSfixed32;
      }
      if (other.HasOptionalSfixed64) {
        OptionalSfixed64 = other.OptionalSfixed64;
      }
      if (other.HasOptionalFloat) {
        OptionalFloat = other.OptionalFloat;
      }
      if (other.HasOptionalDouble) {
        OptionalDouble = other.OptionalDouble;
      }
      if (other.HasOptionalBool) {
        OptionalBool = other.OptionalBool;
      }
      if (other.HasOptionalString) {
        OptionalString = other.OptionalString;
      }
      if (other.HasOptionalBytes) {
        OptionalBytes = other.OptionalBytes;
      }
      if (other.optionalNestedMessage_ != null) {
        if (optionalNestedMessage_ == null) {
          OptionalNestedMessage = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage();
        }
        OptionalNestedMessage.MergeFrom(other.OptionalNestedMessage);
      }
      if (other.optionalForeignMessage_ != null) {
        if (optionalForeignMessage_ == null) {
          OptionalForeignMessage = new global::ProtobufTestMessages.Proto2.ForeignMessageProto2();
        }
        OptionalForeignMessage.MergeFrom(other.OptionalForeignMessage);
      }
      if (other.HasOptionalNestedEnum) {
        OptionalNestedEnum = other.OptionalNestedEnum;
      }
      if (other.HasOptionalForeignEnum) {
        OptionalForeignEnum = other.OptionalForeignEnum;
      }
      if (other.HasOptionalStringPiece) {
        OptionalStringPiece = other.OptionalStringPiece;
      }
      if (other.HasOptionalCord) {
        OptionalCord = other.OptionalCord;
      }
      if (other.recursiveMessage_ != null) {
        if (recursiveMessage_ == null) {
          RecursiveMessage = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2();
        }
        RecursiveMessage.MergeFrom(other.RecursiveMessage);
      }
      repeatedInt32_.Add(other.repeatedInt32_);
      repeatedInt64_.Add(other.repeatedInt64_);
      repeatedUint32_.Add(other.repeatedUint32_);
      repeatedUint64_.Add(other.repeatedUint64_);
      repeatedSint32_.Add(other.repeatedSint32_);
      repeatedSint64_.Add(other.repeatedSint64_);
      repeatedFixed32_.Add(other.repeatedFixed32_);
      repeatedFixed64_.Add(other.repeatedFixed64_);
      repeatedSfixed32_.Add(other.repeatedSfixed32_);
      repeatedSfixed64_.Add(other.repeatedSfixed64_);
      repeatedFloat_.Add(other.repeatedFloat_);
      repeatedDouble_.Add(other.repeatedDouble_);
      repeatedBool_.Add(other.repeatedBool_);
      repeatedString_.Add(other.repeatedString_);
      repeatedBytes_.Add(other.repeatedBytes_);
      repeatedNestedMessage_.Add(other.repeatedNestedMessage_);
      repeatedForeignMessage_.Add(other.repeatedForeignMessage_);
      repeatedNestedEnum_.Add(other.repeatedNestedEnum_);
      repeatedForeignEnum_.Add(other.repeatedForeignEnum_);
      repeatedStringPiece_.Add(other.repeatedStringPiece_);
      repeatedCord_.Add(other.repeatedCord_);
      packedInt32_.Add(other.packedInt32_);
      packedInt64_.Add(other.packedInt64_);
      packedUint32_.Add(other.packedUint32_);
      packedUint64_.Add(other.packedUint64_);
      packedSint32_.Add(other.packedSint32_);
      packedSint64_.Add(other.packedSint64_);
      packedFixed32_.Add(other.packedFixed32_);
      packedFixed64_.Add(other.packedFixed64_);
      packedSfixed32_.Add(other.packedSfixed32_);
      packedSfixed64_.Add(other.packedSfixed64_);
      packedFloat_.Add(other.packedFloat_);
      packedDouble_.Add(other.packedDouble_);
      packedBool_.Add(other.packedBool_);
      packedNestedEnum_.Add(other.packedNestedEnum_);
      unpackedInt32_.Add(other.unpackedInt32_);
      unpackedInt64_.Add(other.unpackedInt64_);
      unpackedUint32_.Add(other.unpackedUint32_);
      unpackedUint64_.Add(other.unpackedUint64_);
      unpackedSint32_.Add(other.unpackedSint32_);
      unpackedSint64_.Add(other.unpackedSint64_);
      unpackedFixed32_.Add(other.unpackedFixed32_);
      unpackedFixed64_.Add(other.unpackedFixed64_);
      unpackedSfixed32_.Add(other.unpackedSfixed32_);
      unpackedSfixed64_.Add(other.unpackedSfixed64_);
      unpackedFloat_.Add(other.unpackedFloat_);
      unpackedDouble_.Add(other.unpackedDouble_);
      unpackedBool_.Add(other.unpackedBool_);
      unpackedNestedEnum_.Add(other.unpackedNestedEnum_);
      mapInt32Int32_.MergeFrom(other.mapInt32Int32_);
      mapInt64Int64_.MergeFrom(other.mapInt64Int64_);
      mapUint32Uint32_.MergeFrom(other.mapUint32Uint32_);
      mapUint64Uint64_.MergeFrom(other.mapUint64Uint64_);
      mapSint32Sint32_.MergeFrom(other.mapSint32Sint32_);
      mapSint64Sint64_.MergeFrom(other.mapSint64Sint64_);
      mapFixed32Fixed32_.MergeFrom(other.mapFixed32Fixed32_);
      mapFixed64Fixed64_.MergeFrom(other.mapFixed64Fixed64_);
      mapSfixed32Sfixed32_.MergeFrom(other.mapSfixed32Sfixed32_);
      mapSfixed64Sfixed64_.MergeFrom(other.mapSfixed64Sfixed64_);
      mapInt32Float_.MergeFrom(other.mapInt32Float_);
      mapInt32Double_.MergeFrom(other.mapInt32Double_);
      mapBoolBool_.MergeFrom(other.mapBoolBool_);
      mapStringString_.MergeFrom(other.mapStringString_);
      mapStringBytes_.MergeFrom(other.mapStringBytes_);
      mapStringNestedMessage_.MergeFrom(other.mapStringNestedMessage_);
      mapStringForeignMessage_.MergeFrom(other.mapStringForeignMessage_);
      mapStringNestedEnum_.MergeFrom(other.mapStringNestedEnum_);
      mapStringForeignEnum_.MergeFrom(other.mapStringForeignEnum_);
      if (other.HasData) {
        if (!HasData) {
          Data = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.Data();
        }
        Data.MergeFrom(other.Data);
      }
      if (other.HasDefaultInt32) {
        DefaultInt32 = other.DefaultInt32;
      }
      if (other.HasDefaultInt64) {
        DefaultInt64 = other.DefaultInt64;
      }
      if (other.HasDefaultUint32) {
        DefaultUint32 = other.DefaultUint32;
      }
      if (other.HasDefaultUint64) {
        DefaultUint64 = other.DefaultUint64;
      }
      if (other.HasDefaultSint32) {
        DefaultSint32 = other.DefaultSint32;
      }
      if (other.HasDefaultSint64) {
        DefaultSint64 = other.DefaultSint64;
      }
      if (other.HasDefaultFixed32) {
        DefaultFixed32 = other.DefaultFixed32;
      }
      if (other.HasDefaultFixed64) {
        DefaultFixed64 = other.DefaultFixed64;
      }
      if (other.HasDefaultSfixed32) {
        DefaultSfixed32 = other.DefaultSfixed32;
      }
      if (other.HasDefaultSfixed64) {
        DefaultSfixed64 = other.DefaultSfixed64;
      }
      if (other.HasDefaultFloat) {
        DefaultFloat = other.DefaultFloat;
      }
      if (other.HasDefaultDouble) {
        DefaultDouble = other.DefaultDouble;
      }
      if (other.HasDefaultBool) {
        DefaultBool = other.DefaultBool;
      }
      if (other.HasDefaultString) {
        DefaultString = other.DefaultString;
      }
      if (other.HasDefaultBytes) {
        DefaultBytes = other.DefaultBytes;
      }
      if (other.HasFieldname1) {
        Fieldname1 = other.Fieldname1;
      }
      if (other.HasFieldName2) {
        FieldName2 = other.FieldName2;
      }
      if (other.HasFieldName3) {
        FieldName3 = other.FieldName3;
      }
      if (other.HasFieldName4) {
        FieldName4 = other.FieldName4;
      }
      if (other.HasField0Name5) {
        Field0Name5 = other.Field0Name5;
      }
      if (other.HasField0Name6) {
        Field0Name6 = other.Field0Name6;
      }
      if (other.HasFieldName7) {
        FieldName7 = other.FieldName7;
      }
      if (other.HasFieldName8) {
        FieldName8 = other.FieldName8;
      }
      if (other.HasFieldName9) {
        FieldName9 = other.FieldName9;
      }
      if (other.HasFieldName10) {
        FieldName10 = other.FieldName10;
      }
      if (other.HasFIELDNAME11) {
        FIELDNAME11 = other.FIELDNAME11;
      }
      if (other.HasFIELDName12) {
        FIELDName12 = other.FIELDName12;
      }
      if (other.HasFieldName13) {
        FieldName13 = other.FieldName13;
      }
      if (other.HasFieldName14) {
        FieldName14 = other.FieldName14;
      }
      if (other.HasFieldName15) {
        FieldName15 = other.FieldName15;
      }
      if (other.HasFieldName16) {
        FieldName16 = other.FieldName16;
      }
      if (other.HasFieldName17) {
        FieldName17 = other.FieldName17;
      }
      if (other.HasFieldName18) {
        FieldName18 = other.FieldName18;
      }
      switch (other.OneofFieldCase) {
        case OneofFieldOneofCase.OneofUint32:
          OneofUint32 = other.OneofUint32;
          break;
        case OneofFieldOneofCase.OneofNestedMessage:
          if (OneofNestedMessage == null) {
            OneofNestedMessage = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage();
          }
          OneofNestedMessage.MergeFrom(other.OneofNestedMessage);
          break;
        case OneofFieldOneofCase.OneofString:
          OneofString = other.OneofString;
          break;
        case OneofFieldOneofCase.OneofBytes:
          OneofBytes = other.OneofBytes;
          break;
        case OneofFieldOneofCase.OneofBool:
          OneofBool = other.OneofBool;
          break;
        case OneofFieldOneofCase.OneofUint64:
          OneofUint64 = other.OneofUint64;
          break;
        case OneofFieldOneofCase.OneofFloat:
          OneofFloat = other.OneofFloat;
          break;
        case OneofFieldOneofCase.OneofDouble:
          OneofDouble = other.OneofDouble;
          break;
        case OneofFieldOneofCase.OneofEnum:
          OneofEnum = other.OneofEnum;
          break;
      }

      pb::ExtensionSet.MergeFrom(ref _extensions, other._extensions);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            if (!pb::ExtensionSet.TryMergeFieldFrom(ref _extensions, input)) {
              _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            }
            break;
          case 8: {
            OptionalInt32 = input.ReadInt32();
            break;
          }
          case 16: {
            OptionalInt64 = input.ReadInt64();
            break;
          }
          case 24: {
            OptionalUint32 = input.ReadUInt32();
            break;
          }
          case 32: {
            OptionalUint64 = input.ReadUInt64();
            break;
          }
          case 40: {
            OptionalSint32 = input.ReadSInt32();
            break;
          }
          case 48: {
            OptionalSint64 = input.ReadSInt64();
            break;
          }
          case 61: {
            OptionalFixed32 = input.ReadFixed32();
            break;
          }
          case 65: {
            OptionalFixed64 = input.ReadFixed64();
            break;
          }
          case 77: {
            OptionalSfixed32 = input.ReadSFixed32();
            break;
          }
          case 81: {
            OptionalSfixed64 = input.ReadSFixed64();
            break;
          }
          case 93: {
            OptionalFloat = input.ReadFloat();
            break;
          }
          case 97: {
            OptionalDouble = input.ReadDouble();
            break;
          }
          case 104: {
            OptionalBool = input.ReadBool();
            break;
          }
          case 114: {
            OptionalString = input.ReadString();
            break;
          }
          case 122: {
            OptionalBytes = input.ReadBytes();
            break;
          }
          case 146: {
            if (optionalNestedMessage_ == null) {
              OptionalNestedMessage = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage();
            }
            input.ReadMessage(OptionalNestedMessage);
            break;
          }
          case 154: {
            if (optionalForeignMessage_ == null) {
              OptionalForeignMessage = new global::ProtobufTestMessages.Proto2.ForeignMessageProto2();
            }
            input.ReadMessage(OptionalForeignMessage);
            break;
          }
          case 168: {
            OptionalNestedEnum = (global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum) input.ReadEnum();
            break;
          }
          case 176: {
            OptionalForeignEnum = (global::ProtobufTestMessages.Proto2.ForeignEnumProto2) input.ReadEnum();
            break;
          }
          case 194: {
            OptionalStringPiece = input.ReadString();
            break;
          }
          case 202: {
            OptionalCord = input.ReadString();
            break;
          }
          case 218: {
            if (recursiveMessage_ == null) {
              RecursiveMessage = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2();
            }
            input.ReadMessage(RecursiveMessage);
            break;
          }
          case 250:
          case 248: {
            repeatedInt32_.AddEntriesFrom(input, _repeated_repeatedInt32_codec);
            break;
          }
          case 258:
          case 256: {
            repeatedInt64_.AddEntriesFrom(input, _repeated_repeatedInt64_codec);
            break;
          }
          case 266:
          case 264: {
            repeatedUint32_.AddEntriesFrom(input, _repeated_repeatedUint32_codec);
            break;
          }
          case 274:
          case 272: {
            repeatedUint64_.AddEntriesFrom(input, _repeated_repeatedUint64_codec);
            break;
          }
          case 282:
          case 280: {
            repeatedSint32_.AddEntriesFrom(input, _repeated_repeatedSint32_codec);
            break;
          }
          case 290:
          case 288: {
            repeatedSint64_.AddEntriesFrom(input, _repeated_repeatedSint64_codec);
            break;
          }
          case 298:
          case 301: {
            repeatedFixed32_.AddEntriesFrom(input, _repeated_repeatedFixed32_codec);
            break;
          }
          case 306:
          case 305: {
            repeatedFixed64_.AddEntriesFrom(input, _repeated_repeatedFixed64_codec);
            break;
          }
          case 314:
          case 317: {
            repeatedSfixed32_.AddEntriesFrom(input, _repeated_repeatedSfixed32_codec);
            break;
          }
          case 322:
          case 321: {
            repeatedSfixed64_.AddEntriesFrom(input, _repeated_repeatedSfixed64_codec);
            break;
          }
          case 330:
          case 333: {
            repeatedFloat_.AddEntriesFrom(input, _repeated_repeatedFloat_codec);
            break;
          }
          case 338:
          case 337: {
            repeatedDouble_.AddEntriesFrom(input, _repeated_repeatedDouble_codec);
            break;
          }
          case 346:
          case 344: {
            repeatedBool_.AddEntriesFrom(input, _repeated_repeatedBool_codec);
            break;
          }
          case 354: {
            repeatedString_.AddEntriesFrom(input, _repeated_repeatedString_codec);
            break;
          }
          case 362: {
            repeatedBytes_.AddEntriesFrom(input, _repeated_repeatedBytes_codec);
            break;
          }
          case 386: {
            repeatedNestedMessage_.AddEntriesFrom(input, _repeated_repeatedNestedMessage_codec);
            break;
          }
          case 394: {
            repeatedForeignMessage_.AddEntriesFrom(input, _repeated_repeatedForeignMessage_codec);
            break;
          }
          case 410:
          case 408: {
            repeatedNestedEnum_.AddEntriesFrom(input, _repeated_repeatedNestedEnum_codec);
            break;
          }
          case 418:
          case 416: {
            repeatedForeignEnum_.AddEntriesFrom(input, _repeated_repeatedForeignEnum_codec);
            break;
          }
          case 434: {
            repeatedStringPiece_.AddEntriesFrom(input, _repeated_repeatedStringPiece_codec);
            break;
          }
          case 442: {
            repeatedCord_.AddEntriesFrom(input, _repeated_repeatedCord_codec);
            break;
          }
          case 450: {
            mapInt32Int32_.AddEntriesFrom(input, _map_mapInt32Int32_codec);
            break;
          }
          case 458: {
            mapInt64Int64_.AddEntriesFrom(input, _map_mapInt64Int64_codec);
            break;
          }
          case 466: {
            mapUint32Uint32_.AddEntriesFrom(input, _map_mapUint32Uint32_codec);
            break;
          }
          case 474: {
            mapUint64Uint64_.AddEntriesFrom(input, _map_mapUint64Uint64_codec);
            break;
          }
          case 482: {
            mapSint32Sint32_.AddEntriesFrom(input, _map_mapSint32Sint32_codec);
            break;
          }
          case 490: {
            mapSint64Sint64_.AddEntriesFrom(input, _map_mapSint64Sint64_codec);
            break;
          }
          case 498: {
            mapFixed32Fixed32_.AddEntriesFrom(input, _map_mapFixed32Fixed32_codec);
            break;
          }
          case 506: {
            mapFixed64Fixed64_.AddEntriesFrom(input, _map_mapFixed64Fixed64_codec);
            break;
          }
          case 514: {
            mapSfixed32Sfixed32_.AddEntriesFrom(input, _map_mapSfixed32Sfixed32_codec);
            break;
          }
          case 522: {
            mapSfixed64Sfixed64_.AddEntriesFrom(input, _map_mapSfixed64Sfixed64_codec);
            break;
          }
          case 530: {
            mapInt32Float_.AddEntriesFrom(input, _map_mapInt32Float_codec);
            break;
          }
          case 538: {
            mapInt32Double_.AddEntriesFrom(input, _map_mapInt32Double_codec);
            break;
          }
          case 546: {
            mapBoolBool_.AddEntriesFrom(input, _map_mapBoolBool_codec);
            break;
          }
          case 554: {
            mapStringString_.AddEntriesFrom(input, _map_mapStringString_codec);
            break;
          }
          case 562: {
            mapStringBytes_.AddEntriesFrom(input, _map_mapStringBytes_codec);
            break;
          }
          case 570: {
            mapStringNestedMessage_.AddEntriesFrom(input, _map_mapStringNestedMessage_codec);
            break;
          }
          case 578: {
            mapStringForeignMessage_.AddEntriesFrom(input, _map_mapStringForeignMessage_codec);
            break;
          }
          case 586: {
            mapStringNestedEnum_.AddEntriesFrom(input, _map_mapStringNestedEnum_codec);
            break;
          }
          case 594: {
            mapStringForeignEnum_.AddEntriesFrom(input, _map_mapStringForeignEnum_codec);
            break;
          }
          case 602:
          case 600: {
            packedInt32_.AddEntriesFrom(input, _repeated_packedInt32_codec);
            break;
          }
          case 610:
          case 608: {
            packedInt64_.AddEntriesFrom(input, _repeated_packedInt64_codec);
            break;
          }
          case 618:
          case 616: {
            packedUint32_.AddEntriesFrom(input, _repeated_packedUint32_codec);
            break;
          }
          case 626:
          case 624: {
            packedUint64_.AddEntriesFrom(input, _repeated_packedUint64_codec);
            break;
          }
          case 634:
          case 632: {
            packedSint32_.AddEntriesFrom(input, _repeated_packedSint32_codec);
            break;
          }
          case 642:
          case 640: {
            packedSint64_.AddEntriesFrom(input, _repeated_packedSint64_codec);
            break;
          }
          case 650:
          case 653: {
            packedFixed32_.AddEntriesFrom(input, _repeated_packedFixed32_codec);
            break;
          }
          case 658:
          case 657: {
            packedFixed64_.AddEntriesFrom(input, _repeated_packedFixed64_codec);
            break;
          }
          case 666:
          case 669: {
            packedSfixed32_.AddEntriesFrom(input, _repeated_packedSfixed32_codec);
            break;
          }
          case 674:
          case 673: {
            packedSfixed64_.AddEntriesFrom(input, _repeated_packedSfixed64_codec);
            break;
          }
          case 682:
          case 685: {
            packedFloat_.AddEntriesFrom(input, _repeated_packedFloat_codec);
            break;
          }
          case 690:
          case 689: {
            packedDouble_.AddEntriesFrom(input, _repeated_packedDouble_codec);
            break;
          }
          case 698:
          case 696: {
            packedBool_.AddEntriesFrom(input, _repeated_packedBool_codec);
            break;
          }
          case 706:
          case 704: {
            packedNestedEnum_.AddEntriesFrom(input, _repeated_packedNestedEnum_codec);
            break;
          }
          case 714:
          case 712: {
            unpackedInt32_.AddEntriesFrom(input, _repeated_unpackedInt32_codec);
            break;
          }
          case 722:
          case 720: {
            unpackedInt64_.AddEntriesFrom(input, _repeated_unpackedInt64_codec);
            break;
          }
          case 730:
          case 728: {
            unpackedUint32_.AddEntriesFrom(input, _repeated_unpackedUint32_codec);
            break;
          }
          case 738:
          case 736: {
            unpackedUint64_.AddEntriesFrom(input, _repeated_unpackedUint64_codec);
            break;
          }
          case 746:
          case 744: {
            unpackedSint32_.AddEntriesFrom(input, _repeated_unpackedSint32_codec);
            break;
          }
          case 754:
          case 752: {
            unpackedSint64_.AddEntriesFrom(input, _repeated_unpackedSint64_codec);
            break;
          }
          case 762:
          case 765: {
            unpackedFixed32_.AddEntriesFrom(input, _repeated_unpackedFixed32_codec);
            break;
          }
          case 770:
          case 769: {
            unpackedFixed64_.AddEntriesFrom(input, _repeated_unpackedFixed64_codec);
            break;
          }
          case 778:
          case 781: {
            unpackedSfixed32_.AddEntriesFrom(input, _repeated_unpackedSfixed32_codec);
            break;
          }
          case 786:
          case 785: {
            unpackedSfixed64_.AddEntriesFrom(input, _repeated_unpackedSfixed64_codec);
            break;
          }
          case 794:
          case 797: {
            unpackedFloat_.AddEntriesFrom(input, _repeated_unpackedFloat_codec);
            break;
          }
          case 802:
          case 801: {
            unpackedDouble_.AddEntriesFrom(input, _repeated_unpackedDouble_codec);
            break;
          }
          case 810:
          case 808: {
            unpackedBool_.AddEntriesFrom(input, _repeated_unpackedBool_codec);
            break;
          }
          case 818:
          case 816: {
            unpackedNestedEnum_.AddEntriesFrom(input, _repeated_unpackedNestedEnum_codec);
            break;
          }
          case 888: {
            OneofUint32 = input.ReadUInt32();
            break;
          }
          case 898: {
            global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage subBuilder = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage();
            if (oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage) {
              subBuilder.MergeFrom(OneofNestedMessage);
            }
            input.ReadMessage(subBuilder);
            OneofNestedMessage = subBuilder;
            break;
          }
          case 906: {
            OneofString = input.ReadString();
            break;
          }
          case 914: {
            OneofBytes = input.ReadBytes();
            break;
          }
          case 920: {
            OneofBool = input.ReadBool();
            break;
          }
          case 928: {
            OneofUint64 = input.ReadUInt64();
            break;
          }
          case 941: {
            OneofFloat = input.ReadFloat();
            break;
          }
          case 945: {
            OneofDouble = input.ReadDouble();
            break;
          }
          case 952: {
            oneofField_ = input.ReadEnum();
            oneofFieldCase_ = OneofFieldOneofCase.OneofEnum;
            break;
          }
          case 1611: {
            if (!HasData) {
              Data = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.Data();
            }
            input.ReadGroup(Data);
            break;
          }
          case 1928: {
            DefaultInt32 = input.ReadInt32();
            break;
          }
          case 1936: {
            DefaultInt64 = input.ReadInt64();
            break;
          }
          case 1944: {
            DefaultUint32 = input.ReadUInt32();
            break;
          }
          case 1952: {
            DefaultUint64 = input.ReadUInt64();
            break;
          }
          case 1960: {
            DefaultSint32 = input.ReadSInt32();
            break;
          }
          case 1968: {
            DefaultSint64 = input.ReadSInt64();
            break;
          }
          case 1981: {
            DefaultFixed32 = input.ReadFixed32();
            break;
          }
          case 1985: {
            DefaultFixed64 = input.ReadFixed64();
            break;
          }
          case 1997: {
            DefaultSfixed32 = input.ReadSFixed32();
            break;
          }
          case 2001: {
            DefaultSfixed64 = input.ReadSFixed64();
            break;
          }
          case 2013: {
            DefaultFloat = input.ReadFloat();
            break;
          }
          case 2017: {
            DefaultDouble = input.ReadDouble();
            break;
          }
          case 2024: {
            DefaultBool = input.ReadBool();
            break;
          }
          case 2034: {
            DefaultString = input.ReadString();
            break;
          }
          case 2042: {
            DefaultBytes = input.ReadBytes();
            break;
          }
          case 3208: {
            Fieldname1 = input.ReadInt32();
            break;
          }
          case 3216: {
            FieldName2 = input.ReadInt32();
            break;
          }
          case 3224: {
            FieldName3 = input.ReadInt32();
            break;
          }
          case 3232: {
            FieldName4 = input.ReadInt32();
            break;
          }
          case 3240: {
            Field0Name5 = input.ReadInt32();
            break;
          }
          case 3248: {
            Field0Name6 = input.ReadInt32();
            break;
          }
          case 3256: {
            FieldName7 = input.ReadInt32();
            break;
          }
          case 3264: {
            FieldName8 = input.ReadInt32();
            break;
          }
          case 3272: {
            FieldName9 = input.ReadInt32();
            break;
          }
          case 3280: {
            FieldName10 = input.ReadInt32();
            break;
          }
          case 3288: {
            FIELDNAME11 = input.ReadInt32();
            break;
          }
          case 3296: {
            FIELDName12 = input.ReadInt32();
            break;
          }
          case 3304: {
            FieldName13 = input.ReadInt32();
            break;
          }
          case 3312: {
            FieldName14 = input.ReadInt32();
            break;
          }
          case 3320: {
            FieldName15 = input.ReadInt32();
            break;
          }
          case 3328: {
            FieldName16 = input.ReadInt32();
            break;
          }
          case 3336: {
            FieldName17 = input.ReadInt32();
            break;
          }
          case 3344: {
            FieldName18 = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            if (!pb::ExtensionSet.TryMergeFieldFrom(ref _extensions, ref input)) {
              _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            }
            break;
          case 8: {
            OptionalInt32 = input.ReadInt32();
            break;
          }
          case 16: {
            OptionalInt64 = input.ReadInt64();
            break;
          }
          case 24: {
            OptionalUint32 = input.ReadUInt32();
            break;
          }
          case 32: {
            OptionalUint64 = input.ReadUInt64();
            break;
          }
          case 40: {
            OptionalSint32 = input.ReadSInt32();
            break;
          }
          case 48: {
            OptionalSint64 = input.ReadSInt64();
            break;
          }
          case 61: {
            OptionalFixed32 = input.ReadFixed32();
            break;
          }
          case 65: {
            OptionalFixed64 = input.ReadFixed64();
            break;
          }
          case 77: {
            OptionalSfixed32 = input.ReadSFixed32();
            break;
          }
          case 81: {
            OptionalSfixed64 = input.ReadSFixed64();
            break;
          }
          case 93: {
            OptionalFloat = input.ReadFloat();
            break;
          }
          case 97: {
            OptionalDouble = input.ReadDouble();
            break;
          }
          case 104: {
            OptionalBool = input.ReadBool();
            break;
          }
          case 114: {
            OptionalString = input.ReadString();
            break;
          }
          case 122: {
            OptionalBytes = input.ReadBytes();
            break;
          }
          case 146: {
            if (optionalNestedMessage_ == null) {
              OptionalNestedMessage = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage();
            }
            input.ReadMessage(OptionalNestedMessage);
            break;
          }
          case 154: {
            if (optionalForeignMessage_ == null) {
              OptionalForeignMessage = new global::ProtobufTestMessages.Proto2.ForeignMessageProto2();
            }
            input.ReadMessage(OptionalForeignMessage);
            break;
          }
          case 168: {
            OptionalNestedEnum = (global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedEnum) input.ReadEnum();
            break;
          }
          case 176: {
            OptionalForeignEnum = (global::ProtobufTestMessages.Proto2.ForeignEnumProto2) input.ReadEnum();
            break;
          }
          case 194: {
            OptionalStringPiece = input.ReadString();
            break;
          }
          case 202: {
            OptionalCord = input.ReadString();
            break;
          }
          case 218: {
            if (recursiveMessage_ == null) {
              RecursiveMessage = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2();
            }
            input.ReadMessage(RecursiveMessage);
            break;
          }
          case 250:
          case 248: {
            repeatedInt32_.AddEntriesFrom(ref input, _repeated_repeatedInt32_codec);
            break;
          }
          case 258:
          case 256: {
            repeatedInt64_.AddEntriesFrom(ref input, _repeated_repeatedInt64_codec);
            break;
          }
          case 266:
          case 264: {
            repeatedUint32_.AddEntriesFrom(ref input, _repeated_repeatedUint32_codec);
            break;
          }
          case 274:
          case 272: {
            repeatedUint64_.AddEntriesFrom(ref input, _repeated_repeatedUint64_codec);
            break;
          }
          case 282:
          case 280: {
            repeatedSint32_.AddEntriesFrom(ref input, _repeated_repeatedSint32_codec);
            break;
          }
          case 290:
          case 288: {
            repeatedSint64_.AddEntriesFrom(ref input, _repeated_repeatedSint64_codec);
            break;
          }
          case 298:
          case 301: {
            repeatedFixed32_.AddEntriesFrom(ref input, _repeated_repeatedFixed32_codec);
            break;
          }
          case 306:
          case 305: {
            repeatedFixed64_.AddEntriesFrom(ref input, _repeated_repeatedFixed64_codec);
            break;
          }
          case 314:
          case 317: {
            repeatedSfixed32_.AddEntriesFrom(ref input, _repeated_repeatedSfixed32_codec);
            break;
          }
          case 322:
          case 321: {
            repeatedSfixed64_.AddEntriesFrom(ref input, _repeated_repeatedSfixed64_codec);
            break;
          }
          case 330:
          case 333: {
            repeatedFloat_.AddEntriesFrom(ref input, _repeated_repeatedFloat_codec);
            break;
          }
          case 338:
          case 337: {
            repeatedDouble_.AddEntriesFrom(ref input, _repeated_repeatedDouble_codec);
            break;
          }
          case 346:
          case 344: {
            repeatedBool_.AddEntriesFrom(ref input, _repeated_repeatedBool_codec);
            break;
          }
          case 354: {
            repeatedString_.AddEntriesFrom(ref input, _repeated_repeatedString_codec);
            break;
          }
          case 362: {
            repeatedBytes_.AddEntriesFrom(ref input, _repeated_repeatedBytes_codec);
            break;
          }
          case 386: {
            repeatedNestedMessage_.AddEntriesFrom(ref input, _repeated_repeatedNestedMessage_codec);
            break;
          }
          case 394: {
            repeatedForeignMessage_.AddEntriesFrom(ref input, _repeated_repeatedForeignMessage_codec);
            break;
          }
          case 410:
          case 408: {
            repeatedNestedEnum_.AddEntriesFrom(ref input, _repeated_repeatedNestedEnum_codec);
            break;
          }
          case 418:
          case 416: {
            repeatedForeignEnum_.AddEntriesFrom(ref input, _repeated_repeatedForeignEnum_codec);
            break;
          }
          case 434: {
            repeatedStringPiece_.AddEntriesFrom(ref input, _repeated_repeatedStringPiece_codec);
            break;
          }
          case 442: {
            repeatedCord_.AddEntriesFrom(ref input, _repeated_repeatedCord_codec);
            break;
          }
          case 450: {
            mapInt32Int32_.AddEntriesFrom(ref input, _map_mapInt32Int32_codec);
            break;
          }
          case 458: {
            mapInt64Int64_.AddEntriesFrom(ref input, _map_mapInt64Int64_codec);
            break;
          }
          case 466: {
            mapUint32Uint32_.AddEntriesFrom(ref input, _map_mapUint32Uint32_codec);
            break;
          }
          case 474: {
            mapUint64Uint64_.AddEntriesFrom(ref input, _map_mapUint64Uint64_codec);
            break;
          }
          case 482: {
            mapSint32Sint32_.AddEntriesFrom(ref input, _map_mapSint32Sint32_codec);
            break;
          }
          case 490: {
            mapSint64Sint64_.AddEntriesFrom(ref input, _map_mapSint64Sint64_codec);
            break;
          }
          case 498: {
            mapFixed32Fixed32_.AddEntriesFrom(ref input, _map_mapFixed32Fixed32_codec);
            break;
          }
          case 506: {
            mapFixed64Fixed64_.AddEntriesFrom(ref input, _map_mapFixed64Fixed64_codec);
            break;
          }
          case 514: {
            mapSfixed32Sfixed32_.AddEntriesFrom(ref input, _map_mapSfixed32Sfixed32_codec);
            break;
          }
          case 522: {
            mapSfixed64Sfixed64_.AddEntriesFrom(ref input, _map_mapSfixed64Sfixed64_codec);
            break;
          }
          case 530: {
            mapInt32Float_.AddEntriesFrom(ref input, _map_mapInt32Float_codec);
            break;
          }
          case 538: {
            mapInt32Double_.AddEntriesFrom(ref input, _map_mapInt32Double_codec);
            break;
          }
          case 546: {
            mapBoolBool_.AddEntriesFrom(ref input, _map_mapBoolBool_codec);
            break;
          }
          case 554: {
            mapStringString_.AddEntriesFrom(ref input, _map_mapStringString_codec);
            break;
          }
          case 562: {
            mapStringBytes_.AddEntriesFrom(ref input, _map_mapStringBytes_codec);
            break;
          }
          case 570: {
            mapStringNestedMessage_.AddEntriesFrom(ref input, _map_mapStringNestedMessage_codec);
            break;
          }
          case 578: {
            mapStringForeignMessage_.AddEntriesFrom(ref input, _map_mapStringForeignMessage_codec);
            break;
          }
          case 586: {
            mapStringNestedEnum_.AddEntriesFrom(ref input, _map_mapStringNestedEnum_codec);
            break;
          }
          case 594: {
            mapStringForeignEnum_.AddEntriesFrom(ref input, _map_mapStringForeignEnum_codec);
            break;
          }
          case 602:
          case 600: {
            packedInt32_.AddEntriesFrom(ref input, _repeated_packedInt32_codec);
            break;
          }
          case 610:
          case 608: {
            packedInt64_.AddEntriesFrom(ref input, _repeated_packedInt64_codec);
            break;
          }
          case 618:
          case 616: {
            packedUint32_.AddEntriesFrom(ref input, _repeated_packedUint32_codec);
            break;
          }
          case 626:
          case 624: {
            packedUint64_.AddEntriesFrom(ref input, _repeated_packedUint64_codec);
            break;
          }
          case 634:
          case 632: {
            packedSint32_.AddEntriesFrom(ref input, _repeated_packedSint32_codec);
            break;
          }
          case 642:
          case 640: {
            packedSint64_.AddEntriesFrom(ref input, _repeated_packedSint64_codec);
            break;
          }
          case 650:
          case 653: {
            packedFixed32_.AddEntriesFrom(ref input, _repeated_packedFixed32_codec);
            break;
          }
          case 658:
          case 657: {
            packedFixed64_.AddEntriesFrom(ref input, _repeated_packedFixed64_codec);
            break;
          }
          case 666:
          case 669: {
            packedSfixed32_.AddEntriesFrom(ref input, _repeated_packedSfixed32_codec);
            break;
          }
          case 674:
          case 673: {
            packedSfixed64_.AddEntriesFrom(ref input, _repeated_packedSfixed64_codec);
            break;
          }
          case 682:
          case 685: {
            packedFloat_.AddEntriesFrom(ref input, _repeated_packedFloat_codec);
            break;
          }
          case 690:
          case 689: {
            packedDouble_.AddEntriesFrom(ref input, _repeated_packedDouble_codec);
            break;
          }
          case 698:
          case 696: {
            packedBool_.AddEntriesFrom(ref input, _repeated_packedBool_codec);
            break;
          }
          case 706:
          case 704: {
            packedNestedEnum_.AddEntriesFrom(ref input, _repeated_packedNestedEnum_codec);
            break;
          }
          case 714:
          case 712: {
            unpackedInt32_.AddEntriesFrom(ref input, _repeated_unpackedInt32_codec);
            break;
          }
          case 722:
          case 720: {
            unpackedInt64_.AddEntriesFrom(ref input, _repeated_unpackedInt64_codec);
            break;
          }
          case 730:
          case 728: {
            unpackedUint32_.AddEntriesFrom(ref input, _repeated_unpackedUint32_codec);
            break;
          }
          case 738:
          case 736: {
            unpackedUint64_.AddEntriesFrom(ref input, _repeated_unpackedUint64_codec);
            break;
          }
          case 746:
          case 744: {
            unpackedSint32_.AddEntriesFrom(ref input, _repeated_unpackedSint32_codec);
            break;
          }
          case 754:
          case 752: {
            unpackedSint64_.AddEntriesFrom(ref input, _repeated_unpackedSint64_codec);
            break;
          }
          case 762:
          case 765: {
            unpackedFixed32_.AddEntriesFrom(ref input, _repeated_unpackedFixed32_codec);
            break;
          }
          case 770:
          case 769: {
            unpackedFixed64_.AddEntriesFrom(ref input, _repeated_unpackedFixed64_codec);
            break;
          }
          case 778:
          case 781: {
            unpackedSfixed32_.AddEntriesFrom(ref input, _repeated_unpackedSfixed32_codec);
            break;
          }
          case 786:
          case 785: {
            unpackedSfixed64_.AddEntriesFrom(ref input, _repeated_unpackedSfixed64_codec);
            break;
          }
          case 794:
          case 797: {
            unpackedFloat_.AddEntriesFrom(ref input, _repeated_unpackedFloat_codec);
            break;
          }
          case 802:
          case 801: {
            unpackedDouble_.AddEntriesFrom(ref input, _repeated_unpackedDouble_codec);
            break;
          }
          case 810:
          case 808: {
            unpackedBool_.AddEntriesFrom(ref input, _repeated_unpackedBool_codec);
            break;
          }
          case 818:
          case 816: {
            unpackedNestedEnum_.AddEntriesFrom(ref input, _repeated_unpackedNestedEnum_codec);
            break;
          }
          case 888: {
            OneofUint32 = input.ReadUInt32();
            break;
          }
          case 898: {
            global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage subBuilder = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.NestedMessage();
            if (oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage) {
              subBuilder.MergeFrom(OneofNestedMessage);
            }
            input.ReadMessage(subBuilder);
            OneofNestedMessage = subBuilder;
            break;
          }
          case 906: {
            OneofString = input.ReadString();
            break;
          }
          case 914: {
            OneofBytes = input.ReadBytes();
            break;
          }
          case 920: {
            OneofBool = input.ReadBool();
            break;
          }
          case 928: {
            OneofUint64 = input.ReadUInt64();
            break;
          }
          case 941: {
            OneofFloat = input.ReadFloat();
            break;
          }
          case 945: {
            OneofDouble = input.ReadDouble();
            break;
          }
          case 952: {
            oneofField_ = input.ReadEnum();
            oneofFieldCase_ = OneofFieldOneofCase.OneofEnum;
            break;
          }
          case 1611: {
            if (!HasData) {
              Data = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.Data();
            }
            input.ReadGroup(Data);
            break;
          }
          case 1928: {
            DefaultInt32 = input.ReadInt32();
            break;
          }
          case 1936: {
            DefaultInt64 = input.ReadInt64();
            break;
          }
          case 1944: {
            DefaultUint32 = input.ReadUInt32();
            break;
          }
          case 1952: {
            DefaultUint64 = input.ReadUInt64();
            break;
          }
          case 1960: {
            DefaultSint32 = input.ReadSInt32();
            break;
          }
          case 1968: {
            DefaultSint64 = input.ReadSInt64();
            break;
          }
          case 1981: {
            DefaultFixed32 = input.ReadFixed32();
            break;
          }
          case 1985: {
            DefaultFixed64 = input.ReadFixed64();
            break;
          }
          case 1997: {
            DefaultSfixed32 = input.ReadSFixed32();
            break;
          }
          case 2001: {
            DefaultSfixed64 = input.ReadSFixed64();
            break;
          }
          case 2013: {
            DefaultFloat = input.ReadFloat();
            break;
          }
          case 2017: {
            DefaultDouble = input.ReadDouble();
            break;
          }
          case 2024: {
            DefaultBool = input.ReadBool();
            break;
          }
          case 2034: {
            DefaultString = input.ReadString();
            break;
          }
          case 2042: {
            DefaultBytes = input.ReadBytes();
            break;
          }
          case 3208: {
            Fieldname1 = input.ReadInt32();
            break;
          }
          case 3216: {
            FieldName2 = input.ReadInt32();
            break;
          }
          case 3224: {
            FieldName3 = input.ReadInt32();
            break;
          }
          case 3232: {
            FieldName4 = input.ReadInt32();
            break;
          }
          case 3240: {
            Field0Name5 = input.ReadInt32();
            break;
          }
          case 3248: {
            Field0Name6 = input.ReadInt32();
            break;
          }
          case 3256: {
            FieldName7 = input.ReadInt32();
            break;
          }
          case 3264: {
            FieldName8 = input.ReadInt32();
            break;
          }
          case 3272: {
            FieldName9 = input.ReadInt32();
            break;
          }
          case 3280: {
            FieldName10 = input.ReadInt32();
            break;
          }
          case 3288: {
            FIELDNAME11 = input.ReadInt32();
            break;
          }
          case 3296: {
            FIELDName12 = input.ReadInt32();
            break;
          }
          case 3304: {
            FieldName13 = input.ReadInt32();
            break;
          }
          case 3312: {
            FieldName14 = input.ReadInt32();
            break;
          }
          case 3320: {
            FieldName15 = input.ReadInt32();
            break;
          }
          case 3328: {
            FieldName16 = input.ReadInt32();
            break;
          }
          case 3336: {
            FieldName17 = input.ReadInt32();
            break;
          }
          case 3344: {
            FieldName18 = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

    public TValue GetExtension<TValue>(pb::Extension<TestAllTypesProto2, TValue> extension) {
      return pb::ExtensionSet.Get(ref _extensions, extension);
    }
    public pbc::RepeatedField<TValue> GetExtension<TValue>(pb::RepeatedExtension<TestAllTypesProto2, TValue> extension) {
      return pb::ExtensionSet.Get(ref _extensions, extension);
    }
    public pbc::RepeatedField<TValue> GetOrInitializeExtension<TValue>(pb::RepeatedExtension<TestAllTypesProto2, TValue> extension) {
      return pb::ExtensionSet.GetOrInitialize(ref _extensions, extension);
    }
    public void SetExtension<TValue>(pb::Extension<TestAllTypesProto2, TValue> extension, TValue value) {
      pb::ExtensionSet.Set(ref _extensions, extension, value);
    }
    public bool HasExtension<TValue>(pb::Extension<TestAllTypesProto2, TValue> extension) {
      return pb::ExtensionSet.Has(ref _extensions, extension);
    }
    public void ClearExtension<TValue>(pb::Extension<TestAllTypesProto2, TValue> extension) {
      pb::ExtensionSet.Clear(ref _extensions, extension);
    }
    public void ClearExtension<TValue>(pb::RepeatedExtension<TestAllTypesProto2, TValue> extension) {
      pb::ExtensionSet.Clear(ref _extensions, extension);
    }

    #region Nested types
    /// <summary>Container for nested types declared in the TestAllTypesProto2 message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      public enum NestedEnum {
        [pbr::OriginalName("FOO")] Foo = 0,
        [pbr::OriginalName("BAR")] Bar = 1,
        [pbr::OriginalName("BAZ")] Baz = 2,
        /// <summary>
        /// Intentionally negative.
        /// </summary>
        [pbr::OriginalName("NEG")] Neg = -1,
      }

      public sealed partial class NestedMessage : pb::IMessage<NestedMessage>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<NestedMessage> _parser = new pb::MessageParser<NestedMessage>(() => new NestedMessage());
        private pb::UnknownFieldSet _unknownFields;
        private int _hasBits0;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<NestedMessage> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public NestedMessage() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public NestedMessage(NestedMessage other) : this() {
          _hasBits0 = other._hasBits0;
          a_ = other.a_;
          corecursive_ = other.corecursive_ != null ? other.corecursive_.Clone() : null;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public NestedMessage Clone() {
          return new NestedMessage(this);
        }

        /// <summary>Field number for the "a" field.</summary>
        public const int AFieldNumber = 1;
        private readonly static int ADefaultValue = 0;

        private int a_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int A {
          get { if ((_hasBits0 & 1) != 0) { return a_; } else { return ADefaultValue; } }
          set {
            _hasBits0 |= 1;
            a_ = value;
          }
        }
        /// <summary>Gets whether the "a" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasA {
          get { return (_hasBits0 & 1) != 0; }
        }
        /// <summary>Clears the value of the "a" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearA() {
          _hasBits0 &= ~1;
        }

        /// <summary>Field number for the "corecursive" field.</summary>
        public const int CorecursiveFieldNumber = 2;
        private global::ProtobufTestMessages.Proto2.TestAllTypesProto2 corecursive_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public global::ProtobufTestMessages.Proto2.TestAllTypesProto2 Corecursive {
          get { return corecursive_; }
          set {
            corecursive_ = value;
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as NestedMessage);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(NestedMessage other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (A != other.A) return false;
          if (!object.Equals(Corecursive, other.Corecursive)) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (HasA) hash ^= A.GetHashCode();
          if (corecursive_ != null) hash ^= Corecursive.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (HasA) {
            output.WriteRawTag(8);
            output.WriteInt32(A);
          }
          if (corecursive_ != null) {
            output.WriteRawTag(18);
            output.WriteMessage(Corecursive);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (HasA) {
            output.WriteRawTag(8);
            output.WriteInt32(A);
          }
          if (corecursive_ != null) {
            output.WriteRawTag(18);
            output.WriteMessage(Corecursive);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (HasA) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(A);
          }
          if (corecursive_ != null) {
            size += 1 + pb::CodedOutputStream.ComputeMessageSize(Corecursive);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(NestedMessage other) {
          if (other == null) {
            return;
          }
          if (other.HasA) {
            A = other.A;
          }
          if (other.corecursive_ != null) {
            if (corecursive_ == null) {
              Corecursive = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2();
            }
            Corecursive.MergeFrom(other.Corecursive);
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 8: {
                A = input.ReadInt32();
                break;
              }
              case 18: {
                if (corecursive_ == null) {
                  Corecursive = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2();
                }
                input.ReadMessage(Corecursive);
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 8: {
                A = input.ReadInt32();
                break;
              }
              case 18: {
                if (corecursive_ == null) {
                  Corecursive = new global::ProtobufTestMessages.Proto2.TestAllTypesProto2();
                }
                input.ReadMessage(Corecursive);
                break;
              }
            }
          }
        }
        #endif

      }

      /// <summary>
      /// groups
      /// </summary>
      public sealed partial class Data : pb::IMessage<Data>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<Data> _parser = new pb::MessageParser<Data>(() => new Data());
        private pb::UnknownFieldSet _unknownFields;
        private int _hasBits0;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<Data> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Descriptor.NestedTypes[20]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public Data() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public Data(Data other) : this() {
          _hasBits0 = other._hasBits0;
          groupInt32_ = other.groupInt32_;
          groupUint32_ = other.groupUint32_;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public Data Clone() {
          return new Data(this);
        }

        /// <summary>Field number for the "group_int32" field.</summary>
        public const int GroupInt32FieldNumber = 202;
        private readonly static int GroupInt32DefaultValue = 0;

        private int groupInt32_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int GroupInt32 {
          get { if ((_hasBits0 & 1) != 0) { return groupInt32_; } else { return GroupInt32DefaultValue; } }
          set {
            _hasBits0 |= 1;
            groupInt32_ = value;
          }
        }
        /// <summary>Gets whether the "group_int32" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasGroupInt32 {
          get { return (_hasBits0 & 1) != 0; }
        }
        /// <summary>Clears the value of the "group_int32" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearGroupInt32() {
          _hasBits0 &= ~1;
        }

        /// <summary>Field number for the "group_uint32" field.</summary>
        public const int GroupUint32FieldNumber = 203;
        private readonly static uint GroupUint32DefaultValue = 0;

        private uint groupUint32_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public uint GroupUint32 {
          get { if ((_hasBits0 & 2) != 0) { return groupUint32_; } else { return GroupUint32DefaultValue; } }
          set {
            _hasBits0 |= 2;
            groupUint32_ = value;
          }
        }
        /// <summary>Gets whether the "group_uint32" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasGroupUint32 {
          get { return (_hasBits0 & 2) != 0; }
        }
        /// <summary>Clears the value of the "group_uint32" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearGroupUint32() {
          _hasBits0 &= ~2;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as Data);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(Data other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (GroupInt32 != other.GroupInt32) return false;
          if (GroupUint32 != other.GroupUint32) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (HasGroupInt32) hash ^= GroupInt32.GetHashCode();
          if (HasGroupUint32) hash ^= GroupUint32.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (HasGroupInt32) {
            output.WriteRawTag(208, 12);
            output.WriteInt32(GroupInt32);
          }
          if (HasGroupUint32) {
            output.WriteRawTag(216, 12);
            output.WriteUInt32(GroupUint32);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (HasGroupInt32) {
            output.WriteRawTag(208, 12);
            output.WriteInt32(GroupInt32);
          }
          if (HasGroupUint32) {
            output.WriteRawTag(216, 12);
            output.WriteUInt32(GroupUint32);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (HasGroupInt32) {
            size += 2 + pb::CodedOutputStream.ComputeInt32Size(GroupInt32);
          }
          if (HasGroupUint32) {
            size += 2 + pb::CodedOutputStream.ComputeUInt32Size(GroupUint32);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(Data other) {
          if (other == null) {
            return;
          }
          if (other.HasGroupInt32) {
            GroupInt32 = other.GroupInt32;
          }
          if (other.HasGroupUint32) {
            GroupUint32 = other.GroupUint32;
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              case 1612:
                return;
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 1616: {
                GroupInt32 = input.ReadInt32();
                break;
              }
              case 1624: {
                GroupUint32 = input.ReadUInt32();
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              case 1612:
                return;
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 1616: {
                GroupInt32 = input.ReadInt32();
                break;
              }
              case 1624: {
                GroupUint32 = input.ReadUInt32();
                break;
              }
            }
          }
        }
        #endif

      }

      /// <summary>
      /// message_set test case.
      /// </summary>
      public sealed partial class MessageSetCorrect : pb::IExtendableMessage<MessageSetCorrect>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<MessageSetCorrect> _parser = new pb::MessageParser<MessageSetCorrect>(() => new MessageSetCorrect());
        private pb::UnknownFieldSet _unknownFields;
        private pb::ExtensionSet<MessageSetCorrect> _extensions;
        private pb::ExtensionSet<MessageSetCorrect> _Extensions { get { return _extensions; } }
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<MessageSetCorrect> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Descriptor.NestedTypes[21]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrect() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrect(MessageSetCorrect other) : this() {
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
          _extensions = pb::ExtensionSet.Clone(other._extensions);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrect Clone() {
          return new MessageSetCorrect(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as MessageSetCorrect);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(MessageSetCorrect other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (!Equals(_extensions, other._extensions)) {
            return false;
          }
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (_extensions != null) {
            hash ^= _extensions.GetHashCode();
          }
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (_extensions != null) {
            _extensions.WriteTo(output);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (_extensions != null) {
            _extensions.WriteTo(ref output);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (_extensions != null) {
            size += _extensions.CalculateSize();
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(MessageSetCorrect other) {
          if (other == null) {
            return;
          }
          pb::ExtensionSet.MergeFrom(ref _extensions, other._extensions);
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                if (!pb::ExtensionSet.TryMergeFieldFrom(ref _extensions, input)) {
                  _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                }
                break;
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                if (!pb::ExtensionSet.TryMergeFieldFrom(ref _extensions, ref input)) {
                  _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                }
                break;
            }
          }
        }
        #endif

        public TValue GetExtension<TValue>(pb::Extension<MessageSetCorrect, TValue> extension) {
          return pb::ExtensionSet.Get(ref _extensions, extension);
        }
        public pbc::RepeatedField<TValue> GetExtension<TValue>(pb::RepeatedExtension<MessageSetCorrect, TValue> extension) {
          return pb::ExtensionSet.Get(ref _extensions, extension);
        }
        public pbc::RepeatedField<TValue> GetOrInitializeExtension<TValue>(pb::RepeatedExtension<MessageSetCorrect, TValue> extension) {
          return pb::ExtensionSet.GetOrInitialize(ref _extensions, extension);
        }
        public void SetExtension<TValue>(pb::Extension<MessageSetCorrect, TValue> extension, TValue value) {
          pb::ExtensionSet.Set(ref _extensions, extension, value);
        }
        public bool HasExtension<TValue>(pb::Extension<MessageSetCorrect, TValue> extension) {
          return pb::ExtensionSet.Has(ref _extensions, extension);
        }
        public void ClearExtension<TValue>(pb::Extension<MessageSetCorrect, TValue> extension) {
          pb::ExtensionSet.Clear(ref _extensions, extension);
        }
        public void ClearExtension<TValue>(pb::RepeatedExtension<MessageSetCorrect, TValue> extension) {
          pb::ExtensionSet.Clear(ref _extensions, extension);
        }

      }

      public sealed partial class MessageSetCorrectExtension1 : pb::IMessage<MessageSetCorrectExtension1>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<MessageSetCorrectExtension1> _parser = new pb::MessageParser<MessageSetCorrectExtension1>(() => new MessageSetCorrectExtension1());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<MessageSetCorrectExtension1> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Descriptor.NestedTypes[22]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrectExtension1() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrectExtension1(MessageSetCorrectExtension1 other) : this() {
          str_ = other.str_;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrectExtension1 Clone() {
          return new MessageSetCorrectExtension1(this);
        }

        /// <summary>Field number for the "str" field.</summary>
        public const int StrFieldNumber = 25;
        private readonly static string StrDefaultValue = "";

        private string str_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Str {
          get { return str_ ?? StrDefaultValue; }
          set {
            str_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }
        /// <summary>Gets whether the "str" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasStr {
          get { return str_ != null; }
        }
        /// <summary>Clears the value of the "str" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearStr() {
          str_ = null;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as MessageSetCorrectExtension1);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(MessageSetCorrectExtension1 other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (Str != other.Str) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (HasStr) hash ^= Str.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (HasStr) {
            output.WriteRawTag(202, 1);
            output.WriteString(Str);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (HasStr) {
            output.WriteRawTag(202, 1);
            output.WriteString(Str);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (HasStr) {
            size += 2 + pb::CodedOutputStream.ComputeStringSize(Str);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(MessageSetCorrectExtension1 other) {
          if (other == null) {
            return;
          }
          if (other.HasStr) {
            Str = other.Str;
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 202: {
                Str = input.ReadString();
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 202: {
                Str = input.ReadString();
                break;
              }
            }
          }
        }
        #endif

        #region Extensions
        /// <summary>Container for extensions for other messages declared in the MessageSetCorrectExtension1 message type.</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static partial class Extensions {
          public static readonly pb::Extension<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrect, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrectExtension1> MessageSetExtension =
            new pb::Extension<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrect, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrectExtension1>(1547769, pb::FieldCodec.ForMessage(12382154, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrectExtension1.Parser));
        }
        #endregion

      }

      public sealed partial class MessageSetCorrectExtension2 : pb::IMessage<MessageSetCorrectExtension2>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<MessageSetCorrectExtension2> _parser = new pb::MessageParser<MessageSetCorrectExtension2>(() => new MessageSetCorrectExtension2());
        private pb::UnknownFieldSet _unknownFields;
        private int _hasBits0;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<MessageSetCorrectExtension2> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Descriptor.NestedTypes[23]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrectExtension2() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrectExtension2(MessageSetCorrectExtension2 other) : this() {
          _hasBits0 = other._hasBits0;
          i_ = other.i_;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrectExtension2 Clone() {
          return new MessageSetCorrectExtension2(this);
        }

        /// <summary>Field number for the "i" field.</summary>
        public const int IFieldNumber = 9;
        private readonly static int IDefaultValue = 0;

        private int i_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int I {
          get { if ((_hasBits0 & 1) != 0) { return i_; } else { return IDefaultValue; } }
          set {
            _hasBits0 |= 1;
            i_ = value;
          }
        }
        /// <summary>Gets whether the "i" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasI {
          get { return (_hasBits0 & 1) != 0; }
        }
        /// <summary>Clears the value of the "i" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearI() {
          _hasBits0 &= ~1;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as MessageSetCorrectExtension2);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(MessageSetCorrectExtension2 other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (I != other.I) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (HasI) hash ^= I.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (HasI) {
            output.WriteRawTag(72);
            output.WriteInt32(I);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (HasI) {
            output.WriteRawTag(72);
            output.WriteInt32(I);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (HasI) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(I);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(MessageSetCorrectExtension2 other) {
          if (other == null) {
            return;
          }
          if (other.HasI) {
            I = other.I;
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 72: {
                I = input.ReadInt32();
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 72: {
                I = input.ReadInt32();
                break;
              }
            }
          }
        }
        #endif

        #region Extensions
        /// <summary>Container for extensions for other messages declared in the MessageSetCorrectExtension2 message type.</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static partial class Extensions {
          public static readonly pb::Extension<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrect, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrectExtension2> MessageSetExtension =
            new pb::Extension<global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrect, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrectExtension2>(4135312, pb::FieldCodec.ForMessage(33082498, global::ProtobufTestMessages.Proto2.TestAllTypesProto2.Types.MessageSetCorrectExtension2.Parser));
        }
        #endregion

      }

    }
    #endregion

  }

  public sealed partial class ForeignMessageProto2 : pb::IMessage<ForeignMessageProto2>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ForeignMessageProto2> _parser = new pb::MessageParser<ForeignMessageProto2>(() => new ForeignMessageProto2());
    private pb::UnknownFieldSet _unknownFields;
    private int _hasBits0;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ForeignMessageProto2> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufTestMessages.Proto2.TestMessagesProto2Reflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ForeignMessageProto2() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ForeignMessageProto2(ForeignMessageProto2 other) : this() {
      _hasBits0 = other._hasBits0;
      c_ = other.c_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ForeignMessageProto2 Clone() {
      return new ForeignMessageProto2(this);
    }

    /// <summary>Field number for the "c" field.</summary>
    public const int CFieldNumber = 1;
    private readonly static int CDefaultValue = 0;

    private int c_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int C {
      get { if ((_hasBits0 & 1) != 0) { return c_; } else { return CDefaultValue; } }
      set {
        _hasBits0 |= 1;
        c_ = value;
      }
    }
    /// <summary>Gets whether the "c" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasC {
      get { return (_hasBits0 & 1) != 0; }
    }
    /// <summary>Clears the value of the "c" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearC() {
      _hasBits0 &= ~1;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ForeignMessageProto2);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ForeignMessageProto2 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (C != other.C) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasC) hash ^= C.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasC) {
        output.WriteRawTag(8);
        output.WriteInt32(C);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasC) {
        output.WriteRawTag(8);
        output.WriteInt32(C);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasC) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(C);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ForeignMessageProto2 other) {
      if (other == null) {
        return;
      }
      if (other.HasC) {
        C = other.C;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            C = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            C = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class UnknownToTestAllTypes : pb::IMessage<UnknownToTestAllTypes>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<UnknownToTestAllTypes> _parser = new pb::MessageParser<UnknownToTestAllTypes>(() => new UnknownToTestAllTypes());
    private pb::UnknownFieldSet _unknownFields;
    private int _hasBits0;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<UnknownToTestAllTypes> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufTestMessages.Proto2.TestMessagesProto2Reflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UnknownToTestAllTypes() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UnknownToTestAllTypes(UnknownToTestAllTypes other) : this() {
      _hasBits0 = other._hasBits0;
      optionalInt32_ = other.optionalInt32_;
      optionalString_ = other.optionalString_;
      nestedMessage_ = other.nestedMessage_ != null ? other.nestedMessage_.Clone() : null;
      optionalGroup_ = other.HasOptionalGroup ? other.optionalGroup_.Clone() : null;
      optionalBool_ = other.optionalBool_;
      repeatedInt32_ = other.repeatedInt32_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UnknownToTestAllTypes Clone() {
      return new UnknownToTestAllTypes(this);
    }

    /// <summary>Field number for the "optional_int32" field.</summary>
    public const int OptionalInt32FieldNumber = 1001;
    private readonly static int OptionalInt32DefaultValue = 0;

    private int optionalInt32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int OptionalInt32 {
      get { if ((_hasBits0 & 1) != 0) { return optionalInt32_; } else { return OptionalInt32DefaultValue; } }
      set {
        _hasBits0 |= 1;
        optionalInt32_ = value;
      }
    }
    /// <summary>Gets whether the "optional_int32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalInt32 {
      get { return (_hasBits0 & 1) != 0; }
    }
    /// <summary>Clears the value of the "optional_int32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalInt32() {
      _hasBits0 &= ~1;
    }

    /// <summary>Field number for the "optional_string" field.</summary>
    public const int OptionalStringFieldNumber = 1002;
    private readonly static string OptionalStringDefaultValue = "";

    private string optionalString_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OptionalString {
      get { return optionalString_ ?? OptionalStringDefaultValue; }
      set {
        optionalString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "optional_string" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalString {
      get { return optionalString_ != null; }
    }
    /// <summary>Clears the value of the "optional_string" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalString() {
      optionalString_ = null;
    }

    /// <summary>Field number for the "nested_message" field.</summary>
    public const int NestedMessageFieldNumber = 1003;
    private global::ProtobufTestMessages.Proto2.ForeignMessageProto2 nestedMessage_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.ForeignMessageProto2 NestedMessage {
      get { return nestedMessage_; }
      set {
        nestedMessage_ = value;
      }
    }

    /// <summary>Field number for the "optionalgroup" field.</summary>
    public const int OptionalGroupFieldNumber = 1004;
    private global::ProtobufTestMessages.Proto2.UnknownToTestAllTypes.Types.OptionalGroup optionalGroup_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.UnknownToTestAllTypes.Types.OptionalGroup OptionalGroup {
      get { return optionalGroup_; }
      set {
        optionalGroup_ = value;
      }
    }
    /// <summary>Gets whether the optionalgroup field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalGroup {
      get { return optionalGroup_ != null; }
    }
    /// <summary>Clears the value of the optionalgroup field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalGroup() {
      optionalGroup_ = null;
    }

    /// <summary>Field number for the "optional_bool" field.</summary>
    public const int OptionalBoolFieldNumber = 1006;
    private readonly static bool OptionalBoolDefaultValue = false;

    private bool optionalBool_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool OptionalBool {
      get { if ((_hasBits0 & 2) != 0) { return optionalBool_; } else { return OptionalBoolDefaultValue; } }
      set {
        _hasBits0 |= 2;
        optionalBool_ = value;
      }
    }
    /// <summary>Gets whether the "optional_bool" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOptionalBool {
      get { return (_hasBits0 & 2) != 0; }
    }
    /// <summary>Clears the value of the "optional_bool" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOptionalBool() {
      _hasBits0 &= ~2;
    }

    /// <summary>Field number for the "repeated_int32" field.</summary>
    public const int RepeatedInt32FieldNumber = 1011;
    private static readonly pb::FieldCodec<int> _repeated_repeatedInt32_codec
        = pb::FieldCodec.ForInt32(8088);
    private readonly pbc::RepeatedField<int> repeatedInt32_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> RepeatedInt32 {
      get { return repeatedInt32_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as UnknownToTestAllTypes);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(UnknownToTestAllTypes other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (OptionalInt32 != other.OptionalInt32) return false;
      if (OptionalString != other.OptionalString) return false;
      if (!object.Equals(NestedMessage, other.NestedMessage)) return false;
      if (!object.Equals(OptionalGroup, other.OptionalGroup)) return false;
      if (OptionalBool != other.OptionalBool) return false;
      if(!repeatedInt32_.Equals(other.repeatedInt32_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasOptionalInt32) hash ^= OptionalInt32.GetHashCode();
      if (HasOptionalString) hash ^= OptionalString.GetHashCode();
      if (nestedMessage_ != null) hash ^= NestedMessage.GetHashCode();
      if (HasOptionalGroup) hash ^= OptionalGroup.GetHashCode();
      if (HasOptionalBool) hash ^= OptionalBool.GetHashCode();
      hash ^= repeatedInt32_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasOptionalInt32) {
        output.WriteRawTag(200, 62);
        output.WriteInt32(OptionalInt32);
      }
      if (HasOptionalString) {
        output.WriteRawTag(210, 62);
        output.WriteString(OptionalString);
      }
      if (nestedMessage_ != null) {
        output.WriteRawTag(218, 62);
        output.WriteMessage(NestedMessage);
      }
      if (HasOptionalGroup) {
        output.WriteRawTag(227, 62);
        output.WriteGroup(OptionalGroup);
        output.WriteRawTag(228, 62);
      }
      if (HasOptionalBool) {
        output.WriteRawTag(240, 62);
        output.WriteBool(OptionalBool);
      }
      repeatedInt32_.WriteTo(output, _repeated_repeatedInt32_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasOptionalInt32) {
        output.WriteRawTag(200, 62);
        output.WriteInt32(OptionalInt32);
      }
      if (HasOptionalString) {
        output.WriteRawTag(210, 62);
        output.WriteString(OptionalString);
      }
      if (nestedMessage_ != null) {
        output.WriteRawTag(218, 62);
        output.WriteMessage(NestedMessage);
      }
      if (HasOptionalGroup) {
        output.WriteRawTag(227, 62);
        output.WriteGroup(OptionalGroup);
        output.WriteRawTag(228, 62);
      }
      if (HasOptionalBool) {
        output.WriteRawTag(240, 62);
        output.WriteBool(OptionalBool);
      }
      repeatedInt32_.WriteTo(ref output, _repeated_repeatedInt32_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasOptionalInt32) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(OptionalInt32);
      }
      if (HasOptionalString) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(OptionalString);
      }
      if (nestedMessage_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(NestedMessage);
      }
      if (HasOptionalGroup) {
        size += 4 + pb::CodedOutputStream.ComputeGroupSize(OptionalGroup);
      }
      if (HasOptionalBool) {
        size += 2 + 1;
      }
      size += repeatedInt32_.CalculateSize(_repeated_repeatedInt32_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(UnknownToTestAllTypes other) {
      if (other == null) {
        return;
      }
      if (other.HasOptionalInt32) {
        OptionalInt32 = other.OptionalInt32;
      }
      if (other.HasOptionalString) {
        OptionalString = other.OptionalString;
      }
      if (other.nestedMessage_ != null) {
        if (nestedMessage_ == null) {
          NestedMessage = new global::ProtobufTestMessages.Proto2.ForeignMessageProto2();
        }
        NestedMessage.MergeFrom(other.NestedMessage);
      }
      if (other.HasOptionalGroup) {
        if (!HasOptionalGroup) {
          OptionalGroup = new global::ProtobufTestMessages.Proto2.UnknownToTestAllTypes.Types.OptionalGroup();
        }
        OptionalGroup.MergeFrom(other.OptionalGroup);
      }
      if (other.HasOptionalBool) {
        OptionalBool = other.OptionalBool;
      }
      repeatedInt32_.Add(other.repeatedInt32_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8008: {
            OptionalInt32 = input.ReadInt32();
            break;
          }
          case 8018: {
            OptionalString = input.ReadString();
            break;
          }
          case 8026: {
            if (nestedMessage_ == null) {
              NestedMessage = new global::ProtobufTestMessages.Proto2.ForeignMessageProto2();
            }
            input.ReadMessage(NestedMessage);
            break;
          }
          case 8035: {
            if (!HasOptionalGroup) {
              OptionalGroup = new global::ProtobufTestMessages.Proto2.UnknownToTestAllTypes.Types.OptionalGroup();
            }
            input.ReadGroup(OptionalGroup);
            break;
          }
          case 8048: {
            OptionalBool = input.ReadBool();
            break;
          }
          case 8090:
          case 8088: {
            repeatedInt32_.AddEntriesFrom(input, _repeated_repeatedInt32_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8008: {
            OptionalInt32 = input.ReadInt32();
            break;
          }
          case 8018: {
            OptionalString = input.ReadString();
            break;
          }
          case 8026: {
            if (nestedMessage_ == null) {
              NestedMessage = new global::ProtobufTestMessages.Proto2.ForeignMessageProto2();
            }
            input.ReadMessage(NestedMessage);
            break;
          }
          case 8035: {
            if (!HasOptionalGroup) {
              OptionalGroup = new global::ProtobufTestMessages.Proto2.UnknownToTestAllTypes.Types.OptionalGroup();
            }
            input.ReadGroup(OptionalGroup);
            break;
          }
          case 8048: {
            OptionalBool = input.ReadBool();
            break;
          }
          case 8090:
          case 8088: {
            repeatedInt32_.AddEntriesFrom(ref input, _repeated_repeatedInt32_codec);
            break;
          }
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the UnknownToTestAllTypes message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      public sealed partial class OptionalGroup : pb::IMessage<OptionalGroup>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<OptionalGroup> _parser = new pb::MessageParser<OptionalGroup>(() => new OptionalGroup());
        private pb::UnknownFieldSet _unknownFields;
        private int _hasBits0;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<OptionalGroup> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::ProtobufTestMessages.Proto2.UnknownToTestAllTypes.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public OptionalGroup() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public OptionalGroup(OptionalGroup other) : this() {
          _hasBits0 = other._hasBits0;
          a_ = other.a_;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public OptionalGroup Clone() {
          return new OptionalGroup(this);
        }

        /// <summary>Field number for the "a" field.</summary>
        public const int AFieldNumber = 1;
        private readonly static int ADefaultValue = 0;

        private int a_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int A {
          get { if ((_hasBits0 & 1) != 0) { return a_; } else { return ADefaultValue; } }
          set {
            _hasBits0 |= 1;
            a_ = value;
          }
        }
        /// <summary>Gets whether the "a" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasA {
          get { return (_hasBits0 & 1) != 0; }
        }
        /// <summary>Clears the value of the "a" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearA() {
          _hasBits0 &= ~1;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as OptionalGroup);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(OptionalGroup other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (A != other.A) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (HasA) hash ^= A.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (HasA) {
            output.WriteRawTag(8);
            output.WriteInt32(A);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (HasA) {
            output.WriteRawTag(8);
            output.WriteInt32(A);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (HasA) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(A);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(OptionalGroup other) {
          if (other == null) {
            return;
          }
          if (other.HasA) {
            A = other.A;
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              case 8036:
                return;
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 8: {
                A = input.ReadInt32();
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              case 8036:
                return;
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 8: {
                A = input.ReadInt32();
                break;
              }
            }
          }
        }
        #endif

      }

    }
    #endregion

  }

  public sealed partial class NullHypothesisProto2 : pb::IMessage<NullHypothesisProto2>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<NullHypothesisProto2> _parser = new pb::MessageParser<NullHypothesisProto2>(() => new NullHypothesisProto2());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<NullHypothesisProto2> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufTestMessages.Proto2.TestMessagesProto2Reflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullHypothesisProto2() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullHypothesisProto2(NullHypothesisProto2 other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullHypothesisProto2 Clone() {
      return new NullHypothesisProto2(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as NullHypothesisProto2);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(NullHypothesisProto2 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(NullHypothesisProto2 other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  public sealed partial class EnumOnlyProto2 : pb::IMessage<EnumOnlyProto2>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<EnumOnlyProto2> _parser = new pb::MessageParser<EnumOnlyProto2>(() => new EnumOnlyProto2());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<EnumOnlyProto2> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufTestMessages.Proto2.TestMessagesProto2Reflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public EnumOnlyProto2() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public EnumOnlyProto2(EnumOnlyProto2 other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public EnumOnlyProto2 Clone() {
      return new EnumOnlyProto2(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as EnumOnlyProto2);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(EnumOnlyProto2 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(EnumOnlyProto2 other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the EnumOnlyProto2 message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      public enum Bool {
        [pbr::OriginalName("kFalse")] KFalse = 0,
        [pbr::OriginalName("kTrue")] KTrue = 1,
      }

    }
    #endregion

  }

  public sealed partial class OneStringProto2 : pb::IMessage<OneStringProto2>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<OneStringProto2> _parser = new pb::MessageParser<OneStringProto2>(() => new OneStringProto2());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<OneStringProto2> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufTestMessages.Proto2.TestMessagesProto2Reflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public OneStringProto2() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public OneStringProto2(OneStringProto2 other) : this() {
      data_ = other.data_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public OneStringProto2 Clone() {
      return new OneStringProto2(this);
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int DataFieldNumber = 1;
    private readonly static string DataDefaultValue = "";

    private string data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Data {
      get { return data_ ?? DataDefaultValue; }
      set {
        data_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "data" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasData {
      get { return data_ != null; }
    }
    /// <summary>Clears the value of the "data" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearData() {
      data_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as OneStringProto2);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(OneStringProto2 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Data != other.Data) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasData) hash ^= Data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasData) {
        output.WriteRawTag(10);
        output.WriteString(Data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasData) {
        output.WriteRawTag(10);
        output.WriteString(Data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasData) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(OneStringProto2 other) {
      if (other == null) {
        return;
      }
      if (other.HasData) {
        Data = other.Data;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Data = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Data = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class ProtoWithKeywords : pb::IMessage<ProtoWithKeywords>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ProtoWithKeywords> _parser = new pb::MessageParser<ProtoWithKeywords>(() => new ProtoWithKeywords());
    private pb::UnknownFieldSet _unknownFields;
    private int _hasBits0;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ProtoWithKeywords> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufTestMessages.Proto2.TestMessagesProto2Reflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ProtoWithKeywords() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ProtoWithKeywords(ProtoWithKeywords other) : this() {
      _hasBits0 = other._hasBits0;
      inline_ = other.inline_;
      concept_ = other.concept_;
      requires_ = other.requires_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ProtoWithKeywords Clone() {
      return new ProtoWithKeywords(this);
    }

    /// <summary>Field number for the "inline" field.</summary>
    public const int InlineFieldNumber = 1;
    private readonly static int InlineDefaultValue = 0;

    private int inline_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Inline {
      get { if ((_hasBits0 & 1) != 0) { return inline_; } else { return InlineDefaultValue; } }
      set {
        _hasBits0 |= 1;
        inline_ = value;
      }
    }
    /// <summary>Gets whether the "inline" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasInline {
      get { return (_hasBits0 & 1) != 0; }
    }
    /// <summary>Clears the value of the "inline" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearInline() {
      _hasBits0 &= ~1;
    }

    /// <summary>Field number for the "concept" field.</summary>
    public const int ConceptFieldNumber = 2;
    private readonly static string ConceptDefaultValue = "";

    private string concept_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Concept {
      get { return concept_ ?? ConceptDefaultValue; }
      set {
        concept_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "concept" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasConcept {
      get { return concept_ != null; }
    }
    /// <summary>Clears the value of the "concept" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearConcept() {
      concept_ = null;
    }

    /// <summary>Field number for the "requires" field.</summary>
    public const int RequiresFieldNumber = 3;
    private static readonly pb::FieldCodec<string> _repeated_requires_codec
        = pb::FieldCodec.ForString(26);
    private readonly pbc::RepeatedField<string> requires_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> Requires {
      get { return requires_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ProtoWithKeywords);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ProtoWithKeywords other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Inline != other.Inline) return false;
      if (Concept != other.Concept) return false;
      if(!requires_.Equals(other.requires_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasInline) hash ^= Inline.GetHashCode();
      if (HasConcept) hash ^= Concept.GetHashCode();
      hash ^= requires_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasInline) {
        output.WriteRawTag(8);
        output.WriteInt32(Inline);
      }
      if (HasConcept) {
        output.WriteRawTag(18);
        output.WriteString(Concept);
      }
      requires_.WriteTo(output, _repeated_requires_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasInline) {
        output.WriteRawTag(8);
        output.WriteInt32(Inline);
      }
      if (HasConcept) {
        output.WriteRawTag(18);
        output.WriteString(Concept);
      }
      requires_.WriteTo(ref output, _repeated_requires_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasInline) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Inline);
      }
      if (HasConcept) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Concept);
      }
      size += requires_.CalculateSize(_repeated_requires_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ProtoWithKeywords other) {
      if (other == null) {
        return;
      }
      if (other.HasInline) {
        Inline = other.Inline;
      }
      if (other.HasConcept) {
        Concept = other.Concept;
      }
      requires_.Add(other.requires_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Inline = input.ReadInt32();
            break;
          }
          case 18: {
            Concept = input.ReadString();
            break;
          }
          case 26: {
            requires_.AddEntriesFrom(input, _repeated_requires_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Inline = input.ReadInt32();
            break;
          }
          case 18: {
            Concept = input.ReadString();
            break;
          }
          case 26: {
            requires_.AddEntriesFrom(ref input, _repeated_requires_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class TestAllRequiredTypesProto2 : pb::IExtendableMessage<TestAllRequiredTypesProto2>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TestAllRequiredTypesProto2> _parser = new pb::MessageParser<TestAllRequiredTypesProto2>(() => new TestAllRequiredTypesProto2());
    private pb::UnknownFieldSet _unknownFields;
    private pb::ExtensionSet<TestAllRequiredTypesProto2> _extensions;
    private pb::ExtensionSet<TestAllRequiredTypesProto2> _Extensions { get { return _extensions; } }
    private int _hasBits0;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TestAllRequiredTypesProto2> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufTestMessages.Proto2.TestMessagesProto2Reflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestAllRequiredTypesProto2() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestAllRequiredTypesProto2(TestAllRequiredTypesProto2 other) : this() {
      _hasBits0 = other._hasBits0;
      requiredInt32_ = other.requiredInt32_;
      requiredInt64_ = other.requiredInt64_;
      requiredUint32_ = other.requiredUint32_;
      requiredUint64_ = other.requiredUint64_;
      requiredSint32_ = other.requiredSint32_;
      requiredSint64_ = other.requiredSint64_;
      requiredFixed32_ = other.requiredFixed32_;
      requiredFixed64_ = other.requiredFixed64_;
      requiredSfixed32_ = other.requiredSfixed32_;
      requiredSfixed64_ = other.requiredSfixed64_;
      requiredFloat_ = other.requiredFloat_;
      requiredDouble_ = other.requiredDouble_;
      requiredBool_ = other.requiredBool_;
      requiredString_ = other.requiredString_;
      requiredBytes_ = other.requiredBytes_;
      requiredNestedMessage_ = other.requiredNestedMessage_ != null ? other.requiredNestedMessage_.Clone() : null;
      requiredForeignMessage_ = other.requiredForeignMessage_ != null ? other.requiredForeignMessage_.Clone() : null;
      requiredNestedEnum_ = other.requiredNestedEnum_;
      requiredForeignEnum_ = other.requiredForeignEnum_;
      requiredStringPiece_ = other.requiredStringPiece_;
      requiredCord_ = other.requiredCord_;
      recursiveMessage_ = other.recursiveMessage_ != null ? other.recursiveMessage_.Clone() : null;
      optionalRecursiveMessage_ = other.optionalRecursiveMessage_ != null ? other.optionalRecursiveMessage_.Clone() : null;
      data_ = other.HasData ? other.data_.Clone() : null;
      defaultInt32_ = other.defaultInt32_;
      defaultInt64_ = other.defaultInt64_;
      defaultUint32_ = other.defaultUint32_;
      defaultUint64_ = other.defaultUint64_;
      defaultSint32_ = other.defaultSint32_;
      defaultSint64_ = other.defaultSint64_;
      defaultFixed32_ = other.defaultFixed32_;
      defaultFixed64_ = other.defaultFixed64_;
      defaultSfixed32_ = other.defaultSfixed32_;
      defaultSfixed64_ = other.defaultSfixed64_;
      defaultFloat_ = other.defaultFloat_;
      defaultDouble_ = other.defaultDouble_;
      defaultBool_ = other.defaultBool_;
      defaultString_ = other.defaultString_;
      defaultBytes_ = other.defaultBytes_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
      _extensions = pb::ExtensionSet.Clone(other._extensions);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestAllRequiredTypesProto2 Clone() {
      return new TestAllRequiredTypesProto2(this);
    }

    /// <summary>Field number for the "required_int32" field.</summary>
    public const int RequiredInt32FieldNumber = 1;
    private readonly static int RequiredInt32DefaultValue = 0;

    private int requiredInt32_;
    /// <summary>
    /// Singular
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int RequiredInt32 {
      get { if ((_hasBits0 & 1) != 0) { return requiredInt32_; } else { return RequiredInt32DefaultValue; } }
      set {
        _hasBits0 |= 1;
        requiredInt32_ = value;
      }
    }
    /// <summary>Gets whether the "required_int32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredInt32 {
      get { return (_hasBits0 & 1) != 0; }
    }
    /// <summary>Clears the value of the "required_int32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredInt32() {
      _hasBits0 &= ~1;
    }

    /// <summary>Field number for the "required_int64" field.</summary>
    public const int RequiredInt64FieldNumber = 2;
    private readonly static long RequiredInt64DefaultValue = 0L;

    private long requiredInt64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long RequiredInt64 {
      get { if ((_hasBits0 & 2) != 0) { return requiredInt64_; } else { return RequiredInt64DefaultValue; } }
      set {
        _hasBits0 |= 2;
        requiredInt64_ = value;
      }
    }
    /// <summary>Gets whether the "required_int64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredInt64 {
      get { return (_hasBits0 & 2) != 0; }
    }
    /// <summary>Clears the value of the "required_int64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredInt64() {
      _hasBits0 &= ~2;
    }

    /// <summary>Field number for the "required_uint32" field.</summary>
    public const int RequiredUint32FieldNumber = 3;
    private readonly static uint RequiredUint32DefaultValue = 0;

    private uint requiredUint32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint RequiredUint32 {
      get { if ((_hasBits0 & 4) != 0) { return requiredUint32_; } else { return RequiredUint32DefaultValue; } }
      set {
        _hasBits0 |= 4;
        requiredUint32_ = value;
      }
    }
    /// <summary>Gets whether the "required_uint32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredUint32 {
      get { return (_hasBits0 & 4) != 0; }
    }
    /// <summary>Clears the value of the "required_uint32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredUint32() {
      _hasBits0 &= ~4;
    }

    /// <summary>Field number for the "required_uint64" field.</summary>
    public const int RequiredUint64FieldNumber = 4;
    private readonly static ulong RequiredUint64DefaultValue = 0UL;

    private ulong requiredUint64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong RequiredUint64 {
      get { if ((_hasBits0 & 8) != 0) { return requiredUint64_; } else { return RequiredUint64DefaultValue; } }
      set {
        _hasBits0 |= 8;
        requiredUint64_ = value;
      }
    }
    /// <summary>Gets whether the "required_uint64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredUint64 {
      get { return (_hasBits0 & 8) != 0; }
    }
    /// <summary>Clears the value of the "required_uint64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredUint64() {
      _hasBits0 &= ~8;
    }

    /// <summary>Field number for the "required_sint32" field.</summary>
    public const int RequiredSint32FieldNumber = 5;
    private readonly static int RequiredSint32DefaultValue = 0;

    private int requiredSint32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int RequiredSint32 {
      get { if ((_hasBits0 & 16) != 0) { return requiredSint32_; } else { return RequiredSint32DefaultValue; } }
      set {
        _hasBits0 |= 16;
        requiredSint32_ = value;
      }
    }
    /// <summary>Gets whether the "required_sint32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredSint32 {
      get { return (_hasBits0 & 16) != 0; }
    }
    /// <summary>Clears the value of the "required_sint32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredSint32() {
      _hasBits0 &= ~16;
    }

    /// <summary>Field number for the "required_sint64" field.</summary>
    public const int RequiredSint64FieldNumber = 6;
    private readonly static long RequiredSint64DefaultValue = 0L;

    private long requiredSint64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long RequiredSint64 {
      get { if ((_hasBits0 & 32) != 0) { return requiredSint64_; } else { return RequiredSint64DefaultValue; } }
      set {
        _hasBits0 |= 32;
        requiredSint64_ = value;
      }
    }
    /// <summary>Gets whether the "required_sint64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredSint64 {
      get { return (_hasBits0 & 32) != 0; }
    }
    /// <summary>Clears the value of the "required_sint64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredSint64() {
      _hasBits0 &= ~32;
    }

    /// <summary>Field number for the "required_fixed32" field.</summary>
    public const int RequiredFixed32FieldNumber = 7;
    private readonly static uint RequiredFixed32DefaultValue = 0;

    private uint requiredFixed32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint RequiredFixed32 {
      get { if ((_hasBits0 & 64) != 0) { return requiredFixed32_; } else { return RequiredFixed32DefaultValue; } }
      set {
        _hasBits0 |= 64;
        requiredFixed32_ = value;
      }
    }
    /// <summary>Gets whether the "required_fixed32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredFixed32 {
      get { return (_hasBits0 & 64) != 0; }
    }
    /// <summary>Clears the value of the "required_fixed32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredFixed32() {
      _hasBits0 &= ~64;
    }

    /// <summary>Field number for the "required_fixed64" field.</summary>
    public const int RequiredFixed64FieldNumber = 8;
    private readonly static ulong RequiredFixed64DefaultValue = 0UL;

    private ulong requiredFixed64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong RequiredFixed64 {
      get { if ((_hasBits0 & 128) != 0) { return requiredFixed64_; } else { return RequiredFixed64DefaultValue; } }
      set {
        _hasBits0 |= 128;
        requiredFixed64_ = value;
      }
    }
    /// <summary>Gets whether the "required_fixed64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredFixed64 {
      get { return (_hasBits0 & 128) != 0; }
    }
    /// <summary>Clears the value of the "required_fixed64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredFixed64() {
      _hasBits0 &= ~128;
    }

    /// <summary>Field number for the "required_sfixed32" field.</summary>
    public const int RequiredSfixed32FieldNumber = 9;
    private readonly static int RequiredSfixed32DefaultValue = 0;

    private int requiredSfixed32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int RequiredSfixed32 {
      get { if ((_hasBits0 & 256) != 0) { return requiredSfixed32_; } else { return RequiredSfixed32DefaultValue; } }
      set {
        _hasBits0 |= 256;
        requiredSfixed32_ = value;
      }
    }
    /// <summary>Gets whether the "required_sfixed32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredSfixed32 {
      get { return (_hasBits0 & 256) != 0; }
    }
    /// <summary>Clears the value of the "required_sfixed32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredSfixed32() {
      _hasBits0 &= ~256;
    }

    /// <summary>Field number for the "required_sfixed64" field.</summary>
    public const int RequiredSfixed64FieldNumber = 10;
    private readonly static long RequiredSfixed64DefaultValue = 0L;

    private long requiredSfixed64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long RequiredSfixed64 {
      get { if ((_hasBits0 & 512) != 0) { return requiredSfixed64_; } else { return RequiredSfixed64DefaultValue; } }
      set {
        _hasBits0 |= 512;
        requiredSfixed64_ = value;
      }
    }
    /// <summary>Gets whether the "required_sfixed64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredSfixed64 {
      get { return (_hasBits0 & 512) != 0; }
    }
    /// <summary>Clears the value of the "required_sfixed64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredSfixed64() {
      _hasBits0 &= ~512;
    }

    /// <summary>Field number for the "required_float" field.</summary>
    public const int RequiredFloatFieldNumber = 11;
    private readonly static float RequiredFloatDefaultValue = 0F;

    private float requiredFloat_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float RequiredFloat {
      get { if ((_hasBits0 & 1024) != 0) { return requiredFloat_; } else { return RequiredFloatDefaultValue; } }
      set {
        _hasBits0 |= 1024;
        requiredFloat_ = value;
      }
    }
    /// <summary>Gets whether the "required_float" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredFloat {
      get { return (_hasBits0 & 1024) != 0; }
    }
    /// <summary>Clears the value of the "required_float" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredFloat() {
      _hasBits0 &= ~1024;
    }

    /// <summary>Field number for the "required_double" field.</summary>
    public const int RequiredDoubleFieldNumber = 12;
    private readonly static double RequiredDoubleDefaultValue = 0D;

    private double requiredDouble_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double RequiredDouble {
      get { if ((_hasBits0 & 2048) != 0) { return requiredDouble_; } else { return RequiredDoubleDefaultValue; } }
      set {
        _hasBits0 |= 2048;
        requiredDouble_ = value;
      }
    }
    /// <summary>Gets whether the "required_double" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredDouble {
      get { return (_hasBits0 & 2048) != 0; }
    }
    /// <summary>Clears the value of the "required_double" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredDouble() {
      _hasBits0 &= ~2048;
    }

    /// <summary>Field number for the "required_bool" field.</summary>
    public const int RequiredBoolFieldNumber = 13;
    private readonly static bool RequiredBoolDefaultValue = false;

    private bool requiredBool_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool RequiredBool {
      get { if ((_hasBits0 & 4096) != 0) { return requiredBool_; } else { return RequiredBoolDefaultValue; } }
      set {
        _hasBits0 |= 4096;
        requiredBool_ = value;
      }
    }
    /// <summary>Gets whether the "required_bool" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredBool {
      get { return (_hasBits0 & 4096) != 0; }
    }
    /// <summary>Clears the value of the "required_bool" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredBool() {
      _hasBits0 &= ~4096;
    }

    /// <summary>Field number for the "required_string" field.</summary>
    public const int RequiredStringFieldNumber = 14;
    private readonly static string RequiredStringDefaultValue = "";

    private string requiredString_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string RequiredString {
      get { return requiredString_ ?? RequiredStringDefaultValue; }
      set {
        requiredString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "required_string" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredString {
      get { return requiredString_ != null; }
    }
    /// <summary>Clears the value of the "required_string" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredString() {
      requiredString_ = null;
    }

    /// <summary>Field number for the "required_bytes" field.</summary>
    public const int RequiredBytesFieldNumber = 15;
    private readonly static pb::ByteString RequiredBytesDefaultValue = pb::ByteString.Empty;

    private pb::ByteString requiredBytes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString RequiredBytes {
      get { return requiredBytes_ ?? RequiredBytesDefaultValue; }
      set {
        requiredBytes_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "required_bytes" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredBytes {
      get { return requiredBytes_ != null; }
    }
    /// <summary>Clears the value of the "required_bytes" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredBytes() {
      requiredBytes_ = null;
    }

    /// <summary>Field number for the "required_nested_message" field.</summary>
    public const int RequiredNestedMessageFieldNumber = 18;
    private global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedMessage requiredNestedMessage_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedMessage RequiredNestedMessage {
      get { return requiredNestedMessage_; }
      set {
        requiredNestedMessage_ = value;
      }
    }

    /// <summary>Field number for the "required_foreign_message" field.</summary>
    public const int RequiredForeignMessageFieldNumber = 19;
    private global::ProtobufTestMessages.Proto2.ForeignMessageProto2 requiredForeignMessage_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.ForeignMessageProto2 RequiredForeignMessage {
      get { return requiredForeignMessage_; }
      set {
        requiredForeignMessage_ = value;
      }
    }

    /// <summary>Field number for the "required_nested_enum" field.</summary>
    public const int RequiredNestedEnumFieldNumber = 21;
    private readonly static global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedEnum RequiredNestedEnumDefaultValue = global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedEnum.Foo;

    private global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedEnum requiredNestedEnum_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedEnum RequiredNestedEnum {
      get { if ((_hasBits0 & 8192) != 0) { return requiredNestedEnum_; } else { return RequiredNestedEnumDefaultValue; } }
      set {
        _hasBits0 |= 8192;
        requiredNestedEnum_ = value;
      }
    }
    /// <summary>Gets whether the "required_nested_enum" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredNestedEnum {
      get { return (_hasBits0 & 8192) != 0; }
    }
    /// <summary>Clears the value of the "required_nested_enum" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredNestedEnum() {
      _hasBits0 &= ~8192;
    }

    /// <summary>Field number for the "required_foreign_enum" field.</summary>
    public const int RequiredForeignEnumFieldNumber = 22;
    private readonly static global::ProtobufTestMessages.Proto2.ForeignEnumProto2 RequiredForeignEnumDefaultValue = global::ProtobufTestMessages.Proto2.ForeignEnumProto2.ForeignFoo;

    private global::ProtobufTestMessages.Proto2.ForeignEnumProto2 requiredForeignEnum_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.ForeignEnumProto2 RequiredForeignEnum {
      get { if ((_hasBits0 & 16384) != 0) { return requiredForeignEnum_; } else { return RequiredForeignEnumDefaultValue; } }
      set {
        _hasBits0 |= 16384;
        requiredForeignEnum_ = value;
      }
    }
    /// <summary>Gets whether the "required_foreign_enum" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredForeignEnum {
      get { return (_hasBits0 & 16384) != 0; }
    }
    /// <summary>Clears the value of the "required_foreign_enum" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredForeignEnum() {
      _hasBits0 &= ~16384;
    }

    /// <summary>Field number for the "required_string_piece" field.</summary>
    public const int RequiredStringPieceFieldNumber = 24;
    private readonly static string RequiredStringPieceDefaultValue = "";

    private string requiredStringPiece_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string RequiredStringPiece {
      get { return requiredStringPiece_ ?? RequiredStringPieceDefaultValue; }
      set {
        requiredStringPiece_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "required_string_piece" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredStringPiece {
      get { return requiredStringPiece_ != null; }
    }
    /// <summary>Clears the value of the "required_string_piece" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredStringPiece() {
      requiredStringPiece_ = null;
    }

    /// <summary>Field number for the "required_cord" field.</summary>
    public const int RequiredCordFieldNumber = 25;
    private readonly static string RequiredCordDefaultValue = "";

    private string requiredCord_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string RequiredCord {
      get { return requiredCord_ ?? RequiredCordDefaultValue; }
      set {
        requiredCord_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "required_cord" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasRequiredCord {
      get { return requiredCord_ != null; }
    }
    /// <summary>Clears the value of the "required_cord" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearRequiredCord() {
      requiredCord_ = null;
    }

    /// <summary>Field number for the "recursive_message" field.</summary>
    public const int RecursiveMessageFieldNumber = 27;
    private global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2 recursiveMessage_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2 RecursiveMessage {
      get { return recursiveMessage_; }
      set {
        recursiveMessage_ = value;
      }
    }

    /// <summary>Field number for the "optional_recursive_message" field.</summary>
    public const int OptionalRecursiveMessageFieldNumber = 28;
    private global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2 optionalRecursiveMessage_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2 OptionalRecursiveMessage {
      get { return optionalRecursiveMessage_; }
      set {
        optionalRecursiveMessage_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int DataFieldNumber = 201;
    private global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.Data data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.Data Data {
      get { return data_; }
      set {
        data_ = value;
      }
    }
    /// <summary>Gets whether the data field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasData {
      get { return data_ != null; }
    }
    /// <summary>Clears the value of the data field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearData() {
      data_ = null;
    }

    /// <summary>Field number for the "default_int32" field.</summary>
    public const int DefaultInt32FieldNumber = 241;
    private readonly static int DefaultInt32DefaultValue = -123456789;

    private int defaultInt32_;
    /// <summary>
    /// default values
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int DefaultInt32 {
      get { if ((_hasBits0 & 32768) != 0) { return defaultInt32_; } else { return DefaultInt32DefaultValue; } }
      set {
        _hasBits0 |= 32768;
        defaultInt32_ = value;
      }
    }
    /// <summary>Gets whether the "default_int32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultInt32 {
      get { return (_hasBits0 & 32768) != 0; }
    }
    /// <summary>Clears the value of the "default_int32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultInt32() {
      _hasBits0 &= ~32768;
    }

    /// <summary>Field number for the "default_int64" field.</summary>
    public const int DefaultInt64FieldNumber = 242;
    private readonly static long DefaultInt64DefaultValue = -9123456789123456789L;

    private long defaultInt64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long DefaultInt64 {
      get { if ((_hasBits0 & 65536) != 0) { return defaultInt64_; } else { return DefaultInt64DefaultValue; } }
      set {
        _hasBits0 |= 65536;
        defaultInt64_ = value;
      }
    }
    /// <summary>Gets whether the "default_int64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultInt64 {
      get { return (_hasBits0 & 65536) != 0; }
    }
    /// <summary>Clears the value of the "default_int64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultInt64() {
      _hasBits0 &= ~65536;
    }

    /// <summary>Field number for the "default_uint32" field.</summary>
    public const int DefaultUint32FieldNumber = 243;
    private readonly static uint DefaultUint32DefaultValue = 2123456789;

    private uint defaultUint32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint DefaultUint32 {
      get { if ((_hasBits0 & 131072) != 0) { return defaultUint32_; } else { return DefaultUint32DefaultValue; } }
      set {
        _hasBits0 |= 131072;
        defaultUint32_ = value;
      }
    }
    /// <summary>Gets whether the "default_uint32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultUint32 {
      get { return (_hasBits0 & 131072) != 0; }
    }
    /// <summary>Clears the value of the "default_uint32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultUint32() {
      _hasBits0 &= ~131072;
    }

    /// <summary>Field number for the "default_uint64" field.</summary>
    public const int DefaultUint64FieldNumber = 244;
    private readonly static ulong DefaultUint64DefaultValue = 10123456789123456789UL;

    private ulong defaultUint64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong DefaultUint64 {
      get { if ((_hasBits0 & 262144) != 0) { return defaultUint64_; } else { return DefaultUint64DefaultValue; } }
      set {
        _hasBits0 |= 262144;
        defaultUint64_ = value;
      }
    }
    /// <summary>Gets whether the "default_uint64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultUint64 {
      get { return (_hasBits0 & 262144) != 0; }
    }
    /// <summary>Clears the value of the "default_uint64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultUint64() {
      _hasBits0 &= ~262144;
    }

    /// <summary>Field number for the "default_sint32" field.</summary>
    public const int DefaultSint32FieldNumber = 245;
    private readonly static int DefaultSint32DefaultValue = -123456789;

    private int defaultSint32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int DefaultSint32 {
      get { if ((_hasBits0 & 524288) != 0) { return defaultSint32_; } else { return DefaultSint32DefaultValue; } }
      set {
        _hasBits0 |= 524288;
        defaultSint32_ = value;
      }
    }
    /// <summary>Gets whether the "default_sint32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultSint32 {
      get { return (_hasBits0 & 524288) != 0; }
    }
    /// <summary>Clears the value of the "default_sint32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultSint32() {
      _hasBits0 &= ~524288;
    }

    /// <summary>Field number for the "default_sint64" field.</summary>
    public const int DefaultSint64FieldNumber = 246;
    private readonly static long DefaultSint64DefaultValue = -9123456789123456789L;

    private long defaultSint64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long DefaultSint64 {
      get { if ((_hasBits0 & 1048576) != 0) { return defaultSint64_; } else { return DefaultSint64DefaultValue; } }
      set {
        _hasBits0 |= 1048576;
        defaultSint64_ = value;
      }
    }
    /// <summary>Gets whether the "default_sint64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultSint64 {
      get { return (_hasBits0 & 1048576) != 0; }
    }
    /// <summary>Clears the value of the "default_sint64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultSint64() {
      _hasBits0 &= ~1048576;
    }

    /// <summary>Field number for the "default_fixed32" field.</summary>
    public const int DefaultFixed32FieldNumber = 247;
    private readonly static uint DefaultFixed32DefaultValue = 2123456789;

    private uint defaultFixed32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint DefaultFixed32 {
      get { if ((_hasBits0 & 2097152) != 0) { return defaultFixed32_; } else { return DefaultFixed32DefaultValue; } }
      set {
        _hasBits0 |= 2097152;
        defaultFixed32_ = value;
      }
    }
    /// <summary>Gets whether the "default_fixed32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultFixed32 {
      get { return (_hasBits0 & 2097152) != 0; }
    }
    /// <summary>Clears the value of the "default_fixed32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultFixed32() {
      _hasBits0 &= ~2097152;
    }

    /// <summary>Field number for the "default_fixed64" field.</summary>
    public const int DefaultFixed64FieldNumber = 248;
    private readonly static ulong DefaultFixed64DefaultValue = 10123456789123456789UL;

    private ulong defaultFixed64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong DefaultFixed64 {
      get { if ((_hasBits0 & 4194304) != 0) { return defaultFixed64_; } else { return DefaultFixed64DefaultValue; } }
      set {
        _hasBits0 |= 4194304;
        defaultFixed64_ = value;
      }
    }
    /// <summary>Gets whether the "default_fixed64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultFixed64 {
      get { return (_hasBits0 & 4194304) != 0; }
    }
    /// <summary>Clears the value of the "default_fixed64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultFixed64() {
      _hasBits0 &= ~4194304;
    }

    /// <summary>Field number for the "default_sfixed32" field.</summary>
    public const int DefaultSfixed32FieldNumber = 249;
    private readonly static int DefaultSfixed32DefaultValue = -123456789;

    private int defaultSfixed32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int DefaultSfixed32 {
      get { if ((_hasBits0 & 8388608) != 0) { return defaultSfixed32_; } else { return DefaultSfixed32DefaultValue; } }
      set {
        _hasBits0 |= 8388608;
        defaultSfixed32_ = value;
      }
    }
    /// <summary>Gets whether the "default_sfixed32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultSfixed32 {
      get { return (_hasBits0 & 8388608) != 0; }
    }
    /// <summary>Clears the value of the "default_sfixed32" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultSfixed32() {
      _hasBits0 &= ~8388608;
    }

    /// <summary>Field number for the "default_sfixed64" field.</summary>
    public const int DefaultSfixed64FieldNumber = 250;
    private readonly static long DefaultSfixed64DefaultValue = -9123456789123456789L;

    private long defaultSfixed64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long DefaultSfixed64 {
      get { if ((_hasBits0 & 16777216) != 0) { return defaultSfixed64_; } else { return DefaultSfixed64DefaultValue; } }
      set {
        _hasBits0 |= 16777216;
        defaultSfixed64_ = value;
      }
    }
    /// <summary>Gets whether the "default_sfixed64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultSfixed64 {
      get { return (_hasBits0 & 16777216) != 0; }
    }
    /// <summary>Clears the value of the "default_sfixed64" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultSfixed64() {
      _hasBits0 &= ~16777216;
    }

    /// <summary>Field number for the "default_float" field.</summary>
    public const int DefaultFloatFieldNumber = 251;
    private readonly static float DefaultFloatDefaultValue = 9e+09F;

    private float defaultFloat_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float DefaultFloat {
      get { if ((_hasBits0 & 33554432) != 0) { return defaultFloat_; } else { return DefaultFloatDefaultValue; } }
      set {
        _hasBits0 |= 33554432;
        defaultFloat_ = value;
      }
    }
    /// <summary>Gets whether the "default_float" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultFloat {
      get { return (_hasBits0 & 33554432) != 0; }
    }
    /// <summary>Clears the value of the "default_float" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultFloat() {
      _hasBits0 &= ~33554432;
    }

    /// <summary>Field number for the "default_double" field.</summary>
    public const int DefaultDoubleFieldNumber = 252;
    private readonly static double DefaultDoubleDefaultValue = 7e+22D;

    private double defaultDouble_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double DefaultDouble {
      get { if ((_hasBits0 & 67108864) != 0) { return defaultDouble_; } else { return DefaultDoubleDefaultValue; } }
      set {
        _hasBits0 |= 67108864;
        defaultDouble_ = value;
      }
    }
    /// <summary>Gets whether the "default_double" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultDouble {
      get { return (_hasBits0 & 67108864) != 0; }
    }
    /// <summary>Clears the value of the "default_double" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultDouble() {
      _hasBits0 &= ~67108864;
    }

    /// <summary>Field number for the "default_bool" field.</summary>
    public const int DefaultBoolFieldNumber = 253;
    private readonly static bool DefaultBoolDefaultValue = true;

    private bool defaultBool_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool DefaultBool {
      get { if ((_hasBits0 & 134217728) != 0) { return defaultBool_; } else { return DefaultBoolDefaultValue; } }
      set {
        _hasBits0 |= 134217728;
        defaultBool_ = value;
      }
    }
    /// <summary>Gets whether the "default_bool" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultBool {
      get { return (_hasBits0 & 134217728) != 0; }
    }
    /// <summary>Clears the value of the "default_bool" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultBool() {
      _hasBits0 &= ~134217728;
    }

    /// <summary>Field number for the "default_string" field.</summary>
    public const int DefaultStringFieldNumber = 254;
    private readonly static string DefaultStringDefaultValue = global::System.Text.Encoding.UTF8.GetString(global::System.Convert.FromBase64String("Um9zZWJ1ZA=="), 0, 7);

    private string defaultString_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string DefaultString {
      get { return defaultString_ ?? DefaultStringDefaultValue; }
      set {
        defaultString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "default_string" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultString {
      get { return defaultString_ != null; }
    }
    /// <summary>Clears the value of the "default_string" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultString() {
      defaultString_ = null;
    }

    /// <summary>Field number for the "default_bytes" field.</summary>
    public const int DefaultBytesFieldNumber = 255;
    private readonly static pb::ByteString DefaultBytesDefaultValue = pb::ByteString.FromBase64("am9zaHVh");

    private pb::ByteString defaultBytes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString DefaultBytes {
      get { return defaultBytes_ ?? DefaultBytesDefaultValue; }
      set {
        defaultBytes_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "default_bytes" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDefaultBytes {
      get { return defaultBytes_ != null; }
    }
    /// <summary>Clears the value of the "default_bytes" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDefaultBytes() {
      defaultBytes_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TestAllRequiredTypesProto2);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TestAllRequiredTypesProto2 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (RequiredInt32 != other.RequiredInt32) return false;
      if (RequiredInt64 != other.RequiredInt64) return false;
      if (RequiredUint32 != other.RequiredUint32) return false;
      if (RequiredUint64 != other.RequiredUint64) return false;
      if (RequiredSint32 != other.RequiredSint32) return false;
      if (RequiredSint64 != other.RequiredSint64) return false;
      if (RequiredFixed32 != other.RequiredFixed32) return false;
      if (RequiredFixed64 != other.RequiredFixed64) return false;
      if (RequiredSfixed32 != other.RequiredSfixed32) return false;
      if (RequiredSfixed64 != other.RequiredSfixed64) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(RequiredFloat, other.RequiredFloat)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(RequiredDouble, other.RequiredDouble)) return false;
      if (RequiredBool != other.RequiredBool) return false;
      if (RequiredString != other.RequiredString) return false;
      if (RequiredBytes != other.RequiredBytes) return false;
      if (!object.Equals(RequiredNestedMessage, other.RequiredNestedMessage)) return false;
      if (!object.Equals(RequiredForeignMessage, other.RequiredForeignMessage)) return false;
      if (RequiredNestedEnum != other.RequiredNestedEnum) return false;
      if (RequiredForeignEnum != other.RequiredForeignEnum) return false;
      if (RequiredStringPiece != other.RequiredStringPiece) return false;
      if (RequiredCord != other.RequiredCord) return false;
      if (!object.Equals(RecursiveMessage, other.RecursiveMessage)) return false;
      if (!object.Equals(OptionalRecursiveMessage, other.OptionalRecursiveMessage)) return false;
      if (!object.Equals(Data, other.Data)) return false;
      if (DefaultInt32 != other.DefaultInt32) return false;
      if (DefaultInt64 != other.DefaultInt64) return false;
      if (DefaultUint32 != other.DefaultUint32) return false;
      if (DefaultUint64 != other.DefaultUint64) return false;
      if (DefaultSint32 != other.DefaultSint32) return false;
      if (DefaultSint64 != other.DefaultSint64) return false;
      if (DefaultFixed32 != other.DefaultFixed32) return false;
      if (DefaultFixed64 != other.DefaultFixed64) return false;
      if (DefaultSfixed32 != other.DefaultSfixed32) return false;
      if (DefaultSfixed64 != other.DefaultSfixed64) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(DefaultFloat, other.DefaultFloat)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(DefaultDouble, other.DefaultDouble)) return false;
      if (DefaultBool != other.DefaultBool) return false;
      if (DefaultString != other.DefaultString) return false;
      if (DefaultBytes != other.DefaultBytes) return false;
      if (!Equals(_extensions, other._extensions)) {
        return false;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasRequiredInt32) hash ^= RequiredInt32.GetHashCode();
      if (HasRequiredInt64) hash ^= RequiredInt64.GetHashCode();
      if (HasRequiredUint32) hash ^= RequiredUint32.GetHashCode();
      if (HasRequiredUint64) hash ^= RequiredUint64.GetHashCode();
      if (HasRequiredSint32) hash ^= RequiredSint32.GetHashCode();
      if (HasRequiredSint64) hash ^= RequiredSint64.GetHashCode();
      if (HasRequiredFixed32) hash ^= RequiredFixed32.GetHashCode();
      if (HasRequiredFixed64) hash ^= RequiredFixed64.GetHashCode();
      if (HasRequiredSfixed32) hash ^= RequiredSfixed32.GetHashCode();
      if (HasRequiredSfixed64) hash ^= RequiredSfixed64.GetHashCode();
      if (HasRequiredFloat) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(RequiredFloat);
      if (HasRequiredDouble) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(RequiredDouble);
      if (HasRequiredBool) hash ^= RequiredBool.GetHashCode();
      if (HasRequiredString) hash ^= RequiredString.GetHashCode();
      if (HasRequiredBytes) hash ^= RequiredBytes.GetHashCode();
      if (requiredNestedMessage_ != null) hash ^= RequiredNestedMessage.GetHashCode();
      if (requiredForeignMessage_ != null) hash ^= RequiredForeignMessage.GetHashCode();
      if (HasRequiredNestedEnum) hash ^= RequiredNestedEnum.GetHashCode();
      if (HasRequiredForeignEnum) hash ^= RequiredForeignEnum.GetHashCode();
      if (HasRequiredStringPiece) hash ^= RequiredStringPiece.GetHashCode();
      if (HasRequiredCord) hash ^= RequiredCord.GetHashCode();
      if (recursiveMessage_ != null) hash ^= RecursiveMessage.GetHashCode();
      if (optionalRecursiveMessage_ != null) hash ^= OptionalRecursiveMessage.GetHashCode();
      if (HasData) hash ^= Data.GetHashCode();
      if (HasDefaultInt32) hash ^= DefaultInt32.GetHashCode();
      if (HasDefaultInt64) hash ^= DefaultInt64.GetHashCode();
      if (HasDefaultUint32) hash ^= DefaultUint32.GetHashCode();
      if (HasDefaultUint64) hash ^= DefaultUint64.GetHashCode();
      if (HasDefaultSint32) hash ^= DefaultSint32.GetHashCode();
      if (HasDefaultSint64) hash ^= DefaultSint64.GetHashCode();
      if (HasDefaultFixed32) hash ^= DefaultFixed32.GetHashCode();
      if (HasDefaultFixed64) hash ^= DefaultFixed64.GetHashCode();
      if (HasDefaultSfixed32) hash ^= DefaultSfixed32.GetHashCode();
      if (HasDefaultSfixed64) hash ^= DefaultSfixed64.GetHashCode();
      if (HasDefaultFloat) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(DefaultFloat);
      if (HasDefaultDouble) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(DefaultDouble);
      if (HasDefaultBool) hash ^= DefaultBool.GetHashCode();
      if (HasDefaultString) hash ^= DefaultString.GetHashCode();
      if (HasDefaultBytes) hash ^= DefaultBytes.GetHashCode();
      if (_extensions != null) {
        hash ^= _extensions.GetHashCode();
      }
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasRequiredInt32) {
        output.WriteRawTag(8);
        output.WriteInt32(RequiredInt32);
      }
      if (HasRequiredInt64) {
        output.WriteRawTag(16);
        output.WriteInt64(RequiredInt64);
      }
      if (HasRequiredUint32) {
        output.WriteRawTag(24);
        output.WriteUInt32(RequiredUint32);
      }
      if (HasRequiredUint64) {
        output.WriteRawTag(32);
        output.WriteUInt64(RequiredUint64);
      }
      if (HasRequiredSint32) {
        output.WriteRawTag(40);
        output.WriteSInt32(RequiredSint32);
      }
      if (HasRequiredSint64) {
        output.WriteRawTag(48);
        output.WriteSInt64(RequiredSint64);
      }
      if (HasRequiredFixed32) {
        output.WriteRawTag(61);
        output.WriteFixed32(RequiredFixed32);
      }
      if (HasRequiredFixed64) {
        output.WriteRawTag(65);
        output.WriteFixed64(RequiredFixed64);
      }
      if (HasRequiredSfixed32) {
        output.WriteRawTag(77);
        output.WriteSFixed32(RequiredSfixed32);
      }
      if (HasRequiredSfixed64) {
        output.WriteRawTag(81);
        output.WriteSFixed64(RequiredSfixed64);
      }
      if (HasRequiredFloat) {
        output.WriteRawTag(93);
        output.WriteFloat(RequiredFloat);
      }
      if (HasRequiredDouble) {
        output.WriteRawTag(97);
        output.WriteDouble(RequiredDouble);
      }
      if (HasRequiredBool) {
        output.WriteRawTag(104);
        output.WriteBool(RequiredBool);
      }
      if (HasRequiredString) {
        output.WriteRawTag(114);
        output.WriteString(RequiredString);
      }
      if (HasRequiredBytes) {
        output.WriteRawTag(122);
        output.WriteBytes(RequiredBytes);
      }
      if (requiredNestedMessage_ != null) {
        output.WriteRawTag(146, 1);
        output.WriteMessage(RequiredNestedMessage);
      }
      if (requiredForeignMessage_ != null) {
        output.WriteRawTag(154, 1);
        output.WriteMessage(RequiredForeignMessage);
      }
      if (HasRequiredNestedEnum) {
        output.WriteRawTag(168, 1);
        output.WriteEnum((int) RequiredNestedEnum);
      }
      if (HasRequiredForeignEnum) {
        output.WriteRawTag(176, 1);
        output.WriteEnum((int) RequiredForeignEnum);
      }
      if (HasRequiredStringPiece) {
        output.WriteRawTag(194, 1);
        output.WriteString(RequiredStringPiece);
      }
      if (HasRequiredCord) {
        output.WriteRawTag(202, 1);
        output.WriteString(RequiredCord);
      }
      if (recursiveMessage_ != null) {
        output.WriteRawTag(218, 1);
        output.WriteMessage(RecursiveMessage);
      }
      if (optionalRecursiveMessage_ != null) {
        output.WriteRawTag(226, 1);
        output.WriteMessage(OptionalRecursiveMessage);
      }
      if (HasData) {
        output.WriteRawTag(203, 12);
        output.WriteGroup(Data);
        output.WriteRawTag(204, 12);
      }
      if (HasDefaultInt32) {
        output.WriteRawTag(136, 15);
        output.WriteInt32(DefaultInt32);
      }
      if (HasDefaultInt64) {
        output.WriteRawTag(144, 15);
        output.WriteInt64(DefaultInt64);
      }
      if (HasDefaultUint32) {
        output.WriteRawTag(152, 15);
        output.WriteUInt32(DefaultUint32);
      }
      if (HasDefaultUint64) {
        output.WriteRawTag(160, 15);
        output.WriteUInt64(DefaultUint64);
      }
      if (HasDefaultSint32) {
        output.WriteRawTag(168, 15);
        output.WriteSInt32(DefaultSint32);
      }
      if (HasDefaultSint64) {
        output.WriteRawTag(176, 15);
        output.WriteSInt64(DefaultSint64);
      }
      if (HasDefaultFixed32) {
        output.WriteRawTag(189, 15);
        output.WriteFixed32(DefaultFixed32);
      }
      if (HasDefaultFixed64) {
        output.WriteRawTag(193, 15);
        output.WriteFixed64(DefaultFixed64);
      }
      if (HasDefaultSfixed32) {
        output.WriteRawTag(205, 15);
        output.WriteSFixed32(DefaultSfixed32);
      }
      if (HasDefaultSfixed64) {
        output.WriteRawTag(209, 15);
        output.WriteSFixed64(DefaultSfixed64);
      }
      if (HasDefaultFloat) {
        output.WriteRawTag(221, 15);
        output.WriteFloat(DefaultFloat);
      }
      if (HasDefaultDouble) {
        output.WriteRawTag(225, 15);
        output.WriteDouble(DefaultDouble);
      }
      if (HasDefaultBool) {
        output.WriteRawTag(232, 15);
        output.WriteBool(DefaultBool);
      }
      if (HasDefaultString) {
        output.WriteRawTag(242, 15);
        output.WriteString(DefaultString);
      }
      if (HasDefaultBytes) {
        output.WriteRawTag(250, 15);
        output.WriteBytes(DefaultBytes);
      }
      if (_extensions != null) {
        _extensions.WriteTo(output);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasRequiredInt32) {
        output.WriteRawTag(8);
        output.WriteInt32(RequiredInt32);
      }
      if (HasRequiredInt64) {
        output.WriteRawTag(16);
        output.WriteInt64(RequiredInt64);
      }
      if (HasRequiredUint32) {
        output.WriteRawTag(24);
        output.WriteUInt32(RequiredUint32);
      }
      if (HasRequiredUint64) {
        output.WriteRawTag(32);
        output.WriteUInt64(RequiredUint64);
      }
      if (HasRequiredSint32) {
        output.WriteRawTag(40);
        output.WriteSInt32(RequiredSint32);
      }
      if (HasRequiredSint64) {
        output.WriteRawTag(48);
        output.WriteSInt64(RequiredSint64);
      }
      if (HasRequiredFixed32) {
        output.WriteRawTag(61);
        output.WriteFixed32(RequiredFixed32);
      }
      if (HasRequiredFixed64) {
        output.WriteRawTag(65);
        output.WriteFixed64(RequiredFixed64);
      }
      if (HasRequiredSfixed32) {
        output.WriteRawTag(77);
        output.WriteSFixed32(RequiredSfixed32);
      }
      if (HasRequiredSfixed64) {
        output.WriteRawTag(81);
        output.WriteSFixed64(RequiredSfixed64);
      }
      if (HasRequiredFloat) {
        output.WriteRawTag(93);
        output.WriteFloat(RequiredFloat);
      }
      if (HasRequiredDouble) {
        output.WriteRawTag(97);
        output.WriteDouble(RequiredDouble);
      }
      if (HasRequiredBool) {
        output.WriteRawTag(104);
        output.WriteBool(RequiredBool);
      }
      if (HasRequiredString) {
        output.WriteRawTag(114);
        output.WriteString(RequiredString);
      }
      if (HasRequiredBytes) {
        output.WriteRawTag(122);
        output.WriteBytes(RequiredBytes);
      }
      if (requiredNestedMessage_ != null) {
        output.WriteRawTag(146, 1);
        output.WriteMessage(RequiredNestedMessage);
      }
      if (requiredForeignMessage_ != null) {
        output.WriteRawTag(154, 1);
        output.WriteMessage(RequiredForeignMessage);
      }
      if (HasRequiredNestedEnum) {
        output.WriteRawTag(168, 1);
        output.WriteEnum((int) RequiredNestedEnum);
      }
      if (HasRequiredForeignEnum) {
        output.WriteRawTag(176, 1);
        output.WriteEnum((int) RequiredForeignEnum);
      }
      if (HasRequiredStringPiece) {
        output.WriteRawTag(194, 1);
        output.WriteString(RequiredStringPiece);
      }
      if (HasRequiredCord) {
        output.WriteRawTag(202, 1);
        output.WriteString(RequiredCord);
      }
      if (recursiveMessage_ != null) {
        output.WriteRawTag(218, 1);
        output.WriteMessage(RecursiveMessage);
      }
      if (optionalRecursiveMessage_ != null) {
        output.WriteRawTag(226, 1);
        output.WriteMessage(OptionalRecursiveMessage);
      }
      if (HasData) {
        output.WriteRawTag(203, 12);
        output.WriteGroup(Data);
        output.WriteRawTag(204, 12);
      }
      if (HasDefaultInt32) {
        output.WriteRawTag(136, 15);
        output.WriteInt32(DefaultInt32);
      }
      if (HasDefaultInt64) {
        output.WriteRawTag(144, 15);
        output.WriteInt64(DefaultInt64);
      }
      if (HasDefaultUint32) {
        output.WriteRawTag(152, 15);
        output.WriteUInt32(DefaultUint32);
      }
      if (HasDefaultUint64) {
        output.WriteRawTag(160, 15);
        output.WriteUInt64(DefaultUint64);
      }
      if (HasDefaultSint32) {
        output.WriteRawTag(168, 15);
        output.WriteSInt32(DefaultSint32);
      }
      if (HasDefaultSint64) {
        output.WriteRawTag(176, 15);
        output.WriteSInt64(DefaultSint64);
      }
      if (HasDefaultFixed32) {
        output.WriteRawTag(189, 15);
        output.WriteFixed32(DefaultFixed32);
      }
      if (HasDefaultFixed64) {
        output.WriteRawTag(193, 15);
        output.WriteFixed64(DefaultFixed64);
      }
      if (HasDefaultSfixed32) {
        output.WriteRawTag(205, 15);
        output.WriteSFixed32(DefaultSfixed32);
      }
      if (HasDefaultSfixed64) {
        output.WriteRawTag(209, 15);
        output.WriteSFixed64(DefaultSfixed64);
      }
      if (HasDefaultFloat) {
        output.WriteRawTag(221, 15);
        output.WriteFloat(DefaultFloat);
      }
      if (HasDefaultDouble) {
        output.WriteRawTag(225, 15);
        output.WriteDouble(DefaultDouble);
      }
      if (HasDefaultBool) {
        output.WriteRawTag(232, 15);
        output.WriteBool(DefaultBool);
      }
      if (HasDefaultString) {
        output.WriteRawTag(242, 15);
        output.WriteString(DefaultString);
      }
      if (HasDefaultBytes) {
        output.WriteRawTag(250, 15);
        output.WriteBytes(DefaultBytes);
      }
      if (_extensions != null) {
        _extensions.WriteTo(ref output);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasRequiredInt32) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RequiredInt32);
      }
      if (HasRequiredInt64) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(RequiredInt64);
      }
      if (HasRequiredUint32) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(RequiredUint32);
      }
      if (HasRequiredUint64) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(RequiredUint64);
      }
      if (HasRequiredSint32) {
        size += 1 + pb::CodedOutputStream.ComputeSInt32Size(RequiredSint32);
      }
      if (HasRequiredSint64) {
        size += 1 + pb::CodedOutputStream.ComputeSInt64Size(RequiredSint64);
      }
      if (HasRequiredFixed32) {
        size += 1 + 4;
      }
      if (HasRequiredFixed64) {
        size += 1 + 8;
      }
      if (HasRequiredSfixed32) {
        size += 1 + 4;
      }
      if (HasRequiredSfixed64) {
        size += 1 + 8;
      }
      if (HasRequiredFloat) {
        size += 1 + 4;
      }
      if (HasRequiredDouble) {
        size += 1 + 8;
      }
      if (HasRequiredBool) {
        size += 1 + 1;
      }
      if (HasRequiredString) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(RequiredString);
      }
      if (HasRequiredBytes) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(RequiredBytes);
      }
      if (requiredNestedMessage_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(RequiredNestedMessage);
      }
      if (requiredForeignMessage_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(RequiredForeignMessage);
      }
      if (HasRequiredNestedEnum) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) RequiredNestedEnum);
      }
      if (HasRequiredForeignEnum) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) RequiredForeignEnum);
      }
      if (HasRequiredStringPiece) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(RequiredStringPiece);
      }
      if (HasRequiredCord) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(RequiredCord);
      }
      if (recursiveMessage_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(RecursiveMessage);
      }
      if (optionalRecursiveMessage_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(OptionalRecursiveMessage);
      }
      if (HasData) {
        size += 4 + pb::CodedOutputStream.ComputeGroupSize(Data);
      }
      if (HasDefaultInt32) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(DefaultInt32);
      }
      if (HasDefaultInt64) {
        size += 2 + pb::CodedOutputStream.ComputeInt64Size(DefaultInt64);
      }
      if (HasDefaultUint32) {
        size += 2 + pb::CodedOutputStream.ComputeUInt32Size(DefaultUint32);
      }
      if (HasDefaultUint64) {
        size += 2 + pb::CodedOutputStream.ComputeUInt64Size(DefaultUint64);
      }
      if (HasDefaultSint32) {
        size += 2 + pb::CodedOutputStream.ComputeSInt32Size(DefaultSint32);
      }
      if (HasDefaultSint64) {
        size += 2 + pb::CodedOutputStream.ComputeSInt64Size(DefaultSint64);
      }
      if (HasDefaultFixed32) {
        size += 2 + 4;
      }
      if (HasDefaultFixed64) {
        size += 2 + 8;
      }
      if (HasDefaultSfixed32) {
        size += 2 + 4;
      }
      if (HasDefaultSfixed64) {
        size += 2 + 8;
      }
      if (HasDefaultFloat) {
        size += 2 + 4;
      }
      if (HasDefaultDouble) {
        size += 2 + 8;
      }
      if (HasDefaultBool) {
        size += 2 + 1;
      }
      if (HasDefaultString) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(DefaultString);
      }
      if (HasDefaultBytes) {
        size += 2 + pb::CodedOutputStream.ComputeBytesSize(DefaultBytes);
      }
      if (_extensions != null) {
        size += _extensions.CalculateSize();
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TestAllRequiredTypesProto2 other) {
      if (other == null) {
        return;
      }
      if (other.HasRequiredInt32) {
        RequiredInt32 = other.RequiredInt32;
      }
      if (other.HasRequiredInt64) {
        RequiredInt64 = other.RequiredInt64;
      }
      if (other.HasRequiredUint32) {
        RequiredUint32 = other.RequiredUint32;
      }
      if (other.HasRequiredUint64) {
        RequiredUint64 = other.RequiredUint64;
      }
      if (other.HasRequiredSint32) {
        RequiredSint32 = other.RequiredSint32;
      }
      if (other.HasRequiredSint64) {
        RequiredSint64 = other.RequiredSint64;
      }
      if (other.HasRequiredFixed32) {
        RequiredFixed32 = other.RequiredFixed32;
      }
      if (other.HasRequiredFixed64) {
        RequiredFixed64 = other.RequiredFixed64;
      }
      if (other.HasRequiredSfixed32) {
        RequiredSfixed32 = other.RequiredSfixed32;
      }
      if (other.HasRequiredSfixed64) {
        RequiredSfixed64 = other.RequiredSfixed64;
      }
      if (other.HasRequiredFloat) {
        RequiredFloat = other.RequiredFloat;
      }
      if (other.HasRequiredDouble) {
        RequiredDouble = other.RequiredDouble;
      }
      if (other.HasRequiredBool) {
        RequiredBool = other.RequiredBool;
      }
      if (other.HasRequiredString) {
        RequiredString = other.RequiredString;
      }
      if (other.HasRequiredBytes) {
        RequiredBytes = other.RequiredBytes;
      }
      if (other.requiredNestedMessage_ != null) {
        if (requiredNestedMessage_ == null) {
          RequiredNestedMessage = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedMessage();
        }
        RequiredNestedMessage.MergeFrom(other.RequiredNestedMessage);
      }
      if (other.requiredForeignMessage_ != null) {
        if (requiredForeignMessage_ == null) {
          RequiredForeignMessage = new global::ProtobufTestMessages.Proto2.ForeignMessageProto2();
        }
        RequiredForeignMessage.MergeFrom(other.RequiredForeignMessage);
      }
      if (other.HasRequiredNestedEnum) {
        RequiredNestedEnum = other.RequiredNestedEnum;
      }
      if (other.HasRequiredForeignEnum) {
        RequiredForeignEnum = other.RequiredForeignEnum;
      }
      if (other.HasRequiredStringPiece) {
        RequiredStringPiece = other.RequiredStringPiece;
      }
      if (other.HasRequiredCord) {
        RequiredCord = other.RequiredCord;
      }
      if (other.recursiveMessage_ != null) {
        if (recursiveMessage_ == null) {
          RecursiveMessage = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2();
        }
        RecursiveMessage.MergeFrom(other.RecursiveMessage);
      }
      if (other.optionalRecursiveMessage_ != null) {
        if (optionalRecursiveMessage_ == null) {
          OptionalRecursiveMessage = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2();
        }
        OptionalRecursiveMessage.MergeFrom(other.OptionalRecursiveMessage);
      }
      if (other.HasData) {
        if (!HasData) {
          Data = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.Data();
        }
        Data.MergeFrom(other.Data);
      }
      if (other.HasDefaultInt32) {
        DefaultInt32 = other.DefaultInt32;
      }
      if (other.HasDefaultInt64) {
        DefaultInt64 = other.DefaultInt64;
      }
      if (other.HasDefaultUint32) {
        DefaultUint32 = other.DefaultUint32;
      }
      if (other.HasDefaultUint64) {
        DefaultUint64 = other.DefaultUint64;
      }
      if (other.HasDefaultSint32) {
        DefaultSint32 = other.DefaultSint32;
      }
      if (other.HasDefaultSint64) {
        DefaultSint64 = other.DefaultSint64;
      }
      if (other.HasDefaultFixed32) {
        DefaultFixed32 = other.DefaultFixed32;
      }
      if (other.HasDefaultFixed64) {
        DefaultFixed64 = other.DefaultFixed64;
      }
      if (other.HasDefaultSfixed32) {
        DefaultSfixed32 = other.DefaultSfixed32;
      }
      if (other.HasDefaultSfixed64) {
        DefaultSfixed64 = other.DefaultSfixed64;
      }
      if (other.HasDefaultFloat) {
        DefaultFloat = other.DefaultFloat;
      }
      if (other.HasDefaultDouble) {
        DefaultDouble = other.DefaultDouble;
      }
      if (other.HasDefaultBool) {
        DefaultBool = other.DefaultBool;
      }
      if (other.HasDefaultString) {
        DefaultString = other.DefaultString;
      }
      if (other.HasDefaultBytes) {
        DefaultBytes = other.DefaultBytes;
      }
      pb::ExtensionSet.MergeFrom(ref _extensions, other._extensions);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            if (!pb::ExtensionSet.TryMergeFieldFrom(ref _extensions, input)) {
              _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            }
            break;
          case 8: {
            RequiredInt32 = input.ReadInt32();
            break;
          }
          case 16: {
            RequiredInt64 = input.ReadInt64();
            break;
          }
          case 24: {
            RequiredUint32 = input.ReadUInt32();
            break;
          }
          case 32: {
            RequiredUint64 = input.ReadUInt64();
            break;
          }
          case 40: {
            RequiredSint32 = input.ReadSInt32();
            break;
          }
          case 48: {
            RequiredSint64 = input.ReadSInt64();
            break;
          }
          case 61: {
            RequiredFixed32 = input.ReadFixed32();
            break;
          }
          case 65: {
            RequiredFixed64 = input.ReadFixed64();
            break;
          }
          case 77: {
            RequiredSfixed32 = input.ReadSFixed32();
            break;
          }
          case 81: {
            RequiredSfixed64 = input.ReadSFixed64();
            break;
          }
          case 93: {
            RequiredFloat = input.ReadFloat();
            break;
          }
          case 97: {
            RequiredDouble = input.ReadDouble();
            break;
          }
          case 104: {
            RequiredBool = input.ReadBool();
            break;
          }
          case 114: {
            RequiredString = input.ReadString();
            break;
          }
          case 122: {
            RequiredBytes = input.ReadBytes();
            break;
          }
          case 146: {
            if (requiredNestedMessage_ == null) {
              RequiredNestedMessage = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedMessage();
            }
            input.ReadMessage(RequiredNestedMessage);
            break;
          }
          case 154: {
            if (requiredForeignMessage_ == null) {
              RequiredForeignMessage = new global::ProtobufTestMessages.Proto2.ForeignMessageProto2();
            }
            input.ReadMessage(RequiredForeignMessage);
            break;
          }
          case 168: {
            RequiredNestedEnum = (global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedEnum) input.ReadEnum();
            break;
          }
          case 176: {
            RequiredForeignEnum = (global::ProtobufTestMessages.Proto2.ForeignEnumProto2) input.ReadEnum();
            break;
          }
          case 194: {
            RequiredStringPiece = input.ReadString();
            break;
          }
          case 202: {
            RequiredCord = input.ReadString();
            break;
          }
          case 218: {
            if (recursiveMessage_ == null) {
              RecursiveMessage = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2();
            }
            input.ReadMessage(RecursiveMessage);
            break;
          }
          case 226: {
            if (optionalRecursiveMessage_ == null) {
              OptionalRecursiveMessage = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2();
            }
            input.ReadMessage(OptionalRecursiveMessage);
            break;
          }
          case 1611: {
            if (!HasData) {
              Data = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.Data();
            }
            input.ReadGroup(Data);
            break;
          }
          case 1928: {
            DefaultInt32 = input.ReadInt32();
            break;
          }
          case 1936: {
            DefaultInt64 = input.ReadInt64();
            break;
          }
          case 1944: {
            DefaultUint32 = input.ReadUInt32();
            break;
          }
          case 1952: {
            DefaultUint64 = input.ReadUInt64();
            break;
          }
          case 1960: {
            DefaultSint32 = input.ReadSInt32();
            break;
          }
          case 1968: {
            DefaultSint64 = input.ReadSInt64();
            break;
          }
          case 1981: {
            DefaultFixed32 = input.ReadFixed32();
            break;
          }
          case 1985: {
            DefaultFixed64 = input.ReadFixed64();
            break;
          }
          case 1997: {
            DefaultSfixed32 = input.ReadSFixed32();
            break;
          }
          case 2001: {
            DefaultSfixed64 = input.ReadSFixed64();
            break;
          }
          case 2013: {
            DefaultFloat = input.ReadFloat();
            break;
          }
          case 2017: {
            DefaultDouble = input.ReadDouble();
            break;
          }
          case 2024: {
            DefaultBool = input.ReadBool();
            break;
          }
          case 2034: {
            DefaultString = input.ReadString();
            break;
          }
          case 2042: {
            DefaultBytes = input.ReadBytes();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            if (!pb::ExtensionSet.TryMergeFieldFrom(ref _extensions, ref input)) {
              _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            }
            break;
          case 8: {
            RequiredInt32 = input.ReadInt32();
            break;
          }
          case 16: {
            RequiredInt64 = input.ReadInt64();
            break;
          }
          case 24: {
            RequiredUint32 = input.ReadUInt32();
            break;
          }
          case 32: {
            RequiredUint64 = input.ReadUInt64();
            break;
          }
          case 40: {
            RequiredSint32 = input.ReadSInt32();
            break;
          }
          case 48: {
            RequiredSint64 = input.ReadSInt64();
            break;
          }
          case 61: {
            RequiredFixed32 = input.ReadFixed32();
            break;
          }
          case 65: {
            RequiredFixed64 = input.ReadFixed64();
            break;
          }
          case 77: {
            RequiredSfixed32 = input.ReadSFixed32();
            break;
          }
          case 81: {
            RequiredSfixed64 = input.ReadSFixed64();
            break;
          }
          case 93: {
            RequiredFloat = input.ReadFloat();
            break;
          }
          case 97: {
            RequiredDouble = input.ReadDouble();
            break;
          }
          case 104: {
            RequiredBool = input.ReadBool();
            break;
          }
          case 114: {
            RequiredString = input.ReadString();
            break;
          }
          case 122: {
            RequiredBytes = input.ReadBytes();
            break;
          }
          case 146: {
            if (requiredNestedMessage_ == null) {
              RequiredNestedMessage = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedMessage();
            }
            input.ReadMessage(RequiredNestedMessage);
            break;
          }
          case 154: {
            if (requiredForeignMessage_ == null) {
              RequiredForeignMessage = new global::ProtobufTestMessages.Proto2.ForeignMessageProto2();
            }
            input.ReadMessage(RequiredForeignMessage);
            break;
          }
          case 168: {
            RequiredNestedEnum = (global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.NestedEnum) input.ReadEnum();
            break;
          }
          case 176: {
            RequiredForeignEnum = (global::ProtobufTestMessages.Proto2.ForeignEnumProto2) input.ReadEnum();
            break;
          }
          case 194: {
            RequiredStringPiece = input.ReadString();
            break;
          }
          case 202: {
            RequiredCord = input.ReadString();
            break;
          }
          case 218: {
            if (recursiveMessage_ == null) {
              RecursiveMessage = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2();
            }
            input.ReadMessage(RecursiveMessage);
            break;
          }
          case 226: {
            if (optionalRecursiveMessage_ == null) {
              OptionalRecursiveMessage = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2();
            }
            input.ReadMessage(OptionalRecursiveMessage);
            break;
          }
          case 1611: {
            if (!HasData) {
              Data = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.Data();
            }
            input.ReadGroup(Data);
            break;
          }
          case 1928: {
            DefaultInt32 = input.ReadInt32();
            break;
          }
          case 1936: {
            DefaultInt64 = input.ReadInt64();
            break;
          }
          case 1944: {
            DefaultUint32 = input.ReadUInt32();
            break;
          }
          case 1952: {
            DefaultUint64 = input.ReadUInt64();
            break;
          }
          case 1960: {
            DefaultSint32 = input.ReadSInt32();
            break;
          }
          case 1968: {
            DefaultSint64 = input.ReadSInt64();
            break;
          }
          case 1981: {
            DefaultFixed32 = input.ReadFixed32();
            break;
          }
          case 1985: {
            DefaultFixed64 = input.ReadFixed64();
            break;
          }
          case 1997: {
            DefaultSfixed32 = input.ReadSFixed32();
            break;
          }
          case 2001: {
            DefaultSfixed64 = input.ReadSFixed64();
            break;
          }
          case 2013: {
            DefaultFloat = input.ReadFloat();
            break;
          }
          case 2017: {
            DefaultDouble = input.ReadDouble();
            break;
          }
          case 2024: {
            DefaultBool = input.ReadBool();
            break;
          }
          case 2034: {
            DefaultString = input.ReadString();
            break;
          }
          case 2042: {
            DefaultBytes = input.ReadBytes();
            break;
          }
        }
      }
    }
    #endif

    public TValue GetExtension<TValue>(pb::Extension<TestAllRequiredTypesProto2, TValue> extension) {
      return pb::ExtensionSet.Get(ref _extensions, extension);
    }
    public pbc::RepeatedField<TValue> GetExtension<TValue>(pb::RepeatedExtension<TestAllRequiredTypesProto2, TValue> extension) {
      return pb::ExtensionSet.Get(ref _extensions, extension);
    }
    public pbc::RepeatedField<TValue> GetOrInitializeExtension<TValue>(pb::RepeatedExtension<TestAllRequiredTypesProto2, TValue> extension) {
      return pb::ExtensionSet.GetOrInitialize(ref _extensions, extension);
    }
    public void SetExtension<TValue>(pb::Extension<TestAllRequiredTypesProto2, TValue> extension, TValue value) {
      pb::ExtensionSet.Set(ref _extensions, extension, value);
    }
    public bool HasExtension<TValue>(pb::Extension<TestAllRequiredTypesProto2, TValue> extension) {
      return pb::ExtensionSet.Has(ref _extensions, extension);
    }
    public void ClearExtension<TValue>(pb::Extension<TestAllRequiredTypesProto2, TValue> extension) {
      pb::ExtensionSet.Clear(ref _extensions, extension);
    }
    public void ClearExtension<TValue>(pb::RepeatedExtension<TestAllRequiredTypesProto2, TValue> extension) {
      pb::ExtensionSet.Clear(ref _extensions, extension);
    }

    #region Nested types
    /// <summary>Container for nested types declared in the TestAllRequiredTypesProto2 message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      public enum NestedEnum {
        [pbr::OriginalName("FOO")] Foo = 0,
        [pbr::OriginalName("BAR")] Bar = 1,
        [pbr::OriginalName("BAZ")] Baz = 2,
        /// <summary>
        /// Intentionally negative.
        /// </summary>
        [pbr::OriginalName("NEG")] Neg = -1,
      }

      public sealed partial class NestedMessage : pb::IMessage<NestedMessage>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<NestedMessage> _parser = new pb::MessageParser<NestedMessage>(() => new NestedMessage());
        private pb::UnknownFieldSet _unknownFields;
        private int _hasBits0;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<NestedMessage> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public NestedMessage() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public NestedMessage(NestedMessage other) : this() {
          _hasBits0 = other._hasBits0;
          a_ = other.a_;
          corecursive_ = other.corecursive_ != null ? other.corecursive_.Clone() : null;
          optionalCorecursive_ = other.optionalCorecursive_ != null ? other.optionalCorecursive_.Clone() : null;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public NestedMessage Clone() {
          return new NestedMessage(this);
        }

        /// <summary>Field number for the "a" field.</summary>
        public const int AFieldNumber = 1;
        private readonly static int ADefaultValue = 0;

        private int a_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int A {
          get { if ((_hasBits0 & 1) != 0) { return a_; } else { return ADefaultValue; } }
          set {
            _hasBits0 |= 1;
            a_ = value;
          }
        }
        /// <summary>Gets whether the "a" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasA {
          get { return (_hasBits0 & 1) != 0; }
        }
        /// <summary>Clears the value of the "a" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearA() {
          _hasBits0 &= ~1;
        }

        /// <summary>Field number for the "corecursive" field.</summary>
        public const int CorecursiveFieldNumber = 2;
        private global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2 corecursive_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2 Corecursive {
          get { return corecursive_; }
          set {
            corecursive_ = value;
          }
        }

        /// <summary>Field number for the "optional_corecursive" field.</summary>
        public const int OptionalCorecursiveFieldNumber = 3;
        private global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2 optionalCorecursive_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2 OptionalCorecursive {
          get { return optionalCorecursive_; }
          set {
            optionalCorecursive_ = value;
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as NestedMessage);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(NestedMessage other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (A != other.A) return false;
          if (!object.Equals(Corecursive, other.Corecursive)) return false;
          if (!object.Equals(OptionalCorecursive, other.OptionalCorecursive)) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (HasA) hash ^= A.GetHashCode();
          if (corecursive_ != null) hash ^= Corecursive.GetHashCode();
          if (optionalCorecursive_ != null) hash ^= OptionalCorecursive.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (HasA) {
            output.WriteRawTag(8);
            output.WriteInt32(A);
          }
          if (corecursive_ != null) {
            output.WriteRawTag(18);
            output.WriteMessage(Corecursive);
          }
          if (optionalCorecursive_ != null) {
            output.WriteRawTag(26);
            output.WriteMessage(OptionalCorecursive);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (HasA) {
            output.WriteRawTag(8);
            output.WriteInt32(A);
          }
          if (corecursive_ != null) {
            output.WriteRawTag(18);
            output.WriteMessage(Corecursive);
          }
          if (optionalCorecursive_ != null) {
            output.WriteRawTag(26);
            output.WriteMessage(OptionalCorecursive);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (HasA) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(A);
          }
          if (corecursive_ != null) {
            size += 1 + pb::CodedOutputStream.ComputeMessageSize(Corecursive);
          }
          if (optionalCorecursive_ != null) {
            size += 1 + pb::CodedOutputStream.ComputeMessageSize(OptionalCorecursive);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(NestedMessage other) {
          if (other == null) {
            return;
          }
          if (other.HasA) {
            A = other.A;
          }
          if (other.corecursive_ != null) {
            if (corecursive_ == null) {
              Corecursive = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2();
            }
            Corecursive.MergeFrom(other.Corecursive);
          }
          if (other.optionalCorecursive_ != null) {
            if (optionalCorecursive_ == null) {
              OptionalCorecursive = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2();
            }
            OptionalCorecursive.MergeFrom(other.OptionalCorecursive);
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 8: {
                A = input.ReadInt32();
                break;
              }
              case 18: {
                if (corecursive_ == null) {
                  Corecursive = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2();
                }
                input.ReadMessage(Corecursive);
                break;
              }
              case 26: {
                if (optionalCorecursive_ == null) {
                  OptionalCorecursive = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2();
                }
                input.ReadMessage(OptionalCorecursive);
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 8: {
                A = input.ReadInt32();
                break;
              }
              case 18: {
                if (corecursive_ == null) {
                  Corecursive = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2();
                }
                input.ReadMessage(Corecursive);
                break;
              }
              case 26: {
                if (optionalCorecursive_ == null) {
                  OptionalCorecursive = new global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2();
                }
                input.ReadMessage(OptionalCorecursive);
                break;
              }
            }
          }
        }
        #endif

      }

      /// <summary>
      /// groups
      /// </summary>
      public sealed partial class Data : pb::IMessage<Data>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<Data> _parser = new pb::MessageParser<Data>(() => new Data());
        private pb::UnknownFieldSet _unknownFields;
        private int _hasBits0;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<Data> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Descriptor.NestedTypes[1]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public Data() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public Data(Data other) : this() {
          _hasBits0 = other._hasBits0;
          groupInt32_ = other.groupInt32_;
          groupUint32_ = other.groupUint32_;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public Data Clone() {
          return new Data(this);
        }

        /// <summary>Field number for the "group_int32" field.</summary>
        public const int GroupInt32FieldNumber = 202;
        private readonly static int GroupInt32DefaultValue = 0;

        private int groupInt32_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int GroupInt32 {
          get { if ((_hasBits0 & 1) != 0) { return groupInt32_; } else { return GroupInt32DefaultValue; } }
          set {
            _hasBits0 |= 1;
            groupInt32_ = value;
          }
        }
        /// <summary>Gets whether the "group_int32" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasGroupInt32 {
          get { return (_hasBits0 & 1) != 0; }
        }
        /// <summary>Clears the value of the "group_int32" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearGroupInt32() {
          _hasBits0 &= ~1;
        }

        /// <summary>Field number for the "group_uint32" field.</summary>
        public const int GroupUint32FieldNumber = 203;
        private readonly static uint GroupUint32DefaultValue = 0;

        private uint groupUint32_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public uint GroupUint32 {
          get { if ((_hasBits0 & 2) != 0) { return groupUint32_; } else { return GroupUint32DefaultValue; } }
          set {
            _hasBits0 |= 2;
            groupUint32_ = value;
          }
        }
        /// <summary>Gets whether the "group_uint32" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasGroupUint32 {
          get { return (_hasBits0 & 2) != 0; }
        }
        /// <summary>Clears the value of the "group_uint32" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearGroupUint32() {
          _hasBits0 &= ~2;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as Data);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(Data other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (GroupInt32 != other.GroupInt32) return false;
          if (GroupUint32 != other.GroupUint32) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (HasGroupInt32) hash ^= GroupInt32.GetHashCode();
          if (HasGroupUint32) hash ^= GroupUint32.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (HasGroupInt32) {
            output.WriteRawTag(208, 12);
            output.WriteInt32(GroupInt32);
          }
          if (HasGroupUint32) {
            output.WriteRawTag(216, 12);
            output.WriteUInt32(GroupUint32);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (HasGroupInt32) {
            output.WriteRawTag(208, 12);
            output.WriteInt32(GroupInt32);
          }
          if (HasGroupUint32) {
            output.WriteRawTag(216, 12);
            output.WriteUInt32(GroupUint32);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (HasGroupInt32) {
            size += 2 + pb::CodedOutputStream.ComputeInt32Size(GroupInt32);
          }
          if (HasGroupUint32) {
            size += 2 + pb::CodedOutputStream.ComputeUInt32Size(GroupUint32);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(Data other) {
          if (other == null) {
            return;
          }
          if (other.HasGroupInt32) {
            GroupInt32 = other.GroupInt32;
          }
          if (other.HasGroupUint32) {
            GroupUint32 = other.GroupUint32;
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              case 1612:
                return;
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 1616: {
                GroupInt32 = input.ReadInt32();
                break;
              }
              case 1624: {
                GroupUint32 = input.ReadUInt32();
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              case 1612:
                return;
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 1616: {
                GroupInt32 = input.ReadInt32();
                break;
              }
              case 1624: {
                GroupUint32 = input.ReadUInt32();
                break;
              }
            }
          }
        }
        #endif

      }

      /// <summary>
      /// message_set test case.
      /// </summary>
      public sealed partial class MessageSetCorrect : pb::IExtendableMessage<MessageSetCorrect>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<MessageSetCorrect> _parser = new pb::MessageParser<MessageSetCorrect>(() => new MessageSetCorrect());
        private pb::UnknownFieldSet _unknownFields;
        private pb::ExtensionSet<MessageSetCorrect> _extensions;
        private pb::ExtensionSet<MessageSetCorrect> _Extensions { get { return _extensions; } }
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<MessageSetCorrect> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Descriptor.NestedTypes[2]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrect() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrect(MessageSetCorrect other) : this() {
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
          _extensions = pb::ExtensionSet.Clone(other._extensions);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrect Clone() {
          return new MessageSetCorrect(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as MessageSetCorrect);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(MessageSetCorrect other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (!Equals(_extensions, other._extensions)) {
            return false;
          }
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (_extensions != null) {
            hash ^= _extensions.GetHashCode();
          }
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (_extensions != null) {
            _extensions.WriteTo(output);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (_extensions != null) {
            _extensions.WriteTo(ref output);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (_extensions != null) {
            size += _extensions.CalculateSize();
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(MessageSetCorrect other) {
          if (other == null) {
            return;
          }
          pb::ExtensionSet.MergeFrom(ref _extensions, other._extensions);
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                if (!pb::ExtensionSet.TryMergeFieldFrom(ref _extensions, input)) {
                  _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                }
                break;
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                if (!pb::ExtensionSet.TryMergeFieldFrom(ref _extensions, ref input)) {
                  _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                }
                break;
            }
          }
        }
        #endif

        public TValue GetExtension<TValue>(pb::Extension<MessageSetCorrect, TValue> extension) {
          return pb::ExtensionSet.Get(ref _extensions, extension);
        }
        public pbc::RepeatedField<TValue> GetExtension<TValue>(pb::RepeatedExtension<MessageSetCorrect, TValue> extension) {
          return pb::ExtensionSet.Get(ref _extensions, extension);
        }
        public pbc::RepeatedField<TValue> GetOrInitializeExtension<TValue>(pb::RepeatedExtension<MessageSetCorrect, TValue> extension) {
          return pb::ExtensionSet.GetOrInitialize(ref _extensions, extension);
        }
        public void SetExtension<TValue>(pb::Extension<MessageSetCorrect, TValue> extension, TValue value) {
          pb::ExtensionSet.Set(ref _extensions, extension, value);
        }
        public bool HasExtension<TValue>(pb::Extension<MessageSetCorrect, TValue> extension) {
          return pb::ExtensionSet.Has(ref _extensions, extension);
        }
        public void ClearExtension<TValue>(pb::Extension<MessageSetCorrect, TValue> extension) {
          pb::ExtensionSet.Clear(ref _extensions, extension);
        }
        public void ClearExtension<TValue>(pb::RepeatedExtension<MessageSetCorrect, TValue> extension) {
          pb::ExtensionSet.Clear(ref _extensions, extension);
        }

      }

      public sealed partial class MessageSetCorrectExtension1 : pb::IMessage<MessageSetCorrectExtension1>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<MessageSetCorrectExtension1> _parser = new pb::MessageParser<MessageSetCorrectExtension1>(() => new MessageSetCorrectExtension1());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<MessageSetCorrectExtension1> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Descriptor.NestedTypes[3]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrectExtension1() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrectExtension1(MessageSetCorrectExtension1 other) : this() {
          str_ = other.str_;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrectExtension1 Clone() {
          return new MessageSetCorrectExtension1(this);
        }

        /// <summary>Field number for the "str" field.</summary>
        public const int StrFieldNumber = 25;
        private readonly static string StrDefaultValue = "";

        private string str_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Str {
          get { return str_ ?? StrDefaultValue; }
          set {
            str_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }
        /// <summary>Gets whether the "str" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasStr {
          get { return str_ != null; }
        }
        /// <summary>Clears the value of the "str" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearStr() {
          str_ = null;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as MessageSetCorrectExtension1);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(MessageSetCorrectExtension1 other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (Str != other.Str) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (HasStr) hash ^= Str.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (HasStr) {
            output.WriteRawTag(202, 1);
            output.WriteString(Str);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (HasStr) {
            output.WriteRawTag(202, 1);
            output.WriteString(Str);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (HasStr) {
            size += 2 + pb::CodedOutputStream.ComputeStringSize(Str);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(MessageSetCorrectExtension1 other) {
          if (other == null) {
            return;
          }
          if (other.HasStr) {
            Str = other.Str;
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 202: {
                Str = input.ReadString();
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 202: {
                Str = input.ReadString();
                break;
              }
            }
          }
        }
        #endif

        #region Extensions
        /// <summary>Container for extensions for other messages declared in the MessageSetCorrectExtension1 message type.</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static partial class Extensions {
          public static readonly pb::Extension<global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrect, global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrectExtension1> MessageSetExtension =
            new pb::Extension<global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrect, global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrectExtension1>(1547769, pb::FieldCodec.ForMessage(12382154, global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrectExtension1.Parser));
        }
        #endregion

      }

      public sealed partial class MessageSetCorrectExtension2 : pb::IMessage<MessageSetCorrectExtension2>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<MessageSetCorrectExtension2> _parser = new pb::MessageParser<MessageSetCorrectExtension2>(() => new MessageSetCorrectExtension2());
        private pb::UnknownFieldSet _unknownFields;
        private int _hasBits0;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<MessageSetCorrectExtension2> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Descriptor.NestedTypes[4]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrectExtension2() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrectExtension2(MessageSetCorrectExtension2 other) : this() {
          _hasBits0 = other._hasBits0;
          i_ = other.i_;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public MessageSetCorrectExtension2 Clone() {
          return new MessageSetCorrectExtension2(this);
        }

        /// <summary>Field number for the "i" field.</summary>
        public const int IFieldNumber = 9;
        private readonly static int IDefaultValue = 0;

        private int i_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int I {
          get { if ((_hasBits0 & 1) != 0) { return i_; } else { return IDefaultValue; } }
          set {
            _hasBits0 |= 1;
            i_ = value;
          }
        }
        /// <summary>Gets whether the "i" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasI {
          get { return (_hasBits0 & 1) != 0; }
        }
        /// <summary>Clears the value of the "i" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearI() {
          _hasBits0 &= ~1;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as MessageSetCorrectExtension2);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(MessageSetCorrectExtension2 other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (I != other.I) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (HasI) hash ^= I.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (HasI) {
            output.WriteRawTag(72);
            output.WriteInt32(I);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (HasI) {
            output.WriteRawTag(72);
            output.WriteInt32(I);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (HasI) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(I);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(MessageSetCorrectExtension2 other) {
          if (other == null) {
            return;
          }
          if (other.HasI) {
            I = other.I;
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 72: {
                I = input.ReadInt32();
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 72: {
                I = input.ReadInt32();
                break;
              }
            }
          }
        }
        #endif

        #region Extensions
        /// <summary>Container for extensions for other messages declared in the MessageSetCorrectExtension2 message type.</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static partial class Extensions {
          public static readonly pb::Extension<global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrect, global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrectExtension2> MessageSetExtension =
            new pb::Extension<global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrect, global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrectExtension2>(4135312, pb::FieldCodec.ForMessage(33082498, global::ProtobufTestMessages.Proto2.TestAllRequiredTypesProto2.Types.MessageSetCorrectExtension2.Parser));
        }
        #endregion

      }

    }
    #endregion

  }

  #endregion

}

#endregion Designer generated code
