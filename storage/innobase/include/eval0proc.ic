/*****************************************************************************

Copyright (c) 1998, 2025, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License, version 2.0, as published by the
Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON><PERSON>ITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0,
for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

*****************************************************************************/

/** @file include/eval0proc.ic
 Executes SQL stored procedures and their control structures

 Created 1/20/1998 Heikki Tuuri
 *******************************************************/

#include "eval0eval.h"
#include "pars0pars.h"
#include "que0que.h"

/** Performs an execution step of a procedure node.
 @return query thread to run next or NULL */
static inline que_thr_t *proc_step(que_thr_t *thr) /*!< in: query thread */
{
  proc_node_t *node;

  ut_ad(thr);

  node = static_cast<proc_node_t *>(thr->run_node);
  ut_ad(que_node_get_type(node) == QUE_NODE_PROC);

  if (thr->prev_node == que_node_get_parent(node)) {
    /* Start execution from the first statement in the statement
    list */

    thr->run_node = node->stat_list;
  } else {
    /* Move to the next statement */
    ut_ad(que_node_get_next(thr->prev_node) == nullptr);

    thr->run_node = nullptr;
  }

  if (thr->run_node == nullptr) {
    thr->run_node = que_node_get_parent(node);
  }

  return (thr);
}

/** Performs an execution step of a procedure call node.
 @return query thread to run next or NULL */
static inline que_thr_t *proc_eval_step(que_thr_t *thr) /*!< in: query thread */
{
  func_node_t *node;

  ut_ad(thr);

  node = static_cast<func_node_t *>(thr->run_node);
  ut_ad(que_node_get_type(node) == QUE_NODE_FUNC);

  /* Evaluate the procedure */

  eval_exp(node);

  thr->run_node = que_node_get_parent(node);

  return (thr);
}
