# MySQL Rest Service schema created.
# MySQL Rest Service, server accounts configured.
# DB `basic_schema` - created
# DB `pk_types_schema` - created
# Registred SERVICE at path: /svc
# Registred DB_SCHEMA at path: /svc/crud_operations
SET @schema_id_crud=@schema_id;
# Registred DB_OBJECT at path: /svc/crud_operations/v1
# Registred DB_OBJECT at path: /svc/crud_operations/t1
# Registred DB_OBJECT at path: /svc/crud_operations/t2
# Registred DB_OBJECT at path: /svc/crud_operations/t3
# Registred DB_OBJECT at path: /svc/crud_operations/t4
# Registred DB_SCHEMA at path: /svc/different_types
SET @schema_id_different_types=@schema_id;
# Registred DB_OBJECT at path: /svc/different_types/all_types
SET @db_object_id_all_types=@db_object_id;
# Registred DB_SCHEMA at path: /svc/different_types_as_pk
SET @schema_id_different_types_as_pk=@schema_id;
# Registred DB_OBJECT at path: /svc/different_types_as_pk/t_int
# Registred DB_OBJECT at path: /svc/different_types_as_pk/t_float
# Registred DB_OBJECT at path: /svc/different_types_as_pk/t_bin
# Registred DB_OBJECT at path: /svc/different_types_as_pk/t_bit
# Registred DB_OBJECT at path: /svc/different_types_as_pk/t_enum
# Registred DB_OBJECT at path: /svc/different_types_as_pk/t_set
# Registred DB_OBJECT at path: /svc/different_types_as_pk/t_timestamp
# Registred DB_OBJECT at path: /svc/different_types_as_pk/t_date
# Registred DB_OBJECT at path: /svc/different_types_as_pk/t_decimal


## I. Show that GET /<service>/<schema>/open-api-catalog/<object> works
##    and lists OpenAPI description for all specified REST Objects.
#
# 1. Check t_decimal REST Objects from different_types_as_pk schema.
#
#
# 2. Check t_set REST Objects from different_types_as_pk schema.
#
#
# 3. Check t_timestamp REST Objects from different_types_as_pk schema.
#
#
# 4. Check t_bin REST Objects from different_types_as_pk schema.
#
#
# 5. Check all_types REST Objects from different_types schema.
#
#
# 6. Check t1 REST Objects from crud_operations schema.
#
#
# 7. Check t2 REST Objects from crud_operations schema.
#
#
# 8. Check t3 REST Objects from crud_operations schema.
#
#
# 9. Check t4 REST Objects from crud_operations schema.
#
#
# 10. Check v1 REST Object from 'view1' view.
#
#

#
# I.1
GET /svc/different_types_as_pk/open-api-catalog/t_decimal
{
    "openapi": "3.1.0",
    "info": {
        "title": "svc OpenAPI specification",
        "version": "1.0.0"
    },
    "paths": {
        "/svc/different_types_as_pk/t_decimal": {
            "get": {
                "summary": "Get t_decimal contents",
                "tags": [
                    "pk_types_schema/t_decimal"
                ],
                "responses": {
                    "200": {
                        "description": "t_decimal contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "array",
                                    "items": {
                                        "$ref": "#/components/schemas/pk_types_schema_t_decimal"
                                    }
                                }
                            }
                        }
                    }
                },
                "parameters": [
                    {
                        "in": "query",
                        "name": "limit",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "offset",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ]
            },
            "delete": {
                "summary": "Delete t_decimal entry",
                "parameters": [
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "pk_types_schema/t_decimal"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "/svc/different_types_as_pk/t_decimal/{id}": {
            "get": {
                "summary": "Get t_decimal contents",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "number"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "pk_types_schema/t_decimal"
                ],
                "responses": {
                    "200": {
                        "description": "t_decimal contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/pk_types_schema_t_decimal"
                                }
                            }
                        }
                    },
                    "404": {
                        "description": "Not found"
                    }
                }
            },
            "delete": {
                "summary": "Delete t_decimal entry",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "number"
                        }
                    }
                ],
                "tags": [
                    "pk_types_schema/t_decimal"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "put": {
                "summary": "Update or create t_decimal entry",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "number"
                        }
                    }
                ],
                "requestBody": {
                    "description": "Item to create or update",
                    "required": true,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/pk_types_schema_t_decimal"
                            }
                        }
                    }
                },
                "tags": [
                    "pk_types_schema/t_decimal"
                ],
                "responses": {
                    "400": {
                        "description": "Invalid input"
                    },
                    "200": {
                        "description": "Item successfully created or updated",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/pk_types_schema_t_decimal"
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "components": {
        "schemas": {
            "pk_types_schema_t_decimal": {
                "type": "object",
                "properties": {
                    "id": {
                        "type": "number",
                        "format": "decimal",
                        "description": "decimal(5,2), Primary Key"
                    }
                }
            }
        }
    }
}
OK

#
# I.2
GET /svc/different_types_as_pk/open-api-catalog/t_set
{
    "openapi": "3.1.0",
    "info": {
        "title": "svc OpenAPI specification",
        "version": "1.0.0"
    },
    "paths": {
        "/svc/different_types_as_pk/t_set": {
            "get": {
                "summary": "Get t_set contents",
                "tags": [
                    "pk_types_schema/t_set"
                ],
                "responses": {
                    "200": {
                        "description": "t_set contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "array",
                                    "items": {
                                        "$ref": "#/components/schemas/pk_types_schema_t_set"
                                    }
                                }
                            }
                        }
                    }
                },
                "parameters": [
                    {
                        "in": "query",
                        "name": "limit",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "offset",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ]
            },
            "delete": {
                "summary": "Delete t_set entry",
                "parameters": [
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "pk_types_schema/t_set"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "/svc/different_types_as_pk/t_set/{id}": {
            "get": {
                "summary": "Get t_set contents",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "pk_types_schema/t_set"
                ],
                "responses": {
                    "200": {
                        "description": "t_set contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/pk_types_schema_t_set"
                                }
                            }
                        }
                    },
                    "404": {
                        "description": "Not found"
                    }
                }
            },
            "delete": {
                "summary": "Delete t_set entry",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "pk_types_schema/t_set"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "put": {
                "summary": "Update or create t_set entry",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "requestBody": {
                    "description": "Item to create or update",
                    "required": true,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/pk_types_schema_t_set"
                            }
                        }
                    }
                },
                "tags": [
                    "pk_types_schema/t_set"
                ],
                "responses": {
                    "400": {
                        "description": "Invalid input"
                    },
                    "200": {
                        "description": "Item successfully created or updated",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/pk_types_schema_t_set"
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "components": {
        "schemas": {
            "pk_types_schema_t_set": {
                "type": "object",
                "properties": {
                    "id": {
                        "type": "string",
                        "enum": [
                            "value1",
                            "value2",
                            "value3"
                        ],
                        "description": "set('value1','value2','value3'), Primary Key"
                    }
                }
            }
        }
    }
}
OK

#
# I.3
GET /svc/different_types_as_pk/open-api-catalog/t_timestamp
{
    "openapi": "3.1.0",
    "info": {
        "title": "svc OpenAPI specification",
        "version": "1.0.0"
    },
    "paths": {
        "/svc/different_types_as_pk/t_timestamp": {
            "get": {
                "summary": "Get t_timestamp contents",
                "tags": [
                    "pk_types_schema/t_timestamp"
                ],
                "responses": {
                    "200": {
                        "description": "t_timestamp contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "array",
                                    "items": {
                                        "$ref": "#/components/schemas/pk_types_schema_t_timestamp"
                                    }
                                }
                            }
                        }
                    }
                },
                "parameters": [
                    {
                        "in": "query",
                        "name": "limit",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "offset",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ]
            },
            "delete": {
                "summary": "Delete t_timestamp entry",
                "parameters": [
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "pk_types_schema/t_timestamp"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "/svc/different_types_as_pk/t_timestamp/{id}": {
            "get": {
                "summary": "Get t_timestamp contents",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "pk_types_schema/t_timestamp"
                ],
                "responses": {
                    "200": {
                        "description": "t_timestamp contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/pk_types_schema_t_timestamp"
                                }
                            }
                        }
                    },
                    "404": {
                        "description": "Not found"
                    }
                }
            },
            "delete": {
                "summary": "Delete t_timestamp entry",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "pk_types_schema/t_timestamp"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "put": {
                "summary": "Update or create t_timestamp entry",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "requestBody": {
                    "description": "Item to create or update",
                    "required": true,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/pk_types_schema_t_timestamp"
                            }
                        }
                    }
                },
                "tags": [
                    "pk_types_schema/t_timestamp"
                ],
                "responses": {
                    "400": {
                        "description": "Invalid input"
                    },
                    "200": {
                        "description": "Item successfully created or updated",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/pk_types_schema_t_timestamp"
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "components": {
        "schemas": {
            "pk_types_schema_t_timestamp": {
                "type": "object",
                "properties": {
                    "id": {
                        "type": "string",
                        "description": "timestamp, Primary Key"
                    }
                }
            }
        }
    }
}
OK

#
# I.4
GET /svc/different_types_as_pk/open-api-catalog/t_bin
{
    "openapi": "3.1.0",
    "info": {
        "title": "svc OpenAPI specification",
        "version": "1.0.0"
    },
    "paths": {
        "/svc/different_types_as_pk/t_bin": {
            "get": {
                "summary": "Get t_bin contents",
                "tags": [
                    "pk_types_schema/t_bin"
                ],
                "responses": {
                    "200": {
                        "description": "t_bin contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "array",
                                    "items": {
                                        "$ref": "#/components/schemas/pk_types_schema_t_bin"
                                    }
                                }
                            }
                        }
                    }
                },
                "parameters": [
                    {
                        "in": "query",
                        "name": "limit",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "offset",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ]
            },
            "delete": {
                "summary": "Delete t_bin entry",
                "parameters": [
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "pk_types_schema/t_bin"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "/svc/different_types_as_pk/t_bin/{id}": {
            "get": {
                "summary": "Get t_bin contents",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "pk_types_schema/t_bin"
                ],
                "responses": {
                    "200": {
                        "description": "t_bin contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/pk_types_schema_t_bin"
                                }
                            }
                        }
                    },
                    "404": {
                        "description": "Not found"
                    }
                }
            },
            "delete": {
                "summary": "Delete t_bin entry",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    }
                ],
                "tags": [
                    "pk_types_schema/t_bin"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "put": {
                "summary": "Update or create t_bin entry",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    }
                ],
                "requestBody": {
                    "description": "Item to create or update",
                    "required": true,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/pk_types_schema_t_bin"
                            }
                        }
                    }
                },
                "tags": [
                    "pk_types_schema/t_bin"
                ],
                "responses": {
                    "400": {
                        "description": "Invalid input"
                    },
                    "200": {
                        "description": "Item successfully created or updated",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/pk_types_schema_t_bin"
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "components": {
        "schemas": {
            "pk_types_schema_t_bin": {
                "type": "object",
                "properties": {
                    "id": {
                        "type": "integer",
                        "description": "binary(3), Primary Key"
                    }
                }
            }
        }
    }
}
OK

#
# I.5
GET /svc/different_types/open-api-catalog/all_types
{
    "openapi": "3.1.0",
    "info": {
        "title": "svc OpenAPI specification",
        "version": "1.0.0"
    },
    "paths": {
        "/svc/different_types/all_types": {
            "get": {
                "summary": "Get all_types contents",
                "tags": [
                    "basic_schema/all_types"
                ],
                "responses": {
                    "200": {
                        "description": "all_types contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "array",
                                    "items": {
                                        "$ref": "#/components/schemas/basic_schema_all_types"
                                    }
                                }
                            }
                        }
                    }
                },
                "parameters": [
                    {
                        "in": "query",
                        "name": "limit",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "offset",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ]
            },
            "post": {
                "summary": "Create all_types entry",
                "requestBody": {
                    "description": "Item to create",
                    "required": true,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/basic_schema_all_types"
                            }
                        }
                    }
                },
                "tags": [
                    "basic_schema/all_types"
                ],
                "responses": {
                    "400": {
                        "description": "Invalid input"
                    },
                    "200": {
                        "description": "Item successfully created",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/basic_schema_all_types"
                                }
                            }
                        }
                    }
                }
            },
            "delete": {
                "summary": "Delete all_types entry",
                "parameters": [
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "basic_schema/all_types"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "components": {
        "schemas": {
            "basic_schema_all_types": {
                "type": "object",
                "properties": {}
            }
        }
    }
}
OK

#
# I.6
GET /svc/crud_operations/open-api-catalog/t1
{
    "openapi": "3.1.0",
    "info": {
        "title": "svc OpenAPI specification",
        "version": "1.0.0"
    },
    "paths": {
        "/svc/crud_operations/t1": {
            "get": {
                "summary": "Get table1 contents",
                "tags": [
                    "basic_schema/table1"
                ],
                "responses": {
                    "200": {
                        "description": "table1 contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "array",
                                    "items": {
                                        "$ref": "#/components/schemas/basic_schema_table1"
                                    }
                                }
                            }
                        }
                    }
                },
                "parameters": [
                    {
                        "in": "query",
                        "name": "limit",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "offset",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ]
            }
        },
        "/svc/crud_operations/t1/{id}": {
            "get": {
                "summary": "Get table1 contents",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "basic_schema/table1"
                ],
                "responses": {
                    "200": {
                        "description": "table1 contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/basic_schema_table1"
                                }
                            }
                        }
                    },
                    "404": {
                        "description": "Not found"
                    }
                }
            }
        }
    },
    "components": {
        "schemas": {
            "basic_schema_table1": {
                "type": "object",
                "properties": {
                    "id": {
                        "type": "integer",
                        "format": "int32",
                        "maximum": 2147483647,
                        "minimum": -2147483648,
                        "example": 0,
                        "description": "int, Primary Key"
                    }
                }
            }
        }
    }
}
OK

#
# I.7
GET /svc/crud_operations/open-api-catalog/t2
{
    "openapi": "3.1.0",
    "info": {
        "title": "svc OpenAPI specification",
        "version": "1.0.0"
    },
    "paths": {
        "/svc/crud_operations/t2": {
            "delete": {
                "summary": "Delete table2 entry",
                "parameters": [
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "basic_schema/table2"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "/svc/crud_operations/t2/{id}": {
            "delete": {
                "summary": "Delete table2 entry",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    }
                ],
                "tags": [
                    "basic_schema/table2"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "components": {
        "schemas": {
            "basic_schema_table2": {
                "type": "object",
                "properties": {
                    "id": {
                        "type": "integer",
                        "format": "int32",
                        "maximum": 2147483647,
                        "minimum": -2147483648,
                        "example": 0,
                        "description": "int, Primary Key"
                    },
                    "name": {
                        "type": "string",
                        "maxLength": 255,
                        "description": "varchar(255)"
                    },
                    "comments": {
                        "type": "string",
                        "maxLength": 512,
                        "description": "varchar(512)"
                    },
                    "date": {
                        "type": "string",
                        "description": "datetime"
                    }
                }
            }
        }
    }
}
OK

#
# I.8
GET /svc/crud_operations/open-api-catalog/t3
{
    "openapi": "3.1.0",
    "info": {
        "title": "svc OpenAPI specification",
        "version": "1.0.0"
    },
    "paths": {
        "/svc/crud_operations/t3": {
            "get": {
                "summary": "Get table3 contents",
                "tags": [
                    "basic_schema/table3"
                ],
                "responses": {
                    "200": {
                        "description": "table3 contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "array",
                                    "items": {
                                        "$ref": "#/components/schemas/basic_schema_table3"
                                    }
                                }
                            }
                        }
                    }
                },
                "parameters": [
                    {
                        "in": "query",
                        "name": "limit",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "offset",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ]
            },
            "post": {
                "summary": "Create table3 entry",
                "requestBody": {
                    "description": "Item to create",
                    "required": true,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/basic_schema_table3"
                            }
                        }
                    }
                },
                "tags": [
                    "basic_schema/table3"
                ],
                "responses": {
                    "400": {
                        "description": "Invalid input"
                    },
                    "200": {
                        "description": "Item successfully created",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/basic_schema_table3"
                                }
                            }
                        }
                    }
                }
            },
            "delete": {
                "summary": "Delete table3 entry",
                "parameters": [
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "basic_schema/table3"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "/svc/crud_operations/t3/{id}": {
            "get": {
                "summary": "Get table3 contents",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "basic_schema/table3"
                ],
                "responses": {
                    "200": {
                        "description": "table3 contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/basic_schema_table3"
                                }
                            }
                        }
                    },
                    "404": {
                        "description": "Not found"
                    }
                }
            },
            "delete": {
                "summary": "Delete table3 entry",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    }
                ],
                "tags": [
                    "basic_schema/table3"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "put": {
                "summary": "Update or create table3 entry",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    }
                ],
                "requestBody": {
                    "description": "Item to create or update",
                    "required": true,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/basic_schema_table3"
                            }
                        }
                    }
                },
                "tags": [
                    "basic_schema/table3"
                ],
                "responses": {
                    "400": {
                        "description": "Invalid input"
                    },
                    "200": {
                        "description": "Item successfully created or updated",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/basic_schema_table3"
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "components": {
        "schemas": {
            "basic_schema_table3": {
                "type": "object",
                "properties": {
                    "id": {
                        "type": "integer",
                        "format": "int32",
                        "maximum": 2147483647,
                        "minimum": -2147483648,
                        "example": 0,
                        "description": "int, Primary Key"
                    },
                    "cvarchar": {
                        "type": "string",
                        "maxLength": 255,
                        "description": "varchar(255)"
                    },
                    "ctext": {
                        "type": "string",
                        "maxLength": 65535,
                        "description": "text"
                    },
                    "cdatetime": {
                        "type": "string",
                        "description": "datetime"
                    },
                    "ctimestamp": {
                        "type": "string",
                        "description": "timestamp"
                    },
                    "cdate": {
                        "type": "string",
                        "format": "date",
                        "description": "date"
                    },
                    "ctime": {
                        "type": "string",
                        "format": "time",
                        "example": "00:00:00",
                        "description": "time"
                    },
                    "cyear": {
                        "type": "integer",
                        "minimum": 1901,
                        "maximum": 2155,
                        "example": 2024,
                        "description": "year"
                    },
                    "csmallint": {
                        "type": "integer",
                        "format": "int32",
                        "maximum": 32767,
                        "minimum": -32768,
                        "example": 0,
                        "description": "smallint"
                    },
                    "cbigint": {
                        "type": "integer",
                        "format": "int64",
                        "example": 0,
                        "description": "bigint"
                    },
                    "cbin": {
                        "type": "integer",
                        "description": "binary(3)"
                    },
                    "cfloat": {
                        "type": "number",
                        "format": "float",
                        "example": 0.0,
                        "description": "float"
                    },
                    "cdouble": {
                        "type": "number",
                        "format": "double",
                        "example": 0.0,
                        "description": "double"
                    },
                    "cdecimal": {
                        "type": "number",
                        "format": "decimal",
                        "description": "decimal(5,2)"
                    },
                    "cenum": {
                        "type": "string",
                        "enum": [
                            "value1",
                            "value2",
                            "value3"
                        ],
                        "description": "enum('value1','value2','value3')"
                    },
                    "cset": {
                        "type": "string",
                        "enum": [
                            "value1",
                            "value2",
                            "value3"
                        ],
                        "description": "set('value1','value2','value3')"
                    },
                    "cbit": {
                        "type": "integer",
                        "format": "int32",
                        "description": "bit(1)"
                    },
                    "cbit1": {
                        "type": "integer",
                        "format": "int32",
                        "description": "bit(1)"
                    },
                    "cbit10": {
                        "type": "integer",
                        "format": "int32",
                        "description": "bit(10)"
                    },
                    "ctinyint": {
                        "type": "integer",
                        "format": "int32",
                        "maximum": 127,
                        "minimum": -128,
                        "example": 0,
                        "description": "tinyint"
                    },
                    "cmediumint": {
                        "type": "integer",
                        "format": "int32",
                        "maximum": 8388607,
                        "minimum": -8388608,
                        "example": 0,
                        "description": "mediumint"
                    },
                    "cblob": {
                        "type": "string",
                        "format": "binary",
                        "maxLength": 65535,
                        "description": "blob"
                    },
                    "geo0": {
                        "type": "object",
                        "description": "geometry"
                    },
                    "geo4326": {
                        "type": "object",
                        "description": "geometry"
                    },
                    "vec": {
                        "type": "array",
                        "items": {
                            "type": "number",
                            "format": "float"
                        },
                        "minItems": 2048,
                        "maxItems": 2048,
                        "description": "vector(2048)"
                    }
                }
            }
        }
    }
}
OK

#
# I.9
GET /svc/crud_operations/open-api-catalog/t4
{
    "openapi": "3.1.0",
    "info": {
        "title": "svc OpenAPI specification",
        "version": "1.0.0"
    },
    "paths": {
        "/svc/crud_operations/t4": {
            "delete": {
                "summary": "Delete table4 entry",
                "parameters": [
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "tags": [
                    "basic_schema/table4"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "/svc/crud_operations/t4/{id}": {
            "delete": {
                "summary": "Delete table4 entry",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    }
                ],
                "tags": [
                    "basic_schema/table4"
                ],
                "responses": {
                    "404": {
                        "description": "Not found"
                    },
                    "200": {
                        "description": "Deleted item(s) count",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "itemsDeleted": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "put": {
                "summary": "Update or create table4 entry",
                "parameters": [
                    {
                        "in": "path",
                        "name": "id",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    }
                ],
                "requestBody": {
                    "description": "Item to create or update",
                    "required": true,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/basic_schema_table4"
                            }
                        }
                    }
                },
                "tags": [
                    "basic_schema/table4"
                ],
                "responses": {
                    "400": {
                        "description": "Invalid input"
                    },
                    "200": {
                        "description": "Item successfully created or updated",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/basic_schema_table4"
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "components": {
        "schemas": {
            "basic_schema_table4": {
                "type": "object",
                "properties": {
                    "id": {
                        "type": "integer",
                        "format": "int32",
                        "maximum": 2147483647,
                        "minimum": -2147483648,
                        "example": 0,
                        "description": "int, Primary Key"
                    },
                    "first_name": {
                        "type": "string",
                        "maxLength": 65535,
                        "description": "text"
                    },
                    "last_name": {
                        "type": "string",
                        "maxLength": 65535,
                        "description": "text"
                    },
                    "table2_id": {
                        "type": "integer",
                        "format": "int32",
                        "maximum": 2147483647,
                        "minimum": -2147483648,
                        "example": 0,
                        "description": "int"
                    }
                }
            }
        }
    }
}
OK

#
# I.10
GET /svc/crud_operations/open-api-catalog/v1
{
    "openapi": "3.1.0",
    "info": {
        "title": "svc OpenAPI specification",
        "version": "1.0.0"
    },
    "paths": {
        "/svc/crud_operations/v1": {
            "get": {
                "summary": "Get view1 contents",
                "tags": [
                    "basic_schema/view1"
                ],
                "responses": {
                    "200": {
                        "description": "view1 contents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "array",
                                    "items": {
                                        "$ref": "#/components/schemas/basic_schema_view1"
                                    }
                                }
                            }
                        }
                    }
                },
                "parameters": [
                    {
                        "in": "query",
                        "name": "limit",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "offset",
                        "required": false,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "in": "query",
                        "name": "q",
                        "description": "filter object",
                        "required": false,
                        "schema": {
                            "type": "string"
                        }
                    }
                ]
            }
        }
    },
    "components": {
        "schemas": {
            "basic_schema_view1": {
                "type": "object",
                "properties": {
                    "id": {
                        "type": "integer",
                        "format": "int32",
                        "maximum": 2147483647,
                        "minimum": -2147483648,
                        "example": 0,
                        "description": "int"
                    },
                    "name": {
                        "type": "string",
                        "maxLength": 255,
                        "description": "varchar(255)"
                    },
                    "comments": {
                        "type": "string",
                        "maxLength": 512,
                        "description": "varchar(512)"
                    },
                    "date": {
                        "type": "string",
                        "description": "datetime"
                    }
                }
            }
        }
    }
}
OK
DROP SCHEMA pk_types_schema;
DROP SCHEMA basic_schema;
DROP SCHEMA mysql_rest_service_metadata;
DROP ROLE mysql_rest_service_admin;
DROP ROLE mysql_rest_service_schema_admin;
DROP ROLE mysql_rest_service_meta_provider;
DROP ROLE mysql_rest_service_data_provider;
DROP ROLE mysql_rest_service_dev;
