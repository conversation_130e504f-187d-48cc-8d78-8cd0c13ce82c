# Copyright (c) 2004, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
max-time: 1800
cmd: testIndex
args: -n DeferredError

max-time: 900
cmd: testIndex
args: -n DeferredMixedLoad T1 T6 T13

max-time: 2000
cmd: testIndex
args: -n DeferredMixedLoadError --skip-ndb-optimized-node-selection T1 T6 T13

max-time: 900
cmd: testIndex
args: -n NF_DeferredMixed T1 T6 T13

max-time: 1800
cmd: testIndex
args: -n NF_Mixed T1 T6 T13

max-time: 900
cmd: testBasic
args: -r 5000 -n 899 T15 D1 D2

max-time: 600
cmd: testBackup
args: -n NFMaster T1

max-time: 600
cmd: testBasic
args: -n PkRead T1

max-time: 600
cmd: testBackup
args: -n NFMasterAsSlave T1

max-time: 600
cmd: testBasic
args: -n PkRead T1

max-time: 600
cmd: testBackup
args: -n NFSlave T1 

max-time: 600
cmd: testBasic
args: -n PkRead T1

max-time: 600
cmd: testBackup
args: -n FailMaster T1

max-time: 600
cmd: testBasic
args: -n PkRead T1

max-time: 600
cmd: testBackup
args: -n FailMasterAsSlave T1

max-time: 600
cmd: testBasic
args: -n PkRead T1

max-time: 600
cmd: testBackup
args: -n FailSlave T1

max-time: 600
cmd: testBasic
args: -n PkRead T1

max-time: 600
cmd: testBackup
args: -n BackupOne T1 T6 I3 D2

max-time: 600
cmd: testBackup
args: -n BackupDDL T1

max-time: 600
cmd: testBackup
args: -n Bug57650 T1

# Bug18379657 added on 17.04.2014
#max-time: 1000
#cmd: testBackup
#args: -n BackupBank T6
#
# BASIC FUNCTIONALITY
max-time: 500
cmd: testBasic
args: -n PkRead

max-time: 500
cmd: testBasic
args: -n PkSimpleRead

max-time: 500
cmd: testBasic
args: -n PkDirtyRead

max-time: 500
cmd: testBasic
args: -n PkUpdate 

max-time: 500
cmd: testBasic
args: -n PkDelete 

max-time: 500
cmd: testBasic
args: -n PkInsert 

max-time: 660
cmd: testBasic
args: -n UpdateAndRead 

max-time: 500
cmd: testBasic
args: -n DeleteRead

max-time: 500
cmd: testSpj
args: -n LookupJoin

max-time: 500
cmd: testSpj
args: -n ScanJoin

max-time: 500
cmd: testSpj
args: -n MixedJoin

max-time: 500
cmd: testSpj
args: -n FeatureDisabled T1

max-time: 500
cmd: testBasic
args: -n PkReadAndLocker T6 D1 D2

max-time: 500
cmd: testBasic
args: -n PkReadAndLocker2 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n PkReadUpdateAndLocker T6 D1 D2

max-time: 500
cmd: testBasic
args: -n ReadWithLocksAndInserts T6 D1 D2

max-time: 500
cmd: testBasic
args: -n PkInsertTwice T1 T6 D1 D2

max-time: 1500
cmd: testBasic
args: -n Fill T13 

# Bug18408665 added on 17.04.2014
#max-time: 1500
#cmd: testBasic
#args: -n Fill T6 
#
max-time: 500
cmd: testBasic
args: -n NoCommitSleep T6 D1 D2

max-time: 500
cmd: testBasic
args: -n NoCommitAndClose T6 D1 D2

max-time: 500
cmd: testBasic
args: -n Commit626 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n CommitTry626 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n CommitAsMuch626 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n NoCommit626 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n NoCommitRollback626 T1 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n Commit630 T1 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n CommitTry630 T1 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n CommitAsMuch630 T1 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n NoCommit630 T1 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n NoCommitRollback630 T1 T6 D1 D2 

max-time: 500
cmd: testBasic
args: -n NoCommitAndClose T1 T6 D1 D2 

max-time: 500
cmd: testBasic
args: -n RollbackUpdate T1 T6 D1 D2 

max-time: 500
cmd: testBasic
args: -n RollbackDeleteMultiple T1 T6 D1 D2 

max-time: 500
cmd: testBasic
args: -n ImplicitRollbackDelete T1 T6 D1 D2 

max-time: 500
cmd: testBasic
args: -n CommitDelete T1 T6 D1 D2 

max-time: 500
cmd: testBasic
args: -n RollbackNothing T1 T6 D1 D2 

max-time: 500
cmd: testBasicAsynch
args: -n PkInsertAsynch 

max-time: 500
cmd: testBasicAsynch
args: -n PkReadAsynch 

max-time: 500
cmd: testBasicAsynch
args: -n PkUpdateAsynch 

max-time: 500
cmd: testBasicAsynch
args: -n PkDeleteAsynch 

max-time: 1000
cmd: testBasic
args: -n MassiveRollback T1 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n MassiveRollback2 T1 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n MassiveRollback3 T1 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n MassiveRollback4 T1 T6 D1 D2

max-time: 500
cmd: testBasic
args: -n TupError

max-time: 500
cmd: testBasic
args: -n InsertError T1

max-time: 500
cmd: testBasic
args: -n InsertError2 T1

# Bug18401439 added on 17.04.2014
#max-time: 600
#cmd: testTimeout
#args: T1
#
max-time: 500
cmd: testBasic
args: -n Bug25090 T1

max-time: 1000
cmd: testBasic
args: -n Bug27756

max-time: 500
cmd: testBasic
args: -n Bug28073

max-time: 500
cmd: testBasic
args: -n Bug20535

max-time: 500
cmd: testBasic
args: -n Bug54944 T1

max-time: 500
cmd: testBasic
args: -n Bug54944DATABUFFER T1

max-time: 600
cmd: testBasic
args: -r 10 -n Bug59496_case1 T2

max-time: 600
cmd: testBasic
args: -r 10 -n Bug59496_case2 T2

#
# INDEX
#
max-time: 1500
cmd: testIndex
args: -n CreateAll T1 T13 T14

max-time: 3600
cmd: testIndex
args: -n InsertDelete T1 

max-time: 3600
cmd: testIndex
args: -n CreateLoadDrop T1 

max-time: 500
cmd: testIndex
args: -n MixedTransaction T1 

max-time: 2500
cmd: testIndex
args: -n BuildDuring T6 

max-time: 2500
cmd: testIndex
args: -n BuildDuring_O T6 

max-time: 600
cmd: testIndex
args: -n Bug46069 T1

max-time: 600
cmd: testIndex
args: -n Bug50118 T1

max-time: 300
cmd: testIndex
args: -n FireTrigOverload T1

max-time: 500
cmd: testIndex
args: -n Bug25059 -r 3000 T1

max-time: 2500
cmd: testIndex
args: -l 2 -n SR1 T6 T13 

max-time: 2500
cmd: testIndex
args: -l 2 -n SR1_O T6 T13 

#
# SCAN TESTS
#
max-time: 500
cmd: testScan
args: -n ScanRead16 

max-time: 500
cmd: testScan
args: -n ScanRead240 

max-time: 500
cmd: testScan
args: -n ScanReadCommitted240 

max-time: 500
cmd: testScan
args: -n ScanUpdate 

max-time: 500
cmd: testScan
args: -n ScanUpdate2 T6 D1 D2

max-time: 500
cmd: testScan
args: -n ScanDelete 

max-time: 500
cmd: testScan
args: -n ScanDelete2 D1 D2

max-time: 500
cmd: testScan
args: -n ScanUpdateAndScanRead T6 D1 D2

max-time: 500
cmd: testScan
args: -n ScanReadAndLocker T6 D1 D2

max-time: 500
cmd: testScan
args: -n ScanReadAndPkRead T6 D1 D2

max-time: 500
cmd: testScan
args: -n ScanRead488 -l 10 T6 D1 D2

max-time: 500
cmd: testScan
args: -n ScanRead488O -l 10 T6 D1 D2 

max-time: 500
cmd: testScan
args: -n Bug42559 T6 D1 D2 

max-time: 1000
cmd: testScan
args: -n ScanRead488T -l 10 T6 D1 D2 

# Bug18408736 added on 17.04.2014
#max-time: 1000
#cmd: testScan
#args: -n ScanRead488_Mixed -l 10 T6 D1 D2
#
max-time: 500
cmd: testScan
args: -n ScanRead488Timeout -l 10 T6 D1 D2

max-time: 1200
cmd: testScan
args: -n ScanRead40 -l 100 T6 D1 D2 

# Bug18408709 added on 17.04.2014
#max-time: 1800
#cmd: testScan
#args: -n ScanRead100 -l 100 T1 D1 D2 
#
# Bug18408713 added on 17.04.2014
#max-time: 1800
#cmd: testScan
#args: -n TupScanRead100 -l 100 T1 D1 D2 
#
# Bug18408731 added on 17.04.2014
#max-time: 600
#cmd: testScan
#args: -n ScanRead40 -l 100 T1 D1 D2 
#
# Bug18408737 added on 17.04.2014
#max-time: 1800
#cmd: testScan
#args: -n ScanRead40RandomTable -l 100 T1 
#
max-time: 500
cmd: testScan
args: -n ScanWithLocksAndInserts T6 D1 D2

max-time: 500
cmd: testScan
args: -n ScanReadAbort T6 D1 D2 

max-time: 500
cmd: testScan
args: -n ScanReadAbort15 T6 D1 D2 

max-time: 500
cmd: testScan
args: -n ScanReadAbort240 T6 D1 D2 

max-time: 500
cmd: testScan
args: -n ScanUpdateAbort16 T6 D1 D2 

max-time: 3600
cmd: testScan
args: -n ScanReadRestart T1 T6 T13

max-time: 3600
cmd: testScan
args: -n ScanReadRestart D1 D2

max-time: 1200
cmd: testScan
args: -n ScanUpdateRestart T6

# Bug18408730 added on 17.04.2014
#max-time: 1200
#cmd: testScan
#args: -n ScanUpdateRestart D1 D2 
#
max-time: 500
cmd: testScan
args: -n CheckGetValue T6 D1 D2 

max-time: 500
cmd: testScan
args: -n CloseWithoutStop T6 D1 D2 

max-time: 500
cmd: testScan
args: -n NextScanWhenNoMore T6 D1 D2 

max-time: 500
cmd: testScan
args: -n ExecuteScanWithoutOpenScan T6 D1 D2 

max-time: 500
cmd: testScan
args: -n OnlyOpenScanOnce T6 D1 D2 

max-time: 500
cmd: testScan
args: -n OnlyOneOpInScanTrans T6 D1 D2 

max-time: 500
cmd: testScan
args: -n OnlyOneOpBeforeOpenScan T6 D1 D2 

max-time: 500
cmd: testScan
args: -n OnlyOneScanPerTrans T6 D1 D2 

max-time: 500
cmd: testScan
args: -n NoCloseTransaction T6 D1 D2 

max-time: 500
cmd: testScan
args: -n CloseRefresh T1

max-time: 500
cmd: testScan
args: -n CheckInactivityTimeOut T6 D1 D2 

max-time: 500
cmd: testScan
args: -n CheckInactivityBeforeClose T6 D1 D2 

max-time: 500
cmd: testScan
args: -n CheckAfterTerror T6 D1 D2 

# Bug18408747 added on 17.04.2014
#max-time: 500
#cmd: testScan
#args: -n ScanReadError5021 T1 D1 D2 
#
max-time: 720
cmd: testScan
args: -n ScanReaderror5022 T1 D1 D2 

max-time: 500
cmd: testScan
args: -n ScanReadError5023 T1 D1 D2 

max-time: 500
cmd: testScan
args: -n ScanReadError5024 T1 D1 D2 

max-time: 500
cmd: testScan
args: -n ScanReadError5025 T1 D1 D2 

max-time: 500
cmd: testScan
args: -n ScanReadError5030 T1 D1 D2 

max-time: 500
cmd: testScan
args: -n ScanReadError8081 T1 D1 D2

max-time: 500
cmd: testScan
args: -n Bug13394788 T1

max-time: 500
cmd: testScan
args: -n InsertDelete T1 T6 D1 D2 

max-time: 500
cmd: testScan
args: -n Bug48700 T1

max-time: 500
cmd: testScan
args: -n CheckAfterTerror T1 D1 D2 

max-time: 1200
cmd: testScan
args: -n ScanReadWhileNodeIsDown T1

max-time: 1200
cmd: testScan
args: -n ScanReadWhileNodeIsDown D1 D2 

max-time: 500
cmd: testScan
args: -l 100 -n Scan-bug8262 T6 D1 D2

max-time: 500
cmd: testScan
args: -n ScanParallelism

max-time: 500
cmd: testScan
args: -n Bug24447 T1

max-time: 1000
cmd: testScan
args: -n ScanVariants

max-time: 500
cmd: testScan
args: -n Bug36124 T1

max-time: 500
cmd: testScan
args: -n Bug54945 --skip-ndb-optimized-node-selection T1

# Bug18408745 added on 17.04.2014
#max-time: 600
#cmd: testScan
#args: -n Bug12324191 T1 T6 T13
#
# Bug18408732 added on 17.04.2014
#max-time: 3600
#cmd: testNodeRestart
#args: -n Bug27003 T1
#
max-time: 300
cmd: testSystemRestart
args: -n Bug29167 T1

max-time: 300
cmd: testSystemRestart
args: -l 2 -n Bug28770 T1

max-time: 1000
cmd: testNodeRestart
args: -n Bug27283 T1

max-time: 500
cmd: testNodeRestart
args: -n Bug15587 T1

max-time: 500
cmd: testNodeRestart
args: -n Bug15632 T1

max-time: 500
cmd: testNodeRestart
args: -n Bug15685 T1

#max-time: 500
#cmd: testSystemRestart
#args: -n Bug18385 T1
#
max-time: 1000
cmd: testNodeRestart
args: -n Bug18414 T1

#max-time: 1000
#cmd: testNodeRestart
#args: -n Bug18612 T1
#
max-time: 1000
cmd: testNodeRestart
args: -n Bug18612SR T1

max-time: 1000
cmd: testNodeRestart
args: -n Bug20185 T1

max-time: 1000
cmd: testNodeRestart
args: -n Bug21271 T6

max-time: 1000
cmd: testIndex
args: -n Bug21384

max-time: 1000
cmd: testNodeRestart
args: -n Bug24717 T1

max-time: 1000
cmd: testNodeRestart
args: -n Bug25364 T1

max-time: 1000
cmd: testNodeRestart
args: -n Bug25554 T1

max-time: 1000
cmd: testNodeRestart
args: -n Bug26457 T1

max-time: 1000
cmd: testNodeRestart
args: -n Bug26481 T1

max-time: 1000
cmd: testNodeRestart
args: -n Bug29364 T1

max-time: 1000
cmd: testNodeRestart
args: -n Bug28023 T6 D2

# Bug18401338 added on 17.04.2014
#max-time: 3000
#cmd: testNodeRestart
#args: -n Bug25984 T1
#
max-time: 300
cmd: testNodeRestart
args: -n Bug32160 T1

# Bug18408722 added on 17.04.2014
#max-time: 2500
#cmd: testNodeRestart
#args: -n MixedPkRead T6 T13 
#
# Bug18408630 added on 17.04.2014
#max-time: 2500
#cmd: testIndex
#args: -n NFNR1 T6 T13
#
# Bug18408719 added on 17.04.2014
#max-time: 2500
#cmd: testIndex
#args: -n NFNR1_O T6 T13 
#
max-time: 2500
cmd: testIndex
args: -n NFNR2 T6 T13 

max-time: 2500
cmd: testIndex
args: -n NFNR2_O T6 T13 

max-time: 2500
cmd: testSpj
args: -n NF_Join T6 T13

#
# DICT TESTS
max-time: 500
cmd: testDict
args: -n Bug29501 T1

max-time: 500
cmd: testDict
args: -n testDropDDObjects T1

max-time: 1500
cmd: testDict
args: -n CreateAndDrop 

max-time: 1000
cmd: testNodeRestart
args: -n Bug28717 T1

max-time: 1500
cmd: testDict
args: -n CreateAndDropAtRandom -l 200 T1

max-time: 1500
cmd: testDict
args: -n CreateAndDropIndexes -l 200 T1

max-time: 1500
cmd: testDict
args: -n CreateAndDropWithData 

max-time: 1500
cmd: testDict
args: -n CreateAndDropDuring T6 D1 D2

max-time: 1500
cmd: testDict
args: -n CreateInvalidTables T1 

max-time: 500
cmd: testDict
args: -n FragmentTypeSingle T1 

max-time: 1500
cmd: testDict
args: -n FragmentTypeAllSmall T1 T6

max-time: 1500
cmd: testDict
args: -n FragmentTypeAllLarge T1 T6

max-time: 1500
cmd: testDict
args: -n TemporaryTables T1 T6

max-time: 1500
cmd: testDict
args: -n Restart_NR2 T1 I3

max-time: 500
cmd: testDict
args: -n Bug21755 T1

max-time: 1500
cmd: testDict
args: -n TableAddAttrs

max-time: 1500
cmd: testDict
args: -n TableAddAttrsDuring T1 T6

max-time: 500
cmd: testDict
args: -n Bug24631 T1

max-time: 600
cmd: testDict
args: -n Bug41905 T1

max-time: 600
cmd: testDict
args: -n TableAddAttrsDuringError

# Bug18401543 added on 17.04.2014
#max-time: 1500
#cmd: testDict
#args: -l 25 -n DictRestart T1
#
max-time: 500
cmd: testDict
args: -n Bug54651 T1

max-time: 1500
cmd: testDict
args: -n CreateMaxTables T6 

#
# TEST NDBAPI
#
max-time: 500
cmd: testDataBuffers
args: 

# Testsuite: testNdbApi
# Number of tests: 5
max-time: 500
cmd: testNdbApi
args: -n MaxNdb T6 

max-time: 500
cmd: testNdbApi
args: -n MaxTransactions T1 T6 T13 

max-time: 500
cmd: testNdbApi
args: -n MaxGetValue T1 T6 T13 

max-time: 500
cmd: testNdbApi
args: -n MaxEqual 

max-time: 500
cmd: testNdbApi
args: -n DeleteNdb T1 T6 

max-time: 500
cmd: testNdbApi
args: -n WaitUntilReady T1 T6 T13 

max-time: 500
cmd: testNdbApi
args: -n GetOperationNoTab T6 

max-time: 500
cmd: testNdbApi
args: -n NdbErrorOperation T6 

max-time: 500
cmd: testNdbApi
args: -n MissingOperation T6 

max-time: 500
cmd: testNdbApi
args: -n GetValueInUpdate T6 

max-time: 500
cmd: testNdbApi
args: -n UpdateWithoutKeys T6 D1 D2 

max-time: 500
cmd: testNdbApi
args: -n UpdateWithoutValues T6 D1 D2 

max-time: 500
cmd: testNdbApi
args: -n ReadWithoutGetValue D1 D2 

max-time: 500
cmd: testNdbApi
args: -n Bug_11133 T1 D1 D2 

max-time: 500
cmd: testNdbApi
args: -n Scan_4006 T1 D1 D2 

max-time: 500
cmd: testNdbApi
args: -n Bug_WritePartialIgnoreError T1 

max-time: 500
cmd: testNdbApi
args: -n ExecuteAsynch T1

max-time: 1000
cmd: testNdbApi
args: -n Bug28443

max-time: 500
cmd: testNdbApi
args: -n BadColNameHandling T6

max-time: 500
cmd: testNdbApi
args: -n SimpleReadAbortOnError T1 T6 T15

max-time: 500
cmd: testNdbApi
args: -n NdbRecordPKAmbiguity T1 T6 T15

max-time: 500
cmd: testNdbApi
args: -n NdbRecordPKUpdate T1 T6 T15

max-time: 500
cmd: testNdbApi
args: -n NdbRecordCICharPKUpdate T1 

max-time: 500
cmd: testNdbApi
args: -n NdbRecordRowLength

max-time: 500
cmd: testNdbApi
args: -n Bug44065

# Bug18408700 added on 17.04.2014
#max-time: 1000
#cmd: testNdbApi
#args: -n Bug44065_org
#
max-time: 1000
cmd: testNdbApi
args: -n RecordSpecificationBackwardCompatibility

max-time: 500
cmd: testNdbApi
args: -n DeleteClusterConnectionWhileUsed T1

max-time: 500
cmd: testInterpreter
args: T1 

# Bug18401844 added on 17.04.2014
#max-time: 7200
#cmd: testOperations
#args:
#
# Bug18401553 added on 17.04.2014
#max-time: 7200
#cmd: testTransactions
#args:
#
max-time: 3000
cmd: testRestartGci
args: T6 D1

max-time: 1500
cmd: testBlobs
args: -version 1 -rows 25

max-time: 1500
cmd: testBlobs
args: -rows 25

max-time: 600
cmd: testBlobs
args: -bug 27018 -skip p

max-time: 600
cmd: testBlobs
args: -bug 27370 -skip p

max-time: 600
cmd: testBlobs
args: -bug 36756 -skip p

max-time: 300
cmd: testBlobs
args: -bug 45768 -skip p

max-time: 300
cmd: testBlobs
args: -bug 48040 -skip p

# Bug18401623 added on 17.04.2014
#max-time: 5000
#cmd: testOIBasic
#args: -case abcdefz
#
max-time: 2000
cmd: testOIBasic
args: -case gz

# Bug18408738 added on 17.04.2014
#max-time: 2000
#cmd: testOIBasic
#args: -case hz
#
max-time: 2500
cmd: testBitfield
args:

max-time: 2500
cmd: testPartitioning
args:

#
#
# SYSTEM RESTARTS
#
max-time: 1500
cmd: testSystemRestart
args: -n basic T1 

max-time: 5000
cmd: testSystemRestart
args: -n SR1 T1 

max-time: 5000
cmd: testSystemRestart
args: -n SR1 T6 

max-time: 5000
cmd: testSystemRestart
args: -n SR1 D1

max-time: 5000
cmd: testSystemRestart
args: -n SR1 D2 

max-time: 5000
cmd: testSystemRestart
args: -n SR2 T1 

max-time: 5000
cmd: testSystemRestart
args: -n SR2 T6 

max-time: 5000
cmd: testSystemRestart
args: -n SR2 D1

max-time: 5000
cmd: testSystemRestart
args: -n SR2 D2 

max-time: 5000
cmd: testSystemRestart
args: -n SR_UNDO T1 

max-time: 5000
cmd: testSystemRestart
args: -n SR_UNDO T6 

max-time: 5000
cmd: testSystemRestart
args: -n SR_UNDO D1

max-time: 5000
cmd: testSystemRestart
args: -n SR_UNDO D2

max-time: 1500
cmd: testSystemRestart
args: -n SR3 T6 

max-time: 1500
cmd: testSystemRestart
args: -n SR4 T6 

#
max-time: 5000
cmd: testSystemRestart
args: -l 1 -n SR6 T1 

max-time: 5000
cmd: testSystemRestart
args: -l 1 -n SR7 T1 

max-time: 5000
cmd: testSystemRestart
args: -l 1 -n SR8 T1 

max-time: 5000
cmd: testSystemRestart
args: -l 1 -n SR9 T1 

max-time: 300
cmd: testNodeRestart
args: -n Bug24543 T1

max-time: 1500
cmd: testSystemRestart
args: -n Bug24664

max-time: 1000
cmd: testNodeRestart
args: -n Bug25468 T1

max-time: 1000
cmd: testNodeRestart
args: -n Bug27466 T1

max-time: 1500
cmd: testSystemRestart
args: -n Bug27434 T1

# Bug18401475 added on 17.04.2014
#max-time: 1000
#cmd: test_event
#args: -l 10 -n Bug27169 T1
#
max-time: 1000
cmd: test_event
args: -n Bug12598496 T1

#
max-time: 1000
cmd: test_event
args: -n Bug18703871 T1

#
max-time: 1000
cmd: test_event
args: -n Apiv2EmptyEpochs T1

#
max-time: 600
cmd: test_event_merge
args: --no-implicit-nulls --separate-events --blob-version 1

#
max-time: 1000
cmd: test_event
args: -n Apiv2EventBufferOverflow T1

#
max-time: 600
cmd: test_event_merge
args: --no-implicit-nulls --separate-events

#
max-time: 600
cmd: test_event_merge
args: --no-implicit-nulls --no-multiops --blob-version 1

#
max-time: 1000
cmd: test_event
args: -n NextEventRemoveInconsisEvent T1

#
max-time: 600
cmd: test_event_merge
args: --no-implicit-nulls --no-multiops

#
max-time: 3600
cmd: test_event
args: -n EventOperationApplier -l 2

#
# Bug18408741 added on 17.04.2014
#max-time: 3600
#cmd: test_event
#args: -n EventOperationApplier_NR -l 2
#
#
# Bug18408715 added on 17.04.2014
#max-time: 600
#cmd: test_event
#args: -n EventOperationApplier_NS T1
#
#
# Bug18408742 added on 17.04.2014
#max-time: 3600
#cmd: test_event
#args: -n MergeEventOperationApplier_NR -l 2
#
#
max-time: 2500
cmd: test_event
args: -n Multi

#
max-time: 3600
cmd: test_event
args: -n CreateDropNR T1

max-time: 600
cmd: testBasic
args: -n PkRead T1

max-time: 300
cmd: testNodeRestart
args: -n Bug31980 T1

max-time: 2500
cmd: testNodeRestart
args: -n CommittedRead T1

max-time: 2500
cmd: testNodeRestart
args: -n RestartRandomNode T6 T13 

max-time: 2500
cmd: testNodeRestart
args: -n LateCommit T1

max-time: 2500
cmd: testNodeRestart
args: -n RestartMasterNodeError T6 T13 

max-time: 2500
cmd: testNodeRestart
args: -n RestartAllNodes T6 T13 

max-time: 2500
cmd: testNodeRestart
args: -n RestartAllNodesAbort T6 T13 

max-time: 2500
cmd: testNodeRestart
args: -n RestartAllNodesError9999 T6 T13 

max-time: 2500
cmd: testNodeRestart
args: -n RestartRandomNodeInitial T6 T13 

max-time: 2500
cmd: testNodeRestart
args: -n NoLoad T6

max-time: 2500
cmd: testNodeRestart
args: -n TwoNodeFailure T6 T13 

max-time: 2500
cmd: testNodeRestart
args: -n TwoMasterNodeFailure T6 T13 

max-time: 2500
cmd: testNodeRestart
args: -n FiftyPercentFail T6 T13 

max-time: 2500
cmd: testNodeRestart
args: -n RestartRandomNodeError T6 T13 

# Bug18401299 added on 17.04.2014
#max-time: 2500
#cmd: testNodeRestart
#args: -l 1 -n MixedReadUpdateScan
#
# Bug18408728 added on 17.04.2014
#max-time: 2500
#cmd: testNodeRestart
#args: -n Terror T6 T13 
#
# Bug18408693 added on 17.04.2014
#max-time: 3600
#cmd: testNodeRestart
#args: -l 1 -n RestartNFDuringNR T6 T13 
#
# Bug18401836 added on 17.04.2014
#max-time: 3600
#cmd: testNodeRestart
#args: -n RestartNodeDuringLCP T6
#
max-time: 3600
cmd: testNodeRestart
args: -n LCPTakeOver T6 

max-time: 2500
cmd: testNodeRestart
args: -n FiftyPercentStopAndWait T6 T13 

#
# MGMAPI AND MGSRV
#
max-time: 1800
cmd: testSingleUserMode
args: T1

# OLD FLEX
max-time: 500
cmd: flexBench
args: -c 25 -t 10 

max-time: 500
cmd: flexHammer
args: -r 5 -t 32 

max-time: 2500
cmd: testNodeRestart
args: -n NF_Hammer -r 5 T1

max-time: 300
cmd: DbCreate
args:

max-time: 180
cmd: DbAsyncGenerator
args: -time 60 -p 1
type: bench

max-time: 180
cmd: DbAsyncGenerator
args: -time 60 -p 25
type: bench

max-time: 180
cmd: DbAsyncGenerator
args: -time 60 -p 100
type: bench

max-time: 180
cmd: DbAsyncGenerator
args: -time 60 -p 200
type: bench

max-time: 180
cmd: DbAsyncGenerator
args: -time 60 -p 1 -proc 25
type: bench

# Bug18401186 added on 17.04.2014
#max-time: 5000
#cmd: testNodeRestart
#args: -n GCP -l 1 T1
#
# Bug18401257 added on 17.04.2014
#max-time: 600
#cmd: testNodeRestart
#args: -n Bug57522 T1
#
max-time: 1200
cmd: testNodeRestart
args: -n Bug41469 T1

# Bug18401361 added on 17.04.2014
#max-time: 600
#cmd: testBasic
#args: -n Bug54986 D2
#
# Bug18379339 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_1 D1
#
# Bug18401499 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_1b D1
#
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_1 D2

# Bug18401521 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_1b D2
#
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_1_LCP D1

# Bug18401529 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_1b_LCP D1
#
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_1_LCP D2

# Bug18401375 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_1b_LCP D2
#
# Bug18408708 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_2 D1
#
# Bug18408696 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_2b D1
#
# Bug18408646 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_2 D2
#
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_2b D2

# Bug18408698 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_2_LCP D1
#
# Bug18408635 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_2b_LCP D1
#
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_2_LCP D2

# Bug18408639 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_2b_LCP D2
#
# Bug18408626 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_3 D1
#
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_3b D1

max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_3 D2

max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_3b D2

# Bug18408642 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_3_LCP D1
#
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_3b_LCP D1

# Bug18408702 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_3_LCP D2
#
# Bug18408649 added on 17.04.2014
#max-time: 1500
#cmd: testSystemRestart
#args: -n SR_DD_3b_LCP D2
#
max-time: 1500
cmd: testSystemRestart
args: -n Bug41915 D2

max-time: 3600
cmd: testSystemRestart
args: -n Bug48436 T1

max-time: 600
cmd: testSystemRestart
args: -n Bug54611 T1

max-time: 300
cmd: test_event
args: -n Bug31701 T1

max-time: 600
cmd: testSystemRestart
args: -n Bug22696 T1

max-time: 1000
cmd: testSRBank
args: -n SR -l 300 -r 15 T1

max-time: 600
cmd: testNodeRestart
args: -n pnr --nologging T1

# Bug18408638 added on 17.04.2014
#max-time: 600
#cmd: testNodeRestart
#args: -n pnr_lcp T1
#
max-time: 600
cmd: testSystemRestart
args: -n to T1

max-time: 600
cmd: testSystemRestart
args: -n to I3

# Bug18401154 added on 17.04.2014
#max-time: 600
#cmd: testSystemRestart
#args: -n to D2
#
max-time: 300
cmd: testNodeRestart
args: -n Bug32922 T1

max-time: 300
cmd: test_event
args: -n Bug33793 T1

# Bug18379632 added on 17.04.2014
#max-time: 1200
#cmd: testNodeRestart
#args: -n Bug34216 -l 10 T1 I3 D2
#
# Bug18408711 added on 17.04.2014
#max-time: 1800
#cmd: testNodeRestart
#args: -n mixedmultiop T1 I2 I3 D2
#
# Bug18401826 added on 17.04.2014
#max-time: 600
#cmd: testNodeRestart
#args: -n Bug34702 T1
#
max-time: 600
cmd: test_event
args: -n Bug35208 T1

max-time: 300
cmd: test_event
args: -n Bug37279 T1

max-time: 300
cmd: test_event
args: -n Bug37338 T1

max-time: 300
cmd: test_event
args: -n Bug37442 T1

# 2008-04-22
# Bug18408682 added on 17.04.2014
#max-time: 1500
#cmd: testNodeRestart
#args: -n MNF -l 15 T1
#
max-time: 300
cmd: testNodeRestart
args: -n Bug36199 T1

max-time: 300
cmd: testNodeRestart
args: -n Bug36246 T1

max-time: 300
cmd: testNodeRestart
args: -n Bug36247 T1

max-time: 300
cmd: testNodeRestart
args: -n Bug36276 T1

# 2008-04-25
max-time: 300
cmd: testNodeRestart
args: -n Bug36245 T1

max-time: 300
cmd: testNodeRestart
args: -n Bug58453 T1

max-time: 300
cmd: test_event
args: -n Bug34853 T1

# EOF 2008-04-25
# 2008-05-29
max-time: 1200
cmd: testDict
args: -l 1 -n FailAddFragment

# EOF 2008-05-29
# 2008-05-30
max-time: 1200
cmd: testDict
args: -l 1 -n FailCreateHashmap T1

# EOF 2008-05-30
# 2008-06-03
max-time: 1200
cmd: testNdbApi
args: -l 100 -n Bug37158

# EOF 2008-06-03
# 2008-06-05
max-time: 1200
cmd: testDict
args: -n FailAddPartition T1 I3

# EOF 2008-06-05
# Test data buffering for TCKEYREQ
max-time: 500
cmd: testLimits
args: -n ExhaustSegmentedSectionPk WIDE_2COL

# Test data buffering for TCINDXREQ
max-time: 500
cmd: testLimits
args: -n ExhaustSegmentedSectionIx WIDE_2COL_IX

# Run some tests on max size / max num cols tables
max-time: 500
cmd: testBasic
args: -n PkRead WIDE_MAXKEY_HUGO WIDE_MAXATTR_HUGO WIDE_MAXKEYMAXCOLS_HUGO WIDE_MINKEYMAXCOLS_HUGO

max-time: 500
cmd: testBasic
args: -n PkUpdate WIDE_MAXKEY_HUGO WIDE_MAXATTR_HUGO WIDE_MAXKEYMAXCOLS_HUGO WIDE_MINKEYMAXCOLS_HUGO

# EOF 2008-06-30
max-time: 500
cmd: test_event
args: -n bug37672 T1

#EOF 2008-07-04
max-time: 500
cmd: testScanFilter
args: T1

# Bug18401324 added on 17.04.2014
#EOF 2008-07-09
#max-time: 600
#cmd: test_event
#args: -r 5000 -n Bug30780 T1
#
#EOF 2008-08-11
# Test data buffering for SCANTABREQ
max-time: 500
cmd: testLimits
args: -n ExhaustSegmentedSectionScan WIDE_2COL

# Bug18401568 added on 17.04.2014
#EOF 2008-08-20
#max-time: 1200
#cmd: testNodeRestart
#args: -n Bug41295 T1
#
# Bug18379463 added on 17.04.2014
#max-time: 1200
#cmd: testNodeRestart
#args: -n Bug42422 -l 1 T1
#
max-time: 500
cmd: testLimits
args: -n DropSignalFragments T1

max-time: 500
cmd: testMgm
args:

max-time: 300
cmd: testScan
args: -n Bug42545 -l 1 T1

max-time: 600
cmd: testNodeRestart
args: -n -l 3 -n Bug43224 T1

max-time: 1200
cmd: testNodeRestart
args: -n Bug43888 T1

max-time: 600
cmd: testNodeRestart
args: -n Bug48474 T1

max-time: 1200
cmd: testNdbApi
args: -n Bug44015 T1

max-time: 1200
cmd: test_event
args: -n Bug44915 T1

max-time: 1200
cmd: test_event
args: -n Bug56579 T1

max-time: 1200
cmd: test_event
args: -n Bug57886 T1

max-time: 1200
cmd: test_event
args: -n createDropEvent_NF T1

max-time: 3600
cmd: testNodeRestart
args: -n Bug44952 T1

max-time: 600
cmd: testNodeRestart
args: -n Bug56044 T1

max-time: 600
cmd: testNodeRestart
args: -n Bug57767 T2

# Bug18401576 added on 17.04.2014
#max-time: 300
#cmd: testSystemRestart
#args: -n Bug45154 D1
#
max-time: 300
cmd: testDict
args: -n Bug36702 D1

max-time: 300
cmd: testDict
args: -n Bug46552 T1

# Bug18379518 added on 17.04.2014
#max-time: 900
#cmd: testDict
#args: -n Bug46585 T1 I3 D1
#
max-time: 300
cmd: testSystemRestart
args: -n Bug46651 T1

# Bug18408712 added on 17.04.2014
#max-time: 300
#cmd: testSystemRestart
#args: -n Bug46412 T1
#
# Test clean ApiFailReq behaviour
max-time: 300
cmd: testNdbApi
args: -n ApiFailReqBehaviour T1

max-time: 300
cmd: testNdbApi
args: -n ReadColumnDuplicates

max-time: 300
cmd: testNdbApi
args: -n Bug51775 T1

max-time: 300
cmd: testBasic
args: -n DDInsertFailUpdateBatch

max-time: 300
cmd: testBlobs
args: -skip hp -bug 28116

# Bug18408653 added on 17.04.2014
#max-time: 300
#cmd: testNdbApi
#args: -n FragmentedApiFailure T1
#
# Series of short (signal train) request generation/handling tests
# Start
max-time: 500
cmd: testBasic
args: --forceshortreqs -n PkUpdate

max-time: 300
cmd: testIndex
args: --forceshortreqs -n InsertDelete T2

max-time: 2500
cmd: testPartitioning
args: --forceshortreqs

# End of short (signal train) handling tests
max-time: 300
cmd: testIndex
args: -n ConstraintDetails

max-time: 900
cmd: testNdbinfo
args:

# Unlock row tests as of 22/01/10
max-time: 300
cmd: testNdbApi
args: -n UnlockBasic T1

max-time: 300
cmd: testNdbApi
args: -n UnlockRepeat T2

max-time: 300
cmd: testNdbApi
args: -n UnlockMulti T3

max-time: 300
cmd: testNdbApi
args: -n UnlockScan T1

max-time: 300
cmd: testBasic
args: -n UnlockBatch T6

max-time: 300
cmd: testBasic
args: -n DoubleUnlock T1

max-time: 300
cmd: testBasic
args: -n UnlockUpdateBatch T3

max-time: 600
cmd: testNodeRestart
args: -n MixReadUnlockRestart T1

max-time: 500
cmd: testNativeDefault
args: 

# Bug18408705 added on 17.04.2014
#max-time: 500
#cmd: testDict
#args: -n Bug53944 T1
#
max-time: 300
cmd: testIndex
args: -n Bug56829 T1

max-time: 300
cmd: testIndex
args: -n Bug12315582 T1

max-time: 300
cmd: testIndex
args: -n Bug60851 T1

max-time: 500
cmd: testNodeRestart
args: -n ForceStopAndRestart T1

max-time: 300
cmd: testDict
args: -n Bug58277 T1

max-time: 300
cmd: testDict
args: -n Bug57057 T1

# Bug18408654 added on 17.04.2014
#max-time: 600
#cmd: testNodeRestart
#args: -n ClusterSplitLatency T1
#
# Refresh tuple
# Bug18408692 added on 17.04.2014
max-time: 300
cmd: testBasic
args: -n RefreshTuple T6 D1

max-time: 300
cmd: testIndex
args: -n RefreshWithOrderedIndex T2 D2

max-time: 300
cmd: testBasic
args: -n RefreshLocking D1

max-time: 300
cmd: testIndexStat
args:

max-time: 300
cmd: testBlobs
args: -bug 62321 -skip p

# async api extensions
max-time: 500
cmd: testAsynchMultiwait
args: -n AsynchMultiwaitPkRead T1

max-time: 500
cmd: testAsynchMultiwait
args: -n AsynchMultiwaitWakeup T1

max-time: 500
cmd: testAsynchMultiwait
args: -n AsynchMultiwait_Version2 T1

# alloc node id
max-time: 500
cmd: testNdbApi
args: -n NdbClusterConnect T1

max-time: 500
cmd: testNdbApi
args: -n NdbClusterConnectionConnect T1

max-time: 500
cmd: testNdbApi
args: -n NdbClusterConnectNR_non_master T1

max-time: 500
cmd: testNdbApi
args: -n NdbClusterConnectNR_slow T1

max-time: 500
cmd: testNdbApi
args: -n NdbClusterConnectSR T1

# Fragmented signal send
max-time: 1800
cmd: testNdbApi
args: -n TestFragmentedSend T1

max-time: 1800
cmd: testNdbApi
args: -n SchemaObjectOwnerCheck T1

max-time: 300
cmd: testNodeRestart
args: -n MasterFailSlowLCP T1

max-time:300
cmd: testScan
args: -nScanFragRecExhaust T1

max-time: 300
cmd: testDict
args: -n Bug13416603 I2

max-time: 300
cmd: testDict
args: -n IndexStatCreate T1

max-time: 300
cmd: testDict
args: -n DropWithTakeover T1

max-time: 300
cmd: testDict
args: -n GetTabInfoRef T1

max-time: 300
cmd: testBasic
args: -n LeakApiConnectObjects T1

# Bug18408726 added on 17.04.2014
#max-time: 300
#cmd: testScan
#args:  -n extraNextResultBug11748194 T1
#
max-time: 1200
cmd: testRedo
args: -nRedoFull T1

# Bug18379486 added on 17.04.2014
#max-time: 600
#cmd: testRedo
#args: -n RestartFD -l 2 T1
#
# Bug18409942 added on 17.04.2014
#max-time : 300
#cmd: testScan
#args: -n ScanKeyInfoExhaust T1
#
max-time : 300
cmd: testDict
args: -n Bug14645319 T1

max-time : 1200
cmd: testLimits
args: -n SlowDihFileWrites T1

max-time : 300
cmd: testScan
args: -n TupCheckSumError T1

max-time : 300
cmd: testScan
args: -n InterpretNok6000 T1

# Bug18408671 added on 17.04.2014
#max-time : 300
#cmd: testDict
#args: -n FK_SRNR1 T1
#
# Bug18408666 added on 17.04.2014
#max-time : 300
#cmd: testDict
#args: -n FK_SRNR2 T1
#
max-time : 300
cmd: testBasic
args: -n Bug16834333 T1

max-time : 300
cmd: testNodeRestart
args: -n Bug16834416 T1

max-time : 600
cmd: testNodeRestart
args: -n Bug16944817 T1

# Bug18401792 added on 17.04.2014
#max-time : 600
#cmd: testDict
#args: -n DictTakeOver_1 T1
#
max-time : 300
cmd: testFK
args: -n CreateDrop T1

# Bug18408667 added on 17.04.2014
#max-time : 300
#cmd: testFK
#args: -n CreateDropWithData T1
#
# Bug18408662 added on 17.04.2014
#max-time : 300
#cmd: testFK
#args: -n CreateDropDuring T1
#
max-time : 300
cmd: testFK
args: -n CreateDropError T1

# Bug18408675 added on 17.04.2014
#max-time : 300
#cmd: testFK
#args: -n Basic1 T1
#
# Bug18408677 added on 17.04.2014
#max-time : 300
#cmd: testFK
#args: -n Basic5 T1
#
# Bug18408670 added on 17.04.2014
#max-time : 300
#cmd: testFK
#args: -n Basic55 T1
#
# Bug18408672 added on 17.04.2014
#max-time : 300
#cmd: testFK
#args: -n TransError T1 T15
#
max-time : 300
cmd: testFK
args: -n Cascade1 T1

# Bug18408680 added on 17.04.2014
#max-time : 300
#cmd: testFK
#args: -n Cascade10 T1
#
# Bug18408674 added on 17.04.2014
#max-time : 300
#cmd: testFK
#args: -n CascadeError T1
#
# Bug18379566 added on 17.04.2014
#max-time : 600
#cmd: testNodeRestart
#args: -n Bug16007980 T1
#
# Bug18408691 added on 17.04.2014
#max-time : 600
#cmd: testNodeRestart
#args: -n Bug16766493 D1
#
max-time : 300
cmd: testDict
args: -n FK_TRANS1 T1

max-time : 300
cmd: testDict
args: -n FK_TRANS2 T1

max-time : 300
cmd: testDict
args: -n FK_Bug18069680 T1

max-time : 300
cmd: testDict
args: -n indexStat T1

max-time : 300
cmd: testDict
args: -n forceGCPWait T1

max-time : 600
cmd: testNodeRestart
args: -n Bug16895311 T1

max-time : 600
cmd: testNodeRestart
args: -n Bug18044717 T1

max-time : 600
cmd: testNodeRestart
args: -n GCPStopFalsePositive T1

max-time: 600
cmd: testBackup
args: -n Bug17882305 T1

max-time: 600
cmd: testBackup
args: -n Bug19202654 T1

max-time : 600
cmd: testNodeRestart
args: -n DeleteRestart T1

max-time : 600
cmd: testBasic
args: -n AccCommitOrder T1

max-time: 1200
cmd: test_event
args: -n EmptyUpdates T1

max-time: 1200
cmd: test_event
args: -n BackwardCompatiblePollNoWait T1

max-time: 1200
cmd: test_event
args: -n BackwardCompatiblePollLongWait T1

max-time: 1200
cmd: test_event
args: -n BackwardCompatiblePollInconsistency T1

#max-time: 1000
#cmd: test_event
#args: -n Apiv2HQE-latestGCI T1
#
#max-time: 1200
#cmd: test_event
#args: -n Apiv2-check_event_queue_cleared T1
#
max-time: 1200
cmd: test_event
args: -n Apiv2-check_event_queue_cleared_initial T1

max-time: 300
cmd: testSystemRestart
args: -n OneNodeWithCleanFilesystem T1

cmd: test_event
args: -n SubscribeNR T1
max-time: 1200
