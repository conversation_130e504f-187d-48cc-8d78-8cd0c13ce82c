--disable_warnings
DROP TABLE IF EXISTS t1;
DROP PROCEDURE IF EXISTS sp1;
--enable_warnings
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)          
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     CONTAINS SQL    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     NO SQL    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     READS SQL DATA    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     MODIFIES SQL DATA    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)         COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     CONTAINS SQL   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     NO SQL   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     READS SQL DATA   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     MODIFIES SQL DATA   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL        
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   CONTAINS SQL    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   NO SQL    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   READS SQL DATA    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   MODIFIES SQL DATA    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL       COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   CONTAINS SQL   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   NO SQL   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   READS SQL DATA   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   MODIFIES SQL DATA   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC      
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC CONTAINS SQL    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC NO SQL    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC READS SQL DATA    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC MODIFIES SQL DATA    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC     COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC CONTAINS SQL   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC NO SQL   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC READS SQL DATA   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC MODIFIES SQL DATA   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC      
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC CONTAINS SQL    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC NO SQL    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC MODIFIES SQL DATA    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC     COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC CONTAINS SQL   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC NO SQL   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC MODIFIES SQL DATA   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC      
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC CONTAINS SQL    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC NO SQL    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC READS SQL DATA    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC MODIFIES SQL DATA    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC     COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC CONTAINS SQL   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC NO SQL   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC READS SQL DATA   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC MODIFIES SQL DATA   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC      
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC CONTAINS SQL    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC NO SQL    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC READS SQL DATA    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC MODIFIES SQL DATA    
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC     COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC CONTAINS SQL   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC NO SQL   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC READS SQL DATA   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC MODIFIES SQL DATA   COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)       SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     CONTAINS SQL SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     NO SQL SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     READS SQL DATA SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     MODIFIES SQL DATA SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)       SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     NO SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL     SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   CONTAINS SQL SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   NO SQL SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   READS SQL DATA SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   MODIFIES SQL DATA SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL     SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   NO SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC   SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC NO SQL SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC   SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC NO SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC   SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC NO SQL SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC   SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC NO SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC   SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC NO SQL SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC   SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC NO SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC   SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC NO SQL SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC   SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC NO SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)        SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     CONTAINS SQL  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     NO SQL  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     READS SQL DATA  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     MODIFIES SQL DATA  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)        SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     NO SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)     MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL      SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   CONTAINS SQL  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   NO SQL  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   READS SQL DATA  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   MODIFIES SQL DATA  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL      SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   NO SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL   MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC    SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC NO SQL  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC    SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC NO SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   NOT DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC    SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC NO SQL  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC    SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC NO SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL NOT DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC    SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC NO SQL  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC    SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC NO SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT)   DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC    SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC NO SQL  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER  
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC    SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC NO SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
DELIMITER //;
CREATE PROCEDURE sp1 (OUT p1 BIGINT, IN p2 BIGINT) LANGUAGE SQL DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
SELECT COUNT(*) INTO p1 FROM t1 WHERE c1 = p2;
END//
DELIMITER ;//
CALL sp1(@a,1);
DROP PROCEDURE sp1;
DROP TABLE t1;

