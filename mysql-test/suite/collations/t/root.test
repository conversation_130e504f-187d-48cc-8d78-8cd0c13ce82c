--source suite/collations/include/unicode.inc
select hex(convert(uc using utf32)), hex(weight_string(convert(uc using utf8mb4) collate utf8mb4_0900_ai_ci)), name from unicode order by uc collate utf8mb4_0900_ai_ci, cp;
select hex(convert(uc using utf32)), hex(weight_string(convert(uc using utf8mb4) collate utf8mb4_0900_as_ci)), name from unicode order by uc collate utf8mb4_0900_as_ci, cp;
select hex(convert(uc using utf32)), hex(weight_string(convert(uc using utf8mb4) collate utf8mb4_0900_as_cs)), name from unicode order by uc collate utf8mb4_0900_as_cs, cp;
drop table if exists unicode;
