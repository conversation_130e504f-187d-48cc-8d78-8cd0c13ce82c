# suite/funcs_1/t/is_table_privileges.test
#
# Check the layout of information_schema.table_privileges and the impact of
# CREATE/ALTER/DROP TABLE/VIEW/SCHEMA ... on it.
#
# Note:
#    This test is not intended
#    - to show information about the all time existing tables
#      within the databases information_schema and mysql
#    - for checking storage engine properties
#      Therefore please do not alter $engine_type and $other_engine_type.
#
# Author:
# 2008-01-23 mleich WL#4203 Reorganize and fix the data dictionary tests of
#                           testsuite funcs_1
#                   Create this script based on older scripts and new code.
#


let $engine_type       = InnoDB;
let $other_engine_type = InnoDB;

let $is_table = TABLE_PRIVILEGES;

# The table INFORMATION_SCHEMA.TABLE_PRIVILEGES must exist
eval SHOW TABLES FROM information_schema LIKE '$is_table';

--echo #######################################################################
--echo # Testcase *******: INFORMATION_SCHEMA tables can be queried via SELECT
--echo #######################################################################
# Ensure that every INFORMATION_SCHEMA table can be queried with a SELECT
# statement, just as if it were an ordinary user-defined table.
#
--source suite/funcs_1/datadict/is_table_query.inc


--echo #########################################################################
--echo # Testcase ********: INFORMATION_SCHEMA.TABLE_PRIVILEGES layout
--echo #########################################################################
# Ensure that the INFORMATION_SCHEMA.TABLE_PRIVILEGES table has the following
# columns, in the following order:
#
# GRANTEE (shows the name of a user who has either granted, or been granted a
#       table privilege),
# TABLE_CATALOG (always shows NULL),
# TABLE_SCHEMA (shows the name of the schema, or database, in which the table
#       for which a privilege has been granted resides),
# TABLE_NAME (shows the name of the table),
# PRIVILEGE_TYPE (shows the type of privilege that was granted; must be either
#       SELECT, INSERT, UPDATE, DELETE, REFERENCES, ALTER, INDEX, DROP or
#       CREATE VIEW),
# IS_GRANTABLE (shows whether that privilege was granted WITH GRANT OPTION).
#
--source suite/funcs_1/datadict/datadict_bug_12777.inc
eval DESCRIBE          information_schema.$is_table;
--source suite/funcs_1/datadict/datadict_bug_12777.inc
eval SHOW CREATE TABLE information_schema.$is_table;
--source suite/funcs_1/datadict/datadict_bug_12777.inc
eval SHOW COLUMNS FROM information_schema.$is_table;

# Note: Retrieval of information within information_schema.columns
#       about information_schema.table_privileges is in is_columns_is.test.

# Show that TABLE_CATALOG is always NULL.
--sorted_result
SELECT table_catalog, table_schema, table_name, privilege_type
FROM information_schema.table_privileges WHERE table_catalog IS NOT NULL;

--echo ######################################################################
--echo # Testcase 3.2.11.2+3.2.11.3+3.2.11.4:
--echo #          INFORMATION_SCHEMA.TABLE_PRIVILEGES accessible information
--echo ######################################################################
# 3.2.11.2: Ensure that the table shows the relevant information on every
#           table privilege which has been granted to the current user or
#           PUBLIC, or which was granted by the current user.
# 3.2.11.3: Ensure that the table does not show any information on any table
#           privilege which was granted to any user other than the current
#           user or PUBLIC, or which was granted by any user other than the
#           current user.
# 3.2.11.4: Ensure that the table does not show any information on any
#           privileges that are not table privileges for the current user.
#
# To be implemented:
#    Check of content within information_schema.table_privileges about
#    databases like 'information_schema' or 'mysql'.
#    2008-02-15 Neither root nor a just created low privileged user has table
#    privileges within these schemas.
#
--disable_warnings
DROP DATABASE IF EXISTS db_datadict;
--enable_warnings
CREATE DATABASE db_datadict;
--replace_result $engine_type <engine_type>
eval
CREATE TABLE db_datadict.tb1(f1 INT, f2 INT, f3 INT)
ENGINE = $engine_type;

--error 0,ER_CANNOT_USER
DROP   USER 'testuser1'@'localhost';
CREATE USER 'testuser1'@'localhost';
GRANT CREATE, SELECT ON db_datadict.*
TO 'testuser1'@'localhost' WITH GRANT OPTION;
GRANT SELECT ON db_datadict.tb1 TO 'testuser1'@'localhost';
--error 0,ER_CANNOT_USER
DROP   USER 'testuser2'@'localhost';
CREATE USER 'testuser2'@'localhost';
GRANT ALL    ON db_datadict.tb1 TO 'testuser2'@'localhost' WITH GRANT OPTION;
--error 0,ER_CANNOT_USER
DROP   USER 'testuser3'@'localhost';
CREATE USER 'testuser3'@'localhost';

let $my_select = SELECT * FROM information_schema.table_privileges
WHERE table_name LIKE 'tb%'
ORDER BY grantee,table_schema,table_name,privilege_type;

--echo # Establish connection testuser1 (user=testuser1)
--replace_result $MASTER_MYPORT MYSQL_PORT $MASTER_MYSOCK MYSQL_SOCK
connect (testuser1, localhost, testuser1, , db_datadict);
--replace_result $other_engine_type <other_engine_type>
eval
CREATE TABLE tb3 (f1 TEXT)
ENGINE = $other_engine_type;
GRANT SELECT ON db_datadict.tb3 TO 'testuser3'@'localhost';
eval $my_select;
SHOW GRANTS FOR 'testuser1'@'localhost';

--echo # Establish connection testuser2 (user=testuser3)
--replace_result $MASTER_MYPORT MYSQL_PORT $MASTER_MYSOCK MYSQL_SOCK
connect (testuser2, localhost, testuser2, , db_datadict);
# we see only table privileges for this user, and not any other privileges
eval $my_select;
SHOW GRANTS FOR 'testuser2'@'localhost';

--echo # Establish connection testuser3 (user=testuser3)
--replace_result $MASTER_MYPORT MYSQL_PORT $MASTER_MYSOCK MYSQL_SOCK
connect (testuser3, localhost, testuser3, , db_datadict);
# we see only table privileges for this user, and not any other privileges
eval $my_select;
SHOW GRANTS FOR 'testuser3'@'localhost';

--echo # Switch to connection default and close the other connections
connection default;
disconnect testuser1;
disconnect testuser2;
disconnect testuser3;

# we see only 'public' table privileges
eval $my_select;
SHOW GRANTS FOR 'testuser1'@'localhost';
SHOW GRANTS FOR 'testuser2'@'localhost';
SHOW GRANTS FOR 'testuser3'@'localhost';

# Cleanup
DROP USER 'testuser1'@'localhost';
DROP USER 'testuser2'@'localhost';
DROP USER 'testuser3'@'localhost';
DROP DATABASE db_datadict;


--echo ################################################################################
--echo # *******3+*******4+*******5: INFORMATION_SCHEMA.TABLE_PRIVILEGES modifications
--echo ################################################################################
# *******3: Ensure that the creation of any new database object (e.g. table or
#           column) automatically inserts all relevant information on that
#           object into every appropriate INFORMATION_SCHEMA table.
# *******4: Ensure that the alteration of any existing database object
#           automatically updates all relevant information on that object in
#           every appropriate INFORMATION_SCHEMA table.
# *******5: Ensure that the dropping of any existing database object
#           automatically deletes all relevant information on that object from
#           every appropriate INFORMATION_SCHEMA table.
#
# Note (mleich):
#    The MySQL privilege system allows to GRANT objects before they exist.
#    (Exception: Grant privileges for columns of not existing tables/views.)
#    There is also no migration of privileges if objects (tables,views,columns)
#    are moved to other databases (tables only), renamed or dropped.
#
--disable_warnings
DROP TABLE IF EXISTS test.t1_table;
DROP VIEW  IF EXISTS test.t1_view;
DROP DATABASE IF EXISTS db_datadict;
--enable_warnings
CREATE DATABASE db_datadict;
--replace_result $engine_type <engine_type>
eval
CREATE TABLE test.t1_table (f1 BIGINT)
DEFAULT CHARACTER SET latin1 COLLATE latin1_swedish_ci
COMMENT = 'Initial Comment' ENGINE = $engine_type;
CREATE VIEW test.t1_view AS SELECT 1;

--error 0,ER_CANNOT_USER
DROP   USER 'testuser1'@'localhost';
CREATE USER 'testuser1'@'localhost';
--error 0,ER_CANNOT_USER
DROP   USER 'the_user'@'localhost';
#
# Check granted TABLE and VIEW
SELECT table_name FROM information_schema.table_privileges
WHERE table_name LIKE 't1_%';
GRANT ALL ON test.t1_table TO 'testuser1'@'localhost';
GRANT ALL ON test.t1_view  TO 'testuser1'@'localhost';
SELECT * FROM information_schema.table_privileges
WHERE table_name LIKE 't1_%'
ORDER BY grantee, table_schema, table_name, privilege_type;
#
# Check modification of GRANTEE (migration of permissions)
SELECT DISTINCT grantee, table_name FROM information_schema.table_privileges
WHERE table_name LIKE 't1_%'
ORDER BY grantee, table_name;
RENAME USER 'testuser1'@'localhost' TO 'the_user'@'localhost';
# FIXME: mleich Workaround for bug to be reported
# It looks like an immediate reloading of the system tables is missing in case
# of RENAME USER.
FLUSH PRIVILEGES;
SELECT DISTINCT grantee, table_name FROM information_schema.table_privileges
WHERE table_name LIKE 't1_%'
ORDER BY grantee, table_name;
--error ER_NONEXISTING_GRANT
SHOW GRANTS FOR 'testuser1'@'localhost';
--sorted_result
SHOW GRANTS FOR 'the_user'@'localhost';
#
# Check modification of TABLE_SCHEMA (no migration of permissions)
SELECT DISTINCT table_schema,table_name FROM information_schema.table_privileges
WHERE table_name LIKE 't1_%'
ORDER BY table_schema,table_name;
RENAME TABLE test.t1_table TO db_datadict.t1_table;
--error ER_FORBID_SCHEMA_CHANGE
RENAME TABLE test.t1_view  TO db_datadict.t1_view;
SELECT DISTINCT table_schema,table_name FROM information_schema.table_privileges
WHERE table_name LIKE 't1_%'
ORDER BY table_schema,table_name;
--sorted_result
SHOW GRANTS FOR 'the_user'@'localhost';
REVOKE ALL PRIVILEGES ON test.t1_table FROM 'the_user'@'localhost';
REVOKE ALL PRIVILEGES ON test.t1_view  FROM 'the_user'@'localhost';
DROP VIEW test.t1_view;
CREATE VIEW db_datadict.t1_view AS SELECT 1;
GRANT ALL ON db_datadict.t1_table TO 'the_user'@'localhost';
GRANT ALL ON db_datadict.t1_view  TO 'the_user'@'localhost';
#
# Check modification of TABLE_NAME (no migration of permissions)
SELECT DISTINCT table_name FROM information_schema.table_privileges
WHERE table_name LIKE 't1_%'
ORDER BY table_name;
RENAME TABLE db_datadict.t1_table TO db_datadict.t1_tablex;
RENAME TABLE db_datadict.t1_view  TO db_datadict.t1_viewx;
SELECT DISTINCT table_name FROM information_schema.table_privileges
WHERE table_name LIKE 't1_%'
ORDER BY table_name;
RENAME TABLE db_datadict.t1_tablex TO db_datadict.t1_table;
RENAME TABLE db_datadict.t1_viewx  TO db_datadict.t1_view;
#
# Check impact of DROP TABLE/VIEW (no removal of permissions)
SELECT DISTINCT table_name FROM information_schema.table_privileges
WHERE table_name LIKE 't1_%'
ORDER BY table_name;
DROP TABLE db_datadict.t1_table;
DROP VIEW  db_datadict.t1_view;
SELECT DISTINCT table_name FROM information_schema.table_privileges
WHERE table_name LIKE 't1_%'
ORDER BY table_name;
#
# Check impact of DROP SCHEMA  (no removal of permissions)
--replace_result $engine_type <engine_type>
eval
CREATE TABLE db_datadict.t1_table
ENGINE = $engine_type AS
SELECT 1;
CREATE VIEW  db_datadict.t1_view      AS SELECT 1;
GRANT ALL ON db_datadict.t1_table TO 'the_user'@'localhost';
GRANT ALL ON db_datadict.t1_view  TO 'the_user'@'localhost';
SELECT DISTINCT table_name FROM information_schema.table_privileges
WHERE table_name LIKE 't1_%'
ORDER BY table_name;
DROP DATABASE db_datadict;
SELECT DISTINCT table_name FROM information_schema.table_privileges
WHERE table_name LIKE 't1_%'
ORDER BY table_name;

# Cleanup
DROP USER 'the_user'@'localhost';


--echo ########################################################################
--echo # Testcases *******-******* + *******-********: INSERT/UPDATE/DELETE and
--echo #           DDL on INFORMATION_SCHEMA table are not supported
--echo ########################################################################
# *******:  Ensure that no user may execute an INSERT statement on any
#           INFORMATION_SCHEMA table.
# *******:  Ensure that no user may execute an UPDATE statement on any
#           INFORMATION_SCHEMA table.
# *******:  Ensure that no user may execute a DELETE statement on any
#           INFORMATION_SCHEMA table.
# *******:  Ensure that no user may create an index on an
#           INFORMATION_SCHEMA table.
# *******:  Ensure that no user may alter the definition of an
#           INFORMATION_SCHEMA table.
# ********: Ensure that no user may drop an INFORMATION_SCHEMA table.
# ********: Ensure that no user may move an INFORMATION_SCHEMA table to any
#           other database.
# ********: Ensure that no user may directly add to, alter, or delete any data
#           in an INFORMATION_SCHEMA table.
#
--disable_warnings
DROP DATABASE IF EXISTS db_datadict;
--enable_warnings
CREATE DATABASE db_datadict;
--replace_result $engine_type <engine_type>
eval
CREATE TABLE db_datadict.t1 (f1 BIGINT, f2 BIGINT)
ENGINE = $engine_type;
--error 0,ER_CANNOT_USER
DROP   USER 'testuser1'@'localhost';
CREATE USER 'testuser1'@'localhost';
GRANT SELECT (f1) ON db_datadict.t1 TO 'testuser1'@'localhost';

--error ER_DBACCESS_DENIED_ERROR
INSERT INTO information_schema.table_privileges
SELECT * FROM information_schema.table_privileges;

--error ER_DBACCESS_DENIED_ERROR
UPDATE information_schema.table_privileges SET table_schema = 'test'
WHERE table_name = 't1';

--error ER_DBACCESS_DENIED_ERROR
DELETE FROM information_schema.table_privileges WHERE table_name = 't1';
--error ER_DBACCESS_DENIED_ERROR
TRUNCATE information_schema.table_privileges;

--error ER_DBACCESS_DENIED_ERROR
CREATE INDEX my_idx_on_tables
ON information_schema.table_privileges(table_schema);
--error ER_DBACCESS_DENIED_ERROR
ALTER TABLE information_schema.table_privileges ADD f1 INT;

--error ER_DBACCESS_DENIED_ERROR
DROP TABLE information_schema.table_privileges;

--error ER_DBACCESS_DENIED_ERROR
ALTER TABLE information_schema.table_privileges
RENAME db_datadict.table_privileges;
--error ER_DBACCESS_DENIED_ERROR
ALTER TABLE information_schema.table_privileges
RENAME information_schema.xtable_privileges;

# Cleanup
DROP DATABASE db_datadict;
DROP USER 'testuser1'@'localhost';

