DROP TABLE IF EXISTS t1,t2,t3;
SET sql_mode='NO_ENGINE_SUBSTITUTION';
SET TIME_ZONE="+03:00";
CREATE TABLE t1(c1 DATE NOT NULL);
SET TIMESTAMP=1171346973;
INSERT INTO t1 (c1) VALUES(NOW());
Warnings:
Note	1265	Data truncated for column 'c1' at row 1
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'1 01:01:01'));
Warnings:
Note	1265	Data truncated for column 'c1' at row 1
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'2 02:01:01'));
Warnings:
Note	1265	Data truncated for column 'c1' at row 1
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'3 03:01:01'));
Warnings:
Note	1265	Data truncated for column 'c1' at row 1
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'4 04:01:01'));
Warnings:
Note	1265	Data truncated for column 'c1' at row 1
SELECT * FROM t1 ORDER BY c1;
c1
2007-02-13
2007-02-14
2007-02-15
2007-02-16
2007-02-17
UPDATE t1 SET c1 = NOW() WHERE c1 >= ADDTIME(NOW(),'2 02:01:01');
Warnings:
Note	1265	Data truncated for column 'c1' at row 4
Note	1265	Data truncated for column 'c1' at row 5
SELECT * FROM t1 ORDER BY c1;
c1
2007-02-13
2007-02-13
2007-02-13
2007-02-14
2007-02-15
DROP TABLE t1;
CREATE TABLE t1(c1 TIME NOT NULL);
SET TIMESTAMP=1171346973;
INSERT INTO t1 (c1) VALUES(NOW());
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'1 01:01:01'));
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'2 02:01:01'));
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'3 03:01:01'));
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'4 04:01:01'));
SELECT * FROM t1 ORDER BY c1;
c1
09:09:33
10:10:34
11:10:34
12:10:34
13:10:34
UPDATE t1 SET c1 = NOW() WHERE c1 >= ADDTIME(NOW(),'2 02:01:01');
SELECT * FROM t1 ORDER BY c1;
c1
09:09:33
10:10:34
11:10:34
12:10:34
13:10:34
DROP TABLE t1;
CREATE TABLE t1(c1 DATETIME NOT NULL);
SET TIMESTAMP=1171346973;
INSERT INTO t1 (c1) VALUES(NOW());
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'1 01:01:01'));
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'2 02:01:01'));
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'3 03:01:01'));
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'4 04:01:01'));
SELECT * FROM t1 ORDER BY c1;
c1
2007-02-13 09:09:33
2007-02-14 10:10:34
2007-02-15 11:10:34
2007-02-16 12:10:34
2007-02-17 13:10:34
UPDATE t1 SET c1 = NOW() WHERE c1 >= ADDTIME(NOW(),'2 02:01:01');
SELECT * FROM t1 ORDER BY c1;
c1
2007-02-13 09:09:33
2007-02-13 09:09:33
2007-02-13 09:09:33
2007-02-13 09:09:33
2007-02-14 10:10:34
DROP TABLE t1;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SET TIMESTAMP=1171346973;
INSERT INTO t1 (c1) VALUES(NOW());
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'1 01:01:01'));
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'2 02:01:01'));
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'3 03:01:01'));
INSERT INTO t1 (c1) VALUES(ADDTIME(NOW(),'4 04:01:01'));
SELECT * FROM t1 ORDER BY c1;
c1
2007-02-13 09:09:33
2007-02-14 10:10:34
2007-02-15 11:10:34
2007-02-16 12:10:34
2007-02-17 13:10:34
UPDATE t1 SET c1 = NOW() WHERE c1 >= ADDTIME(NOW(),'2 02:01:01');
SELECT * FROM t1 ORDER BY c1;
c1
2007-02-13 09:09:33
2007-02-13 09:09:33
2007-02-13 09:09:33
2007-02-13 09:09:33
2007-02-14 10:10:34
DROP TABLE t1;
SET TIME_ZONE= @@global.time_zone;
SET sql_mode=default;
