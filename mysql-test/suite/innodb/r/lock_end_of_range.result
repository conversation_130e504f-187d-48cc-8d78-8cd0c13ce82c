#
# Bug #29508068  UNNECESSARY NEXT-KEY LOCK TAKEN
#
CREATE TABLE t1 (
id INT NOT NULL,
PRIMARY KEY (id ASC)
) ENGINE=InnoDB;
CREATE TABLE t2 (
id INT NOT NULL,
PRIMARY KEY (id DESC)
) ENGINE=InnoDB;
SET @conditions =  CONCAT(
'<=0 <1 <=1 <7 <=7 <=8 <9 <=9 <=10 ',
'>=10 >9 >=9 >3 >=3 >=2 >1 >=1 >0'
);
INSERT INTO t1 VALUES (1),  (3), (4), (5), (6), (7),  (9);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <=0
ORDER BY id ASC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <=0
ORDER BY id DESC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <1
ORDER BY id ASC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <1
ORDER BY id DESC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <=1
ORDER BY id ASC
FOR UPDATE;
id
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <=1
ORDER BY id DESC
FOR UPDATE;
id
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X,GAP	GRANTED	3
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <7
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X,GAP	GRANTED	7
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <7
ORDER BY id DESC
FOR UPDATE;
id
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X,GAP	GRANTED	7
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <=7
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <=7
ORDER BY id DESC
FOR UPDATE;
id
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X,GAP	GRANTED	9
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <=8
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X,GAP	GRANTED	9
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <=8
ORDER BY id DESC
FOR UPDATE;
id
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X,GAP	GRANTED	9
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <9
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X,GAP	GRANTED	9
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <9
ORDER BY id DESC
FOR UPDATE;
id
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X,GAP	GRANTED	9
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <=9
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <=9
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <=10
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <=10
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >=10
ORDER BY id ASC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >=10
ORDER BY id DESC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >9
ORDER BY id ASC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >9
ORDER BY id DESC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >=9
ORDER BY id ASC
FOR UPDATE;
id
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	9
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >=9
ORDER BY id DESC
FOR UPDATE;
id
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >3
ORDER BY id ASC
FOR UPDATE;
id
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >3
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >=3
ORDER BY id ASC
FOR UPDATE;
id
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >=3
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >=2
ORDER BY id ASC
FOR UPDATE;
id
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >=2
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >1
ORDER BY id ASC
FOR UPDATE;
id
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >1
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >=1
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >=1
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >0
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >0
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
DROP TABLE t1;
INSERT INTO t2 VALUES (1),  (3), (4), (5), (6), (7),  (9);
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <=0
ORDER BY id ASC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <=0
ORDER BY id DESC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <1
ORDER BY id ASC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <1
ORDER BY id DESC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <=1
ORDER BY id ASC
FOR UPDATE;
id
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <=1
ORDER BY id DESC
FOR UPDATE;
id
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	1
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <7
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <7
ORDER BY id DESC
FOR UPDATE;
id
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <=7
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <=7
ORDER BY id DESC
FOR UPDATE;
id
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	7
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <=8
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <=8
ORDER BY id DESC
FOR UPDATE;
id
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <9
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <9
ORDER BY id DESC
FOR UPDATE;
id
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <=9
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <=9
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	9
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <=10
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <=10
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >=10
ORDER BY id ASC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	9
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >=10
ORDER BY id DESC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	9
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >9
ORDER BY id ASC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	9
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >9
ORDER BY id DESC
FOR UPDATE;
id
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	9
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >=9
ORDER BY id ASC
FOR UPDATE;
id
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X,GAP	GRANTED	7
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >=9
ORDER BY id DESC
FOR UPDATE;
id
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	9
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >3
ORDER BY id ASC
FOR UPDATE;
id
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X,GAP	GRANTED	3
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >3
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X,GAP	GRANTED	3
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >=3
ORDER BY id ASC
FOR UPDATE;
id
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X,GAP	GRANTED	1
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >=3
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >=2
ORDER BY id ASC
FOR UPDATE;
id
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X,GAP	GRANTED	1
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >=2
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X,GAP	GRANTED	1
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >1
ORDER BY id ASC
FOR UPDATE;
id
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X,GAP	GRANTED	1
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >1
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X,GAP	GRANTED	1
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >=1
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >=1
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >0
ORDER BY id ASC
FOR UPDATE;
id
1
3
4
5
6
7
9
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >0
ORDER BY id DESC
FOR UPDATE;
id
9
7
6
5
4
3
1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
PRIMARY	RECORD	X	GRANTED	9
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
DROP TABLE t2;
CREATE TABLE t1 (
id1 INT NOT NULL,
id2 INT NOT NULL,
PRIMARY KEY (id1 ASC, id2 ASC)
) ENGINE=InnoDB;
CREATE TABLE t2 (
id1 INT NOT NULL,
id2 INT NOT NULL,
PRIMARY KEY (id1 ASC, id2 DESC)
) ENGINE=InnoDB;
CREATE TABLE t3 (
id1 INT NOT NULL,
id2 INT NOT NULL,
PRIMARY KEY (id1 DESC, id2 ASC)
) ENGINE=InnoDB;
CREATE TABLE t4 (
id1 INT NOT NULL,
id2 INT NOT NULL,
PRIMARY KEY (id1 DESC, id2 DESC)
) ENGINE=InnoDB;
SET @conditions =  CONCAT(
'<0 <1 <3 <=3 <=4 <5 <=5 ',
'>6 >5 >3 >=3 >=2 >1 >=1'
);
INSERT INTO t1 (id1,id2) VALUES
(1,1),(1,3),(1,5),(3,1),(3,3),(3,5),(5,1),(5,3),(5,5);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <0
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <0
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <0
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <0
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <1
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <1
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <1
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <1
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X,GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X,GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <=3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <=3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <=4
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=4
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <=4
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=4
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <5
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <5
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <5
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <5
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <=5
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
3	1
3	3
3	5
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=5
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 <=5
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
3	5
3	3
3	1
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=5
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >6
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >6
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >6
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >6
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >5
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >5
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >5
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >5
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >=3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >=3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >=2
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=2
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >=2
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=2
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >1
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >1
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >1
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >1
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >=1
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
3	1
3	3
3	5
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=1
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1 >=1
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
3	5
3	3
3	1
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=1
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
DROP TABLE t1;
INSERT INTO t2 (id1,id2) VALUES
(1,1),(1,3),(1,5),(3,1),(3,3),(3,5),(5,1),(5,3),(5,5);
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <0
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <0
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <0
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <0
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <1
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <1
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <1
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <1
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <=3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <=3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <=4
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=4
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <=4
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=4
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <5
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <5
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <5
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <5
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <=5
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
3	5
3	3
3	1
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=5
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 <=5
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
3	1
3	3
3	5
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=5
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >6
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >6
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >6
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >6
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >5
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >5
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >5
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >5
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >=3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >=3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >=2
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=2
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >=2
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=2
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >1
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >1
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >1
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >1
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >=1
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
3	5
3	3
3	1
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=1
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1 >=1
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
3	1
3	3
3	5
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=1
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
ROLLBACK;
DROP TABLE t2;
INSERT INTO t3 (id1,id2) VALUES
(1,1),(1,3),(1,5),(3,1),(3,3),(3,5),(5,1),(5,3),(5,5);
ANALYZE TABLE t3;
Table	Op	Msg_type	Msg_text
test.t3	analyze	status	OK
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <0
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <0
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <0
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <0
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <1
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <1
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <1
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <1
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X,GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X,GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <=3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <=3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <=4
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=4
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <=4
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=4
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <5
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <5
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <5
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <5
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <=5
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
3	5
3	3
3	1
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=5
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 <=5
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
3	1
3	3
3	5
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=5
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >6
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >6
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >6
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >6
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >5
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >5
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >5
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >5
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >=3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >=3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >=2
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=2
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >=2
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=2
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >1
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >1
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >1
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >1
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >=1
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
3	5
3	3
3	1
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=1
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1 >=1
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
3	1
3	3
3	5
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t3 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=1
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't3';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 1
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3, 1
ROLLBACK;
DROP TABLE t3;
INSERT INTO t4 (id1,id2) VALUES
(1,1),(1,3),(1,5),(3,1),(3,3),(3,5),(5,1),(5,3),(5,5);
ANALYZE TABLE t4;
Table	Op	Msg_type	Msg_text
test.t4	analyze	status	OK
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <0
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <0
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <0
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <0
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <1
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <1
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <1
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <1
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <=3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <=3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <=4
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=4
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <=4
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=4
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <5
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <5
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <5
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <5
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <=5
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
3	1
3	3
3	5
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=5
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 <=5
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
3	5
3	3
3	1
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 <=5
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >6
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >6
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >6
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >6
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >5
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >5
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >5
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	5, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >5
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X,GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 3
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >=3
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=3
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >=3
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=3
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >=2
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=2
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >=2
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=2
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >1
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >1
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >1
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >1
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X,GAP	GRANTED	3, 1
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >=1
ORDER BY id1 ASC
FOR UPDATE;
id1	id2
1	1
1	3
1	5
3	1
3	3
3	5
5	1
5	3
5	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=1
ORDER BY id2 ASC
FOR UPDATE;
id1	id2
3	1
3	3
3	5
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X,GAP	GRANTED	1, 5
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1 >=1
ORDER BY id1 DESC
FOR UPDATE;
id1	id2
5	5
5	3
5	1
3	5
3	3
3	1
1	5
1	3
1	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1, 1
PRIMARY	RECORD	X	GRANTED	1, 3
PRIMARY	RECORD	X	GRANTED	1, 5
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
PRIMARY	RECORD	X	GRANTED	5, 1
PRIMARY	RECORD	X	GRANTED	5, 3
PRIMARY	RECORD	X	GRANTED	5, 5
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t4 FORCE INDEX (PRIMARY)
WHERE id1=3 AND id2 >=1
ORDER BY id2 DESC
FOR UPDATE;
id1	id2
3	5
3	3
3	1
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't4';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	3, 1
PRIMARY	RECORD	X	GRANTED	3, 3
PRIMARY	RECORD	X	GRANTED	3, 5
ROLLBACK;
DROP TABLE t4;
CREATE TABLE t1 (
id VARCHAR(100) NOT NULL,
PRIMARY KEY (id(1) ASC)
) ENGINE=InnoDB COLLATE utf8mb4_0900_bin;
CREATE TABLE t2 (
id VARCHAR(100) NOT NULL,
PRIMARY KEY (id(1) DESC)
) ENGINE=InnoDB COLLATE utf8mb4_0900_bin;
SET @conditions = CONCAT(
'<="c" <="d" <"e" <"ee" <="ee" <="ec" <="ef" ',
'>="g" >="f" >"e" >"ee" >="ee" >="ef" >="ec"'
);
INSERT INTO t1
VALUES ("aa"), ("bb"), ("cc"),   ("ee"),   ("gg"), ("hh"), ("ii");
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="c"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="c"
ORDER BY id DESC
FOR UPDATE;
id
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="d"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
cc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X,GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="d"
ORDER BY id DESC
FOR UPDATE;
id
cc
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X,GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <"e"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
cc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <"e"
ORDER BY id DESC
FOR UPDATE;
id
cc
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <"ee"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
cc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <"ee"
ORDER BY id DESC
FOR UPDATE;
id
cc
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="ee"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
cc
ee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="ee"
ORDER BY id DESC
FOR UPDATE;
id
ee
cc
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="ec"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
cc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="ec"
ORDER BY id DESC
FOR UPDATE;
id
cc
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="ef"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
cc
ee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="ef"
ORDER BY id DESC
FOR UPDATE;
id
ee
cc
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="g"
ORDER BY id ASC
FOR UPDATE;
id
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'g'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="g"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'g'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="f"
ORDER BY id ASC
FOR UPDATE;
id
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="f"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >"e"
ORDER BY id ASC
FOR UPDATE;
id
ee
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >"e"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
ee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >"ee"
ORDER BY id ASC
FOR UPDATE;
id
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >"ee"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="ee"
ORDER BY id ASC
FOR UPDATE;
id
ee
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="ee"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
ee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="ef"
ORDER BY id ASC
FOR UPDATE;
id
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="ef"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="ec"
ORDER BY id ASC
FOR UPDATE;
id
ee
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="ec"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
ee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
DROP TABLE t1;
INSERT INTO t2
VALUES ("aa"), ("bb"), ("cc"),   ("ee"),   ("gg"), ("hh"), ("ii");
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="c"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'c'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="c"
ORDER BY id DESC
FOR UPDATE;
id
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'c'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="d"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
cc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="d"
ORDER BY id DESC
FOR UPDATE;
id
cc
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <"e"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
cc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <"e"
ORDER BY id DESC
FOR UPDATE;
id
cc
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <"ee"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
cc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <"ee"
ORDER BY id DESC
FOR UPDATE;
id
cc
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="ee"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
cc
ee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="ee"
ORDER BY id DESC
FOR UPDATE;
id
ee
cc
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="ec"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
cc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="ec"
ORDER BY id DESC
FOR UPDATE;
id
cc
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="ef"
ORDER BY id ASC
FOR UPDATE;
id
aa
bb
cc
ee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="ef"
ORDER BY id DESC
FOR UPDATE;
id
ee
cc
bb
aa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'a'
PRIMARY	RECORD	X	GRANTED	'b'
PRIMARY	RECORD	X	GRANTED	'c'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="g"
ORDER BY id ASC
FOR UPDATE;
id
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="g"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="f"
ORDER BY id ASC
FOR UPDATE;
id
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X,GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="f"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
PRIMARY	RECORD	X,GAP	GRANTED	'e'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >"e"
ORDER BY id ASC
FOR UPDATE;
id
ee
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'e'
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >"e"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
ee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'e'
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >"ee"
ORDER BY id ASC
FOR UPDATE;
id
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'e'
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >"ee"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'e'
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="ee"
ORDER BY id ASC
FOR UPDATE;
id
ee
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'e'
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="ee"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
ee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'e'
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="ef"
ORDER BY id ASC
FOR UPDATE;
id
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'e'
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="ef"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'e'
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="ec"
ORDER BY id ASC
FOR UPDATE;
id
ee
gg
hh
ii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'e'
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="ec"
ORDER BY id DESC
FOR UPDATE;
id
ii
hh
gg
ee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'e'
PRIMARY	RECORD	X	GRANTED	'g'
PRIMARY	RECORD	X	GRANTED	'h'
PRIMARY	RECORD	X	GRANTED	'i'
ROLLBACK;
DROP TABLE t2;
CREATE TABLE t1 (
id VARCHAR(100) NOT NULL,
PRIMARY KEY (id(2) ASC)
) ENGINE=InnoDB;
CREATE TABLE t2 (
id VARCHAR(100) NOT NULL,
PRIMARY KEY (id(2) DESC)
) ENGINE=InnoDB;
SET @conditions = CONCAT(
'<="c" <="d" <"e" <"ee" <="ee" <="ec" <="ef" ',
'>="g" >="f" >"e" >"ee" >="ee" >="ef" >="ec"'
);
INSERT INTO t1
VALUES ("aaa"), ("bbb"), ("ccc"),   ("eee"),   ("ggg"), ("hhh"), ("iii");
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="c"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X,GAP	GRANTED	'cc'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="c"
ORDER BY id DESC
FOR UPDATE;
id
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X,GAP	GRANTED	'cc'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="d"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
ccc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X,GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="d"
ORDER BY id DESC
FOR UPDATE;
id
ccc
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X,GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <"e"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
ccc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X,GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <"e"
ORDER BY id DESC
FOR UPDATE;
id
ccc
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X,GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <"ee"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
ccc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <"ee"
ORDER BY id DESC
FOR UPDATE;
id
ccc
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="ee"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
ccc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="ee"
ORDER BY id DESC
FOR UPDATE;
id
ccc
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="ec"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
ccc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X,GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="ec"
ORDER BY id DESC
FOR UPDATE;
id
ccc
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X,GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="ef"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
ccc
eee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X,GAP	GRANTED	'gg'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id <="ef"
ORDER BY id DESC
FOR UPDATE;
id
eee
ccc
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X,GAP	GRANTED	'gg'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="g"
ORDER BY id ASC
FOR UPDATE;
id
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="g"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="f"
ORDER BY id ASC
FOR UPDATE;
id
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="f"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >"e"
ORDER BY id ASC
FOR UPDATE;
id
eee
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >"e"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
eee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >"ee"
ORDER BY id ASC
FOR UPDATE;
id
eee
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >"ee"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
eee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="ee"
ORDER BY id ASC
FOR UPDATE;
id
eee
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="ee"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
eee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="ef"
ORDER BY id ASC
FOR UPDATE;
id
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="ef"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="ec"
ORDER BY id ASC
FOR UPDATE;
id
eee
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t1 FORCE INDEX (PRIMARY)
WHERE id >="ec"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
eee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
DROP TABLE t1;
INSERT INTO t2
VALUES ("aaa"), ("bbb"), ("ccc"),   ("eee"),   ("ggg"), ("hhh"), ("iii");
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="c"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="c"
ORDER BY id DESC
FOR UPDATE;
id
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="d"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
ccc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="d"
ORDER BY id DESC
FOR UPDATE;
id
ccc
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <"e"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
ccc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <"e"
ORDER BY id DESC
FOR UPDATE;
id
ccc
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <"ee"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
ccc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <"ee"
ORDER BY id DESC
FOR UPDATE;
id
ccc
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="ee"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
ccc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="ee"
ORDER BY id DESC
FOR UPDATE;
id
ccc
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="ec"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
ccc
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="ec"
ORDER BY id DESC
FOR UPDATE;
id
ccc
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="ef"
ORDER BY id ASC
FOR UPDATE;
id
aaa
bbb
ccc
eee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id <="ef"
ORDER BY id DESC
FOR UPDATE;
id
eee
ccc
bbb
aaa
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'aa'
PRIMARY	RECORD	X	GRANTED	'bb'
PRIMARY	RECORD	X	GRANTED	'cc'
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	supremum pseudo-record
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="g"
ORDER BY id ASC
FOR UPDATE;
id
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X,GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="g"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X,GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="f"
ORDER BY id ASC
FOR UPDATE;
id
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X,GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="f"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X,GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >"e"
ORDER BY id ASC
FOR UPDATE;
id
eee
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X,GAP	GRANTED	'cc'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >"e"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
eee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X,GAP	GRANTED	'cc'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >"ee"
ORDER BY id ASC
FOR UPDATE;
id
eee
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >"ee"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
eee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="ee"
ORDER BY id ASC
FOR UPDATE;
id
eee
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="ee"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
eee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="ef"
ORDER BY id ASC
FOR UPDATE;
id
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X,GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="ef"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X,GAP	GRANTED	'ee'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="ec"
ORDER BY id ASC
FOR UPDATE;
id
eee
ggg
hhh
iii
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X,GAP	GRANTED	'cc'
ROLLBACK;
BEGIN;
SELECT *
FROM t2 FORCE INDEX (PRIMARY)
WHERE id >="ec"
ORDER BY id DESC
FOR UPDATE;
id
iii
hhh
ggg
eee
SELECT index_name,lock_type,lock_mode,lock_status,lock_data
FROM performance_schema.data_locks
WHERE object_name = 't2';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	'ee'
PRIMARY	RECORD	X	GRANTED	'gg'
PRIMARY	RECORD	X	GRANTED	'hh'
PRIMARY	RECORD	X	GRANTED	'ii'
PRIMARY	RECORD	X,GAP	GRANTED	'cc'
ROLLBACK;
DROP TABLE t2;
CREATE TABLE t1 (
id INT NOT NULL,
PRIMARY KEY (id ASC)
) ENGINE=InnoDB;
INSERT INTO t1 VALUES (1),  (3), (4), (5), (6), (7),  (9);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
BEGIN;
SELECT * FROM t1 WHERE id=7 FOR UPDATE;
id
7
BEGIN;
SET DEBUG_SYNC='lock_wait_will_wait SIGNAL con2_will_wait';
SELECT * FROM t1 FORCE INDEX (PRIMARY) WHERE id<=7 FOR UPDATE;;
SET DEBUG_SYNC='now WAIT_FOR con2_will_wait';
ROLLBACK;
id
1
3
4
5
6
7
DROP TABLE t1;
CREATE TABLE t1 (
id INT NOT NULL,
PRIMARY KEY (id ASC)
) ENGINE=InnoDB;
INSERT INTO t1 VALUES (1),  (3), (4), (5), (6), (7),  (9);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SET GLOBAL innodb_purge_stop_now = ON;
DELETE FROM t1 WHERE id=7;
BEGIN;
SELECT * FROM t1 FORCE INDEX (PRIMARY) WHERE id<=7 FOR UPDATE;
id
1
3
4
5
6
SELECT index_name, lock_type, lock_mode, lock_status, lock_data
FROM performance_schema.data_locks
WHERE object_name='t1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
PRIMARY	RECORD	X	GRANTED	6
PRIMARY	RECORD	X	GRANTED	7
ROLLBACK;
SET GLOBAL innodb_purge_run_now = ON;
DROP TABLE t1;
CREATE TABLE t1 (
id INT NOT NULL,
PRIMARY KEY (id ASC)
) ENGINE=InnoDB;
INSERT INTO t1 VALUES (1),  (3), (4), (5), (6), (7),  (9);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SET GLOBAL innodb_purge_stop_now = ON;
DELETE FROM t1 WHERE id=7;
BEGIN;
SELECT * FROM t1 WHERE id=7 FOR UPDATE;
id
BEGIN;
SET DEBUG_SYNC='lock_wait_will_wait SIGNAL con2_will_wait';
SELECT * FROM t1 FORCE INDEX (PRIMARY) WHERE id<=7 FOR UPDATE;;
SET DEBUG_SYNC='now WAIT_FOR con2_will_wait';
ROLLBACK;
id
1
3
4
5
6
SET GLOBAL innodb_purge_run_now = ON;
DROP TABLE t1;
CREATE TABLE t1 (
id INT NOT NULL,
PRIMARY KEY (id ASC)
) ENGINE=InnoDB;
INSERT INTO t1 VALUES (1),  (3), (4), (5), (6), (7),  (9);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SET GLOBAL innodb_purge_stop_now = ON;
DELETE FROM t1 WHERE id=7;
BEGIN;
INSERT INTO t1 VALUES (7);
BEGIN;
SET DEBUG_SYNC='lock_wait_will_wait SIGNAL con2_will_wait';
SELECT * FROM t1 FORCE INDEX (PRIMARY) WHERE id<=7 FOR UPDATE;;
SET DEBUG_SYNC='now WAIT_FOR con2_will_wait';
ROLLBACK;
id
1
3
4
5
6
SET GLOBAL innodb_purge_run_now = ON;
DROP TABLE t1;
CREATE TABLE t1 (
id INT NOT NULL,
PRIMARY KEY (id ASC)
) ENGINE=InnoDB;
INSERT INTO t1 VALUES (1),  (3), (4), (5), (6), (7),  (9);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SET GLOBAL innodb_purge_stop_now = ON;
DELETE FROM t1 WHERE id=7;
BEGIN;
INSERT INTO t1 VALUES (7);
BEGIN;
SET DEBUG_SYNC='lock_wait_will_wait SIGNAL con2_will_wait';
SELECT * FROM t1 FORCE INDEX (PRIMARY) WHERE id<=7 FOR UPDATE;;
SET DEBUG_SYNC='now WAIT_FOR con2_will_wait';
COMMIT;
id
1
3
4
5
6
7
SET GLOBAL innodb_purge_run_now = ON;
DROP TABLE t1;
CREATE TABLE t1 (
id INT NOT NULL,
val INT,
PRIMARY KEY (id ASC)
) ENGINE=InnoDB;
INSERT INTO t1 (id,val) VALUES (1,1),(2,2) ,(3,3), (4,4), (5,5), (6,6),   (9,9);
BEGIN;
DELETE FROM t1 WHERE id=6;
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
BEGIN;
SET DEBUG_SYNC='
    semi_consistent_read_would_wait
    SIGNAL con2_would_wait
    WAIT_FOR con2_can_peek';
SET DEBUG_SYNC='
    row_search_for_mysql_before_return
    SIGNAL con2_returns_row
    WAIT_FOR con2_can_return_row
    EXECUTE 6';
UPDATE t1 SET val=13 WHERE id<=6;
Expecting row number 1
SET DEBUG_SYNC='now WAIT_FOR con2_returns_row';
SET DEBUG_SYNC='now SIGNAL con2_can_return_row';
Expecting row number 2
SET DEBUG_SYNC='now WAIT_FOR con2_returns_row';
SET DEBUG_SYNC='now SIGNAL con2_can_return_row';
Expecting row number 3
SET DEBUG_SYNC='now WAIT_FOR con2_returns_row';
SET DEBUG_SYNC='now SIGNAL con2_can_return_row';
Expecting row number 4
SET DEBUG_SYNC='now WAIT_FOR con2_returns_row';
SET DEBUG_SYNC='now SIGNAL con2_can_return_row';
Expecting row number 5
SET DEBUG_SYNC='now WAIT_FOR con2_returns_row';
SET DEBUG_SYNC='now SIGNAL con2_can_return_row';
SET DEBUG_SYNC='now WAIT_FOR con2_would_wait';
SELECT index_name, lock_type, lock_mode, lock_status, lock_data
FROM performance_schema.data_locks
WHERE object_name='t1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	1
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	2
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	4
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	5
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	6
SET DEBUG_SYNC='now SIGNAL con2_can_peek';
SET DEBUG_SYNC='now WAIT_FOR con2_returns_row';
SELECT index_name, lock_type, lock_mode, lock_status, lock_data
FROM performance_schema.data_locks
WHERE object_name='t1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	1
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	2
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	4
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	5
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	6
COMMIT;
SELECT index_name, lock_type, lock_mode, lock_status, lock_data
FROM performance_schema.data_locks
WHERE object_name='t1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	1
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	2
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	4
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	5
SET DEBUG_SYNC='now SIGNAL con2_can_return_row';
SELECT index_name, lock_type, lock_mode, lock_status, lock_data
FROM performance_schema.data_locks
WHERE object_name='t1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	1
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	2
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	3
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	4
PRIMARY	RECORD	X,REC_NOT_GAP	GRANTED	5
ROLLBACK;
DROP TABLE t1;
#
# Bug #30112238 - [INNODB] ASSERTION FAILURE: ROW0SEL.CC.*DIRECTION != 0
#
CREATE TABLE t1 (
id INT PRIMARY KEY
);
INSERT INTO t1 (id) VALUES (1), (2), (3), (4), (5), (6), (7), (8), (9);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
BEGIN;
SELECT * FROM t1;
id
1
2
3
4
5
6
7
8
9
DELETE FROM t1 WHERE id<=5;
BEGIN;
SELECT index_name, lock_type, lock_mode, lock_status, lock_data FROM performance_schema.data_locks WHERE object_name='t1';
index_name	lock_type	lock_mode	lock_status	lock_data
SELECT id FROM t1 FORCE INDEX (PRIMARY) WHERE id<=5 FOR UPDATE;
id
SELECT index_name, lock_type, lock_mode, lock_status, lock_data FROM performance_schema.data_locks WHERE object_name='t1';
index_name	lock_type	lock_mode	lock_status	lock_data
NULL	TABLE	IX	GRANTED	NULL
PRIMARY	RECORD	X	GRANTED	1
PRIMARY	RECORD	X	GRANTED	2
PRIMARY	RECORD	X	GRANTED	3
PRIMARY	RECORD	X	GRANTED	4
PRIMARY	RECORD	X	GRANTED	5
COMMIT;
DROP TABLE t1;
