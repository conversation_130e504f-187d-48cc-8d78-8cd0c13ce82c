# Copyright (c) 2010, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

# the benchmark's metrics: measure time, memory use
logRealTime=true
logCpuTime=true
logMemUsage=false
includeFullGC=false
logSumOfOps=true

# the number of rows for tables A in a transaction and a scale factor
#aStart=10
#aStart=256
aStart=1024
#aStart=4096
#aEnd=10
#aEnd=256
aEnd=1024
#aEnd=4096
#aEnd=16384
#aEnd=65536
aIncr=4

# the number of rows for tables B0 in a transaction and a scale factor
#bStart=10
#bStart=256
bStart=1024
#bStart=4096
#bEnd=40
#bEnd=256
bEnd=1024
#bEnd=4096
#bEnd=16384
#bEnd=65536
bIncr=4

# the max length (multiple of 10) up to which to scale up strings
#maxStringLength=10
maxStringLength=100
#maxStringLength=1000

# whether operations or connections are to be renewed after a run
renewOperations=false
renewConnection=false

# whether data objects can be cached/reused after a transaction
allowExtendedPC=false

# the number of warmup and finally recorded runs
warmupRuns=0
hotRuns=3

# CURRENT LIMITS:
#
# ndbd errors (increase SendBufferMemory) at:
# aEnd, bEnd >= 65536
#
# msqld errors with JDBC (increase MaxNoOfConcurrentOperations) at:
# aEnd, bEnd >= 16384 
#
# NdbApi (driver+load) at:
# aEnd, bEnd >= 4096:
#
# ClusterJ breaks (exclude=navA->B0,navA->B0_opt) at:
# aEnd, bEnd >= 4096
#
# not yet supported in NdbApiLoad:
# maxStringLength > 255 (or so)
#
# NdbApi (driver):
#   getVarchar100_batch
#   --> getVarchar()
#   !!! error in Operations.cpp, line: 262, code: 1218, msg: Send Buffers overloaded in NDB kernel.
# maxStringLength=100
