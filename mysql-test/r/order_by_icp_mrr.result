set optimizer_switch='index_condition_pushdown=on,mrr=on,mrr_cost_based=off';
drop table if exists t1,t2,t3;
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1 (
id int(6) DEFAULT '0' NOT NULL,
idservice int(5),
clee char(20) NOT NULL,
flag char(1),
KEY id (id),
PRIMARY KEY (clee)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (2,4,'6067169d','Y');
INSERT INTO t1 VALUES (2,5,'606716d1','Y');
INSERT INTO t1 VALUES (2,1,'606717c1','Y');
INSERT INTO t1 VALUES (3,1,'6067178d','Y');
INSERT INTO t1 VALUES (2,6,'60671515','Y');
INSERT INTO t1 VALUES (2,7,'60671569','Y');
INSERT INTO t1 VALUES (2,3,'dd','Y');
CREATE TABLE t2 (
id int(6) NOT NULL auto_increment,
description varchar(40) NOT NULL,
idform varchar(40),
ordre int(6) unsigned DEFAULT '0' NOT NULL,
image varchar(60),
PRIMARY KEY (id),
KEY id (id,ordre)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES (1,'Emettre un appel d''offres','en_construction.html',10,'emettre.gif');
INSERT INTO t2 VALUES (2,'Emettre des soumissions','en_construction.html',20,'emettre.gif');
INSERT INTO t2 VALUES (7,'Liste des t2','t2_liste_form.phtml',51060,'link.gif');
INSERT INTO t2 VALUES (8,'Consulter les soumissions','consulter_soumissions.phtml',200,'link.gif');
INSERT INTO t2 VALUES (9,'Ajouter un type de materiel','typeMateriel_ajoute_form.phtml',51000,'link.gif');
INSERT INTO t2 VALUES (10,'Lister/modifier un type de materiel','typeMateriel_liste_form.phtml',51010,'link.gif');
INSERT INTO t2 VALUES (3,'Créer une fiche de client','clients_ajoute_form.phtml',40000,'link.gif');
INSERT INTO t2 VALUES (4,'Modifier des clients','en_construction.html',40010,'link.gif');
INSERT INTO t2 VALUES (5,'Effacer des clients','en_construction.html',40020,'link.gif');
INSERT INTO t2 VALUES (6,'Ajouter un service','t2_ajoute_form.phtml',51050,'link.gif');
select t1.id,t1.idservice,t2.ordre,t2.description  from t1, t2 where t1.id = 2   and t1.idservice = t2.id  order by t2.ordre;
id	idservice	ordre	description
2	1	10	Emettre un appel d'offres
2	3	40000	Créer une fiche de client
2	4	40010	Modifier des clients
2	5	40020	Effacer des clients
2	6	51050	Ajouter un service
2	7	51060	Liste des t2
drop table t1,t2;
create table t1 (first char(10),last char(10));
insert into t1 values ("Michael","Widenius");
insert into t1 values ("Allan","Larsson");
insert into t1 values ("David","Axmark");
select concat(first," ",last) as name from t1 order by name;
name
Allan Larsson
David Axmark
Michael Widenius
select concat(last," ",first) as name from t1 order by name;
name
Axmark David
Larsson Allan
Widenius Michael
drop table t1;
create table t1 (i int);
insert into t1 values(1),(2),(1),(2),(1),(2),(3);
select distinct i from t1;
i
1
2
3
select distinct i from t1 order by rand(5);
i
1
2
3
select distinct i from t1 order by i desc;
i
3
2
1
select distinct i from t1 order by 1-i;
i
3
2
1
select distinct i from t1 order by mod(i,2),i;
i
2
1
3
drop table t1;
create table t1 ( pk     int primary key, name   varchar(255) not null, number varchar(255) not null);
insert into t1 values (1, 'Gamma',     '123'), (2, 'Gamma Ext', '123a'), (3, 'Alpha',     '001'), (4, 'Beta',      '200c');
select distinct t1.name as 'Building Name',t1.number as 'Building Number' from t1 order by t1.name asc;
Building Name	Building Number
Alpha	001
Beta	200c
Gamma	123
Gamma Ext	123a
drop table t1;
create table t1 (id int not null,col1 int not null,col2 int not null,index(col1));
insert into t1 values(1,2,2),(2,2,1),(3,1,2),(4,1,1),(5,1,4),(6,2,3),(7,3,1),(8,2,4);
select * from t1 order by col1,col2;
id	col1	col2
4	1	1
3	1	2
5	1	4
2	2	1
1	2	2
6	2	3
8	2	4
7	3	1
select col1 from t1 order by id;
col1
2
2
1
1
1
2
3
2
select col1 as id from t1 order by id;
id
1
1
1
2
2
2
2
3
select concat(col1) as id from t1 order by id;
id
1
1
1
2
2
2
2
3
drop table t1;
CREATE TABLE t1 (id int auto_increment primary key,aika varchar(40),aikakentta  timestamp);
insert into t1 (aika) values ('Keskiviikko');
insert into t1 (aika) values ('Tiistai');
insert into t1 (aika) values ('Maanantai');
insert into t1 (aika) values ('Sunnuntai');
SELECT FIELD(SUBSTRING(t1.aika,1,2),'Ma','Ti','Ke','To','Pe','La','Su') AS test FROM t1 ORDER by test;
test
1
2
3
7
drop table t1;
CREATE TABLE t1
(
a          int unsigned       NOT NULL,
b          int unsigned       NOT NULL,
c          int unsigned       NOT NULL,
UNIQUE(a),
INDEX(b),
INDEX(c)
);
CREATE TABLE t2
(
c          int unsigned       NOT NULL,
i          int unsigned       NOT NULL,
INDEX(c)
);
CREATE TABLE t3
(
c          int unsigned       NOT NULL,
v          varchar(64),
INDEX(c)
);
INSERT INTO t1 VALUES (1,1,1);
INSERT INTO t1 VALUES (2,1,2);
INSERT INTO t1 VALUES (3,2,1);
INSERT INTO t1 VALUES (4,2,2);
INSERT INTO t2 VALUES (1,50);
INSERT INTO t2 VALUES (2,25);
INSERT INTO t3 VALUES (1,'123 Park Place');
INSERT INTO t3 VALUES (2,'453 Boardwalk');
SELECT    a,b,if(b = 1,i,if(b = 2,v,''))
FROM      t1
LEFT JOIN t2 USING(c)
LEFT JOIN t3 ON t3.c = t1.c;
a	b	if(b = 1,i,if(b = 2,v,''))
1	1	50
2	1	25
3	2	123 Park Place
4	2	453 Boardwalk
SELECT    a,b,if(b = 1,i,if(b = 2,v,''))
FROM      t1
LEFT JOIN t2 ON t1.c = t2.c
LEFT JOIN t3 ON t3.c = t1.c;
a	b	if(b = 1,i,if(b = 2,v,''))
1	1	50
2	1	25
3	2	123 Park Place
4	2	453 Boardwalk
SELECT    a,b,if(b = 1,i,if(b = 2,v,''))
FROM      t1
LEFT JOIN t2 USING(c)
LEFT JOIN t3 ON t3.c = t1.c
ORDER BY a;
a	b	if(b = 1,i,if(b = 2,v,''))
1	1	50
2	1	25
3	2	123 Park Place
4	2	453 Boardwalk
SELECT    a,b,if(b = 1,i,if(b = 2,v,''))
FROM      t1
LEFT JOIN t2 ON t1.c = t2.c
LEFT JOIN t3 ON t3.c = t1.c
ORDER BY a;
a	b	if(b = 1,i,if(b = 2,v,''))
1	1	50
2	1	25
3	2	123 Park Place
4	2	453 Boardwalk
drop table t1,t2,t3;
create table t1 (ID int not null primary key, TransactionID int not null);
insert into t1 (ID, TransactionID) values  (1,  87), (2,  89), (3,  92), (4,  94), (5,  486), (6,  490), (7,  753), (9,  828), (10, 832), (11, 834), (12, 840);
create table t2 (ID int not null primary key, GroupID int not null);
insert into t2 (ID, GroupID) values (87,  87), (89,  89), (92,  92), (94,  94), (486, 486), (490, 490),(753, 753), (828, 828), (832, 832), (834, 834), (840, 840);
create table t3 (ID int not null primary key, DateOfAction date not null);
insert into t3 (ID, DateOfAction) values  (87,  '1999-07-19'), (89,  '1999-07-19'), (92,  '1999-07-19'), (94,  '1999-07-19'), (486, '1999-07-18'), (490, '2000-03-27'), (753, '2000-03-28'), (828, '1999-07-27'), (832, '1999-07-27'),(834, '1999-07-27'), (840, '1999-07-27');
select t3.DateOfAction, t1.TransactionID from t1 join t2 join t3 where t2.ID = t1.TransactionID and t3.ID = t2.GroupID order by t3.DateOfAction, t1.TransactionID;
DateOfAction	TransactionID
1999-07-18	486
1999-07-19	87
1999-07-19	89
1999-07-19	92
1999-07-19	94
1999-07-27	828
1999-07-27	832
1999-07-27	834
1999-07-27	840
2000-03-27	490
2000-03-28	753
select t3.DateOfAction, t1.TransactionID from t1 join t2 join t3 where t2.ID = t1.TransactionID and t3.ID = t2.GroupID order by t1.TransactionID,t3.DateOfAction;
DateOfAction	TransactionID
1999-07-19	87
1999-07-19	89
1999-07-19	92
1999-07-19	94
1999-07-18	486
2000-03-27	490
2000-03-28	753
1999-07-27	828
1999-07-27	832
1999-07-27	834
1999-07-27	840
drop table t1,t2,t3;
CREATE TABLE t1 (
member_id int(11) NOT NULL auto_increment,
inschrijf_datum varchar(20) NOT NULL default '',
lastchange_datum varchar(20) NOT NULL default '',
nickname varchar(20) NOT NULL default '',
password varchar(8) NOT NULL default '',
voornaam varchar(30) NOT NULL default '',
tussenvoegsels varchar(10) NOT NULL default '',
achternaam varchar(50) NOT NULL default '',
straat varchar(100) NOT NULL default '',
postcode varchar(10) NOT NULL default '',
wijk varchar(40) NOT NULL default '',
plaats varchar(50) NOT NULL default '',
telefoon varchar(10) NOT NULL default '',
geboortedatum date NOT NULL default '0000-00-00',
geslacht varchar(5) NOT NULL default '',
email varchar(80) NOT NULL default '',
uin varchar(15) NOT NULL default '',
homepage varchar(100) NOT NULL default '',
internet varchar(15) NOT NULL default '',
scherk varchar(30) NOT NULL default '',
favo_boek varchar(50) NOT NULL default '',
favo_tijdschrift varchar(50) NOT NULL default '',
favo_tv varchar(50) NOT NULL default '',
favo_eten varchar(50) NOT NULL default '',
favo_muziek varchar(30) NOT NULL default '',
info text NOT NULL default '',
ipnr varchar(30) NOT NULL default '',
PRIMARY KEY  (member_id)
) charset utf8mb4 ENGINE=MyISAM PACK_KEYS=1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1101	BLOB, TEXT, GEOMETRY or JSON column 'info' can't have a default value
insert into t1 (member_id) values (1),(2),(3);
Warnings:
Warning	1364	Field 'info' doesn't have a default value
select member_id, nickname, voornaam FROM t1
ORDER by lastchange_datum DESC LIMIT 2;
member_id	nickname	voornaam
1		
2		
drop table t1;
create table t1 (a int not null, b int, c varchar(10), key (a, b, c)) charset utf8mb4;
insert into t1 values (1, NULL, NULL), (1, NULL, 'b'), (1, 1, NULL), (1, 1, 'b'), (1, 1, 'b'), (2, 1, 'a'), (2, 1, 'b'), (2, 2, 'a'), (2, 2, 'b'), (2, 3, 'c'),(1,3,'b');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where (a = 1 and b is null and c = 'b') or (a > 2) order by a desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	a	a	52	NULL	2	100.00	Using where; Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where (((`test`.`t1`.`c` = 'b') and (`test`.`t1`.`a` = 1) and (`test`.`t1`.`b` is null)) or (`test`.`t1`.`a` > 2)) order by `test`.`t1`.`a` desc
select * from t1 where (a = 1 and b is null and c = 'b') or (a > 2) order by a desc;
a	b	c
1	NULL	b
explain select * from t1 where a >= 1 and a < 3 order by a desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	a	a	52	NULL	11	100.00	Using where; Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` >= 1) and (`test`.`t1`.`a` < 3)) order by `test`.`t1`.`a` desc
explain format=json select * from t1 where a >= 1 and a < 3 order by a desc;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.35"
    },
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t1",
        "access_type": "index",
        "possible_keys": [
          "a"
        ],
        "key": "a",
        "used_key_parts": [
          "a",
          "b",
          "c"
        ],
        "key_length": "52",
        "rows_examined_per_scan": 11,
        "rows_produced_per_join": 11,
        "filtered": "100.00",
        "backward_index_scan": true,
        "using_index": true,
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "1.10",
          "prefix_cost": "1.35",
          "data_read_per_join": "616"
        },
        "used_columns": [
          "a",
          "b",
          "c"
        ],
        "attached_condition": "((`test`.`t1`.`a` >= 1) and (`test`.`t1`.`a` < 3))"
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` >= 1) and (`test`.`t1`.`a` < 3)) order by `test`.`t1`.`a` desc
select * from t1 where a >= 1 and a < 3 order by a desc;
a	b	c
2	1	a
2	1	b
2	2	a
2	2	b
2	3	c
1	1	NULL
1	1	b
1	1	b
1	3	b
1	NULL	NULL
1	NULL	b
explain select * from t1 where a = 1 order by a desc, b desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	a	a	4	const	6	100.00	Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where (`test`.`t1`.`a` = 1) order by `test`.`t1`.`a` desc,`test`.`t1`.`b` desc
select * from t1 where a = 1 order by a desc, b desc;
a	b	c
1	1	NULL
1	1	b
1	1	b
1	3	b
1	NULL	NULL
1	NULL	b
explain select * from t1 where a = 1 and b is null order by a desc, b desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	a	a	9	const,const	2	100.00	Using where; Using index; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` = 1) and (`test`.`t1`.`b` is null)) order by `test`.`t1`.`a` desc,`test`.`t1`.`b` desc
select * from t1 where a = 1 and b is null order by a desc, b desc;
a	b	c
1	NULL	NULL
1	NULL	b
explain select * from t1 where a >= 1 and a < 3 and b >0 order by a desc,b desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	a	a	9	NULL	9	33.33	Using where; Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` >= 1) and (`test`.`t1`.`a` < 3) and (`test`.`t1`.`b` > 0)) order by `test`.`t1`.`a` desc,`test`.`t1`.`b` desc
explain select * from t1 where a = 2 and b >0 order by a desc,b desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	a	a	9	NULL	5	100.00	Using where; Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` = 2) and (`test`.`t1`.`b` > 0)) order by `test`.`t1`.`a` desc,`test`.`t1`.`b` desc
explain select * from t1 where a = 2 and b is null order by a desc,b desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	a	a	9	const,const	1	100.00	Using where; Using index; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` = 2) and (`test`.`t1`.`b` is null)) order by `test`.`t1`.`a` desc,`test`.`t1`.`b` desc
explain select * from t1 where a = 2 and (b is null or b > 0) order by a
desc,b desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	a	a	9	NULL	6	100.00	Using where; Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` = 2) and ((`test`.`t1`.`b` is null) or (`test`.`t1`.`b` > 0))) order by `test`.`t1`.`a` desc,`test`.`t1`.`b` desc
explain select * from t1 where a = 2 and b > 0 order by a desc,b desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	a	a	9	NULL	5	100.00	Using where; Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` = 2) and (`test`.`t1`.`b` > 0)) order by `test`.`t1`.`a` desc,`test`.`t1`.`b` desc
explain select * from t1 where a = 2 and b < 2 order by a desc,b desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	a	a	9	NULL	2	100.00	Using where; Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` = 2) and (`test`.`t1`.`b` < 2)) order by `test`.`t1`.`a` desc,`test`.`t1`.`b` desc
explain select * from t1 where a = 1 order by b desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	a	a	4	const	6	100.00	Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where (`test`.`t1`.`a` = 1) order by `test`.`t1`.`b` desc
select * from t1 where a = 1 order by b desc;
a	b	c
1	1	NULL
1	1	b
1	1	b
1	3	b
1	NULL	NULL
1	NULL	b
alter table t1 modify b int not null, modify c varchar(10) not null;
Warnings:
Warning	1265	Data truncated for column 'b' at row 1
Warning	1265	Data truncated for column 'c' at row 1
Warning	1265	Data truncated for column 'b' at row 2
Warning	1265	Data truncated for column 'c' at row 3
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 order by a, b, c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	a	50	NULL	11	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` order by `test`.`t1`.`a`,`test`.`t1`.`b`,`test`.`t1`.`c`
select * from t1 order by a, b, c;
a	b	c
1	0	
1	0	b
1	1	
1	1	b
1	1	b
1	3	b
2	1	a
2	1	b
2	2	a
2	2	b
2	3	c
explain select * from t1 order by a desc, b desc, c desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	a	50	NULL	11	100.00	Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` order by `test`.`t1`.`a` desc,`test`.`t1`.`b` desc,`test`.`t1`.`c` desc
select * from t1 order by a desc, b desc, c desc;
a	b	c
2	3	c
2	2	b
2	2	a
2	1	b
2	1	a
1	3	b
1	1	b
1	1	b
1	1	
1	0	b
1	0	
explain select * from t1 where (a = 1 and b = 1 and c = 'b') or (a > 2) order by a desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	a	a	50	NULL	3	100.00	Using where; Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where (((`test`.`t1`.`c` = 'b') and (`test`.`t1`.`b` = 1) and (`test`.`t1`.`a` = 1)) or (`test`.`t1`.`a` > 2)) order by `test`.`t1`.`a` desc
select * from t1 where (a = 1 and b = 1 and c = 'b') or (a > 2) order by a desc;
a	b	c
1	1	b
1	1	b
explain select * from t1 where a < 2 and b <= 1 order by a desc, b desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	a	a	4	NULL	6	33.33	Using where; Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` < 2) and (`test`.`t1`.`b` <= 1)) order by `test`.`t1`.`a` desc,`test`.`t1`.`b` desc
select * from t1 where a < 2 and b <= 1 order by a desc, b desc;
a	b	c
1	0	
1	0	b
1	1	
1	1	b
1	1	b
select count(*) from t1 where a < 5 and b > 0;
count(*)
9
select * from t1 where a < 5 and b > 0 order by a desc,b desc;
a	b	c
2	1	a
2	1	b
2	2	a
2	2	b
2	3	c
1	1	
1	1	b
1	1	b
1	3	b
explain select * from t1 where a between 1 and 3 and b <= 1 order by a desc, b desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	a	a	50	NULL	11	33.33	Using where; Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` between 1 and 3) and (`test`.`t1`.`b` <= 1)) order by `test`.`t1`.`a` desc,`test`.`t1`.`b` desc
select * from t1 where a between 1 and 3 and b <= 1 order by a desc, b desc;
a	b	c
2	1	a
2	1	b
1	0	
1	0	b
1	1	
1	1	b
1	1	b
explain select * from t1 where a between 0 and 1 order by a desc, b desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	a	a	4	NULL	6	100.00	Using where; Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where (`test`.`t1`.`a` between 0 and 1) order by `test`.`t1`.`a` desc,`test`.`t1`.`b` desc
select * from t1 where a between 0 and 1 order by a desc, b desc;
a	b	c
1	0	
1	0	b
1	1	
1	1	b
1	1	b
1	3	b
drop table t1;
CREATE TABLE t1 (
gid int(10) unsigned NOT NULL auto_increment,
cid smallint(5) unsigned NOT NULL default '0',
PRIMARY KEY  (gid),
KEY component_id (cid)
) ENGINE=MyISAM;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (103853,108),(103867,108),(103962,108),(104505,108),(104619,108),(104620,108);
ALTER TABLE t1 add skr int(10) not null;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE t2 (
gid int(10) unsigned NOT NULL default '0',
uid smallint(5) unsigned NOT NULL default '1',
sid tinyint(3) unsigned NOT NULL default '1',
PRIMARY KEY  (gid),
KEY uid (uid),
KEY status_id (sid)
) ENGINE=MyISAM;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES (103853,250,5),(103867,27,5),(103962,27,5),(104505,117,5),(104619,75,5),(104620,15,5);
CREATE TABLE t3 (
uid smallint(6) NOT NULL auto_increment,
PRIMARY KEY  (uid)
) ENGINE=MyISAM;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t3 VALUES (1),(15),(27),(75),(117),(250);
ALTER TABLE t3 add skr int(10) not null;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
select t1.gid, t2.sid, t3.uid from t2, t1, t3 where t2.gid = t1.gid and t2.uid = t3.uid order by t3.uid, t1.gid;
gid	sid	uid
104620	5	15
103867	5	27
103962	5	27
104619	5	75
104505	5	117
103853	5	250
select t1.gid, t2.sid, t3.uid from t3, t2, t1 where t2.gid = t1.gid and t2.uid = t3.uid order by t3.uid, t1.gid;
gid	sid	uid
104620	5	15
103867	5	27
103962	5	27
104619	5	75
104505	5	117
103853	5	250
EXPLAIN select t1.gid, t2.sid, t3.uid from t3, t2, t1 where t2.gid = t1.gid and t2.uid = t3.uid order by t1.gid, t3.uid;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	NULL	index	PRIMARY	PRIMARY	2	NULL	6	100.00	Using index; Using temporary; Using filesort
1	SIMPLE	t2	NULL	ref	PRIMARY,uid	uid	2	test.t3.uid	1	100.00	Using index condition
1	SIMPLE	t1	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t2.gid	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`gid` AS `gid`,`test`.`t2`.`sid` AS `sid`,`test`.`t3`.`uid` AS `uid` from `test`.`t3` join `test`.`t2` join `test`.`t1` where ((`test`.`t1`.`gid` = `test`.`t2`.`gid`) and (`test`.`t2`.`uid` = `test`.`t3`.`uid`)) order by `test`.`t1`.`gid`,`test`.`t3`.`uid`
EXPLAIN SELECT t1.gid, t3.uid from t1, t3 where t1.gid = t3.uid order by t1.gid,t3.skr;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	NULL	ALL	PRIMARY	NULL	NULL	NULL	6	100.00	Using temporary; Using filesort
1	SIMPLE	t1	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.uid	1	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`gid` AS `gid`,`test`.`t3`.`uid` AS `uid` from `test`.`t1` join `test`.`t3` where (`test`.`t1`.`gid` = `test`.`t3`.`uid`) order by `test`.`t1`.`gid`,`test`.`t3`.`skr`
EXPLAIN SELECT t1.gid, t2.sid, t3.uid from t2, t1, t3 where t2.gid = t1.gid and t2.uid = t3.uid order by t3.uid, t1.gid;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY,uid	NULL	NULL	NULL	6	100.00	Using temporary; Using filesort
1	SIMPLE	t1	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t2.gid	1	100.00	Using index
1	SIMPLE	t3	NULL	eq_ref	PRIMARY	PRIMARY	2	test.t2.uid	1	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`gid` AS `gid`,`test`.`t2`.`sid` AS `sid`,`test`.`t3`.`uid` AS `uid` from `test`.`t2` join `test`.`t1` join `test`.`t3` where ((`test`.`t1`.`gid` = `test`.`t2`.`gid`) and (`test`.`t2`.`uid` = `test`.`t3`.`uid`)) order by `test`.`t3`.`uid`,`test`.`t1`.`gid`
EXPLAIN SELECT t1.gid, t3.uid from t1, t3 where t1.gid = t3.uid order by t3.skr,t1.gid;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	NULL	ALL	PRIMARY	NULL	NULL	NULL	6	100.00	Using temporary; Using filesort
1	SIMPLE	t1	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t3.uid	1	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`gid` AS `gid`,`test`.`t3`.`uid` AS `uid` from `test`.`t1` join `test`.`t3` where (`test`.`t1`.`gid` = `test`.`t3`.`uid`) order by `test`.`t3`.`skr`,`test`.`t1`.`gid`
EXPLAIN SELECT t1.gid, t3.uid from t1, t3 where t1.skr = t3.uid order by t1.gid,t3.skr;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	6	100.00	Using temporary; Using filesort
1	SIMPLE	t3	NULL	eq_ref	PRIMARY	PRIMARY	2	test.t1.skr	1	100.00	Using index condition
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`gid` AS `gid`,`test`.`t3`.`uid` AS `uid` from `test`.`t1` join `test`.`t3` where (`test`.`t1`.`skr` = `test`.`t3`.`uid`) order by `test`.`t1`.`gid`,`test`.`t3`.`skr`
drop table t1,t2,t3;
CREATE TABLE t1 (
`titre` char(80) NOT NULL default '',
`numeropost` mediumint(8) unsigned NOT NULL auto_increment,
`date` datetime NOT NULL default '0000-00-00 00:00:00',
`auteur` char(35) NOT NULL default '',
`icone` tinyint(2) unsigned NOT NULL default '0',
`lastauteur` char(35) NOT NULL default '',
`nbrep` smallint(6) unsigned NOT NULL default '0',
`dest` char(35) NOT NULL default '',
`lu` tinyint(1) unsigned NOT NULL default '0',
`vue` mediumint(8) unsigned NOT NULL default '0',
`ludest` tinyint(1) unsigned NOT NULL default '0',
`ouvert` tinyint(1) unsigned NOT NULL default '1',
PRIMARY KEY  (`numeropost`),
KEY `date` (`date`),
KEY `dest` (`dest`,`ludest`),
KEY `auteur` (`auteur`,`lu`),
KEY `auteur_2` (`auteur`,`date`),
KEY `dest_2` (`dest`,`date`)
) CHECKSUM=1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE t2 (
`numeropost` mediumint(8) unsigned NOT NULL default '0',
`pseudo` char(35) NOT NULL default '',
PRIMARY KEY  (`numeropost`,`pseudo`),
KEY `pseudo` (`pseudo`)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 (titre,auteur,dest) VALUES ('test','joce','bug');
INSERT INTO t2 (numeropost,pseudo) VALUES (1,'joce'),(1,'bug');
SELECT titre,t1.numeropost,auteur,icone,nbrep,0,date,vue,ouvert,lastauteur,dest FROM t2 LEFT JOIN t1 USING(numeropost) WHERE t2.pseudo='joce' ORDER BY date DESC LIMIT 0,30;
titre	numeropost	auteur	icone	nbrep	0	date	vue	ouvert	lastauteur	dest
test	1	joce	0	0	0	0000-00-00 00:00:00	0	1		bug
SELECT titre,numeropost,auteur,icone,nbrep,0,date,vue,ouvert,lastauteur,dest FROM t2 LEFT JOIN t1 USING(numeropost) WHERE t2.pseudo='joce' ORDER BY date DESC LIMIT 0,30;
titre	numeropost	auteur	icone	nbrep	0	date	vue	ouvert	lastauteur	dest
test	1	joce	0	0	0	0000-00-00 00:00:00	0	1		bug
SELECT titre,t1.numeropost,auteur,icone,nbrep,'0',date,vue,ouvert,lastauteur,dest FROM t2 LEFT JOIN t1 USING(numeropost) WHERE t2.pseudo='joce' ORDER BY date DESC LIMIT 0,30;
titre	numeropost	auteur	icone	nbrep	0	date	vue	ouvert	lastauteur	dest
test	1	joce	0	0	0	0000-00-00 00:00:00	0	1		bug
SELECT titre,numeropost,auteur,icone,nbrep,'0',date,vue,ouvert,lastauteur,dest FROM t2 LEFT JOIN t1 USING(numeropost) WHERE t2.pseudo='joce' ORDER BY date DESC LIMIT 0,30;
titre	numeropost	auteur	icone	nbrep	0	date	vue	ouvert	lastauteur	dest
test	1	joce	0	0	0	0000-00-00 00:00:00	0	1		bug
drop table t1,t2;
CREATE TABLE t1 (a int, b int);
INSERT INTO t1 VALUES (1, 2);
INSERT INTO t1 VALUES (3, 4);
INSERT INTO t1 VALUES (5, NULL);
SELECT * FROM t1 ORDER BY b;
a	b
5	NULL
1	2
3	4
SELECT * FROM t1 ORDER BY b DESC;
a	b
3	4
1	2
5	NULL
SELECT * FROM t1 ORDER BY (a + b);
a	b
5	NULL
1	2
3	4
SELECT * FROM t1 ORDER BY (a + b) DESC;
a	b
3	4
1	2
5	NULL
DROP TABLE t1;
create table t1(id int not null auto_increment primary key, t char(12));
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select id,t from t1 order by id;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	PRIMARY	4	NULL	1000	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`t` AS `t` from `test`.`t1` order by `test`.`t1`.`id`
explain select id,t from t1 force index (primary) order by id;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	PRIMARY	4	NULL	1000	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`t` AS `t` from `test`.`t1` FORCE INDEX (PRIMARY) order by `test`.`t1`.`id`
drop table t1;
CREATE TABLE t1 (
FieldKey varchar(36) NOT NULL default '',
LongVal bigint(20) default NULL,
StringVal mediumtext,
KEY FieldKey (FieldKey),
KEY LongField (FieldKey,LongVal),
KEY StringField (FieldKey,StringVal(32))
) charset utf8mb4;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES ('0',3,'0'),('0',2,'1'),('0',1,'2'),('1',2,'1'),('1',1,'3'), ('1',0,'2'),('2',3,'0'),('2',2,'1'),('2',1,'2'),('2',3,'0'),('2',2,'1'),('2',1,'2'),('3',2,'1'),('3',1,'2'),('3','3','3');
EXPLAIN SELECT * FROM t1 WHERE FieldKey = '1' ORDER BY LongVal;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	FieldKey,LongField,StringField	LongField	146	const	3	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`FieldKey` AS `FieldKey`,`test`.`t1`.`LongVal` AS `LongVal`,`test`.`t1`.`StringVal` AS `StringVal` from `test`.`t1` where (`test`.`t1`.`FieldKey` = '1') order by `test`.`t1`.`LongVal`
SELECT * FROM t1 WHERE FieldKey = '1' ORDER BY LongVal;
FieldKey	LongVal	StringVal
1	0	2
1	1	3
1	2	1
EXPLAIN SELECT * FROM t1 ignore index (FieldKey, LongField) WHERE FieldKey > '2' ORDER BY LongVal;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	StringField	StringField	146	NULL	3	100.00	Using index condition; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`FieldKey` AS `FieldKey`,`test`.`t1`.`LongVal` AS `LongVal`,`test`.`t1`.`StringVal` AS `StringVal` from `test`.`t1` IGNORE INDEX (`LongField`) IGNORE INDEX (`FieldKey`) where (`test`.`t1`.`FieldKey` > '2') order by `test`.`t1`.`LongVal`
SELECT * FROM t1 WHERE FieldKey > '2' ORDER BY LongVal;
FieldKey	LongVal	StringVal
3	1	2
3	2	1
3	3	3
EXPLAIN SELECT * FROM t1 WHERE FieldKey > '2' ORDER BY FieldKey, LongVal;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	FieldKey,LongField,StringField	LongField	146	NULL	3	100.00	Using index condition
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`FieldKey` AS `FieldKey`,`test`.`t1`.`LongVal` AS `LongVal`,`test`.`t1`.`StringVal` AS `StringVal` from `test`.`t1` where (`test`.`t1`.`FieldKey` > '2') order by `test`.`t1`.`FieldKey`,`test`.`t1`.`LongVal`
SELECT * FROM t1 WHERE FieldKey > '2' ORDER BY FieldKey, LongVal;
FieldKey	LongVal	StringVal
3	1	2
3	2	1
3	3	3
DROP TABLE t1;
CREATE TABLE t1 (a INT, b INT);
SET @id=0;
UPDATE t1 SET a=0 ORDER BY (a=@id), b;
DROP TABLE t1;
CREATE TABLE t1 (  id smallint(6) unsigned NOT NULL default '0',  menu tinyint(4) NOT NULL default '0',  KEY id (id),  KEY menu (menu)) ENGINE=MyISAM;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (11384, 2),(11392, 2);
SELECT id FROM t1 WHERE id <11984 AND menu =2 ORDER BY id DESC LIMIT 1 ;
id
11392
drop table t1;
create table t1(a int, b int, index(b));
insert into t1 values (2, 1), (1, 1), (4, NULL), (3, NULL), (6, 2), (5, 2);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain select * from t1 where b=1 or b is null order by a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref_or_null	b	b	5	const	4	100.00	Using index condition; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where ((`test`.`t1`.`b` = 1) or (`test`.`t1`.`b` is null)) order by `test`.`t1`.`a`
explain format=tree select * from t1 where b=1 or b is null order by a;
EXPLAIN
-> Sort: t1.a  (cost=0.9 rows=4)
    -> Index lookup on t1 using b (b = 1 or NULL), with index condition: ((t1.b = 1) or (t1.b is null))  (cost=0.9 rows=4)

select * from t1 where b=1 or b is null order by a;
a	b
1	1
2	1
3	NULL
4	NULL
explain select * from t1 where b=2 or b is null order by a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref_or_null	b	b	5	const	4	100.00	Using index condition; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where ((`test`.`t1`.`b` = 2) or (`test`.`t1`.`b` is null)) order by `test`.`t1`.`a`
select * from t1 where b=2 or b is null order by a;
a	b
3	NULL
4	NULL
5	2
6	2
drop table t1;
create table t1 (a int not null auto_increment, b int not null, c int not null, d int not null,
key(a,b,d), key(c,b,a));
create table t2 like t1;
insert into t1 values (NULL, 1, 2, 0), (NULL, 2, 1, 1), (NULL, 3, 4, 2), (NULL, 4, 3, 3);
insert into t2 select null, b, c, d from t1;
insert into t1 select null, b, c, d from t2;
insert into t2 select null, b, c, d from t1;
insert into t1 select null, b, c, d from t2;
insert into t2 select null, b, c, d from t1;
insert into t1 select null, b, c, d from t2;
insert into t2 select null, b, c, d from t1;
insert into t1 select null, b, c, d from t2;
insert into t2 select null, b, c, d from t1;
insert into t1 select null, b, c, d from t2;
optimize table t1;
Table	Op	Msg_type	Msg_text
test.t1	optimize	note	Table does not support optimize, doing recreate + analyze instead
test.t1	optimize	status	OK
set @row=10;
insert into t1 select 1, b, c + (@row:=@row - 1) * 10, d - @row from t2 limit 10;
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
select * from t1 where a=1 and b in (1) order by c, b, a;
a	b	c	d
1	1	2	0
1	1	12	-1
1	1	52	-5
1	1	92	-9
select * from t1 where a=1 and b in (1);
a	b	c	d
1	1	12	-1
1	1	2	0
1	1	52	-5
1	1	92	-9
drop table t1, t2;
create table t1 (col1 int, col int);
create table t2 (col2 int, col int);
insert into t1 values (1,1),(2,2),(3,3);
insert into t2 values (1,3),(2,2),(3,1);
select t1.* , t2.col as t2_col from t1 left join t2 on (t1.col1=t2.col2)
order by col;
col1	col	t2_col
1	1	3
2	2	2
3	3	1
select col1 as col, col from t1 order by col;
ERROR 23000: Column 'col' in order clause is ambiguous
select t1.col as c1, t2.col as c2 from t1, t2 where t1.col1=t2.col2
order by col;
ERROR 23000: Column 'col' in order clause is ambiguous
select t1.col as c1, t2.col as c2 from t1, t2 where t1.col1=t2.col2
order by col;
ERROR 23000: Column 'col' in order clause is ambiguous
select col1 from t1, t2 where t1.col1=t2.col2 order by col;
ERROR 23000: Column 'col' in order clause is ambiguous
select t1.col as t1_col, t2.col2 from t1, t2 where t1.col1=t2.col2
order by col;
ERROR 23000: Column 'col' in order clause is ambiguous
select t1.col as t1_col, t2.col from t1, t2 where t1.col1=t2.col2
order by col;
t1_col	col
3	1
2	2
1	3
select col2 as c, col as c from t2 order by col;
c	c
3	1
2	2
1	3
select col2 as col, col as col2 from t2 order by col;
col	col2
1	3
2	2
3	1
select t2.col2, t2.col, t2.col from t2 order by col;
col2	col	col
3	1	1
2	2	2
1	3	3
select t2.col2 as col from t2 order by t2.col;
col
3
2
1
select t2.col2 as col, t2.col from t2 order by t2.col;
col	col
3	1
2	2
1	3
select t2.col2, t2.col, t2.col from t2 order by t2.col;
col2	col	col
3	1	1
2	2	2
1	3	3
drop table t1, t2;
create table t1 (a char(25)) charset latin1;
insert into t1 set a = repeat('x', 20);
insert into t1 set a = concat(repeat('x', 19), 'z');
insert into t1 set a = concat(repeat('x', 19), 'ab');
insert into t1 set a = concat(repeat('x', 19), 'aa');
set max_sort_length=20;
select a from t1 order by a;
a
xxxxxxxxxxxxxxxxxxxab
xxxxxxxxxxxxxxxxxxxaa
xxxxxxxxxxxxxxxxxxxx
xxxxxxxxxxxxxxxxxxxz
drop table t1;
create table t1 (
`sid` decimal(8,0) default null,
`wnid` varchar(11) not null default '',
key `wnid14` (`wnid`(4)),
key `wnid` (`wnid`)
) engine=myisam default charset=latin1;
insert into t1 (`sid`, `wnid`) values
('10100','01019000000'),('37986','01019000000'),('37987','01019010000'),
('39560','01019090000'),('37989','01019000000'),('37990','01019011000'),
('37991','01019011000'),('37992','01019019000'),('37993','01019030000'),
('37994','01019090000'),('475','02070000000'),('25253','02071100000'),
('25255','02071100000'),('25256','02071110000'),('25258','02071130000'),
('25259','02071190000'),('25260','02071200000'),('25261','02071210000'),
('25262','02071290000'),('25263','02071300000'),('25264','02071310000'),
('25265','02071310000'),('25266','02071320000'),('25267','02071320000'),
('25269','02071330000'),('25270','02071340000'),('25271','02071350000'),
('25272','02071360000'),('25273','02071370000'),('25281','02071391000'),
('25282','02071391000'),('25283','02071399000'),('25284','02071400000'),
('25285','02071410000'),('25286','02071410000'),('25287','02071420000'),
('25288','02071420000'),('25291','02071430000'),('25290','02071440000'),
('25292','02071450000'),('25293','02071460000'),('25294','02071470000'),
('25295','02071491000'),('25296','02071491000'),('25297','02071499000');
explain select * from t1 where wnid like '0101%' order by wnid;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	wnid14,wnid	wnid	13	NULL	10	100.00	Using index condition
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`sid` AS `sid`,`test`.`t1`.`wnid` AS `wnid` from `test`.`t1` where (`test`.`t1`.`wnid` like '0101%') order by `test`.`t1`.`wnid`
select * from t1 where wnid like '0101%' order by wnid;
sid	wnid
10100	01019000000
37986	01019000000
37989	01019000000
37987	01019010000
37990	01019011000
37991	01019011000
37992	01019019000
37993	01019030000
39560	01019090000
37994	01019090000
drop table t1;
CREATE TABLE t1 (a int);
INSERT INTO t1 VALUES (2), (1), (1), (2), (1);
SELECT a FROM t1 ORDER BY a;
a
1
1
1
2
2
(SELECT a FROM t1) ORDER BY a;
a
1
1
1
2
2
DROP TABLE t1;
CREATE TABLE t1 (a int, b int);
INSERT INTO t1 VALUES (1,30), (2,20), (1,10), (2,30), (1,20), (2,10);
(SELECT b,a FROM t1 ORDER BY a,b) ORDER BY b,a;
b	a
10	1
10	2
20	1
20	2
30	1
30	2
(SELECT b FROM t1 ORDER BY b DESC) ORDER BY b ASC;
b
10
10
20
20
30
30
(SELECT b,a FROM t1 ORDER BY b,a) ORDER BY a,b;
b	a
10	1
20	1
30	1
10	2
20	2
30	2
(SELECT b,a FROM t1 ORDER by b,a LIMIT 3) ORDER by a,b;
b	a
10	1
20	1
10	2
DROP TABLE t1;
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1),(2);
SELECT a + 1 AS num FROM t1 ORDER BY 30 - num;
num
3
2
SELECT CONCAT('test', a) AS str FROM t1 ORDER BY UPPER(str);
str
test1
test2
SELECT a + 1 AS num FROM t1 GROUP BY 30 - num;
num
2
3
SELECT a + 1 AS num FROM t1 HAVING 30 - num;
num
2
3
SELECT a + 1 AS num, num + 1 FROM t1;
ERROR 42S22: Unknown column 'num' in 'field list'
SELECT a + 1 AS num, (select num + 2 FROM t1 LIMIT 1) FROM t1;
num	(select num + 2 FROM t1 LIMIT 1)
2	4
3	5
SELECT a.a + 1 AS num FROM t1 a JOIN t1 b ON num = b.a;
ERROR 42S22: Unknown column 'num' in 'on clause'
DROP TABLE t1;
CREATE TABLE bug25126 (
val int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
);
UPDATE bug25126 SET MissingCol = MissingCol;
ERROR 42S22: Unknown column 'MissingCol' in 'field list'
UPDATE bug25126 SET val = val ORDER BY MissingCol;
ERROR 42S22: Unknown column 'MissingCol' in 'order clause'
UPDATE bug25126 SET val = val ORDER BY val;
UPDATE bug25126 SET val = 1 ORDER BY val;
UPDATE bug25126 SET val = 1 ORDER BY MissingCol;
ERROR 42S22: Unknown column 'MissingCol' in 'order clause'
UPDATE bug25126 SET val = 1 ORDER BY val, MissingCol;
ERROR 42S22: Unknown column 'MissingCol' in 'order clause'
UPDATE bug25126 SET val = MissingCol ORDER BY MissingCol;
ERROR 42S22: Unknown column 'MissingCol' in 'field list'
UPDATE bug25126 SET MissingCol = 1 ORDER BY val, MissingCol;
ERROR 42S22: Unknown column 'MissingCol' in 'field list'
UPDATE bug25126 SET MissingCol = 1 ORDER BY MissingCol;
ERROR 42S22: Unknown column 'MissingCol' in 'field list'
UPDATE bug25126 SET MissingCol = val ORDER BY MissingCol;
ERROR 42S22: Unknown column 'MissingCol' in 'field list'
UPDATE bug25126 SET MissingCol = MissingCol ORDER BY MissingCol;
ERROR 42S22: Unknown column 'MissingCol' in 'field list'
DROP TABLE bug25126;
CREATE TABLE t1 (a int);
SELECT p.a AS val, q.a AS val1 FROM t1 p, t1 q ORDER BY val > 1;
val	val1
SELECT p.a AS val, q.a AS val FROM t1 p, t1 q ORDER BY val;
ERROR 23000: Column 'val' in order clause is ambiguous
SELECT p.a AS val, q.a AS val FROM t1 p, t1 q ORDER BY val > 1;
ERROR 23000: Column 'val' in order clause is ambiguous
DROP TABLE t1;
CREATE TABLE t1 (a int);
INSERT INTO t1 VALUES (3), (2), (4), (1);
SELECT a, IF(a IN (2,3), a, a+10) FROM t1
ORDER BY IF(a IN (2,3), a, a+10);
a	IF(a IN (2,3), a, a+10)
2	2
3	3
1	11
4	14
SELECT a, IF(a NOT IN (2,3), a, a+10) FROM t1 
ORDER BY IF(a NOT IN (2,3), a, a+10);
a	IF(a NOT IN (2,3), a, a+10)
1	1
4	4
2	12
3	13
SELECT a, IF(a IN (2,3), a, a+10) FROM t1 
ORDER BY IF(a NOT IN (2,3), a, a+10);
a	IF(a IN (2,3), a, a+10)
1	11
4	14
2	2
3	3
SELECT a, IF(a BETWEEN 2 AND 3, a, a+10) FROM t1
ORDER BY IF(a BETWEEN 2 AND 3, a, a+10);
a	IF(a BETWEEN 2 AND 3, a, a+10)
2	2
3	3
1	11
4	14
SELECT a, IF(a NOT BETWEEN 2 AND 3, a, a+10) FROM t1 
ORDER BY IF(a NOT BETWEEN 2 AND 3, a, a+10);
a	IF(a NOT BETWEEN 2 AND 3, a, a+10)
1	1
4	4
2	12
3	13
SELECT a, IF(a BETWEEN 2 AND 3, a, a+10) FROM t1 
ORDER BY IF(a NOT BETWEEN 2 AND 3, a, a+10);
a	IF(a BETWEEN 2 AND 3, a, a+10)
1	11
4	14
2	2
3	3
SELECT IF(a IN (1,2), a, '') as x1, IF(a NOT IN (1,2), a, '') as x2
FROM t1 GROUP BY x1, x2;
x1	x2
	3
	4
1	
2	
SELECT IF(a IN (1,2), a, '') as x1, IF(a NOT IN (1,2), a, '') as x2
FROM t1 GROUP BY x1, IF(a NOT IN (1,2), a, '');
x1	x2
	3
	4
1	
2	
SELECT a, a IN (1,2) FROM t1 ORDER BY a IN (1,2);
a	a IN (1,2)
3	0
4	0
2	1
1	1
SELECT a FROM t1 ORDER BY a IN (1,2);
a
3
4
2
1
SELECT a+10 FROM t1 ORDER BY a IN (1,2);
a+10
13
14
12
11
SELECT a, IF(a IN (1,2), a, a+10) FROM t1
ORDER BY IF(a IN (3,4), a, a+10);
a	IF(a IN (1,2), a, a+10)
3	13
4	14
1	1
2	2
DROP TABLE t1;
create table t1 (a int not null, b  int not null, c int not null);
insert t1 values (1,1,1),(1,1,2),(1,2,1);
select a, b from t1 group by a, b order by sum(c);
a	b
1	2
1	1
drop table t1;
CREATE TABLE t1 (a int, b int, PRIMARY KEY  (a));
INSERT INTO t1 VALUES (1,1), (2,2), (3,3);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	Error	Table 'test.t2' doesn't exist
test.t2	analyze	status	Operation failed
explain SELECT t1.b as a, t2.b as c FROM 
t1 LEFT JOIN t1 t2 ON (t1.a = t2.a AND t2.a = 2) 
ORDER BY c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using temporary; Using filesort
1	SIMPLE	t2	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`b` AS `a`,`test`.`t2`.`b` AS `c` from `test`.`t1` left join `test`.`t1` `t2` on(((`test`.`t1`.`a` = 2) and (`test`.`t2`.`a` = 2))) where true order by `c`
SELECT t2.b as c FROM 
t1 LEFT JOIN t1 t2 ON (t1.a = t2.a AND t2.a = 2) 
ORDER BY c;
c
NULL
NULL
2
explain SELECT t1.b as a, t2.b as c FROM 
t1 JOIN t1 t2 ON (t1.a = t2.a AND t2.a = 2)  
ORDER BY c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	NULL
1	SIMPLE	t2	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select '2' AS `a`,'2' AS `c` from `test`.`t1` join `test`.`t1` `t2` where true order by `c`
CREATE TABLE t2 LIKE t1;
INSERT INTO t2 SELECT * from t1;
CREATE TABLE t3 LIKE t1;
INSERT INTO t3 SELECT * from t1;
CREATE TABLE t4 LIKE t1;
INSERT INTO t4 SELECT * from t1;
INSERT INTO t1 values (0,0),(4,4);
SELECT t2.b FROM t1 LEFT JOIN (t2, t3 LEFT JOIN t4 ON t3.a=t4.a)
ON (t1.a=t2.a AND t1.b=t3.b) order by t2.b;
b
NULL
NULL
1
2
3
DROP TABLE t1,t2,t3,t4;
create table t1 (a int, b int, c int);
insert into t1 values (1,2,3), (9,8,3), (19,4,3), (1,4,9);
select a,(sum(b)/sum(c)) as ratio from t1 group by a order by sum(b)/sum(c) asc;
a	ratio
1	0.5000
19	1.3333
9	2.6667
drop table t1;
CREATE TABLE t1 (a INT UNSIGNED NOT NULL, b TIME);
INSERT INTO t1 (a) VALUES (100000), (0), (100), (1000000),(10000), (1000), (10);
UPDATE t1 SET b = SEC_TO_TIME(a);
SELECT a, b FROM t1 ORDER BY b DESC;
a	b
1000000	277:46:40
100000	27:46:40
10000	02:46:40
1000	00:16:40
100	00:01:40
10	00:00:10
0	00:00:00
SELECT a, b FROM t1 ORDER BY SEC_TO_TIME(a) DESC;
a	b
1000000	277:46:40
100000	27:46:40
10000	02:46:40
1000	00:16:40
100	00:01:40
10	00:00:10
0	00:00:00
DROP TABLE t1;
CREATE TABLE t1 (a INT, b INT, PRIMARY KEY (a), UNIQUE KEY b (b));
INSERT INTO t1 VALUES (1,1),(2,2);
CREATE TABLE t2 (a INT, b INT, KEY a (a,b));
INSERT INTO t2 VALUES (1,1),(1,2),(2,1),(2,2);
EXPLAIN SELECT 1 FROM t1,t2 WHERE t1.b=2 AND t1.a=t2.a ORDER BY t2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	const	PRIMARY,b	b	5	const	1	100.00	Using index
1	SIMPLE	t2	NULL	ref	a	a	5	const	2	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`a` = '2')) order by `test`.`t2`.`b`
DROP TABLE t1,t2;
CREATE TABLE t1(
id int auto_increment PRIMARY KEY, c2 int, c3 int, INDEX k2(c2), INDEX k3(c3));
INSERT INTO t1 (c2,c3) VALUES
(31,34),(35,38),(34,31),(32,35),(31,39),
(11,14),(15,18),(14,11),(12,15),(11,19);
INSERT INTO t1 (c2,c3) SELECT c2,c3 FROM t1;
INSERT INTO t1 (c2,c3) SELECT c2,c3 FROM t1;
INSERT INTO t1 (c2,c3) SELECT c2,c3 FROM t1;
INSERT INTO t1 (c2,c3) SELECT c2,c3 FROM t1;
INSERT INTO t1 (c2,c3) SELECT c2,c3 FROM t1;
INSERT INTO t1 (c2,c3) SELECT c2,c3 FROM t1;
INSERT INTO t1 (c2,c3) SELECT c2,c3 FROM t1;
INSERT INTO t1 (c2,c3) SELECT c2,c3 FROM t1;
INSERT INTO t1 (c2,c3) SELECT c2,c3 FROM t1;
INSERT INTO t1 (c2,c3) SELECT c2,c3 FROM t1;
INSERT INTO t1 (c2,c3) SELECT c2,c3 FROM t1;
INSERT INTO t1 (c2,c3) SELECT c2,c3 FROM t1;
UPDATE t1 SET c2=20 WHERE id%100 = 0;
SELECT COUNT(*) FROM t1;
COUNT(*)
40960
CREATE TABLE t2 LIKE t1;
INSERT INTO t2 SELECT * FROM t1 ORDER BY id;
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN SELECT id,c3 FROM t2 WHERE c2=11 ORDER BY c3 LIMIT 20;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	index	k2	k3	5	NULL	#	#	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`id` AS `id`,`test`.`t2`.`c3` AS `c3` from `test`.`t2` where (`test`.`t2`.`c2` = 11) order by `test`.`t2`.`c3` limit 20
EXPLAIN SELECT id,c3 FROM t2 WHERE c2=11 ORDER BY c3 LIMIT 4000;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ref	k2	k2	5	const	8192	100.00	Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`id` AS `id`,`test`.`t2`.`c3` AS `c3` from `test`.`t2` where (`test`.`t2`.`c2` = 11) order by `test`.`t2`.`c3` limit 4000
EXPLAIN SELECT id,c3 FROM t2 WHERE c2 BETWEEN 10 AND 12 ORDER BY c3 LIMIT 20;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	index	k2	k3	5	NULL	40	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`id` AS `id`,`test`.`t2`.`c3` AS `c3` from `test`.`t2` where (`test`.`t2`.`c2` between 10 and 12) order by `test`.`t2`.`c3` limit 20
EXPLAIN SELECT id,c3 FROM t2 WHERE c2 BETWEEN 20 AND 30 ORDER BY c3 LIMIT 4000;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	range	k2	k2	5	NULL	409	100.00	Using index condition; Using MRR; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`id` AS `id`,`test`.`t2`.`c3` AS `c3` from `test`.`t2` where (`test`.`t2`.`c2` between 20 and 30) order by `test`.`t2`.`c3` limit 4000
SELECT c3,id FROM t2 WHERE c2=11 ORDER BY c3 LIMIT 20;
c3	id
14	125
14	135
14	145
14	155
14	16
14	165
14	175
14	185
14	195
14	252
14	262
14	272
14	282
14	31
14	41
14	6
14	62
14	72
14	82
14	92
DROP TABLE t1,t2;
CREATE TABLE t1 (
a INT,
b INT,
PRIMARY KEY (a),
KEY ab(a, b)
);
INSERT INTO t1 VALUES (1,1),(2,2),(3,3),(4,4);
INSERT INTO t1 SELECT a + 4, b + 4 FROM t1;
INSERT INTO t1 SELECT a + 8, b + 8 FROM t1;
INSERT INTO t1 SELECT a +16, b +16 FROM t1;
INSERT INTO t1 SELECT a +32, b +32 FROM t1;
INSERT INTO t1 SELECT a +64, b +64 FROM t1;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT a FROM t1 IGNORE INDEX FOR GROUP BY (PRIMARY, ab) GROUP BY a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	PRIMARY,ab	PRIMARY	4	NULL	128	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` IGNORE INDEX FOR GROUP BY (`ab`) IGNORE INDEX FOR GROUP BY (PRIMARY) group by `test`.`t1`.`a`
flush status;
SELECT a FROM t1 IGNORE INDEX FOR GROUP BY (PRIMARY, ab) GROUP BY a;
a
1
10
100
101
102
103
104
105
106
107
108
109
11
110
111
112
113
114
115
116
117
118
119
12
120
121
122
123
124
125
126
127
128
13
14
15
16
17
18
19
2
20
21
22
23
24
25
26
27
28
29
3
30
31
32
33
34
35
36
37
38
39
4
40
41
42
43
44
45
46
47
48
49
5
50
51
52
53
54
55
56
57
58
59
6
60
61
62
63
64
65
66
67
68
69
7
70
71
72
73
74
75
76
77
78
79
8
80
81
82
83
84
85
86
87
88
89
9
90
91
92
93
94
95
96
97
98
99
show status like 'Created_tmp_tables';
Variable_name	Value
Created_tmp_tables	0
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT a FROM t1 IGNORE INDEX FOR ORDER BY (PRIMARY, ab) ORDER BY a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	PRIMARY	4	NULL	128	100.00	Using index; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` IGNORE INDEX FOR ORDER BY (`ab`) IGNORE INDEX FOR ORDER BY (PRIMARY) order by `test`.`t1`.`a`
flush status;
SELECT a FROM t1 IGNORE INDEX FOR ORDER BY (PRIMARY, ab) ORDER BY a;
a
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
show status like 'Created_tmp_tables';
Variable_name	Value
Created_tmp_tables	0
flush status;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT a FROM t1 IGNORE INDEX (PRIMARY, ab) GROUP BY a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	128	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` IGNORE INDEX (`ab`) IGNORE INDEX (PRIMARY) group by `test`.`t1`.`a`
SELECT a FROM t1 IGNORE INDEX (PRIMARY, ab) GROUP BY a;
a
1
10
100
101
102
103
104
105
106
107
108
109
11
110
111
112
113
114
115
116
117
118
119
12
120
121
122
123
124
125
126
127
128
13
14
15
16
17
18
19
2
20
21
22
23
24
25
26
27
28
29
3
30
31
32
33
34
35
36
37
38
39
4
40
41
42
43
44
45
46
47
48
49
5
50
51
52
53
54
55
56
57
58
59
6
60
61
62
63
64
65
66
67
68
69
7
70
71
72
73
74
75
76
77
78
79
8
80
81
82
83
84
85
86
87
88
89
9
90
91
92
93
94
95
96
97
98
99
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT a FROM t1 IGNORE INDEX (PRIMARY, ab) ORDER BY a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	128	100.00	Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` IGNORE INDEX (`ab`) IGNORE INDEX (PRIMARY) order by `test`.`t1`.`a`
SELECT a FROM t1 IGNORE INDEX (PRIMARY, ab) ORDER BY a;
a
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
show status like 'Created_tmp_tables';
Variable_name	Value
Created_tmp_tables	0
DROP TABLE t1;
#
# Bug#31590: Wrong error message on sort buffer being too small.
#
create table t1(a int, b text);
insert into t1 values (1,REPEAT('x', 60000)),(3,REPEAT('y', 60000));
set session sort_buffer_size= 32768;
CALL mtr.add_suppression("Out of sort memory");
select * from t1 order by b;
ERROR HY001: Out of sort memory, consider increasing server sort buffer size
drop table t1;
call mtr.add_suppression("Out of sort memory; increase server sort buffer size");
#
# Bug #39844: Query Crash Mysql Server 5.0.67
#
CREATE TABLE t1 (a INT PRIMARY KEY);
CREATE TABLE t2 (a INT PRIMARY KEY, b INT);
CREATE TABLE t3 (c INT);
INSERT INTO t1 (a) VALUES (1), (2);
INSERT INTO t2 (a,b) VALUES (1,2), (2,3);
INSERT INTO t3 (c) VALUES (1), (2);
SELECT
(SELECT t1.a FROM t1, t2 WHERE t1.a = t2.b AND t2.a = t3.c ORDER BY t1.a)
FROM t3;
(SELECT t1.a FROM t1, t2 WHERE t1.a = t2.b AND t2.a = t3.c ORDER BY t1.a)
2
NULL
DROP TABLE t1, t2, t3;
#
# Bug #42760: Select doesn't return desired results when we have null
# values
#
CREATE TABLE t1 (
a INT,
c INT,
UNIQUE KEY a_c (a,c),
KEY (a));
INSERT INTO t1 VALUES (1, 10), (2, NULL);
# Must use ref-or-null on the a_c index
EXPLAIN
SELECT 1 AS col FROM t1 WHERE a=2 AND (c=10 OR c IS NULL) ORDER BY c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	a_c,a	a	5	const	1	75.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select 1 AS `col` from `test`.`t1` where ((`test`.`t1`.`a` = 2) and ((`test`.`t1`.`c` = 10) or (`test`.`t1`.`c` is null))) order by `test`.`t1`.`c`
# Must return 1 row
SELECT 1 AS col FROM t1 WHERE a=2 AND (c=10 OR c IS NULL) ORDER BY c;
col
1
# Must use ref-or-null on the a_c index
EXPLAIN
SELECT 1 AS col FROM t1 WHERE a=2 AND (c=10 OR c IS NULL) ORDER BY c DESC;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	ref	a_c,a	x	x	x	x	75.00	x
Warnings:
x	x	x
# Must return 1 row
SELECT 1 AS col FROM t1 WHERE a=2 AND (c=10 OR c IS NULL) ORDER BY c DESC;
col
1
DROP TABLE t1;
End of 5.0 tests
CREATE TABLE t2 (a varchar(32), b int(11), c float, d double, 
UNIQUE KEY a (a,b,c), KEY b (b), KEY c (c)) charset utf8mb4;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE t1 (a varchar(32), b char(3), UNIQUE KEY a (a,b), KEY b (b))
charset utf8mb4;
CREATE TABLE t3 (a varchar(32), b char(3), UNIQUE KEY a (a,b))
charset utf8mb4;
INSERT INTO t3 SELECT * FROM t1;
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
EXPLAIN
SELECT d FROM t1, t2
WHERE t2.b=14 AND t2.a=t1.a AND 5.1<t2.c AND t1.b='DE'
ORDER BY t2.c LIMIT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	a,b	b	13	const	4	100.00	Using index condition; Using where; Using temporary; Using filesort
1	SIMPLE	t2	NULL	ref	a,b,c	a	136	test.t1.a,const	11	78.90	Using index condition
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`b` = 'DE') and (`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t2`.`b` = 14) and (5.1 < `test`.`t2`.`c`)) order by `test`.`t2`.`c` limit 1
SELECT d FROM t1, t2
WHERE t2.b=14 AND t2.a=t1.a AND 5.1<t2.c AND t1.b='DE'
ORDER BY t2.c LIMIT 1;
d
52.5
EXPLAIN
SELECT d FROM t3 AS t1, t2 AS t2 
WHERE t2.b=14 AND t2.a=t1.a AND 5.1<t2.c AND t1.b='DE'
ORDER BY t2.c LIMIT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	range	a,b,c	c	5	NULL	415	27.57	Using index condition; Using where
1	SIMPLE	t1	NULL	eq_ref	a	a	144	test.t2.a,const	1	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`d` AS `d` from `test`.`t3` `t1` join `test`.`t2` where ((`test`.`t1`.`b` = 'DE') and (`test`.`t1`.`a` = `test`.`t2`.`a`) and (`test`.`t2`.`b` = 14) and (5.1 < `test`.`t2`.`c`)) order by `test`.`t2`.`c` limit 1
SELECT d FROM t3 AS t1, t2 AS t2 
WHERE t2.b=14 AND t2.a=t1.a AND 5.1<t2.c AND t1.b='DE'
ORDER BY t2.c LIMIT 1;
d
52.5
DROP TABLE t1,t2,t3;
#
# WL#1393 - Optimizing filesort with small limit
#
CREATE TABLE t1(f0 int auto_increment primary key, f1 int, f2 varchar(200)) charset latin1;
INSERT INTO t1(f1, f2) VALUES 
(0,"0"),(1,"1"),(2,"2"),(3,"3"),(4,"4"),(5,"5"),
(6,"6"),(7,"7"),(8,"8"),(9,"9"),(10,"10"),
(11,"11"),(12,"12"),(13,"13"),(14,"14"),(15,"15"),
(16,"16"),(17,"17"),(18,"18"),(19,"19"),(20,"20"),
(21,"21"),(22,"22"),(23,"23"),(24,"24"),(25,"25"),
(26,"26"),(27,"27"),(28,"28"),(29,"29"),(30,"30"),
(31,"31"),(32,"32"),(33,"33"),(34,"34"),(35,"35"),
(36,"36"),(37,"37"),(38,"38"),(39,"39"),(40,"40"),
(41,"41"),(42,"42"),(43,"43"),(44,"44"),(45,"45"),
(46,"46"),(47,"47"),(48,"48"),(49,"49"),(50,"50"),
(51,"51"),(52,"52"),(53,"53"),(54,"54"),(55,"55"),
(56,"56"),(57,"57"),(58,"58"),(59,"59"),(60,"60"),
(61,"61"),(62,"62"),(63,"63"),(64,"64"),(65,"65"),
(66,"66"),(67,"67"),(68,"68"),(69,"69"),(70,"70"),
(71,"71"),(72,"72"),(73,"73"),(74,"74"),(75,"75"),
(76,"76"),(77,"77"),(78,"78"),(79,"79"),(80,"80"),
(81,"81"),(82,"82"),(83,"83"),(84,"84"),(85,"85"),
(86,"86"),(87,"87"),(88,"88"),(89,"89"),(90,"90"),
(91,"91"),(92,"92"),(93,"93"),(94,"94"),(95,"95"),
(96,"96"),(97,"97"),(98,"98"),(99,"99");
SELECT * FROM t1 ORDER BY f1 ASC, f0 LIMIT 100;
f0	f1	f2
1	0	0
2	1	1
3	2	2
4	3	3
5	4	4
6	5	5
7	6	6
8	7	7
9	8	8
10	9	9
11	10	10
12	11	11
13	12	12
14	13	13
15	14	14
16	15	15
17	16	16
18	17	17
19	18	18
20	19	19
21	20	20
22	21	21
23	22	22
24	23	23
25	24	24
26	25	25
27	26	26
28	27	27
29	28	28
30	29	29
31	30	30
32	31	31
33	32	32
34	33	33
35	34	34
36	35	35
37	36	36
38	37	37
39	38	38
40	39	39
41	40	40
42	41	41
43	42	42
44	43	43
45	44	44
46	45	45
47	46	46
48	47	47
49	48	48
50	49	49
51	50	50
52	51	51
53	52	52
54	53	53
55	54	54
56	55	55
57	56	56
58	57	57
59	58	58
60	59	59
61	60	60
62	61	61
63	62	62
64	63	63
65	64	64
66	65	65
67	66	66
68	67	67
69	68	68
70	69	69
71	70	70
72	71	71
73	72	72
74	73	73
75	74	74
76	75	75
77	76	76
78	77	77
79	78	78
80	79	79
81	80	80
82	81	81
83	82	82
84	83	83
85	84	84
86	85	85
87	86	86
88	87	87
89	88	88
90	89	89
91	90	90
92	91	91
93	92	92
94	93	93
95	94	94
96	95	95
97	96	96
98	97	97
99	98	98
100	99	99
SELECT * FROM t1 ORDER BY f1 ASC, f0 LIMIT 30;
f0	f1	f2
1	0	0
2	1	1
3	2	2
4	3	3
5	4	4
6	5	5
7	6	6
8	7	7
9	8	8
10	9	9
11	10	10
12	11	11
13	12	12
14	13	13
15	14	14
16	15	15
17	16	16
18	17	17
19	18	18
20	19	19
21	20	20
22	21	21
23	22	22
24	23	23
25	24	24
26	25	25
27	26	26
28	27	27
29	28	28
30	29	29
SELECT * FROM t1 ORDER BY f1 ASC, f0 LIMIT 0;
f0	f1	f2
SELECT * FROM t1 ORDER BY f2 DESC, f0 LIMIT 30;
f0	f1	f2
100	99	99
99	98	98
98	97	97
97	96	96
96	95	95
95	94	94
94	93	93
93	92	92
92	91	91
91	90	90
10	9	9
90	89	89
89	88	88
88	87	87
87	86	86
86	85	85
85	84	84
84	83	83
83	82	82
82	81	81
81	80	80
9	8	8
80	79	79
79	78	78
78	77	77
77	76	76
76	75	75
75	74	74
74	73	73
73	72	72
SELECT * FROM t1 ORDER BY f2 DESC, f0 LIMIT 0;
f0	f1	f2
SELECT * FROM t1 WHERE f1>10 ORDER BY f2, f0 LIMIT 20;
f0	f1	f2
12	11	11
13	12	12
14	13	13
15	14	14
16	15	15
17	16	16
18	17	17
19	18	18
20	19	19
21	20	20
22	21	21
23	22	22
24	23	23
25	24	24
26	25	25
27	26	26
28	27	27
29	28	28
30	29	29
31	30	30
SELECT * FROM t1 WHERE f1>10 ORDER BY f2, f0 LIMIT 0;
f0	f1	f2
SELECT * FROM t1 WHERE f1>10 ORDER BY f2, f0 LIMIT 10 OFFSET 10;
f0	f1	f2
22	21	21
23	22	22
24	23	23
25	24	24
26	25	25
27	26	26
28	27	27
29	28	28
30	29	29
31	30	30
SELECT * FROM t1 WHERE f1>10 ORDER BY f2, f0 LIMIT 0 OFFSET 10;
f0	f1	f2
set sort_buffer_size= 32768;
CREATE TEMPORARY TABLE tmp (f1 int, f2 varchar(20));
INSERT INTO tmp SELECT f1, f2 FROM t1;
INSERT INTO t1(f1, f2) SELECT * FROM tmp;
INSERT INTO tmp SELECT f1, f2 FROM t1;
INSERT INTO t1(f1, f2) SELECT * FROM tmp;
SELECT * FROM t1 ORDER BY f1 ASC, f0 LIMIT 30;
f0	f1	f2
1	0	0
101	0	0
228	0	0
328	0	0
428	0	0
2	1	1
102	1	1
229	1	1
329	1	1
429	1	1
3	2	2
103	2	2
230	2	2
330	2	2
430	2	2
4	3	3
104	3	3
231	3	3
331	3	3
431	3	3
5	4	4
105	4	4
232	4	4
332	4	4
432	4	4
6	5	5
106	5	5
233	5	5
333	5	5
433	5	5
SELECT * FROM t1 ORDER BY f1 ASC, f0 LIMIT 0;
f0	f1	f2
SELECT * FROM t1 ORDER BY f2 DESC, f0 LIMIT 30;
f0	f1	f2
100	99	99
200	99	99
327	99	99
427	99	99
527	99	99
99	98	98
199	98	98
326	98	98
426	98	98
526	98	98
98	97	97
198	97	97
325	97	97
425	97	97
525	97	97
97	96	96
197	96	96
324	96	96
424	96	96
524	96	96
96	95	95
196	95	95
323	95	95
423	95	95
523	95	95
95	94	94
195	94	94
322	94	94
422	94	94
522	94	94
SELECT * FROM t1 ORDER BY f2 DESC, f0 LIMIT 0;
f0	f1	f2
SELECT * FROM t1 WHERE f1>10 ORDER BY f2, f0 LIMIT 20;
f0	f1	f2
12	11	11
112	11	11
239	11	11
339	11	11
439	11	11
13	12	12
113	12	12
240	12	12
340	12	12
440	12	12
14	13	13
114	13	13
241	13	13
341	13	13
441	13	13
15	14	14
115	14	14
242	14	14
342	14	14
442	14	14
SELECT * FROM t1 WHERE f1>10 ORDER BY f2, f0 LIMIT 0;
f0	f1	f2
SELECT * FROM t1 WHERE f1>10 ORDER BY f2, f0 LIMIT 10 OFFSET 10;
f0	f1	f2
14	13	13
114	13	13
241	13	13
341	13	13
441	13	13
15	14	14
115	14	14
242	14	14
342	14	14
442	14	14
SELECT * FROM t1 WHERE f1>10 ORDER BY f2, f0 LIMIT 0 OFFSET 10;
f0	f1	f2
set sort_buffer_size= 32768;
SELECT SQL_CALC_FOUND_ROWS * FROM t1
ORDER BY f1, f0 LIMIT 30;
f0	f1	f2
1	0	0
101	0	0
228	0	0
328	0	0
428	0	0
2	1	1
102	1	1
229	1	1
329	1	1
429	1	1
3	2	2
103	2	2
230	2	2
330	2	2
430	2	2
4	3	3
104	3	3
231	3	3
331	3	3
431	3	3
5	4	4
105	4	4
232	4	4
332	4	4
432	4	4
6	5	5
106	5	5
233	5	5
333	5	5
433	5	5
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
500
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
SELECT SQL_CALC_FOUND_ROWS * FROM t1
ORDER BY f1, f0 LIMIT 0;
f0	f1	f2
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
500
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
SELECT SQL_CALC_FOUND_ROWS * FROM t1 WHERE f1>10
ORDER BY f2, f0 LIMIT 20;
f0	f1	f2
12	11	11
112	11	11
239	11	11
339	11	11
439	11	11
13	12	12
113	12	12
240	12	12
340	12	12
440	12	12
14	13	13
114	13	13
241	13	13
341	13	13
441	13	13
15	14	14
115	14	14
242	14	14
342	14	14
442	14	14
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
445
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
SELECT SQL_CALC_FOUND_ROWS * FROM t1 WHERE f1>10
ORDER BY f2, f0 LIMIT 0;
f0	f1	f2
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
445
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
SELECT SQL_CALC_FOUND_ROWS * FROM t1 WHERE f1>10
ORDER BY f2, f0 LIMIT 10 OFFSET 10;
f0	f1	f2
14	13	13
114	13	13
241	13	13
341	13	13
441	13	13
15	14	14
115	14	14
242	14	14
342	14	14
442	14	14
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
445
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
SELECT SQL_CALC_FOUND_ROWS * FROM t1 WHERE f1>10
ORDER BY f2, f0 LIMIT 0 OFFSET 10;
f0	f1	f2
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
445
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
set sort_buffer_size= 327680;
SELECT * FROM t1 JOIN tmp on t1.f2=tmp.f2
ORDER BY tmp.f1, f0 LIMIT 30;
f0	f1	f2	f1	f2
1	0	0	0	0
1	0	0	0	0
1	0	0	0	0
101	0	0	0	0
101	0	0	0	0
101	0	0	0	0
228	0	0	0	0
228	0	0	0	0
228	0	0	0	0
328	0	0	0	0
328	0	0	0	0
328	0	0	0	0
428	0	0	0	0
428	0	0	0	0
428	0	0	0	0
2	1	1	1	1
2	1	1	1	1
2	1	1	1	1
102	1	1	1	1
102	1	1	1	1
102	1	1	1	1
229	1	1	1	1
229	1	1	1	1
229	1	1	1	1
329	1	1	1	1
329	1	1	1	1
329	1	1	1	1
429	1	1	1	1
429	1	1	1	1
429	1	1	1	1
SELECT * FROM t1 JOIN tmp on t1.f2=tmp.f2
ORDER BY tmp.f1, f0 LIMIT 30 OFFSET 30;
f0	f1	f2	f1	f2
3	2	2	2	2
3	2	2	2	2
3	2	2	2	2
103	2	2	2	2
103	2	2	2	2
103	2	2	2	2
230	2	2	2	2
230	2	2	2	2
230	2	2	2	2
330	2	2	2	2
330	2	2	2	2
330	2	2	2	2
430	2	2	2	2
430	2	2	2	2
430	2	2	2	2
4	3	3	3	3
4	3	3	3	3
4	3	3	3	3
104	3	3	3	3
104	3	3	3	3
104	3	3	3	3
231	3	3	3	3
231	3	3	3	3
231	3	3	3	3
331	3	3	3	3
331	3	3	3	3
331	3	3	3	3
431	3	3	3	3
431	3	3	3	3
431	3	3	3	3
SELECT SQL_CALC_FOUND_ROWS * FROM t1 JOIN tmp on t1.f2=tmp.f2
ORDER BY tmp.f1, f0 LIMIT 30 OFFSET 30;
f0	f1	f2	f1	f2
3	2	2	2	2
3	2	2	2	2
3	2	2	2	2
103	2	2	2	2
103	2	2	2	2
103	2	2	2	2
230	2	2	2	2
230	2	2	2	2
230	2	2	2	2
330	2	2	2	2
330	2	2	2	2
330	2	2	2	2
430	2	2	2	2
430	2	2	2	2
430	2	2	2	2
4	3	3	3	3
4	3	3	3	3
4	3	3	3	3
104	3	3	3	3
104	3	3	3	3
104	3	3	3	3
231	3	3	3	3
231	3	3	3	3
231	3	3	3	3
331	3	3	3	3
331	3	3	3	3
331	3	3	3	3
431	3	3	3	3
431	3	3	3	3
431	3	3	3	3
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
1500
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
SELECT SQL_CALC_FOUND_ROWS * FROM t1 JOIN tmp on t1.f2=tmp.f2
WHERE t1.f2>20
ORDER BY tmp.f1, f0 LIMIT 30 OFFSET 30;
f0	f1	f2	f1	f2
24	23	23	23	23
24	23	23	23	23
24	23	23	23	23
124	23	23	23	23
124	23	23	23	23
124	23	23	23	23
251	23	23	23	23
251	23	23	23	23
251	23	23	23	23
351	23	23	23	23
351	23	23	23	23
351	23	23	23	23
451	23	23	23	23
451	23	23	23	23
451	23	23	23	23
25	24	24	24	24
25	24	24	24	24
25	24	24	24	24
125	24	24	24	24
125	24	24	24	24
125	24	24	24	24
252	24	24	24	24
252	24	24	24	24
252	24	24	24	24
352	24	24	24	24
352	24	24	24	24
352	24	24	24	24
452	24	24	24	24
452	24	24	24	24
452	24	24	24	24
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
1185
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
CREATE VIEW v1 as SELECT * FROM t1 ORDER BY f1, f0 LIMIT 30;
SELECT * FROM v1;
f0	f1	f2
1	0	0
101	0	0
228	0	0
328	0	0
428	0	0
2	1	1
102	1	1
229	1	1
329	1	1
429	1	1
3	2	2
103	2	2
230	2	2
330	2	2
430	2	2
4	3	3
104	3	3
231	3	3
331	3	3
431	3	3
5	4	4
105	4	4
232	4	4
332	4	4
432	4	4
6	5	5
106	5	5
233	5	5
333	5	5
433	5	5
drop view v1;
CREATE VIEW v1 as SELECT * FROM t1 ORDER BY f1, f0 LIMIT 100;
SELECT * FROM v1 ORDER BY f2, f0 LIMIT 30;
f0	f1	f2
1	0	0
101	0	0
228	0	0
328	0	0
428	0	0
2	1	1
102	1	1
229	1	1
329	1	1
429	1	1
11	10	10
111	10	10
238	10	10
338	10	10
438	10	10
12	11	11
112	11	11
239	11	11
339	11	11
439	11	11
13	12	12
113	12	12
240	12	12
340	12	12
440	12	12
14	13	13
114	13	13
241	13	13
341	13	13
441	13	13
CREATE VIEW v2 as SELECT * FROM t1 ORDER BY f2, f0 LIMIT 100;
SELECT * FROM v1 JOIN v2 on v1.f1=v2.f1 ORDER BY v1.f2,v1.f0,v2.f0
LIMIT 30;
f0	f1	f2	f0	f1	f2
1	0	0	1	0	0
1	0	0	101	0	0
1	0	0	228	0	0
1	0	0	328	0	0
1	0	0	428	0	0
101	0	0	1	0	0
101	0	0	101	0	0
101	0	0	228	0	0
101	0	0	328	0	0
101	0	0	428	0	0
228	0	0	1	0	0
228	0	0	101	0	0
228	0	0	228	0	0
228	0	0	328	0	0
228	0	0	428	0	0
328	0	0	1	0	0
328	0	0	101	0	0
328	0	0	228	0	0
328	0	0	328	0	0
328	0	0	428	0	0
428	0	0	1	0	0
428	0	0	101	0	0
428	0	0	228	0	0
428	0	0	328	0	0
428	0	0	428	0	0
2	1	1	2	1	1
2	1	1	102	1	1
2	1	1	229	1	1
2	1	1	329	1	1
2	1	1	429	1	1
SELECT floor(f1/10) f3, count(f2) FROM t1
GROUP BY 1 ORDER BY 2,1 LIMIT 5;
f3	count(f2)
0	50
1	50
2	50
3	50
4	50
SELECT floor(f1/10) f3, count(f2) FROM t1
GROUP BY 1 ORDER BY 2,1 LIMIT 0;
f3	count(f2)
CREATE PROCEDURE wl1393_sp_test()
BEGIN
SELECT * FROM t1 WHERE f1>10 ORDER BY f2, f0 LIMIT 30;
SELECT * FROM t1 WHERE f1>10 ORDER BY f2, f0 LIMIT 15 OFFSET 15;
SELECT SQL_CALC_FOUND_ROWS * FROM t1 WHERE f1>10
ORDER BY f2, f0 LIMIT 15 OFFSET 15;
SELECT FOUND_ROWS();
SELECT * FROM v1 ORDER BY f2, f0 LIMIT 30;
END|
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
CALL wl1393_sp_test()|
f0	f1	f2
12	11	11
112	11	11
239	11	11
339	11	11
439	11	11
13	12	12
113	12	12
240	12	12
340	12	12
440	12	12
14	13	13
114	13	13
241	13	13
341	13	13
441	13	13
15	14	14
115	14	14
242	14	14
342	14	14
442	14	14
16	15	15
116	15	15
243	15	15
343	15	15
443	15	15
17	16	16
117	16	16
244	16	16
344	16	16
444	16	16
f0	f1	f2
15	14	14
115	14	14
242	14	14
342	14	14
442	14	14
16	15	15
116	15	15
243	15	15
343	15	15
443	15	15
17	16	16
117	16	16
244	16	16
344	16	16
444	16	16
f0	f1	f2
15	14	14
115	14	14
242	14	14
342	14	14
442	14	14
16	15	15
116	15	15
243	15	15
343	15	15
443	15	15
17	16	16
117	16	16
244	16	16
344	16	16
444	16	16
FOUND_ROWS()
445
f0	f1	f2
1	0	0
101	0	0
228	0	0
328	0	0
428	0	0
2	1	1
102	1	1
229	1	1
329	1	1
429	1	1
11	10	10
111	10	10
238	10	10
338	10	10
438	10	10
12	11	11
112	11	11
239	11	11
339	11	11
439	11	11
13	12	12
113	12	12
240	12	12
340	12	12
440	12	12
14	13	13
114	13	13
241	13	13
341	13	13
441	13	13
DROP PROCEDURE wl1393_sp_test|
SELECT d1.f1, d1.f2 FROM t1
LEFT JOIN (SELECT * FROM t1 ORDER BY f1 LIMIT 30) d1 on t1.f1=d1.f1
ORDER BY d1.f2 DESC LIMIT 30;
f1	f2
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
5	5
4	4
4	4
4	4
4	4
4	4
SELECT * FROM t1 WHERE f1 = (SELECT f1 FROM t1 ORDER BY 1 LIMIT 1);
f0	f1	f2
1	0	0
101	0	0
228	0	0
328	0	0
428	0	0
SELECT * FROM t1 WHERE f1 = (SELECT f1 FROM t1 ORDER BY 1 LIMIT 2);
ERROR 21000: Subquery returns more than 1 row
DROP TABLE t1, tmp;
DROP VIEW v1, v2;
# end of WL#1393 - Optimizing filesort with small limit
#
# Bug #58761
# Crash in Field::is_null in field.h on subquery in WHERE clause
#
CREATE TABLE t1 (
pk INT NOT NULL AUTO_INCREMENT,
col_int_key INT DEFAULT NULL,
col_varchar_key VARCHAR(1) DEFAULT NULL,
PRIMARY KEY (pk),
KEY col_varchar_key (col_varchar_key,col_int_key)
);
INSERT INTO t1 VALUES (27,7,'x');
INSERT INTO t1 VALUES (28,6,'m');
INSERT INTO t1 VALUES (29,4,'c');
CREATE TABLE where_subselect
SELECT DISTINCT `pk` AS field1 , `pk` AS field2 
FROM t1 AS alias1 
WHERE alias1 . `col_int_key` > 229 
OR alias1 . `col_varchar_key` IS NOT NULL
GROUP BY field1, field2
;
SELECT * 
FROM where_subselect
WHERE (field1, field2) IN (  
SELECT DISTINCT `pk` AS field1 , `pk` AS field2 
FROM t1 AS alias1 
WHERE alias1 . `col_int_key` > 229 
OR alias1 . `col_varchar_key` IS NOT NULL
GROUP BY field1, field2
);
field1	field2
27	27
28	28
29	29
DROP TABLE t1;
DROP TABLE where_subselect;
# End of Bug #58761
CREATE TABLE t1 (
id1 INT NOT NULL,
id2 INT  NOT NULL,
junk INT NOT NULL,
PRIMARY KEY (id1, id2, junk),
INDEX id2_j_id1 (id2, junk, id1)
);
INSERT INTO t1 VALUES (1, 1, 1), (2, 1, 2), (3, 1, 3), (4, 1, 4);
INSERT INTO t1 VALUES (5, 2, 1), (6, 2, 2), (7, 2, 3), (8, 2, 4);
INSERT INTO t1 VALUES (9, 3, 1), (10, 3, 2), (11, 3, 3), (12, 3, 4);
INSERT INTO t1 VALUES (13, 4, 1), (14, 4, 2), (15, 4, 3), (16, 4, 4);
INSERT INTO t1 VALUES (17, 5, 1), (18, 5, 2), (19, 5, 3), (20, 5, 4);
INSERT INTO t1 VALUES (21, 6, 1), (22, 6, 2), (23, 6, 3), (24, 6, 4);
INSERT INTO t1 VALUES (25, 7, 1), (26, 7, 2), (27, 7, 3), (28, 7, 4);
INSERT INTO t1 VALUES (29, 8, 1), (30, 8, 2), (31, 8, 3), (32, 8, 4);
INSERT INTO t1 VALUES (33, 9, 1), (34, 9, 2), (35, 9, 3), (36, 9, 4);
EXPLAIN SELECT id1 FROM t1 WHERE id2 = 4 ORDER BY id1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	PRIMARY,id2_j_id1	id2_j_id1	4	const	4	100.00	Using index; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id1` AS `id1` from `test`.`t1` where (`test`.`t1`.`id2` = 4) order by `test`.`t1`.`id1`
SELECT id1 FROM t1 WHERE id2 = 4 ORDER BY id1;
id1
13
14
15
16
DROP TABLE t1;
CREATE TABLE t1 (
a INT,
b INT NOT NULL,
c char(100),
KEY (b, c),
KEY (b, a, c)
)
DEFAULT CHARSET = utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES 
(1,  1, 1),
(2,  2, 2),
(3,  3, 3),
(4,  4, 4),
(5,  5, 5),
(6,  6, 6),
(7,  7, 7),
(8,  8, 8),
(9,  9, 9);
INSERT INTO t1 SELECT a + 10,  b, c  + 10   FROM t1;
INSERT INTO t1 SELECT a + 20,  b, c  + 20   FROM t1;
INSERT INTO t1 SELECT a + 40,  b, c  + 40   FROM t1;
INSERT INTO t1 SELECT a + 80,  b, c  + 80   FROM t1;
INSERT INTO t1 SELECT a + 160, b, c  + 160  FROM t1;
INSERT INTO t1 SELECT a + 320, b, c  + 320  FROM t1;
INSERT INTO t1 SELECT a + 640, b, c  + 640  FROM t1;
INSERT INTO t1 SELECT a + 1280, b, c + 1280 FROM t1 LIMIT 80;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN
SELECT a FROM t1 WHERE b = 1 ORDER BY c DESC LIMIT 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	b,b_2	b_2	4	const	208	100.00	Using index; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`b` = 1) order by `test`.`t1`.`c` desc limit 9
SELECT a FROM t1 WHERE b = 1 ORDER BY c DESC LIMIT 9;
a
991
981
971
961
951
941
931
921
911
EXPLAIN
SELECT DISTINCT a FROM t1 WHERE b = 1 ORDER BY c DESC LIMIT 0, 9;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	b,b_2	b_2	4	const	208	100.00	Using index; Using temporary; Using filesort
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`b` = 1) order by `test`.`t1`.`c` desc limit 0,9
SELECT DISTINCT a FROM t1 WHERE b = 1 ORDER BY c DESC LIMIT 0, 9;
a
991
981
971
961
951
941
931
921
911
DROP TABLE t1;
#
# Bug #43029: FORCE INDEX FOR ORDER BY is ignored when join buffering 
#   is used
#
CREATE TABLE t1 (a INT, b INT, KEY (a));
INSERT INTO t1 VALUES (0, NULL), (1, NULL), (2, NULL), (3, NULL);
INSERT INTO t1 SELECT a+4, b FROM t1;
INSERT INTO t1 SELECT a+8, b FROM t1;
CREATE TABLE t2 (a INT, b INT);
INSERT INTO t2 VALUES (0,NULL), (1,NULL), (2,NULL), (3,NULL), (4,NULL);
INSERT INTO t2 SELECT a+4, b FROM t2;
# shouldn't have "using filesort"
EXPLAIN 
SELECT * FROM t1 FORCE INDEX FOR ORDER BY (a), t2 WHERE t1.a < 2 ORDER BY t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	a	a	5	NULL	2	100.00	Using index condition
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` FORCE INDEX FOR ORDER BY (`a`) join `test`.`t2` where (`test`.`t1`.`a` < 2) order by `test`.`t1`.`a`
# should have "using filesort"
EXPLAIN 
SELECT * FROM t1 USE INDEX FOR ORDER BY (a), t2 WHERE t1.a < 2 ORDER BY t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	a	a	5	NULL	2	100.00	Using index condition; Using MRR; Using temporary; Using filesort
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` USE INDEX FOR ORDER BY (`a`) join `test`.`t2` where (`test`.`t1`.`a` < 2) order by `test`.`t1`.`a`
# should have "using filesort"
EXPLAIN 
SELECT * FROM t1 FORCE INDEX FOR JOIN (a), t2 WHERE t1.a < 2 ORDER BY t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	a	a	5	NULL	2	100.00	Using index condition; Using MRR; Using temporary; Using filesort
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` FORCE INDEX FOR JOIN (`a`) join `test`.`t2` where (`test`.`t1`.`a` < 2) order by `test`.`t1`.`a`
DROP TABLE t1, t2;
#
# Bug #50394: Regression in EXPLAIN with index scan, LIMIT, GROUP BY and
# ORDER BY computed col
#
CREATE TABLE t1 ( a INT NOT NULL, b INT NOT NULL, KEY( a, b ) );
INSERT INTO t1 VALUES (1, 1), (2, 2), (3, 3), (4, 4), (5, 5);
INSERT INTO t1 SELECT a + 5, b + 5 FROM t1;
CREATE TABLE t2( a INT PRIMARY KEY, b INT );
INSERT INTO t2 VALUES (1, 1), (2, 2), (3, 3), (4, 4), (5, 5);
INSERT INTO t2 SELECT a + 5, b + 5 FROM t2;
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN
SELECT count(*) AS c, t1.a
FROM t1 JOIN t2 ON t1.b = t2.a
WHERE t2.b = 1
GROUP BY t1.a
ORDER by c
LIMIT 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	PRIMARY	NULL	NULL	NULL	10	10.00	Using where; Using temporary; Using filesort
1	SIMPLE	t1	NULL	index	a	a	8	NULL	10	10.00	Using where; Using index; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select count(0) AS `c`,`test`.`t1`.`a` AS `a` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`b` = `test`.`t2`.`a`) and (`test`.`t2`.`b` = 1)) group by `test`.`t1`.`a` order by `c` limit 2
DROP TABLE t1, t2;
#
# Bug #59110: Memory leak of QUICK_SELECT_I allocated memory 
#  and
# Bug #59308: Incorrect result for 
SELECT DISTINCT <col>... ORDER BY <col> DESC 

# Use Valgrind to detect #59110!
#
CREATE TABLE t1 (a INT,KEY (a));
INSERT INTO t1 VALUES (1),(2),(3),(4),(5),(6),(7),(8),(9),(10);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT DISTINCT a,1 FROM t1 WHERE a <> 1 ORDER BY a DESC;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	a	a	5	NULL	10	100.00	Using where; Backward index scan; Using index
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`t1`.`a` AS `a`,1 AS `1` from `test`.`t1` where (`test`.`t1`.`a` <> 1) order by `test`.`t1`.`a` desc
SELECT DISTINCT a,1 FROM t1 WHERE a <> 1 ORDER BY a DESC;
a	1
10	1
9	1
8	1
7	1
6	1
5	1
4	1
3	1
2	1
DROP TABLE t1;
#
# Bug#11765255 58201:
# VALGRIND/CRASH WHEN ORDERING BY MULTIPLE AGGREGATE FUNCTIONS
#
select count(*) order by max(1) + min(1);
count(*)
1
End of 5.1 tests
#
# Bug #38745: MySQL 5.1 optimizer uses filesort for ORDER BY
#             when it should use index
#
CREATE TABLE t1 (i1 integer NOT NULL PRIMARY KEY);
CREATE TABLE t2 (i2 integer NOT NULL PRIMARY KEY);
CREATE TABLE t3 (i3 integer);
INSERT INTO t1 VALUES (1), (2), (3), (4), (5), (6), (7), (8), (9), (10), (11), (12);
INSERT INTO t2 SELECT * FROM t1;
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
EXPLAIN
SELECT t1.*, t2.* FROM t1 JOIN t2 ON t1.i1 = t2.i2 
LEFT JOIN t3 ON t2.i2 = t3.i3
ORDER BY t1.i1 LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	PRIMARY	PRIMARY	4	NULL	12	100.00	Using index; Using temporary; Using filesort
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.i1	1	100.00	Using index
1	SIMPLE	t3	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i1` AS `i1`,`test`.`t2`.`i2` AS `i2` from `test`.`t1` join `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`i3` = `test`.`t1`.`i1`)) where (`test`.`t2`.`i2` = `test`.`t1`.`i1`) order by `test`.`t1`.`i1` limit 5
SELECT t1.*, t2.* FROM t1 JOIN t2 ON t1.i1 = t2.i2 
LEFT JOIN t3 ON t2.i2 = t3.i3
ORDER BY t1.i1 LIMIT 5;
i1	i2
1	1
2	2
3	3
4	4
5	5
DROP TABLE t1, t2, t3;
#
# Bug #11885377 VOID JOIN_READ_KEY_UNLOCK_ROW(ST_JOIN_TABLE*): ASSERTION
# `TAB->REF.USE_COUNT'
#
CREATE TABLE t1(a INT PRIMARY KEY);
CREATE TABLE t2(b INT,c INT);
INSERT INTO t1 VALUES (1), (2);
INSERT INTO t2 VALUES (1,2), (2,3);
SELECT (SELECT 1 FROM t1 WHERE a=b AND c=1 ORDER BY a DESC) FROM t2;
(SELECT 1 FROM t1 WHERE a=b AND c=1 ORDER BY a DESC)
NULL
NULL
DROP TABLE t1, t2;
#
# Bug #13531865
# TEST_IF_SKIP_SORT_ORDER() INCORRECTLY SKIP FILESORT IF
# 'TYPE' IS REF_OR_NULL 
# 
#
CREATE TABLE t1 (
a INT,
c INT,
UNIQUE KEY a_c (a,c),
KEY (a)) engine=myisam;
INSERT INTO t1 VALUES (1,10), (2,NULL), (2,10);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
# Using 'KEY a_c' for order-by opt, would have required 
# REF_OR_NULL access which never can be order_by skipped.
# -> Keep initial REF on 'KEY a' selected by cond. optimizer
EXPLAIN
SELECT c FROM t1 WHERE a=2 AND (c=10 OR c IS NULL);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	a_c,a	a	5	const	1	55.56	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` = 2) and ((`test`.`t1`.`c` = 10) or (`test`.`t1`.`c` is null)))
EXPLAIN
SELECT c FROM t1 WHERE a=2 AND (c=10 OR c IS NULL) ORDER BY c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	a_c,a	a	5	const	1	55.56	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` = 2) and ((`test`.`t1`.`c` = 10) or (`test`.`t1`.`c` is null))) order by `test`.`t1`.`c`
SELECT c FROM t1 WHERE a=2 AND (c=10 OR c IS NULL) ORDER BY c;
c
NULL
10
EXPLAIN
SELECT c FROM t1 WHERE a=2 AND (c=10 OR c IS NULL) ORDER BY c DESC;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	a_c,a	a	5	const	1	55.56	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c` AS `c` from `test`.`t1` where ((`test`.`t1`.`a` = 2) and ((`test`.`t1`.`c` = 10) or (`test`.`t1`.`c` is null))) order by `test`.`t1`.`c` desc
SELECT c FROM t1 WHERE a=2 AND (c=10 OR c IS NULL) ORDER BY c DESC;
c
10
NULL
DROP TABLE t1;
#
# Bug #13528826 
# TEST_IF_CHEAPER_ORDERING(): CALCULATES INCORRECT 'SELECT_LIMIT'
# 
#
CREATE TABLE t1(a int PRIMARY KEY, b int) ENGINE=myisam;
INSERT INTO t1 VALUES
(5, 10), (2, 70), (7, 80), (6, 20), (1, 50), (9, 40), (8, 30), (3, 60);
CREATE TABLE t2 (p int, a int, INDEX i_a(a)) ENGINE=myisam;
INSERT INTO t2 VALUES
(103, 7), (109, 3), (102, 3), (108, 1), (106, 3),
(107, 7), (105, 1), (101, 3), (100, 7), (110, 1);
# number of rows in t1 was incorrectly used as an 
# implicit limit-clause if not explicit specified
EXPLAIN
SELECT t1.a FROM t1 LEFT JOIN t2 ON t1.a=t2.a ORDER BY t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	PRIMARY	4	NULL	8	100.00	Using index
1	SIMPLE	t2	NULL	ref	i_a	i_a	5	test.t1.a	2	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`a` = `test`.`t1`.`a`)) where true order by `test`.`t1`.`a`
# Query above used to be explained identical to this:
EXPLAIN
SELECT t1.a FROM t1 LEFT JOIN t2 ON t1.a=t2.a ORDER BY t1.a LIMIT 8;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	PRIMARY	4	NULL	8	100.00	Using index
1	SIMPLE	t2	NULL	ref	i_a	i_a	5	test.t1.a	2	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`a` = `test`.`t1`.`a`)) where true order by `test`.`t1`.`a` limit 8
# A really high limit was required to give the correct explain
EXPLAIN
SELECT t1.a FROM t1 LEFT JOIN t2 ON t1.a=t2.a ORDER BY t1.a LIMIT 1000;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	PRIMARY	4	NULL	8	100.00	Using index
1	SIMPLE	t2	NULL	ref	i_a	i_a	5	test.t1.a	2	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`a` = `test`.`t1`.`a`)) where true order by `test`.`t1`.`a` limit 1000
DROP TABLE t1, t2;
#
# Bug #13949068 ASSERT TAB->REF.KEY == REF_KEY IN
# PLAN_CHANGE_WATCHDOG::~PLAN_CHANGE_WATCHDOG
#
CREATE TABLE t1 (a INT, b INT, KEY(b), KEY(b,a)) ENGINE=INNODB;
INSERT INTO t1 VALUES (0,0);
EXPLAIN SELECT DISTINCT a FROM t1 WHERE b=1 ORDER BY 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	b,b_2	b_2	5	const	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`b` = 1) order by `test`.`t1`.`a`
SELECT DISTINCT a FROM t1 WHERE b=1 ORDER BY 1;
a
DROP TABLE t1;
#
# Bug#18636076 DEBUG CRASH ON 
#              PLAN_CHANGE_WATCHDOG::~PLAN_CHANGE_WATCHDOG
#
CREATE TABLE t1 (
pk INTEGER NOT NULL,
PRIMARY KEY (pk)
) ENGINE=MyISAM;
INSERT INTO t1 VALUES (1), (2), (3);
CREATE VIEW view_t1 AS SELECT * FROM t1;
CREATE TABLE t2 (
i1 INTEGER NOT NULL
) ENGINE=MyISAM;
EXPLAIN SELECT DISTINCT t2.i1, view_t1.pk
FROM view_t1 LEFT JOIN t2
ON  view_t1.pk = t2.i1
WHERE (view_t1.pk <= 204 AND t2.i1 != 3)
OR view_t1.pk BETWEEN 1 AND 7
ORDER BY t2.i1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	system	NULL	NULL	NULL	NULL	0	0.00	const row not found
1	SIMPLE	t1	NULL	index	PRIMARY	PRIMARY	4	NULL	3	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select distinct NULL AS `i1`,`test`.`t1`.`pk` AS `pk` from `test`.`t1` where (((`test`.`t1`.`pk` <= 204) and (NULL <> 3)) or (`test`.`t1`.`pk` between 1 and 7)) order by NULL
SELECT DISTINCT t2.i1, view_t1.pk
FROM view_t1 LEFT JOIN t2
ON  view_t1.pk = t2.i1
WHERE (view_t1.pk <= 204 AND t2.i1 != 3)
OR view_t1.pk BETWEEN 1 AND 7
ORDER BY t2.i1;
i1	pk
NULL	1
NULL	2
NULL	3
DROP VIEW view_t1;
DROP TABLE t1, t2;
SET sql_mode = default;
#
# Bug#20455386 INVALID WRITE IN COPY_FIELD::SET CAUSE CRASH WITH TMP TABLES
#
CREATE TABLE t1(a INT) ENGINE=INNODB;
CREATE TABLE t2(c INT) ENGINE=INNODB;
CREATE TABLE t3(b BLOB NOT NULL) ENGINE=INNODB;
SELECT
(SELECT 1 FROM t1
WHERE (SELECT c FROM t2 ORDER BY COUNT(t3.b ))
)
FROM t3;
(SELECT 1 FROM t1
WHERE (SELECT c FROM t2 ORDER BY COUNT(t3.b ))
)
NULL
DROP TABLE t1,t2,t3;
#
# Bug#16833464: Presence of ORDER BY changes data type of column in
#               CREATE TABLE SELECT
CREATE TABLE z1 (c05 TIME);
(SELECT * FROM z1 ORDER BY '1') ORDER BY '1';
c05
CREATE TABLE z2 (SELECT * FROM z1 ORDER BY '1') ORDER BY '1';
CREATE TABLE z3 (SELECT * FROM z1) ORDER BY '1';
SHOW CREATE TABLE z2;
Table	Create Table
z2	CREATE TABLE `z2` (
  `c05` time DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE z3;
Table	Create Table
z3	CREATE TABLE `z3` (
  `c05` time DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE z1, z2, z3;
#
# Bug#32695670: ASSERTION `M_CURRENT_ROLLUP_LEVEL >= 0 && M_CURRENT_ROLLUP_LEVEL < M_NUM_LEVELS'
#
CREATE TABLE t(
m MEDIUMTEXT,   # must be blob, CHAR(1), TEXT works....
pk INT PRIMARY KEY
);
INSERT INTO t VALUES ('s',1);
SELECT MIN(alias2.m) AS field2,
alias2.pk AS field3
FROM t AS alias1
LEFT JOIN
t AS alias2
ON alias1.m = alias2.m
GROUP BY field3 WITH ROLLUP
ORDER BY UPPER(field2);
field2	field3
s	1
s	NULL
DROP TABLE t;
CREATE TABLE t1 ( a INTEGER, b LONGTEXT );
INSERT INTO t1 VALUES (1,'z');
INSERT INTO t1 VALUES (2,'y');
INSERT INTO t1 VALUES (3,'x');
SELECT MIN(b) AS min_b FROM t1 GROUP BY a ORDER BY COALESCE(MIN(b), 'a');
min_b
x
y
z
SELECT MIN(b) AS min_b FROM t1 GROUP BY a ORDER BY COALESCE(min_b, 'a');
min_b
x
y
z
SELECT MIN(b) AS min_b FROM t1 GROUP BY a ORDER BY COALESCE(MIN(b), 'a') DESC;
min_b
z
y
x
SELECT MIN(b) AS min_b FROM t1 GROUP BY a ORDER BY COALESCE(min_b, 'a') DESC;
min_b
z
y
x
DROP TABLE t1;
#
# Bug#37177744: Wrong ordering of outer join results with
#               the hypergraph optimizer
#
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES (1), (2), (3);
SELECT t2.a FROM t1 LEFT JOIN t1 AS t2 ON t1.a = t2.a AND t2.a = 2
ORDER BY t2.a;
a
NULL
NULL
2
CREATE TABLE t2(a INT, b INT);
INSERT INTO t2 VALUES (2, 0);
SELECT t2.b + t3.b AS expr FROM
t1 LEFT JOIN (t2 JOIN t2 AS t3 ON t2.a = t3.a)
ON t1.a = t2.a AND t1.a = t3.a AND t2.b + t3.b = 0
ORDER BY expr;
expr
NULL
NULL
0
SELECT t2.b + t3.b + t4.b AS expr FROM
t1 LEFT JOIN
(t2 JOIN t2 AS t3 ON t2.a = t3.a JOIN t2 AS t4 ON t3.a = t4.a)
ON t1.a <=> t2.a AND t2.a <=> t3.a AND t3.a <=> t4.a AND
t2.b + t3.b + t4.b = 0
ORDER BY expr;
expr
NULL
NULL
0
DROP TABLE t1, t2;
set optimizer_switch=default;
