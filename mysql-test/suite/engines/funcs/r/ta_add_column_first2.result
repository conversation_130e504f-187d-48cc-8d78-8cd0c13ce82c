DROP TABLE IF EXISTS t1;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bit(1) DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bit(1) DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bit(1) DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bit(1) DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bit(1) NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bit(1) NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bit(1) NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bit(1) NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyint DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyint DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyint DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyint DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyint NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyint NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyint NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyint NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 SMALLINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` smallint DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 SMALLINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` smallint DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 SMALLINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` smallint DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 SMALLINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` smallint DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` smallint NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` smallint NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` smallint NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` smallint NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumint DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumint DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumint DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumint DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumint NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumint NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumint NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumint NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INTEGER  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INTEGER  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INTEGER  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INTEGER  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` int NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIGINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bigint DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIGINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bigint DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIGINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bigint DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIGINT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bigint DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bigint NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bigint NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bigint NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` bigint NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 REAL  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 REAL  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 REAL  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 REAL  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 REAL  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 REAL  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 REAL  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 REAL  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DOUBLE  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DOUBLE  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DOUBLE  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DOUBLE  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` double NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 FLOAT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` float DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 FLOAT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` float DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 FLOAT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` float DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 FLOAT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` float DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` float NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` float NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` float NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` float NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DECIMAL  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DECIMAL  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DECIMAL  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DECIMAL  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 NUMERIC  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 NUMERIC  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 NUMERIC  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 NUMERIC  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` decimal(10,0) NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DATE  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` date DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DATE  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` date DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DATE  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` date DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DATE  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` date DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DATE  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` date NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DATE  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` date NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DATE  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` date NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 DATE  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` date NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIME  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` time DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIME  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` time DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIME  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` time DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIME  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` time DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIME  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` time NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIME  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` time NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIME  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` time NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIME  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` time NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` timestamp NULL DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` timestamp NULL DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` timestamp NULL DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` timestamp NULL DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` timestamp NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` timestamp NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` timestamp NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` timestamp NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 YEAR  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` year DEFAULT NULL,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 YEAR  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` year DEFAULT NULL,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 YEAR  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` year DEFAULT NULL,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 YEAR  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` year DEFAULT NULL,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 YEAR  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` year NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 YEAR  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` year NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 YEAR  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` year NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 YEAR  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` year NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYBLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyblob,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYBLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyblob,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYBLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyblob,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYBLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyblob,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyblob NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyblob NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyblob NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinyblob NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` blob,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` blob,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` blob,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` blob,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` blob NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` blob NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` blob NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 BLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` blob NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumblob,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumblob,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumblob,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumblob,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumblob NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumblob NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumblob NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumblob NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGBLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longblob,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGBLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longblob,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGBLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longblob,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGBLOB  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longblob,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longblob NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longblob NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longblob NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longblob NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYTEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinytext,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYTEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinytext,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYTEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinytext,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYTEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinytext,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinytext NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinytext NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinytext NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` tinytext NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` text,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` text,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` text,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` text,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` text NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` text NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` text NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 TEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` text NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumtext,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumtext,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumtext,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumtext,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumtext NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumtext NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumtext NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` mediumtext NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGTEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longtext,
  `c1` char(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGTEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longtext,
  `c1` varchar(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGTEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longtext,
  `c1` binary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGTEXT  NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longtext,
  `c1` varbinary(15) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longtext NOT NULL,
  `c1` char(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longtext NOT NULL,
  `c1` varchar(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longtext NOT NULL,
  `c1` binary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c2` longtext NOT NULL,
  `c1` varbinary(15) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
