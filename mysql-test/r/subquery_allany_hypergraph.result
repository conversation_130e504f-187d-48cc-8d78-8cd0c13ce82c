WL#13052: Transform table subqueries using derived tables
CREATE TABLE it
(pk    INTEGER PRIMARY KEY,
val   INTEGER NOT NULL,
valn  INTEGER,
nulls INTEGER
);
CREATE TABLE ot(
v INTEGER NOT NULL,
vn INTEGER);
CREATE TABLE itr
(rf    INTEGER,
pki   INTEGER,
val   INTEGER NOT NULL,
valn  INTEGER,
nulls INTEGER,
PRIMARY KEY(rf, pki)
);
CREATE TABLE otr(
rf INTEGER,
v INTEGER NOT NULL,
vn INTEGER);
CREATE TABLE t0row(v INTEGER NOT NULL);
CREATE TABLE t1row(v INTEGER NOT NULL, n INTEGER);
CREATE TABLE t2row(v INTEGER NOT NULL, vn INTEGER, n INTEGER);
CREATE TABLE t3row(v INTEGER NOT NULL, vn INTEGER, n INTEGER);
CREATE TABLE t4row(v INTEGER NOT NULL, vn INTEGER, n INTEGER);
CREATE TABLE t_null(i INTEGER);
CREATE TABLE t_outer(i INTEGER NOT NULL);
CREATE TABLE t_empty(i INTEGER);
INSERT INTO it(pk, val, valn, nulls) VALUES
(1, 1,    1, NULL),
(2, 2,    2, NULL),
(3, 3, NULL, NULL);
INSERT INTO ot VALUES (0, 0), (1, 1), (2, 2), (3, 3), (4, NULL);
INSERT INTO itr SELECT 1, pk, val, valn, nulls FROM it;
INSERT INTO itr SELECT 2, pk, val, valn, nulls FROM it;
INSERT INTO otr SELECT 1, v, vn FROM ot;
INSERT INTO otr SELECT 2, v, vn FROM ot;
INSERT INTO otr SELECT 3, v, vn FROM ot;
INSERT INTO t1row VALUES (2, NULL);
INSERT INTO t2row VALUES (2, 2, NULL), (2, NULL, NULL);
INSERT INTO t3row VALUES (2, 2, NULL), (2, 2, NULL), (2, NULL, NULL);
INSERT INTO t4row VALUES
(2, 2, NULL), (2, 2, NULL), (2, NULL, NULL), (3, 3, NULL);
INSERT INTO t_null VALUES (NULL), (1), (1), (2);
INSERT INTO t_outer VALUES (1), (1), (2);
ANALYZE TABLE it, ot, t1row, t2row, t3row, t4row, t_null, t_outer;
Table	Op	Msg_type	Msg_text
test.it	analyze	status	OK
test.ot	analyze	status	OK
test.t1row	analyze	status	OK
test.t2row	analyze	status	OK
test.t3row	analyze	status	OK
test.t4row	analyze	status	OK
test.t_null	analyze	status	OK
test.t_outer	analyze	status	OK

# -------------------------------------------------------------------#
# Test quantified comparison predicates with subquery_to_derived OFF #
# -------------------------------------------------------------------#


# quantified comparison predicate in SELECT list
# non-nullable columns
# no dependent subquery predicate

SELECT v <>ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT v <>ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) = it.val)  (rows=0.3)
        -> Table scan on it  (rows=3)

SELECT NOT EXISTS (SELECT * FROM it WHERE ot.v = it.val) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT NOT EXISTS (SELECT * FROM it WHERE ot.v = it.val) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Limit: 1 row(s)  (rows=0.3)
        -> Filter: (ot.v = it.val)  (rows=0.3)
            -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT v >ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v >ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: max(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

SELECT v >=ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: max(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

SELECT v <ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v <ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: min(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

SELECT v <=ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT v <=ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: min(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

SELECT v =ANY (SELECT val FROM it) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT v =ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) = it.val)  (rows=0.3)
        -> Table scan on it  (rows=3)

SELECT EXISTS (SELECT * FROM it WHERE ot.v = it.val) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT EXISTS (SELECT * FROM it WHERE ot.v = it.val) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Limit: 1 row(s)  (rows=0.3)
        -> Filter: (ot.v = it.val)  (rows=0.3)
            -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT v >ANY (SELECT val FROM it) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT v >ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: min(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

SELECT v >=ANY (SELECT val FROM it) AS b
FROM ot;
b
0
1
1
1
1
explain format=tree SELECT v >=ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: min(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

SELECT v <ANY (SELECT val FROM it) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT v <ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: max(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

SELECT v <=ANY (SELECT val FROM it) AS b
FROM ot;
b
0
1
1
1
1
explain format=tree SELECT v <=ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: max(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

SELECT v =ALL (SELECT v FROM t0row) AS b
FROM ot;
b
1
1
1
1
1
explain format=tree SELECT v =ALL (SELECT v FROM t0row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) <> t0row.v)  (rows=0.9)
        -> Table scan on t0row  (rows=1)

SELECT v =ALL (SELECT v FROM t1row) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v =ALL (SELECT v FROM t1row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) <> t1row.v)  (rows=0.9)
        -> Table scan on t1row  (rows=1)

SELECT v =ALL (SELECT v FROM t2row) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v =ALL (SELECT v FROM t2row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) <> t2row.v)  (rows=1.8)
        -> Table scan on t2row  (rows=2)

SELECT v =ALL (SELECT v FROM t4row) AS b
FROM ot;
b
0
0
0
0
0
explain format=tree SELECT v =ALL (SELECT v FROM t4row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) <> t4row.v)  (rows=3.6)
        -> Table scan on t4row  (rows=4)

SELECT v <>ANY (SELECT v FROM t0row) AS b
FROM ot;
b
0
0
0
0
0
explain format=tree SELECT v <>ANY (SELECT v FROM t0row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) <> t0row.v)  (rows=0.9)
        -> Table scan on t0row  (rows=1)

SELECT v <>ANY (SELECT v FROM t1row) AS b
FROM ot;
b
0
1
1
1
1
explain format=tree SELECT v <>ANY (SELECT v FROM t1row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) <> t1row.v)  (rows=0.9)
        -> Table scan on t1row  (rows=1)

SELECT v <>ANY (SELECT v FROM t2row) AS b
FROM ot;
b
0
1
1
1
1
explain format=tree SELECT v <>ANY (SELECT v FROM t2row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) <> t2row.v)  (rows=1.8)
        -> Table scan on t2row  (rows=2)

SELECT v <>ANY (SELECT v FROM t4row) AS b
FROM ot;
b
1
1
1
1
1
explain format=tree SELECT v <>ANY (SELECT v FROM t4row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) <> t4row.v)  (rows=3.6)
        -> Table scan on t4row  (rows=4)


# quantified comparison predicate in SELECT list
# nullable outer column
# no dependent subquery predicate

SELECT vn <>ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
NULL
explain format=tree SELECT vn <>ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) = it.val), true)  (rows=3)
        -> Table scan on it  (rows=3)

SELECT NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.val) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.val) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Limit: 1 row(s)  (rows=0.3)
        -> Filter: (ot.vn = it.val)  (rows=0.3)
            -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT vn >ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
0
NULL
explain format=tree SELECT vn >ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <= it.val), true)  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn >=ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
NULL
explain format=tree SELECT vn >=ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) < it.val), true)  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn <ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
NULL
explain format=tree SELECT vn <ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) >= it.val), true)  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn <=ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
1
1
NULL
explain format=tree SELECT vn <=ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) > it.val), true)  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn =ANY (SELECT val FROM it) AS b
FROM ot;
b
0
1
1
1
NULL
explain format=tree SELECT vn =ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) = it.val), true)  (rows=3)
        -> Table scan on it  (rows=3)

SELECT EXISTS (SELECT * FROM it WHERE ot.vn = it.val) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT EXISTS (SELECT * FROM it WHERE ot.vn = it.val) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Limit: 1 row(s)  (rows=0.3)
        -> Filter: (ot.vn = it.val)  (rows=0.3)
            -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT vn >ANY (SELECT val FROM it) AS b
FROM ot;
b
0
0
1
1
NULL
explain format=tree SELECT vn >ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) > it.val), true)  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn >=ANY (SELECT val FROM it) AS b
FROM ot;
b
0
1
1
1
NULL
explain format=tree SELECT vn >=ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) >= it.val), true)  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn <ANY (SELECT val FROM it) AS b
FROM ot;
b
0
1
1
1
NULL
explain format=tree SELECT vn <ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) < it.val), true)  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn <=ANY (SELECT val FROM it) AS b
FROM ot;
b
1
1
1
1
NULL
explain format=tree SELECT vn <=ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <= it.val), true)  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn =ALL (SELECT v FROM t0row) AS b
FROM ot;
b
1
1
1
1
1
explain format=tree SELECT vn =ALL (SELECT v FROM t0row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <> t0row.v), true)  (rows=1)
        -> Table scan on t0row  (rows=1)

SELECT vn =ALL (SELECT v FROM t1row) AS b
FROM ot;
b
0
0
0
1
NULL
explain format=tree SELECT vn =ALL (SELECT v FROM t1row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <> t1row.v), true)  (rows=1)
        -> Table scan on t1row  (rows=1)

SELECT vn =ALL (SELECT v FROM t2row) AS b
FROM ot;
b
0
0
0
1
NULL
explain format=tree SELECT vn =ALL (SELECT v FROM t2row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <> t2row.v), true)  (rows=2)
        -> Table scan on t2row  (rows=2)

SELECT vn =ALL (SELECT v FROM t4row) AS b
FROM ot;
b
0
0
0
0
NULL
explain format=tree SELECT vn =ALL (SELECT v FROM t4row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <> t4row.v), true)  (rows=4)
        -> Table scan on t4row  (rows=4)

SELECT vn <>ANY (SELECT v FROM t0row) AS b
FROM ot;
b
0
0
0
0
0
explain format=tree SELECT vn <>ANY (SELECT v FROM t0row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <> t0row.v), true)  (rows=1)
        -> Table scan on t0row  (rows=1)

SELECT vn <>ANY (SELECT v FROM t1row) AS b
FROM ot;
b
0
1
1
1
NULL
explain format=tree SELECT vn <>ANY (SELECT v FROM t1row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <> t1row.v), true)  (rows=1)
        -> Table scan on t1row  (rows=1)

SELECT vn <>ANY (SELECT v FROM t2row) AS b
FROM ot;
b
0
1
1
1
NULL
explain format=tree SELECT vn <>ANY (SELECT v FROM t2row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <> t2row.v), true)  (rows=2)
        -> Table scan on t2row  (rows=2)

SELECT vn <>ANY (SELECT v FROM t4row) AS b
FROM ot;
b
1
1
1
1
NULL
explain format=tree SELECT vn <>ANY (SELECT v FROM t4row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <> t4row.v), true)  (rows=4)
        -> Table scan on t4row  (rows=4)


# quantified comparison predicate in SELECT list
# nullable column in subquery
# no dependent subquery predicate

SELECT v <>ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
NULL
NULL
NULL
explain format=tree SELECT v <>ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) = it.valn) or (it.valn is null)) and <is_not_null_test>(it.valn))  (rows=0.057)
        -> Table scan on it  (rows=3)

SELECT NOT EXISTS (SELECT * FROM it WHERE ot.v = it.valn) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT NOT EXISTS (SELECT * FROM it WHERE ot.v = it.valn) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Limit: 1 row(s)  (rows=0.3)
        -> Filter: (ot.v = it.valn)  (rows=0.3)
            -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT v >ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT v >ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) <= it.valn) or (it.valn is null)) and <is_not_null_test>(it.valn))  (rows=0.12)
        -> Table scan on it  (rows=3)

SELECT v >=ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
NULL
NULL
NULL
explain format=tree SELECT v >=ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) < it.valn) or (it.valn is null)) and <is_not_null_test>(it.valn))  (rows=0.12)
        -> Table scan on it  (rows=3)

SELECT v <ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
0
0
NULL
explain format=tree SELECT v <ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) >= it.valn) or (it.valn is null)) and <is_not_null_test>(it.valn))  (rows=0.12)
        -> Table scan on it  (rows=3)

SELECT v <=ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT v <=ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) > it.valn) or (it.valn is null)) and <is_not_null_test>(it.valn))  (rows=0.12)
        -> Table scan on it  (rows=3)

SELECT v =ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
NULL
NULL
NULL
explain format=tree SELECT v =ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) = it.valn) or (it.valn is null)) and <is_not_null_test>(it.valn))  (rows=0.057)
        -> Table scan on it  (rows=3)

SELECT EXISTS (SELECT * FROM it WHERE ot.v = it.valn) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT EXISTS (SELECT * FROM it WHERE ot.v = it.valn) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Limit: 1 row(s)  (rows=0.3)
        -> Filter: (ot.v = it.valn)  (rows=0.3)
            -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT v >ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
1
NULL
NULL
explain format=tree SELECT v >ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) > it.valn) or (it.valn is null)) and <is_not_null_test>(it.valn))  (rows=0.12)
        -> Table scan on it  (rows=3)

SELECT v >=ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
1
1
NULL
explain format=tree SELECT v >=ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) >= it.valn) or (it.valn is null)) and <is_not_null_test>(it.valn))  (rows=0.12)
        -> Table scan on it  (rows=3)

SELECT v <ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
NULL
NULL
NULL
explain format=tree SELECT v <ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) < it.valn) or (it.valn is null)) and <is_not_null_test>(it.valn))  (rows=0.12)
        -> Table scan on it  (rows=3)

SELECT v <=ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
1
NULL
NULL
explain format=tree SELECT v <=ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) <= it.valn) or (it.valn is null)) and <is_not_null_test>(it.valn))  (rows=0.12)
        -> Table scan on it  (rows=3)

SELECT v =ALL (SELECT n FROM t1row) AS b
FROM ot;
b
NULL
NULL
NULL
NULL
NULL
explain format=tree SELECT v =ALL (SELECT n FROM t1row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) <> t1row.n) or (t1row.n is null)) and <is_not_null_test>(t1row.n))  (rows=0.091)
        -> Table scan on t1row  (rows=1)

SELECT v =ALL (SELECT vn FROM t2row) AS b
FROM ot;
b
0
0
0
0
NULL
explain format=tree SELECT v =ALL (SELECT vn FROM t2row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) <> t2row.vn) or (t2row.vn is null)) and <is_not_null_test>(t2row.vn))  (rows=0.182)
        -> Table scan on t2row  (rows=2)

SELECT v =ALL (SELECT vn FROM t3row) AS b
FROM ot;
b
0
0
0
0
NULL
explain format=tree SELECT v =ALL (SELECT vn FROM t3row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) <> t3row.vn) or (t3row.vn is null)) and <is_not_null_test>(t3row.vn))  (rows=0.273)
        -> Table scan on t3row  (rows=3)

SELECT v =ALL (SELECT vn FROM t4row) AS b
FROM ot;
b
0
0
0
0
0
explain format=tree SELECT v =ALL (SELECT vn FROM t4row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) <> t4row.vn) or (t4row.vn is null)) and <is_not_null_test>(t4row.vn))  (rows=0.364)
        -> Table scan on t4row  (rows=4)

SELECT v <>ANY (SELECT n FROM t1row) AS b
FROM ot;
b
NULL
NULL
NULL
NULL
NULL
explain format=tree SELECT v <>ANY (SELECT n FROM t1row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) <> t1row.n) or (t1row.n is null)) and <is_not_null_test>(t1row.n))  (rows=0.091)
        -> Table scan on t1row  (rows=1)

SELECT v <>ANY (SELECT vn FROM t2row) AS b
FROM ot;
b
1
1
1
1
NULL
explain format=tree SELECT v <>ANY (SELECT vn FROM t2row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) <> t2row.vn) or (t2row.vn is null)) and <is_not_null_test>(t2row.vn))  (rows=0.182)
        -> Table scan on t2row  (rows=2)

SELECT v <>ANY (SELECT vn FROM t3row) AS b
FROM ot;
b
1
1
1
1
NULL
explain format=tree SELECT v <>ANY (SELECT vn FROM t3row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) <> t3row.vn) or (t3row.vn is null)) and <is_not_null_test>(t3row.vn))  (rows=0.273)
        -> Table scan on t3row  (rows=3)

SELECT v <>ANY (SELECT vn FROM t4row) AS b
FROM ot;
b
1
1
1
1
1
explain format=tree SELECT v <>ANY (SELECT vn FROM t4row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) <> t4row.vn) or (t4row.vn is null)) and <is_not_null_test>(t4row.vn))  (rows=0.364)
        -> Table scan on t4row  (rows=4)


# quantified comparison predicate in SELECT list
# nullable columns
# no dependent subquery predicate

SELECT vn <>ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
NULL
NULL
NULL
explain format=tree SELECT vn <>ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) = it.valn) or (it.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(it.valn), true))  (rows=3)
        -> Table scan on it  (rows=3)

SELECT NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.valn) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.valn) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Limit: 1 row(s)  (rows=0.3)
        -> Filter: (ot.vn = it.valn)  (rows=0.3)
            -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT vn >ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT vn >ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <= it.valn) or (it.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(it.valn), true))  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn >=ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
NULL
NULL
NULL
explain format=tree SELECT vn >=ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) < it.valn) or (it.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(it.valn), true))  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn <ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT vn <ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) >= it.valn) or (it.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(it.valn), true))  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn <=ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
NULL
NULL
NULL
explain format=tree SELECT vn <=ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) > it.valn) or (it.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(it.valn), true))  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn =ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
NULL
NULL
NULL
explain format=tree SELECT vn =ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) = it.valn) or (it.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(it.valn), true))  (rows=3)
        -> Table scan on it  (rows=3)

SELECT EXISTS (SELECT * FROM it WHERE ot.vn = it.valn) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT EXISTS (SELECT * FROM it WHERE ot.vn = it.valn) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Limit: 1 row(s)  (rows=0.3)
        -> Filter: (ot.vn = it.valn)  (rows=0.3)
            -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT vn >ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
NULL
NULL
NULL
explain format=tree SELECT vn >ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) > it.valn) or (it.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(it.valn), true))  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn >=ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
1
NULL
NULL
explain format=tree SELECT vn >=ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) >= it.valn) or (it.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(it.valn), true))  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn <ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
NULL
NULL
NULL
explain format=tree SELECT vn <ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) < it.valn) or (it.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(it.valn), true))  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn <=ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
1
NULL
NULL
explain format=tree SELECT vn <=ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <= it.valn) or (it.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(it.valn), true))  (rows=3)
        -> Table scan on it  (rows=3)

SELECT vn =ALL (SELECT n FROM t1row) AS b
FROM ot;
b
NULL
NULL
NULL
NULL
NULL
explain format=tree SELECT vn =ALL (SELECT n FROM t1row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <> t1row.n) or (t1row.n is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(t1row.n), true))  (rows=1)
        -> Table scan on t1row  (rows=1)

SELECT vn =ALL (SELECT vn FROM t2row) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT vn =ALL (SELECT vn FROM t2row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <> t2row.vn) or (t2row.vn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(t2row.vn), true))  (rows=2)
        -> Table scan on t2row  (rows=2)

SELECT vn =ALL (SELECT vn FROM t3row) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT vn =ALL (SELECT vn FROM t3row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <> t3row.vn) or (t3row.vn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(t3row.vn), true))  (rows=3)
        -> Table scan on t3row  (rows=3)

SELECT vn =ALL (SELECT vn FROM t4row) AS b
FROM ot;
b
0
0
0
0
NULL
explain format=tree SELECT vn =ALL (SELECT vn FROM t4row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <> t4row.vn) or (t4row.vn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(t4row.vn), true))  (rows=4)
        -> Table scan on t4row  (rows=4)

SELECT vn <>ANY (SELECT n FROM t1row) AS b
FROM ot;
b
NULL
NULL
NULL
NULL
NULL
explain format=tree SELECT vn <>ANY (SELECT n FROM t1row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <> t1row.n) or (t1row.n is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(t1row.n), true))  (rows=1)
        -> Table scan on t1row  (rows=1)

SELECT vn <>ANY (SELECT vn FROM t2row) AS b
FROM ot;
b
1
1
1
NULL
NULL
explain format=tree SELECT vn <>ANY (SELECT vn FROM t2row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <> t2row.vn) or (t2row.vn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(t2row.vn), true))  (rows=2)
        -> Table scan on t2row  (rows=2)

SELECT vn <>ANY (SELECT vn FROM t3row) AS b
FROM ot;
b
1
1
1
NULL
NULL
explain format=tree SELECT vn <>ANY (SELECT vn FROM t3row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <> t3row.vn) or (t3row.vn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(t3row.vn), true))  (rows=3)
        -> Table scan on t3row  (rows=3)

SELECT vn <>ANY (SELECT vn FROM t4row) AS b
FROM ot;
b
1
1
1
1
NULL
explain format=tree SELECT vn <>ANY (SELECT vn FROM t4row) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <> t4row.vn) or (t4row.vn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(t4row.vn), true))  (rows=4)
        -> Table scan on t4row  (rows=4)


# quantified comparison predicate in WHERE clause
# non-nullable columns
# no dependent subquery predicate

SELECT *
FROM ot
WHERE v <>ALL (SELECT val FROM it);
v	vn
0	0
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ALL (SELECT val FROM it);
EXPLAIN
-> Hash antijoin (ot.v = it.val)  (rows=4.13)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.v = it.val);
v	vn
0	0
4	NULL
explain format=tree SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.v = it.val);
EXPLAIN
-> Hash antijoin (ot.v = it.val)  (rows=4.13)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it);
v	vn
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it);
EXPLAIN
-> Filter: <not>((ot.v <= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >=ALL (SELECT val FROM it);
v	vn
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >=ALL (SELECT val FROM it);
EXPLAIN
-> Filter: <not>((ot.v < (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <ALL (SELECT val FROM it);
v	vn
0	0
explain format=tree SELECT *
FROM ot
WHERE v <ALL (SELECT val FROM it);
EXPLAIN
-> Filter: <not>((ot.v >= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <=ALL (SELECT val FROM it);
v	vn
0	0
1	1
explain format=tree SELECT *
FROM ot
WHERE v <=ALL (SELECT val FROM it);
EXPLAIN
-> Filter: <not>((ot.v > (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v =ANY (SELECT val FROM it);
v	vn
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE v =ANY (SELECT val FROM it);
EXPLAIN
-> Hash semijoin (FirstMatch) (ot.v = it.val)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.v = it.val);
v	vn
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.v = it.val);
EXPLAIN
-> Hash semijoin (FirstMatch) (ot.v = it.val)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE v >ANY (SELECT val FROM it);
v	vn
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ANY (SELECT val FROM it);
EXPLAIN
-> Filter: <nop>((ot.v > (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >=ANY (SELECT val FROM it);
v	vn
1	1
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >=ANY (SELECT val FROM it);
EXPLAIN
-> Filter: <nop>((ot.v >= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <ANY (SELECT val FROM it);
v	vn
0	0
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE v <ANY (SELECT val FROM it);
EXPLAIN
-> Filter: <nop>((ot.v < (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <=ANY (SELECT val FROM it);
v	vn
0	0
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE v <=ANY (SELECT val FROM it);
EXPLAIN
-> Filter: <nop>((ot.v <= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t0row);
v	vn
0	0
1	1
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t0row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.v) <> t0row.v)  (rows=0.9)
            -> Table scan on t0row  (rows=1)

SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t1row);
v	vn
2	2
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t1row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.v) <> t1row.v)  (rows=0.9)
            -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t2row);
v	vn
2	2
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t2row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.v) <> t2row.v)  (rows=1.8)
            -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t4row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t4row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.v) <> t4row.v)  (rows=3.6)
            -> Table scan on t4row  (rows=4)

SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t0row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t0row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.v) <> t0row.v)  (rows=0.9)
            -> Table scan on t0row  (rows=1)

SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t1row);
v	vn
0	0
1	1
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t1row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.v) <> t1row.v)  (rows=0.9)
            -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t2row);
v	vn
0	0
1	1
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t2row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.v) <> t2row.v)  (rows=1.8)
            -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t4row);
v	vn
0	0
1	1
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t4row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.v) <> t4row.v)  (rows=3.6)
            -> Table scan on t4row  (rows=4)


# quantified comparison predicate in WHERE clause
# nullable outer column
# no dependent subquery predicate

SELECT *
FROM ot
WHERE vn <>ALL (SELECT val FROM it);
v	vn
0	0
explain format=tree SELECT *
FROM ot
WHERE vn <>ALL (SELECT val FROM it);
EXPLAIN
-> Filter: <in_optimizer>(ot.vn,ot.vn in (select #2) is false)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Filter: ((ot.vn = `<materialized_subquery>`.val))  (rows=1)
            -> Limit: 1 row(s)  (rows=1)
                -> Index lookup on <materialized_subquery> using <auto_distinct_key> (val = ot.vn)
                    -> Materialize with deduplication  (rows=3)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.val);
v	vn
0	0
4	NULL
explain format=tree SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.val);
EXPLAIN
-> Hash antijoin (ot.vn = it.val)  (rows=4.13)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE vn >ALL (SELECT val FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn >ALL (SELECT val FROM it);
EXPLAIN
-> Filter: <not>((ot.vn <= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn >=ALL (SELECT val FROM it);
v	vn
3	3
explain format=tree SELECT *
FROM ot
WHERE vn >=ALL (SELECT val FROM it);
EXPLAIN
-> Filter: <not>((ot.vn < (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <ALL (SELECT val FROM it);
v	vn
0	0
explain format=tree SELECT *
FROM ot
WHERE vn <ALL (SELECT val FROM it);
EXPLAIN
-> Filter: <not>((ot.vn >= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <=ALL (SELECT val FROM it);
v	vn
0	0
1	1
explain format=tree SELECT *
FROM ot
WHERE vn <=ALL (SELECT val FROM it);
EXPLAIN
-> Filter: <not>((ot.vn > (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn =ANY (SELECT val FROM it);
v	vn
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn =ANY (SELECT val FROM it);
EXPLAIN
-> Hash semijoin (FirstMatch) (ot.vn = it.val)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.vn = it.val);
v	vn
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.vn = it.val);
EXPLAIN
-> Hash semijoin (FirstMatch) (ot.vn = it.val)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE vn >ANY (SELECT val FROM it);
v	vn
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn >ANY (SELECT val FROM it);
EXPLAIN
-> Filter: <nop>((ot.vn > (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn >=ANY (SELECT val FROM it);
v	vn
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn >=ANY (SELECT val FROM it);
EXPLAIN
-> Filter: <nop>((ot.vn >= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <ANY (SELECT val FROM it);
v	vn
0	0
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE vn <ANY (SELECT val FROM it);
EXPLAIN
-> Filter: <nop>((ot.vn < (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <=ANY (SELECT val FROM it);
v	vn
0	0
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <=ANY (SELECT val FROM it);
EXPLAIN
-> Filter: <nop>((ot.vn <= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t0row);
v	vn
0	0
1	1
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t0row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <> t0row.v), true)  (rows=1)
            -> Table scan on t0row  (rows=1)

SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t1row);
v	vn
2	2
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t1row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <> t1row.v), true)  (rows=1)
            -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t2row);
v	vn
2	2
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t2row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <> t2row.v), true)  (rows=2)
            -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t4row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t4row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) <> t4row.v), true)  (rows=4)
            -> Table scan on t4row  (rows=4)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t0row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t0row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.vn) <> t0row.v)  (rows=0.9)
            -> Table scan on t0row  (rows=1)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t1row);
v	vn
0	0
1	1
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t1row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.vn) <> t1row.v)  (rows=0.9)
            -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t2row);
v	vn
0	0
1	1
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t2row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.vn) <> t2row.v)  (rows=1.8)
            -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t4row);
v	vn
0	0
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t4row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.vn) <> t4row.v)  (rows=3.6)
            -> Table scan on t4row  (rows=4)


# quantified comparison predicate in WHERE clause
# nullable column in subquery
# no dependent subquery predicate

SELECT *
FROM ot
WHERE v <>ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v <>ALL (SELECT valn FROM it);
EXPLAIN
-> Filter: <in_optimizer>(ot.v,ot.v in (select #2) is false)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Filter: ((ot.v = `<materialized_subquery>`.valn))  (rows=1)
            -> Limit: 1 row(s)  (rows=1)
                -> Index lookup on <materialized_subquery> using <auto_distinct_key> (valn = ot.v)
                    -> Materialize with deduplication  (rows=3)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.v = it.valn);
v	vn
0	0
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.v = it.valn);
EXPLAIN
-> Hash antijoin (ot.v = it.valn)  (rows=4.13)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE v >ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT valn FROM it);
EXPLAIN
-> Filter: <not>((ot.v <= <max>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >=ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v >=ALL (SELECT valn FROM it);
EXPLAIN
-> Filter: <not>((ot.v < <max>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v <ALL (SELECT valn FROM it);
EXPLAIN
-> Filter: <not>((ot.v >= <min>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <=ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v <=ALL (SELECT valn FROM it);
EXPLAIN
-> Filter: <not>((ot.v > <min>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v =ANY (SELECT valn FROM it);
v	vn
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE v =ANY (SELECT valn FROM it);
EXPLAIN
-> Hash semijoin (FirstMatch) (ot.v = it.valn)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.v = it.valn);
v	vn
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.v = it.valn);
EXPLAIN
-> Hash semijoin (FirstMatch) (ot.v = it.valn)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE v >ANY (SELECT valn FROM it);
v	vn
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ANY (SELECT valn FROM it);
EXPLAIN
-> Filter: <nop>((ot.v > (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.valn)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >=ANY (SELECT valn FROM it);
v	vn
1	1
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >=ANY (SELECT valn FROM it);
EXPLAIN
-> Filter: <nop>((ot.v >= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.valn)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <ANY (SELECT valn FROM it);
v	vn
0	0
1	1
explain format=tree SELECT *
FROM ot
WHERE v <ANY (SELECT valn FROM it);
EXPLAIN
-> Filter: <nop>((ot.v < (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.valn)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <=ANY (SELECT valn FROM it);
v	vn
0	0
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE v <=ANY (SELECT valn FROM it);
EXPLAIN
-> Filter: <nop>((ot.v <= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.valn)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v =ALL (SELECT n FROM t1row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT n FROM t1row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (((<cache>(ot.v) <> t1row.n) or (t1row.n is null)) and <is_not_null_test>(t1row.n))  (rows=0.091)
            -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE v =ALL (SELECT vn FROM t2row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT vn FROM t2row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (((<cache>(ot.v) <> t2row.vn) or (t2row.vn is null)) and <is_not_null_test>(t2row.vn))  (rows=0.182)
            -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE v =ALL (SELECT vn FROM t3row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT vn FROM t3row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (((<cache>(ot.v) <> t3row.vn) or (t3row.vn is null)) and <is_not_null_test>(t3row.vn))  (rows=0.273)
            -> Table scan on t3row  (rows=3)

SELECT *
FROM ot
WHERE v =ALL (SELECT vn FROM t4row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT vn FROM t4row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (((<cache>(ot.v) <> t4row.vn) or (t4row.vn is null)) and <is_not_null_test>(t4row.vn))  (rows=0.364)
            -> Table scan on t4row  (rows=4)

SELECT *
FROM ot
WHERE v <>ANY (SELECT n FROM t1row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT n FROM t1row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.v) <> t1row.n)  (rows=0.9)
            -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE v <>ANY (SELECT vn FROM t2row);
v	vn
0	0
1	1
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT vn FROM t2row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.v) <> t2row.vn)  (rows=1.8)
            -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE v <>ANY (SELECT vn FROM t3row);
v	vn
0	0
1	1
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT vn FROM t3row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.v) <> t3row.vn)  (rows=2.7)
            -> Table scan on t3row  (rows=3)

SELECT *
FROM ot
WHERE v <>ANY (SELECT vn FROM t4row);
v	vn
0	0
1	1
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT vn FROM t4row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.v,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.v) <> t4row.vn)  (rows=3.6)
            -> Table scan on t4row  (rows=4)


# quantified comparison predicate in WHERE clause
# nullable columns
# no dependent subquery predicate

SELECT *
FROM ot
WHERE vn <>ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn <>ALL (SELECT valn FROM it);
EXPLAIN
-> Filter: <in_optimizer>(ot.vn,ot.vn in (select #2) is false)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Filter: ((ot.vn = `<materialized_subquery>`.valn))  (rows=1)
            -> Limit: 1 row(s)  (rows=1)
                -> Index lookup on <materialized_subquery> using <auto_distinct_key> (valn = ot.vn)
                    -> Materialize with deduplication  (rows=3)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.valn);
v	vn
0	0
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.valn);
EXPLAIN
-> Hash antijoin (ot.vn = it.valn)  (rows=4.13)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE vn >ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn >ALL (SELECT valn FROM it);
EXPLAIN
-> Filter: <not>((ot.vn <= <max>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn >=ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn >=ALL (SELECT valn FROM it);
EXPLAIN
-> Filter: <not>((ot.vn < <max>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn <ALL (SELECT valn FROM it);
EXPLAIN
-> Filter: <not>((ot.vn >= <min>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <=ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn <=ALL (SELECT valn FROM it);
EXPLAIN
-> Filter: <not>((ot.vn > <min>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn =ANY (SELECT valn FROM it);
v	vn
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE vn =ANY (SELECT valn FROM it);
EXPLAIN
-> Hash semijoin (FirstMatch) (ot.vn = it.valn)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.vn = it.valn);
v	vn
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.vn = it.valn);
EXPLAIN
-> Hash semijoin (FirstMatch) (ot.vn = it.valn)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE vn >ANY (SELECT valn FROM it);
v	vn
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn >ANY (SELECT valn FROM it);
EXPLAIN
-> Filter: <nop>((ot.vn > (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.valn)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn >=ANY (SELECT valn FROM it);
v	vn
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn >=ANY (SELECT valn FROM it);
EXPLAIN
-> Filter: <nop>((ot.vn >= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.valn)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <ANY (SELECT valn FROM it);
v	vn
0	0
1	1
explain format=tree SELECT *
FROM ot
WHERE vn <ANY (SELECT valn FROM it);
EXPLAIN
-> Filter: <nop>((ot.vn < (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.valn)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <=ANY (SELECT valn FROM it);
v	vn
0	0
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE vn <=ANY (SELECT valn FROM it);
EXPLAIN
-> Filter: <nop>((ot.vn <= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.valn)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn =ALL (SELECT n FROM t1row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT n FROM t1row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <> t1row.n) or (t1row.n is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(t1row.n), true))  (rows=1)
            -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE vn =ALL (SELECT vn FROM t2row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT vn FROM t2row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <> t2row.vn) or (t2row.vn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(t2row.vn), true))  (rows=2)
            -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE vn =ALL (SELECT vn FROM t3row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT vn FROM t3row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <> t3row.vn) or (t3row.vn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(t3row.vn), true))  (rows=3)
            -> Table scan on t3row  (rows=3)

SELECT *
FROM ot
WHERE vn =ALL (SELECT vn FROM t4row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT vn FROM t4row);
EXPLAIN
-> Filter: <not>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) <> t4row.vn) or (t4row.vn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(t4row.vn), true))  (rows=4)
            -> Table scan on t4row  (rows=4)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT n FROM t1row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT n FROM t1row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.vn) <> t1row.n)  (rows=0.9)
            -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT vn FROM t2row);
v	vn
0	0
1	1
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT vn FROM t2row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.vn) <> t2row.vn)  (rows=1.8)
            -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT vn FROM t3row);
v	vn
0	0
1	1
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT vn FROM t3row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.vn) <> t3row.vn)  (rows=2.7)
            -> Table scan on t3row  (rows=3)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT vn FROM t4row);
v	vn
0	0
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT vn FROM t4row);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(ot.vn,<exists>(select #2)))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(ot.vn) <> t4row.vn)  (rows=3.6)
            -> Table scan on t4row  (rows=4)

#
# Tests with moderated outer query

# First, a non-moderated outer query:
SELECT v >=ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: max(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

# DISTINCT in SELECT list:
SELECT DISTINCT v >=ALL (SELECT val FROM it) AS b
FROM ot;
b
0
1
explain format=tree SELECT DISTINCT v >=ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Sort with duplicate removal: b  (rows=2.24)
    -> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: max(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

# GROUP BY on quantified comparison predicate
SELECT v >=ALL (SELECT val FROM it) AS b, COUNT(*)
FROM ot
GROUP BY b;
b	COUNT(*)
0	3
1	2
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b, COUNT(*)
FROM ot
GROUP BY b;
EXPLAIN
-> Group aggregate: count(0)  (rows=2.24)
    -> Sort: b  (rows=5)
        -> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: max(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

# GROUP BY and HAVING on quantified comparison predicate
SELECT v >=ALL (SELECT val FROM it) AS b, COUNT(*)
FROM ot
GROUP BY b
HAVING b > 0;
b	COUNT(*)
1	2
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b, COUNT(*)
FROM ot
GROUP BY b
HAVING b > 0;
EXPLAIN
-> Filter: (b > 0)  (rows=2.24)
    -> Group aggregate: count(0)  (rows=2.24)
        -> Sort: b  (rows=5)
            -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

# ORDER BY on quantified comparison predicate
SELECT v >=ALL (SELECT val FROM it) AS b
FROM ot
ORDER BY b;
b
0
0
0
1
1
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b
FROM ot
ORDER BY b;
EXPLAIN
-> Sort: b  (rows=5)
    -> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: max(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

# GROUP BY and ORDER BY on quantified comparison predicate
SELECT v >=ALL (SELECT val FROM it) AS b, COUNT(*)
FROM ot
GROUP BY b
ORDER BY b DESC;
b	COUNT(*)
1	2
0	3
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b, COUNT(*)
FROM ot
GROUP BY b
ORDER BY b DESC;
EXPLAIN
-> Group aggregate: count(0)  (rows=2.24)
    -> Sort: b DESC  (rows=5)
        -> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: max(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

# Quantified comparison predicate as WINDOW expression
SELECT v >=ALL (SELECT val FROM it) AS b,
SUM((SELECT b)) OVER () AS s
FROM ot;
b	s
0	2
0	2
0	2
1	2
1	2
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b,
SUM((SELECT b)) OVER () AS s
FROM ot;
EXPLAIN
-> Window aggregate with buffering: sum(b) OVER ()   (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in projection; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'b' of SELECT #3 was resolved in SELECT #1
Note	1249	Select 3 was reduced during optimization
SELECT v >=ALL (SELECT val FROM it) AS b,
SUM((SELECT b)) OVER (PARTITION BY (SELECT b)) AS s
FROM ot;
b	s
0	0
0	0
0	0
1	2
1	2
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b,
SUM((SELECT b)) OVER (PARTITION BY (SELECT b)) AS s
FROM ot;
EXPLAIN
-> Window aggregate with buffering: sum(b) OVER (PARTITION BY b )   (rows=5)
    -> Sort: b  (rows=5)
        -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in projection; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'b' of SELECT #4 was resolved in SELECT #1
Note	1249	Select 4 was reduced during optimization
Note	1276	Field or reference 'b' of SELECT #3 was resolved in SELECT #1
Note	1249	Select 3 was reduced during optimization
#
# Some negative testing
#
# No tables in outer query block
SELECT 1, 1 >ANY (SELECT val FROM it) AS q1, 2 >ANY (SELECT val FROM it) AS q2;
1	q1	q2
1	0	1
explain format=tree SELECT 1, 1 >ANY (SELECT val FROM it) AS q1, 2 >ANY (SELECT val FROM it) AS q2;
EXPLAIN
-> Rows fetched before execution  (rows=1)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: min(it.val)  (rows=1)
        -> Table scan on it  (rows=3)
-> Select #3 (subquery in projection; run only once)
    -> Aggregate: min(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

SELECT 1 WHERE 2 >ANY (SELECT val FROM it);
1
1
explain format=tree SELECT 1 WHERE 2 >ANY (SELECT val FROM it);
EXPLAIN
-> Filter: <nop>((2 > (select #2)))  (rows=1)
    -> Rows fetched before execution  (rows=1)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

# No tables in inner query block
SELECT 1, 1 >ANY (SELECT 0) AS q1 FROM ot;
1	q1
1	1
1	1
1	1
1	1
1	1
explain format=tree SELECT 1, 1 >ANY (SELECT 0) AS q1 FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Rows fetched before execution  (rows=1)

SELECT 1 FROM ot WHERE 1 >ANY (SELECT 0);
1
1
1
1
1
1
explain format=tree SELECT 1 FROM ot WHERE 1 >ANY (SELECT 0);
EXPLAIN
-> Table scan on ot  (rows=5)

# Set operation in subquery
SELECT v >ALL (SELECT val FROM it UNION SELECT val FROM it) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v >ALL (SELECT val FROM it UNION SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Table scan on <union temporary>  (rows=6)
        -> Union materialize with deduplication  (rows=6)
            -> Table scan on it  (rows=3)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it UNION SELECT val FROM it);
v	vn
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it UNION SELECT val FROM it);
EXPLAIN
-> Filter: <not>((ot.v <= <max>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Table scan on <union temporary>  (rows=6)
            -> Union materialize with deduplication  (rows=6)
                -> Table scan on it  (rows=3)
                -> Table scan on it  (rows=3)

# Implicitly grouped subquery
SELECT v >ALL (SELECT MAX(val) FROM it) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v >ALL (SELECT MAX(val) FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) <= <ref_null_helper>(max(it.val)))  (rows=1)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >ALL (SELECT MAX(val) FROM it);
v	vn
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT MAX(val) FROM it);
EXPLAIN
-> Filter: <not>((ot.v <= <max>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

# Explicitly grouped subquery
SELECT v >ALL (SELECT val FROM it GROUP BY val) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v >ALL (SELECT val FROM it GROUP BY val) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: max(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it GROUP BY val);
v	vn
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it GROUP BY val);
EXPLAIN
-> Filter: <not>((ot.v <= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT v >ALL (SELECT MAX(valn) FROM it GROUP BY val) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT v >ALL (SELECT MAX(valn) FROM it GROUP BY val) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) <= <ref_null_helper>(max(it.valn)))  (rows=1.73)
        -> Group aggregate: max(it.valn)  (rows=1.73)
            -> Sort: it.val  (rows=3)
                -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >ALL (SELECT MAX(valn) FROM it GROUP BY val);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT MAX(valn) FROM it GROUP BY val);
EXPLAIN
-> Filter: <not>((ot.v <= <max>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Group aggregate: max(it.valn)  (rows=1.73)
            -> Sort: it.val  (rows=3)
                -> Table scan on it  (rows=3)

# Subquery with WINDOW function
SELECT v >ALL (SELECT MAX(val) OVER () FROM it) AS b
FROM ot;
b
0
0
0
0
1
SELECT *
FROM ot
WHERE v >ALL (SELECT MAX(val) OVER () FROM it);
v	vn
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT MAX(val) OVER () FROM it);
EXPLAIN
-> Filter: <not>((ot.v <= <max>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Window aggregate with buffering: max(it.val) OVER ()   (rows=3)
            -> Table scan on it  (rows=3)

# Subquery with LIMIT (unsupported) and ORDER BY (supported)
SELECT v >ALL (SELECT val FROM it LIMIT 3) AS b
FROM ot;
ERROR 42000: This version of MySQL doesn't yet support 'LIMIT & IN/ALL/ANY/SOME subquery'
SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it LIMIT 3);
ERROR 42000: This version of MySQL doesn't yet support 'LIMIT & IN/ALL/ANY/SOME subquery'
SELECT v >ALL (SELECT val FROM it ORDER BY val DESC) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v >ALL (SELECT val FROM it ORDER BY val DESC) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: max(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it ORDER BY val DESC);
v	vn
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it ORDER BY val DESC);
EXPLAIN
-> Filter: <not>((ot.v <= (select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT v >ALL (SELECT val FROM it ORDER BY val DESC LIMIT 3) AS b
FROM ot;
ERROR 42000: This version of MySQL doesn't yet support 'LIMIT & IN/ALL/ANY/SOME subquery'
SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it ORDER BY val DESC LIMIT 3);
ERROR 42000: This version of MySQL doesn't yet support 'LIMIT & IN/ALL/ANY/SOME subquery'
# Subquery with outer reference in SELECT list
SELECT rf, v, v <>ALL (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	v	b
1	0	1
1	1	0
1	2	0
1	3	0
1	4	1
2	0	1
2	1	0
2	2	0
2	3	0
2	4	1
3	0	1
3	1	1
3	2	1
3	3	1
3	4	1
explain format=tree SELECT rf, v, v <>ALL (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(otr.v) = itr.val)  (rows=0.1)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, v, v =ALL (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	v	b
1	0	0
1	1	0
1	2	0
1	3	0
1	4	0
2	0	0
2	1	0
2	2	0
2	3	0
2	4	0
3	0	1
3	1	1
3	2	1
3	3	1
3	4	1
explain format=tree SELECT rf, v, v =ALL (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(otr.v) <> itr.val)  (rows=0.9)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, v, v >ALL (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	v	b
1	0	0
1	1	0
1	2	0
1	3	0
1	4	1
2	0	0
2	1	0
2	2	0
2	3	0
2	4	1
3	0	1
3	1	1
3	2	1
3	3	1
3	4	1
explain format=tree SELECT rf, v, v >ALL (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(otr.v) <= itr.val)  (rows=0.333)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, v, v <>ANY (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	v	b
1	0	1
1	1	1
1	2	1
1	3	1
1	4	1
2	0	1
2	1	1
2	2	1
2	3	1
2	4	1
3	0	0
3	1	0
3	2	0
3	3	0
3	4	0
explain format=tree SELECT rf, v, v <>ANY (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(otr.v) <> itr.val)  (rows=0.9)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, v, v =ANY (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	v	b
1	0	0
1	1	1
1	2	1
1	3	1
1	4	0
2	0	0
2	1	1
2	2	1
2	3	1
2	4	0
3	0	0
3	1	0
3	2	0
3	3	0
3	4	0
explain format=tree SELECT rf, v, v =ANY (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(otr.v) = itr.val)  (rows=0.1)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, v, v >ANY (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	v	b
1	0	0
1	1	0
1	2	1
1	3	1
1	4	1
2	0	0
2	1	0
2	2	1
2	3	1
2	4	1
3	0	0
3	1	0
3	2	0
3	3	0
3	4	0
explain format=tree SELECT rf, v, v >ANY (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(otr.v) > itr.val)  (rows=0.333)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, vn, vn <>ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	vn	b
1	0	NULL
1	1	0
1	2	0
1	3	NULL
1	NULL	NULL
2	0	NULL
2	1	0
2	2	0
2	3	NULL
2	NULL	NULL
3	0	1
3	1	1
3	2	1
3	3	1
3	NULL	1
explain format=tree SELECT rf, vn, vn <>ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(otr.vn) = itr.valn) or (itr.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(itr.valn), true))  (rows=1)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, vn, vn =ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	vn	b
1	0	0
1	1	0
1	2	0
1	3	0
1	NULL	NULL
2	0	0
2	1	0
2	2	0
2	3	0
2	NULL	NULL
3	0	1
3	1	1
3	2	1
3	3	1
3	NULL	1
explain format=tree SELECT rf, vn, vn =ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(otr.vn) <> itr.valn) or (itr.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(itr.valn), true))  (rows=1)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, vn, vn >ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	vn	b
1	0	0
1	1	0
1	2	0
1	3	NULL
1	NULL	NULL
2	0	0
2	1	0
2	2	0
2	3	NULL
2	NULL	NULL
3	0	1
3	1	1
3	2	1
3	3	1
3	NULL	1
explain format=tree SELECT rf, vn, vn >ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(otr.vn) <= itr.valn) or (itr.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(itr.valn), true))  (rows=1)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, vn, vn <>ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	vn	b
1	0	1
1	1	1
1	2	1
1	3	1
1	NULL	NULL
2	0	1
2	1	1
2	2	1
2	3	1
2	NULL	NULL
3	0	0
3	1	0
3	2	0
3	3	0
3	NULL	0
explain format=tree SELECT rf, vn, vn <>ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(otr.vn) <> itr.valn) or (itr.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(itr.valn), true))  (rows=1)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, vn, vn =ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	vn	b
1	0	NULL
1	1	1
1	2	1
1	3	NULL
1	NULL	NULL
2	0	NULL
2	1	1
2	2	1
2	3	NULL
2	NULL	NULL
3	0	0
3	1	0
3	2	0
3	3	0
3	NULL	0
explain format=tree SELECT rf, vn, vn =ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(otr.vn) = itr.valn) or (itr.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(itr.valn), true))  (rows=1)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, vn, vn >ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	vn	b
1	0	NULL
1	1	NULL
1	2	1
1	3	1
1	NULL	NULL
2	0	NULL
2	1	NULL
2	2	1
2	3	1
2	NULL	NULL
3	0	0
3	1	0
3	2	0
3	3	0
3	NULL	0
explain format=tree SELECT rf, vn, vn >ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(otr.vn) > itr.valn) or (itr.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(itr.valn), true))  (rows=1)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
# Subquery with outer reference in WHERE clause
SELECT *
FROM otr
WHERE v <>ALL (SELECT val FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	0	0
1	4	NULL
2	0	0
2	4	NULL
3	0	0
3	1	1
3	2	2
3	3	3
3	4	NULL
explain format=tree SELECT *
FROM otr
WHERE v <>ALL (SELECT val FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Hash antijoin (otr.v = itr.val), (otr.rf = itr.rf)  (rows=14.4)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on itr  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE v =ALL (SELECT val FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
3	0	0
3	1	1
3	2	2
3	3	3
3	4	NULL
explain format=tree SELECT *
FROM otr
WHERE v =ALL (SELECT val FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: <not>(<in_optimizer>(otr.v,<exists>(select #2)))  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(otr.v) <> itr.val)  (rows=0.9)
            -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE v >ALL (SELECT val FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	4	NULL
2	4	NULL
3	0	0
3	1	1
3	2	2
3	3	3
3	4	NULL
explain format=tree SELECT *
FROM otr
WHERE v >ALL (SELECT val FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: <not>(<in_optimizer>(otr.v,<exists>(select #2)))  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(otr.v) <= itr.val)  (rows=0.333)
            -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE v <>ANY (SELECT val FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	0	0
1	1	1
1	2	2
1	3	3
1	4	NULL
2	0	0
2	1	1
2	2	2
2	3	3
2	4	NULL
explain format=tree SELECT *
FROM otr
WHERE v <>ANY (SELECT val FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(otr.v,<exists>(select #2)))  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(otr.v) <> itr.val)  (rows=0.9)
            -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE v =ANY (SELECT val FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	1	1
1	2	2
1	3	3
2	1	1
2	2	2
2	3	3
explain format=tree SELECT *
FROM otr
WHERE v =ANY (SELECT val FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Hash semijoin (FirstMatch) (otr.v = itr.val), (otr.rf = itr.rf)  (rows=0.612)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on itr  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE v >ANY (SELECT val FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	2	2
1	3	3
1	4	NULL
2	2	2
2	3	3
2	4	NULL
explain format=tree SELECT *
FROM otr
WHERE v >ANY (SELECT val FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(otr.v,<exists>(select #2)))  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(otr.v) > itr.val)  (rows=0.333)
            -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE vn <>ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
3	0	0
3	1	1
3	2	2
3	3	3
3	4	NULL
explain format=tree SELECT *
FROM otr
WHERE vn <>ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: <in_optimizer>(otr.vn,<exists>(select #2) is false)  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<if>(outer_field_is_not_null, ((<cache>(otr.vn) = itr.valn) or (itr.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(itr.valn), true))  (rows=1)
            -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE vn =ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
3	0	0
3	1	1
3	2	2
3	3	3
3	4	NULL
explain format=tree SELECT *
FROM otr
WHERE vn =ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: <not>(<in_optimizer>(otr.vn,<exists>(select #2)))  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<if>(outer_field_is_not_null, ((<cache>(otr.vn) <> itr.valn) or (itr.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(itr.valn), true))  (rows=1)
            -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE vn >ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
3	0	0
3	1	1
3	2	2
3	3	3
3	4	NULL
explain format=tree SELECT *
FROM otr
WHERE vn >ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: <not>(<in_optimizer>(otr.vn,<exists>(select #2)))  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<if>(outer_field_is_not_null, ((<cache>(otr.vn) <= itr.valn) or (itr.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(itr.valn), true))  (rows=1)
            -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE vn <>ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	0	0
1	1	1
1	2	2
1	3	3
2	0	0
2	1	1
2	2	2
2	3	3
explain format=tree SELECT *
FROM otr
WHERE vn <>ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(otr.vn,<exists>(select #2)))  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(otr.vn) <> itr.valn)  (rows=0.9)
            -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE vn =ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	1	1
1	2	2
2	1	1
2	2	2
explain format=tree SELECT *
FROM otr
WHERE vn =ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Hash semijoin (FirstMatch) (otr.vn = itr.valn), (otr.rf = itr.rf)  (rows=0.612)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on itr  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE vn >ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	2	2
1	3	3
2	2	2
2	3	3
explain format=tree SELECT *
FROM otr
WHERE vn >ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: <nop>(<in_optimizer>(otr.vn,<exists>(select #2)))  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<cache>(otr.vn) > itr.valn)  (rows=0.333)
            -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT i, i >ANY (SELECT i FROM t_null) FROM t_outer;
i	i >ANY (SELECT i FROM t_null)
1	NULL
1	NULL
2	1
explain format=tree SELECT i, i >ANY (SELECT i FROM t_null) FROM t_outer;
EXPLAIN
-> Table scan on t_outer  (rows=3)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(t_outer.i) > t_null.i) or (t_null.i is null)) and <is_not_null_test>(t_null.i))  (rows=0.16)
        -> Table scan on t_null  (rows=4)

SELECT NULL >ANY (SELECT i FROM t_empty) AS result FROM t_null;
result
0
0
0
0
explain format=tree SELECT NULL >ANY (SELECT i FROM t_empty) AS result FROM t_null;
EXPLAIN
-> Table scan on t_null  (rows=4)
-> Select #2 (subquery in projection; run only once)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(NULL) > t_empty.i) or (t_empty.i is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(t_empty.i), true))  (rows=1)
        -> Table scan on t_empty  (rows=1)

SELECT i <>ANY (SELECT NULL FROM t_null) FROM t_null;
i <>ANY (SELECT NULL FROM t_null)
NULL
NULL
NULL
NULL
explain format=tree SELECT i <>ANY (SELECT NULL FROM t_null) FROM t_null;
EXPLAIN
-> Table scan on t_null  (rows=4)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(t_null.i) <> NULL) or <cache>((NULL is null))), true) and <if>(outer_field_is_not_null, <is_not_null_test>(NULL), true))  (rows=4)
        -> Table scan on t_null  (rows=4)


Bug#37529060: WL#13052: !Item->hidden in Aggregator_distinct::setup

CREATE TABLE t1 (
pk int NOT NULL,
cv varchar(1) DEFAULT NULL,
cv_key varchar(1) DEFAULT NULL,
PRIMARY KEY (pk)
);
SELECT alias2.pk AS field1,
alias1.pk AS field2
FROM t1 AS alias1 INNER JOIN t1 AS alias2 ON alias2.cv = alias1.cv_key
WHERE alias2.cv <> ANY
(SELECT alias3.cv_key AS field3
FROM t1 AS alias3
WHERE alias3.cv_key >
(SELECT MAX(alias4.cv) AS field4
FROM t1 AS alias4
)
);
field1	field2
explain format=tree SELECT alias2.pk AS field1,
alias1.pk AS field2
FROM t1 AS alias1 INNER JOIN t1 AS alias2 ON alias2.cv = alias1.cv_key
WHERE alias2.cv <> ANY
(SELECT alias3.cv_key AS field3
FROM t1 AS alias3
WHERE alias3.cv_key >
(SELECT MAX(alias4.cv) AS field4
FROM t1 AS alias4
)
);
EXPLAIN
-> Inner hash join (alias2.cv = alias1.cv_key)  (rows=0.1)
    -> Table scan on alias1  (rows=1)
    -> Hash
        -> Filter: <nop>(<in_optimizer>(alias2.cv,<exists>(select #2)))  (rows=1)
            -> Table scan on alias2  (rows=1)
            -> Select #2 (subquery in condition; dependent)
                -> Filter: ((<cache>(alias2.cv) <> alias3.cv_key) and (alias3.cv_key > (select #3)))  (rows=0.3)
                    -> Table scan on alias3  (rows=1)
                    -> Select #3 (subquery in condition; run only once)
                        -> Aggregate: max(alias4.cv)  (rows=1)
                            -> Table scan on alias4  (rows=1)

DROP TABLE t1;
set optimizer_switch='semijoin=off,subquery_to_derived=on';

# -------------------------------------------------------------------#
# Test quantified comparison predicates with subquery_to_derived ON  #
# -------------------------------------------------------------------#


# quantified comparison predicate in SELECT list
# non-nullable columns
# no dependent subquery predicate

SELECT v <>ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT v <>ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (ot.v = derived_1_2.Name_exp_1)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.val  (rows=1.73)
                    -> Table scan on it  (rows=3)

SELECT NOT EXISTS (SELECT * FROM it WHERE ot.v = it.val) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT NOT EXISTS (SELECT * FROM it WHERE ot.v = it.val) AS b
FROM ot;
EXPLAIN
-> Left hash join (ot.v = derived_1_2.Name_exp_2)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.val  (rows=1.73)
                    -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT v >ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v >ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v >=ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v <ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v <ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v <=ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT v <=ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v =ANY (SELECT val FROM it) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT v =ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (ot.v = derived_1_2.Name_exp_1)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.val  (rows=1.73)
                    -> Table scan on it  (rows=3)

SELECT EXISTS (SELECT * FROM it WHERE ot.v = it.val) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT EXISTS (SELECT * FROM it WHERE ot.v = it.val) AS b
FROM ot;
EXPLAIN
-> Left hash join (ot.v = derived_1_2.Name_exp_2)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.val  (rows=1.73)
                    -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT v >ANY (SELECT val FROM it) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT v >ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v >=ANY (SELECT val FROM it) AS b
FROM ot;
b
0
1
1
1
1
explain format=tree SELECT v >=ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v <ANY (SELECT val FROM it) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT v <ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v <=ANY (SELECT val FROM it) AS b
FROM ot;
b
0
1
1
1
1
explain format=tree SELECT v <=ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v =ALL (SELECT v FROM t0row) AS b
FROM ot;
b
1
1
1
1
1
explain format=tree SELECT v =ALL (SELECT v FROM t0row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t0row.v), count(0), count(distinct t0row.v)  (rows=1)
                    -> Table scan on t0row  (rows=1)

SELECT v =ALL (SELECT v FROM t1row) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v =ALL (SELECT v FROM t1row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t1row.v), count(0), count(distinct t1row.v)  (rows=1)
                    -> Table scan on t1row  (rows=1)

SELECT v =ALL (SELECT v FROM t2row) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v =ALL (SELECT v FROM t2row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t2row.v), count(0), count(distinct t2row.v)  (rows=1)
                    -> Table scan on t2row  (rows=2)

SELECT v =ALL (SELECT v FROM t4row) AS b
FROM ot;
b
0
0
0
0
0
explain format=tree SELECT v =ALL (SELECT v FROM t4row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t4row.v), count(0), count(distinct t4row.v)  (rows=1)
                    -> Table scan on t4row  (rows=4)

SELECT v <>ANY (SELECT v FROM t0row) AS b
FROM ot;
b
0
0
0
0
0
explain format=tree SELECT v <>ANY (SELECT v FROM t0row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t0row.v), count(0), count(distinct t0row.v)  (rows=1)
                    -> Table scan on t0row  (rows=1)

SELECT v <>ANY (SELECT v FROM t1row) AS b
FROM ot;
b
0
1
1
1
1
explain format=tree SELECT v <>ANY (SELECT v FROM t1row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t1row.v), count(0), count(distinct t1row.v)  (rows=1)
                    -> Table scan on t1row  (rows=1)

SELECT v <>ANY (SELECT v FROM t2row) AS b
FROM ot;
b
0
1
1
1
1
explain format=tree SELECT v <>ANY (SELECT v FROM t2row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t2row.v), count(0), count(distinct t2row.v)  (rows=1)
                    -> Table scan on t2row  (rows=2)

SELECT v <>ANY (SELECT v FROM t4row) AS b
FROM ot;
b
1
1
1
1
1
explain format=tree SELECT v <>ANY (SELECT v FROM t4row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t4row.v), count(0), count(distinct t4row.v)  (rows=1)
                    -> Table scan on t4row  (rows=4)


# quantified comparison predicate in SELECT list
# nullable outer column
# no dependent subquery predicate

SELECT vn <>ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
NULL
explain format=tree SELECT vn <>ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) = it.val), true)  (rows=3)
        -> Table scan on it  (rows=3)

SELECT NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.val) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.val) AS b
FROM ot;
EXPLAIN
-> Left hash join (ot.vn = derived_1_2.Name_exp_2)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.val  (rows=1.73)
                    -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT vn >ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
0
NULL
explain format=tree SELECT vn >ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn >=ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
NULL
explain format=tree SELECT vn >=ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn <ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
NULL
explain format=tree SELECT vn <ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn <=ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
1
1
NULL
explain format=tree SELECT vn <=ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn =ANY (SELECT val FROM it) AS b
FROM ot;
b
0
1
1
1
NULL
explain format=tree SELECT vn =ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: <if>(outer_field_is_not_null, (<cache>(ot.vn) = it.val), true)  (rows=3)
        -> Table scan on it  (rows=3)

SELECT EXISTS (SELECT * FROM it WHERE ot.vn = it.val) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT EXISTS (SELECT * FROM it WHERE ot.vn = it.val) AS b
FROM ot;
EXPLAIN
-> Left hash join (ot.vn = derived_1_2.Name_exp_2)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.val  (rows=1.73)
                    -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT vn >ANY (SELECT val FROM it) AS b
FROM ot;
b
0
0
1
1
NULL
explain format=tree SELECT vn >ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn >=ANY (SELECT val FROM it) AS b
FROM ot;
b
0
1
1
1
NULL
explain format=tree SELECT vn >=ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn <ANY (SELECT val FROM it) AS b
FROM ot;
b
0
1
1
1
NULL
explain format=tree SELECT vn <ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn <=ANY (SELECT val FROM it) AS b
FROM ot;
b
1
1
1
1
NULL
explain format=tree SELECT vn <=ANY (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn =ALL (SELECT v FROM t0row) AS b
FROM ot;
b
1
1
1
1
1
explain format=tree SELECT vn =ALL (SELECT v FROM t0row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t0row.v), count(0), count(distinct t0row.v)  (rows=1)
                    -> Table scan on t0row  (rows=1)

SELECT vn =ALL (SELECT v FROM t1row) AS b
FROM ot;
b
0
0
0
1
NULL
explain format=tree SELECT vn =ALL (SELECT v FROM t1row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t1row.v), count(0), count(distinct t1row.v)  (rows=1)
                    -> Table scan on t1row  (rows=1)

SELECT vn =ALL (SELECT v FROM t2row) AS b
FROM ot;
b
0
0
0
1
NULL
explain format=tree SELECT vn =ALL (SELECT v FROM t2row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t2row.v), count(0), count(distinct t2row.v)  (rows=1)
                    -> Table scan on t2row  (rows=2)

SELECT vn =ALL (SELECT v FROM t4row) AS b
FROM ot;
b
0
0
0
0
NULL
explain format=tree SELECT vn =ALL (SELECT v FROM t4row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t4row.v), count(0), count(distinct t4row.v)  (rows=1)
                    -> Table scan on t4row  (rows=4)

SELECT vn <>ANY (SELECT v FROM t0row) AS b
FROM ot;
b
0
0
0
0
0
explain format=tree SELECT vn <>ANY (SELECT v FROM t0row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t0row.v), count(0), count(distinct t0row.v)  (rows=1)
                    -> Table scan on t0row  (rows=1)

SELECT vn <>ANY (SELECT v FROM t1row) AS b
FROM ot;
b
0
1
1
1
NULL
explain format=tree SELECT vn <>ANY (SELECT v FROM t1row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t1row.v), count(0), count(distinct t1row.v)  (rows=1)
                    -> Table scan on t1row  (rows=1)

SELECT vn <>ANY (SELECT v FROM t2row) AS b
FROM ot;
b
0
1
1
1
NULL
explain format=tree SELECT vn <>ANY (SELECT v FROM t2row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t2row.v), count(0), count(distinct t2row.v)  (rows=1)
                    -> Table scan on t2row  (rows=2)

SELECT vn <>ANY (SELECT v FROM t4row) AS b
FROM ot;
b
1
1
1
1
NULL
explain format=tree SELECT vn <>ANY (SELECT v FROM t4row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t4row.v), count(0), count(distinct t4row.v)  (rows=1)
                    -> Table scan on t4row  (rows=4)


# quantified comparison predicate in SELECT list
# nullable column in subquery
# no dependent subquery predicate

SELECT v <>ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
NULL
NULL
NULL
explain format=tree SELECT v <>ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) = it.valn) or (it.valn is null)) and <is_not_null_test>(it.valn))  (rows=0.057)
        -> Table scan on it  (rows=3)

SELECT NOT EXISTS (SELECT * FROM it WHERE ot.v = it.valn) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT NOT EXISTS (SELECT * FROM it WHERE ot.v = it.valn) AS b
FROM ot;
EXPLAIN
-> Left hash join (ot.v = derived_1_2.Name_exp_2)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.valn  (rows=1.73)
                    -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT v >ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT v >ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v >=ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
NULL
NULL
NULL
explain format=tree SELECT v >=ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v <ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
0
0
NULL
explain format=tree SELECT v <ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v <=ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT v <=ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v =ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
NULL
NULL
NULL
explain format=tree SELECT v =ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (((<cache>(ot.v) = it.valn) or (it.valn is null)) and <is_not_null_test>(it.valn))  (rows=0.057)
        -> Table scan on it  (rows=3)

SELECT EXISTS (SELECT * FROM it WHERE ot.v = it.valn) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT EXISTS (SELECT * FROM it WHERE ot.v = it.valn) AS b
FROM ot;
EXPLAIN
-> Left hash join (ot.v = derived_1_2.Name_exp_2)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.valn  (rows=1.73)
                    -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT v >ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
1
NULL
NULL
explain format=tree SELECT v >ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v >=ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
1
1
NULL
explain format=tree SELECT v >=ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v <ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
NULL
NULL
NULL
explain format=tree SELECT v <ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v <=ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
1
NULL
NULL
explain format=tree SELECT v <=ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v =ALL (SELECT n FROM t1row) AS b
FROM ot;
b
NULL
NULL
NULL
NULL
NULL
explain format=tree SELECT v =ALL (SELECT n FROM t1row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t1row.n), count(0), max((t1row.n is null)), count(distinct t1row.n)  (rows=1)
                    -> Table scan on t1row  (rows=1)

SELECT v =ALL (SELECT vn FROM t2row) AS b
FROM ot;
b
0
0
0
0
NULL
explain format=tree SELECT v =ALL (SELECT vn FROM t2row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t2row.vn), count(0), max((t2row.vn is null)), count(distinct t2row.vn)  (rows=1)
                    -> Table scan on t2row  (rows=2)

SELECT v =ALL (SELECT vn FROM t3row) AS b
FROM ot;
b
0
0
0
0
NULL
explain format=tree SELECT v =ALL (SELECT vn FROM t3row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t3row.vn), count(0), max((t3row.vn is null)), count(distinct t3row.vn)  (rows=1)
                    -> Table scan on t3row  (rows=3)

SELECT v =ALL (SELECT vn FROM t4row) AS b
FROM ot;
b
0
0
0
0
0
explain format=tree SELECT v =ALL (SELECT vn FROM t4row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t4row.vn), count(0), max((t4row.vn is null)), count(distinct t4row.vn)  (rows=1)
                    -> Table scan on t4row  (rows=4)

SELECT v <>ANY (SELECT n FROM t1row) AS b
FROM ot;
b
NULL
NULL
NULL
NULL
NULL
explain format=tree SELECT v <>ANY (SELECT n FROM t1row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t1row.n), count(0), max((t1row.n is null)), count(distinct t1row.n)  (rows=1)
                    -> Table scan on t1row  (rows=1)

SELECT v <>ANY (SELECT vn FROM t2row) AS b
FROM ot;
b
1
1
1
1
NULL
explain format=tree SELECT v <>ANY (SELECT vn FROM t2row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t2row.vn), count(0), max((t2row.vn is null)), count(distinct t2row.vn)  (rows=1)
                    -> Table scan on t2row  (rows=2)

SELECT v <>ANY (SELECT vn FROM t3row) AS b
FROM ot;
b
1
1
1
1
NULL
explain format=tree SELECT v <>ANY (SELECT vn FROM t3row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t3row.vn), count(0), max((t3row.vn is null)), count(distinct t3row.vn)  (rows=1)
                    -> Table scan on t3row  (rows=3)

SELECT v <>ANY (SELECT vn FROM t4row) AS b
FROM ot;
b
1
1
1
1
1
explain format=tree SELECT v <>ANY (SELECT vn FROM t4row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t4row.vn), count(0), max((t4row.vn is null)), count(distinct t4row.vn)  (rows=1)
                    -> Table scan on t4row  (rows=4)


# quantified comparison predicate in SELECT list
# nullable columns
# no dependent subquery predicate

SELECT vn <>ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
NULL
NULL
NULL
explain format=tree SELECT vn <>ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) = it.valn) or (it.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(it.valn), true))  (rows=3)
        -> Table scan on it  (rows=3)

SELECT NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.valn) AS b
FROM ot;
b
0
0
1
1
1
explain format=tree SELECT NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.valn) AS b
FROM ot;
EXPLAIN
-> Left hash join (ot.vn = derived_1_2.Name_exp_2)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.valn  (rows=1.73)
                    -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT vn >ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT vn >ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn >=ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
NULL
NULL
NULL
explain format=tree SELECT vn >=ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn <ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT vn <ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn <=ALL (SELECT valn FROM it) AS b
FROM ot;
b
0
0
NULL
NULL
NULL
explain format=tree SELECT vn <=ALL (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn =ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
NULL
NULL
NULL
explain format=tree SELECT vn =ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(ot.vn) = it.valn) or (it.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(it.valn), true))  (rows=3)
        -> Table scan on it  (rows=3)

SELECT EXISTS (SELECT * FROM it WHERE ot.vn = it.valn) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT EXISTS (SELECT * FROM it WHERE ot.vn = it.valn) AS b
FROM ot;
EXPLAIN
-> Left hash join (ot.vn = derived_1_2.Name_exp_2)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.valn  (rows=1.73)
                    -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT vn >ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
NULL
NULL
NULL
explain format=tree SELECT vn >ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn >=ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
1
NULL
NULL
explain format=tree SELECT vn >=ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn <ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
NULL
NULL
NULL
explain format=tree SELECT vn <ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn <=ANY (SELECT valn FROM it) AS b
FROM ot;
b
1
1
1
NULL
NULL
explain format=tree SELECT vn <=ANY (SELECT valn FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.valn), count(0), max((it.valn is null))  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT vn =ALL (SELECT n FROM t1row) AS b
FROM ot;
b
NULL
NULL
NULL
NULL
NULL
explain format=tree SELECT vn =ALL (SELECT n FROM t1row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t1row.n), count(0), max((t1row.n is null)), count(distinct t1row.n)  (rows=1)
                    -> Table scan on t1row  (rows=1)

SELECT vn =ALL (SELECT vn FROM t2row) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT vn =ALL (SELECT vn FROM t2row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t2row.vn), count(0), max((t2row.vn is null)), count(distinct t2row.vn)  (rows=1)
                    -> Table scan on t2row  (rows=2)

SELECT vn =ALL (SELECT vn FROM t3row) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT vn =ALL (SELECT vn FROM t3row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t3row.vn), count(0), max((t3row.vn is null)), count(distinct t3row.vn)  (rows=1)
                    -> Table scan on t3row  (rows=3)

SELECT vn =ALL (SELECT vn FROM t4row) AS b
FROM ot;
b
0
0
0
0
NULL
explain format=tree SELECT vn =ALL (SELECT vn FROM t4row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(t4row.vn), count(0), max((t4row.vn is null)), count(distinct t4row.vn)  (rows=1)
                    -> Table scan on t4row  (rows=4)

SELECT vn <>ANY (SELECT n FROM t1row) AS b
FROM ot;
b
NULL
NULL
NULL
NULL
NULL
explain format=tree SELECT vn <>ANY (SELECT n FROM t1row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t1row.n), count(0), max((t1row.n is null)), count(distinct t1row.n)  (rows=1)
                    -> Table scan on t1row  (rows=1)

SELECT vn <>ANY (SELECT vn FROM t2row) AS b
FROM ot;
b
1
1
1
NULL
NULL
explain format=tree SELECT vn <>ANY (SELECT vn FROM t2row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t2row.vn), count(0), max((t2row.vn is null)), count(distinct t2row.vn)  (rows=1)
                    -> Table scan on t2row  (rows=2)

SELECT vn <>ANY (SELECT vn FROM t3row) AS b
FROM ot;
b
1
1
1
NULL
NULL
explain format=tree SELECT vn <>ANY (SELECT vn FROM t3row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t3row.vn), count(0), max((t3row.vn is null)), count(distinct t3row.vn)  (rows=1)
                    -> Table scan on t3row  (rows=3)

SELECT vn <>ANY (SELECT vn FROM t4row) AS b
FROM ot;
b
1
1
1
1
NULL
explain format=tree SELECT vn <>ANY (SELECT vn FROM t4row) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t4row.vn), count(0), max((t4row.vn is null)), count(distinct t4row.vn)  (rows=1)
                    -> Table scan on t4row  (rows=4)


# quantified comparison predicate in WHERE clause
# non-nullable columns
# no dependent subquery predicate

SELECT *
FROM ot
WHERE v <>ALL (SELECT val FROM it);
v	vn
0	0
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ALL (SELECT val FROM it);
EXPLAIN
-> Filter: (derived_1_2.Name_exp_1 is null)  (rows=0.5)
    -> Left hash join (ot.v = derived_1_2.Name_exp_1)  (rows=5)
        -> Table scan on ot  (rows=5)
        -> Hash
            -> Table scan on derived_1_2  (rows=1.73)
                -> Materialize  (rows=1.73)
                    -> Sort with duplicate removal: it.val  (rows=1.73)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.v = it.val);
v	vn
0	0
4	NULL
explain format=tree SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.v = it.val);
EXPLAIN
-> Filter: (derived_1_2.Name_exp_1 is null)  (rows=0.5)
    -> Left hash join (ot.v = derived_1_2.Name_exp_2)  (rows=5)
        -> Table scan on ot  (rows=5)
        -> Hash
            -> Table scan on derived_1_2  (rows=1.73)
                -> Materialize  (rows=1.73)
                    -> Sort with duplicate removal: it.val  (rows=1.73)
                        -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it);
v	vn
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v > derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >=ALL (SELECT val FROM it);
v	vn
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >=ALL (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v >= derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <ALL (SELECT val FROM it);
v	vn
0	0
explain format=tree SELECT *
FROM ot
WHERE v <ALL (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v < derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <=ALL (SELECT val FROM it);
v	vn
0	0
1	1
explain format=tree SELECT *
FROM ot
WHERE v <=ALL (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v <= derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v =ANY (SELECT val FROM it);
v	vn
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE v =ANY (SELECT val FROM it);
EXPLAIN
-> Inner hash join (ot.v = derived_1_2.Name_exp_1)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.val  (rows=1.73)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.v = it.val);
v	vn
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.v = it.val);
EXPLAIN
-> Inner hash join (ot.v = derived_1_2.Name_exp_2)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.val  (rows=1.73)
                    -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE v >ANY (SELECT val FROM it);
v	vn
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ANY (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.v > derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(it.val), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >=ANY (SELECT val FROM it);
v	vn
1	1
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >=ANY (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.v >= derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(it.val), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <ANY (SELECT val FROM it);
v	vn
0	0
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE v <ANY (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.v < derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: max(it.val), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <=ANY (SELECT val FROM it);
v	vn
0	0
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE v <=ANY (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.v <= derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: max(it.val), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t0row);
v	vn
0	0
1	1
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t0row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or ((derived_1_2.Name_exp_3 = 1) and (ot.v = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (derived_1_2.Name_exp_3 = 1))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t0row.v), count(0), count(distinct t0row.v)  (rows=1)
                        -> Table scan on t0row  (rows=1)

SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t1row);
v	vn
2	2
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t1row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or ((derived_1_2.Name_exp_3 = 1) and (ot.v = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (derived_1_2.Name_exp_3 = 1))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t1row.v), count(0), count(distinct t1row.v)  (rows=1)
                        -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t2row);
v	vn
2	2
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t2row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or ((derived_1_2.Name_exp_3 = 1) and (ot.v = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (derived_1_2.Name_exp_3 = 1))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t2row.v), count(0), count(distinct t2row.v)  (rows=1)
                        -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t4row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT v FROM t4row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or ((derived_1_2.Name_exp_3 = 1) and (ot.v = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (derived_1_2.Name_exp_3 = 1))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t4row.v), count(0), count(distinct t4row.v)  (rows=1)
                        -> Table scan on t4row  (rows=4)

SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t0row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t0row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t0row.v), count(0), count(distinct t0row.v)  (rows=1)
                        -> Table scan on t0row  (rows=1)

SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t1row);
v	vn
0	0
1	1
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t1row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t1row.v), count(0), count(distinct t1row.v)  (rows=1)
                        -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t2row);
v	vn
0	0
1	1
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t2row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t2row.v), count(0), count(distinct t2row.v)  (rows=1)
                        -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t4row);
v	vn
0	0
1	1
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT v FROM t4row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t4row.v), count(0), count(distinct t4row.v)  (rows=1)
                        -> Table scan on t4row  (rows=4)


# quantified comparison predicate in WHERE clause
# nullable outer column
# no dependent subquery predicate

SELECT *
FROM ot
WHERE vn <>ALL (SELECT val FROM it);
v	vn
0	0
explain format=tree SELECT *
FROM ot
WHERE vn <>ALL (SELECT val FROM it);
EXPLAIN
-> Filter: <in_optimizer>(ot.vn,ot.vn in (select #2) is false)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Filter: ((ot.vn = `<materialized_subquery>`.val))  (rows=1)
            -> Limit: 1 row(s)  (rows=1)
                -> Index lookup on <materialized_subquery> using <auto_distinct_key> (val = ot.vn)
                    -> Materialize with deduplication  (rows=3)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.val);
v	vn
0	0
4	NULL
explain format=tree SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.val);
EXPLAIN
-> Filter: (derived_1_2.Name_exp_1 is null)  (rows=0.5)
    -> Left hash join (ot.vn = derived_1_2.Name_exp_2)  (rows=5)
        -> Table scan on ot  (rows=5)
        -> Hash
            -> Table scan on derived_1_2  (rows=1.73)
                -> Materialize  (rows=1.73)
                    -> Sort with duplicate removal: it.val  (rows=1.73)
                        -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE vn >ALL (SELECT val FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn >ALL (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.vn > derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn >=ALL (SELECT val FROM it);
v	vn
3	3
explain format=tree SELECT *
FROM ot
WHERE vn >=ALL (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.vn >= derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <ALL (SELECT val FROM it);
v	vn
0	0
explain format=tree SELECT *
FROM ot
WHERE vn <ALL (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.vn < derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <=ALL (SELECT val FROM it);
v	vn
0	0
1	1
explain format=tree SELECT *
FROM ot
WHERE vn <=ALL (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.vn <= derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn =ANY (SELECT val FROM it);
v	vn
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn =ANY (SELECT val FROM it);
EXPLAIN
-> Inner hash join (ot.vn = derived_1_2.Name_exp_1)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.val  (rows=1.73)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.vn = it.val);
v	vn
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.vn = it.val);
EXPLAIN
-> Inner hash join (ot.vn = derived_1_2.Name_exp_2)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.val  (rows=1.73)
                    -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE vn >ANY (SELECT val FROM it);
v	vn
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn >ANY (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.vn > derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(it.val), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn >=ANY (SELECT val FROM it);
v	vn
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn >=ANY (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.vn >= derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(it.val), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <ANY (SELECT val FROM it);
v	vn
0	0
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE vn <ANY (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.vn < derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: max(it.val), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <=ANY (SELECT val FROM it);
v	vn
0	0
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <=ANY (SELECT val FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.vn <= derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: max(it.val), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t0row);
v	vn
0	0
1	1
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t0row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or ((derived_1_2.Name_exp_3 = 1) and (ot.vn = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (derived_1_2.Name_exp_3 = 1))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t0row.v), count(0), count(distinct t0row.v)  (rows=1)
                        -> Table scan on t0row  (rows=1)

SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t1row);
v	vn
2	2
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t1row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or ((derived_1_2.Name_exp_3 = 1) and (ot.vn = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (derived_1_2.Name_exp_3 = 1))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t1row.v), count(0), count(distinct t1row.v)  (rows=1)
                        -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t2row);
v	vn
2	2
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t2row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or ((derived_1_2.Name_exp_3 = 1) and (ot.vn = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (derived_1_2.Name_exp_3 = 1))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t2row.v), count(0), count(distinct t2row.v)  (rows=1)
                        -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t4row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT v FROM t4row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or ((derived_1_2.Name_exp_3 = 1) and (ot.vn = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (derived_1_2.Name_exp_3 = 1))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t4row.v), count(0), count(distinct t4row.v)  (rows=1)
                        -> Table scan on t4row  (rows=4)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t0row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t0row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.vn <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=4.5)
    -> Filter: (ot.vn is not null)  (rows=4.5)
        -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t0row.v), count(0), count(distinct t0row.v)  (rows=1)
                        -> Table scan on t0row  (rows=1)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t1row);
v	vn
0	0
1	1
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t1row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.vn <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=4.5)
    -> Filter: (ot.vn is not null)  (rows=4.5)
        -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t1row.v), count(0), count(distinct t1row.v)  (rows=1)
                        -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t2row);
v	vn
0	0
1	1
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t2row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.vn <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=4.5)
    -> Filter: (ot.vn is not null)  (rows=4.5)
        -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t2row.v), count(0), count(distinct t2row.v)  (rows=1)
                        -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t4row);
v	vn
0	0
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT v FROM t4row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.vn <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=4.5)
    -> Filter: (ot.vn is not null)  (rows=4.5)
        -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t4row.v), count(0), count(distinct t4row.v)  (rows=1)
                        -> Table scan on t4row  (rows=4)


# quantified comparison predicate in WHERE clause
# nullable column in subquery
# no dependent subquery predicate

SELECT *
FROM ot
WHERE v <>ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v <>ALL (SELECT valn FROM it);
EXPLAIN
-> Filter: <in_optimizer>(ot.v,ot.v in (select #2) is false)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Filter: ((ot.v = `<materialized_subquery>`.valn))  (rows=1)
            -> Limit: 1 row(s)  (rows=1)
                -> Index lookup on <materialized_subquery> using <auto_distinct_key> (valn = ot.v)
                    -> Materialize with deduplication  (rows=3)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.v = it.valn);
v	vn
0	0
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.v = it.valn);
EXPLAIN
-> Filter: (derived_1_2.Name_exp_1 is null)  (rows=0.5)
    -> Left hash join (ot.v = derived_1_2.Name_exp_2)  (rows=5)
        -> Table scan on ot  (rows=5)
        -> Hash
            -> Table scan on derived_1_2  (rows=1.73)
                -> Materialize  (rows=1.73)
                    -> Sort with duplicate removal: it.valn  (rows=1.73)
                        -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE v >ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (((ot.v > derived_1_2.Name_exp_1) and if((derived_1_2.Name_exp_3 = 0),true,false)) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: (if((derived_1_2.Name_exp_3 = 0),true,false) or (derived_1_2.Name_exp_2 = 0))  (rows=1)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(it.valn), count(0), max((it.valn is null))  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >=ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v >=ALL (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (((ot.v >= derived_1_2.Name_exp_1) and if((derived_1_2.Name_exp_3 = 0),true,false)) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: (if((derived_1_2.Name_exp_3 = 0),true,false) or (derived_1_2.Name_exp_2 = 0))  (rows=1)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(it.valn), count(0), max((it.valn is null))  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v <ALL (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (((ot.v < derived_1_2.Name_exp_1) and if((derived_1_2.Name_exp_3 = 0),true,false)) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: (if((derived_1_2.Name_exp_3 = 0),true,false) or (derived_1_2.Name_exp_2 = 0))  (rows=1)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: min(it.valn), count(0), max((it.valn is null))  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <=ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v <=ALL (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (((ot.v <= derived_1_2.Name_exp_1) and if((derived_1_2.Name_exp_3 = 0),true,false)) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: (if((derived_1_2.Name_exp_3 = 0),true,false) or (derived_1_2.Name_exp_2 = 0))  (rows=1)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: min(it.valn), count(0), max((it.valn is null))  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v =ANY (SELECT valn FROM it);
v	vn
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE v =ANY (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (ot.v = derived_1_2.Name_exp_1)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.valn  (rows=1.73)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.v = it.valn);
v	vn
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.v = it.valn);
EXPLAIN
-> Inner hash join (ot.v = derived_1_2.Name_exp_2)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.valn  (rows=1.73)
                    -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.v' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE v >ANY (SELECT valn FROM it);
v	vn
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ANY (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.v > derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(it.valn), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >=ANY (SELECT valn FROM it);
v	vn
1	1
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >=ANY (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.v >= derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(it.valn), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <ANY (SELECT valn FROM it);
v	vn
0	0
1	1
explain format=tree SELECT *
FROM ot
WHERE v <ANY (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.v < derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: max(it.valn), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v <=ANY (SELECT valn FROM it);
v	vn
0	0
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE v <=ANY (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.v <= derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: max(it.valn), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v =ALL (SELECT n FROM t1row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT n FROM t1row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1) and (ot.v = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1)))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t1row.n), count(0), max((t1row.n is null)), count(distinct t1row.n)  (rows=1)
                        -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE v =ALL (SELECT vn FROM t2row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT vn FROM t2row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1) and (ot.v = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1)))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t2row.vn), count(0), max((t2row.vn is null)), count(distinct t2row.vn)  (rows=1)
                        -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE v =ALL (SELECT vn FROM t3row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT vn FROM t3row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1) and (ot.v = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1)))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t3row.vn), count(0), max((t3row.vn is null)), count(distinct t3row.vn)  (rows=1)
                        -> Table scan on t3row  (rows=3)

SELECT *
FROM ot
WHERE v =ALL (SELECT vn FROM t4row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v =ALL (SELECT vn FROM t4row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1) and (ot.v = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1)))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t4row.vn), count(0), max((t4row.vn is null)), count(distinct t4row.vn)  (rows=1)
                        -> Table scan on t4row  (rows=4)

SELECT *
FROM ot
WHERE v <>ANY (SELECT n FROM t1row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT n FROM t1row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t1row.n), count(0), count(distinct t1row.n)  (rows=1)
                        -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE v <>ANY (SELECT vn FROM t2row);
v	vn
0	0
1	1
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT vn FROM t2row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t2row.vn), count(0), count(distinct t2row.vn)  (rows=1)
                        -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE v <>ANY (SELECT vn FROM t3row);
v	vn
0	0
1	1
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT vn FROM t3row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t3row.vn), count(0), count(distinct t3row.vn)  (rows=1)
                        -> Table scan on t3row  (rows=3)

SELECT *
FROM ot
WHERE v <>ANY (SELECT vn FROM t4row);
v	vn
0	0
1	1
2	2
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v <>ANY (SELECT vn FROM t4row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t4row.vn), count(0), count(distinct t4row.vn)  (rows=1)
                        -> Table scan on t4row  (rows=4)


# quantified comparison predicate in WHERE clause
# nullable columns
# no dependent subquery predicate

SELECT *
FROM ot
WHERE vn <>ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn <>ALL (SELECT valn FROM it);
EXPLAIN
-> Filter: <in_optimizer>(ot.vn,ot.vn in (select #2) is false)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Filter: ((ot.vn = `<materialized_subquery>`.valn))  (rows=1)
            -> Limit: 1 row(s)  (rows=1)
                -> Index lookup on <materialized_subquery> using <auto_distinct_key> (valn = ot.vn)
                    -> Materialize with deduplication  (rows=3)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.valn);
v	vn
0	0
3	3
4	NULL
explain format=tree SELECT *
FROM ot
WHERE NOT EXISTS (SELECT * FROM it WHERE ot.vn = it.valn);
EXPLAIN
-> Filter: (derived_1_2.Name_exp_1 is null)  (rows=0.5)
    -> Left hash join (ot.vn = derived_1_2.Name_exp_2)  (rows=5)
        -> Table scan on ot  (rows=5)
        -> Hash
            -> Table scan on derived_1_2  (rows=1.73)
                -> Materialize  (rows=1.73)
                    -> Sort with duplicate removal: it.valn  (rows=1.73)
                        -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE vn >ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn >ALL (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (((ot.vn > derived_1_2.Name_exp_1) and if((derived_1_2.Name_exp_3 = 0),true,false)) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: (if((derived_1_2.Name_exp_3 = 0),true,false) or (derived_1_2.Name_exp_2 = 0))  (rows=1)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(it.valn), count(0), max((it.valn is null))  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn >=ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn >=ALL (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (((ot.vn >= derived_1_2.Name_exp_1) and if((derived_1_2.Name_exp_3 = 0),true,false)) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: (if((derived_1_2.Name_exp_3 = 0),true,false) or (derived_1_2.Name_exp_2 = 0))  (rows=1)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(it.valn), count(0), max((it.valn is null))  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn <ALL (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (((ot.vn < derived_1_2.Name_exp_1) and if((derived_1_2.Name_exp_3 = 0),true,false)) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: (if((derived_1_2.Name_exp_3 = 0),true,false) or (derived_1_2.Name_exp_2 = 0))  (rows=1)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: min(it.valn), count(0), max((it.valn is null))  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <=ALL (SELECT valn FROM it);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn <=ALL (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (((ot.vn <= derived_1_2.Name_exp_1) and if((derived_1_2.Name_exp_3 = 0),true,false)) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: (if((derived_1_2.Name_exp_3 = 0),true,false) or (derived_1_2.Name_exp_2 = 0))  (rows=1)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: min(it.valn), count(0), max((it.valn is null))  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn =ANY (SELECT valn FROM it);
v	vn
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE vn =ANY (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (ot.vn = derived_1_2.Name_exp_1)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.valn  (rows=1.73)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.vn = it.valn);
v	vn
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE EXISTS (SELECT * FROM it WHERE ot.vn = it.valn);
EXPLAIN
-> Inner hash join (ot.vn = derived_1_2.Name_exp_2)  (rows=0.866)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1.73)
            -> Materialize  (rows=1.73)
                -> Sort with duplicate removal: it.valn  (rows=1.73)
                    -> Table scan on it  (rows=3)

Warnings:
Note	1276	Field or reference 'test.ot.vn' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM ot
WHERE vn >ANY (SELECT valn FROM it);
v	vn
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn >ANY (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.vn > derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(it.valn), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn >=ANY (SELECT valn FROM it);
v	vn
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn >=ANY (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.vn >= derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(it.valn), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <ANY (SELECT valn FROM it);
v	vn
0	0
1	1
explain format=tree SELECT *
FROM ot
WHERE vn <ANY (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.vn < derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: max(it.valn), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn <=ANY (SELECT valn FROM it);
v	vn
0	0
1	1
2	2
explain format=tree SELECT *
FROM ot
WHERE vn <=ANY (SELECT valn FROM it);
EXPLAIN
-> Inner hash join (no condition), extra conditions: (ot.vn <= derived_1_2.Name_exp_1)  (rows=1.67)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: max(it.valn), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE vn =ALL (SELECT n FROM t1row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT n FROM t1row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1) and (ot.vn = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1)))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t1row.n), count(0), max((t1row.n is null)), count(distinct t1row.n)  (rows=1)
                        -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE vn =ALL (SELECT vn FROM t2row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT vn FROM t2row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1) and (ot.vn = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1)))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t2row.vn), count(0), max((t2row.vn is null)), count(distinct t2row.vn)  (rows=1)
                        -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE vn =ALL (SELECT vn FROM t3row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT vn FROM t3row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1) and (ot.vn = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1)))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t3row.vn), count(0), max((t3row.vn is null)), count(distinct t3row.vn)  (rows=1)
                        -> Table scan on t3row  (rows=3)

SELECT *
FROM ot
WHERE vn =ALL (SELECT vn FROM t4row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn =ALL (SELECT vn FROM t4row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1) and (ot.vn = derived_1_2.Name_exp_1)))  (rows=0.95)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Filter: ((derived_1_2.Name_exp_2 = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1)))  (rows=0.19)
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(t4row.vn), count(0), max((t4row.vn is null)), count(distinct t4row.vn)  (rows=1)
                        -> Table scan on t4row  (rows=4)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT n FROM t1row);
v	vn
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT n FROM t1row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.vn <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=4.5)
    -> Filter: (ot.vn is not null)  (rows=4.5)
        -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t1row.n), count(0), count(distinct t1row.n)  (rows=1)
                        -> Table scan on t1row  (rows=1)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT vn FROM t2row);
v	vn
0	0
1	1
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT vn FROM t2row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.vn <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=4.5)
    -> Filter: (ot.vn is not null)  (rows=4.5)
        -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t2row.vn), count(0), count(distinct t2row.vn)  (rows=1)
                        -> Table scan on t2row  (rows=2)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT vn FROM t3row);
v	vn
0	0
1	1
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT vn FROM t3row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.vn <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=4.5)
    -> Filter: (ot.vn is not null)  (rows=4.5)
        -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t3row.vn), count(0), count(distinct t3row.vn)  (rows=1)
                        -> Table scan on t3row  (rows=3)

SELECT *
FROM ot
WHERE vn <>ANY (SELECT vn FROM t4row);
v	vn
0	0
1	1
2	2
3	3
explain format=tree SELECT *
FROM ot
WHERE vn <>ANY (SELECT vn FROM t4row);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.vn <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=4.5)
    -> Filter: (ot.vn is not null)  (rows=4.5)
        -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Filter: (count(0) <> 0)  (rows=1)
                    -> Aggregate: min(t4row.vn), count(0), count(distinct t4row.vn)  (rows=1)
                        -> Table scan on t4row  (rows=4)

#
# Tests with moderated outer query

# First, a non-moderated outer query:
SELECT v >=ALL (SELECT val FROM it) AS b
FROM ot;
b
0
0
0
1
1
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

# DISTINCT in SELECT list:
SELECT DISTINCT v >=ALL (SELECT val FROM it) AS b
FROM ot;
b
0
1
explain format=tree SELECT DISTINCT v >=ALL (SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Sort with duplicate removal: b  (rows=2.24)
    -> Left hash join (no condition)  (rows=5)
        -> Table scan on ot  (rows=5)
        -> Hash
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(it.val), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

# GROUP BY on quantified comparison predicate
SELECT v >=ALL (SELECT val FROM it) AS b, COUNT(*)
FROM ot
GROUP BY b;
b	COUNT(*)
0	3
1	2
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b, COUNT(*)
FROM ot
GROUP BY b;
EXPLAIN
-> Group aggregate: count(0)  (rows=2.24)
    -> Sort: b  (rows=5)
        -> Left hash join (no condition)  (rows=5)
            -> Table scan on ot  (rows=5)
            -> Hash
                -> Table scan on derived_1_2  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: max(it.val), count(0)  (rows=1)
                            -> Table scan on it  (rows=3)

# GROUP BY and HAVING on quantified comparison predicate
SELECT v >=ALL (SELECT val FROM it) AS b, COUNT(*)
FROM ot
GROUP BY b
HAVING b > 0;
b	COUNT(*)
1	2
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b, COUNT(*)
FROM ot
GROUP BY b
HAVING b > 0;
EXPLAIN
-> Filter: (b > 0)  (rows=2.24)
    -> Group aggregate: count(0)  (rows=2.24)
        -> Sort: b  (rows=5)
            -> Left hash join (no condition)  (rows=5)
                -> Table scan on ot  (rows=5)
                -> Hash
                    -> Table scan on derived_1_2  (rows=1)
                        -> Materialize  (rows=1)
                            -> Aggregate: max(it.val), count(0)  (rows=1)
                                -> Table scan on it  (rows=3)

# ORDER BY on quantified comparison predicate
SELECT v >=ALL (SELECT val FROM it) AS b
FROM ot
ORDER BY b;
b
0
0
0
1
1
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b
FROM ot
ORDER BY b;
EXPLAIN
-> Sort: b  (rows=5)
    -> Left hash join (no condition)  (rows=5)
        -> Table scan on ot  (rows=5)
        -> Hash
            -> Table scan on derived_1_2  (rows=1)
                -> Materialize  (rows=1)
                    -> Aggregate: max(it.val), count(0)  (rows=1)
                        -> Table scan on it  (rows=3)

# GROUP BY and ORDER BY on quantified comparison predicate
SELECT v >=ALL (SELECT val FROM it) AS b, COUNT(*)
FROM ot
GROUP BY b
ORDER BY b DESC;
b	COUNT(*)
1	2
0	3
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b, COUNT(*)
FROM ot
GROUP BY b
ORDER BY b DESC;
EXPLAIN
-> Group aggregate: count(0)  (rows=2.24)
    -> Sort: b DESC  (rows=5)
        -> Left hash join (no condition)  (rows=5)
            -> Table scan on ot  (rows=5)
            -> Hash
                -> Table scan on derived_1_2  (rows=1)
                    -> Materialize  (rows=1)
                        -> Aggregate: max(it.val), count(0)  (rows=1)
                            -> Table scan on it  (rows=3)

# Quantified comparison predicate as WINDOW expression
SELECT v >=ALL (SELECT val FROM it) AS b,
SUM((SELECT b)) OVER () AS s
FROM ot;
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b,
SUM((SELECT b)) OVER () AS s
FROM ot;
SELECT v >=ALL (SELECT val FROM it) AS b,
SUM((SELECT b)) OVER (PARTITION BY (SELECT b)) AS s
FROM ot;
explain format=tree SELECT v >=ALL (SELECT val FROM it) AS b,
SUM((SELECT b)) OVER (PARTITION BY (SELECT b)) AS s
FROM ot;
#
# Some negative testing
#
# No tables in outer query block
SELECT 1, 1 >ANY (SELECT val FROM it) AS q1, 2 >ANY (SELECT val FROM it) AS q2;
1	q1	q2
1	0	1
explain format=tree SELECT 1, 1 >ANY (SELECT val FROM it) AS q1, 2 >ANY (SELECT val FROM it) AS q2;
EXPLAIN
-> Rows fetched before execution  (rows=1)
-> Select #2 (subquery in projection; run only once)
    -> Aggregate: min(it.val)  (rows=1)
        -> Table scan on it  (rows=3)
-> Select #3 (subquery in projection; run only once)
    -> Aggregate: min(it.val)  (rows=1)
        -> Table scan on it  (rows=3)

SELECT 1 WHERE 2 >ANY (SELECT val FROM it);
1
1
explain format=tree SELECT 1 WHERE 2 >ANY (SELECT val FROM it);
EXPLAIN
-> Filter: <nop>((2 > (select #2)))  (rows=1)
    -> Rows fetched before execution  (rows=1)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: min(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

# No tables in inner query block
SELECT 1, 1 >ANY (SELECT 0) AS q1 FROM ot;
1	q1
1	1
1	1
1	1
1	1
1	1
explain format=tree SELECT 1, 1 >ANY (SELECT 0) AS q1 FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Rows fetched before execution  (rows=1)

SELECT 1 FROM ot WHERE 1 >ANY (SELECT 0);
1
1
1
1
1
1
explain format=tree SELECT 1 FROM ot WHERE 1 >ANY (SELECT 0);
EXPLAIN
-> Table scan on ot  (rows=5)

# Set operation in subquery
SELECT v >ALL (SELECT val FROM it UNION SELECT val FROM it) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v >ALL (SELECT val FROM it UNION SELECT val FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; run only once)
    -> Table scan on <union temporary>  (rows=6)
        -> Union materialize with deduplication  (rows=6)
            -> Table scan on it  (rows=3)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it UNION SELECT val FROM it);
v	vn
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it UNION SELECT val FROM it);
EXPLAIN
-> Filter: <not>((ot.v <= <max>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Table scan on <union temporary>  (rows=6)
            -> Union materialize with deduplication  (rows=6)
                -> Table scan on it  (rows=3)
                -> Table scan on it  (rows=3)

# Implicitly grouped subquery
SELECT v >ALL (SELECT MAX(val) FROM it) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v >ALL (SELECT MAX(val) FROM it) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) <= <ref_null_helper>(max(it.val)))  (rows=1)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >ALL (SELECT MAX(val) FROM it);
v	vn
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT MAX(val) FROM it);
EXPLAIN
-> Filter: <not>((ot.v <= <max>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: max(it.val)  (rows=1)
            -> Table scan on it  (rows=3)

# Explicitly grouped subquery
SELECT v >ALL (SELECT val FROM it GROUP BY val) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v >ALL (SELECT val FROM it GROUP BY val) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it GROUP BY val);
v	vn
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it GROUP BY val);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v > derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v >ALL (SELECT MAX(valn) FROM it GROUP BY val) AS b
FROM ot;
b
0
0
0
NULL
NULL
explain format=tree SELECT v >ALL (SELECT MAX(valn) FROM it GROUP BY val) AS b
FROM ot;
EXPLAIN
-> Table scan on ot  (rows=5)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<cache>(ot.v) <= <ref_null_helper>(max(it.valn)))  (rows=1.73)
        -> Group aggregate: max(it.valn)  (rows=1.73)
            -> Sort: it.val  (rows=3)
                -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >ALL (SELECT MAX(valn) FROM it GROUP BY val);
v	vn
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT MAX(valn) FROM it GROUP BY val);
EXPLAIN
-> Filter: <not>((ot.v <= <max>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Group aggregate: max(it.valn)  (rows=1.73)
            -> Sort: it.val  (rows=3)
                -> Table scan on it  (rows=3)

# Subquery with WINDOW function
SELECT v >ALL (SELECT MAX(val) OVER () FROM it) AS b
FROM ot;
b
0
0
0
0
1
SELECT *
FROM ot
WHERE v >ALL (SELECT MAX(val) OVER () FROM it);
v	vn
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT MAX(val) OVER () FROM it);
EXPLAIN
-> Filter: <not>((ot.v <= <max>(select #2)))  (rows=3.33)
    -> Table scan on ot  (rows=5)
    -> Select #2 (subquery in condition; run only once)
        -> Window aggregate with buffering: max(it.val) OVER ()   (rows=3)
            -> Table scan on it  (rows=3)

# Subquery with LIMIT (unsupported) and ORDER BY (supported)
SELECT v >ALL (SELECT val FROM it LIMIT 3) AS b
FROM ot;
ERROR 42000: This version of MySQL doesn't yet support 'LIMIT & IN/ALL/ANY/SOME subquery'
SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it LIMIT 3);
ERROR 42000: This version of MySQL doesn't yet support 'LIMIT & IN/ALL/ANY/SOME subquery'
SELECT v >ALL (SELECT val FROM it ORDER BY val DESC) AS b
FROM ot;
b
0
0
0
0
1
explain format=tree SELECT v >ALL (SELECT val FROM it ORDER BY val DESC) AS b
FROM ot;
EXPLAIN
-> Left hash join (no condition)  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it ORDER BY val DESC);
v	vn
4	NULL
explain format=tree SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it ORDER BY val DESC);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((ot.v > derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_2 = 0))  (rows=5)
    -> Table scan on ot  (rows=5)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: max(it.val), count(0)  (rows=1)
                    -> Table scan on it  (rows=3)

SELECT v >ALL (SELECT val FROM it ORDER BY val DESC LIMIT 3) AS b
FROM ot;
ERROR 42000: This version of MySQL doesn't yet support 'LIMIT & IN/ALL/ANY/SOME subquery'
SELECT *
FROM ot
WHERE v >ALL (SELECT val FROM it ORDER BY val DESC LIMIT 3);
ERROR 42000: This version of MySQL doesn't yet support 'LIMIT & IN/ALL/ANY/SOME subquery'
# Subquery with outer reference in SELECT list
SELECT rf, v, v <>ALL (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	v	b
1	0	1
1	1	0
1	2	0
1	3	0
1	4	1
2	0	1
2	1	0
2	2	0
2	3	0
2	4	1
3	0	1
3	1	1
3	2	1
3	3	1
3	4	1
explain format=tree SELECT rf, v, v <>ALL (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Left hash join (otr.v = derived_1_2.Name_exp_1), (otr.rf = derived_1_2.Name_exp_2)  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Sort with duplicate removal: itr.val, itr.rf  (rows=2.45)
                    -> Table scan on itr  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, v, v =ALL (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	v	b
1	0	0
1	1	0
1	2	0
1	3	0
1	4	0
2	0	0
2	1	0
2	2	0
2	3	0
2	4	0
3	0	1
3	1	1
3	2	1
3	3	1
3	4	1
explain format=tree SELECT rf, v, v =ALL (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Left hash join (otr.rf = derived_1_2.Name_exp_4)  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Group aggregate: max(itr.val), count(0), count(distinct itr.val)  (rows=2.45)
                    -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, v, v >ALL (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	v	b
1	0	0
1	1	0
1	2	0
1	3	0
1	4	1
2	0	0
2	1	0
2	2	0
2	3	0
2	4	1
3	0	1
3	1	1
3	2	1
3	3	1
3	4	1
explain format=tree SELECT rf, v, v >ALL (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Left hash join (otr.rf = derived_1_2.Name_exp_3)  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Group aggregate: max(itr.val), count(0)  (rows=2.45)
                    -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, v, v <>ANY (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	v	b
1	0	1
1	1	1
1	2	1
1	3	1
1	4	1
2	0	1
2	1	1
2	2	1
2	3	1
2	4	1
3	0	0
3	1	0
3	2	0
3	3	0
3	4	0
explain format=tree SELECT rf, v, v <>ANY (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Left hash join (otr.rf = derived_1_2.Name_exp_4)  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Group aggregate: min(itr.val), count(0), count(distinct itr.val)  (rows=2.45)
                    -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, v, v =ANY (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	v	b
1	0	0
1	1	1
1	2	1
1	3	1
1	4	0
2	0	0
2	1	1
2	2	1
2	3	1
2	4	0
3	0	0
3	1	0
3	2	0
3	3	0
3	4	0
explain format=tree SELECT rf, v, v =ANY (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Left hash join (otr.v = derived_1_2.Name_exp_1), (otr.rf = derived_1_2.Name_exp_2)  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Sort with duplicate removal: itr.val, itr.rf  (rows=2.45)
                    -> Table scan on itr  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, v, v >ANY (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	v	b
1	0	0
1	1	0
1	2	1
1	3	1
1	4	1
2	0	0
2	1	0
2	2	1
2	3	1
2	4	1
3	0	0
3	1	0
3	2	0
3	3	0
3	4	0
explain format=tree SELECT rf, v, v >ANY (SELECT val FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Left hash join (otr.rf = derived_1_2.Name_exp_3)  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Group aggregate: min(itr.val), count(0)  (rows=2.45)
                    -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, vn, vn <>ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	vn	b
1	0	NULL
1	1	0
1	2	0
1	3	NULL
1	NULL	NULL
2	0	NULL
2	1	0
2	2	0
2	3	NULL
2	NULL	NULL
3	0	1
3	1	1
3	2	1
3	3	1
3	NULL	1
explain format=tree SELECT rf, vn, vn <>ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(otr.vn) = itr.valn) or (itr.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(itr.valn), true))  (rows=1)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, vn, vn =ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	vn	b
1	0	0
1	1	0
1	2	0
1	3	0
1	NULL	NULL
2	0	0
2	1	0
2	2	0
2	3	0
2	NULL	NULL
3	0	1
3	1	1
3	2	1
3	3	1
3	NULL	1
explain format=tree SELECT rf, vn, vn =ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Left hash join (otr.rf = derived_1_2.Name_exp_5)  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Group aggregate: max(itr.valn), count(0), max((itr.valn is null)), count(distinct itr.valn)  (rows=2.45)
                    -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, vn, vn >ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	vn	b
1	0	0
1	1	0
1	2	0
1	3	NULL
1	NULL	NULL
2	0	0
2	1	0
2	2	0
2	3	NULL
2	NULL	NULL
3	0	1
3	1	1
3	2	1
3	3	1
3	NULL	1
explain format=tree SELECT rf, vn, vn >ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Left hash join (otr.rf = derived_1_2.Name_exp_4)  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Group aggregate: max(itr.valn), count(0), max((itr.valn is null))  (rows=2.45)
                    -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, vn, vn <>ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	vn	b
1	0	1
1	1	1
1	2	1
1	3	1
1	NULL	NULL
2	0	1
2	1	1
2	2	1
2	3	1
2	NULL	NULL
3	0	0
3	1	0
3	2	0
3	3	0
3	NULL	0
explain format=tree SELECT rf, vn, vn <>ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Left hash join (otr.rf = derived_1_2.Name_exp_5)  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Group aggregate: min(itr.valn), count(0), max((itr.valn is null)), count(distinct itr.valn)  (rows=2.45)
                    -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, vn, vn =ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	vn	b
1	0	NULL
1	1	1
1	2	1
1	3	NULL
1	NULL	NULL
2	0	NULL
2	1	1
2	2	1
2	3	NULL
2	NULL	NULL
3	0	0
3	1	0
3	2	0
3	3	0
3	NULL	0
explain format=tree SELECT rf, vn, vn =ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Table scan on otr  (rows=15)
-> Select #2 (subquery in projection; dependent)
    -> Filter: (<if>(outer_field_is_not_null, ((<cache>(otr.vn) = itr.valn) or (itr.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(itr.valn), true))  (rows=1)
        -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT rf, vn, vn >ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
rf	vn	b
1	0	NULL
1	1	NULL
1	2	1
1	3	1
1	NULL	NULL
2	0	NULL
2	1	NULL
2	2	1
2	3	1
2	NULL	NULL
3	0	0
3	1	0
3	2	0
3	3	0
3	NULL	0
explain format=tree SELECT rf, vn, vn >ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf) AS b
FROM otr;
EXPLAIN
-> Left hash join (otr.rf = derived_1_2.Name_exp_4)  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Group aggregate: min(itr.valn), count(0), max((itr.valn is null))  (rows=2.45)
                    -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
# Subquery with outer reference in WHERE clause
SELECT *
FROM otr
WHERE v <>ALL (SELECT val FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	0	0
1	4	NULL
2	0	0
2	4	NULL
3	0	0
3	1	1
3	2	2
3	3	3
3	4	NULL
explain format=tree SELECT *
FROM otr
WHERE v <>ALL (SELECT val FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: (derived_1_2.Name_exp_1 is null)  (rows=1.5)
    -> Left hash join (otr.v = derived_1_2.Name_exp_1), (otr.rf = derived_1_2.Name_exp_2)  (rows=15)
        -> Table scan on otr  (rows=15)
        -> Hash
            -> Table scan on derived_1_2  (rows=2.45)
                -> Materialize  (rows=2.45)
                    -> Sort with duplicate removal: itr.val, itr.rf  (rows=2.45)
                        -> Table scan on itr  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE v =ALL (SELECT val FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
3	0	0
3	1	1
3	2	2
3	3	3
3	4	NULL
explain format=tree SELECT *
FROM otr
WHERE v =ALL (SELECT val FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: ((coalesce(derived_1_2.Name_exp_2,0) = 0) or ((derived_1_2.Name_exp_3 = 1) and (otr.v = derived_1_2.Name_exp_1)))  (rows=15)
    -> Left hash join (otr.rf = derived_1_2.Name_exp_4)  (rows=15)
        -> Table scan on otr  (rows=15)
        -> Hash
            -> Table scan on derived_1_2  (rows=2.45)
                -> Materialize  (rows=2.45)
                    -> Group aggregate: max(itr.val), count(0), count(distinct itr.val)  (rows=2.45)
                        -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE v >ALL (SELECT val FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	4	NULL
2	4	NULL
3	0	0
3	1	1
3	2	2
3	3	3
3	4	NULL
explain format=tree SELECT *
FROM otr
WHERE v >ALL (SELECT val FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: ((otr.v > derived_1_2.Name_exp_1) or (coalesce(derived_1_2.Name_exp_2,0) = 0))  (rows=15)
    -> Left hash join (otr.rf = derived_1_2.Name_exp_3)  (rows=15)
        -> Table scan on otr  (rows=15)
        -> Hash
            -> Table scan on derived_1_2  (rows=2.45)
                -> Materialize  (rows=2.45)
                    -> Group aggregate: max(itr.val), count(0)  (rows=2.45)
                        -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE v <>ANY (SELECT val FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	0	0
1	1	1
1	2	2
1	3	3
1	4	NULL
2	0	0
2	1	1
2	2	2
2	3	3
2	4	NULL
explain format=tree SELECT *
FROM otr
WHERE v <>ANY (SELECT val FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Inner hash join (otr.rf = derived_1_2.Name_exp_4), extra conditions: ((otr.v <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=3.67)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Filter: (coalesce(count(0),0) <> 0)  (rows=2.45)
                    -> Group aggregate: min(itr.val), count(0), count(distinct itr.val)  (rows=2.45)
                        -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE v =ANY (SELECT val FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	1	1
1	2	2
1	3	3
2	1	1
2	2	2
2	3	3
explain format=tree SELECT *
FROM otr
WHERE v =ANY (SELECT val FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Inner hash join (otr.v = derived_1_2.Name_exp_1), (otr.rf = derived_1_2.Name_exp_2)  (rows=0.367)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Sort with duplicate removal: itr.val, itr.rf  (rows=2.45)
                    -> Table scan on itr  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE v >ANY (SELECT val FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	2	2
1	3	3
1	4	NULL
2	2	2
2	3	3
2	4	NULL
explain format=tree SELECT *
FROM otr
WHERE v >ANY (SELECT val FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Inner hash join (otr.rf = derived_1_2.Name_exp_3), extra conditions: (otr.v > derived_1_2.Name_exp_1)  (rows=1.22)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Filter: (coalesce(count(0),0) <> 0)  (rows=2.45)
                    -> Group aggregate: min(itr.val), count(0)  (rows=2.45)
                        -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE vn <>ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
3	0	0
3	1	1
3	2	2
3	3	3
3	4	NULL
explain format=tree SELECT *
FROM otr
WHERE vn <>ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: <in_optimizer>(otr.vn,<exists>(select #2) is false)  (rows=15)
    -> Table scan on otr  (rows=15)
    -> Select #2 (subquery in condition; dependent)
        -> Filter: (<if>(outer_field_is_not_null, ((<cache>(otr.vn) = itr.valn) or (itr.valn is null)), true) and <if>(outer_field_is_not_null, <is_not_null_test>(itr.valn), true))  (rows=1)
            -> Index lookup on itr using PRIMARY (rf = otr.rf)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE vn =ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
3	0	0
3	1	1
3	2	2
3	3	3
3	4	NULL
explain format=tree SELECT *
FROM otr
WHERE vn =ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: ((coalesce(derived_1_2.Name_exp_2,0) = 0) or (if((derived_1_2.Name_exp_3 = 0),true,NULL) and (derived_1_2.Name_exp_4 = 1) and (otr.vn = derived_1_2.Name_exp_1)))  (rows=15)
    -> Left hash join (otr.rf = derived_1_2.Name_exp_5)  (rows=15)
        -> Table scan on otr  (rows=15)
        -> Hash
            -> Table scan on derived_1_2  (rows=2.45)
                -> Materialize  (rows=2.45)
                    -> Group aggregate: max(itr.valn), count(0), max((itr.valn is null)), count(distinct itr.valn)  (rows=2.45)
                        -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE vn >ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
3	0	0
3	1	1
3	2	2
3	3	3
3	4	NULL
explain format=tree SELECT *
FROM otr
WHERE vn >ALL (SELECT valn FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Filter: (((otr.vn > derived_1_2.Name_exp_1) and if((derived_1_2.Name_exp_3 = 0),true,false)) or (coalesce(derived_1_2.Name_exp_2,0) = 0))  (rows=15)
    -> Left hash join (otr.rf = derived_1_2.Name_exp_4)  (rows=15)
        -> Table scan on otr  (rows=15)
        -> Hash
            -> Table scan on derived_1_2  (rows=2.45)
                -> Materialize  (rows=2.45)
                    -> Group aggregate: max(itr.valn), count(0), max((itr.valn is null))  (rows=2.45)
                        -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE vn <>ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	0	0
1	1	1
1	2	2
1	3	3
2	0	0
2	1	1
2	2	2
2	3	3
explain format=tree SELECT *
FROM otr
WHERE vn <>ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Inner hash join (otr.rf = derived_1_2.Name_exp_4), extra conditions: ((otr.vn <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=3.31)
    -> Filter: (otr.vn is not null)  (rows=13.5)
        -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Filter: (coalesce(count(0),0) <> 0)  (rows=2.45)
                    -> Group aggregate: min(itr.valn), count(0), count(distinct itr.valn)  (rows=2.45)
                        -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE vn =ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	1	1
1	2	2
2	1	1
2	2	2
explain format=tree SELECT *
FROM otr
WHERE vn =ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Inner hash join (otr.vn = derived_1_2.Name_exp_1), (otr.rf = derived_1_2.Name_exp_2)  (rows=0.367)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Sort with duplicate removal: itr.valn, itr.rf  (rows=2.45)
                    -> Table scan on itr  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT *
FROM otr
WHERE vn >ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf);
rf	v	vn
1	2	2
1	3	3
2	2	2
2	3	3
explain format=tree SELECT *
FROM otr
WHERE vn >ANY (SELECT valn FROM itr WHERE otr.rf = itr.rf);
EXPLAIN
-> Inner hash join (otr.rf = derived_1_2.Name_exp_3), extra conditions: (otr.vn > derived_1_2.Name_exp_1)  (rows=1.22)
    -> Table scan on otr  (rows=15)
    -> Hash
        -> Table scan on derived_1_2  (rows=2.45)
            -> Materialize  (rows=2.45)
                -> Filter: (coalesce(count(0),0) <> 0)  (rows=2.45)
                    -> Group aggregate: min(itr.valn), count(0)  (rows=2.45)
                        -> Index scan on itr using PRIMARY  (rows=6)

Warnings:
Note	1276	Field or reference 'test.otr.rf' of SELECT #2 was resolved in SELECT #1
SELECT i, i >ANY (SELECT i FROM t_null) FROM t_outer;
i	i >ANY (SELECT i FROM t_null)
1	NULL
1	NULL
2	1
explain format=tree SELECT i, i >ANY (SELECT i FROM t_null) FROM t_outer;
EXPLAIN
-> Left hash join (no condition)  (rows=3)
    -> Table scan on t_outer  (rows=3)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t_null.i), count(0), max((t_null.i is null))  (rows=1)
                    -> Table scan on t_null  (rows=4)

SELECT NULL >ANY (SELECT i FROM t_empty) AS result FROM t_null;
result
0
0
0
0
explain format=tree SELECT NULL >ANY (SELECT i FROM t_empty) AS result FROM t_null;
EXPLAIN
-> Left hash join (no condition)  (rows=4)
    -> Table scan on t_null  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(t_empty.i), count(0), max((t_empty.i is null))  (rows=1)
                    -> Table scan on t_empty  (rows=1)

SELECT i <>ANY (SELECT NULL FROM t_null) FROM t_null;
i <>ANY (SELECT NULL FROM t_null)
NULL
NULL
NULL
NULL
explain format=tree SELECT i <>ANY (SELECT NULL FROM t_null) FROM t_null;
EXPLAIN
-> Left hash join (no condition)  (rows=4)
    -> Table scan on t_null  (rows=4)
    -> Hash
        -> Table scan on derived_1_2  (rows=1)
            -> Materialize  (rows=1)
                -> Aggregate: min(NULL), count(0), max((NULL is null)), count(distinct NULL)  (rows=1)
                    -> Table scan on t_null  (rows=4)


Bug#37529060: WL#13052: !Item->hidden in Aggregator_distinct::setup

CREATE TABLE t1 (
pk int NOT NULL,
cv varchar(1) DEFAULT NULL,
cv_key varchar(1) DEFAULT NULL,
PRIMARY KEY (pk)
);
SELECT alias2.pk AS field1,
alias1.pk AS field2
FROM t1 AS alias1 INNER JOIN t1 AS alias2 ON alias2.cv = alias1.cv_key
WHERE alias2.cv <> ANY
(SELECT alias3.cv_key AS field3
FROM t1 AS alias3
WHERE alias3.cv_key >
(SELECT MAX(alias4.cv) AS field4
FROM t1 AS alias4
)
);
field1	field2
explain format=tree SELECT alias2.pk AS field1,
alias1.pk AS field2
FROM t1 AS alias1 INNER JOIN t1 AS alias2 ON alias2.cv = alias1.cv_key
WHERE alias2.cv <> ANY
(SELECT alias3.cv_key AS field3
FROM t1 AS alias3
WHERE alias3.cv_key >
(SELECT MAX(alias4.cv) AS field4
FROM t1 AS alias4
)
);
EXPLAIN
-> Inner hash join (no condition), extra conditions: ((alias2.cv <> derived_1_2.Name_exp_1) or (derived_1_2.Name_exp_3 > 1))  (rows=0.081)
    -> Table scan on derived_1_2  (rows=0.9)
        -> Materialize  (rows=0.9)
            -> Filter: (derived_2_3.field4 <> 0)  (rows=0.9)
                -> Aggregate: min(alias3.cv_key), count(0), count(distinct alias3.cv_key)  (rows=1)
                    -> Inner hash join (no condition), extra conditions: (alias3.cv_key > derived_2_3.field4)  (rows=0.333)
                        -> Table scan on alias3  (rows=1)
                        -> Hash
                            -> Table scan on derived_2_3  (rows=1)
                                -> Materialize  (rows=1)
                                    -> Aggregate: max(alias4.cv)  (rows=1)
                                        -> Table scan on alias4  (rows=1)
    -> Hash
        -> Inner hash join (alias2.cv = alias1.cv_key)  (rows=0.09)
            -> Table scan on alias1  (rows=1)
            -> Hash
                -> Filter: (alias2.cv is not null)  (rows=0.9)
                    -> Table scan on alias2  (rows=1)

DROP TABLE t1;

# Bug#37533056: WL#13052: Query is transformed where inner query block
#               contains outer references inside an inequality clause.

SELECT rf, v, v <>ALL (SELECT val FROM itr WHERE otr.rf < itr.rf) AS b FROM otr;
rf	v	b
1	0	1
1	1	0
1	2	0
1	3	0
1	4	1
2	0	1
2	1	1
2	2	1
2	3	1
2	4	1
3	0	1
3	1	1
3	2	1
3	3	1
3	4	1
SELECT rf, v, v NOT IN (SELECT val FROM itr WHERE otr.rf < itr.rf) AS b
FROM otr;
rf	v	b
1	0	1
1	1	0
1	2	0
1	3	0
1	4	1
2	0	1
2	1	1
2	2	1
2	3	1
2	4	1
3	0	1
3	1	1
3	2	1
3	3	1
3	4	1
SELECT rf, v, v =ANY (SELECT val FROM itr WHERE otr.rf < itr.rf) AS b FROM otr;
ERROR HY000: Statement requires a transform of a subquery to a non-SET operation (like IN2EXISTS, or subquery-to-LATERAL-derived-table). This is not allowed with optimizer switch 'subquery_to_derived' on.
SELECT rf, v, v IN (SELECT val FROM itr WHERE otr.rf < itr.rf) AS b FROM otr;
ERROR HY000: Statement requires a transform of a subquery to a non-SET operation (like IN2EXISTS, or subquery-to-LATERAL-derived-table). This is not allowed with optimizer switch 'subquery_to_derived' on.
SELECT rf, v, v <ALL (SELECT val FROM itr WHERE otr.rf > itr.rf) AS b FROM otr;
ERROR HY000: Statement requires a transform of a subquery to a non-SET operation (like IN2EXISTS, or subquery-to-LATERAL-derived-table). This is not allowed with optimizer switch 'subquery_to_derived' on.
SELECT rf, v, v >ALL (SELECT val FROM itr WHERE otr.rf <> itr.rf) AS b FROM otr;
ERROR HY000: Statement requires a transform of a subquery to a non-SET operation (like IN2EXISTS, or subquery-to-LATERAL-derived-table). This is not allowed with optimizer switch 'subquery_to_derived' on.
SELECT rf, v, v <ANY (SELECT val FROM itr WHERE otr.rf < itr.rf) AS b FROM otr;
ERROR HY000: Statement requires a transform of a subquery to a non-SET operation (like IN2EXISTS, or subquery-to-LATERAL-derived-table). This is not allowed with optimizer switch 'subquery_to_derived' on.
SELECT rf, v, v >ANY (SELECT val FROM itr WHERE otr.rf <> itr.rf) AS b FROM otr;
ERROR HY000: Statement requires a transform of a subquery to a non-SET operation (like IN2EXISTS, or subquery-to-LATERAL-derived-table). This is not allowed with optimizer switch 'subquery_to_derived' on.
SELECT rf, v, v =ALL (SELECT val FROM itr WHERE otr.rf < itr.rf) AS b FROM otr;
ERROR HY000: Statement requires a transform of a subquery to a non-SET operation (like IN2EXISTS, or subquery-to-LATERAL-derived-table). This is not allowed with optimizer switch 'subquery_to_derived' on.
SELECT rf, v, v <>ANY (SELECT val FROM itr WHERE otr.rf > itr.rf) AS b FROM otr;
ERROR HY000: Statement requires a transform of a subquery to a non-SET operation (like IN2EXISTS, or subquery-to-LATERAL-derived-table). This is not allowed with optimizer switch 'subquery_to_derived' on.
set optimizer_switch=default;
DROP TABLE it, ot, itr, otr, t0row, t1row, t2row, t3row, t4row;
DROP TABLE t_outer, t_null, t_empty;
