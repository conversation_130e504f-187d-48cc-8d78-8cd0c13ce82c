# Checking all valid GEOMETRY types
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"))
{"type": "Point", "coordinates": [10.0, 11.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(-10 -11)"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(-10 -11)"))
{"type": "Point", "coordinates": [-10.0, -11.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E+308)"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E+308)"))
{"type": "Point", "coordinates": [1.7976931348623157e308, 1.7976931348623157e308]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E-308 1.7976931348623157E-308)"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E-308 1.7976931348623157E-308)"))
{"type": "Point", "coordinates": [1.7976931348623155e-308, 1.7976931348623155e-308]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("LINESTRING(30 10, 10 30, 40 40)"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("LINESTRING(30 10, 10 30, 40 40)"))
{"type": "LineString", "coordinates": [[30.0, 10.0], [10.0, 30.0], [40.0, 40.0]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("LINESTRING(0 0, 0 10, 10 10, 10 0)"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("LINESTRING(0 0, 0 10, 10 10, 10 0)"))
{"type": "LineString", "coordinates": [[0.0, 0.0], [0.0, 10.0], [10.0, 10.0], [10.0, 0.0]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POLYGON((30 10, 40 40, 20 40, 10 20, 30 10))"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("POLYGON((30 10, 40 40, 20 40, 10 20, 30 10))"))
{"type": "Polygon", "coordinates": [[[30.0, 10.0], [40.0, 40.0], [20.0, 40.0], [10.0, 20.0], [30.0, 10.0]]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POLYGON((35 10, 45 45, 15 40, 10 20, 35 10),"
                                    "(20 30, 35 35, 30 20, 20 30))"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("POLYGON((35 10, 45 45, 15 40, 10 20, 35 10),"
                                    "(20 30, 35 35, 30 20, 20 30))"))
{"type": "Polygon", "coordinates": [[[35.0, 10.0], [45.0, 45.0], [15.0, 40.0], [10.0, 20.0], [35.0, 10.0]], [[20.0, 30.0], [35.0, 35.0], [30.0, 20.0], [20.0, 30.0]]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOINT(10 40, 40 30, 20 20, 30 10)"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOINT(10 40, 40 30, 20 20, 30 10)"))
{"type": "MultiPoint", "coordinates": [[10.0, 40.0], [40.0, 30.0], [20.0, 20.0], [30.0, 10.0]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOINT(0 0, 10 10, 20 20, 30 30, 40 40)"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOINT(0 0, 10 10, 20 20, 30 30, 40 40)"))
{"type": "MultiPoint", "coordinates": [[0.0, 0.0], [10.0, 10.0], [20.0, 20.0], [30.0, 30.0], [40.0, 40.0]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTILINESTRING((10 10, 20 20, 10 40),"
                                    "(40 40, 30 30, 40 20, 30 10))"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTILINESTRING((10 10, 20 20, 10 40),"
                                    "(40 40, 30 30, 40 20, 30 10))"))
{"type": "MultiLineString", "coordinates": [[[10.0, 10.0], [20.0, 20.0], [10.0, 40.0]], [[40.0, 40.0], [30.0, 30.0], [40.0, 20.0], [30.0, 10.0]]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTILINESTRING((10 10, 20 20, 10 40),"
                                    "(40 40, 30 30, 40 20, 30 10),"
                                    "(10 10, 20 20, 50 50, 100 100))"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTILINESTRING((10 10, 20 20, 10 40),"
                                    "(40 40, 30 30, 40 20, 30 10),"
                                    "(10 10, 20 20, 50 50, 100 100))"))
{"type": "MultiLineString", "coordinates": [[[10.0, 10.0], [20.0, 20.0], [10.0, 40.0]], [[40.0, 40.0], [30.0, 30.0], [40.0, 20.0], [30.0, 10.0]], [[10.0, 10.0], [20.0, 20.0], [50.0, 50.0], [100.0, 100.0]]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOLYGON(((40 40, 20 45, 45 30, 40 40)),"
                                    "((20 35, 10 30, 10 10, 30 5, 45 20, 20 35)))"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOLYGON(((40 40, 20 45, 45 30, 40 40)),"
                                    "((20 35, 10 30, 10 10, 30 5, 45 20, 20 35)))"))
{"type": "MultiPolygon", "coordinates": [[[[40.0, 40.0], [20.0, 45.0], [45.0, 30.0], [40.0, 40.0]]], [[[20.0, 35.0], [10.0, 30.0], [10.0, 10.0], [30.0, 5.0], [45.0, 20.0], [20.0, 35.0]]]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOLYGON(((40 40, 20 45, 45 30, 40 40)),"
                                    "((20 35, 10 30, 10 10, 30 5, 45 20, 20 35),"
                                    "(30 20, 20 15, 20 25, 30 20)))"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOLYGON(((40 40, 20 45, 45 30, 40 40)),"
                                    "((20 35, 10 30, 10 10, 30 5, 45 20, 20 35),"
                                    "(30 20, 20 15, 20 25, 30 20)))"))
{"type": "MultiPolygon", "coordinates": [[[[40.0, 40.0], [20.0, 45.0], [45.0, 30.0], [40.0, 40.0]]], [[[20.0, 35.0], [10.0, 30.0], [10.0, 10.0], [30.0, 5.0], [45.0, 20.0], [20.0, 35.0]], [[30.0, 20.0], [20.0, 15.0], [20.0, 25.0], [30.0, 20.0]]]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION()"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION()"))
{"type": "GeometryCollection", "geometries": []}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(10 20)))"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(10 20)))"))
{"type": "GeometryCollection", "geometries": [{"type": "GeometryCollection", "geometries": [{"type": "Point", "coordinates": [10.0, 20.0]}]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION()))"));
ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION()))"))
{"type": "GeometryCollection", "geometries": [{"type": "GeometryCollection", "geometries": [{"type": "GeometryCollection", "geometries": []}]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "GEOMETRYCOLLECTION(POINT(10 20)),"
                                    "GEOMETRYCOLLECTION(LINESTRING(0 0, 100 100)),"
                                    "GEOMETRYCOLLECTION(POLYGON((0 0, 0 10, 10 10, 0 0)))"
                                    ")"
                                  ));
ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "GEOMETRYCOLLECTION(POINT(10 20)),"
                                    "GEOMETRYCOLLECTION(LINESTRING(0 0, 100 100)),"
                                    "GEOMETRYCO
{"type": "GeometryCollection", "geometries": [{"type": "GeometryCollection", "geometries": [{"type": "Point", "coordinates": [10.0, 20.0]}]}, {"type": "GeometryCollection", "geometries": [{"type": "LineString", "coordinates": [[0.0, 0.0], [100.0, 100.0]]}]}, {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "coordinates": [[[0.0, 0.0], [0.0, 10.0], [10.0, 10.0], [0.0, 0.0]]]}]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "POINT(4 6),"
                                    "LINESTRING(4 6,7 10),"
                                    "POLYGON ((30 10, 40 40, 20 40, 10 20, 30 10)),"
                                    "MULTIPOINT (10 40, 40 30, 20 20, 30 10),"
                                    "MULTILINESTRING ((10 10, 20 20, 10 40),"
                                      "(40 40, 30 30, 40 20, 30 10)),"
                                    "MULTIPOLYGON (((30 20, 45 40, 10 40, 30 20)),"
                                      "((15 5, 40 10, 10 20, 5 10, 15 5)))"
                                    ")"
                                  ));
ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "POINT(4 6),"
                                    "LINESTRING(4 6,7 10),"
                                    "POLYGON ((30 10, 40 40, 20 40, 10 20, 30 10)),"
        
{"type": "GeometryCollection", "geometries": [{"type": "Point", "coordinates": [4.0, 6.0]}, {"type": "LineString", "coordinates": [[4.0, 6.0], [7.0, 10.0]]}, {"type": "Polygon", "coordinates": [[[30.0, 10.0], [40.0, 40.0], [20.0, 40.0], [10.0, 20.0], [30.0, 10.0]]]}, {"type": "MultiPoint", "coordinates": [[10.0, 40.0], [40.0, 30.0], [20.0, 20.0], [30.0, 10.0]]}, {"type": "MultiLineString", "coordinates": [[[10.0, 10.0], [20.0, 20.0], [10.0, 40.0]], [[40.0, 40.0], [30.0, 30.0], [40.0, 20.0], [30.0, 10.0]]]}, {"type": "MultiPolygon", "coordinates": [[[[30.0, 20.0], [45.0, 40.0], [10.0, 40.0], [30.0, 20.0]]], [[[15.0, 5.0], [40.0, 10.0], [10.0, 20.0], [5.0, 10.0], [15.0, 5.0]]]]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "POINT(4 6),"
                                    "GEOMETRYCOLLECTION("
                                      "POLYGON ((30 10, 40 40, 20 40, 10 20, 30 10)),"
                                      "MULTIPOINT (10 40, 40 30, 20 20, 30 10),"
                                      "MULTILINESTRING ((10 10, 20 20, 10 40),"
                                        "(40 40, 30 30, 40 20, 30 10)),"
                                      "MULTIPOLYGON (((30 20, 45 40, 10 40, 30 20)),"
                                        "((15 5, 40 10, 10 20, 5 10, 15 5)))"
                                      "),"
                                    "POINT(6 4)"
                                    ")"
                                  ));
ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "POINT(4 6),"
                                    "GEOMETRYCOLLECTION("
                                      "POLYGON ((30 10, 40 40, 20 40, 10 20, 30 10)),"
        
{"type": "GeometryCollection", "geometries": [{"type": "Point", "coordinates": [4.0, 6.0]}, {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "coordinates": [[[30.0, 10.0], [40.0, 40.0], [20.0, 40.0], [10.0, 20.0], [30.0, 10.0]]]}, {"type": "MultiPoint", "coordinates": [[10.0, 40.0], [40.0, 30.0], [20.0, 20.0], [30.0, 10.0]]}, {"type": "MultiLineString", "coordinates": [[[10.0, 10.0], [20.0, 20.0], [10.0, 40.0]], [[40.0, 40.0], [30.0, 30.0], [40.0, 20.0], [30.0, 10.0]]]}, {"type": "MultiPolygon", "coordinates": [[[[30.0, 20.0], [45.0, 40.0], [10.0, 40.0], [30.0, 20.0]]], [[[15.0, 5.0], [40.0, 10.0], [10.0, 20.0], [5.0, 10.0], [15.0, 5.0]]]]}]}, {"type": "Point", "coordinates": [6.0, 4.0]}]}
# Checking NULL as one or more of the arguments.
SELECT ST_ASGEOJSON(NULL);
ST_ASGEOJSON(NULL)
NULL
SELECT ST_ASGEOJSON(NULL, NULL);
ST_ASGEOJSON(NULL, NULL)
NULL
SELECT ST_ASGEOJSON(NULL, 10);
ST_ASGEOJSON(NULL, 10)
NULL
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"), NULL);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"), NULL)
NULL
SELECT ST_ASGEOJSON(NULL, 10, 0);
ST_ASGEOJSON(NULL, 10, 0)
NULL
SELECT ST_ASGEOJSON(NULL, NULL, 0);
ST_ASGEOJSON(NULL, NULL, 0)
NULL
SELECT ST_ASGEOJSON(NULL, 10, NULL);
ST_ASGEOJSON(NULL, 10, NULL)
NULL
SELECT ST_ASGEOJSON(NULL, NULL, NULL);
ST_ASGEOJSON(NULL, NULL, NULL)
NULL
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"), NULL, 0);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"), NULL, 0)
NULL
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"), 10, NULL);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"), 10, NULL)
NULL
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"), NULL, NULL);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"), NULL, NULL)
NULL
# Checking options argument and maxdecimaldigits argument
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4326), 100, 2);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4326), 100, 2)
{"crs": {"type": "name", "properties": {"name": "EPSG:4326"}}, "type": "Point", "coordinates": [11.0, 10.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4326), 100, 4);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4326), 100, 4)
{"crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG::4326"}}, "type": "Point", "coordinates": [11.0, 10.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4326), 100, 6);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4326), 100, 6)
{"crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG::4326"}}, "type": "Point", "coordinates": [11.0, 10.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 0), 100, 5);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 0), 100, 5)
{"bbox": [10.0, 11.0, 10.0, 11.0], "type": "Point", "coordinates": [10.0, 11.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 0), 100, 2);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 0), 100, 2)
{"type": "Point", "coordinates": [10.0, 11.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", -1), 100, 4);
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", -1000), 100, 6);
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4294967296), 100, 6);
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4294967295123), 100, 7);
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), 0, 0);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), 0, 0)
{"type": "Point", "coordinates": [10.0, 11.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), 4, 7);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), 4, 7)
{"bbox": [10.1235, 11.1235, 10.1235, 11.1235], "type": "Point", "coordinates": [10.1235, 11.1235]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), 10, 0);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), 10, 0)
{"type": "Point", "coordinates": [10.123456789, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), 0);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), 0)
{"type": "Point", "coordinates": [10.0, 11.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), 4);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), 4)
{"type": "Point", "coordinates": [10.1235, 11.1235]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), 10);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), 10)
{"type": "Point", "coordinates": [10.123456789, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.123456789)", 0), 10000);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.123456789)", 0), 10000)
{"type": "Point", "coordinates": [10.123456789012346, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.123456789)", 0), 1000000);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.123456789)", 0), 1000000)
{"type": "Point", "coordinates": [10.123456789012346, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.123456789)", 0), 2147483647);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.123456789)", 0), 2147483647)
{"type": "Point", "coordinates": [10.123456789012346, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 0);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 0)
{"type": "Point", "coordinates": [10.123456789, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 1);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 1)
{"bbox": [10.123456789, 11.123456789, 10.123456789, 11.123456789], "type": "Point", "coordinates": [10.123456789, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 2);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 2)
{"type": "Point", "coordinates": [10.123456789, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 3);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 3)
{"bbox": [10.123456789, 11.123456789, 10.123456789, 11.123456789], "type": "Point", "coordinates": [10.123456789, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 4);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 4)
{"type": "Point", "coordinates": [10.123456789, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 5);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 5)
{"bbox": [10.123456789, 11.123456789, 10.123456789, 11.123456789], "type": "Point", "coordinates": [10.123456789, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 6);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 6)
{"type": "Point", "coordinates": [10.123456789, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 7);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, 7)
{"bbox": [10.123456789, 11.123456789, 10.123456789, 11.123456789], "type": "Point", "coordinates": [10.123456789, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", "0"), 100, 1);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", "0"), 100, 1)
{"bbox": [10.0, 11.0, 10.0, 11.0], "type": "Point", "coordinates": [10.0, 11.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", "-1"), 100, 2);
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", "-1000"), 100, 3);
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", "4294967296"), 100, 5);
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", "4294967295123"), 100, 6);
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", (CAST(4326 AS CHAR))), 100, 7);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", (CAST(4326 AS CHAR))), 100, 7)
{"crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG::4326"}}, "bbox": [11.0, 10.0, 11.0, 10.0], "type": "Point", "coordinates": [11.0, 10.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), "0", 1);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), "0", 1)
{"bbox": [10.0, 11.0, 10.0, 11.0], "type": "Point", "coordinates": [10.0, 11.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), "4", 2);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), "4", 2)
{"type": "Point", "coordinates": [10.1235, 11.1235]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), "10", 3);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.123456789 11.123456789)", 0), "10", 3)
{"bbox": [10.123456789, 11.123456789, 10.123456789, 11.123456789], "type": "Point", "coordinates": [10.123456789, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), "10000", 4);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), "10000", 4)
{"type": "Point", "coordinates": [10.123456789012346, 11.123456789012346]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), "1000000", 5);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), "1000000", 5)
{"bbox": [10.123456789012346, 11.123456789012346, 10.123456789012346, 11.123456789012346], "type": "Point", "coordinates": [10.123456789012346, 11.123456789012346]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), "2147483647", 6);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), "2147483647", 6)
{"type": "Point", "coordinates": [10.123456789012346, 11.123456789012346]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), (CAST(20 AS CHAR)), 7);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), (CAST(20 AS CHAR)), 7)
{"bbox": [10.123456789012346, 11.123456789012346, 10.123456789012346, 11.123456789012346], "type": "Point", "coordinates": [10.123456789012346, 11.123456789012346]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, (CAST(7 AS CHAR)));
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10.1234567890123456789 11.1234567890123456789)", 0), 10, (CAST(7 AS CHAR)))
{"bbox": [10.123456789, 11.123456789, 10.123456789, 11.123456789], "type": "Point", "coordinates": [10.123456789, 11.123456789]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", " 4326.34 "), 100, 1);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", " 4326.34 "), 100, 1)
{"bbox": [11.0, 10.0, 11.0, 10.0], "type": "Point", "coordinates": [11.0, 10.0]}
Warnings:
Warning	1292	Truncated incorrect INTEGER value: ' 4326.34 '
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", "*&$%4326"), 100, 2);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", "*&$%4326"), 100, 2)
{"type": "Point", "coordinates": [10.0, 11.0]}
Warnings:
Warning	1292	Truncated incorrect INTEGER value: '*&$%4326'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 0), "12.34", 3);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 0), "12.34", 3)
{"bbox": [10.0, 11.0, 10.0, 11.0], "type": "Point", "coordinates": [10.0, 11.0]}
Warnings:
Warning	1292	Truncated incorrect INTEGER value: '12.34'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4326), "*&$%$123", 4);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4326), "*&$%$123", 4)
{"crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG::4326"}}, "type": "Point", "coordinates": [11.0, 10.0]}
Warnings:
Warning	1292	Truncated incorrect INTEGER value: '*&$%$123'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4236), 10, "06.34");
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4236), 10, "06.34")
{"crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG::4236"}}, "type": "Point", "coordinates": [11.0, 10.0]}
Warnings:
Warning	1292	Truncated incorrect INTEGER value: '06.34'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4326), 10, "*&$%$123");
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4326), 10, "*&$%$123")
{"type": "Point", "coordinates": [11.0, 10.0]}
Warnings:
Warning	1292	Truncated incorrect INTEGER value: '*&$%$123'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOLYGON(((40 40, 20 45, 45 30, 40 40)),"
                                    "((20 35, 10 30, 10 10, 30 5, 45 20, 20 35),"
                                    "(30 20, 20 15, 20 25, 30 20)))"), 10, 1);
ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOLYGON(((40 40, 20 45, 45 30, 40 40)),"
                                    "((20 35, 10 30, 10 10, 30 5, 45 20, 20 35),"
                                    "(30 20, 20 15, 20 25, 30 20)))"), 10, 1)
{"bbox": [10.0, 5.0, 45.0, 45.0], "type": "MultiPolygon", "coordinates": [[[[40.0, 40.0], [20.0, 45.0], [45.0, 30.0], [40.0, 40.0]]], [[[20.0, 35.0], [10.0, 30.0], [10.0, 10.0], [30.0, 5.0], [45.0, 20.0], [20.0, 35.0]], [[30.0, 20.0], [20.0, 15.0], [20.0, 25.0], [30.0, 20.0]]]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "GEOMETRYCOLLECTION(POINT(10 20)),"
                                    "GEOMETRYCOLLECTION(LINESTRING(0 0, 100 100)),"
                                    "GEOMETRYCOLLECTION(POLYGON((0 0, 0 10, 10 10, 0 0)))"
                                    ")"
                                  ), 5, 1);
ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "GEOMETRYCOLLECTION(POINT(10 20)),"
                                    "GEOMETRYCOLLECTION(LINESTRING(0 0, 100 100)),"
                                    "GEOMETRYCO
{"bbox": [0.0, 0.0, 100.0, 100.0], "type": "GeometryCollection", "geometries": [{"bbox": [10.0, 20.0, 10.0, 20.0], "type": "GeometryCollection", "geometries": [{"bbox": [10.0, 20.0, 10.0, 20.0], "type": "Point", "coordinates": [10.0, 20.0]}]}, {"bbox": [0.0, 0.0, 100.0, 100.0], "type": "GeometryCollection", "geometries": [{"bbox": [0.0, 0.0, 100.0, 100.0], "type": "LineString", "coordinates": [[0.0, 0.0], [100.0, 100.0]]}]}, {"bbox": [0.0, 0.0, 10.0, 10.0], "type": "GeometryCollection", "geometries": [{"bbox": [0.0, 0.0, 10.0, 10.0], "type": "Polygon", "coordinates": [[[0.0, 0.0], [0.0, 10.0], [10.0, 10.0], [0.0, 0.0]]]}]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION("
                                    "POINT(4 6),"
                                    "GEOMETRYCOLLECTION("
                                      "POLYGON ((30 10, 40 40, 20 40, 10 20, 30 10)),"
                                      "MULTIPOINT (10 40, 40 30, 20 20, 30 10),"
                                      "MULTILINESTRING ((10 10, 20 20, 10 40),"
                                        "(40 40, 30 30, 40 20, 30 10)),"
                                      "MULTIPOLYGON (((30 20, 45 40, 10 40, 30 20)),"
                                        "((15 5, 40 10, 10 20, 5 10, 15 5)))"
                                      "),"
                                    "POINT(6 4)"
                                    ")"
                                  ), 10, 1 );
ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION("
                                    "POINT(4 6),"
                                    "GEOMETRYCOLLECTION("
                                      "POLYGON ((30 10, 40 40, 20 40, 10 20, 30 10)),"
         
{"bbox": [4.0, 4.0, 45.0, 40.0], "type": "GeometryCollection", "geometries": [{"bbox": [4.0, 6.0, 4.0, 6.0], "type": "Point", "coordinates": [4.0, 6.0]}, {"bbox": [5.0, 5.0, 45.0, 40.0], "type": "GeometryCollection", "geometries": [{"bbox": [10.0, 10.0, 40.0, 40.0], "type": "Polygon", "coordinates": [[[30.0, 10.0], [40.0, 40.0], [20.0, 40.0], [10.0, 20.0], [30.0, 10.0]]]}, {"bbox": [10.0, 10.0, 40.0, 40.0], "type": "MultiPoint", "coordinates": [[10.0, 40.0], [40.0, 30.0], [20.0, 20.0], [30.0, 10.0]]}, {"bbox": [10.0, 10.0, 40.0, 40.0], "type": "MultiLineString", "coordinates": [[[10.0, 10.0], [20.0, 20.0], [10.0, 40.0]], [[40.0, 40.0], [30.0, 30.0], [40.0, 20.0], [30.0, 10.0]]]}, {"bbox": [5.0, 5.0, 45.0, 40.0], "type": "MultiPolygon", "coordinates": [[[[30.0, 20.0], [45.0, 40.0], [10.0, 40.0], [30.0, 20.0]]], [[[15.0, 5.0], [40.0, 10.0], [10.0, 20.0], [5.0, 10.0], [15.0, 5.0]]]]}]}, {"bbox": [6.0, 4.0, 6.0, 4.0], "type": "Point", "coordinates": [6.0, 4.0]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION()"), 10, 1);
ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION()"), 10, 1)
{"type": "GeometryCollection", "geometries": []}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E-308)"), 0, 1);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E-308)"), 0, 1)
{"bbox": [1.7976931348623157e308, 0.0, 1.7976931348623157e308, 0.0], "type": "Point", "coordinates": [1.7976931348623157e308, 0.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E-308)"), 10, 1);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E-308)"), 10, 1)
{"bbox": [1.7976931348623157e308, 0.0, 1.7976931348623157e308, 0.0], "type": "Point", "coordinates": [1.7976931348623157e308, 0.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E-308)"), 20, 1);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E-308)"), 20, 1)
{"bbox": [1.7976931348623157e308, 0.0, 1.7976931348623157e308, 0.0], "type": "Point", "coordinates": [1.7976931348623157e308, 0.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E-308)"), 30, 1);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E-308)"), 30, 1)
{"bbox": [1.7976931348623157e308, 0.0, 1.7976931348623157e308, 0.0], "type": "Point", "coordinates": [1.7976931348623157e308, 0.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E-308)"), 40, 1);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E-308)"), 40, 1)
{"bbox": [1.7976931348623157e308, 0.0, 1.7976931348623157e308, 0.0], "type": "Point", "coordinates": [1.7976931348623157e308, 0.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E-308)"), 1000, 1);
ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E-308)"), 1000, 1)
{"bbox": [1.7976931348623157e308, 1.7976931348623155e-308, 1.7976931348623157e308, 1.7976931348623155e-308], "type": "Point", "coordinates": [1.7976931348623157e308, 1.7976931348623155e-308]}
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT('POINT(1.2345678901234567890 2.345678901234567890)'), SUM(3));
ERROR HY000: Incorrect type for argument max decimal digits in function st_asgeojson.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT('POINT(1.2345678901234567890 2.345678901234567890)'), AVG(3));
ERROR HY000: Incorrect type for argument max decimal digits in function st_asgeojson.
# Check that function outputs correct character set
SET NAMES 'ascii';
SELECT CHARSET(ST_ASGEOJSON(POINT(-1.42, 283.12)));
CHARSET(ST_ASGEOJSON(POINT(-1.42, 283.12)))
utf8mb4
SET NAMES 'utf8mb3';
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT CHARSET(ST_ASGEOJSON(POINT(-1.42, 283.12)));
CHARSET(ST_ASGEOJSON(POINT(-1.42, 283.12)))
utf8mb4
# Invalid function calls
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("1"));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT"));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT()"));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10)"));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(a b)"));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 b)"));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(a 11)"));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10, 11)"));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POLYGON((40 40, 20 45, 45 30, 40 40)"), 10);
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOLYGON(((40 40, 20 45, 45 30, 40 40)),"
                                    "((20 35, 10 30, 10 10, 30 5, 45 20, 20 35),"), 10, 2);
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "GEOMETRYCOLLECTION(POINT(10 20)),"
                                    "GEOMETRYCOLLECTION(LINESTRING(0 0, 100 100)),"
                                    "GEOMETRYCOLLECTION(POLYGON((0 0, 0 10, 10 10, 0 0))),"
                                    ")"
                                  ));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOINT()"), 10);
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTILINESTRING(())"), 10);
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOLYGON((()))"), 10);
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("LINESTRING(1)"), 10);
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTILINESTRING((0 0, 10 10),())"), 10);
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 2, -1);
ERROR HY000: Incorrect options value: '-1' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 2, -1000);
ERROR HY000: Incorrect options value: '-1000' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 2, 8);
ERROR HY000: Incorrect options value: '8' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 2, 1000);
ERROR HY000: Incorrect options value: '1000' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 2, "-1");
ERROR HY000: Incorrect options value: '-1' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 2, "-1000");
ERROR HY000: Incorrect options value: '-1000' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 2, "8");
ERROR HY000: Incorrect options value: '8' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 2, "1000");
ERROR HY000: Incorrect options value: '1000' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)", 4236), 10, "16.34");
ERROR HY000: Incorrect options value: '16' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), -1, 0);
ERROR HY000: Incorrect max decimal digits value: '-1' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), -1000, 0);
ERROR HY000: Incorrect max decimal digits value: '-1000' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 2147483648, 0);
ERROR HY000: Incorrect max decimal digits value: '2147483648' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 2147483647000, 0);
ERROR HY000: Incorrect max decimal digits value: '2147483647000' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), "-1", 0);
ERROR HY000: Incorrect max decimal digits value: '-1' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), "-1000", 0);
ERROR HY000: Incorrect max decimal digits value: '-1000' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), "2147483648", 0);
ERROR HY000: Incorrect max decimal digits value: '2147483648' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), "2147483647000", 0);
ERROR HY000: Incorrect max decimal digits value: '2147483647000' for function st_asgeojson
SELECT ST_ASGEOJSON();
ERROR 42000: Incorrect parameter count in the call to native function 'ST_ASGEOJSON'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"), 10, 0, 0);
ERROR 42000: Incorrect parameter count in the call to native function 'ST_ASGEOJSON'
SELECT ST_ASGEOJSON(1);
ERROR HY000: Incorrect type for argument geometry in function st_asgeojson.
SELECT ST_ASGEOJSON(1, 2);
ERROR HY000: Incorrect type for argument geometry in function st_asgeojson.
SELECT ST_ASGEOJSON(1, 0, 1);
ERROR HY000: Incorrect type for argument geometry in function st_asgeojson.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 2.1, 0);
ERROR HY000: Incorrect type for argument max decimal digits in function st_asgeojson.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 10, 0.1);
ERROR HY000: Incorrect type for argument options in function st_asgeojson.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), (CAST(10 AS BINARY)), 0);
ERROR HY000: Incorrect type for argument max decimal digits in function st_asgeojson.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 10, (CAST(4 AS BINARY)));
ERROR HY000: Incorrect type for argument options in function st_asgeojson.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), (CAST(10 AS DECIMAL)), 0);
ERROR HY000: Incorrect type for argument max decimal digits in function st_asgeojson.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 20)"), 10, (CAST(4 AS DECIMAL)));
ERROR HY000: Incorrect type for argument options in function st_asgeojson.
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"), abc, 0);
ERROR 42S22: Unknown column 'abc' in 'field list'
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"), 0, abc);
ERROR 42S22: Unknown column 'abc' in 'field list'
SELECT ST_ASGEOJSON("*&$%$123", abc, 0);
ERROR 42S22: Unknown column 'abc' in 'field list'
PREPARE stmt FROM "SELECT ST_ASGEOJSON(?, 10, 0)";
PREPARE stmt FROM "SELECT ST_ASGEOJSON(POINT(10, 10), ?, 0)";
PREPARE stmt FROM "SELECT ST_ASGEOJSON(POINT(10, 10), 10, ?)";
PREPARE stmt FROM "SELECT ST_ASGEOJSON(?, ?, 0)";
PREPARE stmt FROM "SELECT ST_ASGEOJSON(?, 10, ?)";
PREPARE stmt FROM "SELECT ST_ASGEOJSON(POINT(10, 10), ?, ?)";
PREPARE stmt FROM "SELECT ST_ASGEOJSON(?, ?, ?)";
CREATE TABLE t1 (g GEOMETRY);
# Check that packet overflow is handled correctly.
SET @old_max_allowed_packet= @@global.max_allowed_packet;
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
"  LINESTRING(1 2, 3 4, 5 6, 7 8, 9 10, 11 12, 13 14, 15 16, 17 18, 19 20,    "
  "  21 22, 23 24, 25 26, 27 28, 29 30, 31 32, 33 34, 35 36, 37 38, 39 40,      "
  "  41 42, 43 44, 45 46, 47 48, 49 50)                                         "
));
SELECT ST_ASTEXT(g) FROM t1;
ST_ASTEXT(g)
LINESTRING(1 2,3 4,5 6,7 8,9 10,11 12,13 14,15 16,17 18,19 20,21 22,23 24,25 26,27 28,29 30,31 32,33 34,35 36,37 38,39 40,41 42,43 44,45 46,47 48,49 50)
UPDATE t1 SET g = GEOMETRYCOLLECTION(g, g, g, g, g, g, g, g, g, g);
UPDATE t1 SET g = GEOMETRYCOLLECTION(g, g, g, g, g, g, g, g, g, g);
SELECT LENGTH(ST_ASTEXT(g)) FROM t1;
LENGTH(ST_ASTEXT(g))
15519
SELECT LENGTH(ST_ASGEOJSON(g)) FROM t1;
LENGTH(ST_ASGEOJSON(g))
38726
SET @@global.max_allowed_packet = 27648;
SELECT LENGTH(ST_ASGEOJSON(g)) FROM t1;
LENGTH(ST_ASGEOJSON(g))
NULL
Warnings:
Warning	1301	Result of st_asgeojson() was larger than max_allowed_packet (27648) - truncated
SELECT ST_ASGEOJSON(g) FROM t1;
ST_ASGEOJSON(g)
NULL
Warnings:
Warning	1301	Result of st_asgeojson() was larger than max_allowed_packet (27648) - truncated
SET @@global.max_allowed_packet = @old_max_allowed_packet;
DELETE FROM t1;
INSERT INTO t1 VALUES ("");
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
SELECT ST_ASGEOJSON(g) FROM t1;
ST_ASGEOJSON(g)
DELETE FROM t1;
INSERT INTO t1 VALUES (0x00000000010200000000000000000024400000000000002440);
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
SELECT ST_ASGEOJSON(g) FROM t1;
ST_ASGEOJSON(g)
DELETE FROM t1;
INSERT INTO t1 VALUES (0x00000000010300000000000000000024400000000000002440);
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
SELECT ST_ASGEOJSON(g) FROM t1;
ST_ASGEOJSON(g)
DELETE FROM t1;
INSERT INTO t1 VALUES (0x00000000010300000000000000000024400000000000002440);
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
SELECT ST_ASGEOJSON(g) FROM t1;
ST_ASGEOJSON(g)
DELETE FROM t1;
INSERT INTO t1 VALUES (0x0000000001040000000100000001010000000000000000000000000000);
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
SELECT ST_ASGEOJSON(g) FROM t1;
ST_ASGEOJSON(g)
DELETE FROM t1;
INSERT INTO t1 VALUES (0x000000000107000000010000000107000000010000000101000000000000000000244000000000000024);
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
SELECT ST_ASGEOJSON(g) FROM t1;
ST_ASGEOJSON(g)
DELETE FROM t1;
INSERT INTO t1 VALUES (0x00000000010300000001000000050000000000000000003E4000000000000024400000000000004440000000000000444000000000000034400000000000004440000000000000244000000000000034400000000000003E4000000000000024);
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
SELECT ST_ASGEOJSON(g) FROM t1;
ST_ASGEOJSON(g)
DELETE FROM t1;
INSERT INTO t1 VALUES (0x00000000010300000001000000000000000000000000003E4000000000000024400000000000004440000000000000444000000000000034400000000000004440000000000000244000000000000034400000000000003E400000000000002440);
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
SELECT ST_ASGEOJSON(g) FROM t1;
ST_ASGEOJSON(g)
DELETE FROM t1;
INSERT INTO t1 VALUES (0x00000000010200000001000000000000000000244000000000000024);
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
SELECT ST_ASGEOJSON(g) FROM t1;
ST_ASGEOJSON(g)
DELETE FROM t1;
INSERT INTO t1 VALUES (0x0000000001050000000000000001020000000100000000000000000024400000000000003440);
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
SELECT ST_ASGEOJSON(g) FROM t1;
ST_ASGEOJSON(g)
DELETE FROM t1;
INSERT INTO t1 VALUES (0x0000000001050000000200000001020000000100000000000000000024400000000000003440);
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
SELECT ST_ASGEOJSON(g) FROM t1;
ST_ASGEOJSON(g)
DELETE FROM t1;
INSERT INTO t1 VALUES (0x000000000107000000010000000107000000010000000107000000010000000107000000010000000107000000010000000107000000010000000107000000);
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
SELECT ST_ASGEOJSON(g) FROM t1;
ST_ASGEOJSON(g)
DROP TABLE t1;
# Checking all valid GeoJSON types
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}"))
POINT(0 102)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"LineString\", \"coordinates\": "
  "[ [102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0] ] }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"LineString\", \"coordinates\": "
  "[ [102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0] ] }"))
LINESTRING(0 102,1 103,0 104,1 105)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"MultiPoint\", \"coordinates\": "
  "[ [-105.01, 39.57],[-80.66, 35.0] ] }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"MultiPoint\", \"coordinates\": "
  "[ [-105.01, 39.57],[-80.66, 35.0] ] }"))
MULTIPOINT((39.57 -105.01),(35 -80.66))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MultiLineString\", \"coordinates\":      "
"  [                                                      "
"    [                                                    "
"      [-101.5, 39.662],                                  "
"      [-101.75, 39.2415],                                "
"      [-101.23, 39.2415],                                "
"      [-101.749, 39.7984],                               "
"      [-101.5, 39.011]                                   "
"    ],                                                   "
"    [                                                    "
"      [-99.23, 38.6605],                                 "
"      [-99.56, 38.727],                                  "
"      [-99.25, 38.018]                                   "
"    ],                                                   "
"    [                                                    "
"      [-98.499, 38.913],                                 "
"      [-98.499, 38.913],                                 "
"      [-98.38, 38.15],                                   "
"      [-97.5, 38.629]                                    "
"    ]                                                    "
"  ]                                                      "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MultiLineString\", \"coordinates\":      "
"  [                                                      "
"    [                                                    "
"      [-101.5, 39.662],                      
MULTILINESTRING((39.662 -101.5,39.2415 -101.75,39.2415 -101.23,39.7984 -101.749,39.011 -101.5),(38.6605 -99.23,38.727 -99.56,38.018 -99.25),(38.913 -98.499,38.913 -98.499,38.15 -98.38,38.629 -97.5))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"  [                                              "
"    [                                            "
"      [41.83, 71.01],                            "
"      [56.95, 33.75],                            "
"      [21.79, 36.56],                            "
"      [41.83, 71.01]                             "
"    ]                                            "
"  ]                                              "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"  [                                              "
"    [                                            "
"      [41.83, 71.01],                            "
"      [56.95, 33.
POLYGON((71.01 41.83,33.75 56.95,36.56 21.79,71.01 41.83))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"  [                                              "
"    [                                            "
"      [41.83, 71.01],                            "
"      [56.95, 33.75],                            "
"      [21.79, 36.56],                            "
"      [41.83, 71.01]                             "
"    ],                                           "
"    [                                            "
"      [15.40, 15.40],                            "
"      [15.40, 25.40],                            "
"      [25.40, 25.40],                            "
"      [15.40, 15.40]                             "
"    ]                                            "
"  ]                                              "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"  [                                              "
"    [                                            "
"      [41.83, 71.01],                            "
"      [56.95, 33.
POLYGON((71.01 41.83,33.75 56.95,36.56 21.79,71.01 41.83),(15.4 15.4,25.4 15.4,25.4 25.4,15.4 15.4))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MultiPolygon\", \"coordinates\":     "
"  [                                                  "
"    [                                                "
"      [                                              "
"        [102.0, 2.0],                                "
"        [103.0, 2.0],                                "
"        [103.0, 3.0],                                "
"        [102.0, 3.0],                                "
"        [102.0, 2.0]                                 "
"      ]                                              "
"    ],                                               "
"    [                                                "
"      [                                              "
"        [100.0, 0.0],                                "
"        [101.0, 0.0],                                "
"        [101.0, 1.0],                                "
"        [100.0, 1.0],                                "
"        [100.0, 0.0]                                 "
"      ]                                              "
"    ]                                                "
"  ]                                                  "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MultiPolygon\", \"coordinates\":     "
"  [                                                  "
"    [                                                "
"      [                                              "
" 
MULTIPOLYGON(((2 102,2 103,3 103,3 102,2 102)),((0 100,0 101,1 101,1 100,0 100)))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MultiPolygon\", \"coordinates\":     "
"  [                                                  "
"    [                                                "
"      [                                              "
"        [102.0, 2.0],                                "
"        [103.0, 2.0],                                "
"        [103.0, 3.0],                                "
"        [102.0, 3.0],                                "
"        [102.0, 2.0]                                 "
"      ],                                             "
"      [                                              "
"        [1.0, 0.0],                                  "
"        [0.0, 0.0],                                  "
"        [0.0, 1.0],                                  "
"        [1.0, 0.0]                                   "
"      ]                                              "
"    ],                                               "
"    [                                                "
"      [                                              "
"        [100.0, 0.0],                                "
"        [101.0, 0.0],                                "
"        [101.0, 1.0],                                "
"        [100.0, 1.0],                                "
"        [100.0, 0.0]                                 "
"      ]                                              "
"    ]                                                "
"  ]                                                  "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MultiPolygon\", \"coordinates\":     "
"  [                                                  "
"    [                                                "
"      [                                              "
" 
MULTIPOLYGON(((2 102,2 103,3 103,3 102,2 102),(0 1,0 0,1 0,0 1)),((0 100,0 101,1 101,1 100,0 100)))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":    "
"    [ ]                                                  "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":    "
"    [ ]                                                  "
"  }"))
GEOMETRYCOLLECTION EMPTY
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":          "
"    [                                                          "
"      { \"type\": \"GeometryCollection\", \"geometries\":      "
"        [                                                      "
"          { \"type\": \"GeometryCollection\", \"geometries\":  "
"            [ ]                                                "
"          }                                                    "
"        ]                                                      "
"      }                                                        "
"    ]                                                          "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":          "
"    [                                                          "
"      { \"type\": \"GeometryCollection\", \"geometries\":      "
"        [                  
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION EMPTY))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":    "
"    [                                                    "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ]                                                "
"      },                                                 "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ]                                                "
"      }                                                  "
"    ]                                                    "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":    "
"    [                                                    "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                    
GEOMETRYCOLLECTION(LINESTRING(0 102,1 103,0 104,1 105),LINESTRING(0 102,1 103,0 104,1 105))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":    "
"    [                                                    "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ]                                                "
"      },                                                 "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ]                                                "
"      },                                                 "
"      { \"type\": \"Point\", \"coordinates\":            "
"        [102.0, 0.0]                                     "
"      }                                                  "
"    ]                                                    "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":    "
"    [                                                    "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                    
GEOMETRYCOLLECTION(LINESTRING(0 102,1 103,0 104,1 105),LINESTRING(0 102,1 103,0 104,1 105),POINT(0 102))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":      "
"    [                                                      "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                                       "
"      },                                                   "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                                       "
"      },                                                   "
"      { \"type\": \"GeometryCollection\", \"geometries\":  "
"        [                                                  "
"          { \"type\": \"Point\", \"coordinates\":          "
"            [102.0, 0.5]                                   "
"          },                                               "
"          { \"type\": \"Point\", \"coordinates\":          "
"            [102.0, 0.5]                                   "
"          }                                                "
"        ]                                                  "
"      },                                                   "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                                       "
"      }                                                    "
"    ]                                                      "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":      "
"    [                                                      "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                   
GEOMETRYCOLLECTION(POINT(0.5 102),POINT(0.5 102),GEOMETRYCOLLECTION(POINT(0.5 102),POINT(0.5 102)),POINT(0.5 102))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":      "
"    [                                                      "
"      { \"type\": \"MultiPoint\", \"coordinates\":         "
"        [                                                  "
"          [-105.01, 39.57],                                "
"          [-80.66, 35.0]                                   "
"        ]                                                  "
"      },                                                   "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                                       "
"      },                                                   "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                                       "
"      },                                                   "
"      { \"type\": \"GeometryCollection\", \"geometries\":  "
"        [                                                  "
"          { \"type\": \"Point\", \"coordinates\":          "
"            [102.0, 0.5]                                   "
"          },                                               "
"          { \"type\": \"Point\", \"coordinates\":          "
"            [102.0, 0.5]                                   "
"          }                                                "
"        ]                                                  "
"      },                                                   "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                                       "
"      }                                                    "
"    ]                                                      "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":      "
"    [                                                      "
"      { \"type\": \"MultiPoint\", \"coordinates\":         "
"        [                              
GEOMETRYCOLLECTION(MULTIPOINT((39.57 -105.01),(35 -80.66)),POINT(0.5 102),POINT(0.5 102),GEOMETRYCOLLECTION(POINT(0.5 102),POINT(0.5 102)),POINT(0.5 102))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":      "
"  [                                                        "
"    { \"type\": \"Point\", \"coordinates\":                "
"      [-105.01621, 39.57422]                               "
"    },                                                     "
"    { \"type\": \"MultiPoint\", \"coordinates\":           "
"      [                                                    "
"        [-105.01, 39.57],                                  "
"        [-80.66, 35.05]                                    "
"      ]                                                    "
"    },                                                     "
"    { \"type\": \"LineString\", \"coordinates\":           "
"      [                                                    "
"        [-101.5, 39.662],                                  "
"        [-101.75, 39.2415],                                "
"        [-101.64, 39.2415]                                 "
"      ]                                                    "
"    },                                                     "
"    { \"type\": \"MultiLineString\", \"coordinates\":      "
"      [                                                    "
"        [                                                  "
"          [-101.5, 39.662],                                "
"          [-101.75, 39.2415],                              "
"          [-101.23, 39.2415],                              "
"          [-101.749, 39.7984],                             "
"          [-101.5, 39.011]                                 "
"        ],                                                 "
"        [                                                  "
"          [-99.23, 38.6605],                               "
"          [-99.56, 38.727],                                "
"          [-99.25, 38.018]                                 "
"        ],                                                 "
"        [                                                  "
"          [-98.499, 38.913],                               "
"          [-98.499, 38.913],                               "
"          [-98.38, 38.15],                                 "
"          [-97.5, 38.629]                                  "
"        ]                                                  "
"      ]                                                    "
"    },                                                     "
"    { \"type\": \"Polygon\", \"coordinates\":              "
"      [                                                    "
"        [                                                  "
"          [41.83,71.01],                                   "
"          [56.95,33.75],                                   "
"          [21.79,36.56],                                   "
"          [41.83,71.01]                                    "
"        ]                                                  "
"      ]                                                    "
"    },                                                     "
"    {   \"type\": \"MultiPolygon\", \"coordinates\":       "
"      [                                                    "
"        [                                                  "
"          [                                                "
"            [102.0, 2.0],                                  "
"            [103.0, 2.0],                                  "
"            [103.0, 3.0],                                  "
"            [102.0, 3.0],                                  "
"            [102.0, 2.0]                                   "
"          ]                                                "
"        ],                                                 "
"        [                                                  "
"          [                                                "
"            [100.0, 0.0],                                  "
"            [101.0, 0.0],                                  "
"            [101.0, 1.0],                                  "
"            [100.0, 1.0],                                  "
"            [100.0, 0.0]                                   "
"          ]                                                "
"        ]                                                  "
"      ]                                                    "
"    }                                                      "
"  ]                                                        "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":      "
"  [                                                        "
"    { \"type\": \"Point\", \"coordinates\":                "
"      [-105.01621, 39.57422]           
GEOMETRYCOLLECTION(POINT(39.57422 -105.01621),MULTIPOINT((39.57 -105.01),(35.05 -80.66)),LINESTRING(39.662 -101.5,39.2415 -101.75,39.2415 -101.64),MULTILINESTRING((39.662 -101.5,39.2415 -101.75,39.2415 -101.23,39.7984 -101.749,39.011 -101.5),(38.6605 -99.23,38.727 -99.56,38.018 -99.25),(38.913 -98.499,38.913 -98.499,38.15 -98.38,38.629 -97.5)),POLYGON((71.01 41.83,33.75 56.95,36.56 21.79,71.01 41.83)),MULTIPOLYGON(((2 102,2 103,3 103,3 102,2 102)),((0 100,0 101,1 101,1 100,0 100))))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  {                                                              "
"  \"type\": \"Feature\", \"id\": \"stadium\", \"geometry\":      "
"    { \"type\": \"Point\", \"coordinates\":                      "
"      [-104.99404, 39.75621]                                     "
"    },                                                           "
"  \"properties\":                                                "
"    {                                                            "
"      \"name\": \"Coors Field\",                                 "
"      \"amenity\": \"Baseball Stadium\",                         "
"      \"popupContent\": \"This is where the Rockies play!\"      "
"    }                                                            "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  {                                                              "
"  \"type\": \"Feature\", \"id\": \"stadium\", \"geometry\":      "
"    { \"type\": \"Point\", \"coordinates\":                      "
"      [-104.99404, 39
POINT(39.75621 -104.99404)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"FeatureCollection\", \"features\":     "
"    [                                                  "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83775386582222, 35.24980190252168]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS CENTER\",            "
"          \"address\": \"1326 WOODWARD AV\"            "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83827000459532, 35.25674709224663]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS NEIGHBORHOOD PARK\", "
"          \"address\": \"2605  DOUBLE OAKS RD\"        "
"        }                                              "
"      }                                                "
"    ]                                                  "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"FeatureCollection\", \"features\":     "
"    [                                                  "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":    
GEOMETRYCOLLECTION(POINT(35.24980190252168 -80.83775386582222),POINT(35.25674709224663 -80.83827000459532))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  {   \"type\": \"FeatureCollection\", \"features\":   "
"    [                                                  "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83775386582222, 35.24980190252168]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS CENTER\",            "
"          \"address\": \"1326 WOODWARD AV\"            "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83827000459532, 35.25674709224663]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS NEIGHBORHOOD PARK\", "
"          \"address\": \"2605  DOUBLE OAKS RD\"        "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        null,                                          "
"      \"properties\":                                  "
"        {                                              "
"          \"descr\": \"A NULL/EMPTY POINT\"            "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-87.83827000459532, 42.0]                   "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS NEIGHBORHOOD PARK\", "
"          \"address\": \"2605  DOUBLE OAKS RD\"        "
"        }                                              "
"      }                                                "
"    ]                                                  "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  {   \"type\": \"FeatureCollection\", \"features\":   "
"    [                                                  "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":    
GEOMETRYCOLLECTION(POINT(35.24980190252168 -80.83775386582222),POINT(35.25674709224663 -80.83827000459532),POINT(42 -87.83827000459532))
# Checking that E notation is supported.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [1.23e3, 2.2e-1]}", 1, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [1.23e3, 2.2e-1]}", 1, 0))
POINT(1230 0.22)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [1e-300, 1e300]}", 1, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [1e-300, 1e300]}", 1, 0))
POINT(1e-300 1e300)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [1.7976931348623157E+308, -1.7976931348623157E+308]}", 1, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [1.7976931348623157E+308, -1.7976931348623157E+308]}", 1, 0))
POINT(1.7976931348623157e308 -1.7976931348623157e308)
# Checking for NULL arguments and GeoJSON null objects
SELECT ST_GEOMFROMGEOJSON(NULL);
ST_GEOMFROMGEOJSON(NULL)
NULL
SELECT ST_GEOMFROMGEOJSON(NULL, 1);
ST_GEOMFROMGEOJSON(NULL, 1)
NULL
SELECT ST_GEOMFROMGEOJSON("{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}",
NULL);
ST_GEOMFROMGEOJSON("{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}",
NULL)
NULL
SELECT ST_GEOMFROMGEOJSON(NULL, NULL, NULL);
ST_GEOMFROMGEOJSON(NULL, NULL, NULL)
NULL
SELECT ST_GEOMFROMGEOJSON("{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}",
NULL, NULL);
ST_GEOMFROMGEOJSON("{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}",
NULL, NULL)
NULL
SELECT ST_GEOMFROMGEOJSON(NULL, 1, NULL);
ST_GEOMFROMGEOJSON(NULL, 1, NULL)
NULL
SELECT ST_GEOMFROMGEOJSON(NULL, NULL, 4326);
ST_GEOMFROMGEOJSON(NULL, NULL, 4326)
NULL
SELECT ST_GEOMFROMGEOJSON("{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}",
1, NULL);
ST_GEOMFROMGEOJSON("{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}",
1, NULL)
NULL
SELECT ST_GEOMFROMGEOJSON("{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}",
NULL, 4326);
ST_GEOMFROMGEOJSON("{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}",
NULL, 4326)
NULL
SELECT ST_GEOMFROMGEOJSON(NULL, 1, 4326);
ST_GEOMFROMGEOJSON(NULL, 1, 4326)
NULL
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Feature\", \"geometry\": null,   \"properties\":     "
"    { \"foo\": \"bar\" }                                             "
"  }");
ST_GEOMFROMGEOJSON(
"  { \"type\": \"Feature\", \"geometry\": null,   \"properties\":     "
"    { \"foo\": \"bar\" }                                             "
"  }")
NULL
# Checking for case sensitivity/insensitivity
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  {                                                              "
"  \"type\": \"Feature\", \"GeOmEtRy\":                           "
"    { \"TYPE\": \"Point\", \"COORDINATES\":                      "
"      [-104.99404, 39.75621]                                     "
"    },                                                           "
"  \"PrOPERtiES\":                                                "
"    {                                                            "
"      \"name\": \"Coors Field\",                                 "
"      \"amenity\": \"Baseball Stadium\",                         "
"      \"popupContent\": \"This is where the Rockies play!\"      "
"    }                                                            "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  {                                                              "
"  \"type\": \"Feature\", \"GeOmEtRy\":                           "
"    { \"TYPE\": \"Point\", \"COORDINATES\":                      "
"      [-104.99404, 39
POINT(39.75621 -104.99404)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"TyPe\": \"Point\", \"cOoRdInAtEs\": [102.0, 0.0]}"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"TyPe\": \"Point\", \"cOoRdInAtEs\": [102.0, 0.0]}"))
POINT(0 102)
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"CRS\":                                           "
"        { \"TYPE\": \"name\", \"PROPERTIES\":            "
"          { \"NAME\": \"urn:ogc:def:crs:EPSG::1234\" }   "
"        }                                                "
"  }");
ERROR SR001: There's no spatial reference system with SRID 1234.
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { \"name\": \"urn:ogc:def:crs:OGC:1.3:crs84\" }         "
"        }                                                         "
"  }", 1));
ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { \"name\"
4326
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { \"name\": \"urn:ogc:def:crs:OGC:1.3:crs84\" }         "
"        }                                                         "
"  }", 1, 10));
ERROR SR001: There's no spatial reference system with SRID 10.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"point\", \"coordinates\": [102.0, 0.0]}"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"multiPoint\", \"coordinates\": [102.0, 0.0]}"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Linestring\", \"coordinates\": [102.0, 0.0]}"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"multiLinestring\", \"coordinates\": [102.0, 0.0]}"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"polygon\", \"coordinates\": [102.0, 0.0]}"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"MultiPolyGoN\", \"coordinates\": [102.0, 0.0]}"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"GEOMETRYCOLLECTION\", \"coordinates\": [102.0, 0.0]}"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
# Checking invalid/incorrect JSON and GeoJSON inputs
SELECT ST_GEOMFROMGEOJSON("");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "The document is empty." at position 0.
SELECT ST_GEOMFROMGEOJSON("{}");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'type'
SELECT ST_GEOMFROMGEOJSON("()");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid value." at position 0.
SELECT ST_GEOMFROMGEOJSON("[]");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON("NULL");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid value." at position 0.
SELECT ST_GEOMFROMGEOJSON(
"{                                                                        "
"  \"type\": \"FeatureCollection\",                                       "
"  \"features\": [                                                        "
"    {                                                                    "
"      \"type\": \"FeatureCollection\",                                   "
"      \"features\": []                                                   "
"    },                                                                   "
"    {                                                                    "
"      \"type\": \"Feature\",                                             "
"      \"geometry\": { \"type\": \"Point\", \"coordinates\": [10, 10] },  "
"      \"properties\": null                                               "
"    }                                                                    "
"  ]                                                                      "
"}");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(CAST(
"{                                                                        "
"  \"type\": \"FeatureCollection\",                                       "
"  \"features\": [                                                        "
"    {                                                                    "
"      \"type\": \"FeatureCollection\",                                   "
"      \"features\": []                                                   "
"    },                                                                   "
"    {                                                                    "
"      \"type\": \"Feature\",                                             "
"      \"geometry\": { \"type\": \"Point\", \"coordinates\": [10, 10] },  "
"      \"properties\": null                                               "
"    }                                                                    "
"  ]                                                                      "
"}" AS BINARY));
ERROR HY000: Incorrect type for argument geojson in function st_geomfromgeojson.
SELECT ST_GEOMFROMGEOJSON(CAST(
"{                                                                        "
"  \"type\": \"FeatureCollection\",                                       "
"  \"features\": [                                                        "
"    {                                                                    "
"      \"type\": \"FeatureCollection\",                                   "
"      \"features\": []                                                   "
"    },                                                                   "
"    {                                                                    "
"      \"type\": \"Feature\",                                             "
"      \"geometry\": { \"type\": \"Point\", \"coordinates\": [10, 10] },  "
"      \"properties\": null                                               "
"    }                                                                    "
"  ]                                                                      "
"}" AS DATE));
ERROR HY000: Incorrect type for argument geojson in function st_geomfromgeojson.
# Checking correct handling of different coordinate dimensions (option one (default option))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [] }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10] }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }"))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }"));
ERROR HY000: Unsupported number of coordinate dimensions in function st_geomfromgeojson: Found 3, expected 2
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }"));
ERROR HY000: Unsupported number of coordinate dimensions in function st_geomfromgeojson: Found 4, expected 2
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [] }", 1));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10] }", 1));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", 1));
ERROR HY000: Unsupported number of coordinate dimensions in function st_geomfromgeojson: Found 3, expected 2
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", 1));
ERROR HY000: Unsupported number of coordinate dimensions in function st_geomfromgeojson: Found 4, expected 2
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [] }", "1"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10] }", "1"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "1"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "1"))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", "1"));
ERROR HY000: Unsupported number of coordinate dimensions in function st_geomfromgeojson: Found 3, expected 2
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", "1"));
ERROR HY000: Unsupported number of coordinate dimensions in function st_geomfromgeojson: Found 4, expected 2
# Checking correct handling of different coordinate dimensions (option two)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [] }", 2));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10] }", 2));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 2));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 2))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", 2));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", 2))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", 2));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", 2))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [] }", "2"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10] }", "2"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "2"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "2"))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", "2"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", "2"))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", "2"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", "2"))
POINT(15 10)
# Checking correct handling of different coordinate dimensions (option three)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [] }", 3));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10] }", 3));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 3));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 3))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", 3));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", 3))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", 3));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", 3))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [] }", "3"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10] }", "3"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "3"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "3"))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", "3"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", "3"))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", "3"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", "3"))
POINT(15 10)
# Checking correct handling of different coordinate dimensions (option four)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [] }", 4));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10] }", 4));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 4));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 4))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", 4));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", 4))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", 4));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", 4))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [] }", "4"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10] }", "4"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "4"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "4"))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", "4"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", "4"))
POINT(15 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", "4"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", "4"))
POINT(15 10)
# Checking with valid ST_SRID value
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, 0))
POINT(10 15)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", 2, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", 2, 0))
POINT(10 15)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", 3, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20] }", 3, 0))
POINT(10 15)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", 4, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15, 20, 25] }", 4, 0))
POINT(10 15)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, "0"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, "0"))
POINT(10 15)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}", SUM(1), SUM(4326)));
ERROR HY000: Incorrect type for argument SRID in function st_geomfromgeojson.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}", AVG(1), AVG(4326)));
ERROR HY000: Incorrect type for argument SRID in function st_geomfromgeojson.
# Various GeoJSON specification details
SELECT ST_GEOMFROMGEOJSON("{ type: \"Point\", \"coordinates\": [42, 42] }");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a name for object member." at position 2.
SELECT ST_GEOMFROMGEOJSON(
"  { \"label\": \"foo\", \"data\":            "
"    { \"type\": \"Point\", \"coordinates\":  "
"      [10, 12]                               "
"    }                                        "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'type'
SELECT ST_GEOMFROMGEOJSON("{ \"type\": Point, \"coordinates\": [10, 10] }");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid value." at position 10.
SELECT ST_GEOMFROMGEOJSON("{ \"type\": [ \"Point\" ], \"coordinates\": [10, 10] }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'type' must be of type 'string'
SELECT ST_GEOMFROMGEOJSON("{ \"typ\": \"Point\", \"coordinates\": [10, 10] }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'type'
# Checking "type" member with invalid string
SELECT ST_GEOMFROMGEOJSON("{ \"type\": \"point\", \"coordinates\": [10, 10] }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON("{ \"type\": \"POINT\", \"coordinates\": [10, 10] }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"linestring\", \"coordinates\": "
  "[ [102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0] ] }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"LINESTRING\", \"coordinates\": "
  "[ [102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0] ] }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"polygon\", \"coordinates\":      "
"  [                                              "
"    [                                            "
"      [41.83, 71.01],                            "
"      [56.95, 33.75],                            "
"      [21.79, 36.56],                            "
"      [41.83, 71.01]                             "
"    ]                                            "
"  ]                                              "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"POLYGON\", \"coordinates\":      "
"  [                                              "
"    [                                            "
"      [41.83, 71.01],                            "
"      [56.95, 33.75],                            "
"      [21.79, 36.56],                            "
"      [41.83, 71.01]                             "
"    ]                                            "
"  ]                                              "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"multipoint\", \"coordinates\": "
  "[ [-105.01,39.57],[-80.66,35.0] ] }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"MULTIPOINT\", \"coordinates\": "
  "[ [-105.01,39.57],[-80.66,35.0] ] }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"multilinestring\", \"coordinates\":      "
"  [                                                      "
"    [                                                    "
"      [-101.5, 39.662],                                  "
"      [-101.75, 39.2415],                                "
"      [-101.23, 39.2415],                                "
"      [-101.749, 39.7984],                               "
"      [-101.5, 39.011]                                   "
"    ],                                                   "
"    [                                                    "
"      [-99.23, 38.6605],                                 "
"      [-99.56, 38.727],                                  "
"      [-99.25, 38.018]                                   "
"    ],                                                   "
"    [                                                    "
"      [-98.499, 38.913],                                 "
"      [-98.499, 38.913],                                 "
"      [-98.38, 38.15],                                   "
"      [-97.5, 38.629]                                    "
"    ]                                                    "
"  ]                                                      "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MULTILINESTRING\", \"coordinates\":      "
"  [                                                      "
"    [                                                    "
"      [-101.5, 39.662],                                  "
"      [-101.75, 39.2415],                                "
"      [-101.23, 39.2415],                                "
"      [-101.749, 39.7984],                               "
"      [-101.5, 39.011]                                   "
"    ],                                                   "
"    [                                                    "
"      [-99.23, 38.6605],                                 "
"      [-99.56, 38.727],                                  "
"      [-99.25, 38.018]                                   "
"    ],                                                   "
"    [                                                    "
"      [-98.499, 38.913],                                 "
"      [-98.499, 38.913],                                 "
"      [-98.38, 38.15],                                   "
"      [-97.5, 38.629]                                    "
"    ]                                                    "
"  ]                                                      "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"multipolygon\", \"coordinates\":     "
"  [                                                  "
"    [                                                "
"      [                                              "
"        [102.0, 2.0],                                "
"        [103.0, 2.0],                                "
"        [103.0, 3.0],                                "
"        [102.0, 3.0],                                "
"        [102.0, 2.0]                                 "
"      ]                                              "
"    ],                                               "
"    [                                                "
"      [                                              "
"        [100.0, 0.0],                                "
"        [101.0, 0.0],                                "
"        [101.0, 1.0],                                "
"        [100.0, 1.0],                                "
"        [100.0, 0.0]                                 "
"      ]                                              "
"    ]                                                "
"  ]                                                  "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MULTIPOLYGON\", \"coordinates\":     "
"  [                                                  "
"    [                                                "
"      [                                              "
"        [102.0, 2.0],                                "
"        [103.0, 2.0],                                "
"        [103.0, 3.0],                                "
"        [102.0, 3.0],                                "
"        [102.0, 2.0]                                 "
"      ]                                              "
"    ],                                               "
"    [                                                "
"      [                                              "
"        [100.0, 0.0],                                "
"        [101.0, 0.0],                                "
"        [101.0, 1.0],                                "
"        [100.0, 1.0],                                "
"        [100.0, 0.0]                                 "
"      ]                                              "
"    ]                                                "
"  ]                                                  "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"geometrycollection\", \"geometries\":    "
"    [                                                    "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ]                                                "
"      },                                                 "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ]                                                "
"      }                                                  "
"    ]                                                    "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GEOMETRYCOLLECTION\", \"geometries\":    "
"    [                                                    "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ]                                                "
"      },                                                 "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ]                                                "
"      }                                                  "
"    ]                                                    "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  {                                                              "
"  \"type\": \"feature\", \"id\": \"stadium\", \"geometry\":      "
"    { \"type\": \"Point\", \"coordinates\":                      "
"      [-104.99404, 39.75621]                                     "
"    },                                                           "
"  \"properties\":                                                "
"    {                                                            "
"      \"name\": \"Coors Field\",                                 "
"      \"amenity\": \"Baseball Stadium\",                         "
"      \"popupContent\": \"This is where the Rockies play!\"      "
"    }                                                            "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  {                                                              "
"  \"type\": \"FEATURE\", \"id\": \"stadium\", \"geometry\":      "
"    { \"type\": \"Point\", \"coordinates\":                      "
"      [-104.99404, 39.75621]                                     "
"    },                                                           "
"  \"properties\":                                                "
"    {                                                            "
"      \"name\": \"Coors Field\",                                 "
"      \"amenity\": \"Baseball Stadium\",                         "
"      \"popupContent\": \"This is where the Rockies play!\"      "
"    }                                                            "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"featurecollection\", \"features\":     "
"    [                                                  "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83775386582222, 35.24980190252168]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS CENTER\",            "
"          \"address\": \"1326 WOODWARD AV\"            "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83827000459532, 35.25674709224663]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS NEIGHBORHOOD PARK\", "
"          \"address\": \"2605  DOUBLE OAKS RD\"        "
"        }                                              "
"      }                                                "
"    ]                                                  "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"FEATURECOLLECTION\", \"features\":     "
"    [                                                  "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83775386582222, 35.24980190252168]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS CENTER\",            "
"          \"address\": \"1326 WOODWARD AV\"            "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83827000459532, 35.25674709224663]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS NEIGHBORHOOD PARK\", "
"          \"address\": \"2605  DOUBLE OAKS RD\"        "
"        }                                              "
"      }                                                "
"    ]                                                  "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON("{ \"type\": \"Feature\", \"coordinates\": [10, 10] }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'geometry'
SELECT ST_GEOMFROMGEOJSON("{ \"type\": \"FeatureCollection\", \"coordinates\": [10, 10] }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'features'
SELECT ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\",\"coordinates\": \"[42, 42]\" }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'coordinates' must be of type 'array'
SELECT ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\",\"cordinates\": [42, 42] }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'coordinates'
SELECT ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\",\"cordinates\": \"42, 42\" }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'coordinates'
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometrie\":     "
"    [                                                    "
"      { \"type\": \"Point\", \"coordinates\":            "
"        [0, 0]                                           "
"      }                                                  "
"    ]                                                    "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'geometries'
SELECT ST_GEOMFROMGEOJSON("{ \"type\": \"GeometryCollection\", \"geometries\": { \"type\": \"Point\", \"coordinates\": [0, 0] } }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'geometries' must be of type 'array'
SELECT ST_GEOMFROMGEOJSON("{ \"type\": \"Feature\", \"geometry\": { \"type\": \"Point\", \"coordinates\": [0, 0] } }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'properties'
# Testing CRS Parsing and ST_SRID handling
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 20] }"));
ST_SRID(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 20] }"))
4326
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"urn:ogc:def:crs:EPSG::1234\" }   "
"        }                                                "
"  }"));
ERROR SR001: There's no spatial reference system with SRID 1234.
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"urn:ogc:def:crs:EPSG::1\" }      "
"        }                                                "
"  }"));
ERROR SR001: There's no spatial reference system with SRID 1.
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],            "
"      \"crs\":                                                 "
"        { \"type\": \"name\", \"properties\":                  "
"          { \"name\": \"urn:ogc:def:crs:EPSG::4294967295\" }   "
"        }                                                      "
"  }"));
ERROR SR001: There's no spatial reference system with SRID 4294967295.
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPSG:234\" }                     "
"        }                                                "
"  }"));
ERROR SR001: There's no spatial reference system with SRID 234.
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPSG:1\" }                       "
"        }                                                "
"  }"));
ERROR SR001: There's no spatial reference system with SRID 1.
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPSG:4294967295\" }              "
"        }                                                "
"  }"));
ERROR SR001: There's no spatial reference system with SRID 4294967295.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\": null                                      "
"  }"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\": null                                      "
"  }"))
POINT(20 10)
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPSG:4294967296\" }              "
"        }                                                "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPSG:0\" }                       "
"        }                                                "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPSG:-1\" }                      "
"        }                                                "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPSG:-2147483648\" }             "
"        }                                                "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"urn:ogc:def:crs:EPSG::0\" }      "
"        }                                                "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  {   \"type\": \"Point\", \"coordinates\": [10, 20],    "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"urn:ogc:def:crs:EPSG::-1\" }     "
"        }                                                "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],            "
"      \"crs\":                                                 "
"        { \"type\": \"name\", \"properties\":                  "
"          { \"name\": \"urn:ogc:def:crs:EPSG::-2147483648\" }  "
"        }                                                      "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],            "
"      \"crs\":                                                 "
"        { \"type\": \"name\", \"properties\":                  "
"          { \"name\": \"urn:ogc:def:crs:EPSG:4326\" }          "
"        }                                                      "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],            "
"      \"crs\":                                                 "
"        { \"type\": \"name\", \"properties\":                  "
"          { \"name\": \"ogc:def:crs:EPSG::4326\" }             "
"        }                                                      "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],            "
"      \"crs\":                                                 "
"        { \"type\": \"name\", \"properties\":                  "
"          { \"name\": \"ogc:def:crs:EPSG::\" }                 "
"        }                                                      "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"urn:ogc:def:crs:EPSG::\" }       "
"        }                                                "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"\" }                             "
"        }                                                "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPSG=1\" }                       "
"        }                                                "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \":1\" }                           "
"        }                                                "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPS:1\" }                        "
"        }                                                "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"link\", \"properties\":            "
"          { \"href\": \"http://example.com/crs/42\",     "
"            \"type\": \"proj4\"                          "
"          }                                              "
"        }                                                "
"  }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPS:1\" }                        "
"        }                                                "
"  } ", 1 , 4326 ));
ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPS:1\" }              
4326
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],            "
"      \"crs\":                                                 "
"        { \"type\": \"name\", \"properties\":                  "
"          { \"name\": \"urn:ogc:def:crs:EPSG::1\" }            "
"        }                                                      "
"  }", 1, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],            "
"      \"crs\":                                                 "
"        { \"type\": \"name\", \"properties\":                  "
"          { \"name\": \"urn
POINT(10 20)
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],            "
"      \"crs\":                                                 "
"        { \"type\": \"name\", \"properties\":                  "
"          { \"name\": \"urn:ogc:def:crs:EPSG::0\" }            "
"        }                                                      "
"  }", 1, 4326));
ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],            "
"      \"crs\":                                                 "
"        { \"type\": \"name\", \"properties\":                  "
"          { \"name\": \"urn:o
4326
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { \"name\": \"urn:ogc:def:crs:OGC:1.3:CRS100\" }        "
"        }                                                         "
"  }", 1));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { \"name\": \"urn:ogc:def:crs:OGC:1.30:CRS84\" }        "
"        }                                                         "
"  }", 1));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { : \"urn:ogc:def:crs:OGC:1.3:CRS84\" }                 "
"        }                                                         "
"  }", 1));
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a name for object member." at position 196.
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { \"name\": { \"urn:ogc:def:crs:OGC:1.3:CRS84\" } }     "
"        }                                                         "
"  }", 1));
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a colon after a name of object member." at position 238.
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\":                                     "
"          { \"name\": \"urn:ogc:def:crs:OGC:1.3:CRS84\" }         "
"        }                                                         "
"  }", 1));
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a comma or '}' after an object member." at position 148.
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"properties\":                               "
"          { \"name\": \"urn:ogc:def:crs:OGC:1.3:CRS84\" }         "
"        }                                                         "
"  }", 1));
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a comma or '}' after an object member." at position 154.
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { \"name\": \" \" }                                     "
"        }                                                         "
"  }", 1));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],            "
"      \"crs\":                                                 "
"        [                                                      "
"          { \"type\": \"name\", \"properties\":                "
"            { \"name\": \"urn:ogc:def:crs:EPSG::1\" }          "
"          }                                                    "
"        ]                                                      "
"  }", 1));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'crs' must be of type 'object'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],            "
"      \"crs\":                                                 "
"        { \"type\": \"name\", \"properties\":                  "
"          {  }                                                 "
"        }                                                      "
"  }", 1));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'name'
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":    "
"    [                                                    "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ],                                               "
"        \"crs\":                                         "
"          { \"type\": \"name\", \"properties\":          "
"            { \"name\": \"urn:ogc:def:crs:EPSG::2000\" } "
"          }                                              "
"      },                                                 "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ],                                               "
"        \"crs\":                                         "
"          { \"type\": \"name\", \"properties\":          "
"            { \"name\": \"urn:ogc:def:crs:EPSG::4326\" } "
"          }                                              "
"      }                                                  "
"    ]                                                    "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'crs' must be specified in the top level object.
SELECT ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 20], \"crs\": { } }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'type'
SELECT ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, \"20\"] } ");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'array coordinate' must be of type 'number'
SELECT ST_GEOMFROMGEOJSON("", -9223372036854775808);
ERROR HY000: Incorrect option value: '-9223372036854775808' for function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON("", 9223372036854775807);
ERROR HY000: Incorrect option value: '9223372036854775807' for function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON("", 9223372036854775808);
ERROR HY000: Incorrect option value: '9223372036854775808' for function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON("", 1, -9223372036854775808);
ERROR 22003: SRID value is out of range in 'st_geomfromgeojson'
SELECT ST_GEOMFROMGEOJSON("", 1, 9223372036854775807);
ERROR 22003: SRID value is out of range in 'st_geomfromgeojson'
SELECT ST_GEOMFROMGEOJSON("", 1, 9223372036854775808);
ERROR 22003: SRID value is out of range in 'st_geomfromgeojson'
# Checking invalid/incorrect option parameter input
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 0));
ERROR HY000: Incorrect option value: '0' for function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", -10));
ERROR HY000: Incorrect option value: '-10' for function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", -1000));
ERROR HY000: Incorrect option value: '-1000' for function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 5));
ERROR HY000: Incorrect option value: '5' for function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 100));
ERROR HY000: Incorrect option value: '100' for function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 10000));
ERROR HY000: Incorrect option value: '10000' for function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "0"));
ERROR HY000: Incorrect option value: '0' for function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "-10"));
ERROR HY000: Incorrect option value: '-10' for function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "-1000"));
ERROR HY000: Incorrect option value: '-1000' for function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "5"));
ERROR HY000: Incorrect option value: '5' for function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "100"));
ERROR HY000: Incorrect option value: '100' for function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", "10000"));
ERROR HY000: Incorrect option value: '10000' for function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1.1));
ERROR HY000: Incorrect type for argument options in function st_geomfromgeojson.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", (CAST(1 AS DECIMAL))));
ERROR HY000: Incorrect type for argument options in function st_geomfromgeojson.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", (CAST(1 AS BINARY))));
ERROR HY000: Incorrect type for argument options in function st_geomfromgeojson.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", a));
ERROR 42S22: Unknown column 'a' in 'field list'
# Checking invalid/incorrect ST_SRID parameter input
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, -1));
ERROR 22003: SRID value is out of range in 'st_geomfromgeojson'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, -1000));
ERROR 22003: SRID value is out of range in 'st_geomfromgeojson'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, 4294967296));
ERROR 22003: SRID value is out of range in 'st_geomfromgeojson'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, 4294967295000));
ERROR 22003: SRID value is out of range in 'st_geomfromgeojson'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, "-1"));
ERROR 22003: SRID value is out of range in 'st_geomfromgeojson'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, "-1000"));
ERROR 22003: SRID value is out of range in 'st_geomfromgeojson'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, "4294967296"));
ERROR 22003: SRID value is out of range in 'st_geomfromgeojson'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, "4294967295000"));
ERROR 22003: SRID value is out of range in 'st_geomfromgeojson'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, 1.1));
ERROR HY000: Incorrect type for argument SRID in function st_geomfromgeojson.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, (CAST(1 AS DECIMAL))));
ERROR HY000: Incorrect type for argument SRID in function st_geomfromgeojson.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, (CAST(1 AS BINARY))));
ERROR HY000: Incorrect type for argument SRID in function st_geomfromgeojson.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, a));
ERROR 42S22: Unknown column 'a' in 'field list'
# Invalid function calls
SELECT ST_GEOMFROMGEOJSON("{ \"type\": \"Feature\", \"geometry\": [10, 20] }");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'geometry' must be of type 'object'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON());
ERROR 42000: Incorrect parameter count in the call to native function 'ST_GEOMFROMGEOJSON'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }", 1, 0, 2));
ERROR 42000: Incorrect parameter count in the call to native function 'ST_GEOMFROMGEOJSON'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\":: \"Point\", \"coordinates\": [10, 15] }", 1, 0));
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid value." at position 9.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": [ \"Point\" ], \"coordinates\": [10, 15] }", 1, 0));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'type' must be of type 'string'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\":  }", 1, 0));
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid value." at position 11.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Geometry\", \"coordinates\": [10, 15] }", 1, 0));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": { \"Point\" }, \"coordinates\": [10, 15] }", 1, 0));
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a colon after a name of object member." at position 20.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"LineString\", \"coordinates\": [[100.0, 0.0, 1.0, 2.0], [101.0, 0.0]] }"));
ERROR HY000: Unsupported number of coordinate dimensions in function st_geomfromgeojson: Found 4, expected 2
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"    [                                            "
"      [                                          "
"        [100.0, 0.0, 1.0],                       "
"        [101.0, 0.0],                            "
"        [101.0, 1.0],                            "
"        [100.0, 0.0]                             "
"      ]                                          "
"    ]                                            "
"  }"));
ERROR HY000: Unsupported number of coordinate dimensions in function st_geomfromgeojson: Found 3, expected 2
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  {                                                              "
"  \"type\": \"Feature\", \"Point\":                              "
"    { \"type\": \"Point\", \"coordinates\":                      "
"      [-104.99404, 39.75621]                                     "
"    },                                                           "
"  \"properties\":                                                "
"    {                                                            "
"      \"name\": \"Coors Field\",                                 "
"      \"amenity\": \"Baseball Stadium\",                         "
"      \"popupContent\": \"This is where the Rockies play!\"      "
"    }                                                            "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'geometry'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  {                                                              "
"  \"type\": \"Feature\", \"name\": , \"geometry\":               "
"    { \"type\": \"Point\", \"coordinates\":                      "
"      [-104.99404, 39.75621]                                     "
"    },                                                           "
"  \"properties\":                                                "
"    {                                                            "
"      \"name\": \"Coors Field\",                                 "
"      \"amenity\": \"Baseball Stadium\",                         "
"      \"popupContent\": \"This is where the Rockies play!\"      "
"    }                                                            "
"  }"));
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid value." at position 94.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  {                                                              "
"  \"type\": \"Feature\", \"geometry\":                           "
"    { \"type\": \"Point\", \"coordinates\":                      "
"      [-104.99404, 39.75621]                                     "
"    },                                                           "
"  \"properties\":                                                "
"    {                                                            "
"      \"name\": \"Coors Field\", \"abc\",                        "
"      \"amenity\": \"Baseball Stadium\",                         "
"      \"popupContent\": \"This is where the Rockies play!\"      "
"    }                                                            "
"  }"));
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a colon after a name of object member." at position 475.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  {                                                              "
"  \"type\": \"Feature\", \"geometry\":                           "
"    { \"type\": \"Point\", \"coordinates\":                      "
"      [-104.99404, 39.75621]                                     "
"    },                                                           "
"  \"properties\":                                                "
"    {                                                            "
"      \"name\": \"Coors Field\",                                 "
"      \"amenity\": \"Baseball Stadium\",                         "
"      \"type\": \"Point\", \"coordinates\":                      "
"    }                                                            "
"  }"));
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid value." at position 626.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  {                                                              "
"  \"type\": \"Feature\", \"geometry\":                           "
"    { \"type\": \"Point\", \"coordinates\":                      "
"      [-104.99404, 39.75621]                                     "
"    },                                                           "
"  \"properties\":                                                "
"    {                                                            "
"      \"name\": \"Coors Field\",                                 "
"      \"amenity\": \"Baseball Stadium\",                         "
"      { \"type\": \"Point\", \"coordinates\":                    "
"        [-104.99404, 39.75621]                                   "
"      },                                                         "
"    }                                                            "
"  }"));
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a name for object member." at position 569.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"    [                                            "
"      [                                          "
"        [1, 1]                                   "
"      ]                                          "
"    ]                                            "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"    [                                            "
"      [                                          "
"        [1, 1],                                  "
"        [2, 2]                                   "
"      ]                                          "
"    ]                                            "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"    [                                            "
"      [                                          "
"        [1, 1],                                  "
"        [2, 2],                                  "
"        [3, 3]                                   "
"      ]                                          "
"    ]                                            "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"    [                                            "
"      [                                          "
"        [1, 1],                                  "
"        [1, 4],                                  "
"        [4, 4],                                  "
"        [1, 1]                                   "
"      ],                                         "
"      [                                          "
"        [2, 2]                                   "
"      ]                                          "
"    ]                                            "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"    [                                            "
"      [                                          "
"        [1, 1],                                  "
"        [1, 4],                                  "
"        [4, 4],                                  "
"        [1, 1]                                   "
"      ],                                         "
"      [                                          "
"        [2, 2],                                  "
"        [2, 3]                                   "
"      ]                                          "
"    ]                                            "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"    [                                            "
"      [                                          "
"        [1, 1],                                  "
"        [1, 4],                                  "
"        [4, 4],                                  "
"        [1, 1]                                   "
"      ],                                         "
"      [                                          "
"        [2, 2],                                  "
"        [2, 3],                                  "
"        [3, 3]                                   "
"      ]                                          "
"    ]                                            "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"    [                                            "
"      [                                          "
"        [1, 1],                                  "
"        1,                                       "
"        [4, 4],                                  "
"        [1, 1]                                   "
"      ],                                         "
"      [                                          "
"        [2, 2],                                  "
"        [2, 3],                                  "
"        [3, 3]                                   "
"      ]                                          "
"    ]                                            "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"    [                                            "
"      [                                          "
"        [1, 1],                                  "
"        [1, 4],                                  "
"        [4, 4],                                  "
"        [1, 1]                                   "
"      ],                                         "
"      [                                          "
"        [2, 2],                                  "
"        0,                                       "
"        [3, 3]                                   "
"      ]                                          "
"    ]                                            "
"  }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"    [                                            "
"      [                                          "
"        [1, 1],                                  "
"        [2, 2, 2],                               "
"        [3, 3, 3, 3],                            "
"        [1, 1]                                   "
"      ]                                          "
"    ]                                            "
"  }"));
ERROR HY000: Unsupported number of coordinate dimensions in function st_geomfromgeojson: Found 3, expected 2
SELECT ST_GEOMFROMGEOJSON(
"{                                      "
"  \"type\" : \"firstName\": \"John\"   "
"}                                      "
);
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a comma or '}' after an object member." at position 61.
PREPARE stmt FROM "SELECT ST_GEOMFROMGEOJSON(?, 0, 0)";
PREPARE stmt FROM "SELECT ST_GEOMFROMGEOJSON('', ?, 0)";
PREPARE stmt FROM "SELECT ST_GEOMFROMGEOJSON('', 0, ?)";
PREPARE stmt FROM "SELECT ST_GEOMFROMGEOJSON(?, ?, 0)";
PREPARE stmt FROM "SELECT ST_GEOMFROMGEOJSON(?, 0, ?)";
PREPARE stmt FROM "SELECT ST_GEOMFROMGEOJSON('', ?, ?)";
PREPARE stmt FROM "SELECT ST_GEOMFROMGEOJSON(?, ?, ?)";
SELECT ST_GEOMFROMGEOJSON("{}foo");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "The document root must not be followed by other values." at position 2.
SELECT ST_GEOMFROMGEOJSON("[12 foo 13]");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a comma or ']' after an array element." at position 4.
SELECT ST_GEOMFROMGEOJSON("{ \"type\": nul123 } ");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid value." at position 13.
SELECT ST_GEOMFROMGEOJSON("{ \"true\": true, \"false\": false } ");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'type'
SELECT ST_GEOMFROMGEOJSON("{ \"true\": tru123 } ");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid value." at position 13.
SELECT ST_GEOMFROMGEOJSON("{ \"false\": fals123 } ");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid value." at position 15.
SELECT ST_GEOMFROMGEOJSON("{ \"hex\": \"\\u0Aa\" } ");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Incorrect hex digit after \u escape in string." at position 10.
SELECT ST_GEOMFROMGEOJSON("{ \"hex\": \"\\\\0000b\" } ");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'type'
SELECT ST_GEOMFROMGEOJSON("{ \"hex\": \"\\uD811b\" } ");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "The surrogate pair in string is invalid." at position 10.
SELECT ST_GEOMFROMGEOJSON("{ \"hex\": \"\\uD811\\uDB99\" } ");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "The surrogate pair in string is invalid." at position 10.
SELECT ST_GEOMFROMGEOJSON("{ \"hex\": \"\\uD811\\uDC01\" } ");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'type'
SELECT ST_GEOMFROMGEOJSON("{ \"hex\": \"\\e000G\" } ");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid escape character in string." at position 10.
SELECT ST_GEOMFROMGEOJSON("{ \"test ");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a closing quotation mark in string." at position 8.
SELECT ST_GEOMFROMGEOJSON("{ \"\nabel\": 123 ");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid encoding in string." at position 3.
SELECT ST_GEOMFROMGEOJSON("[ -2147483651, 4294967395 ] ");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON("[ -9223372036854775900, 184467440737095516700] ");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON("["
  "900000000000000000000000000000000000000000000000000000000000000000000000000000"
  "000000000000000000000000000000000000000000000000000000000000000000000000000000"
  "000000000000000000000000000000000000000000000000000000000000000000000000000000"
  "000000000000000000000000000000000000000000000000000000000000000000000000000"
"]");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Number too big to be stored in double." at position 1.
SELECT ST_GEOMFROMGEOJSON("[ 1. ] ");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Miss fraction part in number." at position 4.
SELECT ST_GEOMFROMGEOJSON("[ 1e1000 ] ");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Number too big to be stored in double." at position 2.
SELECT ST_GEOMFROMGEOJSON("[ 1e ] ");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Miss exponent in number." at position 4.
SELECT ST_GEOMFROMGEOJSON("[ -92233720368547759, 1844674407370955167] ");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON("[ -214748365, 429496739 ] ");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON("[ -0 ]");
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON("{ \"\\u0000\" }");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a colon after a name of object member." at position 11.
SELECT ST_GEOMFROMGEOJSON("{ \"\\u06FF\" }");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a colon after a name of object member." at position 11.
SELECT ST_GEOMFROMGEOJSON("{ \"\\uFFFF\" }");
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Missing a colon after a name of object member." at position 11.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON("{ \"type\": \"Point\", \"coordinates\": [4294967295, 9223372036854775807] }", 1, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON("{ \"type\": \"Point\", \"coordinates\": [4294967295, 9223372036854775807] }", 1, 0))
POINT(4294967295 9.223372036854776e18)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON("{ \"type\": \"Point\", \"coordinates\": [18446744073709551615, 9223372036854775807] }", 1, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON("{ \"type\": \"Point\", \"coordinates\": [18446744073709551615, 9223372036854775807] }", 1, 0))
POINT(1.8446744073709552e19 9.223372036854776e18)
SELECT ST_AsGeoJSON(ST_GeomFromGeoJSON('{"type":"Point","coordinates":[2e308,-0]}'));
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Number too big to be stored in double." at position 31.
# Checking that Point with wrong coordinate array depth are invalidated.
SELECT ST_GEOMFROMGEOJSON(
"{                      "
"  \"type\": \"Point\", "
"  \"coordinates\": [ ] "
"}                      "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                "
"  \"type\": \"Point\",           "
"  \"coordinates\": [ [ 1, 2 ] ]  "
"}                                "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                            "
"  \"type\": \"Point\",       "
"  \"coordinates\": [ [ ] ]   "
"}                            "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                              "
"  \"type\": \"Point\",         "
"  \"coordinates\": [ [ [ ] ] ] "
"}                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
# Checking that LineString with wrong coordinate
# array depth are invalidated.
SELECT ST_GEOMFROMGEOJSON(
"{                            "
"  \"type\": \"LineString\",  "
"  \"coordinates\": [ ]       "
"}                            "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                            "
"  \"type\": \"LineString\",  "
"  \"coordinates\": [ [ ] ]   "
"}                            "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"LineString\",          "
"  \"coordinates\": [ 101.0, 1.0 ]    "
"}                                    "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                          "
"  \"type\": \"LineString\",                "
"  \"coordinates\": [ [ [ 101.0, 1.0 ] ] ]  "
"}                                          "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
# Checking that Polygon with wrong coordinate
# array depth are invalidated.
SELECT ST_GEOMFROMGEOJSON(
"{                          "
"  \"type\": \"Polygon\",   "
"  \"coordinates\": [ ]     "
"}                          "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                          "
"  \"type\": \"Polygon\",   "
"  \"coordinates\": [ [ ] ] "
"}                          "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                "
"  \"type\": \"Polygon\",         "
"  \"coordinates\": [ [ [ ] ] ]   "
"}                                "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"Polygon\",             "
"  \"coordinates\": [ [ [ [ ] ] ] ]   "
"}                                    "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"Polygon\",             "
"  \"coordinates\": [ 100.0, 0.0 ]    "
"}                                    "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                      "
"  \"type\": \"Polygon\",               "
"  \"coordinates\": [ [ 100.0, 0.0 ] ]  "
"}                                      "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                              "
"  \"type\": \"Polygon\",                       "
"  \"coordinates\": [ [ [ [ 100.0, 0.0 ] ] ] ]  "
"}                                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                  "
"  \"type\": \"Polygon\",           "
"  \"coordinates\": [ [ [ [ ] ] ] ] "
"}                                  "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
# Checking that Polygon rings with less than four points are invalidated.
SELECT ST_GEOMFROMGEOJSON(
"{                                              "
"  \"type\": \"Polygon\",                       "
"  \"coordinates\": [ [ [ 100.0, 0.0 ] ] ]      "
"}                                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                        "
"  \"type\": \"Polygon\", "
"  \"coordinates\":       "
"  [ [                    "
"    [ 100.0, 0.0 ],      "
"    [ 100.0, 0.0 ]       "
"  ] ]                    "
"}                        "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                          "
"  \"type\": \"Polygon\",   "
"  \"coordinates\":         "
"  [ [                      "
"    [ 100.0, 0.0 ],        "
"    [ 100.0, 0.0 ],        "
"    [ 100.0, 0.0 ]         "
"  ] ]                      "
"}                          "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
# Checking that Polygons with unclosed rings are invalidated.
SELECT ST_GEOMFROMGEOJSON(
"{                          "
"  \"type\": \"Polygon\",   "
"  \"coordinates\":         "
"  [ [                      "
"    [ 100.0, 0.0 ],        "
"    [ 101.0, 0.0 ],        "
"    [ 102.0, 0.0 ],        "
"    [ 100.1, 0.0 ]         "
"  ] ]                      "
"}                          "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                        "
"  \"type\": \"Polygon\", "
"  \"coordinates\":       "
"  [ [                    "
"    [ 100.0, 0.0 ],      "
"    [ 101.0, 0.0 ],      "
"    [ 102.0, 0.0 ],      "
"    [ 100.0, 0.1 ]       "
"  ] ]                    "
"}                        "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                          "
"  \"type\": \"Polygon\",   "
"  \"coordinates\":         "
"  [                        "
"    [                      "
"      [ 100.0, 0.0 ],      "
"      [ 101.0, 0.0 ],      "
"      [ 102.0, 0.0 ],      "
"      [ 100.0, 0.0 ]       "
"    ],                     "
"    [                      "
"      [ 10.0, 2.0 ],       "
"      [ 11.0, 2.2 ],       "
"      [ 12.0, 2.3 ],       "
"      [ 10.0, 2.1 ]        "
"    ]                      "
"  ]                        "
"}                          "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                        "
"  \"type\": \"Polygon\", "
"  \"coordinates\":       "
"  [                      "
"    [                    "
"      [ 100.0, 0.0 ],    "
"      [ 101.0, 0.0 ],    "
"      [ 102.0, 0.0 ],    "
"      [ 100.0, 0.0 ]     "
"    ],                   "
"    [                    "
"      [ 10.0, 2.0 ],     "
"      [ 11.0, 2.2 ],     "
"      [ 12.0, 2.3 ],     "
"      [ 10.1, 2.0 ]      "
"    ]                    "
"  ]                      "
"}                        "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
# Checking that MultiPoint with wrong coordinate
# array depth are invalidated.
SELECT ST_GEOMFROMGEOJSON(
"{                            "
"  \"type\": \"MultiPoint\",  "
"  \"coordinates\": [ ]       "
"}                            "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                            "
"  \"type\": \"MultiPoint\",  "
"  \"coordinates\": [ [ ] ]   "
"}                            "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                "
"  \"type\": \"MultiPoint\",      "
"  \"coordinates\": [ [ [ ] ] ]   "
"}                                "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                  "
"  \"type\": \"MultiPoint\",        "
"  \"coordinates\": [ 100.0, 0.0 ]  "
"}                                  "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                          "
"  \"type\": \"MultiPoint\",                "
"  \"coordinates\": [ [ [ 100.0, 0.0 ] ] ]  "
"}                                          "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
# Checking that MultiLineString with wrong coordinate
# array depth are invalidated.
SELECT ST_GEOMFROMGEOJSON(
"{                                "
"  \"type\": \"MultiLineString\", "
"  \"coordinates\": [ ]           "
"}                                "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                  "
"  \"type\": \"MultiLineString\",   "
"  \"coordinates\": [ [ ] ]         "
"}                                  "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                  "
"  \"type\": \"MultiLineString\",   "
"  \"coordinates\": [ [ [ ] ] ]     "
"}                                  "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"MultiLineString\",     "
"  \"coordinates\": [ [ [ [ ] ] ] ]   "
"}                                    "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                  "
"  \"type\": \"MultiLineString\",   "
"  \"coordinates\": [ 100.0, 0.0 ]  "
"}                                  "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                      "
"  \"type\": \"MultiLineString\",       "
"  \"coordinates\": [ [ 100.0, 0.0 ] ]  "
"}                                      "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                              "
"  \"type\": \"MultiLineString\",               "
"  \"coordinates\": [ [ [ [ 100.0, 0.0 ] ] ] ]  "
"}                                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
# Checking that MultiPolygon with wrong coordinate
# array depth are invalidated.
SELECT ST_GEOMFROMGEOJSON(
"{                              "
"  \"type\": \"MultiPolygon\",  "
"  \"coordinates\": [ ]         "
"}                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                              "
"  \"type\": \"MultiPolygon\",  "
"  \"coordinates\": [ [ ] ]     "
"}                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                              "
"  \"type\": \"MultiPolygon\",  "
"  \"coordinates\": [ [ [ ] ] ] "
"}                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                  "
"  \"type\": \"MultiPolygon\",      "
"  \"coordinates\": [ [ [ [ ] ] ] ] "
"}                                  "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                  "
"  \"type\": \"MultiPolygon\",      "
"  \"coordinates\": [ 102.0, 2.0 ]  "
"}                                  "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                              "
"  \"type\": \"MultiPolygon\",  "
"  \"coordinates\":             "
"  [                            "
"    [ 102.0, 2.0 ],            "
"    [ 103.0, 2.0 ],            "
"    [ 103.0, 3.0 ],            "
"    [ 102.0, 3.0 ],            "
"    [ 102.0, 2.0 ],            "
"    [ 100.0, 0.0 ],            "
"    [ 101.0, 0.0 ],            "
"    [ 101.0, 1.0 ],            "
"    [ 100.0, 1.0 ],            "
"    [ 100.0, 0.0 ]             "
"  ]                            "
"}                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                              "
"  \"type\": \"MultiPolygon\",  "
"  \"coordinates\":             "
"  [                            "
"    [                          "
"      [ 102.0, 2.0 ],          "
"      [ 103.0, 2.0 ],          "
"      [ 103.0, 3.0 ],          "
"      [ 102.0, 3.0 ],          "
"      [ 102.0, 2.0 ]           "
"    ],                         "
"    [                          "
"      [ 100.0, 0.0 ],          "
"      [ 101.0, 0.0 ],          "
"      [ 101.0, 1.0 ],          "
"      [ 100.0, 1.0 ],          "
"      [ 100.0, 0.0 ]           "
"    ]                          "
"  ]                            "
"}                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                              "
"  \"type\": \"MultiPolygon\",  "
"  \"coordinates\":             "
"  [                            "
"    [                          "
"      [                        "
"        [                      "
"          [ 102.0, 2.0 ],      "
"          [ 103.0, 2.0 ],      "
"          [ 103.0, 3.0 ],      "
"          [ 102.0, 3.0 ],      "
"          [ 102.0, 2.0 ]       "
"        ]                      "
"      ]                        "
"    ],                         "
"    [                          "
"      [ 100.0, 0.0 ],          "
"      [ 101.0, 0.0 ],          "
"      [ 101.0, 1.0 ],          "
"      [ 100.0, 1.0 ],          "
"      [ 100.0, 0.0 ]           "
"    ]                          "
"  ]                            "
"}                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
# Checking that MultiPolygon rings with less than four points
# are invalidated.
SELECT ST_GEOMFROMGEOJSON(
"{                                              "
"  \"type\": \"MultiPolygon\",                  "
"  \"coordinates\": [ [ [ [ 102.0, 2.0 ] ] ] ]  "
"}                                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                              "
"  \"type\": \"MultiPolygon\",  "
"  \"coordinates\":             "
"  [                            "
"    [                          "
"      [                        "
"        [ 103.0, 3.0 ],        "
"        [ 102.0, 2.0 ]         "
"      ]                        "
"    ]                          "
"  ]                            "
"}                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                              "
"  \"type\": \"MultiPolygon\",  "
"  \"coordinates\":             "
"  [                            "
"    [                          "
"      [                        "
"        [ 102.0, 2.0 ],        "
"        [ 103.0, 3.0 ],        "
"        [ 102.0, 2.0 ]         "
"      ]                        "
"    ]                          "
"  ]                            "
"}                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
# Checking that MultiPolygon with unclosed rings are invalidated.
SELECT ST_GEOMFROMGEOJSON(
"{                              "
"  \"type\": \"MultiPolygon\",  "
"  \"coordinates\":             "
"  [                            "
"    [                          "
"      [                        "
"        [ 102.0, 2.0 ],        "
"        [ 103.0, 2.0 ],        "
"        [ 103.0, 3.0 ],        "
"        [ 102.1, 2.0 ]         "
"      ]                        "
"    ]                          "
"  ]                            "
"}                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                              "
"  \"type\": \"MultiPolygon\",  "
"  \"coordinates\":             "
"  [                            "
"    [                          "
"      [                        "
"        [ 102.0, 2.0 ],        "
"        [ 103.0, 2.0 ],        "
"        [ 103.0, 3.0 ],        "
"        [ 102.0, 2.1 ]         "
"      ]                        "
"    ]                          "
"  ]                            "
"}                              "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
# Checking that GeometryCollection with non-objects in array
# are invalidated.
SELECT ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"GeometryCollection\",  "
"  \"geometries\": [ [ ] ]            "
"}                                    "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'geometries' must be of type 'object array'
SELECT ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"GeometryCollection\",  "
"  \"geometries\": [10, 20]           "
"}                                    "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'geometries' must be of type 'object array'
SELECT ST_GEOMFROMGEOJSON(
"{                                      "
"  \"type\": \"GeometryCollection\",    "
"  \"geometries\": [\"foo\", \"bar\"]   "
"}                                      "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'geometries' must be of type 'object array'
SELECT ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"GeometryCollection\",  "
"  \"geometries\": [ foo ]            "
"}                                    "
);
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid value." at position 89.
SELECT ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"GeometryCollection\",  "
"  \"geometries\": [ null ]           "
"}                                    "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'geometries' must be of type 'object array'
SELECT ST_GEOMFROMGEOJSON(
"{                                                          "
"  \"type\": \"GeometryCollection\",                        "
"  \"geometries\":                                          "
"  [                                                        "
"    {   \"type\": \"Point\",   \"coordinates\": [1, 2] },  "
"    [ ]                                                    "
"  ]                                                        "
"}                                                          "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'geometries' must be of type 'object array'
# Nested GeoJSON functions
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"), 5, 0)));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(10 11)"), 5, 0)))
POINT(11 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(-10 -11)"), 5, 1)));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(-10 -11)"), 5, 1)))
POINT(-11 -10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E+308)"), 100, 2), 1, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E+308 1.7976931348623157E+308)"), 100, 2), 1, 0))
POINT(1.7976931348623157e308 1.7976931348623157e308)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E-308 1.7976931348623157E-308)"), 100, 3), 1, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("POINT(1.7976931348623157E-308 1.7976931348623157E-308)"), 100, 3), 1, 0))
POINT(0 0)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("LINESTRING(30 10, 10 30, 40 40)"), 10, 4)));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("LINESTRING(30 10, 10 30, 40 40)"), 10, 4)))
LINESTRING(10 30,30 10,40 40)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("LINESTRING(0 0, 0 10, 10 10, 10 0)"), 10, 5)));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("LINESTRING(0 0, 0 10, 10 10, 10 0)"), 10, 5)))
LINESTRING(0 0,10 0,10 10,0 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("POLYGON((30 10, 40 40, 20 40, 10 20,30 10))"), 20, 6)));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("POLYGON((30 10, 40 40, 20 40, 10 20,30 10))"), 20, 6)))
POLYGON((10 30,40 40,40 20,20 10,10 30))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("POLYGON((35 10, 45 45, 15 40, 10 20, 35 10),"
                                    "(20 30, 35 35, 30 20,20 30))"), 20, 7)));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("POLYGON((35 10, 45 45, 15 40, 10 20, 35 10),"
                                    "(20 30, 35 35, 30 20,20 30))"), 20, 7)))
POLYGON((10 35,45 45,40 15,20 10,10 35),(30 20,35 35,20 30,30 20))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOINT(10 40, 40 30, 20 20, 30 10)"))));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOINT(10 40, 40 30, 20 20, 30 10)"))))
MULTIPOINT((40 10),(30 40),(20 20),(10 30))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOINT(0 0, 10 10, 20 20, 30 30, 40 40)"))));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOINT(0 0, 10 10, 20 20, 30 30, 40 40)"))))
MULTIPOINT((0 0),(10 10),(20 20),(30 30),(40 40))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTILINESTRING((10 10, 20 20, 10 40),"
                                    "(40 40, 30 30, 40 20, 30 10))"))));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTILINESTRING((10 10, 20 20, 10 40),"
                                    "(40 40, 30 30, 40 20, 30 10))"))))
MULTILINESTRING((10 10,20 20,40 10),(40 40,30 30,20 40,10 30))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTILINESTRING((10 10, 20 20, 10 40),"
                                    "(40 40, 30 30, 40 20, 30 10),"
                                    "(10 10, 20 20, 50 50, 100 100))")), 1, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTILINESTRING((10 10, 20 20, 10 40),"
                                    "(40 40, 30 30, 40 20, 30 10),"
                                    "(10 10, 20 20, 50 50, 100 100))")), 1, 0))
MULTILINESTRING((10 10,20 20,10 40),(40 40,30 30,40 20,30 10),(10 10,20 20,50 50,100 100))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOLYGON(((40 40, 20 45, 45 30, 40 40)),"
                                    "((20 35, 10 30, 10 10, 30 5, 45 20, 20 35)))"))));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOLYGON(((40 40, 20 45, 45 30, 40 40)),"
                                    "((20 35, 10 30, 10 10, 30 5, 45 20, 20 35)))"))))
MULTIPOLYGON(((40 40,45 20,30 45,40 40)),((35 20,30 10,10 10,5 30,20 45,35 20)))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOLYGON(((40 40, 20 45, 45 30, 40 40)),"
                                    "((20 35, 10 30, 10 10, 30 5, 45 20, 20 35),"
                                    "(30 20, 20 15, 20 25, 30 20)))"))));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("MULTIPOLYGON(((40 40, 20 45, 45 30, 40 40)),"
                                    "((20 35, 10 30, 10 10, 30 5, 45 20, 20 35),"
                                    "(30 20, 20 15, 20 25, 30 20)))")
MULTIPOLYGON(((40 40,45 20,30 45,40 40)),((35 20,30 10,10 10,5 30,20 45,35 20),(20 30,15 20,25 20,20 30)))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION()"))));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION()"))))
GEOMETRYCOLLECTION EMPTY
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(10 20)))"))));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(10 20)))"))))
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(20 10)))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION()))"))));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION()))"))))
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION EMPTY))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "GEOMETRYCOLLECTION(POINT(10 20)),"
                                    "GEOMETRYCOLLECTION(LINESTRING(0 0, 100 100)),"
                                    "GEOMETRYCOLLECTION(POLYGON((0 0, 0 10, 10 10, 0 0)))"
                                    ")"
                                  )), 1, 0));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "GEOMETRYCOLLECTION(POINT(10 20)),"
                                    "GEOMETRYCOLLECTION(LINESTRING(0 0, 100 100)),"
                   
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(10 20)),GEOMETRYCOLLECTION(LINESTRING(0 0,100 100)),GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,0 0))))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "POINT(4 6),"
                                    "LINESTRING(4 6,7 10),"
                                    "POLYGON ((30 10, 40 40, 20 40, 10 20, 30 10)),"
                                    "MULTIPOINT (10 40, 40 30, 20 20, 30 10),"
                                    "MULTILINESTRING ((10 10, 20 20, 10 40),"
                                      "(40 40, 30 30, 40 20, 30 10)),"
                                    "MULTIPOLYGON (((30 20, 45 40, 10 40, 30 20)),"
                                      "((15 5, 40 10, 10 20, 5 10, 15 5)))"
                                    ")"
                                  ))));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "POINT(4 6),"
                                    "LINESTRING(4 6,7 10),"
                                    "POLYGON ((30 10, 40 40, 20 4
GEOMETRYCOLLECTION(POINT(6 4),LINESTRING(6 4,10 7),POLYGON((10 30,40 40,40 20,20 10,10 30)),MULTIPOINT((40 10),(30 40),(20 20),(10 30)),MULTILINESTRING((10 10,20 20,40 10),(40 40,30 30,20 40,10 30)),MULTIPOLYGON(((20 30,40 45,40 10,20 30)),((5 15,10 40,20 10,10 5,5 15))))
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "POINT(4 6),"
                                    "GEOMETRYCOLLECTION("
                                      "POLYGON ((30 10, 40 40, 20 40, 10 20, 30 10)),"
                                      "MULTIPOINT (10 40, 40 30, 20 20, 30 10),"
                                      "MULTILINESTRING ((10 10, 20 20, 10 40),"
                                        "(40 40, 30 30, 40 20, 30 10)),"
                                      "MULTIPOLYGON (((30 20, 45 40, 10 40, 30 20)),"
                                        "((15 5, 40 10, 10 20, 5 10, 15 5)))"
                                      "),"
                                    "POINT(6 4)"
                                    ")"
                                  ))));
ST_ASTEXT(ST_GEOMFROMGEOJSON(ST_ASGEOJSON(ST_GEOMFROMTEXT("GEOMETRYCOLLECTION( "
                                    "POINT(4 6),"
                                    "GEOMETRYCOLLECTION("
                                      "POLYGON ((30 10, 40 40, 20 4
GEOMETRYCOLLECTION(POINT(6 4),GEOMETRYCOLLECTION(POLYGON((10 30,40 40,40 20,20 10,10 30)),MULTIPOINT((40 10),(30 40),(20 20),(10 30)),MULTILINESTRING((10 10,20 20,40 10),(40 40,30 30,20 40,10 30)),MULTIPOLYGON(((20 30,40 45,40 10,20 30)),((5 15,10 40,20 10,10 5,5 15)))),POINT(4 6))
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [102.0, 0.0]}"))
{"type": "Point", "coordinates": [102.0, 0.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"LineString\", \"coordinates\": "
  "[ [102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0] ] }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"LineString\", \"coordinates\": "
  "[ [102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0] ] }"))
{"type": "LineString", "coordinates": [[102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"MultiPoint\", \"coordinates\": "
  "[ [-105.01, 39.57],[-80.66, 35.0] ] }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"MultiPoint\", \"coordinates\": "
  "[ [-105.01, 39.57],[-80.66, 35.0] ] }"))
{"type": "MultiPoint", "coordinates": [[-105.01, 39.57], [-80.66, 35.0]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MultiLineString\", \"coordinates\":      "
"  [                                                      "
"    [                                                    "
"      [-101.5, 39.662],                                  "
"      [-101.75, 39.2415],                                "
"      [-101.23, 39.2415],                                "
"      [-101.749, 39.7984],                               "
"      [-101.5, 39.011]                                   "
"    ],                                                   "
"    [                                                    "
"      [-99.23, 38.6605],                                 "
"      [-99.56, 38.727],                                  "
"      [-99.25, 38.018]                                   "
"    ],                                                   "
"    [                                                    "
"      [-98.499, 38.913],                                 "
"      [-98.499, 38.913],                                 "
"      [-98.38, 38.15],                                   "
"      [-97.5, 38.629]                                    "
"    ]                                                    "
"  ]                                                      "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MultiLineString\", \"coordinates\":      "
"  [                                                      "
"    [                                                    "
"      [-101.5, 39.662],                   
{"type": "MultiLineString", "coordinates": [[[-101.5, 39.662], [-101.75, 39.2415], [-101.23, 39.2415], [-101.749, 39.7984], [-101.5, 39.011]], [[-99.23, 38.6605], [-99.56, 38.727], [-99.25, 38.018]], [[-98.499, 38.913], [-98.499, 38.913], [-98.38, 38.15], [-97.5, 38.629]]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"  [                                              "
"    [                                            "
"      [41.83, 71.01],                            "
"      [56.95, 33.75],                            "
"      [21.79, 36.56],                            "
"      [41.83, 71.01]                             "
"    ]                                            "
"  ]                                              "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"  [                                              "
"    [                                            "
"      [41.83, 71.01],                            "
"      [56.95, 
{"type": "Polygon", "coordinates": [[[41.83, 71.01], [56.95, 33.75], [21.79, 36.56], [41.83, 71.01]]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"  [                                              "
"    [                                            "
"      [41.83, 71.01],                            "
"      [56.95, 33.75],                            "
"      [21.79, 36.56],                            "
"      [41.83, 71.01]                             "
"    ],                                           "
"    [                                            "
"      [15.40, 15.40],                            "
"      [15.40, 25.40],                            "
"      [25.40, 25.40],                            "
"      [15.40, 15.40]                             "
"    ]                                            "
"  ]                                              "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Polygon\", \"coordinates\":      "
"  [                                              "
"    [                                            "
"      [41.83, 71.01],                            "
"      [56.95, 
{"type": "Polygon", "coordinates": [[[41.83, 71.01], [56.95, 33.75], [21.79, 36.56], [41.83, 71.01]], [[15.4, 15.4], [15.4, 25.4], [25.4, 25.4], [15.4, 15.4]]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MultiPolygon\", \"coordinates\":     "
"  [                                                  "
"    [                                                "
"      [                                              "
"        [102.0, 2.0],                                "
"        [103.0, 2.0],                                "
"        [103.0, 3.0],                                "
"        [102.0, 3.0],                                "
"        [102.0, 2.0]                                 "
"      ]                                              "
"    ],                                               "
"    [                                                "
"      [                                              "
"        [100.0, 0.0],                                "
"        [101.0, 0.0],                                "
"        [101.0, 1.0],                                "
"        [100.0, 1.0],                                "
"        [100.0, 0.0]                                 "
"      ]                                              "
"    ]                                                "
"  ]                                                  "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MultiPolygon\", \"coordinates\":     "
"  [                                                  "
"    [                                                "
"      [                                              "
{"type": "MultiPolygon", "coordinates": [[[[102.0, 2.0], [103.0, 2.0], [103.0, 3.0], [102.0, 3.0], [102.0, 2.0]]], [[[100.0, 0.0], [101.0, 0.0], [101.0, 1.0], [100.0, 1.0], [100.0, 0.0]]]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MultiPolygon\", \"coordinates\":     "
"  [                                                  "
"    [                                                "
"      [                                              "
"        [102.0, 2.0],                                "
"        [103.0, 2.0],                                "
"        [103.0, 3.0],                                "
"        [102.0, 3.0],                                "
"        [102.0, 2.0]                                 "
"      ],                                             "
"      [                                              "
"        [1.0, 0.0],                                  "
"        [0.0, 0.0],                                  "
"        [0.0, 1.0],                                  "
"        [1.0, 0.0]                                   "
"      ]                                              "
"    ],                                               "
"    [                                                "
"      [                                              "
"        [100.0, 0.0],                                "
"        [101.0, 0.0],                                "
"        [101.0, 1.0],                                "
"        [100.0, 1.0],                                "
"        [100.0, 0.0]                                 "
"      ]                                              "
"    ]                                                "
"  ]                                                  "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"MultiPolygon\", \"coordinates\":     "
"  [                                                  "
"    [                                                "
"      [                                              "
{"type": "MultiPolygon", "coordinates": [[[[102.0, 2.0], [103.0, 2.0], [103.0, 3.0], [102.0, 3.0], [102.0, 2.0]], [[1.0, 0.0], [0.0, 0.0], [0.0, 1.0], [1.0, 0.0]]], [[[100.0, 0.0], [101.0, 0.0], [101.0, 1.0], [100.0, 1.0], [100.0, 0.0]]]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":    "
"    [ ]                                                  "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":    "
"    [ ]                                                  "
"  }"))
{"type": "GeometryCollection", "geometries": []}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":          "
"    [                                                          "
"      { \"type\": \"GeometryCollection\", \"geometries\":      "
"        [                                                      "
"          { \"type\": \"GeometryCollection\", \"geometries\":  "
"            [ ]                                                "
"          }                                                    "
"        ]                                                      "
"      }                                                        "
"    ]                                                          "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":          "
"    [                                                          "
"      { \"type\": \"GeometryCollection\", \"geometries\":      "
"        [               
{"type": "GeometryCollection", "geometries": [{"type": "GeometryCollection", "geometries": [{"type": "GeometryCollection", "geometries": []}]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":    "
"    [                                                    "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ]                                                "
"      },                                                 "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ]                                                "
"      }                                                  "
"    ]                                                    "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":    "
"    [                                                    "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                 
{"type": "GeometryCollection", "geometries": [{"type": "LineString", "coordinates": [[102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0]]}, {"type": "LineString", "coordinates": [[102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0]]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":    "
"    [                                                    "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ]                                                "
"      },                                                 "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                                "
"          [102.0, 0.0],                                  "
"          [103.0, 1.0],                                  "
"          [104.0, 0.0],                                  "
"          [105.0, 1.0]                                   "
"        ]                                                "
"      },                                                 "
"      { \"type\": \"Point\", \"coordinates\":            "
"        [102.0, 0.0]                                     "
"      }                                                  "
"    ]                                                    "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":    "
"    [                                                    "
"      { \"type\": \"LineString\", \"coordinates\":       "
"        [                                 
{"type": "GeometryCollection", "geometries": [{"type": "LineString", "coordinates": [[102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0]]}, {"type": "LineString", "coordinates": [[102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0]]}, {"type": "Point", "coordinates": [102.0, 0.0]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":      "
"    [                                                      "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                                       "
"      },                                                   "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                                       "
"      },                                                   "
"      { \"type\": \"GeometryCollection\", \"geometries\":  "
"        [                                                  "
"          { \"type\": \"Point\", \"coordinates\":          "
"            [102.0, 0.5]                                   "
"          },                                               "
"          { \"type\": \"Point\", \"coordinates\":          "
"            [102.0, 0.5]                                   "
"          }                                                "
"        ]                                                  "
"      },                                                   "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                                       "
"      }                                                    "
"    ]                                                      "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":      "
"    [                                                      "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                
{"type": "GeometryCollection", "geometries": [{"type": "Point", "coordinates": [102.0, 0.5]}, {"type": "Point", "coordinates": [102.0, 0.5]}, {"type": "GeometryCollection", "geometries": [{"type": "Point", "coordinates": [102.0, 0.5]}, {"type": "Point", "coordinates": [102.0, 0.5]}]}, {"type": "Point", "coordinates": [102.0, 0.5]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":      "
"    [                                                      "
"      { \"type\": \"MultiPoint\", \"coordinates\":         "
"        [                                                  "
"          [-105.01, 39.57],                                "
"          [-80.66, 35.0]                                   "
"        ]                                                  "
"      },                                                   "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                                       "
"      },                                                   "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                                       "
"      },                                                   "
"      { \"type\": \"GeometryCollection\", \"geometries\":  "
"        [                                                  "
"          { \"type\": \"Point\", \"coordinates\":          "
"            [102.0, 0.5]                                   "
"          },                                               "
"          { \"type\": \"Point\", \"coordinates\":          "
"            [102.0, 0.5]                                   "
"          }                                                "
"        ]                                                  "
"      },                                                   "
"      { \"type\": \"Point\", \"coordinates\":              "
"        [102.0, 0.5]                                       "
"      }                                                    "
"    ]                                                      "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":      "
"    [                                                      "
"      { \"type\": \"MultiPoint\", \"coordinates\":         "
"        [                           
{"type": "GeometryCollection", "geometries": [{"type": "MultiPoint", "coordinates": [[-105.01, 39.57], [-80.66, 35.0]]}, {"type": "Point", "coordinates": [102.0, 0.5]}, {"type": "Point", "coordinates": [102.0, 0.5]}, {"type": "GeometryCollection", "geometries": [{"type": "Point", "coordinates": [102.0, 0.5]}, {"type": "Point", "coordinates": [102.0, 0.5]}]}, {"type": "Point", "coordinates": [102.0, 0.5]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":      "
"  [                                                        "
"    { \"type\": \"Point\", \"coordinates\":                "
"      [-105.01621, 39.57422]                               "
"    },                                                     "
"    { \"type\": \"MultiPoint\", \"coordinates\":           "
"      [                                                    "
"        [-105.01, 39.57],                                  "
"        [-80.66, 35.05]                                    "
"      ]                                                    "
"    },                                                     "
"    { \"type\": \"LineString\", \"coordinates\":           "
"      [                                                    "
"        [-101.5, 39.662],                                  "
"        [-101.75, 39.2415],                                "
"        [-101.64, 39.2415]                                 "
"      ]                                                    "
"    },                                                     "
"    { \"type\": \"MultiLineString\", \"coordinates\":      "
"      [                                                    "
"        [                                                  "
"          [-101.5, 39.662],                                "
"          [-101.75, 39.2415],                              "
"          [-101.23, 39.2415],                              "
"          [-101.749, 39.7984],                             "
"          [-101.5, 39.011]                                 "
"        ],                                                 "
"        [                                                  "
"          [-99.23, 38.6605],                               "
"          [-99.56, 38.727],                                "
"          [-99.25, 38.018]                                 "
"        ],                                                 "
"        [                                                  "
"          [-98.499, 38.913],                               "
"          [-98.499, 38.913],                               "
"          [-98.38, 38.15],                                 "
"          [-97.5, 38.629]                                  "
"        ]                                                  "
"      ]                                                    "
"    },                                                     "
"    { \"type\": \"Polygon\", \"coordinates\":              "
"      [                                                    "
"        [                                                  "
"          [41.83,71.01],                                   "
"          [56.95,33.75],                                   "
"          [21.79,36.56],                                   "
"          [41.83,71.01]                                    "
"        ]                                                  "
"      ]                                                    "
"    },                                                     "
"    {   \"type\": \"MultiPolygon\", \"coordinates\":       "
"      [                                                    "
"        [                                                  "
"          [                                                "
"            [102.0, 2.0],                                  "
"            [103.0, 2.0],                                  "
"            [103.0, 3.0],                                  "
"            [102.0, 3.0],                                  "
"            [102.0, 2.0]                                   "
"          ]                                                "
"        ],                                                 "
"        [                                                  "
"          [                                                "
"            [100.0, 0.0],                                  "
"            [101.0, 0.0],                                  "
"            [101.0, 1.0],                                  "
"            [100.0, 1.0],                                  "
"            [100.0, 0.0]                                   "
"          ]                                                "
"        ]                                                  "
"      ]                                                    "
"    }                                                      "
"  ]                                                        "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"GeometryCollection\", \"geometries\":      "
"  [                                                        "
"    { \"type\": \"Point\", \"coordinates\":                "
"      [-105.01621, 39.57422]        
{"type": "GeometryCollection", "geometries": [{"type": "Point", "coordinates": [-105.01621, 39.57422]}, {"type": "MultiPoint", "coordinates": [[-105.01, 39.57], [-80.66, 35.05]]}, {"type": "LineString", "coordinates": [[-101.5, 39.662], [-101.75, 39.2415], [-101.64, 39.2415]]}, {"type": "MultiLineString", "coordinates": [[[-101.5, 39.662], [-101.75, 39.2415], [-101.23, 39.2415], [-101.749, 39.7984], [-101.5, 39.011]], [[-99.23, 38.6605], [-99.56, 38.727], [-99.25, 38.018]], [[-98.499, 38.913], [-98.499, 38.913], [-98.38, 38.15], [-97.5, 38.629]]]}, {"type": "Polygon", "coordinates": [[[41.83, 71.01], [56.95, 33.75], [21.79, 36.56], [41.83, 71.01]]]}, {"type": "MultiPolygon", "coordinates": [[[[102.0, 2.0], [103.0, 2.0], [103.0, 3.0], [102.0, 3.0], [102.0, 2.0]]], [[[100.0, 0.0], [101.0, 0.0], [101.0, 1.0], [100.0, 1.0], [100.0, 0.0]]]]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  {                                                              "
"  \"type\": \"Feature\", \"id\": \"stadium\", \"geometry\":      "
"    { \"type\": \"Point\", \"coordinates\":                      "
"      [-104.99404, 39.75621]                                     "
"    },                                                           "
"  \"properties\":                                                "
"    {                                                            "
"      \"name\": \"Coors Field\",                                 "
"      \"amenity\": \"Baseball Stadium\",                         "
"      \"popupContent\": \"This is where the Rockies play!\"      "
"    }                                                            "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  {                                                              "
"  \"type\": \"Feature\", \"id\": \"stadium\", \"geometry\":      "
"    { \"type\": \"Point\", \"coordinates\":                      "
"      [-104.99404,
{"type": "Point", "coordinates": [-104.99404, 39.75621]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"FeatureCollection\", \"features\":     "
"    [                                                  "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83775386582222, 35.24980190252168]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS CENTER\",            "
"          \"address\": \"1326 WOODWARD AV\"            "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83827000459532, 35.25674709224663]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS NEIGHBORHOOD PARK\", "
"          \"address\": \"2605  DOUBLE OAKS RD\"        "
"        }                                              "
"      }                                                "
"    ]                                                  "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"FeatureCollection\", \"features\":     "
"    [                                                  "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\": 
{"type": "GeometryCollection", "geometries": [{"type": "Point", "coordinates": [-80.83775386582222, 35.24980190252168]}, {"type": "Point", "coordinates": [-80.83827000459532, 35.25674709224663]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  {   \"type\": \"FeatureCollection\", \"features\":   "
"    [                                                  "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83775386582222, 35.24980190252168]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS CENTER\",            "
"          \"address\": \"1326 WOODWARD AV\"            "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83827000459532, 35.25674709224663]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS NEIGHBORHOOD PARK\", "
"          \"address\": \"2605  DOUBLE OAKS RD\"        "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        null,                                          "
"      \"properties\":                                  "
"        {                                              "
"          \"descr\": \"A NULL/EMPTY POINT\"            "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-87.83827000459532, 42.0]                   "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS NEIGHBORHOOD PARK\", "
"          \"address\": \"2605  DOUBLE OAKS RD\"        "
"        }                                              "
"      }                                                "
"    ]                                                  "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  {   \"type\": \"FeatureCollection\", \"features\":   "
"    [                                                  "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\": 
{"type": "GeometryCollection", "geometries": [{"type": "Point", "coordinates": [-80.83775386582222, 35.24980190252168]}, {"type": "Point", "coordinates": [-80.83827000459532, 35.25674709224663]}, {"type": "Point", "coordinates": [-87.83827000459532, 42.0]}]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [1.23e3, 2.2e-1]}", 1, 0));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [1.23e3, 2.2e-1]}", 1, 0))
{"type": "Point", "coordinates": [1230.0, 0.22]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [1e-300, 1e300]}", 1, 0));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [1e-300, 1e300]}", 1, 0))
{"type": "Point", "coordinates": [1e-300, 1e300]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [1.7976931348623157E+308, -1.7976931348623157E+308]}", 1, 0));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [1.7976931348623157E+308, -1.7976931348623157E+308]}", 1, 0))
{"type": "Point", "coordinates": [1.7976931348623157e308, -1.7976931348623157e308]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 20] }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 20] }"))
{"type": "Point", "coordinates": [10.0, 20.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"urn:ogc:def:crs:EPSG::1234\" }   "
"        }                                                "
"  }"), 5, 0);
ERROR SR001: There's no spatial reference system with SRID 1234.
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"urn:ogc:def:crs:EPSG::1\" }      "
"        }                                                "
"  }"), 5, 1);
ERROR SR001: There's no spatial reference system with SRID 1.
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],            "
"      \"crs\":                                                 "
"        { \"type\": \"name\", \"properties\":                  "
"          { \"name\": \"urn:ogc:def:crs:EPSG::4294967295\" }   "
"        }                                                      "
"  }"), 5, 2);
ERROR SR001: There's no spatial reference system with SRID 4294967295.
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPSG:234\" }                     "
"        }                                                "
"  }"), 5, 3);
ERROR SR001: There's no spatial reference system with SRID 234.
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPSG:1\" }                       "
"        }                                                "
"  }"), 5, 5);
ERROR SR001: There's no spatial reference system with SRID 1.
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\":                                           "
"        { \"type\": \"name\", \"properties\":            "
"          { \"name\": \"EPSG:4294967295\" }              "
"        }                                                "
"  }"), 5, 7);
ERROR SR001: There's no spatial reference system with SRID 4294967295.
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\": null                                      "
"  }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],      "
"      \"crs\": null                                      "
"  }"))
{"type": "Point", "coordinates": [10.0, 20.0]}
# Check that GeomFromGeoJSON can read data from a JSON field.
CREATE TABLE json_tbl
(
id INT NOT NULL AUTO_INCREMENT,
json_value JSON,
PRIMARY KEY (id)
);
INSERT INTO json_tbl (json_value) VALUES
('{ "type": "Point", "coordinates": [100.0, 0.0] }'),
('{
  "type": "LineString",
  "coordinates":
    [ [100.0, 0.0], [101.0, 1.0] ]
}'),
('{
  "type": "Polygon",
  "coordinates":
    [ [ [100.0, 0.0], [101.0, 0.0], [101.0, 1.0], [100.0, 1.0], [100.0, 0.0] ] ]
}'),
('{
  "type": "Polygon",
  "coordinates": [
    [ [100.0, 0.0], [101.0, 0.0], [101.0, 1.0], [100.0, 1.0], [100.0, 0.0] ],
    [ [100.2, 0.2], [100.8, 0.2], [100.8, 0.8], [100.2, 0.8], [100.2, 0.2] ]
  ]
}'),
('{
  "type": "MultiPoint",
  "coordinates": [ [100.0, 0.0], [101.0, 1.0] ]
}'),
('{ "type": "Point", "coordinates": [-12.144, 13.736489] }');
SELECT ST_AsText(ST_GeomFromGeoJSON(json_value)) FROM json_tbl;
ST_AsText(ST_GeomFromGeoJSON(json_value))
POINT(0 100)
LINESTRING(0 100,1 101)
POLYGON((0 100,0 101,1 101,1 100,0 100))
POLYGON((0 100,0 101,1 101,1 100,0 100),(0.2 100.2,0.2 100.8,0.8 100.8,0.8 100.2,0.2 100.2))
MULTIPOINT((0 100),(1 101))
POINT(13.736489 -12.144)
DROP TABLE json_tbl;
# Checking that member and type names with extra ending does not get
# accepted/validated.
SELECT ST_GEOMFROMGEOJSON(
"{                                "
"  \"type1234567890\": \"Point\", "
"  \"coordinates\": [102, 11]     "
"}                                "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'type'
SELECT ST_GEOMFROMGEOJSON(
"{                                "
"  \"type \": \"Point\",          "
"  \"coordinates\": [102, 11]     "
"}                                "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'type'
SELECT ST_GEOMFROMGEOJSON(
"{                                "
"  \"type\": \"Point \",          "
"  \"coordinates\": [102, 11]     "
"}                                "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                "
"  \"type\": \"Pointt\",          "
"  \"coordinates\": [102, 11]     "
"}                                "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON(
"{                                "
"  \"type\": \"Point\",           "
"  \"coordinates \": [102, 11]    "
"}                                "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'coordinates'
SELECT ST_GEOMFROMGEOJSON(
"{                                "
"  \"type\": \"Point\",           "
"  \"coordinatess\": [102, 11]    "
"}                                "
);
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'coordinates'
# Check that rapidjson parses decimal numbers with trailing zeroes
# correctly.
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                  "
"  \"type\": \"Point\",             "
"  \"coordinates\": [102.0000, 11]  "
"}                                  "
));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                  "
"  \"type\": \"Point\",             "
"  \"coordinates\": [102.0000, 11]  "
"}                                  "
))
POINT(11 102)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"Point\",               "
"  \"coordinates\": [102.00000, 11]   "
"}                                    "
));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"Point\",               "
"  \"coordinates\": [102.00000, 11]   "
"}                                    "
))
POINT(11 102)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"Point\",               "
"  \"coordinates\": [102.000000, 11]  "
"}                                    "
));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"Point\",               "
"  \"coordinates\": [102.000000, 11]  "
"}                                    "
))
POINT(11 102)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                  "
"  \"type\": \"Point\",             "
"  \"coordinates\": [10.0000, 11]  "
"}                                  "
));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                  "
"  \"type\": \"Point\",             "
"  \"coordinates\": [10.0000, 11]  "
"}                                  "
))
POINT(11 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"Point\",               "
"  \"coordinates\": [10.00000, 11]   "
"}                                    "
));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"Point\",               "
"  \"coordinates\": [10.00000, 11]   "
"}                                    "
))
POINT(11 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"Point\",               "
"  \"coordinates\": [10.000000, 11]  "
"}                                    "
));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"Point\",               "
"  \"coordinates\": [10.000000, 11]  "
"}                                    "
))
POINT(11 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                                                "
"  \"type\": \"Point\",                                           "
"  \"coordinates\": [10.00000000000000000000000000000000000, 11]  "
"}                                                                "
));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                                                "
"  \"type\": \"Point\",                                           "
"  \"coordinates\": [10.00000000000000000000000000000000000, 11]  "
"}                    
POINT(11 10)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                  "
"  \"type\": \"Point\",             "
"  \"coordinates\": [102, 11.0000]  "
"}                                  "
));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                  "
"  \"type\": \"Point\",             "
"  \"coordinates\": [102, 11.0000]  "
"}                                  "
))
POINT(11 102)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                   "
"  \"type\": \"Point\",              "
"  \"coordinates\": [102, 11.00000]  "
"}                                   "
));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                   "
"  \"type\": \"Point\",              "
"  \"coordinates\": [102, 11.00000]  "
"}                                   "
))
POINT(11 102)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"Point\",               "
"  \"coordinates\": [102, 11.000000]  "
"}                                    "
));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{                                    "
"  \"type\": \"Point\",               "
"  \"coordinates\": [102, 11.000000]  "
"}                                    "
))
POINT(11 102)
SELECT ST_ASGEOJSON(POINT(1, 1), -9223372036854775808);
ERROR HY000: Incorrect max decimal digits value: '-9223372036854775808' for function st_asgeojson
SELECT ST_ASGEOJSON(POINT(1, 1), 10, -9223372036854775808);
ERROR HY000: Incorrect options value: '-9223372036854775808' for function st_asgeojson
SELECT ST_ASGEOJSON(POINT(1, 1), 18446744073709551615);
ERROR HY000: Incorrect max decimal digits value: '18446744073709551615' for function st_asgeojson
SELECT ST_ASGEOJSON(POINT(1, 1), 10, 18446744073709551615);
ERROR HY000: Incorrect options value: '18446744073709551615' for function st_asgeojson
SELECT ST_GEOMFROMGEOJSON('{"type":"Point", "coordinates":[10, 20]}', 1, -9223372036854775808);
ERROR 22003: SRID value is out of range in 'st_geomfromgeojson'
SELECT ST_GEOMFROMGEOJSON('{"type":"Point", "coordinates":[10, 20]}', -9223372036854775808);
ERROR HY000: Incorrect option value: '-9223372036854775808' for function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON('{"type":"Point", "coordinates":[10, 20]}', 1, 18446744073709551615);
ERROR 22003: SRID value is out of range in 'st_geomfromgeojson'
SELECT ST_GEOMFROMGEOJSON('{"type":"Point", "coordinates":[10, 20]}', 18446744073709551615);
ERROR HY000: Incorrect option value: '18446744073709551615' for function st_geomfromgeojson
SELECT ST_ASGEOJSON(POINT(1, 1), -9223372036854775809);
ERROR HY000: Incorrect type for argument max decimal digits in function st_asgeojson.
SELECT ST_ASGEOJSON(POINT(1, 1), 10, -9223372036854775809);
ERROR HY000: Incorrect type for argument options in function st_asgeojson.
SELECT ST_ASGEOJSON(POINT(1, 1), 18446744073709551616);
ERROR HY000: Incorrect type for argument max decimal digits in function st_asgeojson.
SELECT ST_ASGEOJSON(POINT(1, 1), 10, 18446744073709551616);
ERROR HY000: Incorrect type for argument options in function st_asgeojson.
SELECT ST_GEOMFROMGEOJSON('{"type":"Point", "coordinates":[10, 20]}', 1, -9223372036854775809);
ERROR HY000: Incorrect type for argument SRID in function st_geomfromgeojson.
SELECT ST_GEOMFROMGEOJSON('{"type":"Point", "coordinates":[10, 20]}', -9223372036854775809);
ERROR HY000: Incorrect type for argument options in function st_geomfromgeojson.
SELECT ST_GEOMFROMGEOJSON('{"type":"Point", "coordinates":[10, 20]}', 1, 18446744073709551616);
ERROR HY000: Incorrect type for argument SRID in function st_geomfromgeojson.
SELECT ST_GEOMFROMGEOJSON('{"type":"Point", "coordinates":[10, 20]}', 18446744073709551616);
ERROR HY000: Incorrect type for argument options in function st_geomfromgeojson.
CREATE TABLE geojson(
gid INT NOT NULL AUTO_INCREMENT,
geojson_value TEXT,
PRIMARY KEY (`gid`)
);
INSERT INTO geojson (geojson_value) VALUES
(ST_AsGeoJSON(ST_GeomFromText('POINT(10 11)', 4326), 100, 2)),
(ST_AsGeoJSON(ST_GeomFromText('POINT(-1.324 66.231)', 0), 100, 2)),
(ST_AsGeoJSON(ST_GeomFromText('POINT(98.344 -23.001)', 0), 100, 2)),
(ST_AsGeoJSON(ST_GeomFromText('POINT(0.0 0.0)', 0), 100, 2));
SELECT ST_AsText(ST_GeomFromGeoJSON(geojson_value)) FROM geojson;
ST_AsText(ST_GeomFromGeoJSON(geojson_value))
POINT(10 11)
POINT(66.231 -1.324)
POINT(-23.001 98.344)
POINT(0 0)
SELECT ST_SRID(ST_GeomFromGeoJSON(geojson_value)) FROM geojson;
ST_SRID(ST_GeomFromGeoJSON(geojson_value))
4326
4326
4326
4326
DROP TABLE geojson;
#
# Bug#21384048 ASSERTION FAILED: N >= 0 && N <= 308
#              IN RAPIDJSON::INTERNAL::FASTPATH
#
SELECT ST_GEOMFROMGEOJSON('1E-36181012216111515851075235238');
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON('-1E-36181012216111515851075235238');
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT ST_GEOMFROMGEOJSON('1E+36181012216111515851075235238');
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Number too big to be stored in double." at position 0.
SELECT ST_GEOMFROMGEOJSON('-1E+36181012216111515851075235238');
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Number too big to be stored in double." at position 0.
#
# Bug#21389101 ST_GEOMFROMGEOJSON: STACK OVERFLOW IN
#              RAPIDJSON::GENERICREADER
#
SELECT ST_GeomFromGeoJSON(REPEAT('[', 1000));
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT ST_GeomFromGeoJSON(REPEAT('{"foo":', 1000));
ERROR 22032: The JSON document exceeds the maximum depth.
#
# Bug#21616810 CONNECTION HANG OR ASSERTION 0 IN
#              THD::SEND_STATEMENT_STATUS, SQL_CLASS.CC
#
CREATE TABLE t (a INT) ENGINE=InnoDB;
INSERT INTO t (a) VALUES (0), (1);
SELECT 1 FROM t GROUP BY ST_AsGeoJSON(NULL, "1", NULL);
1
1
SELECT 1 FROM t GROUP BY ST_AsGeoJSON(NULL, 5, 0);
1
1
DROP TABLE t;
#
# Bug#22804853 ST_GEOMFROMGEOJSON: ASSERTION FAILED: 0, FILE FILESORT.CC
#
CREATE TABLE t (a INT) ENGINE=InnoDB;
INSERT INTO t VALUES (1), (2);
SELECT DISTINCT SQL_BIG_RESULT ST_GeomFromGeoJSON('1', 1, ST_SRID(a)) FROM t;
ERROR 22023: Invalid GIS data provided to function st_srid.
DROP TABLE t;
#
# Bug#22912800 ST_ASGEOJSON() DOESN'T WORK WITH SESSION VARIABLES
#
SET @geometry := ST_GeomFromText("POINT(4.9 52.366667)");
SELECT ST_AsGeoJSON(@geometry);
ST_AsGeoJSON(@geometry)
{"type": "Point", "coordinates": [4.9, 52.366667]}
#
# Bug#22930020 INVALID DATA FOR ST_GEOMFROMGEOJSON() IS HARD TO DIAGNOSE
#
CREATE TABLE t1 (col1 JSON);
INSERT INTO t1 (col1) VALUES
('{ "type": "Feature", "geometry": null, "properties": {} }');
ALTER TABLE t1 ADD COLUMN `col2` geometry
GENERATED ALWAYS AS (ST_GeomFromGeoJSON(col1->'$.geometry')) VIRTUAL;
SELECT * FROM t1;
col1	col2
{"type": "Feature", "geometry": null, "properties": {}}	NULL
SELECT ST_GeomFromGeoJSON(CAST('null' AS JSON));
ST_GeomFromGeoJSON(CAST('null' AS JSON))
NULL
SELECT ST_GeomFromGeoJSON('null');
ST_GeomFromGeoJSON('null')
NULL
SELECT ST_GeomFromGeoJSON('Null');
ERROR 22032: Invalid JSON text in argument 1 to function st_geomfromgeojson: "Invalid value." at position 0.
DROP TABLE t1;
#
# WL#8579 Spatial Reference Systems
#
# SRID 0 (should pass)
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT('POINT(0 0)', 0));
ST_ASGEOJSON(ST_GEOMFROMTEXT('POINT(0 0)', 0))
{"type": "Point", "coordinates": [0.0, 0.0]}
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { \"name\": \"urn:ogc:def:crs:OGC:1.3:crs84\" }         "
"        }                                                         "
"  }", 1, 0));
ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { \"name\"
0
# Projected SRS (should pass)
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT('POINT(0 0)', 2000));
ST_ASGEOJSON(ST_GEOMFROMTEXT('POINT(0 0)', 2000))
{"type": "Point", "coordinates": [0.0, 0.0]}
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { \"name\": \"urn:ogc:def:crs:OGC:1.3:crs84\" }         "
"        }                                                         "
"  }", 1, 2000));
ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { \"name\"
2000
# Geographic SRS (should pass)
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT('POINT(0 0)', 4326));
ST_ASGEOJSON(ST_GEOMFROMTEXT('POINT(0 0)', 4326))
{"type": "Point", "coordinates": [0.0, 0.0]}
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { \"name\": \"urn:ogc:def:crs:OGC:1.3:crs84\" }         "
"        }                                                         "
"  }", 1, 4326));
ST_SRID(ST_GEOMFROMGEOJSON(
"  { \"type\": \"Point\", \"coordinates\": [10, 20],               "
"      \"crs\":                                                    "
"        { \"type\": \"name\", \"properties\":                     "
"          { \"name\"
4326
#
# Bug #26941370 ST_GEOMFROMGEOJSON ALLOWS OUT-OF-RANGE COORDINATES
#
SELECT ST_AsText(
ST_GeomFromGeoJSON('{"type": "Point", "coordinates":[-180.0, 0.0]}')
);
ERROR 22S02: Longitude -180.000000 is out of range in function st_geomfromgeojson. It must be within (-180.000000, 180.000000].
SELECT ST_AsText(
ST_GeomFromGeoJSON('{"type": "Point", "coordinates":[-179.99999999999, 0.0]}')
);
ST_AsText(
ST_GeomFromGeoJSON('{"type": "Point", "coordinates":[-179.99999999999, 0.0]}')
)
POINT(0 -179.99999999999)
SELECT ST_AsText(
ST_GeomFromGeoJSON('{"type": "Point", "coordinates":[180.0, 0.0]}')
);
ST_AsText(
ST_GeomFromGeoJSON('{"type": "Point", "coordinates":[180.0, 0.0]}')
)
POINT(0 180)
SELECT ST_AsText(
ST_GeomFromGeoJSON('{"type": "Point", "coordinates":[180.000000000001, 0.0]}')
);
ERROR 22S02: Longitude 180.000000 is out of range in function st_geomfromgeojson. It must be within (-180.000000, 180.000000].
SELECT ST_AsText(
ST_GeomFromGeoJSON('{"type": "Point", "coordinates":[0.0, -90.000000000001]}')
);
ERROR 22S03: Latitude -90.000000 is out of range in function st_geomfromgeojson. It must be within [-90.000000, 90.000000].
SELECT ST_AsText(
ST_GeomFromGeoJSON('{"type": "Point", "coordinates":[0.0, -90.0]}')
);
ST_AsText(
ST_GeomFromGeoJSON('{"type": "Point", "coordinates":[0.0, -90.0]}')
)
POINT(-90 0)
SELECT ST_AsText(
ST_GeomFromGeoJSON('{"type": "Point", "coordinates":[0.0, 90.0]}')
);
ST_AsText(
ST_GeomFromGeoJSON('{"type": "Point", "coordinates":[0.0, 90.0]}')
)
POINT(90 0)
SELECT ST_AsText(
ST_GeomFromGeoJSON('{"type": "Point", "coordinates":[0.0, 90.0000000000001]}')
);
ERROR 22S03: Latitude 90.000000 is out of range in function st_geomfromgeojson. It must be within [-90.000000, 90.000000].
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  {   \"type\": \"FeatureCollection\",                 "
"      \"features\":                                    "
"    [                                                  "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83775386582222, 35.24980190252168]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS CENTER\",            "
"          \"address\": \"1326 WOODWARD AV\"            "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83827000459532, 35.25674709224663]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS NEIGHBORHOOD PARK\", "
"          \"address\": \"2605  DOUBLE OAKS RD\"        "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        null,                                          "
"      \"properties\":                                  "
"        {                                              "
"          \"descr\": \"A NULL/EMPTY POINT\"            "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\",                         "
"          \"crs\":                                     "
"            { \"type\": \"name\", \"properties\":      "
"              { \"name\": \"EPSG:4326\" }              "
"            },                                         "
"          \"coordinates\":                             "
"          [-87.83827000459532, 42.0]                   "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS NEIGHBORHOOD PARK\", "
"          \"address\": \"2605  DOUBLE OAKS RD\"        "
"        }                                              "
"      }                                                "
"    ]                                                  "
"  }")) AS srid;
srid
4326
SELECT ST_SRID(ST_GEOMFROMGEOJSON(
"  {   \"type\": \"FeatureCollection\",                 "
"      \"features\":                                    "
"    [                                                  "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83775386582222, 35.24980190252168]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS CENTER\",            "
"          \"address\": \"1326 WOODWARD AV\"            "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\", \"coordinates\":        "
"          [-80.83827000459532, 35.25674709224663]      "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS NEIGHBORHOOD PARK\", "
"          \"address\": \"2605  DOUBLE OAKS RD\"        "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        null,                                          "
"      \"properties\":                                  "
"        {                                              "
"          \"descr\": \"A NULL/EMPTY POINT\"            "
"        }                                              "
"      },                                               "
"      { \"type\": \"Feature\", \"geometry\":           "
"        { \"type\": \"Point\",                         "
"          \"crs\":                                     "
"            { \"type\": \"name\", \"properties\":      "
"              { \"name\": \"EPSG:2000\" }              "
"            },                                         "
"          \"coordinates\":                             "
"          [-87.83827000459532, 42.0]                   "
"        },                                             "
"      \"properties\":                                  "
"        {                                              "
"          \"name\": \"DOUBLE OAKS NEIGHBORHOOD PARK\", "
"          \"address\": \"2605  DOUBLE OAKS RD\"        "
"        }                                              "
"      }                                                "
"    ]                                                  "
"  }")) AS srid;
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'crs' must be specified in the top level object.
#
# Bug#32047630: MISSING ERROR PROPAGATION IN JSON FUNCTIONS
#
SELECT JSON_ARRAY(ST_ASGEOJSON(ST_GEOMFROMTEXT('')), 1);
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT JSON_ARRAY(ST_ASGEOJSON(0x00), 1);
ERROR 22023: Invalid GIS data provided to function st_asgeojson.
