CREATE TABLE t1 (a INT);
RESET BINARY LOGS AND GTIDS;
SET TIMESTAMP= UNIX_TIMESTAMP("2031-01-01 12:00:00");
INSERT INTO t1 VALUES(1);
SET TIMESTAMP= UNIX_TIMESTAMP("2032-01-01 12:00:00");
INSERT INTO t1 VALUES(2);
SET TIMESTAMP= UNIX_TIMESTAMP("2033-01-01 12:00:00");
INSERT INTO t1 VALUES(3);
FLUSH LOGS;
SET TIMESTAMP= UNIX_TIMESTAMP("2034-01-01 12:00:00");
INSERT INTO t1 VALUES(4);
SET TIMESTAMP= UNIX_TIMESTAMP("2035-01-01 12:00:00");
INSERT INTO t1 VALUES(5);
FLUSH LOGS;

==== Local ====

---- base64-output ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- offset ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-position ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- stop-position ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-position stop-position ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-datetime ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- stop-datetime ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-datetime stop-datetime ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

==== Local with 2 binlogs on command line ====

---- base64-output ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(5)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- offset ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(5)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-position ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(5)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- stop-position ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-position stop-position ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-datetime ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(5)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- stop-datetime ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-datetime stop-datetime ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

==== Remote ====

---- base64-output ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- offset ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-position ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- stop-position ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-position stop-position ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-datetime ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- stop-datetime ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-datetime stop-datetime ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

==== Remote with 2 binlogs on command line ====

---- base64-output ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(5)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- offset ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(5)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-position ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(5)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- stop-position ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-position stop-position ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-datetime ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(5)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- stop-datetime ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- start-datetime stop-datetime ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

---- to-last-log ----
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(1)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(2)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(3)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(4)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO t1 VALUES(5)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
# [empty]
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;

==== clean up ====
DROP TABLE t1;
