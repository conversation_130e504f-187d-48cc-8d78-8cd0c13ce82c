reset binary logs and gtids;
set timestamp=1000000000;
drop table if exists t1,t2,t3,t4,t5,t03,t04;
create table t1 (word varchar(20));
create table t2 (id int auto_increment not null primary key);
insert into t1 values ("abirvalg");
insert into t2 values ();
load data infile '../../std_data/words.dat' into table t1;
load data infile '../../std_data/words.dat' into table t1;
load data infile '../../std_data/words.dat' into table t1;
load data infile '../../std_data/words.dat' into table t1;
load data infile '../../std_data/words.dat' into table t1;
insert into t1 values ("Alas");
flush logs;

--- Local --
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
DROP TABLE IF EXISTS `t1`,`t2`,`t3`,`t4`,`t5`,`t03`,`t04` /* generated by server */
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
/*!80013 SET @@session.sql_require_primary_key=0*//*!*/;
create table t1 (word varchar(20))
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
/*!80013 SET @@session.sql_require_primary_key=0*//*!*/;
create table t2 (id int auto_increment not null primary key)
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
insert into t1 values ("abirvalg")
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET INSERT_ID=1/*!*/;
SET TIMESTAMP=#/*!*/;
insert into t2 values ()
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`word`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`word`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`word`)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
--- Broken LOAD DATA --
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`word`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`word`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
insert into t1 values ("Alas")
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
--- Broken LOAD DATA with streaming input --
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`word`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`word`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
insert into t1 values ("Alas")
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
--- --database --
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
COMMIT /* added by mysqlbinlog *//*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
COMMIT /* added by mysqlbinlog *//*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
COMMIT /* added by mysqlbinlog *//*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
--- --start-position --
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
insert into t1 values ("Alas")
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
--- --start-position with streaming input --
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
insert into t1 values ("Alas")
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
--- Remote --
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
DROP TABLE IF EXISTS `t1`,`t2`,`t3`,`t4`,`t5`,`t03`,`t04` /* generated by server */
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
/*!80013 SET @@session.sql_require_primary_key=0*//*!*/;
create table t1 (word varchar(20))
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
/*!80013 SET @@session.sql_require_primary_key=0*//*!*/;
create table t2 (id int auto_increment not null primary key)
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
insert into t1 values ("abirvalg")
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET INSERT_ID=1/*!*/;
SET TIMESTAMP=#/*!*/;
insert into t2 values ()
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`word`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`word`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`word`)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
--- Broken LOAD DATA --
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`word`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`word`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
insert into t1 values ("Alas")
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
--- --database --
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
COMMIT /* added by mysqlbinlog *//*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
COMMIT /* added by mysqlbinlog *//*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
COMMIT /* added by mysqlbinlog *//*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
--- --start-position --
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
insert into t1 values ("Alas")
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
--- reading stdin --
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
drop table t1,t2;
flush logs;
flush logs;
select * from t5  /* must be (1),(1) */;
a
1
1
drop table t5;
flush logs;
create table t5 (c1 int, c2 varchar(128) character set latin1 not null);
insert into t5 values (1, date_format('2001-01-01','%W'));
set lc_time_names=de_DE;
insert into t5 values (2, date_format('2001-01-01','%W'));
set lc_time_names=en_US;
insert into t5 values (3, date_format('2001-01-01','%W'));
select * from t5 order by c1;
c1	c2
1	Monday
2	Montag
3	Monday
flush logs;
drop table t5;
select * from t5 order by c1;
c1	c2
1	Monday
2	Montag
3	Monday
drop table t5;
drop procedure if exists p1;
flush logs;
create procedure p1()
begin
select 1;
end;
//
flush logs;
call p1();
1
1
drop procedure p1;
call p1();
ERROR 42000: PROCEDURE test.p1 does not exist
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
CREATE DEFINER=`root`@`localhost` PROCEDURE `p1`()
begin
select 1;
end
/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
call p1();
1
1
drop procedure p1;
flush logs;
create table t1 (a varchar(64) character set utf8mb3);
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
load data infile '../../std_data/loaddata6.dat' into table t1 CHARACTER SET latin1;
set character_set_database=koi8r;
Warnings:
Warning	1681	Updating 'character_set_database' is deprecated. It will be made read-only in a future release.
load data infile '../../std_data/loaddata6.dat' into table t1;
set character_set_database=latin1;
Warnings:
Warning	1681	Updating 'character_set_database' is deprecated. It will be made read-only in a future release.
load data infile '../../std_data/loaddata6.dat' into table t1;
load data infile '../../std_data/loaddata6.dat' into table t1;
set character_set_database=koi8r;
Warnings:
Warning	1681	Updating 'character_set_database' is deprecated. It will be made read-only in a future release.
load data infile '../../std_data/loaddata6.dat' into table t1;
set character_set_database=latin1;
Warnings:
Warning	1681	Updating 'character_set_database' is deprecated. It will be made read-only in a future release.
load data infile '../../std_data/loaddata6.dat' into table t1;
load data infile '../../std_data/loaddata6.dat' into table t1 character set koi8r;
select hex(a) from t1;
hex(a)
C3BF
D0AA
C3BF
C3BF
D0AA
C3BF
D0AA
drop table t1;
flush logs;
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1168113696/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb4 *//*!*/;
SET @@session.character_set_client=255,@@session.collation_connection=255,@@session.collation_server=255/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
/*!80011 SET @@session.default_collation_for_utf8mb4=255*//*!*/;
/*!80013 SET @@session.sql_require_primary_key=0*//*!*/;
create table t1 (a varchar(64) character set utf8mb3)
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` CHARACTER SET latin1 FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`a`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.collation_database=7/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`a`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.collation_database=8/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`a`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`a`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.collation_database=7/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`a`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.collation_database=8/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`a`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
LOAD DATA LOCAL INFILE 'MYSQLTEST_VARDIR/tmp/SQL_LOAD_MB-#-#' INTO TABLE `t1` CHARACTER SET koi8r FIELDS TERMINATED BY '\t' ENCLOSED BY '' ESCAPED BY '\\' LINES TERMINATED BY '\n' (`a`)
/*!*/;
COMMIT/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
DROP TABLE `t1` /* generated by server */
/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
CREATE TABLE t1 (c1 CHAR(10));
FLUSH LOGS;
INSERT INTO t1 VALUES ('0123456789');
FLUSH LOGS;
DROP TABLE t1;
We expect this value to be 1
The bug being tested was that 'Query' lines were not preceded by '#'
If the line is in the table, it had to have been preceded by a '#'

SELECT COUNT(*) AS `BUG#28293_expect_3` FROM patch WHERE a LIKE '%Query%';
BUG#28293_expect_3
2
DROP TABLE patch;
FLUSH LOGS;
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES(connection_id());
FLUSH LOGS;
DROP TABLE t1;
1
DROP TABLE t1;
shell> mysqlbinlog std_data/corrupt-relay-bin.000624 > var/tmp/bug31793.sql
set timestamp= default;
FLUSH LOGS;
Bug#31611 Security risk with BINLOG statement
SET BINLOG_FORMAT='ROW';
Warnings:
Warning	1287	'@@binlog_format' is deprecated and will be removed in a future release.
CREATE DATABASE mysqltest1;
CREATE USER untrusted@localhost;
GRANT SELECT ON mysqltest1.* TO untrusted@localhost;
SHOW GRANTS FOR untrusted@localhost;
Grants for untrusted@localhost
GRANT USAGE ON *.* TO `untrusted`@`localhost`
GRANT SELECT ON `mysqltest1`.* TO `untrusted`@`localhost`
USE mysqltest1;
CREATE TABLE t1 (a INT, b CHAR(64));
flush logs;
INSERT INTO t1 VALUES (1,USER());
flush logs;
mysqlbinlog var/log/master_binlog_file > var/tmp/bug31611.sql
mysql mysqltest1 -uuntrusted < var/tmp/bug31611.sql
INSERT INTO t1 VALUES (1,USER());
ERROR 42000: INSERT command denied to user 'untrusted'@'localhost' for table 't1'
SELECT * FROM t1;
a	b
1	root@localhost
DROP DATABASE mysqltest1;
DROP USER untrusted@localhost;
Bug#32580 mysqlbinlog cannot read binlog event with user variables
USE test;
SET BINLOG_FORMAT = STATEMENT;
Warnings:
Warning	1287	'@@binlog_format' is deprecated and will be removed in a future release.
FLUSH LOGS;
CREATE TABLE t1 (a_real FLOAT, an_int INT, a_decimal DECIMAL(5,2), a_string CHAR(32));
SET @a_real = rand(20) * 1000;
SET @an_int = 1000;
SET @a_decimal = CAST(rand(19) * 999 AS DECIMAL(5,2));
SET @a_string = 'Just a test';
INSERT INTO t1 VALUES (@a_real, @an_int, @a_decimal, @a_string);
FLUSH LOGS;
SELECT * FROM t1;
a_real	158.883
an_int	1000
a_decimal	907.79
a_string	Just a test
DROP TABLE t1;
>> mysqlbinlog var/log/master_binlog_file > var/tmp/bug32580.sql
>> mysql test < var/tmp/bug32580.sql
SELECT * FROM t1;
a_real	158.883
an_int	1000
a_decimal	907.79
a_string	Just a test
DROP TABLE t1;
SET @@global.server_id= 4294967295;
RESET BINARY LOGS AND GTIDS;
FLUSH LOGS;
SELECT
(@a:=LOAD_FILE("MYSQLTEST_VARDIR/tmp/mysqlbinlog_bug37313.binlog"))
IS NOT NULL AS Loaded;
Loaded
1
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
*** Unsigned server_id 4294967295 is found: 1 ***
SET @@global.server_id= 1;
RESET BINARY LOGS AND GTIDS;
FLUSH LOGS;
RESET BINARY LOGS AND GTIDS;
FLUSH LOGS;
#
# Test if the 'BEGIN', 'ROLLBACK' and 'COMMIT' are output if the database specified exists
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=1, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=0/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C latin1 *//*!*/;
SET @@session.character_set_client=8,@@session.collation_connection=8,@@session.collation_server=8/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
create table t1(a int) engine= innodb
/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
insert into t1 (a) values (1)
/*!*/;
COMMIT/*!*/;
SET TIMESTAMP=#/*!*/;
create table t3(a int) engine= innodb
/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
insert into t3 (a) values (2)
/*!*/;
SET TIMESTAMP=#/*!*/;
ROLLBACK
/*!*/;
SET TIMESTAMP=#/*!*/;
create table t5(a int) engine= NDB
/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
insert into t5 (a) values (3)
/*!*/;
SET TIMESTAMP=#/*!*/;
COMMIT
/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
#
# Test if the 'BEGIN', 'ROLLBACK' and 'COMMIT' are output if the database specified does not exist
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=1, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=0/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C latin1 *//*!*/;
SET @@session.character_set_client=8,@@session.collation_connection=8,@@session.collation_server=8/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
BEGIN
/*!*/;
COMMIT/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
ROLLBACK
/*!*/;
SET TIMESTAMP=#/*!*/;
BEGIN
/*!*/;
SET TIMESTAMP=#/*!*/;
COMMIT
/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
#
# Test if the 'SAVEPOINT', 'ROLLBACK TO' are output if the database specified exists
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=1, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=0/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C latin1 *//*!*/;
SET @@session.character_set_client=8,@@session.collation_connection=8,@@session.collation_server=8/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
SavePoint mixed_cases
/*!*/;
use `db1`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO db1.t2 VALUES("in savepoint mixed_cases")
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO db1.t1 VALUES(40)
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
ROLLBACK TO mixed_cases
/*!*/;
use `db1`/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO db1.t2 VALUES("after rollback to")
/*!*/;
SET TIMESTAMP=#/*!*/;
INSERT INTO db1.t1 VALUES(50)
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
#
# Test if the 'SAVEPOINT', 'ROLLBACK TO' are output if the database specified does not exist
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=1, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=0/*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C latin1 *//*!*/;
SET @@session.character_set_client=8,@@session.collation_connection=8,@@session.collation_server=8/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
BEGIN
/*!*/;
use `test`/*!*/;
SET TIMESTAMP=#/*!*/;
SavePoint mixed_cases
/*!*/;
SET TIMESTAMP=#/*!*/;
ROLLBACK TO mixed_cases
/*!*/;
COMMIT/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
End of 5.0 tests
End of 5.1 tests
# Expect error for unknown argument.
Unknown option to base64-output: always
Alternatives are: 'NEVER','AUTO','UNSPEC','DECODE-ROWS'
# Expect error for unknown argument again.
Unknown option to base64-output: std_data/master-bin.000001
Alternatives are: 'NEVER','AUTO','UNSPEC','DECODE-ROWS'
RESET BINARY LOGS AND GTIDS;
CREATE DATABASE test1;
USE test1;
CREATE TABLE t1(id int);
DROP DATABASE test1;
CREATE DATABASE test1;
USE test1;
CREATE TABLE t1(id int);
DROP TABLE t1;
DROP DATABASE test1;
FLUSH LOGS;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
binlog.000002	#	Query	#	#	CREATE DATABASE test1
binlog.000002	#	Query	#	#	use `test1`; CREATE TABLE t1(id int)
binlog.000002	#	Query	#	#	DROP DATABASE test1
binlog.000002	#	Query	#	#	CREATE DATABASE test1
binlog.000002	#	Query	#	#	use `test1`; CREATE TABLE t1(id int)
binlog.000002	#	Query	#	#	use `test1`; DROP TABLE `t1` /* generated by server */
binlog.000002	#	Query	#	#	DROP DATABASE test1
RESET BINARY LOGS AND GTIDS;
USE test;
CREATE TABLE t1 (a INT);
SET GLOBAL SERVER_ID = 2;
DROP TABLE t1;
FLUSH LOGS;
SHOW TABLES IN test;
Tables_in_test
t1
SHOW TABLES IN test;
Tables_in_test
SET GLOBAL SERVER_ID = 1;
#
# Bug#12688860 : SECURITY RECOMMENDATION: PASSWORDS ON CLI
#
include/rpl/mysqlbinlog.inc
mysqlbinlog: [Warning] Using a password on the command line interface can be insecure.
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
# at #
# # server id #  end_log_pos # CRC32 # 	Start: binlog v #, server v # created # at startup
ROLLBACK/*!*/;
BINLOG '
pj7eWA8BAAAAdwAAAHsAAAAAAAQAOC4wLjItZG1yLWRlYnVnLWxvZy1hc2FuAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAACmPt5YEwANAAgAAAAABAAEAAAAXwAEGggAAAAICAgCAAAACgoKKioAEjQA
AcqIPNQ=
'/*!*/;
# at #
# # server id #  end_log_pos # CRC32 # 	Previous-GTIDs
# [empty]
# at #
# # server id #  end_log_pos # CRC32 # 	Stop
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
RESET BINARY LOGS AND GTIDS;
WARNING: --short-form is deprecated and will be removed in a future version

RESET BINARY LOGS AND GTIDS;
WARNING: --stop-never-slave-server-id is deprecated and will be removed in a future version. Use --connection-server-id instead.


End of tests
