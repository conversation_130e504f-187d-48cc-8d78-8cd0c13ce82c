# Copyright (c) 2015, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
#
# GENERATED FILE
#
cmd: testBackup
args: -n OutOfScanRecordsInLDM T1
max-time: 180

cmd: testFK
args: -n TransError T1
max-time: 240

cmd: testFK
args: -n CreateDropDuring T1
max-time: 180

cmd: testDict
args: -n DictTakeOver_1 T1
max-time: 1080

cmd: testDict
args: -n Bug53944 T1
max-time: 360

cmd: testNdbApi
args: -n MaxOperations T1 T6 T13
max-time: 540

cmd: testDict
args: -n DropTableConcurrentLCP2 T1
max-time: 1260

cmd: testTimeout
args: T1
max-time: 600

cmd: test_event
args: -n Multi_NR T1
max-time: 1260

cmd: test_event
args: -n EventOperationApplier_NS T1
max-time: 2880

cmd: testNodeRestart
args: -n pnr_lcp T1
max-time: 1260

cmd: testIndex
args: -n NFNR3 T6 T13
max-time: 1260

cmd: testIndex
args: -n NFNR1_O T6 T13
max-time: 1800

cmd: testSystemRestart
args: -n SR_DD_2b_LCP D1
max-time: 1980

cmd: testNodeRestart
args: -n NodeFailGCPOpen T1
max-time: 2340

cmd: testNodeRestart
args: -n Terror T6 T13
max-time: 2340

cmd: testNodeRestart
args: -n Bug34702 T1
max-time: 4500

