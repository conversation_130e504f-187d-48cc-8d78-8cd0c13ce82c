DROP TABLE IF EXISTS t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a';
COUNT(*)
14580
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a';
COUNT(*)
21870
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1;
COUNT(*)
4941
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1;
COUNT(*)
5751
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1134
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1215
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b';
COUNT(*)
14580
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b';
COUNT(*)
26730
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1;
COUNT(*)
4779
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1;
COUNT(*)
5589
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1134
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1215
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c';
COUNT(*)
19440
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c';
COUNT(*)
24300
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1;
COUNT(*)
6480
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1;
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1944
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1620
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d';
COUNT(*)
9720
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1;
COUNT(*)
3078
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
648
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e';
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1;
COUNT(*)
1782
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
324
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f';
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1;
COUNT(*)
1620
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
324
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a';
COUNT(*)
14580
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a';
COUNT(*)
21870
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1;
COUNT(*)
4941
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1;
COUNT(*)
5751
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1134
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1215
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b';
COUNT(*)
14580
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b';
COUNT(*)
26730
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1;
COUNT(*)
4779
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1;
COUNT(*)
5589
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1134
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1215
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c';
COUNT(*)
19440
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c';
COUNT(*)
24300
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1;
COUNT(*)
6480
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1;
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1944
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1620
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d';
COUNT(*)
9720
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1;
COUNT(*)
3078
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
648
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e';
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1;
COUNT(*)
1782
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
324
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f';
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1;
COUNT(*)
1620
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
324
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a';
COUNT(*)
14580
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a';
COUNT(*)
21870
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1;
COUNT(*)
4941
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1;
COUNT(*)
5751
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1134
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1215
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b';
COUNT(*)
14580
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b';
COUNT(*)
26730
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1;
COUNT(*)
4779
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1;
COUNT(*)
5589
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1134
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1215
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c';
COUNT(*)
19440
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c';
COUNT(*)
24300
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1;
COUNT(*)
6480
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1;
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1944
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1620
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d';
COUNT(*)
9720
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1;
COUNT(*)
3078
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
648
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e';
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1;
COUNT(*)
1782
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
324
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f';
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1;
COUNT(*)
1620
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
324
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a';
COUNT(*)
14580
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a';
COUNT(*)
21870
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1;
COUNT(*)
4941
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1;
COUNT(*)
5751
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1134
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1215
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b';
COUNT(*)
14580
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b';
COUNT(*)
26730
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1;
COUNT(*)
4779
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1;
COUNT(*)
5589
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1134
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1215
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c';
COUNT(*)
19440
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c';
COUNT(*)
24300
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1;
COUNT(*)
6480
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1;
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1944
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1620
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d';
COUNT(*)
9720
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1;
COUNT(*)
3078
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
648
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e';
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1;
COUNT(*)
1782
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
324
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f';
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1;
COUNT(*)
1620
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
324
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a';
COUNT(*)
14580
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a';
COUNT(*)
21870
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1;
COUNT(*)
4941
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1;
COUNT(*)
5751
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1134
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1215
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b';
COUNT(*)
14580
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b';
COUNT(*)
26730
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1;
COUNT(*)
4779
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1;
COUNT(*)
5589
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1134
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1215
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c';
COUNT(*)
19440
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c';
COUNT(*)
24300
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1;
COUNT(*)
6480
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1;
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1944
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1620
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d';
COUNT(*)
9720
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1;
COUNT(*)
3078
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
648
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e';
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1;
COUNT(*)
1782
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
324
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f';
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1;
COUNT(*)
1620
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
324
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a';
COUNT(*)
14580
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a';
COUNT(*)
21870
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1;
COUNT(*)
4941
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1;
COUNT(*)
5751
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1134
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1215
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b';
COUNT(*)
14580
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b';
COUNT(*)
26730
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1;
COUNT(*)
4779
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1;
COUNT(*)
5589
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1134
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1215
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c';
COUNT(*)
19440
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c';
COUNT(*)
24300
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1;
COUNT(*)
6480
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1;
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
1944
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
1620
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d';
COUNT(*)
9720
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1;
COUNT(*)
3078
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
648
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e';
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1;
COUNT(*)
1782
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
324
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f';
COUNT(*)
4860
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1;
COUNT(*)
1620
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
324
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT COUNT(*) FROM t1,t1 AS b;
COUNT(*)
72900
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y';
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
COUNT(*)
0
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
COUNT(*)
0
DROP TABLE t1;
