# For advice on how to change settings please see
# http://dev.mysql.com/doc/refman/@MYSQL_BASE_VERSION@/en/server-configuration-defaults.html

[mysqld]
#
# Remove leading # and set to the amount of RAM for the most important data
# cache in MySQL. Start at 70% of total RAM for dedicated server, else 10%.
# innodb_buffer_pool_size = 128M
#
# Remove leading # to turn on a very important data integrity option: logging
# changes to the binary log between backups.
# log_bin
#
# Remove leading # to set options mainly useful for reporting servers.
# The server defaults are faster for transactions and fast SELECTs.
# Adjust sizes as needed, experiment to find the optimal values.
# join_buffer_size = 128M
# sort_buffer_size = 2M
# read_rnd_buffer_size = 2M

host-cache-size=0
skip-name-resolve
datadir=@INSTALL_MYSQLDATADIR@
socket=@MYSQL_UNIX_ADDR@
secure-file-priv=/var/lib/mysql-files
user=mysql

pid-file=@PIDFILE_RPM@
