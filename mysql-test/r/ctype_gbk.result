drop table if exists t1;
SET @test_character_set= 'gbk';
SET @test_collation= 'gbk_chinese_ci';
SET @safe_character_set_server= @@character_set_server;
SET @safe_collation_server= @@collation_server;
SET @safe_character_set_client= @@character_set_client;
SET @safe_character_set_results= @@character_set_results;
SET character_set_server= @test_character_set;
SET collation_server= @test_collation;
CREATE DATABASE d1;
USE d1;
CREATE TABLE t1 (c CHAR(10), KEY(c));
SHOW FULL COLUMNS FROM t1;
Field	Type	Collation	Null	Key	Default	Extra	Privileges	Comment
c	char(10)	gbk_chinese_ci	YES	MUL	NULL			
INSERT INTO t1 VALUES ('aaa'),('aaaa'),('aaaaa');
SELECT c as want3results FROM t1 WHERE c LIKE 'aaa%';
want3results
aaa
aaaa
aaaaa
DROP TABLE t1;
CREATE TABLE t1 (c1 varchar(15), KEY c1 (c1(2)));
SHOW FULL COLUMNS FROM t1;
Field	Type	Collation	Null	Key	Default	Extra	Privileges	Comment
c1	varchar(15)	gbk_chinese_ci	YES	MUL	NULL			
INSERT INTO t1 VALUES ('location'),('loberge'),('lotre'),('boabab');
SELECT c1 as want3results from t1 where c1 like 'l%';
want3results
location
loberge
lotre
SELECT c1 as want3results from t1 where c1 like 'lo%';
want3results
location
loberge
lotre
SELECT c1 as want1result  from t1 where c1 like 'loc%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'loca%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'locat%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'locati%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'locatio%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'location%';
want1result
location
DROP TABLE t1;
create table t1 (a set('a') not null);
insert ignore into t1 values (),();
Warnings:
Warning	1364	Field 'a' doesn't have a default value
select cast(a as char(1)) from t1;
cast(a as char(1))


select a sounds like a from t1;
a sounds like a
1
1
select 1 from t1 order by cast(a as char(1));
1
1
1
drop table t1;
set names utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
create table t1 (
name varchar(10),
level smallint unsigned);
show create table t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `name` varchar(10) DEFAULT NULL,
  `level` smallint unsigned DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=gbk
insert into t1 values ('string',1);
select concat(name,space(level)), concat(name, repeat(' ',level)) from t1;
concat(name,space(level))	concat(name, repeat(' ',level))
string 	string 
drop table t1;
DROP DATABASE d1;
USE test;
SET character_set_server= @safe_character_set_server;
SET collation_server= @safe_collation_server;
SET character_set_client= @safe_character_set_client;
SET character_set_results= @safe_character_set_results;
SET NAMES gbk;
SET collation_connection='gbk_chinese_ci';
create table t1 select repeat('a',4000) a;
delete from t1;
insert into t1 values ('a'), ('a '), ('a\t');
select collation(a),hex(a) from t1 order by a;
collation(a)	hex(a)
gbk_chinese_ci	6109
gbk_chinese_ci	61
gbk_chinese_ci	6120
drop table t1;
create table t1 engine=innodb select repeat('a',50) as c1;
alter table t1 add index(c1(5));
insert into t1 values ('abcdefg'),('abcde100'),('abcde110'),('abcde111');
select collation(c1) from t1 limit 1;
collation(c1)
gbk_chinese_ci
select c1 from t1 where c1 like 'abcdef%' order by c1;
c1
abcdefg
select c1 from t1 where c1 like 'abcde1%' order by c1;
c1
abcde100
abcde110
abcde111
select c1 from t1 where c1 like 'abcde11%' order by c1;
c1
abcde110
abcde111
select c1 from t1 where c1 like 'abcde111%' order by c1;
c1
abcde111
drop table t1;
select @@collation_connection;
@@collation_connection
gbk_chinese_ci
create table t1 ROW_FORMAT=DYNAMIC select repeat('a',50) as c1 ;
insert into t1 values('abcdef');
insert into t1 values('_bcdef');
insert into t1 values('a_cdef');
insert into t1 values('ab_def');
insert into t1 values('abc_ef');
insert into t1 values('abcd_f');
insert into t1 values('abcde_');
select c1 as c1u from t1 where c1 like 'ab\_def';
c1u
ab_def
select c1 as c2h from t1 where c1 like 'ab#_def' escape '#';
c2h
ab_def
drop table t1;
CREATE TABLE t1 AS
SELECT 10 AS a, REPEAT('a',20) AS b, REPEAT('a',8) AS c, REPEAT('a',8) AS d;
ALTER TABLE t1 ADD PRIMARY KEY(a), ADD KEY(b);
INSERT INTO t1 (a, b) VALUES (1, repeat(0xF1F2,5));
INSERT INTO t1 (a, b) VALUES (2, repeat(0xF1F2,10));
INSERT INTO t1 (a, b) VALUES (3, repeat(0xF1F2,11));
INSERT INTO t1 (a, b) VALUES (4, repeat(0xF1F2,12));
SELECT hex(concat(repeat(0xF1F2, 10), '%'));
hex(concat(repeat(0xF1F2, 10), '%'))
F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F225
3 rows expected
SELECT a, hex(b), c FROM t1 WHERE b LIKE concat(repeat(0xF1F2,10), '%');
a	hex(b)	c
2	F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2	NULL
3	F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2	NULL
4	F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2	NULL
DROP TABLE t1;
drop table if exists t1;
create table t1 select repeat('a',10) as c1;
delete from t1;
insert into t1 values (0x20),(0x21),(0x22),(0x23),(0x24),(0x25),(0x26),(0x27),(0x28),(0x29),(0x2A),(0x2B),(0x2C),(0x2D),(0x2E),(0x2F);
insert into t1 values (0x30),(0x31),(0x32),(0x33),(0x34),(0x35),(0x36),(0x37),(0x38),(0x39),(0x3A),(0x3B),(0x3C),(0x3D),(0x3E),(0x3F);
insert into t1 values (0x40),(0x41),(0x42),(0x43),(0x44),(0x45),(0x46),(0x47),(0x48),(0x49),(0x4A),(0x4B),(0x4C),(0x4D),(0x4E),(0x4F);
insert into t1 values (0x50),(0x51),(0x52),(0x53),(0x54),(0x55),(0x56),(0x57),(0x58),(0x59),(0x5A),(0x5B),(0x5C),(0x5D),(0x5E),(0x5F);
insert into t1 values (0x60),(0x61),(0x62),(0x63),(0x64),(0x65),(0x66),(0x67),(0x68),(0x69),(0x6A),(0x6B),(0x6C),(0x6D),(0x6E),(0x6F);
insert into t1 values (0x70),(0x71),(0x72),(0x73),(0x74),(0x75),(0x76),(0x77),(0x78),(0x79),(0x7A),(0x7B),(0x7C),(0x7D),(0x7E),(0x7F);
SELECT HEX(cx), cy
FROM (SELECT GROUP_CONCAT(c1 ORDER BY binary c1 SEPARATOR '') AS cx,
GROUP_CONCAT(HEX(c1) ORDER BY BINARY c1) AS cy
FROM t1
GROUP BY c1
) AS dt;
HEX(cx)	cy
20	20
21	21
22	22
23	23
24	24
25	25
26	26
27	27
28	28
29	29
2A	2A
2B	2B
2C	2C
2D	2D
2E	2E
2F	2F
30	30
31	31
32	32
33	33
34	34
35	35
36	36
37	37
38	38
39	39
3A	3A
3B	3B
3C	3C
3D	3D
3E	3E
3F	3F
40	40
4161	41,61
4262	42,62
4363	43,63
4464	44,64
4565	45,65
4666	46,66
4767	47,67
4868	48,68
4969	49,69
4A6A	4A,6A
4B6B	4B,6B
4C6C	4C,6C
4D6D	4D,6D
4E6E	4E,6E
4F6F	4F,6F
5070	50,70
5171	51,71
5272	52,72
5373	53,73
5474	54,74
5575	55,75
5676	56,76
5777	57,77
5878	58,78
59797E	59,79,7E
5A7A	5A,7A
5D	5D
5B	5B
5C	5C
5E	5E
5F	5F
60	60
7B	7B
7C	7C
7D	7D
7F	7F
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
drop table t1;
SET collation_connection='gbk_bin';
create table t1 select repeat('a',4000) a;
delete from t1;
insert into t1 values ('a'), ('a '), ('a\t');
select collation(a),hex(a) from t1 order by a;
collation(a)	hex(a)
gbk_bin	6109
gbk_bin	61
gbk_bin	6120
drop table t1;
create table t1 engine=innodb select repeat('a',50) as c1;
alter table t1 add index(c1(5));
insert into t1 values ('abcdefg'),('abcde100'),('abcde110'),('abcde111');
select collation(c1) from t1 limit 1;
collation(c1)
gbk_bin
select c1 from t1 where c1 like 'abcdef%' order by c1;
c1
abcdefg
select c1 from t1 where c1 like 'abcde1%' order by c1;
c1
abcde100
abcde110
abcde111
select c1 from t1 where c1 like 'abcde11%' order by c1;
c1
abcde110
abcde111
select c1 from t1 where c1 like 'abcde111%' order by c1;
c1
abcde111
drop table t1;
select @@collation_connection;
@@collation_connection
gbk_bin
create table t1 ROW_FORMAT=DYNAMIC select repeat('a',50) as c1 ;
insert into t1 values('abcdef');
insert into t1 values('_bcdef');
insert into t1 values('a_cdef');
insert into t1 values('ab_def');
insert into t1 values('abc_ef');
insert into t1 values('abcd_f');
insert into t1 values('abcde_');
select c1 as c1u from t1 where c1 like 'ab\_def';
c1u
ab_def
select c1 as c2h from t1 where c1 like 'ab#_def' escape '#';
c2h
ab_def
drop table t1;
CREATE TABLE t1 AS
SELECT 10 AS a, REPEAT('a',20) AS b, REPEAT('a',8) AS c, REPEAT('a',8) AS d;
ALTER TABLE t1 ADD PRIMARY KEY(a), ADD KEY(b);
INSERT INTO t1 (a, b) VALUES (1, repeat(0xF1F2,5));
INSERT INTO t1 (a, b) VALUES (2, repeat(0xF1F2,10));
INSERT INTO t1 (a, b) VALUES (3, repeat(0xF1F2,11));
INSERT INTO t1 (a, b) VALUES (4, repeat(0xF1F2,12));
SELECT hex(concat(repeat(0xF1F2, 10), '%'));
hex(concat(repeat(0xF1F2, 10), '%'))
F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F225
3 rows expected
SELECT a, hex(b), c FROM t1 WHERE b LIKE concat(repeat(0xF1F2,10), '%');
a	hex(b)	c
2	F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2	NULL
3	F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2	NULL
4	F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2	NULL
DROP TABLE t1;
SET NAMES gbk;
CREATE TABLE t1 (a text) character set gbk;
INSERT INTO t1 VALUES (0xA3A0),(0xA1A1);
SELECT hex(a) FROM t1 ORDER BY a;
hex(a)
A1A1
A3A0
DROP TABLE t1;
select hex(convert(_gbk 0xA14041 using ucs2));
hex(convert(_gbk 0xA14041 using ucs2))
003F0041
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
create table t1 (c1 text not null, c2 text not null) character set gbk;
alter table t1 change c1 c1 mediumtext  character set gbk not null;
show create table t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumtext CHARACTER SET gbk COLLATE gbk_chinese_ci NOT NULL,
  `c2` text NOT NULL
) ENGINE=default_engine DEFAULT CHARSET=gbk
drop table t1;
CREATE TABLE t1(a MEDIUMTEXT CHARACTER SET gbk,
b MEDIUMTEXT CHARACTER SET big5);
INSERT INTO t1 VALUES
(REPEAT(0x1125,200000), REPEAT(0x1125,200000)), ('', ''), ('', '');
SELECT a FROM t1 GROUP BY 1 LIMIT 1 INTO @nullll;
SELECT b FROM t1 GROUP BY 1 LIMIT 1 INTO @nullll;
DROP TABLES t1;
End of 5.0 tests
#
# Start of 5.5 tests
#
#
# Testing WL#4583 Case conversion in Asian character sets 
#
SET NAMES utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SET collation_connection=gbk_chinese_ci;
CREATE TABLE t1 (b VARCHAR(2));
INSERT INTO t1 VALUES ('0'),('1'),('2'),('3'),('4'),('5'),('6'),('7');
INSERT INTO t1 VALUES ('8'),('9'),('A'),('B'),('C'),('D'),('E'),('F');
CREATE TEMPORARY TABLE head AS SELECT concat(b1.b, b2.b) AS head FROM t1 b1, t1 b2;
CREATE TEMPORARY TABLE tail AS SELECT concat(b1.b, b2.b) AS tail FROM t1 b1, t1 b2;
DROP TABLE t1;
CREATE TABLE t1 AS
SELECT concat(head, tail) AS code, ' ' AS a
FROM head, tail
WHERE (head BETWEEN '80' AND 'FF') AND (tail BETWEEN '20' AND 'FF')
ORDER BY head, tail;
DROP TEMPORARY TABLE head, tail;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `code` varchar(8) DEFAULT NULL,
  `a` varchar(1) CHARACTER SET gbk NOT NULL DEFAULT ''
) ENGINE=default_engine DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SET @@session.max_error_count = 64;
UPDATE IGNORE t1 SET a=unhex(code) ORDER BY code;
Warnings:
Warning	1366	Incorrect string value: '\x80 ' for column 'a' at row 1
Warning	1366	Incorrect string value: '\x80!' for column 'a' at row 2
Warning	1366	Incorrect string value: '\x80"' for column 'a' at row 3
Warning	1366	Incorrect string value: '\x80#' for column 'a' at row 4
Warning	1366	Incorrect string value: '\x80$' for column 'a' at row 5
Warning	1366	Incorrect string value: '\x80%' for column 'a' at row 6
Warning	1366	Incorrect string value: '\x80&' for column 'a' at row 7
Warning	1366	Incorrect string value: '\x80'' for column 'a' at row 8
Warning	1366	Incorrect string value: '\x80(' for column 'a' at row 9
Warning	1366	Incorrect string value: '\x80)' for column 'a' at row 10
Warning	1366	Incorrect string value: '\x80*' for column 'a' at row 11
Warning	1366	Incorrect string value: '\x80+' for column 'a' at row 12
Warning	1366	Incorrect string value: '\x80,' for column 'a' at row 13
Warning	1366	Incorrect string value: '\x80-' for column 'a' at row 14
Warning	1366	Incorrect string value: '\x80.' for column 'a' at row 15
Warning	1366	Incorrect string value: '\x80/' for column 'a' at row 16
Warning	1366	Incorrect string value: '\x800' for column 'a' at row 17
Warning	1366	Incorrect string value: '\x801' for column 'a' at row 18
Warning	1366	Incorrect string value: '\x802' for column 'a' at row 19
Warning	1366	Incorrect string value: '\x803' for column 'a' at row 20
Warning	1366	Incorrect string value: '\x804' for column 'a' at row 21
Warning	1366	Incorrect string value: '\x805' for column 'a' at row 22
Warning	1366	Incorrect string value: '\x806' for column 'a' at row 23
Warning	1366	Incorrect string value: '\x807' for column 'a' at row 24
Warning	1366	Incorrect string value: '\x808' for column 'a' at row 25
Warning	1366	Incorrect string value: '\x809' for column 'a' at row 26
Warning	1366	Incorrect string value: '\x80:' for column 'a' at row 27
Warning	1366	Incorrect string value: '\x80;' for column 'a' at row 28
Warning	1366	Incorrect string value: '\x80<' for column 'a' at row 29
Warning	1366	Incorrect string value: '\x80=' for column 'a' at row 30
Warning	1366	Incorrect string value: '\x80>' for column 'a' at row 31
Warning	1366	Incorrect string value: '\x80?' for column 'a' at row 32
Warning	1366	Incorrect string value: '\x80@' for column 'a' at row 33
Warning	1366	Incorrect string value: '\x80A' for column 'a' at row 34
Warning	1366	Incorrect string value: '\x80B' for column 'a' at row 35
Warning	1366	Incorrect string value: '\x80C' for column 'a' at row 36
Warning	1366	Incorrect string value: '\x80D' for column 'a' at row 37
Warning	1366	Incorrect string value: '\x80E' for column 'a' at row 38
Warning	1366	Incorrect string value: '\x80F' for column 'a' at row 39
Warning	1366	Incorrect string value: '\x80G' for column 'a' at row 40
Warning	1366	Incorrect string value: '\x80H' for column 'a' at row 41
Warning	1366	Incorrect string value: '\x80I' for column 'a' at row 42
Warning	1366	Incorrect string value: '\x80J' for column 'a' at row 43
Warning	1366	Incorrect string value: '\x80K' for column 'a' at row 44
Warning	1366	Incorrect string value: '\x80L' for column 'a' at row 45
Warning	1366	Incorrect string value: '\x80M' for column 'a' at row 46
Warning	1366	Incorrect string value: '\x80N' for column 'a' at row 47
Warning	1366	Incorrect string value: '\x80O' for column 'a' at row 48
Warning	1366	Incorrect string value: '\x80P' for column 'a' at row 49
Warning	1366	Incorrect string value: '\x80Q' for column 'a' at row 50
Warning	1366	Incorrect string value: '\x80R' for column 'a' at row 51
Warning	1366	Incorrect string value: '\x80S' for column 'a' at row 52
Warning	1366	Incorrect string value: '\x80T' for column 'a' at row 53
Warning	1366	Incorrect string value: '\x80U' for column 'a' at row 54
Warning	1366	Incorrect string value: '\x80V' for column 'a' at row 55
Warning	1366	Incorrect string value: '\x80W' for column 'a' at row 56
Warning	1366	Incorrect string value: '\x80X' for column 'a' at row 57
Warning	1366	Incorrect string value: '\x80Y' for column 'a' at row 58
Warning	1366	Incorrect string value: '\x80Z' for column 'a' at row 59
Warning	1366	Incorrect string value: '\x80[' for column 'a' at row 60
Warning	1366	Incorrect string value: '\x80\' for column 'a' at row 61
Warning	1366	Incorrect string value: '\x80]' for column 'a' at row 62
Warning	1366	Incorrect string value: '\x80^' for column 'a' at row 63
Warning	1366	Incorrect string value: '\x80_' for column 'a' at row 64
SET @@session.max_error_count = DEFAULT;
SELECT COUNT(*) FROM t1 WHERE a<>'';
COUNT(*)
23940
SELECT code, hex(upper(a)), hex(lower(a)),a, upper(a), lower(a) FROM t1 WHERE hex(a)<>hex(upper(a)) OR hex(a)<>hex(lower(a));
code	hex(upper(a))	hex(lower(a))	a	upper(a)	lower(a)
A2A1	A2F1	A2A1	ⅰ	Ⅰ	ⅰ
A2A2	A2F2	A2A2	ⅱ	Ⅱ	ⅱ
A2A3	A2F3	A2A3	ⅲ	Ⅲ	ⅲ
A2A4	A2F4	A2A4	ⅳ	Ⅳ	ⅳ
A2A5	A2F5	A2A5	ⅴ	Ⅴ	ⅴ
A2A6	A2F6	A2A6	ⅵ	Ⅵ	ⅵ
A2A7	A2F7	A2A7	ⅶ	Ⅶ	ⅶ
A2A8	A2F8	A2A8	ⅷ	Ⅷ	ⅷ
A2A9	A2F9	A2A9	ⅸ	Ⅸ	ⅸ
A2AA	A2FA	A2AA	ⅹ	Ⅹ	ⅹ
A2F1	A2F1	A2A1	Ⅰ	Ⅰ	ⅰ
A2F2	A2F2	A2A2	Ⅱ	Ⅱ	ⅱ
A2F3	A2F3	A2A3	Ⅲ	Ⅲ	ⅲ
A2F4	A2F4	A2A4	Ⅳ	Ⅳ	ⅳ
A2F5	A2F5	A2A5	Ⅴ	Ⅴ	ⅴ
A2F6	A2F6	A2A6	Ⅵ	Ⅵ	ⅵ
A2F7	A2F7	A2A7	Ⅶ	Ⅶ	ⅶ
A2F8	A2F8	A2A8	Ⅷ	Ⅷ	ⅷ
A2F9	A2F9	A2A9	Ⅸ	Ⅸ	ⅸ
A2FA	A2FA	A2AA	Ⅹ	Ⅹ	ⅹ
A3C1	A3C1	A3E1	Ａ	Ａ	ａ
A3C2	A3C2	A3E2	Ｂ	Ｂ	ｂ
A3C3	A3C3	A3E3	Ｃ	Ｃ	ｃ
A3C4	A3C4	A3E4	Ｄ	Ｄ	ｄ
A3C5	A3C5	A3E5	Ｅ	Ｅ	ｅ
A3C6	A3C6	A3E6	Ｆ	Ｆ	ｆ
A3C7	A3C7	A3E7	Ｇ	Ｇ	ｇ
A3C8	A3C8	A3E8	Ｈ	Ｈ	ｈ
A3C9	A3C9	A3E9	Ｉ	Ｉ	ｉ
A3CA	A3CA	A3EA	Ｊ	Ｊ	ｊ
A3CB	A3CB	A3EB	Ｋ	Ｋ	ｋ
A3CC	A3CC	A3EC	Ｌ	Ｌ	ｌ
A3CD	A3CD	A3ED	Ｍ	Ｍ	ｍ
A3CE	A3CE	A3EE	Ｎ	Ｎ	ｎ
A3CF	A3CF	A3EF	Ｏ	Ｏ	ｏ
A3D0	A3D0	A3F0	Ｐ	Ｐ	ｐ
A3D1	A3D1	A3F1	Ｑ	Ｑ	ｑ
A3D2	A3D2	A3F2	Ｒ	Ｒ	ｒ
A3D3	A3D3	A3F3	Ｓ	Ｓ	ｓ
A3D4	A3D4	A3F4	Ｔ	Ｔ	ｔ
A3D5	A3D5	A3F5	Ｕ	Ｕ	ｕ
A3D6	A3D6	A3F6	Ｖ	Ｖ	ｖ
A3D7	A3D7	A3F7	Ｗ	Ｗ	ｗ
A3D8	A3D8	A3F8	Ｘ	Ｘ	ｘ
A3D9	A3D9	A3F9	Ｙ	Ｙ	ｙ
A3DA	A3DA	A3FA	Ｚ	Ｚ	ｚ
A3E1	A3C1	A3E1	ａ	Ａ	ａ
A3E2	A3C2	A3E2	ｂ	Ｂ	ｂ
A3E3	A3C3	A3E3	ｃ	Ｃ	ｃ
A3E4	A3C4	A3E4	ｄ	Ｄ	ｄ
A3E5	A3C5	A3E5	ｅ	Ｅ	ｅ
A3E6	A3C6	A3E6	ｆ	Ｆ	ｆ
A3E7	A3C7	A3E7	ｇ	Ｇ	ｇ
A3E8	A3C8	A3E8	ｈ	Ｈ	ｈ
A3E9	A3C9	A3E9	ｉ	Ｉ	ｉ
A3EA	A3CA	A3EA	ｊ	Ｊ	ｊ
A3EB	A3CB	A3EB	ｋ	Ｋ	ｋ
A3EC	A3CC	A3EC	ｌ	Ｌ	ｌ
A3ED	A3CD	A3ED	ｍ	Ｍ	ｍ
A3EE	A3CE	A3EE	ｎ	Ｎ	ｎ
A3EF	A3CF	A3EF	ｏ	Ｏ	ｏ
A3F0	A3D0	A3F0	ｐ	Ｐ	ｐ
A3F1	A3D1	A3F1	ｑ	Ｑ	ｑ
A3F2	A3D2	A3F2	ｒ	Ｒ	ｒ
A3F3	A3D3	A3F3	ｓ	Ｓ	ｓ
A3F4	A3D4	A3F4	ｔ	Ｔ	ｔ
A3F5	A3D5	A3F5	ｕ	Ｕ	ｕ
A3F6	A3D6	A3F6	ｖ	Ｖ	ｖ
A3F7	A3D7	A3F7	ｗ	Ｗ	ｗ
A3F8	A3D8	A3F8	ｘ	Ｘ	ｘ
A3F9	A3D9	A3F9	ｙ	Ｙ	ｙ
A3FA	A3DA	A3FA	ｚ	Ｚ	ｚ
A6A1	A6A1	A6C1	Α	Α	α
A6A2	A6A2	A6C2	Β	Β	β
A6A3	A6A3	A6C3	Γ	Γ	γ
A6A4	A6A4	A6C4	Δ	Δ	δ
A6A5	A6A5	A6C5	Ε	Ε	ε
A6A6	A6A6	A6C6	Ζ	Ζ	ζ
A6A7	A6A7	A6C7	Η	Η	η
A6A8	A6A8	A6C8	Θ	Θ	θ
A6A9	A6A9	A6C9	Ι	Ι	ι
A6AA	A6AA	A6CA	Κ	Κ	κ
A6AB	A6AB	A6CB	Λ	Λ	λ
A6AC	A6AC	A6CC	Μ	Μ	μ
A6AD	A6AD	A6CD	Ν	Ν	ν
A6AE	A6AE	A6CE	Ξ	Ξ	ξ
A6AF	A6AF	A6CF	Ο	Ο	ο
A6B0	A6B0	A6D0	Π	Π	π
A6B1	A6B1	A6D1	Ρ	Ρ	ρ
A6B2	A6B2	A6D2	Σ	Σ	σ
A6B3	A6B3	A6D3	Τ	Τ	τ
A6B4	A6B4	A6D4	Υ	Υ	υ
A6B5	A6B5	A6D5	Φ	Φ	φ
A6B6	A6B6	A6D6	Χ	Χ	χ
A6B7	A6B7	A6D7	Ψ	Ψ	ψ
A6B8	A6B8	A6D8	Ω	Ω	ω
A6C1	A6A1	A6C1	α	Α	α
A6C2	A6A2	A6C2	β	Β	β
A6C3	A6A3	A6C3	γ	Γ	γ
A6C4	A6A4	A6C4	δ	Δ	δ
A6C5	A6A5	A6C5	ε	Ε	ε
A6C6	A6A6	A6C6	ζ	Ζ	ζ
A6C7	A6A7	A6C7	η	Η	η
A6C8	A6A8	A6C8	θ	Θ	θ
A6C9	A6A9	A6C9	ι	Ι	ι
A6CA	A6AA	A6CA	κ	Κ	κ
A6CB	A6AB	A6CB	λ	Λ	λ
A6CC	A6AC	A6CC	μ	Μ	μ
A6CD	A6AD	A6CD	ν	Ν	ν
A6CE	A6AE	A6CE	ξ	Ξ	ξ
A6CF	A6AF	A6CF	ο	Ο	ο
A6D0	A6B0	A6D0	π	Π	π
A6D1	A6B1	A6D1	ρ	Ρ	ρ
A6D2	A6B2	A6D2	σ	Σ	σ
A6D3	A6B3	A6D3	τ	Τ	τ
A6D4	A6B4	A6D4	υ	Υ	υ
A6D5	A6B5	A6D5	φ	Φ	φ
A6D6	A6B6	A6D6	χ	Χ	χ
A6D7	A6B7	A6D7	ψ	Ψ	ψ
A6D8	A6B8	A6D8	ω	Ω	ω
A7A1	A7A1	A7D1	А	А	а
A7A2	A7A2	A7D2	Б	Б	б
A7A3	A7A3	A7D3	В	В	в
A7A4	A7A4	A7D4	Г	Г	г
A7A5	A7A5	A7D5	Д	Д	д
A7A6	A7A6	A7D6	Е	Е	е
A7A7	A7A7	A7D7	Ё	Ё	ё
A7A8	A7A8	A7D8	Ж	Ж	ж
A7A9	A7A9	A7D9	З	З	з
A7AA	A7AA	A7DA	И	И	и
A7AB	A7AB	A7DB	Й	Й	й
A7AC	A7AC	A7DC	К	К	к
A7AD	A7AD	A7DD	Л	Л	л
A7AE	A7AE	A7DE	М	М	м
A7AF	A7AF	A7DF	Н	Н	н
A7B0	A7B0	A7E0	О	О	о
A7B1	A7B1	A7E1	П	П	п
A7B2	A7B2	A7E2	Р	Р	р
A7B3	A7B3	A7E3	С	С	с
A7B4	A7B4	A7E4	Т	Т	т
A7B5	A7B5	A7E5	У	У	у
A7B6	A7B6	A7E6	Ф	Ф	ф
A7B7	A7B7	A7E7	Х	Х	х
A7B8	A7B8	A7E8	Ц	Ц	ц
A7B9	A7B9	A7E9	Ч	Ч	ч
A7BA	A7BA	A7EA	Ш	Ш	ш
A7BB	A7BB	A7EB	Щ	Щ	щ
A7BC	A7BC	A7EC	Ъ	Ъ	ъ
A7BD	A7BD	A7ED	Ы	Ы	ы
A7BE	A7BE	A7EE	Ь	Ь	ь
A7BF	A7BF	A7EF	Э	Э	э
A7C0	A7C0	A7F0	Ю	Ю	ю
A7C1	A7C1	A7F1	Я	Я	я
A7D1	A7A1	A7D1	а	А	а
A7D2	A7A2	A7D2	б	Б	б
A7D3	A7A3	A7D3	в	В	в
A7D4	A7A4	A7D4	г	Г	г
A7D5	A7A5	A7D5	д	Д	д
A7D6	A7A6	A7D6	е	Е	е
A7D7	A7A7	A7D7	ё	Ё	ё
A7D8	A7A8	A7D8	ж	Ж	ж
A7D9	A7A9	A7D9	з	З	з
A7DA	A7AA	A7DA	и	И	и
A7DB	A7AB	A7DB	й	Й	й
A7DC	A7AC	A7DC	к	К	к
A7DD	A7AD	A7DD	л	Л	л
A7DE	A7AE	A7DE	м	М	м
A7DF	A7AF	A7DF	н	Н	н
A7E0	A7B0	A7E0	о	О	о
A7E1	A7B1	A7E1	п	П	п
A7E2	A7B2	A7E2	р	Р	р
A7E3	A7B3	A7E3	с	С	с
A7E4	A7B4	A7E4	т	Т	т
A7E5	A7B5	A7E5	у	У	у
A7E6	A7B6	A7E6	ф	Ф	ф
A7E7	A7B7	A7E7	х	Х	х
A7E8	A7B8	A7E8	ц	Ц	ц
A7E9	A7B9	A7E9	ч	Ч	ч
A7EA	A7BA	A7EA	ш	Ш	ш
A7EB	A7BB	A7EB	щ	Щ	щ
A7EC	A7BC	A7EC	ъ	Ъ	ъ
A7ED	A7BD	A7ED	ы	Ы	ы
A7EE	A7BE	A7EE	ь	Ь	ь
A7EF	A7BF	A7EF	э	Э	э
A7F0	A7C0	A7F0	ю	Ю	ю
A7F1	A7C1	A7F1	я	Я	я
SELECT * FROM t1
WHERE HEX(CAST(LOWER(a) AS CHAR CHARACTER SET utf8mb3)) <>
HEX(LOWER(CAST(a AS CHAR CHARACTER SET utf8mb3))) ORDER BY code;
code	a
A2FB	Ⅺ
A2FC	Ⅻ
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT * FROM t1
WHERE HEX(CAST(UPPER(a) AS CHAR CHARACTER SET utf8mb3)) <>
HEX(UPPER(CAST(a AS CHAR CHARACTER SET utf8mb3))) ORDER BY code;
code	a
A8A1	ā
A8A2	á
A8A3	ǎ
A8A4	à
A8A5	ē
A8A6	é
A8A7	ě
A8A8	è
A8A9	ī
A8AA	í
A8AB	ǐ
A8AC	ì
A8AD	ō
A8AE	ó
A8AF	ǒ
A8B0	ò
A8B1	ū
A8B2	ú
A8B3	ǔ
A8B4	ù
A8B5	ǖ
A8B6	ǘ
A8B7	ǚ
A8B8	ǜ
A8B9	ü
A8BA	ê
A8BD	ń
A8BE	ň
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
DROP TABLE t1;
#
# End of 5.5 tests
#
#
# Start of 5.6 tests
#
#
# WL#3664 WEIGHT_STRING
#
set names gbk;
select @@collation_connection;
@@collation_connection
gbk_chinese_ci
select hex(weight_string('a'));
hex(weight_string('a'))
41
select hex(weight_string('A'));
hex(weight_string('A'))
41
select hex(weight_string('abc'));
hex(weight_string('abc'))
414243
select hex(weight_string('abc' as char(2)));
hex(weight_string('abc' as char(2)))
4142
select hex(weight_string('abc' as char(3)));
hex(weight_string('abc' as char(3)))
414243
select hex(weight_string('abc' as char(5)));
hex(weight_string('abc' as char(5)))
4142432020
select hex(weight_string('abc', 1, 2, 0xC0));
hex(weight_string('abc', 1, 2, 0xC0))
41
select hex(weight_string('abc', 2, 2, 0xC0));
hex(weight_string('abc', 2, 2, 0xC0))
4142
select hex(weight_string('abc', 3, 2, 0xC0));
hex(weight_string('abc', 3, 2, 0xC0))
414220
select hex(weight_string('abc', 4, 2, 0xC0));
hex(weight_string('abc', 4, 2, 0xC0))
41422020
select hex(weight_string('abc', 5, 2, 0xC0));
hex(weight_string('abc', 5, 2, 0xC0))
4142202020
select hex(weight_string('abc',25, 2, 0xC0));
hex(weight_string('abc',25, 2, 0xC0))
41422020202020202020202020202020202020202020202020
select hex(weight_string('abc', 1, 3, 0xC0));
hex(weight_string('abc', 1, 3, 0xC0))
41
select hex(weight_string('abc', 2, 3, 0xC0));
hex(weight_string('abc', 2, 3, 0xC0))
4142
select hex(weight_string('abc', 3, 3, 0xC0));
hex(weight_string('abc', 3, 3, 0xC0))
414243
select hex(weight_string('abc', 4, 3, 0xC0));
hex(weight_string('abc', 4, 3, 0xC0))
41424320
select hex(weight_string('abc', 5, 3, 0xC0));
hex(weight_string('abc', 5, 3, 0xC0))
4142432020
select hex(weight_string('abc',25, 3, 0xC0));
hex(weight_string('abc',25, 3, 0xC0))
41424320202020202020202020202020202020202020202020
select hex(weight_string('abc', 1, 4, 0xC0));
hex(weight_string('abc', 1, 4, 0xC0))
41
select hex(weight_string('abc', 2, 4, 0xC0));
hex(weight_string('abc', 2, 4, 0xC0))
4142
select hex(weight_string('abc', 3, 4, 0xC0));
hex(weight_string('abc', 3, 4, 0xC0))
414243
select hex(weight_string('abc', 4, 4, 0xC0));
hex(weight_string('abc', 4, 4, 0xC0))
41424320
select hex(weight_string('abc', 5, 4, 0xC0));
hex(weight_string('abc', 5, 4, 0xC0))
4142432020
select hex(weight_string('abc',25, 4, 0xC0));
hex(weight_string('abc',25, 4, 0xC0))
41424320202020202020202020202020202020202020202020
select collation(cast(0xA1A1 as char));
collation(cast(0xA1A1 as char))
gbk_chinese_ci
select hex(weight_string(cast(0x6141 as char)));
hex(weight_string(cast(0x6141 as char)))
4141
select hex(weight_string(cast(0xA1A1 as char)));
hex(weight_string(cast(0xA1A1 as char)))
810B
select hex(weight_string(cast(0xA1A1 as char) as char(1)));
hex(weight_string(cast(0xA1A1 as char) as char(1)))
810B
select hex(weight_string(cast(0xA1A1A1A1 as char) as char(1)));
hex(weight_string(cast(0xA1A1A1A1 as char) as char(1)))
810B
select hex(weight_string(cast(0xA1A1 as char) as char(3)));
hex(weight_string(cast(0xA1A1 as char) as char(3)))
810B2020
select hex(weight_string(cast(0xA1A1A1A1 as char) as char(3)));
hex(weight_string(cast(0xA1A1A1A1 as char) as char(3)))
810B810B20
select hex(weight_string(cast(0x40A1A1 as char) as char(3)));
hex(weight_string(cast(0x40A1A1 as char) as char(3)))
40810B20
select hex(weight_string(cast(0x40A1A1A1A1 as char) as char(3)));
hex(weight_string(cast(0x40A1A1A1A1 as char) as char(3)))
40810B810B
select hex(weight_string(cast(0x40A1A1A1A1A1A1 as char) as char(3)));
hex(weight_string(cast(0x40A1A1A1A1A1A1 as char) as char(3)))
40810B810B
select hex(weight_string(cast(0x4040A1A1A1A1A1A1 as char) as char(3)));
hex(weight_string(cast(0x4040A1A1A1A1A1A1 as char) as char(3)))
4040810B
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 1, 2, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 1, 2, 0xC0))
81
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 2, 2, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 2, 2, 0xC0))
810B
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 3, 2, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 3, 2, 0xC0))
810B81
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 4, 2, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 4, 2, 0xC0))
810B810B
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 5, 2, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 5, 2, 0xC0))
810B810B20
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char),25, 2, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char),25, 2, 0xC0))
810B810B202020202020202020202020202020202020202020
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 1, 3, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 1, 3, 0xC0))
81
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 2, 3, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 2, 3, 0xC0))
810B
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 3, 3, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 3, 3, 0xC0))
810B81
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 4, 3, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 4, 3, 0xC0))
810B810B
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 5, 3, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 5, 3, 0xC0))
810B810B81
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char),25, 3, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char),25, 3, 0xC0))
810B810B810B20202020202020202020202020202020202020
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 1, 4, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 1, 4, 0xC0))
81
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 2, 4, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 2, 4, 0xC0))
810B
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 3, 4, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 3, 4, 0xC0))
810B81
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 4, 4, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 4, 4, 0xC0))
810B810B
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 5, 4, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 5, 4, 0xC0))
810B810B81
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char),25, 4, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char),25, 4, 0xC0))
810B810B810B20202020202020202020202020202020202020
select collation(cast(0x8140 as char));
collation(cast(0x8140 as char))
gbk_chinese_ci
select hex(weight_string(cast(0x6141 as char)));
hex(weight_string(cast(0x6141 as char)))
4141
select hex(weight_string(cast(0x8140 as char)));
hex(weight_string(cast(0x8140 as char)))
A2CD
select hex(weight_string(cast(0x8140 as char) as char(1)));
hex(weight_string(cast(0x8140 as char) as char(1)))
A2CD
select hex(weight_string(cast(0x81408140 as char) as char(1)));
hex(weight_string(cast(0x81408140 as char) as char(1)))
A2CD
select hex(weight_string(cast(0x8140 as char) as char(3)));
hex(weight_string(cast(0x8140 as char) as char(3)))
A2CD2020
select hex(weight_string(cast(0x81408140 as char) as char(3)));
hex(weight_string(cast(0x81408140 as char) as char(3)))
A2CDA2CD20
select hex(weight_string(cast(0x408140 as char) as char(3)));
hex(weight_string(cast(0x408140 as char) as char(3)))
40A2CD20
select hex(weight_string(cast(0x4081408140 as char) as char(3)));
hex(weight_string(cast(0x4081408140 as char) as char(3)))
40A2CDA2CD
select hex(weight_string(cast(0x40814081408140 as char) as char(3)));
hex(weight_string(cast(0x40814081408140 as char) as char(3)))
40A2CDA2CD
select hex(weight_string(cast(0x4040814081408140 as char) as char(3)));
hex(weight_string(cast(0x4040814081408140 as char) as char(3)))
4040A2CD
select hex(weight_string(cast(0x814081408140 as char), 1, 2, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 1, 2, 0xC0))
A2
select hex(weight_string(cast(0x814081408140 as char), 2, 2, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 2, 2, 0xC0))
A2CD
select hex(weight_string(cast(0x814081408140 as char), 3, 2, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 3, 2, 0xC0))
A2CDA2
select hex(weight_string(cast(0x814081408140 as char), 4, 2, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 4, 2, 0xC0))
A2CDA2CD
select hex(weight_string(cast(0x814081408140 as char), 5, 2, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 5, 2, 0xC0))
A2CDA2CD20
select hex(weight_string(cast(0x814081408140 as char),25, 2, 0xC0));
hex(weight_string(cast(0x814081408140 as char),25, 2, 0xC0))
A2CDA2CD202020202020202020202020202020202020202020
select hex(weight_string(cast(0x814081408140 as char), 1, 3, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 1, 3, 0xC0))
A2
select hex(weight_string(cast(0x814081408140 as char), 2, 3, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 2, 3, 0xC0))
A2CD
select hex(weight_string(cast(0x814081408140 as char), 3, 3, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 3, 3, 0xC0))
A2CDA2
select hex(weight_string(cast(0x814081408140 as char), 4, 3, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 4, 3, 0xC0))
A2CDA2CD
select hex(weight_string(cast(0x814081408140 as char), 5, 3, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 5, 3, 0xC0))
A2CDA2CDA2
select hex(weight_string(cast(0x814081408140 as char),25, 3, 0xC0));
hex(weight_string(cast(0x814081408140 as char),25, 3, 0xC0))
A2CDA2CDA2CD20202020202020202020202020202020202020
select hex(weight_string(cast(0x814081408140 as char), 1, 4, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 1, 4, 0xC0))
A2
select hex(weight_string(cast(0x814081408140 as char), 2, 4, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 2, 4, 0xC0))
A2CD
select hex(weight_string(cast(0x814081408140 as char), 3, 4, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 3, 4, 0xC0))
A2CDA2
select hex(weight_string(cast(0x814081408140 as char), 4, 4, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 4, 4, 0xC0))
A2CDA2CD
select hex(weight_string(cast(0x814081408140 as char), 5, 4, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 5, 4, 0xC0))
A2CDA2CDA2
select hex(weight_string(cast(0x814081408140 as char),25, 4, 0xC0));
hex(weight_string(cast(0x814081408140 as char),25, 4, 0xC0))
A2CDA2CDA2CD20202020202020202020202020202020202020
select collation(cast(0xA1A1 as char));
collation(cast(0xA1A1 as char))
gbk_chinese_ci
select hex(weight_string(cast(0x6141 as char)));
hex(weight_string(cast(0x6141 as char)))
4141
select hex(weight_string(cast(0x8EA1 as char)));
hex(weight_string(cast(0x8EA1 as char)))
8E08
select hex(weight_string(cast(0x8EA1 as char) as char(1)));
hex(weight_string(cast(0x8EA1 as char) as char(1)))
8E08
select hex(weight_string(cast(0x8EA18EA1 as char) as char(1)));
hex(weight_string(cast(0x8EA18EA1 as char) as char(1)))
8E08
select hex(weight_string(cast(0x8EA1 as char) as char(3)));
hex(weight_string(cast(0x8EA1 as char) as char(3)))
8E082020
select hex(weight_string(cast(0x8EA18EA1 as char) as char(3)));
hex(weight_string(cast(0x8EA18EA1 as char) as char(3)))
8E088E0820
select hex(weight_string(cast(0x408EA1 as char) as char(3)));
hex(weight_string(cast(0x408EA1 as char) as char(3)))
408E0820
select hex(weight_string(cast(0x408EA18EA1 as char) as char(3)));
hex(weight_string(cast(0x408EA18EA1 as char) as char(3)))
408E088E08
select hex(weight_string(cast(0x408EA18EA18EA1 as char) as char(3)));
hex(weight_string(cast(0x408EA18EA18EA1 as char) as char(3)))
408E088E08
select hex(weight_string(cast(0x40408EA18EA18EA1 as char) as char(3)));
hex(weight_string(cast(0x40408EA18EA18EA1 as char) as char(3)))
40408E08
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 1, 2, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 1, 2, 0xC0))
8E
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 2, 2, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 2, 2, 0xC0))
8E08
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 3, 2, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 3, 2, 0xC0))
8E088E
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 4, 2, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 4, 2, 0xC0))
8E088E08
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 5, 2, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 5, 2, 0xC0))
8E088E0820
select hex(weight_string(cast(0x8EA18EA18EA1 as char),25, 2, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char),25, 2, 0xC0))
8E088E08202020202020202020202020202020202020202020
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 1, 3, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 1, 3, 0xC0))
8E
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 2, 3, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 2, 3, 0xC0))
8E08
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 3, 3, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 3, 3, 0xC0))
8E088E
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 4, 3, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 4, 3, 0xC0))
8E088E08
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 5, 3, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 5, 3, 0xC0))
8E088E088E
select hex(weight_string(cast(0x8EA18EA18EA1 as char),25, 3, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char),25, 3, 0xC0))
8E088E088E0820202020202020202020202020202020202020
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 1, 4, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 1, 4, 0xC0))
8E
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 2, 4, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 2, 4, 0xC0))
8E08
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 3, 4, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 3, 4, 0xC0))
8E088E
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 4, 4, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 4, 4, 0xC0))
8E088E08
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 5, 4, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 5, 4, 0xC0))
8E088E088E
select hex(weight_string(cast(0x8EA18EA18EA1 as char),25, 4, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char),25, 4, 0xC0))
8E088E088E0820202020202020202020202020202020202020
set collation_connection=gbk_bin;
select @@collation_connection;
@@collation_connection
gbk_bin
select hex(weight_string('a'));
hex(weight_string('a'))
61
select hex(weight_string('A'));
hex(weight_string('A'))
41
select hex(weight_string('abc'));
hex(weight_string('abc'))
616263
select hex(weight_string('abc' as char(2)));
hex(weight_string('abc' as char(2)))
6162
select hex(weight_string('abc' as char(3)));
hex(weight_string('abc' as char(3)))
616263
select hex(weight_string('abc' as char(5)));
hex(weight_string('abc' as char(5)))
6162632020
select hex(weight_string('abc', 1, 2, 0xC0));
hex(weight_string('abc', 1, 2, 0xC0))
61
select hex(weight_string('abc', 2, 2, 0xC0));
hex(weight_string('abc', 2, 2, 0xC0))
6162
select hex(weight_string('abc', 3, 2, 0xC0));
hex(weight_string('abc', 3, 2, 0xC0))
616220
select hex(weight_string('abc', 4, 2, 0xC0));
hex(weight_string('abc', 4, 2, 0xC0))
61622020
select hex(weight_string('abc', 5, 2, 0xC0));
hex(weight_string('abc', 5, 2, 0xC0))
6162202020
select hex(weight_string('abc',25, 2, 0xC0));
hex(weight_string('abc',25, 2, 0xC0))
61622020202020202020202020202020202020202020202020
select hex(weight_string('abc', 1, 3, 0xC0));
hex(weight_string('abc', 1, 3, 0xC0))
61
select hex(weight_string('abc', 2, 3, 0xC0));
hex(weight_string('abc', 2, 3, 0xC0))
6162
select hex(weight_string('abc', 3, 3, 0xC0));
hex(weight_string('abc', 3, 3, 0xC0))
616263
select hex(weight_string('abc', 4, 3, 0xC0));
hex(weight_string('abc', 4, 3, 0xC0))
61626320
select hex(weight_string('abc', 5, 3, 0xC0));
hex(weight_string('abc', 5, 3, 0xC0))
6162632020
select hex(weight_string('abc',25, 3, 0xC0));
hex(weight_string('abc',25, 3, 0xC0))
61626320202020202020202020202020202020202020202020
select hex(weight_string('abc', 1, 4, 0xC0));
hex(weight_string('abc', 1, 4, 0xC0))
61
select hex(weight_string('abc', 2, 4, 0xC0));
hex(weight_string('abc', 2, 4, 0xC0))
6162
select hex(weight_string('abc', 3, 4, 0xC0));
hex(weight_string('abc', 3, 4, 0xC0))
616263
select hex(weight_string('abc', 4, 4, 0xC0));
hex(weight_string('abc', 4, 4, 0xC0))
61626320
select hex(weight_string('abc', 5, 4, 0xC0));
hex(weight_string('abc', 5, 4, 0xC0))
6162632020
select hex(weight_string('abc',25, 4, 0xC0));
hex(weight_string('abc',25, 4, 0xC0))
61626320202020202020202020202020202020202020202020
select collation(cast(0xA1A1 as char));
collation(cast(0xA1A1 as char))
gbk_bin
select hex(weight_string(cast(0x6141 as char)));
hex(weight_string(cast(0x6141 as char)))
6141
select hex(weight_string(cast(0xA1A1 as char)));
hex(weight_string(cast(0xA1A1 as char)))
A1A1
select hex(weight_string(cast(0xA1A1 as char) as char(1)));
hex(weight_string(cast(0xA1A1 as char) as char(1)))
A1A1
select hex(weight_string(cast(0xA1A1A1A1 as char) as char(1)));
hex(weight_string(cast(0xA1A1A1A1 as char) as char(1)))
A1A1
select hex(weight_string(cast(0xA1A1 as char) as char(3)));
hex(weight_string(cast(0xA1A1 as char) as char(3)))
A1A12020
select hex(weight_string(cast(0xA1A1A1A1 as char) as char(3)));
hex(weight_string(cast(0xA1A1A1A1 as char) as char(3)))
A1A1A1A120
select hex(weight_string(cast(0x40A1A1 as char) as char(3)));
hex(weight_string(cast(0x40A1A1 as char) as char(3)))
40A1A120
select hex(weight_string(cast(0x40A1A1A1A1 as char) as char(3)));
hex(weight_string(cast(0x40A1A1A1A1 as char) as char(3)))
40A1A1A1A1
select hex(weight_string(cast(0x40A1A1A1A1A1A1 as char) as char(3)));
hex(weight_string(cast(0x40A1A1A1A1A1A1 as char) as char(3)))
40A1A1A1A1
select hex(weight_string(cast(0x4040A1A1A1A1A1A1 as char) as char(3)));
hex(weight_string(cast(0x4040A1A1A1A1A1A1 as char) as char(3)))
4040A1A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 1, 2, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 1, 2, 0xC0))
A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 2, 2, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 2, 2, 0xC0))
A1A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 3, 2, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 3, 2, 0xC0))
A1A1A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 4, 2, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 4, 2, 0xC0))
A1A1A1A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 5, 2, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 5, 2, 0xC0))
A1A1A1A120
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char),25, 2, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char),25, 2, 0xC0))
A1A1A1A1202020202020202020202020202020202020202020
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 1, 3, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 1, 3, 0xC0))
A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 2, 3, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 2, 3, 0xC0))
A1A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 3, 3, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 3, 3, 0xC0))
A1A1A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 4, 3, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 4, 3, 0xC0))
A1A1A1A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 5, 3, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 5, 3, 0xC0))
A1A1A1A1A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char),25, 3, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char),25, 3, 0xC0))
A1A1A1A1A1A120202020202020202020202020202020202020
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 1, 4, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 1, 4, 0xC0))
A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 2, 4, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 2, 4, 0xC0))
A1A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 3, 4, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 3, 4, 0xC0))
A1A1A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 4, 4, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 4, 4, 0xC0))
A1A1A1A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 5, 4, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char), 5, 4, 0xC0))
A1A1A1A1A1
select hex(weight_string(cast(0xA1A1A1A1A1A1 as char),25, 4, 0xC0));
hex(weight_string(cast(0xA1A1A1A1A1A1 as char),25, 4, 0xC0))
A1A1A1A1A1A120202020202020202020202020202020202020
select collation(cast(0x8140 as char));
collation(cast(0x8140 as char))
gbk_bin
select hex(weight_string(cast(0x6141 as char)));
hex(weight_string(cast(0x6141 as char)))
6141
select hex(weight_string(cast(0x8140 as char)));
hex(weight_string(cast(0x8140 as char)))
8140
select hex(weight_string(cast(0x8140 as char) as char(1)));
hex(weight_string(cast(0x8140 as char) as char(1)))
8140
select hex(weight_string(cast(0x81408140 as char) as char(1)));
hex(weight_string(cast(0x81408140 as char) as char(1)))
8140
select hex(weight_string(cast(0x8140 as char) as char(3)));
hex(weight_string(cast(0x8140 as char) as char(3)))
81402020
select hex(weight_string(cast(0x81408140 as char) as char(3)));
hex(weight_string(cast(0x81408140 as char) as char(3)))
8140814020
select hex(weight_string(cast(0x408140 as char) as char(3)));
hex(weight_string(cast(0x408140 as char) as char(3)))
40814020
select hex(weight_string(cast(0x4081408140 as char) as char(3)));
hex(weight_string(cast(0x4081408140 as char) as char(3)))
4081408140
select hex(weight_string(cast(0x40814081408140 as char) as char(3)));
hex(weight_string(cast(0x40814081408140 as char) as char(3)))
4081408140
select hex(weight_string(cast(0x4040814081408140 as char) as char(3)));
hex(weight_string(cast(0x4040814081408140 as char) as char(3)))
40408140
select hex(weight_string(cast(0x814081408140 as char), 1, 2, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 1, 2, 0xC0))
81
select hex(weight_string(cast(0x814081408140 as char), 2, 2, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 2, 2, 0xC0))
8140
select hex(weight_string(cast(0x814081408140 as char), 3, 2, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 3, 2, 0xC0))
814081
select hex(weight_string(cast(0x814081408140 as char), 4, 2, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 4, 2, 0xC0))
81408140
select hex(weight_string(cast(0x814081408140 as char), 5, 2, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 5, 2, 0xC0))
8140814020
select hex(weight_string(cast(0x814081408140 as char),25, 2, 0xC0));
hex(weight_string(cast(0x814081408140 as char),25, 2, 0xC0))
81408140202020202020202020202020202020202020202020
select hex(weight_string(cast(0x814081408140 as char), 1, 3, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 1, 3, 0xC0))
81
select hex(weight_string(cast(0x814081408140 as char), 2, 3, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 2, 3, 0xC0))
8140
select hex(weight_string(cast(0x814081408140 as char), 3, 3, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 3, 3, 0xC0))
814081
select hex(weight_string(cast(0x814081408140 as char), 4, 3, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 4, 3, 0xC0))
81408140
select hex(weight_string(cast(0x814081408140 as char), 5, 3, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 5, 3, 0xC0))
8140814081
select hex(weight_string(cast(0x814081408140 as char),25, 3, 0xC0));
hex(weight_string(cast(0x814081408140 as char),25, 3, 0xC0))
81408140814020202020202020202020202020202020202020
select hex(weight_string(cast(0x814081408140 as char), 1, 4, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 1, 4, 0xC0))
81
select hex(weight_string(cast(0x814081408140 as char), 2, 4, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 2, 4, 0xC0))
8140
select hex(weight_string(cast(0x814081408140 as char), 3, 4, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 3, 4, 0xC0))
814081
select hex(weight_string(cast(0x814081408140 as char), 4, 4, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 4, 4, 0xC0))
81408140
select hex(weight_string(cast(0x814081408140 as char), 5, 4, 0xC0));
hex(weight_string(cast(0x814081408140 as char), 5, 4, 0xC0))
8140814081
select hex(weight_string(cast(0x814081408140 as char),25, 4, 0xC0));
hex(weight_string(cast(0x814081408140 as char),25, 4, 0xC0))
81408140814020202020202020202020202020202020202020
select collation(cast(0xA1A1 as char));
collation(cast(0xA1A1 as char))
gbk_bin
select hex(weight_string(cast(0x6141 as char)));
hex(weight_string(cast(0x6141 as char)))
6141
select hex(weight_string(cast(0x8EA1 as char)));
hex(weight_string(cast(0x8EA1 as char)))
8EA1
select hex(weight_string(cast(0x8EA1 as char) as char(1)));
hex(weight_string(cast(0x8EA1 as char) as char(1)))
8EA1
select hex(weight_string(cast(0x8EA18EA1 as char) as char(1)));
hex(weight_string(cast(0x8EA18EA1 as char) as char(1)))
8EA1
select hex(weight_string(cast(0x8EA1 as char) as char(3)));
hex(weight_string(cast(0x8EA1 as char) as char(3)))
8EA12020
select hex(weight_string(cast(0x8EA18EA1 as char) as char(3)));
hex(weight_string(cast(0x8EA18EA1 as char) as char(3)))
8EA18EA120
select hex(weight_string(cast(0x408EA1 as char) as char(3)));
hex(weight_string(cast(0x408EA1 as char) as char(3)))
408EA120
select hex(weight_string(cast(0x408EA18EA1 as char) as char(3)));
hex(weight_string(cast(0x408EA18EA1 as char) as char(3)))
408EA18EA1
select hex(weight_string(cast(0x408EA18EA18EA1 as char) as char(3)));
hex(weight_string(cast(0x408EA18EA18EA1 as char) as char(3)))
408EA18EA1
select hex(weight_string(cast(0x40408EA18EA18EA1 as char) as char(3)));
hex(weight_string(cast(0x40408EA18EA18EA1 as char) as char(3)))
40408EA1
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 1, 2, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 1, 2, 0xC0))
8E
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 2, 2, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 2, 2, 0xC0))
8EA1
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 3, 2, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 3, 2, 0xC0))
8EA18E
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 4, 2, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 4, 2, 0xC0))
8EA18EA1
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 5, 2, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 5, 2, 0xC0))
8EA18EA120
select hex(weight_string(cast(0x8EA18EA18EA1 as char),25, 2, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char),25, 2, 0xC0))
8EA18EA1202020202020202020202020202020202020202020
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 1, 3, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 1, 3, 0xC0))
8E
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 2, 3, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 2, 3, 0xC0))
8EA1
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 3, 3, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 3, 3, 0xC0))
8EA18E
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 4, 3, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 4, 3, 0xC0))
8EA18EA1
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 5, 3, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 5, 3, 0xC0))
8EA18EA18E
select hex(weight_string(cast(0x8EA18EA18EA1 as char),25, 3, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char),25, 3, 0xC0))
8EA18EA18EA120202020202020202020202020202020202020
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 1, 4, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 1, 4, 0xC0))
8E
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 2, 4, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 2, 4, 0xC0))
8EA1
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 3, 4, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 3, 4, 0xC0))
8EA18E
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 4, 4, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 4, 4, 0xC0))
8EA18EA1
select hex(weight_string(cast(0x8EA18EA18EA1 as char), 5, 4, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char), 5, 4, 0xC0))
8EA18EA18E
select hex(weight_string(cast(0x8EA18EA18EA1 as char),25, 4, 0xC0));
hex(weight_string(cast(0x8EA18EA18EA1 as char),25, 4, 0xC0))
8EA18EA18EA120202020202020202020202020202020202020
#
# Bugs#12635232: VALGRIND WARNINGS: IS_IPV6, IS_IPV4, INET6_ATON,
# INET6_NTOA + MULTIBYTE CHARSET.
#
SET NAMES gbk;
SELECT is_ipv4(inet_ntoa('1'));
is_ipv4(inet_ntoa('1'))
1
SELECT is_ipv6(inet_ntoa('1'));
is_ipv6(inet_ntoa('1'))
0
SELECT HEX(inet6_aton(inet_ntoa('1')));
HEX(inet6_aton(inet_ntoa('1')))
00000001
SELECT inet6_ntoa(inet_ntoa('1'));
inet6_ntoa(inet_ntoa('1'))
NULL
#
# Bug#14040277 UNINITIALIZED VALUE REFERENCED IN STR_TO_IPV6
#
SELECT inet6_aton(soundex('a'));
inet6_aton(soundex('a'))
NULL
#
# Bug#19047425 UNINITIALISED VALUE IN STR_TO_IPV6
#
do is_ipv4_mapped(inet6_aton(convert(_ascii "a:" using gbk)));
#
# End of 5.6 tests
#
