DROP TABLE IF EXISTS t1,t2,t3,t4;
SET sql_mode='NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1(c1 TIME NOT NULL, c2 TIME NULL, c3 DATE, PRIMARY KEY(c1), UNIQUE INDEX(c2));
CREATE TABLE t2(c1 TIME NOT NULL, c2 TIME NOT NULL, c3 DATE, PRIMARY KEY(c1,c2));
CREATE TABLE t3(c1 TIME NOT NULL, c2 TIME NULL, c3 DATE, UNIQUE INDEX idx(c1,c2));
CREATE TABLE t4(c1 TIME NOT NULL, c2 TIME NULL, c3 DATE);
INSERT INTO t1 VALUES('12:34:56.78','12:34:56.78','2009-01-01'),('10:22:33','10:22:33','2009-01-02'),('1:23','1:23','2009-01-03'),('20 11:22:33','20 11:22:33','2009-01-04'),('34 9:23','34 9:23','2009-01-05'),('0 10','0 10','2009-01-06'),('45','45','2009-01-07'),('-838:59:59','-838:59:59','2009-01-08'),('0','0','2009-01-09'),('838:59:59','838:59:59','2009-01-10');
INSERT INTO t2 VALUES('12:34:56.78','12:34:56.78','2009-01-01'),('10:22:33','10:22:33','2009-01-02'),('1:23','1:23','2009-01-03'),('20 11:22:33','20 11:22:33','2009-01-04'),('34 9:23','34 9:23','2009-01-05'),('0 10','0 10','2009-01-06'),('45','45','2009-01-07'),('-838:59:59','-838:59:59','2009-01-08'),('0','0','2009-01-09'),('838:59:59','838:59:59','2009-01-10');
INSERT INTO t3 VALUES('12:34:56.78','12:34:56.78','2009-01-01'),('10:22:33','10:22:33','2009-01-02'),('1:23','1:23','2009-01-03'),('20 11:22:33','20 11:22:33','2009-01-04'),('34 9:23','34 9:23','2009-01-05'),('0 10','0 10','2009-01-06'),('45','45','2009-01-07'),('-838:59:59','-838:59:59','2009-01-08'),('0','0','2009-01-09'),('838:59:59','838:59:59','2009-01-10');
INSERT INTO t4 VALUES('12:34:56.78','12:34:56.78','2009-01-01'),('10:22:33','10:22:33','2009-01-02'),('1:23','1:23','2009-01-03'),('20 11:22:33','20 11:22:33','2009-01-04'),('34 9:23','34 9:23','2009-01-05'),('0 10','0 10','2009-01-06'),('45','45','2009-01-07'),('-838:59:59','-838:59:59','2009-01-08'),('0','0','2009-01-09'),('838:59:59','838:59:59','2009-01-10');
INSERT INTO t1 VALUES('101112','101112','2009-01-11');
INSERT INTO t2 VALUES('101112','101112','2009-01-11');
INSERT INTO t3 VALUES('101112','101112','2009-01-11');
INSERT INTO t4 VALUES('101112','101112','2009-01-11');
INSERT INTO t1 VALUES(111112,111112,'2009-01-12'),(12,12,'2009-01-13'),(1234,1234,'2009-01-14'),(123458,123458,'2009-01-15'),(123556.99,123556.99,'2009-01-16');
INSERT INTO t2 VALUES(111112,111112,'2009-01-12'),(12,12,'2009-01-13'),(1234,1234,'2009-01-14'),(123458,123458,'2009-01-15'),(123556.99,123556.99,'2009-01-16');
INSERT INTO t3 VALUES(111112,111112,'2009-01-12'),(12,12,'2009-01-13'),(1234,1234,'2009-01-14'),(123458,123458,'2009-01-15'),(123556.99,123556.99,'2009-01-16');
INSERT INTO t4 VALUES(111112,111112,'2009-01-12'),(12,12,'2009-01-13'),(1234,1234,'2009-01-14'),(123458,123458,'2009-01-15'),(123556.99,123556.99,'2009-01-16');
SET TIMESTAMP=1233216687;
INSERT INTO t1 VALUES(CURRENT_TIME(),CURRENT_TIME(),'2009-01-17');
INSERT INTO t2 VALUES(CURRENT_TIME(),CURRENT_TIME(),'2009-01-17');
INSERT INTO t3 VALUES(CURRENT_TIME(),CURRENT_TIME(),'2009-01-17');
INSERT INTO t4 VALUES(CURRENT_TIME(),CURRENT_TIME(),'2009-01-17');
INSERT INTO t1 VALUES('8:3:2','8:3:2','2009-01-18'),('1112','1112','2009-01-19'),(11,11,'2009-01-20'),('00:12:30','00:12:30','2009-01-23'),('9:00:45','9:00:45','2009-01-24'),('9:36:00','9:36:00','2009-01-25');
INSERT INTO t2 VALUES('8:3:2','8:3:2','2009-01-18'),('1112','1112','2009-01-19'),(11,11,'2009-01-20'),('00:12:30','00:12:30','2009-01-23'),('9:00:45','9:00:45','2009-01-24'),('9:36:00','9:36:00','2009-01-25');
INSERT INTO t3 VALUES('8:3:2','8:3:2','2009-01-18'),('1112','1112','2009-01-19'),(11,11,'2009-01-20'),('00:12:30','00:12:30','2009-01-23'),('9:00:45','9:00:45','2009-01-24'),('9:36:00','9:36:00','2009-01-25');
INSERT INTO t4 VALUES('8:3:2','8:3:2','2009-01-18'),('1112','1112','2009-01-19'),(11,11,'2009-01-20'),('00:12:30','00:12:30','2009-01-23'),('9:00:45','9:00:45','2009-01-24'),('9:36:00','9:36:00','2009-01-25');
INSERT INTO t1 VALUES('-850:00:00','-850:00:00','2009-01-21');
ERROR 23000: Duplicate entry '-838:59:59' for key 't1.PRIMARY'
INSERT INTO t1 VALUES('850:00:00','850:00:00','2009-01-21');
ERROR 23000: Duplicate entry '838:59:59' for key 't1.PRIMARY'
DELETE FROM t1 WHERE c1='-838:59:59' AND c2='-838:59:59';
DELETE FROM t1 WHERE c1='838:59:59' AND c2='838:59:59';
INSERT INTO t1 VALUES('-850:00:00','-850:00:00','2009-01-21'),('850:00:00','850:00:00','2009-01-21');
Warnings:
Warning	1264	Out of range value for column 'c1' at row 1
Warning	1264	Out of range value for column 'c2' at row 1
Warning	1264	Out of range value for column 'c1' at row 2
Warning	1264	Out of range value for column 'c2' at row 2
INSERT INTO t4 VALUES('10.22.22','10.22.22','2009-01-26'),(1234567,1234567,'2009-01-27'),(123456789,123456789,'2009-01-28'),(123456789.10,123456789.10,'2009-01-29'),('10 22:22','10 22:22','2009-01-30'),('12.45a','12.45a','2009-01-31');
Warnings:
Warning	1265	Data truncated for column 'c1' at row 1
Warning	1265	Data truncated for column 'c2' at row 1
Warning	1264	Out of range value for column 'c1' at row 2
Warning	1264	Out of range value for column 'c2' at row 2
Warning	1264	Out of range value for column 'c1' at row 3
Warning	1264	Out of range value for column 'c2' at row 3
Warning	1264	Out of range value for column 'c1' at row 4
Warning	1264	Out of range value for column 'c2' at row 4
Warning	1265	Data truncated for column 'c1' at row 6
Warning	1265	Data truncated for column 'c2' at row 6
INSERT INTO t1 VALUES('8:29:45',NULL,'2009-02-01');
INSERT INTO t3 VALUES('8:29:45',NULL,'2009-02-01');
INSERT INTO t4 VALUES('8:29:45',NULL,'2009-02-01');
INSERT INTO t1(c1,c2) VALUES('9:30',NULL);
DELETE FROM t1 WHERE c1='9:30' AND c2 IS NULL;
SELECT * FROM t1;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT * FROM t2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t4;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:00	00:00:00	2009-01-27
00:00:10	00:00:10	2009-01-26
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:12	00:00:12	2009-01-31
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
262:22:00	262:22:00	2009-01-30
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
838:59:59	838:59:59	2009-01-28
838:59:59	838:59:59	2009-01-29
SELECT * FROM t1;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
total_rows	min_value	max(c1)
24	-838:59:59	838:59:59
SELECT * FROM t1 WHERE c3 = '2009-01-17';
c1	c2	c3
11:11:27	11:11:27	2009-01-17
SELECT c1 FROM t1;
c1
-838:59:59
00:00:00
00:00:11
00:00:12
00:00:45
00:11:12
00:12:30
00:12:34
01:23:00
08:03:02
08:29:45
09:00:45
09:36:00
10:00:00
10:11:12
10:22:33
11:11:12
11:11:27
12:34:57
12:34:58
12:35:57
491:22:33
825:23:00
838:59:59
SELECT c1 FROM t1 ORDER BY c1 DESC;
c1
838:59:59
825:23:00
491:22:33
12:35:57
12:34:58
12:34:57
11:11:27
11:11:12
10:22:33
10:11:12
10:00:00
09:36:00
09:00:45
08:29:45
08:03:02
01:23:00
00:12:34
00:12:30
00:11:12
00:00:45
00:00:12
00:00:11
00:00:00
-838:59:59
SELECT * FROM t1 ORDER BY c1 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c1 = '838:59:59' ORDER BY c1;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 = '838:59:59' ORDER BY c1 LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 = '838:59:59' ORDER BY c1 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 = '838:59:59' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 <> '838:59:59' ORDER BY c1;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c1 <> '838:59:59' ORDER BY c1 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 > '838:59:59' ORDER BY c1;
c1	c2	c3
SELECT * FROM t1 WHERE c1 > '838:59:59' ORDER BY c1 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c1 >= '838:59:59' ORDER BY c1;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 >= '838:59:59' ORDER BY c1 LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 < '838:59:59' ORDER BY c1;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c1 < '838:59:59' ORDER BY c1 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 <= '838:59:59' ORDER BY c1;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 <= '838:59:59' ORDER BY c1 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 <=> '838:59:59' ORDER BY c1;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 <=> '838:59:59' ORDER BY c1 LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 BETWEEN '00:00:00' AND '838:59:59' ORDER BY c1;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 BETWEEN '00:00:00' AND '838:59:59' ORDER BY c1 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t1 WHERE c1 IN ('00:00:00','838:59:59') ORDER BY c1;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 IN ('00:00:00','838:59:59') ORDER BY c1 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 >= '00:00:00' AND c1 < '838:59:59' AND c2 = '11:11:12' ORDER BY c1;
c1	c2	c3
11:11:12	11:11:12	2009-01-12
SELECT * FROM t1 WHERE c1 >= '00:00:00' AND c1 < '838:59:59' AND c2 = '11:11:12' ORDER BY c1 LIMIT 2;
c1	c2	c3
11:11:12	11:11:12	2009-01-12
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
c1	c2	c3
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 <> '838:59:59' ORDER BY c1 DESC;
c1	c2	c3
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:29:45	NULL	2009-02-01
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 <> '838:59:59' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
SELECT * FROM t1 WHERE c1 > '838:59:59' ORDER BY c1 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c1 > '838:59:59' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c1 >= '838:59:59' ORDER BY c1 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 >= '838:59:59' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 < '838:59:59' ORDER BY c1 DESC;
c1	c2	c3
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:29:45	NULL	2009-02-01
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 < '838:59:59' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
SELECT * FROM t1 WHERE c1 <= '838:59:59' ORDER BY c1 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:29:45	NULL	2009-02-01
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 <= '838:59:59' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c1 <=> '838:59:59' ORDER BY c1 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 <=> '838:59:59' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 BETWEEN '00:00:00' AND '838:59:59' ORDER BY c1 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:29:45	NULL	2009-02-01
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 BETWEEN '00:00:00' AND '838:59:59' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c1 IN ('00:00:00','838:59:59') ORDER BY c1 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 IN ('00:00:00','838:59:59') ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 >= '00:00:00' AND c1 < '838:59:59' AND c2 = '11:11:12' ORDER BY c1 DESC;
c1	c2	c3
11:11:12	11:11:12	2009-01-12
SELECT * FROM t1 WHERE c1 >= '00:00:00' AND c1 < '838:59:59' AND c2 = '11:11:12' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
11:11:12	11:11:12	2009-01-12
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:29:45	NULL	2009-02-01
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
total_rows	min_value	max(c1)
24	-838:59:59	838:59:59
SELECT * FROM t1 WHERE c3 = '2009-01-16';
c1	c2	c3
12:35:57	12:35:57	2009-01-16
SELECT c1 FROM t1;
c1
-838:59:59
00:00:00
00:00:11
00:00:12
00:00:45
00:11:12
00:12:30
00:12:34
01:23:00
08:03:02
08:29:45
09:00:45
09:36:00
10:00:00
10:11:12
10:22:33
11:11:12
11:11:27
12:34:57
12:34:58
12:35:57
491:22:33
825:23:00
838:59:59
SELECT c1 FROM t1 ORDER BY c1 DESC;
c1
838:59:59
825:23:00
491:22:33
12:35:57
12:34:58
12:34:57
11:11:27
11:11:12
10:22:33
10:11:12
10:00:00
09:36:00
09:00:45
08:29:45
08:03:02
01:23:00
00:12:34
00:12:30
00:11:12
00:00:45
00:00:12
00:00:11
00:00:00
-838:59:59
SELECT * FROM t1 ORDER BY c1 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c1 = '00:00:00' ORDER BY c1;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 = '00:00:00' ORDER BY c1 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 = '00:00:00' ORDER BY c1 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 = '00:00:00' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 <> '00:00:00' ORDER BY c1;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 <> '00:00:00' ORDER BY c1 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:11	00:00:11	2009-01-20
SELECT * FROM t1 WHERE c1 > '00:00:00' ORDER BY c1;
c1	c2	c3
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 > '00:00:00' ORDER BY c1 LIMIT 2;
c1	c2	c3
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
SELECT * FROM t1 WHERE c1 >= '00:00:00' ORDER BY c1;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 >= '00:00:00' ORDER BY c1 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t1 WHERE c1 < '00:00:00' ORDER BY c1;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 < '00:00:00' ORDER BY c1 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 <= '00:00:00' ORDER BY c1;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 <= '00:00:00' ORDER BY c1 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 <=> '00:00:00' ORDER BY c1;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 <=> '00:00:00' ORDER BY c1 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 BETWEEN '00:00:00' AND '09:36:00' ORDER BY c1;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
SELECT * FROM t1 WHERE c1 BETWEEN '00:00:00' AND '09:36:00' ORDER BY c1 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t1 WHERE c1 IN ('00:00:00','09:36:00') ORDER BY c1;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
09:36:00	09:36:00	2009-01-25
SELECT * FROM t1 WHERE c1 IN ('00:00:00','09:36:00') ORDER BY c1 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
09:36:00	09:36:00	2009-01-25
SELECT * FROM t1 WHERE c1 >= '00:00:00' AND c1 < '09:36:00' AND c2 = '01:23:00' ORDER BY c1;
c1	c2	c3
01:23:00	01:23:00	2009-01-03
SELECT * FROM t1 WHERE c1 >= '00:00:00' AND c1 < '09:36:00' AND c2 = '01:23:00' ORDER BY c1 LIMIT 2;
c1	c2	c3
01:23:00	01:23:00	2009-01-03
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
c1	c2	c3
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 <> '00:00:00' ORDER BY c1 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:29:45	NULL	2009-02-01
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 <> '00:00:00' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c1 > '00:00:00' ORDER BY c1 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:29:45	NULL	2009-02-01
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
SELECT * FROM t1 WHERE c1 > '00:00:00' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c1 >= '00:00:00' ORDER BY c1 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:29:45	NULL	2009-02-01
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 >= '00:00:00' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c1 < '00:00:00' ORDER BY c1 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 < '00:00:00' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 <= '00:00:00' ORDER BY c1 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 <= '00:00:00' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 <=> '00:00:00' ORDER BY c1 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 <=> '00:00:00' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 BETWEEN '00:00:00' AND '09:36:00' ORDER BY c1 DESC;
c1	c2	c3
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:29:45	NULL	2009-02-01
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 BETWEEN '00:00:00' AND '09:36:00' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
SELECT * FROM t1 WHERE c1 IN ('00:00:00','09:36:00') ORDER BY c1 DESC;
c1	c2	c3
09:36:00	09:36:00	2009-01-25
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 IN ('00:00:00','09:36:00') ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
09:36:00	09:36:00	2009-01-25
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c1 >= '00:00:00' AND c1 < '09:36:00' AND c2 = '01:23:00' ORDER BY c1 DESC;
c1	c2	c3
01:23:00	01:23:00	2009-01-03
SELECT * FROM t1 WHERE c1 >= '00:00:00' AND c1 < '09:36:00' AND c2 = '01:23:00' ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
01:23:00	01:23:00	2009-01-03
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:29:45	NULL	2009-02-01
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t1;
total_rows	min_value	max(c2)
24	-838:59:59	838:59:59
SELECT * FROM t1 WHERE c3 = '2009-01-15';
c1	c2	c3
12:34:58	12:34:58	2009-01-15
SELECT c2 FROM t1;
c2
-838:59:59
00:00:00
00:00:11
00:00:12
00:00:45
00:11:12
00:12:30
00:12:34
01:23:00
08:03:02
09:00:45
09:36:00
10:00:00
10:11:12
10:22:33
11:11:12
11:11:27
12:34:57
12:34:58
12:35:57
491:22:33
825:23:00
838:59:59
NULL
SELECT c2 FROM t1 ORDER BY c2 DESC;
c2
838:59:59
825:23:00
491:22:33
12:35:57
12:34:58
12:34:57
11:11:27
11:11:12
10:22:33
10:11:12
10:00:00
09:36:00
09:00:45
08:03:02
01:23:00
00:12:34
00:12:30
00:11:12
00:00:45
00:00:12
00:00:11
00:00:00
-838:59:59
NULL
SELECT * FROM t1 ORDER BY c2 LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c2 = '-838:59:59' ORDER BY c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 = '-838:59:59' ORDER BY c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 = '-838:59:59' ORDER BY c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 = '-838:59:59' ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 <> '-838:59:59' ORDER BY c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 <> '-838:59:59' ORDER BY c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t1 WHERE c2 > '-838:59:59' ORDER BY c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 > '-838:59:59' ORDER BY c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t1 WHERE c2 >= '-838:59:59' ORDER BY c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 >= '-838:59:59' ORDER BY c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c2 < '-838:59:59' ORDER BY c2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 < '-838:59:59' ORDER BY c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 <= '-838:59:59' ORDER BY c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 <= '-838:59:59' ORDER BY c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 <=> '-838:59:59' ORDER BY c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 <=> '-838:59:59' ORDER BY c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 BETWEEN '-838:59:59' AND '10:00:00' ORDER BY c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
SELECT * FROM t1 WHERE c2 BETWEEN '-838:59:59' AND '10:00:00' ORDER BY c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c2 IN ('-838:59:59','10:00:00') ORDER BY c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
10:00:00	10:00:00	2009-01-06
SELECT * FROM t1 WHERE c2 IN ('-838:59:59','10:00:00') ORDER BY c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
10:00:00	10:00:00	2009-01-06
SELECT * FROM t1 WHERE c2 >= '-838:59:59' AND c2 < '10:00:00' AND c1 = '00:11:12' ORDER BY c2;
c1	c2	c3
00:11:12	00:11:12	2009-01-19
SELECT * FROM t1 WHERE c2 >= '-838:59:59' AND c2 < '10:00:00' AND c1 = '00:11:12' ORDER BY c2 LIMIT 2;
c1	c2	c3
00:11:12	00:11:12	2009-01-19
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c2 <> '-838:59:59' ORDER BY c2 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c2 <> '-838:59:59' ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c2 > '-838:59:59' ORDER BY c2 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c2 > '-838:59:59' ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c2 >= '-838:59:59' ORDER BY c2 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 >= '-838:59:59' ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c2 < '-838:59:59' ORDER BY c2 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 < '-838:59:59' ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 <= '-838:59:59' ORDER BY c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 <= '-838:59:59' ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 <=> '-838:59:59' ORDER BY c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 <=> '-838:59:59' ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 BETWEEN '-838:59:59' AND '10:00:00' ORDER BY c2 DESC;
c1	c2	c3
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 BETWEEN '-838:59:59' AND '10:00:00' ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
SELECT * FROM t1 WHERE c2 IN ('-838:59:59','10:00:00') ORDER BY c2 DESC;
c1	c2	c3
10:00:00	10:00:00	2009-01-06
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 IN ('-838:59:59','10:00:00') ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
10:00:00	10:00:00	2009-01-06
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 >= '-838:59:59' AND c2 < '10:00:00' AND c1 = '00:11:12' ORDER BY c2 DESC;
c1	c2	c3
00:11:12	00:11:12	2009-01-19
SELECT * FROM t1 WHERE c2 >= '-838:59:59' AND c2 < '10:00:00' AND c1 = '00:11:12' ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
00:11:12	00:11:12	2009-01-19
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t1;
total_rows	min_value	max(c2)
24	-838:59:59	838:59:59
SELECT * FROM t1 WHERE c3 = '2009-01-11';
c1	c2	c3
10:11:12	10:11:12	2009-01-11
SELECT c2 FROM t1;
c2
-838:59:59
00:00:00
00:00:11
00:00:12
00:00:45
00:11:12
00:12:30
00:12:34
01:23:00
08:03:02
09:00:45
09:36:00
10:00:00
10:11:12
10:22:33
11:11:12
11:11:27
12:34:57
12:34:58
12:35:57
491:22:33
825:23:00
838:59:59
NULL
SELECT c2 FROM t1 ORDER BY c2 DESC;
c2
838:59:59
825:23:00
491:22:33
12:35:57
12:34:58
12:34:57
11:11:27
11:11:12
10:22:33
10:11:12
10:00:00
09:36:00
09:00:45
08:03:02
01:23:00
00:12:34
00:12:30
00:11:12
00:00:45
00:00:12
00:00:11
00:00:00
-838:59:59
NULL
SELECT * FROM t1 ORDER BY c2 LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c2 = NULL ORDER BY c2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 = NULL ORDER BY c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 = NULL ORDER BY c2 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 = NULL ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 <> NULL ORDER BY c2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 <> NULL ORDER BY c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 > NULL ORDER BY c2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 > NULL ORDER BY c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 >= NULL ORDER BY c2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 >= NULL ORDER BY c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 < NULL ORDER BY c2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 < NULL ORDER BY c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 <= NULL ORDER BY c2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 <= NULL ORDER BY c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 <=> NULL ORDER BY c2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t1 WHERE c2 <=> NULL ORDER BY c2 LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t1 WHERE c2 BETWEEN NULL AND '10:22:33' ORDER BY c2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 BETWEEN NULL AND '10:22:33' ORDER BY c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 IN (NULL,'10:22:33') ORDER BY c2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 IN (NULL,'10:22:33') ORDER BY c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 >= NULL AND c2 < '10:22:33' AND c1 = '491:22:33' ORDER BY c2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 >= NULL AND c2 < '10:22:33' AND c1 = '491:22:33' ORDER BY c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
00:00:00	00:00:00	2009-01-09
SELECT * FROM t1 WHERE c2 <> NULL ORDER BY c2 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 <> NULL ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 > NULL ORDER BY c2 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 > NULL ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 >= NULL ORDER BY c2 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 >= NULL ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 < NULL ORDER BY c2 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 < NULL ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 <= NULL ORDER BY c2 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 <= NULL ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 <=> NULL ORDER BY c2 DESC;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t1 WHERE c2 <=> NULL ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t1 WHERE c2 BETWEEN NULL AND '10:22:33' ORDER BY c2 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 BETWEEN NULL AND '10:22:33' ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 IN (NULL,'10:22:33') ORDER BY c2 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 IN (NULL,'10:22:33') ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 >= NULL AND c2 < '10:22:33' AND c1 = '491:22:33' ORDER BY c2 DESC;
c1	c2	c3
SELECT * FROM t1 WHERE c2 >= NULL AND c2 < '10:22:33' AND c1 = '491:22:33' ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
491:22:33	491:22:33	2009-01-04
12:35:57	12:35:57	2009-01-16
12:34:58	12:34:58	2009-01-15
12:34:57	12:34:57	2009-01-01
11:11:27	11:11:27	2009-01-17
11:11:12	11:11:12	2009-01-12
10:22:33	10:22:33	2009-01-02
10:11:12	10:11:12	2009-01-11
10:00:00	10:00:00	2009-01-06
09:36:00	09:36:00	2009-01-25
09:00:45	09:00:45	2009-01-24
08:03:02	08:03:02	2009-01-18
01:23:00	01:23:00	2009-01-03
00:12:34	00:12:34	2009-01-14
00:12:30	00:12:30	2009-01-23
00:11:12	00:11:12	2009-01-19
00:00:45	00:00:45	2009-01-07
00:00:12	00:00:12	2009-01-13
00:00:11	00:00:11	2009-01-20
00:00:00	00:00:00	2009-01-09
-838:59:59	-838:59:59	2009-01-21
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-21
825:23:00	825:23:00	2009-01-05
SELECT * FROM t1 WHERE c1='838:59:59' OR c2='-838:59:59';
c1	c2	c3
-838:59:59	-838:59:59	2009-01-21
838:59:59	838:59:59	2009-01-21
SELECT * FROM t2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
total_rows	min_value	max(c1)
23	-838:59:59	838:59:59
SELECT * FROM t2 WHERE c3 = '2009-01-17';
c1	c2	c3
11:11:27	11:11:27	2009-01-17
SELECT c1,c2 FROM t2;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 = '838:59:59' ORDER BY c1,c2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 = '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 = '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 = '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 <> '838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
SELECT * FROM t2 WHERE c1 <> '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 > '838:59:59' ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c1 > '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c1 >= '838:59:59' ORDER BY c1,c2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 >= '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 < '838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
SELECT * FROM t2 WHERE c1 < '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 <= '838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 <= '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 <=> '838:59:59' ORDER BY c1,c2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 <=> '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 BETWEEN '00:00:00' AND '838:59:59' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 BETWEEN '00:00:00' AND '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t2 WHERE c1 IN ('00:00:00','838:59:59') ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 IN ('00:00:00','838:59:59') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 >= '00:00:00' AND c1 < '838:59:59' AND c2 = '11:11:12' ORDER BY c1,c2;
c1	c2	c3
11:11:12	11:11:12	2009-01-12
SELECT * FROM t2 WHERE c1 >= '00:00:00' AND c1 < '838:59:59' AND c2 = '11:11:12' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
11:11:12	11:11:12	2009-01-12
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 <> '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
SELECT * FROM t2 WHERE c1 <> '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 > '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c1 > '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c1 >= '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 >= '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 < '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
SELECT * FROM t2 WHERE c1 < '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 <= '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 <= '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 <=> '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 <=> '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 BETWEEN '00:00:00' AND '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 BETWEEN '00:00:00' AND '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t2 WHERE c1 IN ('00:00:00','838:59:59') ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 IN ('00:00:00','838:59:59') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 >= '00:00:00' AND c1 < '838:59:59' AND c2 = '11:11:12' ORDER BY c1,c2 DESC;
c1	c2	c3
11:11:12	11:11:12	2009-01-12
SELECT * FROM t2 WHERE c1 >= '00:00:00' AND c1 < '838:59:59' AND c2 = '11:11:12' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
11:11:12	11:11:12	2009-01-12
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
total_rows	min_value	max(c1)
23	-838:59:59	838:59:59
SELECT * FROM t2 WHERE c3 = '2009-01-16';
c1	c2	c3
12:35:57	12:35:57	2009-01-16
SELECT c1,c2 FROM t2;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 = '00:00:00' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 = '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 = '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 = '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 <> '00:00:00' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 <> '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:11	00:00:11	2009-01-20
SELECT * FROM t2 WHERE c1 > '00:00:00' ORDER BY c1,c2;
c1	c2	c3
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 > '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
SELECT * FROM t2 WHERE c1 >= '00:00:00' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 >= '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t2 WHERE c1 < '00:00:00' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c1 < '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c1 <= '00:00:00' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 <= '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 <=> '00:00:00' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 <=> '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 BETWEEN '00:00:00' AND '09:36:00' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
SELECT * FROM t2 WHERE c1 BETWEEN '00:00:00' AND '09:36:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t2 WHERE c1 IN ('00:00:00','09:36:00') ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
09:36:00	09:36:00	2009-01-25
SELECT * FROM t2 WHERE c1 IN ('00:00:00','09:36:00') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
09:36:00	09:36:00	2009-01-25
SELECT * FROM t2 WHERE c1 >= '00:00:00' AND c1 < '09:36:00' AND c2 = '01:23:00' ORDER BY c1,c2;
c1	c2	c3
01:23:00	01:23:00	2009-01-03
SELECT * FROM t2 WHERE c1 >= '00:00:00' AND c1 < '09:36:00' AND c2 = '01:23:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
01:23:00	01:23:00	2009-01-03
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 <> '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 <> '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:11	00:00:11	2009-01-20
SELECT * FROM t2 WHERE c1 > '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 > '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
SELECT * FROM t2 WHERE c1 >= '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 >= '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t2 WHERE c1 < '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c1 < '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c1 <= '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 <= '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 <=> '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 <=> '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c1 BETWEEN '00:00:00' AND '09:36:00' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
SELECT * FROM t2 WHERE c1 BETWEEN '00:00:00' AND '09:36:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t2 WHERE c1 IN ('00:00:00','09:36:00') ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
09:36:00	09:36:00	2009-01-25
SELECT * FROM t2 WHERE c1 IN ('00:00:00','09:36:00') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
09:36:00	09:36:00	2009-01-25
SELECT * FROM t2 WHERE c1 >= '00:00:00' AND c1 < '09:36:00' AND c2 = '01:23:00' ORDER BY c1,c2 DESC;
c1	c2	c3
01:23:00	01:23:00	2009-01-03
SELECT * FROM t2 WHERE c1 >= '00:00:00' AND c1 < '09:36:00' AND c2 = '01:23:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
01:23:00	01:23:00	2009-01-03
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
total_rows	min_value	max(c2)
23	-838:59:59	838:59:59
SELECT * FROM t2 WHERE c3 = '2009-01-15';
c1	c2	c3
12:34:58	12:34:58	2009-01-15
SELECT c1,c2 FROM t2;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c2 = '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c2 = '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c2 = '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c2 = '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c2 <> '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c2 <> '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t2 WHERE c2 > '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c2 > '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t2 WHERE c2 >= '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c2 >= '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c2 < '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 < '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 <= '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c2 <= '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c2 <=> '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c2 <=> '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c2 BETWEEN '-838:59:59' AND '10:00:00' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
SELECT * FROM t2 WHERE c2 BETWEEN '-838:59:59' AND '10:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c2 IN ('-838:59:59','10:00:00') ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
10:00:00	10:00:00	2009-01-06
SELECT * FROM t2 WHERE c2 IN ('-838:59:59','10:00:00') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
10:00:00	10:00:00	2009-01-06
SELECT * FROM t2 WHERE c2 >= '-838:59:59' AND c2 < '10:00:00' AND c1 = '00:11:12' ORDER BY c1,c2;
c1	c2	c3
00:11:12	00:11:12	2009-01-19
SELECT * FROM t2 WHERE c2 >= '-838:59:59' AND c2 < '10:00:00' AND c1 = '00:11:12' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:11:12	00:11:12	2009-01-19
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c2 <> '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c2 <> '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t2 WHERE c2 > '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c2 > '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t2 WHERE c2 >= '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c2 >= '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c2 < '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c2 < '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 <= '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c2 <= '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c2 <=> '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c2 <=> '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t2 WHERE c2 BETWEEN '-838:59:59' AND '10:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
SELECT * FROM t2 WHERE c2 BETWEEN '-838:59:59' AND '10:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c2 IN ('-838:59:59','10:00:00') ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
10:00:00	10:00:00	2009-01-06
SELECT * FROM t2 WHERE c2 IN ('-838:59:59','10:00:00') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
10:00:00	10:00:00	2009-01-06
SELECT * FROM t2 WHERE c2 >= '-838:59:59' AND c2 < '10:00:00' AND c1 = '00:11:12' ORDER BY c1,c2 DESC;
c1	c2	c3
00:11:12	00:11:12	2009-01-19
SELECT * FROM t2 WHERE c2 >= '-838:59:59' AND c2 < '10:00:00' AND c1 = '00:11:12' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:11:12	00:11:12	2009-01-19
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
total_rows	min_value	max(c2)
23	-838:59:59	838:59:59
SELECT * FROM t2 WHERE c3 = '2009-01-11';
c1	c2	c3
10:11:12	10:11:12	2009-01-11
SELECT c1,c2 FROM t2;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c2 = NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 = NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 = NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c2 = NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 <> NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 <> NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 > NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 > NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 >= NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 >= NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 < NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 < NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 <= NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 <= NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 <=> NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 <=> NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 BETWEEN NULL AND '10:22:33' ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 BETWEEN NULL AND '10:22:33' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IN (NULL,'10:22:33') ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IN (NULL,'10:22:33') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 >= NULL AND c2 < '10:22:33' AND c1 = '491:22:33' ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 >= NULL AND c2 < '10:22:33' AND c1 = '491:22:33' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t2 WHERE c2 <> NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c2 <> NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 > NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c2 > NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 >= NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c2 >= NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 < NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c2 < NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 <= NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c2 <= NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 <=> NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c2 <=> NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 BETWEEN NULL AND '10:22:33' ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c2 BETWEEN NULL AND '10:22:33' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IN (NULL,'10:22:33') ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IN (NULL,'10:22:33') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 >= NULL AND c2 < '10:22:33' AND c1 = '491:22:33' ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c2 >= NULL AND c2 < '10:22:33' AND c1 = '491:22:33' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
total_rows	min_value	max(c1)
24	-838:59:59	838:59:59
SELECT * FROM t3 WHERE c3 = '2009-01-17';
c1	c2	c3
11:11:27	11:11:27	2009-01-17
SELECT c1,c2 FROM t3;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
08:29:45	NULL
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
08:29:45	NULL
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 = '838:59:59' ORDER BY c1,c2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 = '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 = '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 = '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 <> '838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
SELECT * FROM t3 WHERE c1 <> '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 > '838:59:59' ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t3 WHERE c1 > '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c1 >= '838:59:59' ORDER BY c1,c2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 >= '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 < '838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
SELECT * FROM t3 WHERE c1 < '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 <= '838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 <= '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 <=> '838:59:59' ORDER BY c1,c2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 <=> '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 BETWEEN '00:00:00' AND '838:59:59' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 BETWEEN '00:00:00' AND '838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t3 WHERE c1 IN ('00:00:00','838:59:59') ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 IN ('00:00:00','838:59:59') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 >= '00:00:00' AND c1 < '838:59:59' AND c2 = '11:11:12' ORDER BY c1,c2;
c1	c2	c3
11:11:12	11:11:12	2009-01-12
SELECT * FROM t3 WHERE c1 >= '00:00:00' AND c1 < '838:59:59' AND c2 = '11:11:12' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
11:11:12	11:11:12	2009-01-12
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 <> '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
SELECT * FROM t3 WHERE c1 <> '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 > '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t3 WHERE c1 > '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c1 >= '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 >= '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 < '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
SELECT * FROM t3 WHERE c1 < '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 <= '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 <= '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 <=> '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 <=> '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 BETWEEN '00:00:00' AND '838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 BETWEEN '00:00:00' AND '838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t3 WHERE c1 IN ('00:00:00','838:59:59') ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 IN ('00:00:00','838:59:59') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 >= '00:00:00' AND c1 < '838:59:59' AND c2 = '11:11:12' ORDER BY c1,c2 DESC;
c1	c2	c3
11:11:12	11:11:12	2009-01-12
SELECT * FROM t3 WHERE c1 >= '00:00:00' AND c1 < '838:59:59' AND c2 = '11:11:12' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
11:11:12	11:11:12	2009-01-12
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
total_rows	min_value	max(c1)
24	-838:59:59	838:59:59
SELECT * FROM t3 WHERE c3 = '2009-01-16';
c1	c2	c3
12:35:57	12:35:57	2009-01-16
SELECT c1,c2 FROM t3;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
08:29:45	NULL
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
08:29:45	NULL
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 = '00:00:00' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 = '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 = '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 = '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 <> '00:00:00' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 <> '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:11	00:00:11	2009-01-20
SELECT * FROM t3 WHERE c1 > '00:00:00' ORDER BY c1,c2;
c1	c2	c3
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 > '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
SELECT * FROM t3 WHERE c1 >= '00:00:00' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 >= '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t3 WHERE c1 < '00:00:00' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c1 < '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c1 <= '00:00:00' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 <= '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 <=> '00:00:00' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 <=> '00:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 BETWEEN '00:00:00' AND '09:36:00' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
SELECT * FROM t3 WHERE c1 BETWEEN '00:00:00' AND '09:36:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t3 WHERE c1 IN ('00:00:00','09:36:00') ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
09:36:00	09:36:00	2009-01-25
SELECT * FROM t3 WHERE c1 IN ('00:00:00','09:36:00') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
09:36:00	09:36:00	2009-01-25
SELECT * FROM t3 WHERE c1 >= '00:00:00' AND c1 < '09:36:00' AND c2 = '01:23:00' ORDER BY c1,c2;
c1	c2	c3
01:23:00	01:23:00	2009-01-03
SELECT * FROM t3 WHERE c1 >= '00:00:00' AND c1 < '09:36:00' AND c2 = '01:23:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
01:23:00	01:23:00	2009-01-03
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 <> '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 <> '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:11	00:00:11	2009-01-20
SELECT * FROM t3 WHERE c1 > '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 > '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
SELECT * FROM t3 WHERE c1 >= '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 >= '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t3 WHERE c1 < '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c1 < '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c1 <= '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 <= '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 <=> '00:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 <=> '00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c1 BETWEEN '00:00:00' AND '09:36:00' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
SELECT * FROM t3 WHERE c1 BETWEEN '00:00:00' AND '09:36:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t3 WHERE c1 IN ('00:00:00','09:36:00') ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
09:36:00	09:36:00	2009-01-25
SELECT * FROM t3 WHERE c1 IN ('00:00:00','09:36:00') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
09:36:00	09:36:00	2009-01-25
SELECT * FROM t3 WHERE c1 >= '00:00:00' AND c1 < '09:36:00' AND c2 = '01:23:00' ORDER BY c1,c2 DESC;
c1	c2	c3
01:23:00	01:23:00	2009-01-03
SELECT * FROM t3 WHERE c1 >= '00:00:00' AND c1 < '09:36:00' AND c2 = '01:23:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
01:23:00	01:23:00	2009-01-03
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
total_rows	min_value	max(c2)
24	-838:59:59	838:59:59
SELECT * FROM t3 WHERE c3 = '2009-01-15';
c1	c2	c3
12:34:58	12:34:58	2009-01-15
SELECT c1,c2 FROM t3;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
08:29:45	NULL
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
08:29:45	NULL
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c2 = '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c2 = '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c2 = '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c2 = '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c2 <> '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c2 <> '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t3 WHERE c2 > '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c2 > '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t3 WHERE c2 >= '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c2 >= '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c2 < '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 < '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 <= '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c2 <= '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c2 <=> '-838:59:59' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c2 <=> '-838:59:59' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c2 BETWEEN '-838:59:59' AND '10:00:00' ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
SELECT * FROM t3 WHERE c2 BETWEEN '-838:59:59' AND '10:00:00' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c2 IN ('-838:59:59','10:00:00') ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
10:00:00	10:00:00	2009-01-06
SELECT * FROM t3 WHERE c2 IN ('-838:59:59','10:00:00') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
10:00:00	10:00:00	2009-01-06
SELECT * FROM t3 WHERE c2 >= '-838:59:59' AND c2 < '10:00:00' AND c1 = '00:11:12' ORDER BY c1,c2;
c1	c2	c3
00:11:12	00:11:12	2009-01-19
SELECT * FROM t3 WHERE c2 >= '-838:59:59' AND c2 < '10:00:00' AND c1 = '00:11:12' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
00:11:12	00:11:12	2009-01-19
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c2 <> '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c2 <> '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t3 WHERE c2 > '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c2 > '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
SELECT * FROM t3 WHERE c2 >= '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c2 >= '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c2 < '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t3 WHERE c2 < '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 <= '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c2 <= '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c2 <=> '-838:59:59' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c2 <=> '-838:59:59' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
SELECT * FROM t3 WHERE c2 BETWEEN '-838:59:59' AND '10:00:00' ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
SELECT * FROM t3 WHERE c2 BETWEEN '-838:59:59' AND '10:00:00' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c2 IN ('-838:59:59','10:00:00') ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
10:00:00	10:00:00	2009-01-06
SELECT * FROM t3 WHERE c2 IN ('-838:59:59','10:00:00') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
10:00:00	10:00:00	2009-01-06
SELECT * FROM t3 WHERE c2 >= '-838:59:59' AND c2 < '10:00:00' AND c1 = '00:11:12' ORDER BY c1,c2 DESC;
c1	c2	c3
00:11:12	00:11:12	2009-01-19
SELECT * FROM t3 WHERE c2 >= '-838:59:59' AND c2 < '10:00:00' AND c1 = '00:11:12' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
00:11:12	00:11:12	2009-01-19
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
08:29:45	NULL	2009-02-01
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
total_rows	min_value	max(c2)
24	-838:59:59	838:59:59
SELECT * FROM t3 WHERE c3 = '2009-01-11';
c1	c2	c3
10:11:12	10:11:12	2009-01-11
SELECT c1,c2 FROM t3;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
08:29:45	NULL
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;
c1	c2
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
08:29:45	NULL
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c1,c2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND '10:22:33' ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND '10:22:33' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 IN (NULL,'10:22:33') ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 IN (NULL,'10:22:33') ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < '10:22:33' AND c1 = '491:22:33' ORDER BY c1,c2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < '10:22:33' AND c1 = '491:22:33' ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c1,c2 DESC;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND '10:22:33' ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND '10:22:33' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 IN (NULL,'10:22:33') ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t3 WHERE c2 IN (NULL,'10:22:33') ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < '10:22:33' AND c1 = '491:22:33' ORDER BY c1,c2 DESC;
c1	c2	c3
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < '10:22:33' AND c1 = '491:22:33' ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
00:00:11	00:00:11	2009-01-20
00:00:12	00:00:12	2009-01-13
00:00:45	00:00:45	2009-01-07
00:11:12	00:11:12	2009-01-19
00:12:30	00:12:30	2009-01-23
00:12:34	00:12:34	2009-01-14
01:23:00	01:23:00	2009-01-03
08:03:02	08:03:02	2009-01-18
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
10:00:00	10:00:00	2009-01-06
10:11:12	10:11:12	2009-01-11
10:22:33	10:22:33	2009-01-02
11:11:12	11:11:12	2009-01-12
11:11:27	11:11:27	2009-01-17
12:34:57	12:34:57	2009-01-01
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
c1	c2	c3
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
SELECT c1 FROM t3 GROUP BY c1;
c1
-838:59:59
00:00:00
00:00:11
00:00:12
00:00:45
00:11:12
00:12:30
00:12:34
01:23:00
08:03:02
08:29:45
09:00:45
09:36:00
10:00:00
10:11:12
10:22:33
11:11:12
11:11:27
12:34:57
12:34:58
12:35:57
491:22:33
825:23:00
838:59:59
SELECT DISTINCT c1 FROM t3;
c1
-838:59:59
00:00:00
00:00:11
00:00:12
00:00:45
00:11:12
00:12:30
00:12:34
01:23:00
08:03:02
08:29:45
09:00:45
09:36:00
10:00:00
10:11:12
10:22:33
11:11:12
11:11:27
12:34:57
12:34:58
12:35:57
491:22:33
825:23:00
838:59:59
SELECT c1,MIN(c2) FROM t3 GROUP BY c1;
c1	MIN(c2)
-838:59:59	-838:59:59
00:00:00	00:00:00
00:00:11	00:00:11
00:00:12	00:00:12
00:00:45	00:00:45
00:11:12	00:11:12
00:12:30	00:12:30
00:12:34	00:12:34
01:23:00	01:23:00
08:03:02	08:03:02
08:29:45	NULL
09:00:45	09:00:45
09:36:00	09:36:00
10:00:00	10:00:00
10:11:12	10:11:12
10:22:33	10:22:33
11:11:12	11:11:12
11:11:27	11:11:27
12:34:57	12:34:57
12:34:58	12:34:58
12:35:57	12:35:57
491:22:33	491:22:33
825:23:00	825:23:00
838:59:59	838:59:59
SELECT CAST(235959.123456 AS TIME);
CAST(235959.123456 AS TIME)
23:59:59
SELECT CAST(0.235959123456e+6 AS TIME);
CAST(0.235959123456e+6 AS TIME)
23:59:59
SELECT CAST(235959123456e-6 AS TIME);
CAST(235959123456e-6 AS TIME)
23:59:59
SELECT CAST(235959.1234567 AS TIME);
CAST(235959.1234567 AS TIME)
23:59:59
SELECT CAST(0.2359591234567e6 AS TIME);
CAST(0.2359591234567e6 AS TIME)
23:59:59
SELECT CAST(0.2359591234567e+30 AS TIME);
CAST(0.2359591234567e+30 AS TIME)
NULL
Warnings:
Warning	1292	Truncated incorrect time value: '2.359591234567e29'
select cast('100:55:50' as time) < cast('24:00:00' as time);
cast('100:55:50' as time) < cast('24:00:00' as time)
0
select cast('100:55:50' as time) < cast('024:00:00' as time);
cast('100:55:50' as time) < cast('024:00:00' as time)
0
select cast('300:55:50' as time) < cast('240:00:00' as time);
cast('300:55:50' as time) < cast('240:00:00' as time)
0
select cast('100:55:50' as time) > cast('24:00:00' as time);
cast('100:55:50' as time) > cast('24:00:00' as time)
1
select cast('100:55:50' as time) > cast('024:00:00' as time);
cast('100:55:50' as time) > cast('024:00:00' as time)
1
select cast('300:55:50' as time) > cast('240:00:00' as time);
cast('300:55:50' as time) > cast('240:00:00' as time)
1
create table t5 (f1 time);
insert into t5 values ('24:00:00');
select cast('24:00:00' as time) = (select f1 from t5);
cast('24:00:00' as time) = (select f1 from t5)
1
drop table t5;
INSERT INTO t1(c1,c2) VALUES('10:22:33','10:22:34') /* throws error as row exists with c1='10:22:33' */;
ERROR 23000: Duplicate entry '10:22:33' for key 't1.PRIMARY'
INSERT INTO t2(c1,c2) VALUES('12:34:56.78','12:34:56.78') /* throws error as row exists with c1='12:34:56.78',c2='12:34:56.78' */;
ERROR 23000: Duplicate entry '12:34:57-12:34:57' for key 't2.PRIMARY'
INSERT INTO t1(c1,c2) VALUES('10:22:34','34 9:23') /* throws error as row exists with c2='34 9:23' */;
ERROR 23000: Duplicate entry '825:23:00' for key 't1.c2'
INSERT INTO t3(c1,c2) VALUES('34 9:23','34 9:23') /* throws error as row exists with c1='34 9:23',c2='34 9:23' */;
ERROR 23000: Duplicate entry '825:23:00-825:23:00' for key 't3.idx'
INSERT IGNORE INTO t1(c1,c2) VALUES('10:22:33','10:22:34') /* doesn't throw error */;
Warnings:
Warning	1062	Duplicate entry '10:22:33' for key 't1.PRIMARY'
INSERT IGNORE INTO t2(c1,c2) VALUES('12:34:56.78','12:34:56.78') /*doesn't throw error */;
Warnings:
Warning	1062	Duplicate entry '12:34:57-12:34:57' for key 't2.PRIMARY'
INSERT IGNORE INTO t1(c1,c2) VALUES('10:22:34','34 9:23') /*doesn't throw error */;
Warnings:
Warning	1062	Duplicate entry '825:23:00' for key 't1.c2'
INSERT IGNORE INTO t3(c1,c2) VALUES('34 9:23','34 9:23') /*doesn't throw error */;
Warnings:
Warning	1062	Duplicate entry '825:23:00-825:23:00' for key 't3.idx'
SELECT * FROM t1 WHERE c1='10:23:33' /* no rows */;
c1	c2	c3
INSERT INTO t1(c1) VALUES('10:22:33') ON DUPLICATE KEY UPDATE c1='10:23:33';
SELECT * FROM t1 WHERE c1='10:23:33' /* 1 row */;
c1	c2	c3
10:23:33	10:22:33	2009-01-02
SELECT * FROM t2 WHERE c1='12:34:56.79' AND c2='12:34:57.78' /* no rows */;
c1	c2	c3
INSERT INTO t2(c1,c2) VALUES('12:34:56.78','12:34:56.78') ON DUPLICATE KEY UPDATE c1='12:34:56.79',c2='12:34:57.78';
SELECT * FROM t2 WHERE c1='12:34:56.79' AND c2='12:34:57.78' /* 1 row */;
c1	c2	c3
12:34:57	12:34:58	2009-01-01
SELECT * FROM t1 WHERE c1='10:22:35' AND c2='33 9:23' /* no rows */;
c1	c2	c3
INSERT INTO t1(c1,c2) VALUES('10:22:34','34 9:23') ON DUPLICATE KEY UPDATE c1='10:22:35',c2='33 9:23';
SELECT * FROM t1 WHERE c1='10:22:35' AND c2='33 9:23' /* 1 row */;
c1	c2	c3
10:22:35	801:23:00	2009-01-05
SELECT * FROM t3 WHERE c1='32 9:23' AND c2='33 9:23' /* no rows */;
c1	c2	c3
INSERT INTO t3(c1,c2) VALUES('34 9:23','34 9:23') ON DUPLICATE KEY UPDATE c1='32 9:23',c2='33 9:23';
SELECT * FROM t3 WHERE c1='32 9:23' AND c2='33 9:23' /* 1 row */;
c1	c2	c3
777:23:00	801:23:00	2009-01-05
INSERT INTO t1 SET c1='07:23:55',c2='13 06:23:55';
INSERT INTO t2 SET c1='07:23:55',c2='13 06:23:55';
INSERT INTO t3 SET c1='07:23:55';
INSERT INTO t4 SET c2='07:23:55';
Warnings:
Warning	1364	Field 'c1' doesn't have a default value
SELECT * FROM t1 WHERE c1='07:23:55' AND c2='13 06:23:55' /* Returns 1 row with values for other column as NULL */;
c1	c2	c3
07:23:55	318:23:55	NULL
SELECT * FROM t2 WHERE c1='07:23:55' AND c2='13 06:23:55' /* Returns 1 row with values for other column as NULL */;
c1	c2	c3
07:23:55	318:23:55	NULL
SELECT * FROM t3 WHERE c1='07:23:55' /* Returns 1 row with values for other column as NULL */;
c1	c2	c3
07:23:55	NULL	NULL
SELECT * FROM t4 WHERE c2='07:23:55' /* Returns 1 row with values for other column as NULL */;
c1	c2	c3
00:00:00	07:23:55	NULL
CREATE TABLE t5(c1 TIME NOT NULL, c2 TIME NULL, c3 DATE, INDEX idx(c1,c2));
INSERT INTO t5 SELECT * FROM t4;
SELECT * FROM t5;
c1	c2	c3
12:34:57	12:34:57	2009-01-01
10:22:33	10:22:33	2009-01-02
01:23:00	01:23:00	2009-01-03
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
10:00:00	10:00:00	2009-01-06
00:00:45	00:00:45	2009-01-07
-838:59:59	-838:59:59	2009-01-08
00:00:00	00:00:00	2009-01-09
838:59:59	838:59:59	2009-01-10
10:11:12	10:11:12	2009-01-11
11:11:12	11:11:12	2009-01-12
00:00:12	00:00:12	2009-01-13
00:12:34	00:12:34	2009-01-14
12:34:58	12:34:58	2009-01-15
12:35:57	12:35:57	2009-01-16
11:11:27	11:11:27	2009-01-17
08:03:02	08:03:02	2009-01-18
00:11:12	00:11:12	2009-01-19
00:00:11	00:00:11	2009-01-20
00:12:30	00:12:30	2009-01-23
09:00:45	09:00:45	2009-01-24
09:36:00	09:36:00	2009-01-25
00:00:10	00:00:10	2009-01-26
00:00:00	00:00:00	2009-01-27
838:59:59	838:59:59	2009-01-28
838:59:59	838:59:59	2009-01-29
262:22:00	262:22:00	2009-01-30
00:00:12	00:00:12	2009-01-31
08:29:45	NULL	2009-02-01
00:00:00	07:23:55	NULL
TRUNCATE TABLE t5;
INSERT INTO t5 SELECT * FROM t4 WHERE c1 >='12colon34colon56';
Warnings:
Warning	1292	Incorrect time value: '12colon34colon56' for column 'c1' at row 1
SELECT * FROM t5;
c1	c2	c3
491:22:33	491:22:33	2009-01-04
825:23:00	825:23:00	2009-01-05
838:59:59	838:59:59	2009-01-10
838:59:59	838:59:59	2009-01-28
838:59:59	838:59:59	2009-01-29
262:22:00	262:22:00	2009-01-30
TRUNCATE TABLE t5;
DROP TABLE t5;
DROP TABLE t1,t2,t3,t4;
SET sql_mode=default;
