############## mysql-test\t\flush_basic.test ##################################
#                                                                             #
# Variable Name: flush                                                        #
# Scope: GLOBAL                                                               #
# Access Type: Dynamic                                                        #
# Data Type: boolean                                                          #
# Default Value: OFF                                                          #
# Range:                                                                      #
#                                                                             #
#                                                                             #
# Creation Date: 2008-02-09                                                   #
# Author:  Salman                                                             #
#                                                                             #
# Description: Test Cases of Dynamic System Variable flush                    #
#              that checks the behavior of this variable in the following ways#
#              * Default Value                                                #
#              * Valid & Invalid values                                       #
#              * Scope & Access method                                        #
#              * Data Integrity                                               #
#                                                                             #
# Reference: http://dev.mysql.com/doc/refman/5.1/en/                          #
#      server-options.html#option_mysqld_flush                                #
#                                                                             #
###############################################################################

--source include/load_sysvars.inc
######################################################################## 
#                    START OF flush TESTS                              #
######################################################################## 


#############################################################
# Saving initial value of flush in a temporary variable     #
#############################################################

SET @start_value = @@global.flush;
SELECT @start_value;


--echo '#--------------------FN_DYNVARS_030_01------------------------#'
#############################################################
#              Display the DEFAULT value of flush           #
#############################################################

SET @@global.flush = ON;
SET @@global.flush = DEFAULT;
SELECT @@global.flush;


--echo '#---------------------FN_DYNVARS_030_02-------------------------#'
############################################### 
#     Verify default value of variable        #
############################################### 

SET @@global.flush = @start_value;
SELECT @@global.flush;


--echo '#--------------------FN_DYNVARS_030_03------------------------#'
#############################################################
#        Change the value of flush to a valid value         #
#############################################################

SET @@global.flush = ON;
SELECT @@global.flush;
SET @@global.flush = OFF;
SELECT @@global.flush;
SET @@global.flush = 0;
SELECT @@global.flush;
SET @@global.flush = 1;
SELECT @@global.flush;


--echo '#--------------------FN_DYNVARS_030_04-------------------------#'
########################################################################### 
#              Change the value of flush to invalid value                 #
########################################################################### 

--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.flush = 2;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.flush = -1;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.flush = TRUEF;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.flush = TRUE_F;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.flush = FALSE0;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.flush = OON;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.flush = ONN;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.flush = OOFF;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.flush = 0FF;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.flush = ' ';
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.flush = " ";
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.flush = '';


--echo '#-------------------FN_DYNVARS_030_05----------------------------#'
########################################################################### 
#            Test if accessing session flush gives error                  #
########################################################################### 

--Error ER_GLOBAL_VARIABLE
SET @@session.flush = 1;
--Error ER_INCORRECT_GLOBAL_LOCAL_VAR
SELECT @@session.flush;


--echo '#----------------------FN_DYNVARS_030_06------------------------#'
####################################################################
# Check if the value in GLOBAL Tables matches values in variable   #
####################################################################

--disable_warnings
SELECT IF(@@global.flush, "ON", "OFF") = VARIABLE_VALUE 
FROM performance_schema.global_variables 
WHERE VARIABLE_NAME='flush';
--enable_warnings


--echo '#---------------------FN_DYNVARS_030_07----------------------#'
################################################################### 
#      Check if TRUE and FALSE values can be used on variable     #
################################################################### 

SET @@global.flush = TRUE;
SELECT @@global.flush;
SET @@global.flush = FALSE;
SELECT @@global.flush;


--echo '#---------------------FN_DYNVARS_030_08----------------------#'
###############################################################################
#    Check if accessing variable without SCOPE points to same global variable #
###############################################################################

SET @@global.flush = 1;
SELECT @@flush = @@global.flush;

--echo '#---------------------FN_DYNVARS_030_09----------------------#'
##########################################################################  
#        Check if flush can be accessed with and without @@ sign         #
##########################################################################

--Error ER_GLOBAL_VARIABLE
SET flush = 1;
--Error ER_PARSE_ERROR
SET global.flush = 1;
--Error ER_UNKNOWN_TABLE
SELECT global.flush;
--Error ER_BAD_FIELD_ERROR
SELECT flush;
--Error ER_INCORRECT_GLOBAL_LOCAL_VAR
SELECT @@session.flush;


##############################  
#   Restore initial value    #
##############################

SET @@global.flush = @start_value;
SELECT @@global.flush;


#############################################################
#              END OF flush TESTS                           #
#############################################################
