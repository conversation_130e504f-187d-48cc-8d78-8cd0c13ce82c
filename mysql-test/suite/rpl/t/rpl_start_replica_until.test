# ==== Purpose ====
#
# Verify that START REPLICA UNTIL replicates until the given binlog
# position but not longer.  Verify that START REPLICA UNTIL with various
# incorrect arguments gives an error.
#
# ==== Method ====
#
# On master, create a table and insert some rows.  On slave, START
# SLAVE UNTIL so that it reads one event at a time, and check the
# table and the slave status each time.
#
# Then, on slave, run START REPLICA UNTIL with incorrect arguments and
# verify that it gives an error.
#
# ==== Related bugs ====
#
# Bug in this test: BUG#37717: rpl.rpl_stm_until 'stmt' fails sporadically on pushbuild

# This test is binary_log format agnostic so row is chosen.
--source include/have_binlog_format_row.inc
--source include/rpl/init_source_replica.inc

# Test is dependent on binlog positions

# prepare version for substitutions
let $VERSION=`select version()`;

# Stop replica before it starts replication.  Also sync with master
# to avoid nondeterministic behaviour.
--echo [on slave]
--source include/rpl/sync_to_replica.inc
--source include/rpl/stop_replica.inc

--echo ==== Create some events on master ====

--echo [on master]
connection master;
create table t1(n int not null auto_increment primary key);
insert into t1 values (1),(2),(3),(4);
let $source_log_pos_1= query_get_value(SHOW BINARY LOG STATUS, Position, 1);
let $source_log_file= query_get_value(SHOW BINARY LOG STATUS, File, 1);
drop table t1;
create table t2(n int not null auto_increment primary key);
insert into t2 values (1),(2);
let $source_log_pos_2= query_get_value(SHOW BINARY LOG STATUS, Position, 1);
insert into t2 values (3),(4);
drop table t2;

--echo ==== Replicate one event at a time on slave ====

# try to replicate all queries until drop of t1
--echo [on slave]
connection slave;
--replace_result $source_log_file SOURCE_LOG_FILE $source_log_pos_1 SOURCE_LOG_POS
eval START REPLICA until SOURCE_LOG_FILE='$source_log_file', SOURCE_LOG_POS=$source_log_pos_1;
--source include/rpl/wait_for_receiver_to_start.inc
--source include/rpl/wait_for_applier_to_stop.inc
# here table should be still not deleted
select * from t1;
--let $slave_param= Exec_Source_Log_Pos
--let $slave_param_value= $source_log_pos_1
--source include/rpl/assert_replica_status.inc

# this should fail right after start
--replace_result 291 SOURCE_LOG_POS
START REPLICA until SOURCE_LOG_FILE='master-no-such-bin.000001', SOURCE_LOG_POS=291;
--source include/rpl/wait_for_receiver_to_start.inc
--source include/rpl/wait_for_applier_to_stop.inc
# again this table should be still not deleted
select * from t1;
--let $slave_param= Exec_Source_Log_Pos
--let $slave_param_value= $source_log_pos_1
--source include/rpl/assert_replica_status.inc

let $relay_log_file= slave-relay-bin.000003;
let $source_log_pos= $source_log_pos_2;
source include/rpl/convert_binlog_position_to_relay_log_position.inc;
# try replicate all up to and not including the second insert to t2;
--replace_result $relay_log_pos RELAY_LOG_POS
eval START REPLICA until relay_log_file='$relay_log_file', relay_log_pos=$relay_log_pos;
--source include/rpl/wait_for_receiver_to_start.inc
--source include/rpl/wait_for_applier_to_stop.inc
select * from t2;
--let $slave_param= Exec_Source_Log_Pos
--let $slave_param_value= $source_log_pos
--source include/rpl/assert_replica_status.inc

# clean up
START REPLICA;
--echo [on master]
connection master;
--echo [on slave]
--source include/rpl/sync_to_replica.inc
--source include/rpl/stop_replica.inc

--let $exec_log_pos_1= query_get_value(SHOW REPLICA STATUS, Exec_Source_Log_Pos, 1)
# this should stop immediately as we are already there
--replace_result $source_log_file SOURCE_LOG_FILE $source_log_pos_2 SOURCE_LOG_POS
eval START REPLICA until SOURCE_LOG_FILE='$source_log_file', SOURCE_LOG_POS=$source_log_pos_2;
--source include/rpl/wait_for_receiver_to_start.inc
--source include/rpl/wait_for_applier_to_stop.inc
--let $slave_param= Exec_Source_Log_Pos
--let $slave_param_value= $exec_log_pos_1
--source include/rpl/assert_replica_status.inc

--echo ==== Test various error conditions ====

--replace_result 561 SOURCE_LOG_POS
--error 1277
START REPLICA until SOURCE_LOG_FILE='master-bin', SOURCE_LOG_POS=561;
--replace_result 561 SOURCE_LOG_POS 12 RELAY_LOG_POS
--error 1277
START REPLICA until SOURCE_LOG_FILE='master-bin.000001', SOURCE_LOG_POS=561, relay_log_pos=12;
--error 1277
START REPLICA until SOURCE_LOG_FILE='master-bin.000001';
--error 1277
START REPLICA until relay_log_file='slave-relay-bin.000002';
--replace_result 561 SOURCE_LOG_POS
--error 1277
START REPLICA until relay_log_file='slave-relay-bin.000002', SOURCE_LOG_POS=561;
# Warning should be given for second command
START REPLICA sql_thread;
--replace_result 776 SOURCE_LOG_POS
START REPLICA until SOURCE_LOG_FILE='master-bin.000001', SOURCE_LOG_POS=776;

#
# Note: The test for START REPLICA UNTIL SQL_AFTER_MTS_GAPS is located
#       in rpl_parallel_start_stop.test
#

#
# bug#47210  first execution of "start slave until" stops too early  
#
# testing that a slave rotate event that is caused by stopping the slave
# does not intervene anymore in UNTIL condition.
#

connection slave;
source include/rpl/stop_replica.inc;
--disable_warnings
drop table if exists t1;
--enable_warnings
RESET REPLICA;
RESET BINARY LOGS AND GTIDS;
--replace_result $MASTER_MYPORT SOURCE_PORT
eval CHANGE REPLICATION SOURCE to SOURCE_HOST='127.0.0.1',SOURCE_PORT=$MASTER_MYPORT, SOURCE_USER='root'; 

connection master;
--disable_warnings
drop table if exists t1;
--enable_warnings
reset binary logs and gtids;
create table t1 (a int primary key auto_increment);
save_master_pos;
let $master_pos= query_get_value(SHOW BINARY LOG STATUS, Position, 1);

connection slave;
START REPLICA;
sync_with_master;

# at this point slave will close the relay log stamping it with its own
# Rotate log event. This event won't be examined on matter of the master 
# UNTIL pos anymore.
source include/rpl/stop_replica.inc;
let $slave_exec_pos= query_get_value(SHOW REPLICA STATUS, Exec_Source_Log_Pos, 1);

--echo master and slave are in sync now
let $diff_pos= `select $master_pos - $slave_exec_pos`;
eval select $diff_pos as zero;

connection master;
insert into t1 set a=null;
let $until_pos= query_get_value(SHOW BINARY LOG STATUS, Position, 1);
insert into t1 set a=null;
select count(*) as two from t1;

connection slave;
--replace_result $source_log_file SOURCE_LOG_FILE $until_pos UNTIL_POS;
eval START REPLICA until SOURCE_LOG_FILE='$source_log_file', SOURCE_LOG_POS= $until_pos;
source include/rpl/wait_for_applier_to_stop.inc;
let $slave_exec_pos= query_get_value(SHOW REPLICA STATUS, Exec_Source_Log_Pos, 1);
--echo slave stopped at the prescribed position
let $diff_pos= `select $until_pos - $slave_exec_pos`;
eval select $diff_pos as zero;
select count(*) as one from t1;


connection master;
drop table t1;

connection slave;
START REPLICA;
sync_with_master;

#
# Bug #19010820 START SLAVE UNTIL WILL TIMEOUT IF WAITING FOR
#               THE LAST TRANSACTION IN RELAYLOG
#
# This part of the test verifies correctness and effectiveness of the
# until-stopping when the position used is inside the last group/transaction
# in the relaylog.
#
# It will create a single event transaction (if GTIDs are disabled)
# or a two events transaction (if GTIDs are enabled), and will start
# the SQL thread using the UNTIL Source_Log_File/POS and RELAY_LOG_FILE/POS,
# pointing to the first or last byte of the created transaction.
#
# The test expects that, after issuing the START REPLICA SQL_THREAD with the
# UNTIL clause, the SQL thread will apply the created transaction and will
# update its position before stopping. The test will verify the
# binlog/relaylog file, the position, and finally will wait for the SQL
# thread to stop.
#

--source include/rpl/connection_source.inc
--echo # Make sure the master will put the new events in a new binlog file
FLUSH LOGS;

# Check if SQL thread stops correctly
# after executing the last event of the master binlog
# based on master binlog file and position
--echo # Testing the execution until the last transaction of the master binlog

# Stop the SQL thread after syncing
--source include/rpl/sync_to_replica.inc
--source include/rpl/stop_applier.inc

# Create a table in the master (one event without GTIDS, two with GTIDS)
--source include/rpl/connection_source.inc
CREATE TABLE t1 (a INT) ENGINE=InnoDB;

# Save master position to use on UNTIL clause of START REPLICA
--let $master_file= query_get_value(SHOW BINARY LOG STATUS, File, 1)
--let $master_pos= query_get_value(SHOW BINARY LOG STATUS, Position, 1)
--dec $master_pos

# Sync the IO thread with the master
--source include/rpl/sync_to_replica_received.inc

--echo # Start SQL thread until it executed the CREATE TABLE
--replace_result $master_file MASTER_FILE $master_pos MASTER_POS
eval START REPLICA SQL_THREAD UNTIL SOURCE_LOG_FILE = "$master_file", SOURCE_LOG_POS = $master_pos;

--echo # Wait until SQL thread reaches last master binlog file
--let $slave_param= Relay_Source_Log_File
--let $slave_param_value= $master_file
--let $slave_param_comparison= =
--source include/rpl/wait_for_replica_status.inc

--echo # Wait until SQL thread reaches desired master binlog position
--let $slave_param= Exec_Source_Log_Pos
--let $slave_param_value= $master_pos
--let $slave_param_comparison= >=
--source include/rpl/wait_for_replica_status.inc

--echo # If the desired position was reached, SQL thread should stop.
--source include/rpl/wait_for_applier_to_stop.inc

--let $assert_cond= "[SHOW TABLES LIKE "t1"]" = "t1"
--let $assert_text= t1 should be replicated
--source include/assert.inc
#
# Check if SQL thread stops correctly
# after executing the last event of the slave relaylog
# based on slave relaylog file and position
--echo # Testing the execution until the last transaction of the slave relaylog

# Save slave position to use on UNTIL clause of START REPLICA
--let $slave_file= query_get_value(SHOW REPLICA STATUS, Relay_Log_File, 1)
--let $slave_pos= query_get_value(SHOW REPLICA STATUS, Relay_Log_Pos, 1)
--inc $slave_pos

# Create a table in the master
# (one event if GTID_MODE=OFF; two events if GTID_MODE=ON)
--source include/rpl/connection_source.inc
CREATE TABLE t2 (a INT) ENGINE=InnoDB;

# Sync the IO thread with the master
--source include/rpl/sync_to_replica_received.inc

--echo # Start SQL thread until it executed the CREATE TABLE
--replace_result $slave_file SLAVE_FILE $slave_pos SLAVE_POS
eval START REPLICA SQL_THREAD UNTIL RELAY_LOG_FILE = "$slave_file", RELAY_LOG_POS = $slave_pos;

--echo # Wait until SQL thread reaches last slave relaylog file
--let $slave_param= Relay_Log_File
--let $slave_param_value= $slave_file
--let $slave_param_comparison= =
--source include/rpl/wait_for_replica_status.inc

--echo # Wait until SQL thread reaches desired slave relaylog position
--let $slave_param= Relay_Log_Pos
--let $slave_param_value= $slave_pos
--let $slave_param_comparison= >=
--source include/rpl/wait_for_replica_status.inc

--echo # If the desired position was reached, SQL thread should stop.
--source include/rpl/wait_for_applier_to_stop.inc

--let $assert_cond= "[SHOW TABLES LIKE "t2"]" = "t2"
--let $assert_text= t2 should be replicated
--source include/assert.inc

# Cleanup
--source include/rpl/start_applier.inc
--source include/rpl/connection_source.inc
DROP TABLE t1, t2;

# End of tests
--source include/rpl/deinit.inc
