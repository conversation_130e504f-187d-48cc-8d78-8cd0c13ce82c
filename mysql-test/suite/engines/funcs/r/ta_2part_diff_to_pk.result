DROP TABLE IF EXISTS t1;
SET sql_mode='NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1(c1 BIT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` bit(1) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` tinyint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` smallint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` mediumint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` int NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` bigint NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` double NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` float NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` decimal(10,0) NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` date NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` time NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` timestamp NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bit(1) NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 REAL NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` double NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` float NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` decimal(10,0) NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 DATE NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` date NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIME NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` time NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` timestamp NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 YEAR NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` year NOT NULL,
  `c2` year NOT NULL,
  `c3` int DEFAULT NULL,
  `c4` varchar(10) NOT NULL,
  PRIMARY KEY (`c1`,`c2`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
SET sql_mode=default;
