#Want to skip this test from daily Valgrind execution
--source include/no_valgrind_without_big.inc
--disable_warnings
DROP TABLE IF EXISTS t1;
--enable_warnings
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='a' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='a' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='b' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='b' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='c' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='c' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='d' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='d' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='e' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='e' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='f' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='f' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
--source suite/engines/funcs/t/data1.inc
SELECT COUNT(*) FROM t1,t1 AS b;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y';
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c2='y' AND t1.c1 = b.c1 AND t1.c2 = b.c2;
SELECT COUNT(*) FROM t1,t1 AS b WHERE t1.c1='y' AND t1.c2 = b.c1 AND t1.c1 = b.c2;
DROP TABLE t1;

