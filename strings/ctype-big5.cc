/* Copyright (c) 2000, 2025, Oracle and/or its affiliates.

   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.

   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   Without limiting anything contained in the foregoing, this file,
   which is part of C Driver for MySQL (Connector/C), is also subject to the
   Universal FOSS Exception, version 1.0, a copy of which can be found at
   http://oss.oracle.com/licenses/universal-foss-exception.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.

   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */

/*
  Support for Chinese(BIG5) characters.
  This file is basically usa7 character sets with some extra functions
  for big5 handling
 */

#include <algorithm>
#include <cstddef>
#include <cstdint>

#include "mysql/strings/m_ctype.h"
#include "strings/m_ctype_internals.h"

#define isbig5head(c) (0xa1 <= (uint8_t)(c) && (uint8_t)(c) <= 0xf9)
#define isbig5tail(c)                                \
  ((0x40 <= (uint8_t)(c) && (uint8_t)(c) <= 0x7e) || \
   (0xa1 <= (uint8_t)(c) && (uint8_t)(c) <= 0xfe))

#define isbig5code(c, d) (isbig5head(c) && isbig5tail(d))
#define big5code(c, d) (((uint8_t)(c) << 8) | (uint8_t)(d))
#define big5head(e) ((uint8_t)((e) >> 8))
#define big5tail(e) ((uint8_t)((e)&0xff))

static const uint8_t ctype_big5[257] = {
    0, /* For standard library */
    32,  32,  32,  32,  32,  32,  32,  32,  32,  40,  40, 40, 40, 40, 32, 32,
    32,  32,  32,  32,  32,  32,  32,  32,  32,  32,  32, 32, 32, 32, 32, 32,
    72,  16,  16,  16,  16,  16,  16,  16,  16,  16,  16, 16, 16, 16, 16, 16,
    132, 132, 132, 132, 132, 132, 132, 132, 132, 132, 16, 16, 16, 16, 16, 16,
    16,  129, 129, 129, 129, 129, 129, 1,   1,   1,   1,  1,  1,  1,  1,  1,
    1,   1,   1,   1,   1,   1,   1,   1,   1,   1,   1,  16, 16, 16, 16, 16,
    16,  130, 130, 130, 130, 130, 130, 2,   2,   2,   2,  2,  2,  2,  2,  2,
    2,   2,   2,   2,   2,   2,   2,   2,   2,   2,   2,  16, 16, 16, 16, 32,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,  0,  0,  0,  0,  0,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,  0,  0,  0,  0,  0,
    0,   3,   3,   3,   3,   3,   3,   3,   3,   3,   3,  3,  3,  3,  3,  3,
    3,   3,   3,   3,   3,   3,   3,   3,   3,   3,   3,  3,  3,  3,  3,  3,
    3,   3,   3,   3,   3,   3,   3,   3,   3,   3,   3,  3,  3,  3,  3,  3,
    3,   3,   3,   3,   3,   3,   3,   3,   3,   3,   3,  3,  3,  3,  3,  3,
    3,   3,   3,   3,   3,   3,   3,   3,   3,   3,   3,  3,  3,  3,  3,  3,
    3,   3,   3,   3,   3,   3,   3,   3,   3,   3,   0,  0,  0,  0,  0,  0,
};

// clang-format off
static const uint8_t to_lower_big5[] = {
    '\000',        '\001',        '\002',        '\003',        '\004',
    '\005',        '\006',        '\007',        '\010',        '\011',
    '\012',        '\013',        '\014',        '\015',        '\016',
    '\017',        '\020',        '\021',        '\022',        '\023',
    '\024',        '\025',        '\026',        '\027',        '\030',
    '\031',        '\032',        '\033',        '\034',        '\035',
    '\036',        '\037',        ' ',           '!',           '"',
    '#',           '$',           '%',           '&',           '\'',
    '(',           ')',           '*',           '+',           ',',
    '-',           '.',           '/',           '0',           '1',
    '2',           '3',           '4',           '5',           '6',
    '7',           '8',           '9',           ':',           ';',
    '<',           '=',           '>',           '?',           '@',
    'a',           'b',           'c',           'd',           'e',
    'f',           'g',           'h',           'i',           'j',
    'k',           'l',           'm',           'n',           'o',
    'p',           'q',           'r',           's',           't',
    'u',           'v',           'w',           'x',           'y',
    'z',           '[',           '\\',          ']',           '^',
    '_',           '`',           'a',           'b',           'c',
    'd',           'e',           'f',           'g',           'h',
    'i',           'j',           'k',           'l',           'm',
    'n',           'o',           'p',           'q',           'r',
    's',           't',           'u',           'v',           'w',
    'x',           'y',           'z',           '{',           '|',
    '}',           '~',           '\177',        u'\200',       u'\201',
    u'\202',       u'\203',       u'\204',       u'\205',       u'\206',
    u'\207',       u'\210',       u'\211',       u'\212',       u'\213',
    u'\214',       u'\215',       u'\216',       u'\217',       u'\220',
    u'\221',       u'\222',       u'\223',       u'\224',       u'\225',
    u'\226',       u'\227',       u'\230',       u'\231',       u'\232',
    u'\233',       u'\234',       u'\235',       u'\236',       u'\237',
    u'\240',       u'\241',       u'\242',       u'\243',       u'\244',
    u'\245',       u'\246',       u'\247',       u'\250',       u'\251',
    u'\252',       u'\253',       u'\254',       u'\255',       u'\256',
    u'\257',       u'\260',       u'\261',       u'\262',       u'\263',
    u'\264',       u'\265',       u'\266',       u'\267',       u'\270',
    u'\271',       u'\272',       u'\273',       u'\274',       u'\275',
    u'\276',       u'\277',       u'\300',       u'\301',       u'\302',
    u'\303',       u'\304',       u'\305',       u'\306',       u'\307',
    u'\310',       u'\311',       u'\312',       u'\313',       u'\314',
    u'\315',       u'\316',       u'\317',       u'\320',       u'\321',
    u'\322',       u'\323',       u'\324',       u'\325',       u'\326',
    u'\327',       u'\330',       u'\331',       u'\332',       u'\333',
    u'\334',       u'\335',       u'\336',       u'\337',       u'\340',
    u'\341',       u'\342',       u'\343',       u'\344',       u'\345',
    u'\346',       u'\347',       u'\350',       u'\351',       u'\352',
    u'\353',       u'\354',       u'\355',       u'\356',       u'\357',
    u'\360',       u'\361',       u'\362',       u'\363',       u'\364',
    u'\365',       u'\366',       u'\367',       u'\370',       u'\371',
    u'\372',       u'\373',       u'\374',       u'\375',       u'\376',
    u'\377',
};
// clang-format on

// clang-format off
static const uint8_t to_upper_big5[] = {
    '\000',        '\001',        '\002',        '\003',        '\004',
    '\005',        '\006',        '\007',        '\010',        '\011',
    '\012',        '\013',        '\014',        '\015',        '\016',
    '\017',        '\020',        '\021',        '\022',        '\023',
    '\024',        '\025',        '\026',        '\027',        '\030',
    '\031',        '\032',        '\033',        '\034',        '\035',
    '\036',        '\037',        ' ',           '!',           '"',
    '#',           '$',           '%',           '&',           '\'',
    '(',           ')',           '*',           '+',           ',',
    '-',           '.',           '/',           '0',           '1',
    '2',           '3',           '4',           '5',           '6',
    '7',           '8',           '9',           ':',           ';',
    '<',           '=',           '>',           '?',           '@',
    'A',           'B',           'C',           'D',           'E',
    'F',           'G',           'H',           'I',           'J',
    'K',           'L',           'M',           'N',           'O',
    'P',           'Q',           'R',           'S',           'T',
    'U',           'V',           'W',           'X',           'Y',
    'Z',           '[',           '\\',          ']',           '^',
    '_',           '`',           'A',           'B',           'C',
    'D',           'E',           'F',           'G',           'H',
    'I',           'J',           'K',           'L',           'M',
    'N',           'O',           'P',           'Q',           'R',
    'S',           'T',           'U',           'V',           'W',
    'X',           'Y',           'Z',           '{',           '|',
    '}',           '~',           '\177',        u'\200',       u'\201',
    u'\202',       u'\203',       u'\204',       u'\205',       u'\206',
    u'\207',       u'\210',       u'\211',       u'\212',       u'\213',
    u'\214',       u'\215',       u'\216',       u'\217',       u'\220',
    u'\221',       u'\222',       u'\223',       u'\224',       u'\225',
    u'\226',       u'\227',       u'\230',       u'\231',       u'\232',
    u'\233',       u'\234',       u'\235',       u'\236',       u'\237',
    u'\240',       u'\241',       u'\242',       u'\243',       u'\244',
    u'\245',       u'\246',       u'\247',       u'\250',       u'\251',
    u'\252',       u'\253',       u'\254',       u'\255',       u'\256',
    u'\257',       u'\260',       u'\261',       u'\262',       u'\263',
    u'\264',       u'\265',       u'\266',       u'\267',       u'\270',
    u'\271',       u'\272',       u'\273',       u'\274',       u'\275',
    u'\276',       u'\277',       u'\300',       u'\301',       u'\302',
    u'\303',       u'\304',       u'\305',       u'\306',       u'\307',
    u'\310',       u'\311',       u'\312',       u'\313',       u'\314',
    u'\315',       u'\316',       u'\317',       u'\320',       u'\321',
    u'\322',       u'\323',       u'\324',       u'\325',       u'\326',
    u'\327',       u'\330',       u'\331',       u'\332',       u'\333',
    u'\334',       u'\335',       u'\336',       u'\337',       u'\340',
    u'\341',       u'\342',       u'\343',       u'\344',       u'\345',
    u'\346',       u'\347',       u'\350',       u'\351',       u'\352',
    u'\353',       u'\354',       u'\355',       u'\356',       u'\357',
    u'\360',       u'\361',       u'\362',       u'\363',       u'\364',
    u'\365',       u'\366',       u'\367',       u'\370',       u'\371',
    u'\372',       u'\373',       u'\374',       u'\375',       u'\376',
    u'\377',
};
// clang-format on

// clang-format off
static const uint8_t sort_order_big5[] = {
    '\000',        '\001',        '\002',        '\003',        '\004',
    '\005',        '\006',        '\007',        '\010',        '\011',
    '\012',        '\013',        '\014',        '\015',        '\016',
    '\017',        '\020',        '\021',        '\022',        '\023',
    '\024',        '\025',        '\026',        '\027',        '\030',
    '\031',        '\032',        '\033',        '\034',        '\035',
    '\036',        '\037',        ' ',           '!',           '"',
    '#',           '$',           '%',           '&',           '\'',
    '(',           ')',           '*',           '+',           ',',
    '-',           '.',           '/',           '0',           '1',
    '2',           '3',           '4',           '5',           '6',
    '7',           '8',           '9',           ':',           ';',
    '<',           '=',           '>',           '?',           '@',
    'A',           'B',           'C',           'D',           'E',
    'F',           'G',           'H',           'I',           'J',
    'K',           'L',           'M',           'N',           'O',
    'P',           'Q',           'R',           'S',           'T',
    'U',           'V',           'W',           'X',           'Y',
    'Z',           '\\',          ']',           '[',           '^',
    '_',           'E',           'A',           'B',           'C',
    'D',           'E',           'F',           'G',           'H',
    'I',           'J',           'K',           'L',           'M',
    'N',           'O',           'P',           'Q',           'R',
    'S',           'T',           'U',           'V',           'W',
    'X',           'Y',           'Z',           '{',           '|',
    '}',           'Y',           '\177',        u'\200',       u'\201',
    u'\202',       u'\203',       u'\204',       u'\205',       u'\206',
    u'\207',       u'\210',       u'\211',       u'\212',       u'\213',
    u'\214',       u'\215',       u'\216',       u'\217',       u'\220',
    u'\221',       u'\222',       u'\223',       u'\224',       u'\225',
    u'\226',       u'\227',       u'\230',       u'\231',       u'\232',
    u'\233',       u'\234',       u'\235',       u'\236',       u'\237',
    u'\240',       u'\241',       u'\242',       u'\243',       u'\244',
    u'\245',       u'\246',       u'\247',       u'\250',       u'\251',
    u'\252',       u'\253',       u'\254',       u'\255',       u'\256',
    u'\257',       u'\260',       u'\261',       u'\262',       u'\263',
    u'\264',       u'\265',       u'\266',       u'\267',       u'\270',
    u'\271',       u'\272',       u'\273',       u'\274',       u'\275',
    u'\276',       u'\277',       u'\300',       u'\301',       u'\302',
    u'\303',       u'\304',       u'\305',       u'\306',       u'\307',
    u'\310',       u'\311',       u'\312',       u'\313',       u'\314',
    u'\315',       u'\316',       u'\317',       u'\320',       u'\321',
    u'\322',       u'\323',       u'\324',       u'\325',       u'\326',
    u'\327',       u'\330',       u'\331',       u'\332',       u'\333',
    u'\334',       u'\335',       u'\336',       u'\337',       u'\340',
    u'\341',       u'\342',       u'\343',       u'\344',       u'\345',
    u'\346',       u'\347',       u'\350',       u'\351',       u'\352',
    u'\353',       u'\354',       u'\355',       u'\356',       u'\357',
    u'\360',       u'\361',       u'\362',       u'\363',       u'\364',
    u'\365',       u'\366',       u'\367',       u'\370',       u'\371',
    u'\372',       u'\373',       u'\374',       u'\375',       u'\376',
    u'\377',
};
// clang-format on

static const MY_UNICASE_CHARACTER cA2[256] = {
    /* A200-A20F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* A210-A21F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* A220-A22F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* A230-A23F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* A240-A24F */
    {0xA240, 0xA240, 0xFFFD},
    {0xA241, 0xA241, 0xFF0F},
    {0xA242, 0xA242, 0xFF3C},
    {0xA243, 0xA243, 0xFF04},
    {0xA244, 0xA244, 0x00A5},
    {0xA245, 0xA245, 0x3012},
    {0xA246, 0xA246, 0x00A2},
    {0xA247, 0xA247, 0x00A3},
    {0xA248, 0xA248, 0xFF05},
    {0xA249, 0xA249, 0xFF20},
    {0xA24A, 0xA24A, 0x2103},
    {0xA24B, 0xA24B, 0x2109},
    {0xA24C, 0xA24C, 0xFE69},
    {0xA24D, 0xA24D, 0xFE6A},
    {0xA24E, 0xA24E, 0xFE6B},
    {0xA24F, 0xA24F, 0x33D5},
    /* A250-A25F */
    {0xA250, 0xA250, 0x339C},
    {0xA251, 0xA251, 0x339D},
    {0xA252, 0xA252, 0x339E},
    {0xA253, 0xA253, 0x33CE},
    {0xA254, 0xA254, 0x33A1},
    {0xA255, 0xA255, 0x338E},
    {0xA256, 0xA256, 0x338F},
    {0xA257, 0xA257, 0x33C4},
    {0xA258, 0xA258, 0x00B0},
    {0xA259, 0xA259, 0x5159},
    {0xA25A, 0xA25A, 0x515B},
    {0xA25B, 0xA25B, 0x515E},
    {0xA25C, 0xA25C, 0x515D},
    {0xA25D, 0xA25D, 0x5161},
    {0xA25E, 0xA25E, 0x5163},
    {0xA25F, 0xA25F, 0x55E7},
    /* A260-A26F */
    {0xA260, 0xA260, 0x74E9},
    {0xA261, 0xA261, 0x7CCE},
    {0xA262, 0xA262, 0x2581},
    {0xA263, 0xA263, 0x2582},
    {0xA264, 0xA264, 0x2583},
    {0xA265, 0xA265, 0x2584},
    {0xA266, 0xA266, 0x2585},
    {0xA267, 0xA267, 0x2586},
    {0xA268, 0xA268, 0x2587},
    {0xA269, 0xA269, 0x2588},
    {0xA26A, 0xA26A, 0x258F},
    {0xA26B, 0xA26B, 0x258E},
    {0xA26C, 0xA26C, 0x258D},
    {0xA26D, 0xA26D, 0x258C},
    {0xA26E, 0xA26E, 0x258B},
    {0xA26F, 0xA26F, 0x258A},
    /* A270-A27F */
    {0xA270, 0xA270, 0x2589},
    {0xA271, 0xA271, 0x253C},
    {0xA272, 0xA272, 0x2534},
    {0xA273, 0xA273, 0x252C},
    {0xA274, 0xA274, 0x2524},
    {0xA275, 0xA275, 0x251C},
    {0xA276, 0xA276, 0x2594},
    {0xA277, 0xA277, 0x2500},
    {0xA278, 0xA278, 0x2502},
    {0xA279, 0xA279, 0x2595},
    {0xA27A, 0xA27A, 0x250C},
    {0xA27B, 0xA27B, 0x2510},
    {0xA27C, 0xA27C, 0x2514},
    {0xA27D, 0xA27D, 0x2518},
    {0xA27E, 0xA27E, 0x256D},
    {0x0000, 0x0000, 0x0000},
    /* A280-A28F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* A290-A29F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* A2A0-A2AF */
    {0x0000, 0x0000, 0x0000},
    {0xA2A1, 0xA2A1, 0x256E},
    {0xA2A2, 0xA2A2, 0x2570},
    {0xA2A3, 0xA2A3, 0x256F},
    {0xA2A4, 0xA2A4, 0x2550},
    {0xA2A5, 0xA2A5, 0x255E},
    {0xA2A6, 0xA2A6, 0x256A},
    {0xA2A7, 0xA2A7, 0x2561},
    {0xA2A8, 0xA2A8, 0x25E2},
    {0xA2A9, 0xA2A9, 0x25E3},
    {0xA2AA, 0xA2AA, 0x25E5},
    {0xA2AB, 0xA2AB, 0x25E4},
    {0xA2AC, 0xA2AC, 0x2571},
    {0xA2AD, 0xA2AD, 0x2572},
    {0xA2AE, 0xA2AE, 0x2573},
    {0xA2AF, 0xA2AF, 0xFF10},
    /* A2B0-A2BF */
    {0xA2B0, 0xA2B0, 0xFF11},
    {0xA2B1, 0xA2B1, 0xFF12},
    {0xA2B2, 0xA2B2, 0xFF13},
    {0xA2B3, 0xA2B3, 0xFF14},
    {0xA2B4, 0xA2B4, 0xFF15},
    {0xA2B5, 0xA2B5, 0xFF16},
    {0xA2B6, 0xA2B6, 0xFF17},
    {0xA2B7, 0xA2B7, 0xFF18},
    {0xA2B8, 0xA2B8, 0xFF19},
    {0xA2B9, 0xA2B9, 0x2160},
    {0xA2BA, 0xA2BA, 0x2161},
    {0xA2BB, 0xA2BB, 0x2162},
    {0xA2BC, 0xA2BC, 0x2163},
    {0xA2BD, 0xA2BD, 0x2164},
    {0xA2BE, 0xA2BE, 0x2165},
    {0xA2BF, 0xA2BF, 0x2166},
    /* A2C0-A2CF */
    {0xA2C0, 0xA2C0, 0x2167},
    {0xA2C1, 0xA2C1, 0x2168},
    {0xA2C2, 0xA2C2, 0x2169},
    {0xA2C3, 0xA2C3, 0x3021},
    {0xA2C4, 0xA2C4, 0x3022},
    {0xA2C5, 0xA2C5, 0x3023},
    {0xA2C6, 0xA2C6, 0x3024},
    {0xA2C7, 0xA2C7, 0x3025},
    {0xA2C8, 0xA2C8, 0x3026},
    {0xA2C9, 0xA2C9, 0x3027},
    {0xA2CA, 0xA2CA, 0x3028},
    {0xA2CB, 0xA2CB, 0x3029},
    {0xA2CC, 0xA2CC, 0xFFFD},
    {0xA2CD, 0xA2CD, 0x5344},
    {0xA2CE, 0xA2CE, 0xFFFD},
    {0xA2CF, 0xA2E9, 0xFF21},
    /* A2D0-A2DF */
    {0xA2D0, 0xA2EA, 0xFF22},
    {0xA2D1, 0xA2EB, 0xFF23},
    {0xA2D2, 0xA2EC, 0xFF24},
    {0xA2D3, 0xA2ED, 0xFF25},
    {0xA2D4, 0xA2EE, 0xFF26},
    {0xA2D5, 0xA2EF, 0xFF27},
    {0xA2D6, 0xA2F0, 0xFF28},
    {0xA2D7, 0xA2F1, 0xFF29},
    {0xA2D8, 0xA2F2, 0xFF2A},
    {0xA2D9, 0xA2F3, 0xFF2B},
    {0xA2DA, 0xA2F4, 0xFF2C},
    {0xA2DB, 0xA2F5, 0xFF2D},
    {0xA2DC, 0xA2F6, 0xFF2E},
    {0xA2DD, 0xA2F7, 0xFF2F},
    {0xA2DE, 0xA2F8, 0xFF30},
    {0xA2DF, 0xA2F9, 0xFF31},
    /* A2E0-A2EF */
    {0xA2E0, 0xA2FA, 0xFF32},
    {0xA2E1, 0xA2FB, 0xFF33},
    {0xA2E2, 0xA2FC, 0xFF34},
    {0xA2E3, 0xA2FD, 0xFF35},
    {0xA2E4, 0xA2FE, 0xFF36},
    {0xA2E5, 0xA340, 0xFF37},
    {0xA2E6, 0xA341, 0xFF38},
    {0xA2E7, 0xA342, 0xFF39},
    {0xA2E8, 0xA343, 0xFF3A},
    {0xA2CF, 0xA2E9, 0xFF41},
    {0xA2D0, 0xA2EA, 0xFF42},
    {0xA2D1, 0xA2EB, 0xFF43},
    {0xA2D2, 0xA2EC, 0xFF44},
    {0xA2D3, 0xA2ED, 0xFF45},
    {0xA2D4, 0xA2EE, 0xFF46},
    {0xA2D5, 0xA2EF, 0xFF47},
    /* A2F0-A2FF */
    {0xA2D6, 0xA2F0, 0xFF48},
    {0xA2D7, 0xA2F1, 0xFF49},
    {0xA2D8, 0xA2F2, 0xFF4A},
    {0xA2D9, 0xA2F3, 0xFF4B},
    {0xA2DA, 0xA2F4, 0xFF4C},
    {0xA2DB, 0xA2F5, 0xFF4D},
    {0xA2DC, 0xA2F6, 0xFF4E},
    {0xA2DD, 0xA2F7, 0xFF4F},
    {0xA2DE, 0xA2F8, 0xFF50},
    {0xA2DF, 0xA2F9, 0xFF51},
    {0xA2E0, 0xA2FA, 0xFF52},
    {0xA2E1, 0xA2FB, 0xFF53},
    {0xA2E2, 0xA2FC, 0xFF54},
    {0xA2E3, 0xA2FD, 0xFF55},
    {0xA2E4, 0xA2FE, 0xFF56},
    {0x0000, 0x0000, 0x0000}};

static const MY_UNICASE_CHARACTER cA3[256] = {
    /* A300-A30F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* A310-A31F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* A320-A32F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* A330-A33F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* A340-A34F */
    {0xA2E5, 0xA340, 0xFF57},
    {0xA2E6, 0xA341, 0xFF58},
    {0xA2E7, 0xA342, 0xFF59},
    {0xA2E8, 0xA343, 0xFF5A},
    {0xA344, 0xA35C, 0x0391},
    {0xA345, 0xA35D, 0x0392},
    {0xA346, 0xA35E, 0x0393},
    {0xA347, 0xA35F, 0x0394},
    {0xA348, 0xA360, 0x0395},
    {0xA349, 0xA361, 0x0396},
    {0xA34A, 0xA362, 0x0397},
    {0xA34B, 0xA363, 0x0398},
    {0xA34C, 0xA364, 0x0399},
    {0xA34D, 0xA365, 0x039A},
    {0xA34E, 0xA366, 0x039B},
    {0xA34F, 0xA367, 0x039C},
    /* A350-A35F */
    {0xA350, 0xA368, 0x039D},
    {0xA351, 0xA369, 0x039E},
    {0xA352, 0xA36A, 0x039F},
    {0xA353, 0xA36B, 0x03A0},
    {0xA354, 0xA36C, 0x03A1},
    {0xA355, 0xA36D, 0x03A3},
    {0xA356, 0xA36E, 0x03A4},
    {0xA357, 0xA36F, 0x03A5},
    {0xA358, 0xA370, 0x03A6},
    {0xA359, 0xA371, 0x03A7},
    {0xA35A, 0xA372, 0x03A8},
    {0xA35B, 0xA373, 0x03A9},
    {0xA344, 0xA35C, 0x03B1},
    {0xA345, 0xA35D, 0x03B2},
    {0xA346, 0xA35E, 0x03B3},
    {0xA347, 0xA35F, 0x03B4},
    /* A360-A36F */
    {0xA348, 0xA360, 0x03B5},
    {0xA349, 0xA361, 0x03B6},
    {0xA34A, 0xA362, 0x03B7},
    {0xA34B, 0xA363, 0x03B8},
    {0xA34C, 0xA364, 0x03B9},
    {0xA34D, 0xA365, 0x03BA},
    {0xA34E, 0xA366, 0x03BB},
    {0xA34F, 0xA367, 0x03BC},
    {0xA350, 0xA368, 0x03BD},
    {0xA351, 0xA369, 0x03BE},
    {0xA352, 0xA36A, 0x03BF},
    {0xA353, 0xA36B, 0x03C0},
    {0xA354, 0xA36C, 0x03C1},
    {0xA355, 0xA36D, 0x03C3},
    {0xA356, 0xA36E, 0x03C4},
    {0xA357, 0xA36F, 0x03C5},
    /* A370-A37F */
    {0xA358, 0xA370, 0x03C6},
    {0xA359, 0xA371, 0x03C7},
    {0xA35A, 0xA372, 0x03C8},
    {0xA35B, 0xA373, 0x03C9},
    {0xA374, 0xA374, 0x3105},
    {0xA375, 0xA375, 0x3106},
    {0xA376, 0xA376, 0x3107},
    {0xA377, 0xA377, 0x3108},
    {0xA378, 0xA378, 0x3109},
    {0xA379, 0xA379, 0x310A},
    {0xA37A, 0xA37A, 0x310B},
    {0xA37B, 0xA37B, 0x310C},
    {0xA37C, 0xA37C, 0x310D},
    {0xA37D, 0xA37D, 0x310E},
    {0xA37E, 0xA37E, 0x310F},
    {0x0000, 0x0000, 0x0000},
    /* A380-A38F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* A390-A39F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* A3A0-A3AF */
    {0x0000, 0x0000, 0x0000},
    {0xA3A1, 0xA3A1, 0x3110},
    {0xA3A2, 0xA3A2, 0x3111},
    {0xA3A3, 0xA3A3, 0x3112},
    {0xA3A4, 0xA3A4, 0x3113},
    {0xA3A5, 0xA3A5, 0x3114},
    {0xA3A6, 0xA3A6, 0x3115},
    {0xA3A7, 0xA3A7, 0x3116},
    {0xA3A8, 0xA3A8, 0x3117},
    {0xA3A9, 0xA3A9, 0x3118},
    {0xA3AA, 0xA3AA, 0x3119},
    {0xA3AB, 0xA3AB, 0x311A},
    {0xA3AC, 0xA3AC, 0x311B},
    {0xA3AD, 0xA3AD, 0x311C},
    {0xA3AE, 0xA3AE, 0x311D},
    {0xA3AF, 0xA3AF, 0x311E},
    /* A3B0-A3BF */
    {0xA3B0, 0xA3B0, 0x311F},
    {0xA3B1, 0xA3B1, 0x3120},
    {0xA3B2, 0xA3B2, 0x3121},
    {0xA3B3, 0xA3B3, 0x3122},
    {0xA3B4, 0xA3B4, 0x3123},
    {0xA3B5, 0xA3B5, 0x3124},
    {0xA3B6, 0xA3B6, 0x3125},
    {0xA3B7, 0xA3B7, 0x3126},
    {0xA3B8, 0xA3B8, 0x3127},
    {0xA3B9, 0xA3B9, 0x3128},
    {0xA3BA, 0xA3BA, 0x3129},
    {0xA3BB, 0xA3BB, 0x02D9},
    {0xA3BC, 0xA3BC, 0x02C9},
    {0xA3BD, 0xA3BD, 0x02CA},
    {0xA3BE, 0xA3BE, 0x02C7},
    {0xA3BF, 0xA3BF, 0x02CB},
    /* A3C0-A3CF */
    {0xA3C0, 0xA3C0, 0x003F},
    {0xA3C1, 0xA3C1, 0x003F},
    {0xA3C2, 0xA3C2, 0x003F},
    {0xA3C3, 0xA3C3, 0x003F},
    {0xA3C4, 0xA3C4, 0x003F},
    {0xA3C5, 0xA3C5, 0x003F},
    {0xA3C6, 0xA3C6, 0x003F},
    {0xA3C7, 0xA3C7, 0x003F},
    {0xA3C8, 0xA3C8, 0x003F},
    {0xA3C9, 0xA3C9, 0x003F},
    {0xA3CA, 0xA3CA, 0x003F},
    {0xA3CB, 0xA3CB, 0x003F},
    {0xA3CC, 0xA3CC, 0x003F},
    {0xA3CD, 0xA3CD, 0x003F},
    {0xA3CE, 0xA3CE, 0x003F},
    {0xA3CF, 0xA3CF, 0x003F},
    /* A3D0-A3DF */
    {0xA3D0, 0xA3D0, 0x003F},
    {0xA3D1, 0xA3D1, 0x003F},
    {0xA3D2, 0xA3D2, 0x003F},
    {0xA3D3, 0xA3D3, 0x003F},
    {0xA3D4, 0xA3D4, 0x003F},
    {0xA3D5, 0xA3D5, 0x003F},
    {0xA3D6, 0xA3D6, 0x003F},
    {0xA3D7, 0xA3D7, 0x003F},
    {0xA3D8, 0xA3D8, 0x003F},
    {0xA3D9, 0xA3D9, 0x003F},
    {0xA3DA, 0xA3DA, 0x003F},
    {0xA3DB, 0xA3DB, 0x003F},
    {0xA3DC, 0xA3DC, 0x003F},
    {0xA3DD, 0xA3DD, 0x003F},
    {0xA3DE, 0xA3DE, 0x003F},
    {0xA3DF, 0xA3DF, 0x003F},
    /* A3E0-A3EF */
    {0xA3E0, 0xA3E0, 0x003F},
    {0xA3E1, 0xA3E1, 0x003F},
    {0xA3E2, 0xA3E2, 0x003F},
    {0xA3E3, 0xA3E3, 0x003F},
    {0xA3E4, 0xA3E4, 0x003F},
    {0xA3E5, 0xA3E5, 0x003F},
    {0xA3E6, 0xA3E6, 0x003F},
    {0xA3E7, 0xA3E7, 0x003F},
    {0xA3E8, 0xA3E8, 0x003F},
    {0xA3E9, 0xA3E9, 0x003F},
    {0xA3EA, 0xA3EA, 0x003F},
    {0xA3EB, 0xA3EB, 0x003F},
    {0xA3EC, 0xA3EC, 0x003F},
    {0xA3ED, 0xA3ED, 0x003F},
    {0xA3EE, 0xA3EE, 0x003F},
    {0xA3EF, 0xA3EF, 0x003F},
    /* A3F0-A3FF */
    {0xA3F0, 0xA3F0, 0x003F},
    {0xA3F1, 0xA3F1, 0x003F},
    {0xA3F2, 0xA3F2, 0x003F},
    {0xA3F3, 0xA3F3, 0x003F},
    {0xA3F4, 0xA3F4, 0x003F},
    {0xA3F5, 0xA3F5, 0x003F},
    {0xA3F6, 0xA3F6, 0x003F},
    {0xA3F7, 0xA3F7, 0x003F},
    {0xA3F8, 0xA3F8, 0x003F},
    {0xA3F9, 0xA3F9, 0x003F},
    {0xA3FA, 0xA3FA, 0x003F},
    {0xA3FB, 0xA3FB, 0x003F},
    {0xA3FC, 0xA3FC, 0x003F},
    {0xA3FD, 0xA3FD, 0x003F},
    {0xA3FE, 0xA3FE, 0x003F},
    {0x0000, 0x0000, 0x0000}};

static const MY_UNICASE_CHARACTER cC7[256] = {
    /* C700-C70F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* C710-C71F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* C720-C72F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* C730-C73F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* C740-C74F */
    {0xC740, 0xC740, 0x30A8},
    {0xC741, 0xC741, 0x30A9},
    {0xC742, 0xC742, 0x30AA},
    {0xC743, 0xC743, 0x30AB},
    {0xC744, 0xC744, 0x30AC},
    {0xC745, 0xC745, 0x30AD},
    {0xC746, 0xC746, 0x30AE},
    {0xC747, 0xC747, 0x30AF},
    {0xC748, 0xC748, 0x30B0},
    {0xC749, 0xC749, 0x30B1},
    {0xC74A, 0xC74A, 0x30B2},
    {0xC74B, 0xC74B, 0x30B3},
    {0xC74C, 0xC74C, 0x30B4},
    {0xC74D, 0xC74D, 0x30B5},
    {0xC74E, 0xC74E, 0x30B6},
    {0xC74F, 0xC74F, 0x30B7},
    /* C750-C75F */
    {0xC750, 0xC750, 0x30B8},
    {0xC751, 0xC751, 0x30B9},
    {0xC752, 0xC752, 0x30BA},
    {0xC753, 0xC753, 0x30BB},
    {0xC754, 0xC754, 0x30BC},
    {0xC755, 0xC755, 0x30BD},
    {0xC756, 0xC756, 0x30BE},
    {0xC757, 0xC757, 0x30BF},
    {0xC758, 0xC758, 0x30C0},
    {0xC759, 0xC759, 0x30C1},
    {0xC75A, 0xC75A, 0x30C2},
    {0xC75B, 0xC75B, 0x30C3},
    {0xC75C, 0xC75C, 0x30C4},
    {0xC75D, 0xC75D, 0x30C5},
    {0xC75E, 0xC75E, 0x30C6},
    {0xC75F, 0xC75F, 0x30C7},
    /* C760-C76F */
    {0xC760, 0xC760, 0x30C8},
    {0xC761, 0xC761, 0x30C9},
    {0xC762, 0xC762, 0x30CA},
    {0xC763, 0xC763, 0x30CB},
    {0xC764, 0xC764, 0x30CC},
    {0xC765, 0xC765, 0x30CD},
    {0xC766, 0xC766, 0x30CE},
    {0xC767, 0xC767, 0x30CF},
    {0xC768, 0xC768, 0x30D0},
    {0xC769, 0xC769, 0x30D1},
    {0xC76A, 0xC76A, 0x30D2},
    {0xC76B, 0xC76B, 0x30D3},
    {0xC76C, 0xC76C, 0x30D4},
    {0xC76D, 0xC76D, 0x30D5},
    {0xC76E, 0xC76E, 0x30D6},
    {0xC76F, 0xC76F, 0x30D7},
    /* C770-C77F */
    {0xC770, 0xC770, 0x30D8},
    {0xC771, 0xC771, 0x30D9},
    {0xC772, 0xC772, 0x30DA},
    {0xC773, 0xC773, 0x30DB},
    {0xC774, 0xC774, 0x30DC},
    {0xC775, 0xC775, 0x30DD},
    {0xC776, 0xC776, 0x30DE},
    {0xC777, 0xC777, 0x30DF},
    {0xC778, 0xC778, 0x30E0},
    {0xC779, 0xC779, 0x30E1},
    {0xC77A, 0xC77A, 0x30E2},
    {0xC77B, 0xC77B, 0x30E3},
    {0xC77C, 0xC77C, 0x30E4},
    {0xC77D, 0xC77D, 0x30E5},
    {0xC77E, 0xC77E, 0x30E6},
    {0x0000, 0x0000, 0x0000},
    /* C780-C78F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* C790-C79F */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    /* C7A0-C7AF */
    {0x0000, 0x0000, 0x0000},
    {0xC7A1, 0xC7A1, 0x30E7},
    {0xC7A2, 0xC7A2, 0x30E8},
    {0xC7A3, 0xC7A3, 0x30E9},
    {0xC7A4, 0xC7A4, 0x30EA},
    {0xC7A5, 0xC7A5, 0x30EB},
    {0xC7A6, 0xC7A6, 0x30EC},
    {0xC7A7, 0xC7A7, 0x30ED},
    {0xC7A8, 0xC7A8, 0x30EE},
    {0xC7A9, 0xC7A9, 0x30EF},
    {0xC7AA, 0xC7AA, 0x30F0},
    {0xC7AB, 0xC7AB, 0x30F1},
    {0xC7AC, 0xC7AC, 0x30F2},
    {0xC7AD, 0xC7AD, 0x30F3},
    {0xC7AE, 0xC7AE, 0x30F4},
    {0xC7AF, 0xC7AF, 0x30F5},
    /* C7B0-C7BF */
    {0xC7B0, 0xC7B0, 0x30F6},
    {0xC7B1, 0xC7CC, 0x0414},
    {0xC7B2, 0xC7CD, 0x0415},
    {0xC7B3, 0xC7CE, 0x0401},
    {0xC7B4, 0xC7CF, 0x0416},
    {0xC7B5, 0xC7D0, 0x0417},
    {0xC7B6, 0xC7D1, 0x0418},
    {0xC7B7, 0xC7D2, 0x0419},
    {0xC7B8, 0xC7D3, 0x041A},
    {0xC7B9, 0xC7D4, 0x041B},
    {0xC7BA, 0xC7D5, 0x041C},
    {0xC7BB, 0xC7DC, 0x0423},
    {0xC7BC, 0xC7DD, 0x0424},
    {0xC7BD, 0xC7DE, 0x0425},
    {0xC7BE, 0xC7DF, 0x0426},
    {0xC7BF, 0xC7E0, 0x0427},
    /* C7C0-C7CF */
    {0xC7C0, 0xC7E1, 0x0428},
    {0xC7C1, 0xC7E2, 0x0429},
    {0xC7C2, 0xC7E3, 0x042A},
    {0xC7C3, 0xC7E4, 0x042B},
    {0xC7C4, 0xC7E5, 0x042C},
    {0xC7C5, 0xC7E6, 0x042D},
    {0xC7C6, 0xC7E7, 0x042E},
    {0xC7C7, 0xC7E8, 0x042F},
    {0xC7C8, 0xC7C8, 0x0430},
    {0xC7C9, 0xC7C9, 0x0431},
    {0xC7CA, 0xC7CA, 0x0432},
    {0xC7CB, 0xC7CB, 0x0433},
    {0xC7B1, 0xC7CC, 0x0434},
    {0xC7B2, 0xC7CD, 0x0435},
    {0xC7B3, 0xC7CE, 0x0451},
    {0xC7B4, 0xC7CF, 0x0436},
    /* C7D0-C7DF */
    {0xC7B5, 0xC7D0, 0x0437},
    {0xC7B6, 0xC7D1, 0x0438},
    {0xC7B7, 0xC7D2, 0x0439},
    {0xC7B8, 0xC7D3, 0x043A},
    {0xC7B9, 0xC7D4, 0x043B},
    {0xC7BA, 0xC7D5, 0x043C},
    {0xC7D6, 0xC7D6, 0x043D},
    {0xC7D7, 0xC7D7, 0x043E},
    {0xC7D8, 0xC7D8, 0x043F},
    {0xC7D9, 0xC7D9, 0x0440},
    {0xC7DA, 0xC7DA, 0x0441},
    {0xC7DB, 0xC7DB, 0x0442},
    {0xC7BB, 0xC7DC, 0x0443},
    {0xC7BC, 0xC7DD, 0x0444},
    {0xC7BD, 0xC7DE, 0x0445},
    {0xC7BE, 0xC7DF, 0x0446},
    /* C7E0-C7EF */
    {0xC7BF, 0xC7E0, 0x0447},
    {0xC7C0, 0xC7E1, 0x0448},
    {0xC7C1, 0xC7E2, 0x0449},
    {0xC7C2, 0xC7E3, 0x044A},
    {0xC7C3, 0xC7E4, 0x044B},
    {0xC7C4, 0xC7E5, 0x044C},
    {0xC7C5, 0xC7E6, 0x044D},
    {0xC7C6, 0xC7E7, 0x044E},
    {0xC7C7, 0xC7E8, 0x044F},
    {0xC7E9, 0xC7E9, 0x2460},
    {0xC7EA, 0xC7EA, 0x2461},
    {0xC7EB, 0xC7EB, 0x2462},
    {0xC7EC, 0xC7EC, 0x2463},
    {0xC7ED, 0xC7ED, 0x2464},
    {0xC7EE, 0xC7EE, 0x2465},
    {0xC7EF, 0xC7EF, 0x2466},
    /* C7F0-C7FF */
    {0xC7F0, 0xC7F0, 0x2467},
    {0xC7F1, 0xC7F1, 0x2468},
    {0xC7F2, 0xC7F2, 0x2469},
    {0xC7F3, 0xC7F3, 0x2474},
    {0xC7F4, 0xC7F4, 0x2475},
    {0xC7F5, 0xC7F5, 0x2476},
    {0xC7F6, 0xC7F6, 0x2477},
    {0xC7F7, 0xC7F7, 0x2478},
    {0xC7F8, 0xC7F8, 0x2479},
    {0xC7F9, 0xC7F9, 0x247A},
    {0xC7FA, 0xC7FA, 0x247B},
    {0xC7FB, 0xC7FB, 0x247C},
    {0xC7FC, 0xC7FC, 0x247D},
    {0xC7FD, 0xC7FD, 0x003F},
    {0xC7FE, 0xC7FE, 0x003F},
    {0x0000, 0x0000, 0x0000}};

static const MY_UNICASE_CHARACTER *my_caseinfo_pages_big5[256] = {
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, /* 0 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 1 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 2 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 3 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 4 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 5 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 6 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 7 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 8 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 9 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, cA2,     cA3,
    nullptr, nullptr, nullptr, nullptr, /* A */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* B */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, cC7, /* C */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* D */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* E */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* F */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr,
};

static MY_UNICASE_INFO my_caseinfo_big5 = {0xFFFF, my_caseinfo_pages_big5};

static uint16_t big5strokexfrm(uint16_t i) {
  if ((i == 0xA440) || (i == 0xA441)) return 0xA440;
  if (((i >= 0xA442) && (i <= 0xA453)) || ((i >= 0xC940) && (i <= 0xC944)))
    return 0xA442;
  if (((i >= 0xA454) && (i <= 0xA47E)) || ((i >= 0xC945) && (i <= 0xC94C)))
    return 0xA454;
  if (((i >= 0xA4A1) && (i <= 0xA4FD)) || ((i >= 0xC94D) && (i <= 0xC962)))
    return 0xA4A1;
  if (((i >= 0xA4FE) && (i <= 0xA5DF)) || ((i >= 0xC963) && (i <= 0xC9AA)))
    return 0xA4FE;
  if (((i >= 0xA5E0) && (i <= 0xA6E9)) || ((i >= 0xC9AB) && (i <= 0xCA59)))
    return 0xA5E0;
  if (((i >= 0xA6EA) && (i <= 0xA8C2)) || ((i >= 0xCA5A) && (i <= 0xCBB0)))
    return 0xA6EA;
  if ((i == 0xA260) || ((i >= 0xA8C3) && (i <= 0xAB44)) ||
      ((i >= 0xCBB1) && (i <= 0xCDDC)))
    return 0xA8C3;
  if ((i == 0xA259) || (i == 0xF9DA) || ((i >= 0xAB45) && (i <= 0xADBB)) ||
      ((i >= 0xCDDD) && (i <= 0xD0C7)))
    return 0xAB45;
  if ((i == 0xA25A) || ((i >= 0xADBC) && (i <= 0xB0AD)) ||
      ((i >= 0xD0C8) && (i <= 0xD44A)))
    return 0xADBC;
  if ((i == 0xA25B) || (i == 0xA25C) || ((i >= 0xB0AE) && (i <= 0xB3C2)) ||
      ((i >= 0xD44B) && (i <= 0xD850)))
    return 0xB0AE;
  if ((i == 0xF9DB) || ((i >= 0xB3C3) && (i <= 0xB6C2)) ||
      ((i >= 0xD851) && (i <= 0xDCB0)))
    return 0xB3C3;
  if ((i == 0xA25D) || (i == 0xA25F) || (i == 0xC6A1) || (i == 0xF9D6) ||
      (i == 0xF9D8) || ((i >= 0xB6C3) && (i <= 0xB9AB)) ||
      ((i >= 0xDCB1) && (i <= 0xE0EF)))
    return 0xB6C3;
  if ((i == 0xF9DC) || ((i >= 0xB9AC) && (i <= 0xBBF4)) ||
      ((i >= 0xE0F0) && (i <= 0xE4E5)))
    return 0xB9AC;
  if ((i == 0xA261) || ((i >= 0xBBF5) && (i <= 0xBEA6)) ||
      ((i >= 0xE4E6) && (i <= 0xE8F3)))
    return 0xBBF5;
  if ((i == 0xA25E) || (i == 0xF9D7) || (i == 0xF9D9) ||
      ((i >= 0xBEA7) && (i <= 0xC074)) || ((i >= 0xE8F4) && (i <= 0xECB8)))
    return 0xBEA7;
  if (((i >= 0xC075) && (i <= 0xC24E)) || ((i >= 0xECB9) && (i <= 0xEFB6)))
    return 0xC075;
  if (((i >= 0xC24F) && (i <= 0xC35E)) || ((i >= 0xEFB7) && (i <= 0xF1EA)))
    return 0xC24F;
  if (((i >= 0xC35F) && (i <= 0xC454)) || ((i >= 0xF1EB) && (i <= 0xF3FC)))
    return 0xC35F;
  if (((i >= 0xC455) && (i <= 0xC4D6)) || ((i >= 0xF3FD) && (i <= 0xF5BF)))
    return 0xC455;
  if (((i >= 0xC4D7) && (i <= 0xC56A)) || ((i >= 0xF5C0) && (i <= 0xF6D5)))
    return 0xC4D7;
  if (((i >= 0xC56B) && (i <= 0xC5C7)) || ((i >= 0xF6D6) && (i <= 0xF7CF)))
    return 0xC56B;
  if (((i >= 0xC5C8) && (i <= 0xC5F0)) || ((i >= 0xF7D0) && (i <= 0xF8A4)))
    return 0xC5C8;
  if (((i >= 0xC5F1) && (i <= 0xC654)) || ((i >= 0xF8A5) && (i <= 0xF8ED)))
    return 0xC5F1;
  if (((i >= 0xC655) && (i <= 0xC664)) || ((i >= 0xF8EE) && (i <= 0xF96A)))
    return 0xC655;
  if (((i >= 0xC665) && (i <= 0xC66B)) || ((i >= 0xF96B) && (i <= 0xF9A1)))
    return 0xC665;
  if (((i >= 0xC66C) && (i <= 0xC675)) || ((i >= 0xF9A2) && (i <= 0xF9B9)))
    return 0xC66C;
  if (((i >= 0xC676) && (i <= 0xC678)) || ((i >= 0xF9BA) && (i <= 0xF9C5)))
    return 0xC676;
  if (((i >= 0xC679) && (i <= 0xC67C)) || ((i >= 0xF9C7) && (i <= 0xF9CB)))
    return 0xC679;
  if ((i == 0xC67D) || ((i >= 0xF9CC) && (i <= 0xF9CF))) return 0xC67D;
  if (i == 0xF9D0) return 0xF9D0;
  if ((i == 0xC67E) || (i == 0xF9D1)) return 0xC67E;
  if ((i == 0xF9C6) || (i == 0xF9D2)) return 0xF9C6;
  if (i == 0xF9D3) return 0xF9D3;
  if (i == 0xF9D4) return 0xF9D4;
  if (i == 0xF9D5) return 0xF9D5;
  return 0xA140;
}

static int my_strnncoll_big5_internal(const uint8_t **a_res,
                                      const uint8_t **b_res, size_t length) {
  const uint8_t *a = *a_res;
  const uint8_t *b = *b_res;

  while (length--) {
    if ((length > 0) && isbig5code(*a, *(a + 1)) && isbig5code(*b, *(b + 1))) {
      if (*a != *b || *(a + 1) != *(b + 1))
        return ((int)big5code(*a, *(a + 1)) - (int)big5code(*b, *(b + 1)));
      a += 2;
      b += 2;
      length--;
    } else if (sort_order_big5[*a++] != sort_order_big5[*b++])
      return ((int)sort_order_big5[a[-1]] - (int)sort_order_big5[b[-1]]);
  }
  *a_res = a;
  *b_res = b;
  return 0;
}

/* Compare strings */

extern "C" {
static int my_strnncoll_big5(const CHARSET_INFO *cs [[maybe_unused]],
                             const uint8_t *a, size_t a_length,
                             const uint8_t *b, size_t b_length,
                             bool b_is_prefix) {
  size_t const length = std::min(a_length, b_length);
  int const res = my_strnncoll_big5_internal(&a, &b, length);
  return res ? res : (int)((b_is_prefix ? length : a_length) - b_length);
}

/* compare strings, ignore end space */

static int my_strnncollsp_big5(const CHARSET_INFO *cs [[maybe_unused]],
                               const uint8_t *a, size_t a_length,
                               const uint8_t *b, size_t b_length) {
  size_t const length = std::min(a_length, b_length);
  int res = my_strnncoll_big5_internal(&a, &b, length);

  if (!res && a_length != b_length) {
    int swap = 1;
    /*
      Check the next not space character of the longer key. If it's < ' ',
      then it's smaller than the other key.
    */
    if (a_length < b_length) {
      /* put longer key in a */
      a_length = b_length;
      a = b;
      swap = -1; /* swap sign of result */
      res = -res;
    }
    for (const uint8_t *end = a + a_length - length; a < end; a++) {
      if (*a != ' ') return (*a < ' ') ? -swap : swap;
    }
  }
  return res;
}

static size_t my_strnxfrm_big5(const CHARSET_INFO *cs, uint8_t *dst,
                               size_t dstlen, unsigned nweights,
                               const uint8_t *src, size_t srclen,
                               unsigned flags) {
  uint8_t *d0 = dst;
  uint8_t *de = dst + dstlen;
  const uint8_t *se = src + srclen;
  const uint8_t *sort_order = cs->sort_order;

  for (; dst < de && src < se && nweights; nweights--) {
    if (cs->cset->ismbchar(cs, (const char *)src, (const char *)se)) {
      /*
        Note, it is safe not to check (src < se)
        in the code below, because ismbchar() would
        not return TRUE if src was too short
      */
      uint16_t const e = big5strokexfrm((uint16_t)big5code(*src, *(src + 1)));
      *dst++ = big5head(e);
      if (dst < de) *dst++ = big5tail(e);
      src += 2;
    } else
      *dst++ = sort_order ? sort_order[*src++] : *src++;
  }
  return my_strxfrm_pad(cs, d0, dst, de, nweights, flags);
}

static unsigned ismbchar_big5(const CHARSET_INFO *cs [[maybe_unused]],
                              const char *p, const char *e) {
  return (isbig5head(*(p)) && (e) - (p) > 1 && isbig5tail(*((p) + 1)) ? 2 : 0);
}

static unsigned mbcharlen_big5(const CHARSET_INFO *cs [[maybe_unused]],
                               unsigned c) {
  return (isbig5head(c) ? 2 : 1);
}
}  // extern "C"

/* page 0 0xA140-0xC7FC */
static const uint16_t tab_big5_uni0[] = {
    0x3000, 0xFF0C, 0x3001, 0x3002, 0xFF0E, 0x2022, 0xFF1B, 0xFF1A, 0xFF1F,
    0xFF01, 0xFE30, 0x2026, 0x2025, 0xFE50, 0xFF64, 0xFE52, 0x00B7, 0xFE54,
    0xFE55, 0xFE56, 0xFE57, 0xFF5C, 0x2013, 0xFE31, 0x2014, 0xFE33, 0xFFFD,
    0xFE34, 0xFE4F, 0xFF08, 0xFF09, 0xFE35, 0xFE36, 0xFF5B, 0xFF5D, 0xFE37,
    0xFE38, 0x3014, 0x3015, 0xFE39, 0xFE3A, 0x3010, 0x3011, 0xFE3B, 0xFE3C,
    0x300A, 0x300B, 0xFE3D, 0xFE3E, 0x3008, 0x3009, 0xFE3F, 0xFE40, 0x300C,
    0x300D, 0xFE41, 0xFE42, 0x300E, 0x300F, 0xFE43, 0xFE44, 0xFE59, 0xFE5A,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0xFE5B, 0xFE5C,
    0xFE5D, 0xFE5E, 0x2018, 0x2019, 0x201C, 0x201D, 0x301D, 0x301E, 0x2035,
    0x2032, 0xFF03, 0xFF06, 0xFF0A, 0x203B, 0x00A7, 0x3003, 0x25CB, 0x25CF,
    0x25B3, 0x25B2, 0x25CE, 0x2606, 0x2605, 0x25C7, 0x25C6, 0x25A1, 0x25A0,
    0x25BD, 0x25BC, 0x32A3, 0x2105, 0x203E, 0xFFFD, 0xFF3F, 0xFFFD, 0xFE49,
    0xFE4A, 0xFE4D, 0xFE4E, 0xFE4B, 0xFE4C, 0xFE5F, 0xFE60, 0xFE61, 0xFF0B,
    0xFF0D, 0x00D7, 0x00F7, 0x00B1, 0x221A, 0xFF1C, 0xFF1E, 0xFF1D, 0x2266,
    0x2267, 0x2260, 0x221E, 0x2252, 0x2261, 0xFE62, 0xFE63, 0xFE64, 0xFE65,
    0xFE66, 0x223C, 0x2229, 0x222A, 0x22A5, 0x2220, 0x221F, 0x22BF, 0x33D2,
    0x33D1, 0x222B, 0x222E, 0x2235, 0x2234, 0x2640, 0x2642, 0x2641, 0x2609,
    0x2191, 0x2193, 0x2190, 0x2192, 0x2196, 0x2197, 0x2199, 0x2198, 0x2225,
    0x2223, 0xFFFD, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0xFFFD, 0xFF0F, 0xFF3C, 0xFF04, 0x00A5,
    0x3012, 0x00A2, 0x00A3, 0xFF05, 0xFF20, 0x2103, 0x2109, 0xFE69, 0xFE6A,
    0xFE6B, 0x33D5, 0x339C, 0x339D, 0x339E, 0x33CE, 0x33A1, 0x338E, 0x338F,
    0x33C4, 0x00B0, 0x5159, 0x515B, 0x515E, 0x515D, 0x5161, 0x5163, 0x55E7,
    0x74E9, 0x7CCE, 0x2581, 0x2582, 0x2583, 0x2584, 0x2585, 0x2586, 0x2587,
    0x2588, 0x258F, 0x258E, 0x258D, 0x258C, 0x258B, 0x258A, 0x2589, 0x253C,
    0x2534, 0x252C, 0x2524, 0x251C, 0x2594, 0x2500, 0x2502, 0x2595, 0x250C,
    0x2510, 0x2514, 0x2518, 0x256D, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x256E, 0x2570, 0x256F, 0x2550, 0x255E, 0x256A, 0x2561,
    0x25E2, 0x25E3, 0x25E5, 0x25E4, 0x2571, 0x2572, 0x2573, 0xFF10, 0xFF11,
    0xFF12, 0xFF13, 0xFF14, 0xFF15, 0xFF16, 0xFF17, 0xFF18, 0xFF19, 0x2160,
    0x2161, 0x2162, 0x2163, 0x2164, 0x2165, 0x2166, 0x2167, 0x2168, 0x2169,
    0x3021, 0x3022, 0x3023, 0x3024, 0x3025, 0x3026, 0x3027, 0x3028, 0x3029,
    0xFFFD, 0x5344, 0xFFFD, 0xFF21, 0xFF22, 0xFF23, 0xFF24, 0xFF25, 0xFF26,
    0xFF27, 0xFF28, 0xFF29, 0xFF2A, 0xFF2B, 0xFF2C, 0xFF2D, 0xFF2E, 0xFF2F,
    0xFF30, 0xFF31, 0xFF32, 0xFF33, 0xFF34, 0xFF35, 0xFF36, 0xFF37, 0xFF38,
    0xFF39, 0xFF3A, 0xFF41, 0xFF42, 0xFF43, 0xFF44, 0xFF45, 0xFF46, 0xFF47,
    0xFF48, 0xFF49, 0xFF4A, 0xFF4B, 0xFF4C, 0xFF4D, 0xFF4E, 0xFF4F, 0xFF50,
    0xFF51, 0xFF52, 0xFF53, 0xFF54, 0xFF55, 0xFF56, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0xFF57,
    0xFF58, 0xFF59, 0xFF5A, 0x0391, 0x0392, 0x0393, 0x0394, 0x0395, 0x0396,
    0x0397, 0x0398, 0x0399, 0x039A, 0x039B, 0x039C, 0x039D, 0x039E, 0x039F,
    0x03A0, 0x03A1, 0x03A3, 0x03A4, 0x03A5, 0x03A6, 0x03A7, 0x03A8, 0x03A9,
    0x03B1, 0x03B2, 0x03B3, 0x03B4, 0x03B5, 0x03B6, 0x03B7, 0x03B8, 0x03B9,
    0x03BA, 0x03BB, 0x03BC, 0x03BD, 0x03BE, 0x03BF, 0x03C0, 0x03C1, 0x03C3,
    0x03C4, 0x03C5, 0x03C6, 0x03C7, 0x03C8, 0x03C9, 0x3105, 0x3106, 0x3107,
    0x3108, 0x3109, 0x310A, 0x310B, 0x310C, 0x310D, 0x310E, 0x310F, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x3110, 0x3111, 0x3112,
    0x3113, 0x3114, 0x3115, 0x3116, 0x3117, 0x3118, 0x3119, 0x311A, 0x311B,
    0x311C, 0x311D, 0x311E, 0x311F, 0x3120, 0x3121, 0x3122, 0x3123, 0x3124,
    0x3125, 0x3126, 0x3127, 0x3128, 0x3129, 0x02D9, 0x02C9, 0x02CA, 0x02C7,
    0x02CB, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x4E00, 0x4E59, 0x4E01, 0x4E03, 0x4E43, 0x4E5D,
    0x4E86, 0x4E8C, 0x4EBA, 0x513F, 0x5165, 0x516B, 0x51E0, 0x5200, 0x5201,
    0x529B, 0x5315, 0x5341, 0x535C, 0x53C8, 0x4E09, 0x4E0B, 0x4E08, 0x4E0A,
    0x4E2B, 0x4E38, 0x51E1, 0x4E45, 0x4E48, 0x4E5F, 0x4E5E, 0x4E8E, 0x4EA1,
    0x5140, 0x5203, 0x52FA, 0x5343, 0x53C9, 0x53E3, 0x571F, 0x58EB, 0x5915,
    0x5927, 0x5973, 0x5B50, 0x5B51, 0x5B53, 0x5BF8, 0x5C0F, 0x5C22, 0x5C38,
    0x5C71, 0x5DDD, 0x5DE5, 0x5DF1, 0x5DF2, 0x5DF3, 0x5DFE, 0x5E72, 0x5EFE,
    0x5F0B, 0x5F13, 0x624D, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x4E11, 0x4E10, 0x4E0D, 0x4E2D, 0x4E30, 0x4E39, 0x4E4B, 0x5C39,
    0x4E88, 0x4E91, 0x4E95, 0x4E92, 0x4E94, 0x4EA2, 0x4EC1, 0x4EC0, 0x4EC3,
    0x4EC6, 0x4EC7, 0x4ECD, 0x4ECA, 0x4ECB, 0x4EC4, 0x5143, 0x5141, 0x5167,
    0x516D, 0x516E, 0x516C, 0x5197, 0x51F6, 0x5206, 0x5207, 0x5208, 0x52FB,
    0x52FE, 0x52FF, 0x5316, 0x5339, 0x5348, 0x5347, 0x5345, 0x535E, 0x5384,
    0x53CB, 0x53CA, 0x53CD, 0x58EC, 0x5929, 0x592B, 0x592A, 0x592D, 0x5B54,
    0x5C11, 0x5C24, 0x5C3A, 0x5C6F, 0x5DF4, 0x5E7B, 0x5EFF, 0x5F14, 0x5F15,
    0x5FC3, 0x6208, 0x6236, 0x624B, 0x624E, 0x652F, 0x6587, 0x6597, 0x65A4,
    0x65B9, 0x65E5, 0x66F0, 0x6708, 0x6728, 0x6B20, 0x6B62, 0x6B79, 0x6BCB,
    0x6BD4, 0x6BDB, 0x6C0F, 0x6C34, 0x706B, 0x722A, 0x7236, 0x723B, 0x7247,
    0x7259, 0x725B, 0x72AC, 0x738B, 0x4E19, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x4E16, 0x4E15,
    0x4E14, 0x4E18, 0x4E3B, 0x4E4D, 0x4E4F, 0x4E4E, 0x4EE5, 0x4ED8, 0x4ED4,
    0x4ED5, 0x4ED6, 0x4ED7, 0x4EE3, 0x4EE4, 0x4ED9, 0x4EDE, 0x5145, 0x5144,
    0x5189, 0x518A, 0x51AC, 0x51F9, 0x51FA, 0x51F8, 0x520A, 0x52A0, 0x529F,
    0x5305, 0x5306, 0x5317, 0x531D, 0x4EDF, 0x534A, 0x5349, 0x5361, 0x5360,
    0x536F, 0x536E, 0x53BB, 0x53EF, 0x53E4, 0x53F3, 0x53EC, 0x53EE, 0x53E9,
    0x53E8, 0x53FC, 0x53F8, 0x53F5, 0x53EB, 0x53E6, 0x53EA, 0x53F2, 0x53F1,
    0x53F0, 0x53E5, 0x53ED, 0x53FB, 0x56DB, 0x56DA, 0x5916, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x592E, 0x5931, 0x5974, 0x5976,
    0x5B55, 0x5B83, 0x5C3C, 0x5DE8, 0x5DE7, 0x5DE6, 0x5E02, 0x5E03, 0x5E73,
    0x5E7C, 0x5F01, 0x5F18, 0x5F17, 0x5FC5, 0x620A, 0x6253, 0x6254, 0x6252,
    0x6251, 0x65A5, 0x65E6, 0x672E, 0x672C, 0x672A, 0x672B, 0x672D, 0x6B63,
    0x6BCD, 0x6C11, 0x6C10, 0x6C38, 0x6C41, 0x6C40, 0x6C3E, 0x72AF, 0x7384,
    0x7389, 0x74DC, 0x74E6, 0x7518, 0x751F, 0x7528, 0x7529, 0x7530, 0x7531,
    0x7532, 0x7533, 0x758B, 0x767D, 0x76AE, 0x76BF, 0x76EE, 0x77DB, 0x77E2,
    0x77F3, 0x793A, 0x79BE, 0x7A74, 0x7ACB, 0x4E1E, 0x4E1F, 0x4E52, 0x4E53,
    0x4E69, 0x4E99, 0x4EA4, 0x4EA6, 0x4EA5, 0x4EFF, 0x4F09, 0x4F19, 0x4F0A,
    0x4F15, 0x4F0D, 0x4F10, 0x4F11, 0x4F0F, 0x4EF2, 0x4EF6, 0x4EFB, 0x4EF0,
    0x4EF3, 0x4EFD, 0x4F01, 0x4F0B, 0x5149, 0x5147, 0x5146, 0x5148, 0x5168,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x5171, 0x518D, 0x51B0, 0x5217, 0x5211, 0x5212, 0x520E,
    0x5216, 0x52A3, 0x5308, 0x5321, 0x5320, 0x5370, 0x5371, 0x5409, 0x540F,
    0x540C, 0x540A, 0x5410, 0x5401, 0x540B, 0x5404, 0x5411, 0x540D, 0x5408,
    0x5403, 0x540E, 0x5406, 0x5412, 0x56E0, 0x56DE, 0x56DD, 0x5733, 0x5730,
    0x5728, 0x572D, 0x572C, 0x572F, 0x5729, 0x5919, 0x591A, 0x5937, 0x5938,
    0x5984, 0x5978, 0x5983, 0x597D, 0x5979, 0x5982, 0x5981, 0x5B57, 0x5B58,
    0x5B87, 0x5B88, 0x5B85, 0x5B89, 0x5BFA, 0x5C16, 0x5C79, 0x5DDE, 0x5E06,
    0x5E76, 0x5E74, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x5F0F, 0x5F1B, 0x5FD9, 0x5FD6, 0x620E, 0x620C, 0x620D, 0x6210, 0x6263,
    0x625B, 0x6258, 0x6536, 0x65E9, 0x65E8, 0x65EC, 0x65ED, 0x66F2, 0x66F3,
    0x6709, 0x673D, 0x6734, 0x6731, 0x6735, 0x6B21, 0x6B64, 0x6B7B, 0x6C16,
    0x6C5D, 0x6C57, 0x6C59, 0x6C5F, 0x6C60, 0x6C50, 0x6C55, 0x6C61, 0x6C5B,
    0x6C4D, 0x6C4E, 0x7070, 0x725F, 0x725D, 0x767E, 0x7AF9, 0x7C73, 0x7CF8,
    0x7F36, 0x7F8A, 0x7FBD, 0x8001, 0x8003, 0x800C, 0x8012, 0x8033, 0x807F,
    0x8089, 0x808B, 0x808C, 0x81E3, 0x81EA, 0x81F3, 0x81FC, 0x820C, 0x821B,
    0x821F, 0x826E, 0x8272, 0x827E, 0x866B, 0x8840, 0x884C, 0x8863, 0x897F,
    0x9621, 0x4E32, 0x4EA8, 0x4F4D, 0x4F4F, 0x4F47, 0x4F57, 0x4F5E, 0x4F34,
    0x4F5B, 0x4F55, 0x4F30, 0x4F50, 0x4F51, 0x4F3D, 0x4F3A, 0x4F38, 0x4F43,
    0x4F54, 0x4F3C, 0x4F46, 0x4F63, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x4F5C, 0x4F60, 0x4F2F,
    0x4F4E, 0x4F36, 0x4F59, 0x4F5D, 0x4F48, 0x4F5A, 0x514C, 0x514B, 0x514D,
    0x5175, 0x51B6, 0x51B7, 0x5225, 0x5224, 0x5229, 0x522A, 0x5228, 0x52AB,
    0x52A9, 0x52AA, 0x52AC, 0x5323, 0x5373, 0x5375, 0x541D, 0x542D, 0x541E,
    0x543E, 0x5426, 0x544E, 0x5427, 0x5446, 0x5443, 0x5433, 0x5448, 0x5442,
    0x541B, 0x5429, 0x544A, 0x5439, 0x543B, 0x5438, 0x542E, 0x5435, 0x5436,
    0x5420, 0x543C, 0x5440, 0x5431, 0x542B, 0x541F, 0x542C, 0x56EA, 0x56F0,
    0x56E4, 0x56EB, 0x574A, 0x5751, 0x5740, 0x574D, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x5747, 0x574E, 0x573E, 0x5750, 0x574F,
    0x573B, 0x58EF, 0x593E, 0x599D, 0x5992, 0x59A8, 0x599E, 0x59A3, 0x5999,
    0x5996, 0x598D, 0x59A4, 0x5993, 0x598A, 0x59A5, 0x5B5D, 0x5B5C, 0x5B5A,
    0x5B5B, 0x5B8C, 0x5B8B, 0x5B8F, 0x5C2C, 0x5C40, 0x5C41, 0x5C3F, 0x5C3E,
    0x5C90, 0x5C91, 0x5C94, 0x5C8C, 0x5DEB, 0x5E0C, 0x5E8F, 0x5E87, 0x5E8A,
    0x5EF7, 0x5F04, 0x5F1F, 0x5F64, 0x5F62, 0x5F77, 0x5F79, 0x5FD8, 0x5FCC,
    0x5FD7, 0x5FCD, 0x5FF1, 0x5FEB, 0x5FF8, 0x5FEA, 0x6212, 0x6211, 0x6284,
    0x6297, 0x6296, 0x6280, 0x6276, 0x6289, 0x626D, 0x628A, 0x627C, 0x627E,
    0x6279, 0x6273, 0x6292, 0x626F, 0x6298, 0x626E, 0x6295, 0x6293, 0x6291,
    0x6286, 0x6539, 0x653B, 0x6538, 0x65F1, 0x66F4, 0x675F, 0x674E, 0x674F,
    0x6750, 0x6751, 0x675C, 0x6756, 0x675E, 0x6749, 0x6746, 0x6760, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x6753, 0x6757, 0x6B65, 0x6BCF, 0x6C42, 0x6C5E, 0x6C99, 0x6C81,
    0x6C88, 0x6C89, 0x6C85, 0x6C9B, 0x6C6A, 0x6C7A, 0x6C90, 0x6C70, 0x6C8C,
    0x6C68, 0x6C96, 0x6C92, 0x6C7D, 0x6C83, 0x6C72, 0x6C7E, 0x6C74, 0x6C86,
    0x6C76, 0x6C8D, 0x6C94, 0x6C98, 0x6C82, 0x7076, 0x707C, 0x707D, 0x7078,
    0x7262, 0x7261, 0x7260, 0x72C4, 0x72C2, 0x7396, 0x752C, 0x752B, 0x7537,
    0x7538, 0x7682, 0x76EF, 0x77E3, 0x79C1, 0x79C0, 0x79BF, 0x7A76, 0x7CFB,
    0x7F55, 0x8096, 0x8093, 0x809D, 0x8098, 0x809B, 0x809A, 0x80B2, 0x826F,
    0x8292, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x828B,
    0x828D, 0x898B, 0x89D2, 0x8A00, 0x8C37, 0x8C46, 0x8C55, 0x8C9D, 0x8D64,
    0x8D70, 0x8DB3, 0x8EAB, 0x8ECA, 0x8F9B, 0x8FB0, 0x8FC2, 0x8FC6, 0x8FC5,
    0x8FC4, 0x5DE1, 0x9091, 0x90A2, 0x90AA, 0x90A6, 0x90A3, 0x9149, 0x91C6,
    0x91CC, 0x9632, 0x962E, 0x9631, 0x962A, 0x962C, 0x4E26, 0x4E56, 0x4E73,
    0x4E8B, 0x4E9B, 0x4E9E, 0x4EAB, 0x4EAC, 0x4F6F, 0x4F9D, 0x4F8D, 0x4F73,
    0x4F7F, 0x4F6C, 0x4F9B, 0x4F8B, 0x4F86, 0x4F83, 0x4F70, 0x4F75, 0x4F88,
    0x4F69, 0x4F7B, 0x4F96, 0x4F7E, 0x4F8F, 0x4F91, 0x4F7A, 0x5154, 0x5152,
    0x5155, 0x5169, 0x5177, 0x5176, 0x5178, 0x51BD, 0x51FD, 0x523B, 0x5238,
    0x5237, 0x523A, 0x5230, 0x522E, 0x5236, 0x5241, 0x52BE, 0x52BB, 0x5352,
    0x5354, 0x5353, 0x5351, 0x5366, 0x5377, 0x5378, 0x5379, 0x53D6, 0x53D4,
    0x53D7, 0x5473, 0x5475, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x5496, 0x5478, 0x5495, 0x5480,
    0x547B, 0x5477, 0x5484, 0x5492, 0x5486, 0x547C, 0x5490, 0x5471, 0x5476,
    0x548C, 0x549A, 0x5462, 0x5468, 0x548B, 0x547D, 0x548E, 0x56FA, 0x5783,
    0x5777, 0x576A, 0x5769, 0x5761, 0x5766, 0x5764, 0x577C, 0x591C, 0x5949,
    0x5947, 0x5948, 0x5944, 0x5954, 0x59BE, 0x59BB, 0x59D4, 0x59B9, 0x59AE,
    0x59D1, 0x59C6, 0x59D0, 0x59CD, 0x59CB, 0x59D3, 0x59CA, 0x59AF, 0x59B3,
    0x59D2, 0x59C5, 0x5B5F, 0x5B64, 0x5B63, 0x5B97, 0x5B9A, 0x5B98, 0x5B9C,
    0x5B99, 0x5B9B, 0x5C1A, 0x5C48, 0x5C45, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x5C46, 0x5CB7, 0x5CA1, 0x5CB8, 0x5CA9, 0x5CAB,
    0x5CB1, 0x5CB3, 0x5E18, 0x5E1A, 0x5E16, 0x5E15, 0x5E1B, 0x5E11, 0x5E78,
    0x5E9A, 0x5E97, 0x5E9C, 0x5E95, 0x5E96, 0x5EF6, 0x5F26, 0x5F27, 0x5F29,
    0x5F80, 0x5F81, 0x5F7F, 0x5F7C, 0x5FDD, 0x5FE0, 0x5FFD, 0x5FF5, 0x5FFF,
    0x600F, 0x6014, 0x602F, 0x6035, 0x6016, 0x602A, 0x6015, 0x6021, 0x6027,
    0x6029, 0x602B, 0x601B, 0x6216, 0x6215, 0x623F, 0x623E, 0x6240, 0x627F,
    0x62C9, 0x62CC, 0x62C4, 0x62BF, 0x62C2, 0x62B9, 0x62D2, 0x62DB, 0x62AB,
    0x62D3, 0x62D4, 0x62CB, 0x62C8, 0x62A8, 0x62BD, 0x62BC, 0x62D0, 0x62D9,
    0x62C7, 0x62CD, 0x62B5, 0x62DA, 0x62B1, 0x62D8, 0x62D6, 0x62D7, 0x62C6,
    0x62AC, 0x62CE, 0x653E, 0x65A7, 0x65BC, 0x65FA, 0x6614, 0x6613, 0x660C,
    0x6606, 0x6602, 0x660E, 0x6600, 0x660F, 0x6615, 0x660A, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x6607, 0x670D, 0x670B, 0x676D, 0x678B, 0x6795, 0x6771, 0x679C, 0x6773,
    0x6777, 0x6787, 0x679D, 0x6797, 0x676F, 0x6770, 0x677F, 0x6789, 0x677E,
    0x6790, 0x6775, 0x679A, 0x6793, 0x677C, 0x676A, 0x6772, 0x6B23, 0x6B66,
    0x6B67, 0x6B7F, 0x6C13, 0x6C1B, 0x6CE3, 0x6CE8, 0x6CF3, 0x6CB1, 0x6CCC,
    0x6CE5, 0x6CB3, 0x6CBD, 0x6CBE, 0x6CBC, 0x6CE2, 0x6CAB, 0x6CD5, 0x6CD3,
    0x6CB8, 0x6CC4, 0x6CB9, 0x6CC1, 0x6CAE, 0x6CD7, 0x6CC5, 0x6CF1, 0x6CBF,
    0x6CBB, 0x6CE1, 0x6CDB, 0x6CCA, 0x6CAC, 0x6CEF, 0x6CDC, 0x6CD6, 0x6CE0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x7095, 0x708E,
    0x7092, 0x708A, 0x7099, 0x722C, 0x722D, 0x7238, 0x7248, 0x7267, 0x7269,
    0x72C0, 0x72CE, 0x72D9, 0x72D7, 0x72D0, 0x73A9, 0x73A8, 0x739F, 0x73AB,
    0x73A5, 0x753D, 0x759D, 0x7599, 0x759A, 0x7684, 0x76C2, 0x76F2, 0x76F4,
    0x77E5, 0x77FD, 0x793E, 0x7940, 0x7941, 0x79C9, 0x79C8, 0x7A7A, 0x7A79,
    0x7AFA, 0x7CFE, 0x7F54, 0x7F8C, 0x7F8B, 0x8005, 0x80BA, 0x80A5, 0x80A2,
    0x80B1, 0x80A1, 0x80AB, 0x80A9, 0x80B4, 0x80AA, 0x80AF, 0x81E5, 0x81FE,
    0x820D, 0x82B3, 0x829D, 0x8299, 0x82AD, 0x82BD, 0x829F, 0x82B9, 0x82B1,
    0x82AC, 0x82A5, 0x82AF, 0x82B8, 0x82A3, 0x82B0, 0x82BE, 0x82B7, 0x864E,
    0x8671, 0x521D, 0x8868, 0x8ECB, 0x8FCE, 0x8FD4, 0x8FD1, 0x90B5, 0x90B8,
    0x90B1, 0x90B6, 0x91C7, 0x91D1, 0x9577, 0x9580, 0x961C, 0x9640, 0x963F,
    0x963B, 0x9644, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x9642, 0x96B9, 0x96E8, 0x9752, 0x975E,
    0x4E9F, 0x4EAD, 0x4EAE, 0x4FE1, 0x4FB5, 0x4FAF, 0x4FBF, 0x4FE0, 0x4FD1,
    0x4FCF, 0x4FDD, 0x4FC3, 0x4FB6, 0x4FD8, 0x4FDF, 0x4FCA, 0x4FD7, 0x4FAE,
    0x4FD0, 0x4FC4, 0x4FC2, 0x4FDA, 0x4FCE, 0x4FDE, 0x4FB7, 0x5157, 0x5192,
    0x5191, 0x51A0, 0x524E, 0x5243, 0x524A, 0x524D, 0x524C, 0x524B, 0x5247,
    0x52C7, 0x52C9, 0x52C3, 0x52C1, 0x530D, 0x5357, 0x537B, 0x539A, 0x53DB,
    0x54AC, 0x54C0, 0x54A8, 0x54CE, 0x54C9, 0x54B8, 0x54A6, 0x54B3, 0x54C7,
    0x54C2, 0x54BD, 0x54AA, 0x54C1, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x54C4, 0x54C8, 0x54AF, 0x54AB, 0x54B1, 0x54BB, 0x54A9,
    0x54A7, 0x54BF, 0x56FF, 0x5782, 0x578B, 0x57A0, 0x57A3, 0x57A2, 0x57CE,
    0x57AE, 0x5793, 0x5955, 0x5951, 0x594F, 0x594E, 0x5950, 0x59DC, 0x59D8,
    0x59FF, 0x59E3, 0x59E8, 0x5A03, 0x59E5, 0x59EA, 0x59DA, 0x59E6, 0x5A01,
    0x59FB, 0x5B69, 0x5BA3, 0x5BA6, 0x5BA4, 0x5BA2, 0x5BA5, 0x5C01, 0x5C4E,
    0x5C4F, 0x5C4D, 0x5C4B, 0x5CD9, 0x5CD2, 0x5DF7, 0x5E1D, 0x5E25, 0x5E1F,
    0x5E7D, 0x5EA0, 0x5EA6, 0x5EFA, 0x5F08, 0x5F2D, 0x5F65, 0x5F88, 0x5F85,
    0x5F8A, 0x5F8B, 0x5F87, 0x5F8C, 0x5F89, 0x6012, 0x601D, 0x6020, 0x6025,
    0x600E, 0x6028, 0x604D, 0x6070, 0x6068, 0x6062, 0x6046, 0x6043, 0x606C,
    0x606B, 0x606A, 0x6064, 0x6241, 0x62DC, 0x6316, 0x6309, 0x62FC, 0x62ED,
    0x6301, 0x62EE, 0x62FD, 0x6307, 0x62F1, 0x62F7, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x62EF,
    0x62EC, 0x62FE, 0x62F4, 0x6311, 0x6302, 0x653F, 0x6545, 0x65AB, 0x65BD,
    0x65E2, 0x6625, 0x662D, 0x6620, 0x6627, 0x662F, 0x661F, 0x6628, 0x6631,
    0x6624, 0x66F7, 0x67FF, 0x67D3, 0x67F1, 0x67D4, 0x67D0, 0x67EC, 0x67B6,
    0x67AF, 0x67F5, 0x67E9, 0x67EF, 0x67C4, 0x67D1, 0x67B4, 0x67DA, 0x67E5,
    0x67B8, 0x67CF, 0x67DE, 0x67F3, 0x67B0, 0x67D9, 0x67E2, 0x67DD, 0x67D2,
    0x6B6A, 0x6B83, 0x6B86, 0x6BB5, 0x6BD2, 0x6BD7, 0x6C1F, 0x6CC9, 0x6D0B,
    0x6D32, 0x6D2A, 0x6D41, 0x6D25, 0x6D0C, 0x6D31, 0x6D1E, 0x6D17, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x6D3B, 0x6D3D, 0x6D3E,
    0x6D36, 0x6D1B, 0x6CF5, 0x6D39, 0x6D27, 0x6D38, 0x6D29, 0x6D2E, 0x6D35,
    0x6D0E, 0x6D2B, 0x70AB, 0x70BA, 0x70B3, 0x70AC, 0x70AF, 0x70AD, 0x70B8,
    0x70AE, 0x70A4, 0x7230, 0x7272, 0x726F, 0x7274, 0x72E9, 0x72E0, 0x72E1,
    0x73B7, 0x73CA, 0x73BB, 0x73B2, 0x73CD, 0x73C0, 0x73B3, 0x751A, 0x752D,
    0x754F, 0x754C, 0x754E, 0x754B, 0x75AB, 0x75A4, 0x75A5, 0x75A2, 0x75A3,
    0x7678, 0x7686, 0x7687, 0x7688, 0x76C8, 0x76C6, 0x76C3, 0x76C5, 0x7701,
    0x76F9, 0x76F8, 0x7709, 0x770B, 0x76FE, 0x76FC, 0x7707, 0x77DC, 0x7802,
    0x7814, 0x780C, 0x780D, 0x7946, 0x7949, 0x7948, 0x7947, 0x79B9, 0x79BA,
    0x79D1, 0x79D2, 0x79CB, 0x7A7F, 0x7A81, 0x7AFF, 0x7AFD, 0x7C7D, 0x7D02,
    0x7D05, 0x7D00, 0x7D09, 0x7D07, 0x7D04, 0x7D06, 0x7F38, 0x7F8E, 0x7FBF,
    0x8004, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x8010, 0x800D, 0x8011, 0x8036, 0x80D6, 0x80E5,
    0x80DA, 0x80C3, 0x80C4, 0x80CC, 0x80E1, 0x80DB, 0x80CE, 0x80DE, 0x80E4,
    0x80DD, 0x81F4, 0x8222, 0x82E7, 0x8303, 0x8305, 0x82E3, 0x82DB, 0x82E6,
    0x8304, 0x82E5, 0x8302, 0x8309, 0x82D2, 0x82D7, 0x82F1, 0x8301, 0x82DC,
    0x82D4, 0x82D1, 0x82DE, 0x82D3, 0x82DF, 0x82EF, 0x8306, 0x8650, 0x8679,
    0x867B, 0x867A, 0x884D, 0x886B, 0x8981, 0x89D4, 0x8A08, 0x8A02, 0x8A03,
    0x8C9E, 0x8CA0, 0x8D74, 0x8D73, 0x8DB4, 0x8ECD, 0x8ECC, 0x8FF0, 0x8FE6,
    0x8FE2, 0x8FEA, 0x8FE5, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x8FED, 0x8FEB, 0x8FE4, 0x8FE8, 0x90CA, 0x90CE, 0x90C1, 0x90C3,
    0x914B, 0x914A, 0x91CD, 0x9582, 0x9650, 0x964B, 0x964C, 0x964D, 0x9762,
    0x9769, 0x97CB, 0x97ED, 0x97F3, 0x9801, 0x98A8, 0x98DB, 0x98DF, 0x9996,
    0x9999, 0x4E58, 0x4EB3, 0x500C, 0x500D, 0x5023, 0x4FEF, 0x5026, 0x5025,
    0x4FF8, 0x5029, 0x5016, 0x5006, 0x503C, 0x501F, 0x501A, 0x5012, 0x5011,
    0x4FFA, 0x5000, 0x5014, 0x5028, 0x4FF1, 0x5021, 0x500B, 0x5019, 0x5018,
    0x4FF3, 0x4FEE, 0x502D, 0x502A, 0x4FFE, 0x502B, 0x5009, 0x517C, 0x51A4,
    0x51A5, 0x51A2, 0x51CD, 0x51CC, 0x51C6, 0x51CB, 0x5256, 0x525C, 0x5254,
    0x525B, 0x525D, 0x532A, 0x537F, 0x539F, 0x539D, 0x53DF, 0x54E8, 0x5510,
    0x5501, 0x5537, 0x54FC, 0x54E5, 0x54F2, 0x5506, 0x54FA, 0x5514, 0x54E9,
    0x54ED, 0x54E1, 0x5509, 0x54EE, 0x54EA, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x54E6, 0x5527,
    0x5507, 0x54FD, 0x550F, 0x5703, 0x5704, 0x57C2, 0x57D4, 0x57CB, 0x57C3,
    0x5809, 0x590F, 0x5957, 0x5958, 0x595A, 0x5A11, 0x5A18, 0x5A1C, 0x5A1F,
    0x5A1B, 0x5A13, 0x59EC, 0x5A20, 0x5A23, 0x5A29, 0x5A25, 0x5A0C, 0x5A09,
    0x5B6B, 0x5C58, 0x5BB0, 0x5BB3, 0x5BB6, 0x5BB4, 0x5BAE, 0x5BB5, 0x5BB9,
    0x5BB8, 0x5C04, 0x5C51, 0x5C55, 0x5C50, 0x5CED, 0x5CFD, 0x5CFB, 0x5CEA,
    0x5CE8, 0x5CF0, 0x5CF6, 0x5D01, 0x5CF4, 0x5DEE, 0x5E2D, 0x5E2B, 0x5EAB,
    0x5EAD, 0x5EA7, 0x5F31, 0x5F92, 0x5F91, 0x5F90, 0x6059, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x6063, 0x6065, 0x6050, 0x6055,
    0x606D, 0x6069, 0x606F, 0x6084, 0x609F, 0x609A, 0x608D, 0x6094, 0x608C,
    0x6085, 0x6096, 0x6247, 0x62F3, 0x6308, 0x62FF, 0x634E, 0x633E, 0x632F,
    0x6355, 0x6342, 0x6346, 0x634F, 0x6349, 0x633A, 0x6350, 0x633D, 0x632A,
    0x632B, 0x6328, 0x634D, 0x634C, 0x6548, 0x6549, 0x6599, 0x65C1, 0x65C5,
    0x6642, 0x6649, 0x664F, 0x6643, 0x6652, 0x664C, 0x6645, 0x6641, 0x66F8,
    0x6714, 0x6715, 0x6717, 0x6821, 0x6838, 0x6848, 0x6846, 0x6853, 0x6839,
    0x6842, 0x6854, 0x6829, 0x68B3, 0x6817, 0x684C, 0x6851, 0x683D, 0x67F4,
    0x6850, 0x6840, 0x683C, 0x6843, 0x682A, 0x6845, 0x6813, 0x6818, 0x6841,
    0x6B8A, 0x6B89, 0x6BB7, 0x6C23, 0x6C27, 0x6C28, 0x6C26, 0x6C24, 0x6CF0,
    0x6D6A, 0x6D95, 0x6D88, 0x6D87, 0x6D66, 0x6D78, 0x6D77, 0x6D59, 0x6D93,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6D6C, 0x6D89, 0x6D6E, 0x6D5A, 0x6D74, 0x6D69, 0x6D8C,
    0x6D8A, 0x6D79, 0x6D85, 0x6D65, 0x6D94, 0x70CA, 0x70D8, 0x70E4, 0x70D9,
    0x70C8, 0x70CF, 0x7239, 0x7279, 0x72FC, 0x72F9, 0x72FD, 0x72F8, 0x72F7,
    0x7386, 0x73ED, 0x7409, 0x73EE, 0x73E0, 0x73EA, 0x73DE, 0x7554, 0x755D,
    0x755C, 0x755A, 0x7559, 0x75BE, 0x75C5, 0x75C7, 0x75B2, 0x75B3, 0x75BD,
    0x75BC, 0x75B9, 0x75C2, 0x75B8, 0x768B, 0x76B0, 0x76CA, 0x76CD, 0x76CE,
    0x7729, 0x771F, 0x7720, 0x7728, 0x77E9, 0x7830, 0x7827, 0x7838, 0x781D,
    0x7834, 0x7837, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x7825, 0x782D, 0x7820, 0x781F, 0x7832, 0x7955, 0x7950, 0x7960, 0x795F,
    0x7956, 0x795E, 0x795D, 0x7957, 0x795A, 0x79E4, 0x79E3, 0x79E7, 0x79DF,
    0x79E6, 0x79E9, 0x79D8, 0x7A84, 0x7A88, 0x7AD9, 0x7B06, 0x7B11, 0x7C89,
    0x7D21, 0x7D17, 0x7D0B, 0x7D0A, 0x7D20, 0x7D22, 0x7D14, 0x7D10, 0x7D15,
    0x7D1A, 0x7D1C, 0x7D0D, 0x7D19, 0x7D1B, 0x7F3A, 0x7F5F, 0x7F94, 0x7FC5,
    0x7FC1, 0x8006, 0x8018, 0x8015, 0x8019, 0x8017, 0x803D, 0x803F, 0x80F1,
    0x8102, 0x80F0, 0x8105, 0x80ED, 0x80F4, 0x8106, 0x80F8, 0x80F3, 0x8108,
    0x80FD, 0x810A, 0x80FC, 0x80EF, 0x81ED, 0x81EC, 0x8200, 0x8210, 0x822A,
    0x822B, 0x8228, 0x822C, 0x82BB, 0x832B, 0x8352, 0x8354, 0x834A, 0x8338,
    0x8350, 0x8349, 0x8335, 0x8334, 0x834F, 0x8332, 0x8339, 0x8336, 0x8317,
    0x8340, 0x8331, 0x8328, 0x8343, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x8654, 0x868A, 0x86AA,
    0x8693, 0x86A4, 0x86A9, 0x868C, 0x86A3, 0x869C, 0x8870, 0x8877, 0x8881,
    0x8882, 0x887D, 0x8879, 0x8A18, 0x8A10, 0x8A0E, 0x8A0C, 0x8A15, 0x8A0A,
    0x8A17, 0x8A13, 0x8A16, 0x8A0F, 0x8A11, 0x8C48, 0x8C7A, 0x8C79, 0x8CA1,
    0x8CA2, 0x8D77, 0x8EAC, 0x8ED2, 0x8ED4, 0x8ECF, 0x8FB1, 0x9001, 0x9006,
    0x8FF7, 0x9000, 0x8FFA, 0x8FF4, 0x9003, 0x8FFD, 0x9005, 0x8FF8, 0x9095,
    0x90E1, 0x90DD, 0x90E2, 0x9152, 0x914D, 0x914C, 0x91D8, 0x91DD, 0x91D7,
    0x91DC, 0x91D9, 0x9583, 0x9662, 0x9663, 0x9661, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x965B, 0x965D, 0x9664, 0x9658, 0x965E,
    0x96BB, 0x98E2, 0x99AC, 0x9AA8, 0x9AD8, 0x9B25, 0x9B32, 0x9B3C, 0x4E7E,
    0x507A, 0x507D, 0x505C, 0x5047, 0x5043, 0x504C, 0x505A, 0x5049, 0x5065,
    0x5076, 0x504E, 0x5055, 0x5075, 0x5074, 0x5077, 0x504F, 0x500F, 0x506F,
    0x506D, 0x515C, 0x5195, 0x51F0, 0x526A, 0x526F, 0x52D2, 0x52D9, 0x52D8,
    0x52D5, 0x5310, 0x530F, 0x5319, 0x533F, 0x5340, 0x533E, 0x53C3, 0x66FC,
    0x5546, 0x556A, 0x5566, 0x5544, 0x555E, 0x5561, 0x5543, 0x554A, 0x5531,
    0x5556, 0x554F, 0x5555, 0x552F, 0x5564, 0x5538, 0x552E, 0x555C, 0x552C,
    0x5563, 0x5533, 0x5541, 0x5557, 0x5708, 0x570B, 0x5709, 0x57DF, 0x5805,
    0x580A, 0x5806, 0x57E0, 0x57E4, 0x57FA, 0x5802, 0x5835, 0x57F7, 0x57F9,
    0x5920, 0x5962, 0x5A36, 0x5A41, 0x5A49, 0x5A66, 0x5A6A, 0x5A40, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x5A3C, 0x5A62, 0x5A5A, 0x5A46, 0x5A4A, 0x5B70, 0x5BC7, 0x5BC5,
    0x5BC4, 0x5BC2, 0x5BBF, 0x5BC6, 0x5C09, 0x5C08, 0x5C07, 0x5C60, 0x5C5C,
    0x5C5D, 0x5D07, 0x5D06, 0x5D0E, 0x5D1B, 0x5D16, 0x5D22, 0x5D11, 0x5D29,
    0x5D14, 0x5D19, 0x5D24, 0x5D27, 0x5D17, 0x5DE2, 0x5E38, 0x5E36, 0x5E33,
    0x5E37, 0x5EB7, 0x5EB8, 0x5EB6, 0x5EB5, 0x5EBE, 0x5F35, 0x5F37, 0x5F57,
    0x5F6C, 0x5F69, 0x5F6B, 0x5F97, 0x5F99, 0x5F9E, 0x5F98, 0x5FA1, 0x5FA0,
    0x5F9C, 0x607F, 0x60A3, 0x6089, 0x60A0, 0x60A8, 0x60CB, 0x60B4, 0x60E6,
    0x60BD, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x60C5,
    0x60BB, 0x60B5, 0x60DC, 0x60BC, 0x60D8, 0x60D5, 0x60C6, 0x60DF, 0x60B8,
    0x60DA, 0x60C7, 0x621A, 0x621B, 0x6248, 0x63A0, 0x63A7, 0x6372, 0x6396,
    0x63A2, 0x63A5, 0x6377, 0x6367, 0x6398, 0x63AA, 0x6371, 0x63A9, 0x6389,
    0x6383, 0x639B, 0x636B, 0x63A8, 0x6384, 0x6388, 0x6399, 0x63A1, 0x63AC,
    0x6392, 0x638F, 0x6380, 0x637B, 0x6369, 0x6368, 0x637A, 0x655D, 0x6556,
    0x6551, 0x6559, 0x6557, 0x555F, 0x654F, 0x6558, 0x6555, 0x6554, 0x659C,
    0x659B, 0x65AC, 0x65CF, 0x65CB, 0x65CC, 0x65CE, 0x665D, 0x665A, 0x6664,
    0x6668, 0x6666, 0x665E, 0x66F9, 0x52D7, 0x671B, 0x6881, 0x68AF, 0x68A2,
    0x6893, 0x68B5, 0x687F, 0x6876, 0x68B1, 0x68A7, 0x6897, 0x68B0, 0x6883,
    0x68C4, 0x68AD, 0x6886, 0x6885, 0x6894, 0x689D, 0x68A8, 0x689F, 0x68A1,
    0x6882, 0x6B32, 0x6BBA, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x6BEB, 0x6BEC, 0x6C2B, 0x6D8E,
    0x6DBC, 0x6DF3, 0x6DD9, 0x6DB2, 0x6DE1, 0x6DCC, 0x6DE4, 0x6DFB, 0x6DFA,
    0x6E05, 0x6DC7, 0x6DCB, 0x6DAF, 0x6DD1, 0x6DAE, 0x6DDE, 0x6DF9, 0x6DB8,
    0x6DF7, 0x6DF5, 0x6DC5, 0x6DD2, 0x6E1A, 0x6DB5, 0x6DDA, 0x6DEB, 0x6DD8,
    0x6DEA, 0x6DF1, 0x6DEE, 0x6DE8, 0x6DC6, 0x6DC4, 0x6DAA, 0x6DEC, 0x6DBF,
    0x6DE6, 0x70F9, 0x7109, 0x710A, 0x70FD, 0x70EF, 0x723D, 0x727D, 0x7281,
    0x731C, 0x731B, 0x7316, 0x7313, 0x7319, 0x7387, 0x7405, 0x740A, 0x7403,
    0x7406, 0x73FE, 0x740D, 0x74E0, 0x74F6, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x74F7, 0x751C, 0x7522, 0x7565, 0x7566, 0x7562,
    0x7570, 0x758F, 0x75D4, 0x75D5, 0x75B5, 0x75CA, 0x75CD, 0x768E, 0x76D4,
    0x76D2, 0x76DB, 0x7737, 0x773E, 0x773C, 0x7736, 0x7738, 0x773A, 0x786B,
    0x7843, 0x784E, 0x7965, 0x7968, 0x796D, 0x79FB, 0x7A92, 0x7A95, 0x7B20,
    0x7B28, 0x7B1B, 0x7B2C, 0x7B26, 0x7B19, 0x7B1E, 0x7B2E, 0x7C92, 0x7C97,
    0x7C95, 0x7D46, 0x7D43, 0x7D71, 0x7D2E, 0x7D39, 0x7D3C, 0x7D40, 0x7D30,
    0x7D33, 0x7D44, 0x7D2F, 0x7D42, 0x7D32, 0x7D31, 0x7F3D, 0x7F9E, 0x7F9A,
    0x7FCC, 0x7FCE, 0x7FD2, 0x801C, 0x804A, 0x8046, 0x812F, 0x8116, 0x8123,
    0x812B, 0x8129, 0x8130, 0x8124, 0x8202, 0x8235, 0x8237, 0x8236, 0x8239,
    0x838E, 0x839E, 0x8398, 0x8378, 0x83A2, 0x8396, 0x83BD, 0x83AB, 0x8392,
    0x838A, 0x8393, 0x8389, 0x83A0, 0x8377, 0x837B, 0x837C, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x8386, 0x83A7, 0x8655, 0x5F6A, 0x86C7, 0x86C0, 0x86B6, 0x86C4, 0x86B5,
    0x86C6, 0x86CB, 0x86B1, 0x86AF, 0x86C9, 0x8853, 0x889E, 0x8888, 0x88AB,
    0x8892, 0x8896, 0x888D, 0x888B, 0x8993, 0x898F, 0x8A2A, 0x8A1D, 0x8A23,
    0x8A25, 0x8A31, 0x8A2D, 0x8A1F, 0x8A1B, 0x8A22, 0x8C49, 0x8C5A, 0x8CA9,
    0x8CAC, 0x8CAB, 0x8CA8, 0x8CAA, 0x8CA7, 0x8D67, 0x8D66, 0x8DBE, 0x8DBA,
    0x8EDB, 0x8EDF, 0x9019, 0x900D, 0x901A, 0x9017, 0x9023, 0x901F, 0x901D,
    0x9010, 0x9015, 0x901E, 0x9020, 0x900F, 0x9022, 0x9016, 0x901B, 0x9014,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x90E8, 0x90ED,
    0x90FD, 0x9157, 0x91CE, 0x91F5, 0x91E6, 0x91E3, 0x91E7, 0x91ED, 0x91E9,
    0x9589, 0x966A, 0x9675, 0x9673, 0x9678, 0x9670, 0x9674, 0x9676, 0x9677,
    0x966C, 0x96C0, 0x96EA, 0x96E9, 0x7AE0, 0x7ADF, 0x9802, 0x9803, 0x9B5A,
    0x9CE5, 0x9E75, 0x9E7F, 0x9EA5, 0x9EBB, 0x50A2, 0x508D, 0x5085, 0x5099,
    0x5091, 0x5080, 0x5096, 0x5098, 0x509A, 0x6700, 0x51F1, 0x5272, 0x5274,
    0x5275, 0x5269, 0x52DE, 0x52DD, 0x52DB, 0x535A, 0x53A5, 0x557B, 0x5580,
    0x55A7, 0x557C, 0x558A, 0x559D, 0x5598, 0x5582, 0x559C, 0x55AA, 0x5594,
    0x5587, 0x558B, 0x5583, 0x55B3, 0x55AE, 0x559F, 0x553E, 0x55B2, 0x559A,
    0x55BB, 0x55AC, 0x55B1, 0x557E, 0x5589, 0x55AB, 0x5599, 0x570D, 0x582F,
    0x582A, 0x5834, 0x5824, 0x5830, 0x5831, 0x5821, 0x581D, 0x5820, 0x58F9,
    0x58FA, 0x5960, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x5A77, 0x5A9A, 0x5A7F, 0x5A92, 0x5A9B,
    0x5AA7, 0x5B73, 0x5B71, 0x5BD2, 0x5BCC, 0x5BD3, 0x5BD0, 0x5C0A, 0x5C0B,
    0x5C31, 0x5D4C, 0x5D50, 0x5D34, 0x5D47, 0x5DFD, 0x5E45, 0x5E3D, 0x5E40,
    0x5E43, 0x5E7E, 0x5ECA, 0x5EC1, 0x5EC2, 0x5EC4, 0x5F3C, 0x5F6D, 0x5FA9,
    0x5FAA, 0x5FA8, 0x60D1, 0x60E1, 0x60B2, 0x60B6, 0x60E0, 0x611C, 0x6123,
    0x60FA, 0x6115, 0x60F0, 0x60FB, 0x60F4, 0x6168, 0x60F1, 0x610E, 0x60F6,
    0x6109, 0x6100, 0x6112, 0x621F, 0x6249, 0x63A3, 0x638C, 0x63CF, 0x63C0,
    0x63E9, 0x63C9, 0x63C6, 0x63CD, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x63D2, 0x63E3, 0x63D0, 0x63E1, 0x63D6, 0x63ED, 0x63EE,
    0x6376, 0x63F4, 0x63EA, 0x63DB, 0x6452, 0x63DA, 0x63F9, 0x655E, 0x6566,
    0x6562, 0x6563, 0x6591, 0x6590, 0x65AF, 0x666E, 0x6670, 0x6674, 0x6676,
    0x666F, 0x6691, 0x667A, 0x667E, 0x6677, 0x66FE, 0x66FF, 0x671F, 0x671D,
    0x68FA, 0x68D5, 0x68E0, 0x68D8, 0x68D7, 0x6905, 0x68DF, 0x68F5, 0x68EE,
    0x68E7, 0x68F9, 0x68D2, 0x68F2, 0x68E3, 0x68CB, 0x68CD, 0x690D, 0x6912,
    0x690E, 0x68C9, 0x68DA, 0x696E, 0x68FB, 0x6B3E, 0x6B3A, 0x6B3D, 0x6B98,
    0x6B96, 0x6BBC, 0x6BEF, 0x6C2E, 0x6C2F, 0x6C2C, 0x6E2F, 0x6E38, 0x6E54,
    0x6E21, 0x6E32, 0x6E67, 0x6E4A, 0x6E20, 0x6E25, 0x6E23, 0x6E1B, 0x6E5B,
    0x6E58, 0x6E24, 0x6E56, 0x6E6E, 0x6E2D, 0x6E26, 0x6E6F, 0x6E34, 0x6E4D,
    0x6E3A, 0x6E2C, 0x6E43, 0x6E1D, 0x6E3E, 0x6ECB, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6E89,
    0x6E19, 0x6E4E, 0x6E63, 0x6E44, 0x6E72, 0x6E69, 0x6E5F, 0x7119, 0x711A,
    0x7126, 0x7130, 0x7121, 0x7136, 0x716E, 0x711C, 0x724C, 0x7284, 0x7280,
    0x7336, 0x7325, 0x7334, 0x7329, 0x743A, 0x742A, 0x7433, 0x7422, 0x7425,
    0x7435, 0x7436, 0x7434, 0x742F, 0x741B, 0x7426, 0x7428, 0x7525, 0x7526,
    0x756B, 0x756A, 0x75E2, 0x75DB, 0x75E3, 0x75D9, 0x75D8, 0x75DE, 0x75E0,
    0x767B, 0x767C, 0x7696, 0x7693, 0x76B4, 0x76DC, 0x774F, 0x77ED, 0x785D,
    0x786C, 0x786F, 0x7A0D, 0x7A08, 0x7A0B, 0x7A05, 0x7A00, 0x7A98, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x7A97, 0x7A96, 0x7AE5,
    0x7AE3, 0x7B49, 0x7B56, 0x7B46, 0x7B50, 0x7B52, 0x7B54, 0x7B4D, 0x7B4B,
    0x7B4F, 0x7B51, 0x7C9F, 0x7CA5, 0x7D5E, 0x7D50, 0x7D68, 0x7D55, 0x7D2B,
    0x7D6E, 0x7D72, 0x7D61, 0x7D66, 0x7D62, 0x7D70, 0x7D73, 0x5584, 0x7FD4,
    0x7FD5, 0x800B, 0x8052, 0x8085, 0x8155, 0x8154, 0x814B, 0x8151, 0x814E,
    0x8139, 0x8146, 0x813E, 0x814C, 0x8153, 0x8174, 0x8212, 0x821C, 0x83E9,
    0x8403, 0x83F8, 0x840D, 0x83E0, 0x83C5, 0x840B, 0x83C1, 0x83EF, 0x83F1,
    0x83F4, 0x8457, 0x840A, 0x83F0, 0x840C, 0x83CC, 0x83FD, 0x83F2, 0x83CA,
    0x8438, 0x840E, 0x8404, 0x83DC, 0x8407, 0x83D4, 0x83DF, 0x865B, 0x86DF,
    0x86D9, 0x86ED, 0x86D4, 0x86DB, 0x86E4, 0x86D0, 0x86DE, 0x8857, 0x88C1,
    0x88C2, 0x88B1, 0x8983, 0x8996, 0x8A3B, 0x8A60, 0x8A55, 0x8A5E, 0x8A3C,
    0x8A41, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x8A54, 0x8A5B, 0x8A50, 0x8A46, 0x8A34, 0x8A3A,
    0x8A36, 0x8A56, 0x8C61, 0x8C82, 0x8CAF, 0x8CBC, 0x8CB3, 0x8CBD, 0x8CC1,
    0x8CBB, 0x8CC0, 0x8CB4, 0x8CB7, 0x8CB6, 0x8CBF, 0x8CB8, 0x8D8A, 0x8D85,
    0x8D81, 0x8DCE, 0x8DDD, 0x8DCB, 0x8DDA, 0x8DD1, 0x8DCC, 0x8DDB, 0x8DC6,
    0x8EFB, 0x8EF8, 0x8EFC, 0x8F9C, 0x902E, 0x9035, 0x9031, 0x9038, 0x9032,
    0x9036, 0x9102, 0x90F5, 0x9109, 0x90FE, 0x9163, 0x9165, 0x91CF, 0x9214,
    0x9215, 0x9223, 0x9209, 0x921E, 0x920D, 0x9210, 0x9207, 0x9211, 0x9594,
    0x958F, 0x958B, 0x9591, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x9593, 0x9592, 0x958E, 0x968A, 0x968E, 0x968B, 0x967D, 0x9685,
    0x9686, 0x968D, 0x9672, 0x9684, 0x96C1, 0x96C5, 0x96C4, 0x96C6, 0x96C7,
    0x96EF, 0x96F2, 0x97CC, 0x9805, 0x9806, 0x9808, 0x98E7, 0x98EA, 0x98EF,
    0x98E9, 0x98F2, 0x98ED, 0x99AE, 0x99AD, 0x9EC3, 0x9ECD, 0x9ED1, 0x4E82,
    0x50AD, 0x50B5, 0x50B2, 0x50B3, 0x50C5, 0x50BE, 0x50AC, 0x50B7, 0x50BB,
    0x50AF, 0x50C7, 0x527F, 0x5277, 0x527D, 0x52DF, 0x52E6, 0x52E4, 0x52E2,
    0x52E3, 0x532F, 0x55DF, 0x55E8, 0x55D3, 0x55E6, 0x55CE, 0x55DC, 0x55C7,
    0x55D1, 0x55E3, 0x55E4, 0x55EF, 0x55DA, 0x55E1, 0x55C5, 0x55C6, 0x55E5,
    0x55C9, 0x5712, 0x5713, 0x585E, 0x5851, 0x5858, 0x5857, 0x585A, 0x5854,
    0x586B, 0x584C, 0x586D, 0x584A, 0x5862, 0x5852, 0x584B, 0x5967, 0x5AC1,
    0x5AC9, 0x5ACC, 0x5ABE, 0x5ABD, 0x5ABC, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x5AB3, 0x5AC2,
    0x5AB2, 0x5D69, 0x5D6F, 0x5E4C, 0x5E79, 0x5EC9, 0x5EC8, 0x5F12, 0x5F59,
    0x5FAC, 0x5FAE, 0x611A, 0x610F, 0x6148, 0x611F, 0x60F3, 0x611B, 0x60F9,
    0x6101, 0x6108, 0x614E, 0x614C, 0x6144, 0x614D, 0x613E, 0x6134, 0x6127,
    0x610D, 0x6106, 0x6137, 0x6221, 0x6222, 0x6413, 0x643E, 0x641E, 0x642A,
    0x642D, 0x643D, 0x642C, 0x640F, 0x641C, 0x6414, 0x640D, 0x6436, 0x6416,
    0x6417, 0x6406, 0x656C, 0x659F, 0x65B0, 0x6697, 0x6689, 0x6687, 0x6688,
    0x6696, 0x6684, 0x6698, 0x668D, 0x6703, 0x6994, 0x696D, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x695A, 0x6977, 0x6960, 0x6954,
    0x6975, 0x6930, 0x6982, 0x694A, 0x6968, 0x696B, 0x695E, 0x6953, 0x6979,
    0x6986, 0x695D, 0x6963, 0x695B, 0x6B47, 0x6B72, 0x6BC0, 0x6BBF, 0x6BD3,
    0x6BFD, 0x6EA2, 0x6EAF, 0x6ED3, 0x6EB6, 0x6EC2, 0x6E90, 0x6E9D, 0x6EC7,
    0x6EC5, 0x6EA5, 0x6E98, 0x6EBC, 0x6EBA, 0x6EAB, 0x6ED1, 0x6E96, 0x6E9C,
    0x6EC4, 0x6ED4, 0x6EAA, 0x6EA7, 0x6EB4, 0x714E, 0x7159, 0x7169, 0x7164,
    0x7149, 0x7167, 0x715C, 0x716C, 0x7166, 0x714C, 0x7165, 0x715E, 0x7146,
    0x7168, 0x7156, 0x723A, 0x7252, 0x7337, 0x7345, 0x733F, 0x733E, 0x746F,
    0x745A, 0x7455, 0x745F, 0x745E, 0x7441, 0x743F, 0x7459, 0x745B, 0x745C,
    0x7576, 0x7578, 0x7600, 0x75F0, 0x7601, 0x75F2, 0x75F1, 0x75FA, 0x75FF,
    0x75F4, 0x75F3, 0x76DE, 0x76DF, 0x775B, 0x776B, 0x7766, 0x775E, 0x7763,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x7779, 0x776A, 0x776C, 0x775C, 0x7765, 0x7768, 0x7762,
    0x77EE, 0x788E, 0x78B0, 0x7897, 0x7898, 0x788C, 0x7889, 0x787C, 0x7891,
    0x7893, 0x787F, 0x797A, 0x797F, 0x7981, 0x842C, 0x79BD, 0x7A1C, 0x7A1A,
    0x7A20, 0x7A14, 0x7A1F, 0x7A1E, 0x7A9F, 0x7AA0, 0x7B77, 0x7BC0, 0x7B60,
    0x7B6E, 0x7B67, 0x7CB1, 0x7CB3, 0x7CB5, 0x7D93, 0x7D79, 0x7D91, 0x7D81,
    0x7D8F, 0x7D5B, 0x7F6E, 0x7F69, 0x7F6A, 0x7F72, 0x7FA9, 0x7FA8, 0x7FA4,
    0x8056, 0x8058, 0x8086, 0x8084, 0x8171, 0x8170, 0x8178, 0x8165, 0x816E,
    0x8173, 0x816B, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x8179, 0x817A, 0x8166, 0x8205, 0x8247, 0x8482, 0x8477, 0x843D, 0x8431,
    0x8475, 0x8466, 0x846B, 0x8449, 0x846C, 0x845B, 0x843C, 0x8435, 0x8461,
    0x8463, 0x8469, 0x846D, 0x8446, 0x865E, 0x865C, 0x865F, 0x86F9, 0x8713,
    0x8708, 0x8707, 0x8700, 0x86FE, 0x86FB, 0x8702, 0x8703, 0x8706, 0x870A,
    0x8859, 0x88DF, 0x88D4, 0x88D9, 0x88DC, 0x88D8, 0x88DD, 0x88E1, 0x88CA,
    0x88D5, 0x88D2, 0x899C, 0x89E3, 0x8A6B, 0x8A72, 0x8A73, 0x8A66, 0x8A69,
    0x8A70, 0x8A87, 0x8A7C, 0x8A63, 0x8AA0, 0x8A71, 0x8A85, 0x8A6D, 0x8A62,
    0x8A6E, 0x8A6C, 0x8A79, 0x8A7B, 0x8A3E, 0x8A68, 0x8C62, 0x8C8A, 0x8C89,
    0x8CCA, 0x8CC7, 0x8CC8, 0x8CC4, 0x8CB2, 0x8CC3, 0x8CC2, 0x8CC5, 0x8DE1,
    0x8DDF, 0x8DE8, 0x8DEF, 0x8DF3, 0x8DFA, 0x8DEA, 0x8DE4, 0x8DE6, 0x8EB2,
    0x8F03, 0x8F09, 0x8EFE, 0x8F0A, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x8F9F, 0x8FB2, 0x904B,
    0x904A, 0x9053, 0x9042, 0x9054, 0x903C, 0x9055, 0x9050, 0x9047, 0x904F,
    0x904E, 0x904D, 0x9051, 0x903E, 0x9041, 0x9112, 0x9117, 0x916C, 0x916A,
    0x9169, 0x91C9, 0x9237, 0x9257, 0x9238, 0x923D, 0x9240, 0x923E, 0x925B,
    0x924B, 0x9264, 0x9251, 0x9234, 0x9249, 0x924D, 0x9245, 0x9239, 0x923F,
    0x925A, 0x9598, 0x9698, 0x9694, 0x9695, 0x96CD, 0x96CB, 0x96C9, 0x96CA,
    0x96F7, 0x96FB, 0x96F9, 0x96F6, 0x9756, 0x9774, 0x9776, 0x9810, 0x9811,
    0x9813, 0x980A, 0x9812, 0x980C, 0x98FC, 0x98F4, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x98FD, 0x98FE, 0x99B3, 0x99B1, 0x99B4,
    0x9AE1, 0x9CE9, 0x9E82, 0x9F0E, 0x9F13, 0x9F20, 0x50E7, 0x50EE, 0x50E5,
    0x50D6, 0x50ED, 0x50DA, 0x50D5, 0x50CF, 0x50D1, 0x50F1, 0x50CE, 0x50E9,
    0x5162, 0x51F3, 0x5283, 0x5282, 0x5331, 0x53AD, 0x55FE, 0x5600, 0x561B,
    0x5617, 0x55FD, 0x5614, 0x5606, 0x5609, 0x560D, 0x560E, 0x55F7, 0x5616,
    0x561F, 0x5608, 0x5610, 0x55F6, 0x5718, 0x5716, 0x5875, 0x587E, 0x5883,
    0x5893, 0x588A, 0x5879, 0x5885, 0x587D, 0x58FD, 0x5925, 0x5922, 0x5924,
    0x596A, 0x5969, 0x5AE1, 0x5AE6, 0x5AE9, 0x5AD7, 0x5AD6, 0x5AD8, 0x5AE3,
    0x5B75, 0x5BDE, 0x5BE7, 0x5BE1, 0x5BE5, 0x5BE6, 0x5BE8, 0x5BE2, 0x5BE4,
    0x5BDF, 0x5C0D, 0x5C62, 0x5D84, 0x5D87, 0x5E5B, 0x5E63, 0x5E55, 0x5E57,
    0x5E54, 0x5ED3, 0x5ED6, 0x5F0A, 0x5F46, 0x5F70, 0x5FB9, 0x6147, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x613F, 0x614B, 0x6177, 0x6162, 0x6163, 0x615F, 0x615A, 0x6158,
    0x6175, 0x622A, 0x6487, 0x6458, 0x6454, 0x64A4, 0x6478, 0x645F, 0x647A,
    0x6451, 0x6467, 0x6434, 0x646D, 0x647B, 0x6572, 0x65A1, 0x65D7, 0x65D6,
    0x66A2, 0x66A8, 0x669D, 0x699C, 0x69A8, 0x6995, 0x69C1, 0x69AE, 0x69D3,
    0x69CB, 0x699B, 0x69B7, 0x69BB, 0x69AB, 0x69B4, 0x69D0, 0x69CD, 0x69AD,
    0x69CC, 0x69A6, 0x69C3, 0x69A3, 0x6B49, 0x6B4C, 0x6C33, 0x6F33, 0x6F14,
    0x6EFE, 0x6F13, 0x6EF4, 0x6F29, 0x6F3E, 0x6F20, 0x6F2C, 0x6F0F, 0x6F02,
    0x6F22, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6EFF,
    0x6EEF, 0x6F06, 0x6F31, 0x6F38, 0x6F32, 0x6F23, 0x6F15, 0x6F2B, 0x6F2F,
    0x6F88, 0x6F2A, 0x6EEC, 0x6F01, 0x6EF2, 0x6ECC, 0x6EF7, 0x7194, 0x7199,
    0x717D, 0x718A, 0x7184, 0x7192, 0x723E, 0x7292, 0x7296, 0x7344, 0x7350,
    0x7464, 0x7463, 0x746A, 0x7470, 0x746D, 0x7504, 0x7591, 0x7627, 0x760D,
    0x760B, 0x7609, 0x7613, 0x76E1, 0x76E3, 0x7784, 0x777D, 0x777F, 0x7761,
    0x78C1, 0x789F, 0x78A7, 0x78B3, 0x78A9, 0x78A3, 0x798E, 0x798F, 0x798D,
    0x7A2E, 0x7A31, 0x7AAA, 0x7AA9, 0x7AED, 0x7AEF, 0x7BA1, 0x7B95, 0x7B8B,
    0x7B75, 0x7B97, 0x7B9D, 0x7B94, 0x7B8F, 0x7BB8, 0x7B87, 0x7B84, 0x7CB9,
    0x7CBD, 0x7CBE, 0x7DBB, 0x7DB0, 0x7D9C, 0x7DBD, 0x7DBE, 0x7DA0, 0x7DCA,
    0x7DB4, 0x7DB2, 0x7DB1, 0x7DBA, 0x7DA2, 0x7DBF, 0x7DB5, 0x7DB8, 0x7DAD,
    0x7DD2, 0x7DC7, 0x7DAC, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x7F70, 0x7FE0, 0x7FE1, 0x7FDF,
    0x805E, 0x805A, 0x8087, 0x8150, 0x8180, 0x818F, 0x8188, 0x818A, 0x817F,
    0x8182, 0x81E7, 0x81FA, 0x8207, 0x8214, 0x821E, 0x824B, 0x84C9, 0x84BF,
    0x84C6, 0x84C4, 0x8499, 0x849E, 0x84B2, 0x849C, 0x84CB, 0x84B8, 0x84C0,
    0x84D3, 0x8490, 0x84BC, 0x84D1, 0x84CA, 0x873F, 0x871C, 0x873B, 0x8722,
    0x8725, 0x8734, 0x8718, 0x8755, 0x8737, 0x8729, 0x88F3, 0x8902, 0x88F4,
    0x88F9, 0x88F8, 0x88FD, 0x88E8, 0x891A, 0x88EF, 0x8AA6, 0x8A8C, 0x8A9E,
    0x8AA3, 0x8A8D, 0x8AA1, 0x8A93, 0x8AA4, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x8AAA, 0x8AA5, 0x8AA8, 0x8A98, 0x8A91, 0x8A9A,
    0x8AA7, 0x8C6A, 0x8C8D, 0x8C8C, 0x8CD3, 0x8CD1, 0x8CD2, 0x8D6B, 0x8D99,
    0x8D95, 0x8DFC, 0x8F14, 0x8F12, 0x8F15, 0x8F13, 0x8FA3, 0x9060, 0x9058,
    0x905C, 0x9063, 0x9059, 0x905E, 0x9062, 0x905D, 0x905B, 0x9119, 0x9118,
    0x911E, 0x9175, 0x9178, 0x9177, 0x9174, 0x9278, 0x9280, 0x9285, 0x9298,
    0x9296, 0x927B, 0x9293, 0x929C, 0x92A8, 0x927C, 0x9291, 0x95A1, 0x95A8,
    0x95A9, 0x95A3, 0x95A5, 0x95A4, 0x9699, 0x969C, 0x969B, 0x96CC, 0x96D2,
    0x9700, 0x977C, 0x9785, 0x97F6, 0x9817, 0x9818, 0x98AF, 0x98B1, 0x9903,
    0x9905, 0x990C, 0x9909, 0x99C1, 0x9AAF, 0x9AB0, 0x9AE6, 0x9B41, 0x9B42,
    0x9CF4, 0x9CF6, 0x9CF3, 0x9EBC, 0x9F3B, 0x9F4A, 0x5104, 0x5100, 0x50FB,
    0x50F5, 0x50F9, 0x5102, 0x5108, 0x5109, 0x5105, 0x51DC, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x5287, 0x5288, 0x5289, 0x528D, 0x528A, 0x52F0, 0x53B2, 0x562E, 0x563B,
    0x5639, 0x5632, 0x563F, 0x5634, 0x5629, 0x5653, 0x564E, 0x5657, 0x5674,
    0x5636, 0x562F, 0x5630, 0x5880, 0x589F, 0x589E, 0x58B3, 0x589C, 0x58AE,
    0x58A9, 0x58A6, 0x596D, 0x5B09, 0x5AFB, 0x5B0B, 0x5AF5, 0x5B0C, 0x5B08,
    0x5BEE, 0x5BEC, 0x5BE9, 0x5BEB, 0x5C64, 0x5C65, 0x5D9D, 0x5D94, 0x5E62,
    0x5E5F, 0x5E61, 0x5EE2, 0x5EDA, 0x5EDF, 0x5EDD, 0x5EE3, 0x5EE0, 0x5F48,
    0x5F71, 0x5FB7, 0x5FB5, 0x6176, 0x6167, 0x616E, 0x615D, 0x6155, 0x6182,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x617C, 0x6170,
    0x616B, 0x617E, 0x61A7, 0x6190, 0x61AB, 0x618E, 0x61AC, 0x619A, 0x61A4,
    0x6194, 0x61AE, 0x622E, 0x6469, 0x646F, 0x6479, 0x649E, 0x64B2, 0x6488,
    0x6490, 0x64B0, 0x64A5, 0x6493, 0x6495, 0x64A9, 0x6492, 0x64AE, 0x64AD,
    0x64AB, 0x649A, 0x64AC, 0x6499, 0x64A2, 0x64B3, 0x6575, 0x6577, 0x6578,
    0x66AE, 0x66AB, 0x66B4, 0x66B1, 0x6A23, 0x6A1F, 0x69E8, 0x6A01, 0x6A1E,
    0x6A19, 0x69FD, 0x6A21, 0x6A13, 0x6A0A, 0x69F3, 0x6A02, 0x6A05, 0x69ED,
    0x6A11, 0x6B50, 0x6B4E, 0x6BA4, 0x6BC5, 0x6BC6, 0x6F3F, 0x6F7C, 0x6F84,
    0x6F51, 0x6F66, 0x6F54, 0x6F86, 0x6F6D, 0x6F5B, 0x6F78, 0x6F6E, 0x6F8E,
    0x6F7A, 0x6F70, 0x6F64, 0x6F97, 0x6F58, 0x6ED5, 0x6F6F, 0x6F60, 0x6F5F,
    0x719F, 0x71AC, 0x71B1, 0x71A8, 0x7256, 0x729B, 0x734E, 0x7357, 0x7469,
    0x748B, 0x7483, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x747E, 0x7480, 0x757F, 0x7620, 0x7629,
    0x761F, 0x7624, 0x7626, 0x7621, 0x7622, 0x769A, 0x76BA, 0x76E4, 0x778E,
    0x7787, 0x778C, 0x7791, 0x778B, 0x78CB, 0x78C5, 0x78BA, 0x78CA, 0x78BE,
    0x78D5, 0x78BC, 0x78D0, 0x7A3F, 0x7A3C, 0x7A40, 0x7A3D, 0x7A37, 0x7A3B,
    0x7AAF, 0x7AAE, 0x7BAD, 0x7BB1, 0x7BC4, 0x7BB4, 0x7BC6, 0x7BC7, 0x7BC1,
    0x7BA0, 0x7BCC, 0x7CCA, 0x7DE0, 0x7DF4, 0x7DEF, 0x7DFB, 0x7DD8, 0x7DEC,
    0x7DDD, 0x7DE8, 0x7DE3, 0x7DDA, 0x7DDE, 0x7DE9, 0x7D9E, 0x7DD9, 0x7DF2,
    0x7DF9, 0x7F75, 0x7F77, 0x7FAF, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x7FE9, 0x8026, 0x819B, 0x819C, 0x819D, 0x81A0, 0x819A,
    0x8198, 0x8517, 0x853D, 0x851A, 0x84EE, 0x852C, 0x852D, 0x8513, 0x8511,
    0x8523, 0x8521, 0x8514, 0x84EC, 0x8525, 0x84FF, 0x8506, 0x8782, 0x8774,
    0x8776, 0x8760, 0x8766, 0x8778, 0x8768, 0x8759, 0x8757, 0x874C, 0x8753,
    0x885B, 0x885D, 0x8910, 0x8907, 0x8912, 0x8913, 0x8915, 0x890A, 0x8ABC,
    0x8AD2, 0x8AC7, 0x8AC4, 0x8A95, 0x8ACB, 0x8AF8, 0x8AB2, 0x8AC9, 0x8AC2,
    0x8ABF, 0x8AB0, 0x8AD6, 0x8ACD, 0x8AB6, 0x8AB9, 0x8ADB, 0x8C4C, 0x8C4E,
    0x8C6C, 0x8CE0, 0x8CDE, 0x8CE6, 0x8CE4, 0x8CEC, 0x8CED, 0x8CE2, 0x8CE3,
    0x8CDC, 0x8CEA, 0x8CE1, 0x8D6D, 0x8D9F, 0x8DA3, 0x8E2B, 0x8E10, 0x8E1D,
    0x8E22, 0x8E0F, 0x8E29, 0x8E1F, 0x8E21, 0x8E1E, 0x8EBA, 0x8F1D, 0x8F1B,
    0x8F1F, 0x8F29, 0x8F26, 0x8F2A, 0x8F1C, 0x8F1E, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x8F25,
    0x9069, 0x906E, 0x9068, 0x906D, 0x9077, 0x9130, 0x912D, 0x9127, 0x9131,
    0x9187, 0x9189, 0x918B, 0x9183, 0x92C5, 0x92BB, 0x92B7, 0x92EA, 0x92AC,
    0x92E4, 0x92C1, 0x92B3, 0x92BC, 0x92D2, 0x92C7, 0x92F0, 0x92B2, 0x95AD,
    0x95B1, 0x9704, 0x9706, 0x9707, 0x9709, 0x9760, 0x978D, 0x978B, 0x978F,
    0x9821, 0x982B, 0x981C, 0x98B3, 0x990A, 0x9913, 0x9912, 0x9918, 0x99DD,
    0x99D0, 0x99DF, 0x99DB, 0x99D1, 0x99D5, 0x99D2, 0x99D9, 0x9AB7, 0x9AEE,
    0x9AEF, 0x9B27, 0x9B45, 0x9B44, 0x9B77, 0x9B6F, 0x9D06, 0x9D09, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x9D03, 0x9EA9, 0x9EBE,
    0x9ECE, 0x58A8, 0x9F52, 0x5112, 0x5118, 0x5114, 0x5110, 0x5115, 0x5180,
    0x51AA, 0x51DD, 0x5291, 0x5293, 0x52F3, 0x5659, 0x566B, 0x5679, 0x5669,
    0x5664, 0x5678, 0x566A, 0x5668, 0x5665, 0x5671, 0x566F, 0x566C, 0x5662,
    0x5676, 0x58C1, 0x58BE, 0x58C7, 0x58C5, 0x596E, 0x5B1D, 0x5B34, 0x5B78,
    0x5BF0, 0x5C0E, 0x5F4A, 0x61B2, 0x6191, 0x61A9, 0x618A, 0x61CD, 0x61B6,
    0x61BE, 0x61CA, 0x61C8, 0x6230, 0x64C5, 0x64C1, 0x64CB, 0x64BB, 0x64BC,
    0x64DA, 0x64C4, 0x64C7, 0x64C2, 0x64CD, 0x64BF, 0x64D2, 0x64D4, 0x64BE,
    0x6574, 0x66C6, 0x66C9, 0x66B9, 0x66C4, 0x66C7, 0x66B8, 0x6A3D, 0x6A38,
    0x6A3A, 0x6A59, 0x6A6B, 0x6A58, 0x6A39, 0x6A44, 0x6A62, 0x6A61, 0x6A4B,
    0x6A47, 0x6A35, 0x6A5F, 0x6A48, 0x6B59, 0x6B77, 0x6C05, 0x6FC2, 0x6FB1,
    0x6FA1, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x6FC3, 0x6FA4, 0x6FC1, 0x6FA7, 0x6FB3, 0x6FC0,
    0x6FB9, 0x6FB6, 0x6FA6, 0x6FA0, 0x6FB4, 0x71BE, 0x71C9, 0x71D0, 0x71D2,
    0x71C8, 0x71D5, 0x71B9, 0x71CE, 0x71D9, 0x71DC, 0x71C3, 0x71C4, 0x7368,
    0x749C, 0x74A3, 0x7498, 0x749F, 0x749E, 0x74E2, 0x750C, 0x750D, 0x7634,
    0x7638, 0x763A, 0x76E7, 0x76E5, 0x77A0, 0x779E, 0x779F, 0x77A5, 0x78E8,
    0x78DA, 0x78EC, 0x78E7, 0x79A6, 0x7A4D, 0x7A4E, 0x7A46, 0x7A4C, 0x7A4B,
    0x7ABA, 0x7BD9, 0x7C11, 0x7BC9, 0x7BE4, 0x7BDB, 0x7BE1, 0x7BE9, 0x7BE6,
    0x7CD5, 0x7CD6, 0x7E0A, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x7E11, 0x7E08, 0x7E1B, 0x7E23, 0x7E1E, 0x7E1D, 0x7E09, 0x7E10,
    0x7F79, 0x7FB2, 0x7FF0, 0x7FF1, 0x7FEE, 0x8028, 0x81B3, 0x81A9, 0x81A8,
    0x81FB, 0x8208, 0x8258, 0x8259, 0x854A, 0x8559, 0x8548, 0x8568, 0x8569,
    0x8543, 0x8549, 0x856D, 0x856A, 0x855E, 0x8783, 0x879F, 0x879E, 0x87A2,
    0x878D, 0x8861, 0x892A, 0x8932, 0x8925, 0x892B, 0x8921, 0x89AA, 0x89A6,
    0x8AE6, 0x8AFA, 0x8AEB, 0x8AF1, 0x8B00, 0x8ADC, 0x8AE7, 0x8AEE, 0x8AFE,
    0x8B01, 0x8B02, 0x8AF7, 0x8AED, 0x8AF3, 0x8AF6, 0x8AFC, 0x8C6B, 0x8C6D,
    0x8C93, 0x8CF4, 0x8E44, 0x8E31, 0x8E34, 0x8E42, 0x8E39, 0x8E35, 0x8F3B,
    0x8F2F, 0x8F38, 0x8F33, 0x8FA8, 0x8FA6, 0x9075, 0x9074, 0x9078, 0x9072,
    0x907C, 0x907A, 0x9134, 0x9192, 0x9320, 0x9336, 0x92F8, 0x9333, 0x932F,
    0x9322, 0x92FC, 0x932B, 0x9304, 0x931A, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x9310, 0x9326,
    0x9321, 0x9315, 0x932E, 0x9319, 0x95BB, 0x96A7, 0x96A8, 0x96AA, 0x96D5,
    0x970E, 0x9711, 0x9716, 0x970D, 0x9713, 0x970F, 0x975B, 0x975C, 0x9766,
    0x9798, 0x9830, 0x9838, 0x983B, 0x9837, 0x982D, 0x9839, 0x9824, 0x9910,
    0x9928, 0x991E, 0x991B, 0x9921, 0x991A, 0x99ED, 0x99E2, 0x99F1, 0x9AB8,
    0x9ABC, 0x9AFB, 0x9AED, 0x9B28, 0x9B91, 0x9D15, 0x9D23, 0x9D26, 0x9D28,
    0x9D12, 0x9D1B, 0x9ED8, 0x9ED4, 0x9F8D, 0x9F9C, 0x512A, 0x511F, 0x5121,
    0x5132, 0x52F5, 0x568E, 0x5680, 0x5690, 0x5685, 0x5687, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x568F, 0x58D5, 0x58D3, 0x58D1,
    0x58CE, 0x5B30, 0x5B2A, 0x5B24, 0x5B7A, 0x5C37, 0x5C68, 0x5DBC, 0x5DBA,
    0x5DBD, 0x5DB8, 0x5E6B, 0x5F4C, 0x5FBD, 0x61C9, 0x61C2, 0x61C7, 0x61E6,
    0x61CB, 0x6232, 0x6234, 0x64CE, 0x64CA, 0x64D8, 0x64E0, 0x64F0, 0x64E6,
    0x64EC, 0x64F1, 0x64E2, 0x64ED, 0x6582, 0x6583, 0x66D9, 0x66D6, 0x6A80,
    0x6A94, 0x6A84, 0x6AA2, 0x6A9C, 0x6ADB, 0x6AA3, 0x6A7E, 0x6A97, 0x6A90,
    0x6AA0, 0x6B5C, 0x6BAE, 0x6BDA, 0x6C08, 0x6FD8, 0x6FF1, 0x6FDF, 0x6FE0,
    0x6FDB, 0x6FE4, 0x6FEB, 0x6FEF, 0x6F80, 0x6FEC, 0x6FE1, 0x6FE9, 0x6FD5,
    0x6FEE, 0x6FF0, 0x71E7, 0x71DF, 0x71EE, 0x71E6, 0x71E5, 0x71ED, 0x71EC,
    0x71F4, 0x71E0, 0x7235, 0x7246, 0x7370, 0x7372, 0x74A9, 0x74B0, 0x74A6,
    0x74A8, 0x7646, 0x7642, 0x764C, 0x76EA, 0x77B3, 0x77AA, 0x77B0, 0x77AC,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x77A7, 0x77AD, 0x77EF, 0x78F7, 0x78FA, 0x78F4, 0x78EF,
    0x7901, 0x79A7, 0x79AA, 0x7A57, 0x7ABF, 0x7C07, 0x7C0D, 0x7BFE, 0x7BF7,
    0x7C0C, 0x7BE0, 0x7CE0, 0x7CDC, 0x7CDE, 0x7CE2, 0x7CDF, 0x7CD9, 0x7CDD,
    0x7E2E, 0x7E3E, 0x7E46, 0x7E37, 0x7E32, 0x7E43, 0x7E2B, 0x7E3D, 0x7E31,
    0x7E45, 0x7E41, 0x7E34, 0x7E39, 0x7E48, 0x7E35, 0x7E3F, 0x7E2F, 0x7F44,
    0x7FF3, 0x7FFC, 0x8071, 0x8072, 0x8070, 0x806F, 0x8073, 0x81C6, 0x81C3,
    0x81BA, 0x81C2, 0x81C0, 0x81BF, 0x81BD, 0x81C9, 0x81BE, 0x81E8, 0x8209,
    0x8271, 0x85AA, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x8584, 0x857E, 0x859C, 0x8591, 0x8594, 0x85AF, 0x859B, 0x8587, 0x85A8,
    0x858A, 0x8667, 0x87C0, 0x87D1, 0x87B3, 0x87D2, 0x87C6, 0x87AB, 0x87BB,
    0x87BA, 0x87C8, 0x87CB, 0x893B, 0x8936, 0x8944, 0x8938, 0x893D, 0x89AC,
    0x8B0E, 0x8B17, 0x8B19, 0x8B1B, 0x8B0A, 0x8B20, 0x8B1D, 0x8B04, 0x8B10,
    0x8C41, 0x8C3F, 0x8C73, 0x8CFA, 0x8CFD, 0x8CFC, 0x8CF8, 0x8CFB, 0x8DA8,
    0x8E49, 0x8E4B, 0x8E48, 0x8E4A, 0x8F44, 0x8F3E, 0x8F42, 0x8F45, 0x8F3F,
    0x907F, 0x907D, 0x9084, 0x9081, 0x9082, 0x9080, 0x9139, 0x91A3, 0x919E,
    0x919C, 0x934D, 0x9382, 0x9328, 0x9375, 0x934A, 0x9365, 0x934B, 0x9318,
    0x937E, 0x936C, 0x935B, 0x9370, 0x935A, 0x9354, 0x95CA, 0x95CB, 0x95CC,
    0x95C8, 0x95C6, 0x96B1, 0x96B8, 0x96D6, 0x971C, 0x971E, 0x97A0, 0x97D3,
    0x9846, 0x98B6, 0x9935, 0x9A01, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x99FF, 0x9BAE, 0x9BAB,
    0x9BAA, 0x9BAD, 0x9D3B, 0x9D3F, 0x9E8B, 0x9ECF, 0x9EDE, 0x9EDC, 0x9EDD,
    0x9EDB, 0x9F3E, 0x9F4B, 0x53E2, 0x5695, 0x56AE, 0x58D9, 0x58D8, 0x5B38,
    0x5F5D, 0x61E3, 0x6233, 0x64F4, 0x64F2, 0x64FE, 0x6506, 0x64FA, 0x64FB,
    0x64F7, 0x65B7, 0x66DC, 0x6726, 0x6AB3, 0x6AAC, 0x6AC3, 0x6ABB, 0x6AB8,
    0x6AC2, 0x6AAE, 0x6AAF, 0x6B5F, 0x6B78, 0x6BAF, 0x7009, 0x700B, 0x6FFE,
    0x7006, 0x6FFA, 0x7011, 0x700F, 0x71FB, 0x71FC, 0x71FE, 0x71F8, 0x7377,
    0x7375, 0x74A7, 0x74BF, 0x7515, 0x7656, 0x7658, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x7652, 0x77BD, 0x77BF, 0x77BB, 0x77BC,
    0x790E, 0x79AE, 0x7A61, 0x7A62, 0x7A60, 0x7AC4, 0x7AC5, 0x7C2B, 0x7C27,
    0x7C2A, 0x7C1E, 0x7C23, 0x7C21, 0x7CE7, 0x7E54, 0x7E55, 0x7E5E, 0x7E5A,
    0x7E61, 0x7E52, 0x7E59, 0x7F48, 0x7FF9, 0x7FFB, 0x8077, 0x8076, 0x81CD,
    0x81CF, 0x820A, 0x85CF, 0x85A9, 0x85CD, 0x85D0, 0x85C9, 0x85B0, 0x85BA,
    0x85B9, 0x85A6, 0x87EF, 0x87EC, 0x87F2, 0x87E0, 0x8986, 0x89B2, 0x89F4,
    0x8B28, 0x8B39, 0x8B2C, 0x8B2B, 0x8C50, 0x8D05, 0x8E59, 0x8E63, 0x8E66,
    0x8E64, 0x8E5F, 0x8E55, 0x8EC0, 0x8F49, 0x8F4D, 0x9087, 0x9083, 0x9088,
    0x91AB, 0x91AC, 0x91D0, 0x9394, 0x938A, 0x9396, 0x93A2, 0x93B3, 0x93AE,
    0x93AC, 0x93B0, 0x9398, 0x939A, 0x9397, 0x95D4, 0x95D6, 0x95D0, 0x95D5,
    0x96E2, 0x96DC, 0x96D9, 0x96DB, 0x96DE, 0x9724, 0x97A3, 0x97A6, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x97AD, 0x97F9, 0x984D, 0x984F, 0x984C, 0x984E, 0x9853, 0x98BA,
    0x993E, 0x993F, 0x993D, 0x992E, 0x99A5, 0x9A0E, 0x9AC1, 0x9B03, 0x9B06,
    0x9B4F, 0x9B4E, 0x9B4D, 0x9BCA, 0x9BC9, 0x9BFD, 0x9BC8, 0x9BC0, 0x9D51,
    0x9D5D, 0x9D60, 0x9EE0, 0x9F15, 0x9F2C, 0x5133, 0x56A5, 0x58DE, 0x58DF,
    0x58E2, 0x5BF5, 0x9F90, 0x5EEC, 0x61F2, 0x61F7, 0x61F6, 0x61F5, 0x6500,
    0x650F, 0x66E0, 0x66DD, 0x6AE5, 0x6ADD, 0x6ADA, 0x6AD3, 0x701B, 0x701F,
    0x7028, 0x701A, 0x701D, 0x7015, 0x7018, 0x7206, 0x720D, 0x7258, 0x72A2,
    0x7378, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x737A,
    0x74BD, 0x74CA, 0x74E3, 0x7587, 0x7586, 0x765F, 0x7661, 0x77C7, 0x7919,
    0x79B1, 0x7A6B, 0x7A69, 0x7C3E, 0x7C3F, 0x7C38, 0x7C3D, 0x7C37, 0x7C40,
    0x7E6B, 0x7E6D, 0x7E79, 0x7E69, 0x7E6A, 0x7F85, 0x7E73, 0x7FB6, 0x7FB9,
    0x7FB8, 0x81D8, 0x85E9, 0x85DD, 0x85EA, 0x85D5, 0x85E4, 0x85E5, 0x85F7,
    0x87FB, 0x8805, 0x880D, 0x87F9, 0x87FE, 0x8960, 0x895F, 0x8956, 0x895E,
    0x8B41, 0x8B5C, 0x8B58, 0x8B49, 0x8B5A, 0x8B4E, 0x8B4F, 0x8B46, 0x8B59,
    0x8D08, 0x8D0A, 0x8E7C, 0x8E72, 0x8E87, 0x8E76, 0x8E6C, 0x8E7A, 0x8E74,
    0x8F54, 0x8F4E, 0x8FAD, 0x908A, 0x908B, 0x91B1, 0x91AE, 0x93E1, 0x93D1,
    0x93DF, 0x93C3, 0x93C8, 0x93DC, 0x93DD, 0x93D6, 0x93E2, 0x93CD, 0x93D8,
    0x93E4, 0x93D7, 0x93E8, 0x95DC, 0x96B4, 0x96E3, 0x972A, 0x9727, 0x9761,
    0x97DC, 0x97FB, 0x985E, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x9858, 0x985B, 0x98BC, 0x9945,
    0x9949, 0x9A16, 0x9A19, 0x9B0D, 0x9BE8, 0x9BE7, 0x9BD6, 0x9BDB, 0x9D89,
    0x9D61, 0x9D72, 0x9D6A, 0x9D6C, 0x9E92, 0x9E97, 0x9E93, 0x9EB4, 0x52F8,
    0x56A8, 0x56B7, 0x56B6, 0x56B4, 0x56BC, 0x58E4, 0x5B40, 0x5B43, 0x5B7D,
    0x5BF6, 0x5DC9, 0x61F8, 0x61FA, 0x6518, 0x6514, 0x6519, 0x66E6, 0x6727,
    0x6AEC, 0x703E, 0x7030, 0x7032, 0x7210, 0x737B, 0x74CF, 0x7662, 0x7665,
    0x7926, 0x792A, 0x792C, 0x792B, 0x7AC7, 0x7AF6, 0x7C4C, 0x7C43, 0x7C4D,
    0x7CEF, 0x7CF0, 0x8FAE, 0x7E7D, 0x7E7C, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x7E82, 0x7F4C, 0x8000, 0x81DA, 0x8266, 0x85FB,
    0x85F9, 0x8611, 0x85FA, 0x8606, 0x860B, 0x8607, 0x860A, 0x8814, 0x8815,
    0x8964, 0x89BA, 0x89F8, 0x8B70, 0x8B6C, 0x8B66, 0x8B6F, 0x8B5F, 0x8B6B,
    0x8D0F, 0x8D0D, 0x8E89, 0x8E81, 0x8E85, 0x8E82, 0x91B4, 0x91CB, 0x9418,
    0x9403, 0x93FD, 0x95E1, 0x9730, 0x98C4, 0x9952, 0x9951, 0x99A8, 0x9A2B,
    0x9A30, 0x9A37, 0x9A35, 0x9C13, 0x9C0D, 0x9E79, 0x9EB5, 0x9EE8, 0x9F2F,
    0x9F5F, 0x9F63, 0x9F61, 0x5137, 0x5138, 0x56C1, 0x56C0, 0x56C2, 0x5914,
    0x5C6C, 0x5DCD, 0x61FC, 0x61FE, 0x651D, 0x651C, 0x6595, 0x66E9, 0x6AFB,
    0x6B04, 0x6AFA, 0x6BB2, 0x704C, 0x721B, 0x72A7, 0x74D6, 0x74D4, 0x7669,
    0x77D3, 0x7C50, 0x7E8F, 0x7E8C, 0x7FBC, 0x8617, 0x862D, 0x861A, 0x8823,
    0x8822, 0x8821, 0x881F, 0x896A, 0x896C, 0x89BD, 0x8B74, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x8B77, 0x8B7D, 0x8D13, 0x8E8A, 0x8E8D, 0x8E8B, 0x8F5F, 0x8FAF, 0x91BA,
    0x942E, 0x9433, 0x9435, 0x943A, 0x9438, 0x9432, 0x942B, 0x95E2, 0x9738,
    0x9739, 0x9732, 0x97FF, 0x9867, 0x9865, 0x9957, 0x9A45, 0x9A43, 0x9A40,
    0x9A3E, 0x9ACF, 0x9B54, 0x9B51, 0x9C2D, 0x9C25, 0x9DAF, 0x9DB4, 0x9DC2,
    0x9DB8, 0x9E9D, 0x9EEF, 0x9F19, 0x9F5C, 0x9F66, 0x9F67, 0x513C, 0x513B,
    0x56C8, 0x56CA, 0x56C9, 0x5B7F, 0x5DD4, 0x5DD2, 0x5F4E, 0x61FF, 0x6524,
    0x6B0A, 0x6B61, 0x7051, 0x7058, 0x7380, 0x74E4, 0x758A, 0x766E, 0x766C,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x79B3, 0x7C60,
    0x7C5F, 0x807E, 0x807D, 0x81DF, 0x8972, 0x896F, 0x89FC, 0x8B80, 0x8D16,
    0x8D17, 0x8E91, 0x8E93, 0x8F61, 0x9148, 0x9444, 0x9451, 0x9452, 0x973D,
    0x973E, 0x97C3, 0x97C1, 0x986B, 0x9955, 0x9A55, 0x9A4D, 0x9AD2, 0x9B1A,
    0x9C49, 0x9C31, 0x9C3E, 0x9C3B, 0x9DD3, 0x9DD7, 0x9F34, 0x9F6C, 0x9F6A,
    0x9F94, 0x56CC, 0x5DD6, 0x6200, 0x6523, 0x652B, 0x652A, 0x66EC, 0x6B10,
    0x74DA, 0x7ACA, 0x7C64, 0x7C63, 0x7C65, 0x7E93, 0x7E96, 0x7E94, 0x81E2,
    0x8638, 0x863F, 0x8831, 0x8B8A, 0x9090, 0x908F, 0x9463, 0x9460, 0x9464,
    0x9768, 0x986F, 0x995C, 0x9A5A, 0x9A5B, 0x9A57, 0x9AD3, 0x9AD4, 0x9AD1,
    0x9C54, 0x9C57, 0x9C56, 0x9DE5, 0x9E9F, 0x9EF4, 0x56D1, 0x58E9, 0x652C,
    0x705E, 0x7671, 0x7672, 0x77D7, 0x7F50, 0x7F88, 0x8836, 0x8839, 0x8862,
    0x8B93, 0x8B92, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x8B96, 0x8277, 0x8D1B, 0x91C0, 0x946A,
    0x9742, 0x9748, 0x9744, 0x97C6, 0x9870, 0x9A5F, 0x9B22, 0x9B58, 0x9C5F,
    0x9DF9, 0x9DFA, 0x9E7C, 0x9E7D, 0x9F07, 0x9F77, 0x9F72, 0x5EF3, 0x6B16,
    0x7063, 0x7C6C, 0x7C6E, 0x883B, 0x89C0, 0x8EA1, 0x91C1, 0x9472, 0x9470,
    0x9871, 0x995E, 0x9AD6, 0x9B23, 0x9ECC, 0x7064, 0x77DA, 0x8B9A, 0x9477,
    0x97C9, 0x9A62, 0x9A65, 0x7E9C, 0x8B9C, 0x8EAA, 0x91C5, 0x947D, 0x947E,
    0x947C, 0x9C77, 0x9C78, 0x9EF7, 0x8C54, 0x947F, 0x9E1A, 0x7228, 0x9A6A,
    0x9B31, 0x9E1B, 0x9E1E, 0x7C72, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x30FE, 0x309D, 0x309E, 0x3005, 0x3041, 0x3042, 0x3043,
    0x3044, 0x3045, 0x3046, 0x3047, 0x3048, 0x3049, 0x304A, 0x304B, 0x304C,
    0x304D, 0x304E, 0x304F, 0x3050, 0x3051, 0x3052, 0x3053, 0x3054, 0x3055,
    0x3056, 0x3057, 0x3058, 0x3059, 0x305A, 0x305B, 0x305C, 0x305D, 0x305E,
    0x305F, 0x3060, 0x3061, 0x3062, 0x3063, 0x3064, 0x3065, 0x3066, 0x3067,
    0x3068, 0x3069, 0x306A, 0x306B, 0x306C, 0x306D, 0x306E, 0x306F, 0x3070,
    0x3071, 0x3072, 0x3073, 0x3074, 0x3075, 0x3076, 0x3077, 0x3078, 0x3079,
    0x307A, 0x307B, 0x307C, 0x307D, 0x307E, 0x307F, 0x3080, 0x3081, 0x3082,
    0x3083, 0x3084, 0x3085, 0x3086, 0x3087, 0x3088, 0x3089, 0x308A, 0x308B,
    0x308C, 0x308D, 0x308E, 0x308F, 0x3090, 0x3091, 0x3092, 0x3093, 0x30A1,
    0x30A2, 0x30A3, 0x30A4, 0x30A5, 0x30A6, 0x30A7, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x30A8,
    0x30A9, 0x30AA, 0x30AB, 0x30AC, 0x30AD, 0x30AE, 0x30AF, 0x30B0, 0x30B1,
    0x30B2, 0x30B3, 0x30B4, 0x30B5, 0x30B6, 0x30B7, 0x30B8, 0x30B9, 0x30BA,
    0x30BB, 0x30BC, 0x30BD, 0x30BE, 0x30BF, 0x30C0, 0x30C1, 0x30C2, 0x30C3,
    0x30C4, 0x30C5, 0x30C6, 0x30C7, 0x30C8, 0x30C9, 0x30CA, 0x30CB, 0x30CC,
    0x30CD, 0x30CE, 0x30CF, 0x30D0, 0x30D1, 0x30D2, 0x30D3, 0x30D4, 0x30D5,
    0x30D6, 0x30D7, 0x30D8, 0x30D9, 0x30DA, 0x30DB, 0x30DC, 0x30DD, 0x30DE,
    0x30DF, 0x30E0, 0x30E1, 0x30E2, 0x30E3, 0x30E4, 0x30E5, 0x30E6, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x30E7, 0x30E8, 0x30E9,
    0x30EA, 0x30EB, 0x30EC, 0x30ED, 0x30EE, 0x30EF, 0x30F0, 0x30F1, 0x30F2,
    0x30F3, 0x30F4, 0x30F5, 0x30F6, 0x0414, 0x0415, 0x0401, 0x0416, 0x0417,
    0x0418, 0x0419, 0x041A, 0x041B, 0x041C, 0x0423, 0x0424, 0x0425, 0x0426,
    0x0427, 0x0428, 0x0429, 0x042A, 0x042B, 0x042C, 0x042D, 0x042E, 0x042F,
    0x0430, 0x0431, 0x0432, 0x0433, 0x0434, 0x0435, 0x0451, 0x0436, 0x0437,
    0x0438, 0x0439, 0x043A, 0x043B, 0x043C, 0x043D, 0x043E, 0x043F, 0x0440,
    0x0441, 0x0442, 0x0443, 0x0444, 0x0445, 0x0446, 0x0447, 0x0448, 0x0449,
    0x044A, 0x044B, 0x044C, 0x044D, 0x044E, 0x044F, 0x2460, 0x2461, 0x2462,
    0x2463, 0x2464, 0x2465, 0x2466, 0x2467, 0x2468, 0x2469, 0x2474, 0x2475,
    0x2476, 0x2477, 0x2478, 0x2479, 0x247A, 0x247B, 0x247C, 0x247D};

/* page 1 0xC940-0xF9DC */
static const uint16_t tab_big5_uni1[] = {
    0x4E42, 0x4E5C, 0x51F5, 0x531A, 0x5382, 0x4E07, 0x4E0C, 0x4E47, 0x4E8D,
    0x56D7, 0xFA0C, 0x5C6E, 0x5F73, 0x4E0F, 0x5187, 0x4E0E, 0x4E2E, 0x4E93,
    0x4EC2, 0x4EC9, 0x4EC8, 0x5198, 0x52FC, 0x536C, 0x53B9, 0x5720, 0x5903,
    0x592C, 0x5C10, 0x5DFF, 0x65E1, 0x6BB3, 0x6BCC, 0x6C14, 0x723F, 0x4E31,
    0x4E3C, 0x4EE8, 0x4EDC, 0x4EE9, 0x4EE1, 0x4EDD, 0x4EDA, 0x520C, 0x531C,
    0x534C, 0x5722, 0x5723, 0x5917, 0x592F, 0x5B81, 0x5B84, 0x5C12, 0x5C3B,
    0x5C74, 0x5C73, 0x5E04, 0x5E80, 0x5E82, 0x5FC9, 0x6209, 0x6250, 0x6C15,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x6C36, 0x6C43,
    0x6C3F, 0x6C3B, 0x72AE, 0x72B0, 0x738A, 0x79B8, 0x808A, 0x961E, 0x4F0E,
    0x4F18, 0x4F2C, 0x4EF5, 0x4F14, 0x4EF1, 0x4F00, 0x4EF7, 0x4F08, 0x4F1D,
    0x4F02, 0x4F05, 0x4F22, 0x4F13, 0x4F04, 0x4EF4, 0x4F12, 0x51B1, 0x5213,
    0x5209, 0x5210, 0x52A6, 0x5322, 0x531F, 0x534D, 0x538A, 0x5407, 0x56E1,
    0x56DF, 0x572E, 0x572A, 0x5734, 0x593C, 0x5980, 0x597C, 0x5985, 0x597B,
    0x597E, 0x5977, 0x597F, 0x5B56, 0x5C15, 0x5C25, 0x5C7C, 0x5C7A, 0x5C7B,
    0x5C7E, 0x5DDF, 0x5E75, 0x5E84, 0x5F02, 0x5F1A, 0x5F74, 0x5FD5, 0x5FD4,
    0x5FCF, 0x625C, 0x625E, 0x6264, 0x6261, 0x6266, 0x6262, 0x6259, 0x6260,
    0x625A, 0x6265, 0x65EF, 0x65EE, 0x673E, 0x6739, 0x6738, 0x673B, 0x673A,
    0x673F, 0x673C, 0x6733, 0x6C18, 0x6C46, 0x6C52, 0x6C5C, 0x6C4F, 0x6C4A,
    0x6C54, 0x6C4B, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x6C4C, 0x7071, 0x725E, 0x72B4, 0x72B5,
    0x738E, 0x752A, 0x767F, 0x7A75, 0x7F51, 0x8278, 0x827C, 0x8280, 0x827D,
    0x827F, 0x864D, 0x897E, 0x9099, 0x9097, 0x9098, 0x909B, 0x9094, 0x9622,
    0x9624, 0x9620, 0x9623, 0x4F56, 0x4F3B, 0x4F62, 0x4F49, 0x4F53, 0x4F64,
    0x4F3E, 0x4F67, 0x4F52, 0x4F5F, 0x4F41, 0x4F58, 0x4F2D, 0x4F33, 0x4F3F,
    0x4F61, 0x518F, 0x51B9, 0x521C, 0x521E, 0x5221, 0x52AD, 0x52AE, 0x5309,
    0x5363, 0x5372, 0x538E, 0x538F, 0x5430, 0x5437, 0x542A, 0x5454, 0x5445,
    0x5419, 0x541C, 0x5425, 0x5418, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x543D, 0x544F, 0x5441, 0x5428, 0x5424, 0x5447, 0x56EE,
    0x56E7, 0x56E5, 0x5741, 0x5745, 0x574C, 0x5749, 0x574B, 0x5752, 0x5906,
    0x5940, 0x59A6, 0x5998, 0x59A0, 0x5997, 0x598E, 0x59A2, 0x5990, 0x598F,
    0x59A7, 0x59A1, 0x5B8E, 0x5B92, 0x5C28, 0x5C2A, 0x5C8D, 0x5C8F, 0x5C88,
    0x5C8B, 0x5C89, 0x5C92, 0x5C8A, 0x5C86, 0x5C93, 0x5C95, 0x5DE0, 0x5E0A,
    0x5E0E, 0x5E8B, 0x5E89, 0x5E8C, 0x5E88, 0x5E8D, 0x5F05, 0x5F1D, 0x5F78,
    0x5F76, 0x5FD2, 0x5FD1, 0x5FD0, 0x5FED, 0x5FE8, 0x5FEE, 0x5FF3, 0x5FE1,
    0x5FE4, 0x5FE3, 0x5FFA, 0x5FEF, 0x5FF7, 0x5FFB, 0x6000, 0x5FF4, 0x623A,
    0x6283, 0x628C, 0x628E, 0x628F, 0x6294, 0x6287, 0x6271, 0x627B, 0x627A,
    0x6270, 0x6281, 0x6288, 0x6277, 0x627D, 0x6272, 0x6274, 0x6537, 0x65F0,
    0x65F4, 0x65F3, 0x65F2, 0x65F5, 0x6745, 0x6747, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6759,
    0x6755, 0x674C, 0x6748, 0x675D, 0x674D, 0x675A, 0x674B, 0x6BD0, 0x6C19,
    0x6C1A, 0x6C78, 0x6C67, 0x6C6B, 0x6C84, 0x6C8B, 0x6C8F, 0x6C71, 0x6C6F,
    0x6C69, 0x6C9A, 0x6C6D, 0x6C87, 0x6C95, 0x6C9C, 0x6C66, 0x6C73, 0x6C65,
    0x6C7B, 0x6C8E, 0x7074, 0x707A, 0x7263, 0x72BF, 0x72BD, 0x72C3, 0x72C6,
    0x72C1, 0x72BA, 0x72C5, 0x7395, 0x7397, 0x7393, 0x7394, 0x7392, 0x753A,
    0x7539, 0x7594, 0x7595, 0x7681, 0x793D, 0x8034, 0x8095, 0x8099, 0x8090,
    0x8092, 0x809C, 0x8290, 0x828F, 0x8285, 0x828E, 0x8291, 0x8293, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x828A, 0x8283, 0x8284,
    0x8C78, 0x8FC9, 0x8FBF, 0x909F, 0x90A1, 0x90A5, 0x909E, 0x90A7, 0x90A0,
    0x9630, 0x9628, 0x962F, 0x962D, 0x4E33, 0x4F98, 0x4F7C, 0x4F85, 0x4F7D,
    0x4F80, 0x4F87, 0x4F76, 0x4F74, 0x4F89, 0x4F84, 0x4F77, 0x4F4C, 0x4F97,
    0x4F6A, 0x4F9A, 0x4F79, 0x4F81, 0x4F78, 0x4F90, 0x4F9C, 0x4F94, 0x4F9E,
    0x4F92, 0x4F82, 0x4F95, 0x4F6B, 0x4F6E, 0x519E, 0x51BC, 0x51BE, 0x5235,
    0x5232, 0x5233, 0x5246, 0x5231, 0x52BC, 0x530A, 0x530B, 0x533C, 0x5392,
    0x5394, 0x5487, 0x547F, 0x5481, 0x5491, 0x5482, 0x5488, 0x546B, 0x547A,
    0x547E, 0x5465, 0x546C, 0x5474, 0x5466, 0x548D, 0x546F, 0x5461, 0x5460,
    0x5498, 0x5463, 0x5467, 0x5464, 0x56F7, 0x56F9, 0x576F, 0x5772, 0x576D,
    0x576B, 0x5771, 0x5770, 0x5776, 0x5780, 0x5775, 0x577B, 0x5773, 0x5774,
    0x5762, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x5768, 0x577D, 0x590C, 0x5945, 0x59B5, 0x59BA,
    0x59CF, 0x59CE, 0x59B2, 0x59CC, 0x59C1, 0x59B6, 0x59BC, 0x59C3, 0x59D6,
    0x59B1, 0x59BD, 0x59C0, 0x59C8, 0x59B4, 0x59C7, 0x5B62, 0x5B65, 0x5B93,
    0x5B95, 0x5C44, 0x5C47, 0x5CAE, 0x5CA4, 0x5CA0, 0x5CB5, 0x5CAF, 0x5CA8,
    0x5CAC, 0x5C9F, 0x5CA3, 0x5CAD, 0x5CA2, 0x5CAA, 0x5CA7, 0x5C9D, 0x5CA5,
    0x5CB6, 0x5CB0, 0x5CA6, 0x5E17, 0x5E14, 0x5E19, 0x5F28, 0x5F22, 0x5F23,
    0x5F24, 0x5F54, 0x5F82, 0x5F7E, 0x5F7D, 0x5FDE, 0x5FE5, 0x602D, 0x6026,
    0x6019, 0x6032, 0x600B, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x6034, 0x600A, 0x6017, 0x6033, 0x601A, 0x601E, 0x602C, 0x6022,
    0x600D, 0x6010, 0x602E, 0x6013, 0x6011, 0x600C, 0x6009, 0x601C, 0x6214,
    0x623D, 0x62AD, 0x62B4, 0x62D1, 0x62BE, 0x62AA, 0x62B6, 0x62CA, 0x62AE,
    0x62B3, 0x62AF, 0x62BB, 0x62A9, 0x62B0, 0x62B8, 0x653D, 0x65A8, 0x65BB,
    0x6609, 0x65FC, 0x6604, 0x6612, 0x6608, 0x65FB, 0x6603, 0x660B, 0x660D,
    0x6605, 0x65FD, 0x6611, 0x6610, 0x66F6, 0x670A, 0x6785, 0x676C, 0x678E,
    0x6792, 0x6776, 0x677B, 0x6798, 0x6786, 0x6784, 0x6774, 0x678D, 0x678C,
    0x677A, 0x679F, 0x6791, 0x6799, 0x6783, 0x677D, 0x6781, 0x6778, 0x6779,
    0x6794, 0x6B25, 0x6B80, 0x6B7E, 0x6BDE, 0x6C1D, 0x6C93, 0x6CEC, 0x6CEB,
    0x6CEE, 0x6CD9, 0x6CB6, 0x6CD4, 0x6CAD, 0x6CE7, 0x6CB7, 0x6CD0, 0x6CC2,
    0x6CBA, 0x6CC3, 0x6CC6, 0x6CED, 0x6CF2, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x6CD2, 0x6CDD,
    0x6CB4, 0x6C8A, 0x6C9D, 0x6C80, 0x6CDE, 0x6CC0, 0x6D30, 0x6CCD, 0x6CC7,
    0x6CB0, 0x6CF9, 0x6CCF, 0x6CE9, 0x6CD1, 0x7094, 0x7098, 0x7085, 0x7093,
    0x7086, 0x7084, 0x7091, 0x7096, 0x7082, 0x709A, 0x7083, 0x726A, 0x72D6,
    0x72CB, 0x72D8, 0x72C9, 0x72DC, 0x72D2, 0x72D4, 0x72DA, 0x72CC, 0x72D1,
    0x73A4, 0x73A1, 0x73AD, 0x73A6, 0x73A2, 0x73A0, 0x73AC, 0x739D, 0x74DD,
    0x74E8, 0x753F, 0x7540, 0x753E, 0x758C, 0x7598, 0x76AF, 0x76F3, 0x76F1,
    0x76F0, 0x76F5, 0x77F8, 0x77FC, 0x77F9, 0x77FB, 0x77FA, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x77F7, 0x7942, 0x793F, 0x79C5,
    0x7A78, 0x7A7B, 0x7AFB, 0x7C75, 0x7CFD, 0x8035, 0x808F, 0x80AE, 0x80A3,
    0x80B8, 0x80B5, 0x80AD, 0x8220, 0x82A0, 0x82C0, 0x82AB, 0x829A, 0x8298,
    0x829B, 0x82B5, 0x82A7, 0x82AE, 0x82BC, 0x829E, 0x82BA, 0x82B4, 0x82A8,
    0x82A1, 0x82A9, 0x82C2, 0x82A4, 0x82C3, 0x82B6, 0x82A2, 0x8670, 0x866F,
    0x866D, 0x866E, 0x8C56, 0x8FD2, 0x8FCB, 0x8FD3, 0x8FCD, 0x8FD6, 0x8FD5,
    0x8FD7, 0x90B2, 0x90B4, 0x90AF, 0x90B3, 0x90B0, 0x9639, 0x963D, 0x963C,
    0x963A, 0x9643, 0x4FCD, 0x4FC5, 0x4FD3, 0x4FB2, 0x4FC9, 0x4FCB, 0x4FC1,
    0x4FD4, 0x4FDC, 0x4FD9, 0x4FBB, 0x4FB3, 0x4FDB, 0x4FC7, 0x4FD6, 0x4FBA,
    0x4FC0, 0x4FB9, 0x4FEC, 0x5244, 0x5249, 0x52C0, 0x52C2, 0x533D, 0x537C,
    0x5397, 0x5396, 0x5399, 0x5398, 0x54BA, 0x54A1, 0x54AD, 0x54A5, 0x54CF,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x54C3, 0x830D, 0x54B7, 0x54AE, 0x54D6, 0x54B6, 0x54C5,
    0x54C6, 0x54A0, 0x5470, 0x54BC, 0x54A2, 0x54BE, 0x5472, 0x54DE, 0x54B0,
    0x57B5, 0x579E, 0x579F, 0x57A4, 0x578C, 0x5797, 0x579D, 0x579B, 0x5794,
    0x5798, 0x578F, 0x5799, 0x57A5, 0x579A, 0x5795, 0x58F4, 0x590D, 0x5953,
    0x59E1, 0x59DE, 0x59EE, 0x5A00, 0x59F1, 0x59DD, 0x59FA, 0x59FD, 0x59FC,
    0x59F6, 0x59E4, 0x59F2, 0x59F7, 0x59DB, 0x59E9, 0x59F3, 0x59F5, 0x59E0,
    0x59FE, 0x59F4, 0x59ED, 0x5BA8, 0x5C4C, 0x5CD0, 0x5CD8, 0x5CCC, 0x5CD7,
    0x5CCB, 0x5CDB, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x5CDE, 0x5CDA, 0x5CC9, 0x5CC7, 0x5CCA, 0x5CD6, 0x5CD3, 0x5CD4, 0x5CCF,
    0x5CC8, 0x5CC6, 0x5CCE, 0x5CDF, 0x5CF8, 0x5DF9, 0x5E21, 0x5E22, 0x5E23,
    0x5E20, 0x5E24, 0x5EB0, 0x5EA4, 0x5EA2, 0x5E9B, 0x5EA3, 0x5EA5, 0x5F07,
    0x5F2E, 0x5F56, 0x5F86, 0x6037, 0x6039, 0x6054, 0x6072, 0x605E, 0x6045,
    0x6053, 0x6047, 0x6049, 0x605B, 0x604C, 0x6040, 0x6042, 0x605F, 0x6024,
    0x6044, 0x6058, 0x6066, 0x606E, 0x6242, 0x6243, 0x62CF, 0x630D, 0x630B,
    0x62F5, 0x630E, 0x6303, 0x62EB, 0x62F9, 0x630F, 0x630C, 0x62F8, 0x62F6,
    0x6300, 0x6313, 0x6314, 0x62FA, 0x6315, 0x62FB, 0x62F0, 0x6541, 0x6543,
    0x65AA, 0x65BF, 0x6636, 0x6621, 0x6632, 0x6635, 0x661C, 0x6626, 0x6622,
    0x6633, 0x662B, 0x663A, 0x661D, 0x6634, 0x6639, 0x662E, 0x670F, 0x6710,
    0x67C1, 0x67F2, 0x67C8, 0x67BA, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x67DC, 0x67BB, 0x67F8,
    0x67D8, 0x67C0, 0x67B7, 0x67C5, 0x67EB, 0x67E4, 0x67DF, 0x67B5, 0x67CD,
    0x67B3, 0x67F7, 0x67F6, 0x67EE, 0x67E3, 0x67C2, 0x67B9, 0x67CE, 0x67E7,
    0x67F0, 0x67B2, 0x67FC, 0x67C6, 0x67ED, 0x67CC, 0x67AE, 0x67E6, 0x67DB,
    0x67FA, 0x67C9, 0x67CA, 0x67C3, 0x67EA, 0x67CB, 0x6B28, 0x6B82, 0x6B84,
    0x6BB6, 0x6BD6, 0x6BD8, 0x6BE0, 0x6C20, 0x6C21, 0x6D28, 0x6D34, 0x6D2D,
    0x6D1F, 0x6D3C, 0x6D3F, 0x6D12, 0x6D0A, 0x6CDA, 0x6D33, 0x6D04, 0x6D19,
    0x6D3A, 0x6D1A, 0x6D11, 0x6D00, 0x6D1D, 0x6D42, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x6D01, 0x6D18, 0x6D37, 0x6D03, 0x6D0F,
    0x6D40, 0x6D07, 0x6D20, 0x6D2C, 0x6D08, 0x6D22, 0x6D09, 0x6D10, 0x70B7,
    0x709F, 0x70BE, 0x70B1, 0x70B0, 0x70A1, 0x70B4, 0x70B5, 0x70A9, 0x7241,
    0x7249, 0x724A, 0x726C, 0x7270, 0x7273, 0x726E, 0x72CA, 0x72E4, 0x72E8,
    0x72EB, 0x72DF, 0x72EA, 0x72E6, 0x72E3, 0x7385, 0x73CC, 0x73C2, 0x73C8,
    0x73C5, 0x73B9, 0x73B6, 0x73B5, 0x73B4, 0x73EB, 0x73BF, 0x73C7, 0x73BE,
    0x73C3, 0x73C6, 0x73B8, 0x73CB, 0x74EC, 0x74EE, 0x752E, 0x7547, 0x7548,
    0x75A7, 0x75AA, 0x7679, 0x76C4, 0x7708, 0x7703, 0x7704, 0x7705, 0x770A,
    0x76F7, 0x76FB, 0x76FA, 0x77E7, 0x77E8, 0x7806, 0x7811, 0x7812, 0x7805,
    0x7810, 0x780F, 0x780E, 0x7809, 0x7803, 0x7813, 0x794A, 0x794C, 0x794B,
    0x7945, 0x7944, 0x79D5, 0x79CD, 0x79CF, 0x79D6, 0x79CE, 0x7A80, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x7A7E, 0x7AD1, 0x7B00, 0x7B01, 0x7C7A, 0x7C78, 0x7C79, 0x7C7F,
    0x7C80, 0x7C81, 0x7D03, 0x7D08, 0x7D01, 0x7F58, 0x7F91, 0x7F8D, 0x7FBE,
    0x8007, 0x800E, 0x800F, 0x8014, 0x8037, 0x80D8, 0x80C7, 0x80E0, 0x80D1,
    0x80C8, 0x80C2, 0x80D0, 0x80C5, 0x80E3, 0x80D9, 0x80DC, 0x80CA, 0x80D5,
    0x80C9, 0x80CF, 0x80D7, 0x80E6, 0x80CD, 0x81FF, 0x8221, 0x8294, 0x82D9,
    0x82FE, 0x82F9, 0x8307, 0x82E8, 0x8300, 0x82D5, 0x833A, 0x82EB, 0x82D6,
    0x82F4, 0x82EC, 0x82E1, 0x82F2, 0x82F5, 0x830C, 0x82FB, 0x82F6, 0x82F0,
    0x82EA, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x82E4,
    0x82E0, 0x82FA, 0x82F3, 0x82ED, 0x8677, 0x8674, 0x867C, 0x8673, 0x8841,
    0x884E, 0x8867, 0x886A, 0x8869, 0x89D3, 0x8A04, 0x8A07, 0x8D72, 0x8FE3,
    0x8FE1, 0x8FEE, 0x8FE0, 0x90F1, 0x90BD, 0x90BF, 0x90D5, 0x90C5, 0x90BE,
    0x90C7, 0x90CB, 0x90C8, 0x91D4, 0x91D3, 0x9654, 0x964F, 0x9651, 0x9653,
    0x964A, 0x964E, 0x501E, 0x5005, 0x5007, 0x5013, 0x5022, 0x5030, 0x501B,
    0x4FF5, 0x4FF4, 0x5033, 0x5037, 0x502C, 0x4FF6, 0x4FF7, 0x5017, 0x501C,
    0x5020, 0x5027, 0x5035, 0x502F, 0x5031, 0x500E, 0x515A, 0x5194, 0x5193,
    0x51CA, 0x51C4, 0x51C5, 0x51C8, 0x51CE, 0x5261, 0x525A, 0x5252, 0x525E,
    0x525F, 0x5255, 0x5262, 0x52CD, 0x530E, 0x539E, 0x5526, 0x54E2, 0x5517,
    0x5512, 0x54E7, 0x54F3, 0x54E4, 0x551A, 0x54FF, 0x5504, 0x5508, 0x54EB,
    0x5511, 0x5505, 0x54F1, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x550A, 0x54FB, 0x54F7, 0x54F8,
    0x54E0, 0x550E, 0x5503, 0x550B, 0x5701, 0x5702, 0x57CC, 0x5832, 0x57D5,
    0x57D2, 0x57BA, 0x57C6, 0x57BD, 0x57BC, 0x57B8, 0x57B6, 0x57BF, 0x57C7,
    0x57D0, 0x57B9, 0x57C1, 0x590E, 0x594A, 0x5A19, 0x5A16, 0x5A2D, 0x5A2E,
    0x5A15, 0x5A0F, 0x5A17, 0x5A0A, 0x5A1E, 0x5A33, 0x5B6C, 0x5BA7, 0x5BAD,
    0x5BAC, 0x5C03, 0x5C56, 0x5C54, 0x5CEC, 0x5CFF, 0x5CEE, 0x5CF1, 0x5CF7,
    0x5D00, 0x5CF9, 0x5E29, 0x5E28, 0x5EA8, 0x5EAE, 0x5EAA, 0x5EAC, 0x5F33,
    0x5F30, 0x5F67, 0x605D, 0x605A, 0x6067, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x6041, 0x60A2, 0x6088, 0x6080, 0x6092, 0x6081,
    0x609D, 0x6083, 0x6095, 0x609B, 0x6097, 0x6087, 0x609C, 0x608E, 0x6219,
    0x6246, 0x62F2, 0x6310, 0x6356, 0x632C, 0x6344, 0x6345, 0x6336, 0x6343,
    0x63E4, 0x6339, 0x634B, 0x634A, 0x633C, 0x6329, 0x6341, 0x6334, 0x6358,
    0x6354, 0x6359, 0x632D, 0x6347, 0x6333, 0x635A, 0x6351, 0x6338, 0x6357,
    0x6340, 0x6348, 0x654A, 0x6546, 0x65C6, 0x65C3, 0x65C4, 0x65C2, 0x664A,
    0x665F, 0x6647, 0x6651, 0x6712, 0x6713, 0x681F, 0x681A, 0x6849, 0x6832,
    0x6833, 0x683B, 0x684B, 0x684F, 0x6816, 0x6831, 0x681C, 0x6835, 0x682B,
    0x682D, 0x682F, 0x684E, 0x6844, 0x6834, 0x681D, 0x6812, 0x6814, 0x6826,
    0x6828, 0x682E, 0x684D, 0x683A, 0x6825, 0x6820, 0x6B2C, 0x6B2F, 0x6B2D,
    0x6B31, 0x6B34, 0x6B6D, 0x8082, 0x6B88, 0x6BE6, 0x6BE4, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x6BE8, 0x6BE3, 0x6BE2, 0x6BE7, 0x6C25, 0x6D7A, 0x6D63, 0x6D64, 0x6D76,
    0x6D0D, 0x6D61, 0x6D92, 0x6D58, 0x6D62, 0x6D6D, 0x6D6F, 0x6D91, 0x6D8D,
    0x6DEF, 0x6D7F, 0x6D86, 0x6D5E, 0x6D67, 0x6D60, 0x6D97, 0x6D70, 0x6D7C,
    0x6D5F, 0x6D82, 0x6D98, 0x6D2F, 0x6D68, 0x6D8B, 0x6D7E, 0x6D80, 0x6D84,
    0x6D16, 0x6D83, 0x6D7B, 0x6D7D, 0x6D75, 0x6D90, 0x70DC, 0x70D3, 0x70D1,
    0x70DD, 0x70CB, 0x7F39, 0x70E2, 0x70D7, 0x70D2, 0x70DE, 0x70E0, 0x70D4,
    0x70CD, 0x70C5, 0x70C6, 0x70C7, 0x70DA, 0x70CE, 0x70E1, 0x7242, 0x7278,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x7277, 0x7276,
    0x7300, 0x72FA, 0x72F4, 0x72FE, 0x72F6, 0x72F3, 0x72FB, 0x7301, 0x73D3,
    0x73D9, 0x73E5, 0x73D6, 0x73BC, 0x73E7, 0x73E3, 0x73E9, 0x73DC, 0x73D2,
    0x73DB, 0x73D4, 0x73DD, 0x73DA, 0x73D7, 0x73D8, 0x73E8, 0x74DE, 0x74DF,
    0x74F4, 0x74F5, 0x7521, 0x755B, 0x755F, 0x75B0, 0x75C1, 0x75BB, 0x75C4,
    0x75C0, 0x75BF, 0x75B6, 0x75BA, 0x768A, 0x76C9, 0x771D, 0x771B, 0x7710,
    0x7713, 0x7712, 0x7723, 0x7711, 0x7715, 0x7719, 0x771A, 0x7722, 0x7727,
    0x7823, 0x782C, 0x7822, 0x7835, 0x782F, 0x7828, 0x782E, 0x782B, 0x7821,
    0x7829, 0x7833, 0x782A, 0x7831, 0x7954, 0x795B, 0x794F, 0x795C, 0x7953,
    0x7952, 0x7951, 0x79EB, 0x79EC, 0x79E0, 0x79EE, 0x79ED, 0x79EA, 0x79DC,
    0x79DE, 0x79DD, 0x7A86, 0x7A89, 0x7A85, 0x7A8B, 0x7A8C, 0x7A8A, 0x7A87,
    0x7AD8, 0x7B10, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x7B04, 0x7B13, 0x7B05, 0x7B0F, 0x7B08,
    0x7B0A, 0x7B0E, 0x7B09, 0x7B12, 0x7C84, 0x7C91, 0x7C8A, 0x7C8C, 0x7C88,
    0x7C8D, 0x7C85, 0x7D1E, 0x7D1D, 0x7D11, 0x7D0E, 0x7D18, 0x7D16, 0x7D13,
    0x7D1F, 0x7D12, 0x7D0F, 0x7D0C, 0x7F5C, 0x7F61, 0x7F5E, 0x7F60, 0x7F5D,
    0x7F5B, 0x7F96, 0x7F92, 0x7FC3, 0x7FC2, 0x7FC0, 0x8016, 0x803E, 0x8039,
    0x80FA, 0x80F2, 0x80F9, 0x80F5, 0x8101, 0x80FB, 0x8100, 0x8201, 0x822F,
    0x8225, 0x8333, 0x832D, 0x8344, 0x8319, 0x8351, 0x8325, 0x8356, 0x833F,
    0x8341, 0x8326, 0x831C, 0x8322, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x8342, 0x834E, 0x831B, 0x832A, 0x8308, 0x833C, 0x834D,
    0x8316, 0x8324, 0x8320, 0x8337, 0x832F, 0x8329, 0x8347, 0x8345, 0x834C,
    0x8353, 0x831E, 0x832C, 0x834B, 0x8327, 0x8348, 0x8653, 0x8652, 0x86A2,
    0x86A8, 0x8696, 0x868D, 0x8691, 0x869E, 0x8687, 0x8697, 0x8686, 0x868B,
    0x869A, 0x8685, 0x86A5, 0x8699, 0x86A1, 0x86A7, 0x8695, 0x8698, 0x868E,
    0x869D, 0x8690, 0x8694, 0x8843, 0x8844, 0x886D, 0x8875, 0x8876, 0x8872,
    0x8880, 0x8871, 0x887F, 0x886F, 0x8883, 0x887E, 0x8874, 0x887C, 0x8A12,
    0x8C47, 0x8C57, 0x8C7B, 0x8CA4, 0x8CA3, 0x8D76, 0x8D78, 0x8DB5, 0x8DB7,
    0x8DB6, 0x8ED1, 0x8ED3, 0x8FFE, 0x8FF5, 0x9002, 0x8FFF, 0x8FFB, 0x9004,
    0x8FFC, 0x8FF6, 0x90D6, 0x90E0, 0x90D9, 0x90DA, 0x90E3, 0x90DF, 0x90E5,
    0x90D8, 0x90DB, 0x90D7, 0x90DC, 0x90E4, 0x9150, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x914E,
    0x914F, 0x91D5, 0x91E2, 0x91DA, 0x965C, 0x965F, 0x96BC, 0x98E3, 0x9ADF,
    0x9B2F, 0x4E7F, 0x5070, 0x506A, 0x5061, 0x505E, 0x5060, 0x5053, 0x504B,
    0x505D, 0x5072, 0x5048, 0x504D, 0x5041, 0x505B, 0x504A, 0x5062, 0x5015,
    0x5045, 0x505F, 0x5069, 0x506B, 0x5063, 0x5064, 0x5046, 0x5040, 0x506E,
    0x5073, 0x5057, 0x5051, 0x51D0, 0x526B, 0x526D, 0x526C, 0x526E, 0x52D6,
    0x52D3, 0x532D, 0x539C, 0x5575, 0x5576, 0x553C, 0x554D, 0x5550, 0x5534,
    0x552A, 0x5551, 0x5562, 0x5536, 0x5535, 0x5530, 0x5552, 0x5545, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x550C, 0x5532, 0x5565,
    0x554E, 0x5539, 0x5548, 0x552D, 0x553B, 0x5540, 0x554B, 0x570A, 0x5707,
    0x57FB, 0x5814, 0x57E2, 0x57F6, 0x57DC, 0x57F4, 0x5800, 0x57ED, 0x57FD,
    0x5808, 0x57F8, 0x580B, 0x57F3, 0x57CF, 0x5807, 0x57EE, 0x57E3, 0x57F2,
    0x57E5, 0x57EC, 0x57E1, 0x580E, 0x57FC, 0x5810, 0x57E7, 0x5801, 0x580C,
    0x57F1, 0x57E9, 0x57F0, 0x580D, 0x5804, 0x595C, 0x5A60, 0x5A58, 0x5A55,
    0x5A67, 0x5A5E, 0x5A38, 0x5A35, 0x5A6D, 0x5A50, 0x5A5F, 0x5A65, 0x5A6C,
    0x5A53, 0x5A64, 0x5A57, 0x5A43, 0x5A5D, 0x5A52, 0x5A44, 0x5A5B, 0x5A48,
    0x5A8E, 0x5A3E, 0x5A4D, 0x5A39, 0x5A4C, 0x5A70, 0x5A69, 0x5A47, 0x5A51,
    0x5A56, 0x5A42, 0x5A5C, 0x5B72, 0x5B6E, 0x5BC1, 0x5BC0, 0x5C59, 0x5D1E,
    0x5D0B, 0x5D1D, 0x5D1A, 0x5D20, 0x5D0C, 0x5D28, 0x5D0D, 0x5D26, 0x5D25,
    0x5D0F, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x5D30, 0x5D12, 0x5D23, 0x5D1F, 0x5D2E, 0x5E3E,
    0x5E34, 0x5EB1, 0x5EB4, 0x5EB9, 0x5EB2, 0x5EB3, 0x5F36, 0x5F38, 0x5F9B,
    0x5F96, 0x5F9F, 0x608A, 0x6090, 0x6086, 0x60BE, 0x60B0, 0x60BA, 0x60D3,
    0x60D4, 0x60CF, 0x60E4, 0x60D9, 0x60DD, 0x60C8, 0x60B1, 0x60DB, 0x60B7,
    0x60CA, 0x60BF, 0x60C3, 0x60CD, 0x60C0, 0x6332, 0x6365, 0x638A, 0x6382,
    0x637D, 0x63BD, 0x639E, 0x63AD, 0x639D, 0x6397, 0x63AB, 0x638E, 0x636F,
    0x6387, 0x6390, 0x636E, 0x63AF, 0x6375, 0x639C, 0x636D, 0x63AE, 0x637C,
    0x63A4, 0x633B, 0x639F, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x6378, 0x6385, 0x6381, 0x6391, 0x638D, 0x6370, 0x6553, 0x65CD,
    0x6665, 0x6661, 0x665B, 0x6659, 0x665C, 0x6662, 0x6718, 0x6879, 0x6887,
    0x6890, 0x689C, 0x686D, 0x686E, 0x68AE, 0x68AB, 0x6956, 0x686F, 0x68A3,
    0x68AC, 0x68A9, 0x6875, 0x6874, 0x68B2, 0x688F, 0x6877, 0x6892, 0x687C,
    0x686B, 0x6872, 0x68AA, 0x6880, 0x6871, 0x687E, 0x689B, 0x6896, 0x688B,
    0x68A0, 0x6889, 0x68A4, 0x6878, 0x687B, 0x6891, 0x688C, 0x688A, 0x687D,
    0x6B36, 0x6B33, 0x6B37, 0x6B38, 0x6B91, 0x6B8F, 0x6B8D, 0x6B8E, 0x6B8C,
    0x6C2A, 0x6DC0, 0x6DAB, 0x6DB4, 0x6DB3, 0x6E74, 0x6DAC, 0x6DE9, 0x6DE2,
    0x6DB7, 0x6DF6, 0x6DD4, 0x6E00, 0x6DC8, 0x6DE0, 0x6DDF, 0x6DD6, 0x6DBE,
    0x6DE5, 0x6DDC, 0x6DDD, 0x6DDB, 0x6DF4, 0x6DCA, 0x6DBD, 0x6DED, 0x6DF0,
    0x6DBA, 0x6DD5, 0x6DC2, 0x6DCF, 0x6DC9, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x6DD0, 0x6DF2,
    0x6DD3, 0x6DFD, 0x6DD7, 0x6DCD, 0x6DE3, 0x6DBB, 0x70FA, 0x710D, 0x70F7,
    0x7117, 0x70F4, 0x710C, 0x70F0, 0x7104, 0x70F3, 0x7110, 0x70FC, 0x70FF,
    0x7106, 0x7113, 0x7100, 0x70F8, 0x70F6, 0x710B, 0x7102, 0x710E, 0x727E,
    0x727B, 0x727C, 0x727F, 0x731D, 0x7317, 0x7307, 0x7311, 0x7318, 0x730A,
    0x7308, 0x72FF, 0x730F, 0x731E, 0x7388, 0x73F6, 0x73F8, 0x73F5, 0x7404,
    0x7401, 0x73FD, 0x7407, 0x7400, 0x73FA, 0x73FC, 0x73FF, 0x740C, 0x740B,
    0x73F4, 0x7408, 0x7564, 0x7563, 0x75CE, 0x75D2, 0x75CF, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x75CB, 0x75CC, 0x75D1, 0x75D0,
    0x768F, 0x7689, 0x76D3, 0x7739, 0x772F, 0x772D, 0x7731, 0x7732, 0x7734,
    0x7733, 0x773D, 0x7725, 0x773B, 0x7735, 0x7848, 0x7852, 0x7849, 0x784D,
    0x784A, 0x784C, 0x7826, 0x7845, 0x7850, 0x7964, 0x7967, 0x7969, 0x796A,
    0x7963, 0x796B, 0x7961, 0x79BB, 0x79FA, 0x79F8, 0x79F6, 0x79F7, 0x7A8F,
    0x7A94, 0x7A90, 0x7B35, 0x7B47, 0x7B34, 0x7B25, 0x7B30, 0x7B22, 0x7B24,
    0x7B33, 0x7B18, 0x7B2A, 0x7B1D, 0x7B31, 0x7B2B, 0x7B2D, 0x7B2F, 0x7B32,
    0x7B38, 0x7B1A, 0x7B23, 0x7C94, 0x7C98, 0x7C96, 0x7CA3, 0x7D35, 0x7D3D,
    0x7D38, 0x7D36, 0x7D3A, 0x7D45, 0x7D2C, 0x7D29, 0x7D41, 0x7D47, 0x7D3E,
    0x7D3F, 0x7D4A, 0x7D3B, 0x7D28, 0x7F63, 0x7F95, 0x7F9C, 0x7F9D, 0x7F9B,
    0x7FCA, 0x7FCB, 0x7FCD, 0x7FD0, 0x7FD1, 0x7FC7, 0x7FCF, 0x7FC9, 0x801F,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x801E, 0x801B, 0x8047, 0x8043, 0x8048, 0x8118, 0x8125,
    0x8119, 0x811B, 0x812D, 0x811F, 0x812C, 0x811E, 0x8121, 0x8115, 0x8127,
    0x811D, 0x8122, 0x8211, 0x8238, 0x8233, 0x823A, 0x8234, 0x8232, 0x8274,
    0x8390, 0x83A3, 0x83A8, 0x838D, 0x837A, 0x8373, 0x83A4, 0x8374, 0x838F,
    0x8381, 0x8395, 0x8399, 0x8375, 0x8394, 0x83A9, 0x837D, 0x8383, 0x838C,
    0x839D, 0x839B, 0x83AA, 0x838B, 0x837E, 0x83A5, 0x83AF, 0x8388, 0x8397,
    0x83B0, 0x837F, 0x83A6, 0x8387, 0x83AE, 0x8376, 0x839A, 0x8659, 0x8656,
    0x86BF, 0x86B7, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x86C2, 0x86C1, 0x86C5, 0x86BA, 0x86B0, 0x86C8, 0x86B9, 0x86B3, 0x86B8,
    0x86CC, 0x86B4, 0x86BB, 0x86BC, 0x86C3, 0x86BD, 0x86BE, 0x8852, 0x8889,
    0x8895, 0x88A8, 0x88A2, 0x88AA, 0x889A, 0x8891, 0x88A1, 0x889F, 0x8898,
    0x88A7, 0x8899, 0x889B, 0x8897, 0x88A4, 0x88AC, 0x888C, 0x8893, 0x888E,
    0x8982, 0x89D6, 0x89D9, 0x89D5, 0x8A30, 0x8A27, 0x8A2C, 0x8A1E, 0x8C39,
    0x8C3B, 0x8C5C, 0x8C5D, 0x8C7D, 0x8CA5, 0x8D7D, 0x8D7B, 0x8D79, 0x8DBC,
    0x8DC2, 0x8DB9, 0x8DBF, 0x8DC1, 0x8ED8, 0x8EDE, 0x8EDD, 0x8EDC, 0x8ED7,
    0x8EE0, 0x8EE1, 0x9024, 0x900B, 0x9011, 0x901C, 0x900C, 0x9021, 0x90EF,
    0x90EA, 0x90F0, 0x90F4, 0x90F2, 0x90F3, 0x90D4, 0x90EB, 0x90EC, 0x90E9,
    0x9156, 0x9158, 0x915A, 0x9153, 0x9155, 0x91EC, 0x91F4, 0x91F1, 0x91F3,
    0x91F8, 0x91E4, 0x91F9, 0x91EA, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x91EB, 0x91F7, 0x91E8,
    0x91EE, 0x957A, 0x9586, 0x9588, 0x967C, 0x966D, 0x966B, 0x9671, 0x966F,
    0x96BF, 0x976A, 0x9804, 0x98E5, 0x9997, 0x509B, 0x5095, 0x5094, 0x509E,
    0x508B, 0x50A3, 0x5083, 0x508C, 0x508E, 0x509D, 0x5068, 0x509C, 0x5092,
    0x5082, 0x5087, 0x515F, 0x51D4, 0x5312, 0x5311, 0x53A4, 0x53A7, 0x5591,
    0x55A8, 0x55A5, 0x55AD, 0x5577, 0x5645, 0x55A2, 0x5593, 0x5588, 0x558F,
    0x55B5, 0x5581, 0x55A3, 0x5592, 0x55A4, 0x557D, 0x558C, 0x55A6, 0x557F,
    0x5595, 0x55A1, 0x558E, 0x570C, 0x5829, 0x5837, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x5819, 0x581E, 0x5827, 0x5823, 0x5828,
    0x57F5, 0x5848, 0x5825, 0x581C, 0x581B, 0x5833, 0x583F, 0x5836, 0x582E,
    0x5839, 0x5838, 0x582D, 0x582C, 0x583B, 0x5961, 0x5AAF, 0x5A94, 0x5A9F,
    0x5A7A, 0x5AA2, 0x5A9E, 0x5A78, 0x5AA6, 0x5A7C, 0x5AA5, 0x5AAC, 0x5A95,
    0x5AAE, 0x5A37, 0x5A84, 0x5A8A, 0x5A97, 0x5A83, 0x5A8B, 0x5AA9, 0x5A7B,
    0x5A7D, 0x5A8C, 0x5A9C, 0x5A8F, 0x5A93, 0x5A9D, 0x5BEA, 0x5BCD, 0x5BCB,
    0x5BD4, 0x5BD1, 0x5BCA, 0x5BCE, 0x5C0C, 0x5C30, 0x5D37, 0x5D43, 0x5D6B,
    0x5D41, 0x5D4B, 0x5D3F, 0x5D35, 0x5D51, 0x5D4E, 0x5D55, 0x5D33, 0x5D3A,
    0x5D52, 0x5D3D, 0x5D31, 0x5D59, 0x5D42, 0x5D39, 0x5D49, 0x5D38, 0x5D3C,
    0x5D32, 0x5D36, 0x5D40, 0x5D45, 0x5E44, 0x5E41, 0x5F58, 0x5FA6, 0x5FA5,
    0x5FAB, 0x60C9, 0x60B9, 0x60CC, 0x60E2, 0x60CE, 0x60C4, 0x6114, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x60F2, 0x610A, 0x6116, 0x6105, 0x60F5, 0x6113, 0x60F8, 0x60FC,
    0x60FE, 0x60C1, 0x6103, 0x6118, 0x611D, 0x6110, 0x60FF, 0x6104, 0x610B,
    0x624A, 0x6394, 0x63B1, 0x63B0, 0x63CE, 0x63E5, 0x63E8, 0x63EF, 0x63C3,
    0x649D, 0x63F3, 0x63CA, 0x63E0, 0x63F6, 0x63D5, 0x63F2, 0x63F5, 0x6461,
    0x63DF, 0x63BE, 0x63DD, 0x63DC, 0x63C4, 0x63D8, 0x63D3, 0x63C2, 0x63C7,
    0x63CC, 0x63CB, 0x63C8, 0x63F0, 0x63D7, 0x63D9, 0x6532, 0x6567, 0x656A,
    0x6564, 0x655C, 0x6568, 0x6565, 0x658C, 0x659D, 0x659E, 0x65AE, 0x65D0,
    0x65D2, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x667C,
    0x666C, 0x667B, 0x6680, 0x6671, 0x6679, 0x666A, 0x6672, 0x6701, 0x690C,
    0x68D3, 0x6904, 0x68DC, 0x692A, 0x68EC, 0x68EA, 0x68F1, 0x690F, 0x68D6,
    0x68F7, 0x68EB, 0x68E4, 0x68F6, 0x6913, 0x6910, 0x68F3, 0x68E1, 0x6907,
    0x68CC, 0x6908, 0x6970, 0x68B4, 0x6911, 0x68EF, 0x68C6, 0x6914, 0x68F8,
    0x68D0, 0x68FD, 0x68FC, 0x68E8, 0x690B, 0x690A, 0x6917, 0x68CE, 0x68C8,
    0x68DD, 0x68DE, 0x68E6, 0x68F4, 0x68D1, 0x6906, 0x68D4, 0x68E9, 0x6915,
    0x6925, 0x68C7, 0x6B39, 0x6B3B, 0x6B3F, 0x6B3C, 0x6B94, 0x6B97, 0x6B99,
    0x6B95, 0x6BBD, 0x6BF0, 0x6BF2, 0x6BF3, 0x6C30, 0x6DFC, 0x6E46, 0x6E47,
    0x6E1F, 0x6E49, 0x6E88, 0x6E3C, 0x6E3D, 0x6E45, 0x6E62, 0x6E2B, 0x6E3F,
    0x6E41, 0x6E5D, 0x6E73, 0x6E1C, 0x6E33, 0x6E4B, 0x6E40, 0x6E51, 0x6E3B,
    0x6E03, 0x6E2E, 0x6E5E, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x6E68, 0x6E5C, 0x6E61, 0x6E31,
    0x6E28, 0x6E60, 0x6E71, 0x6E6B, 0x6E39, 0x6E22, 0x6E30, 0x6E53, 0x6E65,
    0x6E27, 0x6E78, 0x6E64, 0x6E77, 0x6E55, 0x6E79, 0x6E52, 0x6E66, 0x6E35,
    0x6E36, 0x6E5A, 0x7120, 0x711E, 0x712F, 0x70FB, 0x712E, 0x7131, 0x7123,
    0x7125, 0x7122, 0x7132, 0x711F, 0x7128, 0x713A, 0x711B, 0x724B, 0x725A,
    0x7288, 0x7289, 0x7286, 0x7285, 0x728B, 0x7312, 0x730B, 0x7330, 0x7322,
    0x7331, 0x7333, 0x7327, 0x7332, 0x732D, 0x7326, 0x7323, 0x7335, 0x730C,
    0x742E, 0x742C, 0x7430, 0x742B, 0x7416, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x741A, 0x7421, 0x742D, 0x7431, 0x7424, 0x7423,
    0x741D, 0x7429, 0x7420, 0x7432, 0x74FB, 0x752F, 0x756F, 0x756C, 0x75E7,
    0x75DA, 0x75E1, 0x75E6, 0x75DD, 0x75DF, 0x75E4, 0x75D7, 0x7695, 0x7692,
    0x76DA, 0x7746, 0x7747, 0x7744, 0x774D, 0x7745, 0x774A, 0x774E, 0x774B,
    0x774C, 0x77DE, 0x77EC, 0x7860, 0x7864, 0x7865, 0x785C, 0x786D, 0x7871,
    0x786A, 0x786E, 0x7870, 0x7869, 0x7868, 0x785E, 0x7862, 0x7974, 0x7973,
    0x7972, 0x7970, 0x7A02, 0x7A0A, 0x7A03, 0x7A0C, 0x7A04, 0x7A99, 0x7AE6,
    0x7AE4, 0x7B4A, 0x7B3B, 0x7B44, 0x7B48, 0x7B4C, 0x7B4E, 0x7B40, 0x7B58,
    0x7B45, 0x7CA2, 0x7C9E, 0x7CA8, 0x7CA1, 0x7D58, 0x7D6F, 0x7D63, 0x7D53,
    0x7D56, 0x7D67, 0x7D6A, 0x7D4F, 0x7D6D, 0x7D5C, 0x7D6B, 0x7D52, 0x7D54,
    0x7D69, 0x7D51, 0x7D5F, 0x7D4E, 0x7F3E, 0x7F3F, 0x7F65, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x7F66, 0x7FA2, 0x7FA0, 0x7FA1, 0x7FD7, 0x8051, 0x804F, 0x8050, 0x80FE,
    0x80D4, 0x8143, 0x814A, 0x8152, 0x814F, 0x8147, 0x813D, 0x814D, 0x813A,
    0x81E6, 0x81EE, 0x81F7, 0x81F8, 0x81F9, 0x8204, 0x823C, 0x823D, 0x823F,
    0x8275, 0x833B, 0x83CF, 0x83F9, 0x8423, 0x83C0, 0x83E8, 0x8412, 0x83E7,
    0x83E4, 0x83FC, 0x83F6, 0x8410, 0x83C6, 0x83C8, 0x83EB, 0x83E3, 0x83BF,
    0x8401, 0x83DD, 0x83E5, 0x83D8, 0x83FF, 0x83E1, 0x83CB, 0x83CE, 0x83D6,
    0x83F5, 0x83C9, 0x8409, 0x840F, 0x83DE, 0x8411, 0x8406, 0x83C2, 0x83F3,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x83D5, 0x83FA,
    0x83C7, 0x83D1, 0x83EA, 0x8413, 0x83C3, 0x83EC, 0x83EE, 0x83C4, 0x83FB,
    0x83D7, 0x83E2, 0x841B, 0x83DB, 0x83FE, 0x86D8, 0x86E2, 0x86E6, 0x86D3,
    0x86E3, 0x86DA, 0x86EA, 0x86DD, 0x86EB, 0x86DC, 0x86EC, 0x86E9, 0x86D7,
    0x86E8, 0x86D1, 0x8848, 0x8856, 0x8855, 0x88BA, 0x88D7, 0x88B9, 0x88B8,
    0x88C0, 0x88BE, 0x88B6, 0x88BC, 0x88B7, 0x88BD, 0x88B2, 0x8901, 0x88C9,
    0x8995, 0x8998, 0x8997, 0x89DD, 0x89DA, 0x89DB, 0x8A4E, 0x8A4D, 0x8A39,
    0x8A59, 0x8A40, 0x8A57, 0x8A58, 0x8A44, 0x8A45, 0x8A52, 0x8A48, 0x8A51,
    0x8A4A, 0x8A4C, 0x8A4F, 0x8C5F, 0x8C81, 0x8C80, 0x8CBA, 0x8CBE, 0x8CB0,
    0x8CB9, 0x8CB5, 0x8D84, 0x8D80, 0x8D89, 0x8DD8, 0x8DD3, 0x8DCD, 0x8DC7,
    0x8DD6, 0x8DDC, 0x8DCF, 0x8DD5, 0x8DD9, 0x8DC8, 0x8DD7, 0x8DC5, 0x8EEF,
    0x8EF7, 0x8EFA, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x8EF9, 0x8EE6, 0x8EEE, 0x8EE5, 0x8EF5,
    0x8EE7, 0x8EE8, 0x8EF6, 0x8EEB, 0x8EF1, 0x8EEC, 0x8EF4, 0x8EE9, 0x902D,
    0x9034, 0x902F, 0x9106, 0x912C, 0x9104, 0x90FF, 0x90FC, 0x9108, 0x90F9,
    0x90FB, 0x9101, 0x9100, 0x9107, 0x9105, 0x9103, 0x9161, 0x9164, 0x915F,
    0x9162, 0x9160, 0x9201, 0x920A, 0x9225, 0x9203, 0x921A, 0x9226, 0x920F,
    0x920C, 0x9200, 0x9212, 0x91FF, 0x91FD, 0x9206, 0x9204, 0x9227, 0x9202,
    0x921C, 0x9224, 0x9219, 0x9217, 0x9205, 0x9216, 0x957B, 0x958D, 0x958C,
    0x9590, 0x9687, 0x967E, 0x9688, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x9689, 0x9683, 0x9680, 0x96C2, 0x96C8, 0x96C3, 0x96F1,
    0x96F0, 0x976C, 0x9770, 0x976E, 0x9807, 0x98A9, 0x98EB, 0x9CE6, 0x9EF9,
    0x4E83, 0x4E84, 0x4EB6, 0x50BD, 0x50BF, 0x50C6, 0x50AE, 0x50C4, 0x50CA,
    0x50B4, 0x50C8, 0x50C2, 0x50B0, 0x50C1, 0x50BA, 0x50B1, 0x50CB, 0x50C9,
    0x50B6, 0x50B8, 0x51D7, 0x527A, 0x5278, 0x527B, 0x527C, 0x55C3, 0x55DB,
    0x55CC, 0x55D0, 0x55CB, 0x55CA, 0x55DD, 0x55C0, 0x55D4, 0x55C4, 0x55E9,
    0x55BF, 0x55D2, 0x558D, 0x55CF, 0x55D5, 0x55E2, 0x55D6, 0x55C8, 0x55F2,
    0x55CD, 0x55D9, 0x55C2, 0x5714, 0x5853, 0x5868, 0x5864, 0x584F, 0x584D,
    0x5849, 0x586F, 0x5855, 0x584E, 0x585D, 0x5859, 0x5865, 0x585B, 0x583D,
    0x5863, 0x5871, 0x58FC, 0x5AC7, 0x5AC4, 0x5ACB, 0x5ABA, 0x5AB8, 0x5AB1,
    0x5AB5, 0x5AB0, 0x5ABF, 0x5AC8, 0x5ABB, 0x5AC6, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x5AB7,
    0x5AC0, 0x5ACA, 0x5AB4, 0x5AB6, 0x5ACD, 0x5AB9, 0x5A90, 0x5BD6, 0x5BD8,
    0x5BD9, 0x5C1F, 0x5C33, 0x5D71, 0x5D63, 0x5D4A, 0x5D65, 0x5D72, 0x5D6C,
    0x5D5E, 0x5D68, 0x5D67, 0x5D62, 0x5DF0, 0x5E4F, 0x5E4E, 0x5E4A, 0x5E4D,
    0x5E4B, 0x5EC5, 0x5ECC, 0x5EC6, 0x5ECB, 0x5EC7, 0x5F40, 0x5FAF, 0x5FAD,
    0x60F7, 0x6149, 0x614A, 0x612B, 0x6145, 0x6136, 0x6132, 0x612E, 0x6146,
    0x612F, 0x614F, 0x6129, 0x6140, 0x6220, 0x9168, 0x6223, 0x6225, 0x6224,
    0x63C5, 0x63F1, 0x63EB, 0x6410, 0x6412, 0x6409, 0x6420, 0x6424, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x6433, 0x6443, 0x641F,
    0x6415, 0x6418, 0x6439, 0x6437, 0x6422, 0x6423, 0x640C, 0x6426, 0x6430,
    0x6428, 0x6441, 0x6435, 0x642F, 0x640A, 0x641A, 0x6440, 0x6425, 0x6427,
    0x640B, 0x63E7, 0x641B, 0x642E, 0x6421, 0x640E, 0x656F, 0x6592, 0x65D3,
    0x6686, 0x668C, 0x6695, 0x6690, 0x668B, 0x668A, 0x6699, 0x6694, 0x6678,
    0x6720, 0x6966, 0x695F, 0x6938, 0x694E, 0x6962, 0x6971, 0x693F, 0x6945,
    0x696A, 0x6939, 0x6942, 0x6957, 0x6959, 0x697A, 0x6948, 0x6949, 0x6935,
    0x696C, 0x6933, 0x693D, 0x6965, 0x68F0, 0x6978, 0x6934, 0x6969, 0x6940,
    0x696F, 0x6944, 0x6976, 0x6958, 0x6941, 0x6974, 0x694C, 0x693B, 0x694B,
    0x6937, 0x695C, 0x694F, 0x6951, 0x6932, 0x6952, 0x692F, 0x697B, 0x693C,
    0x6B46, 0x6B45, 0x6B43, 0x6B42, 0x6B48, 0x6B41, 0x6B9B, 0xFA0D, 0x6BFB,
    0x6BFC, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x6BF9, 0x6BF7, 0x6BF8, 0x6E9B, 0x6ED6, 0x6EC8,
    0x6E8F, 0x6EC0, 0x6E9F, 0x6E93, 0x6E94, 0x6EA0, 0x6EB1, 0x6EB9, 0x6EC6,
    0x6ED2, 0x6EBD, 0x6EC1, 0x6E9E, 0x6EC9, 0x6EB7, 0x6EB0, 0x6ECD, 0x6EA6,
    0x6ECF, 0x6EB2, 0x6EBE, 0x6EC3, 0x6EDC, 0x6ED8, 0x6E99, 0x6E92, 0x6E8E,
    0x6E8D, 0x6EA4, 0x6EA1, 0x6EBF, 0x6EB3, 0x6ED0, 0x6ECA, 0x6E97, 0x6EAE,
    0x6EA3, 0x7147, 0x7154, 0x7152, 0x7163, 0x7160, 0x7141, 0x715D, 0x7162,
    0x7172, 0x7178, 0x716A, 0x7161, 0x7142, 0x7158, 0x7143, 0x714B, 0x7170,
    0x715F, 0x7150, 0x7153, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x7144, 0x714D, 0x715A, 0x724F, 0x728D, 0x728C, 0x7291, 0x7290,
    0x728E, 0x733C, 0x7342, 0x733B, 0x733A, 0x7340, 0x734A, 0x7349, 0x7444,
    0x744A, 0x744B, 0x7452, 0x7451, 0x7457, 0x7440, 0x744F, 0x7450, 0x744E,
    0x7442, 0x7446, 0x744D, 0x7454, 0x74E1, 0x74FF, 0x74FE, 0x74FD, 0x751D,
    0x7579, 0x7577, 0x6983, 0x75EF, 0x760F, 0x7603, 0x75F7, 0x75FE, 0x75FC,
    0x75F9, 0x75F8, 0x7610, 0x75FB, 0x75F6, 0x75ED, 0x75F5, 0x75FD, 0x7699,
    0x76B5, 0x76DD, 0x7755, 0x775F, 0x7760, 0x7752, 0x7756, 0x775A, 0x7769,
    0x7767, 0x7754, 0x7759, 0x776D, 0x77E0, 0x7887, 0x789A, 0x7894, 0x788F,
    0x7884, 0x7895, 0x7885, 0x7886, 0x78A1, 0x7883, 0x7879, 0x7899, 0x7880,
    0x7896, 0x787B, 0x797C, 0x7982, 0x797D, 0x7979, 0x7A11, 0x7A18, 0x7A19,
    0x7A12, 0x7A17, 0x7A15, 0x7A22, 0x7A13, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x7A1B, 0x7A10,
    0x7AA3, 0x7AA2, 0x7A9E, 0x7AEB, 0x7B66, 0x7B64, 0x7B6D, 0x7B74, 0x7B69,
    0x7B72, 0x7B65, 0x7B73, 0x7B71, 0x7B70, 0x7B61, 0x7B78, 0x7B76, 0x7B63,
    0x7CB2, 0x7CB4, 0x7CAF, 0x7D88, 0x7D86, 0x7D80, 0x7D8D, 0x7D7F, 0x7D85,
    0x7D7A, 0x7D8E, 0x7D7B, 0x7D83, 0x7D7C, 0x7D8C, 0x7D94, 0x7D84, 0x7D7D,
    0x7D92, 0x7F6D, 0x7F6B, 0x7F67, 0x7F68, 0x7F6C, 0x7FA6, 0x7FA5, 0x7FA7,
    0x7FDB, 0x7FDC, 0x8021, 0x8164, 0x8160, 0x8177, 0x815C, 0x8169, 0x815B,
    0x8162, 0x8172, 0x6721, 0x815E, 0x8176, 0x8167, 0x816F, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x8144, 0x8161, 0x821D, 0x8249,
    0x8244, 0x8240, 0x8242, 0x8245, 0x84F1, 0x843F, 0x8456, 0x8476, 0x8479,
    0x848F, 0x848D, 0x8465, 0x8451, 0x8440, 0x8486, 0x8467, 0x8430, 0x844D,
    0x847D, 0x845A, 0x8459, 0x8474, 0x8473, 0x845D, 0x8507, 0x845E, 0x8437,
    0x843A, 0x8434, 0x847A, 0x8443, 0x8478, 0x8432, 0x8445, 0x8429, 0x83D9,
    0x844B, 0x842F, 0x8442, 0x842D, 0x845F, 0x8470, 0x8439, 0x844E, 0x844C,
    0x8452, 0x846F, 0x84C5, 0x848E, 0x843B, 0x8447, 0x8436, 0x8433, 0x8468,
    0x847E, 0x8444, 0x842B, 0x8460, 0x8454, 0x846E, 0x8450, 0x870B, 0x8704,
    0x86F7, 0x870C, 0x86FA, 0x86D6, 0x86F5, 0x874D, 0x86F8, 0x870E, 0x8709,
    0x8701, 0x86F6, 0x870D, 0x8705, 0x88D6, 0x88CB, 0x88CD, 0x88CE, 0x88DE,
    0x88DB, 0x88DA, 0x88CC, 0x88D0, 0x8985, 0x899B, 0x89DF, 0x89E5, 0x89E4,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x89E1, 0x89E0, 0x89E2, 0x89DC, 0x89E6, 0x8A76, 0x8A86,
    0x8A7F, 0x8A61, 0x8A3F, 0x8A77, 0x8A82, 0x8A84, 0x8A75, 0x8A83, 0x8A81,
    0x8A74, 0x8A7A, 0x8C3C, 0x8C4B, 0x8C4A, 0x8C65, 0x8C64, 0x8C66, 0x8C86,
    0x8C84, 0x8C85, 0x8CCC, 0x8D68, 0x8D69, 0x8D91, 0x8D8C, 0x8D8E, 0x8D8F,
    0x8D8D, 0x8D93, 0x8D94, 0x8D90, 0x8D92, 0x8DF0, 0x8DE0, 0x8DEC, 0x8DF1,
    0x8DEE, 0x8DD0, 0x8DE9, 0x8DE3, 0x8DE2, 0x8DE7, 0x8DF2, 0x8DEB, 0x8DF4,
    0x8F06, 0x8EFF, 0x8F01, 0x8F00, 0x8F05, 0x8F07, 0x8F08, 0x8F02, 0x8F0B,
    0x9052, 0x903F, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x9044, 0x9049, 0x903D, 0x9110, 0x910D, 0x910F, 0x9111, 0x9116, 0x9114,
    0x910B, 0x910E, 0x916E, 0x916F, 0x9248, 0x9252, 0x9230, 0x923A, 0x9266,
    0x9233, 0x9265, 0x925E, 0x9283, 0x922E, 0x924A, 0x9246, 0x926D, 0x926C,
    0x924F, 0x9260, 0x9267, 0x926F, 0x9236, 0x9261, 0x9270, 0x9231, 0x9254,
    0x9263, 0x9250, 0x9272, 0x924E, 0x9253, 0x924C, 0x9256, 0x9232, 0x959F,
    0x959C, 0x959E, 0x959B, 0x9692, 0x9693, 0x9691, 0x9697, 0x96CE, 0x96FA,
    0x96FD, 0x96F8, 0x96F5, 0x9773, 0x9777, 0x9778, 0x9772, 0x980F, 0x980D,
    0x980E, 0x98AC, 0x98F6, 0x98F9, 0x99AF, 0x99B2, 0x99B0, 0x99B5, 0x9AAD,
    0x9AAB, 0x9B5B, 0x9CEA, 0x9CED, 0x9CE7, 0x9E80, 0x9EFD, 0x50E6, 0x50D4,
    0x50D7, 0x50E8, 0x50F3, 0x50DB, 0x50EA, 0x50DD, 0x50E4, 0x50D3, 0x50EC,
    0x50F0, 0x50EF, 0x50E3, 0x50E0, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x51D8, 0x5280, 0x5281,
    0x52E9, 0x52EB, 0x5330, 0x53AC, 0x5627, 0x5615, 0x560C, 0x5612, 0x55FC,
    0x560F, 0x561C, 0x5601, 0x5613, 0x5602, 0x55FA, 0x561D, 0x5604, 0x55FF,
    0x55F9, 0x5889, 0x587C, 0x5890, 0x5898, 0x5886, 0x5881, 0x587F, 0x5874,
    0x588B, 0x587A, 0x5887, 0x5891, 0x588E, 0x5876, 0x5882, 0x5888, 0x587B,
    0x5894, 0x588F, 0x58FE, 0x596B, 0x5ADC, 0x5AEE, 0x5AE5, 0x5AD5, 0x5AEA,
    0x5ADA, 0x5AED, 0x5AEB, 0x5AF3, 0x5AE2, 0x5AE0, 0x5ADB, 0x5AEC, 0x5ADE,
    0x5ADD, 0x5AD9, 0x5AE8, 0x5ADF, 0x5B77, 0x5BE0, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x5BE3, 0x5C63, 0x5D82, 0x5D80, 0x5D7D,
    0x5D86, 0x5D7A, 0x5D81, 0x5D77, 0x5D8A, 0x5D89, 0x5D88, 0x5D7E, 0x5D7C,
    0x5D8D, 0x5D79, 0x5D7F, 0x5E58, 0x5E59, 0x5E53, 0x5ED8, 0x5ED1, 0x5ED7,
    0x5ECE, 0x5EDC, 0x5ED5, 0x5ED9, 0x5ED2, 0x5ED4, 0x5F44, 0x5F43, 0x5F6F,
    0x5FB6, 0x612C, 0x6128, 0x6141, 0x615E, 0x6171, 0x6173, 0x6152, 0x6153,
    0x6172, 0x616C, 0x6180, 0x6174, 0x6154, 0x617A, 0x615B, 0x6165, 0x613B,
    0x616A, 0x6161, 0x6156, 0x6229, 0x6227, 0x622B, 0x642B, 0x644D, 0x645B,
    0x645D, 0x6474, 0x6476, 0x6472, 0x6473, 0x647D, 0x6475, 0x6466, 0x64A6,
    0x644E, 0x6482, 0x645E, 0x645C, 0x644B, 0x6453, 0x6460, 0x6450, 0x647F,
    0x643F, 0x646C, 0x646B, 0x6459, 0x6465, 0x6477, 0x6573, 0x65A0, 0x66A1,
    0x66A0, 0x669F, 0x6705, 0x6704, 0x6722, 0x69B1, 0x69B6, 0x69C9, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x69A0, 0x69CE, 0x6996, 0x69B0, 0x69AC, 0x69BC, 0x6991, 0x6999,
    0x698E, 0x69A7, 0x698D, 0x69A9, 0x69BE, 0x69AF, 0x69BF, 0x69C4, 0x69BD,
    0x69A4, 0x69D4, 0x69B9, 0x69CA, 0x699A, 0x69CF, 0x69B3, 0x6993, 0x69AA,
    0x69A1, 0x699E, 0x69D9, 0x6997, 0x6990, 0x69C2, 0x69B5, 0x69A5, 0x69C6,
    0x6B4A, 0x6B4D, 0x6B4B, 0x6B9E, 0x6B9F, 0x6BA0, 0x6BC3, 0x6BC4, 0x6BFE,
    0x6ECE, 0x6EF5, 0x6EF1, 0x6F03, 0x6F25, 0x6EF8, 0x6F37, 0x6EFB, 0x6F2E,
    0x6F09, 0x6F4E, 0x6F19, 0x6F1A, 0x6F27, 0x6F18, 0x6F3B, 0x6F12, 0x6EED,
    0x6F0A, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6F36,
    0x6F73, 0x6EF9, 0x6EEE, 0x6F2D, 0x6F40, 0x6F30, 0x6F3C, 0x6F35, 0x6EEB,
    0x6F07, 0x6F0E, 0x6F43, 0x6F05, 0x6EFD, 0x6EF6, 0x6F39, 0x6F1C, 0x6EFC,
    0x6F3A, 0x6F1F, 0x6F0D, 0x6F1E, 0x6F08, 0x6F21, 0x7187, 0x7190, 0x7189,
    0x7180, 0x7185, 0x7182, 0x718F, 0x717B, 0x7186, 0x7181, 0x7197, 0x7244,
    0x7253, 0x7297, 0x7295, 0x7293, 0x7343, 0x734D, 0x7351, 0x734C, 0x7462,
    0x7473, 0x7471, 0x7475, 0x7472, 0x7467, 0x746E, 0x7500, 0x7502, 0x7503,
    0x757D, 0x7590, 0x7616, 0x7608, 0x760C, 0x7615, 0x7611, 0x760A, 0x7614,
    0x76B8, 0x7781, 0x777C, 0x7785, 0x7782, 0x776E, 0x7780, 0x776F, 0x777E,
    0x7783, 0x78B2, 0x78AA, 0x78B4, 0x78AD, 0x78A8, 0x787E, 0x78AB, 0x789E,
    0x78A5, 0x78A0, 0x78AC, 0x78A2, 0x78A4, 0x7998, 0x798A, 0x798B, 0x7996,
    0x7995, 0x7994, 0x7993, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x7997, 0x7988, 0x7992, 0x7990,
    0x7A2B, 0x7A4A, 0x7A30, 0x7A2F, 0x7A28, 0x7A26, 0x7AA8, 0x7AAB, 0x7AAC,
    0x7AEE, 0x7B88, 0x7B9C, 0x7B8A, 0x7B91, 0x7B90, 0x7B96, 0x7B8D, 0x7B8C,
    0x7B9B, 0x7B8E, 0x7B85, 0x7B98, 0x5284, 0x7B99, 0x7BA4, 0x7B82, 0x7CBB,
    0x7CBF, 0x7CBC, 0x7CBA, 0x7DA7, 0x7DB7, 0x7DC2, 0x7DA3, 0x7DAA, 0x7DC1,
    0x7DC0, 0x7DC5, 0x7D9D, 0x7DCE, 0x7DC4, 0x7DC6, 0x7DCB, 0x7DCC, 0x7DAF,
    0x7DB9, 0x7D96, 0x7DBC, 0x7D9F, 0x7DA6, 0x7DAE, 0x7DA9, 0x7DA1, 0x7DC9,
    0x7F73, 0x7FE2, 0x7FE3, 0x7FE5, 0x7FDE, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x8024, 0x805D, 0x805C, 0x8189, 0x8186, 0x8183,
    0x8187, 0x818D, 0x818C, 0x818B, 0x8215, 0x8497, 0x84A4, 0x84A1, 0x849F,
    0x84BA, 0x84CE, 0x84C2, 0x84AC, 0x84AE, 0x84AB, 0x84B9, 0x84B4, 0x84C1,
    0x84CD, 0x84AA, 0x849A, 0x84B1, 0x84D0, 0x849D, 0x84A7, 0x84BB, 0x84A2,
    0x8494, 0x84C7, 0x84CC, 0x849B, 0x84A9, 0x84AF, 0x84A8, 0x84D6, 0x8498,
    0x84B6, 0x84CF, 0x84A0, 0x84D7, 0x84D4, 0x84D2, 0x84DB, 0x84B0, 0x8491,
    0x8661, 0x8733, 0x8723, 0x8728, 0x876B, 0x8740, 0x872E, 0x871E, 0x8721,
    0x8719, 0x871B, 0x8743, 0x872C, 0x8741, 0x873E, 0x8746, 0x8720, 0x8732,
    0x872A, 0x872D, 0x873C, 0x8712, 0x873A, 0x8731, 0x8735, 0x8742, 0x8726,
    0x8727, 0x8738, 0x8724, 0x871A, 0x8730, 0x8711, 0x88F7, 0x88E7, 0x88F1,
    0x88F2, 0x88FA, 0x88FE, 0x88EE, 0x88FC, 0x88F6, 0x88FB, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x88F0, 0x88EC, 0x88EB, 0x899D, 0x89A1, 0x899F, 0x899E, 0x89E9, 0x89EB,
    0x89E8, 0x8AAB, 0x8A99, 0x8A8B, 0x8A92, 0x8A8F, 0x8A96, 0x8C3D, 0x8C68,
    0x8C69, 0x8CD5, 0x8CCF, 0x8CD7, 0x8D96, 0x8E09, 0x8E02, 0x8DFF, 0x8E0D,
    0x8DFD, 0x8E0A, 0x8E03, 0x8E07, 0x8E06, 0x8E05, 0x8DFE, 0x8E00, 0x8E04,
    0x8F10, 0x8F11, 0x8F0E, 0x8F0D, 0x9123, 0x911C, 0x9120, 0x9122, 0x911F,
    0x911D, 0x911A, 0x9124, 0x9121, 0x911B, 0x917A, 0x9172, 0x9179, 0x9173,
    0x92A5, 0x92A4, 0x9276, 0x929B, 0x927A, 0x92A0, 0x9294, 0x92AA, 0x928D,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x92A6, 0x929A,
    0x92AB, 0x9279, 0x9297, 0x927F, 0x92A3, 0x92EE, 0x928E, 0x9282, 0x9295,
    0x92A2, 0x927D, 0x9288, 0x92A1, 0x928A, 0x9286, 0x928C, 0x9299, 0x92A7,
    0x927E, 0x9287, 0x92A9, 0x929D, 0x928B, 0x922D, 0x969E, 0x96A1, 0x96FF,
    0x9758, 0x977D, 0x977A, 0x977E, 0x9783, 0x9780, 0x9782, 0x977B, 0x9784,
    0x9781, 0x977F, 0x97CE, 0x97CD, 0x9816, 0x98AD, 0x98AE, 0x9902, 0x9900,
    0x9907, 0x999D, 0x999C, 0x99C3, 0x99B9, 0x99BB, 0x99BA, 0x99C2, 0x99BD,
    0x99C7, 0x9AB1, 0x9AE3, 0x9AE7, 0x9B3E, 0x9B3F, 0x9B60, 0x9B61, 0x9B5F,
    0x9CF1, 0x9CF2, 0x9CF5, 0x9EA7, 0x50FF, 0x5103, 0x5130, 0x50F8, 0x5106,
    0x5107, 0x50F6, 0x50FE, 0x510B, 0x510C, 0x50FD, 0x510A, 0x528B, 0x528C,
    0x52F1, 0x52EF, 0x5648, 0x5642, 0x564C, 0x5635, 0x5641, 0x564A, 0x5649,
    0x5646, 0x5658, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x565A, 0x5640, 0x5633, 0x563D, 0x562C,
    0x563E, 0x5638, 0x562A, 0x563A, 0x571A, 0x58AB, 0x589D, 0x58B1, 0x58A0,
    0x58A3, 0x58AF, 0x58AC, 0x58A5, 0x58A1, 0x58FF, 0x5AFF, 0x5AF4, 0x5AFD,
    0x5AF7, 0x5AF6, 0x5B03, 0x5AF8, 0x5B02, 0x5AF9, 0x5B01, 0x5B07, 0x5B05,
    0x5B0F, 0x5C67, 0x5D99, 0x5D97, 0x5D9F, 0x5D92, 0x5DA2, 0x5D93, 0x5D95,
    0x5DA0, 0x5D9C, 0x5DA1, 0x5D9A, 0x5D9E, 0x5E69, 0x5E5D, 0x5E60, 0x5E5C,
    0x7DF3, 0x5EDB, 0x5EDE, 0x5EE1, 0x5F49, 0x5FB2, 0x618B, 0x6183, 0x6179,
    0x61B1, 0x61B0, 0x61A2, 0x6189, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x619B, 0x6193, 0x61AF, 0x61AD, 0x619F, 0x6192, 0x61AA,
    0x61A1, 0x618D, 0x6166, 0x61B3, 0x622D, 0x646E, 0x6470, 0x6496, 0x64A0,
    0x6485, 0x6497, 0x649C, 0x648F, 0x648B, 0x648A, 0x648C, 0x64A3, 0x649F,
    0x6468, 0x64B1, 0x6498, 0x6576, 0x657A, 0x6579, 0x657B, 0x65B2, 0x65B3,
    0x66B5, 0x66B0, 0x66A9, 0x66B2, 0x66B7, 0x66AA, 0x66AF, 0x6A00, 0x6A06,
    0x6A17, 0x69E5, 0x69F8, 0x6A15, 0x69F1, 0x69E4, 0x6A20, 0x69FF, 0x69EC,
    0x69E2, 0x6A1B, 0x6A1D, 0x69FE, 0x6A27, 0x69F2, 0x69EE, 0x6A14, 0x69F7,
    0x69E7, 0x6A40, 0x6A08, 0x69E6, 0x69FB, 0x6A0D, 0x69FC, 0x69EB, 0x6A09,
    0x6A04, 0x6A18, 0x6A25, 0x6A0F, 0x69F6, 0x6A26, 0x6A07, 0x69F4, 0x6A16,
    0x6B51, 0x6BA5, 0x6BA3, 0x6BA2, 0x6BA6, 0x6C01, 0x6C00, 0x6BFF, 0x6C02,
    0x6F41, 0x6F26, 0x6F7E, 0x6F87, 0x6FC6, 0x6F92, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6F8D,
    0x6F89, 0x6F8C, 0x6F62, 0x6F4F, 0x6F85, 0x6F5A, 0x6F96, 0x6F76, 0x6F6C,
    0x6F82, 0x6F55, 0x6F72, 0x6F52, 0x6F50, 0x6F57, 0x6F94, 0x6F93, 0x6F5D,
    0x6F00, 0x6F61, 0x6F6B, 0x6F7D, 0x6F67, 0x6F90, 0x6F53, 0x6F8B, 0x6F69,
    0x6F7F, 0x6F95, 0x6F63, 0x6F77, 0x6F6A, 0x6F7B, 0x71B2, 0x71AF, 0x719B,
    0x71B0, 0x71A0, 0x719A, 0x71A9, 0x71B5, 0x719D, 0x71A5, 0x719E, 0x71A4,
    0x71A1, 0x71AA, 0x719C, 0x71A7, 0x71B3, 0x7298, 0x729A, 0x7358, 0x7352,
    0x735E, 0x735F, 0x7360, 0x735D, 0x735B, 0x7361, 0x735A, 0x7359, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x7362, 0x7487, 0x7489,
    0x748A, 0x7486, 0x7481, 0x747D, 0x7485, 0x7488, 0x747C, 0x7479, 0x7508,
    0x7507, 0x757E, 0x7625, 0x761E, 0x7619, 0x761D, 0x761C, 0x7623, 0x761A,
    0x7628, 0x761B, 0x769C, 0x769D, 0x769E, 0x769B, 0x778D, 0x778F, 0x7789,
    0x7788, 0x78CD, 0x78BB, 0x78CF, 0x78CC, 0x78D1, 0x78CE, 0x78D4, 0x78C8,
    0x78C3, 0x78C4, 0x78C9, 0x799A, 0x79A1, 0x79A0, 0x799C, 0x79A2, 0x799B,
    0x6B76, 0x7A39, 0x7AB2, 0x7AB4, 0x7AB3, 0x7BB7, 0x7BCB, 0x7BBE, 0x7BAC,
    0x7BCE, 0x7BAF, 0x7BB9, 0x7BCA, 0x7BB5, 0x7CC5, 0x7CC8, 0x7CCC, 0x7CCB,
    0x7DF7, 0x7DDB, 0x7DEA, 0x7DE7, 0x7DD7, 0x7DE1, 0x7E03, 0x7DFA, 0x7DE6,
    0x7DF6, 0x7DF1, 0x7DF0, 0x7DEE, 0x7DDF, 0x7F76, 0x7FAC, 0x7FB0, 0x7FAD,
    0x7FED, 0x7FEB, 0x7FEA, 0x7FEC, 0x7FE6, 0x7FE8, 0x8064, 0x8067, 0x81A3,
    0x819F, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x819E, 0x8195, 0x81A2, 0x8199, 0x8197, 0x8216,
    0x824F, 0x8253, 0x8252, 0x8250, 0x824E, 0x8251, 0x8524, 0x853B, 0x850F,
    0x8500, 0x8529, 0x850E, 0x8509, 0x850D, 0x851F, 0x850A, 0x8527, 0x851C,
    0x84FB, 0x852B, 0x84FA, 0x8508, 0x850C, 0x84F4, 0x852A, 0x84F2, 0x8515,
    0x84F7, 0x84EB, 0x84F3, 0x84FC, 0x8512, 0x84EA, 0x84E9, 0x8516, 0x84FE,
    0x8528, 0x851D, 0x852E, 0x8502, 0x84FD, 0x851E, 0x84F6, 0x8531, 0x8526,
    0x84E7, 0x84E8, 0x84F0, 0x84EF, 0x84F9, 0x8518, 0x8520, 0x8530, 0x850B,
    0x8519, 0x852F, 0x8662, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x8756, 0x8763, 0x8764, 0x8777, 0x87E1, 0x8773, 0x8758, 0x8754,
    0x875B, 0x8752, 0x8761, 0x875A, 0x8751, 0x875E, 0x876D, 0x876A, 0x8750,
    0x874E, 0x875F, 0x875D, 0x876F, 0x876C, 0x877A, 0x876E, 0x875C, 0x8765,
    0x874F, 0x877B, 0x8775, 0x8762, 0x8767, 0x8769, 0x885A, 0x8905, 0x890C,
    0x8914, 0x890B, 0x8917, 0x8918, 0x8919, 0x8906, 0x8916, 0x8911, 0x890E,
    0x8909, 0x89A2, 0x89A4, 0x89A3, 0x89ED, 0x89F0, 0x89EC, 0x8ACF, 0x8AC6,
    0x8AB8, 0x8AD3, 0x8AD1, 0x8AD4, 0x8AD5, 0x8ABB, 0x8AD7, 0x8ABE, 0x8AC0,
    0x8AC5, 0x8AD8, 0x8AC3, 0x8ABA, 0x8ABD, 0x8AD9, 0x8C3E, 0x8C4D, 0x8C8F,
    0x8CE5, 0x8CDF, 0x8CD9, 0x8CE8, 0x8CDA, 0x8CDD, 0x8CE7, 0x8DA0, 0x8D9C,
    0x8DA1, 0x8D9B, 0x8E20, 0x8E23, 0x8E25, 0x8E24, 0x8E2E, 0x8E15, 0x8E1B,
    0x8E16, 0x8E11, 0x8E19, 0x8E26, 0x8E27, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x8E14, 0x8E12,
    0x8E18, 0x8E13, 0x8E1C, 0x8E17, 0x8E1A, 0x8F2C, 0x8F24, 0x8F18, 0x8F1A,
    0x8F20, 0x8F23, 0x8F16, 0x8F17, 0x9073, 0x9070, 0x906F, 0x9067, 0x906B,
    0x912F, 0x912B, 0x9129, 0x912A, 0x9132, 0x9126, 0x912E, 0x9185, 0x9186,
    0x918A, 0x9181, 0x9182, 0x9184, 0x9180, 0x92D0, 0x92C3, 0x92C4, 0x92C0,
    0x92D9, 0x92B6, 0x92CF, 0x92F1, 0x92DF, 0x92D8, 0x92E9, 0x92D7, 0x92DD,
    0x92CC, 0x92EF, 0x92C2, 0x92E8, 0x92CA, 0x92C8, 0x92CE, 0x92E6, 0x92CD,
    0x92D5, 0x92C9, 0x92E0, 0x92DE, 0x92E7, 0x92D1, 0x92D3, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x92B5, 0x92E1, 0x92C6, 0x92B4,
    0x957C, 0x95AC, 0x95AB, 0x95AE, 0x95B0, 0x96A4, 0x96A2, 0x96D3, 0x9705,
    0x9708, 0x9702, 0x975A, 0x978A, 0x978E, 0x9788, 0x97D0, 0x97CF, 0x981E,
    0x981D, 0x9826, 0x9829, 0x9828, 0x9820, 0x981B, 0x9827, 0x98B2, 0x9908,
    0x98FA, 0x9911, 0x9914, 0x9916, 0x9917, 0x9915, 0x99DC, 0x99CD, 0x99CF,
    0x99D3, 0x99D4, 0x99CE, 0x99C9, 0x99D6, 0x99D8, 0x99CB, 0x99D7, 0x99CC,
    0x9AB3, 0x9AEC, 0x9AEB, 0x9AF3, 0x9AF2, 0x9AF1, 0x9B46, 0x9B43, 0x9B67,
    0x9B74, 0x9B71, 0x9B66, 0x9B76, 0x9B75, 0x9B70, 0x9B68, 0x9B64, 0x9B6C,
    0x9CFC, 0x9CFA, 0x9CFD, 0x9CFF, 0x9CF7, 0x9D07, 0x9D00, 0x9CF9, 0x9CFB,
    0x9D08, 0x9D05, 0x9D04, 0x9E83, 0x9ED3, 0x9F0F, 0x9F10, 0x511C, 0x5113,
    0x5117, 0x511A, 0x5111, 0x51DE, 0x5334, 0x53E1, 0x5670, 0x5660, 0x566E,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x5673, 0x5666, 0x5663, 0x566D, 0x5672, 0x565E, 0x5677,
    0x571C, 0x571B, 0x58C8, 0x58BD, 0x58C9, 0x58BF, 0x58BA, 0x58C2, 0x58BC,
    0x58C6, 0x5B17, 0x5B19, 0x5B1B, 0x5B21, 0x5B14, 0x5B13, 0x5B10, 0x5B16,
    0x5B28, 0x5B1A, 0x5B20, 0x5B1E, 0x5BEF, 0x5DAC, 0x5DB1, 0x5DA9, 0x5DA7,
    0x5DB5, 0x5DB0, 0x5DAE, 0x5DAA, 0x5DA8, 0x5DB2, 0x5DAD, 0x5DAF, 0x5DB4,
    0x5E67, 0x5E68, 0x5E66, 0x5E6F, 0x5EE9, 0x5EE7, 0x5EE6, 0x5EE8, 0x5EE5,
    0x5F4B, 0x5FBC, 0x619D, 0x61A8, 0x6196, 0x61C5, 0x61B4, 0x61C6, 0x61C1,
    0x61CC, 0x61BA, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x61BF, 0x61B8, 0x618C, 0x64D7, 0x64D6, 0x64D0, 0x64CF, 0x64C9, 0x64BD,
    0x6489, 0x64C3, 0x64DB, 0x64F3, 0x64D9, 0x6533, 0x657F, 0x657C, 0x65A2,
    0x66C8, 0x66BE, 0x66C0, 0x66CA, 0x66CB, 0x66CF, 0x66BD, 0x66BB, 0x66BA,
    0x66CC, 0x6723, 0x6A34, 0x6A66, 0x6A49, 0x6A67, 0x6A32, 0x6A68, 0x6A3E,
    0x6A5D, 0x6A6D, 0x6A76, 0x6A5B, 0x6A51, 0x6A28, 0x6A5A, 0x6A3B, 0x6A3F,
    0x6A41, 0x6A6A, 0x6A64, 0x6A50, 0x6A4F, 0x6A54, 0x6A6F, 0x6A69, 0x6A60,
    0x6A3C, 0x6A5E, 0x6A56, 0x6A55, 0x6A4D, 0x6A4E, 0x6A46, 0x6B55, 0x6B54,
    0x6B56, 0x6BA7, 0x6BAA, 0x6BAB, 0x6BC8, 0x6BC7, 0x6C04, 0x6C03, 0x6C06,
    0x6FAD, 0x6FCB, 0x6FA3, 0x6FC7, 0x6FBC, 0x6FCE, 0x6FC8, 0x6F5E, 0x6FC4,
    0x6FBD, 0x6F9E, 0x6FCA, 0x6FA8, 0x7004, 0x6FA5, 0x6FAE, 0x6FBA, 0x6FAC,
    0x6FAA, 0x6FCF, 0x6FBF, 0x6FB8, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x6FA2, 0x6FC9, 0x6FAB,
    0x6FCD, 0x6FAF, 0x6FB2, 0x6FB0, 0x71C5, 0x71C2, 0x71BF, 0x71B8, 0x71D6,
    0x71C0, 0x71C1, 0x71CB, 0x71D4, 0x71CA, 0x71C7, 0x71CF, 0x71BD, 0x71D8,
    0x71BC, 0x71C6, 0x71DA, 0x71DB, 0x729D, 0x729E, 0x7369, 0x7366, 0x7367,
    0x736C, 0x7365, 0x736B, 0x736A, 0x747F, 0x749A, 0x74A0, 0x7494, 0x7492,
    0x7495, 0x74A1, 0x750B, 0x7580, 0x762F, 0x762D, 0x7631, 0x763D, 0x7633,
    0x763C, 0x7635, 0x7632, 0x7630, 0x76BB, 0x76E6, 0x779A, 0x779D, 0x77A1,
    0x779C, 0x779B, 0x77A2, 0x77A3, 0x7795, 0x7799, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x7797, 0x78DD, 0x78E9, 0x78E5, 0x78EA,
    0x78DE, 0x78E3, 0x78DB, 0x78E1, 0x78E2, 0x78ED, 0x78DF, 0x78E0, 0x79A4,
    0x7A44, 0x7A48, 0x7A47, 0x7AB6, 0x7AB8, 0x7AB5, 0x7AB1, 0x7AB7, 0x7BDE,
    0x7BE3, 0x7BE7, 0x7BDD, 0x7BD5, 0x7BE5, 0x7BDA, 0x7BE8, 0x7BF9, 0x7BD4,
    0x7BEA, 0x7BE2, 0x7BDC, 0x7BEB, 0x7BD8, 0x7BDF, 0x7CD2, 0x7CD4, 0x7CD7,
    0x7CD0, 0x7CD1, 0x7E12, 0x7E21, 0x7E17, 0x7E0C, 0x7E1F, 0x7E20, 0x7E13,
    0x7E0E, 0x7E1C, 0x7E15, 0x7E1A, 0x7E22, 0x7E0B, 0x7E0F, 0x7E16, 0x7E0D,
    0x7E14, 0x7E25, 0x7E24, 0x7F43, 0x7F7B, 0x7F7C, 0x7F7A, 0x7FB1, 0x7FEF,
    0x802A, 0x8029, 0x806C, 0x81B1, 0x81A6, 0x81AE, 0x81B9, 0x81B5, 0x81AB,
    0x81B0, 0x81AC, 0x81B4, 0x81B2, 0x81B7, 0x81A7, 0x81F2, 0x8255, 0x8256,
    0x8257, 0x8556, 0x8545, 0x856B, 0x854D, 0x8553, 0x8561, 0x8558, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x8540, 0x8546, 0x8564, 0x8541, 0x8562, 0x8544, 0x8551, 0x8547,
    0x8563, 0x853E, 0x855B, 0x8571, 0x854E, 0x856E, 0x8575, 0x8555, 0x8567,
    0x8560, 0x858C, 0x8566, 0x855D, 0x8554, 0x8565, 0x856C, 0x8663, 0x8665,
    0x8664, 0x879B, 0x878F, 0x8797, 0x8793, 0x8792, 0x8788, 0x8781, 0x8796,
    0x8798, 0x8779, 0x8787, 0x87A3, 0x8785, 0x8790, 0x8791, 0x879D, 0x8784,
    0x8794, 0x879C, 0x879A, 0x8789, 0x891E, 0x8926, 0x8930, 0x892D, 0x892E,
    0x8927, 0x8931, 0x8922, 0x8929, 0x8923, 0x892F, 0x892C, 0x891F, 0x89F1,
    0x8AE0, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x8AE2,
    0x8AF2, 0x8AF4, 0x8AF5, 0x8ADD, 0x8B14, 0x8AE4, 0x8ADF, 0x8AF0, 0x8AC8,
    0x8ADE, 0x8AE1, 0x8AE8, 0x8AFF, 0x8AEF, 0x8AFB, 0x8C91, 0x8C92, 0x8C90,
    0x8CF5, 0x8CEE, 0x8CF1, 0x8CF0, 0x8CF3, 0x8D6C, 0x8D6E, 0x8DA5, 0x8DA7,
    0x8E33, 0x8E3E, 0x8E38, 0x8E40, 0x8E45, 0x8E36, 0x8E3C, 0x8E3D, 0x8E41,
    0x8E30, 0x8E3F, 0x8EBD, 0x8F36, 0x8F2E, 0x8F35, 0x8F32, 0x8F39, 0x8F37,
    0x8F34, 0x9076, 0x9079, 0x907B, 0x9086, 0x90FA, 0x9133, 0x9135, 0x9136,
    0x9193, 0x9190, 0x9191, 0x918D, 0x918F, 0x9327, 0x931E, 0x9308, 0x931F,
    0x9306, 0x930F, 0x937A, 0x9338, 0x933C, 0x931B, 0x9323, 0x9312, 0x9301,
    0x9346, 0x932D, 0x930E, 0x930D, 0x92CB, 0x931D, 0x92FA, 0x9325, 0x9313,
    0x92F9, 0x92F7, 0x9334, 0x9302, 0x9324, 0x92FF, 0x9329, 0x9339, 0x9335,
    0x932A, 0x9314, 0x930C, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x930B, 0x92FE, 0x9309, 0x9300,
    0x92FB, 0x9316, 0x95BC, 0x95CD, 0x95BE, 0x95B9, 0x95BA, 0x95B6, 0x95BF,
    0x95B5, 0x95BD, 0x96A9, 0x96D4, 0x970B, 0x9712, 0x9710, 0x9799, 0x9797,
    0x9794, 0x97F0, 0x97F8, 0x9835, 0x982F, 0x9832, 0x9924, 0x991F, 0x9927,
    0x9929, 0x999E, 0x99EE, 0x99EC, 0x99E5, 0x99E4, 0x99F0, 0x99E3, 0x99EA,
    0x99E9, 0x99E7, 0x9AB9, 0x9ABF, 0x9AB4, 0x9ABB, 0x9AF6, 0x9AFA, 0x9AF9,
    0x9AF7, 0x9B33, 0x9B80, 0x9B85, 0x9B87, 0x9B7C, 0x9B7E, 0x9B7B, 0x9B82,
    0x9B93, 0x9B92, 0x9B90, 0x9B7A, 0x9B95, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x9B7D, 0x9B88, 0x9D25, 0x9D17, 0x9D20, 0x9D1E,
    0x9D14, 0x9D29, 0x9D1D, 0x9D18, 0x9D22, 0x9D10, 0x9D19, 0x9D1F, 0x9E88,
    0x9E86, 0x9E87, 0x9EAE, 0x9EAD, 0x9ED5, 0x9ED6, 0x9EFA, 0x9F12, 0x9F3D,
    0x5126, 0x5125, 0x5122, 0x5124, 0x5120, 0x5129, 0x52F4, 0x5693, 0x568C,
    0x568D, 0x5686, 0x5684, 0x5683, 0x567E, 0x5682, 0x567F, 0x5681, 0x58D6,
    0x58D4, 0x58CF, 0x58D2, 0x5B2D, 0x5B25, 0x5B32, 0x5B23, 0x5B2C, 0x5B27,
    0x5B26, 0x5B2F, 0x5B2E, 0x5B7B, 0x5BF1, 0x5BF2, 0x5DB7, 0x5E6C, 0x5E6A,
    0x5FBE, 0x5FBB, 0x61C3, 0x61B5, 0x61BC, 0x61E7, 0x61E0, 0x61E5, 0x61E4,
    0x61E8, 0x61DE, 0x64EF, 0x64E9, 0x64E3, 0x64EB, 0x64E4, 0x64E8, 0x6581,
    0x6580, 0x65B6, 0x65DA, 0x66D2, 0x6A8D, 0x6A96, 0x6A81, 0x6AA5, 0x6A89,
    0x6A9F, 0x6A9B, 0x6AA1, 0x6A9E, 0x6A87, 0x6A93, 0x6A8E, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x6A95, 0x6A83, 0x6AA8, 0x6AA4, 0x6A91, 0x6A7F, 0x6AA6, 0x6A9A, 0x6A85,
    0x6A8C, 0x6A92, 0x6B5B, 0x6BAD, 0x6C09, 0x6FCC, 0x6FA9, 0x6FF4, 0x6FD4,
    0x6FE3, 0x6FDC, 0x6FED, 0x6FE7, 0x6FE6, 0x6FDE, 0x6FF2, 0x6FDD, 0x6FE2,
    0x6FE8, 0x71E1, 0x71F1, 0x71E8, 0x71F2, 0x71E4, 0x71F0, 0x71E2, 0x7373,
    0x736E, 0x736F, 0x7497, 0x74B2, 0x74AB, 0x7490, 0x74AA, 0x74AD, 0x74B1,
    0x74A5, 0x74AF, 0x7510, 0x7511, 0x7512, 0x750F, 0x7584, 0x7643, 0x7648,
    0x7649, 0x7647, 0x76A4, 0x76E9, 0x77B5, 0x77AB, 0x77B2, 0x77B7, 0x77B6,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x77B4, 0x77B1,
    0x77A8, 0x77F0, 0x78F3, 0x78FD, 0x7902, 0x78FB, 0x78FC, 0x78F2, 0x7905,
    0x78F9, 0x78FE, 0x7904, 0x79AB, 0x79A8, 0x7A5C, 0x7A5B, 0x7A56, 0x7A58,
    0x7A54, 0x7A5A, 0x7ABE, 0x7AC0, 0x7AC1, 0x7C05, 0x7C0F, 0x7BF2, 0x7C00,
    0x7BFF, 0x7BFB, 0x7C0E, 0x7BF4, 0x7C0B, 0x7BF3, 0x7C02, 0x7C09, 0x7C03,
    0x7C01, 0x7BF8, 0x7BFD, 0x7C06, 0x7BF0, 0x7BF1, 0x7C10, 0x7C0A, 0x7CE8,
    0x7E2D, 0x7E3C, 0x7E42, 0x7E33, 0x9848, 0x7E38, 0x7E2A, 0x7E49, 0x7E40,
    0x7E47, 0x7E29, 0x7E4C, 0x7E30, 0x7E3B, 0x7E36, 0x7E44, 0x7E3A, 0x7F45,
    0x7F7F, 0x7F7E, 0x7F7D, 0x7FF4, 0x7FF2, 0x802C, 0x81BB, 0x81C4, 0x81CC,
    0x81CA, 0x81C5, 0x81C7, 0x81BC, 0x81E9, 0x825B, 0x825A, 0x825C, 0x8583,
    0x8580, 0x858F, 0x85A7, 0x8595, 0x85A0, 0x858B, 0x85A3, 0x857B, 0x85A4,
    0x859A, 0x859E, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x8577, 0x857C, 0x8589, 0x85A1, 0x857A,
    0x8578, 0x8557, 0x858E, 0x8596, 0x8586, 0x858D, 0x8599, 0x859D, 0x8581,
    0x85A2, 0x8582, 0x8588, 0x8585, 0x8579, 0x8576, 0x8598, 0x8590, 0x859F,
    0x8668, 0x87BE, 0x87AA, 0x87AD, 0x87C5, 0x87B0, 0x87AC, 0x87B9, 0x87B5,
    0x87BC, 0x87AE, 0x87C9, 0x87C3, 0x87C2, 0x87CC, 0x87B7, 0x87AF, 0x87C4,
    0x87CA, 0x87B4, 0x87B6, 0x87BF, 0x87B8, 0x87BD, 0x87DE, 0x87B2, 0x8935,
    0x8933, 0x893C, 0x893E, 0x8941, 0x8952, 0x8937, 0x8942, 0x89AD, 0x89AF,
    0x89AE, 0x89F2, 0x89F3, 0x8B1E, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x8B18, 0x8B16, 0x8B11, 0x8B05, 0x8B0B, 0x8B22, 0x8B0F,
    0x8B12, 0x8B15, 0x8B07, 0x8B0D, 0x8B08, 0x8B06, 0x8B1C, 0x8B13, 0x8B1A,
    0x8C4F, 0x8C70, 0x8C72, 0x8C71, 0x8C6F, 0x8C95, 0x8C94, 0x8CF9, 0x8D6F,
    0x8E4E, 0x8E4D, 0x8E53, 0x8E50, 0x8E4C, 0x8E47, 0x8F43, 0x8F40, 0x9085,
    0x907E, 0x9138, 0x919A, 0x91A2, 0x919B, 0x9199, 0x919F, 0x91A1, 0x919D,
    0x91A0, 0x93A1, 0x9383, 0x93AF, 0x9364, 0x9356, 0x9347, 0x937C, 0x9358,
    0x935C, 0x9376, 0x9349, 0x9350, 0x9351, 0x9360, 0x936D, 0x938F, 0x934C,
    0x936A, 0x9379, 0x9357, 0x9355, 0x9352, 0x934F, 0x9371, 0x9377, 0x937B,
    0x9361, 0x935E, 0x9363, 0x9367, 0x9380, 0x934E, 0x9359, 0x95C7, 0x95C0,
    0x95C9, 0x95C3, 0x95C5, 0x95B7, 0x96AE, 0x96B0, 0x96AC, 0x9720, 0x971F,
    0x9718, 0x971D, 0x9719, 0x979A, 0x97A1, 0x979C, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x979E,
    0x979D, 0x97D5, 0x97D4, 0x97F1, 0x9841, 0x9844, 0x984A, 0x9849, 0x9845,
    0x9843, 0x9925, 0x992B, 0x992C, 0x992A, 0x9933, 0x9932, 0x992F, 0x992D,
    0x9931, 0x9930, 0x9998, 0x99A3, 0x99A1, 0x9A02, 0x99FA, 0x99F4, 0x99F7,
    0x99F9, 0x99F8, 0x99F6, 0x99FB, 0x99FD, 0x99FE, 0x99FC, 0x9A03, 0x9ABE,
    0x9AFE, 0x9AFD, 0x9B01, 0x9AFC, 0x9B48, 0x9B9A, 0x9BA8, 0x9B9E, 0x9B9B,
    0x9BA6, 0x9BA1, 0x9BA5, 0x9BA4, 0x9B86, 0x9BA2, 0x9BA0, 0x9BAF, 0x9D33,
    0x9D41, 0x9D67, 0x9D36, 0x9D2E, 0x9D2F, 0x9D31, 0x9D38, 0x9D30, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x9D45, 0x9D42, 0x9D43,
    0x9D3E, 0x9D37, 0x9D40, 0x9D3D, 0x7FF5, 0x9D2D, 0x9E8A, 0x9E89, 0x9E8D,
    0x9EB0, 0x9EC8, 0x9EDA, 0x9EFB, 0x9EFF, 0x9F24, 0x9F23, 0x9F22, 0x9F54,
    0x9FA0, 0x5131, 0x512D, 0x512E, 0x5698, 0x569C, 0x5697, 0x569A, 0x569D,
    0x5699, 0x5970, 0x5B3C, 0x5C69, 0x5C6A, 0x5DC0, 0x5E6D, 0x5E6E, 0x61D8,
    0x61DF, 0x61ED, 0x61EE, 0x61F1, 0x61EA, 0x61F0, 0x61EB, 0x61D6, 0x61E9,
    0x64FF, 0x6504, 0x64FD, 0x64F8, 0x6501, 0x6503, 0x64FC, 0x6594, 0x65DB,
    0x66DA, 0x66DB, 0x66D8, 0x6AC5, 0x6AB9, 0x6ABD, 0x6AE1, 0x6AC6, 0x6ABA,
    0x6AB6, 0x6AB7, 0x6AC7, 0x6AB4, 0x6AAD, 0x6B5E, 0x6BC9, 0x6C0B, 0x7007,
    0x700C, 0x700D, 0x7001, 0x7005, 0x7014, 0x700E, 0x6FFF, 0x7000, 0x6FFB,
    0x7026, 0x6FFC, 0x6FF7, 0x700A, 0x7201, 0x71FF, 0x71F9, 0x7203, 0x71FD,
    0x7376, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x74B8, 0x74C0, 0x74B5, 0x74C1, 0x74BE, 0x74B6,
    0x74BB, 0x74C2, 0x7514, 0x7513, 0x765C, 0x7664, 0x7659, 0x7650, 0x7653,
    0x7657, 0x765A, 0x76A6, 0x76BD, 0x76EC, 0x77C2, 0x77BA, 0x78FF, 0x790C,
    0x7913, 0x7914, 0x7909, 0x7910, 0x7912, 0x7911, 0x79AD, 0x79AC, 0x7A5F,
    0x7C1C, 0x7C29, 0x7C19, 0x7C20, 0x7C1F, 0x7C2D, 0x7C1D, 0x7C26, 0x7C28,
    0x7C22, 0x7C25, 0x7C30, 0x7E5C, 0x7E50, 0x7E56, 0x7E63, 0x7E58, 0x7E62,
    0x7E5F, 0x7E51, 0x7E60, 0x7E57, 0x7E53, 0x7FB5, 0x7FB3, 0x7FF7, 0x7FF8,
    0x8075, 0x81D1, 0x81D2, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x81D0, 0x825F, 0x825E, 0x85B4, 0x85C6, 0x85C0, 0x85C3, 0x85C2,
    0x85B3, 0x85B5, 0x85BD, 0x85C7, 0x85C4, 0x85BF, 0x85CB, 0x85CE, 0x85C8,
    0x85C5, 0x85B1, 0x85B6, 0x85D2, 0x8624, 0x85B8, 0x85B7, 0x85BE, 0x8669,
    0x87E7, 0x87E6, 0x87E2, 0x87DB, 0x87EB, 0x87EA, 0x87E5, 0x87DF, 0x87F3,
    0x87E4, 0x87D4, 0x87DC, 0x87D3, 0x87ED, 0x87D8, 0x87E3, 0x87A4, 0x87D7,
    0x87D9, 0x8801, 0x87F4, 0x87E8, 0x87DD, 0x8953, 0x894B, 0x894F, 0x894C,
    0x8946, 0x8950, 0x8951, 0x8949, 0x8B2A, 0x8B27, 0x8B23, 0x8B33, 0x8B30,
    0x8B35, 0x8B47, 0x8B2F, 0x8B3C, 0x8B3E, 0x8B31, 0x8B25, 0x8B37, 0x8B26,
    0x8B36, 0x8B2E, 0x8B24, 0x8B3B, 0x8B3D, 0x8B3A, 0x8C42, 0x8C75, 0x8C99,
    0x8C98, 0x8C97, 0x8CFE, 0x8D04, 0x8D02, 0x8D00, 0x8E5C, 0x8E62, 0x8E60,
    0x8E57, 0x8E56, 0x8E5E, 0x8E65, 0x8E67, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x8E5B, 0x8E5A,
    0x8E61, 0x8E5D, 0x8E69, 0x8E54, 0x8F46, 0x8F47, 0x8F48, 0x8F4B, 0x9128,
    0x913A, 0x913B, 0x913E, 0x91A8, 0x91A5, 0x91A7, 0x91AF, 0x91AA, 0x93B5,
    0x938C, 0x9392, 0x93B7, 0x939B, 0x939D, 0x9389, 0x93A7, 0x938E, 0x93AA,
    0x939E, 0x93A6, 0x9395, 0x9388, 0x9399, 0x939F, 0x938D, 0x93B1, 0x9391,
    0x93B2, 0x93A4, 0x93A8, 0x93B4, 0x93A3, 0x93A5, 0x95D2, 0x95D3, 0x95D1,
    0x96B3, 0x96D7, 0x96DA, 0x5DC2, 0x96DF, 0x96D8, 0x96DD, 0x9723, 0x9722,
    0x9725, 0x97AC, 0x97AE, 0x97A8, 0x97AB, 0x97A4, 0x97AA, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x97A2, 0x97A5, 0x97D7, 0x97D9,
    0x97D6, 0x97D8, 0x97FA, 0x9850, 0x9851, 0x9852, 0x98B8, 0x9941, 0x993C,
    0x993A, 0x9A0F, 0x9A0B, 0x9A09, 0x9A0D, 0x9A04, 0x9A11, 0x9A0A, 0x9A05,
    0x9A07, 0x9A06, 0x9AC0, 0x9ADC, 0x9B08, 0x9B04, 0x9B05, 0x9B29, 0x9B35,
    0x9B4A, 0x9B4C, 0x9B4B, 0x9BC7, 0x9BC6, 0x9BC3, 0x9BBF, 0x9BC1, 0x9BB5,
    0x9BB8, 0x9BD3, 0x9BB6, 0x9BC4, 0x9BB9, 0x9BBD, 0x9D5C, 0x9D53, 0x9D4F,
    0x9D4A, 0x9D5B, 0x9D4B, 0x9D59, 0x9D56, 0x9D4C, 0x9D57, 0x9D52, 0x9D54,
    0x9D5F, 0x9D58, 0x9D5A, 0x9E8E, 0x9E8C, 0x9EDF, 0x9F01, 0x9F00, 0x9F16,
    0x9F25, 0x9F2B, 0x9F2A, 0x9F29, 0x9F28, 0x9F4C, 0x9F55, 0x5134, 0x5135,
    0x5296, 0x52F7, 0x53B4, 0x56AB, 0x56AD, 0x56A6, 0x56A7, 0x56AA, 0x56AC,
    0x58DA, 0x58DD, 0x58DB, 0x5912, 0x5B3D, 0x5B3E, 0x5B3F, 0x5DC3, 0x5E70,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x5FBF, 0x61FB, 0x6507, 0x6510, 0x650D, 0x6509, 0x650C,
    0x650E, 0x6584, 0x65DE, 0x65DD, 0x66DE, 0x6AE7, 0x6AE0, 0x6ACC, 0x6AD1,
    0x6AD9, 0x6ACB, 0x6ADF, 0x6ADC, 0x6AD0, 0x6AEB, 0x6ACF, 0x6ACD, 0x6ADE,
    0x6B60, 0x6BB0, 0x6C0C, 0x7019, 0x7027, 0x7020, 0x7016, 0x702B, 0x7021,
    0x7022, 0x7023, 0x7029, 0x7017, 0x7024, 0x701C, 0x702A, 0x720C, 0x720A,
    0x7207, 0x7202, 0x7205, 0x72A5, 0x72A6, 0x72A4, 0x72A3, 0x72A1, 0x74CB,
    0x74C5, 0x74B7, 0x74C3, 0x7516, 0x7660, 0x77C9, 0x77CA, 0x77C4, 0x77F1,
    0x791D, 0x791B, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x7921, 0x791C, 0x7917, 0x791E, 0x79B0, 0x7A67, 0x7A68, 0x7C33, 0x7C3C,
    0x7C39, 0x7C2C, 0x7C3B, 0x7CEC, 0x7CEA, 0x7E76, 0x7E75, 0x7E78, 0x7E70,
    0x7E77, 0x7E6F, 0x7E7A, 0x7E72, 0x7E74, 0x7E68, 0x7F4B, 0x7F4A, 0x7F83,
    0x7F86, 0x7FB7, 0x7FFD, 0x7FFE, 0x8078, 0x81D7, 0x81D5, 0x8264, 0x8261,
    0x8263, 0x85EB, 0x85F1, 0x85ED, 0x85D9, 0x85E1, 0x85E8, 0x85DA, 0x85D7,
    0x85EC, 0x85F2, 0x85F8, 0x85D8, 0x85DF, 0x85E3, 0x85DC, 0x85D1, 0x85F0,
    0x85E6, 0x85EF, 0x85DE, 0x85E2, 0x8800, 0x87FA, 0x8803, 0x87F6, 0x87F7,
    0x8809, 0x880C, 0x880B, 0x8806, 0x87FC, 0x8808, 0x87FF, 0x880A, 0x8802,
    0x8962, 0x895A, 0x895B, 0x8957, 0x8961, 0x895C, 0x8958, 0x895D, 0x8959,
    0x8988, 0x89B7, 0x89B6, 0x89F6, 0x8B50, 0x8B48, 0x8B4A, 0x8B40, 0x8B53,
    0x8B56, 0x8B54, 0x8B4B, 0x8B55, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x8B51, 0x8B42, 0x8B52,
    0x8B57, 0x8C43, 0x8C77, 0x8C76, 0x8C9A, 0x8D06, 0x8D07, 0x8D09, 0x8DAC,
    0x8DAA, 0x8DAD, 0x8DAB, 0x8E6D, 0x8E78, 0x8E73, 0x8E6A, 0x8E6F, 0x8E7B,
    0x8EC2, 0x8F52, 0x8F51, 0x8F4F, 0x8F50, 0x8F53, 0x8FB4, 0x9140, 0x913F,
    0x91B0, 0x91AD, 0x93DE, 0x93C7, 0x93CF, 0x93C2, 0x93DA, 0x93D0, 0x93F9,
    0x93EC, 0x93CC, 0x93D9, 0x93A9, 0x93E6, 0x93CA, 0x93D4, 0x93EE, 0x93E3,
    0x93D5, 0x93C4, 0x93CE, 0x93C0, 0x93D2, 0x93E7, 0x957D, 0x95DA, 0x95DB,
    0x96E1, 0x9729, 0x972B, 0x972C, 0x9728, 0x9726, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x97B3, 0x97B7, 0x97B6, 0x97DD, 0x97DE,
    0x97DF, 0x985C, 0x9859, 0x985D, 0x9857, 0x98BF, 0x98BD, 0x98BB, 0x98BE,
    0x9948, 0x9947, 0x9943, 0x99A6, 0x99A7, 0x9A1A, 0x9A15, 0x9A25, 0x9A1D,
    0x9A24, 0x9A1B, 0x9A22, 0x9A20, 0x9A27, 0x9A23, 0x9A1E, 0x9A1C, 0x9A14,
    0x9AC2, 0x9B0B, 0x9B0A, 0x9B0E, 0x9B0C, 0x9B37, 0x9BEA, 0x9BEB, 0x9BE0,
    0x9BDE, 0x9BE4, 0x9BE6, 0x9BE2, 0x9BF0, 0x9BD4, 0x9BD7, 0x9BEC, 0x9BDC,
    0x9BD9, 0x9BE5, 0x9BD5, 0x9BE1, 0x9BDA, 0x9D77, 0x9D81, 0x9D8A, 0x9D84,
    0x9D88, 0x9D71, 0x9D80, 0x9D78, 0x9D86, 0x9D8B, 0x9D8C, 0x9D7D, 0x9D6B,
    0x9D74, 0x9D75, 0x9D70, 0x9D69, 0x9D85, 0x9D73, 0x9D7B, 0x9D82, 0x9D6F,
    0x9D79, 0x9D7F, 0x9D87, 0x9D68, 0x9E94, 0x9E91, 0x9EC0, 0x9EFC, 0x9F2D,
    0x9F40, 0x9F41, 0x9F4D, 0x9F56, 0x9F57, 0x9F58, 0x5337, 0x56B2, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x56B5, 0x56B3, 0x58E3, 0x5B45, 0x5DC6, 0x5DC7, 0x5EEE, 0x5EEF,
    0x5FC0, 0x5FC1, 0x61F9, 0x6517, 0x6516, 0x6515, 0x6513, 0x65DF, 0x66E8,
    0x66E3, 0x66E4, 0x6AF3, 0x6AF0, 0x6AEA, 0x6AE8, 0x6AF9, 0x6AF1, 0x6AEE,
    0x6AEF, 0x703C, 0x7035, 0x702F, 0x7037, 0x7034, 0x7031, 0x7042, 0x7038,
    0x703F, 0x703A, 0x7039, 0x7040, 0x703B, 0x7033, 0x7041, 0x7213, 0x7214,
    0x72A8, 0x737D, 0x737C, 0x74BA, 0x76AB, 0x76AA, 0x76BE, 0x76ED, 0x77CC,
    0x77CE, 0x77CF, 0x77CD, 0x77F2, 0x7925, 0x7923, 0x7927, 0x7928, 0x7924,
    0x7929, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x79B2,
    0x7A6E, 0x7A6C, 0x7A6D, 0x7AF7, 0x7C49, 0x7C48, 0x7C4A, 0x7C47, 0x7C45,
    0x7CEE, 0x7E7B, 0x7E7E, 0x7E81, 0x7E80, 0x7FBA, 0x7FFF, 0x8079, 0x81DB,
    0x81D9, 0x820B, 0x8268, 0x8269, 0x8622, 0x85FF, 0x8601, 0x85FE, 0x861B,
    0x8600, 0x85F6, 0x8604, 0x8609, 0x8605, 0x860C, 0x85FD, 0x8819, 0x8810,
    0x8811, 0x8817, 0x8813, 0x8816, 0x8963, 0x8966, 0x89B9, 0x89F7, 0x8B60,
    0x8B6A, 0x8B5D, 0x8B68, 0x8B63, 0x8B65, 0x8B67, 0x8B6D, 0x8DAE, 0x8E86,
    0x8E88, 0x8E84, 0x8F59, 0x8F56, 0x8F57, 0x8F55, 0x8F58, 0x8F5A, 0x908D,
    0x9143, 0x9141, 0x91B7, 0x91B5, 0x91B2, 0x91B3, 0x940B, 0x9413, 0x93FB,
    0x9420, 0x940F, 0x9414, 0x93FE, 0x9415, 0x9410, 0x9428, 0x9419, 0x940D,
    0x93F5, 0x9400, 0x93F7, 0x9407, 0x940E, 0x9416, 0x9412, 0x93FA, 0x9409,
    0x93F8, 0x940A, 0x93FF, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x93FC, 0x940C, 0x93F6, 0x9411,
    0x9406, 0x95DE, 0x95E0, 0x95DF, 0x972E, 0x972F, 0x97B9, 0x97BB, 0x97FD,
    0x97FE, 0x9860, 0x9862, 0x9863, 0x985F, 0x98C1, 0x98C2, 0x9950, 0x994E,
    0x9959, 0x994C, 0x994B, 0x9953, 0x9A32, 0x9A34, 0x9A31, 0x9A2C, 0x9A2A,
    0x9A36, 0x9A29, 0x9A2E, 0x9A38, 0x9A2D, 0x9AC7, 0x9ACA, 0x9AC6, 0x9B10,
    0x9B12, 0x9B11, 0x9C0B, 0x9C08, 0x9BF7, 0x9C05, 0x9C12, 0x9BF8, 0x9C40,
    0x9C07, 0x9C0E, 0x9C06, 0x9C17, 0x9C14, 0x9C09, 0x9D9F, 0x9D99, 0x9DA4,
    0x9D9D, 0x9D92, 0x9D98, 0x9D90, 0x9D9B, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x9DA0, 0x9D94, 0x9D9C, 0x9DAA, 0x9D97, 0x9DA1,
    0x9D9A, 0x9DA2, 0x9DA8, 0x9D9E, 0x9DA3, 0x9DBF, 0x9DA9, 0x9D96, 0x9DA6,
    0x9DA7, 0x9E99, 0x9E9B, 0x9E9A, 0x9EE5, 0x9EE4, 0x9EE7, 0x9EE6, 0x9F30,
    0x9F2E, 0x9F5B, 0x9F60, 0x9F5E, 0x9F5D, 0x9F59, 0x9F91, 0x513A, 0x5139,
    0x5298, 0x5297, 0x56C3, 0x56BD, 0x56BE, 0x5B48, 0x5B47, 0x5DCB, 0x5DCF,
    0x5EF1, 0x61FD, 0x651B, 0x6B02, 0x6AFC, 0x6B03, 0x6AF8, 0x6B00, 0x7043,
    0x7044, 0x704A, 0x7048, 0x7049, 0x7045, 0x7046, 0x721D, 0x721A, 0x7219,
    0x737E, 0x7517, 0x766A, 0x77D0, 0x792D, 0x7931, 0x792F, 0x7C54, 0x7C53,
    0x7CF2, 0x7E8A, 0x7E87, 0x7E88, 0x7E8B, 0x7E86, 0x7E8D, 0x7F4D, 0x7FBB,
    0x8030, 0x81DD, 0x8618, 0x862A, 0x8626, 0x861F, 0x8623, 0x861C, 0x8619,
    0x8627, 0x862E, 0x8621, 0x8620, 0x8629, 0x861E, 0x8625, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x8829, 0x881D, 0x881B, 0x8820, 0x8824, 0x881C, 0x882B, 0x884A, 0x896D,
    0x8969, 0x896E, 0x896B, 0x89FA, 0x8B79, 0x8B78, 0x8B45, 0x8B7A, 0x8B7B,
    0x8D10, 0x8D14, 0x8DAF, 0x8E8E, 0x8E8C, 0x8F5E, 0x8F5B, 0x8F5D, 0x9146,
    0x9144, 0x9145, 0x91B9, 0x943F, 0x943B, 0x9436, 0x9429, 0x943D, 0x943C,
    0x9430, 0x9439, 0x942A, 0x9437, 0x942C, 0x9440, 0x9431, 0x95E5, 0x95E4,
    0x95E3, 0x9735, 0x973A, 0x97BF, 0x97E1, 0x9864, 0x98C9, 0x98C6, 0x98C0,
    0x9958, 0x9956, 0x9A39, 0x9A3D, 0x9A46, 0x9A44, 0x9A42, 0x9A41, 0x9A3A,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x9A3F, 0x9ACD,
    0x9B15, 0x9B17, 0x9B18, 0x9B16, 0x9B3A, 0x9B52, 0x9C2B, 0x9C1D, 0x9C1C,
    0x9C2C, 0x9C23, 0x9C28, 0x9C29, 0x9C24, 0x9C21, 0x9DB7, 0x9DB6, 0x9DBC,
    0x9DC1, 0x9DC7, 0x9DCA, 0x9DCF, 0x9DBE, 0x9DC5, 0x9DC3, 0x9DBB, 0x9DB5,
    0x9DCE, 0x9DB9, 0x9DBA, 0x9DAC, 0x9DC8, 0x9DB1, 0x9DAD, 0x9DCC, 0x9DB3,
    0x9DCD, 0x9DB2, 0x9E7A, 0x9E9C, 0x9EEB, 0x9EEE, 0x9EED, 0x9F1B, 0x9F18,
    0x9F1A, 0x9F31, 0x9F4E, 0x9F65, 0x9F64, 0x9F92, 0x4EB9, 0x56C6, 0x56C5,
    0x56CB, 0x5971, 0x5B4B, 0x5B4C, 0x5DD5, 0x5DD1, 0x5EF2, 0x6521, 0x6520,
    0x6526, 0x6522, 0x6B0B, 0x6B08, 0x6B09, 0x6C0D, 0x7055, 0x7056, 0x7057,
    0x7052, 0x721E, 0x721F, 0x72A9, 0x737F, 0x74D8, 0x74D5, 0x74D9, 0x74D7,
    0x766D, 0x76AD, 0x7935, 0x79B4, 0x7A70, 0x7A71, 0x7C57, 0x7C5C, 0x7C59,
    0x7C5B, 0x7C5A, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x7CF4, 0x7CF1, 0x7E91, 0x7F4F, 0x7F87,
    0x81DE, 0x826B, 0x8634, 0x8635, 0x8633, 0x862C, 0x8632, 0x8636, 0x882C,
    0x8828, 0x8826, 0x882A, 0x8825, 0x8971, 0x89BF, 0x89BE, 0x89FB, 0x8B7E,
    0x8B84, 0x8B82, 0x8B86, 0x8B85, 0x8B7F, 0x8D15, 0x8E95, 0x8E94, 0x8E9A,
    0x8E92, 0x8E90, 0x8E96, 0x8E97, 0x8F60, 0x8F62, 0x9147, 0x944C, 0x9450,
    0x944A, 0x944B, 0x944F, 0x9447, 0x9445, 0x9448, 0x9449, 0x9446, 0x973F,
    0x97E3, 0x986A, 0x9869, 0x98CB, 0x9954, 0x995B, 0x9A4E, 0x9A53, 0x9A54,
    0x9A4C, 0x9A4F, 0x9A48, 0x9A4A, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x9A49, 0x9A52, 0x9A50, 0x9AD0, 0x9B19, 0x9B2B, 0x9B3B,
    0x9B56, 0x9B55, 0x9C46, 0x9C48, 0x9C3F, 0x9C44, 0x9C39, 0x9C33, 0x9C41,
    0x9C3C, 0x9C37, 0x9C34, 0x9C32, 0x9C3D, 0x9C36, 0x9DDB, 0x9DD2, 0x9DDE,
    0x9DDA, 0x9DCB, 0x9DD0, 0x9DDC, 0x9DD1, 0x9DDF, 0x9DE9, 0x9DD9, 0x9DD8,
    0x9DD6, 0x9DF5, 0x9DD5, 0x9DDD, 0x9EB6, 0x9EF0, 0x9F35, 0x9F33, 0x9F32,
    0x9F42, 0x9F6B, 0x9F95, 0x9FA2, 0x513D, 0x5299, 0x58E8, 0x58E7, 0x5972,
    0x5B4D, 0x5DD8, 0x882F, 0x5F4F, 0x6201, 0x6203, 0x6204, 0x6529, 0x6525,
    0x6596, 0x66EB, 0x6B11, 0x6B12, 0x6B0F, 0x6BCA, 0x705B, 0x705A, 0x7222,
    0x7382, 0x7381, 0x7383, 0x7670, 0x77D4, 0x7C67, 0x7C66, 0x7E95, 0x826C,
    0x863A, 0x8640, 0x8639, 0x863C, 0x8631, 0x863B, 0x863E, 0x8830, 0x8832,
    0x882E, 0x8833, 0x8976, 0x8974, 0x8973, 0x89FE, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x8B8C,
    0x8B8E, 0x8B8B, 0x8B88, 0x8C45, 0x8D19, 0x8E98, 0x8F64, 0x8F63, 0x91BC,
    0x9462, 0x9455, 0x945D, 0x9457, 0x945E, 0x97C4, 0x97C5, 0x9800, 0x9A56,
    0x9A59, 0x9B1E, 0x9B1F, 0x9B20, 0x9C52, 0x9C58, 0x9C50, 0x9C4A, 0x9C4D,
    0x9C4B, 0x9C55, 0x9C59, 0x9C4C, 0x9C4E, 0x9DFB, 0x9DF7, 0x9DEF, 0x9DE3,
    0x9DEB, 0x9DF8, 0x9DE4, 0x9DF6, 0x9DE1, 0x9DEE, 0x9DE6, 0x9DF2, 0x9DF0,
    0x9DE2, 0x9DEC, 0x9DF4, 0x9DF3, 0x9DE8, 0x9DED, 0x9EC2, 0x9ED0, 0x9EF2,
    0x9EF3, 0x9F06, 0x9F1C, 0x9F38, 0x9F37, 0x9F36, 0x9F43, 0x9F4F, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x9F71, 0x9F70, 0x9F6E,
    0x9F6F, 0x56D3, 0x56CD, 0x5B4E, 0x5C6D, 0x652D, 0x66ED, 0x66EE, 0x6B13,
    0x705F, 0x7061, 0x705D, 0x7060, 0x7223, 0x74DB, 0x74E5, 0x77D5, 0x7938,
    0x79B7, 0x79B6, 0x7C6A, 0x7E97, 0x7F89, 0x826D, 0x8643, 0x8838, 0x8837,
    0x8835, 0x884B, 0x8B94, 0x8B95, 0x8E9E, 0x8E9F, 0x8EA0, 0x8E9D, 0x91BE,
    0x91BD, 0x91C2, 0x946B, 0x9468, 0x9469, 0x96E5, 0x9746, 0x9743, 0x9747,
    0x97C7, 0x97E5, 0x9A5E, 0x9AD5, 0x9B59, 0x9C63, 0x9C67, 0x9C66, 0x9C62,
    0x9C5E, 0x9C60, 0x9E02, 0x9DFE, 0x9E07, 0x9E03, 0x9E06, 0x9E05, 0x9E00,
    0x9E01, 0x9E09, 0x9DFF, 0x9DFD, 0x9E04, 0x9EA0, 0x9F1E, 0x9F46, 0x9F74,
    0x9F75, 0x9F76, 0x56D4, 0x652E, 0x65B8, 0x6B18, 0x6B19, 0x6B17, 0x6B1A,
    0x7062, 0x7226, 0x72AA, 0x77D8, 0x77D9, 0x7939, 0x7C69, 0x7C6B, 0x7CF6,
    0x7E9A, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x7E98, 0x7E9B, 0x7E99, 0x81E0, 0x81E1, 0x8646,
    0x8647, 0x8648, 0x8979, 0x897A, 0x897C, 0x897B, 0x89FF, 0x8B98, 0x8B99,
    0x8EA5, 0x8EA4, 0x8EA3, 0x946E, 0x946D, 0x946F, 0x9471, 0x9473, 0x9749,
    0x9872, 0x995F, 0x9C68, 0x9C6E, 0x9C6D, 0x9E0B, 0x9E0D, 0x9E10, 0x9E0F,
    0x9E12, 0x9E11, 0x9EA1, 0x9EF5, 0x9F09, 0x9F47, 0x9F78, 0x9F7B, 0x9F7A,
    0x9F79, 0x571E, 0x7066, 0x7C6F, 0x883C, 0x8DB2, 0x8EA6, 0x91C3, 0x9474,
    0x9478, 0x9476, 0x9475, 0x9A60, 0x9C74, 0x9C73, 0x9C71, 0x9C75, 0x9E14,
    0x9E13, 0x9EF6, 0x9F0A, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x9FA4, 0x7068, 0x7065, 0x7CF7, 0x866A, 0x883E, 0x883D, 0x883F,
    0x8B9E, 0x8C9C, 0x8EA9, 0x8EC9, 0x974B, 0x9873, 0x9874, 0x98CC, 0x9961,
    0x99AB, 0x9A64, 0x9A66, 0x9A67, 0x9B24, 0x9E15, 0x9E17, 0x9F48, 0x6207,
    0x6B1E, 0x7227, 0x864C, 0x8EA8, 0x9482, 0x9480, 0x9481, 0x9A69, 0x9A68,
    0x9B2E, 0x9E19, 0x7229, 0x864B, 0x8B9F, 0x9483, 0x9C79, 0x9EB7, 0x7675,
    0x9A6B, 0x9C7A, 0x9E1D, 0x7069, 0x706A, 0x9EA4, 0x9F7E, 0x9F49, 0x9F98,
    0x7881, 0x92B9, 0x88CF, 0x58BB, 0x6052, 0x7CA7, 0x5AFA};

static int func_big5_uni_onechar(int code) {
  if ((code >= 0xA140) && (code <= 0xC7FC))
    return (tab_big5_uni0[code - 0xA140]);
  if ((code >= 0xC940) && (code <= 0xF9DC))
    return (tab_big5_uni1[code - 0xC940]);
  return (0);
}

/* page 0 0x00A2-0x00F7 */
static const uint16_t tab_uni_big50[] = {
    0xA246, 0xA247, 0,      0xA244, 0, 0xA1B1, 0, 0,     0,      0, 0, 0, 0,
    0,      0xA258, 0xA1D3, 0,      0, 0,      0, 0,     0xA150, 0, 0, 0, 0,
    0,      0,      0,      0,      0, 0,      0, 0,     0,      0, 0, 0, 0,
    0,      0,      0,      0,      0, 0,      0, 0,     0,      0, 0, 0, 0,
    0,      0xA1D1, 0,      0,      0, 0,      0, 0,     0,      0, 0, 0, 0,
    0,      0,      0,      0,      0, 0,      0, 0,     0,      0, 0, 0, 0,
    0,      0,      0,      0,      0, 0,      0, 0xA1D2};

/* page 1 0x02C7-0x0451 */
static const uint16_t tab_uni_big51[] = {
    0xA3BE, 0,      0xA3BC, 0xA3BD, 0xA3BF, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0xA3BB, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0xA344, 0xA345, 0xA346, 0xA347, 0xA348,
    0xA349, 0xA34A, 0xA34B, 0xA34C, 0xA34D, 0xA34E, 0xA34F, 0xA350, 0xA351,
    0xA352, 0xA353, 0xA354, 0,      0xA355, 0xA356, 0xA357, 0xA358, 0xA359,
    0xA35A, 0xA35B, 0,      0,      0,      0,      0,      0,      0,
    0xA35C, 0xA35D, 0xA35E, 0xA35F, 0xA360, 0xA361, 0xA362, 0xA363, 0xA364,
    0xA365, 0xA366, 0xA367, 0xA368, 0xA369, 0xA36A, 0xA36B, 0xA36C, 0,
    0xA36D, 0xA36E, 0xA36F, 0xA370, 0xA371, 0xA372, 0xA373, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0xC7B3,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0xC7B1, 0xC7B2, 0xC7B4, 0xC7B5, 0xC7B6, 0xC7B7, 0xC7B8, 0xC7B9, 0xC7BA,
    0,      0,      0,      0,      0,      0,      0xC7BB, 0xC7BC, 0xC7BD,
    0xC7BE, 0xC7BF, 0xC7C0, 0xC7C1, 0xC7C2, 0xC7C3, 0xC7C4, 0xC7C5, 0xC7C6,
    0xC7C7, 0xC7C8, 0xC7C9, 0xC7CA, 0xC7CB, 0xC7CC, 0xC7CD, 0xC7CF, 0xC7D0,
    0xC7D1, 0xC7D2, 0xC7D3, 0xC7D4, 0xC7D5, 0xC7D6, 0xC7D7, 0xC7D8, 0xC7D9,
    0xC7DA, 0xC7DB, 0xC7DC, 0xC7DD, 0xC7DE, 0xC7DF, 0xC7E0, 0xC7E1, 0xC7E2,
    0xC7E3, 0xC7E4, 0xC7E5, 0xC7E6, 0xC7E7, 0xC7E8, 0,      0xC7CE};

/* page 2 0x2013-0x22BF */
static const uint16_t tab_uni_big52[] = {
    0xA156, 0xA158, 0,      0,      0,      0xA1A5, 0xA1A6, 0,      0,
    0xA1A7, 0xA1A8, 0,      0,      0,      0,      0xA145, 0,      0,
    0xA14C, 0xA14B, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0xA1AC, 0,      0,      0xA1AB, 0,
    0,      0,      0,      0,      0xA1B0, 0,      0,      0xA1C2, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0xA24A, 0,      0xA1C1,
    0,      0,      0,      0xA24B, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0xA2B9, 0xA2BA, 0xA2BB, 0xA2BC, 0xA2BD, 0xA2BE, 0xA2BF, 0xA2C0, 0xA2C1,
    0xA2C2, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xA1F6, 0xA1F4, 0xA1F7, 0xA1F5, 0,      0,
    0xA1F8, 0xA1F9, 0xA1FB, 0xA1FA, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0xA1D4, 0,      0,
    0,      0xA1DB, 0xA1E8, 0xA1E7, 0,      0,      0xA1FD, 0,      0xA1FC,
    0,      0,      0,      0xA1E4, 0xA1E5, 0xA1EC, 0,      0,      0xA1ED,
    0,      0,      0,      0,      0,      0xA1EF, 0xA1EE, 0,      0,
    0,      0,      0,      0,      0xA1E3, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0xA1DC,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0xA1DA, 0xA1DD, 0,      0,      0,
    0,      0xA1D8, 0xA1D9, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0xA1E6, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0xA1E9};

/* page 3 0x2460-0x2642 */
static const uint16_t tab_uni_big53[] = {
    0xC7E9, 0xC7EA, 0xC7EB, 0xC7EC, 0xC7ED, 0xC7EE, 0xC7EF, 0xC7F0, 0xC7F1,
    0xC7F2, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0xC7F3, 0xC7F4, 0xC7F5, 0xC7F6, 0xC7F7, 0xC7F8, 0xC7F9,
    0xC7FA, 0xC7FB, 0xC7FC, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0xA277, 0,
    0xA278, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0xA27A, 0,      0,      0,      0xA27B, 0,      0,      0,
    0xA27C, 0,      0,      0,      0xA27D, 0,      0,      0,      0xA275,
    0,      0,      0,      0,      0,      0,      0,      0xA274, 0,
    0,      0,      0,      0,      0,      0,      0xA273, 0,      0,
    0,      0,      0,      0,      0,      0xA272, 0,      0,      0,
    0,      0,      0,      0,      0xA271, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0xA2A4, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0xA2A5, 0,      0,      0xA2A7, 0,      0,      0,
    0,      0,      0,      0,      0,      0xA2A6, 0,      0,      0xA27E,
    0xA2A1, 0xA2A3, 0xA2A2, 0xA2AC, 0xA2AD, 0xA2AE, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0xA262, 0xA263, 0xA264, 0xA265, 0xA266, 0xA267, 0xA268, 0xA269,
    0xA270, 0xA26F, 0xA26E, 0xA26D, 0xA26C, 0xA26B, 0xA26A, 0,      0,
    0,      0,      0xA276, 0xA279, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0xA1BD, 0xA1BC, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0xA1B6, 0xA1B5, 0,      0,
    0,      0,      0,      0,      0,      0,      0xA1BF, 0xA1BE, 0,
    0,      0,      0,      0,      0,      0,      0,      0xA1BB, 0xA1BA,
    0,      0,      0,      0xA1B3, 0,      0,      0xA1B7, 0xA1B4, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0xA2A8,
    0xA2A9, 0xA2AB, 0xA2AA, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0xA1B9, 0xA1B8,
    0,      0,      0xA1F3, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xA1F0, 0xA1F2, 0xA1F1};

/* page 4 0x3000-0x3129 */
static const uint16_t tab_uni_big54[] = {
    0xA140, 0xA142, 0xA143, 0xA1B2, 0,      0xC6A4, 0,      0,      0xA171,
    0xA172, 0xA16D, 0xA16E, 0xA175, 0xA176, 0xA179, 0xA17A, 0xA169, 0xA16A,
    0xA245, 0,      0xA165, 0xA166, 0,      0,      0,      0,      0,
    0,      0,      0xA1A9, 0xA1AA, 0,      0,      0xA2C3, 0xA2C4, 0xA2C5,
    0xA2C6, 0xA2C7, 0xA2C8, 0xA2C9, 0xA2CA, 0xA2CB, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0xC6A5, 0xC6A6, 0xC6A7, 0xC6A8, 0xC6A9, 0xC6AA, 0xC6AB,
    0xC6AC, 0xC6AD, 0xC6AE, 0xC6AF, 0xC6B0, 0xC6B1, 0xC6B2, 0xC6B3, 0xC6B4,
    0xC6B5, 0xC6B6, 0xC6B7, 0xC6B8, 0xC6B9, 0xC6BA, 0xC6BB, 0xC6BC, 0xC6BD,
    0xC6BE, 0xC6BF, 0xC6C0, 0xC6C1, 0xC6C2, 0xC6C3, 0xC6C4, 0xC6C5, 0xC6C6,
    0xC6C7, 0xC6C8, 0xC6C9, 0xC6CA, 0xC6CB, 0xC6CC, 0xC6CD, 0xC6CE, 0xC6CF,
    0xC6D0, 0xC6D1, 0xC6D2, 0xC6D3, 0xC6D4, 0xC6D5, 0xC6D6, 0xC6D7, 0xC6D8,
    0xC6D9, 0xC6DA, 0xC6DB, 0xC6DC, 0xC6DD, 0xC6DE, 0xC6DF, 0xC6E0, 0xC6E1,
    0xC6E2, 0xC6E3, 0xC6E4, 0xC6E5, 0xC6E6, 0xC6E7, 0xC6E8, 0xC6E9, 0xC6EA,
    0xC6EB, 0xC6EC, 0xC6ED, 0xC6EE, 0xC6EF, 0xC6F0, 0xC6F1, 0xC6F2, 0xC6F3,
    0xC6F4, 0xC6F5, 0xC6F6, 0xC6F7, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0xC6A2, 0xC6A3, 0,      0,      0xC6F8,
    0xC6F9, 0xC6FA, 0xC6FB, 0xC6FC, 0xC6FD, 0xC6FE, 0xC740, 0xC741, 0xC742,
    0xC743, 0xC744, 0xC745, 0xC746, 0xC747, 0xC748, 0xC749, 0xC74A, 0xC74B,
    0xC74C, 0xC74D, 0xC74E, 0xC74F, 0xC750, 0xC751, 0xC752, 0xC753, 0xC754,
    0xC755, 0xC756, 0xC757, 0xC758, 0xC759, 0xC75A, 0xC75B, 0xC75C, 0xC75D,
    0xC75E, 0xC75F, 0xC760, 0xC761, 0xC762, 0xC763, 0xC764, 0xC765, 0xC766,
    0xC767, 0xC768, 0xC769, 0xC76A, 0xC76B, 0xC76C, 0xC76D, 0xC76E, 0xC76F,
    0xC770, 0xC771, 0xC772, 0xC773, 0xC774, 0xC775, 0xC776, 0xC777, 0xC778,
    0xC779, 0xC77A, 0xC77B, 0xC77C, 0xC77D, 0xC77E, 0xC7A1, 0xC7A2, 0xC7A3,
    0xC7A4, 0xC7A5, 0xC7A6, 0xC7A7, 0xC7A8, 0xC7A9, 0xC7AA, 0xC7AB, 0xC7AC,
    0xC7AD, 0xC7AE, 0xC7AF, 0xC7B0, 0,      0,      0,      0,      0,
    0,      0,      0xC6A1, 0,      0,      0,      0,      0,      0,
    0xA374, 0xA375, 0xA376, 0xA377, 0xA378, 0xA379, 0xA37A, 0xA37B, 0xA37C,
    0xA37D, 0xA37E, 0xA3A1, 0xA3A2, 0xA3A3, 0xA3A4, 0xA3A5, 0xA3A6, 0xA3A7,
    0xA3A8, 0xA3A9, 0xA3AA, 0xA3AB, 0xA3AC, 0xA3AD, 0xA3AE, 0xA3AF, 0xA3B0,
    0xA3B1, 0xA3B2, 0xA3B3, 0xA3B4, 0xA3B5, 0xA3B6, 0xA3B7, 0xA3B8, 0xA3B9,
    0xA3BA};

/* page 5 0x32A3-0x32A3 */
static const uint16_t tab_uni_big55[] = {0xA1C0};

/* page 6 0x338E-0x33D5 */
static const uint16_t tab_uni_big56[] = {
    0xA255, 0xA256, 0,      0,      0, 0, 0,      0, 0, 0, 0, 0, 0,
    0,      0xA250, 0xA251, 0xA252, 0, 0, 0xA254, 0, 0, 0, 0, 0, 0,
    0,      0,      0,      0,      0, 0, 0,      0, 0, 0, 0, 0, 0,
    0,      0,      0,      0,      0, 0, 0,      0, 0, 0, 0, 0, 0,
    0,      0,      0xA257, 0,      0, 0, 0,      0, 0, 0, 0, 0, 0xA253,
    0,      0,      0xA1EB, 0xA1EA, 0, 0, 0xA24F};

/* page 7 0x4E00-0x9483 */
static const uint16_t tab_uni_big57[] = {
    0xA440, 0xA442, 0,      0xA443, 0,      0,      0,      0xC945, 0xA456,
    0xA454, 0xA457, 0xA455, 0xC946, 0xA4A3, 0xC94F, 0xC94D, 0xA4A2, 0xA4A1,
    0,      0,      0xA542, 0xA541, 0xA540, 0,      0xA543, 0xA4FE, 0,
    0,      0,      0,      0xA5E0, 0xA5E1, 0,      0,      0,      0,
    0,      0,      0xA8C3, 0,      0,      0,      0,      0xA458, 0,
    0xA4A4, 0xC950, 0,      0xA4A5, 0xC963, 0xA6EA, 0xCBB1, 0,      0,
    0,      0,      0xA459, 0xA4A6, 0,      0xA544, 0xC964, 0,      0,
    0,      0,      0,      0xC940, 0xA444, 0,      0xA45B, 0,      0xC947,
    0xA45C, 0,      0,      0xA4A7, 0,      0xA545, 0xA547, 0xA546, 0,
    0,      0xA5E2, 0xA5E3, 0,      0,      0xA8C4, 0,      0xADBC, 0xA441,
    0,      0,      0xC941, 0xA445, 0xA45E, 0xA45D, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0xA5E4, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0xA8C5, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0xB0AE, 0xD44B, 0,      0,      0xB6C3, 0xDCB1, 0xDCB2, 0,      0xA446,
    0,      0xA4A9, 0,      0,      0xA8C6, 0xA447, 0xC948, 0xA45F, 0,
    0,      0xA4AA, 0xA4AC, 0xC951, 0xA4AD, 0xA4AB, 0,      0,      0,
    0xA5E5, 0,      0xA8C7, 0,      0,      0xA8C8, 0xAB45, 0,      0xA460,
    0xA4AE, 0,      0xA5E6, 0xA5E8, 0xA5E7, 0,      0xA6EB, 0,      0,
    0xA8C9, 0xA8CA, 0xAB46, 0xAB47, 0,      0,      0,      0,      0xADBD,
    0,      0,      0xDCB3, 0,      0,      0xF6D6, 0xA448, 0,      0,
    0,      0,      0,      0xA4B0, 0xA4AF, 0xC952, 0xA4B1, 0xA4B7, 0,
    0xA4B2, 0xA4B3, 0xC954, 0xC953, 0xA4B5, 0xA4B6, 0,      0xA4B4, 0,
    0,      0,      0,      0,      0,      0xA54A, 0xA54B, 0xA54C, 0xA54D,
    0xA549, 0xA550, 0xC96A, 0,      0xC966, 0xC969, 0xA551, 0xA561, 0,
    0xC968, 0,      0xA54E, 0xA54F, 0xA548, 0,      0,      0xC965, 0xC967,
    0,      0,      0,      0,      0,      0,      0xA5F5, 0xC9B0, 0xA5F2,
    0xA5F6, 0xC9BA, 0xC9AE, 0xA5F3, 0xC9B2, 0,      0,      0,      0xA5F4,
    0,      0xA5F7, 0,      0xA5E9, 0xC9B1, 0xA5F8, 0xC9B5, 0,      0xC9B9,
    0xC9B6, 0,      0,      0xC9B3, 0xA5EA, 0xA5EC, 0xA5F9, 0,      0xA5EE,
    0xC9AB, 0xA5F1, 0xA5EF, 0xA5F0, 0xC9BB, 0xC9B8, 0xC9AF, 0xA5ED, 0,
    0,      0xC9AC, 0xA5EB, 0,      0,      0,      0xC9B4, 0,      0,
    0,      0,      0xC9B7, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xC9AD, 0xCA66, 0,      0xA742, 0xA6F4, 0,
    0,      0xCA67, 0xA6F1, 0,      0xA744, 0,      0xA6F9, 0,      0xA6F8,
    0xCA5B, 0xA6FC, 0xA6F7, 0xCA60, 0xCA68, 0,      0xCA64, 0,      0xA6FA,
    0,      0,      0xA6FD, 0xA6EE, 0xA747, 0xCA5D, 0,      0,      0xCBBD,
    0xA6EC, 0xA743, 0xA6ED, 0xA6F5, 0xA6F6, 0xCA62, 0xCA5E, 0xA6FB, 0xA6F3,
    0xCA5A, 0xA6EF, 0xCA65, 0xA745, 0xA748, 0xA6F2, 0xA740, 0xA746, 0xA6F0,
    0xCA63, 0xA741, 0xCA69, 0xCA5C, 0xA6FE, 0xCA5F, 0,      0,      0xCA61,
    0,      0xA8D8, 0xCBBF, 0xCBCB, 0xA8D0, 0,      0xCBCC, 0xA8CB, 0xA8D5,
    0,      0,      0xA8CE, 0xCBB9, 0xA8D6, 0xCBB8, 0xCBBC, 0xCBC3, 0xCBC1,
    0xA8DE, 0xA8D9, 0xCBB3, 0xCBB5, 0xA8DB, 0xA8CF, 0xCBB6, 0xCBC2, 0xCBC9,
    0xA8D4, 0xCBBB, 0xCBB4, 0xA8D3, 0xCBB7, 0xA8D7, 0xCBBA, 0,      0xA8D2,
    0,      0xA8CD, 0,      0xA8DC, 0xCBC4, 0xA8DD, 0xCBC8, 0,      0xCBC6,
    0xCBCA, 0xA8DA, 0xCBBE, 0xCBB2, 0,      0xCBC0, 0xA8D1, 0xCBC5, 0xA8CC,
    0xCBC7, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0xAB56, 0xAB4A,
    0,      0,      0xCDE0, 0xCDE8, 0,      0xAB49, 0xAB51, 0xAB5D, 0,
    0xCDEE, 0xCDEC, 0xCDE7, 0,      0,      0,      0xAB4B, 0xCDED, 0xCDE3,
    0xAB59, 0xAB50, 0xAB58, 0xCDDE, 0,      0xCDEA, 0,      0xCDE1, 0xAB54,
    0xCDE2, 0,      0xCDDD, 0xAB5B, 0xAB4E, 0xAB57, 0xAB4D, 0,      0xCDDF,
    0xCDE4, 0,      0xCDEB, 0xAB55, 0xAB52, 0xCDE6, 0xAB5A, 0xCDE9, 0xCDE5,
    0xAB4F, 0xAB5C, 0xAB53, 0xAB4C, 0xAB48, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0xCDEF, 0,      0xADD7,
    0xADC1, 0,      0xADD1, 0,      0xADD6, 0xD0D0, 0xD0CF, 0xD0D4, 0xD0D5,
    0xADC4, 0,      0xADCD, 0,      0,      0,      0xADDA, 0,      0xADCE,
    0,      0,      0,      0,      0xD0C9, 0xADC7, 0xD0CA, 0,      0xADDC,
    0,      0xADD3, 0xADBE, 0xADBF, 0xD0DD, 0xB0BF, 0,      0xADCC, 0xADCB,
    0xD0CB, 0xADCF, 0xD45B, 0xADC6, 0xD0D6, 0xADD5, 0xADD4, 0xADCA, 0xD0CE,
    0xD0D7, 0,      0xD0C8, 0xADC9, 0xD0D8, 0xADD2, 0xD0CC, 0xADC0, 0,
    0xADC3, 0xADC2, 0xD0D9, 0xADD0, 0xADC5, 0xADD9, 0xADDB, 0xD0D3, 0xADD8,
    0,      0xD0DB, 0xD0CD, 0xD0DC, 0,      0xD0D1, 0,      0xD0DA, 0,
    0xD0D2, 0,      0,      0,      0,      0xADC8, 0,      0,      0,
    0xD463, 0xD457, 0,      0xB0B3, 0,      0xD45C, 0xD462, 0xB0B2, 0xD455,
    0xB0B6, 0xD459, 0xD452, 0xB0B4, 0xD456, 0xB0B9, 0xB0BE, 0,      0xD467,
    0,      0xD451, 0,      0xB0BA, 0,      0xD466, 0,      0,      0xB0B5,
    0xD458, 0xB0B1, 0xD453, 0xD44F, 0xD45D, 0xD450, 0xD44E, 0xD45A, 0xD460,
    0xD461, 0xB0B7, 0,      0,      0xD85B, 0xD45E, 0xD44D, 0xD45F, 0,
    0xB0C1, 0xD464, 0xB0C0, 0xD44C, 0,      0xD454, 0xD465, 0xB0BC, 0xB0BB,
    0xB0B8, 0xB0BD, 0,      0,      0xB0AF, 0,      0,      0xB0B0, 0,
    0,      0xB3C8, 0,      0xD85E, 0xD857, 0,      0xB3C5, 0,      0xD85F,
    0,      0,      0,      0xD855, 0xD858, 0xB3C4, 0xD859, 0,      0,
    0xB3C7, 0xD85D, 0,      0xD853, 0xD852, 0xB3C9, 0,      0xB3CA, 0xB3C6,
    0xB3CB, 0xD851, 0xD85C, 0xD85A, 0xD854, 0,      0,      0,      0xB3C3,
    0xD856, 0,      0,      0,      0,      0,      0,      0,      0,
    0xB6CA, 0xB6C4, 0xDCB7, 0xB6CD, 0xDCBD, 0xDCC0, 0xB6C6, 0xB6C7, 0xDCBA,
    0xB6C5, 0xDCC3, 0xB6CB, 0xDCC4, 0,      0xDCBF, 0xB6CC, 0,      0xDCB4,
    0xB6C9, 0xDCB5, 0,      0xDCBE, 0xDCBC, 0,      0xDCB8, 0xB6C8, 0xDCB6,
    0xB6CE, 0xDCBB, 0xDCC2, 0xDCB9, 0xDCC1, 0,      0,      0xB9B6, 0xB9B3,
    0,      0xB9B4, 0,      0xE0F9, 0xE0F1, 0xB9B2, 0xB9AF, 0xE0F2, 0,
    0,      0xB9B1, 0xE0F5, 0,      0xE0F7, 0,      0,      0xE0FE, 0,
    0,      0xE0FD, 0xE0F8, 0xB9AE, 0xE0F0, 0xB9AC, 0xE0F3, 0xB9B7, 0xE0F6,
    0,      0xE0FA, 0xB9B0, 0xB9AD, 0xE0FC, 0xE0FB, 0xB9B5, 0,      0xE0F4,
    0,      0xBBF8, 0xE4EC, 0,      0xE4E9, 0xBBF9, 0,      0xBBF7, 0,
    0xE4F0, 0xE4ED, 0xE4E6, 0xBBF6, 0,      0xBBFA, 0xE4E7, 0xBBF5, 0xBBFD,
    0xE4EA, 0xE4EB, 0xBBFB, 0xBBFC, 0xE4F1, 0xE4EE, 0xE4EF, 0,      0,
    0,      0xBEAA, 0xE8F8, 0xBEA7, 0xE8F5, 0xBEA9, 0xBEAB, 0,      0xE8F6,
    0xBEA8, 0,      0xE8F7, 0,      0xE8F4, 0,      0,      0xC076, 0xECBD,
    0xC077, 0xECBB, 0,      0xECBC, 0xECBA, 0xECB9, 0,      0,      0xECBE,
    0xC075, 0,      0,      0xEFB8, 0xEFB9, 0,      0xE4E8, 0xEFB7, 0xC078,
    0xC35F, 0xF1EB, 0xF1EC, 0,      0xC4D7, 0xC4D8, 0xF5C1, 0xF5C0, 0xC56C,
    0xC56B, 0xF7D0, 0,      0xA449, 0xA461, 0xA4B9, 0,      0xA4B8, 0xA553,
    0xA552, 0xA5FC, 0xA5FB, 0xA5FD, 0xA5FA, 0,      0xA74A, 0xA749, 0xA74B,
    0,      0,      0,      0,      0xA8E0, 0,      0xA8DF, 0xA8E1, 0,
    0xAB5E, 0,      0xA259, 0xD0DE, 0xA25A, 0xB0C2, 0xA25C, 0xA25B, 0xD860,
    0,      0xA25D, 0xB9B8, 0xA25E, 0,      0xA44A, 0,      0xA4BA, 0xA5FE,
    0xA8E2, 0,      0xA44B, 0xA4BD, 0xA4BB, 0xA4BC, 0,      0,      0xA640,
    0,      0,      0,      0xA74C, 0xA8E4, 0xA8E3, 0xA8E5, 0,      0,
    0,      0xADDD, 0,      0,      0,      0xBEAC, 0,      0,      0,
    0,      0,      0,      0xC94E, 0,      0xA554, 0xA555, 0,      0,
    0xA641, 0,      0xCA6A, 0,      0xAB60, 0xAB5F, 0xD0E0, 0xD0DF, 0xB0C3,
    0,      0xA4BE, 0xC955, 0,      0,      0,      0,      0,      0xCBCD,
    0,      0xAB61, 0,      0xADE0, 0,      0xADDE, 0xADDF, 0,      0,
    0,      0,      0xBEAD, 0,      0xA556, 0,      0,      0,      0xA642,
    0xC9BC, 0,      0,      0,      0,      0xA74D, 0xA74E, 0,      0xCA6B,
    0,      0,      0xCBCE, 0xA8E6, 0xCBCF, 0,      0,      0,      0,
    0,      0xD0E2, 0xD0E3, 0xADE3, 0,      0xD0E4, 0,      0xD0E1, 0xADE4,
    0xADE2, 0xADE1, 0xD0E5, 0,      0xD468, 0,      0,      0,      0xD861,
    0,      0,      0xDCC5, 0xE140, 0,      0,      0,      0xBBFE, 0xBEAE,
    0xE8F9, 0,      0xA44C, 0xA45A, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0xB0C4, 0xB3CD, 0,      0xB9B9, 0,      0xC942, 0xA4BF, 0,      0xA559,
    0xA557, 0xA558, 0,      0,      0xA8E7, 0,      0,      0xA44D, 0xA44E,
    0,      0xA462, 0,      0,      0xA4C0, 0xA4C1, 0xA4C2, 0xC9BE, 0xA55A,
    0,      0xC96B, 0,      0xA646, 0,      0xC9BF, 0xA644, 0xA645, 0xC9BD,
    0,      0,      0xA647, 0xA643, 0,      0,      0,      0,      0xCA6C,
    0xAAEC, 0xCA6D, 0,      0,      0xCA6E, 0,      0,      0xA750, 0xA74F,
    0,      0,      0xA753, 0xA751, 0xA752, 0,      0,      0,      0xA8ED,
    0,      0xA8EC, 0xCBD4, 0xCBD1, 0xCBD2, 0,      0xCBD0, 0xA8EE, 0xA8EA,
    0xA8E9, 0,      0xA8EB, 0xA8E8, 0,      0,      0,      0,      0,
    0xA8EF, 0,      0xAB63, 0xCDF0, 0,      0xCBD3, 0xAB68, 0,      0xCDF1,
    0xAB64, 0xAB67, 0xAB66, 0xAB65, 0xAB62, 0,      0,      0,      0xD0E8,
    0,      0xADE7, 0xD0EB, 0xADE5, 0,      0,      0,      0xD0E7, 0xADE8,
    0xADE6, 0xADE9, 0xD0E9, 0xD0EA, 0,      0xD0E6, 0xD0EC, 0,      0,
    0,      0,      0,      0,      0xB3D1, 0xB0C5, 0xD469, 0xD46B, 0xD46A,
    0xD46C, 0xB0C6, 0,      0,      0xB3CE, 0,      0xB3CF, 0xB3D0, 0,
    0xB6D0, 0xDCC7, 0,      0xDCC6, 0xDCC8, 0xDCC9, 0xB6D1, 0,      0xB6CF,
    0xE141, 0xE142, 0xB9BB, 0xB9BA, 0xE35A, 0,      0,      0xBC40, 0xBC41,
    0xBC42, 0xBC44, 0xE4F2, 0xE4F3, 0xBC43, 0,      0,      0,      0xBEAF,
    0,      0xBEB0, 0,      0,      0xF1ED, 0xF5C3, 0xF5C2, 0xF7D1, 0,
    0xA44F, 0,      0,      0,      0xA55C, 0xA55B, 0,      0,      0xA648,
    0,      0,      0xC9C0, 0,      0,      0xA755, 0xA756, 0xA754, 0xA757,
    0xCA6F, 0xCA70, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0xA8F1, 0xCBD5, 0,      0xA8F0,
    0,      0xCDF2, 0xAB6C, 0xCDF3, 0xAB6B, 0,      0,      0,      0xAB69,
    0,      0xAB6A, 0,      0,      0,      0xD0ED, 0,      0,      0,
    0,      0xB0C7, 0xD46E, 0,      0xB0CA, 0xD46D, 0xB1E5, 0xB0C9, 0xB0C8,
    0,      0xB3D4, 0,      0xB3D3, 0xB3D2, 0xB6D2, 0,      0,      0xB6D5,
    0xB6D6, 0xB6D4, 0,      0xB6D3, 0,      0,      0xE143, 0,      0xE144,
    0,      0,      0,      0xE4F5, 0xBC45, 0xE4F4, 0,      0xBEB1, 0xECBF,
    0xC079, 0,      0xF1EE, 0xC455, 0,      0xA463, 0xA4C3, 0xC956, 0,
    0xA4C4, 0xA4C5, 0,      0,      0,      0,      0,      0xA55D, 0xA55E,
    0,      0xA649, 0xCA71, 0xCBD6, 0xCBD7, 0,      0xAB6D, 0xD0EE, 0xB0CC,
    0xB0CB, 0xD863, 0xD862, 0,      0,      0xA450, 0xA4C6, 0xA55F, 0,
    0xB0CD, 0xC943, 0,      0xC96C, 0xA560, 0,      0xC9C2, 0xA64B, 0xA64A,
    0xC9C1, 0xA758, 0,      0,      0,      0,      0,      0,      0xADEA,
    0,      0,      0xD46F, 0,      0xB6D7, 0xE145, 0xB9BC, 0,      0,
    0xE8FA, 0,      0,      0xF3FD, 0,      0xA4C7, 0,      0,      0xCBD8,
    0xCDF4, 0xB0D0, 0xB0CE, 0xB0CF, 0xA451, 0,      0xA464, 0xA2CD, 0xA4CA,
    0,      0xA4C9, 0xA4C8, 0xA563, 0xA562, 0,      0xC96D, 0xC9C3, 0,
    0,      0,      0xA8F5, 0xA8F2, 0xA8F4, 0xA8F3, 0,      0,      0xAB6E,
    0,      0,      0xB3D5, 0,      0xA452, 0,      0xA4CB, 0,      0xA565,
    0xA564, 0,      0xCA72, 0,      0,      0xA8F6, 0,      0,      0,
    0,      0,      0xC957, 0,      0xA567, 0xA566, 0xA64C, 0xA64D, 0xCA73,
    0xA759, 0,      0xA75A, 0,      0xA8F7, 0xA8F8, 0xA8F9, 0,      0xAB6F,
    0xCDF5, 0,      0,      0xADEB, 0,      0,      0xC944, 0,      0xA4CC,
    0,      0,      0,      0,      0,      0xC9C4, 0,      0,      0,
    0xCA74, 0xCA75, 0,      0,      0xCBD9, 0,      0xCBDA, 0,      0xCDF7,
    0xCDF6, 0xCDF9, 0xCDF8, 0xAB70, 0,      0xD470, 0xADED, 0xD0EF, 0xADEC,
    0,      0,      0,      0,      0xD864, 0xB3D6, 0,      0xD865, 0,
    0,      0,      0,      0xE146, 0xB9BD, 0,      0,      0,      0,
    0xBC46, 0,      0xF1EF, 0,      0,      0,      0,      0xC958, 0,
    0xA568, 0,      0,      0,      0,      0,      0,      0,      0xB0D1,
    0,      0,      0,      0,      0xA453, 0xA465, 0xA4CE, 0xA4CD, 0,
    0xA4CF, 0,      0,      0,      0,      0,      0,      0xA8FB, 0,
    0xA8FA, 0xA8FC, 0,      0,      0,      0xAB71, 0,      0,      0,
    0xADEE, 0,      0xE8FB, 0xC24F, 0xA466, 0xA56A, 0xA579, 0xA574, 0,
    0xA56F, 0xA56E, 0xA575, 0xA573, 0xA56C, 0xA57A, 0xA56D, 0xA569, 0xA578,
    0xA577, 0xA576, 0xA56B, 0,      0xA572, 0,      0,      0xA571, 0,
    0,      0xA57B, 0xA570, 0,      0,      0,      0,      0xA653, 0,
    0xA659, 0xA655, 0,      0xA65B, 0xC9C5, 0xA658, 0xA64E, 0xA651, 0xA654,
    0xA650, 0xA657, 0xA65A, 0xA64F, 0xA652, 0xA656, 0xA65C, 0,      0,
    0,      0,      0,      0xCA7E, 0xCA7B, 0,      0xA767, 0xCA7C, 0xA75B,
    0xA75D, 0xA775, 0xA770, 0,      0,      0,      0xCAA5, 0xCA7D, 0xA75F,
    0xA761, 0xCAA4, 0xA768, 0xCA78, 0xA774, 0xA776, 0xA75C, 0xA76D, 0,
    0xCA76, 0xA773, 0,      0xA764, 0,      0xA76E, 0xA76F, 0xCA77, 0xA76C,
    0xA76A, 0,      0xA76B, 0xA771, 0xCAA1, 0xA75E, 0,      0xA772, 0xCAA3,
    0xA766, 0xA763, 0,      0xCA7A, 0xA762, 0xCAA6, 0xA765, 0,      0xA769,
    0,      0,      0,      0xA760, 0xCAA2, 0,      0,      0,      0,
    0xCA79, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xCBEB, 0xCBEA, 0xA94F, 0xCBED, 0xCBEF, 0xCBE4,
    0xCBE7, 0xCBEE, 0xA950, 0,      0,      0xCBE1, 0xCBE5, 0,      0,
    0xCBE9, 0xCE49, 0xA94B, 0xCE4D, 0xA8FD, 0xCBE6, 0xA8FE, 0xA94C, 0xA945,
    0xA941, 0,      0xCBE2, 0xA944, 0xA949, 0xA952, 0xCBE3, 0xCBDC, 0xA943,
    0xCBDD, 0xCBDF, 0,      0xA946, 0,      0xA948, 0xCBDB, 0xCBE0, 0,
    0,      0xA951, 0xA94D, 0xCBE8, 0xA953, 0,      0xA94A, 0xCBDE, 0xA947,
    0,      0,      0xA942, 0xA940, 0,      0xCBEC, 0,      0xA94E, 0,
    0,      0,      0,      0,      0xCE48, 0xCDFB, 0xCE4B, 0,      0,
    0xCDFD, 0xAB78, 0xABA8, 0xAB74, 0xABA7, 0xAB7D, 0xABA4, 0xAB72, 0xCDFC,
    0xCE43, 0xABA3, 0xCE4F, 0xABA5, 0,      0xAB79, 0,      0,      0xCE45,
    0xCE42, 0xAB77, 0,      0xCDFA, 0xABA6, 0xCE4A, 0xAB7C, 0xCE4C, 0xABA9,
    0xAB73, 0xAB7E, 0xAB7B, 0xCE40, 0xABA1, 0xCE46, 0xCE47, 0xAB7A, 0xABA2,
    0xAB76, 0,      0,      0,      0,      0xAB75, 0xCDFE, 0,      0,
    0,      0,      0,      0,      0xCE44, 0,      0,      0,      0,
    0,      0,      0,      0xCE4E, 0,      0xD144, 0xADFB, 0xD0F1, 0,
    0xD0F6, 0xADF4, 0xAE40, 0xD0F4, 0xADEF, 0xADF9, 0xADFE, 0xD0FB, 0,
    0xADFA, 0xADFD, 0,      0,      0xD0FE, 0xADF5, 0xD0F5, 0,      0,
    0,      0xD142, 0xD143, 0,      0xADF7, 0xD141, 0xADF3, 0xAE43, 0,
    0xD0F8, 0,      0xADF1, 0,      0xD146, 0xD0F9, 0xD0FD, 0xADF6, 0xAE42,
    0xD0FA, 0xADFC, 0xD140, 0xD147, 0xD4A1, 0,      0xD145, 0xAE44, 0xADF0,
    0xD0FC, 0xD0F3, 0,      0xADF8, 0,      0,      0xD0F2, 0,      0,
    0xD0F7, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xD0F0, 0xAE41, 0,      0,      0xD477, 0,
    0xB0E4, 0xD4A7, 0xB0E2, 0xB0DF, 0xD47C, 0xB0DB, 0xD4A2, 0xB0E6, 0xD476,
    0xD47B, 0xD47A, 0xADF2, 0xB0E1, 0xD4A5, 0,      0xD4A8, 0xD473, 0,
    0xB3E8, 0,      0xD4A9, 0xB0E7, 0,      0xB0D9, 0xB0D6, 0xD47E, 0xB0D3,
    0,      0xD4A6, 0,      0xB0DA, 0xD4AA, 0,      0xD474, 0xD4A4, 0xB0DD,
    0xD475, 0xD478, 0xD47D, 0,      0,      0xB0DE, 0xB0DC, 0xB0E8, 0,
    0,      0,      0,      0xB0E3, 0,      0xB0D7, 0xB1D2, 0,      0xB0D8,
    0xD479, 0xB0E5, 0xB0E0, 0xD4A3, 0xB0D5, 0,      0,      0,      0xB0D4,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0xD471, 0xD472, 0xD86A, 0,      0,      0,      0xB3D7, 0xB3DA,
    0xD875, 0xB3EE, 0xD878, 0xB3D8, 0xD871, 0xB3DE, 0xB3E4, 0xB5BD, 0,
    0,      0xB3E2, 0xD86E, 0xB3EF, 0xB3DB, 0xB3E3, 0xD876, 0xDCD7, 0xD87B,
    0xD86F, 0,      0xD866, 0xD873, 0xD86D, 0xB3E1, 0xD879, 0,      0,
    0xB3DD, 0xB3F1, 0xB3EA, 0,      0xB3DF, 0xB3DC, 0,      0xB3E7, 0,
    0xD87A, 0xD86C, 0xD872, 0xD874, 0xD868, 0xD877, 0xB3D9, 0xD867, 0,
    0xB3E0, 0xB3F0, 0xB3EC, 0xD869, 0xB3E6, 0,      0,      0xB3ED, 0xB3E9,
    0xB3E5, 0,      0xD870, 0,      0,      0,      0,      0,      0xB3EB,
    0,      0,      0,      0xDCD5, 0xDCD1, 0,      0xDCE0, 0xDCCA, 0xDCD3,
    0xB6E5, 0xB6E6, 0xB6DE, 0xDCDC, 0xB6E8, 0xDCCF, 0xDCCE, 0xDCCC, 0xDCDE,
    0xB6DC, 0xDCD8, 0xDCCD, 0xB6DF, 0xDCD6, 0xB6DA, 0xDCD2, 0xDCD9, 0xDCDB,
    0,      0,      0xDCDF, 0xB6E3, 0xDCCB, 0xB6DD, 0xDCD0, 0,      0xB6D8,
    0,      0xB6E4, 0xDCDA, 0xB6E0, 0xB6E1, 0xB6E7, 0xB6DB, 0xA25F, 0xB6D9,
    0xDCD4, 0,      0,      0,      0,      0,      0xB6E2, 0,      0,
    0xDCDD, 0,      0,      0,      0xB9CD, 0xB9C8, 0,      0xE155, 0xE151,
    0,      0xE14B, 0xB9C2, 0xB9BE, 0xE154, 0xB9BF, 0xE14E, 0xE150, 0,
    0xE153, 0,      0xB9C4, 0,      0xB9CB, 0xB9C5, 0,      0,      0xE149,
    0xB9C6, 0xB9C7, 0xE14C, 0xB9CC, 0,      0xE14A, 0xE14F, 0xB9C3, 0xE148,
    0xB9C9, 0xB9C1, 0,      0,      0,      0xB9C0, 0xE14D, 0xE152, 0,
    0xB9CA, 0,      0,      0,      0,      0,      0,      0,      0xE147,
    0,      0xBC4D, 0xE547, 0,      0xE544, 0,      0xBC47, 0xBC53, 0xBC54,
    0,      0xBC4A, 0xE542, 0xBC4C, 0xE4F9, 0xBC52, 0,      0xE546, 0xBC49,
    0xE548, 0xBC48, 0,      0xE543, 0xE545, 0xBC4B, 0xE541, 0xE4FA, 0xE4F7,
    0,      0,      0xD86B, 0xE4FD, 0,      0xE4F6, 0xE4FC, 0xE4FB, 0,
    0xE4F8, 0,      0xBC4F, 0,      0,      0,      0,      0xBC4E, 0,
    0,      0,      0xBC50, 0xE4FE, 0xBEB2, 0xE540, 0,      0,      0,
    0xE945, 0,      0xE8FD, 0,      0xBEBE, 0xE942, 0xBEB6, 0xBEBA, 0xE941,
    0,      0xBEB9, 0xBEB5, 0xBEB8, 0xBEB3, 0xBEBD, 0xE943, 0xE8FE, 0xBEBC,
    0xE8FC, 0xBEBB, 0xE944, 0xE940, 0xBC51, 0,      0xBEBF, 0xE946, 0xBEB7,
    0xBEB4, 0,      0,      0,      0,      0xECC6, 0xECC8, 0xC07B, 0xECC9,
    0xECC7, 0xECC5, 0xECC4, 0xC07D, 0xECC3, 0xC07E, 0,      0,      0,
    0,      0xECC1, 0xECC2, 0xC07A, 0xC0A1, 0xC07C, 0,      0,      0xECC0,
    0,      0xC250, 0,      0xEFBC, 0xEFBA, 0xEFBF, 0xEFBD, 0,      0xEFBB,
    0xEFBE, 0,      0,      0,      0,      0,      0,      0,      0xC360,
    0xF1F2, 0xF1F3, 0xC456, 0,      0xF1F4, 0xF1F0, 0xF1F5, 0xF1F1, 0xC251,
    0,      0,      0,      0xF3FE, 0xF441, 0xC459, 0xF440, 0xC458, 0xC457,
    0,      0,      0,      0,      0xC45A, 0xF5C5, 0xF5C6, 0,      0xC4DA,
    0xC4D9, 0xC4DB, 0xF5C4, 0,      0xF6D8, 0xF6D7, 0,      0xC56D, 0xC56F,
    0xC56E, 0xF6D9, 0xC5C8, 0xF8A6, 0,      0,      0,      0xC5F1, 0,
    0xF8A5, 0xF8EE, 0,      0,      0xC949, 0,      0,      0xA57D, 0xA57C,
    0,      0xA65F, 0xA65E, 0xC9C7, 0xA65D, 0xC9C6, 0,      0,      0xA779,
    0xCAA9, 0,      0xCAA8, 0,      0,      0xA777, 0xA77A, 0,      0,
    0xCAA7, 0,      0xA778, 0,      0,      0,      0,      0,      0,
    0xCBF0, 0,      0xCBF1, 0xA954, 0,      0,      0,      0,      0xABAA,
    0,      0xD148, 0xD149, 0xAE45, 0xAE46, 0,      0,      0xD4AC, 0xB0E9,
    0xB0EB, 0xD4AB, 0xB0EA, 0xD87C, 0xB3F2, 0,      0,      0,      0,
    0xB6E9, 0xB6EA, 0xDCE1, 0,      0xB9CF, 0,      0xB9CE, 0,      0xE549,
    0xE948, 0xE947, 0,      0xF96B, 0xA467, 0xC959, 0,      0xC96E, 0xC96F,
    0,      0,      0,      0,      0xA662, 0xA666, 0xC9C9, 0,      0xA664,
    0xA663, 0xC9C8, 0xA665, 0xA661, 0,      0,      0xA660, 0xC9CA, 0,
    0,      0,      0,      0,      0,      0xA7A6, 0,      0,      0xA7A3,
    0,      0xA77D, 0xCAAA, 0,      0,      0,      0xCAAB, 0,      0xA7A1,
    0,      0xCAAD, 0xA77B, 0xCAAE, 0xCAAC, 0xA77E, 0xA7A2, 0xA7A5, 0xA7A4,
    0xA77C, 0xCAAF, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0xA959, 0xCBFE,
    0,      0xA95B, 0,      0xA95A, 0,      0xCC40, 0xA958, 0xA957, 0xCBF5,
    0,      0xCBF4, 0,      0xCBF2, 0xCBF7, 0xCBF6, 0xCBF3, 0xCBFC, 0xCBFD,
    0xCBFA, 0xCBF8, 0xA956, 0,      0,      0,      0xCBFB, 0xA95C, 0xCC41,
    0,      0,      0xCBF9, 0,      0xABAB, 0xA955, 0,      0,      0,
    0,      0,      0,      0,      0xABAC, 0xCE54, 0,      0,      0xCE5A,
    0,      0,      0,      0xABB2, 0xCE58, 0xCE5E, 0,      0xCE55, 0xCE59,
    0xCE5B, 0xCE5D, 0xCE57, 0,      0xCE56, 0xCE51, 0xCE52, 0xABAD, 0,
    0xABAF, 0xABAE, 0xCE53, 0xCE5C, 0,      0,      0,      0,      0,
    0,      0,      0,      0xABB1, 0,      0,      0,      0,      0,
    0,      0xCE50, 0xD153, 0,      0xD152, 0xD157, 0xD14E, 0,      0xD151,
    0xD150, 0,      0xD154, 0,      0xD158, 0xAE47, 0xAE4A, 0,      0,
    0xD14F, 0xD155, 0,      0,      0,      0xAE49, 0xD14A, 0,      0xABB0,
    0xD4BA, 0xD156, 0,      0xD14D, 0,      0xAE48, 0xD14C, 0,      0,
    0,      0,      0,      0,      0xD4B1, 0,      0,      0xB0EC, 0xB0F0,
    0xD4C1, 0xD4AF, 0xD4BD, 0xB0F1, 0xD4BF, 0,      0xD4C5, 0,      0xD4C9,
    0,      0,      0xD4C0, 0xD4B4, 0xD4BC, 0,      0xD4CA, 0xD4C8, 0xD4BE,
    0xD4B9, 0xD4B2, 0xD8A6, 0xD4B0, 0xB0F5, 0xD4B7, 0xB0F6, 0xB0F2, 0xD4AD,
    0xD4C3, 0xD4B5, 0,      0,      0xD4B3, 0xD4C6, 0xB0F3, 0,      0xD4CC,
    0xB0ED, 0xB0EF, 0xD4BB, 0xD4B6, 0xAE4B, 0xB0EE, 0xD4B8, 0xD4C7, 0xD4CB,
    0xD4C2, 0,      0xD4C4, 0,      0,      0,      0xD4AE, 0,      0,
    0,      0,      0xD8A1, 0,      0xD8AA, 0xD8A9, 0xB3FA, 0xD8A2, 0,
    0xB3FB, 0xB3F9, 0,      0xD8A4, 0xB3F6, 0xD8A8, 0,      0xD8A3, 0xD8A5,
    0xD87D, 0xB3F4, 0,      0xD8B2, 0xD8B1, 0xD8AE, 0xB3F3, 0xB3F7, 0xB3F8,
    0xD14B, 0xD8AB, 0xB3F5, 0xB0F4, 0xD8AD, 0xD87E, 0xD8B0, 0xD8AF, 0,
    0xD8B3, 0,      0xDCEF, 0,      0xD8AC, 0,      0,      0,      0,
    0,      0,      0,      0,      0xD8A7, 0xDCE7, 0xB6F4, 0xB6F7, 0xB6F2,
    0xDCE6, 0xDCEA, 0xDCE5, 0,      0xB6EC, 0xB6F6, 0xDCE2, 0xB6F0, 0xDCE9,
    0,      0xB6EE, 0xB6ED, 0xDCEC, 0xB6EF, 0xDCEE, 0,      0xDCEB, 0xB6EB,
    0,      0,      0,      0xB6F5, 0xDCF0, 0xDCE4, 0xDCED, 0,      0,
    0xDCE3, 0,      0,      0xB6F1, 0,      0xB6F3, 0,      0xDCE8, 0,
    0xDCF1, 0,      0,      0xE15D, 0xB9D0, 0xE163, 0,      0,      0xB9D5,
    0xE15F, 0xE166, 0xE157, 0xB9D7, 0xB9D1, 0xE15C, 0xBC55, 0xE15B, 0xE164,
    0xB9D2, 0,      0xB9D6, 0xE15A, 0xE160, 0xE165, 0xE156, 0xB9D4, 0xE15E,
    0,      0,      0xE162, 0xE168, 0xE158, 0xE161, 0,      0xB9D3, 0xE167,
    0,      0,      0,      0xE159, 0,      0,      0,      0xBC59, 0xE54B,
    0xBC57, 0xBC56, 0xE54D, 0xE552, 0,      0xE54E, 0,      0xE551, 0xBC5C,
    0,      0xBEA5, 0xBC5B, 0,      0xE54A, 0xE550, 0,      0xBC5A, 0xE54F,
    0,      0xE54C, 0,      0xBC58, 0,      0,      0,      0,      0,
    0,      0xE94D, 0xF9D9, 0xE94F, 0xE94A, 0xBEC1, 0xE94C, 0,      0xBEC0,
    0xE94E, 0,      0,      0xBEC3, 0xE950, 0xBEC2, 0xE949, 0xE94B, 0,
    0,      0,      0,      0xC0A5, 0xECCC, 0,      0xC0A4, 0xECCD, 0xC0A3,
    0xECCB, 0xC0A2, 0xECCA, 0,      0xC253, 0xC252, 0xF1F6, 0xF1F8, 0,
    0xF1F7, 0xC361, 0xC362, 0,      0,      0xC363, 0xF442, 0xC45B, 0,
    0,      0xF7D3, 0xF7D2, 0xC5F2, 0,      0xA468, 0xA4D0, 0,      0,
    0xA7A7, 0,      0,      0,      0,      0xCE5F, 0,      0,      0,
    0,      0xB3FC, 0xB3FD, 0,      0xDCF2, 0xB9D8, 0xE169, 0xE553, 0,
    0,      0,      0xC95A, 0,      0,      0xCAB0, 0,      0,      0,
    0,      0,      0xCC42, 0xCE60, 0xD159, 0xAE4C, 0,      0,      0xF1F9,
    0,      0xC4DC, 0xA469, 0xA57E, 0xC970, 0,      0xA667, 0xA668, 0,
    0xA95D, 0,      0,      0,      0xB0F7, 0,      0xB9DA, 0,      0xB9DB,
    0xB9D9, 0,      0xA46A, 0,      0xA4D1, 0xA4D3, 0xA4D2, 0xC95B, 0xA4D4,
    0xA5A1, 0xC971, 0,      0xA5A2, 0,      0,      0,      0,      0,
    0xA669, 0xA66A, 0,      0,      0,      0xC9CB, 0,      0xA7A8, 0,
    0xCAB1, 0,      0,      0,      0xA961, 0xCC43, 0,      0xA95F, 0xA960,
    0xA95E, 0xD15A, 0,      0,      0,      0xABB6, 0xABB5, 0xABB7, 0xABB4,
    0,      0xCE61, 0xA962, 0xABB3, 0,      0xAE4D, 0xAE4E, 0,      0xAE4F,
    0,      0xD4CD, 0,      0,      0,      0xB3FE, 0xD8B4, 0xB0F8, 0,
    0,      0,      0,      0xB6F8, 0,      0xB9DD, 0xB9DC, 0xE16A, 0,
    0xBC5D, 0xBEC4, 0,      0xEFC0, 0xF6DA, 0xF7D4, 0xA46B, 0xA5A3, 0,
    0xA5A4, 0xC9D1, 0xA66C, 0xA66F, 0,      0xC9CF, 0xC9CD, 0xA66E, 0xC9D0,
    0xC9D2, 0xC9CC, 0xA671, 0xA670, 0xA66D, 0xA66B, 0xC9CE, 0,      0,
    0,      0,      0xA7B3, 0,      0,      0xA7B0, 0xCAB6, 0xCAB9, 0xCAB8,
    0,      0xA7AA, 0xA7B2, 0,      0,      0xA7AF, 0xCAB5, 0xCAB3, 0xA7AE,
    0,      0,      0,      0xA7A9, 0xA7AC, 0,      0xCAB4, 0xCABB, 0xCAB7,
    0xA7AD, 0xA7B1, 0xA7B4, 0xCAB2, 0xCABA, 0xA7AB, 0,      0,      0,
    0,      0,      0xA967, 0xA96F, 0,      0xCC4F, 0xCC48, 0xA970, 0xCC53,
    0xCC44, 0xCC4B, 0,      0,      0xA966, 0xCC45, 0xA964, 0xCC4C, 0xCC50,
    0xA963, 0,      0xCC51, 0xCC4A, 0,      0xCC4D, 0,      0xA972, 0xA969,
    0xCC54, 0xCC52, 0,      0xA96E, 0xA96C, 0xCC49, 0xA96B, 0xCC47, 0xCC46,
    0xA96A, 0xA968, 0xA971, 0xA96D, 0xA965, 0,      0xCC4E, 0,      0xABB9,
    0,      0xABC0, 0xCE6F, 0xABB8, 0xCE67, 0xCE63, 0,      0xCE73, 0xCE62,
    0,      0xABBB, 0xCE6C, 0xABBE, 0xABC1, 0,      0xABBC, 0xCE70, 0xABBF,
    0,      0xAE56, 0xCE76, 0xCE64, 0,      0,      0xCE66, 0xCE6D, 0xCE71,
    0xCE75, 0xCE72, 0xCE6B, 0xCE6E, 0,      0,      0xCE68, 0xABC3, 0xCE6A,
    0xCE69, 0xCE74, 0xABBA, 0xCE65, 0xABC2, 0,      0xABBD, 0,      0,
    0,      0,      0,      0xAE5C, 0xD162, 0,      0xAE5B, 0,      0,
    0xD160, 0,      0xAE50, 0,      0xAE55, 0,      0xD15F, 0xD15C, 0xD161,
    0xAE51, 0xD15B, 0,      0xAE54, 0xAE52, 0,      0xD163, 0xAE53, 0xAE57,
    0,      0,      0xAE58, 0,      0xAE5A, 0,      0,      0,      0xAE59,
    0,      0,      0,      0xD15D, 0xD15E, 0,      0,      0,      0,
    0xD164, 0,      0xD4D4, 0xB0F9, 0xD8C2, 0xD4D3, 0xD4E6, 0,      0,
    0xB140, 0,      0xD4E4, 0,      0xB0FE, 0xB0FA, 0xD4ED, 0xD4DD, 0xD4E0,
    0,      0xB143, 0xD4EA, 0xD4E2, 0xB0FB, 0xB144, 0,      0xD4E7, 0xD4E5,
    0,      0,      0xD4D6, 0xD4EB, 0xD4DF, 0xD4DA, 0,      0xD4D0, 0xD4EC,
    0xD4DC, 0xD4CF, 0,      0xB142, 0xD4E1, 0xD4EE, 0xD4DE, 0xD4D2, 0xD4D7,
    0xD4CE, 0,      0xB141, 0,      0xD4DB, 0xD4D8, 0xB0FC, 0xD4D1, 0,
    0xD4E9, 0xB0FD, 0,      0xD4D9, 0xD4D5, 0,      0,      0xD4E8, 0,
    0,      0,      0,      0,      0,      0xB440, 0xD8BB, 0,      0xD8B8,
    0xD8C9, 0xD8BD, 0xD8CA, 0,      0xB442, 0,      0,      0,      0xD8C6,
    0xD8C3, 0,      0,      0,      0,      0,      0xD8C4, 0xD8C7, 0xD8CB,
    0,      0xD4E3, 0xD8CD, 0xDD47, 0,      0xB443, 0xD8CE, 0xD8B6, 0xD8C0,
    0,      0xD8C5, 0,      0,      0xB441, 0xB444, 0xD8CC, 0xD8CF, 0xD8BA,
    0xD8B7, 0,      0,      0xD8B9, 0,      0,      0xD8BE, 0xD8BC, 0xB445,
    0,      0xD8C8, 0,      0,      0xD8BF, 0,      0xD8C1, 0xD8B5, 0xDCFA,
    0xDCF8, 0xB742, 0xB740, 0xDD43, 0xDCF9, 0xDD44, 0xDD40, 0xDCF7, 0xDD46,
    0xDCF6, 0xDCFD, 0xB6FE, 0xB6FD, 0xB6FC, 0xDCFB, 0xDD41, 0xB6F9, 0xB741,
    0,      0xDCF4, 0,      0xDCFE, 0xDCF3, 0xDCFC, 0xB6FA, 0xDD42, 0xDCF5,
    0xB6FB, 0xDD45, 0,      0,      0,      0,      0,      0,      0,
    0xE16E, 0xB9E2, 0xB9E1, 0xB9E3, 0xE17A, 0xE170, 0xE176, 0xE16B, 0xE179,
    0xE178, 0xE17C, 0xE175, 0xB9DE, 0xE174, 0xB9E4, 0,      0xE16D, 0xB9DF,
    0,      0xE17B, 0xB9E0, 0xE16F, 0xE172, 0xE177, 0xE171, 0xE16C, 0,
    0,      0,      0,      0xE173, 0xE555, 0xBC61, 0xE558, 0xE557, 0xE55A,
    0xE55C, 0xF9DC, 0xBC5F, 0,      0xE556, 0,      0xE554, 0,      0xE55D,
    0xE55B, 0xE559, 0,      0xE55F, 0,      0xE55E, 0xBC63, 0xBC5E, 0,
    0xBC60, 0xBC62, 0,      0,      0xE560, 0xE957, 0,      0,      0xE956,
    0xE955, 0,      0xE958, 0xE951, 0,      0xE952, 0xE95A, 0xE953, 0,
    0xBEC5, 0xE95C, 0,      0xE95B, 0xE954, 0,      0xECD1, 0xC0A8, 0xECCF,
    0xECD4, 0xECD3, 0xE959, 0,      0xC0A7, 0,      0xECD2, 0xECCE, 0xECD6,
    0xECD5, 0xC0A6, 0,      0xECD0, 0,      0xBEC6, 0,      0,      0,
    0xC254, 0,      0,      0,      0xEFC1, 0xF1FA, 0xF1FB, 0xF1FC, 0xC45C,
    0,      0,      0xC45D, 0,      0xF443, 0,      0xF5C8, 0xF5C7, 0,
    0,      0xF6DB, 0xF6DC, 0xF7D5, 0xF8A7, 0,      0xA46C, 0xA46D, 0,
    0xA46E, 0xA4D5, 0xA5A5, 0xC9D3, 0xA672, 0xA673, 0,      0xA7B7, 0xA7B8,
    0xA7B6, 0xA7B5, 0,      0xA973, 0,      0,      0xCC55, 0xA975, 0xA974,
    0xCC56, 0,      0,      0,      0xABC4, 0,      0xAE5D, 0xD165, 0,
    0xD4F0, 0,      0xB145, 0xB447, 0xD4EF, 0xB446, 0,      0xB9E5, 0,
    0xE17D, 0xBEC7, 0,      0xC0A9, 0xECD7, 0,      0xC45E, 0,      0xC570,
    0,      0xC972, 0,      0xA5A6, 0xC973, 0xA676, 0,      0xA674, 0xA675,
    0xA677, 0,      0xA7BA, 0xA7B9, 0,      0xCABC, 0xA7BB, 0,      0,
    0xCABD, 0xCC57, 0,      0xCC58, 0,      0xA976, 0xA978, 0xA97A, 0xA977,
    0xA97B, 0xA979, 0,      0,      0,      0,      0,      0xABC8, 0xABC5,
    0xABC7, 0xABC9, 0xABC6, 0xD166, 0xCE77, 0,      0,      0,      0xD168,
    0xD167, 0xAE63, 0,      0xAE5F, 0,      0,      0xAE60, 0xAE62, 0xAE64,
    0xAE61, 0,      0xAE66, 0xAE65, 0,      0,      0,      0,      0,
    0xB14A, 0xD4F2, 0xD4F1, 0xB149, 0,      0xB148, 0xB147, 0xB14B, 0xB146,
    0,      0,      0xD8D5, 0xD8D2, 0xB449, 0xD8D1, 0xD8D6, 0,      0xB44B,
    0xD8D4, 0xB448, 0xB44A, 0xD8D3, 0,      0xDD48, 0,      0xDD49, 0xDD4A,
    0,      0,      0,      0,      0xB9E6, 0xB9EE, 0xE17E, 0xB9E8, 0xB9EC,
    0xE1A1, 0xB9ED, 0xB9E9, 0xB9EA, 0xB9E7, 0xB9EB, 0xBC66, 0xD8D0, 0xBC67,
    0xBC65, 0,      0xBC64, 0xE95D, 0xBEC8, 0xECD8, 0xECD9, 0,      0,
    0xC364, 0xC45F, 0,      0xA46F, 0,      0xA678, 0,      0,      0,
    0,      0,      0,      0xABCA, 0,      0xD169, 0xAE67, 0,      0,
    0xB14E, 0xB14D, 0xB14C, 0xB44C, 0xB44D, 0xD8D7, 0xB9EF, 0xBEC9, 0xA470,
    0xC95C, 0xA4D6, 0xC974, 0,      0,      0xC9D4, 0xA679, 0,      0,
    0,      0xA97C, 0,      0,      0,      0,      0xDD4B, 0,      0,
    0xA471, 0,      0xA4D7, 0xC9D5, 0,      0,      0xCABE, 0,      0xCABF,
    0,      0xA7BC, 0,      0,      0,      0xD8D8, 0xB44E, 0,      0xDD4C,
    0,      0,      0,      0xC0AA, 0xA472, 0xA4A8, 0xA4D8, 0xC975, 0xA5A7,
    0,      0xA7C0, 0xA7BF, 0xA7BD, 0xA7BE, 0,      0,      0xCC59, 0xA97E,
    0xA9A1, 0xCC5A, 0xA97D, 0,      0,      0xABCE, 0xCE78, 0xABCD, 0xABCB,
    0xABCC, 0xAE6A, 0xAE68, 0,      0,      0xD16B, 0xAE69, 0xD16A, 0,
    0xAE5E, 0xD4F3, 0,      0,      0xB150, 0xB151, 0,      0,      0xB14F,
    0,      0xB9F0, 0xE1A2, 0xBC68, 0xBC69, 0,      0xE561, 0xC0AB, 0xEFC2,
    0xEFC3, 0,      0xC4DD, 0xF8A8, 0xC94B, 0xA4D9, 0,      0xA473, 0,
    0xC977, 0xC976, 0,      0,      0,      0,      0xA67A, 0xC9D7, 0xC9D8,
    0xC9D6, 0,      0xC9D9, 0,      0,      0,      0,      0,      0,
    0,      0xCAC7, 0,      0xCAC2, 0xCAC4, 0xCAC6, 0xCAC3, 0xA7C4, 0xCAC0,
    0,      0xCAC1, 0xA7C1, 0xA7C2, 0xCAC5, 0xCAC8, 0xA7C3, 0xCAC9, 0,
    0,      0,      0,      0,      0,      0,      0xCC68, 0,      0xCC62,
    0xCC5D, 0xA9A3, 0xCC65, 0xCC63, 0xCC5C, 0xCC69, 0xCC6C, 0xCC67, 0xCC60,
    0xA9A5, 0xCC66, 0xA9A6, 0xCC61, 0xCC64, 0xCC5B, 0xCC5F, 0xCC6B, 0xA9A7,
    0,      0xA9A8, 0,      0xCC5E, 0xCC6A, 0xA9A2, 0xA9A4, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0xCEAB, 0xCEA4, 0xCEAA, 0xCEA3, 0xCEA5, 0xCE7D, 0xCE7B,
    0,      0xCEAC, 0xCEA9, 0xCE79, 0,      0xABD0, 0xCEA7, 0xCEA8, 0,
    0xCEA6, 0xCE7C, 0xCE7A, 0xABCF, 0xCEA2, 0xCE7E, 0,      0,      0xCEA1,
    0xCEAD, 0,      0,      0,      0,      0,      0,      0,      0,
    0xAE6F, 0,      0xAE6E, 0,      0xD16C, 0xAE6B, 0xD16E, 0,      0xAE70,
    0xD16F, 0,      0,      0xAE73, 0,      0xAE71, 0xD170, 0xCEAE, 0xD172,
    0,      0xAE6D, 0,      0xAE6C, 0,      0xD16D, 0xD171, 0xAE72, 0,
    0,      0,      0,      0xB153, 0xB152, 0,      0,      0,      0xD4F5,
    0xD4F9, 0xD4FB, 0xB154, 0xD4FE, 0,      0xB158, 0xD541, 0,      0xB15A,
    0,      0xB156, 0xB15E, 0,      0xB15B, 0xD4F7, 0xB155, 0,      0xD4F6,
    0xD4F4, 0xD543, 0xD4F8, 0,      0xB157, 0xD542, 0xB15C, 0xD4FD, 0xD4FC,
    0xB15D, 0xD4FA, 0xB159, 0,      0,      0,      0,      0xD544, 0,
    0xD540, 0xD8E7, 0xD8EE, 0xD8E3, 0xB451, 0xD8DF, 0xD8EF, 0xD8D9, 0xD8EC,
    0xD8EA, 0xD8E4, 0,      0xD8ED, 0xD8E6, 0,      0xD8DE, 0xD8F0, 0xD8DC,
    0xD8E9, 0xD8DA, 0,      0xD8F1, 0,      0xB452, 0,      0xD8EB, 0xDD4F,
    0xD8DD, 0xB44F, 0,      0xD8E1, 0,      0xB450, 0xD8E0, 0xD8E5, 0,
    0,      0xD8E2, 0,      0,      0,      0xD8E8, 0,      0,      0,
    0,      0xDD53, 0,      0,      0,      0xDD56, 0xDD4E, 0,      0xDD50,
    0,      0xDD55, 0xDD54, 0xB743, 0,      0xD8DB, 0xDD52, 0,      0,
    0xB744, 0,      0xDD4D, 0xDD51, 0,      0,      0,      0,      0xE1A9,
    0,      0xE1B0, 0xE1A7, 0,      0xE1AE, 0xE1A5, 0xE1AD, 0xE1B1, 0xE1A4,
    0xE1A8, 0xE1A3, 0,      0xB9F1, 0,      0xE1A6, 0xB9F2, 0xE1AC, 0xE1AB,
    0xE1AA, 0,      0,      0xE1AF, 0,      0,      0,      0,      0xE565,
    0xE567, 0xBC6B, 0xE568, 0,      0xE563, 0,      0xE562, 0xE56C, 0,
    0xE56A, 0xBC6A, 0xE56D, 0xE564, 0xE569, 0xE56B, 0xE566, 0,      0,
    0,      0,      0xE961, 0xE966, 0xE960, 0xE965, 0,      0xE95E, 0xE968,
    0xE964, 0xE969, 0xE963, 0xE95F, 0xE967, 0,      0xE96A, 0xE962, 0,
    0xECDA, 0xC0AF, 0,      0xC0AD, 0,      0xC0AC, 0xC0AE, 0,      0,
    0xEFC4, 0,      0xF172, 0xF1FD, 0,      0,      0xF444, 0xF445, 0,
    0xC460, 0,      0xF5C9, 0,      0xC4DE, 0,      0xF5CA, 0,      0xF6DE,
    0xC572, 0,      0xC571, 0xF6DD, 0xC5C9, 0,      0xF7D6, 0,      0,
    0,      0,      0xA474, 0xA67B, 0xC9DA, 0xCACA, 0xA8B5, 0xB15F, 0,
    0,      0xA475, 0xA5AA, 0xA5A9, 0xA5A8, 0,      0,      0xA7C5, 0,
    0,      0xAE74, 0,      0xDD57, 0xA476, 0xA477, 0xA478, 0xA4DA, 0,
    0,      0xABD1, 0,      0xCEAF, 0,      0,      0,      0xB453, 0xA479,
    0xC95D, 0,      0,      0xA5AB, 0xA5AC, 0xC978, 0,      0xA67C, 0,
    0,      0,      0xCACB, 0,      0xA7C6, 0,      0xCACC, 0,      0,
    0xA9AE, 0,      0,      0xCC6E, 0xA9AC, 0xA9AB, 0xCC6D, 0xA9A9, 0xCC6F,
    0xA9AA, 0xA9AD, 0,      0xABD2, 0,      0xABD4, 0xCEB3, 0xCEB0, 0xCEB1,
    0xCEB2, 0xCEB4, 0xABD3, 0,      0,      0xD174, 0xD173, 0,      0xAE76,
    0,      0xAE75, 0,      0,      0,      0,      0,      0xB162, 0xD546,
    0,      0xB161, 0xB163, 0xB160, 0,      0,      0,      0,      0xB455,
    0xD545, 0,      0xB456, 0xD8F3, 0,      0xB457, 0xD8F2, 0xB454, 0,
    0,      0,      0,      0xDD5A, 0xDD5C, 0xB745, 0xDD5B, 0xDD59, 0xDD58,
    0,      0,      0,      0xE1B4, 0xB9F7, 0xB9F5, 0,      0xB9F6, 0xE1B2,
    0xE1B3, 0,      0xB9F3, 0xE571, 0xE56F, 0,      0xBC6D, 0xE570, 0xBC6E,
    0xBC6C, 0xB9F4, 0,      0,      0xE96D, 0xE96B, 0xE96C, 0xE56E, 0xECDC,
    0xC0B0, 0xECDB, 0xEFC5, 0xEFC6, 0xE96E, 0xF1FE, 0,      0xA47A, 0xA5AD,
    0xA67E, 0xC9DB, 0xA67D, 0,      0xA9AF, 0xB746, 0,      0xA4DB, 0xA5AE,
    0xABD5, 0xB458, 0,      0xC979, 0,      0xC97A, 0,      0xC9DC, 0,
    0,      0xA7C8, 0xCAD0, 0xCACE, 0xA7C9, 0xCACD, 0xCACF, 0xCAD1, 0,
    0xA7C7, 0,      0,      0,      0,      0,      0xA9B3, 0xA9B4, 0xA9B1,
    0,      0,      0xA9B0, 0xCEB8, 0xA9B2, 0,      0,      0,      0xABD6,
    0,      0xCEB7, 0xCEB9, 0xCEB6, 0xCEBA, 0xABD7, 0xAE79, 0xD175, 0,
    0xD177, 0xAE77, 0xD178, 0xAE78, 0xD176, 0,      0xCEB5, 0xD547, 0xD54A,
    0xD54B, 0xD548, 0xB167, 0xB166, 0xB164, 0xB165, 0xD549, 0,      0,
    0,      0,      0xB168, 0,      0,      0xB45A, 0xB45B, 0,      0xB45C,
    0xDD5D, 0xDD5F, 0xDD61, 0xB748, 0xB747, 0xB459, 0xDD60, 0xDD5E, 0,
    0xE1B8, 0,      0,      0xE1B6, 0xE1BC, 0xB9F8, 0xE1BD, 0xE1BA, 0xB9F9,
    0xE1B7, 0xE1B5, 0xE1BB, 0xBC70, 0xE573, 0xE1B9, 0xBC72, 0xE574, 0xBC71,
    0xBC74, 0xE575, 0xBC6F, 0xBC73, 0,      0xE973, 0xE971, 0xE970, 0xE972,
    0xE96F, 0,      0,      0xC366, 0,      0xF446, 0xF447, 0,      0xF5CB,
    0xF6DF, 0xC655, 0,      0,      0xA9B5, 0xA7CA, 0,      0,      0xABD8,
    0,      0,      0,      0xA47B, 0xA4DC, 0,      0xA5AF, 0xC9DD, 0,
    0xA7CB, 0xCAD2, 0,      0xCEBB, 0xABD9, 0,      0xB9FA, 0xA47C, 0,
    0,      0,      0xA6A1, 0,      0,      0xB749, 0xA47D, 0xA4DD, 0xA4DE,
    0,      0xA5B1, 0xA5B0, 0,      0xC9DE, 0xA6A2, 0,      0xCAD3, 0,
    0xA7CC, 0,      0,      0xCC71, 0xCC72, 0xCC73, 0,      0xA9B6, 0xA9B7,
    0xCC70, 0xA9B8, 0,      0,      0,      0xABDA, 0xCEBC, 0,      0xD17A,
    0xAE7A, 0,      0xD179, 0,      0xB169, 0xD54C, 0xB16A, 0xD54D, 0,
    0,      0,      0xB45D, 0,      0,      0,      0xDD62, 0,      0,
    0xE1BF, 0xE1BE, 0,      0xB9FB, 0,      0xBC75, 0xE576, 0xBECA, 0xE974,
    0xC0B1, 0,      0xC573, 0xF7D8, 0,      0,      0,      0,      0xCC74,
    0,      0xCEBD, 0xB16B, 0xD8F4, 0xB74A, 0,      0,      0,      0xC255,
    0,      0,      0,      0,      0xA7CE, 0,      0xA7CD, 0xABDB, 0,
    0xD17B, 0,      0xB16D, 0xB343, 0xB16E, 0xB16C, 0xB45E, 0,      0xE1C0,
    0xB9FC, 0xBC76, 0,      0xC94C, 0xC9DF, 0,      0xCAD5, 0xA7CF, 0xCAD4,
    0xA7D0, 0,      0,      0xA9BC, 0xCC77, 0xCC76, 0xA9BB, 0xA9B9, 0xA9BA,
    0xCC75, 0,      0,      0xABDD, 0xCEBE, 0xABE0, 0xABDC, 0xABE2, 0xABDE,
    0xABDF, 0xABE1, 0,      0,      0,      0xAE7D, 0xAE7C, 0xAE7B, 0,
    0,      0,      0xD54F, 0xB16F, 0xB172, 0xB170, 0,      0xD54E, 0xB175,
    0,      0xB171, 0xD550, 0xB174, 0xB173, 0,      0,      0,      0xD8F6,
    0xD8F5, 0,      0xB461, 0xB45F, 0xB460, 0xD8F7, 0xB74B, 0xDD64, 0xB74C,
    0xDD63, 0,      0,      0xE577, 0,      0,      0xBC78, 0xE1C1, 0xBC77,
    0,      0xB9FD, 0,      0xECDE, 0xE975, 0xC0B2, 0xECDD, 0xF240, 0xF448,
    0xF449, 0,      0xA4DF, 0,      0xA5B2, 0,      0,      0,      0xC97B,
    0,      0,      0xA7D2, 0xA7D4, 0,      0xC9E2, 0xCAD8, 0xCAD7, 0xCAD6,
    0,      0xC9E1, 0xC9E0, 0xA6A4, 0xA7D3, 0xA7D1, 0xA6A3, 0,      0,
    0,      0xA9BD, 0xCC78, 0,      0xA9BE, 0xCADD, 0,      0xCADF, 0xCADE,
    0xCC79, 0,      0,      0xCADA, 0,      0xA7D8, 0xA7D6, 0,      0xCAD9,
    0xCADB, 0xCAE1, 0,      0xA7D5, 0,      0xCADC, 0xCAE5, 0xA9C0, 0,
    0xCAE2, 0xA7D7, 0,      0xCAE0, 0xCAE3, 0,      0xA9BF, 0,      0xA9C1,
    0xCAE4, 0,      0,      0,      0,      0,      0,      0,      0,
    0xCCAF, 0xCCA2, 0xCC7E, 0xCCAE, 0xCCA9, 0xABE7, 0xA9C2, 0xCCAA, 0xCCAD,
    0xABE3, 0xCCAC, 0xA9C3, 0xA9C8, 0xA9C6, 0xCCA3, 0,      0xCC7C, 0xCCA5,
    0xA9CD, 0xCCB0, 0xABE4, 0xCCA6, 0,      0xABE5, 0xA9C9, 0xCCA8, 0,
    0xCECD, 0xABE6, 0xCC7B, 0xA9CA, 0xABE8, 0xA9CB, 0xA9C7, 0xA9CC, 0xCCA7,
    0xCC7A, 0xCCAB, 0xA9C4, 0,      0,      0xCC7D, 0xCCA4, 0xCCA1, 0xA9C5,
    0,      0xCEBF, 0,      0xCEC0, 0,      0,      0,      0,      0,
    0,      0xCECA, 0xD1A1, 0xCECB, 0xABEE, 0xCECE, 0xCEC4, 0xABED, 0xCEC6,
    0,      0xCEC7, 0,      0,      0xCEC9, 0xABE9, 0,      0,      0xAEA3,
    0,      0xF9DA, 0xCEC5, 0xCEC1, 0xAEA4, 0,      0,      0xCECF, 0xAE7E,
    0xD17D, 0xCEC8, 0,      0xD17C, 0xCEC3, 0xCECC, 0,      0,      0xABEC,
    0xAEA1, 0xABF2, 0xAEA2, 0xCED0, 0xD17E, 0xABEB, 0xAEA6, 0xABF1, 0xABF0,
    0xABEF, 0xAEA5, 0xCED1, 0xAEA7, 0xABEA, 0,      0xCEC2, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0xB176, 0xD1A4, 0xD1A6, 0,      0xD1A8, 0xAEA8, 0xAEAE, 0xD553,
    0xD1AC, 0xD1A3, 0xB178, 0xD551, 0,      0xAEAD, 0xAEAB, 0xD1AE, 0,
    0xD552, 0,      0xD1A5, 0,      0xAEAC, 0xD1A9, 0xAEAF, 0xD1AB, 0,
    0,      0xAEAA, 0xD1AA, 0xD1AD, 0xD1A7, 0,      0xAEA9, 0xB179, 0,
    0xD1A2, 0xB177, 0,      0,      0,      0,      0xB17A, 0,      0,
    0,      0,      0,      0,      0,      0xD555, 0xD55E, 0xB464, 0,
    0xB17C, 0xB1A3, 0xB465, 0xD560, 0xB1AA, 0xD8F9, 0xD556, 0xB1A2, 0xB1A5,
    0xB17E, 0xD554, 0xD562, 0xD565, 0xD949, 0,      0xD563, 0xD8FD, 0xB1A1,
    0xB1A8, 0xB1AC, 0xD55D, 0xD8F8, 0xD561, 0xB17B, 0xD8FA, 0xD564, 0xD8FC,
    0xD559, 0,      0xB462, 0,      0xD557, 0xD558, 0xB1A7, 0,      0,
    0xB1A6, 0xD55B, 0xB1AB, 0xD55F, 0xB1A4, 0xD55C, 0,      0xB1A9, 0xB466,
    0xB463, 0xD8FB, 0,      0xD55A, 0,      0xB17D, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0xB46B, 0xB46F, 0xD940,
    0xB751, 0xB46D, 0xD944, 0xB471, 0xDD65, 0xD946, 0xB753, 0xB469, 0xB46C,
    0xD947, 0,      0xD948, 0xD94E, 0xB473, 0xB754, 0,      0xD94A, 0xD94F,
    0xD943, 0xB75E, 0,      0xB755, 0xB472, 0xD941, 0xD950, 0,      0xB75D,
    0xB470, 0xB74E, 0xD94D, 0,      0xB474, 0xD945, 0xD8FE, 0xB46A, 0xD942,
    0,      0xD94B, 0,      0xB74D, 0xB752, 0xB467, 0xD94C, 0,      0xB750,
    0,      0,      0,      0xB468, 0,      0,      0,      0xB75C, 0xE1C3,
    0xDD70, 0,      0xDD68, 0xE1C2, 0,      0xDD6C, 0xDD6E, 0,      0,
    0xDD6B, 0,      0xB75B, 0,      0xDD6A, 0xB75F, 0,      0,      0,
    0xE1D2, 0,      0,      0xB75A, 0xBA40, 0xDD71, 0xE1C4, 0,      0,
    0xB758, 0xDD69, 0xDD6D, 0xB9FE, 0xB74F, 0xDD66, 0xDD67, 0xBA41, 0xB757,
    0xB759, 0xB756, 0xDD6F, 0,      0,      0xE1C8, 0xE1C9, 0xE1CE, 0xBC7D,
    0xE1D5, 0,      0xBA47, 0,      0xBA46, 0xE1D0, 0,      0xBC7C, 0xE1C5,
    0xBA45, 0,      0xE1D4, 0xBA43, 0xBA44, 0,      0xE1D1, 0xE5AA, 0xBC7A,
    0xB46E, 0,      0xE1D3, 0xBCA3, 0xE1CB, 0,      0xBC7B, 0,      0xBCA2,
    0xE1C6, 0xE1CA, 0xE1C7, 0xE1CD, 0xBA48, 0xBC79, 0xBA42, 0,      0xE57A,
    0xE1CF, 0,      0xBCA1, 0,      0xBCA4, 0,      0xE1CC, 0,      0xBC7E,
    0xE579, 0,      0,      0,      0,      0,      0xE57E, 0xBECE, 0xE578,
    0xE9A3, 0xE5A9, 0xBCA8, 0,      0xBCA6, 0xBECC, 0xE5A6, 0xE5A2, 0xBCAC,
    0,      0xE978, 0,      0,      0,      0xBCAA, 0xE5A1, 0,      0xE976,
    0,      0xE5A5, 0,      0xE5A8, 0xE57D, 0,      0xBCAB, 0,      0,
    0xBCA5, 0xE977, 0xBECD, 0xE5A7, 0xBCA7, 0xBCA9, 0xE5A4, 0xBCAD, 0xE5A3,
    0xE57C, 0xE57B, 0xBECB, 0xE5AB, 0xE97A, 0xECE0, 0xBED0, 0,      0xE9A2,
    0,      0xE97E, 0,      0xECE1, 0,      0xBED1, 0xE9A1, 0,      0xE97C,
    0xC0B4, 0xECDF, 0,      0xE979, 0xE97B, 0xC0B5, 0xBED3, 0xC0B3, 0xBED2,
    0xC0B7, 0xE97D, 0xBECF, 0,      0,      0,      0,      0,      0,
    0,      0,      0xEFCF, 0,      0xEFC7, 0,      0,      0,      0,
    0,      0xECE7, 0xEFC8, 0xECE3, 0,      0,      0xC256, 0xECE5, 0xECE4,
    0xC0B6, 0xECE2, 0xECE6, 0xEFD0, 0xEFCC, 0xEFCE, 0,      0xEFC9, 0xEFCA,
    0,      0xEFCD, 0xEFCB, 0xC367, 0,      0,      0xC36A, 0xC369, 0xC368,
    0xC461, 0xF44A, 0xC462, 0xF241, 0xC4DF, 0xF5CC, 0xC4E0, 0xC574, 0xC5CA,
    0xF7D9, 0,      0xF7DA, 0xF7DB, 0,      0,      0xF9BA, 0xA4E0, 0xC97C,
    0xA5B3, 0,      0xA6A6, 0xA6A7, 0xA6A5, 0,      0xA6A8, 0xA7DA, 0xA7D9,
    0,      0xCCB1, 0xA9CF, 0xA9CE, 0,      0,      0xD1AF, 0xB1AD, 0xB1AE,
    0,      0,      0,      0xB475, 0xDD72, 0xB760, 0xB761, 0xDD74, 0xDD76,
    0xDD75, 0,      0xE1D7, 0,      0xE1D6, 0xBA49, 0xE1D8, 0,      0xE5AC,
    0xBCAE, 0,      0xBED4, 0,      0xC0B8, 0xC257, 0xC0B9, 0,      0xA4E1,
    0,      0,      0,      0xCAE6, 0,      0,      0xCCB2, 0xA9D1, 0xA9D0,
    0xA9D2, 0xABF3, 0xCED2, 0xCED3, 0,      0,      0xD1B0, 0xAEB0, 0xB1AF,
    0xB476, 0xD951, 0xA4E2, 0,      0xA47E, 0xA4E3, 0,      0xC97D, 0xA5B7,
    0xA5B6, 0xA5B4, 0xA5B5, 0,      0,      0,      0xA6AB, 0xC9E9, 0xC9EB,
    0xA6AA, 0xC9E3, 0,      0xC9E4, 0,      0xC9EA, 0xC9E6, 0xC9E8, 0xA6A9,
    0xC9E5, 0xC9EC, 0xC9E7, 0,      0,      0,      0,      0,      0,
    0xA7E1, 0xA7EA, 0xA7E8, 0xCAF0, 0xCAED, 0xCAF5, 0xA7E6, 0xCAF6, 0,
    0xA7DF, 0xCAF3, 0,      0xA7E5, 0xCAEF, 0xCAEE, 0xA7E3, 0xCAF4, 0xA7E4,
    0xA9D3, 0xA7DE, 0xCAF1, 0,      0xCAE7, 0xA7DB, 0,      0xA7EE, 0xCAEC,
    0xCAF2, 0xA7E0, 0xA7E2, 0,      0xCAE8, 0,      0xCAE9, 0xCAEA, 0,
    0xA7ED, 0xA7E7, 0xA7EC, 0xCAEB, 0xA7EB, 0xA7DD, 0xA7DC, 0xA7E9, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0xA9E1, 0xCCBE, 0xCCB7, 0xA9DC,
    0xA9EF, 0xCCB3, 0xCCBA, 0xCCBC, 0xCCBF, 0xA9EA, 0,      0xCCBB, 0xCCB4,
    0xA9E8, 0xCCB8, 0,      0xCCC0, 0xA9D9, 0,      0xCCBD, 0xA9E3, 0xA9E2,
    0xCCB6, 0xA9D7, 0,      0,      0xA9D8, 0,      0xA9D6, 0,      0xA9EE,
    0xA9E6, 0xA9E0, 0xA9D4, 0xCCB9, 0xA9DF, 0xA9D5, 0xA9E7, 0xA9F0, 0xCED4,
    0xA9E4, 0xCCB5, 0xA9DA, 0xA9DD, 0xA9DE, 0,      0xA9EC, 0xA9ED, 0xA9EB,
    0xA9E5, 0xA9E9, 0xA9DB, 0xABF4, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0xCEDA, 0xAC41, 0xABF8, 0xABFA, 0xAC40, 0xCEE6, 0xABFD, 0xD1B1, 0xAEB1,
    0xAC43, 0xCED7, 0xCEDF, 0xABFE, 0xCEDE, 0xCEDB, 0xCEE3, 0xCEE5, 0xABF7,
    0xABFB, 0xAC42, 0xAEB3, 0xCEE0, 0xABF9, 0xAC45, 0xCED9, 0,      0,
    0,      0xABFC, 0xAEB2, 0xABF6, 0,      0xCED6, 0xCEDD, 0xCED5, 0xCED8,
    0xCEDC, 0xD1B2, 0xAC44, 0,      0xCEE1, 0xCEE2, 0xCEE4, 0xABF5, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0xAEC1, 0xD1BE,
    0xAEBF, 0xAEC0, 0xD1B4, 0xD1C4, 0,      0xAEB6, 0,      0,      0xD566,
    0xD1C6, 0xD1C0, 0,      0xD1B7, 0,      0xD1C9, 0xD1BA, 0xAEBC, 0xD57D,
    0xD1BD, 0xAEBE, 0xAEB5, 0,      0xD1CB, 0xD1BF, 0xAEB8, 0xD1B8, 0xD1B5,
    0xD1B6, 0xAEB9, 0xD1C5, 0xD1CC, 0xAEBB, 0xD1BC, 0xD1BB, 0xAEC3, 0xAEC2,
    0xAEB4, 0xAEBA, 0xAEBD, 0xD1C8, 0,      0,      0xD1C2, 0xAEB7, 0xD1B3,
    0xD1CA, 0xD1C1, 0xD1C3, 0xD1C7, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0xD567, 0,      0xB1B7, 0xB1CB,
    0xB1CA, 0,      0xB1BF, 0,      0xD579, 0xD575, 0xD572, 0xD5A6, 0xB1BA,
    0xB1B2, 0,      0,      0xD577, 0xB4A8, 0xB1B6, 0xD5A1, 0,      0xB1CC,
    0xB1C9, 0xD57B, 0xD56A, 0,      0,      0xB1C8, 0xD5A3, 0xD569, 0xB1BD,
    0xB1C1, 0xD5A2, 0,      0xD573, 0xB1C2, 0xB1BC, 0xD568, 0,      0xB478,
    0xD5A5, 0xD571, 0xB1C7, 0xD574, 0xD5A4, 0xB1C6, 0,      0xD952, 0,
    0xB1B3, 0xD56F, 0xB1B8, 0xB1C3, 0,      0xB1BE, 0xD578, 0xD56E, 0xD56C,
    0xD57E, 0xB1B0, 0xB1C4, 0xB1B4, 0xB477, 0xD57C, 0xB1B5, 0,      0xB1B1,
    0xB1C0, 0xB1BB, 0xB1B9, 0xD570, 0xB1C5, 0xD56D, 0xD57A, 0xD576, 0xD954,
    0xD953, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xD56B, 0xD964, 0,      0xB47A, 0,      0xD96A,
    0xD959, 0xD967, 0xDD77, 0xB47D, 0xD96B, 0xD96E, 0xB47C, 0xD95C, 0xD96D,
    0xD96C, 0xB47E, 0xD955, 0xB479, 0xB4A3, 0,      0xB4A1, 0xD969, 0,
    0xD95F, 0xB4A5, 0xD970, 0xD968, 0xD971, 0xB4AD, 0xB4AB, 0xD966, 0xD965,
    0,      0xD963, 0xD95D, 0xB4A4, 0,      0xB4A2, 0xD1B9, 0xD956, 0,
    0xDDB7, 0xD957, 0xB47B, 0xB4AA, 0xDD79, 0,      0xB4A6, 0xB4A7, 0xD958,
    0xD96F, 0xDD78, 0xD960, 0xD95B, 0xB4A9, 0xD961, 0xD95E, 0,      0,
    0xB4AE, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0xB770, 0,      0,      0xDD7C, 0xDDB1,
    0xDDB6, 0xDDAA, 0xB76C, 0xDDBB, 0xB769, 0xDD7A, 0,      0xDD7B, 0xB762,
    0xB76B, 0xDDA4, 0xB76E, 0xB76F, 0xDDA5, 0,      0xDDB2, 0xDDB8, 0xB76A,
    0,      0xB764, 0xDDA3, 0xDD7D, 0xDDBA, 0xDDA8, 0xDDA9, 0xDD7E, 0xDDB4,
    0xDDAB, 0xDDB5, 0xDDAD, 0,      0xB765, 0xE1D9, 0xB768, 0xB766, 0xDDB9,
    0xDDB0, 0xDDAC, 0,      0,      0xDDA1, 0xBA53, 0xDDAF, 0xB76D, 0xDDA7,
    0,      0xDDA6, 0,      0,      0,      0xB767, 0xB763, 0xE1EE, 0xDDB3,
    0xDDAE, 0,      0xDDA2, 0,      0,      0,      0,      0,      0,
    0,      0xE1E9, 0,      0xE1DA, 0xE1E5, 0,      0xE1EC, 0xBA51, 0xB4AC,
    0xE1EA, 0xBA4C, 0,      0,      0,      0xBA4B, 0xE1F1, 0,      0xE1DB,
    0xE1E8, 0xE1DC, 0xE1E7, 0xBA4F, 0xE1EB, 0xD962, 0,      0,      0,
    0xE1F2, 0xE1E3, 0xBA52, 0xE5BA, 0xBCAF, 0,      0xE1F0, 0xE1EF, 0xBA54,
    0xE5AD, 0xBCB0, 0xE5AE, 0,      0xE1DF, 0xE1E0, 0xE1DD, 0xE1E2, 0xE1DE,
    0xE1F3, 0xBA4E, 0xBCB1, 0xBA50, 0xBA55, 0,      0xE1E1, 0,      0xE1ED,
    0,      0,      0xE1E6, 0,      0,      0xE5B1, 0,      0xBA4A, 0xBCB4,
    0xE9AA, 0xE5B6, 0xE5B5, 0xE5B7, 0,      0,      0xE5B4, 0xBCB5, 0,
    0xBCBB, 0xBCB8, 0,      0xBCB9, 0xE5AF, 0xE5B2, 0xE5BC, 0xBCC1, 0xBCBF,
    0,      0xE5B3, 0xD95A, 0xBCB2, 0xE5B9, 0xE5B0, 0,      0xBCC2, 0xE5B8,
    0xBA4D, 0xBCB7, 0xE1E4, 0,      0,      0xBCBA, 0,      0xBCBE, 0xBCC0,
    0xBCBD, 0xBCBC, 0,      0xBCB6, 0xE5BB, 0xBCB3, 0xBCC3, 0,      0,
    0,      0,      0,      0,      0,      0xBED8, 0xBED9, 0xE9A9, 0xBEE2,
    0xBEDF, 0,      0xBED6, 0xBEDD, 0xE9AB, 0xBEDB, 0xBED5, 0,      0xBEDC,
    0,      0xE9A8, 0xC0BB, 0xBED7, 0,      0xBEDE, 0xC0BA, 0xE9A7, 0xE9A6,
    0,      0xBEE0, 0,      0xBEE1, 0,      0xE9A5, 0xE9A4, 0xC0BC, 0xE9AE,
    0xBEDA, 0xE9AC, 0,      0,      0,      0,      0xC0BD, 0,      0xC0C2,
    0xECEA, 0xECEC, 0,      0xC0BF, 0,      0xECED, 0xECE9, 0,      0xECEB,
    0xC0C0, 0xC0C3, 0,      0xECE8, 0xC0BE, 0xC0C1, 0xC259, 0xE9AD, 0xC258,
    0,      0,      0xC25E, 0xEFD4, 0,      0xC25C, 0xC25D, 0xEFD7, 0xEFD3,
    0xC25A, 0xEFD1, 0xC36B, 0xEFD5, 0,      0xEFD6, 0xEFD2, 0,      0xC25B,
    0xF242, 0,      0xF245, 0,      0,      0xF246, 0xF244, 0xF247, 0xC36C,
    0xF243, 0,      0,      0xF44E, 0xC464, 0xF44D, 0xF44C, 0xF44B, 0xC463,
    0xC465, 0,      0xF5CD, 0xC4E2, 0xC4E1, 0,      0,      0xF6E1, 0xF6E0,
    0xF6E3, 0xC5CB, 0xC575, 0xF7DD, 0xF6E2, 0,      0,      0xF7DC, 0xC5CD,
    0xC5CC, 0xC5F3, 0xF8A9, 0xF8EF, 0xA4E4, 0,      0,      0xD972, 0xE9AF,
    0,      0,      0xA6AC, 0xCAF7, 0xA7F1, 0xA7EF, 0,      0xA7F0, 0,
    0xCCC1, 0xA9F1, 0xAC46, 0,      0xCEE7, 0,      0xCEE8, 0,      0xAC47,
    0xD1CE, 0,      0xAEC4, 0xAEC5, 0xD1CD, 0,      0,      0,      0,
    0xB1D3, 0,      0xB1CF, 0,      0xD5A7, 0xB1D6, 0xB1D5, 0xB1CE, 0xB1D1,
    0xB1D4, 0xB1D0, 0,      0,      0xD976, 0xB1CD, 0xB4AF, 0,      0,
    0,      0xB4B1, 0xB4B2, 0xD975, 0xD978, 0xB4B0, 0xD973, 0xD977, 0,
    0xD974, 0,      0xB771, 0,      0,      0xDDBC, 0,      0,      0xBA56,
    0xE1F4, 0xBEE3, 0xBCC4, 0xE5BD, 0xBCC5, 0xBCC6, 0xE5BF, 0xE5BE, 0xE5C0,
    0xE9B1, 0,      0,      0xE9B0, 0xECEF, 0xECEE, 0xC0C4, 0xC0C5, 0xF248,
    0,      0,      0xA4E5, 0,      0,      0,      0,      0xD979, 0,
    0,      0,      0xB4B4, 0xB4B3, 0xDDBD, 0,      0xEFD8, 0xC4E3, 0xF7DE,
    0xA4E6, 0,      0xAEC6, 0,      0xB1D8, 0xB1D7, 0xD97A, 0xD97B, 0xB772,
    0xE1F5, 0xBA57, 0xE9B2, 0,      0xA4E7, 0xA5B8, 0,      0xA9F2, 0xCCC2,
    0,      0xCEE9, 0xAC48, 0xB1D9, 0,      0xD97C, 0xB4B5, 0xB773, 0,
    0xE5C1, 0xE5C2, 0,      0,      0xECF0, 0xC25F, 0xF8F0, 0xA4E8, 0,
    0xCCC3, 0xA9F3, 0xAC49, 0,      0xCEEA, 0,      0xAEC7, 0xD1D2, 0xD1D0,
    0xD1D1, 0xAEC8, 0xD1CF, 0,      0,      0,      0,      0xB1DB, 0xB1DC,
    0xD5A8, 0xB1DD, 0xB1DA, 0xD97D, 0,      0xD97E, 0xDDBE, 0,      0,
    0xBA59, 0xBA58, 0,      0,      0xECF1, 0xEFD9, 0,      0xF24A, 0xF249,
    0xF44F, 0,      0xC95E, 0xAC4A, 0,      0,      0xA4E9, 0xA5B9, 0,
    0xA6AE, 0xA6AD, 0,      0,      0xA6AF, 0xA6B0, 0xC9EE, 0xC9ED, 0xCAF8,
    0xA7F2, 0xCAFB, 0xCAFA, 0xCAF9, 0xCAFC, 0,      0,      0,      0,
    0xA9F4, 0xCCC9, 0xCCC5, 0xCCCE, 0,      0,      0xA9FB, 0,      0xA9F9,
    0xCCCA, 0xCCC6, 0xCCCD, 0xA9F8, 0xAA40, 0xCCC8, 0xCCC4, 0xA9FE, 0xCCCB,
    0xA9F7, 0xCCCC, 0xA9FA, 0xA9FC, 0xCCD0, 0xCCCF, 0xCCC7, 0xA9F6, 0xA9F5,
    0xA9FD, 0,      0,      0,      0,      0,      0,      0xCEEF, 0xCEF5,
    0,      0xAC50, 0xAC4D, 0xCEEC, 0xCEF1, 0,      0xAC53, 0xAC4B, 0xCEF0,
    0xAC4E, 0xAC51, 0,      0,      0xCEF3, 0,      0xAC4C, 0xCEF8, 0xAC4F,
    0,      0xAC52, 0xCEED, 0xCEF2, 0xCEF6, 0xCEEE, 0xCEEB, 0,      0,
    0xCEF7, 0xCEF4, 0,      0,      0,      0,      0,      0,      0xAED0,
    0xAEC9, 0xAECC, 0,      0xAECF, 0,      0xD1D5, 0,      0xAECA, 0xD1D3,
    0,      0xAECE, 0,      0,      0xAECB, 0,      0xD1D6, 0xAECD, 0,
    0,      0,      0,      0,      0,      0xD5AC, 0xB1DF, 0xD5AB, 0xD5AD,
    0xB1DE, 0xB1E3, 0xD1D4, 0,      0xD5AA, 0xD5AE, 0,      0xB1E0, 0xD5A9,
    0xB1E2, 0,      0xB1E1, 0,      0xD9A7, 0,      0xD9A2, 0,      0xB4B6,
    0xB4BA, 0xB4B7, 0xD9A5, 0xD9A8, 0,      0xB4B8, 0,      0xB4B9, 0xB4BE,
    0xDDC7, 0xD9A6, 0xB4BC, 0xD9A3, 0xD9A1, 0,      0xB4BD, 0,      0xD9A4,
    0,      0,      0,      0xB779, 0,      0xDDBF, 0xB776, 0xB777, 0xB775,
    0xDDC4, 0xDDC3, 0xDDC0, 0xB77B, 0,      0,      0xDDC2, 0xB4BB, 0,
    0,      0xDDC6, 0xDDC1, 0xB778, 0xB774, 0xB77A, 0xDDC5, 0,      0,
    0,      0xBA5C, 0,      0xE1F8, 0xE1F7, 0xE1F6, 0xBA5A, 0,      0,
    0,      0,      0,      0xBA5B, 0xE5C5, 0xE5C8, 0xBCC8, 0,      0,
    0xBCC7, 0xE5C9, 0xE5C4, 0xBCCA, 0xE5C6, 0,      0xBCC9, 0xE5C3, 0,
    0xE5C7, 0xBEE9, 0xBEE6, 0xE9BB, 0xE9BA, 0,      0xE9B9, 0xE9B4, 0,
    0xE9B5, 0,      0,      0,      0xBEE7, 0,      0xBEE4, 0xBEE8, 0xE9B3,
    0xBEE5, 0xE9B6, 0xE9B7, 0xE9BC, 0,      0,      0xE9B8, 0,      0,
    0xECF2, 0,      0,      0,      0xC0C7, 0,      0xEFDC, 0xC0C6, 0xEFDA,
    0xEFDB, 0xC260, 0xC36E, 0xF24B, 0,      0xC36D, 0,      0,      0xF451,
    0xF452, 0,      0xC466, 0,      0xF450, 0xC4E4, 0,      0xF7DF, 0xC5CE,
    0xF8AA, 0xF8AB, 0,      0xA4EA, 0,      0xA6B1, 0xA6B2, 0xA7F3, 0,
    0xCCD1, 0xAC54, 0xAED1, 0xB1E4, 0,      0,      0xB0D2, 0,      0xB4BF,
    0xB4C0, 0xB3CC, 0xD9A9, 0,      0xB77C, 0xE1FA, 0xE1F9, 0,      0,
    0xA4EB, 0xA6B3, 0xCCD2, 0xAA42, 0,      0xAA41, 0,      0xCEF9, 0xCEFA,
    0,      0xD1D7, 0xD1D8, 0xAED2, 0xAED3, 0,      0xAED4, 0xD5AF, 0,
    0,      0xB1E6, 0,      0xB4C2, 0,      0xB4C1, 0xDDC8, 0xDF7A, 0xE1FB,
    0xE9BD, 0,      0,      0xC261, 0xC467, 0xA4EC, 0,      0xA5BC, 0xA5BD,
    0xA5BB, 0xA5BE, 0xA5BA, 0,      0,      0xA6B6, 0,      0xC9F6, 0xA6B5,
    0xA6B7, 0,      0,      0xC9F1, 0xC9F0, 0xC9F3, 0xC9F2, 0xC9F5, 0xA6B4,
    0xC9EF, 0xC9F4, 0,      0,      0,      0,      0,      0xCAFD, 0xA7FD,
    0xCAFE, 0xCB43, 0xA7FC, 0,      0xCB47, 0xCB42, 0xCB45, 0xA7F5, 0xA7F6,
    0xA7F7, 0xA7F8, 0,      0xA840, 0,      0xCB41, 0xA7FA, 0xA841, 0,
    0xCB40, 0xCB46, 0,      0xA7F9, 0xCB44, 0xA7FB, 0xA7F4, 0xA7FE, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0xAA57,
    0,      0xCCD4, 0xAA43, 0,      0xAA4D, 0xAA4E, 0xAA46, 0xAA58, 0xAA48,
    0xCCDC, 0xAA53, 0xCCD7, 0xAA49, 0xCCE6, 0xCCE7, 0xCCDF, 0xCCD8, 0xAA56,
    0xCCE4, 0xAA51, 0xAA4F, 0,      0xCCE5, 0,      0xCCE3, 0xCCDB, 0xCCD3,
    0xCCDA, 0xAA4A, 0,      0xAA50, 0,      0xAA44, 0xCCDE, 0xCCDD, 0xCCD5,
    0,      0xAA52, 0xCCE1, 0xCCD6, 0xAA55, 0xCCE8, 0xAA45, 0,      0xAA4C,
    0xCCD9, 0xCCE2, 0xAA54, 0,      0xAA47, 0xAA4B, 0,      0xCCE0, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0xCF5B, 0xAC5C, 0xAC69, 0,      0xCF56,
    0xCF4C, 0xAC62, 0xCF4A, 0xAC5B, 0xCF45, 0xAC65, 0xCF52, 0xCEFE, 0xCF41,
    0,      0,      0,      0,      0xCF44, 0xCEFB, 0xCF51, 0xCF61, 0xAC60,
    0xCF46, 0xCF58, 0,      0xCEFD, 0xCF5F, 0xCF60, 0xCF63, 0xCF5A, 0xCF4B,
    0xCF53, 0xAC66, 0xAC59, 0xAC61, 0xAC6D, 0xAC56, 0xAC58, 0,      0,
    0,      0xCF43, 0xAC6A, 0xAC63, 0xCF5D, 0xCF40, 0xAC6C, 0xAC67, 0xCF49,
    0,      0,      0xAC6B, 0xCF50, 0xCF48, 0xAC64, 0xCF5C, 0xCF54, 0,
    0xAC5E, 0xCF62, 0xCF47, 0xAC5A, 0xCF59, 0xCF4F, 0xAC5F, 0xCF55, 0xAC57,
    0xCEFC, 0xAC68, 0xAEE3, 0xAC5D, 0xCF4E, 0xCF4D, 0xCF42, 0,      0xCF5E,
    0,      0xCF57, 0,      0,      0xAC55, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0xD1EC, 0xAEEA, 0xD1ED, 0,
    0xD1E1, 0xAEDF, 0xAEEB, 0,      0xD1DA, 0,      0xD1E3, 0xD1EB, 0,
    0xD1D9, 0xD1F4, 0xAED5, 0,      0,      0,      0xD1F3, 0xD1EE, 0,
    0xD1EF, 0xAEDD, 0xAEE8, 0xD1E5, 0,      0xD1E6, 0xD1F0, 0xD1E7, 0,
    0xD1E2, 0xD1DC, 0xD1DD, 0xD1EA, 0xD1E4, 0,      0,      0xAED6, 0xAEDA,
    0xD1F2, 0xD1DE, 0xAEE6, 0xAEE2, 0,      0,      0xAEE5, 0xAEEC, 0xAEDB,
    0xAEE7, 0xD1E9, 0xAEE9, 0xAED8, 0,      0xAED7, 0xD1DB, 0,      0xD1DF,
    0xAEE0, 0xD1F1, 0xD1E8, 0xD1E0, 0xAEE4, 0xAEE1, 0,      0xAED9, 0xAEDC,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0xD5C4, 0,      0xD5B4, 0xD5B5, 0xD5B9,
    0,      0xD5C8, 0xD5C5, 0,      0xD5BE, 0xD5BD, 0xB1ED, 0xD5C1, 0xD5D0,
    0xD5B0, 0,      0xD5D1, 0xD5C3, 0xD5D5, 0xD5C9, 0xB1EC, 0xD5C7, 0xB1E7,
    0xB1FC, 0xB1F2, 0,      0xB1F6, 0xB1F5, 0xD5B1, 0,      0xD5CE, 0xD5D4,
    0xD5CC, 0xD5D3, 0,      0,      0xD5C0, 0xD5B2, 0xD5D2, 0xD5C2, 0xB1EA,
    0xB1F7, 0,      0xD5CB, 0xB1F0, 0,      0,      0,      0xD5CA, 0xD5B3,
    0xB1F8, 0,      0xB1FA, 0xD5CD, 0xB1FB, 0xB1E9, 0xD5BA, 0xD5CF, 0,
    0,      0xB1EF, 0xB1F9, 0xD5BC, 0xD5C6, 0xD5B7, 0xD5BB, 0xB1F4, 0xD5B6,
    0xB1E8, 0xB1F1, 0xB1EE, 0xD5BF, 0xAEDE, 0xD9C0, 0xB1EB, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xB1F3, 0,      0xD9C3, 0xD9D9, 0xD9CE, 0xB4D6,
    0,      0xB4D1, 0xD9BD, 0xB4D2, 0xD9CD, 0,      0xD9C6, 0xD9D3, 0xB4CE,
    0xD9AB, 0xD9D5, 0xB4C4, 0xD9B3, 0xB4C7, 0xB4C6, 0,      0xB4D7, 0,
    0xD9AD, 0xD9CF, 0xD9D0, 0xB4C9, 0xB4C5, 0xD9BB, 0,      0xB4D0, 0xD9B6,
    0,      0xD9D1, 0xB4CC, 0xD9C9, 0xD9D6, 0xD9B0, 0xD9B5, 0xD9AF, 0,
    0xB4CB, 0xD9C2, 0xDDDE, 0xD9B1, 0xB4CF, 0xD9BA, 0xD9D2, 0xB4CA, 0xD9B7,
    0xD9B4, 0xD9C5, 0xB4CD, 0xB4C3, 0xB4D9, 0xD9C8, 0xD9C7, 0,      0,
    0,      0,      0,      0,      0xD9AC, 0xB4C8, 0xD9D4, 0xD9BC, 0xD9BE,
    0,      0xD9CB, 0xD9CA, 0xD9AA, 0xB4D3, 0xB4D5, 0xD9B2, 0xD9B9, 0xD9C1,
    0xB4D4, 0xD9B8, 0xD9C4, 0xD9D7, 0,      0xD9CC, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0xD9D8, 0,      0,      0,      0,      0xD9AE, 0,      0,
    0,      0,      0xDDF2, 0xB7A6, 0,      0xDDF0, 0xDDDB, 0xDDE0, 0xDDD9,
    0,      0xDDEC, 0xDDCB, 0xDDD2, 0,      0xDDEA, 0xDDF4, 0xDDDC, 0,
    0xDDCF, 0xDDE2, 0xDDE7, 0xDDD3, 0,      0xDDE4, 0xDDD0, 0,      0,
    0xDDD7, 0xDDD8, 0xB7A8, 0xDDEB, 0xDDE9, 0,      0xDDCC, 0xDDEE, 0,
    0xDDEF, 0xDDF1, 0xB7AC, 0xB7A4, 0,      0xD5B8, 0xDDD4, 0xDDE6, 0xDDD5,
    0xB7A1, 0xB7B1, 0xDDED, 0xB7AF, 0xB7AB, 0xDDCA, 0xB7A3, 0,      0xDDCD,
    0xB7B0, 0,      0xDDDD, 0xDDC9, 0,      0xB7A9, 0xDDE1, 0xDDD1, 0xB7AA,
    0xDDDA, 0xB77E, 0xB4D8, 0xDDE3, 0xD9BF, 0xDDCE, 0,      0,      0xDDE8,
    0xB7A5, 0xDDE5, 0xB7A2, 0xDDDF, 0xB7AD, 0xDDD6, 0xDDF3, 0,      0,
    0,      0,      0,      0,      0xB7A7, 0xDEC6, 0,      0,      0xB7AE,
    0,      0,      0,      0,      0,      0,      0xE24A, 0xE248, 0,
    0xE25E, 0xE246, 0,      0xE258, 0xB77D, 0xBA5F, 0xE242, 0xE25D, 0,
    0xE247, 0xE255, 0xBA64, 0xBA5D, 0,      0xE25B, 0,      0xE240, 0xE25A,
    0,      0xBA6F, 0xE251, 0xE261, 0xBA6D, 0xE249, 0xBA5E, 0xE24B, 0xE259,
    0xBA67, 0xE244, 0xBA6B, 0xBA61, 0xE24D, 0xE243, 0xE1FC, 0,      0xE257,
    0xBA68, 0xE260, 0xE1FD, 0xBA65, 0,      0xE253, 0,      0xBA66, 0xE245,
    0xE250, 0xE24C, 0xE24E, 0,      0xBA60, 0xE25F, 0xBA6E, 0xE24F, 0,
    0xE262, 0,      0,      0xE1FE, 0xE254, 0xBA63, 0xBA6C, 0xBA6A, 0xE241,
    0xE256, 0xBA69, 0,      0,      0xBA62, 0xE252, 0,      0,      0,
    0,      0xE25C, 0,      0,      0,      0,      0,      0,      0,
    0,      0xE5D5, 0,      0xE5D1, 0xE5CD, 0xE5E1, 0xE5DE, 0xBCCD, 0,
    0,      0xE5E5, 0xE5D4, 0xBCD8, 0xE5DB, 0,      0,      0xE5D0, 0xE5DA,
    0xBCD5, 0xE5EE, 0,      0xE5EB, 0xE5DD, 0xE5CE, 0,      0,      0xE5E2,
    0xE5E4, 0xBCD1, 0xE5D8, 0xE5D3, 0xE5CA, 0xBCCE, 0xBCD6, 0,      0xE5E7,
    0xBCD7, 0xE5CB, 0xE5ED, 0xE5E0, 0xE5E6, 0xBCD4, 0,      0,      0xE5E3,
    0,      0xE5EA, 0,      0xBCD9, 0,      0xBCD3, 0xE5DC, 0xE5CF, 0xE5EF,
    0xE5CC, 0xE5E8, 0xBCD0, 0,      0xE5D6, 0,      0xE5D7, 0xBCCF, 0xBCCC,
    0xE5D2, 0xBCD2, 0,      0xBCCB, 0,      0xE5E9, 0xE5EC, 0xE5D9, 0xE9CA,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0xE9C2, 0,      0xE9BE, 0xBEF6, 0,      0,      0xBEEB, 0xBEF0, 0xBEEC,
    0xE9CC, 0xE9D7, 0xBEEA, 0xE9C4, 0xE9CD, 0xE5DF, 0xE9CE, 0,      0,
    0xBEF1, 0,      0xE9DD, 0xBEF5, 0xBEF8, 0xE9C0, 0,      0xBEF4, 0,
    0xE9DB, 0xE9DC, 0xE9D2, 0xE9D1, 0xE9C9, 0,      0,      0xE9D3, 0xE9DA,
    0xE9D9, 0,      0xBEEF, 0xBEED, 0xE9CB, 0xE9C8, 0,      0xE9C5, 0xE9D8,
    0xBEF7, 0xE9D6, 0xBEF3, 0xBEF2, 0,      0xE9D0, 0,      0xE9BF, 0xE9C1,
    0xE9C3, 0xE9D5, 0xE9CF, 0xBEEE, 0,      0xE9C6, 0,      0xE9D4, 0,
    0,      0,      0,      0,      0,      0xE9C7, 0,      0,      0,
    0,      0,      0,      0,      0xC0CF, 0xED45, 0xC0C8, 0xECF5, 0,
    0xED41, 0xC0CA, 0xED48, 0,      0xECFC, 0,      0xECF7, 0,      0,
    0xED49, 0xECF3, 0xECFE, 0,      0xC0D1, 0xED44, 0xED4A, 0xECFD, 0xC0C9,
    0xED40, 0xECF4, 0xC0D0, 0,      0,      0xED47, 0xECF9, 0xC0CC, 0,
    0xECFB, 0xECF8, 0xC0D2, 0xECFA, 0xC0CB, 0xC0CE, 0xED43, 0xECF6, 0xED46,
    0,      0xED42, 0,      0,      0,      0xC263, 0xEFE7, 0xC268, 0xC269,
    0,      0,      0,      0xC262, 0xEFE6, 0,      0xEFE3, 0xEFE4, 0xC266,
    0xEFDE, 0xEFE2, 0xC265, 0,      0xEFDF, 0,      0,      0,      0,
    0xC267, 0xC264, 0,      0xEFDD, 0xEFE1, 0xEFE5, 0,      0,      0,
    0xF251, 0xF24E, 0xF257, 0,      0xF256, 0xF254, 0xF24F, 0,      0xC372,
    0,      0,      0,      0,      0,      0xF250, 0xC371, 0xC0CD, 0xF253,
    0xC370, 0xF258, 0xF252, 0xF24D, 0xEFE0, 0,      0,      0,      0xC36F,
    0,      0xF24C, 0xF456, 0,      0xF455, 0xF255, 0xC468, 0,      0xF459,
    0xF45A, 0xF454, 0xF458, 0,      0xF453, 0,      0,      0,      0,
    0xF5D1, 0xF457, 0xC4E7, 0xC4E5, 0xF5CF, 0,      0,      0,      0xF5D2,
    0,      0xF5CE, 0xF5D0, 0xC4E6, 0,      0,      0,      0xF6E5, 0xF6E6,
    0xC576, 0xF6E4, 0,      0,      0,      0xF7E2, 0xC5CF, 0xF7E0, 0xF7E1,
    0xF8AC, 0,      0,      0xC656, 0xF8F3, 0xF8F1, 0xF8F2, 0xF8F4, 0,
    0,      0,      0xF9BB, 0,      0xA4ED, 0xA6B8, 0,      0xAA59, 0,
    0xCCE9, 0,      0,      0xCF64, 0,      0,      0,      0xD1F5, 0xD1F7,
    0,      0xD1F6, 0,      0xD1F8, 0xB1FD, 0xD5D7, 0xD1F9, 0,      0xD5D6,
    0xD5D8, 0xD5D9, 0xD9DA, 0xB4DB, 0xD9DB, 0xD9DD, 0xB4DC, 0xB4DA, 0xD9DC,
    0,      0xDDFA, 0xDDF8, 0xDDF7, 0,      0xDDF6, 0xDDF5, 0xB7B2, 0xDDF9,
    0xBA70, 0xE263, 0xE265, 0xBA71, 0xE264, 0xBCDB, 0,      0xBCDA, 0xE5F0,
    0,      0,      0xE9DF, 0xE9DE, 0xE9E0, 0,      0,      0xBEF9, 0,
    0xED4B, 0xC0D3, 0,      0xEFE8, 0xC26A, 0xF259, 0xC577, 0xA4EE, 0xA5BF,
    0xA6B9, 0xA842, 0xAA5A, 0xAA5B, 0,      0,      0xAC6E, 0,      0,
    0xD1FA, 0,      0,      0,      0,      0xB7B3, 0,      0,      0,
    0xE6D1, 0xBEFA, 0xC26B, 0xA4EF, 0,      0xA6BA, 0,      0,      0xCCEB,
    0xAA5C, 0xCCEA, 0,      0xCF65, 0xAC6F, 0xCF66, 0,      0xAC70, 0,
    0xD1FC, 0xAEEE, 0xAEED, 0,      0xD5DE, 0xD5DC, 0xD5DD, 0xD5DB, 0,
    0xD5DA, 0,      0,      0xD9DE, 0xD9E1, 0xB4DE, 0xD9DF, 0xB4DD, 0xD9E0,
    0,      0xDDFB, 0,      0,      0xE266, 0xE267, 0xE268, 0,      0xE5F3,
    0xE5F2, 0xBCDC, 0xE5F1, 0xE5F4, 0xE9E1, 0,      0,      0xE9E2, 0xE9E3,
    0,      0xED4C, 0xC0D4, 0xC26C, 0xF25A, 0,      0xC4E8, 0xC95F, 0,
    0xAC71, 0xCF67, 0xAEEF, 0,      0,      0xB1FE, 0,      0xB4DF, 0xD9E2,
    0,      0xB7B5, 0xB7B4, 0,      0,      0xE269, 0xE26A, 0xBCDD, 0xBCDE,
    0xE9E5, 0xE9E4, 0xEFE9, 0xF7E3, 0xA4F0, 0xC960, 0xA5C0, 0,      0xA843,
    0xCB48, 0,      0xAC72, 0xB7B6, 0xA4F1, 0,      0xCF68, 0xAC73, 0xCF69,
    0,      0xC0D5, 0xA4F2, 0,      0,      0xCCEC, 0,      0xCF6A, 0,
    0xD242, 0xD241, 0xD1FE, 0,      0xD1FD, 0xD243, 0xD240, 0,      0,
    0xB240, 0xB241, 0,      0,      0xB4E0, 0xD9E3, 0,      0xD9E4, 0xD9E5,
    0,      0,      0,      0xDE41, 0xDE42, 0xDE40, 0,      0xDDFD, 0xDDFE,
    0xB7B7, 0xE26B, 0xE5F7, 0xE5F6, 0xE5F5, 0xE5F8, 0xE9E7, 0xE9E6, 0xBEFB,
    0xE9E8, 0,      0xC0D6, 0xED4D, 0,      0xEFEA, 0xF25B, 0xF6E7, 0,
    0xA4F3, 0xA5C2, 0xA5C1, 0,      0xAA5D, 0xC961, 0xC97E, 0xA6BB, 0,
    0xC9F7, 0xCB49, 0xCB4A, 0xAA5E, 0,      0xCCED, 0,      0xAC74, 0xCF6B,
    0xCF6C, 0,      0xAEF0, 0xAEF4, 0xD244, 0xAEF3, 0xAEF1, 0xAEF2, 0,
    0xD5DF, 0xB242, 0xB4E3, 0,      0xB4E1, 0xB4E2, 0xD9E6, 0,      0,
    0xBA72, 0xA4F4, 0,      0xC9A1, 0,      0xA5C3, 0,      0,      0xC9A4,
    0,      0,      0xA5C6, 0xC9A3, 0xA5C5, 0xA5C4, 0xA844, 0xC9A2, 0,
    0,      0xC9F8, 0,      0,      0,      0xC9FC, 0xC9FE, 0xCA40, 0xA6C5,
    0xA6C6, 0xC9FB, 0xA6C1, 0,      0xC9F9, 0,      0xC9FD, 0xA6C2, 0,
    0xA6BD, 0,      0xA6BE, 0,      0xA6C4, 0xC9FA, 0xA6BC, 0xA845, 0xA6BF,
    0xA6C0, 0xA6C3, 0,      0,      0,      0xCB5B, 0xCB59, 0xCB4C, 0xA851,
    0xCB53, 0xA84C, 0xCB4D, 0,      0xCB55, 0,      0xCB52, 0xA84F, 0xCB51,
    0xA856, 0xCB5A, 0xA858, 0,      0xA85A, 0,      0xCB4B, 0,      0xA84D,
    0xCB5C, 0,      0xA854, 0xA857, 0,      0xCD45, 0xA847, 0xA85E, 0xA855,
    0xCB4E, 0xA84A, 0xA859, 0xCB56, 0xA848, 0xA849, 0xCD43, 0xCB4F, 0xA850,
    0xA85B, 0xCB5D, 0xCB50, 0xA84E, 0,      0xA853, 0xCCEE, 0xA85C, 0xCB57,
    0xA852, 0,      0xA85D, 0xA846, 0xCB54, 0xA84B, 0xCB58, 0xCD44, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xAA6A, 0xAA7A, 0xCCF5, 0xAA71, 0,      0xCD4B,
    0xAA62, 0,      0xAA65, 0xCD42, 0,      0xCCF3, 0xCCF7, 0xAA6D, 0xAA6F,
    0xCCFA, 0xAA76, 0xAA68, 0xAA66, 0xAA67, 0xAA75, 0xCD47, 0xAA70, 0xCCF9,
    0xCCFB, 0xAA6E, 0xAA73, 0xCCFC, 0xCD4A, 0,      0xAC75, 0xAA79, 0,
    0xAA63, 0xCD49, 0,      0xCD4D, 0xCCF8, 0xCD4F, 0xCD40, 0xAA6C, 0xCCF4,
    0xAA6B, 0xAA7D, 0xAA72, 0,      0xCCF2, 0xCF75, 0xAA78, 0xAA7C, 0xCD41,
    0xCD46, 0,      0xAA7E, 0xAA77, 0xAA69, 0xAA5F, 0,      0xAA64, 0,
    0xCCF6, 0xAA60, 0xCD4E, 0,      0xCCF0, 0xCCEF, 0xCCFD, 0xCCF1, 0xAA7B,
    0xAEF5, 0xAA74, 0xCCFE, 0xAA61, 0,      0xACA6, 0,      0,      0,
    0xCD4C, 0,      0,      0,      0,      0,      0,      0xCF7C, 0xCFA1,
    0,      0xCFA4, 0xCF77, 0,      0,      0xCFA7, 0xCFAA, 0xCFAC, 0xCF74,
    0xAC76, 0xAC7B, 0xD249, 0xACAD, 0xCFA5, 0xCFAD, 0xCF7B, 0xCF73, 0,
    0,      0,      0xD264, 0xAC7E, 0xCFA2, 0xCF78, 0xCF7A, 0xACA5, 0,
    0xCF7D, 0xAC7D, 0xCF70, 0xCFA8, 0,      0xCFAB, 0,      0,      0xAC7A,
    0,      0xACA8, 0xCF6D, 0xACAA, 0xAC78, 0xACAE, 0xCFA9, 0xCF6F, 0xACAB,
    0xD25E, 0xCD48, 0xAC7C, 0xAC77, 0xCF76, 0xCF6E, 0xACAC, 0xACA4, 0xCFA3,
    0xACA9, 0xACA7, 0xCF79, 0xACA1, 0xCF71, 0xACA2, 0xACA3, 0xCF72, 0xCFA6,
    0xAC79, 0xCF7E, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0xD24C, 0xAEFD, 0xAF43, 0,
    0,      0,      0xD255, 0xD25B, 0xD257, 0xD24A, 0xD24D, 0xD246, 0xD247,
    0xAF4A, 0xAEFA, 0xD256, 0xD25F, 0xAF45, 0xAEF6, 0,      0xAF40, 0xD24E,
    0xAF42, 0xD24F, 0xD259, 0,      0,      0,      0xAF44, 0xD268, 0xD248,
    0xAEFC, 0xAEFB, 0xAF48, 0xD245, 0xD266, 0xD25A, 0xD267, 0xD261, 0xD253,
    0xD262, 0,      0xD25C, 0xD265, 0xD263, 0xAF49, 0xD254, 0xAEF9, 0xAEF8,
    0xAF41, 0xAF47, 0xD260, 0xAF46, 0xD251, 0xB243, 0,      0xD269, 0xD250,
    0xD24B, 0xAEFE, 0xAF4B, 0xAEF7, 0,      0xD258, 0xD25D, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0xB265, 0xD5E1, 0xD5E5,
    0,      0xB252, 0xB250, 0,      0,      0xB247, 0xD5E3, 0xD5E2, 0xB25B,
    0,      0xD5E8, 0xB255, 0,      0xD5FA, 0xD647, 0xB244, 0xD5F7, 0xD5F0,
    0xB267, 0xD5E0, 0,      0xD5FC, 0,      0xB264, 0xB258, 0xB263, 0xB24E,
    0xD5EC, 0xD5FE, 0xD5F6, 0xB24F, 0xB249, 0xD645, 0,      0xD5FD, 0xD640,
    0xB251, 0xB259, 0xD642, 0xD5EA, 0xD5FB, 0xD5EF, 0xD644, 0xB25E, 0xB246,
    0xB25C, 0xD5F4, 0xD5F2, 0xD5F3, 0xB253, 0xD5EE, 0xD5ED, 0xB248, 0xD5E7,
    0xD646, 0xB24A, 0xD5F1, 0xB268, 0,      0xB262, 0xD5E6, 0xB25F, 0xB25D,
    0xB266, 0xD5F8, 0xB261, 0xD252, 0xD5F9, 0xB260, 0xD641, 0xB245, 0xD5F5,
    0xB257, 0xD5E9, 0xB256, 0,      0xB254, 0xB24C, 0xB24B, 0xD9E7, 0xD643,
    0,      0,      0xD5EB, 0,      0,      0xD9FC, 0,      0xB24D, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0xB541, 0xB25A, 0xB4EE, 0xD9F6, 0xB4FC, 0,      0xD9EA, 0xB4EB, 0xB4E7,
    0xDA49, 0xB4ED, 0xB4F1, 0xB4EC, 0xB4F5, 0xDA4D, 0xDA44, 0,      0,
    0xD9F1, 0xB4FA, 0xB4F4, 0xD9FD, 0xB4E4, 0xDA4A, 0xDA43, 0xB4E8, 0xD9F7,
    0xB4F7, 0xDA55, 0xDA56, 0,      0xB4E5, 0xDA48, 0xB4F9, 0xD9FB, 0xD9ED,
    0xD9EE, 0xB4FD, 0xD9F2, 0xD9F9, 0xD9F3, 0,      0xB4FB, 0xB544, 0xD9EF,
    0xD9E8, 0xD9E9, 0,      0xD9EB, 0xB4EA, 0xD9F8, 0,      0xB4F8, 0xB542,
    0,      0,      0xD9FA, 0xDA53, 0xDA4B, 0xB4E6, 0xDA51, 0xB4F2, 0,
    0xB4F0, 0,      0xDA57, 0xB4EF, 0xDA41, 0xD9F4, 0xD9FE, 0xB547, 0xDA45,
    0xDA42, 0xD9F0, 0xB543, 0xDA4F, 0xDA4C, 0xDA54, 0xB4E9, 0xDA40, 0xB546,
    0,      0xDA47, 0,      0,      0xB4F3, 0xB4F6, 0,      0xDA46, 0xB545,
    0xD9F5, 0xD5E4, 0,      0,      0xDA50, 0xDA4E, 0xDA52, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xD9EC, 0xB540, 0,      0,      0,      0xDE61,
    0xDE60, 0xDE46, 0xB7BD, 0,      0xDE5F, 0xDE49, 0xDE4A, 0,      0xB7C7,
    0xDE68, 0xB7C2, 0xDE5E, 0,      0xDE43, 0xB7C8, 0xB7BE, 0xDE52, 0xDE48,
    0xDE4B, 0xDE63, 0xB7B8, 0xDE6A, 0xDE62, 0xB7C1, 0xDE57, 0xB7CC, 0,
    0,      0xB7CB, 0xB7C5, 0,      0,      0xDE69, 0xB7B9, 0xDE55, 0xDE4C,
    0xDE59, 0xDE65, 0xB7CD, 0,      0xB7BB, 0xDE54, 0,      0xDE4D, 0xB7C4,
    0,      0xB7C3, 0xDE50, 0xDE5A, 0xDE64, 0xDE47, 0xDE51, 0xB7BC, 0xDE5B,
    0xB7C9, 0xB7C0, 0xDE4E, 0xB7BF, 0xDE45, 0xDE53, 0xDE67, 0xB4FE, 0xBAB0,
    0xDE56, 0xE26C, 0xDE58, 0xDE66, 0xB7C6, 0xDE4F, 0xB7BA, 0xB7CA, 0xBCF0,
    0xDE44, 0,      0xDE5D, 0,      0,      0,      0xDE5C, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xE2AA, 0xBAAD, 0xE27D, 0xE2A4, 0xBAA2, 0,
    0xE26E, 0xBAAF, 0,      0xBA77, 0xE26D, 0xE2B0, 0xBAB1, 0xE271, 0xE2A3,
    0,      0xE273, 0xE2B3, 0xE2AF, 0xBA75, 0xBAA1, 0xE653, 0xBAAE, 0xBA7D,
    0xE26F, 0,      0xE2AE, 0xBAA3, 0xE2AB, 0xE2B8, 0xE275, 0xE27E, 0,
    0,      0xE2B6, 0xE2AC, 0xBA7C, 0,      0,      0xE27C, 0xBA76, 0xBA74,
    0xBAA8, 0,      0,      0xE27A, 0xE277, 0xE278, 0,      0xE2B2, 0,
    0xE2B7, 0xE2B5, 0xBA7A, 0xE2B9, 0xBA7E, 0xBAA7, 0,      0xE270, 0xE5FA,
    0xE279, 0,      0xBA78, 0xBAAC, 0xBAA9, 0xBA7B, 0xE2A5, 0xE274, 0xBAAA,
    0xE2A7, 0xBAA4, 0xBAA6, 0xBA73, 0,      0xE2A9, 0xE2A1, 0xE272, 0xBAA5,
    0xE2B1, 0xE2B4, 0xE27B, 0xE2A8, 0,      0xBA79, 0xBCDF, 0xE2A6, 0xE5F9,
    0,      0xE2AD, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xE276, 0xE644, 0xE64E, 0xBCE2, 0xE64D, 0xE659,
    0xBCE4, 0xE64B, 0,      0xE64F, 0xBCEF, 0,      0xE646, 0xBCE7, 0,
    0xE652, 0xE9F0, 0xBCF3, 0xBCF2, 0xE654, 0xE643, 0xE65E, 0xBCED, 0,
    0xBCE3, 0xE657, 0,      0xE65B, 0xE660, 0xE655, 0xE649, 0xBCE6, 0xBCE9,
    0xBCF1, 0xBCEC, 0,      0xE64C, 0xE2A2, 0,      0,      0xE648, 0xE65F,
    0xBCE8, 0,      0xBCEB, 0xE661, 0xBCE0, 0xE656, 0xE5FB, 0xE65C, 0xC0DF,
    0,      0xE64A, 0,      0xBCE1, 0xE645, 0xBCE5, 0xE5FC, 0xBAAB, 0xE641,
    0,      0xE65A, 0xE642, 0xE640, 0xBCEA, 0,      0xE658, 0,      0xE5FE,
    0xE651, 0xE650, 0xE65D, 0xE647, 0xBCEE, 0,      0,      0,      0,
    0,      0,      0xE9F3, 0,      0xBF49, 0xBEFE, 0xEA40, 0xE9EB, 0xBF41,
    0xE9F7, 0xBF48, 0xBF43, 0xE9F5, 0xED4F, 0xE9FB, 0xEA42, 0xE9FA, 0xE9E9,
    0xE9F8, 0xEA44, 0xEA46, 0xBEFD, 0xEA45, 0xBF44, 0xBF4A, 0,      0xBF47,
    0,      0xE9FE, 0xBF46, 0xE9F9, 0,      0xE9ED, 0xE9F2, 0,      0xE9FD,
    0xBF45, 0xBF42, 0xBEFC, 0xBF40, 0xE9F1, 0,      0xE5FD, 0xE9EC, 0xE9EF,
    0xEA41, 0xE9F4, 0xE9EA, 0xED4E, 0xEA43, 0xE9EE, 0xE9FC, 0,      0,
    0,      0,      0xED51, 0xC0E3, 0,      0,      0xC0D7, 0,      0,
    0xC0DB, 0xED53, 0xED59, 0xED57, 0xC0D9, 0xC0DA, 0xC0E1, 0xED5A, 0xED52,
    0xC0DC, 0,      0xED56, 0xED55, 0xED5B, 0xC0E2, 0,      0xC0DD, 0xC0E0,
    0xED54, 0xC0E4, 0xC0DE, 0xC0E5, 0xC0D8, 0xED58, 0,      0xED50, 0,
    0,      0xEFF7, 0,      0,      0xC271, 0xEFF4, 0xEFF6, 0,      0xC26F,
    0xEFF2, 0xEFF3, 0xEFEE, 0,      0,      0xE9F6, 0xEFEF, 0xC270, 0xEFEB,
    0,      0xC26D, 0xEFF8, 0xC26E, 0xEFEC, 0xEFED, 0xEFF1, 0xC273, 0,
    0xC272, 0,      0,      0xEFF0, 0xC378, 0xF25F, 0xF265, 0xC379, 0xF25C,
    0xC376, 0xC373, 0xF267, 0xC377, 0,      0xC374, 0xF25E, 0xF261, 0xF262,
    0xF263, 0xF266, 0,      0xEFF5, 0xF25D, 0xC375, 0xF264, 0xF268, 0xF260,
    0,      0,      0,      0xF45D, 0xC46A, 0xF460, 0xC46B, 0xF468, 0xF45F,
    0xF45C, 0,      0xF45E, 0xF462, 0xF465, 0xF464, 0xF467, 0xF45B, 0,
    0xC469, 0xF463, 0xF466, 0xF469, 0xF461, 0xF5D3, 0xF5D4, 0xF5D8, 0xF5D9,
    0,      0xF5D6, 0xF5D7, 0xF5D5, 0,      0xC4E9, 0,      0,      0,
    0,      0xC578, 0xF6EB, 0,      0,      0xF6E8, 0xF6E9, 0xF6EA, 0xC579,
    0,      0xF7E5, 0xF7E4, 0,      0xF8AF, 0xC5F4, 0xF8AD, 0xF8B0, 0xF8AE,
    0xF8F5, 0xC657, 0xC665, 0xF9A3, 0xF96C, 0,      0xF9A2, 0xF9D0, 0xF9D1,
    0xA4F5, 0,      0,      0,      0,      0xA6C7, 0xCA41, 0,      0,
    0xCB5E, 0,      0xA85F, 0,      0xA862, 0,      0xCB5F, 0,      0xA860,
    0xA861, 0,      0,      0,      0,      0xCD58, 0xCD5A, 0xCD55, 0xCD52,
    0xCD54, 0,      0,      0,      0xAAA4, 0,      0,      0,      0xAAA2,
    0,      0,      0xCD56, 0xAAA3, 0xCD53, 0xCD50, 0xAAA1, 0xCD57, 0,
    0xCD51, 0xAAA5, 0xCD59, 0,      0,      0,      0,      0xCFAF, 0,
    0xCFB3, 0,      0,      0xACB7, 0,      0,      0,      0,      0xCFB6,
    0,      0xACAF, 0xACB2, 0xACB4, 0xACB6, 0xACB3, 0xCFB2, 0xCFB1, 0,
    0xACB1, 0xCFB4, 0xCFB5, 0,      0xCFAE, 0xACB5, 0,      0xACB0, 0,
    0,      0,      0xCFB0, 0,      0,      0,      0,      0,      0,
    0xD277, 0xD278, 0xD279, 0xAF50, 0,      0xAF4C, 0xD26E, 0,      0xD276,
    0xD27B, 0xAF51, 0,      0xD26C, 0xD272, 0xD26B, 0xD275, 0,      0,
    0xD271, 0xAF4D, 0xAF4F, 0xD27A, 0,      0xD26A, 0xD26D, 0xD273, 0,
    0xD274, 0xD27C, 0xD270, 0,      0xAF4E, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0xB26D, 0xD64E, 0,
    0,      0xD650, 0xD64C, 0,      0xD658, 0xD64A, 0xD657, 0xB269, 0xD648,
    0xDA5B, 0xD652, 0xB26C, 0,      0xD653, 0xD656, 0,      0xD65A, 0,
    0xD64F, 0,      0xD654, 0,      0,      0xB26A, 0xB26B, 0xD659, 0xD64D,
    0xD649, 0xD65B, 0,      0xD651, 0,      0,      0xD655, 0,      0,
    0,      0xD64B, 0,      0xB548, 0xB549, 0xDA65, 0xB54F, 0,      0xDA59,
    0xDA62, 0xDA58, 0xB54C, 0xDA60, 0xDA5E, 0,      0xDA5F, 0xB54A, 0,
    0xDA63, 0,      0,      0,      0,      0,      0xDA5C, 0xDA5A, 0xB54B,
    0xDA5D, 0xDA61, 0,      0,      0,      0xB54D, 0,      0,      0,
    0xDA64, 0,      0,      0,      0,      0,      0,      0xDE70, 0xDE77,
    0xDE79, 0xDEA1, 0,      0xB7DA, 0xDE6B, 0,      0xB7D2, 0,      0xDE7A,
    0xB7D7, 0xDEA2, 0xB7CE, 0,      0xDE7D, 0,      0xDE6D, 0xDE7E, 0xDE6C,
    0,      0xB7DC, 0,      0xDE78, 0xB7CF, 0xDEA3, 0,      0xB7D4, 0xDE71,
    0xB7D9, 0xDE7C, 0xDE6F, 0xDE76, 0xDE72, 0xDE6E, 0xB7D1, 0xB7D8, 0xB7D6,
    0xB7D3, 0xB7DB, 0xB7D0, 0xDE75, 0,      0xB7D5, 0,      0xB54E, 0,
    0xDE7B, 0,      0xDE73, 0,      0,      0,      0,      0,      0xDE74,
    0,      0,      0xE2C1, 0,      0xBAB4, 0,      0,      0xE2BD, 0xE2C3,
    0xE2BF, 0,      0xBAB6, 0xE2BE, 0xE2C2, 0xE2BA, 0,      0xE2BC, 0xBAB5,
    0,      0,      0,      0,      0xE2C0, 0xE2BB, 0,      0xBAB7, 0,
    0xBAB2, 0,      0,      0xE2C4, 0,      0xBAB3, 0xE667, 0xE664, 0xE670,
    0xE66A, 0xE66C, 0xBCF4, 0xE666, 0xE66E, 0,      0,      0xE66D, 0xE66B,
    0,      0xE671, 0xBCF7, 0xE668, 0xE66F, 0,      0xBCF5, 0,      0,
    0xE663, 0xE665, 0xBCF6, 0xE662, 0xE672, 0,      0xE669, 0,      0,
    0xEA4A, 0xBF51, 0,      0,      0xEA55, 0xEA53, 0xBF4B, 0xEA49, 0xEA4C,
    0xEA4D, 0xEA48, 0xBF55, 0xBF56, 0xEA47, 0xEA56, 0xEA51, 0xBF4F, 0xBF4C,
    0xEA50, 0xEA4E, 0,      0,      0xBF52, 0xEA52, 0xBF4D, 0,      0xBF4E,
    0,      0xEA4F, 0xBF50, 0xEA4B, 0,      0xEA54, 0xBF53, 0xEA57, 0xEA58,
    0xBF54, 0,      0,      0xC0E7, 0xC0EE, 0xED5C, 0xED62, 0,      0xED60,
    0xC0EA, 0xC0E9, 0xC0E6, 0xED5E, 0,      0,      0,      0xC0EC, 0xC0EB,
    0xC0E8, 0,      0xED61, 0xED5D, 0xED5F, 0,      0xC0ED, 0,      0,
    0,      0xC277, 0xEFFB, 0,      0xC274, 0xC275, 0xEFFD, 0xC276, 0xEFFA,
    0,      0xEFF9, 0xF26C, 0xEFFC, 0,      0xF26D, 0xC37A, 0xF26B, 0,
    0,      0xF26A, 0,      0xF269, 0xC37B, 0,      0,      0xC46C, 0,
    0,      0xF46A, 0xF46B, 0,      0,      0,      0,      0xF5DC, 0xF5DB,
    0xC4EA, 0,      0xF5DA, 0xF6EC, 0xF6ED, 0,      0,      0xF7E6, 0xF8B1,
    0,      0,      0xF8F6, 0xF9BC, 0xC679, 0xF9C6, 0xA4F6, 0,      0xAAA6,
    0xAAA7, 0,      0,      0xACB8, 0,      0,      0,      0,      0xC0EF,
    0xA4F7, 0,      0xAAA8, 0xAF52, 0xB7DD, 0xA4F8, 0,      0xB26E, 0xBAB8,
    0xC962, 0,      0xCFB7, 0xD27D, 0,      0xE2C5, 0,      0xC0F0, 0xA4F9,
    0xAAA9, 0xCFB8, 0xCFB9, 0xDA66, 0xB550, 0,      0,      0xDEA4, 0,
    0,      0xB7DE, 0xE2C6, 0,      0,      0xBCF8, 0,      0xC37C, 0xA4FA,
    0xDA67, 0xA4FB, 0,      0xA6C9, 0xCA42, 0xA6C8, 0xA865, 0xA864, 0xA863,
    0xCB60, 0,      0,      0,      0xAAAA, 0,      0xAAAB, 0xCD5B, 0,
    0xCFBA, 0,      0xCFBD, 0xACBA, 0xCFBB, 0,      0xACB9, 0xCFBC, 0xACBB,
    0,      0xD2A2, 0xD2A1, 0xD27E, 0xAF53, 0,      0xD65D, 0xD65E, 0xB26F,
    0xD65C, 0xD65F, 0xB552, 0xB270, 0,      0,      0xB551, 0xDA6B, 0xDA6A,
    0,      0xDA68, 0xDA69, 0,      0xDA6C, 0xDEA6, 0xDEA5, 0xDEA9, 0,
    0xDEA8, 0xDEA7, 0xBAB9, 0xE2C9, 0,      0xE2C8, 0xBABA, 0xE2C7, 0xE673,
    0,      0xE674, 0xBCF9, 0,      0xEA59, 0xEA5A, 0,      0,      0xF272,
    0xC37D, 0xF271, 0xF270, 0xF26E, 0xF26F, 0xC4EB, 0xF46C, 0xF6EE, 0xF8F7,
    0,      0xA4FC, 0,      0xC9A5, 0xA5C7, 0xC9A6, 0,      0,      0,
    0xCA43, 0xCA44, 0,      0,      0,      0,      0xCB66, 0,      0,
    0xCB62, 0,      0xCB61, 0xAAAC, 0xCB65, 0xA867, 0xCB63, 0xA866, 0xCB67,
    0xCB64, 0,      0,      0xCD5F, 0xCFBE, 0xCD5D, 0xCD64, 0,      0xAAAD,
    0,      0xAAB0, 0xCD65, 0xCD61, 0,      0xCD62, 0,      0xCD5C, 0xAAAF,
    0xCD5E, 0xAAAE, 0xCD63, 0,      0xCD60, 0,      0,      0xCFC2, 0xACBD,
    0xACBE, 0,      0xCFC5, 0xCFBF, 0,      0xCFC4, 0,      0xCFC0, 0xACBC,
    0xCFC3, 0xCFC1, 0,      0,      0,      0,      0,      0,      0,
    0xD2A8, 0xD2A5, 0,      0xD2A7, 0xAF58, 0xAF57, 0xAF55, 0xD2A4, 0xD2A9,
    0xAF54, 0xAF56, 0xD2A6, 0xD667, 0xD2A3, 0xD2AA, 0,      0,      0,
    0,      0,      0xD662, 0xD666, 0,      0xD665, 0xDA6E, 0xDA79, 0,
    0,      0xD668, 0,      0xD663, 0xDA6D, 0xB274, 0,      0,      0xB273,
    0xD661, 0xD664, 0xB275, 0,      0xB272, 0xB271, 0xD660, 0xD669, 0,
    0,      0,      0xDA70, 0xDA77, 0,      0xB554, 0xDA76, 0xDA73, 0,
    0xB556, 0,      0,      0,      0xDA75, 0,      0,      0xDA6F, 0xDA71,
    0xDA74, 0xDA72, 0xB555, 0xDA78, 0xB553, 0xB7DF, 0,      0,      0xDEAD,
    0xDEAC, 0xDEAA, 0,      0xB7E2, 0xB7E1, 0xDEAE, 0,      0xDEAB, 0xE2CA,
    0xBABB, 0xB7E0, 0,      0,      0,      0xDEB0, 0xDEAF, 0,      0xE2CD,
    0xE2CB, 0xBCFA, 0,      0xBABC, 0xE2CC, 0xE676, 0,      0,      0,
    0,      0xBCFB, 0xE675, 0xE67E, 0xE67D, 0xE67B, 0,      0xE67A, 0xE677,
    0xE678, 0xE679, 0xE67C, 0xE6A1, 0,      0,      0xEA5F, 0xEA5C, 0xEA5D,
    0xBF57, 0xEA5B, 0xEA61, 0xEA60, 0xEA5E, 0,      0xED64, 0xED65, 0xC0F1,
    0,      0xC0F2, 0xED63, 0,      0xC279, 0xEFFE, 0xC278, 0xC37E, 0,
    0xC3A1, 0xC46D, 0xF46E, 0xF46D, 0xF5DD, 0xF6EF, 0xC57A, 0xF7E8, 0xF7E7,
    0xF7E9, 0xA5C8, 0xCFC6, 0xAF59, 0xB276, 0xD66A, 0xA5C9, 0xC9A7, 0xA4FD,
    0,      0,      0xCA45, 0,      0,      0,      0xCB6C, 0xCB6A, 0xCB6B,
    0xCB68, 0xA868, 0xCB69, 0,      0,      0,      0,      0,      0xCD6D,
    0,      0xAAB3, 0xCD6B, 0xCD67, 0xCD6A, 0,      0xCD66, 0xAAB5, 0xCD69,
    0,      0xAAB2, 0xAAB1, 0,      0xAAB4, 0xCD6C, 0xCD68, 0,      0,
    0,      0,      0xACC2, 0xACC5, 0xCFCE, 0xCFCD, 0xCFCC, 0xACBF, 0xCFD5,
    0xCFCB, 0,      0xACC1, 0xD2AF, 0,      0xCFD2, 0xCFD0, 0xACC4, 0,
    0xCFC8, 0xCFD3, 0,      0xCFCA, 0xCFD4, 0xCFD1, 0xCFC9, 0,      0xACC0,
    0xCFD6, 0xCFC7, 0xACC3, 0,      0,      0,      0,      0xD2B4, 0xD2AB,
    0xD2B6, 0,      0xD2AE, 0xD2B9, 0xD2BA, 0xD2AC, 0xD2B8, 0xD2B5, 0xD2B3,
    0xD2B7, 0xAF5F, 0,      0xAF5D, 0,      0,      0xD2B1, 0,      0xD2AD,
    0,      0xD2B0, 0xD2BB, 0xD2B2, 0xAF5E, 0xCFCF, 0,      0xAF5A, 0xAF5C,
    0,      0,      0,      0,      0,      0xD678, 0xD66D, 0xD66B, 0,
    0xD66C, 0,      0xD673, 0,      0xD674, 0xD670, 0xB27B, 0xD675, 0xD672,
    0xD66F, 0,      0xB279, 0xD66E, 0xB277, 0xB27A, 0xD671, 0xD679, 0xAF5B,
    0xB278, 0xD677, 0xD676, 0xB27C, 0,      0,      0,      0,      0,
    0,      0,      0,      0xDA7E, 0,      0,      0,      0xDAA1, 0xB560,
    0,      0xDAA7, 0,      0,      0xDAA9, 0xDAA2, 0xB55A, 0xDAA6, 0xDAA5,
    0xB55B, 0xB561, 0,      0xB562, 0xDAA8, 0xB558, 0xDA7D, 0xDA7B, 0xDAA3,
    0xDA7A, 0xB55F, 0xDA7C, 0xDAA4, 0xDAAA, 0xB559, 0xB55E, 0xB55C, 0xB55D,
    0,      0,      0,      0xB557, 0,      0,      0,      0,      0xB7E9,
    0xDEB7, 0xB7E8, 0xDEBB, 0,      0xDEB1, 0,      0xDEBC, 0,      0,
    0,      0xDEB2, 0xDEB3, 0,      0xDEBD, 0xDEBA, 0xDEB8, 0xDEB9, 0xDEB5,
    0xDEB4, 0,      0xDEBE, 0xB7E5, 0,      0xDEB6, 0,      0xB7EA, 0xB7E4,
    0xB7EB, 0xB7EC, 0,      0xB7E7, 0xB7E6, 0,      0,      0xE2CE, 0xBABE,
    0xBABD, 0,      0,      0xE2D3, 0,      0xBCFC, 0xBABF, 0,      0,
    0xBAC1, 0xE2D4, 0xB7E3, 0xBAC0, 0xE2D0, 0xE2D2, 0xE2CF, 0,      0xE2D1,
    0,      0,      0,      0xE6AB, 0,      0,      0xE6AA, 0xE6A7, 0xBD40,
    0xEA62, 0xBD41, 0xE6A6, 0,      0xBCFE, 0,      0xE6A8, 0xE6A5, 0xE6A2,
    0xE6A9, 0xE6A3, 0xE6A4, 0xBCFD, 0,      0,      0,      0,      0xED69,
    0,      0xEA66, 0,      0xEA65, 0xEA67, 0,      0xED66, 0xBF5A, 0,
    0xEA63, 0,      0xBF58, 0,      0xBF5C, 0xBF5B, 0xEA64, 0xEA68, 0,
    0xBF59, 0,      0xED6D, 0xC0F5, 0xC27A, 0xC0F6, 0xC0F3, 0xED6A, 0xED68,
    0,      0xED6B, 0,      0xED6E, 0xC0F4, 0xED6C, 0xED67, 0,      0,
    0xF042, 0xF045, 0xF275, 0xF040, 0,      0xF46F, 0xF046, 0,      0xC3A2,
    0xF044, 0xC27B, 0xF041, 0xF043, 0xF047, 0xF276, 0,      0xF274, 0,
    0,      0,      0,      0xC3A3, 0xF273, 0,      0,      0,      0xC46E,
    0,      0,      0,      0,      0xC4ED, 0xF6F1, 0xC4EC, 0xF6F3, 0xF6F0,
    0xF6F2, 0xC5D0, 0xF8B2, 0xA5CA, 0xCD6E, 0xD2BC, 0xD2BD, 0xB27D, 0xDEBF,
    0xBF5D, 0xC3A4, 0xC57B, 0xF8B3, 0xA5CB, 0,      0xCD6F, 0xA260, 0,
    0,      0xCFD7, 0,      0xCFD8, 0,      0,      0,      0,      0,
    0xD2BE, 0xD2BF, 0xB27E, 0xB2A1, 0,      0,      0,      0xDAAB, 0,
    0xDEC2, 0xDEC1, 0xDEC0, 0xE2D5, 0,      0xE2D6, 0xE2D7, 0xBAC2, 0,
    0,      0xE6AD, 0xE6AC, 0,      0,      0xEA69, 0xBF5E, 0xBF5F, 0,
    0xED72, 0xED6F, 0xED70, 0xED71, 0xF049, 0xF048, 0xC27C, 0xF277, 0xF5DE,
    0xA5CC, 0,      0xACC6, 0,      0xB2A2, 0xDEC3, 0,      0xA5CD, 0,
    0xD2C0, 0xB2A3, 0,      0,      0xB563, 0xB564, 0,      0xA5CE, 0xA5CF,
    0xCA46, 0xA86A, 0xA869, 0xACC7, 0xCFD9, 0xDAAC, 0xA5D0, 0xA5D1, 0xA5D2,
    0xA5D3, 0,      0,      0,      0xA86B, 0xA86C, 0xCB6E, 0xCB6D, 0,
    0,      0xAAB6, 0xCD72, 0xCD70, 0xCD71, 0,      0,      0,      0,
    0,      0,      0xCFDA, 0xCFDB, 0,      0,      0xACCB, 0xACC9, 0,
    0xACCA, 0xACC8, 0,      0,      0,      0,      0xAF60, 0,      0,
    0,      0,      0xAF64, 0xAF63, 0xD2C1, 0xAF62, 0xAF61, 0,      0xD2C2,
    0,      0,      0xB2A6, 0xD67B, 0xD67A, 0xB2A4, 0xB2A5, 0,      0,
    0,      0xB566, 0xB565, 0xDAAE, 0,      0,      0xDAAD, 0xB2A7, 0,
    0,      0,      0,      0,      0xB7ED, 0xDEC5, 0xB7EE, 0xDEC4, 0,
    0,      0,      0xE2D8, 0xE6AE, 0xBD42, 0xEA6A, 0,      0,      0,
    0xED73, 0,      0xC3A6, 0xC3A5, 0,      0,      0xC57C, 0xA5D4, 0xCD73,
    0,      0,      0xB2A8, 0xE2D9, 0xBAC3, 0,      0,      0xCB6F, 0xCB70,
    0,      0,      0xCD74, 0xAAB8, 0xAAB9, 0,      0,      0xAAB7, 0,
    0,      0,      0,      0xACCF, 0xACD0, 0xACCD, 0xACCE, 0,      0xCFDC,
    0,      0,      0xCFDD, 0xACCC, 0,      0,      0,      0,      0xD2C3,
    0,      0xAF68, 0xAF69, 0,      0xB2AB, 0xD2C9, 0,      0xAF6E, 0xAF6C,
    0xD2CA, 0xD2C5, 0xAF6B, 0xAF6A, 0xAF65, 0xD2C8, 0xD2C7, 0xD2C4, 0xAF6D,
    0,      0xD2C6, 0xAF66, 0,      0xAF67, 0,      0,      0xB2AC, 0xD6A1,
    0xD6A2, 0xB2AD, 0xD67C, 0xD67E, 0xD6A4, 0xD6A3, 0xD67D, 0,      0xB2A9,
    0xB2AA, 0,      0xDAB6, 0xB56B, 0xB56A, 0xDAB0, 0xB568, 0,      0xDAB3,
    0xB56C, 0xDAB4, 0xB56D, 0xDAB1, 0xB567, 0xB569, 0xDAB5, 0,      0xDAB2,
    0xDAAF, 0,      0,      0,      0,      0,      0xDED2, 0,      0xDEC7,
    0xB7F0, 0xB7F3, 0xB7F2, 0xB7F7, 0xB7F6, 0xDED3, 0xDED1, 0xDECA, 0xDECE,
    0xDECD, 0xB7F4, 0xDED0, 0xDECC, 0xDED4, 0xDECB, 0xB7F5, 0xB7EF, 0xB7F1,
    0,      0xDEC9, 0,      0,      0,      0,      0xE2DB, 0xBAC7, 0xE2DF,
    0xBAC6, 0xE2DC, 0xBAC5, 0,      0xDEC8, 0xDECF, 0xE2DE, 0,      0xBAC8,
    0xE2E0, 0xE2DD, 0xE2DA, 0,      0,      0xE6B1, 0xE6B5, 0xE6B7, 0xE6B3,
    0xE6B2, 0xE6B0, 0xBD45, 0xBD43, 0xBD48, 0xBD49, 0xE6B4, 0xBD46, 0xE6AF,
    0xBD47, 0xBAC4, 0xE6B6, 0xBD44, 0,      0,      0,      0xEA6C, 0,
    0xEA6B, 0xEA73, 0xEA6D, 0xEA72, 0xEA6F, 0xBF60, 0xEA71, 0,      0,
    0xBF61, 0,      0xBF62, 0,      0xEA70, 0xEA6E, 0,      0,      0,
    0,      0xC0F8, 0xED74, 0,      0,      0xC0F7, 0xED77, 0xED75, 0xED76,
    0,      0,      0xC0F9, 0,      0,      0,      0xF04D, 0,      0xC2A1,
    0xF04E, 0,      0,      0xC27D, 0xF04F, 0xC27E, 0xF04C, 0xF050, 0,
    0xF04A, 0,      0,      0xC3A7, 0xF278, 0xC3A8, 0xC46F, 0,      0xF04B,
    0xC470, 0,      0,      0,      0xC4EE, 0xF5DF, 0,      0xC57E, 0xF6F4,
    0xC57D, 0,      0xF7EA, 0xC5F5, 0xC5F6, 0,      0,      0xF9CC, 0,
    0,      0xACD1, 0xCFDE, 0,      0xB56E, 0xB56F, 0xA5D5, 0xA6CA, 0xCA47,
    0,      0xCB71, 0xA86D, 0,      0xAABA, 0,      0xACD2, 0xACD3, 0xACD4,
    0xD6A6, 0xD2CB, 0xAF6F, 0,      0,      0xB2AE, 0xD6A5, 0,      0,
    0xDAB8, 0xB571, 0,      0xDAB7, 0xB570, 0,      0,      0xDED5, 0xBD4A,
    0xE6BB, 0xE6B8, 0xE6B9, 0xE6BA, 0,      0,      0,      0,      0,
    0xED78, 0,      0xF051, 0,      0,      0,      0xF471, 0xF470, 0,
    0xF6F5, 0xA5D6, 0xCD75, 0xAF70, 0,      0,      0,      0xB572, 0xDED6,
    0,      0,      0xE2E1, 0,      0xBD4B, 0xEA74, 0,      0xF052, 0xF472,
    0xA5D7, 0,      0,      0xAABB, 0xACD7, 0xCFDF, 0xACD8, 0xACD6, 0,
    0xACD5, 0xD2CC, 0xAF71, 0,      0,      0xAF72, 0xAF73, 0,      0,
    0,      0xB2B0, 0xD6A7, 0xB2AF, 0,      0,      0,      0,      0,
    0xDAB9, 0xB2B1, 0xB573, 0xDED7, 0xB7F8, 0xB7F9, 0,      0xBAC9, 0,
    0xBACA, 0xBD4C, 0xBF64, 0xEA75, 0xBF63, 0,      0xED79, 0xC0FA, 0,
    0xF053, 0xF473, 0xA5D8, 0xA86E, 0xCD78, 0xCD77, 0xAABC, 0xCD76, 0xAABD,
    0xCD79, 0,      0xCFE5, 0xACDB, 0xACDA, 0xCFE7, 0xCFE6, 0xACDF, 0,
    0xACDE, 0,      0,      0xACD9, 0,      0xCFE1, 0xCFE2, 0xCFE3, 0,
    0xACE0, 0xCFE0, 0xACDC, 0xCFE4, 0xACDD, 0,      0,      0,      0,
    0xD2CF, 0xD2D3, 0xD2D1, 0xD2D0, 0,      0xD2D4, 0,      0,      0,
    0xD2D5, 0xD2D6, 0xD2CE, 0,      0xD2CD, 0,      0xAF75, 0xAF76, 0,
    0xD2D7, 0xD2D2, 0,      0xD6B0, 0,      0xD2D8, 0xAF77, 0xAF74, 0,
    0,      0,      0xD6AA, 0,      0xD6A9, 0,      0xD6AB, 0xD6AC, 0xD6AE,
    0xD6AD, 0xD6B2, 0xB2B5, 0xB2B2, 0xB2B6, 0xD6A8, 0xB2B7, 0xD6B1, 0xB2B4,
    0xD6AF, 0xB2B3, 0,      0,      0,      0,      0,      0xDABC, 0xDABE,
    0xDABA, 0xDABB, 0,      0,      0xDABF, 0xDAC1, 0xDAC2, 0xDABD, 0xDAC0,
    0xB574, 0,      0,      0xDEDB, 0,      0xDEE0, 0xDED8, 0xDEDC, 0,
    0,      0xDEE1, 0xDEDD, 0xB7FA, 0xB843, 0,      0xB7FD, 0xDED9, 0xDEDA,
    0xBACE, 0xB846, 0xB7FE, 0,      0xB844, 0xB7FC, 0xDEDF, 0xB845, 0xDEDE,
    0xB841, 0xB7FB, 0xB842, 0xDEE2, 0xE2E6, 0xE2E8, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0xB840, 0,      0,
    0xE2E3, 0xBACC, 0xE2E9, 0xBACD, 0xE2E7, 0xE2E2, 0xE2E5, 0xE2EA, 0xBACB,
    0xE2E4, 0,      0xBD4E, 0xE6BF, 0xE6BE, 0,      0xBD51, 0xBD4F, 0xE6BC,
    0xBD4D, 0xE6BD, 0,      0xBD50, 0,      0,      0,      0xEA7D, 0,
    0xEAA1, 0,      0xEA7E, 0xEA76, 0xEA7A, 0xEA79, 0xEA77, 0xBF66, 0xBF67,
    0xBF65, 0xEA78, 0xEA7B, 0xEA7C, 0,      0xBF68, 0,      0xC140, 0xEDA3,
    0,      0xC0FC, 0xED7B, 0xC0FE, 0xC141, 0,      0,      0xC0FD, 0xEDA2,
    0xED7C, 0xC0FB, 0xEDA1, 0xED7A, 0xED7E, 0xED7D, 0,      0,      0xF055,
    0xC2A4, 0xC2A5, 0xC2A2, 0,      0xC2A3, 0,      0,      0xF054, 0,
    0xF27B, 0,      0,      0xC3A9, 0,      0xF279, 0xF27A, 0,      0xF474,
    0xF477, 0xF475, 0xF476, 0xF5E0, 0,      0,      0xC4EF, 0xF7EB, 0xF8B4,
    0,      0xC5F7, 0xF8F8, 0xF8F9, 0xC666, 0xA5D9, 0xACE1, 0,      0xDAC3,
    0,      0xDEE3, 0,      0xA5DA, 0xA86F, 0,      0xAABE, 0,      0xCFE8,
    0xCFE9, 0xAF78, 0,      0,      0xDAC4, 0xB575, 0xB847, 0xC142, 0xEDA4,
    0xF27C, 0xF478, 0xA5DB, 0,      0,      0,      0xCDA1, 0xCD7A, 0xCD7C,
    0xCD7E, 0xCD7D, 0xCD7B, 0xAABF, 0,      0,      0,      0,      0xACE2,
    0xCFF2, 0,      0xCFED, 0xCFEA, 0,      0,      0xCFF1, 0,      0,
    0xACE4, 0xACE5, 0xCFF0, 0xCFEF, 0xCFEE, 0xCFEB, 0xCFEC, 0xCFF3, 0xACE3,
    0,      0,      0,      0,      0,      0,      0,      0,      0xAF7C,
    0,      0xAFA4, 0xAFA3, 0xD2E1, 0xD2DB, 0xD2D9, 0,      0xAFA1, 0xD6B9,
    0xAF7A, 0xD2DE, 0xD2E2, 0xD2E4, 0xD2E0, 0xD2DA, 0xAFA2, 0xD2DF, 0xD2DD,
    0xAF79, 0xD2E5, 0xAFA5, 0xD2E3, 0xAF7D, 0xD2DC, 0,      0xAF7E, 0xAF7B,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0xB2B9, 0,      0xD6BA, 0,      0,      0xD6B3, 0xD6B5, 0xD6B7,
    0,      0xD6B8, 0xD6B6, 0xB2BA, 0,      0xD6BB, 0,      0xD6B4, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0xDAC8,
    0xB576, 0xDAD0, 0,      0xDAC5, 0,      0xDAD1, 0,      0xDAC6, 0xDAC7,
    0,      0,      0xDACF, 0xDACE, 0xDACB, 0xB2B8, 0xB577, 0xDAC9, 0xDACC,
    0xB578, 0xDACD, 0xDACA, 0,      0,      0,      0,      0,      0,
    0,      0xDEEE, 0,      0xDEF2, 0xB84E, 0,      0xE2F0, 0xB851, 0xDEF0,
    0xF9D6, 0,      0xDEED, 0xDEE8, 0xDEEA, 0xDEEB, 0xDEE4, 0,      0xB84D,
    0,      0,      0xB84C, 0,      0xB848, 0xDEE7, 0,      0xB84F, 0,
    0xB850, 0xDEE6, 0xDEE9, 0xDEF1, 0xB84A, 0xB84B, 0xDEEF, 0xDEE5, 0,
    0,      0,      0xE2F2, 0xBAD0, 0xE2F4, 0xDEEC, 0xE2F6, 0xBAD4, 0xE2F7,
    0xE2F3, 0,      0xBAD1, 0xE2EF, 0xBAD3, 0xE2EC, 0xE2F1, 0xE2F5, 0xE2EE,
    0,      0,      0xB849, 0,      0xE2EB, 0xBAD2, 0xE2ED, 0,      0,
    0,      0,      0,      0xBD54, 0xE6C1, 0xBD58, 0,      0xBD56, 0,
    0,      0xBACF, 0,      0xE6C8, 0xE6C9, 0xBD53, 0,      0,      0xE6C7,
    0xE6CA, 0xBD55, 0xBD52, 0xE6C3, 0xE6C0, 0xE6C5, 0xE6C2, 0xBD59, 0xE6C4,
    0,      0,      0xE6C6, 0xBD57, 0,      0,      0,      0,      0xBF6A,
    0xEAA8, 0,      0xEAA2, 0xEAA6, 0xEAAC, 0xEAAD, 0xEAA9, 0xEAAA, 0xEAA7,
    0,      0xEAA4, 0,      0xBF6C, 0xBF69, 0xEAA3, 0xEAA5, 0,      0xBF6B,
    0xEAAB, 0,      0xC146, 0,      0,      0xEDAA, 0xEDA5, 0xC145, 0,
    0,      0xC143, 0,      0xEDAC, 0xC144, 0xEDA8, 0xEDA9, 0xEDA6, 0xEDAD,
    0xF056, 0,      0xC147, 0xEDA7, 0,      0xEDAE, 0xEDAB, 0,      0,
    0,      0xF05A, 0,      0,      0xF057, 0,      0xC2A6, 0,      0xF05B,
    0xF05D, 0xF05C, 0xF058, 0xF059, 0,      0,      0xF2A3, 0,      0xC3AA,
    0,      0xF27E, 0xF2A2, 0xF27D, 0xF2A4, 0,      0,      0xF2A1, 0,
    0xF47A, 0xF47D, 0xF479, 0xC471, 0xF47B, 0xF47C, 0xF47E, 0xC472, 0xC474,
    0xC473, 0xF5E1, 0,      0xF5E3, 0,      0xF5E2, 0,      0,      0,
    0xF6F6, 0,      0,      0xF8B5, 0xF8FA, 0xA5DC, 0,      0,      0xCB72,
    0xAAC0, 0xCDA3, 0xAAC1, 0xAAC2, 0xCDA2, 0,      0xCFF8, 0xCFF7, 0xACE6,
    0xACE9, 0xACE8, 0xACE7, 0xCFF4, 0xCFF6, 0xCFF5, 0,      0,      0xD2E8,
    0xAFA7, 0xD2EC, 0xD2EB, 0xD2EA, 0xD2E6, 0xAFA6, 0xAFAA, 0xAFAD, 0,
    0,      0xAFAE, 0xD2E7, 0xD2E9, 0xAFAC, 0xAFAB, 0xAFA9, 0xAFA8, 0xD6C2,
    0,      0xD6C0, 0xD6BC, 0xB2BB, 0,      0xD6BD, 0xB2BC, 0xD6BE, 0xD6BF,
    0xD6C1, 0,      0xB2BD, 0,      0,      0xDAD5, 0,      0xDAD4, 0xDAD3,
    0xDAD2, 0,      0,      0,      0,      0xDEF6, 0xB852, 0,      0xDEF3,
    0xDEF5, 0,      0xB853, 0,      0xB854, 0xDEF4, 0,      0,      0,
    0,      0,      0xE341, 0,      0xE2F9, 0xE2FA, 0,      0xBAD7, 0xBAD5,
    0xBAD6, 0xE343, 0,      0xE342, 0xE2FE, 0xE2FD, 0xE2FC, 0xE2FB, 0xE340,
    0xE2F8, 0,      0xE6CB, 0xE6D0, 0xE6CE, 0,      0,      0,      0xE6CD,
    0xE6CC, 0xE6CF, 0,      0xEAAE, 0,      0xBF6D, 0xC148, 0xEDB0, 0,
    0xC149, 0xEDAF, 0xF05F, 0xF05E, 0xC2A7, 0,      0xF2A5, 0xC3AB, 0xF4A1,
    0xC5A1, 0xF6F7, 0,      0xF8B7, 0xF8B6, 0xC9A8, 0xACEA, 0xACEB, 0xD6C3,
    0,      0xB856, 0xA5DD, 0xA872, 0xA871, 0xA870, 0,      0,      0,
    0xCDA4, 0,      0,      0xAAC4, 0xAAC3, 0,      0xACEE, 0,      0xCFFA,
    0xCFFD, 0xCFFB, 0,      0xACEC, 0xACED, 0,      0,      0xCFF9, 0xCFFC,
    0,      0xAFB5, 0,      0,      0,      0xD2F3, 0xD2F5, 0xD2F4, 0xAFB2,
    0xD2EF, 0,      0,      0xAFB0, 0xAFAF, 0,      0xAFB3, 0xAFB1, 0,
    0xAFB4, 0xD2F2, 0xD2ED, 0xD2EE, 0xD2F1, 0xD2F0, 0,      0,      0,
    0,      0,      0,      0,      0xD6C6, 0xD6C7, 0xD6C5, 0,      0xD6C4,
    0xB2BE, 0,      0,      0,      0,      0xB57D, 0,      0xDAD6, 0xDAD8,
    0xDADA, 0xB57C, 0,      0,      0xB57A, 0,      0xDAD7, 0xB57B, 0xDAD9,
    0xB579, 0,      0,      0xDF41, 0xDEF7, 0xDEFA, 0xDEFE, 0xB85A, 0xDEFC,
    0,      0xDEFB, 0xDEF8, 0xDEF9, 0xB858, 0xDF40, 0xB857, 0,      0xB85C,
    0xB85B, 0xB859, 0,      0xDEFD, 0,      0,      0,      0xE349, 0,
    0xE348, 0,      0,      0xE344, 0,      0,      0xBAD8, 0xE347, 0xE346,
    0xBAD9, 0,      0,      0,      0,      0,      0xBD5E, 0,      0xE6D2,
    0,      0xBD5F, 0xBD5B, 0xBD5D, 0,      0xBD5A, 0xBD5C, 0,      0,
    0,      0xEAAF, 0,      0xBF70, 0xEAB1, 0xEAB0, 0,      0xE345, 0xBF72,
    0xBF71, 0xBF6E, 0xBF6F, 0,      0,      0,      0,      0,      0xEDB5,
    0,      0xEDB3, 0xC14A, 0xEDB4, 0,      0xEDB6, 0xEDB2, 0xEDB1, 0,
    0,      0xF060, 0xC2AA, 0xC2A8, 0xC2A9, 0,      0,      0,      0,
    0xF2A6, 0xF2A7, 0xC3AD, 0,      0xC3AC, 0xF4A3, 0xF4A4, 0xF4A2, 0,
    0xF6F8, 0xF6F9, 0,      0,      0xA5DE, 0xCA48, 0xA873, 0,      0xCDA5,
    0xAAC6, 0xAAC5, 0xCDA6, 0,      0,      0xD040, 0xACEF, 0xCFFE, 0xACF0,
    0,      0,      0xAFB6, 0xD2F8, 0xD2F6, 0xD2FC, 0xAFB7, 0xD2F7, 0xD2FB,
    0xD2F9, 0xD2FA, 0,      0,      0xD6C8, 0xD6CA, 0,      0xB2BF, 0,
    0xD6C9, 0xB2C0, 0xB5A2, 0xB5A1, 0xB57E, 0xDADB, 0,      0,      0,
    0,      0xDF44, 0xB85D, 0xB85E, 0,      0xDF43, 0xDF42, 0,      0,
    0,      0,      0xE34A, 0xBADB, 0xBADA, 0xE34B, 0xE34C, 0,      0xBD61,
    0xBD60, 0,      0xEAB5, 0xE6D3, 0xE6D5, 0xE6D4, 0xEAB4, 0xEAB2, 0xEAB6,
    0xEAB3, 0,      0xBF73, 0,      0,      0,      0xEDB7, 0xC14B, 0xEDB8,
    0xEDB9, 0,      0,      0xC2AB, 0xC2AC, 0,      0xC475, 0,      0,
    0xC5D1, 0xA5DF, 0,      0,      0,      0,      0,      0xD041, 0,
    0,      0,      0,      0,      0,      0xD2FD, 0xAFB8, 0,      0,
    0,      0,      0,      0xB3BA, 0xB3B9, 0,      0,      0xB5A4, 0xDADD,
    0xB5A3, 0xDADC, 0,      0,      0,      0,      0xDF45, 0,      0xBADC,
    0xE34D, 0xBADD, 0,      0,      0,      0,      0,      0,      0xC476,
    0xF4A5, 0,      0xA6CB, 0xAAC7, 0xCDA7, 0,      0xACF2, 0,      0xACF1,
    0xD042, 0xD043, 0,      0,      0xD340, 0xD342, 0xAFB9, 0,      0xD344,
    0xD347, 0xD345, 0,      0,      0,      0xD346, 0xD343, 0xD2FE, 0xAFBA,
    0xD348, 0xD341, 0,      0,      0,      0,      0xD6D3, 0xB2C6, 0xD6DC,
    0xB2C3, 0,      0xD6D5, 0xB2C7, 0,      0xB2C1, 0,      0xD6D0, 0xD6DD,
    0xD6D1, 0xD6CE, 0xB2C5, 0,      0xB2C2, 0,      0xD6D4, 0xD6D7, 0xB2C4,
    0xD6D8, 0xB2C8, 0xD6D9, 0xD6CF, 0xD6D6, 0xD6DA, 0xD6D2, 0xD6CD, 0xD6CB,
    0,      0,      0xD6DB, 0,      0,      0xDADF, 0,      0,      0,
    0,      0xDAE4, 0,      0,      0,      0xDAE0, 0xDAE6, 0xB5A7, 0xD6CC,
    0xDAE1, 0xB5A5, 0xDADE, 0xB5AC, 0xDAE2, 0xB5AB, 0xDAE3, 0xB5AD, 0xB5A8,
    0xB5AE, 0xB5A9, 0,      0xB5AA, 0,      0xB5A6, 0,      0xDAE5, 0,
    0,      0,      0,      0,      0,      0,      0xB861, 0xDF50, 0,
    0xDF53, 0xDF47, 0xDF4C, 0xDF46, 0xB863, 0,      0xDF4A, 0,      0,
    0,      0xDF48, 0xB862, 0,      0xDF4F, 0xDF4E, 0xDF4B, 0xDF4D, 0xDF49,
    0xBAE1, 0xDF52, 0xB85F, 0xDF51, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0xE35D, 0,      0xBAE8, 0xE358, 0,
    0xBAE7, 0xE34E, 0,      0xE350, 0xBAE0, 0xE355, 0xE354, 0xE357, 0xBAE5,
    0xE352, 0xE351, 0,      0,      0xBAE4, 0xBADF, 0xE353, 0xBAE2, 0xE359,
    0xE35B, 0,      0xE356, 0xE34F, 0xBAE3, 0,      0,      0xBD69, 0xBADE,
    0,      0,      0xE35C, 0,      0,      0,      0,      0,      0,
    0,      0xE6D9, 0xBD62, 0,      0xE6DB, 0,      0xBD63, 0,      0,
    0xBD65, 0xE6DE, 0,      0xE6D6, 0xBAE6, 0xE6DC, 0,      0,      0,
    0,      0xE6D8, 0,      0xB860, 0xBD68, 0,      0,      0xBD64, 0,
    0xBD66, 0xBD67, 0,      0xBF76, 0xE6DD, 0xE6D7, 0xBD6A, 0,      0xE6DA,
    0,      0,      0,      0,      0,      0xEAC0, 0xEABB, 0,      0,
    0xEAC5, 0xBF74, 0xEABD, 0xBF78, 0xEAC3, 0xEABA, 0xEAB7, 0xEAC6, 0xC151,
    0xBF79, 0xEAC2, 0xEAB8, 0xBF77, 0xEABC, 0xBF7B, 0xEAB9, 0xEABE, 0xBF7A,
    0xEAC1, 0xEAC4, 0,      0,      0,      0,      0xEDCB, 0xEDCC, 0xEDBC,
    0xEDC3, 0xEDC1, 0,      0,      0xC14F, 0xEDC8, 0xEABF, 0,      0xEDBF,
    0,      0xEDC9, 0xC14E, 0xEDBE, 0xEDBD, 0xEDC7, 0xEDC4, 0xEDC6, 0,
    0xEDBA, 0xEDCA, 0xC14C, 0,      0xEDC5, 0xEDCE, 0xEDC2, 0xC150, 0xC14D,
    0xEDC0, 0xEDBB, 0xEDCD, 0xBF75, 0,      0,      0,      0,      0,
    0,      0,      0xF063, 0,      0,      0xF061, 0xF067, 0xC2B0, 0xF065,
    0xF064, 0xC2B2, 0xF06A, 0xC2B1, 0,      0xF06B, 0xF068, 0xC2AE, 0xF069,
    0xF062, 0xC2AF, 0xC2AD, 0xF2AB, 0xF066, 0,      0,      0xF06C, 0,
    0,      0xF2A8, 0,      0,      0,      0xC3B2, 0xC3B0, 0xF2AA, 0,
    0xF2AC, 0xF2A9, 0xC3B1, 0xC3AE, 0xC3AF, 0xC3B3, 0,      0,      0xC478,
    0,      0xF4AA, 0,      0xF4A9, 0xF4A7, 0xF4A6, 0xF4A8, 0,      0xC477,
    0xC479, 0,      0,      0xC4F0, 0,      0,      0xF5E5, 0xF5E4, 0,
    0,      0xF6FA, 0,      0xF6FC, 0xF6FE, 0xF6FD, 0xF6FB, 0,      0,
    0xC5A3, 0xC5A2, 0,      0,      0xC5D3, 0xC5D2, 0xC5D4, 0xF7ED, 0xF7EC,
    0,      0xF8FB, 0xF8B8, 0xF8FC, 0xC658, 0,      0xC659, 0xF96D, 0,
    0,      0xC67E, 0xA6CC, 0,      0xCDA8, 0,      0,      0xD045, 0xD046,
    0xD044, 0,      0,      0xACF3, 0,      0xD047, 0xD048, 0xD049, 0,
    0,      0xD349, 0xD34F, 0,      0,      0xD34D, 0xAFBB, 0xD34B, 0,
    0xD34C, 0xD34E, 0,      0,      0,      0xD34A, 0xB2C9, 0,      0xD6DE,
    0xB2CB, 0xD6E0, 0xB2CA, 0xD6DF, 0,      0,      0,      0,      0,
    0xDAE8, 0xB5AF, 0,      0xDAEA, 0xDAE7, 0xD6E1, 0,      0xB5B0, 0,
    0xF9DB, 0xDAE9, 0,      0,      0,      0,      0,      0,      0xDF56,
    0,      0xB864, 0xDF54, 0xB865, 0xDF55, 0xB866, 0,      0,      0,
    0xBAE9, 0xE361, 0xE35E, 0xE360, 0xBAEA, 0xBAEB, 0xE35F, 0,      0,
    0,      0,      0,      0xE6DF, 0,      0,      0xE6E0, 0,      0xBD6B,
    0xE6E2, 0xE6E1, 0,      0xA261, 0,      0xEACA, 0xEACB, 0xEAC7, 0,
    0xEAC8, 0xBF7C, 0xBF7D, 0xEAC9, 0,      0xC157, 0,      0,      0xC153,
    0xC158, 0xC154, 0xC156, 0xC152, 0,      0xC155, 0,      0,      0,
    0,      0xC2B3, 0xEDCF, 0,      0xF2AE, 0,      0xF2AD, 0,      0xF4AB,
    0xC47A, 0xC47B, 0xF741, 0xF5E6, 0,      0xF740, 0,      0xF8FD, 0xF9A4,
    0xA6CD, 0,      0,      0xA874, 0,      0xCDA9, 0xAAC8, 0,      0xACF6,
    0xD04C, 0xACF4, 0xD04A, 0xACF9, 0xACF5, 0xACFA, 0xACF8, 0xD04B, 0xACF7,
    0xAFBF, 0xAFBE, 0xD35A, 0xAFC7, 0xD353, 0xD359, 0xAFC3, 0xD352, 0xD358,
    0xD356, 0xAFC2, 0xAFC4, 0xD355, 0xAFBD, 0xD354, 0xAFC8, 0xAFC5, 0xAFC9,
    0xAFC6, 0xD351, 0xD350, 0xD357, 0xAFC0, 0xAFBC, 0xAFC1, 0,      0,
    0,      0,      0,      0xD6F0, 0xD6E9, 0,      0xB5B5, 0xD6E8, 0,
    0xB2CF, 0xB2D6, 0xB2D3, 0xB2D9, 0xB2D8, 0xB2D4, 0,      0xD6E2, 0xD6E5,
    0,      0xD6E4, 0xB2D0, 0xD6E6, 0xD6EF, 0xB2D1, 0xD6E3, 0xD6EC, 0xD6ED,
    0xB2D2, 0xD6EA, 0xB2D7, 0xB2CD, 0xB2D5, 0xD6E7, 0xB2CC, 0xD6EB, 0,
    0,      0xD6EE, 0,      0,      0,      0xDAFB, 0xDAF2, 0xB5B2, 0xDAF9,
    0xDAF6, 0xDAEE, 0xDAF7, 0xB5B4, 0xDAEF, 0,      0xDAEB, 0,      0,
    0xB86C, 0xDAF4, 0,      0xB5B1, 0xDAFA, 0,      0xB5B8, 0xB5BA, 0xDAED,
    0,      0,      0xB5B9, 0xDAF0, 0xB5B3, 0xDAF8, 0xDAF1, 0xDAF5, 0,
    0xDAF3, 0xB5B6, 0xDAEC, 0xB5BB, 0xB2CE, 0xB5B7, 0xB5BC, 0,      0,
    0,      0,      0,      0xB868, 0xDF5D, 0xDF5F, 0xDF61, 0xDF65, 0,
    0xDF5B, 0xDF59, 0xB86A, 0,      0xDF60, 0xDF64, 0xDF5C, 0xDF58, 0,
    0xDF57, 0,      0,      0,      0xDF62, 0xDF5A, 0xDF5E, 0xB86B, 0,
    0xB869, 0xDF66, 0xB867, 0xDF63, 0,      0xE372, 0,      0,      0,
    0,      0,      0xBAEE, 0xE36A, 0xBD78, 0xE374, 0xBAF1, 0xE378, 0xBAF7,
    0xE365, 0,      0,      0xE375, 0xE362, 0,      0xE377, 0xE366, 0,
    0xBAFE, 0xBAFB, 0xE376, 0xE370, 0xBAED, 0xBAF5, 0xBAF4, 0,      0xBAF3,
    0xBAF9, 0,      0xE363, 0xBAFA, 0xE371, 0xBAF6, 0xBAEC, 0xE373, 0xBAEF,
    0xBAF0, 0xBAF8, 0xE368, 0xE367, 0xE364, 0,      0xE36C, 0xE369, 0xE36D,
    0xBAFD, 0,      0xE379, 0xBAF2, 0xE36E, 0xE36F, 0,      0xE36B, 0,
    0,      0,      0xBAFC, 0,      0,      0,      0,      0xE6E7, 0xBD70,
    0xBD79, 0xBD75, 0xE6E4, 0,      0xBD72, 0xBD76, 0xE6F0, 0xBD6C, 0xE6E8,
    0,      0xBD74, 0,      0,      0xE6EB, 0xE6E6, 0xBD73, 0xBD77, 0xE6E5,
    0,      0xBD71, 0,      0xE6EF, 0xBD6E, 0xE6EE, 0xE6ED, 0xBD7A, 0xE572,
    0xBD6D, 0,      0xE6EC, 0xE6E3, 0,      0xBD7B, 0xE6EA, 0xBD6F, 0,
    0,      0,      0,      0,      0,      0,      0xE6E9, 0,      0,
    0,      0,      0xBFA2, 0xBFA7, 0xBF7E, 0xEAD8, 0xEACF, 0xEADB, 0xEAD3,
    0xEAD9, 0xBFA8, 0xBFA1, 0xEACC, 0xEAD2, 0xEADC, 0xEAD5, 0xEADA, 0xEACE,
    0,      0,      0xEAD6, 0xBFA3, 0xEAD4, 0xBFA6, 0xBFA5, 0xEAD0, 0xEAD1,
    0xEACD, 0xEAD7, 0xBFA4, 0xEADE, 0xEADD, 0,      0,      0,      0xEDDA,
    0xEDD6, 0xC15F, 0,      0xEDD0, 0xC159, 0xC169, 0xEDDC, 0xC161, 0xC15D,
    0xEDD3, 0xC164, 0xC167, 0xEDDE, 0xC15C, 0xEDD5, 0xC165, 0xEDE0, 0xEDDD,
    0xEDD1, 0xC160, 0xC15A, 0xC168, 0xEDD8, 0xC163, 0xEDD2, 0xC15E, 0xEDDF,
    0xC162, 0xC15B, 0xEDD9, 0xC166, 0xEDD7, 0,      0,      0xEDDB, 0,
    0,      0,      0xF06E, 0xF074, 0xC2B9, 0xF077, 0xC2B4, 0xC2B5, 0xF06F,
    0xF076, 0xF071, 0xC2BA, 0xC2B7, 0,      0xF06D, 0,      0xC2B6, 0xF073,
    0xF075, 0xC2B8, 0xF072, 0xF070, 0,      0,      0,      0,      0xF2B8,
    0xC3B7, 0xC3B8, 0xC3B4, 0,      0xC3B5, 0,      0xF2B4, 0xF2B2, 0,
    0xF2B6, 0xC3BA, 0xF2B7, 0xF2B0, 0xF2AF, 0xF2B3, 0xF2B1, 0xC3B6, 0xF2B5,
    0xF4AC, 0xC47E, 0xC47D, 0xF4AD, 0,      0xF4AF, 0xF4AE, 0xC4A1, 0,
    0,      0,      0xF5EB, 0xF5E8, 0xF5E9, 0,      0xF5E7, 0xF5EA, 0xC4F2,
    0xF5EC, 0,      0xC4F1, 0,      0xF742, 0,      0xC5D5, 0xC5D7, 0xF7EE,
    0xC5D6, 0xF8B9, 0xF940, 0xF942, 0xF8FE, 0xF941, 0xC66C, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0xA6CE, 0,
    0xACFB, 0xD26F, 0xAFCA, 0,      0,      0xB2DA, 0xDAFC, 0xDAFD, 0,
    0,      0,      0xEADF, 0xC16A, 0xEDE1, 0,      0,      0xC2BB, 0,
    0xF2BA, 0xF2B9, 0xC4A2, 0xF5ED, 0,      0xF743, 0xC5F8, 0xCA49, 0,
    0,      0xAAC9, 0xA875, 0,      0,      0xD04D, 0,      0,      0xD360,
    0xD35B, 0xD35F, 0xD35D, 0xAFCB, 0xD35E, 0xD35C, 0,      0xD6F1, 0,
    0xDAFE, 0xDB40, 0xDF69, 0xDF6A, 0xB86E, 0xB86F, 0xDF68, 0xDF6B, 0xDF67,
    0xB86D, 0,      0xBB40, 0,      0xB870, 0xE37A, 0,      0xBD7C, 0xE6F1,
    0xBD7D, 0,      0xBFA9, 0xEAE2, 0xEAE0, 0xEAE1, 0xEDE4, 0xEDE3, 0xEDE2,
    0,      0,      0,      0xF2BB, 0,      0xC3B9, 0xF2BC, 0xF744, 0xC5F9,
    0xF8BA, 0xA6CF, 0xAACB, 0xAACA, 0xD04F, 0xACFC, 0,      0,      0xD04E,
    0xD362, 0,      0xAFCC, 0xD6F2, 0xD361, 0,      0,      0,      0xB2DC,
    0xD6F5, 0xD6F3, 0xD6F4, 0xB2DB, 0,      0xDB42, 0xDB43, 0xDB41, 0,
    0xB873, 0xDF6D, 0xDF6C, 0xDF6E, 0xB872, 0xB871, 0,      0,      0xE6F2,
    0xE6F4, 0,      0xBD7E, 0xE6F3, 0xEAE3, 0xBFAA, 0xF079, 0,      0xF078,
    0xC3BB, 0xF2BD, 0xC3BD, 0xC3BC, 0xF4B0, 0xF5EE, 0xC4F3, 0xA6D0, 0xD050,
    0xACFD, 0xD365, 0xAFCE, 0xD364, 0xD363, 0,      0xAFCD, 0,      0xD6FB,
    0,      0xD6FD, 0xD6F6, 0xD6F7, 0xB2DD, 0xD6F8, 0xB2DE, 0xD6FC, 0xD6F9,
    0xD6FA, 0xB2DF, 0,      0xB5BE, 0xB5BF, 0,      0xDB44, 0,      0,
    0,      0xDF6F, 0xDF70, 0,      0xE37E, 0xBB43, 0xBB41, 0xBB42, 0xE37B,
    0xE37C, 0,      0xE37D, 0xE6F9, 0,      0xE6FA, 0xBDA1, 0xE6F7, 0xE6F6,
    0xE6F8, 0xE6F5, 0xBFAD, 0xEAE4, 0xBFAB, 0xBFAC, 0xEDE6, 0xC16B, 0xEDE5,
    0xEFA8, 0,      0xF07A, 0xF07B, 0xC2BC, 0,      0xC2BD, 0xC16C, 0xF2BE,
    0xF2BF, 0xF4B1, 0xC4A3, 0xA6D1, 0,      0xA6D2, 0xACFE, 0xAACC, 0xAFCF,
    0xD051, 0,      0,      0,      0xB5C0, 0xA6D3, 0xAD41, 0xD052, 0xD053,
    0xAD40, 0xAD42, 0xA6D4, 0,      0xD054, 0xAFD1, 0xD366, 0xAFD3, 0xAFD0,
    0xAFD2, 0,      0xD741, 0xB2E0, 0,      0xD740, 0xD6FE, 0,      0xDF71,
    0,      0,      0xE3A1, 0,      0xBDA2, 0,      0xBFAE, 0xEAE6, 0xEAE5,
    0,      0xEDE7, 0,      0,      0,      0xF5EF, 0,      0,      0xA6D5,
    0xCB73, 0xCDAA, 0xAD43, 0xD055, 0,      0xD368, 0,      0,      0,
    0xAFD4, 0xD367, 0xAFD5, 0,      0,      0,      0xD743, 0,      0,
    0xB2E2, 0xD742, 0xD744, 0,      0xB2E1, 0,      0,      0,      0,
    0xDB46, 0xDB47, 0xDB45, 0xB5C1, 0,      0,      0,      0xB874, 0,
    0xB875, 0,      0xBB45, 0,      0xE3A3, 0xE3A2, 0xBB44, 0,      0,
    0,      0,      0,      0xE6FB, 0,      0,      0xE6FC, 0,      0,
    0,      0,      0xEAE7, 0,      0,      0xC170, 0xC16F, 0xC16D, 0xC16E,
    0xC171, 0,      0xF07C, 0xC2BF, 0xC2BE, 0xF2C0, 0xF4B2, 0,      0,
    0,      0xC5A5, 0xC5A4, 0xA6D6, 0,      0,      0xD1FB, 0,      0xB877,
    0xB5C2, 0xB876, 0xBB46, 0,      0xA6D7, 0xC9A9, 0xA6D8, 0xA6D9, 0,
    0,      0xCDAB, 0xCB76, 0,      0xCB77, 0xA877, 0,      0xCB74, 0xA876,
    0,      0xA879, 0xCB75, 0xA87B, 0xA87A, 0xCB78, 0xA878, 0,      0,
    0,      0xAAD1, 0xAACF, 0xCDAD, 0,      0xAACE, 0,      0,      0,
    0xAAD3, 0xAAD5, 0xAAD2, 0,      0xCDB0, 0xCDAC, 0xAAD6, 0,      0xAAD0,
    0xA87C, 0,      0xAAD4, 0xCDAF, 0,      0,      0xCDAE, 0,      0xAACD,
    0,      0,      0,      0,      0,      0,      0,      0xD05B, 0xAD47,
    0xAD48, 0xD05D, 0,      0xD057, 0xD05A, 0xD063, 0xD061, 0,      0xAD49,
    0xD067, 0xAD4C, 0xD064, 0xD05C, 0xD059, 0,      0,      0xDB49, 0xD062,
    0xAD44, 0xD065, 0xD056, 0xD05F, 0xAD46, 0xAD4B, 0xD060, 0xAD4F, 0xAD4D,
    0,      0xD058, 0xAD4A, 0,      0xD05E, 0xAD4E, 0xAD45, 0xD066, 0,
    0,      0,      0,      0,      0,      0xAFDA, 0,      0xAFE3, 0xAFD8,
    0xAFD6, 0xD36A, 0xAFDE, 0xAFDB, 0xD36C, 0,      0,      0xAFDD, 0xD36B,
    0xD369, 0xD36E, 0xAFE2, 0xAFE0, 0xDB48, 0,      0xD36F, 0xD36D, 0xAFD7,
    0,      0,      0xAFD9, 0xAFDC, 0,      0xAFDF, 0,      0xAFE1, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0xD74E, 0xB2E4, 0,      0xD745, 0xD747, 0,      0xD748, 0,      0xD750,
    0xD74C, 0xD74A, 0,      0xD74D, 0xD751, 0xB2E5, 0xB2E9, 0xD746, 0,
    0xD74F, 0,      0xB2E7, 0,      0xB2E6, 0xD74B, 0xD749, 0,      0xB2E3,
    0xB2E8, 0,      0,      0,      0,      0,      0,      0,      0,
    0xB5C8, 0xDB51, 0,      0,      0xDB4F, 0xB5CA, 0,      0,      0,
    0,      0xDB4A, 0xDFA1, 0,      0xB5C9, 0xDB4E, 0,      0,      0xDB4B,
    0xB5C5, 0xB5CB, 0xDB50, 0xB5C7, 0xDB4D, 0xBB47, 0xB5C6, 0xDB4C, 0xB5CC,
    0xB5C4, 0xB5C3, 0,      0,      0,      0,      0,      0xDF77, 0xDF75,
    0,      0xDF7B, 0,      0xDF73, 0xDFA2, 0xDF78, 0,      0xDF72, 0xB87B,
    0xB8A3, 0xDF7D, 0,      0xDF76, 0,      0xB87E, 0,      0,      0xB87C,
    0xDF7E, 0xB879, 0xB878, 0xDF79, 0xB87D, 0xB5CD, 0,      0xDF7C, 0xDF74,
    0xB87A, 0xB8A1, 0xB8A2, 0,      0,      0,      0,      0xBB4C, 0xBB48,
    0,      0xBB4D, 0xE3A6, 0,      0,      0xE3A5, 0xE3A7, 0xBB4A, 0xE3A4,
    0xBB4B, 0xE3AA, 0xE3A9, 0xE3A8, 0,      0xBB49, 0,      0,      0,
    0,      0,      0xE741, 0,      0xE744, 0xBDA8, 0xE743, 0xBDA7, 0xBDA3,
    0xBDA4, 0xBDA5, 0xE740, 0xE6FE, 0xBDA6, 0,      0xE742, 0xE6FD, 0,
    0,      0xEAE9, 0xEAF3, 0xBFB1, 0xBFB0, 0,      0xEAED, 0xEAEF, 0,
    0xEAEA, 0,      0xEAEE, 0xEAE8, 0xEAF1, 0xBFAF, 0xEAF0, 0xEAEC, 0,
    0xEAF2, 0,      0xEAEB, 0xC174, 0xEDE8, 0xEDEE, 0xC178, 0xC17A, 0xC177,
    0xC176, 0,      0xC175, 0xC173, 0xEDE9, 0xEDEC, 0xC172, 0xEDED, 0,
    0xC179, 0xEDEB, 0,      0xEDEA, 0xC2C0, 0,      0xC2C1, 0xF0A1, 0xF07D,
    0xF07E, 0,      0,      0xF2C2, 0,      0xF2C1, 0xC3BE, 0xF4B4, 0xC4A4,
    0xF4B3, 0,      0xF5F0, 0xF745, 0xC5A6, 0xF943, 0xF944, 0xC5D8, 0xA6DA,
    0,      0xAAD7, 0xDB52, 0xBB4E, 0xC17B, 0xEDEF, 0xA6DB, 0,      0xAFE5,
    0xAFE4, 0xDB53, 0,      0,      0,      0xEAF4, 0xA6DC, 0xAD50, 0,
    0,      0xDB54, 0xDB55, 0xDB56, 0xBB4F, 0xBFB2, 0xA6DD, 0,      0xAAD8,
    0xD068, 0xAFE6, 0xD370, 0xB2EA, 0,      0xDB57, 0xB8A4, 0,      0xBB50,
    0xBFB3, 0xC17C, 0xC2C2, 0xF4B5, 0xA6DE, 0xAAD9, 0,      0,      0xAFE7,
    0xD752, 0xB5CE, 0,      0xBB51, 0xE3AB, 0xE745, 0,      0,      0,
    0,      0xA6DF, 0xB5CF, 0xDFA3, 0xBB52, 0xA6E0, 0xCDB1, 0xD069, 0xAD51,
    0,      0,      0xD372, 0,      0,      0xAFEA, 0,      0xAFE8, 0xAFE9,
    0xAFEB, 0,      0,      0xD371, 0,      0,      0xD757, 0xD754, 0xD756,
    0xB2EB, 0xB2ED, 0xB2EC, 0xD753, 0xB2EE, 0xD755, 0,      0xDB58, 0xDB59,
    0,      0xDB5A, 0xDFA6, 0,      0xDFA7, 0,      0xDFA5, 0xDFA8, 0,
    0xB8A5, 0,      0xDFA4, 0,      0xBB53, 0,      0,      0xE74A, 0xE746,
    0xE749, 0xE74B, 0xE748, 0xE747, 0,      0xEAF5, 0xEAF6, 0xEAF7, 0xBFB4,
    0xBFB5, 0xEDF1, 0xEDF0, 0xEDF2, 0,      0xF0A3, 0xF0A2, 0,      0xF2C4,
    0,      0xF2C5, 0xF2C3, 0,      0xC4A5, 0,      0xF4B6, 0xF4B7, 0,
    0xF746, 0xF7EF, 0xF8BB, 0xA6E1, 0xA87D, 0,      0xC17D, 0xA6E2, 0,
    0xD758, 0xDB5B, 0,      0xC641, 0xCA4A, 0,      0,      0,      0xCA4B,
    0xCA4D, 0xA6E3, 0xCA4E, 0xCA4C, 0,      0,      0xCBA2, 0xCBA3, 0xCB7B,
    0,      0,      0,      0,      0xCBA1, 0xA8A1, 0,      0xA8A2, 0xCB7C,
    0xCB7A, 0xCB79, 0xCB7D, 0xA87E, 0xCB7E, 0xD06A, 0,      0,      0,
    0xCDB6, 0xAADC, 0xCDB5, 0xCDB7, 0,      0xAADB, 0xCDBC, 0xAADF, 0xCDB2,
    0xCDC0, 0xCDC6, 0xAAE6, 0xCDC3, 0xAAE3, 0,      0xCDB9, 0xCDBF, 0xCDC1,
    0,      0xCDB4, 0xAAE2, 0xAADD, 0xCDBA, 0xAAE4, 0xAAE7, 0xAAE1, 0,
    0xAADA, 0xCDBE, 0xCDB8, 0xCDC5, 0xAAE9, 0xAAE5, 0xAAE0, 0xCDBD, 0xAFEC,
    0xCDBB, 0xAADE, 0xAAE8, 0,      0xCDB3, 0,      0xCDC2, 0xCDC4, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xAD62, 0xAD5C, 0xAD64, 0xAD61, 0xD071, 0xD074,
    0xAD5D, 0,      0xD06B, 0,      0xAD56, 0xAD60, 0,      0xAD63, 0xAD65,
    0xD0A2, 0xD077, 0,      0xAD55, 0xD0A1, 0xAD59, 0xAD57, 0xAD52, 0xD06F,
    0,      0xD07E, 0xD073, 0xD076, 0xD0A5, 0,      0xAD66, 0xD07D, 0xAD5E,
    0xD078, 0xD0A4, 0xD075, 0xD079, 0xD07C, 0,      0,      0xD06D, 0xD0A3,
    0xD07B, 0,      0,      0xD06C, 0,      0xD070, 0xAD5F, 0xAD5A, 0xAD53,
    0xAD58, 0xAD54, 0xAD67, 0xD06E, 0xD3A5, 0xAD5B, 0,      0,      0xD07A,
    0xCE41, 0,      0,      0,      0,      0,      0,      0,      0,
    0xD3A8, 0xAFFA, 0,      0xD376, 0,      0xD3A3, 0xD37D, 0,      0xD3B2,
    0,      0xD3AA, 0,      0xD37E, 0,      0xD3A9, 0xD378, 0xD37C, 0xD3B5,
    0xAFFD, 0xD3AD, 0xD3A4, 0xAFED, 0xD3B3, 0xD374, 0,      0xD3AC, 0,
    0xAFFC, 0xAFF7, 0xD373, 0xAFF5, 0xAFF4, 0xAFF9, 0xD3AB, 0xAFF1, 0xAFF8,
    0xD072, 0xDB5C, 0xD3A6, 0,      0,      0xD37A, 0xAFFB, 0xD37B, 0xD3A1,
    0xAFFE, 0xD375, 0xD3AF, 0,      0xD3AE, 0xD3B6, 0xAFF3, 0xAFF0, 0xD3B4,
    0xD3B0, 0xD3A7, 0xD3A2, 0xAFF6, 0xAFF2, 0xD377, 0xAFEE, 0xD3B1, 0xAFEF,
    0,      0xD379, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xD75E, 0xD760, 0xD765, 0xD779, 0xB2FC, 0xB2F2,
    0,      0xD75D, 0xB2FD, 0xB2FE, 0xD768, 0xD76F, 0xD775, 0,      0xD762,
    0,      0xD769, 0,      0,      0xB340, 0xD777, 0xD772, 0xB2FA, 0xB2F8,
    0xD76E, 0xD76A, 0xD75C, 0xB2EF, 0xD761, 0xD759, 0,      0xB2F7, 0xB2F9,
    0xD766, 0xD763, 0xB2F4, 0xD773, 0xB2F1, 0xD764, 0xD77A, 0xD76C, 0,
    0xD76B, 0xB2F0, 0,      0xB2FB, 0,      0xB2F3, 0xD75A, 0xD75F, 0xD770,
    0xD776, 0xB341, 0xD75B, 0xD767, 0xD76D, 0xB2F6, 0,      0,      0xD778,
    0xD771, 0xD774, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0xB2F5, 0,      0xDB6C, 0xDB60,
    0xB5D7, 0xDB7D, 0xDBA7, 0xDBAA, 0xB5D5, 0xDB68, 0xDBA3, 0xDB69, 0xDB77,
    0xB5E2, 0xDB73, 0xB5DF, 0,      0xDB74, 0xDB5D, 0,      0xDBA4, 0,
    0,      0xB5E8, 0xDBA1, 0xDB75, 0xDBAC, 0xDB70, 0xDFC8, 0,      0xDBAF,
    0xB5E6, 0xDB6E, 0xDB7A, 0xB5E9, 0xB5D4, 0xDB72, 0xDBAD, 0xDB6B, 0xDB64,
    0xDB6F, 0,      0xDB63, 0xDB61, 0xB5D0, 0xDBA5, 0xDB6A, 0xDBA8, 0,
    0xDBA9, 0xB5D8, 0xB5DD, 0xB5D9, 0xB5E1, 0xDB7E, 0xB5DA, 0xDB76, 0xDB66,
    0,      0xB5D2, 0xDB5E, 0xDBA2, 0xDBAB, 0xDB65, 0xB5E0, 0xDBB0, 0xDB71,
    0,      0xDB6D, 0,      0xB5D1, 0xB5E5, 0,      0xDB7C, 0xB5E7, 0,
    0xDB78, 0xB5DC, 0xB5D6, 0xB5DE, 0xB5D3, 0xB5E4, 0xDB79, 0xDB67, 0xDB7B,
    0xDB62, 0xDBA6, 0,      0,      0,      0,      0,      0,      0,
    0xDBAE, 0,      0,      0,      0,      0,      0,      0,      0xDB5F,
    0,      0,      0,      0,      0,      0xDFC7, 0,      0xDFDD, 0xB855,
    0xDFCC, 0,      0xDFCA, 0xDFB5, 0xB8A9, 0xDFC5, 0xDFD9, 0xDFC1, 0xB8B1,
    0xDFD8, 0xDFBF, 0xB5E3, 0xDFCF, 0xDFC0, 0xDFD6, 0xB8B0, 0xB8A8, 0,
    0xDFAA, 0xDFB2, 0,      0xDFCB, 0xDFC3, 0xDFDC, 0xDFC6, 0xB8B6, 0xDFD7,
    0,      0xB8AD, 0,      0xDFC9, 0xDFD1, 0xDFB6, 0xDFD0, 0,      0xDFE1,
    0xDFB1, 0xDFD2, 0,      0xDFDF, 0,      0xDFAB, 0xB5DB, 0,      0xDFB9,
    0xDFB8, 0xB8AF, 0,      0xDFBC, 0xDFBE, 0xDFCD, 0xDFDE, 0xB8B2, 0,
    0xB8B3, 0,      0xDFB0, 0xB8AB, 0xDFB4, 0xDFDA, 0xB8B4, 0,      0xB8AC,
    0xB8AE, 0xB8B5, 0xDFE0, 0xDFD3, 0xDFCE, 0,      0,      0xDFBB, 0xDFBA,
    0xB8AA, 0xDFAC, 0xB8A7, 0xDFC4, 0xDFAD, 0xDFC2, 0,      0,      0xDFB7,
    0xDFDB, 0,      0,      0,      0xB8A6, 0,      0,      0,      0xDFB3,
    0,      0,      0,      0,      0,      0,      0xDFAF, 0xDFD5, 0xDFAE,
    0xBB60, 0xE3D3, 0,      0,      0xE3C2, 0,      0,      0xE3AC, 0xE3CA,
    0xBB58, 0xE3BB, 0xE3C5, 0xBB5B, 0xE3BE, 0xBB59, 0xE3AF, 0xE3CD, 0xE3AE,
    0xE3C1, 0,      0xE3AD, 0,      0,      0xE3BF, 0xE3C8, 0xE3C6, 0xE3BA,
    0xE3B5, 0xE3B3, 0,      0xE3B4, 0xE3C7, 0xE3D2, 0xE3BC, 0xBB5A, 0,
    0xE3B7, 0,      0xE3CB, 0,      0xBB5D, 0xE3B6, 0xE3B0, 0xE3C0, 0xBB61,
    0,      0,      0xBB55, 0xBB5E, 0xE3B8, 0xE3B2, 0,      0xBB57, 0xDFD4,
    0xBB56, 0xE3C3, 0,      0xBB54, 0xBB63, 0xBB5C, 0xE3C4, 0xE3B9, 0xE3B1,
    0xE3CC, 0xE3BD, 0xBB62, 0xE3D0, 0xBB5F, 0xE3CF, 0,      0xE3C9, 0xE3CE,
    0,      0,      0,      0xE3D1, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0xE773, 0xE774, 0xE767,
    0xE766, 0xE762, 0xBDB4, 0,      0xBDAC, 0xE776, 0xE775, 0xDFA9, 0xE75F,
    0xE763, 0xE75D, 0,      0xE770, 0xE761, 0,      0xE777, 0xE75A, 0xE758,
    0xE764, 0xE76E, 0xE769, 0xBDB6, 0xE74F, 0,      0xE76D, 0,      0,
    0,      0xBDB7, 0xDFBD, 0xE75B, 0xE752, 0xE755, 0xE77B, 0xE75C, 0xE753,
    0xE751, 0xE74E, 0,      0xBDB0, 0xE765, 0xBDAF, 0xBDB3, 0xE760, 0xE768,
    0xBDA9, 0xE778, 0xE77C, 0xBDAB, 0,      0xE757, 0xE76B, 0xE76F, 0xE754,
    0xE779, 0xBDB2, 0,      0xBDB1, 0xE74C, 0xBDB5, 0xE772, 0xE756, 0xE76A,
    0xE750, 0xE75E, 0xE759, 0xBDAD, 0xBDAE, 0xE76C, 0xE77D, 0xE77A, 0xE771,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0xE74D, 0,      0xBDAA, 0xEB49, 0,      0xEB40, 0xEB43, 0,      0xBFBB,
    0xEB45, 0xEAF9, 0xEB41, 0xEB47, 0xBFB8, 0xBFBC, 0xBFB6, 0,      0,
    0xEAFB, 0xEB4C, 0,      0,      0xEB46, 0,      0xEAFC, 0xEB55, 0xEB4F,
    0xEAF8, 0xEE46, 0xEAFE, 0xBFB7, 0,      0xEB4A, 0,      0xEB54, 0xBFBF,
    0,      0xEB51, 0xEAFD, 0xEB44, 0xEB48, 0xEB42, 0xEB56, 0xEB53, 0xEB50,
    0xBFB9, 0xBFBA, 0xBFBE, 0xEAFA, 0xEB57, 0xBFBD, 0xEB4D, 0,      0,
    0xEB4B, 0,      0,      0,      0xEB4E, 0xEE53, 0xEE40, 0xEE45, 0xEE52,
    0xEE44, 0xEDFB, 0xEE41, 0,      0xC1A2, 0,      0xEDF4, 0xEE4D, 0xEE4F,
    0xEDF3, 0xC1A1, 0xEE51, 0xEE49, 0xC1A8, 0xEE50, 0xEE42, 0xC1AA, 0xEDF9,
    0xEB52, 0xEE4A, 0xEE47, 0xEDF5, 0xEE55, 0xC1A4, 0,      0,      0xC1A5,
    0xEDF7, 0xEE48, 0,      0xEE54, 0xEE4B, 0xEDFD, 0xC1A7, 0xC1A3, 0xEE4C,
    0xEDFE, 0xEE56, 0xEDF8, 0xEE43, 0xEE4E, 0xEDFA, 0xEDFC, 0,      0xC2CB,
    0xEDF6, 0xC1A9, 0xC2C4, 0xC17E, 0,      0,      0,      0,      0xC1A6,
    0xC2C8, 0xF0B3, 0,      0xF0A9, 0xF0A4, 0xF0AA, 0xF0B4, 0xF0B8, 0xF0B7,
    0xC2CA, 0xC2C9, 0,      0,      0xF0AB, 0xF0B9, 0xF0AE, 0xF0A6, 0,
    0xF0A8, 0xF0A7, 0xF0AD, 0xF0B2, 0xF0A5, 0xF0AC, 0xF0B1, 0xC2C7, 0,
    0xF0AF, 0,      0xC2C5, 0xF0B0, 0xC2C3, 0xC2C6, 0xF2D5, 0xF0B5, 0,
    0,      0xC3C2, 0,      0xF2CD, 0xF2D1, 0xF2C9, 0xF2CC, 0,      0xF2D4,
    0xC3C0, 0xF2D9, 0xF2D2, 0,      0xF2CA, 0xF2DA, 0xF2D3, 0xC3C3, 0xC3C4,
    0xF2D7, 0,      0xF2CB, 0xC3BF, 0xC3C1, 0xF2C6, 0xF2CE, 0xF2C8, 0,
    0xF2D8, 0xF2D6, 0xF2C7, 0xF2CF, 0,      0,      0,      0xF4BE, 0xC3C5,
    0xF2D0, 0xC4A7, 0xC4A9, 0xC4A6, 0,      0xF4C3, 0xF4BB, 0xF4B9, 0xF4BD,
    0xF4BA, 0,      0,      0xF4BF, 0xF4C1, 0xC4AA, 0xC4AC, 0,      0xF4C0,
    0xC4AD, 0xC4AB, 0xF4C2, 0,      0,      0,      0,      0xC4A8, 0,
    0,      0,      0,      0,      0xC4F4, 0xF5F1, 0xF5F7, 0xC4F6, 0xF4BC,
    0xF5F6, 0,      0xF5FD, 0xF5F4, 0xF5FB, 0xF5FA, 0xF4B8, 0xF5F5, 0xF0B6,
    0xF5FE, 0xF5F3, 0xF5F8, 0,      0xF5FC, 0xF5F2, 0,      0xF74A, 0xC4F5,
    0xF5F9, 0,      0,      0xF7F4, 0xF74B, 0xF749, 0xF747, 0xF748, 0xF74C,
    0,      0xC5D9, 0xF7F2, 0xF7F0, 0xF7F5, 0xF7F3, 0,      0xF7F6, 0xC5DA,
    0xF7F1, 0,      0,      0xF8BC, 0,      0,      0xF945, 0xF946, 0xF947,
    0,      0,      0xF9C7, 0xF9BD, 0xCA4F, 0xAAEA, 0,      0xAD68, 0,
    0xD3B8, 0xD3B7, 0xB040, 0xB342, 0xD77C, 0,      0,      0xD77B, 0,
    0xB5EA, 0xB8B8, 0,      0xB8B7, 0xB8B9, 0,      0xE3D4, 0xE77E, 0xEB58,
    0xEB5A, 0xEB59, 0,      0xC1AB, 0xEE57, 0xF0BA, 0xF9A5, 0xA6E4, 0,
    0xCDC9, 0xCDCA, 0xCDC8, 0xCDC7, 0xAAEB, 0,      0xD0A9, 0xD0A7, 0,
    0,      0xD0A6, 0,      0xAD69, 0xAD6B, 0xAD6A, 0xD0A8, 0,      0,
    0,      0,      0,      0,      0,      0,      0xD3C4, 0xD3C1, 0xD3BF,
    0,      0,      0xB041, 0xD3C2, 0xB046, 0xD3BC, 0xD3CB, 0,      0xD3CD,
    0xD3BD, 0,      0xB043, 0xD3CE, 0xD3C9, 0xD3BB, 0xD3C0, 0xD3CA, 0xD3C6,
    0xD3C3, 0,      0xB048, 0xD3CC, 0xD3BE, 0,      0,      0xD3C7, 0xD3B9,
    0xB047, 0xB044, 0xD3C5, 0,      0xD3C8, 0xD3BA, 0xB045, 0xB042, 0,
    0,      0,      0,      0xB34C, 0xD7A5, 0xB34B, 0,      0xD7A8, 0xD7AB,
    0xB348, 0xB346, 0xD77E, 0xD7A9, 0xD7A7, 0xD7A4, 0xD7AC, 0xD7AD, 0xD7AF,
    0xD7B0, 0xD77D, 0xB345, 0xD7A2, 0xD7A1, 0xD7AE, 0xB347, 0xD7A3, 0xB349,
    0xB344, 0xD7A6, 0xB34D, 0,      0xB34A, 0xD7AA, 0,      0,      0,
    0xB5F1, 0xDBBF, 0,      0xDBB4, 0xB5EE, 0,      0xDFE7, 0xDBBD, 0xDBB1,
    0xB5EC, 0xDBB6, 0xB5EF, 0xDBBA, 0xDBB8, 0xB5F2, 0xB5EB, 0,      0,
    0xDBB2, 0xDBB5, 0xB5F0, 0,      0xDBB3, 0,      0xDBBE, 0xDBBC, 0xDBB7,
    0xDBB9, 0xDBBB, 0xB5ED, 0,      0,      0,      0,      0,      0,
    0,      0xDFE8, 0xDFEE, 0xDFE4, 0xDFEA, 0xB8BA, 0xDFE6, 0xB8C0, 0,
    0,      0xB8BF, 0,      0xB8BE, 0xDFED, 0xB8C1, 0xB8C2, 0xDFE3, 0xDFF0,
    0xB8C3, 0xB8BD, 0xB8BC, 0xDFEC, 0xB8C4, 0xDFE2, 0xDFE5, 0xDFEF, 0xDFEB,
    0,      0,      0xE3F4, 0xE3E9, 0xB8BB, 0,      0,      0,      0,
    0xBB6A, 0xE3DD, 0xE3F2, 0xE3DE, 0xBB65, 0,      0xE3DB, 0,      0xE3E4,
    0xE3DC, 0xBB67, 0xE3D6, 0xE3F1, 0xBB68, 0xE3EE, 0xE3EF, 0xE3D7, 0xBB6D,
    0xE3E6, 0,      0xE3E0, 0xE3E7, 0xE3DA, 0,      0xE3F3, 0xE3EB, 0xE3E5,
    0xE3D5, 0xBB69, 0xE3EC, 0,      0xBB6C, 0xE3F0, 0,      0xE3EA, 0xBB66,
    0xE3E8, 0,      0xE3E2, 0xBB64, 0xE3D9, 0xE3E1, 0xE3ED, 0xE3DF, 0,
    0,      0xE3E3, 0,      0,      0,      0,      0,      0xBDC1, 0xDFE9,
    0xE7B2, 0xE7BB, 0xE7B1, 0xE7AD, 0xE7AA, 0xBDC2, 0xE7A8, 0xBB6B, 0xE7A1,
    0xBDC0, 0xE7A7, 0xBDBF, 0xE7AC, 0xE7A9, 0xE7B9, 0xE7B4, 0xE7AE, 0xE7B3,
    0xBDBB, 0xE7AB, 0xE7BE, 0xE7A2, 0xE7A3, 0xE7BA, 0xBDBC, 0xE7BF, 0xBDBE,
    0xE7C0, 0xE7B0, 0xE3D8, 0xE7B6, 0xE7AF, 0xE7B8, 0xE7B5, 0,      0,
    0,      0xE7A6, 0xBDB9, 0xE7BD, 0xBDBA, 0xE7A4, 0xBDBD, 0xEB64, 0xE7B7,
    0xE7BC, 0,      0,      0,      0,      0,      0xEB61, 0xBDB8, 0xBFC0,
    0xEB6B, 0xEB67, 0,      0xEB65, 0xEB60, 0xEB6F, 0,      0,      0,
    0xBFC4, 0,      0xEB5C, 0xEB68, 0xEB69, 0xEB5F, 0xEB5E, 0xEB6C, 0,
    0xEB62, 0xEB5D, 0xEB63, 0,      0xEB6E, 0xEB5B, 0xEB6D, 0xEB6A, 0xBFC2,
    0xBFC1, 0,      0,      0xBFC3, 0xEB66, 0xF0CB, 0,      0,      0,
    0,      0,      0xEE59, 0xC1B1, 0xEE5D, 0xEE5A, 0xEE61, 0xEE67, 0xEE5C,
    0,      0xEE70, 0xC1AE, 0xEE6A, 0xEE5F, 0xEE6B, 0xEE66, 0xEE6D, 0xEE5E,
    0xC1B3, 0xC1B2, 0xEE60, 0xEE6E, 0xEE58, 0xEE6C, 0xC1AC, 0,      0xEE64,
    0xEE63, 0xEE68, 0xEE5B, 0xC1B0, 0,      0xC1B4, 0xEE62, 0xEE69, 0xC1B5,
    0xEE65, 0,      0,      0,      0,      0xC1AD, 0xC1AF, 0xF0C7, 0xF0C5,
    0,      0,      0xF0CC, 0xF0C9, 0xF0CD, 0,      0xF0BE, 0xF0C6, 0xF0D1,
    0xEE6F, 0xF0C2, 0xC2CF, 0xE7A5, 0xF0BD, 0xF0CA, 0xF0C4, 0xF0C1, 0xF0BC,
    0xF0BB, 0xF0D0, 0,      0xF0C0, 0xF0BF, 0xC2CD, 0xF0C8, 0,      0xC2CC,
    0,      0,      0xC2CE, 0xF0C3, 0xF0CF, 0,      0xF2DE, 0xF2DF, 0,
    0xC3C9, 0xF2DC, 0xC3C6, 0xF2E4, 0,      0xC3CA, 0xF2E6, 0xF2DB, 0xF0CE,
    0xF2E8, 0xF2DD, 0,      0xC3C7, 0xF2E3, 0,      0xF2E5, 0xF2E0, 0xF2E7,
    0xF2E2, 0xF2E1, 0xC3C8, 0,      0,      0xF4C5, 0xF4C6, 0,      0xF4C8,
    0xC4AE, 0xC4AF, 0xF4C9, 0xF4C7, 0,      0xF4C4, 0,      0xF642, 0xF645,
    0xF641, 0,      0xC4FA, 0xF643, 0xC4F9, 0xC4F8, 0xC4F7, 0xF644, 0xF751,
    0xF74F, 0,      0xF74E, 0xF640, 0xF750, 0xF646, 0xF74D, 0,      0xF7F9,
    0xF7D7, 0xF7F7, 0xC5DB, 0xF7F8, 0xF7FA, 0,      0xF8BF, 0xC5FA, 0xF8BE,
    0xF8BD, 0xC5FB, 0,      0xC65A, 0xF96E, 0xF9A7, 0xF9A6, 0xF9A8, 0xA6E5,
    0xD0AA, 0,      0xD3CF, 0xD3D0, 0,      0,      0,      0xDBC0, 0,
    0xF647, 0xF8C0, 0xA6E6, 0xAD6C, 0xD0AB, 0,      0,      0,      0xD7B1,
    0xB34E, 0,      0xDBC2, 0xDBC1, 0xB5F3, 0,      0xB8C5, 0xE7C1, 0xBDC3,
    0,      0xBDC4, 0,      0,      0,      0xBFC5, 0xC5FC, 0xA6E7, 0,
    0,      0,      0xD0AC, 0xAAED, 0xD0AE, 0xD0AD, 0xAD6D, 0,      0xD3D1,
    0,      0xD3D8, 0xB049, 0xD3D6, 0xD3D4, 0,      0xD3DB, 0xD3D2, 0xD3D3,
    0xB04A, 0,      0xB04E, 0,      0,      0xD3DC, 0xB04D, 0xD3DA, 0xD3D7,
    0xD3D5, 0xB04B, 0xB04C, 0xD3D9, 0,      0,      0,      0,      0xB350,
    0xD7B2, 0,      0xB355, 0xD7C2, 0xB354, 0xD7C4, 0,      0,      0xD7B8,
    0xB352, 0xD7C3, 0,      0xD7B3, 0xB353, 0xD7BF, 0xD7BB, 0xD7BD, 0xD7B7,
    0xD7BE, 0,      0,      0xB34F, 0xD7BA, 0,      0xD7B9, 0xD7B5, 0,
    0xD7C0, 0,      0,      0xD7BC, 0xD7B4, 0,      0xD7B6, 0xB351, 0xD7C1,
    0,      0,      0,      0,      0xB5F6, 0xDBCD, 0,      0,      0,
    0xDBC9, 0xDBCB, 0xDBC6, 0xDBC5, 0xDBC3, 0,      0xDBCA, 0xDBCC, 0xDBC8,
    0,      0xDBC7, 0xB5F4, 0xB5F5, 0,      0,      0,      0,      0,
    0,      0xDBCF, 0xB8CD, 0xDFF2, 0xDFF8, 0xDFF3, 0xDFF4, 0xF9D8, 0xDFF9,
    0,      0xB8CF, 0,      0xB8C7, 0xB8CE, 0xDFF1, 0xDBC4, 0xB8CA, 0xB8C8,
    0xDFF7, 0xDFF6, 0xB8C9, 0xB8CB, 0xDFF5, 0xB8C6, 0,      0xB8CC, 0,
    0,      0,      0,      0,      0xE3F6, 0xBB74, 0,      0,      0xE442,
    0xE441, 0,      0xE3FB, 0xBB76, 0xE440, 0xE3F7, 0xE3F8, 0xBB6E, 0xBB70,
    0,      0xE3FD, 0xE3F5, 0xBB72, 0xBB71, 0xE3F9, 0xE3FE, 0xE3FC, 0xBB73,
    0xE3FA, 0,      0,      0xDBCE, 0xBB6F, 0,      0,      0xE7C2, 0xE7C9,
    0xBDC6, 0,      0xE7CD, 0xBDCA, 0xE7C5, 0xE7C3, 0,      0xE7CC, 0,
    0xBDC5, 0xE7CB, 0xBDC7, 0xBDC8, 0xE7C4, 0xBDC9, 0xE7CA, 0xE7C6, 0xE7C7,
    0xE7C8, 0xBB75, 0,      0,      0,      0xEB70, 0xEB7C, 0,      0xBFCA,
    0xEB77, 0xEB79, 0,      0xBFC8, 0xEB71, 0xEB75, 0,      0xEB78, 0xBFC6,
    0xBFC9, 0xEB7B, 0xEB73, 0xEB74, 0xEB7A, 0xEB72, 0xEB76, 0xBFC7, 0xEE72,
    0,      0xEE71, 0xC1B7, 0xEE77, 0xC1B9, 0,      0,      0xC1B6, 0xEE73,
    0xC1BA, 0xEE74, 0,      0,      0xEE75, 0xEE78, 0,      0xC1B8, 0,
    0xF0D6, 0,      0,      0xF0D9, 0,      0xF0D3, 0xF0D5, 0,      0,
    0xF0D4, 0xF0D7, 0xF0D8, 0xEE76, 0xF0D2, 0,      0,      0xC3CD, 0xF2EC,
    0xF2EF, 0xF2F1, 0xF2EA, 0xF2EB, 0xF2EE, 0xF2F0, 0xC3CE, 0xC3CC, 0xC3CB,
    0xF2ED, 0xF2E9, 0xF4CA, 0xC4B0, 0,      0xF4CB, 0,      0,      0xF649,
    0xC4FB, 0xF64B, 0xC4FC, 0xF648, 0xF64A, 0xC5A8, 0,      0xF752, 0xC5A7,
    0xF7FD, 0xF7FC, 0,      0xF7FB, 0,      0,      0xF948, 0xF949, 0xF94B,
    0xF94A, 0,      0xCA50, 0xA6E8, 0,      0xAD6E, 0xD7C5, 0xB5F7, 0,
    0xDFFA, 0xC2D0, 0,      0xF2F2, 0,      0,      0xA8A3, 0,      0,
    0,      0xB357, 0,      0,      0,      0xB356, 0,      0xDBD0, 0xB5F8,
    0xDBD2, 0xDBD1, 0,      0,      0xDFFB, 0xB8D0, 0xE443, 0xE446, 0xE445,
    0,      0xE444, 0xE7CE, 0xE7D0, 0xE7CF, 0,      0xBFCC, 0,      0,
    0,      0xBFCB, 0,      0xC1BB, 0xEE79, 0xEE7B, 0xEE7A, 0,      0,
    0xC2D1, 0,      0,      0,      0xF2F4, 0xF2F3, 0,      0xF4CC, 0xC4B1,
    0,      0,      0xC4FD, 0xF754, 0xF753, 0xC65B, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0xA8A4, 0xD0AF, 0xAD6F, 0xD7C8,
    0xD7C6, 0,      0,      0xD7C7, 0xDBD4, 0xDBD5, 0xE043, 0xDBD3, 0,
    0xDFFC, 0xE041, 0xE040, 0xE042, 0xB8D1, 0xDFFE, 0xDFFD, 0xE044, 0,
    0xE449, 0xE447, 0,      0xE448, 0xE7D3, 0xE7D1, 0,      0,      0xE7D2,
    0xEB7D, 0xEE7C, 0xEE7D, 0xC2D2, 0,      0xF2F5, 0xF4CD, 0xC4B2, 0,
    0xF64C, 0xF755, 0xC5A9, 0,      0xF7FE, 0xF94C, 0xA8A5, 0,      0xAD71,
    0xAD72, 0xD0B0, 0,      0,      0xD0B1, 0xAD70, 0,      0xB054, 0,
    0xB052, 0,      0xB051, 0xB058, 0xB050, 0xB059, 0xD3DD, 0xB056, 0,
    0xB053, 0xB057, 0xB055, 0xB04F, 0,      0,      0xB35F, 0,      0xB359,
    0xD7CC, 0xB35E, 0,      0,      0xB360, 0xB35A, 0,      0xB35B, 0,
    0xD7CA, 0,      0,      0xB358, 0,      0xD7CB, 0xB35D, 0,      0,
    0xD7C9, 0xB35C, 0,      0,      0xB644, 0,      0xB646, 0,      0,
    0xDBD8, 0xB645, 0xB5F9, 0xB5FD, 0,      0xB8E4, 0xE049, 0xDBDA, 0xB5FE,
    0,      0,      0xDBDD, 0xDBDE, 0xB643, 0,      0xDBE0, 0,      0xDBE2,
    0,      0xDBE3, 0xDBD7, 0xDBD6, 0xDBE4, 0xB642, 0xDBE1, 0xDBDF, 0,
    0xB640, 0xB5FB, 0xB647, 0xDBDB, 0xDBDC, 0xDBD9, 0,      0xB641, 0,
    0,      0xB5FC, 0,      0xB5FA, 0xE048, 0xB8DF, 0xB8DA, 0,      0,
    0xB8D5, 0,      0xB8E5, 0xB8D6, 0,      0xB8D2, 0xB8E1, 0xB8DE, 0xB8E0,
    0,      0xB8D7, 0xB8DC, 0xB8D3, 0xB8D4, 0xE050, 0xE04D, 0xE045, 0xE04A,
    0,      0xB8E2, 0xE051, 0xB8E3, 0xB8D9, 0,      0,      0xE047, 0,
    0xE04F, 0xE04B, 0xE04E, 0xE04C, 0xB8DD, 0xE046, 0xB8D8, 0,      0,
    0,      0xE44C, 0xBB78, 0xBB7B, 0,      0xE44E, 0,      0xBBA5, 0xE44D,
    0xBB7D, 0,      0xBDCF, 0xE44F, 0,      0xBBA4, 0xE44B, 0xBBA6, 0,
    0,      0,      0xBB79, 0,      0xB8DB, 0xBB7C, 0,      0xBB7A, 0xBB7E,
    0xBBA2, 0xBB77, 0xBBA7, 0xBBA3, 0,      0xBBA1, 0xE44A, 0,      0,
    0,      0,      0xBDD6, 0,      0xBDD2, 0,      0,      0,      0xBDD9,
    0,      0xE7D6, 0xBDDA, 0xE7E2, 0xE7DB, 0xBDCB, 0xE7E3, 0xE7DD, 0xBDD5,
    0xE7DE, 0,      0xBDD4, 0xE7E1, 0xBDCE, 0xE7DF, 0xE7D5, 0xBDCD, 0xEBAA,
    0xBDD3, 0,      0xBDD0, 0,      0xBDD8, 0,      0xE7D4, 0,      0xE7D8,
    0xBDCC, 0xE7D7, 0xE7D9, 0xE7DA, 0xBDD7, 0xE7DC, 0xE7E0, 0xE7E4, 0,
    0xBDDB, 0xBFD2, 0xEBA5, 0xEBAB, 0xEBA8, 0xEB7E, 0xEBAC, 0xEBA1, 0,
    0xEBA7, 0,      0xBFCD, 0xBFD3, 0xEBAD, 0,      0,      0xBFCF, 0,
    0xBFD9, 0xBFD4, 0xEBAF, 0xEBA9, 0xBFD0, 0xEBA2, 0xBFDA, 0xEBA3, 0xEBA4,
    0xBFDB, 0xBFD8, 0xBDD1, 0,      0xBFCE, 0xEBB0, 0xBFDC, 0,      0xBFD5,
    0xEBAE, 0xBFD1, 0xBFD6, 0xBFD7, 0,      0xC1C3, 0xEEA4, 0xEEAD, 0xEEAA,
    0xEEAC, 0,      0xC1C0, 0xEEA5, 0,      0xEEAB, 0xC1BC, 0xEEA7, 0xC1C4,
    0xEEA3, 0xEEA8, 0xEEAF, 0xEBA6, 0xEEA9, 0xEEA2, 0xC1BD, 0xEEA1, 0xC1BE,
    0xEEB0, 0xC1BF, 0xEEAE, 0xC1C2, 0xEE7E, 0,      0xC1C1, 0,      0xEEA6,
    0xF0DC, 0xF0EA, 0xF0E5, 0xF0E7, 0xF0DB, 0xC2D3, 0,      0xF0DA, 0xC2D6,
    0xC2D5, 0,      0xF0E9, 0xF0E1, 0xF0DE, 0xF0E4, 0,      0xF0DD, 0,
    0xF0DF, 0xF0E8, 0xF0E6, 0,      0xC2D4, 0xF0ED, 0xF0EB, 0xF0E2, 0xF0EC,
    0xF0E3, 0,      0xF2F9, 0xC3CF, 0xF341, 0,      0,      0xF64F, 0xC3D6,
    0xF0E0, 0xF2F7, 0xC3D2, 0xF2F8, 0xF2FD, 0,      0,      0xC3D4, 0xC3D5,
    0xF2F6, 0xF340, 0xF342, 0xF2FA, 0xF2FC, 0xF2FE, 0xF2FB, 0xF343, 0xC3D1,
    0xC3D7, 0xC3D3, 0,      0xC3D0, 0xF4D0, 0,      0xC4B7, 0xF4CE, 0,
    0,      0xF4D2, 0,      0xF4D3, 0xC4B5, 0xF4D4, 0xF4D1, 0,      0xF4CF,
    0xC4B8, 0xC4B4, 0xF4D5, 0,      0xC4B6, 0xC4B3, 0,      0,      0,
    0xC4FE, 0,      0,      0xC540, 0xF64E, 0xF64D, 0xF650, 0xF651, 0,
    0xC541, 0xF756, 0xF75B, 0xC5AA, 0,      0xF758, 0,      0xF757, 0xF75A,
    0xF759, 0,      0xF843, 0,      0xC5DC, 0xF842, 0xF840, 0,      0xF841,
    0,      0,      0,      0xC5FE, 0xC5FD, 0xF8C1, 0xF8C2, 0xC640, 0,
    0xF94D, 0xF94E, 0xC667, 0,      0xC66D, 0,      0xF9A9, 0xF9C8, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0xA8A6, 0,      0xD7CD,
    0,      0xD7CE, 0xE052, 0xE450, 0xE7E5, 0xC1C6, 0,      0xC1C5, 0xF0EE,
    0xF344, 0,      0xF844, 0xA8A7, 0xD3DE, 0xB05A, 0xB361, 0xE054, 0xE053,
    0xBDDC, 0xE7E6, 0xBDDD, 0xEEB1, 0xC2D7, 0,      0,      0,      0xC676,
    0xA8A8, 0xCDCB, 0xD3DF, 0,      0,      0xB362, 0,      0xD7CF, 0xD7D0,
    0,      0xDBE5, 0,      0xB648, 0xB8E6, 0,      0xE056, 0xE055, 0xE057,
    0,      0xE451, 0xE452, 0xBBA8, 0xBFDD, 0xBDDE, 0xBFDE, 0,      0xEEB5,
    0xEEB2, 0xEEB4, 0xEEB3, 0xC1C7, 0,      0xF0EF, 0xF346, 0xF345, 0xCBA4,
    0xB05C, 0xB05B, 0xD3E0, 0,      0xD7D1, 0,      0,      0xDBE7, 0xDBE6,
    0xB649, 0,      0xE059, 0xE05A, 0xE058, 0,      0,      0xB8E8, 0xB8E7,
    0,      0xBBAA, 0xBBA9, 0,      0xE7E7, 0xEBB3, 0xEBB1, 0xEBB2, 0xBFDF,
    0xEEB7, 0xEEB6, 0,      0xF0F2, 0xF0F1, 0xF0F0, 0xF347, 0,      0xF9AA,
    0xA8A9, 0xAD73, 0,      0xAD74, 0xB05D, 0xB05E, 0xD3E2, 0xD3E1, 0xD7D2,
    0,      0xB368, 0xB366, 0xB363, 0xB367, 0xB365, 0xB364, 0,      0,
    0xB64A, 0xDBEA, 0,      0xB8ED, 0xB64C, 0xB651, 0xDBEC, 0xB653, 0xB652,
    0xB655, 0xDBEB, 0xDBE8, 0xB64F, 0xB64B, 0xB64D, 0xDBE9, 0xB654, 0xB650,
    0xB64E, 0xB8EF, 0xB8EE, 0xB8EC, 0xB8F0, 0,      0xB8EA, 0xB8EB, 0,
    0xB8E9, 0,      0xE05B, 0,      0,      0xE454, 0,      0xBBAC, 0xBBAD,
    0xBBAB, 0,      0xE453, 0,      0xE455, 0,      0xE7EA, 0xE7EC, 0,
    0xBDE7, 0xE7ED, 0xBDE0, 0xE7E9, 0xBDDF, 0xBDE9, 0xBDE5, 0xBDE6, 0xBDE2,
    0xE7E8, 0xBDE1, 0xE7EE, 0xE7EB, 0,      0xBDE8, 0,      0xBDE3, 0xBDE4,
    0xEBB5, 0,      0xEBB7, 0xEBB6, 0,      0xEBB8, 0xBFE0, 0xEBB4, 0,
    0,      0xC1CB, 0xEEB8, 0xC1C8, 0xC1CC, 0xC1CA, 0xC1C9, 0xF0F3, 0,
    0xF0F6, 0,      0xF0F5, 0,      0xF0F4, 0xC2D8, 0xF348, 0xF349, 0xC3D8,
    0xF34A, 0xC3D9, 0,      0,      0xC4BA, 0,      0xC4B9, 0xF652, 0,
    0,      0xC542, 0xF653, 0xF75C, 0xC5AB, 0xC5AC, 0,      0xF845, 0,
    0xC642, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0xA8AA, 0,      0xB36A, 0xB369, 0xE05C, 0xE05D, 0,      0xBBAE,
    0xEBB9, 0xBDEA, 0xEBBA, 0xEEB9, 0xA8AB, 0,      0xD0B2, 0xAD76, 0xAD75,
    0,      0xD3E3, 0xB05F, 0xD3E4, 0xD7D5, 0,      0xD7D4, 0,      0xD7D3,
    0,      0,      0xDBEE, 0xB658, 0,      0,      0xDBED, 0xB657, 0,
    0,      0,      0xDBEF, 0xB656, 0,      0xE05F, 0xE062, 0xE060, 0xE061,
    0xE065, 0xE05E, 0xE066, 0xE063, 0xE064, 0xBBB0, 0xE456, 0,      0,
    0xBBAF, 0,      0xE7F2, 0xE7F0, 0,      0,      0xBDEB, 0xE7EF, 0xE7F1,
    0,      0xBDEC, 0,      0xEBBB, 0,      0xEBBC, 0xC1CD, 0,      0xF34C,
    0xF34E, 0xF34B, 0xF34D, 0xF4D6, 0xF654, 0,      0,      0xF96F, 0xA8AC,
    0xAD77, 0xD3E5, 0xD3E7, 0xD3E6, 0,      0xD7D8, 0xB36C, 0,      0xD7D6,
    0,      0xB36B, 0xD7D9, 0,      0xD7DA, 0xD7D7, 0,      0,      0xDBFB,
    0xB660, 0xDBF3, 0xDBF9, 0,      0,      0xB65B, 0xB65E, 0xDBF2, 0xB659,
    0xDBF6, 0xE06C, 0xB65D, 0,      0xDBF1, 0,      0xDBF7, 0xDBF4, 0xDBFA,
    0xDBF0, 0xDBF8, 0xB65C, 0xB65F, 0xDBF5, 0xB65A, 0,      0xB8F2, 0xE068,
    0xB8F1, 0xE06F, 0xE06E, 0xB8F8, 0,      0xB8F9, 0xE070, 0xB8F3, 0xE06D,
    0xB8F7, 0xE072, 0xE069, 0,      0xE06B, 0xB8F4, 0xE067, 0xE06A, 0xE071,
    0xB8F5, 0xE073, 0,      0,      0,      0,      0,      0xB8F6, 0,
    0xBBB1, 0xE45B, 0xE461, 0xE459, 0xE462, 0,      0xE458, 0xE45D, 0xE463,
    0xE460, 0xE45F, 0xE45E, 0,      0xE457, 0xE45C, 0,      0,      0xE45A,
    0,      0xBDF1, 0xBDEE, 0xE7FB, 0xE841, 0xE843, 0xE840, 0xE7F8, 0xE7FA,
    0xE845, 0xE842, 0xE7FC, 0xE846, 0xE7F9, 0xE844, 0xBDEF, 0xBDF5, 0xBDF3,
    0xE7F3, 0xBDF4, 0xBDF0, 0xE7F4, 0xE7F6, 0xE7F5, 0xE7FD, 0xE7FE, 0,
    0xBDF2, 0,      0xBDED, 0,      0,      0xE7F7, 0,      0xEBC6, 0xBFE2,
    0,      0xEBBD, 0xBFE3, 0xBFE6, 0xEBC2, 0,      0xEBBF, 0xBFE5, 0,
    0,      0xEBC3, 0xEBC4, 0xEBBE, 0xEBC7, 0xEBC0, 0xEBC5, 0xBFE4, 0,
    0xBFE1, 0xEBC1, 0,      0xEEBF, 0xC1D0, 0xC1CE, 0xC1D1, 0xC1CF, 0xEEBE,
    0xEEBB, 0xEEBA, 0,      0xEEBD, 0,      0,      0xEEBC, 0xF145, 0xC2DE,
    0xF0FB, 0xF0FA, 0,      0xC2D9, 0xF141, 0xF140, 0xF0F7, 0xF143, 0xF0FC,
    0xC2DD, 0xF0F9, 0xF142, 0xF0F8, 0xC2DA, 0xC2DC, 0xF0FD, 0xC2DB, 0xF0FE,
    0,      0xF144, 0xF352, 0,      0xC3DE, 0xF34F, 0,      0xF353, 0,
    0,      0xC3DB, 0xF351, 0xC3E0, 0,      0xC3DD, 0,      0xF350, 0,
    0xC3DF, 0xF354, 0xC3DA, 0,      0,      0,      0,      0xC4BC, 0xC4BE,
    0,      0xF4D9, 0xC4BD, 0xF4D7, 0xC3DC, 0xF4D8, 0xC4BB, 0xC543, 0xC545,
    0xF656, 0xC544, 0xF655, 0,      0xF761, 0xC5AD, 0xF760, 0xC5AE, 0xF75E,
    0xF75D, 0xF762, 0xF763, 0xF846, 0,      0xF75F, 0,      0,      0xF8C6,
    0xF8C3, 0xF8C4, 0xF8C5, 0xC65C, 0,      0xF951, 0xF950, 0xF94F, 0xF970,
    0,      0xF9BE, 0xF9AB, 0xC66E, 0xA8AD, 0xB060, 0,      0,      0,
    0,      0,      0xB8FA, 0,      0,      0,      0,      0,      0,
    0,      0xBDF6, 0,      0,      0xEBC8, 0,      0,      0xC2DF, 0,
    0xF355, 0,      0,      0,      0,      0,      0,      0xF9AC, 0xA8AE,
    0xAAEE, 0xAD79, 0xAD78, 0,      0xB063, 0,      0xD3E8, 0xB061, 0xD3E9,
    0xB062, 0,      0,      0xD7DF, 0xD7DB, 0,      0,      0xB36D, 0xD7DE,
    0xD7DD, 0xD7DC, 0xB36E, 0xD7E0, 0xD7E1, 0,      0,      0,      0xDC43,
    0xDC41, 0xDC45, 0xDC46, 0xDC4C, 0,      0xDC48, 0xDC4A, 0,      0xDC42,
    0xDBFC, 0,      0xDC49, 0,      0,      0xDC4B, 0xDC44, 0xDC47, 0xDBFD,
    0xB662, 0xDC40, 0xDBFE, 0xB661, 0xB663, 0,      0xB8FD, 0xE075, 0xE077,
    0xE076, 0xE07B, 0xB8FB, 0,      0xE078, 0xE074, 0xE079, 0xE07A, 0xB8FC,
    0xB8FE, 0xE07C, 0,      0xE467, 0xE466, 0,      0xE464, 0xE465, 0xBBB3,
    0xBBB5, 0xBBB2, 0xBBB4, 0xE84D, 0xE84E, 0xE849, 0,      0xE84A, 0xBDF8,
    0xBDFD, 0xBDF7, 0xBDFE, 0xBDF9, 0xE84B, 0,      0,      0xE84C, 0xE848,
    0xBE40, 0xBDFB, 0,      0,      0xBDFA, 0xBDFC, 0,      0xE847, 0,
    0xEBCA, 0xBFE8, 0,      0,      0xEBCC, 0xBFEA, 0xEBCF, 0xEBCB, 0xEBC9,
    0xEBCE, 0xBFE9, 0xEBCD, 0,      0xBFE7, 0,      0,      0xC1D3, 0xC1D6,
    0xEEC1, 0,      0xC1D4, 0xEEC0, 0xC1D2, 0xC1D5, 0xF146, 0xF147, 0xF148,
    0xC2E0, 0,      0xF149, 0,      0xC2E1, 0xC3E2, 0xF358, 0xF359, 0xF357,
    0xF356, 0xF35A, 0xC3E1, 0xF4DD, 0xF4DB, 0xF4DC, 0xF4DE, 0xF4DA, 0xF4DF,
    0xF658, 0,      0xF659, 0xF657, 0xC546, 0xF764, 0xC5AF, 0xF765, 0xF848,
    0xF847, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0xA8AF, 0xB664, 0,      0,      0xB940, 0,      0,      0,
    0xBBB6, 0,      0,      0xBFEC, 0,      0xBFEB, 0,      0,      0,
    0,      0xC3E3, 0xC47C, 0xC547, 0xA8B0, 0xB064, 0xB941, 0,      0xF35B,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0xCBA6, 0,      0,      0xA8B1, 0,      0xA8B4, 0xA8B3, 0xA8B2,
    0,      0,      0xCBA5, 0,      0xCDCD, 0,      0xCDCF, 0xAAEF, 0,
    0,      0xAAF1, 0xCDCC, 0xCDCE, 0xAAF0, 0xCDD1, 0xCDD0, 0xCDD2, 0,
    0,      0,      0,      0,      0,      0,      0,      0xD0B6, 0xD0B4,
    0xAD7C, 0xD0B3, 0xADA3, 0xAD7E, 0xAD7B, 0,      0xADA4, 0,      0xAD7D,
    0xADA2, 0,      0xADA1, 0xD0B5, 0,      0xAD7A, 0,      0,      0,
    0xB06A, 0xD3EB, 0xD3F1, 0xB067, 0xB06E, 0,      0xB069, 0xD3EE, 0xD3F0,
    0xB06C, 0xD3EA, 0xD3ED, 0xB068, 0xB065, 0xD3EC, 0xB06B, 0xD3EF, 0xB06D,
    0xB066, 0,      0,      0,      0,      0xD7E3, 0xD7E6, 0xB370, 0,
    0xB37A, 0xB376, 0xD7E4, 0,      0,      0xB37E, 0xB377, 0xB37C, 0xB372,
    0,      0xB36F, 0xB371, 0xB37D, 0xD7E5, 0xB375, 0xB378, 0xB374, 0xB379,
    0xD7E7, 0xB37B, 0xB373, 0xD7E2, 0,      0,      0,      0,      0,
    0,      0,      0,      0xDC4D, 0xB665, 0xDC4F, 0,      0xB667, 0xB669,
    0,      0xDC4E, 0xB666, 0xB66A, 0,      0xB668, 0,      0,      0,
    0xB947, 0xE0A3, 0xB94F, 0xE07E, 0,      0xB950, 0xB945, 0,      0xE0A1,
    0,      0,      0xB94A, 0,      0xE0A2, 0xB943, 0xB942, 0,      0xB94D,
    0xB94C, 0xB94B, 0xB949, 0xB94E, 0xE07D, 0xB944, 0xB946, 0xB948, 0,
    0,      0xBBB8, 0xBBBB, 0,      0xBBBF, 0xBBB9, 0xBBBE, 0xBBBC, 0,
    0xBBB7, 0,      0xBBBD, 0xBBBA, 0,      0,      0,      0xE852, 0xBE43,
    0xBE41, 0,      0xE853, 0,      0xBE44, 0xBE42, 0xE851, 0xE850, 0,
    0xBFF0, 0xE84F, 0xBFEE, 0xBFED, 0xEBD0, 0xBE45, 0xBFEF, 0xEBD1, 0xBFF2,
    0xEBD2, 0xBFF1, 0xC1D8, 0xEEC3, 0xC1D7, 0xC1DC, 0xC1DA, 0xC1DB, 0xC2E3,
    0xC1D9, 0xEEC2, 0xEBD3, 0xC2E2, 0xC2E4, 0,      0xC3E4, 0xC3E5, 0,
    0xF4E0, 0,      0xC5DE, 0xC5DD, 0xA8B6, 0,      0,      0xCA55, 0xB06F,
    0,      0xCA52, 0xCA53, 0xCA51, 0,      0xCA54, 0,      0,      0xCBAA,
    0xCBA7, 0xCBAC, 0xCBA8, 0xA8B7, 0xA8BA, 0,      0xCBA9, 0xA8B9, 0xCBAB,
    0,      0,      0xA8B8, 0,      0,      0,      0,      0xCDD5, 0xCDD7,
    0xAAF4, 0xCDD3, 0xCDD6, 0xCDD4, 0xAAF2, 0xAAF5, 0,      0xAAF3, 0,
    0,      0,      0,      0xD0B8, 0xD0BC, 0xD0B9, 0,      0xADA7, 0,
    0xADA8, 0,      0xD0BB, 0,      0xD0BD, 0xD0BF, 0,      0xADA5, 0xD0BE,
    0,      0,      0xADA6, 0,      0,      0,      0,      0,      0xD7EE,
    0xD0BA, 0xD3F2, 0xD3FB, 0xD3F9, 0xD3F4, 0xD3F5, 0xD3FA, 0xD3FC, 0xB071,
    0,      0xD3F7, 0xD3F3, 0xB070, 0xB072, 0xD3F6, 0xD3FD, 0xD3F8, 0,
    0,      0xB3A1, 0xD7F1, 0xD7E9, 0xD7EF, 0xD7F0, 0xB3A2, 0,      0xD7E8,
    0xD7EA, 0xD0B7, 0xD7EC, 0xD7ED, 0xD7EB, 0xB66C, 0,      0,      0,
    0xDC56, 0xEBD4, 0xDC57, 0xDC54, 0xB3A3, 0xB66E, 0xDC53, 0xDC59, 0xDC58,
    0xB66B, 0xDC5C, 0xDC52, 0xDC5B, 0xDC50, 0xDC5A, 0xDC55, 0xB66D, 0,
    0xE0AA, 0,      0xE0A5, 0xE0AB, 0xE0A6, 0xE0A4, 0xE0A7, 0xB951, 0,
    0xE0A9, 0,      0xE0A8, 0xB952, 0xBBC1, 0xBBC0, 0xE46E, 0xE471, 0xE469,
    0xE46D, 0xBBC2, 0xE46C, 0xE46A, 0xE470, 0xE46B, 0xE468, 0xE46F, 0,
    0xE859, 0xBE48, 0xF14A, 0xE856, 0xE857, 0xE855, 0xDC51, 0xBE47, 0xE85A,
    0xE854, 0xBE46, 0xBE49, 0xE858, 0xEBD5, 0xBFF3, 0xEBD6, 0xEBD7, 0,
    0xEEC4, 0xC1DD, 0xF14B, 0xF14C, 0,      0,      0xF14D, 0xF35D, 0xF35C,
    0xF4E2, 0,      0xF4E1, 0xF65B, 0xF65C, 0xF65A, 0xF766, 0xC5B0, 0xA8BB,
    0xADAA, 0xADA9, 0xB075, 0xB074, 0xD440, 0xD441, 0xD3FE, 0,      0xB073,
    0xD7F5, 0,      0xD7F6, 0xD7F2, 0xB3A4, 0xD7F3, 0,      0xD7F4, 0,
    0,      0,      0,      0xDC5F, 0xDC61, 0xDC5D, 0xDC60, 0xB66F, 0xDC5E,
    0xB670, 0,      0,      0xDD73, 0xB955, 0xB954, 0,      0xB953, 0,
    0xE0AC, 0xE0AD, 0,      0,      0xE473, 0xE475, 0xBBC6, 0xBBC3, 0,
    0xBBC5, 0xBBC4, 0xE474, 0xE472, 0,      0,      0,      0,      0,
    0xE861, 0xE85E, 0xE85F, 0xBE4D, 0xE860, 0xE85B, 0xE85C, 0xBE4A, 0,
    0xBE4B, 0xE85D, 0xBE4C, 0,      0xEBDB, 0,      0xEBDC, 0xEBD9, 0xEBDA,
    0xBFF4, 0xEBD8, 0,      0,      0,      0,      0,      0xEEC8, 0xEEC5,
    0xEEC7, 0xC1E0, 0xEECB, 0xC1DF, 0xEEC9, 0xEECC, 0xEECA, 0xEEC6, 0xC1DE,
    0,      0xF14F, 0,      0xF150, 0xF14E, 0,      0xF152, 0xC2E5, 0xC2E6,
    0xF35F, 0xC3E7, 0xF151, 0xF35E, 0xC3E6, 0xF4E5, 0xF4E6, 0xC4BF, 0xF4E4,
    0,      0xF4E3, 0,      0xF65D, 0xC548, 0,      0xF849, 0xF8C8, 0xF8C7,
    0,      0xC643, 0xC65D, 0xF8C9, 0xF971, 0,      0xC66F, 0xA8BC, 0xAAF6,
    0,      0xB956, 0,      0xC4C0, 0xA8BD, 0xADAB, 0xB3A5, 0xB671, 0xC2E7,
    0xAAF7, 0,      0xD0C1, 0xD0C0, 0xD442, 0,      0xB078, 0xB076, 0xB07A,
    0xD444, 0,      0xB079, 0xB077, 0,      0,      0,      0,      0xD443,
    0xB3A8, 0xD7FC, 0,      0xB3A7, 0xB3A9, 0xD842, 0xB3AB, 0xD7FE, 0xD840,
    0xD7F7, 0xB3AA, 0xD843, 0,      0,      0xD7F9, 0,      0xD7FA, 0xD7F8,
    0xB3A6, 0,      0xD841, 0xD7FB, 0xD7FD, 0,      0,      0,      0xDC6D,
    0,      0xDC6C, 0xDC6A, 0xDC62, 0xDC71, 0xDC65, 0xDC6F, 0xDC76, 0xDC6E,
    0xB679, 0,      0xB675, 0xDC63, 0,      0xDC69, 0xB677, 0,      0xDC68,
    0xB678, 0xB67A, 0xDC6B, 0,      0xB672, 0xB673, 0xDC77, 0xDC75, 0,
    0xDC74, 0xDC66, 0,      0xDC72, 0,      0xB676, 0,      0,      0,
    0,      0xB674, 0xDC73, 0xDC64, 0xDC67, 0xDC70, 0,      0,      0,
    0,      0,      0xE4BA, 0xE0B7, 0,      0xE0B0, 0xE0C3, 0xE0CC, 0xE0B3,
    0xB961, 0,      0xE0C0, 0xB957, 0xB959, 0xB965, 0xE0B1, 0,      0,
    0xB95A, 0xB95C, 0xB966, 0xB95B, 0,      0,      0,      0,      0xB964,
    0xE0B9, 0,      0xE0AE, 0xB962, 0xE0B8, 0xB95E, 0xE0CA, 0xB963, 0xE0C8,
    0xE0BC, 0xE0C6, 0xB960, 0xE0AF, 0xE0C9, 0xE0C4, 0,      0xE0CB, 0xB958,
    0,      0,      0xB967, 0xB95D, 0,      0,      0xE0B5, 0,      0xE0BD,
    0xE0C1, 0,      0xE0C5, 0xB95F, 0xE0B4, 0xE0B2, 0xE0BE, 0,      0,
    0,      0,      0xE0BB, 0xE0BA, 0,      0xE0BF, 0xE0C2, 0,      0xE0C7,
    0,      0,      0,      0xE478, 0,      0xBBC7, 0xE4A4, 0xE47A, 0xBBCC,
    0xBBD0, 0xE4AD, 0xE4B5, 0xE4A6, 0xBBC8, 0,      0xE4AA, 0xE0B6, 0,
    0xBBC9, 0xE4B1, 0xE4B6, 0xE4AE, 0,      0xE4B0, 0xE4B9, 0xE4B2, 0xE47E,
    0xE4A9, 0,      0,      0xBBD1, 0,      0xBBCD, 0xE47C, 0xE4AB, 0xBBCB,
    0xE4A5, 0xBBCA, 0xE4B3, 0xE4A2, 0xE479, 0xBBCE, 0xE4B8, 0,      0,
    0xE47B, 0xE4AF, 0xE4AC, 0xE4A7, 0xE477, 0xE476, 0xE4A1, 0xE4B4, 0xBBCF,
    0xE4B7, 0xE47D, 0xE4A3, 0xBE52, 0,      0,      0,      0,      0,
    0xBE5A, 0xBE55, 0xE8A4, 0xE8A1, 0xE867, 0xBE50, 0,      0xF9D7, 0,
    0xBE4F, 0xBE56, 0,      0,      0,      0xE865, 0xBE54, 0xE871, 0xE863,
    0xE864, 0xBE4E, 0xE8A3, 0xBE58, 0xE874, 0xE879, 0xE873, 0xEBEE, 0xE86F,
    0xE877, 0xE875, 0xE868, 0xE862, 0xE87D, 0xBE57, 0xE87E, 0,      0xE878,
    0,      0xE86D, 0xE86B, 0xE866, 0,      0,      0,      0xE86E, 0xE87B,
    0xE86A, 0xE87A, 0xE8A2, 0,      0,      0xBE53, 0,      0xE876, 0xE87C,
    0xE872, 0xE86C, 0xBE51, 0,      0,      0,      0xE4A8, 0xE870, 0xBE59,
    0xE869, 0,      0,      0,      0,      0,      0xEBF4, 0xBFF7, 0xEBF3,
    0xEBF0, 0xEC44, 0xBFFB, 0,      0xEC41, 0xEBF8, 0xEC43, 0xEBE9, 0xEBF6,
    0,      0xBFFD, 0,      0xEBE1, 0,      0xEBDF, 0xEC42, 0,      0xEC40,
    0xEBFE, 0xEBED, 0xEBEC, 0xEBE2, 0xC040, 0,      0xEBE8, 0xEBF2, 0xEBFD,
    0xC043, 0xEC45, 0,      0xC1E8, 0xC045, 0xBFFE, 0xEBE6, 0,      0xEBEF,
    0xEBDE, 0xEBE0, 0xBFF5, 0xC042, 0xBFFA, 0xEBE7, 0xEBF7, 0xEBF1, 0xC041,
    0xEBDD, 0xC1E3, 0xEBF9, 0xEBFC, 0xBFFC, 0,      0xEBEB, 0xC044, 0xBFF9,
    0,      0,      0,      0xBFF8, 0xEBF5, 0xEBFB, 0xBFF6, 0,      0xEBE4,
    0xEBFA, 0,      0,      0xEBE5, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0xEBEA, 0xEED2, 0,      0xEED7, 0xC1E5,
    0xC1E7, 0xEEDD, 0xC1E1, 0xEEEC, 0xEEE3, 0xEED8, 0xEED9, 0xEEE2, 0,
    0xC1EE, 0xEEE1, 0xEED1, 0xEEE0, 0xEED4, 0xEEED, 0xC1ED, 0xC1EB, 0xEED5,
    0,      0xEEE8, 0,      0xEEDA, 0xEEE7, 0,      0xEEE9, 0xEED0, 0xC1E6,
    0,      0xEEEA, 0,      0,      0xEEDE, 0,      0xC1EA, 0xEEDB, 0,
    0,      0xC1EC, 0xEEE4, 0,      0,      0,      0xC1E4, 0xEED6, 0xEEE5,
    0,      0xEEDF, 0xEBE3, 0xEEE6, 0xEED3, 0,      0xC1E9, 0,      0xEEEB,
    0,      0xC1E2, 0xEECE, 0,      0,      0,      0,      0xF160, 0xF159,
    0xC2E9, 0,      0xF154, 0xF163, 0xF15B, 0xEEDC, 0,      0xF165, 0xF155,
    0,      0xC2E8, 0xF15F, 0xC2EA, 0xC2F2, 0xC2F0, 0xF161, 0xC2F1, 0xF157,
    0,      0xF158, 0xF15D, 0xF162, 0,      0xEECD, 0xC2EB, 0xF16A, 0xF167,
    0xF16B, 0xF15E, 0xF15A, 0xF168, 0xF36A, 0xF15C, 0,      0xC2EE, 0,
    0xC2ED, 0xEECF, 0xC2EF, 0xF164, 0xF166, 0xC2EC, 0xF169, 0xF153, 0,
    0xF156, 0,      0,      0,      0,      0,      0,      0,      0,
    0xF373, 0,      0xF363, 0xC3EB, 0xF371, 0,      0,      0xF361, 0xC3EC,
    0,      0xF36C, 0,      0xF368, 0xC3F1, 0xF372, 0xF362, 0xF365, 0xC3E9,
    0xF374, 0,      0xF36D, 0xF370, 0xC3EF, 0xC3F4, 0xC3F2, 0xF369, 0xF364,
    0,      0xC3ED, 0xC3EE, 0xF360, 0xC3EA, 0,      0xC3E8, 0xC3F0, 0xF36F,
    0xC3F3, 0,      0xF36B, 0xF375, 0xC3F5, 0,      0,      0,      0xF367,
    0,      0xF36E, 0,      0,      0,      0,      0,      0,      0xF4F3,
    0xF542, 0xF4F5, 0xF4FC, 0xF366, 0xF4FA, 0xF4E9, 0xF540, 0xC4C3, 0xF4ED,
    0xF4FE, 0xF4F4, 0,      0,      0xC4C2, 0,      0,      0xF544, 0xF4F6,
    0,      0xF4FB, 0xF4FD, 0xF4E7, 0xF541, 0xF4F2, 0xF4F7, 0xF4EB, 0xF4EF,
    0xF543, 0xF4F9, 0xF4E8, 0xF4EC, 0xF4EE, 0xF4F8, 0,      0xC4C1, 0xF4F1,
    0,      0,      0,      0,      0,      0,      0xF4EA, 0,      0,
    0,      0,      0,      0,      0,      0xF4F0, 0xF661, 0xF666, 0xC54F,
    0xF668, 0,      0xC549, 0,      0xF664, 0xF66A, 0xC54E, 0xC54A, 0,
    0xC54B, 0xF660, 0xF667, 0xC54D, 0xF665, 0xC54C, 0xF65F, 0xF663, 0xF662,
    0,      0xF65E, 0xF669, 0,      0,      0,      0xC5B1, 0xF76D, 0xF770,
    0xF76C, 0xF76E, 0xF76F, 0xF769, 0xF76A, 0xF767, 0,      0,      0xF76B,
    0xF768, 0xC5B2, 0xC5B3, 0,      0,      0xF84B, 0,      0xF84D, 0,
    0,      0,      0,      0,      0xF84C, 0xF84E, 0,      0xC5E0, 0,
    0xF84A, 0xC5DF, 0xC5E1, 0,      0,      0,      0xF8CB, 0xF8CC, 0xC644,
    0xF8CA, 0,      0xF953, 0xF952, 0xF954, 0xC65F, 0xF955, 0xC65E, 0xF956,
    0xF972, 0xF975, 0xF974, 0xC668, 0xF973, 0,      0,      0,      0xC672,
    0xC670, 0xC671, 0xC677, 0xF9C0, 0xF9C1, 0xF9BF, 0xF9C9};

/* page 8 0x9577-0x9FA4 */
static const uint16_t tab_uni_big58[] = {
    0xAAF8, 0,      0,      0xD844, 0xDC78, 0xE8A5, 0xF376, 0,      0,
    0xAAF9, 0,      0xADAC, 0xB07B, 0,      0,      0xD845, 0,      0xD846,
    0xB3AC, 0,      0xB67D, 0xDC7A, 0xDC79, 0xB6A3, 0xB67C, 0xDC7B, 0xB67E,
    0xB6A2, 0xB6A1, 0xB67B, 0,      0,      0,      0xB968, 0,      0,
    0xE0D0, 0xE0CE, 0,      0xE0CF, 0xE0CD, 0,      0xBBD2, 0,      0xBBD5,
    0xBBD7, 0xBBD6, 0,      0,      0xBBD3, 0xBBD4, 0,      0xE8A7, 0xE8A6,
    0xBE5B, 0xE8A8, 0,      0xE8A9, 0xBE5C, 0,      0,      0,      0xEC4D,
    0xEC4B, 0xEEF3, 0,      0xEC49, 0xEC4A, 0xC046, 0xEC46, 0xEC4E, 0xEC48,
    0xEC4C, 0xEEEF, 0,      0,      0xEEF1, 0,      0xEEF2, 0xC1F3, 0xEEEE,
    0xC1F2, 0xEEF0, 0xC1EF, 0xC1F0, 0xC1F1, 0xEC47, 0,      0,      0xC2F5,
    0xF16E, 0xF16C, 0xF16D, 0xC2F3, 0xC2F6, 0xC2F4, 0,      0,      0,
    0xF377, 0xF378, 0xC3F6, 0,      0xF545, 0xF547, 0xF546, 0xC4C4, 0xC550,
    0xF66D, 0xF66C, 0xF66B, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xAAFA, 0,      0xC9AA, 0,      0xCA58, 0xA6E9,
    0xCA56, 0xCA59, 0xCA57, 0,      0,      0,      0xCBAE, 0,      0xA8C1,
    0,      0xA8C2, 0xCBB0, 0xA8BF, 0xCBAF, 0xCBAD, 0xA8C0, 0xA8BE, 0,
    0,      0,      0,      0,      0,      0xCDD8, 0xCDDB, 0xAAFD, 0xCDDA,
    0xCDD9, 0,      0xAAFC, 0xAAFB, 0,      0xAB40, 0xCDDC, 0xAAFE, 0,
    0,      0,      0,      0,      0xD0C6, 0xADAE, 0xADAF, 0xADB0, 0xD0C7,
    0xD0C3, 0xADAD, 0xD0C4, 0,      0xD0C5, 0xD0C2, 0,      0,      0,
    0xB0A4, 0,      0,      0xB0A1, 0xD445, 0xB0A2, 0xB0A5, 0xD446, 0,
    0xB07E, 0xB07C, 0xB07D, 0xB0A3, 0,      0,      0,      0,      0,
    0xB3AD, 0xD849, 0xB3B5, 0xD848, 0,      0xD84B, 0xB3B1, 0xD84A, 0xB6AB,
    0xB3AF, 0xB3B2, 0xB3AE, 0xB3B3, 0xB3B4, 0xB3B0, 0,      0,      0,
    0xD847, 0xB6A7, 0xDC7D, 0,      0xDCA3, 0,      0,      0xDCA2, 0xB6AC,
    0xB6A8, 0xB6A9, 0xDC7C, 0xDC7E, 0xDCA1, 0xB6A4, 0xB6A6, 0,      0xB6AA,
    0xB6A5, 0,      0,      0xE0D3, 0xE0D1, 0xE0D2, 0xB96A, 0xB96B, 0,
    0xE0D4, 0xB969, 0xBBD8, 0,      0xBBDA, 0xBBD9, 0,      0xE4BB, 0,
    0,      0xE4BC, 0xE8AB, 0,      0xE8AA, 0,      0,      0xC047, 0xC048,
    0xEC4F, 0xC049, 0,      0xEEF6, 0,      0xEEF4, 0,      0xEEF5, 0xC1F4,
    0,      0xF16F, 0xC3F7, 0,      0,      0,      0xC1F5, 0xAB41, 0,
    0xB0A6, 0xD447, 0,      0,      0xD84C, 0xB3B6, 0xB6AD, 0xDCA4, 0xDCA6,
    0xB6AF, 0xB6AE, 0xB6B0, 0xB6B1, 0xDCA5, 0xB96E, 0xB96F, 0xB96D, 0xBBDB,
    0xB96C, 0xE0D5, 0,      0,      0,      0xBBDC, 0xE8AC, 0xEC50, 0xC04A,
    0xC1F6, 0xF170, 0xF174, 0xC2F9, 0xF171, 0xC2FA, 0xC2F8, 0xF175, 0xC2FB,
    0xF173, 0,      0xF379, 0xC2F7, 0xC3F8, 0,      0xF8CD, 0,      0,
    0xAB42, 0xB3B8, 0xB3B7, 0,      0,      0,      0,      0xB6B2, 0xDCA8,
    0xDCA7, 0xB6B3, 0,      0,      0xE0D9, 0xB973, 0xB970, 0xE0D8, 0xB972,
    0xE0D6, 0xB971, 0,      0xE0D7, 0,      0xE4BD, 0xBBDD, 0,      0xE8AF,
    0,      0xBE5D, 0xE8AD, 0xBE5E, 0xBE5F, 0xE8AE, 0xBE60, 0,      0xEC51,
    0,      0xC04E, 0xC04B, 0xC050, 0xEC53, 0xC04C, 0xEC52, 0xC04F, 0,
    0,      0xC04D, 0,      0xEEF9, 0xEEFB, 0,      0,      0xC1F7, 0xEEFA,
    0xC1F8, 0xEEF8, 0xEEF7, 0,      0xF177, 0xF176, 0xC2FC, 0xF178, 0xF37E,
    0xC3FA, 0xF37D, 0xF37A, 0xC3F9, 0xF37B, 0xF37C, 0,      0xF548, 0xF549,
    0xC4C5, 0,      0xC553, 0,      0,      0xF66E, 0,      0,      0xC551,
    0xC552, 0xF66F, 0,      0,      0xC5B4, 0xC5B5, 0xF771, 0,      0,
    0xC645, 0xF8CF, 0xC647, 0,      0xF8CE, 0xF8D0, 0xC646, 0xF957, 0,
    0xF9AD, 0,      0,      0,      0,      0,      0,      0xAB43, 0,
    0,      0,      0xB974, 0,      0xE4BE, 0,      0xE8B0, 0xC051, 0xC052,
    0,      0xAB44, 0,      0xBE61, 0xC3FB, 0xADB1, 0,      0,      0,
    0xC053, 0,      0xC5E2, 0xADB2, 0xD84D, 0,      0xDCA9, 0,      0xDCAB,
    0,      0xDCAA, 0,      0xE0DD, 0xE0DA, 0xB975, 0,      0xB976, 0xE0DB,
    0xE0DC, 0,      0xE4C0, 0xE4C5, 0xBBDE, 0xE4BF, 0xE4C1, 0xE4C8, 0xE4C3,
    0xE4C7, 0xE4C4, 0xE4C2, 0xE4C6, 0xBBDF, 0,      0,      0xE8B3, 0,
    0xE8B1, 0xBE63, 0,      0xBE62, 0xE8B2, 0xBE64, 0,      0,      0,
    0,      0xEC56, 0,      0,      0xEC55, 0xC054, 0xEC54, 0xEEFC, 0,
    0xEEFE, 0xEF41, 0xEF40, 0,      0xC1F9, 0xEEFD, 0xF1A1, 0xC2FD, 0xF17D,
    0xF1A2, 0xC2FE, 0,      0xF17B, 0,      0xF17E, 0xF17C, 0xF179, 0xC340,
    0xF17A, 0,      0,      0,      0,      0xF3A1, 0,      0,      0xF3A3,
    0xF3A2, 0,      0xF54A, 0,      0xF54B, 0,      0,      0,      0xF670,
    0,      0xC5B7, 0,      0xC5B6, 0xF84F, 0xF850, 0xC648, 0xF8D1, 0,
    0xC669, 0,      0xADB3, 0xB6B4, 0xE4CA, 0xE4C9, 0xE8B5, 0xE8B4, 0,
    0,      0xC1FA, 0xEF43, 0xEF42, 0xF1A5, 0xF1A3, 0xF1A6, 0xF1A4, 0,
    0,      0xC3FC, 0xF3A4, 0xF3A5, 0xF3A6, 0,      0xF671, 0,      0xF772,
    0,      0xF8D2, 0,      0,      0,      0,      0,      0,      0,
    0xADB4, 0,      0,      0xEC57, 0xEF44, 0,      0xADB5, 0,      0,
    0xBBE0, 0,      0xEC58, 0xC341, 0xF1A7, 0xC3FD, 0,      0xF54C, 0xF54D,
    0xC554, 0xF851, 0xADB6, 0xB3BB, 0xB3BC, 0xD84E, 0xB6B5, 0xB6B6, 0xDCAC,
    0xB6B7, 0,      0xB97A, 0,      0xB97C, 0xE0DF, 0xE0E0, 0xE0DE, 0xB977,
    0xB978, 0xB97B, 0xB979, 0,      0,      0xE4CB, 0xBBE1, 0xBBE2, 0,
    0,      0xE8BC, 0xBE67, 0xE8B7, 0xE8B6, 0,      0xE8BB, 0xBE65, 0,
    0,      0xC05B, 0,      0xE8B8, 0xE8BD, 0xE8BA, 0xE8B9, 0,      0xBE66,
    0,      0xC059, 0,      0xEC5A, 0xC055, 0,      0xEC5B, 0,      0,
    0xEC59, 0,      0xC058, 0xC056, 0xC05A, 0,      0xC057, 0,      0,
    0,      0,      0,      0xEF45, 0,      0xEF4A, 0xEF46, 0xEF49, 0xC1FB,
    0,      0xEDD4, 0xEF48, 0xEF47, 0,      0xC344, 0xC342, 0xC345, 0xC343,
    0xF1A8, 0xF1A9, 0xF1AA, 0xC346, 0,      0,      0,      0xF3AA, 0xC440,
    0xF3A8, 0,      0xC441, 0xF3A7, 0xF3A9, 0xC3FE, 0xF551, 0xF54E, 0,
    0xF54F, 0xF550, 0xF672, 0xC556, 0,      0xC555, 0,      0xF774, 0xF773,
    0xC5B8, 0,      0,      0,      0xC5E3, 0xC649, 0xC660, 0xF958, 0xF9AE,
    0xF9AF, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0xADB7, 0xDCAD,
    0,      0,      0xE0E1, 0xE4CC, 0xE4CD, 0xBBE3, 0,      0xBBE4, 0xE8BE,
    0xBE68, 0,      0,      0xC1FC, 0,      0xF1AB, 0,      0xC347, 0xF3AD,
    0xC442, 0xF3AC, 0xF3AE, 0xF3AB, 0xF675, 0xF552, 0xF553, 0,      0xC4C6,
    0,      0xF674, 0,      0,      0xF673, 0,      0xF775, 0xF9B0, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0xADB8, 0,      0,      0,      0xADB9,
    0,      0,      0xB0A7, 0xD448, 0,      0xD84F, 0,      0xB6B8, 0,
    0xB6BB, 0xB6B9, 0xDCAE, 0,      0xB6BD, 0,      0xB6BA, 0,      0,
    0xB6BC, 0,      0xB97E, 0,      0xE0E2, 0,      0,      0xE0E3, 0xE8C0,
    0,      0xB97D, 0xB9A1, 0xB9A2, 0,      0xE4CF, 0,      0xE4CE, 0xBBE5,
    0,      0xBBE6, 0,      0xE4D0, 0xE8BF, 0xBBE8, 0xBE69, 0,      0xBBE7,
    0,      0,      0,      0xC05C, 0xE8C1, 0xBE6B, 0xBE6A, 0xE8C2, 0xE8C5,
    0xE8C3, 0xE8C4, 0xBE6C, 0,      0xC061, 0xC05F, 0,      0,      0xC05E,
    0xEC5D, 0,      0xC060, 0,      0,      0xEC5C, 0xEF4B, 0,      0xEC5E,
    0xC05D, 0xEC5F, 0xEF4E, 0xEF4C, 0xEF4D, 0xEF52, 0xC34B, 0xEF51, 0xEF54,
    0xEF53, 0xEF50, 0xEF4F, 0,      0xC1FD, 0,      0,      0,      0,
    0xF1AE, 0,      0xF1AD, 0xC34A, 0xC348, 0xC349, 0,      0xF1AC, 0,
    0xF3B1, 0,      0xC443, 0,      0xF3B0, 0xF3AF, 0xC444, 0,      0xF558,
    0xF557, 0,      0xF555, 0,      0xF554, 0xC4C8, 0xC4C7, 0xF559, 0xF776,
    0xC5B9, 0xF677, 0xC557, 0xF676, 0xF556, 0,      0xF777, 0xC5E4, 0,
    0xC661, 0xF959, 0,      0xF9B1, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0xADBA, 0xD850, 0xEF55, 0xADBB, 0,      0,      0xE4D2,
    0xE4D1, 0xEC60, 0,      0,      0xEF57, 0,      0xEF56, 0,      0xC34C,
    0xF3B2, 0xF3B3, 0xC4C9, 0,      0,      0xF9B2, 0xB0A8, 0xB6BF, 0xB6BE,
    0xE0E4, 0xE0E6, 0xB9A4, 0xE0E5, 0xB9A3, 0xB9A5, 0xE0E7, 0,      0,
    0,      0xE4D4, 0xE4D6, 0xE4D5, 0,      0xE4D8, 0,      0,      0,
    0xBBE9, 0xE4D7, 0xE4D3, 0,      0,      0,      0xE4D9, 0,      0xE8CC,
    0,      0xE8CF, 0xE8D1, 0xE8C7, 0xE8CB, 0xE8C8, 0xBE6E, 0xBE71, 0xBE73,
    0xE8C9, 0xE8CA, 0xBE72, 0xE8CD, 0xE8D0, 0xE8CE, 0xBE74, 0,      0xBE70,
    0xE8C6, 0xBE6D, 0,      0xBE6F, 0,      0,      0xC063, 0xEC66, 0xEC64,
    0xEC63, 0,      0xEC69, 0,      0xEC68, 0xEC67, 0,      0xEC62, 0xC062,
    0xEC61, 0,      0xEC65, 0xC064, 0,      0,      0xEF5A, 0,      0xEF5E,
    0xEF5B, 0xEF5D, 0xEF5C, 0xEF59, 0xEF5F, 0xEF62, 0xEF60, 0xEF61, 0xC240,
    0,      0xC1FE, 0xEF58, 0xEF63, 0xF1B3, 0xF1B6, 0xF1B8, 0xF1B7, 0,
    0xF1B1, 0xF1B5, 0xF1B0, 0,      0xF1B2, 0xC34D, 0xF1AF, 0,      0xF1B4,
    0,      0,      0xF3C0, 0xF3B5, 0xC445, 0,      0,      0xC446, 0xF3B4,
    0xF3B9, 0xF3BF, 0xF3B7, 0xF3BE, 0,      0xF3BB, 0,      0xF3BA, 0xF3BD,
    0xF3B8, 0xF3B6, 0,      0xF3BC, 0,      0xF560, 0xF55E, 0xC4CA, 0xF55D,
    0xF563, 0xF561, 0,      0xC4CB, 0xF55C, 0xF55A, 0,      0xF55B, 0xC4CD,
    0xF55F, 0xC4CC, 0xF562, 0xF678, 0xF67E, 0,      0,      0xF679, 0xC55B,
    0xF6A1, 0xC55A, 0xF67D, 0xF67C, 0xC559, 0xF67B, 0xC558, 0xF67A, 0,
    0xF77D, 0xF7A1, 0xF77E, 0,      0xF77B, 0xC5BB, 0xF778, 0xF77C, 0xF7A3,
    0,      0xF7A2, 0xF779, 0xF77A, 0xC5BA, 0xF852, 0xC5E7, 0,      0xF853,
    0xC5E5, 0xC5E6, 0,      0,      0xF8D3, 0xC64A, 0xF976, 0,      0xC66A,
    0,      0xF9B3, 0xC66B, 0xF9B4, 0xF9B5, 0xF9C3, 0xF9C2, 0xC67A, 0xF9CD,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0xB0A9, 0,      0,
    0xE0E9, 0,      0xE0E8, 0,      0xBBEA, 0xBBEB, 0xE4DA, 0,      0xE8D2,
    0xEC6C, 0,      0,      0xBE75, 0xC065, 0xEC6A, 0,      0xEC6D, 0xC066,
    0,      0xEF64, 0xEC6B, 0xF1B9, 0xC34E, 0xF3C1, 0,      0,      0,
    0xF566, 0xF564, 0,      0,      0xF565, 0,      0,      0xF6A2, 0,
    0xC55C, 0xF7A4, 0xC5EA, 0xC5BC, 0xC5E8, 0xC5E9, 0xF8D4, 0xC662, 0,
    0xB0AA, 0,      0,      0,      0xF1BA, 0,      0,      0xD449, 0,
    0xB9A6, 0,      0xE4DB, 0,      0,      0xBBEC, 0xE4DC, 0,      0,
    0,      0xE8D4, 0xE8D3, 0xC068, 0xBE76, 0xBE77, 0,      0xE8D7, 0xE8D6,
    0xE8D5, 0,      0,      0xEC6E, 0xEC71, 0,      0xEC70, 0xEC6F, 0xC067,
    0xEF68, 0xEF66, 0xEF65, 0,      0,      0xEF67, 0,      0xC34F, 0xF1BC,
    0xF1BD, 0xC350, 0,      0xF1BB, 0,      0xF3C3, 0xF3C2, 0xF3C5, 0xC447,
    0xF3C4, 0,      0xF567, 0xF569, 0xF568, 0,      0,      0xF6A3, 0xF6A6,
    0xF6A4, 0xF6A5, 0xF7A5, 0xC5BD, 0,      0,      0,      0xF854, 0xF855,
    0xF856, 0,      0xC64B, 0xC663, 0xF9B6, 0xB0AB, 0,      0xBE78, 0xC069,
    0xF1BE, 0,      0xF7A6, 0,      0,      0xF9C4, 0xD44A, 0,      0xC67B,
    0xB0AC, 0xEC72, 0,      0xF1BF, 0,      0xF3C6, 0,      0,      0xF6A7,
    0xF7A7, 0xB0AD, 0,      0xE4DD, 0xE4DE, 0,      0xBBED, 0xBBEE, 0xE8D9,
    0xBE7A, 0xBE79, 0xE8D8, 0,      0xEF69, 0,      0xF1C0, 0xF1C2, 0xF1C1,
    0xC353, 0xC352, 0xC351, 0,      0xC55E, 0xF6A8, 0,      0xC55D, 0xF7A9,
    0xF7A8, 0,      0xC64C, 0xF8D5, 0xB3BD, 0xE0EA, 0,      0,      0,
    0xE4E1, 0xE4DF, 0xE4E0, 0,      0,      0xE8E2, 0,      0xE8DD, 0xE8DA,
    0xE8E1, 0,      0,      0,      0xE8E3, 0,      0,      0xBE7C, 0xE8E0,
    0xE8DC, 0,      0,      0xE8DB, 0xE8DF, 0xE8DE, 0xBE7B, 0,      0,
    0xEC7D, 0xEC78, 0xEC76, 0xECA1, 0xEC77, 0,      0xEC73, 0,      0xEC79,
    0,      0,      0xEC74, 0xEF72, 0xEC75, 0xECA2, 0,      0,      0,
    0,      0,      0,      0,      0xEC7C, 0xC06A, 0xEC7B, 0xEC7A, 0,
    0xEC7E, 0,      0,      0,      0,      0xEF6A, 0xEF6D, 0,      0,
    0xEF6C, 0,      0xEF74, 0xEF6F, 0xEF73, 0,      0xEF71, 0xEF70, 0xEF6E,
    0,      0xEF6B, 0,      0xC243, 0xC242, 0,      0xC244, 0xC241, 0xEF75,
    0,      0,      0,      0,      0,      0xF1C8, 0xF1CB, 0,      0xF1C9,
    0xF1CD, 0,      0,      0,      0xF1CE, 0,      0xF1C6, 0xC358, 0xF1C7,
    0,      0xF1C5, 0xF1CC, 0,      0xF1C4, 0xF1C3, 0xC357, 0xC355, 0xC354,
    0,      0,      0,      0,      0,      0,      0,      0,      0xF1CA,
    0xF3CF, 0xF3D5, 0xC44A, 0xF3D0, 0,      0xF3D3, 0xF3D7, 0xC44B, 0xF3D2,
    0,      0xF3CA, 0,      0xF3C9, 0xF3D6, 0xF3CD, 0,      0xF3CB, 0xF3D4,
    0xF3CC, 0xC449, 0xC448, 0,      0xF3C7, 0xF3C8, 0xF3D1, 0,      0,
    0,      0xF3CE, 0,      0,      0,      0,      0,      0,      0xF56C,
    0xF56F, 0,      0,      0,      0,      0xC356, 0,      0,      0,
    0,      0,      0,      0,      0xF56D, 0xF573, 0xF571, 0xF56B, 0xF576,
    0,      0xF56A, 0,      0xC4CF, 0xF572, 0,      0,      0,      0xF56E,
    0xC4CE, 0xF575, 0,      0,      0xF574, 0,      0,      0,      0,
    0xF6AB, 0xF6AA, 0,      0,      0,      0xF6B1, 0,      0xF6AD, 0xF6B0,
    0xC560, 0,      0,      0xF6AE, 0xF6AF, 0,      0xF6A9, 0xF6AC, 0xC55F,
    0,      0,      0,      0xC5BF, 0xF7B4, 0xF7AF, 0xF7B3, 0,      0xF7B6,
    0xF7B2, 0,      0xF7AE, 0,      0xC5C1, 0xF7B1, 0xF7B5, 0xC5C0, 0xF7AC,
    0xF570, 0xF7B0, 0,      0,      0xF7AD, 0,      0xF7AA, 0,      0xF7AB,
    0xC5BE, 0xF85A, 0xF85C, 0xF85F, 0xF85B, 0xF860, 0,      0xF859, 0,
    0xF857, 0,      0xC5EB, 0xF85D, 0xC5ED, 0xC5EC, 0xF858, 0xF85E, 0,
    0,      0,      0,      0xF8DA, 0xC64D, 0xF8DB, 0,      0xF8D9, 0xF8D6,
    0,      0,      0xF8D8, 0xF8D7, 0xF95A, 0,      0,      0,      0,
    0xF95C, 0xF95B, 0,      0,      0xF979, 0,      0xF978, 0xF977, 0xF97A,
    0,      0xC673, 0xC674, 0xF9CA, 0xF9CE, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0xB3BE, 0xDCAF, 0xE0ED, 0,      0xB9A7, 0xE0EB,
    0,      0,      0xE0EC, 0,      0,      0,      0xE4E2, 0xE4E3, 0xBBF1,
    0xBBEF, 0xE4E4, 0xBBF0, 0xE8E8, 0,      0xE8EB, 0xE8E5, 0xE8EC, 0xE8E4,
    0xE8E6, 0,      0xE8E7, 0xE8EA, 0,      0,      0xBEA1, 0xE8EF, 0xE8EE,
    0xBE7D, 0xE8E9, 0xE8ED, 0xBE7E, 0,      0,      0,      0,      0,
    0,      0xECAC, 0,      0xC06F, 0,      0xECA7, 0xC06B, 0,      0xECA4,
    0xECAA, 0xECAD, 0,      0xC070, 0,      0xECA9, 0xECA6, 0xECAE, 0xECA5,
    0,      0xECAB, 0xC06C, 0,      0xECA3, 0xC06D, 0,      0xC06E, 0xECA8,
    0,      0,      0,      0xEFA9, 0xEF7A, 0xEF7B, 0xEF7E, 0xEF7C, 0,
    0xEF76, 0,      0,      0xEF79, 0xEFA5, 0xEF7D, 0,      0,      0xC245,
    0,      0xEFA7, 0xEFA4, 0xC246, 0xEFA6, 0xEF77, 0xEFA2, 0xEFA3, 0,
    0xEFA1, 0,      0,      0,      0,      0xF1D2, 0xF1D4, 0xF1D7, 0,
    0,      0xF1D1, 0,      0xC359, 0xF1D9, 0xF1D0, 0xF1DA, 0,      0xF1D6,
    0xF1D8, 0xF1DC, 0xF1D5, 0xF1DD, 0xF1D3, 0xF1CF, 0xC35A, 0,      0xF1DB,
    0xC35B, 0xC44D, 0,      0,      0,      0,      0,      0xEF78, 0xF3F1,
    0xF3E8, 0xC44F, 0xF3E4, 0xC450, 0,      0,      0xF3ED, 0xF3E7, 0xF3DD,
    0xC44E, 0xF3EA, 0xF3E5, 0xF3E6, 0,      0xF3D8, 0xF3DF, 0xF3EE, 0,
    0xF3EB, 0,      0xF3E3, 0,      0xF3EF, 0xF3DE, 0xF3D9, 0xF3EC, 0,
    0xF3DB, 0xF3E9, 0xF3E0, 0xF3F0, 0xF3DC, 0xC44C, 0xF3DA, 0xF3E1, 0xF3E2,
    0,      0,      0,      0xF57D, 0,      0xF57B, 0,      0xF5A2, 0,
    0xF5AE, 0xF5A5, 0xF57C, 0xF578, 0xF5A7, 0xF57E, 0xF5A3, 0xF57A, 0xF5AA,
    0xF577, 0xF5A1, 0xF5A6, 0xF5A8, 0xF5AB, 0xF579, 0,      0xF5AF, 0xF5B0,
    0xF5A9, 0xF5AD, 0xF5A4, 0,      0xF6C1, 0xF6C4, 0,      0xC561, 0,
    0xF6C3, 0xF6C8, 0xF6C6, 0xC562, 0xF6BD, 0xF6B3, 0xF6B2, 0xC564, 0xF6BF,
    0xF6C0, 0xF6BC, 0xF6B4, 0,      0xF6B9, 0xF5AC, 0,      0xF6B5, 0xC563,
    0xF6BB, 0,      0xF6BA, 0,      0xF6B6, 0xF6C2, 0,      0xF6B7, 0xF7BB,
    0xF6C5, 0xF6C7, 0xF6BE, 0xF6B8, 0xF7BC, 0xF7BE, 0xF7B8, 0xC5C2, 0,
    0xF7C5, 0xF7C3, 0xC5C3, 0xF7C2, 0xF7C1, 0xF7BA, 0xF7B7, 0xF7BD, 0xF7C6,
    0xF7B9, 0xF7BF, 0,      0xF869, 0xF86E, 0xF864, 0xF867, 0xC5EE, 0xF86B,
    0,      0xF872, 0xF7C0, 0,      0xF865, 0xF86F, 0xF873, 0xF86A, 0xF863,
    0xF86D, 0,      0xF86C, 0xF871, 0xF870, 0xF7C4, 0xF868, 0xF862, 0xF866,
    0xC64E, 0xC64F, 0xF861, 0,      0xF8E6, 0xF8DD, 0xF8E5, 0xF8E2, 0xF8E3,
    0xF8DC, 0xF8DF, 0xF8E7, 0xF8E1, 0xF8E0, 0xF8DE, 0,      0xF8E4, 0,
    0xF95D, 0,      0xF95E, 0,      0xF960, 0xF95F, 0xF962, 0xF961, 0xF97C,
    0xF97B, 0xF9B7, 0,      0xF9B8, 0,      0xF9C5, 0xC678, 0xC67C, 0,
    0xF9CF, 0xC67D, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0xB3BF, 0,
    0,      0,      0xC4D0, 0xF6C9, 0,      0xC650, 0xC651, 0,      0xB3C0,
    0xE0EE, 0,      0xB9A8, 0xE8F0, 0,      0,      0xECB0, 0xECB1, 0xECAF,
    0xEFAB, 0xEFAA, 0xC247, 0xF1DF, 0xEFAC, 0xF1DE, 0,      0,      0xF3F3,
    0xC451, 0xC453, 0xF3F2, 0,      0,      0xC452, 0,      0xF5B1, 0xF5B3,
    0xF5B2, 0xF6CA, 0xC565, 0,      0xC5EF, 0xF8E8, 0xF963, 0,      0,
    0xF9D2, 0xB3C1, 0,      0xE4E5, 0,      0xBEA2, 0,      0,      0,
    0xECB3, 0xECB2, 0,      0xEFAD, 0,      0,      0,      0xC454, 0xC4D1,
    0xF7C7, 0xF9CB, 0,      0,      0,      0xB3C2, 0xBBF2, 0,      0xBEA3,
    0,      0xF3F4, 0,      0xF874, 0xB6C0, 0,      0,      0,      0,
    0xEFAE, 0,      0,      0,      0xC664, 0xB6C1, 0xBEA4, 0xC248, 0xF875,
    0xB6C2, 0,      0xE8F1, 0xC072, 0xECB4, 0xECB5, 0,      0xC071, 0,
    0xEFAF, 0xC24C, 0xC24A, 0xC24B, 0xC249, 0xF1E0, 0xC35C, 0,      0,
    0,      0xF5B5, 0xF5B4, 0xF5B7, 0xF5B6, 0xC4D2, 0,      0,      0xF6CB,
    0,      0xF6CD, 0xF6CC, 0xC566, 0xF7C8, 0,      0xF876, 0xF877, 0xC5F0,
    0xF964, 0xF97D, 0xC675, 0,      0xDCB0, 0xECB6, 0xEFB0, 0xF3F5, 0xE0EF,
    0,      0xEFB1, 0xF1E2, 0xF1E1, 0,      0,      0,      0,      0xF878,
    0xC652, 0,      0xF965, 0xF97E, 0,      0,      0,      0xB9A9, 0xE8F2,
    0xE8F3, 0,      0xECB7, 0xB9AA, 0,      0xC35D, 0xF1E3, 0,      0xF6CF,
    0xC567, 0xF6D0, 0xF6CE, 0xF879, 0,      0xF8E9, 0,      0xB9AB, 0,
    0xEFB4, 0xEFB3, 0xEFB2, 0xF1E4, 0,      0,      0xF1E8, 0xF1E7, 0xF1E6,
    0xF1E5, 0xC35E, 0xF3F6, 0xF5B9, 0xC4D3, 0xF5B8, 0xF6D1, 0xF7CB, 0xF7CA,
    0xC5C4, 0xF7C9, 0xF87C, 0xF87B, 0xF87A, 0,      0,      0xBBF3, 0,
    0xECB8, 0xC24D, 0,      0xF3F7, 0xF3F8, 0xF7CC, 0xF87D, 0,      0,
    0xF8EA, 0xF966, 0xF9B9, 0xF9D4, 0xBBF4, 0xC24E, 0xF1E9, 0xF3F9, 0xF6D2,
    0xF87E, 0,      0,      0xBEA6, 0,      0xEFB5, 0xF1EA, 0xF3FA, 0xF3FB,
    0xF3FC, 0xF5BE, 0,      0xF5BA, 0xC568, 0xF5BD, 0xF5BC, 0xC4D4, 0xF5BB,
    0xC4D6, 0,      0xC4D5, 0xF6D4, 0xF6D3, 0xC569, 0xC56A, 0,      0,
    0xC5C6, 0xF7CD, 0xC5C5, 0,      0xF8A3, 0xF8A4, 0xF8A2, 0xF8A1, 0xC654,
    0,      0xF8EB, 0xF8EC, 0xF8ED, 0xC653, 0xF967, 0xF96A, 0xF969, 0xF968,
    0,      0,      0xF9D3, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0xC073,
    0,      0,      0xC365, 0xF5BF, 0xF6D5, 0,      0xC5C7, 0xF7CE, 0,
    0,      0xF9D5, 0,      0,      0,      0xC074, 0,      0,      0,
    0xEFB6, 0,      0xF7CF, 0,      0xF9A1};

/* page 9 0xFA0C-0xFA0D */
static const uint16_t tab_uni_big59[] = {0xC94A, 0xDDFC};

/* page 10 0xFE30-0xFFFD */
static const uint16_t tab_uni_big510[] = {
    0xA14A, 0xA157, 0,      0xA159, 0xA15B, 0xA15F, 0xA160, 0xA163, 0xA164,
    0xA167, 0xA168, 0xA16B, 0xA16C, 0xA16F, 0xA170, 0xA173, 0xA174, 0xA177,
    0xA178, 0xA17B, 0xA17C, 0,      0,      0,      0,      0xA1C6, 0xA1C7,
    0xA1CA, 0xA1CB, 0xA1C8, 0xA1C9, 0xA15C, 0xA14D, 0,      0xA14F, 0,
    0xA151, 0xA152, 0xA153, 0xA154, 0,      0xA17D, 0xA17E, 0xA1A1, 0xA1A2,
    0xA1A3, 0xA1A4, 0xA1CC, 0xA1CD, 0xA1CE, 0xA1DE, 0xA1DF, 0xA1E0, 0xA1E1,
    0xA1E2, 0,      0,      0xA24C, 0xA24D, 0xA24E, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0xA149, 0,      0xA1AD, 0xA243, 0xA248, 0xA1AE, 0,
    0xA15D, 0xA15E, 0xA1AF, 0xA1CF, 0xA141, 0xA1D0, 0xA144, 0xA241, 0xA2AF,
    0xA2B0, 0xA2B1, 0xA2B2, 0xA2B3, 0xA2B4, 0xA2B5, 0xA2B6, 0xA2B7, 0xA2B8,
    0xA147, 0xA146, 0xA1D5, 0xA1D7, 0xA1D6, 0xA148, 0xA249, 0xA2CF, 0xA2D0,
    0xA2D1, 0xA2D2, 0xA2D3, 0xA2D4, 0xA2D5, 0xA2D6, 0xA2D7, 0xA2D8, 0xA2D9,
    0xA2DA, 0xA2DB, 0xA2DC, 0xA2DD, 0xA2DE, 0xA2DF, 0xA2E0, 0xA2E1, 0xA2E2,
    0xA2E3, 0xA2E4, 0xA2E5, 0xA2E6, 0xA2E7, 0xA2E8, 0,      0xA242, 0,
    0,      0xA1C4, 0,      0xA2E9, 0xA2EA, 0xA2EB, 0xA2EC, 0xA2ED, 0xA2EE,
    0xA2EF, 0xA2F0, 0xA2F1, 0xA2F2, 0xA2F3, 0xA2F4, 0xA2F5, 0xA2F6, 0xA2F7,
    0xA2F8, 0xA2F9, 0xA2FA, 0xA2FB, 0xA2FC, 0xA2FD, 0xA2FE, 0xA340, 0xA341,
    0xA342, 0xA343, 0xA161, 0xA155, 0xA162, 0,      0,      0,      0,
    0,      0,      0xA14E, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0xA2CE};

static int func_uni_big5_onechar(int code) {
  if ((code >= 0x00A2) && (code <= 0x00F7))
    return (tab_uni_big50[code - 0x00A2]);
  if ((code >= 0x02C7) && (code <= 0x0451))
    return (tab_uni_big51[code - 0x02C7]);
  if ((code >= 0x2013) && (code <= 0x22BF))
    return (tab_uni_big52[code - 0x2013]);
  if ((code >= 0x2460) && (code <= 0x2642))
    return (tab_uni_big53[code - 0x2460]);
  if ((code >= 0x3000) && (code <= 0x3129))
    return (tab_uni_big54[code - 0x3000]);
  if ((code >= 0x32A3) && (code <= 0x32A3))
    return (tab_uni_big55[code - 0x32A3]);
  if ((code >= 0x338E) && (code <= 0x33D5))
    return (tab_uni_big56[code - 0x338E]);
  if ((code >= 0x4E00) && (code <= 0x9483))
    return (tab_uni_big57[code - 0x4E00]);
  if ((code >= 0x9577) && (code <= 0x9FA4))
    return (tab_uni_big58[code - 0x9577]);
  if ((code >= 0xFA0C) && (code <= 0xFA0D))
    return (tab_uni_big59[code - 0xFA0C]);
  if ((code >= 0xFE30) && (code <= 0xFFFD))
    return (tab_uni_big510[code - 0xFE30]);
  return (0);
}

extern "C" {
static int my_wc_mb_big5(const CHARSET_INFO *cs [[maybe_unused]], my_wc_t wc,
                         uint8_t *s, uint8_t *e) {
  int code;

  if (s >= e) return MY_CS_TOOSMALL;

  if ((int)wc < 0x80) {
    s[0] = (uint8_t)wc;
    return 1;
  }

  if (!(code = func_uni_big5_onechar(wc))) return MY_CS_ILUNI;

  if (s + 2 > e) return MY_CS_TOOSMALL;

  s[0] = code >> 8;
  s[1] = code & 0xFF;

  return 2;
}

static int my_mb_wc_big5(const CHARSET_INFO *cs [[maybe_unused]], my_wc_t *pwc,
                         const uint8_t *s, const uint8_t *e) {
  int hi;

  if (s >= e) return MY_CS_TOOSMALL;

  if ((hi = s[0]) < 0x80) {
    pwc[0] = hi;
    return 1;
  }

  if (s + 2 > e) return MY_CS_TOOSMALL2;

  if (!(pwc[0] = func_big5_uni_onechar((hi << 8) + s[1]))) return -2;

  return 2;
}

/*
  Returns a well formed length of a BIG5 string.
  CP950 and HKSCS additional characters are also accepted.
*/
static size_t my_well_formed_len_big5(const CHARSET_INFO *cs [[maybe_unused]],
                                      const char *b, const char *e, size_t pos,
                                      int *error) {
  const char *b0 = b;
  const char *emb = e - 1; /* Last possible end of an MB character */

  *error = 0;
  while (pos-- && b < e) {
    if ((uint8_t)b[0] < 128) {
      /* Single byte ascii character */
      b++;
    } else if ((b < emb) && isbig5code((uint8_t)*b, (uint8_t)b[1])) {
      /* Double byte character */
      b += 2;
    } else {
      /* Wrong byte sequence */
      *error = 1;
      break;
    }
  }
  return (size_t)(b - b0);
}
}  // extern "C"

static MY_COLLATION_HANDLER my_collation_big5_chinese_ci_handler = {
    nullptr, /* init */
    nullptr,
    my_strnncoll_big5,
    my_strnncollsp_big5,
    my_strnxfrm_big5,
    my_strnxfrmlen_simple,
    my_like_range_mb,
    my_wildcmp_mb,
    my_strcasecmp_mb,
    my_instr_mb,
    my_hash_sort_simple,
    my_propagate_simple};

static MY_CHARSET_HANDLER my_charset_big5_handler = {nullptr, /* init */
                                                     ismbchar_big5,
                                                     mbcharlen_big5,
                                                     my_numchars_mb,
                                                     my_charpos_mb3,
                                                     my_well_formed_len_big5,
                                                     my_lengthsp_8bit,
                                                     my_numcells_8bit,
                                                     my_mb_wc_big5, /* mb_wc */
                                                     my_wc_mb_big5, /* wc_mb */
                                                     my_mb_ctype_mb,
                                                     my_caseup_str_mb,
                                                     my_casedn_str_mb,
                                                     my_caseup_mb,
                                                     my_casedn_mb,
                                                     my_snprintf_8bit,
                                                     my_long10_to_str_8bit,
                                                     my_longlong10_to_str_8bit,
                                                     my_fill_8bit,
                                                     my_strntol_8bit,
                                                     my_strntoul_8bit,
                                                     my_strntoll_8bit,
                                                     my_strntoull_8bit,
                                                     my_strntod_8bit,
                                                     my_strtoll10_8bit,
                                                     my_strntoull10rnd_8bit,
                                                     my_scan_8bit};

CHARSET_INFO my_charset_big5_chinese_ci = {
    1,
    0,
    0,                                               /* number     */
    MY_CS_COMPILED | MY_CS_PRIMARY | MY_CS_STRNXFRM, /* state      */
    "big5",                                          /* cs name    */
    "big5_chinese_ci",                               /* m_coll_name */
    "Big5 Traditional Chinese",                      /* comment    */
    nullptr,                                         /* tailoring */
    nullptr,                                         /* coll_param */
    ctype_big5,
    to_lower_big5,
    to_upper_big5,
    sort_order_big5,
    nullptr,           /* uca          */
    nullptr,           /* tab_to_uni   */
    nullptr,           /* tab_from_uni */
    &my_caseinfo_big5, /* caseinfo     */
    nullptr,           /* state_map    */
    nullptr,           /* ident_map    */
    1,                 /* strxfrm_multiply */
    1,                 /* caseup_multiply  */
    1,                 /* casedn_multiply  */
    1,                 /* mbminlen   */
    2,                 /* mbmaxlen   */
    1,                 /* mbmaxlenlen */
    0,                 /* min_sort_char */
    0xF9D5,            /* max_sort_char */
    ' ',               /* pad char      */
    true,              /* escape_with_backslash_is_dangerous */
    1,                 /* levels_for_compare */
    &my_charset_big5_handler,
    &my_collation_big5_chinese_ci_handler,
    PAD_SPACE};

CHARSET_INFO my_charset_big5_bin = {
    84,
    0,
    0,                              /* number     */
    MY_CS_COMPILED | MY_CS_BINSORT, /* state */
    "big5",                         /* cs name    */
    "big5_bin",                     /* m_coll_name */
    "Big5 Traditional Chinese",     /* comment    */
    nullptr,                        /* tailoring */
    nullptr,                        /* coll_param */
    ctype_big5,
    to_lower_big5,
    to_upper_big5,
    nullptr,           /* sort_order   */
    nullptr,           /* uca          */
    nullptr,           /* tab_to_uni   */
    nullptr,           /* tab_from_uni */
    &my_caseinfo_big5, /* caseinfo     */
    nullptr,           /* state_map    */
    nullptr,           /* ident_map    */
    1,                 /* strxfrm_multiply */
    1,                 /* caseup_multiply  */
    1,                 /* casedn_multiply  */
    1,                 /* mbminlen   */
    2,                 /* mbmaxlen   */
    1,                 /* mbmaxlenlen */
    0,                 /* min_sort_char */
    0xF9FE,            /* max_sort_char */
    ' ',               /* pad char      */
    true,              /* escape_with_backslash_is_dangerous */
    1,                 /* levels_for_compare */
    &my_charset_big5_handler,
    &my_collation_mb_bin_handler,
    PAD_SPACE};
