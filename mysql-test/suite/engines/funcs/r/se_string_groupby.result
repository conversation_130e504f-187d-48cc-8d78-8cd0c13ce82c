DROP TABLE IF EXISTS t1;
#------------------------------
# Test CHAR 'a'
#------------------------------
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'a';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
a	81	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
a	81	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
a	a	18	9
a	b	27	9
a	c	18	4
a	d	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
a	a	18	9
a	b	27	9
a	c	18	4
a	d	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	a	18	9
b	a	27	9
c	a	18	4
d	a	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	a	18	9
b	a	27	9
c	a	18	4
d	a	18	5
DROP TABLE t1;
#------------------------------
# Test CHAR 'b'
#------------------------------
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'b';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
b	99	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
b	99	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
b	a	27	9
b	b	18	3
b	c	18	6
b	e	18	4
b	z	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
b	a	27	9
b	b	18	3
b	c	18	6
b	e	18	4
b	z	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	b	27	9
b	b	18	3
c	b	18	6
e	b	18	4
z	b	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	b	27	9
b	b	18	3
c	b	18	6
e	b	18	4
z	b	18	5
DROP TABLE t1;
#------------------------------
# Test CHAR 'c'
#------------------------------
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'c';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
c	90	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
c	90	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
c	a	9	5
c	b	9	5
c	c	36	9
c	d	18	9
c	f	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
c	a	9	5
c	b	9	5
c	c	36	9
c	d	18	9
c	f	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	c	9	5
b	c	9	5
c	c	36	9
d	c	18	9
f	c	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	c	9	5
b	c	9	5
c	c	36	9
d	c	18	9
f	c	18	5
DROP TABLE t1;
#------------------------------
# Test CHAR 'd'
#------------------------------
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'd';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test CHAR 'e'
#------------------------------
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'e';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test CHAR 'f'
#------------------------------
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'f';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test CHAR 'y'
#------------------------------
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'y';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test VARCHAR 'a'
#------------------------------
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'a';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
a	81	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
a	81	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
a	a	18	9
a	b	27	9
a	c	18	4
a	d	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
a	a	18	9
a	b	27	9
a	c	18	4
a	d	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	a	18	9
b	a	27	9
c	a	18	4
d	a	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	a	18	9
b	a	27	9
c	a	18	4
d	a	18	5
DROP TABLE t1;
#------------------------------
# Test VARCHAR 'b'
#------------------------------
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'b';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
b	99	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
b	99	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
b	a	27	9
b	b	18	3
b	c	18	6
b	e	18	4
b	z	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
b	a	27	9
b	b	18	3
b	c	18	6
b	e	18	4
b	z	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	b	27	9
b	b	18	3
c	b	18	6
e	b	18	4
z	b	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	b	27	9
b	b	18	3
c	b	18	6
e	b	18	4
z	b	18	5
DROP TABLE t1;
#------------------------------
# Test VARCHAR 'c'
#------------------------------
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'c';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
c	90	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
c	90	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
c	a	9	5
c	b	9	5
c	c	36	9
c	d	18	9
c	f	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
c	a	9	5
c	b	9	5
c	c	36	9
c	d	18	9
c	f	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	c	9	5
b	c	9	5
c	c	36	9
d	c	18	9
f	c	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	c	9	5
b	c	9	5
c	c	36	9
d	c	18	9
f	c	18	5
DROP TABLE t1;
#------------------------------
# Test VARCHAR 'd'
#------------------------------
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'd';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test VARCHAR 'e'
#------------------------------
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'e';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test VARCHAR 'f'
#------------------------------
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'f';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test VARCHAR 'y'
#------------------------------
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'y';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test BINARY 'a'
#------------------------------
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'a';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
a	81	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
a	81	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
a	a	18	9
a	b	27	9
a	c	18	4
a	d	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
a	a	18	9
a	b	27	9
a	c	18	4
a	d	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	a	18	9
b	a	27	9
c	a	18	4
d	a	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	a	18	9
b	a	27	9
c	a	18	4
d	a	18	5
DROP TABLE t1;
#------------------------------
# Test BINARY 'b'
#------------------------------
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'b';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
b	99	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
b	99	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
b	a	27	9
b	b	18	3
b	c	18	6
b	e	18	4
b	z	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
b	a	27	9
b	b	18	3
b	c	18	6
b	e	18	4
b	z	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	b	27	9
b	b	18	3
c	b	18	6
e	b	18	4
z	b	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	b	27	9
b	b	18	3
c	b	18	6
e	b	18	4
z	b	18	5
DROP TABLE t1;
#------------------------------
# Test BINARY 'c'
#------------------------------
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'c';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
c	90	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
c	90	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
c	a	9	5
c	b	9	5
c	c	36	9
c	d	18	9
c	f	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
c	a	9	5
c	b	9	5
c	c	36	9
c	d	18	9
c	f	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	c	9	5
b	c	9	5
c	c	36	9
d	c	18	9
f	c	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	c	9	5
b	c	9	5
c	c	36	9
d	c	18	9
f	c	18	5
DROP TABLE t1;
#------------------------------
# Test BINARY 'd'
#------------------------------
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'd';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test BINARY 'e'
#------------------------------
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'e';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test BINARY 'f'
#------------------------------
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'f';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test BINARY 'y'
#------------------------------
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'y';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test VARBINARY 'a'
#------------------------------
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'a';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
a	81	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
a	81	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
a	a	18	9
a	b	27	9
a	c	18	4
a	d	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
a	a	18	9
a	b	27	9
a	c	18	4
a	d	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	a	18	9
b	a	27	9
c	a	18	4
d	a	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	a	18	9
b	a	27	9
c	a	18	4
d	a	18	5
DROP TABLE t1;
#------------------------------
# Test VARBINARY 'b'
#------------------------------
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'b';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
b	99	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
b	99	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
b	a	27	9
b	b	18	3
b	c	18	6
b	e	18	4
b	z	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
b	a	27	9
b	b	18	3
b	c	18	6
b	e	18	4
b	z	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	b	27	9
b	b	18	3
c	b	18	6
e	b	18	4
z	b	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	b	27	9
b	b	18	3
c	b	18	6
e	b	18	4
z	b	18	5
DROP TABLE t1;
#------------------------------
# Test VARBINARY 'c'
#------------------------------
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'c';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
c	90	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
c	90	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
c	a	9	5
c	b	9	5
c	c	36	9
c	d	18	9
c	f	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
c	a	9	5
c	b	9	5
c	c	36	9
c	d	18	9
c	f	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	c	9	5
b	c	9	5
c	c	36	9
d	c	18	9
f	c	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	c	9	5
b	c	9	5
c	c	36	9
d	c	18	9
f	c	18	5
DROP TABLE t1;
#------------------------------
# Test VARBINARY 'd'
#------------------------------
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'd';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test VARBINARY 'e'
#------------------------------
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'e';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test VARBINARY 'f'
#------------------------------
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'f';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test VARBINARY 'y'
#------------------------------
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'y';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test BLOB 'a'
#------------------------------
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'a';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
a	81	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
a	81	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
a	a	18	9
a	b	27	9
a	c	18	4
a	d	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
a	a	18	9
a	b	27	9
a	c	18	4
a	d	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	a	18	9
b	a	27	9
c	a	18	4
d	a	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	a	18	9
b	a	27	9
c	a	18	4
d	a	18	5
DROP TABLE t1;
#------------------------------
# Test BLOB 'b'
#------------------------------
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'b';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
b	99	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
b	99	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
b	a	27	9
b	b	18	3
b	c	18	6
b	e	18	4
b	z	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
b	a	27	9
b	b	18	3
b	c	18	6
b	e	18	4
b	z	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	b	27	9
b	b	18	3
c	b	18	6
e	b	18	4
z	b	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	b	27	9
b	b	18	3
c	b	18	6
e	b	18	4
z	b	18	5
DROP TABLE t1;
#------------------------------
# Test BLOB 'c'
#------------------------------
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'c';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
c	90	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
c	90	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
c	a	9	5
c	b	9	5
c	c	36	9
c	d	18	9
c	f	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
c	a	9	5
c	b	9	5
c	c	36	9
c	d	18	9
c	f	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	c	9	5
b	c	9	5
c	c	36	9
d	c	18	9
f	c	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	c	9	5
b	c	9	5
c	c	36	9
d	c	18	9
f	c	18	5
DROP TABLE t1;
#------------------------------
# Test BLOB 'd'
#------------------------------
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'd';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test BLOB 'e'
#------------------------------
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'e';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test BLOB 'f'
#------------------------------
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'f';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test BLOB 'y'
#------------------------------
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'y';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test TEXT 'a'
#------------------------------
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'a';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
a	81	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
a	81	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
a	a	18	9
a	b	27	9
a	c	18	4
a	d	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
a	a	18	9
a	b	27	9
a	c	18	4
a	d	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	a	18	9
b	a	27	9
c	a	18	4
d	a	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	a	18	9
b	a	27	9
c	a	18	4
d	a	18	5
DROP TABLE t1;
#------------------------------
# Test TEXT 'b'
#------------------------------
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'b';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
b	99	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
b	99	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
b	a	27	9
b	b	18	3
b	c	18	6
b	e	18	4
b	z	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
b	a	27	9
b	b	18	3
b	c	18	6
b	e	18	4
b	z	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	b	27	9
b	b	18	3
c	b	18	6
e	b	18	4
z	b	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	b	27	9
b	b	18	3
c	b	18	6
e	b	18	4
z	b	18	5
DROP TABLE t1;
#------------------------------
# Test TEXT 'c'
#------------------------------
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'c';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
c	90	9
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
c	90	9
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
c	a	9	5
c	b	9	5
c	c	36	9
c	d	18	9
c	f	18	5
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
c	a	9	5
c	b	9	5
c	c	36	9
c	d	18	9
c	f	18	5
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	c	9	5
b	c	9	5
c	c	36	9
d	c	18	9
f	c	18	5
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
a	c	9	5
b	c	9	5
c	c	36	9
d	c	18	9
f	c	18	5
DROP TABLE t1;
#------------------------------
# Test TEXT 'd'
#------------------------------
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'd';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test TEXT 'e'
#------------------------------
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'e';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test TEXT 'f'
#------------------------------
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'f';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
#------------------------------
# Test TEXT 'y'
#------------------------------
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
set @value := 'e';
SELECT c1 FROM t1 GROUP BY c1;
c1
a
b
c
SELECT c1,COUNT(*) FROM t1 GROUP BY c1;
c1	COUNT(*)
a	81
b	99
c	90
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1;
c1	COUNT(*)	MAX(c3)
SELECT c1,c2 FROM t1 GROUP BY c1,c2;
c1	c2
a	a
a	b
a	c
a	d
b	a
b	b
b	c
b	e
b	z
c	a
c	b
c	c
c	d
c	f
SELECT c1,c2,COUNT(*) FROM t1 GROUP BY c1,c2;
c1	c2	COUNT(*)
a	a	18
a	b	27
a	c	18
a	d	18
b	a	27
b	b	18
b	c	18
b	e	18
b	z	18
c	a	9
c	b	9
c	c	36
c	d	18
c	f	18
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c1,c2,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c1,c2;
c1	c2	COUNT(*)	MAX(c3)
SELECT c2,c1 FROM t1 GROUP BY c2,c1;
c2	c1
a	a
a	b
a	c
b	a
b	b
b	c
c	a
c	b
c	c
d	a
d	c
e	b
f	c
z	b
SELECT c2,c1,COUNT(*) FROM t1 GROUP BY c2,c1;
c2	c1	COUNT(*)
a	a	18
a	b	27
a	c	9
b	a	27
b	b	18
b	c	9
c	a	18
c	b	18
c	c	36
d	a	18
d	c	18
e	b	18
f	c	18
z	b	18
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
SELECT c2,c1,COUNT(*),MAX(c3) FROM t1 WHERE c1=@value OR c2 = '#C2' GROUP BY c2,c1;
c2	c1	COUNT(*)	MAX(c3)
DROP TABLE t1;
