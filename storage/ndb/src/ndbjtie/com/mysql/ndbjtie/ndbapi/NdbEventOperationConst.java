/*
  Copyright (c) 2010, 2025, Oracle and/or its affiliates.

  This program is free software; you can redistribute it and/or modify
  it under the terms of the GNU General Public License, version 2.0,
  as published by the Free Software Foundation.

  This program is designed to work with certain software (including
  but not limited to OpenSSL) that is licensed under separate terms,
  as designated in a particular file or component or in included license
  documentation.  The authors of MySQL hereby grant you an additional
  permission to link the program and your derivative works with the
  separately licensed software that they have either included with
  the program or referenced in the documentation.

  This program is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License, version 2.0, for more details.

  You should have received a copy of the GNU General Public License
  along with this program; if not, write to the Free Software
  Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
*/
/*
 * NdbEventOperationConst.java
 */

package com.mysql.ndbjtie.ndbapi;

import java.nio.ByteBuffer;

import com.mysql.jtie.Wrapper;

public interface NdbEventOperationConst
{
    int isOverrun() /*_const_*/;
    boolean isConsistent() /*_const_*/;
    int/*_NdbDictionary.Event.TableEvent_*/ getEventType() /*_const_*/;
    boolean tableNameChanged() /*_const_*/;
    boolean tableFrmChanged() /*_const_*/;
    boolean tableFragmentationChanged() /*_const_*/;
    boolean tableRangeListChanged() /*_const_*/;
    long/*_Uint64_*/ getGCI() /*_const_*/;
    int/*_Uint32_*/ getAnyValue() /*_const_*/;
    long/*_Uint64_*/ getLatestGCI() /*_const_*/;
    NdbErrorConst/*_const NdbError &_*/ getNdbError() /*_const_*/;
}
