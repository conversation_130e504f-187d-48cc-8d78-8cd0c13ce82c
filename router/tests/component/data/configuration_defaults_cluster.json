{"common": {"accept_external_connections": true, "bind_address": "0.0.0.0", "client_connect_timeout": 9, "client_ssl_cipher": "", "client_ssl_curves": "", "client_ssl_dh_params": "", "client_ssl_mode": "PREFERRED", "connect_timeout": 5, "connection_sharing": false, "connection_sharing_delay": 1.0, "max_connect_errors": 100, "max_connections": 0, "max_idle_server_connections": 64, "max_total_connections": 512, "name": "system", "net_buffer_length": 16384, "read_timeout": 30, "server_ssl_ca": "", "server_ssl_capath": "", "server_ssl_cipher": "", "server_ssl_crl": "", "server_ssl_crlpath": "", "server_ssl_curves": "", "server_ssl_mode": "PREFERRED", "server_ssl_verify": "DISABLED", "socket": "", "thread_stack_size": 1024, "unknown_config_option": "error", "wait_for_my_writes": true, "wait_for_my_writes_timeout": 2}, "connection_pool": {"idle_timeout": 5, "max_idle_server_connections": 64}, "destination_status": {"error_quarantine_interval": 1, "error_quarantine_threshold": 1}, "endpoints": {"bootstrap_ro": {"accept_external_connections": true, "bind_address": "0.0.0.0", "bind_port": 6447, "client_connect_timeout": 9, "client_ssl_cipher": "", "client_ssl_curves": "", "client_ssl_dh_params": "", "client_ssl_mode": "PREFERRED", "connect_timeout": 5, "connection_sharing": false, "connection_sharing_delay": 1.0, "destinations": "metadata-cache://my-cluster/?role=SECONDARY", "max_connect_errors": 100, "max_connections": 0, "net_buffer_length": 16384, "protocol": "classic", "router_require_enforce": true, "routing_strategy": "round-robin-with-fallback", "server_ssl_ca": "", "server_ssl_capath": "", "server_ssl_cipher": "", "server_ssl_crl": "", "server_ssl_crlpath": "", "server_ssl_curves": "", "server_ssl_mode": "PREFERRED", "server_ssl_verify": "DISABLED", "socket": "", "thread_stack_size": 1024, "wait_for_my_writes": true, "wait_for_my_writes_timeout": 2}, "bootstrap_rw": {"accept_external_connections": true, "bind_address": "0.0.0.0", "bind_port": 6446, "client_connect_timeout": 9, "client_ssl_cipher": "", "client_ssl_curves": "", "client_ssl_dh_params": "", "client_ssl_mode": "PREFERRED", "connect_timeout": 5, "connection_sharing": false, "connection_sharing_delay": 1.0, "destinations": "metadata-cache://my-cluster/?role=PRIMARY", "max_connect_errors": 100, "max_connections": 0, "net_buffer_length": 16384, "protocol": "classic", "router_require_enforce": true, "routing_strategy": "first-available", "server_ssl_ca": "", "server_ssl_capath": "", "server_ssl_cipher": "", "server_ssl_crl": "", "server_ssl_crlpath": "", "server_ssl_curves": "", "server_ssl_mode": "PREFERRED", "server_ssl_verify": "DISABLED", "socket": "", "thread_stack_size": 1024, "wait_for_my_writes": true, "wait_for_my_writes_timeout": 2}, "bootstrap_rw_split": {"accept_external_connections": true, "access_mode": "auto", "bind_address": "0.0.0.0", "bind_port": 6450, "client_connect_timeout": 9, "client_ssl_cipher": "", "client_ssl_curves": "", "client_ssl_dh_params": "", "client_ssl_mode": "PREFERRED", "connect_timeout": 5, "connection_sharing": true, "connection_sharing_delay": 1.0, "destinations": "metadata-cache://my-cluster/?role=PRIMARY_AND_SECONDARY", "max_connect_errors": 100, "max_connections": 0, "net_buffer_length": 16384, "protocol": "classic", "router_require_enforce": true, "routing_strategy": "round-robin", "server_ssl_ca": "", "server_ssl_capath": "", "server_ssl_cipher": "", "server_ssl_crl": "", "server_ssl_crlpath": "", "server_ssl_curves": "", "server_ssl_mode": "PREFERRED", "server_ssl_verify": "DISABLED", "socket": "", "thread_stack_size": 1024, "wait_for_my_writes": true, "wait_for_my_writes_timeout": 2}, "bootstrap_x_ro": {"accept_external_connections": true, "bind_address": "0.0.0.0", "bind_port": 6449, "client_connect_timeout": 9, "client_ssl_cipher": "", "client_ssl_curves": "", "client_ssl_dh_params": "", "client_ssl_mode": "PREFERRED", "connect_timeout": 5, "connection_sharing": false, "connection_sharing_delay": 1.0, "destinations": "metadata-cache://my-cluster/?role=SECONDARY", "max_connect_errors": 100, "max_connections": 0, "net_buffer_length": 16384, "protocol": "x", "router_require_enforce": false, "routing_strategy": "round-robin-with-fallback", "server_ssl_ca": "", "server_ssl_capath": "", "server_ssl_cipher": "", "server_ssl_crl": "", "server_ssl_crlpath": "", "server_ssl_curves": "", "server_ssl_mode": "PREFERRED", "server_ssl_verify": "DISABLED", "socket": "", "thread_stack_size": 1024, "wait_for_my_writes": true, "wait_for_my_writes_timeout": 2}, "bootstrap_x_rw": {"accept_external_connections": true, "bind_address": "0.0.0.0", "bind_port": 6448, "client_connect_timeout": 9, "client_ssl_cipher": "", "client_ssl_curves": "", "client_ssl_dh_params": "", "client_ssl_mode": "PREFERRED", "connect_timeout": 5, "connection_sharing": false, "connection_sharing_delay": 1.0, "destinations": "metadata-cache://my-cluster/?role=PRIMARY", "max_connect_errors": 100, "max_connections": 0, "net_buffer_length": 16384, "protocol": "x", "router_require_enforce": false, "routing_strategy": "first-available", "server_ssl_ca": "", "server_ssl_capath": "", "server_ssl_cipher": "", "server_ssl_crl": "", "server_ssl_crlpath": "", "server_ssl_curves": "", "server_ssl_mode": "PREFERRED", "server_ssl_verify": "DISABLED", "socket": "", "thread_stack_size": 1024, "wait_for_my_writes": true, "wait_for_my_writes_timeout": 2}}, "http_authentication_backends": {"default_auth_backend": {"backend": "metadata_cache", "filename": ""}}, "http_authentication_realm": {"backend": "default_auth_backend", "method": "basic", "name": "default_realm", "require": "valid-user"}, "http_server": {"bind_address": "0.0.0.0", "port": 8443, "require_realm": "", "ssl": true, "ssl_cert": "", "ssl_cipher": "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_CCM_SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES256-CCM:ECDHE-ECDSA-AES128-CCM:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-CCM:DHE-RSA-AES256-CCM:DHE-RSA-CHACHA20-POLY1305", "ssl_curves": "", "ssl_dh_params": "", "ssl_key": "", "static_folder": ""}, "io": {"threads": 0}, "loggers": {"filelog": {"destination": "", "filename": "mysqlrouter.log", "level": "info", "timestamp_precision": "second"}}, "metadata_cache": {"auth_cache_refresh_interval": 2.0, "auth_cache_ttl": -1.0, "close_connection_after_refresh": false, "connect_timeout": 5, "read_timeout": 30, "ssl_ca": "", "ssl_capath": "", "ssl_cipher": "", "ssl_crl": "", "ssl_crlpath": "", "ssl_mode": "PREFERRED", "thread_stack_size": 1024, "tls_version": "", "ttl": 0.5, "use_gr_notifications": false}, "rest_configs": {"rest_api": {"require_realm": ""}, "rest_metadata_cache": {"require_realm": "default_auth_realm"}, "rest_router": {"require_realm": "default_auth_realm"}, "rest_routing": {"require_realm": "default_auth_realm"}}, "routing_rules": {"invalidated_cluster_policy": "drop_all", "read_only_targets": "secondaries", "stats_updates_frequency": -1, "unreachable_quorum_allowed_traffic": "none", "use_replica_primary_as_rw": false}}