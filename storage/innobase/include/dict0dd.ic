/*****************************************************************************

Copyright (c) 2016, 2025, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License, version 2.0, as published by the
Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON><PERSON>ITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0,
for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

*****************************************************************************/

/** @file include/dict0dd.ic
 Data dictionary access

 Created  10/2016 Jimmy Yamg
 *******************************************************/

#include "sql_table.h"
#ifndef UNIV_HOTBACKUP
#include "dict0crea.h"
#include "dict0priv.h"
#include "field.h"
#include "sql/table.h"
#include "sql_base.h"

/** Get the explicit dd::Tablespace::id of a partition.
@param[in]      partition       partition or subpartition
@return the explicit dd::Tablespace::id
@retval dd::INVALID_OBJECT_ID   if there is no explicit tablespace */
inline dd::Object_id dd_get_space_id(const dd::Partition &partition) {
  ut_ad(dd_part_is_stored(&partition));
  ut_ad(dd_table_is_partitioned(partition.table()));
  ut_ad((partition.table().subpartition_type() == dd::Table::ST_NONE) ==
        (partition.parent() == nullptr));

  dd::Object_id id = partition.tablespace_id();
  if (id == dd::INVALID_OBJECT_ID) {
    if (const dd::Partition *parent = partition.parent()) {
      /* If there is no explicit TABLESPACE for the
      subpartition, fall back to the TABLESPACE
      of the partition. */
      id = parent->tablespace_id();
    }
  }
  if (id == dd::INVALID_OBJECT_ID) {
    /* If there is no explicit TABLESPACE for the partition,
    fall back to the TABLESPACE of the table. */
    id = partition.table().tablespace_id();
  }
  return (id);
}

/** Look up a column in a table using the system_charset_info collation.
@param[in]      dd_table        data dictionary table
@param[in]      name            column name
@return the column
@retval nullptr if not found */
inline const dd::Column *dd_find_column(const dd::Table *dd_table,
                                        const char *name) {
  for (const dd::Column *c : dd_table->columns()) {
    if (!my_strcasecmp(system_charset_info, c->name().c_str(), name)) {
      return (c);
    }
  }
  return (nullptr);
}

/** Add a hidden column when creating a table.
@param[in,out]  dd_table        table containing user columns and indexes
@param[in]      name            hidden column name
@param[in]      length          length of the column, in bytes
@param[in]      type            column type
@return the added column, or NULL if there already was a column by that name */
inline dd::Column *dd_add_hidden_column(dd::Table *dd_table, const char *name,
                                        uint length,
                                        dd::enum_column_types type) {
  if (const dd::Column *c = dd_find_column(dd_table, name)) {
    my_error(ER_WRONG_COLUMN_NAME, MYF(0), c->name().c_str());
    return (nullptr);
  }

  dd::Column *col = dd_table->add_column();
  col->set_hidden(dd::Column::enum_hidden_type::HT_HIDDEN_SE);
  col->set_name(name);
  col->set_type(type);
  col->set_nullable(false);
  col->set_char_length(length);
  col->set_collation_id(my_charset_bin.number);

  return (col);
}

inline bool dd_drop_hidden_column(dd::Table *dd_table, const char *name) {
#ifdef UNIV_DEBUG
  const dd::Column *col = dd_find_column(dd_table, name);
  ut_ad(col != nullptr);
  ut_ad(dd_column_is_dropped(col));
#endif
  return dd_table->drop_column(name);
}

/** Add a hidden index element at the end.
@param[in,out]  index   created index metadata
@param[in]      column  column of the index */
inline void dd_add_hidden_element(dd::Index *index, const dd::Column *column) {
  dd::Index_element *e = index->add_element(const_cast<dd::Column *>(column));
  e->set_hidden(true);
  e->set_order(dd::Index_element::ORDER_ASC);
}

/** Initialize a hidden unique B-tree index.
@param[in,out]  index   created index metadata
@param[in]      name    name of the index
@param[in]      column  column of the index
@return the initialized index */
inline dd::Index *dd_set_hidden_unique_index(dd::Index *index, const char *name,
                                             const dd::Column *column) {
  index->set_name(name);
  index->set_hidden(true);
  index->set_algorithm(dd::Index::IA_BTREE);
  index->set_type(dd::Index::IT_UNIQUE);
  index->set_engine(innobase_hton_name);
  dd_add_hidden_element(index, column);
  return (index);
}

inline dict_table_t *dd_table_open_on_id_in_mem(table_id_t table_id,
                                                bool dict_locked) {
  dict_table_t *table;

  if (!dict_locked) {
    dict_sys_mutex_enter();
  }

  ut_ad(dict_sys_mutex_own());

  /* Look for the table ID in the hash table */
  const auto table_id_hash_value = ut::hash_uint64(table_id);

  HASH_SEARCH(id_hash, dict_sys->table_id_hash, table_id_hash_value,
              dict_table_t *, table, ut_ad(table->cached),
              table->id == table_id);

  ut_ad(table == nullptr || table->cached);

  if (table != nullptr) {
    table->acquire();
  }

  if (!dict_locked) {
    dict_sys_mutex_exit();
  }

  return (table);
}

/** Returns a cached table object based on table name.
@param[in]      name            table name
@param[in]      dict_locked     true=data dictionary locked
@return table, NULL if does not exist */
inline dict_table_t *dd_table_open_on_name_in_mem(const char *name,
                                                  bool dict_locked) {
  dict_table_t *table = nullptr;

  if (!dict_locked) {
    dict_sys_mutex_enter();
  }

  table = dict_table_check_if_in_cache_low(name);

  ut_ad(!table || table->cached);

  if (table != nullptr) {
    if (table->can_be_evicted) {
      dict_move_to_mru(table);
    }

    table->acquire_with_lock();
  }

  if (!dict_locked) {
    dict_sys_mutex_exit();
  }

  return (table);
}

/** Check whether there exist a column named as "FTS_DOC_ID", which is
reserved for InnoDB FTS Doc ID
@param[in]      thd             MySQL thread handle
@param[in]      form            information on table
                                columns and indexes
@param[out]     doc_id_col      Doc ID column number if
                                there exist a FTS_DOC_ID column,
                                ULINT_UNDEFINED if column is of the
                                wrong type/name/size
@return true if there exist a "FTS_DOC_ID" column */
inline bool create_table_check_doc_id_col(THD *thd, const TABLE *form,
                                          ulint *doc_id_col) {
  for (ulint i = 0; i < form->s->fields; i++) {
    const Field *field;
    ulint col_type;
    ulint col_len;
    ulint unsigned_type;

    field = form->field[i];
    if (!field->stored_in_db) continue;

    col_type = get_innobase_type_from_mysql_type(&unsigned_type, field);

    col_len = field->pack_length();

    if (innobase_strcasecmp(field->field_name, FTS_DOC_ID_COL_NAME) == 0) {
      /* Note the name is case sensitive due to
      our internal query parser */
      if (col_type == DATA_INT && !field->is_nullable() &&
          col_len == sizeof(doc_id_t) &&
          (strcmp(field->field_name, FTS_DOC_ID_COL_NAME) == 0)) {
        *doc_id_col = i;
      } else {
        push_warning_printf(thd, Sql_condition::SL_WARNING,
                            ER_ILLEGAL_HA_CREATE_OPTION,
                            "InnoDB: FTS_DOC_ID column must be"
                            " of BIGINT NOT NULL type, and named"
                            " in all capitalized characters");
        my_error(ER_WRONG_COLUMN_NAME, MYF(0), field->field_name);
        *doc_id_col = ULINT_UNDEFINED;
      }

      return (true);
    }
  }

  return (false);
}

/** Return a display name for the row format
@param[in]      row_format      Row Format
@return row format name */
inline const char *get_row_format_name(enum row_type row_format) {
  switch (row_format) {
    case ROW_TYPE_COMPACT:
      return ("ROW_FORMAT=COMPACT");
    case ROW_TYPE_COMPRESSED:
      return ("ROW_FORMAT=COMPRESSED");
    case ROW_TYPE_DYNAMIC:
      return ("ROW_FORMAT=DYNAMIC");
    case ROW_TYPE_REDUNDANT:
      return ("ROW_FORMAT=REDUNDANT");
    case ROW_TYPE_DEFAULT:
      return ("ROW_FORMAT=DEFAULT");
    case ROW_TYPE_FIXED:
      return ("ROW_FORMAT=FIXED");
    case ROW_TYPE_PAGED:
      return ("ROW_FORMAT=PAGED");
    case ROW_TYPE_NOT_USED:
      break;
  }
  return ("ROW_FORMAT");
}

/** Acquire a shared metadata lock.
@param[in,out]  thd     current thread
@param[out]     mdl     metadata lock
@param[in]      db      schema name
@param[in]      table   table name
@retval false if acquired
@retval true if failed (my_error() will have been called) */
[[nodiscard]] static inline bool dd_mdl_acquire(THD *thd, MDL_ticket **mdl,
                                                const char *db,
                                                const char *table) {
  bool ret = false;
  char table_name[MAX_TABLE_NAME_LEN + 1];
  const char *table_p = table;

  char db_name[MAX_DATABASE_NAME_LEN + 1];
  const char *db_p = db;

  if (innobase_get_lower_case_table_names() == 2) {
    ulint len = strlen(table);
    ut_memcpy(table_name, table, len);
    table_name[len] = '\0';
    innobase_casedn_str(table_name);
    table_p = table_name;

    len = strlen(db);
    ut_memcpy(db_name, db, len);
    db_name[len] = '\0';
    innobase_casedn_str(db_name);
    db_p = db_name;
  } else {
#ifndef _WIN32
    if (innobase_get_lower_case_table_names() == 1) {
      ulint len = strlen(table);
      ut_memcpy(table_name, table, len);
      table_name[len] = '\0';
      innobase_casedn_str(table_name);
      table_p = table_name;

      len = strlen(db);
      ut_memcpy(db_name, db, len);
      db_name[len] = '\0';
      innobase_casedn_str(db_name);
      db_p = db_name;
    }
#endif /* !_WIN32 */
  }

  ret = dd::acquire_shared_table_mdl(thd, db_p, table_p, false, mdl);

  return (ret);
}

/** Get the version attribute.
@param[in]      dd_table        dd::Table
@return table dynamic metadata version if exists, otherwise 0 */
inline uint64_t dd_get_version(const dd::Table *dd_table) {
  uint64_t version = 0;
  const dd::Properties &p = dd_table->se_private_data();

  if (!p.exists(dd_table_key_strings[DD_TABLE_VERSION]) ||
      p.get(dd_table_key_strings[DD_TABLE_VERSION],
            reinterpret_cast<uint64_t *>(&version))) {
    return (0);
  }

  return (version);
}
#endif /* !UNIV_HOTBACKUP */
