##################### mysql-test\t\binlog_order_commits_basic.test #############
#                                                                              #
# Variable Name: binlog_order_commits                                          #
# Scope: GLOBAL                                                                #
# Access Type: Dynamic                                                         #
# Data Type: BOOLEAN                                                           #
# Default Value: FALSE                                                         #
# Valid Values: TRUE, FALSE                                                    #
#                                                                              #
#                                                                              #
# Creation Date: 2012-05-10                                                    #
# Author:  Nuno Carvalho                                                       #
#                                                                              #
# Description: Test Cases of Dynamic System Variable "binlog_order_commits"    #
#              that checks behavior of this variable in the following ways     #
#              * Default Value                                                 #
#              * Valid & Invalid values                                        #
#              * Scope & Access method                                         #
#              * Data Integrity                                                #
#                                                                              #
# Reference: http://dev.mysql.com/doc/refman/5.1/en/                           #
#              server-options.html#option_mysqld_event-scheduler               #
#                                                                              #
################################################################################

--source include/load_sysvars.inc

###########################################################
#           START OF binlog_order_commits TESTS           #
###########################################################


############################################################################
#   Saving initial value of binlog_order_commits in a temporary variable   #
############################################################################

SET @start_value = @@global.binlog_order_commits;
SELECT @start_value;


--echo '#---------------------FN_DYNVARS_004_01-------------------------#'
###############################################
#     Verify default value of variable        #
###############################################

SET @@global.binlog_order_commits = DEFAULT;
SELECT @@global.binlog_order_commits = FALSE;


--echo '#--------------------FN_DYNVARS_004_02------------------------#'
######################################################################
#        Change the value of binlog_order_commits to a valid value   #
######################################################################

SET @@global.binlog_order_commits = ON;
SELECT @@global.binlog_order_commits;
SET @@global.binlog_order_commits = OFF;
SELECT @@global.binlog_order_commits;

--echo '#--------------------FN_DYNVARS_004_03-------------------------#'
######################################################################
#        Change the value of binlog_order_commits to invalid value   #
######################################################################

--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.binlog_order_commits = 2;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.binlog_order_commits = -1;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.binlog_order_commits = TRUEF;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.binlog_order_commits = TRUE_F;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.binlog_order_commits = FALSE0;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.binlog_order_commits = OON;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.binlog_order_commits = ONN;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.binlog_order_commits = OOFF;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.binlog_order_commits = 0FF;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.binlog_order_commits = ' ';
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.binlog_order_commits = " ";
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.binlog_order_commits = '';


--echo '#-------------------FN_DYNVARS_004_04----------------------------#'
########################################################################
#         Test if accessing session binlog_order_commits gives error   #
########################################################################

--Error ER_GLOBAL_VARIABLE
SET @@session.binlog_order_commits = OFF;
--Error ER_INCORRECT_GLOBAL_LOCAL_VAR
SELECT @@session.binlog_order_commits;


--echo '#----------------------FN_DYNVARS_004_05------------------------#'
##############################################################################
# Check if the value in GLOBAL Tables matches values in variable             #
##############################################################################

--disable_warnings
SELECT IF(@@global.binlog_order_commits, "ON", "OFF") = VARIABLE_VALUE 
FROM performance_schema.global_variables 
WHERE VARIABLE_NAME='binlog_order_commits';
--enable_warnings


--echo '#---------------------FN_DYNVARS_004_06----------------------#'
################################################################
#        Check if 0 and 1 values can be used on variable       #
################################################################

SET @@global.binlog_order_commits = 0;
SELECT @@global.binlog_order_commits;
SET @@global.binlog_order_commits = 1;
SELECT @@global.binlog_order_commits;

--echo '#---------------------FN_DYNVARS_004_07----------------------#'
################################################################### 
#      Check if TRUE and FALSE values can be used on variable     #
################################################################### 

SET @@global.binlog_order_commits = TRUE;
SELECT @@global.binlog_order_commits;
SET @@global.binlog_order_commits = FALSE;
SELECT @@global.binlog_order_commits;

--echo '#---------------------FN_DYNVARS_004_08----------------------#'
##############################################################################
#    Check if accessing variable with SESSION,LOCAL and without SCOPE points #
#    to same session variable                                                #
##############################################################################

SET @@global.binlog_order_commits = ON;
SELECT @@binlog_order_commits = @@global.binlog_order_commits;

--echo '#---------------------FN_DYNVARS_004_09----------------------#'
######################################################################
#   Check if binlog_order_commits can be accessed with and without @@ sign #
######################################################################
--Error ER_GLOBAL_VARIABLE
SET binlog_order_commits = ON;
--Error ER_PARSE_ERROR
SET local.binlog_order_commits = OFF;
--Error ER_UNKNOWN_TABLE
SELECT local.binlog_order_commits;
--Error ER_PARSE_ERROR
SET global.binlog_order_commits = ON;
--Error ER_UNKNOWN_TABLE
SELECT global.binlog_order_commits;
--Error ER_BAD_FIELD_ERROR
SELECT binlog_order_commits;
--Error ER_INCORRECT_GLOBAL_LOCAL_VAR
SELECT @@session.binlog_order_commits;



##############################  
#   Restore initial value    #
##############################

SET @@global.binlog_order_commits = @start_value;
SELECT @@global.binlog_order_commits;


####################################################
#       END OF binlog_order_commits TESTS          #
####################################################
