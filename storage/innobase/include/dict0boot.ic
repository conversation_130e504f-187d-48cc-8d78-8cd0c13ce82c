/*****************************************************************************

Copyright (c) 1996, 2025, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License, version 2.0, as published by the
Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON><PERSON>ITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0,
for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

*****************************************************************************/

/** @file include/dict0boot.ic
 Data dictionary creation and booting

 Created 4/18/1996 Heikki Tuuri
 *******************************************************/

/** Returns a new row id.
 @return the new id */
static inline row_id_t dict_sys_get_new_row_id(void) {
  row_id_t id;

  dict_sys_mutex_enter();

  id = dict_sys->row_id;

  if (0 == (id % DICT_HDR_ROW_ID_WRITE_MARGIN)) {
    dict_hdr_flush_row_id();
  }

  dict_sys->row_id++;

  dict_sys_mutex_exit();

  return (id);
}

/** Reads a row id from a record or other 6-byte stored form.
 @return row id */
static inline row_id_t dict_sys_read_row_id(
    const byte *field) /*!< in: record field */
{
  static_assert(DATA_ROW_ID_LEN == 6, "DATA_ROW_ID_LEN != 6");

  return (mach_read_from_6(field));
}

/** Writes a row id to a record or other 6-byte stored form. */
static inline void dict_sys_write_row_id(byte *field, /*!< in: record field */
                                         row_id_t row_id) /*!< in: row id */
{
  static_assert(DATA_ROW_ID_LEN == 6, "DATA_ROW_ID_LEN != 6");

  mach_write_to_6(field, row_id);
}

/** Check if a table id belongs to old innodb internal system table.
@param[in]      id              table id
@return true if the table id belongs to a system table. */
static inline bool dict_is_old_sys_table(table_id_t id) {
  if (srv_is_upgrade_mode) {
    return (id < DICT_HDR_FIRST_ID);
  }
  return (false);
}
