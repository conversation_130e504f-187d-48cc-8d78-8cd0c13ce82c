include/group_replication.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection server1]

# 1. Setup group of 2 servers, M1(primary) and M2(secondary).
SET @debug_save_m1= @@GLOBAL.DEBUG;
CREATE TABLE t1(c1 int primary key);
include/start_and_bootstrap_group_replication.inc
[connection server2]
SET @debug_save_m2= @@GLOBAL.DEBUG;
include/start_group_replication.inc

# 2. Execute a transaction on server1 and block it in begin.
#    It is yet not known if a transaction will be binlogged or not.
[connection server1]
SET @@GLOBAL.DEBUG= '+d,group_replication_wait_on_observer_trans';
INSERT INTO t1 values(1);
[connection server_1]
SET DEBUG_SYNC= "now WAIT_FOR signal.group_replication_wait_on_observer_trans_waiting";
SET @@GLOBAL.DEBUG= '-d,group_replication_wait_on_observer_trans';

# 3. Begin change of primary from server2(secondary) with timeout of 1800.
[connection server2]
SELECT group_replication_set_as_primary("SERVER2_UUID", 1800);;
[connection server_1]

# 4. Now unblock transaction on server1.
[connection server_1]
SET DEBUG_SYNC= "now SIGNAL signal.group_replication_wait_on_observer_trans_continue";

# 5. Reap transaction running on server1.
#    It should pass since it will move to commit state during timeout.
[connection server1]
SET GLOBAL DEBUG= @debug_save_m1;
[connection server_2]
SET GLOBAL DEBUG= @debug_save_m2;
SET DEBUG_SYNC= 'RESET';
[connection server2]
group_replication_set_as_primary("SERVER2_UUID", 1800)
Primary server switched to: SERVER2_UUID

# 6. Assert primary changed.
#    Assert data is present on M1 and M2.
[connection server2]
include/gr_assert_primary_member.inc
[connection server1]
include/gr_assert_secondary_member.inc
include/assert.inc ['There is a value 1 in table t1.']
include/diff_tables.inc [server1:test.t1, server2:test.t1]

# 7. Execute a transaction on server2 and block it in begin.
#    It is yet not known if a transaction will be binlogged or not.
[connection server2]
SET @@GLOBAL.DEBUG= '+d,group_replication_wait_on_observer_trans';
INSERT INTO t1 values(2);
[connection server_2]
SET DEBUG_SYNC= "now WAIT_FOR signal.group_replication_wait_on_observer_trans_waiting";
SET @@GLOBAL.DEBUG= '-d,group_replication_wait_on_observer_trans';

# 8. Begin change of primary from server2(primary) with timeout of 1800.
[connection server_2]
SELECT group_replication_set_as_primary("SERVER1_UUID", 1800);;
[connection server_2_1]

# 9. Now unblock transaction on server2.
[connection server_2_1]
SET DEBUG_SYNC= "now SIGNAL signal.group_replication_wait_on_observer_trans_continue";

# 10. Reap transaction running on server2.
#     It should pass since it will move to commit state during timeout.
#     Also reap the change primary operation it should succeed.
[connection server2]
[connection server_2_1]
SET DEBUG_SYNC= 'RESET';
[connection server_2]
group_replication_set_as_primary("SERVER1_UUID", 1800)
Primary server switched to: SERVER1_UUID

# 11. Assert primary changed.
#     Assert data is present on M1 and M2.
[connection server1]
include/gr_assert_primary_member.inc
[connection server2]
include/gr_assert_secondary_member.inc
include/assert.inc ['There is a value 2 in table t1.']
include/diff_tables.inc [server1:test.t1, server2:test.t1]

# 12. Cleanup.
[connection server1]
DROP TABLE t1;
include/group_replication_end.inc
