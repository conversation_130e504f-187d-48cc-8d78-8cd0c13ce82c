# Main objective of this test is verification that session created by
# one auth-app may be used with its services
#
# 1. Verify that authentication endpoints are created and avaiable
# despite the number of authentication application assgined to
# service.
# 2. Verifiy that session can be shared by multiple services
#

--source include/have_router.inc
--source ../include/mrs/is_mrs_schema_v3_or_v4.inc

--let $extra_mrs_router_id=1
--source ../include/predefined_setup/configure_router_mrs_root.inc
--source ../include/schema/basic_schema.sql

create user mrsuser@'%' identified by 'S3kre7';
grant all on *.* to mrsuser@'%';

## Test starts here
--echo
--echo
--echo ## I. Create three servcices and three authentication application,
--echo ##    assign them that first covers service 1,2, second covers service 2,3,
--echo ##    third covers services 1,2,3:
--echo #
--echo # 1. Verify that services, point correct authapp in /authentication/authApps endpoints.
--echo # 2. Verify that authentication to each of those services can be done with one of assigned
--echo #    auth-apps (and remember created session).
--echo #
--echo ## II. Verify if the auth-app sessions can be used with assigned services:
--echo #
--echo # 1. Verify that session created with "first" auth-app is usable with service 1,2, and fails with service 3.
--echo # 2. Verify that session created with "first" auth-app is usable with service 2,3, and fails with service 1.
--echo # 3. Verify that session created with "first" auth-app is usable with service 1,2,3.
--echo #
--echo #


--echo
--echo #
--echo # I.1

--source ../include/mrs/start_object_definition.inc

############ A
--let $mrs_add_service_path="/a"
--let $mrs_add_host_name=""
--source ../include/mrs/service/add.inc

--let $mrs_add_schema=basic_schema
--let $mrs_add_schema_path=/sch
--let $mrs_add_schema_auth=1
--source ../include/mrs/db_schema/add.inc

--let $mrs_add_db_object=table1
--let $mrs_add_db_object_path=/t
--source ../include/mrs/db_object/add.inc

############ B
--let $mrs_add_service_path="/b"
--let $mrs_add_host_name=""
--source ../include/mrs/service/add.inc

--let $mrs_add_schema=basic_schema
--let $mrs_add_schema_path=/sch
--let $mrs_add_schema_auth=1
--source ../include/mrs/db_schema/add.inc

--let $mrs_add_db_object=table1
--let $mrs_add_db_object_path=/t
--source ../include/mrs/db_object/add.inc

############ C
--let $mrs_add_service_path="/c"
--let $mrs_add_host_name=""
--source ../include/mrs/service/add.inc


--let $mrs_add_schema=basic_schema
--let $mrs_add_schema_path=/sch
--let $mrs_add_schema_auth=1
--source ../include/mrs/db_schema/add.inc

--let $mrs_add_db_object=table1
--let $mrs_add_db_object_path=/t
--source ../include/mrs/db_object/add.inc


--let $mrs_add_auth_app=first
--let $mrs_add_auth_vendor=MySQL Internal
--let $mrs_add_auth_service=/a|/b
--source ../include/mrs/auth_app/add.inc

--let $mrs_add_auth_app=second
--let $mrs_add_auth_vendor=MySQL Internal
--let $mrs_add_auth_service=/b|/c
--source ../include/mrs/auth_app/add.inc

--let $mrs_add_auth_app=third
--let $mrs_add_auth_vendor=MySQL Internal
--let $mrs_add_auth_service=/a|/b|/c
--source ../include/mrs/auth_app/add.inc

--source ../include/mrs/end_object_definition.inc

--echo
--echo #
--echo # I.1
--let $mrs_client_arg_path='/a/authentication/authApps'
--source ../include/mrs/mrs_client.inc

--let $mrs_client_arg_path='/b/authentication/authApps'
--source ../include/mrs/mrs_client.inc

--let $mrs_client_arg_path='/c/authentication/authApps'
--source ../include/mrs/mrs_client.inc

--echo
--echo #
--echo # I.2
exec $MRS_CLIENT_ARGS
  -a BASIC
  --path /a/authentication/login?authApp=first
  -u mrsuser -p S3kre7
  --session-file $MYSQL_TMP_DIR/user1_session1.dat;

exec $MRS_CLIENT_ARGS
  -a BASIC
  --path /b/authentication/login?authApp=second
  -u mrsuser -p S3kre7
  --session-file $MYSQL_TMP_DIR/user1_session2.dat;

exec $MRS_CLIENT_ARGS
  -a BASIC
  --path /c/authentication/login?authApp=third
  -u mrsuser -p S3kre7
  --session-file $MYSQL_TMP_DIR/user1_session3.dat;

--echo
--echo #
--echo # II.1

exec $MRS_CLIENT_ARGS
  --path /a/sch/t/1
  --session-file $MYSQL_TMP_DIR/user1_session1.dat;

exec $MRS_CLIENT_ARGS
  --path /b/sch/t/1
  --session-file $MYSQL_TMP_DIR/user1_session1.dat;

exec $MRS_CLIENT_ARGS
  --path /c/sch/t/1
  --expected-status Unauthorized
  --session-file $MYSQL_TMP_DIR/user1_session1.dat;

--echo
--echo #
--echo # II.2

exec $MRS_CLIENT_ARGS
  --path /a/sch/t/1
  --expected-status Unauthorized
  --session-file $MYSQL_TMP_DIR/user1_session2.dat;

exec $MRS_CLIENT_ARGS
  --path /b/sch/t/1
  --session-file $MYSQL_TMP_DIR/user1_session2.dat;

exec $MRS_CLIENT_ARGS
  --path /c/sch/t/1
  --session-file $MYSQL_TMP_DIR/user1_session2.dat;

--echo
--echo #
--echo # II.3

exec $MRS_CLIENT_ARGS
  --path /a/sch/t/1
  --session-file $MYSQL_TMP_DIR/user1_session3.dat;

exec $MRS_CLIENT_ARGS
  --path /b/sch/t/1
  --session-file $MYSQL_TMP_DIR/user1_session3.dat;

exec $MRS_CLIENT_ARGS
  --path /c/sch/t/1
  --session-file $MYSQL_TMP_DIR/user1_session3.dat;

# Cleanup
drop user mrsuser@'%';
remove_file $MYSQL_TMP_DIR/user1_session1.dat;
remove_file $MYSQL_TMP_DIR/user1_session2.dat;
remove_file $MYSQL_TMP_DIR/user1_session3.dat;
--source ../include/mrs/cleanup.inc
