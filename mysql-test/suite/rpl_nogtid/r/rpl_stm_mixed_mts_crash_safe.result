include/rpl/init_source_replica.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection master]
########################################################################
#                               SET UP
########################################################################
'Big rpl_mta_crash_safe'
==== begin rpl_mta_crash_safe.inc:configure ====
---- begin configure database test_1 ----
common/rpl/mixing_engines.inc [commands=configure]
CREATE TABLE nt_1 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_2 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_3 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_4 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_5 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_6 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE tt_1 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_2 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_3 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_4 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_5 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_6 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
INSERT INTO nt_1(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_2(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_3(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_4(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_5(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_6(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_1(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_2(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_3(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_4(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_5(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_6(trans_id, stmt_id) VALUES(1,1);
CREATE PROCEDURE pc_i_tt_5_suc (IN p_trans_id INTEGER, IN p_stmt_id INTEGER)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
END|
CREATE PROCEDURE pc_i_nt_5_suc (IN p_trans_id INTEGER, IN p_stmt_id INTEGER)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
END|
CREATE FUNCTION fc_i_tt_5_suc (p_trans_id INTEGER, p_stmt_id INTEGER) RETURNS VARCHAR(64)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
RETURN "fc_i_tt_5_suc";
END|
CREATE FUNCTION fc_i_nt_5_suc (p_trans_id INTEGER, p_stmt_id INTEGER) RETURNS VARCHAR(64)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
RETURN "fc_i_nt_5_suc";
END|
CREATE FUNCTION fc_i_nt_3_tt_3_suc (p_trans_id INTEGER, p_stmt_id INTEGER) RETURNS VARCHAR(64)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_3 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_3(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
SELECT max(stmt_id) INTO in_stmt_id FROM tt_3 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_3(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
RETURN "fc_i_nt_3_tt_3_suc";
END|
CREATE TRIGGER tr_i_tt_3_to_nt_3 AFTER INSERT ON tt_3 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_3 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_3(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO nt_3(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
CREATE TRIGGER tr_i_nt_4_to_tt_4 AFTER INSERT ON nt_4 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_4 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_4(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO tt_4(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
CREATE TRIGGER tr_i_tt_5_to_tt_6 AFTER INSERT ON tt_5 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_6 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id, 1), 1) INTO in_stmt_id;
INSERT INTO tt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO tt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
CREATE TRIGGER tr_i_nt_5_to_nt_6 AFTER INSERT ON nt_5 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_6 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO nt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
---- end configure database test_1 ----
---- begin configure database test_2 ----
common/rpl/mixing_engines.inc [commands=configure]
CREATE TABLE nt_1 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_2 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_3 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_4 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_5 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_6 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE tt_1 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_2 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_3 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_4 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_5 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_6 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
INSERT INTO nt_1(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_2(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_3(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_4(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_5(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_6(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_1(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_2(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_3(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_4(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_5(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_6(trans_id, stmt_id) VALUES(1,1);
CREATE PROCEDURE pc_i_tt_5_suc (IN p_trans_id INTEGER, IN p_stmt_id INTEGER)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
END|
CREATE PROCEDURE pc_i_nt_5_suc (IN p_trans_id INTEGER, IN p_stmt_id INTEGER)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
END|
CREATE FUNCTION fc_i_tt_5_suc (p_trans_id INTEGER, p_stmt_id INTEGER) RETURNS VARCHAR(64)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
RETURN "fc_i_tt_5_suc";
END|
CREATE FUNCTION fc_i_nt_5_suc (p_trans_id INTEGER, p_stmt_id INTEGER) RETURNS VARCHAR(64)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
RETURN "fc_i_nt_5_suc";
END|
CREATE FUNCTION fc_i_nt_3_tt_3_suc (p_trans_id INTEGER, p_stmt_id INTEGER) RETURNS VARCHAR(64)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_3 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_3(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
SELECT max(stmt_id) INTO in_stmt_id FROM tt_3 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_3(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
RETURN "fc_i_nt_3_tt_3_suc";
END|
CREATE TRIGGER tr_i_tt_3_to_nt_3 AFTER INSERT ON tt_3 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_3 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_3(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO nt_3(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
CREATE TRIGGER tr_i_nt_4_to_tt_4 AFTER INSERT ON nt_4 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_4 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_4(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO tt_4(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
CREATE TRIGGER tr_i_tt_5_to_tt_6 AFTER INSERT ON tt_5 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_6 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id, 1), 1) INTO in_stmt_id;
INSERT INTO tt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO tt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
CREATE TRIGGER tr_i_nt_5_to_nt_6 AFTER INSERT ON nt_5 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_6 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO nt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
---- end configure database test_2 ----
---- begin configure database test_3 ----
common/rpl/mixing_engines.inc [commands=configure]
CREATE TABLE nt_1 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_2 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_3 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_4 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_5 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_6 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE tt_1 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_2 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_3 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_4 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_5 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
CREATE TABLE tt_6 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = "Innodb";
INSERT INTO nt_1(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_2(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_3(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_4(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_5(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_6(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_1(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_2(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_3(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_4(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_5(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_6(trans_id, stmt_id) VALUES(1,1);
CREATE PROCEDURE pc_i_tt_5_suc (IN p_trans_id INTEGER, IN p_stmt_id INTEGER)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
END|
CREATE PROCEDURE pc_i_nt_5_suc (IN p_trans_id INTEGER, IN p_stmt_id INTEGER)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
END|
CREATE FUNCTION fc_i_tt_5_suc (p_trans_id INTEGER, p_stmt_id INTEGER) RETURNS VARCHAR(64)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
RETURN "fc_i_tt_5_suc";
END|
CREATE FUNCTION fc_i_nt_5_suc (p_trans_id INTEGER, p_stmt_id INTEGER) RETURNS VARCHAR(64)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
RETURN "fc_i_nt_5_suc";
END|
CREATE FUNCTION fc_i_nt_3_tt_3_suc (p_trans_id INTEGER, p_stmt_id INTEGER) RETURNS VARCHAR(64)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_3 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_3(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
SELECT max(stmt_id) INTO in_stmt_id FROM tt_3 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_3(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
RETURN "fc_i_nt_3_tt_3_suc";
END|
CREATE TRIGGER tr_i_tt_3_to_nt_3 AFTER INSERT ON tt_3 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_3 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_3(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO nt_3(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
CREATE TRIGGER tr_i_nt_4_to_tt_4 AFTER INSERT ON nt_4 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_4 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_4(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO tt_4(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
CREATE TRIGGER tr_i_tt_5_to_tt_6 AFTER INSERT ON tt_5 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_6 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id, 1), 1) INTO in_stmt_id;
INSERT INTO tt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO tt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
CREATE TRIGGER tr_i_nt_5_to_nt_6 AFTER INSERT ON nt_5 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_6 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO nt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
---- end configure database test_3 ----
include/rpl/stop_replica.inc
SHOW CREATE TABLE mysql.slave_master_info;
Table	Create Table
slave_master_info	CREATE TABLE `slave_master_info` (
  `Number_of_lines` int unsigned NOT NULL COMMENT 'Number of lines in the file.',
  `Master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'The name of the master binary log currently being read from the master.',
  `Master_log_pos` bigint unsigned NOT NULL COMMENT 'The master log position of the last read event.',
  `Host` varchar(255) CHARACTER SET ascii COLLATE ascii_general_ci DEFAULT NULL COMMENT 'The host name of the source.',
  `User_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The user name used to connect to the master.',
  `User_password` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The password used to connect to the master.',
  `Port` int unsigned NOT NULL COMMENT 'The network port used to connect to the master.',
  `Connect_retry` int unsigned NOT NULL COMMENT 'The period (in seconds) that the slave will wait before trying to reconnect to the master.',
  `Enabled_ssl` tinyint(1) NOT NULL COMMENT 'Indicates whether the server supports SSL connections.',
  `Ssl_ca` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The file used for the Certificate Authority (CA) certificate.',
  `Ssl_capath` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The path to the Certificate Authority (CA) certificates.',
  `Ssl_cert` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the SSL certificate file.',
  `Ssl_cipher` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the cipher in use for the SSL connection.',
  `Ssl_key` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the SSL key file.',
  `Ssl_verify_server_cert` tinyint(1) NOT NULL COMMENT 'Whether to verify the server certificate.',
  `Heartbeat` float NOT NULL,
  `Bind` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Displays which interface is employed when connecting to the MySQL server',
  `Ignored_server_ids` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The number of server IDs to be ignored, followed by the actual server IDs',
  `Uuid` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The master server uuid.',
  `Retry_count` bigint unsigned NOT NULL COMMENT 'Number of reconnect attempts, to the master, before giving up.',
  `Ssl_crl` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The file used for the Certificate Revocation List (CRL)',
  `Ssl_crlpath` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The path used for Certificate Revocation List (CRL) files',
  `Enabled_auto_position` tinyint(1) NOT NULL COMMENT 'Indicates whether GTIDs will be used to retrieve events from the master.',
  `Channel_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'The channel on which the replica is connected to a source. Used in Multisource Replication',
  `Tls_version` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Tls version',
  `Public_key_path` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The file containing public key of master server.',
  `Get_public_key` tinyint(1) NOT NULL COMMENT 'Preference to get public key from master.',
  `Network_namespace` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Network namespace used for communication with the master server.',
  `Master_compression_algorithm` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'Compression algorithm supported for data transfer between source and replica.',
  `Master_zstd_compression_level` int unsigned NOT NULL COMMENT 'Compression level associated with zstd compression algorithm.',
  `Tls_ciphersuites` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Ciphersuites used for TLS 1.3 communication with the master server.',
  `Source_connection_auto_failover` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Indicates whether the channel connection failover is enabled.',
  `Gtid_only` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Indicates if this channel only uses GTIDs and does not persist positions.',
  PRIMARY KEY (`Channel_name`)
) /*!50100 TABLESPACE `mysql` */ ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 ROW_FORMAT=DYNAMIC COMMENT='Master Information'
SHOW CREATE TABLE mysql.slave_relay_log_info;
Table	Create Table
slave_relay_log_info	CREATE TABLE `slave_relay_log_info` (
  `Number_of_lines` int unsigned NOT NULL COMMENT 'Number of lines in the file or rows in the table. Used to version table definitions.',
  `Relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the current relay log file.',
  `Relay_log_pos` bigint unsigned DEFAULT NULL COMMENT 'The relay log position of the last executed event.',
  `Master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the master binary log file from which the events in the relay log file were read.',
  `Master_log_pos` bigint unsigned DEFAULT NULL COMMENT 'The master log position of the last executed event.',
  `Sql_delay` int DEFAULT NULL COMMENT 'The number of seconds that the slave must lag behind the master.',
  `Number_of_workers` int unsigned DEFAULT NULL,
  `Id` int unsigned DEFAULT NULL COMMENT 'Internal Id that uniquely identifies this record.',
  `Channel_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'The channel on which the replica is connected to a source. Used in Multisource Replication',
  `Privilege_checks_username` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'Username part of PRIVILEGE_CHECKS_USER.',
  `Privilege_checks_hostname` varchar(255) CHARACTER SET ascii COLLATE ascii_general_ci DEFAULT NULL COMMENT 'Hostname part of PRIVILEGE_CHECKS_USER.',
  `Require_row_format` tinyint(1) NOT NULL COMMENT 'Indicates whether the channel shall only accept row based events.',
  `Require_table_primary_key_check` enum('STREAM','ON','OFF','GENERATE') NOT NULL DEFAULT 'STREAM' COMMENT 'Indicates what is the channel policy regarding tables without primary keys on create and alter table queries',
  `Assign_gtids_to_anonymous_transactions_type` enum('OFF','LOCAL','UUID') NOT NULL DEFAULT 'OFF' COMMENT 'Indicates whether the channel will generate a new GTID for anonymous transactions. OFF means that anonymous transactions will remain anonymous. LOCAL means that anonymous transactions will be assigned a newly generated GTID based on server_uuid. UUID indicates that anonymous transactions will be assigned a newly generated GTID based on Assign_gtids_to_anonymous_transactions_value',
  `Assign_gtids_to_anonymous_transactions_value` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Indicates the UUID used while generating GTIDs for anonymous transactions',
  PRIMARY KEY (`Channel_name`)
) /*!50100 TABLESPACE `mysql` */ ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 ROW_FORMAT=DYNAMIC COMMENT='Relay Log Information'
SHOW CREATE TABLE mysql.slave_worker_info;
Table	Create Table
slave_worker_info	CREATE TABLE `slave_worker_info` (
  `Id` int unsigned NOT NULL,
  `Relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Relay_log_pos` bigint unsigned NOT NULL,
  `Master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Master_log_pos` bigint unsigned NOT NULL,
  `Checkpoint_relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Checkpoint_relay_log_pos` bigint unsigned NOT NULL,
  `Checkpoint_master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Checkpoint_master_log_pos` bigint unsigned NOT NULL,
  `Checkpoint_seqno` int unsigned NOT NULL,
  `Checkpoint_group_size` int unsigned NOT NULL,
  `Checkpoint_group_bitmap` blob NOT NULL,
  `Channel_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'The channel on which the replica is connected to a source. Used in Multisource Replication',
  PRIMARY KEY (`Channel_name`,`Id`)
) /*!50100 TABLESPACE `mysql` */ ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 ROW_FORMAT=DYNAMIC COMMENT='Worker Information'
ALTER TABLE mysql.slave_master_info ENGINE= Innodb;
ALTER TABLE mysql.slave_relay_log_info ENGINE= Innodb;
ALTER TABLE mysql.slave_worker_info ENGINE= Innodb;
SHOW CREATE TABLE mysql.slave_master_info;
Table	Create Table
slave_master_info	CREATE TABLE `slave_master_info` (
  `Number_of_lines` int unsigned NOT NULL COMMENT 'Number of lines in the file.',
  `Master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'The name of the master binary log currently being read from the master.',
  `Master_log_pos` bigint unsigned NOT NULL COMMENT 'The master log position of the last read event.',
  `Host` varchar(255) CHARACTER SET ascii COLLATE ascii_general_ci DEFAULT NULL COMMENT 'The host name of the source.',
  `User_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The user name used to connect to the master.',
  `User_password` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The password used to connect to the master.',
  `Port` int unsigned NOT NULL COMMENT 'The network port used to connect to the master.',
  `Connect_retry` int unsigned NOT NULL COMMENT 'The period (in seconds) that the slave will wait before trying to reconnect to the master.',
  `Enabled_ssl` tinyint(1) NOT NULL COMMENT 'Indicates whether the server supports SSL connections.',
  `Ssl_ca` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The file used for the Certificate Authority (CA) certificate.',
  `Ssl_capath` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The path to the Certificate Authority (CA) certificates.',
  `Ssl_cert` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the SSL certificate file.',
  `Ssl_cipher` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the cipher in use for the SSL connection.',
  `Ssl_key` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the SSL key file.',
  `Ssl_verify_server_cert` tinyint(1) NOT NULL COMMENT 'Whether to verify the server certificate.',
  `Heartbeat` float NOT NULL,
  `Bind` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Displays which interface is employed when connecting to the MySQL server',
  `Ignored_server_ids` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The number of server IDs to be ignored, followed by the actual server IDs',
  `Uuid` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The master server uuid.',
  `Retry_count` bigint unsigned NOT NULL COMMENT 'Number of reconnect attempts, to the master, before giving up.',
  `Ssl_crl` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The file used for the Certificate Revocation List (CRL)',
  `Ssl_crlpath` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The path used for Certificate Revocation List (CRL) files',
  `Enabled_auto_position` tinyint(1) NOT NULL COMMENT 'Indicates whether GTIDs will be used to retrieve events from the master.',
  `Channel_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'The channel on which the replica is connected to a source. Used in Multisource Replication',
  `Tls_version` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Tls version',
  `Public_key_path` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The file containing public key of master server.',
  `Get_public_key` tinyint(1) NOT NULL COMMENT 'Preference to get public key from master.',
  `Network_namespace` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Network namespace used for communication with the master server.',
  `Master_compression_algorithm` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'Compression algorithm supported for data transfer between source and replica.',
  `Master_zstd_compression_level` int unsigned NOT NULL COMMENT 'Compression level associated with zstd compression algorithm.',
  `Tls_ciphersuites` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Ciphersuites used for TLS 1.3 communication with the master server.',
  `Source_connection_auto_failover` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Indicates whether the channel connection failover is enabled.',
  `Gtid_only` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Indicates if this channel only uses GTIDs and does not persist positions.',
  PRIMARY KEY (`Channel_name`)
) /*!50100 TABLESPACE `mysql` */ ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 ROW_FORMAT=DYNAMIC COMMENT='Master Information'
SHOW CREATE TABLE mysql.slave_relay_log_info;
Table	Create Table
slave_relay_log_info	CREATE TABLE `slave_relay_log_info` (
  `Number_of_lines` int unsigned NOT NULL COMMENT 'Number of lines in the file or rows in the table. Used to version table definitions.',
  `Relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the current relay log file.',
  `Relay_log_pos` bigint unsigned DEFAULT NULL COMMENT 'The relay log position of the last executed event.',
  `Master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the master binary log file from which the events in the relay log file were read.',
  `Master_log_pos` bigint unsigned DEFAULT NULL COMMENT 'The master log position of the last executed event.',
  `Sql_delay` int DEFAULT NULL COMMENT 'The number of seconds that the slave must lag behind the master.',
  `Number_of_workers` int unsigned DEFAULT NULL,
  `Id` int unsigned DEFAULT NULL COMMENT 'Internal Id that uniquely identifies this record.',
  `Channel_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'The channel on which the replica is connected to a source. Used in Multisource Replication',
  `Privilege_checks_username` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'Username part of PRIVILEGE_CHECKS_USER.',
  `Privilege_checks_hostname` varchar(255) CHARACTER SET ascii COLLATE ascii_general_ci DEFAULT NULL COMMENT 'Hostname part of PRIVILEGE_CHECKS_USER.',
  `Require_row_format` tinyint(1) NOT NULL COMMENT 'Indicates whether the channel shall only accept row based events.',
  `Require_table_primary_key_check` enum('STREAM','ON','OFF','GENERATE') NOT NULL DEFAULT 'STREAM' COMMENT 'Indicates what is the channel policy regarding tables without primary keys on create and alter table queries',
  `Assign_gtids_to_anonymous_transactions_type` enum('OFF','LOCAL','UUID') NOT NULL DEFAULT 'OFF' COMMENT 'Indicates whether the channel will generate a new GTID for anonymous transactions. OFF means that anonymous transactions will remain anonymous. LOCAL means that anonymous transactions will be assigned a newly generated GTID based on server_uuid. UUID indicates that anonymous transactions will be assigned a newly generated GTID based on Assign_gtids_to_anonymous_transactions_value',
  `Assign_gtids_to_anonymous_transactions_value` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Indicates the UUID used while generating GTIDs for anonymous transactions',
  PRIMARY KEY (`Channel_name`)
) /*!50100 TABLESPACE `mysql` */ ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 ROW_FORMAT=DYNAMIC COMMENT='Relay Log Information'
SHOW CREATE TABLE mysql.slave_worker_info;
Table	Create Table
slave_worker_info	CREATE TABLE `slave_worker_info` (
  `Id` int unsigned NOT NULL,
  `Relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Relay_log_pos` bigint unsigned NOT NULL,
  `Master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Master_log_pos` bigint unsigned NOT NULL,
  `Checkpoint_relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Checkpoint_relay_log_pos` bigint unsigned NOT NULL,
  `Checkpoint_master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Checkpoint_master_log_pos` bigint unsigned NOT NULL,
  `Checkpoint_seqno` int unsigned NOT NULL,
  `Checkpoint_group_size` int unsigned NOT NULL,
  `Checkpoint_group_bitmap` blob NOT NULL,
  `Channel_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'The channel on which the replica is connected to a source. Used in Multisource Replication',
  PRIMARY KEY (`Channel_name`,`Id`)
) /*!50100 TABLESPACE `mysql` */ ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 ROW_FORMAT=DYNAMIC COMMENT='Worker Information'
==== end rpl_mta_crash_safe.inc:configure ====
########################################################################
#                                TEST
########################################################################
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 1 --- Number of Groups per worker 1 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (7, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (7, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (7, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (7, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 1 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (8, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (8, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (8, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (8, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (9, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (9, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (9, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (9, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (10, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (10, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (10, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (10, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (11, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (11, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (11, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (11, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (12, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (12, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (12, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (12, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 1 --- Number of Groups per worker 1 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (13, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (13, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (13, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (13, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 1 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (14, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (14, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (14, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (14, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (15, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (15, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (15, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (15, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (16, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (16, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (16, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (16, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (17, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (17, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (17, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (17, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (18, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (18, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (18, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (18, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 2 --- Number of Groups per worker 1 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (19, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (19, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (19, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (19, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 1 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (20, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (20, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (20, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (20, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (21, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (21, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (21, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (21, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (22, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (22, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (22, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (22, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (23, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (23, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (23, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (23, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (24, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (24, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (24, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (24, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (25, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (25, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (25, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (25, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (26, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (26, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (26, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (26, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (27, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (27, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (27, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (27, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (28, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (28, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (28, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (28, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (29, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (29, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (29, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (29, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (30, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (30, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (30, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (30, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 2 --- Number of Groups per worker 1 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (31, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (31, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (31, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (31, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 1 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (32, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (32, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (32, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (32, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (33, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (33, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (33, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (33, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (34, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (34, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (34, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (34, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (35, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (35, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (35, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (35, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (36, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (36, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (36, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (36, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (37, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (37, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (37, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (37, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (38, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (38, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (38, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (38, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (39, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (39, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (39, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (39, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (40, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (40, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (40, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (40, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (41, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (41, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (41, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (41, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (42, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (42, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (42, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (42, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 3 --- Number of Groups per worker 1 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (43, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (43, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (43, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (43, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 1 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (44, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (44, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (44, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (44, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 1 --- Debugging Groups 1 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (45, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (45, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (45, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (45, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (46, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (46, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (46, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (46, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (47, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (47, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (47, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (47, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (48, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (48, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (48, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (48, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (49, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (49, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (49, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (49, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (50, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (50, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (50, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (50, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (51, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (51, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (51, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (51, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (52, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (52, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (52, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (52, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (53, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (53, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (53, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (53, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (54, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (54, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (54, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (54, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (55, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (55, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (55, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (55, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (56, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (56, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (56, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (56, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (57, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (57, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (57, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (57, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (58, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (58, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (58, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (58, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (59, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (59, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (59, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (59, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (60, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (60, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (60, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (60, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 3 --- Number of Groups per worker 1 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (61, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (61, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (61, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (61, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 1 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (62, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (62, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (62, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (62, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 1 --- Debugging Groups 1 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (63, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (63, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (63, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (63, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (64, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (64, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (64, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (64, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (65, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (65, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (65, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (65, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (66, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (66, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (66, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (66, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (67, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (67, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (67, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (67, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (68, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (68, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (68, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (68, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 1 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (69, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (69, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (69, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (69, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (70, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (70, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (70, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (70, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (71, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (71, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (71, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (71, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (72, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (72, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (72, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (72, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (73, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (73, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (73, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (73, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (74, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (74, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (74, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (74, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (75, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (75, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (75, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (75, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (76, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (76, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (76, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (76, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (77, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (77, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (77, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (77, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 1 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (78, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (78, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (78, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (78, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 1 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (79, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (79, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (79, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (79, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (80, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (80, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (80, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (80, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (81, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (81, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (81, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (81, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (82, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (82, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (82, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (82, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (83, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (83, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (83, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (83, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 1 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (84, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (84, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (84, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (84, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (85, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (85, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (85, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (85, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (86, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (86, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (86, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (86, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (87, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (87, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (87, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (87, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (88, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (88, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (88, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (88, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 2;
Generating Number of Workers 1 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (89, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (89, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (89, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (89, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (90, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (90, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (90, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (90, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 2;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 2;
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (91, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (91, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (91, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (91, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (92, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (92, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (92, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (92, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (93, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (93, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (93, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (93, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 2;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (94, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (94, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (94, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (94, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (95, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (95, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (95, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (95, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (96, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (96, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (96, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (96, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (97, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (97, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (97, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (97, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (98, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (98, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (98, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (98, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (99, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (99, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (99, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (99, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (100, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (100, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (100, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (100, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (101, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (101, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (101, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (101, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (102, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (102, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (102, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (102, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (103, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (103, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (103, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (103, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (104, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (104, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (104, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (104, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (105, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (105, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (105, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (105, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (106, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (106, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (106, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (106, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (107, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (107, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (107, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (107, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (108, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (108, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (108, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (108, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (109, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (109, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (109, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (109, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (110, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (110, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (110, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (110, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (111, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (111, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (111, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (111, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (112, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (112, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (112, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (112, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (113, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (113, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (113, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (113, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 2;
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (114, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (114, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (114, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (114, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (115, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (115, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (115, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (115, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (116, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (116, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (116, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (116, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (117, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (117, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (117, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (117, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 2;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 2;
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (118, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (118, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (118, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (118, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (119, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (119, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (119, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (119, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (120, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (120, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (120, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (120, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (121, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (121, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (121, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (121, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (122, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (122, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (122, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (122, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (123, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (123, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (123, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (123, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 2;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (124, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (124, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (124, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (124, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (125, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (125, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (125, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (125, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (126, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (126, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (126, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (126, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (127, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (127, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (127, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (127, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (128, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (128, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (128, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (128, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (129, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (129, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (129, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (129, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (130, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (130, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (130, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (130, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (131, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (131, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (131, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (131, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (132, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (132, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (132, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (132, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (133, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (133, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (133, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (133, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (134, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (134, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (134, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (134, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (135, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (135, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (135, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (135, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (136, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (136, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (136, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (136, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (137, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (137, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (137, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (137, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (138, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (138, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (138, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (138, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (139, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (139, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (139, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (139, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (140, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (140, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (140, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (140, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (141, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (141, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (141, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (141, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (142, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (142, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (142, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (142, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (143, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (143, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (143, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (143, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (144, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (144, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (144, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (144, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (145, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (145, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (145, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (145, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (146, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (146, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (146, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (146, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (147, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (147, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (147, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (147, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (148, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (148, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (148, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (148, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (149, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (149, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (149, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (149, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (150, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (150, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (150, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (150, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (151, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (151, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (151, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (151, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (152, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (152, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (152, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (152, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (153, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (153, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (153, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (153, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 2;
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (154, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (154, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (154, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (154, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (155, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (155, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (155, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (155, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (156, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (156, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (156, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (156, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (157, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (157, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (157, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (157, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (158, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (158, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (158, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (158, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 2 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (159, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (159, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (159, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (159, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 2;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 2;
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (160, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (160, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (160, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (160, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (161, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (161, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (161, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (161, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (162, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (162, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (162, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (162, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (163, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (163, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (163, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (163, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (164, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (164, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (164, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (164, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (165, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (165, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (165, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (165, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (166, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (166, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (166, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (166, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (167, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (167, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (167, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (167, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 2 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (168, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (168, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (168, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (168, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 2;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (169, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (169, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (169, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (169, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (170, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (170, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (170, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (170, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (171, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (171, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (171, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (171, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (172, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (172, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (172, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (172, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (173, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (173, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (173, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (173, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (174, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (174, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (174, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (174, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 2;
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (175, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (175, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (175, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (175, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (176, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (176, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (176, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (176, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (177, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (177, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (177, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (177, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 2;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 3;
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (178, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (178, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (178, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (178, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (179, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (179, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (179, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (179, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 1 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (180, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (180, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (180, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (180, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 1;
SET @@global.replica_checkpoint_period= 3;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (181, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (181, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (181, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (181, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (182, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (182, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (182, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (182, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (183, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (183, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (183, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (183, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (184, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (184, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (184, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (184, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (185, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (185, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (185, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (185, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (186, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (186, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (186, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (186, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (187, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (187, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (187, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (187, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (188, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (188, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (188, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (188, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (189, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (189, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (189, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (189, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (190, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (190, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (190, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (190, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (191, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (191, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (191, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (191, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (192, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (192, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (192, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (192, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 2;
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (193, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (193, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (193, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (193, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (194, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (194, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (194, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (194, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (195, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (195, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (195, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (195, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (196, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (196, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (196, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (196, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (197, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (197, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (197, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (197, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (198, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (198, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (198, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (198, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 2;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 3;
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (199, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (199, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (199, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (199, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (200, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (200, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (200, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (200, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (201, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (201, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (201, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (201, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (202, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (202, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (202, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (202, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (203, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (203, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (203, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (203, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 2 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (204, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (204, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (204, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (204, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 2;
SET @@global.replica_checkpoint_period= 3;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 0;
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (205, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (205, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (205, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (205, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (206, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (206, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (206, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (206, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (207, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (207, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (207, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (207, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (208, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (208, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (208, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (208, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (209, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (209, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (209, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (209, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (210, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (210, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (210, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (210, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (211, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (211, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (211, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (211, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (212, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (212, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (212, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (212, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (213, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (213, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (213, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (213, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 0;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 1;
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (214, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (214, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (214, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (214, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (215, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (215, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (215, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (215, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (216, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (216, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (216, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (216, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (217, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (217, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (217, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (217, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (218, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (218, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (218, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (218, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (219, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (219, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (219, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (219, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (220, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (220, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (220, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (220, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (221, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (221, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (221, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (221, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (222, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (222, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (222, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (222, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 1;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 2;
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (223, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (223, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (223, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (223, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (224, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (224, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (224, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (224, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (225, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (225, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (225, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (225, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (226, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (226, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (226, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (226, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (227, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (227, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (227, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (227, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (228, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (228, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (228, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (228, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (229, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (229, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (229, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (229, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (230, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (230, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (230, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (230, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (231, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (231, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (231, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (231, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 2;
==== end rpl_mta_crash_safe.inc:recovery ====
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 3;
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (232, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (232, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (232, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (232, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (233, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (233, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (233, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (233, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (234, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (234, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (234, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (234, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (235, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (235, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (235, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (235, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (236, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (236, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (236, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (236, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (237, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (237, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (237, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (237, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_1
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (238, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (238, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (238, 2)
master-bin.000001	#	Query	#	#	use `test_1`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (238, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_2
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (239, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (239, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (239, 2)
master-bin.000001	#	Query	#	#	use `test_2`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (239, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
Generating Number of Workers 3 --- Number of Groups per worker 3 --- Debugging Groups 3 --- Database test_3
==== begin rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
common/rpl/mixing_engines.inc [commands=B T T C]
BEGIN;
INSERT INTO tt_1(trans_id, stmt_id) VALUES (240, 2);
INSERT INTO tt_1(trans_id, stmt_id) VALUES (240, 4);
COMMIT;
-b-b-b-b-b-b-b-b-b-b-b- >> B T T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (240, 2)
master-bin.000001	#	Query	#	#	use `test_3`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (240, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T C << -e-e-e-e-e-e-e-e-e-e-e-

==== end rpl_mta_crash_safe.inc:common/rpl/mixing_engines.inc ====
----Executing----
==== begin rpl_mta_crash_safe.inc:recovery ====
include/rpl/start_receiver.inc
** fixing gaps **
include/rpl/start_server.inc [server_number=2 parameters: --skip-replica-start --relay-log-recovery=0]
START REPLICA UNTIL SQL_AFTER_MTS_GAPS;
include/rpl/wait_for_replica_status.inc [Until_Condition]
include/rpl/wait_for_applier_to_stop.inc
** regular restart **
include/rpl/restart_server.inc [server_number=2 parameters: --skip-replica-start --replica-transaction-retries=0 --sync-source-info=1]
include/rpl/start_receiver.inc
include/rpl/start_applier.inc
include/rpl/stop_replica.inc
SET @@global.replica_parallel_workers= 3;
SET @@global.replica_checkpoint_period= 3;
==== end rpl_mta_crash_safe.inc:recovery ====
########################################################################
#                          CHECK CONSISTENCY
########################################################################
include/rpl/start_replica.inc
include/rpl/sync_to_replica.inc
########################################################################
#                             CLEAN UP
########################################################################
==== begin rpl_mta_crash_safe.inc:clean ====
include/rpl/stop_replica.inc
include/rpl/start_replica.inc
---- begin clean database test_1 ----
common/rpl/mixing_engines.inc [commands=clean]
DROP TABLE tt_1;
DROP TABLE tt_2;
DROP TABLE tt_3;
DROP TABLE tt_4;
DROP TABLE tt_5;
DROP TABLE tt_6;
DROP TABLE nt_1;
DROP TABLE nt_2;
DROP TABLE nt_3;
DROP TABLE nt_4;
DROP TABLE nt_5;
DROP TABLE nt_6;
DROP PROCEDURE pc_i_tt_5_suc;
DROP PROCEDURE pc_i_nt_5_suc;
DROP FUNCTION fc_i_tt_5_suc;
DROP FUNCTION fc_i_nt_5_suc;
DROP FUNCTION fc_i_nt_3_tt_3_suc;
---- end clean database test_1 ----
---- begin clean database test_2 ----
common/rpl/mixing_engines.inc [commands=clean]
DROP TABLE tt_1;
DROP TABLE tt_2;
DROP TABLE tt_3;
DROP TABLE tt_4;
DROP TABLE tt_5;
DROP TABLE tt_6;
DROP TABLE nt_1;
DROP TABLE nt_2;
DROP TABLE nt_3;
DROP TABLE nt_4;
DROP TABLE nt_5;
DROP TABLE nt_6;
DROP PROCEDURE pc_i_tt_5_suc;
DROP PROCEDURE pc_i_nt_5_suc;
DROP FUNCTION fc_i_tt_5_suc;
DROP FUNCTION fc_i_nt_5_suc;
DROP FUNCTION fc_i_nt_3_tt_3_suc;
---- end clean database test_2 ----
---- begin clean database test_3 ----
common/rpl/mixing_engines.inc [commands=clean]
DROP TABLE tt_1;
DROP TABLE tt_2;
DROP TABLE tt_3;
DROP TABLE tt_4;
DROP TABLE tt_5;
DROP TABLE tt_6;
DROP TABLE nt_1;
DROP TABLE nt_2;
DROP TABLE nt_3;
DROP TABLE nt_4;
DROP TABLE nt_5;
DROP TABLE nt_6;
DROP PROCEDURE pc_i_tt_5_suc;
DROP PROCEDURE pc_i_nt_5_suc;
DROP FUNCTION fc_i_tt_5_suc;
DROP FUNCTION fc_i_nt_5_suc;
DROP FUNCTION fc_i_nt_3_tt_3_suc;
---- end clean database test_3 ----
==== end rpl_mta_crash_safe.inc:clean ====
include/rpl/deinit.inc
