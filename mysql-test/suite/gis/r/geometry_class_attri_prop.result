# Creating the spatial Geometry object
USE test;
CREATE TABLE gis_point (fid INTEGER NOT NULL PRIMARY KEY, g POINT);
CREATE TABLE gis_linestring (fid INTEGER NOT NULL PRIMARY KEY, g LINESTRING);
CREATE TABLE gis_polygon (fid INTEGER NOT NULL PRIMARY KEY, g POLY<PERSON><PERSON>);
CREATE TABLE gis_multi_point (fid INTEGER NOT NULL PRIMARY KEY, g MULTIPOINT);
CREATE TABLE gis_multi_linestring (fid INTEGER NOT NULL PRIMARY KEY, g MULTILINESTRING);
CREATE TABLE gis_multi_polygon (fid INTEGER NOT NULL PRIMARY KEY, g MULTIPOLYGON);
CREATE TABLE gis_geometrycollection (fid INTEGER NOT NULL PRIMARY KEY, g GEOMETRYCOLLECTION);
CREATE TABLE gis_geometry (fid INTEGER NOT NULL PRIMARY KEY, g GEOMETRY);
# Inserting POINT Geometry Values 
INSERT INTO gis_point VALUES
(101, ST_POINTFROMTEXT('POINT(0 0)')),
(102, ST_POINTFROMTEXT('POINT(1 0)')),
(103, ST_POINTFROMTEXT('POINT(0 1)')),
(104, ST_POINTFROMTEXT('POINT(1 1)')),
(105, ST_POINTFROMTEXT('POINT(-1 1)'));
INSERT INTO gis_point VALUES
(106, ST_POINTFROMWKB(ST_ASWKB(POINT(0,0)))),
(107, ST_POINTFROMWKB(ST_ASWKB(POINT(10,0)))),
(108, ST_POINTFROMWKB(ST_ASWKB(POINT(0,10)))),
(109, ST_POINTFROMWKB(ST_ASWKB(POINT(-10,0)))),
(110, ST_POINTFROMWKB(ST_ASWKB(POINT(0,-10))));
INSERT INTO gis_point VALUES
(111, ST_POINTFROMWKB(ST_ASWKB(ST_POINTFROMTEXT('POINT(1 1)')))),
(112, ST_POINTFROMWKB(ST_ASWKB(ST_POINTFROMTEXT('POINT(1000 1000)')))),
(113, ST_POINTFROMWKB(ST_ASWKB(ST_POINTFROMTEXT('POINT(1000 -1000)')))),
(114, ST_POINTFROMWKB(ST_ASWKB(ST_POINTFROMTEXT('POINT(-1000 1000)')))),
(115, ST_POINTFROMWKB(ST_ASWKB(ST_POINTFROMTEXT('POINT(-1000 -1000)'))));
# Inserting LINESTRING Geometry Values 
INSERT INTO gis_linestring VALUES
(201, ST_LINEFROMTEXT('LINESTRING(0 0,5 5)')),
(202, ST_LINEFROMTEXT('LINESTRING(0 0,2 2,4 4)')),
(203, ST_LINEFROMTEXT('LINESTRING(0 0,5 5,10 10)'));
INSERT INTO gis_linestring VALUES
(204, ST_LINESTRINGFROMTEXT('LINESTRING(10 10,5 5)')),
(205, ST_LINESTRINGFROMTEXT('LINESTRING(0 0,12 12,24 24)')),
(206, ST_LINESTRINGFROMTEXT('LINESTRING(0 0,50 50,100 100)'));
INSERT INTO gis_linestring VALUES
(207, ST_LINEFROMWKB(ST_ASWKB(LINESTRING(POINT(0,0), POINT(5,5))))),
(208, ST_LINEFROMWKB(ST_ASWKB(LINESTRING(POINT(0,0), POINT(-5,-5), POINT(-10,10))))),
(209, ST_LINEFROMWKB(ST_ASWKB(LINESTRING(POINT(0,0), POINT(2,2), POINT(4,4), POINT(6,6), POINT(8,8)))));
INSERT INTO gis_linestring VALUES
(210, ST_LINESTRINGFROMWKB(ST_ASWKB(LINESTRING(POINT(0,0), POINT(5,5))))),
(211, ST_LINESTRINGFROMWKB(ST_ASWKB(LINESTRING(POINT(0,0), POINT(-50,-50), POINT(10,-10))))),
(212, ST_LINESTRINGFROMWKB(ST_ASWKB(LINESTRING(POINT(0,0), POINT(1000,1000), POINT(1000,-1000)))));
INSERT INTO gis_linestring VALUES
(213, ST_LINEFROMWKB(ST_ASWKB(ST_LINEFROMTEXT('LINESTRING(1000 1000,1000 -1000)')))),
(214, ST_LINEFROMWKB(ST_ASWKB(ST_LINEFROMTEXT('LINESTRING(1 1,2 2,3 3,4 4,5 5,6 6,7 7,8 8,9 9)')))),
(215, ST_LINESTRINGFROMWKB(ST_ASWKB(ST_LINESTRINGFROMTEXT('LINESTRING(10 10,10 -10,-10 -10,-10 10,10 10)'))));
# Inserting POLYGON Geometry Values 
INSERT INTO gis_polygon VALUES
(301, ST_POLYFROMTEXT('POLYGON((0 0,0 5,5 5, 0 0))')),
(302, ST_POLYFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))')),
(303, ST_POLYFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'));
INSERT INTO gis_polygon VALUES
(304, ST_POLYGONFROMTEXT('POLYGON((0 0,0 50,50 50,50 0,0 0))')),
(305, ST_POLYGONFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4))')),
(306, ST_POLYGONFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0),(2 2,2 4,4 4,4 2,2 2),(6 6,6 8,8 8,8 6,6 6))'));
INSERT INTO gis_polygon VALUES
(307, ST_POLYFROMWKB(ST_ASWKB(POLYGON(LINESTRING(POINT(0,0), POINT(0,5), POINT(5,5), POINT(0,0)))))),
(308, ST_POLYFROMWKB(ST_ASWKB(POLYGON(LINESTRING(POINT(0,0), POINT(0,15), POINT(15,15), POINT(15,0), POINT(0,0)))))),
(309, ST_POLYFROMWKB(ST_ASWKB(POLYGON(LINESTRING(POINT(0,0), POINT(0,10), POINT(10,10), POINT(10,0), POINT(0,0)),
LINESTRING(POINT(4,4), POINT(4,6), POINT(6,6), POINT(6,4), POINT(4,4))))));
INSERT INTO gis_polygon VALUES
(310, ST_POLYGONFROMWKB(ST_ASWKB(POLYGON(LINESTRING(POINT(0,0), POINT(0,5), POINT(5,5), POINT(0,0)))))),
(311, ST_POLYGONFROMWKB(ST_ASWKB(POLYGON(LINESTRING(POINT(10,10), POINT(10,15), POINT(15,15), POINT(15,10), POINT(10,10)))))),
(312, ST_POLYGONFROMWKB(ST_ASWKB(POLYGON(LINESTRING(POINT(10,10), POINT(10,20), POINT(20,20), POINT(20,10), POINT(10,10)),
LINESTRING(POINT(14,14), POINT(14,16), POINT(16,16), POINT(16,14), POINT(14,14))))));
INSERT INTO gis_polygon VALUES
(313, ST_POLYFROMWKB(ST_ASWKB(ST_POLYFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,5 5,0 0))')))),
(314, ST_POLYFROMWKB(ST_ASWKB(ST_POLYGONFROMTEXT('POLYGON((10 0,10 10,0 10,-10 10,-10 0,-10 -10,0 10,10 -10,10 0))')))),
(315, ST_POLYGONFROMWKB(ST_ASWKB(ST_POLYGONFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0),(2 2,2 4,4 4,4 2,2 2),(6 6,6 8,8 8,8 6,6 6))'))));
# Inserting MULTIPOINT Geometry Values 
INSERT INTO gis_multi_point VALUES
(401, ST_MPOINTFROMTEXT('MULTIPOINT(0 0)')),
(402, ST_MPOINTFROMTEXT('MULTIPOINT(0 0,2 2,4 4)')),
(403, ST_MPOINTFROMTEXT('MULTIPOINT(0 0,5 5,10 10)'));
INSERT INTO gis_multi_point VALUES
(404, ST_MULTIPOINTFROMTEXT('MULTIPOINT(0 0,100 100)')),
(405, ST_MULTIPOINTFROMTEXT('MULTIPOINT(0 0,1000 1000)')),
(406, ST_MULTIPOINTFROMTEXT('MULTIPOINT(1000 1000,1000 -1000,-1000 1000,-1000 -1000)'));
INSERT INTO gis_multi_point VALUES
(407, ST_MPOINTFROMWKB(ST_ASWKB(MULTIPOINT(POINT(0,0))))),
(408, ST_MPOINTFROMWKB(ST_ASWKB(MULTIPOINT(POINT(0,0), POINT(10,10))))),
(409, ST_MPOINTFROMWKB(ST_ASWKB(MULTIPOINT(POINT(0,0), POINT(2,2), POINT(4,4), POINT(6,6)))));
INSERT INTO gis_multi_point VALUES
(410, ST_MULTIPOINTFROMWKB(ST_ASWKB(MULTIPOINT(POINT(0,0))))),
(411, ST_MULTIPOINTFROMWKB(ST_ASWKB(MULTIPOINT(POINT(0,0), POINT(1000,1000))))),
(412, ST_MULTIPOINTFROMWKB(ST_ASWKB(MULTIPOINT(POINT(1000,1000), POINT(-1000,1000), POINT(1000,-1000), POINT(-1000,-1000)))));
INSERT INTO gis_multi_point VALUES
(413, ST_MPOINTFROMWKB(ST_ASWKB(ST_MPOINTFROMTEXT('MULTIPOINT(0 0)')))),
(414, ST_MPOINTFROMWKB(ST_ASWKB(ST_MPOINTFROMTEXT('MULTIPOINT(0 0,1000 1000,-1000 -1000)')))),
(415, ST_MPOINTFROMWKB(ST_ASWKB(ST_MPOINTFROMTEXT('MULTIPOINT(1000 1000,1000 -1000,-1000 1000,-1000 -1000,1000 1000)'))));
# Inserting MULTILINESTRING Geometry Values 
INSERT INTO gis_multi_linestring VALUES
(501, ST_MLINEFROMTEXT('MULTILINESTRING((0 0,2 2))')),
(502, ST_MLINEFROMTEXT('MULTILINESTRING((0 0,2 2,4 4))')),
(503, ST_MLINEFROMTEXT('MULTILINESTRING((0 0,2 2,4 4),(6 6,8 8,10 10))'));
INSERT INTO gis_multi_linestring VALUES
(504, ST_MULTILINESTRINGFROMTEXT('MULTILINESTRING((0 0,100 100,-100 -100))')),
(505, ST_MULTILINESTRINGFROMTEXT('MULTILINESTRING((1000 1000,-1000 -1000))')),
(506, ST_MULTILINESTRINGFROMTEXT('MULTILINESTRING((1000 1000,-1000 -1000),(1000 -1000,-1000 1000))'));
INSERT INTO gis_multi_linestring VALUES
(507, ST_MLINEFROMWKB(ST_ASWKB(MULTILINESTRING(LINESTRING(POINT(0,0), POINT(2,2)))))),
(508, ST_MLINEFROMWKB(ST_ASWKB(MULTILINESTRING(LINESTRING(POINT(0,0), POINT(12,12), POINT(24,24)))))),
(509, ST_MLINEFROMWKB(ST_ASWKB(MULTILINESTRING(LINESTRING(POINT(0,0), POINT(2,2), POINT(4,4)),
LINESTRING(POINT(6,6), POINT(8,8), POINT(10,10))))));
INSERT INTO gis_multi_linestring VALUES
(510, ST_MULTILINESTRINGFROMWKB(ST_ASWKB(MULTILINESTRING(LINESTRING(POINT(0,0), POINT(2,2), POINT(4,4)))))),
(511, ST_MULTILINESTRINGFROMWKB(ST_ASWKB(MULTILINESTRING(LINESTRING(POINT(0,0), POINT(1,1), POINT(2,2)))))),
(512, ST_MULTILINESTRINGFROMWKB(ST_ASWKB(MULTILINESTRING(LINESTRING(POINT(0,0), POINT(12,12), POINT(24,24)),
LINESTRING(POINT(36,36), POINT(48,48), POINT(50,50))))));
INSERT INTO gis_multi_linestring VALUES
(513, ST_MLINEFROMWKB(ST_ASWKB(ST_MLINEFROMTEXT('MULTILINESTRING((0 0,10 10),(0 10,10 0))')))),
(514, ST_MLINEFROMWKB(ST_ASWKB(ST_MULTILINESTRINGFROMTEXT('MULTILINESTRING((0 0,10 10,-10 10,0 0),(0 0,-10 -10,10 -10,0 0))')))),
(515, ST_MULTILINESTRINGFROMWKB(ST_ASWKB(ST_MULTILINESTRINGFROMTEXT('MULTILINESTRING((0 0,0 100),(0 0,100 0),(0 0,0 -100),(0 0,-100 0))'))));
# Inserting MULTIPOLGYON Geometry Values 
INSERT INTO gis_multi_polygon VALUES
(601, ST_MPOLYFROMTEXT('MULTIPOLYGON(((0 0,0 5,5 5,0 0)))')),
(602, ST_MPOLYFROMTEXT('MULTIPOLYGON(((0 0,0 5,5 5,0 0)),((5 5,5 10,10 10,5 5)))')),
(603, ST_MPOLYFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)))'));
INSERT INTO gis_multi_polygon VALUES
(604, ST_MULTIPOLYGONFROMTEXT('MULTIPOLYGON(((0 0,0 5,5 5, 0 0)))')),
(605, ST_MULTIPOLYGONFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4, 4 4)),((0 0,0 -2,-2 -2, 0 0)))')),
(606, ST_MULTIPOLYGONFROMTEXT('MULTIPOLYGON(((0 0,5 5,-5 5,0 0)),((0 0,-5 -5,5 -5,0 0)))'));
INSERT INTO gis_multi_polygon VALUES
(607, ST_MPOLYFROMWKB(ST_ASWKB(MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0), POINT(5,0), POINT(5,5), POINT(0,5), POINT(0,0))))))),
(608, ST_MPOLYFROMWKB(ST_ASWKB(MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0), POINT(10,0), POINT(10,10), POINT(0,10), POINT(0,0)),
LINESTRING(POINT(4,4), POINT(4,6), POINT(6,6), POINT(6,4), POINT(4,4))))))),
(609, ST_MPOLYFROMWKB(ST_ASWKB(MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0), POINT(5,0), POINT(5,5), POINT(0,5), POINT(0,0))),
POLYGON(LINESTRING(POINT(0,0), POINT(-5,0), POINT(-5,-5), POINT(0,-5), POINT(0,0)))))));
INSERT INTO gis_multi_polygon VALUES
(610, ST_MULTIPOLYGONFROMWKB(ST_ASWKB(MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0), POINT(-5,0), POINT(-5,-5), POINT(0,-5), POINT(0,0))))))),
(611, ST_MULTIPOLYGONFROMWKB(ST_ASWKB(MULTIPOLYGON(POLYGON(LINESTRING(POINT(10,10), POINT(20,10), POINT(20,20), POINT(10,20), POINT(10,10)),
LINESTRING(POINT(14,14), POINT(14,16), POINT(16,16), POINT(16,14), POINT(14,14))))))),
(612, ST_MULTIPOLYGONFROMWKB(ST_ASWKB(MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0), POINT(0,10), POINT(10,10), POINT(10,0), POINT(0,0)),
LINESTRING(POINT(4,4), POINT(4,6), POINT(6,6), POINT(6,4), POINT(4,4))),
POLYGON(LINESTRING(POINT(0,0), POINT(-5,0), POINT(-5,-5), POINT(0,-5), POINT(0,0)))))));
INSERT INTO gis_multi_polygon VALUES
(613, ST_MPOLYFROMWKB(ST_ASWKB(ST_MPOLYFROMTEXT('MULTIPOLYGON(((0 0,5 5,5 -5,0 0)),((0 0,-5 5,-5 -5,0 0)))')))),
(614, ST_MPOLYFROMWKB(ST_ASWKB(ST_MULTIPOLYGONFROMTEXT('MULTIPOLYGON(((0 0,10 10,-10 10,0 0)),((0 0,-10 -10,10 -10,0 0)))')))),
(615, ST_MULTIPOLYGONFROMWKB(ST_ASWKB(ST_MULTIPOLYGONFROMTEXT('MULTIPOLYGON(((0 0,5 5,10 0,5 -5,0 0)))'))));
# Inserting GEOMETRYCOLLECTION Geometry Values 
INSERT INTO gis_geometrycollection VALUES
(701, ST_GEOMCOLLFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))')),
(702, ST_GEOMCOLLFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0), LINESTRING(0 0,10 10))')),
(703, ST_GEOMCOLLFROMTEXT('GEOMETRYCOLLECTION(POINT(5 5), POLYGON((0 0,0 10,10 10,10 0,0 0)))'));
INSERT INTO gis_geometrycollection VALUES
(704, ST_GEOMETRYCOLLECTIONFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0), LINESTRING(0 0,10 10))')),
(705, ST_GEOMETRYCOLLECTIONFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0),'
                                                           'LINESTRING(0 0,10 10),'
                                                           'POLYGON((0 0,0 10,10 10,10 0, 0 0)))')),
(706, ST_GEOMETRYCOLLECTIONFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(0 0,5 5,10 10),'
                                                           'MULTILINESTRING((0 0,10 10),(0 10,10 0)),'
                                                           'MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0)),'
                                                                        '((0 0,-10 0,-10 -10,0 -10,0 0))))'));
INSERT INTO gis_geometrycollection VALUES
(707, ST_GEOMCOLLFROMWKB(ST_ASWKB(GEOMETRYCOLLECTION(POINT(0,0))))),
(708, ST_GEOMCOLLFROMWKB(ST_ASWKB(GEOMETRYCOLLECTION(POINT(0,0),
LINESTRING(POINT(0,0), POINT(2,2), POINT(4,4), POINT(6,6), POINT(8,8)),
POLYGON(LINESTRING(POINT(0,0), POINT(0,10), POINT(10,10), POINT(10,0), POINT(0,0)),
LINESTRING(POINT(4,4), POINT(4,6), POINT(6,6), POINT(6,4), POINT(4,4))))))),
(709, ST_GEOMCOLLFROMWKB(ST_ASWKB(GEOMETRYCOLLECTION(MULTIPOINT(POINT(0,0), POINT(5,5), POINT(10,10)),
MULTILINESTRING(LINESTRING(POINT(0,0), POINT(2,2), POINT(4,4), POINT(6,6), POINT(8,8)),
LINESTRING(POINT(10,10), POINT(5,5), POINT(0,10))),
MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0), POINT(0,10), POINT(10,10), POINT(10,0), POINT(0,0)),
LINESTRING(POINT(4,4), POINT(4,6), POINT(6,6), POINT(6,4), POINT(4,4))),
POLYGON(LINESTRING(POINT(0,0), POINT(-10,0), POINT(-10,-10), POINT(0,0))))))));
INSERT INTO gis_geometrycollection VALUES
(710, ST_GEOMETRYCOLLECTIONFROMWKB(ST_ASWKB(GEOMETRYCOLLECTION(POINT(0,0),
LINESTRING(POINT(0,0), POINT(100,100)))))),
(711, ST_GEOMETRYCOLLECTIONFROMWKB(ST_ASWKB(GEOMETRYCOLLECTION(POINT(10,10),
LINESTRING(POINT(10,10), POINT(12,12), POINT(14,14), POINT(16,16), POINT(18,18)),
POLYGON(LINESTRING(POINT(0,0), POINT(0,10), POINT(10,10), POINT(10,0), POINT(0,0)),
LINESTRING(POINT(4,4), POINT(4,6), POINT(6,6), POINT(6,4), POINT(4,4))))))),
(712, ST_GEOMETRYCOLLECTIONFROMWKB(ST_ASWKB(GEOMETRYCOLLECTION(MULTIPOINT(POINT(10,10), POINT(15,15), POINT(20,20)),
MULTILINESTRING(LINESTRING(POINT(0,0), POINT(2,2), POINT(4,4), POINT(6,6), POINT(8,8)),
LINESTRING(POINT(10,10), POINT(5,5), POINT(0,10))),
MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0), POINT(0,10), POINT(10,10), POINT(10,0), POINT(0,0)),
LINESTRING(POINT(4,4), POINT(4,6), POINT(6,6), POINT(6,4), POINT(4,4))),
POLYGON(LINESTRING(POINT(0,0), POINT(-10,0), POINT(-10,-10), POINT(0,0))))))));
INSERT INTO gis_geometrycollection VALUES
(713, ST_GEOMCOLLFROMWKB(ST_ASWKB(ST_GEOMCOLLFROMTEXT('GEOMETRYCOLLECTION('
                                                                'POINT(0 0),'
                                                                'LINESTRING(0 0,10 10),'
                                                                'POLYGON((0 0,0 10,10 10,10 0, 0 0)),'
                                                                'MULTIPOINT(0 0,2 2,4 4,6 6,8 8,10 10),'
                                                                'MULTILINESTRING((0 0,10 10),(0 10,10 0)),'
                                                                'MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)),((5 5,5 10,10 10,10 5,5 5))))')))),
(714, ST_GEOMCOLLFROMWKB(ST_ASWKB(ST_GEOMCOLLFROMTEXT('GEOMETRYCOLLECTION('
                                                                'GEOMETRYCOLLECTION('
                                                                      'POINT(0 0)),'
                                                                'GEOMETRYCOLLECTION('
                                                                      'LINESTRING(0 0,10 10)),'
                                                                'GEOMETRYCOLLECTION('
                                                                      'POLYGON((0 0,0 10,10 10,10 0, 0 0))),'
                                                                'GEOMETRYCOLLECTION('
                                                                       'MULTIPOINT(0 0,2 2,4 4,6 6,8 8,10 10)),'
                                                                'GEOMETRYCOLLECTION('
                                                                       'MULTILINESTRING((0 0,10 10),(0 10,10 0))),'
                                                                'GEOMETRYCOLLECTION('
                                                                       'MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)),((5 5,5 10,10 10,10 5,5 5)))))')))),
(715, ST_GEOMCOLLFROMWKB(ST_ASWKB(ST_GEOMCOLLFROMTEXT('GEOMETRYCOLLECTION('
                                                                'GEOMETRYCOLLECTION(),'
                                                                'POINT(0 0),'
                                                                'GEOMETRYCOLLECTION('
                                                                      'LINESTRING(0 0,10 10),'
                                                                      'GEOMETRYCOLLECTION('
                                                                            'GEOMETRYCOLLECTION())),'
                                                                'GEOMETRYCOLLECTION(),'
                                                                'GEOMETRYCOLLECTION('
                                                                       'GEOMETRYCOLLECTION()),'
                                                                'POLYGON((0 0,0 10,10 10,10 0, 0 0)),'
                                                                'MULTIPOINT(0 0,2 2,4 4,6 6,8 8,10 10),'
                                                                'MULTILINESTRING((0 0,10 10),(0 10,10 0)),'
                                                                'MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)),((5 5,5 10,10 10,10 5,5 5))))'))));
# Inserting the spatial values of all kinds to the parent class
INSERT INTO gis_geometry SELECT * FROM gis_point;
INSERT INTO gis_geometry SELECT * FROM gis_linestring;
INSERT INTO gis_geometry SELECT * FROM gis_polygon;
INSERT INTO gis_geometry SELECT * FROM gis_multi_point;
INSERT INTO gis_geometry SELECT * FROM gis_multi_linestring;
INSERT INTO gis_geometry SELECT * FROM gis_multi_polygon;
INSERT INTO gis_geometry SELECT * FROM gis_geometrycollection;
# Checking the integrity of the above insert statements 
SELECT COUNT(g) FROM gis_geometry;
COUNT(g)
105
SELECT COUNT(ST_ASTEXT(g)) FROM gis_geometry;
COUNT(ST_ASTEXT(g))
105
SELECT COUNT(DISTINCT(g)) FROM gis_geometry;
COUNT(DISTINCT(g))
87
SELECT COUNT(DISTINCT(ST_ASTEXT(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_ASTEXT(g)))
87
# Displaying the inserted spatial Data
SELECT fid, ST_ASTEXT(g) FROM gis_point;
fid	ST_ASTEXT(g)
101	POINT(0 0)
102	POINT(1 0)
103	POINT(0 1)
104	POINT(1 1)
105	POINT(-1 1)
106	POINT(0 0)
107	POINT(10 0)
108	POINT(0 10)
109	POINT(-10 0)
110	POINT(0 -10)
111	POINT(1 1)
112	POINT(1000 1000)
113	POINT(1000 -1000)
114	POINT(-1000 1000)
115	POINT(-1000 -1000)
SELECT fid, ST_ASTEXT(g) FROM gis_linestring;
fid	ST_ASTEXT(g)
201	LINESTRING(0 0,5 5)
202	LINESTRING(0 0,2 2,4 4)
203	LINESTRING(0 0,5 5,10 10)
204	LINESTRING(10 10,5 5)
205	LINESTRING(0 0,12 12,24 24)
206	LINESTRING(0 0,50 50,100 100)
207	LINESTRING(0 0,5 5)
208	LINESTRING(0 0,-5 -5,-10 10)
209	LINESTRING(0 0,2 2,4 4,6 6,8 8)
210	LINESTRING(0 0,5 5)
211	LINESTRING(0 0,-50 -50,10 -10)
212	LINESTRING(0 0,1000 1000,1000 -1000)
213	LINESTRING(1000 1000,1000 -1000)
214	LINESTRING(1 1,2 2,3 3,4 4,5 5,6 6,7 7,8 8,9 9)
215	LINESTRING(10 10,10 -10,-10 -10,-10 10,10 10)
SELECT fid, ST_ASTEXT(g) FROM gis_polygon;
fid	ST_ASTEXT(g)
301	POLYGON((0 0,0 5,5 5,0 0))
302	POLYGON((0 0,0 5,5 5,5 0,0 0))
303	POLYGON((0 0,0 10,10 10,10 0,0 0))
304	POLYGON((0 0,0 50,50 50,50 0,0 0))
305	POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4))
306	POLYGON((0 0,0 10,10 10,10 0,0 0),(2 2,2 4,4 4,4 2,2 2),(6 6,6 8,8 8,8 6,6 6))
307	POLYGON((0 0,0 5,5 5,0 0))
308	POLYGON((0 0,0 15,15 15,15 0,0 0))
309	POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4))
310	POLYGON((0 0,0 5,5 5,0 0))
311	POLYGON((10 10,10 15,15 15,15 10,10 10))
312	POLYGON((10 10,10 20,20 20,20 10,10 10),(14 14,14 16,16 16,16 14,14 14))
313	POLYGON((0 0,0 10,10 10,10 0,5 5,0 0))
314	POLYGON((10 0,10 10,0 10,-10 10,-10 0,-10 -10,0 10,10 -10,10 0))
315	POLYGON((0 0,0 10,10 10,10 0,0 0),(2 2,2 4,4 4,4 2,2 2),(6 6,6 8,8 8,8 6,6 6))
SELECT fid, ST_ASTEXT(g) FROM gis_multi_point;
fid	ST_ASTEXT(g)
401	MULTIPOINT((0 0))
402	MULTIPOINT((0 0),(2 2),(4 4))
403	MULTIPOINT((0 0),(5 5),(10 10))
404	MULTIPOINT((0 0),(100 100))
405	MULTIPOINT((0 0),(1000 1000))
406	MULTIPOINT((1000 1000),(1000 -1000),(-1000 1000),(-1000 -1000))
407	MULTIPOINT((0 0))
408	MULTIPOINT((0 0),(10 10))
409	MULTIPOINT((0 0),(2 2),(4 4),(6 6))
410	MULTIPOINT((0 0))
411	MULTIPOINT((0 0),(1000 1000))
412	MULTIPOINT((1000 1000),(-1000 1000),(1000 -1000),(-1000 -1000))
413	MULTIPOINT((0 0))
414	MULTIPOINT((0 0),(1000 1000),(-1000 -1000))
415	MULTIPOINT((1000 1000),(1000 -1000),(-1000 1000),(-1000 -1000),(1000 1000))
SELECT fid, ST_ASTEXT(g) FROM gis_multi_linestring;
fid	ST_ASTEXT(g)
501	MULTILINESTRING((0 0,2 2))
502	MULTILINESTRING((0 0,2 2,4 4))
503	MULTILINESTRING((0 0,2 2,4 4),(6 6,8 8,10 10))
504	MULTILINESTRING((0 0,100 100,-100 -100))
505	MULTILINESTRING((1000 1000,-1000 -1000))
506	MULTILINESTRING((1000 1000,-1000 -1000),(1000 -1000,-1000 1000))
507	MULTILINESTRING((0 0,2 2))
508	MULTILINESTRING((0 0,12 12,24 24))
509	MULTILINESTRING((0 0,2 2,4 4),(6 6,8 8,10 10))
510	MULTILINESTRING((0 0,2 2,4 4))
511	MULTILINESTRING((0 0,1 1,2 2))
512	MULTILINESTRING((0 0,12 12,24 24),(36 36,48 48,50 50))
513	MULTILINESTRING((0 0,10 10),(0 10,10 0))
514	MULTILINESTRING((0 0,10 10,-10 10,0 0),(0 0,-10 -10,10 -10,0 0))
515	MULTILINESTRING((0 0,0 100),(0 0,100 0),(0 0,0 -100),(0 0,-100 0))
SELECT fid, ST_ASTEXT(g) FROM gis_multi_polygon;
fid	ST_ASTEXT(g)
601	MULTIPOLYGON(((0 0,0 5,5 5,0 0)))
602	MULTIPOLYGON(((0 0,0 5,5 5,0 0)),((5 5,5 10,10 10,5 5)))
603	MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)))
604	MULTIPOLYGON(((0 0,0 5,5 5,0 0)))
605	MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)),((0 0,0 -2,-2 -2,0 0)))
606	MULTIPOLYGON(((0 0,5 5,-5 5,0 0)),((0 0,-5 -5,5 -5,0 0)))
607	MULTIPOLYGON(((0 0,5 0,5 5,0 5,0 0)))
608	MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))
609	MULTIPOLYGON(((0 0,5 0,5 5,0 5,0 0)),((0 0,-5 0,-5 -5,0 -5,0 0)))
610	MULTIPOLYGON(((0 0,-5 0,-5 -5,0 -5,0 0)))
611	MULTIPOLYGON(((10 10,20 10,20 20,10 20,10 10),(14 14,14 16,16 16,16 14,14 14)))
612	MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)),((0 0,-5 0,-5 -5,0 -5,0 0)))
613	MULTIPOLYGON(((0 0,5 5,5 -5,0 0)),((0 0,-5 5,-5 -5,0 0)))
614	MULTIPOLYGON(((0 0,10 10,-10 10,0 0)),((0 0,-10 -10,10 -10,0 0)))
615	MULTIPOLYGON(((0 0,5 5,10 0,5 -5,0 0)))
SELECT fid, ST_ASTEXT(g) FROM gis_geometrycollection;
fid	ST_ASTEXT(g)
701	GEOMETRYCOLLECTION(POINT(0 0))
702	GEOMETRYCOLLECTION(POINT(0 0),LINESTRING(0 0,10 10))
703	GEOMETRYCOLLECTION(POINT(5 5),POLYGON((0 0,0 10,10 10,10 0,0 0)))
704	GEOMETRYCOLLECTION(POINT(0 0),LINESTRING(0 0,10 10))
705	GEOMETRYCOLLECTION(POINT(0 0),LINESTRING(0 0,10 10),POLYGON((0 0,0 10,10 10,10 0,0 0)))
706	GEOMETRYCOLLECTION(MULTIPOINT((0 0),(5 5),(10 10)),MULTILINESTRING((0 0,10 10),(0 10,10 0)),MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0)),((0 0,-10 0,-10 -10,0 -10,0 0))))
707	GEOMETRYCOLLECTION(POINT(0 0))
708	GEOMETRYCOLLECTION(POINT(0 0),LINESTRING(0 0,2 2,4 4,6 6,8 8),POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)))
709	GEOMETRYCOLLECTION(MULTIPOINT((0 0),(5 5),(10 10)),MULTILINESTRING((0 0,2 2,4 4,6 6,8 8),(10 10,5 5,0 10)),MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)),((0 0,-10 0,-10 -10,0 0))))
710	GEOMETRYCOLLECTION(POINT(0 0),LINESTRING(0 0,100 100))
711	GEOMETRYCOLLECTION(POINT(10 10),LINESTRING(10 10,12 12,14 14,16 16,18 18),POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)))
712	GEOMETRYCOLLECTION(MULTIPOINT((10 10),(15 15),(20 20)),MULTILINESTRING((0 0,2 2,4 4,6 6,8 8),(10 10,5 5,0 10)),MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)),((0 0,-10 0,-10 -10,0 0))))
713	GEOMETRYCOLLECTION(POINT(0 0),LINESTRING(0 0,10 10),POLYGON((0 0,0 10,10 10,10 0,0 0)),MULTIPOINT((0 0),(2 2),(4 4),(6 6),(8 8),(10 10)),MULTILINESTRING((0 0,10 10),(0 10,10 0)),MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)),((5 5,5 10,10 10,10 5,5 5))))
714	GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(0 0)),GEOMETRYCOLLECTION(LINESTRING(0 0,10 10)),GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0))),GEOMETRYCOLLECTION(MULTIPOINT((0 0),(2 2),(4 4),(6 6),(8 8),(10 10))),GEOMETRYCOLLECTION(MULTILINESTRING((0 0,10 10),(0 10,10 0))),GEOMETRYCOLLECTION(MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)),((5 5,5 10,10 10,10 5,5 5)))))
715	GEOMETRYCOLLECTION(GEOMETRYCOLLECTION EMPTY,POINT(0 0),GEOMETRYCOLLECTION(LINESTRING(0 0,10 10),GEOMETRYCOLLECTION(GEOMETRYCOLLECTION EMPTY)),GEOMETRYCOLLECTION EMPTY,GEOMETRYCOLLECTION(GEOMETRYCOLLECTION EMPTY),POLYGON((0 0,0 10,10 10,10 0,0 0)),MULTIPOINT((0 0),(2 2),(4 4),(6 6),(8 8),(10 10)),MULTILINESTRING((0 0,10 10),(0 10,10 0)),MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)),((5 5,5 10,10 10,10 5,5 5))))
SELECT fid, ST_ASTEXT(g) FROM gis_geometry;
fid	ST_ASTEXT(g)
101	POINT(0 0)
102	POINT(1 0)
103	POINT(0 1)
104	POINT(1 1)
105	POINT(-1 1)
106	POINT(0 0)
107	POINT(10 0)
108	POINT(0 10)
109	POINT(-10 0)
110	POINT(0 -10)
111	POINT(1 1)
112	POINT(1000 1000)
113	POINT(1000 -1000)
114	POINT(-1000 1000)
115	POINT(-1000 -1000)
201	LINESTRING(0 0,5 5)
202	LINESTRING(0 0,2 2,4 4)
203	LINESTRING(0 0,5 5,10 10)
204	LINESTRING(10 10,5 5)
205	LINESTRING(0 0,12 12,24 24)
206	LINESTRING(0 0,50 50,100 100)
207	LINESTRING(0 0,5 5)
208	LINESTRING(0 0,-5 -5,-10 10)
209	LINESTRING(0 0,2 2,4 4,6 6,8 8)
210	LINESTRING(0 0,5 5)
211	LINESTRING(0 0,-50 -50,10 -10)
212	LINESTRING(0 0,1000 1000,1000 -1000)
213	LINESTRING(1000 1000,1000 -1000)
214	LINESTRING(1 1,2 2,3 3,4 4,5 5,6 6,7 7,8 8,9 9)
215	LINESTRING(10 10,10 -10,-10 -10,-10 10,10 10)
301	POLYGON((0 0,0 5,5 5,0 0))
302	POLYGON((0 0,0 5,5 5,5 0,0 0))
303	POLYGON((0 0,0 10,10 10,10 0,0 0))
304	POLYGON((0 0,0 50,50 50,50 0,0 0))
305	POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4))
306	POLYGON((0 0,0 10,10 10,10 0,0 0),(2 2,2 4,4 4,4 2,2 2),(6 6,6 8,8 8,8 6,6 6))
307	POLYGON((0 0,0 5,5 5,0 0))
308	POLYGON((0 0,0 15,15 15,15 0,0 0))
309	POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4))
310	POLYGON((0 0,0 5,5 5,0 0))
311	POLYGON((10 10,10 15,15 15,15 10,10 10))
312	POLYGON((10 10,10 20,20 20,20 10,10 10),(14 14,14 16,16 16,16 14,14 14))
313	POLYGON((0 0,0 10,10 10,10 0,5 5,0 0))
314	POLYGON((10 0,10 10,0 10,-10 10,-10 0,-10 -10,0 10,10 -10,10 0))
315	POLYGON((0 0,0 10,10 10,10 0,0 0),(2 2,2 4,4 4,4 2,2 2),(6 6,6 8,8 8,8 6,6 6))
401	MULTIPOINT((0 0))
402	MULTIPOINT((0 0),(2 2),(4 4))
403	MULTIPOINT((0 0),(5 5),(10 10))
404	MULTIPOINT((0 0),(100 100))
405	MULTIPOINT((0 0),(1000 1000))
406	MULTIPOINT((1000 1000),(1000 -1000),(-1000 1000),(-1000 -1000))
407	MULTIPOINT((0 0))
408	MULTIPOINT((0 0),(10 10))
409	MULTIPOINT((0 0),(2 2),(4 4),(6 6))
410	MULTIPOINT((0 0))
411	MULTIPOINT((0 0),(1000 1000))
412	MULTIPOINT((1000 1000),(-1000 1000),(1000 -1000),(-1000 -1000))
413	MULTIPOINT((0 0))
414	MULTIPOINT((0 0),(1000 1000),(-1000 -1000))
415	MULTIPOINT((1000 1000),(1000 -1000),(-1000 1000),(-1000 -1000),(1000 1000))
501	MULTILINESTRING((0 0,2 2))
502	MULTILINESTRING((0 0,2 2,4 4))
503	MULTILINESTRING((0 0,2 2,4 4),(6 6,8 8,10 10))
504	MULTILINESTRING((0 0,100 100,-100 -100))
505	MULTILINESTRING((1000 1000,-1000 -1000))
506	MULTILINESTRING((1000 1000,-1000 -1000),(1000 -1000,-1000 1000))
507	MULTILINESTRING((0 0,2 2))
508	MULTILINESTRING((0 0,12 12,24 24))
509	MULTILINESTRING((0 0,2 2,4 4),(6 6,8 8,10 10))
510	MULTILINESTRING((0 0,2 2,4 4))
511	MULTILINESTRING((0 0,1 1,2 2))
512	MULTILINESTRING((0 0,12 12,24 24),(36 36,48 48,50 50))
513	MULTILINESTRING((0 0,10 10),(0 10,10 0))
514	MULTILINESTRING((0 0,10 10,-10 10,0 0),(0 0,-10 -10,10 -10,0 0))
515	MULTILINESTRING((0 0,0 100),(0 0,100 0),(0 0,0 -100),(0 0,-100 0))
601	MULTIPOLYGON(((0 0,0 5,5 5,0 0)))
602	MULTIPOLYGON(((0 0,0 5,5 5,0 0)),((5 5,5 10,10 10,5 5)))
603	MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)))
604	MULTIPOLYGON(((0 0,0 5,5 5,0 0)))
605	MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)),((0 0,0 -2,-2 -2,0 0)))
606	MULTIPOLYGON(((0 0,5 5,-5 5,0 0)),((0 0,-5 -5,5 -5,0 0)))
607	MULTIPOLYGON(((0 0,5 0,5 5,0 5,0 0)))
608	MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))
609	MULTIPOLYGON(((0 0,5 0,5 5,0 5,0 0)),((0 0,-5 0,-5 -5,0 -5,0 0)))
610	MULTIPOLYGON(((0 0,-5 0,-5 -5,0 -5,0 0)))
611	MULTIPOLYGON(((10 10,20 10,20 20,10 20,10 10),(14 14,14 16,16 16,16 14,14 14)))
612	MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)),((0 0,-5 0,-5 -5,0 -5,0 0)))
613	MULTIPOLYGON(((0 0,5 5,5 -5,0 0)),((0 0,-5 5,-5 -5,0 0)))
614	MULTIPOLYGON(((0 0,10 10,-10 10,0 0)),((0 0,-10 -10,10 -10,0 0)))
615	MULTIPOLYGON(((0 0,5 5,10 0,5 -5,0 0)))
701	GEOMETRYCOLLECTION(POINT(0 0))
702	GEOMETRYCOLLECTION(POINT(0 0),LINESTRING(0 0,10 10))
703	GEOMETRYCOLLECTION(POINT(5 5),POLYGON((0 0,0 10,10 10,10 0,0 0)))
704	GEOMETRYCOLLECTION(POINT(0 0),LINESTRING(0 0,10 10))
705	GEOMETRYCOLLECTION(POINT(0 0),LINESTRING(0 0,10 10),POLYGON((0 0,0 10,10 10,10 0,0 0)))
706	GEOMETRYCOLLECTION(MULTIPOINT((0 0),(5 5),(10 10)),MULTILINESTRING((0 0,10 10),(0 10,10 0)),MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0)),((0 0,-10 0,-10 -10,0 -10,0 0))))
707	GEOMETRYCOLLECTION(POINT(0 0))
708	GEOMETRYCOLLECTION(POINT(0 0),LINESTRING(0 0,2 2,4 4,6 6,8 8),POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)))
709	GEOMETRYCOLLECTION(MULTIPOINT((0 0),(5 5),(10 10)),MULTILINESTRING((0 0,2 2,4 4,6 6,8 8),(10 10,5 5,0 10)),MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)),((0 0,-10 0,-10 -10,0 0))))
710	GEOMETRYCOLLECTION(POINT(0 0),LINESTRING(0 0,100 100))
711	GEOMETRYCOLLECTION(POINT(10 10),LINESTRING(10 10,12 12,14 14,16 16,18 18),POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)))
712	GEOMETRYCOLLECTION(MULTIPOINT((10 10),(15 15),(20 20)),MULTILINESTRING((0 0,2 2,4 4,6 6,8 8),(10 10,5 5,0 10)),MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)),((0 0,-10 0,-10 -10,0 0))))
713	GEOMETRYCOLLECTION(POINT(0 0),LINESTRING(0 0,10 10),POLYGON((0 0,0 10,10 10,10 0,0 0)),MULTIPOINT((0 0),(2 2),(4 4),(6 6),(8 8),(10 10)),MULTILINESTRING((0 0,10 10),(0 10,10 0)),MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)),((5 5,5 10,10 10,10 5,5 5))))
714	GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(0 0)),GEOMETRYCOLLECTION(LINESTRING(0 0,10 10)),GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0))),GEOMETRYCOLLECTION(MULTIPOINT((0 0),(2 2),(4 4),(6 6),(8 8),(10 10))),GEOMETRYCOLLECTION(MULTILINESTRING((0 0,10 10),(0 10,10 0))),GEOMETRYCOLLECTION(MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)),((5 5,5 10,10 10,10 5,5 5)))))
715	GEOMETRYCOLLECTION(GEOMETRYCOLLECTION EMPTY,POINT(0 0),GEOMETRYCOLLECTION(LINESTRING(0 0,10 10),GEOMETRYCOLLECTION(GEOMETRYCOLLECTION EMPTY)),GEOMETRYCOLLECTION EMPTY,GEOMETRYCOLLECTION(GEOMETRYCOLLECTION EMPTY),POLYGON((0 0,0 10,10 10,10 0,0 0)),MULTIPOINT((0 0),(2 2),(4 4),(6 6),(8 8),(10 10)),MULTILINESTRING((0 0,10 10),(0 10,10 0)),MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)),((5 5,5 10,10 10,10 5,5 5))))
# Checking the geometry type of the spatial objects inserted into GEOMETRY class
SELECT fid, ST_GEOMETRYTYPE(g) FROM gis_geometry;
fid	ST_GEOMETRYTYPE(g)
101	POINT
102	POINT
103	POINT
104	POINT
105	POINT
106	POINT
107	POINT
108	POINT
109	POINT
110	POINT
111	POINT
112	POINT
113	POINT
114	POINT
115	POINT
201	LINESTRING
202	LINESTRING
203	LINESTRING
204	LINESTRING
205	LINESTRING
206	LINESTRING
207	LINESTRING
208	LINESTRING
209	LINESTRING
210	LINESTRING
211	LINESTRING
212	LINESTRING
213	LINESTRING
214	LINESTRING
215	LINESTRING
301	POLYGON
302	POLYGON
303	POLYGON
304	POLYGON
305	POLYGON
306	POLYGON
307	POLYGON
308	POLYGON
309	POLYGON
310	POLYGON
311	POLYGON
312	POLYGON
313	POLYGON
314	POLYGON
315	POLYGON
401	MULTIPOINT
402	MULTIPOINT
403	MULTIPOINT
404	MULTIPOINT
405	MULTIPOINT
406	MULTIPOINT
407	MULTIPOINT
408	MULTIPOINT
409	MULTIPOINT
410	MULTIPOINT
411	MULTIPOINT
412	MULTIPOINT
413	MULTIPOINT
414	MULTIPOINT
415	MULTIPOINT
501	MULTILINESTRING
502	MULTILINESTRING
503	MULTILINESTRING
504	MULTILINESTRING
505	MULTILINESTRING
506	MULTILINESTRING
507	MULTILINESTRING
508	MULTILINESTRING
509	MULTILINESTRING
510	MULTILINESTRING
511	MULTILINESTRING
512	MULTILINESTRING
513	MULTILINESTRING
514	MULTILINESTRING
515	MULTILINESTRING
601	MULTIPOLYGON
602	MULTIPOLYGON
603	MULTIPOLYGON
604	MULTIPOLYGON
605	MULTIPOLYGON
606	MULTIPOLYGON
607	MULTIPOLYGON
608	MULTIPOLYGON
609	MULTIPOLYGON
610	MULTIPOLYGON
611	MULTIPOLYGON
612	MULTIPOLYGON
613	MULTIPOLYGON
614	MULTIPOLYGON
615	MULTIPOLYGON
701	GEOMCOLLECTION
702	GEOMCOLLECTION
703	GEOMCOLLECTION
704	GEOMCOLLECTION
705	GEOMCOLLECTION
706	GEOMCOLLECTION
707	GEOMCOLLECTION
708	GEOMCOLLECTION
709	GEOMCOLLECTION
710	GEOMCOLLECTION
711	GEOMCOLLECTION
712	GEOMCOLLECTION
713	GEOMCOLLECTION
714	GEOMCOLLECTION
715	GEOMCOLLECTION
SELECT COUNT(ST_GEOMETRYTYPE(g)) FROM gis_geometry;
COUNT(ST_GEOMETRYTYPE(g))
105
SELECT COUNT(DISTINCT(ST_GEOMETRYTYPE(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_GEOMETRYTYPE(g)))
7
# Checking the ST_SRID of the spatial objects inserted into GEOMETRY class
SELECT fid, ST_SRID(g) FROM gis_geometry;
fid	ST_SRID(g)
101	0
102	0
103	0
104	0
105	0
106	0
107	0
108	0
109	0
110	0
111	0
112	0
113	0
114	0
115	0
201	0
202	0
203	0
204	0
205	0
206	0
207	0
208	0
209	0
210	0
211	0
212	0
213	0
214	0
215	0
301	0
302	0
303	0
304	0
305	0
306	0
307	0
308	0
309	0
310	0
311	0
312	0
313	0
314	0
315	0
401	0
402	0
403	0
404	0
405	0
406	0
407	0
408	0
409	0
410	0
411	0
412	0
413	0
414	0
415	0
501	0
502	0
503	0
504	0
505	0
506	0
507	0
508	0
509	0
510	0
511	0
512	0
513	0
514	0
515	0
601	0
602	0
603	0
604	0
605	0
606	0
607	0
608	0
609	0
610	0
611	0
612	0
613	0
614	0
615	0
701	0
702	0
703	0
704	0
705	0
706	0
707	0
708	0
709	0
710	0
711	0
712	0
713	0
714	0
715	0
SELECT COUNT(ST_SRID(g)) FROM gis_geometry;
COUNT(ST_SRID(g))
105
SELECT COUNT(DISTINCT(ST_SRID(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_SRID(g)))
1
# Checking the Dimension of the spatial objects inserted into GEOMETRY class
SELECT fid, ST_DIMENSION(g) FROM gis_geometry;
fid	ST_DIMENSION(g)
101	0
102	0
103	0
104	0
105	0
106	0
107	0
108	0
109	0
110	0
111	0
112	0
113	0
114	0
115	0
201	1
202	1
203	1
204	1
205	1
206	1
207	1
208	1
209	1
210	1
211	1
212	1
213	1
214	1
215	1
301	2
302	2
303	2
304	2
305	2
306	2
307	2
308	2
309	2
310	2
311	2
312	2
313	2
314	2
315	2
401	0
402	0
403	0
404	0
405	0
406	0
407	0
408	0
409	0
410	0
411	0
412	0
413	0
414	0
415	0
501	1
502	1
503	1
504	1
505	1
506	1
507	1
508	1
509	1
510	1
511	1
512	1
513	1
514	1
515	1
601	2
602	2
603	2
604	2
605	2
606	2
607	2
608	2
609	2
610	2
611	2
612	2
613	2
614	2
615	2
701	0
702	1
703	2
704	1
705	2
706	2
707	0
708	2
709	2
710	1
711	2
712	2
713	2
714	2
715	NULL
SELECT COUNT(ST_DIMENSION(g)) FROM gis_geometry;
COUNT(ST_DIMENSION(g))
104
SELECT COUNT(DISTINCT(ST_DIMENSION(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_DIMENSION(g)))
3
# Checking if the geometries are simple or not
SELECT fid, ST_ISSIMPLE(g) FROM gis_geometry;
fid	ST_ISSIMPLE(g)
101	1
102	1
103	1
104	1
105	1
106	1
107	1
108	1
109	1
110	1
111	1
112	1
113	1
114	1
115	1
201	1
202	1
203	1
204	1
205	1
206	1
207	1
208	1
209	1
210	1
211	1
212	1
213	1
214	1
215	1
301	1
302	1
303	1
304	1
305	1
306	1
307	1
308	1
309	1
310	1
311	1
312	1
313	1
314	1
315	1
401	1
402	1
403	1
404	1
405	1
406	1
407	1
408	1
409	1
410	1
411	1
412	1
413	1
414	1
415	0
501	1
502	1
503	1
504	0
505	1
506	0
507	1
508	1
509	1
510	1
511	1
512	1
513	0
514	0
515	1
601	1
602	1
603	1
604	1
605	1
606	1
607	1
608	1
609	1
610	1
611	1
612	1
613	1
614	1
615	1
701	1
702	1
703	0
704	1
705	0
706	0
707	1
708	0
709	0
710	1
711	1
712	0
713	0
714	0
715	0
SELECT COUNT(ST_ISSIMPLE(g)) FROM gis_geometry;
COUNT(ST_ISSIMPLE(g))
105
SELECT COUNT(DISTINCT(ST_ISSIMPLE(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_ISSIMPLE(g)))
2
# Checking the Envelope of the spatial objects inserted into GEOMETRY class
SELECT fid, ST_ASTEXT(ST_ENVELOPE(g)) FROM gis_geometry;
fid	ST_ASTEXT(ST_ENVELOPE(g))
101	POINT(0 0)
102	POINT(1 0)
103	POINT(0 1)
104	POINT(1 1)
105	POINT(-1 1)
106	POINT(0 0)
107	POINT(10 0)
108	POINT(0 10)
109	POINT(-10 0)
110	POINT(0 -10)
111	POINT(1 1)
112	POINT(1000 1000)
113	POINT(1000 -1000)
114	POINT(-1000 1000)
115	POINT(-1000 -1000)
201	POLYGON((0 0,5 0,5 5,0 5,0 0))
202	POLYGON((0 0,4 0,4 4,0 4,0 0))
203	POLYGON((0 0,10 0,10 10,0 10,0 0))
204	POLYGON((5 5,10 5,10 10,5 10,5 5))
205	POLYGON((0 0,24 0,24 24,0 24,0 0))
206	POLYGON((0 0,100 0,100 100,0 100,0 0))
207	POLYGON((0 0,5 0,5 5,0 5,0 0))
208	POLYGON((-10 -5,0 -5,0 10,-10 10,-10 -5))
209	POLYGON((0 0,8 0,8 8,0 8,0 0))
210	POLYGON((0 0,5 0,5 5,0 5,0 0))
211	POLYGON((-50 -50,10 -50,10 0,-50 0,-50 -50))
212	POLYGON((0 -1000,1000 -1000,1000 1000,0 1000,0 -1000))
213	LINESTRING(1000 -1000,1000 1000)
214	POLYGON((1 1,9 1,9 9,1 9,1 1))
215	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
301	POLYGON((0 0,5 0,5 5,0 5,0 0))
302	POLYGON((0 0,5 0,5 5,0 5,0 0))
303	POLYGON((0 0,10 0,10 10,0 10,0 0))
304	POLYGON((0 0,50 0,50 50,0 50,0 0))
305	POLYGON((0 0,10 0,10 10,0 10,0 0))
306	POLYGON((0 0,10 0,10 10,0 10,0 0))
307	POLYGON((0 0,5 0,5 5,0 5,0 0))
308	POLYGON((0 0,15 0,15 15,0 15,0 0))
309	POLYGON((0 0,10 0,10 10,0 10,0 0))
310	POLYGON((0 0,5 0,5 5,0 5,0 0))
311	POLYGON((10 10,15 10,15 15,10 15,10 10))
312	POLYGON((10 10,20 10,20 20,10 20,10 10))
313	POLYGON((0 0,10 0,10 10,0 10,0 0))
314	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
315	POLYGON((0 0,10 0,10 10,0 10,0 0))
401	POINT(0 0)
402	POLYGON((0 0,4 0,4 4,0 4,0 0))
403	POLYGON((0 0,10 0,10 10,0 10,0 0))
404	POLYGON((0 0,100 0,100 100,0 100,0 0))
405	POLYGON((0 0,1000 0,1000 1000,0 1000,0 0))
406	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
407	POINT(0 0)
408	POLYGON((0 0,10 0,10 10,0 10,0 0))
409	POLYGON((0 0,6 0,6 6,0 6,0 0))
410	POINT(0 0)
411	POLYGON((0 0,1000 0,1000 1000,0 1000,0 0))
412	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
413	POINT(0 0)
414	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
415	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
501	POLYGON((0 0,2 0,2 2,0 2,0 0))
502	POLYGON((0 0,4 0,4 4,0 4,0 0))
503	POLYGON((0 0,10 0,10 10,0 10,0 0))
504	POLYGON((-100 -100,100 -100,100 100,-100 100,-100 -100))
505	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
506	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
507	POLYGON((0 0,2 0,2 2,0 2,0 0))
508	POLYGON((0 0,24 0,24 24,0 24,0 0))
509	POLYGON((0 0,10 0,10 10,0 10,0 0))
510	POLYGON((0 0,4 0,4 4,0 4,0 0))
511	POLYGON((0 0,2 0,2 2,0 2,0 0))
512	POLYGON((0 0,50 0,50 50,0 50,0 0))
513	POLYGON((0 0,10 0,10 10,0 10,0 0))
514	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
515	POLYGON((-100 -100,100 -100,100 100,-100 100,-100 -100))
601	POLYGON((0 0,5 0,5 5,0 5,0 0))
602	POLYGON((0 0,10 0,10 10,0 10,0 0))
603	POLYGON((0 0,10 0,10 10,0 10,0 0))
604	POLYGON((0 0,5 0,5 5,0 5,0 0))
605	POLYGON((-2 -2,10 -2,10 10,-2 10,-2 -2))
606	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
607	POLYGON((0 0,5 0,5 5,0 5,0 0))
608	POLYGON((0 0,10 0,10 10,0 10,0 0))
609	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
610	POLYGON((-5 -5,0 -5,0 0,-5 0,-5 -5))
611	POLYGON((10 10,20 10,20 20,10 20,10 10))
612	POLYGON((-5 -5,10 -5,10 10,-5 10,-5 -5))
613	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
614	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
615	POLYGON((0 -5,10 -5,10 5,0 5,0 -5))
701	POINT(0 0)
702	POLYGON((0 0,10 0,10 10,0 10,0 0))
703	POLYGON((0 0,10 0,10 10,0 10,0 0))
704	POLYGON((0 0,10 0,10 10,0 10,0 0))
705	POLYGON((0 0,10 0,10 10,0 10,0 0))
706	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
707	POINT(0 0)
708	POLYGON((0 0,10 0,10 10,0 10,0 0))
709	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
710	POLYGON((0 0,100 0,100 100,0 100,0 0))
711	POLYGON((0 0,18 0,18 18,0 18,0 0))
712	POLYGON((-10 -10,20 -10,20 20,-10 20,-10 -10))
713	POLYGON((0 0,10 0,10 10,0 10,0 0))
714	POLYGON((0 0,10 0,10 10,0 10,0 0))
715	POLYGON((0 0,10 0,10 10,0 10,0 0))
SELECT COUNT(ST_ENVELOPE(g)) FROM gis_geometry;
COUNT(ST_ENVELOPE(g))
105
SELECT COUNT(DISTINCT(ST_ENVELOPE(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_ENVELOPE(g)))
42
# Checking the Centroid of the spatial objects inserted into GEOMETRY class
SELECT fid, ST_ASTEXT(ST_CENTROID(g)) FROM gis_geometry;
fid	ST_ASTEXT(ST_CENTROID(g))
101	POINT(0 0)
102	POINT(1 0)
103	POINT(0 1)
104	POINT(1 1)
105	POINT(-1 1)
106	POINT(0 0)
107	POINT(10 0)
108	POINT(0 10)
109	POINT(-10 0)
110	POINT(0 -10)
111	POINT(1 1)
112	POINT(1000 1000)
113	POINT(1000 -1000)
114	POINT(-1000 1000)
115	POINT(-1000 -1000)
201	POINT(2.5 2.5)
202	POINT(2 2)
203	POINT(5 5)
204	POINT(7.5 7.5)
205	POINT(12 12)
206	POINT(50 50)
207	POINT(2.5 2.5)
208	POINT(-5.9549150281 0.9549150281)
209	POINT(4 4)
210	POINT(2.5 2.5)
211	POINT(-22.4754878398 -27.5245121602)
212	POINT(792.8932188135 207.1067811865)
213	POINT(1000 0)
214	POINT(5 5)
215	POINT(0 0)
301	POINT(1.6666666667 3.3333333333)
302	POINT(2.5 2.5)
303	POINT(5 5)
304	POINT(25 25)
305	POINT(5 5)
306	POINT(5 5)
307	POINT(1.6666666667 3.3333333333)
308	POINT(7.5 7.5)
309	POINT(5 5)
310	POINT(1.6666666667 3.3333333333)
311	POINT(12.5 12.5)
312	POINT(15 15)
313	POINT(5 6.1111111111)
314	POINT(0 3.3333333333)
315	POINT(5 5)
401	POINT(0 0)
402	POINT(2 2)
403	POINT(5 5)
404	POINT(50 50)
405	POINT(500 500)
406	POINT(0 0)
407	POINT(0 0)
408	POINT(5 5)
409	POINT(3 3)
410	POINT(0 0)
411	POINT(500 500)
412	POINT(0 0)
413	POINT(0 0)
414	POINT(0 0)
415	POINT(200 200)
501	POINT(1 1)
502	POINT(2 2)
503	POINT(5 5)
504	POINT(16.6666666667 16.6666666667)
505	POINT(0 0)
506	POINT(0 0)
507	POINT(1 1)
508	POINT(12 12)
509	POINT(5 5)
510	POINT(2 2)
511	POINT(1 1)
512	POINT(23.4210526316 23.4210526316)
513	POINT(5 5)
514	POINT(0 0)
515	POINT(0 0)
601	POINT(1.6666666667 3.3333333333)
602	POINT(4.1666666667 5.8333333333)
603	POINT(5 5)
604	POINT(1.6666666667 3.3333333333)
605	POINT(4.8843537415 4.8707482993)
606	POINT(0 0)
607	POINT(2.5 2.5)
608	POINT(5 5)
609	POINT(0 0)
610	POINT(-2.5 -2.5)
611	POINT(15 15)
612	POINT(3.4504132231 3.4504132231)
613	POINT(0 0)
614	POINT(0 0)
615	POINT(5 0)
701	POINT(0 0)
702	POINT(5 5)
703	POINT(5 5)
704	POINT(5 5)
705	POINT(5 5)
706	POINT(0 0)
707	POINT(0 0)
708	POINT(5 5)
709	POINT(1.00456621 2.1461187215)
710	POINT(50 50)
711	POINT(5 5)
712	POINT(1.00456621 2.1461187215)
713	POINT(5 5)
714	POINT(5 5)
715	POINT(5 5)
SELECT COUNT(ST_CENTROID(g)) FROM gis_geometry;
COUNT(ST_CENTROID(g))
105
SELECT COUNT(DISTINCT(ST_CENTROID(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_CENTROID(g)))
42
# Checking the ConvexHull of the spatial objects inserted into GEOMETRY class
SELECT fid, ST_ASTEXT(ST_CONVEXHULL(g)) FROM gis_geometry;
fid	ST_ASTEXT(ST_CONVEXHULL(g))
101	POINT(0 0)
102	POINT(1 0)
103	POINT(0 1)
104	POINT(1 1)
105	POINT(-1 1)
106	POINT(0 0)
107	POINT(10 0)
108	POINT(0 10)
109	POINT(-10 0)
110	POINT(0 -10)
111	POINT(1 1)
112	POINT(1000 1000)
113	POINT(1000 -1000)
114	POINT(-1000 1000)
115	POINT(-1000 -1000)
201	LINESTRING(0 0,5 5)
202	LINESTRING(0 0,4 4)
203	LINESTRING(0 0,10 10)
204	LINESTRING(5 5,10 10)
205	LINESTRING(0 0,24 24)
206	LINESTRING(0 0,100 100)
207	LINESTRING(0 0,5 5)
208	POLYGON((-10 10,-5 -5,0 0,-10 10))
209	LINESTRING(0 0,8 8)
210	LINESTRING(0 0,5 5)
211	POLYGON((-50 -50,10 -10,0 0,-50 -50))
212	POLYGON((0 0,1000 -1000,1000 1000,0 0))
213	LINESTRING(1000 -1000,1000 1000)
214	LINESTRING(1 1,9 9)
215	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
301	POLYGON((0 0,5 5,0 5,0 0))
302	POLYGON((0 0,5 0,5 5,0 5,0 0))
303	POLYGON((0 0,10 0,10 10,0 10,0 0))
304	POLYGON((0 0,50 0,50 50,0 50,0 0))
305	POLYGON((0 0,10 0,10 10,0 10,0 0))
306	POLYGON((0 0,10 0,10 10,0 10,0 0))
307	POLYGON((0 0,5 5,0 5,0 0))
308	POLYGON((0 0,15 0,15 15,0 15,0 0))
309	POLYGON((0 0,10 0,10 10,0 10,0 0))
310	POLYGON((0 0,5 5,0 5,0 0))
311	POLYGON((10 10,15 10,15 15,10 15,10 10))
312	POLYGON((10 10,20 10,20 20,10 20,10 10))
313	POLYGON((0 0,10 0,10 10,0 10,0 0))
314	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
315	POLYGON((0 0,10 0,10 10,0 10,0 0))
401	POINT(0 0)
402	LINESTRING(0 0,4 4)
403	LINESTRING(0 0,10 10)
404	LINESTRING(0 0,100 100)
405	LINESTRING(0 0,1000 1000)
406	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
407	POINT(0 0)
408	LINESTRING(0 0,10 10)
409	LINESTRING(0 0,6 6)
410	POINT(0 0)
411	LINESTRING(0 0,1000 1000)
412	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
413	POINT(0 0)
414	LINESTRING(-1000 -1000,1000 1000)
415	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
501	LINESTRING(0 0,2 2)
502	LINESTRING(0 0,4 4)
503	LINESTRING(0 0,10 10)
504	LINESTRING(-100 -100,100 100)
505	LINESTRING(-1000 -1000,1000 1000)
506	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
507	LINESTRING(0 0,2 2)
508	LINESTRING(0 0,24 24)
509	LINESTRING(0 0,10 10)
510	LINESTRING(0 0,4 4)
511	LINESTRING(0 0,2 2)
512	LINESTRING(0 0,50 50)
513	POLYGON((0 0,10 0,10 10,0 10,0 0))
514	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
515	POLYGON((-100 0,0 -100,100 0,0 100,-100 0))
601	POLYGON((0 0,5 5,0 5,0 0))
602	POLYGON((0 0,10 10,5 10,0 5,0 0))
603	POLYGON((0 0,10 0,10 10,0 10,0 0))
604	POLYGON((0 0,5 5,0 5,0 0))
605	POLYGON((-2 -2,0 -2,10 0,10 10,0 10,-2 -2))
606	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
607	POLYGON((0 0,5 0,5 5,0 5,0 0))
608	POLYGON((0 0,10 0,10 10,0 10,0 0))
609	POLYGON((-5 -5,0 -5,5 0,5 5,0 5,-5 0,-5 -5))
610	POLYGON((-5 -5,0 -5,0 0,-5 0,-5 -5))
611	POLYGON((10 10,20 10,20 20,10 20,10 10))
612	POLYGON((-5 -5,0 -5,10 0,10 10,0 10,-5 0,-5 -5))
613	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
614	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
615	POLYGON((0 0,5 -5,10 0,5 5,0 0))
701	POINT(0 0)
702	LINESTRING(0 0,10 10)
703	POLYGON((0 0,10 0,10 10,0 10,0 0))
704	LINESTRING(0 0,10 10)
705	POLYGON((0 0,10 0,10 10,0 10,0 0))
706	POLYGON((-10 -10,0 -10,10 0,10 10,0 10,-10 0,-10 -10))
707	POINT(0 0)
708	POLYGON((0 0,10 0,10 10,0 10,0 0))
709	POLYGON((-10 -10,10 0,10 10,0 10,-10 0,-10 -10))
710	LINESTRING(0 0,100 100)
711	POLYGON((0 0,10 0,18 18,0 10,0 0))
712	POLYGON((-10 -10,10 0,20 20,0 10,-10 0,-10 -10))
713	POLYGON((0 0,10 0,10 10,0 10,0 0))
714	POLYGON((0 0,10 0,10 10,0 10,0 0))
715	POLYGON((0 0,10 0,10 10,0 10,0 0))
SELECT COUNT(ST_CONVEXHULL(g)) FROM gis_geometry;
COUNT(ST_CONVEXHULL(g))
105
SELECT COUNT(DISTINCT(ST_CONVEXHULL(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_CONVEXHULL(g)))
52
# Checking if the geometries are empty or not
SELECT fid, ST_ISEMPTY(g) FROM gis_geometry;
fid	ST_ISEMPTY(g)
101	0
102	0
103	0
104	0
105	0
106	0
107	0
108	0
109	0
110	0
111	0
112	0
113	0
114	0
115	0
201	0
202	0
203	0
204	0
205	0
206	0
207	0
208	0
209	0
210	0
211	0
212	0
213	0
214	0
215	0
301	0
302	0
303	0
304	0
305	0
306	0
307	0
308	0
309	0
310	0
311	0
312	0
313	0
314	0
315	0
401	0
402	0
403	0
404	0
405	0
406	0
407	0
408	0
409	0
410	0
411	0
412	0
413	0
414	0
415	0
501	0
502	0
503	0
504	0
505	0
506	0
507	0
508	0
509	0
510	0
511	0
512	0
513	0
514	0
515	0
601	0
602	0
603	0
604	0
605	0
606	0
607	0
608	0
609	0
610	0
611	0
612	0
613	0
614	0
615	0
701	0
702	0
703	0
704	0
705	0
706	0
707	0
708	0
709	0
710	0
711	0
712	0
713	0
714	0
715	0
SELECT COUNT(ST_ISEMPTY(g)) FROM gis_geometry;
COUNT(ST_ISEMPTY(g))
105
SELECT COUNT(DISTINCT(ST_ISEMPTY(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_ISEMPTY(g)))
1
# Check the procedure to work with the Geometry type properties
CREATE PROCEDURE geom_attri()
BEGIN
SELECT fid, ST_GEOMETRYTYPE(g) FROM gis_geometry; 
SELECT fid, ST_SRID(g) FROM gis_geometry;
SELECT fid, ST_DIMENSION(g) FROM gis_geometry;
SELECT fid, ST_ISSIMPLE(g) FROM gis_geometry;
SELECT fid, ST_ASTEXT(ST_ENVELOPE(g)) FROM gis_geometry;
SELECT fid, ST_ASTEXT(ST_CENTROID(g)) FROM gis_geometry;
SELECT fid, ST_ASTEXT(ST_CONVEXHULL(g)) FROM gis_geometry;
SELECT fid, ST_ISEMPTY(g) FROM gis_geometry;
END |
# Call the proc
CALL geom_attri;
fid	ST_GEOMETRYTYPE(g)
101	POINT
102	POINT
103	POINT
104	POINT
105	POINT
106	POINT
107	POINT
108	POINT
109	POINT
110	POINT
111	POINT
112	POINT
113	POINT
114	POINT
115	POINT
201	LINESTRING
202	LINESTRING
203	LINESTRING
204	LINESTRING
205	LINESTRING
206	LINESTRING
207	LINESTRING
208	LINESTRING
209	LINESTRING
210	LINESTRING
211	LINESTRING
212	LINESTRING
213	LINESTRING
214	LINESTRING
215	LINESTRING
301	POLYGON
302	POLYGON
303	POLYGON
304	POLYGON
305	POLYGON
306	POLYGON
307	POLYGON
308	POLYGON
309	POLYGON
310	POLYGON
311	POLYGON
312	POLYGON
313	POLYGON
314	POLYGON
315	POLYGON
401	MULTIPOINT
402	MULTIPOINT
403	MULTIPOINT
404	MULTIPOINT
405	MULTIPOINT
406	MULTIPOINT
407	MULTIPOINT
408	MULTIPOINT
409	MULTIPOINT
410	MULTIPOINT
411	MULTIPOINT
412	MULTIPOINT
413	MULTIPOINT
414	MULTIPOINT
415	MULTIPOINT
501	MULTILINESTRING
502	MULTILINESTRING
503	MULTILINESTRING
504	MULTILINESTRING
505	MULTILINESTRING
506	MULTILINESTRING
507	MULTILINESTRING
508	MULTILINESTRING
509	MULTILINESTRING
510	MULTILINESTRING
511	MULTILINESTRING
512	MULTILINESTRING
513	MULTILINESTRING
514	MULTILINESTRING
515	MULTILINESTRING
601	MULTIPOLYGON
602	MULTIPOLYGON
603	MULTIPOLYGON
604	MULTIPOLYGON
605	MULTIPOLYGON
606	MULTIPOLYGON
607	MULTIPOLYGON
608	MULTIPOLYGON
609	MULTIPOLYGON
610	MULTIPOLYGON
611	MULTIPOLYGON
612	MULTIPOLYGON
613	MULTIPOLYGON
614	MULTIPOLYGON
615	MULTIPOLYGON
701	GEOMCOLLECTION
702	GEOMCOLLECTION
703	GEOMCOLLECTION
704	GEOMCOLLECTION
705	GEOMCOLLECTION
706	GEOMCOLLECTION
707	GEOMCOLLECTION
708	GEOMCOLLECTION
709	GEOMCOLLECTION
710	GEOMCOLLECTION
711	GEOMCOLLECTION
712	GEOMCOLLECTION
713	GEOMCOLLECTION
714	GEOMCOLLECTION
715	GEOMCOLLECTION
fid	ST_SRID(g)
101	0
102	0
103	0
104	0
105	0
106	0
107	0
108	0
109	0
110	0
111	0
112	0
113	0
114	0
115	0
201	0
202	0
203	0
204	0
205	0
206	0
207	0
208	0
209	0
210	0
211	0
212	0
213	0
214	0
215	0
301	0
302	0
303	0
304	0
305	0
306	0
307	0
308	0
309	0
310	0
311	0
312	0
313	0
314	0
315	0
401	0
402	0
403	0
404	0
405	0
406	0
407	0
408	0
409	0
410	0
411	0
412	0
413	0
414	0
415	0
501	0
502	0
503	0
504	0
505	0
506	0
507	0
508	0
509	0
510	0
511	0
512	0
513	0
514	0
515	0
601	0
602	0
603	0
604	0
605	0
606	0
607	0
608	0
609	0
610	0
611	0
612	0
613	0
614	0
615	0
701	0
702	0
703	0
704	0
705	0
706	0
707	0
708	0
709	0
710	0
711	0
712	0
713	0
714	0
715	0
fid	ST_DIMENSION(g)
101	0
102	0
103	0
104	0
105	0
106	0
107	0
108	0
109	0
110	0
111	0
112	0
113	0
114	0
115	0
201	1
202	1
203	1
204	1
205	1
206	1
207	1
208	1
209	1
210	1
211	1
212	1
213	1
214	1
215	1
301	2
302	2
303	2
304	2
305	2
306	2
307	2
308	2
309	2
310	2
311	2
312	2
313	2
314	2
315	2
401	0
402	0
403	0
404	0
405	0
406	0
407	0
408	0
409	0
410	0
411	0
412	0
413	0
414	0
415	0
501	1
502	1
503	1
504	1
505	1
506	1
507	1
508	1
509	1
510	1
511	1
512	1
513	1
514	1
515	1
601	2
602	2
603	2
604	2
605	2
606	2
607	2
608	2
609	2
610	2
611	2
612	2
613	2
614	2
615	2
701	0
702	1
703	2
704	1
705	2
706	2
707	0
708	2
709	2
710	1
711	2
712	2
713	2
714	2
715	NULL
fid	ST_ISSIMPLE(g)
101	1
102	1
103	1
104	1
105	1
106	1
107	1
108	1
109	1
110	1
111	1
112	1
113	1
114	1
115	1
201	1
202	1
203	1
204	1
205	1
206	1
207	1
208	1
209	1
210	1
211	1
212	1
213	1
214	1
215	1
301	1
302	1
303	1
304	1
305	1
306	1
307	1
308	1
309	1
310	1
311	1
312	1
313	1
314	1
315	1
401	1
402	1
403	1
404	1
405	1
406	1
407	1
408	1
409	1
410	1
411	1
412	1
413	1
414	1
415	0
501	1
502	1
503	1
504	0
505	1
506	0
507	1
508	1
509	1
510	1
511	1
512	1
513	0
514	0
515	1
601	1
602	1
603	1
604	1
605	1
606	1
607	1
608	1
609	1
610	1
611	1
612	1
613	1
614	1
615	1
701	1
702	1
703	0
704	1
705	0
706	0
707	1
708	0
709	0
710	1
711	1
712	0
713	0
714	0
715	0
fid	ST_ASTEXT(ST_ENVELOPE(g))
101	POINT(0 0)
102	POINT(1 0)
103	POINT(0 1)
104	POINT(1 1)
105	POINT(-1 1)
106	POINT(0 0)
107	POINT(10 0)
108	POINT(0 10)
109	POINT(-10 0)
110	POINT(0 -10)
111	POINT(1 1)
112	POINT(1000 1000)
113	POINT(1000 -1000)
114	POINT(-1000 1000)
115	POINT(-1000 -1000)
201	POLYGON((0 0,5 0,5 5,0 5,0 0))
202	POLYGON((0 0,4 0,4 4,0 4,0 0))
203	POLYGON((0 0,10 0,10 10,0 10,0 0))
204	POLYGON((5 5,10 5,10 10,5 10,5 5))
205	POLYGON((0 0,24 0,24 24,0 24,0 0))
206	POLYGON((0 0,100 0,100 100,0 100,0 0))
207	POLYGON((0 0,5 0,5 5,0 5,0 0))
208	POLYGON((-10 -5,0 -5,0 10,-10 10,-10 -5))
209	POLYGON((0 0,8 0,8 8,0 8,0 0))
210	POLYGON((0 0,5 0,5 5,0 5,0 0))
211	POLYGON((-50 -50,10 -50,10 0,-50 0,-50 -50))
212	POLYGON((0 -1000,1000 -1000,1000 1000,0 1000,0 -1000))
213	LINESTRING(1000 -1000,1000 1000)
214	POLYGON((1 1,9 1,9 9,1 9,1 1))
215	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
301	POLYGON((0 0,5 0,5 5,0 5,0 0))
302	POLYGON((0 0,5 0,5 5,0 5,0 0))
303	POLYGON((0 0,10 0,10 10,0 10,0 0))
304	POLYGON((0 0,50 0,50 50,0 50,0 0))
305	POLYGON((0 0,10 0,10 10,0 10,0 0))
306	POLYGON((0 0,10 0,10 10,0 10,0 0))
307	POLYGON((0 0,5 0,5 5,0 5,0 0))
308	POLYGON((0 0,15 0,15 15,0 15,0 0))
309	POLYGON((0 0,10 0,10 10,0 10,0 0))
310	POLYGON((0 0,5 0,5 5,0 5,0 0))
311	POLYGON((10 10,15 10,15 15,10 15,10 10))
312	POLYGON((10 10,20 10,20 20,10 20,10 10))
313	POLYGON((0 0,10 0,10 10,0 10,0 0))
314	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
315	POLYGON((0 0,10 0,10 10,0 10,0 0))
401	POINT(0 0)
402	POLYGON((0 0,4 0,4 4,0 4,0 0))
403	POLYGON((0 0,10 0,10 10,0 10,0 0))
404	POLYGON((0 0,100 0,100 100,0 100,0 0))
405	POLYGON((0 0,1000 0,1000 1000,0 1000,0 0))
406	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
407	POINT(0 0)
408	POLYGON((0 0,10 0,10 10,0 10,0 0))
409	POLYGON((0 0,6 0,6 6,0 6,0 0))
410	POINT(0 0)
411	POLYGON((0 0,1000 0,1000 1000,0 1000,0 0))
412	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
413	POINT(0 0)
414	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
415	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
501	POLYGON((0 0,2 0,2 2,0 2,0 0))
502	POLYGON((0 0,4 0,4 4,0 4,0 0))
503	POLYGON((0 0,10 0,10 10,0 10,0 0))
504	POLYGON((-100 -100,100 -100,100 100,-100 100,-100 -100))
505	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
506	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
507	POLYGON((0 0,2 0,2 2,0 2,0 0))
508	POLYGON((0 0,24 0,24 24,0 24,0 0))
509	POLYGON((0 0,10 0,10 10,0 10,0 0))
510	POLYGON((0 0,4 0,4 4,0 4,0 0))
511	POLYGON((0 0,2 0,2 2,0 2,0 0))
512	POLYGON((0 0,50 0,50 50,0 50,0 0))
513	POLYGON((0 0,10 0,10 10,0 10,0 0))
514	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
515	POLYGON((-100 -100,100 -100,100 100,-100 100,-100 -100))
601	POLYGON((0 0,5 0,5 5,0 5,0 0))
602	POLYGON((0 0,10 0,10 10,0 10,0 0))
603	POLYGON((0 0,10 0,10 10,0 10,0 0))
604	POLYGON((0 0,5 0,5 5,0 5,0 0))
605	POLYGON((-2 -2,10 -2,10 10,-2 10,-2 -2))
606	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
607	POLYGON((0 0,5 0,5 5,0 5,0 0))
608	POLYGON((0 0,10 0,10 10,0 10,0 0))
609	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
610	POLYGON((-5 -5,0 -5,0 0,-5 0,-5 -5))
611	POLYGON((10 10,20 10,20 20,10 20,10 10))
612	POLYGON((-5 -5,10 -5,10 10,-5 10,-5 -5))
613	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
614	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
615	POLYGON((0 -5,10 -5,10 5,0 5,0 -5))
701	POINT(0 0)
702	POLYGON((0 0,10 0,10 10,0 10,0 0))
703	POLYGON((0 0,10 0,10 10,0 10,0 0))
704	POLYGON((0 0,10 0,10 10,0 10,0 0))
705	POLYGON((0 0,10 0,10 10,0 10,0 0))
706	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
707	POINT(0 0)
708	POLYGON((0 0,10 0,10 10,0 10,0 0))
709	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
710	POLYGON((0 0,100 0,100 100,0 100,0 0))
711	POLYGON((0 0,18 0,18 18,0 18,0 0))
712	POLYGON((-10 -10,20 -10,20 20,-10 20,-10 -10))
713	POLYGON((0 0,10 0,10 10,0 10,0 0))
714	POLYGON((0 0,10 0,10 10,0 10,0 0))
715	POLYGON((0 0,10 0,10 10,0 10,0 0))
fid	ST_ASTEXT(ST_CENTROID(g))
101	POINT(0 0)
102	POINT(1 0)
103	POINT(0 1)
104	POINT(1 1)
105	POINT(-1 1)
106	POINT(0 0)
107	POINT(10 0)
108	POINT(0 10)
109	POINT(-10 0)
110	POINT(0 -10)
111	POINT(1 1)
112	POINT(1000 1000)
113	POINT(1000 -1000)
114	POINT(-1000 1000)
115	POINT(-1000 -1000)
201	POINT(2.5 2.5)
202	POINT(2 2)
203	POINT(5 5)
204	POINT(7.5 7.5)
205	POINT(12 12)
206	POINT(50 50)
207	POINT(2.5 2.5)
208	POINT(-5.9549150281 0.9549150281)
209	POINT(4 4)
210	POINT(2.5 2.5)
211	POINT(-22.4754878398 -27.5245121602)
212	POINT(792.8932188135 207.1067811865)
213	POINT(1000 0)
214	POINT(5 5)
215	POINT(0 0)
301	POINT(1.6666666667 3.3333333333)
302	POINT(2.5 2.5)
303	POINT(5 5)
304	POINT(25 25)
305	POINT(5 5)
306	POINT(5 5)
307	POINT(1.6666666667 3.3333333333)
308	POINT(7.5 7.5)
309	POINT(5 5)
310	POINT(1.6666666667 3.3333333333)
311	POINT(12.5 12.5)
312	POINT(15 15)
313	POINT(5 6.1111111111)
314	POINT(0 3.3333333333)
315	POINT(5 5)
401	POINT(0 0)
402	POINT(2 2)
403	POINT(5 5)
404	POINT(50 50)
405	POINT(500 500)
406	POINT(0 0)
407	POINT(0 0)
408	POINT(5 5)
409	POINT(3 3)
410	POINT(0 0)
411	POINT(500 500)
412	POINT(0 0)
413	POINT(0 0)
414	POINT(0 0)
415	POINT(200 200)
501	POINT(1 1)
502	POINT(2 2)
503	POINT(5 5)
504	POINT(16.6666666667 16.6666666667)
505	POINT(0 0)
506	POINT(0 0)
507	POINT(1 1)
508	POINT(12 12)
509	POINT(5 5)
510	POINT(2 2)
511	POINT(1 1)
512	POINT(23.4210526316 23.4210526316)
513	POINT(5 5)
514	POINT(0 0)
515	POINT(0 0)
601	POINT(1.6666666667 3.3333333333)
602	POINT(4.1666666667 5.8333333333)
603	POINT(5 5)
604	POINT(1.6666666667 3.3333333333)
605	POINT(4.8843537415 4.8707482993)
606	POINT(0 0)
607	POINT(2.5 2.5)
608	POINT(5 5)
609	POINT(0 0)
610	POINT(-2.5 -2.5)
611	POINT(15 15)
612	POINT(3.4504132231 3.4504132231)
613	POINT(0 0)
614	POINT(0 0)
615	POINT(5 0)
701	POINT(0 0)
702	POINT(5 5)
703	POINT(5 5)
704	POINT(5 5)
705	POINT(5 5)
706	POINT(0 0)
707	POINT(0 0)
708	POINT(5 5)
709	POINT(1.00456621 2.1461187215)
710	POINT(50 50)
711	POINT(5 5)
712	POINT(1.00456621 2.1461187215)
713	POINT(5 5)
714	POINT(5 5)
715	POINT(5 5)
fid	ST_ASTEXT(ST_CONVEXHULL(g))
101	POINT(0 0)
102	POINT(1 0)
103	POINT(0 1)
104	POINT(1 1)
105	POINT(-1 1)
106	POINT(0 0)
107	POINT(10 0)
108	POINT(0 10)
109	POINT(-10 0)
110	POINT(0 -10)
111	POINT(1 1)
112	POINT(1000 1000)
113	POINT(1000 -1000)
114	POINT(-1000 1000)
115	POINT(-1000 -1000)
201	LINESTRING(0 0,5 5)
202	LINESTRING(0 0,4 4)
203	LINESTRING(0 0,10 10)
204	LINESTRING(5 5,10 10)
205	LINESTRING(0 0,24 24)
206	LINESTRING(0 0,100 100)
207	LINESTRING(0 0,5 5)
208	POLYGON((-10 10,-5 -5,0 0,-10 10))
209	LINESTRING(0 0,8 8)
210	LINESTRING(0 0,5 5)
211	POLYGON((-50 -50,10 -10,0 0,-50 -50))
212	POLYGON((0 0,1000 -1000,1000 1000,0 0))
213	LINESTRING(1000 -1000,1000 1000)
214	LINESTRING(1 1,9 9)
215	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
301	POLYGON((0 0,5 5,0 5,0 0))
302	POLYGON((0 0,5 0,5 5,0 5,0 0))
303	POLYGON((0 0,10 0,10 10,0 10,0 0))
304	POLYGON((0 0,50 0,50 50,0 50,0 0))
305	POLYGON((0 0,10 0,10 10,0 10,0 0))
306	POLYGON((0 0,10 0,10 10,0 10,0 0))
307	POLYGON((0 0,5 5,0 5,0 0))
308	POLYGON((0 0,15 0,15 15,0 15,0 0))
309	POLYGON((0 0,10 0,10 10,0 10,0 0))
310	POLYGON((0 0,5 5,0 5,0 0))
311	POLYGON((10 10,15 10,15 15,10 15,10 10))
312	POLYGON((10 10,20 10,20 20,10 20,10 10))
313	POLYGON((0 0,10 0,10 10,0 10,0 0))
314	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
315	POLYGON((0 0,10 0,10 10,0 10,0 0))
401	POINT(0 0)
402	LINESTRING(0 0,4 4)
403	LINESTRING(0 0,10 10)
404	LINESTRING(0 0,100 100)
405	LINESTRING(0 0,1000 1000)
406	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
407	POINT(0 0)
408	LINESTRING(0 0,10 10)
409	LINESTRING(0 0,6 6)
410	POINT(0 0)
411	LINESTRING(0 0,1000 1000)
412	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
413	POINT(0 0)
414	LINESTRING(-1000 -1000,1000 1000)
415	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
501	LINESTRING(0 0,2 2)
502	LINESTRING(0 0,4 4)
503	LINESTRING(0 0,10 10)
504	LINESTRING(-100 -100,100 100)
505	LINESTRING(-1000 -1000,1000 1000)
506	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
507	LINESTRING(0 0,2 2)
508	LINESTRING(0 0,24 24)
509	LINESTRING(0 0,10 10)
510	LINESTRING(0 0,4 4)
511	LINESTRING(0 0,2 2)
512	LINESTRING(0 0,50 50)
513	POLYGON((0 0,10 0,10 10,0 10,0 0))
514	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
515	POLYGON((-100 0,0 -100,100 0,0 100,-100 0))
601	POLYGON((0 0,5 5,0 5,0 0))
602	POLYGON((0 0,10 10,5 10,0 5,0 0))
603	POLYGON((0 0,10 0,10 10,0 10,0 0))
604	POLYGON((0 0,5 5,0 5,0 0))
605	POLYGON((-2 -2,0 -2,10 0,10 10,0 10,-2 -2))
606	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
607	POLYGON((0 0,5 0,5 5,0 5,0 0))
608	POLYGON((0 0,10 0,10 10,0 10,0 0))
609	POLYGON((-5 -5,0 -5,5 0,5 5,0 5,-5 0,-5 -5))
610	POLYGON((-5 -5,0 -5,0 0,-5 0,-5 -5))
611	POLYGON((10 10,20 10,20 20,10 20,10 10))
612	POLYGON((-5 -5,0 -5,10 0,10 10,0 10,-5 0,-5 -5))
613	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
614	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
615	POLYGON((0 0,5 -5,10 0,5 5,0 0))
701	POINT(0 0)
702	LINESTRING(0 0,10 10)
703	POLYGON((0 0,10 0,10 10,0 10,0 0))
704	LINESTRING(0 0,10 10)
705	POLYGON((0 0,10 0,10 10,0 10,0 0))
706	POLYGON((-10 -10,0 -10,10 0,10 10,0 10,-10 0,-10 -10))
707	POINT(0 0)
708	POLYGON((0 0,10 0,10 10,0 10,0 0))
709	POLYGON((-10 -10,10 0,10 10,0 10,-10 0,-10 -10))
710	LINESTRING(0 0,100 100)
711	POLYGON((0 0,10 0,18 18,0 10,0 0))
712	POLYGON((-10 -10,10 0,20 20,0 10,-10 0,-10 -10))
713	POLYGON((0 0,10 0,10 10,0 10,0 0))
714	POLYGON((0 0,10 0,10 10,0 10,0 0))
715	POLYGON((0 0,10 0,10 10,0 10,0 0))
fid	ST_ISEMPTY(g)
101	0
102	0
103	0
104	0
105	0
106	0
107	0
108	0
109	0
110	0
111	0
112	0
113	0
114	0
115	0
201	0
202	0
203	0
204	0
205	0
206	0
207	0
208	0
209	0
210	0
211	0
212	0
213	0
214	0
215	0
301	0
302	0
303	0
304	0
305	0
306	0
307	0
308	0
309	0
310	0
311	0
312	0
313	0
314	0
315	0
401	0
402	0
403	0
404	0
405	0
406	0
407	0
408	0
409	0
410	0
411	0
412	0
413	0
414	0
415	0
501	0
502	0
503	0
504	0
505	0
506	0
507	0
508	0
509	0
510	0
511	0
512	0
513	0
514	0
515	0
601	0
602	0
603	0
604	0
605	0
606	0
607	0
608	0
609	0
610	0
611	0
612	0
613	0
614	0
615	0
701	0
702	0
703	0
704	0
705	0
706	0
707	0
708	0
709	0
710	0
711	0
712	0
713	0
714	0
715	0
# Dropping the created procedure
DROP PROCEDURE geom_attri;
# Creating an empty table as gis_geometry_1
CREATE TABLE gis_geometry_1 AS SELECT * FROM gis_geometry;
TRUNCATE TABLE gis_geometry_1;
# Create a tigger to populate the data FROM gis_geometry to gis_geometry_1
CREATE TRIGGER geom_trigger AFTER UPDATE ON gis_geometry
FOR EACH ROW
BEGIN
INSERT INTO gis_geometry_1 SELECT * FROM gis_geometry;
END|
# Calling the trigger
UPDATE gis_geometry SET fid = 999 where fid = 111;
# Checking the GEOMETRY class properties on the spatial data in the gis_geometry_1 table
SELECT fid, ST_GEOMETRYTYPE(g) FROM gis_geometry_1;
fid	ST_GEOMETRYTYPE(g)
101	POINT
102	POINT
103	POINT
104	POINT
105	POINT
106	POINT
107	POINT
108	POINT
109	POINT
110	POINT
112	POINT
113	POINT
114	POINT
115	POINT
201	LINESTRING
202	LINESTRING
203	LINESTRING
204	LINESTRING
205	LINESTRING
206	LINESTRING
207	LINESTRING
208	LINESTRING
209	LINESTRING
210	LINESTRING
211	LINESTRING
212	LINESTRING
213	LINESTRING
214	LINESTRING
215	LINESTRING
301	POLYGON
302	POLYGON
303	POLYGON
304	POLYGON
305	POLYGON
306	POLYGON
307	POLYGON
308	POLYGON
309	POLYGON
310	POLYGON
311	POLYGON
312	POLYGON
313	POLYGON
314	POLYGON
315	POLYGON
401	MULTIPOINT
402	MULTIPOINT
403	MULTIPOINT
404	MULTIPOINT
405	MULTIPOINT
406	MULTIPOINT
407	MULTIPOINT
408	MULTIPOINT
409	MULTIPOINT
410	MULTIPOINT
411	MULTIPOINT
412	MULTIPOINT
413	MULTIPOINT
414	MULTIPOINT
415	MULTIPOINT
501	MULTILINESTRING
502	MULTILINESTRING
503	MULTILINESTRING
504	MULTILINESTRING
505	MULTILINESTRING
506	MULTILINESTRING
507	MULTILINESTRING
508	MULTILINESTRING
509	MULTILINESTRING
510	MULTILINESTRING
511	MULTILINESTRING
512	MULTILINESTRING
513	MULTILINESTRING
514	MULTILINESTRING
515	MULTILINESTRING
601	MULTIPOLYGON
602	MULTIPOLYGON
603	MULTIPOLYGON
604	MULTIPOLYGON
605	MULTIPOLYGON
606	MULTIPOLYGON
607	MULTIPOLYGON
608	MULTIPOLYGON
609	MULTIPOLYGON
610	MULTIPOLYGON
611	MULTIPOLYGON
612	MULTIPOLYGON
613	MULTIPOLYGON
614	MULTIPOLYGON
615	MULTIPOLYGON
701	GEOMCOLLECTION
702	GEOMCOLLECTION
703	GEOMCOLLECTION
704	GEOMCOLLECTION
705	GEOMCOLLECTION
706	GEOMCOLLECTION
707	GEOMCOLLECTION
708	GEOMCOLLECTION
709	GEOMCOLLECTION
710	GEOMCOLLECTION
711	GEOMCOLLECTION
712	GEOMCOLLECTION
713	GEOMCOLLECTION
714	GEOMCOLLECTION
715	GEOMCOLLECTION
999	POINT
SELECT fid, ST_SRID(g) FROM gis_geometry_1;
fid	ST_SRID(g)
101	0
102	0
103	0
104	0
105	0
106	0
107	0
108	0
109	0
110	0
112	0
113	0
114	0
115	0
201	0
202	0
203	0
204	0
205	0
206	0
207	0
208	0
209	0
210	0
211	0
212	0
213	0
214	0
215	0
301	0
302	0
303	0
304	0
305	0
306	0
307	0
308	0
309	0
310	0
311	0
312	0
313	0
314	0
315	0
401	0
402	0
403	0
404	0
405	0
406	0
407	0
408	0
409	0
410	0
411	0
412	0
413	0
414	0
415	0
501	0
502	0
503	0
504	0
505	0
506	0
507	0
508	0
509	0
510	0
511	0
512	0
513	0
514	0
515	0
601	0
602	0
603	0
604	0
605	0
606	0
607	0
608	0
609	0
610	0
611	0
612	0
613	0
614	0
615	0
701	0
702	0
703	0
704	0
705	0
706	0
707	0
708	0
709	0
710	0
711	0
712	0
713	0
714	0
715	0
999	0
SELECT fid, ST_DIMENSION(g) FROM gis_geometry_1;
fid	ST_DIMENSION(g)
101	0
102	0
103	0
104	0
105	0
106	0
107	0
108	0
109	0
110	0
112	0
113	0
114	0
115	0
201	1
202	1
203	1
204	1
205	1
206	1
207	1
208	1
209	1
210	1
211	1
212	1
213	1
214	1
215	1
301	2
302	2
303	2
304	2
305	2
306	2
307	2
308	2
309	2
310	2
311	2
312	2
313	2
314	2
315	2
401	0
402	0
403	0
404	0
405	0
406	0
407	0
408	0
409	0
410	0
411	0
412	0
413	0
414	0
415	0
501	1
502	1
503	1
504	1
505	1
506	1
507	1
508	1
509	1
510	1
511	1
512	1
513	1
514	1
515	1
601	2
602	2
603	2
604	2
605	2
606	2
607	2
608	2
609	2
610	2
611	2
612	2
613	2
614	2
615	2
701	0
702	1
703	2
704	1
705	2
706	2
707	0
708	2
709	2
710	1
711	2
712	2
713	2
714	2
715	NULL
999	0
SELECT fid, ST_ISSIMPLE(g) FROM gis_geometry_1;
fid	ST_ISSIMPLE(g)
101	1
102	1
103	1
104	1
105	1
106	1
107	1
108	1
109	1
110	1
112	1
113	1
114	1
115	1
201	1
202	1
203	1
204	1
205	1
206	1
207	1
208	1
209	1
210	1
211	1
212	1
213	1
214	1
215	1
301	1
302	1
303	1
304	1
305	1
306	1
307	1
308	1
309	1
310	1
311	1
312	1
313	1
314	1
315	1
401	1
402	1
403	1
404	1
405	1
406	1
407	1
408	1
409	1
410	1
411	1
412	1
413	1
414	1
415	0
501	1
502	1
503	1
504	0
505	1
506	0
507	1
508	1
509	1
510	1
511	1
512	1
513	0
514	0
515	1
601	1
602	1
603	1
604	1
605	1
606	1
607	1
608	1
609	1
610	1
611	1
612	1
613	1
614	1
615	1
701	1
702	1
703	0
704	1
705	0
706	0
707	1
708	0
709	0
710	1
711	1
712	0
713	0
714	0
715	0
999	1
SELECT fid, ST_ASTEXT(ST_ENVELOPE(g)) FROM gis_geometry_1;
fid	ST_ASTEXT(ST_ENVELOPE(g))
101	POINT(0 0)
102	POINT(1 0)
103	POINT(0 1)
104	POINT(1 1)
105	POINT(-1 1)
106	POINT(0 0)
107	POINT(10 0)
108	POINT(0 10)
109	POINT(-10 0)
110	POINT(0 -10)
112	POINT(1000 1000)
113	POINT(1000 -1000)
114	POINT(-1000 1000)
115	POINT(-1000 -1000)
201	POLYGON((0 0,5 0,5 5,0 5,0 0))
202	POLYGON((0 0,4 0,4 4,0 4,0 0))
203	POLYGON((0 0,10 0,10 10,0 10,0 0))
204	POLYGON((5 5,10 5,10 10,5 10,5 5))
205	POLYGON((0 0,24 0,24 24,0 24,0 0))
206	POLYGON((0 0,100 0,100 100,0 100,0 0))
207	POLYGON((0 0,5 0,5 5,0 5,0 0))
208	POLYGON((-10 -5,0 -5,0 10,-10 10,-10 -5))
209	POLYGON((0 0,8 0,8 8,0 8,0 0))
210	POLYGON((0 0,5 0,5 5,0 5,0 0))
211	POLYGON((-50 -50,10 -50,10 0,-50 0,-50 -50))
212	POLYGON((0 -1000,1000 -1000,1000 1000,0 1000,0 -1000))
213	LINESTRING(1000 -1000,1000 1000)
214	POLYGON((1 1,9 1,9 9,1 9,1 1))
215	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
301	POLYGON((0 0,5 0,5 5,0 5,0 0))
302	POLYGON((0 0,5 0,5 5,0 5,0 0))
303	POLYGON((0 0,10 0,10 10,0 10,0 0))
304	POLYGON((0 0,50 0,50 50,0 50,0 0))
305	POLYGON((0 0,10 0,10 10,0 10,0 0))
306	POLYGON((0 0,10 0,10 10,0 10,0 0))
307	POLYGON((0 0,5 0,5 5,0 5,0 0))
308	POLYGON((0 0,15 0,15 15,0 15,0 0))
309	POLYGON((0 0,10 0,10 10,0 10,0 0))
310	POLYGON((0 0,5 0,5 5,0 5,0 0))
311	POLYGON((10 10,15 10,15 15,10 15,10 10))
312	POLYGON((10 10,20 10,20 20,10 20,10 10))
313	POLYGON((0 0,10 0,10 10,0 10,0 0))
314	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
315	POLYGON((0 0,10 0,10 10,0 10,0 0))
401	POINT(0 0)
402	POLYGON((0 0,4 0,4 4,0 4,0 0))
403	POLYGON((0 0,10 0,10 10,0 10,0 0))
404	POLYGON((0 0,100 0,100 100,0 100,0 0))
405	POLYGON((0 0,1000 0,1000 1000,0 1000,0 0))
406	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
407	POINT(0 0)
408	POLYGON((0 0,10 0,10 10,0 10,0 0))
409	POLYGON((0 0,6 0,6 6,0 6,0 0))
410	POINT(0 0)
411	POLYGON((0 0,1000 0,1000 1000,0 1000,0 0))
412	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
413	POINT(0 0)
414	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
415	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
501	POLYGON((0 0,2 0,2 2,0 2,0 0))
502	POLYGON((0 0,4 0,4 4,0 4,0 0))
503	POLYGON((0 0,10 0,10 10,0 10,0 0))
504	POLYGON((-100 -100,100 -100,100 100,-100 100,-100 -100))
505	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
506	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
507	POLYGON((0 0,2 0,2 2,0 2,0 0))
508	POLYGON((0 0,24 0,24 24,0 24,0 0))
509	POLYGON((0 0,10 0,10 10,0 10,0 0))
510	POLYGON((0 0,4 0,4 4,0 4,0 0))
511	POLYGON((0 0,2 0,2 2,0 2,0 0))
512	POLYGON((0 0,50 0,50 50,0 50,0 0))
513	POLYGON((0 0,10 0,10 10,0 10,0 0))
514	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
515	POLYGON((-100 -100,100 -100,100 100,-100 100,-100 -100))
601	POLYGON((0 0,5 0,5 5,0 5,0 0))
602	POLYGON((0 0,10 0,10 10,0 10,0 0))
603	POLYGON((0 0,10 0,10 10,0 10,0 0))
604	POLYGON((0 0,5 0,5 5,0 5,0 0))
605	POLYGON((-2 -2,10 -2,10 10,-2 10,-2 -2))
606	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
607	POLYGON((0 0,5 0,5 5,0 5,0 0))
608	POLYGON((0 0,10 0,10 10,0 10,0 0))
609	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
610	POLYGON((-5 -5,0 -5,0 0,-5 0,-5 -5))
611	POLYGON((10 10,20 10,20 20,10 20,10 10))
612	POLYGON((-5 -5,10 -5,10 10,-5 10,-5 -5))
613	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
614	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
615	POLYGON((0 -5,10 -5,10 5,0 5,0 -5))
701	POINT(0 0)
702	POLYGON((0 0,10 0,10 10,0 10,0 0))
703	POLYGON((0 0,10 0,10 10,0 10,0 0))
704	POLYGON((0 0,10 0,10 10,0 10,0 0))
705	POLYGON((0 0,10 0,10 10,0 10,0 0))
706	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
707	POINT(0 0)
708	POLYGON((0 0,10 0,10 10,0 10,0 0))
709	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
710	POLYGON((0 0,100 0,100 100,0 100,0 0))
711	POLYGON((0 0,18 0,18 18,0 18,0 0))
712	POLYGON((-10 -10,20 -10,20 20,-10 20,-10 -10))
713	POLYGON((0 0,10 0,10 10,0 10,0 0))
714	POLYGON((0 0,10 0,10 10,0 10,0 0))
715	POLYGON((0 0,10 0,10 10,0 10,0 0))
999	POINT(1 1)
SELECT fid, ST_ASTEXT(ST_CENTROID(g)) FROM gis_geometry_1;
fid	ST_ASTEXT(ST_CENTROID(g))
101	POINT(0 0)
102	POINT(1 0)
103	POINT(0 1)
104	POINT(1 1)
105	POINT(-1 1)
106	POINT(0 0)
107	POINT(10 0)
108	POINT(0 10)
109	POINT(-10 0)
110	POINT(0 -10)
112	POINT(1000 1000)
113	POINT(1000 -1000)
114	POINT(-1000 1000)
115	POINT(-1000 -1000)
201	POINT(2.5 2.5)
202	POINT(2 2)
203	POINT(5 5)
204	POINT(7.5 7.5)
205	POINT(12 12)
206	POINT(50 50)
207	POINT(2.5 2.5)
208	POINT(-5.9549150281 0.9549150281)
209	POINT(4 4)
210	POINT(2.5 2.5)
211	POINT(-22.4754878398 -27.5245121602)
212	POINT(792.8932188135 207.1067811865)
213	POINT(1000 0)
214	POINT(5 5)
215	POINT(0 0)
301	POINT(1.6666666667 3.3333333333)
302	POINT(2.5 2.5)
303	POINT(5 5)
304	POINT(25 25)
305	POINT(5 5)
306	POINT(5 5)
307	POINT(1.6666666667 3.3333333333)
308	POINT(7.5 7.5)
309	POINT(5 5)
310	POINT(1.6666666667 3.3333333333)
311	POINT(12.5 12.5)
312	POINT(15 15)
313	POINT(5 6.1111111111)
314	POINT(0 3.3333333333)
315	POINT(5 5)
401	POINT(0 0)
402	POINT(2 2)
403	POINT(5 5)
404	POINT(50 50)
405	POINT(500 500)
406	POINT(0 0)
407	POINT(0 0)
408	POINT(5 5)
409	POINT(3 3)
410	POINT(0 0)
411	POINT(500 500)
412	POINT(0 0)
413	POINT(0 0)
414	POINT(0 0)
415	POINT(200 200)
501	POINT(1 1)
502	POINT(2 2)
503	POINT(5 5)
504	POINT(16.6666666667 16.6666666667)
505	POINT(0 0)
506	POINT(0 0)
507	POINT(1 1)
508	POINT(12 12)
509	POINT(5 5)
510	POINT(2 2)
511	POINT(1 1)
512	POINT(23.4210526316 23.4210526316)
513	POINT(5 5)
514	POINT(0 0)
515	POINT(0 0)
601	POINT(1.6666666667 3.3333333333)
602	POINT(4.1666666667 5.8333333333)
603	POINT(5 5)
604	POINT(1.6666666667 3.3333333333)
605	POINT(4.8843537415 4.8707482993)
606	POINT(0 0)
607	POINT(2.5 2.5)
608	POINT(5 5)
609	POINT(0 0)
610	POINT(-2.5 -2.5)
611	POINT(15 15)
612	POINT(3.4504132231 3.4504132231)
613	POINT(0 0)
614	POINT(0 0)
615	POINT(5 0)
701	POINT(0 0)
702	POINT(5 5)
703	POINT(5 5)
704	POINT(5 5)
705	POINT(5 5)
706	POINT(0 0)
707	POINT(0 0)
708	POINT(5 5)
709	POINT(1.00456621 2.1461187215)
710	POINT(50 50)
711	POINT(5 5)
712	POINT(1.00456621 2.1461187215)
713	POINT(5 5)
714	POINT(5 5)
715	POINT(5 5)
999	POINT(1 1)
SELECT fid, ST_ASTEXT(ST_CONVEXHULL(g)) FROM gis_geometry_1;
fid	ST_ASTEXT(ST_CONVEXHULL(g))
101	POINT(0 0)
102	POINT(1 0)
103	POINT(0 1)
104	POINT(1 1)
105	POINT(-1 1)
106	POINT(0 0)
107	POINT(10 0)
108	POINT(0 10)
109	POINT(-10 0)
110	POINT(0 -10)
112	POINT(1000 1000)
113	POINT(1000 -1000)
114	POINT(-1000 1000)
115	POINT(-1000 -1000)
201	LINESTRING(0 0,5 5)
202	LINESTRING(0 0,4 4)
203	LINESTRING(0 0,10 10)
204	LINESTRING(5 5,10 10)
205	LINESTRING(0 0,24 24)
206	LINESTRING(0 0,100 100)
207	LINESTRING(0 0,5 5)
208	POLYGON((-10 10,-5 -5,0 0,-10 10))
209	LINESTRING(0 0,8 8)
210	LINESTRING(0 0,5 5)
211	POLYGON((-50 -50,10 -10,0 0,-50 -50))
212	POLYGON((0 0,1000 -1000,1000 1000,0 0))
213	LINESTRING(1000 -1000,1000 1000)
214	LINESTRING(1 1,9 9)
215	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
301	POLYGON((0 0,5 5,0 5,0 0))
302	POLYGON((0 0,5 0,5 5,0 5,0 0))
303	POLYGON((0 0,10 0,10 10,0 10,0 0))
304	POLYGON((0 0,50 0,50 50,0 50,0 0))
305	POLYGON((0 0,10 0,10 10,0 10,0 0))
306	POLYGON((0 0,10 0,10 10,0 10,0 0))
307	POLYGON((0 0,5 5,0 5,0 0))
308	POLYGON((0 0,15 0,15 15,0 15,0 0))
309	POLYGON((0 0,10 0,10 10,0 10,0 0))
310	POLYGON((0 0,5 5,0 5,0 0))
311	POLYGON((10 10,15 10,15 15,10 15,10 10))
312	POLYGON((10 10,20 10,20 20,10 20,10 10))
313	POLYGON((0 0,10 0,10 10,0 10,0 0))
314	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
315	POLYGON((0 0,10 0,10 10,0 10,0 0))
401	POINT(0 0)
402	LINESTRING(0 0,4 4)
403	LINESTRING(0 0,10 10)
404	LINESTRING(0 0,100 100)
405	LINESTRING(0 0,1000 1000)
406	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
407	POINT(0 0)
408	LINESTRING(0 0,10 10)
409	LINESTRING(0 0,6 6)
410	POINT(0 0)
411	LINESTRING(0 0,1000 1000)
412	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
413	POINT(0 0)
414	LINESTRING(-1000 -1000,1000 1000)
415	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
501	LINESTRING(0 0,2 2)
502	LINESTRING(0 0,4 4)
503	LINESTRING(0 0,10 10)
504	LINESTRING(-100 -100,100 100)
505	LINESTRING(-1000 -1000,1000 1000)
506	POLYGON((-1000 -1000,1000 -1000,1000 1000,-1000 1000,-1000 -1000))
507	LINESTRING(0 0,2 2)
508	LINESTRING(0 0,24 24)
509	LINESTRING(0 0,10 10)
510	LINESTRING(0 0,4 4)
511	LINESTRING(0 0,2 2)
512	LINESTRING(0 0,50 50)
513	POLYGON((0 0,10 0,10 10,0 10,0 0))
514	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
515	POLYGON((-100 0,0 -100,100 0,0 100,-100 0))
601	POLYGON((0 0,5 5,0 5,0 0))
602	POLYGON((0 0,10 10,5 10,0 5,0 0))
603	POLYGON((0 0,10 0,10 10,0 10,0 0))
604	POLYGON((0 0,5 5,0 5,0 0))
605	POLYGON((-2 -2,0 -2,10 0,10 10,0 10,-2 -2))
606	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
607	POLYGON((0 0,5 0,5 5,0 5,0 0))
608	POLYGON((0 0,10 0,10 10,0 10,0 0))
609	POLYGON((-5 -5,0 -5,5 0,5 5,0 5,-5 0,-5 -5))
610	POLYGON((-5 -5,0 -5,0 0,-5 0,-5 -5))
611	POLYGON((10 10,20 10,20 20,10 20,10 10))
612	POLYGON((-5 -5,0 -5,10 0,10 10,0 10,-5 0,-5 -5))
613	POLYGON((-5 -5,5 -5,5 5,-5 5,-5 -5))
614	POLYGON((-10 -10,10 -10,10 10,-10 10,-10 -10))
615	POLYGON((0 0,5 -5,10 0,5 5,0 0))
701	POINT(0 0)
702	LINESTRING(0 0,10 10)
703	POLYGON((0 0,10 0,10 10,0 10,0 0))
704	LINESTRING(0 0,10 10)
705	POLYGON((0 0,10 0,10 10,0 10,0 0))
706	POLYGON((-10 -10,0 -10,10 0,10 10,0 10,-10 0,-10 -10))
707	POINT(0 0)
708	POLYGON((0 0,10 0,10 10,0 10,0 0))
709	POLYGON((-10 -10,10 0,10 10,0 10,-10 0,-10 -10))
710	LINESTRING(0 0,100 100)
711	POLYGON((0 0,10 0,18 18,0 10,0 0))
712	POLYGON((-10 -10,10 0,20 20,0 10,-10 0,-10 -10))
713	POLYGON((0 0,10 0,10 10,0 10,0 0))
714	POLYGON((0 0,10 0,10 10,0 10,0 0))
715	POLYGON((0 0,10 0,10 10,0 10,0 0))
999	POINT(1 1)
SELECT fid, ST_ISEMPTY(g) FROM gis_geometry_1;
fid	ST_ISEMPTY(g)
101	0
102	0
103	0
104	0
105	0
106	0
107	0
108	0
109	0
110	0
112	0
113	0
114	0
115	0
201	0
202	0
203	0
204	0
205	0
206	0
207	0
208	0
209	0
210	0
211	0
212	0
213	0
214	0
215	0
301	0
302	0
303	0
304	0
305	0
306	0
307	0
308	0
309	0
310	0
311	0
312	0
313	0
314	0
315	0
401	0
402	0
403	0
404	0
405	0
406	0
407	0
408	0
409	0
410	0
411	0
412	0
413	0
414	0
415	0
501	0
502	0
503	0
504	0
505	0
506	0
507	0
508	0
509	0
510	0
511	0
512	0
513	0
514	0
515	0
601	0
602	0
603	0
604	0
605	0
606	0
607	0
608	0
609	0
610	0
611	0
612	0
613	0
614	0
615	0
701	0
702	0
703	0
704	0
705	0
706	0
707	0
708	0
709	0
710	0
711	0
712	0
713	0
714	0
715	0
999	0
# Cleaning up the trigger
DROP TRIGGER geom_trigger;
# Checking the Geometry class properties within the cursor
CREATE PROCEDURE geom_cursor()
BEGIN
DECLARE v GEOMETRY;
DECLARE c CURSOR FOR SELECT g FROM gis_geometry;
OPEN c;
FETCH c INTO v;
CLOSE c;
SELECT ST_GEOMETRYTYPE(v);
SELECT ST_SRID(v);
SELECT ST_DIMENSION(v);
SELECT ST_ASTEXT(ST_ENVELOPE(v));
SELECT ST_ISSIMPLE(v);
SELECT ST_ASTEXT(ST_CENTROID(v));
SELECT ST_ASTEXT(ST_CONVEXHULL(v));
SELECT ST_ISEMPTY(v);
END|
# Calling the cursor
CALL geom_cursor();
ST_GEOMETRYTYPE(v)
POINT
ST_SRID(v)
0
ST_DIMENSION(v)
0
ST_ASTEXT(ST_ENVELOPE(v))
POINT(0 0)
ST_ISSIMPLE(v)
1
ST_ASTEXT(ST_CENTROID(v))
POINT(0 0)
ST_ASTEXT(ST_CONVEXHULL(v))
POINT(0 0)
ST_ISEMPTY(v)
0
# Dropping the created cursor
DROP PROCEDURE geom_cursor;
# Checking the Self join with the geometry properties
SELECT
ST_GEOMETRYTYPE(tableX.g), ST_GEOMETRYTYPE(tableY.g),
ST_SRID(tableX.g), ST_SRID(tableX.g),
ST_DIMENSION(tableX.g), ST_DIMENSION(tableX.g),
ST_ISSIMPLE(tableX.g), ST_ISSIMPLE(tableX.g),
ST_ISEMPTY(tableX.g), ST_ISEMPTY(tableX.g)
FROM gis_geometry AS tableX, gis_geometry AS tableY
WHERE
ST_GEOMETRYTYPE(tableX.g) = ST_GEOMETRYTYPE(tableY.g) AND
ST_SRID(tableX.g) = ST_SRID(tableY.g) AND
ST_DIMENSION(tableX.g) = ST_DIMENSION(tableY.g) AND
ST_ISSIMPLE(tableX.g) = ST_ISSIMPLE(tableY.g) AND
ST_ISEMPTY(tableX.g) = ST_ISEMPTY(tableY.g);
ST_GEOMETRYTYPE(tableX.g)	ST_GEOMETRYTYPE(tableY.g)	ST_SRID(tableX.g)	ST_SRID(tableX.g)	ST_DIMENSION(tableX.g)	ST_DIMENSION(tableX.g)	ST_ISSIMPLE(tableX.g)	ST_ISSIMPLE(tableX.g)	ST_ISEMPTY(tableX.g)	ST_ISEMPTY(tableX.g)
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
LINESTRING	LINESTRING	0	0	1	1	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
POLYGON	POLYGON	0	0	2	2	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	1	1	0	0
MULTIPOINT	MULTIPOINT	0	0	0	0	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	0	0	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTILINESTRING	MULTILINESTRING	0	0	1	1	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
MULTIPOLYGON	MULTIPOLYGON	0	0	2	2	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	0	0	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	0	0	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	1	1	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	1	1	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	1	1	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	1	1	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	1	1	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	1	1	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	0	0	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	0	0	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	1	1	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	1	1	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	1	1	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	1	1	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
GEOMCOLLECTION	GEOMCOLLECTION	0	0	2	2	0	0	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
POINT	POINT	0	0	0	0	1	1	0	0
# Checking the aggregate functions on the geometry class properties 
# Checking the SUM function
SELECT SUM(ST_GEOMETRYTYPE(g)) FROM gis_geometry WHERE fid % 100 = 1;
SUM(ST_GEOMETRYTYPE(g))
0
Warnings:
Warning	1292	Truncated incorrect DOUBLE value: 'POINT'
Warning	1292	Truncated incorrect DOUBLE value: 'LINESTRING'
Warning	1292	Truncated incorrect DOUBLE value: 'POLYGON'
Warning	1292	Truncated incorrect DOUBLE value: 'MULTIPOINT'
Warning	1292	Truncated incorrect DOUBLE value: 'MULTILINESTRING'
Warning	1292	Truncated incorrect DOUBLE value: 'MULTIPOLYGON'
Warning	1292	Truncated incorrect DOUBLE value: 'GEOMCOLLECTION'
SELECT SUM(ST_SRID(g)) FROM gis_geometry;
SUM(ST_SRID(g))
0
SELECT SUM(ST_DIMENSION(g)) FROM gis_geometry;
SUM(ST_DIMENSION(g))
111
SELECT SUM(ST_ISSIMPLE(g)) FROM gis_geometry;
SUM(ST_ISSIMPLE(g))
91
SELECT SUM(ST_ISEMPTY(g)) FROM gis_geometry;
SUM(ST_ISEMPTY(g))
0
# Checking the MAX function
SELECT MAX(ST_GEOMETRYTYPE(g)) FROM gis_geometry;
MAX(ST_GEOMETRYTYPE(g))
POLYGON
SELECT MAX(ST_SRID(g)) FROM gis_geometry;
MAX(ST_SRID(g))
0
SELECT MAX(ST_DIMENSION(g)) FROM gis_geometry;
MAX(ST_DIMENSION(g))
2
SELECT MAX(ST_ISSIMPLE(g)) FROM gis_geometry;
MAX(ST_ISSIMPLE(g))
1
SELECT MAX(ST_ISEMPTY(g)) FROM gis_geometry;
MAX(ST_ISEMPTY(g))
0
# Checking the MIN function
SELECT MIN(ST_GEOMETRYTYPE(g)) FROM gis_geometry;
MIN(ST_GEOMETRYTYPE(g))
GEOMCOLLECTION
SELECT MIN(ST_SRID(g)) FROM gis_geometry;
MIN(ST_SRID(g))
0
SELECT MIN(ST_DIMENSION(g)) FROM gis_geometry;
MIN(ST_DIMENSION(g))
0
SELECT MIN(ST_ISSIMPLE(g)) FROM gis_geometry;
MIN(ST_ISSIMPLE(g))
0
SELECT MIN(ST_ISEMPTY(g)) FROM gis_geometry;
MIN(ST_ISEMPTY(g))
0
# Checking the STD function
SELECT STD(ST_GEOMETRYTYPE(g)) FROM gis_geometry WHERE fid % 100 = 1;
STD(ST_GEOMETRYTYPE(g))
0
Warnings:
Warning	1292	Truncated incorrect DOUBLE value: 'POINT'
Warning	1292	Truncated incorrect DOUBLE value: 'LINESTRING'
Warning	1292	Truncated incorrect DOUBLE value: 'POLYGON'
Warning	1292	Truncated incorrect DOUBLE value: 'MULTIPOINT'
Warning	1292	Truncated incorrect DOUBLE value: 'MULTILINESTRING'
Warning	1292	Truncated incorrect DOUBLE value: 'MULTIPOLYGON'
Warning	1292	Truncated incorrect DOUBLE value: 'GEOMCOLLECTION'
SELECT STD(ST_SRID(g)) FROM gis_geometry;
STD(ST_SRID(g))
0
SELECT STD(ST_DIMENSION(g)) FROM gis_geometry;
STD(ST_DIMENSION(g))
0.8235059091
SELECT STD(ST_ISSIMPLE(g)) FROM gis_geometry;
STD(ST_ISSIMPLE(g))
0.33993463423951903
SELECT STD(ST_ISEMPTY(g)) FROM gis_geometry;
STD(ST_ISEMPTY(g))
0
# Checking the AVG function
SELECT AVG(ST_GEOMETRYTYPE(g)) FROM gis_geometry WHERE fid % 100 = 1;
AVG(ST_GEOMETRYTYPE(g))
0
Warnings:
Warning	1292	Truncated incorrect DOUBLE value: 'POINT'
Warning	1292	Truncated incorrect DOUBLE value: 'LINESTRING'
Warning	1292	Truncated incorrect DOUBLE value: 'POLYGON'
Warning	1292	Truncated incorrect DOUBLE value: 'MULTIPOINT'
Warning	1292	Truncated incorrect DOUBLE value: 'MULTILINESTRING'
Warning	1292	Truncated incorrect DOUBLE value: 'MULTIPOLYGON'
Warning	1292	Truncated incorrect DOUBLE value: 'GEOMCOLLECTION'
SELECT AVG(ST_SRID(g)) FROM gis_geometry;
AVG(ST_SRID(g))
0.0000
SELECT AVG(ST_DIMENSION(g)) FROM gis_geometry;
AVG(ST_DIMENSION(g))
1.0673
SELECT AVG(ST_ISSIMPLE(g)) FROM gis_geometry;
AVG(ST_ISSIMPLE(g))
0.8667
SELECT AVG(ST_ISEMPTY(g)) FROM gis_geometry;
AVG(ST_ISEMPTY(g))
0.0000
# Checking the DISTINCT function
SELECT COUNT(DISTINCT(ST_GEOMETRYTYPE(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_GEOMETRYTYPE(g)))
7
SELECT COUNT(DISTINCT(ST_SRID(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_SRID(g)))
1
SELECT COUNT(DISTINCT(ST_DIMENSION(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_DIMENSION(g)))
3
SELECT COUNT(DISTINCT(ST_ENVELOPE(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_ENVELOPE(g)))
42
SELECT COUNT(DISTINCT(ST_ISSIMPLE(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_ISSIMPLE(g)))
2
SELECT COUNT(DISTINCT(ST_CENTROID(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_CENTROID(g)))
42
SELECT COUNT(DISTINCT(ST_CONVEXHULL(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_CONVEXHULL(g)))
52
SELECT COUNT(DISTINCT(ST_ISEMPTY(g))) FROM gis_geometry;
COUNT(DISTINCT(ST_ISEMPTY(g)))
1
# Final cleanup
DROP TABLE gis_point;
DROP TABLE gis_linestring;
DROP TABLE gis_polygon;
DROP TABLE gis_multi_point;
DROP TABLE gis_multi_linestring;
DROP TABLE gis_multi_polygon;
DROP TABLE gis_geometrycollection;
DROP TABLE gis_geometry;
DROP TABLE gis_geometry_1;
