############## mysql-test\t\foreign_key_checks_basic.test #####################
#                                                                             #
# Variable Name: foreign_key_checks                                           #
# Scope: SESSION                                                              # 
# Access Type: Dynamic                                                        #
# Data Type: boolean                                                          #
# Default Value: NA                                                           #
# Range: NA                                                                   #
#                                                                             #
#                                                                             #
# Creation Date: 2008-02-07                                                   #
# Author:  Rizwan                                                             #
#                                                                             #
# Description: Test Cases of Dynamic System Variable foreign_key_checks       #
#              that checks the behavior of this variable in the following ways#
#              * Default Value                                                #
#              * Valid & Invalid values                                       #
#              * Scope & Access method                                        #
#              * Data Integrity                                               #
#                                                                             #
# Reference: http://dev.mysql.com/doc/refman/5.1/en/                          #
#  server-system-variables.html                                               #
#                                                                             #
###############################################################################

--source include/load_sysvars.inc

######################################################################## 
#                    START OF foreign_key_checks TESTS                 #
######################################################################## 


################################################################################ 
#      Saving initial value of foreign_key_checks in a temporary variable      #
################################################################################ 

SET @session_start_value = @@session.foreign_key_checks;
SELECT @session_start_value;

--echo '#--------------------FN_DYNVARS_032_01------------------------#'
######################################################################## 
#           Display the DEFAULT value of foreign_key_checks            #
######################################################################## 

SET @@session.foreign_key_checks = 1;
SET @@session.foreign_key_checks = DEFAULT;
SELECT @@session.foreign_key_checks;


--echo '#---------------------FN_DYNVARS_032_02-------------------------#'
############################################################################# 
#   Check if foreign_key_checks can be accessed with and without @@ sign    #
############################################################################# 

SET foreign_key_checks = 1;
SELECT @@foreign_key_checks;

--Error ER_UNKNOWN_TABLE
SELECT session.foreign_key_checks;

--Error ER_UNKNOWN_TABLE
SELECT local.foreign_key_checks;

SET session foreign_key_checks = 0;
SELECT @@session.foreign_key_checks;


--echo '#--------------------FN_DYNVARS_032_03------------------------#'
######################################################################## 
#      change the value of foreign_key_checks to a valid value         #
######################################################################## 

SET @@session.foreign_key_checks = 0;
SELECT @@session.foreign_key_checks;
SET @@session.foreign_key_checks = 1;
SELECT @@session.foreign_key_checks;


--echo '#--------------------FN_DYNVARS_032_04-------------------------#'
########################################################################### 
#       Change the value of foreign_key_checks to invalid value           #
########################################################################### 

--Error ER_WRONG_VALUE_FOR_VAR
SET @@session.foreign_key_checks = -1;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@session.foreign_key_checks = 2;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@session.foreign_key_checks = "T";
--Error ER_WRONG_VALUE_FOR_VAR
SET @@session.foreign_key_checks = "Y";
--Error ER_WRONG_VALUE_FOR_VAR
SET @@session.foreign_key_checks = TRÜE;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@session.foreign_key_checks = ÕN;
--error ER_PARSE_ERROR
SET @@session.foreign_key_checks = OF;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@session.foreign_key_checks = ÓFF;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@session.foreign_key_checks = '¹';
--Error ER_WRONG_VALUE_FOR_VAR
SET @@session.foreign_key_checks = NO;


--echo '#-------------------FN_DYNVARS_032_05----------------------------#'
########################################################################### 
#       Test if accessing global foreign_key_checks gives error           #
########################################################################### 

SET @@global.foreign_key_checks = 0;
SELECT @@global.foreign_key_checks;
SET @@global.foreign_key_checks = 1;

--echo '#----------------------FN_DYNVARS_032_06------------------------#'
######################################################################### 
#     Check if the value in GLOBAL Table contains variable value        #
#########################################################################

--disable_warnings
SELECT count(VARIABLE_VALUE) FROM performance_schema.global_variables WHERE VARIABLE_NAME='foreign_key_checks';
--enable_warnings

--echo '#----------------------FN_DYNVARS_032_07------------------------#'
######################################################################### 
#     Check if the value in GLOBAL Table matches value in variable     #
#########################################################################

--disable_warnings
SELECT IF(@@session.foreign_key_checks, "ON", "OFF") = VARIABLE_VALUE 
FROM performance_schema.session_variables 
WHERE VARIABLE_NAME='foreign_key_checks';
--enable_warnings
SELECT @@session.foreign_key_checks;
--disable_warnings
SELECT VARIABLE_VALUE 
FROM performance_schema.session_variables 
WHERE VARIABLE_NAME='foreign_key_checks';
--enable_warnings


--echo '#---------------------FN_DYNVARS_032_08-------------------------#'
################################################################### 
#        Check if ON and OFF values can be used on variable       #
################################################################### 

SET @@session.foreign_key_checks = OFF;
SELECT @@session.foreign_key_checks;
SET @@session.foreign_key_checks = ON;
SELECT @@session.foreign_key_checks;

--echo '#---------------------FN_DYNVARS_032_09----------------------#'
################################################################### 
#      Check if TRUE and FALSE values can be used on variable     #
################################################################### 

SET @@session.foreign_key_checks = TRUE;
SELECT @@session.foreign_key_checks;
SET @@session.foreign_key_checks = FALSE;
SELECT @@session.foreign_key_checks;

##############################  
#   Restore initial value    #
##############################

SET @@session.foreign_key_checks = @session_start_value;
SELECT @@session.foreign_key_checks;

###############################################################
#                    END OF foreign_key_checks TESTS          #
############################################################### 
