drop table if exists t1,t2,t3;
CREATE TABLE t1 (S1 INT);
CREATE TABLE t2 (S1 INT);
INSERT INTO t1 VALUES (1);
INSERT INTO t2 VALUES (2);
SELECT * FROM t1 JOIN t2;
S1	S1
1	2
SELECT * FROM t1 INNER JOIN t2;
S1	S1
1	2
SELECT * from t1 JOIN t2 USING (S1);
S1
SELECT * FROM t1 INNER JOIN t2 USING (S1);
S1
SELECT * from t1 CROSS JOIN t2;
S1	S1
1	2
SELECT * from t1 LEFT JOIN t2 USING(S1);
S1
1
SELECT * from t1 LEFT JOIN t2 ON(t2.S1=2);
S1	S1
1	2
SELECT * from t1 RIGHT JOIN t2 USING(S1);
S1
2
SELECT * from t1 RIGHT JOIN t2 ON(t1.S1=1);
S1	S1
1	2
drop table t1,t2;
create table t1 (id int primary key);
create table t2 (id int);
insert into t1 values (75);
insert into t1 values (79);
insert into t1 values (78);
insert into t1 values (77);
replace into t1 values (76);
replace into t1 values (76);
insert into t1 values (104);
insert into t1 values (103);
insert into t1 values (102);
insert into t1 values (101);
insert into t1 values (105);
insert into t1 values (106);
insert into t1 values (107);
insert into t2 values (107),(75),(1000);
select t1.id, t2.id from t1, t2 where t2.id = t1.id;
id	id
107	107
75	75
select t1.id, count(t2.id) from t1,t2 where t2.id = t1.id group by t1.id;
id	count(t2.id)
107	1
75	1
select t1.id, count(t2.id) from t1,t2 where t2.id = t1.id group by t2.id;
id	count(t2.id)
107	1
75	1
select t1.id,t2.id from t2 left join t1 on t1.id>=74 and t1.id<=0 where t2.id=75 and t1.id is null;
id	id
NULL	75
explain select t1.id,t2.id from t2 left join t1 on t1.id>=74 and t1.id<=0 where t2.id=75 and t1.id is null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	const	PRIMARY	NULL	NULL	NULL	1	100.00	Impossible ON condition
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t2`.`id` AS `id` from `test`.`t2` left join `test`.`t1` on(((`test`.`t1`.`id` >= 74) and (`test`.`t1`.`id` <= 0))) where ((`test`.`t2`.`id` = 75) and (`test`.`t1`.`id` is null))
explain select t1.id, t2.id from t1, t2 where t2.id = t1.id and t1.id <0 and t1.id > 0;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t2`.`id` AS `id` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`id` < 0) and (`test`.`t1`.`id` > 0) and multiple equal(`test`.`t2`.`id`, `test`.`t1`.`id`))
drop table t1,t2;
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1 (
id int(11) NOT NULL auto_increment,
token varchar(100) DEFAULT '' NOT NULL,
count int(11) DEFAULT '0' NOT NULL,
qty int(11),
phone char(1) DEFAULT '' NOT NULL,
timestamp datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
PRIMARY KEY (id),
KEY token (token(15)),
KEY timestamp (timestamp),
UNIQUE token_2 (token(75),count,phone)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
SET sql_mode = default;
INSERT INTO t1 VALUES (21,'e45703b64de71482360de8fec94c3ade',3,7800,'n','1999-12-23 17:22:21');
INSERT INTO t1 VALUES (22,'e45703b64de71482360de8fec94c3ade',4,5000,'y','1999-12-23 17:22:21');
INSERT INTO t1 VALUES (18,'346d1cb63c89285b2351f0ca4de40eda',3,13200,'b','1999-12-23 11:58:04');
INSERT INTO t1 VALUES (17,'ca6ddeb689e1b48a04146b1b5b6f936a',4,15000,'b','1999-12-23 11:36:53');
INSERT INTO t1 VALUES (16,'ca6ddeb689e1b48a04146b1b5b6f936a',3,13200,'b','1999-12-23 11:36:53');
INSERT INTO t1 VALUES (26,'a71250b7ed780f6ef3185bfffe027983',5,1500,'b','1999-12-27 09:44:24');
INSERT INTO t1 VALUES (24,'4d75906f3c37ecff478a1eb56637aa09',3,5400,'y','1999-12-23 17:29:12');
INSERT INTO t1 VALUES (25,'4d75906f3c37ecff478a1eb56637aa09',4,6500,'y','1999-12-23 17:29:12');
INSERT INTO t1 VALUES (27,'a71250b7ed780f6ef3185bfffe027983',3,6200,'b','1999-12-27 09:44:24');
INSERT INTO t1 VALUES (28,'a71250b7ed780f6ef3185bfffe027983',3,5400,'y','1999-12-27 09:44:36');
INSERT INTO t1 VALUES (29,'a71250b7ed780f6ef3185bfffe027983',4,17700,'b','1999-12-27 09:45:05');
CREATE TABLE t2 (
id int(11) NOT NULL auto_increment,
category int(11) DEFAULT '0' NOT NULL,
county int(11) DEFAULT '0' NOT NULL,
state int(11) DEFAULT '0' NOT NULL,
phones int(11) DEFAULT '0' NOT NULL,
nophones int(11) DEFAULT '0' NOT NULL,
PRIMARY KEY (id),
KEY category (category,county,state)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES (3,2,11,12,5400,7800);
INSERT INTO t2 VALUES (4,2,25,12,6500,11200);
INSERT INTO t2 VALUES (5,1,37,6,10000,12000);
select a.id, b.category as catid, b.state as stateid, b.county as countyid from t1 a, t2 b ignore index (primary) where (a.token ='a71250b7ed780f6ef3185bfffe027983') and (a.count = b.id);
id	catid	stateid	countyid
26	1	6	37
27	2	12	11
28	2	12	11
29	2	12	25
select a.id, b.category as catid, b.state as stateid, b.county as
countyid from t1 a, t2 b where (a.token =
'a71250b7ed780f6ef3185bfffe027983') and (a.count = b.id) order by a.id;
id	catid	stateid	countyid
26	1	6	37
27	2	12	11
28	2	12	11
29	2	12	25
drop table t1, t2;
create table t1 (a int primary key);
insert into t1 values(1),(2);
select t1.a from t1 as t1 left join t1 as t2 using (a) left join t1 as t3 using (a) left join t1 as t4 using (a) left join t1 as t5 using (a) left join t1 as t6 using (a) left join t1 as t7 using (a) left join t1 as t8 using (a) left join t1 as t9 using (a) left join t1 as t10 using (a) left join t1 as t11 using (a) left join t1 as t12 using (a) left join t1 as t13 using (a) left join t1 as t14 using (a) left join t1 as t15 using (a) left join t1 as t16 using (a) left join t1 as t17 using (a) left join t1 as t18 using (a) left join t1 as t19 using (a) left join t1 as t20 using (a) left join t1 as t21 using (a) left join t1 as t22 using (a) left join t1 as t23 using (a) left join t1 as t24 using (a) left join t1 as t25 using (a) left join t1 as t26 using (a) left join t1 as t27 using (a) left join t1 as t28 using (a) left join t1 as t29 using (a) left join t1 as t30 using (a) left join t1 as t31 using (a);
a
1
2
select t1.a from t1 as t1 left join t1 as t2 using (a) left join t1 as t3 using (a) left join t1 as t4 using (a) left join t1 as t5 using (a) left join t1 as t6 using (a) left join t1 as t7 using (a) left join t1 as t8 using (a) left join t1 as t9 using (a) left join t1 as t10 using (a) left join t1 as t11 using (a) left join t1 as t12 using (a) left join t1 as t13 using (a) left join t1 as t14 using (a) left join t1 as t15 using (a) left join t1 as t16 using (a) left join t1 as t17 using (a) left join t1 as t18 using (a) left join t1 as t19 using (a) left join t1 as t20 using (a) left join t1 as t21 using (a) left join t1 as t22 using (a) left join t1 as t23 using (a) left join t1 as t24 using (a) left join t1 as t25 using (a) left join t1 as t26 using (a) left join t1 as t27 using (a) left join t1 as t28 using (a) left join t1 as t29 using (a) left join t1 as t30 using (a) left join t1 as t31 using (a) left join t1 as t32 using (a) left join t1 as t33 using (a) left join t1 as t34 using (a) left join t1 as t35 using (a) left join t1 as t36 using (a) left join t1 as t37 using (a) left join t1 as t38 using (a) left join t1 as t39 using (a) left join t1 as t40 using (a) left join t1 as t41 using (a) left join t1 as t42 using (a) left join t1 as t43 using (a) left join t1 as t44 using (a) left join t1 as t45 using (a) left join t1 as t46 using (a) left join t1 as t47 using (a) left join t1 as t48 using (a) left join t1 as t49 using (a) left join t1 as t50 using (a) left join t1 as t51 using (a) left join t1 as t52 using (a) left join t1 as t53 using (a) left join t1 as t54 using (a) left join t1 as t55 using (a) left join t1 as t56 using (a) left join t1 as t57 using (a) left join t1 as t58 using (a) left join t1 as t59 using (a) left join t1 as t60 using (a) left join t1 as t61 using (a) left join t1 as t62 using (a) left join t1 as t63 using (a) left join t1 as t64 using (a) left join t1 as t65 using (a);
ERROR HY000: Too many tables; MySQL can only use XX tables in a join
select a from t1 as t1 left join t1 as t2 using (a) left join t1 as t3 using (a) left join t1 as t4 using (a) left join t1 as t5 using (a) left join t1 as t6 using (a) left join t1 as t7 using (a) left join t1 as t8 using (a) left join t1 as t9 using (a) left join t1 as t10 using (a) left join t1 as t11 using (a) left join t1 as t12 using (a) left join t1 as t13 using (a) left join t1 as t14 using (a) left join t1 as t15 using (a) left join t1 as t16 using (a) left join t1 as t17 using (a) left join t1 as t18 using (a) left join t1 as t19 using (a) left join t1 as t20 using (a) left join t1 as t21 using (a) left join t1 as t22 using (a) left join t1 as t23 using (a) left join t1 as t24 using (a) left join t1 as t25 using (a) left join t1 as t26 using (a) left join t1 as t27 using (a) left join t1 as t28 using (a) left join t1 as t29 using (a) left join t1 as t30 using (a) left join t1 as t31 using (a);
a
1
2
select a from t1 as t1 left join t1 as t2 using (a) left join t1 as t3 using (a) left join t1 as t4 using (a) left join t1 as t5 using (a) left join t1 as t6 using (a) left join t1 as t7 using (a) left join t1 as t8 using (a) left join t1 as t9 using (a) left join t1 as t10 using (a) left join t1 as t11 using (a) left join t1 as t12 using (a) left join t1 as t13 using (a) left join t1 as t14 using (a) left join t1 as t15 using (a) left join t1 as t16 using (a) left join t1 as t17 using (a) left join t1 as t18 using (a) left join t1 as t19 using (a) left join t1 as t20 using (a) left join t1 as t21 using (a) left join t1 as t22 using (a) left join t1 as t23 using (a) left join t1 as t24 using (a) left join t1 as t25 using (a) left join t1 as t26 using (a) left join t1 as t27 using (a) left join t1 as t28 using (a) left join t1 as t29 using (a) left join t1 as t30 using (a) left join t1 as t31 using (a) left join t1 as t32 using (a) left join t1 as t33 using (a) left join t1 as t34 using (a) left join t1 as t35 using (a) left join t1 as t36 using (a) left join t1 as t37 using (a) left join t1 as t38 using (a) left join t1 as t39 using (a) left join t1 as t40 using (a) left join t1 as t41 using (a) left join t1 as t42 using (a) left join t1 as t43 using (a) left join t1 as t44 using (a) left join t1 as t45 using (a) left join t1 as t46 using (a) left join t1 as t47 using (a) left join t1 as t48 using (a) left join t1 as t49 using (a) left join t1 as t50 using (a) left join t1 as t51 using (a) left join t1 as t52 using (a) left join t1 as t53 using (a) left join t1 as t54 using (a) left join t1 as t55 using (a) left join t1 as t56 using (a) left join t1 as t57 using (a) left join t1 as t58 using (a) left join t1 as t59 using (a) left join t1 as t60 using (a) left join t1 as t61 using (a) left join t1 as t62 using (a) left join t1 as t63 using (a) left join t1 as t64 using (a) left join t1 as t65 using (a);
ERROR HY000: Too many tables; MySQL can only use XX tables in a join
drop table t1;
CREATE TABLE t1 (
a int(11) NOT NULL,
b int(11) NOT NULL,
PRIMARY KEY  (a,b)
) ENGINE=MyISAM;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1,1),(1,2),(1,3),(1,4),(1,5),(1,6),(1,7),(2,3);
CREATE TABLE t2 (
a int(11) default NULL
) ENGINE=MyISAM;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES (2),(3);
SELECT t1.a,t2.a,b FROM t1,t2 WHERE t1.a=t2.a AND (t1.a=1 OR t1.a=2) AND b>=1 AND b<=3;
a	a	b
2	2	3
DROP TABLE t1, t2;
CREATE TABLE t1 (d DATE NOT NULL);
CREATE TABLE t2 (d DATE NOT NULL);
INSERT INTO t1 (d) VALUES ('2001-08-01'),('1000-01-01');
SELECT * FROM t1 LEFT JOIN t2 USING (d) WHERE t2.d IS NULL;
d
2001-08-01
1000-01-01
SELECT * FROM t1 LEFT JOIN t2 USING (d) WHERE d IS NULL;
d
SELECT * from t1 WHERE t1.d IS NULL;
d
SELECT * FROM t1 WHERE 1/0 IS NULL;
d
2001-08-01
1000-01-01
Warnings:
Warning	1365	Division by 0
DROP TABLE t1,t2;
CREATE TABLE t1 (
Document_ID varchar(50) NOT NULL default '',
Contractor_ID varchar(6) NOT NULL default '',
Language_ID char(3) NOT NULL default '',
Expiration_Date datetime default NULL,
Publishing_Date datetime default NULL,
Title text,
Column_ID varchar(50) NOT NULL default '',
PRIMARY KEY  (Language_ID,Document_ID,Contractor_ID)
);
INSERT INTO t1 VALUES ('xep80','1','ger','2001-12-31 20:00:00','2001-11-12 10:58:00','Kartenbestellung - jetzt auch online','anle'),('','999998','',NULL,NULL,NULL,'');
CREATE TABLE t2 (
Contractor_ID char(6) NOT NULL default '',
Language_ID char(3) NOT NULL default '',
Document_ID char(50) NOT NULL default '',
CanRead char(1) default NULL,
Customer_ID int(11) NOT NULL default '0',
PRIMARY KEY  (Contractor_ID,Language_ID,Document_ID,Customer_ID)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES ('5','ger','xep80','1',999999),('1','ger','xep80','1',999999);
CREATE TABLE t3 (
Language_ID char(3) NOT NULL default '',
Column_ID char(50) NOT NULL default '',
Contractor_ID char(6) NOT NULL default '',
CanRead char(1) default NULL,
Active char(1) default NULL,
PRIMARY KEY  (Language_ID,Column_ID,Contractor_ID)
);
INSERT INTO t3 VALUES ('ger','home','1','1','1'),('ger','Test','1','0','0'),('ger','derclu','1','0','0'),('ger','clubne','1','0','0'),('ger','philos','1','0','0'),('ger','clubko','1','0','0'),('ger','clubim','1','1','1'),('ger','progra','1','0','0'),('ger','progvo','1','0','0'),('ger','progsp','1','0','0'),('ger','progau','1','0','0'),('ger','progku','1','0','0'),('ger','progss','1','0','0'),('ger','nachl','1','0','0'),('ger','mitgli','1','0','0'),('ger','mitsu','1','0','0'),('ger','mitbus','1','0','0'),('ger','ergmar','1','1','1'),('ger','home','4','1','1'),('ger','derclu','4','1','1'),('ger','clubne','4','0','0'),('ger','philos','4','1','1'),('ger','clubko','4','1','1'),('ger','clubim','4','1','1'),('ger','progra','4','1','1'),('ger','progvo','4','1','1'),('ger','progsp','4','1','1'),('ger','progau','4','0','0'),('ger','progku','4','1','1'),('ger','progss','4','1','1'),('ger','nachl','4','1','1'),('ger','mitgli','4','0','0'),('ger','mitsu','4','0','0'),('ger','mitbus','4','0','0'),('ger','ergmar','4','1','1'),('ger','progra2','1','0','0'),('ger','archiv','4','1','1'),('ger','anmeld','4','1','1'),('ger','thema','4','1','1'),('ger','edito','4','1','1'),('ger','madis','4','1','1'),('ger','enma','4','1','1'),('ger','madis','1','1','1'),('ger','enma','1','1','1'),('ger','vorsch','4','0','0'),('ger','veranst','4','0','0'),('ger','anle','4','1','1'),('ger','redak','4','1','1'),('ger','nele','4','1','1'),('ger','aukt','4','1','1'),('ger','callcenter','4','1','1'),('ger','anle','1','0','0');
delete from t1 where Contractor_ID='999998';
insert into t1 (Contractor_ID) Values ('999998');
SELECT DISTINCT COUNT(t1.Title) FROM t1,
t2, t3 WHERE 
t1.Document_ID='xep80' AND t1.Contractor_ID='1' AND 
t1.Language_ID='ger' AND '2001-12-21 23:14:24' >= 
Publishing_Date AND '2001-12-21 23:14:24' <= Expiration_Date AND 
t1.Document_ID = t2.Document_ID AND 
t1.Language_ID = t2.Language_ID AND 
t1.Contractor_ID = t2.Contractor_ID AND ( 
t2.Customer_ID = '4'  OR 
t2.Customer_ID = '999999'  OR 
t2.Customer_ID = '1' )AND t2.CanRead 
= '1'  AND t1.Column_ID=t3.Column_ID AND 
t1.Language_ID=t3.Language_ID AND ( 
t3.Contractor_ID = '4'  OR 
t3.Contractor_ID = '999999'  OR 
t3.Contractor_ID = '1') AND 
t3.CanRead='1' AND t3.Active='1';
COUNT(t1.Title)
1
SELECT DISTINCT COUNT(t1.Title) FROM t1,
t2, t3 WHERE 
t1.Document_ID='xep80' AND t1.Contractor_ID='1' AND 
t1.Language_ID='ger' AND '2001-12-21 23:14:24' >= 
Publishing_Date AND '2001-12-21 23:14:24' <= Expiration_Date AND 
t1.Document_ID = t2.Document_ID AND 
t1.Language_ID = t2.Language_ID AND 
t1.Contractor_ID = t2.Contractor_ID AND ( 
t2.Customer_ID = '4'  OR 
t2.Customer_ID = '999999'  OR 
t2.Customer_ID = '1' )AND t2.CanRead 
= '1'  AND t1.Column_ID=t3.Column_ID AND 
t1.Language_ID=t3.Language_ID AND ( 
t3.Contractor_ID = '4'  OR 
t3.Contractor_ID = '999999'  OR 
t3.Contractor_ID = '1') AND 
t3.CanRead='1' AND t3.Active='1';
COUNT(t1.Title)
1
drop table t1,t2,t3;
CREATE TABLE t1 (
t1_id int(11) default NULL,
t2_id int(11) default NULL,
type enum('Cost','Percent') default NULL,
cost_unit enum('Cost','Unit') default NULL,
min_value double default NULL,
max_value double default NULL,
t3_id int(11) default NULL,
item_id int(11) default NULL
) ENGINE=MyISAM;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (12,5,'Percent','Cost',-1,0,-1,-1),(14,4,'Percent','Cost',-1,0,-1,-1),(18,5,'Percent','Cost',-1,0,-1,-1),(19,4,'Percent','Cost',-1,0,-1,-1),(20,5,'Percent','Cost',100,-1,22,291),(21,5,'Percent','Cost',100,-1,18,291),(22,1,'Percent','Cost',100,-1,6,291),(23,1,'Percent','Cost',100,-1,21,291),(24,1,'Percent','Cost',100,-1,9,291),(25,1,'Percent','Cost',100,-1,4,291),(26,1,'Percent','Cost',100,-1,20,291),(27,4,'Percent','Cost',100,-1,7,202),(28,1,'Percent','Cost',50,-1,-1,137),(29,2,'Percent','Cost',100,-1,4,354),(30,2,'Percent','Cost',100,-1,9,137),(93,2,'Cost','Cost',-1,10000000,-1,-1);
CREATE TABLE t2 (
id int(10) unsigned NOT NULL auto_increment,
name varchar(255) default NULL,
PRIMARY KEY  (id)
) ENGINE=MyISAM;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES (1,'s1'),(2,'s2'),(3,'s3'),(4,'s4'),(5,'s5');
select t1.*, t2.*  from t1, t2 where t2.id=t1.t2_id limit 2;
t1_id	t2_id	type	cost_unit	min_value	max_value	t3_id	item_id	id	name
12	5	Percent	Cost	-1	0	-1	-1	5	s5
14	4	Percent	Cost	-1	0	-1	-1	4	s4
drop table t1,t2;
CREATE TABLE t1 (
siteid varchar(25) NOT NULL default '',
emp_id varchar(30) NOT NULL default '',
rate_code varchar(10) default NULL,
UNIQUE KEY site_emp (siteid,emp_id),
KEY siteid (siteid)
) ENGINE=MyISAM;
INSERT INTO t1 VALUES ('rivercats','psmith','cust'), ('rivercats','KWalker','cust');
CREATE TABLE t2 (
siteid varchar(25) NOT NULL default '',
rate_code varchar(10) NOT NULL default '',
base_rate float NOT NULL default '0',
PRIMARY KEY  (siteid,rate_code),
FULLTEXT KEY rate_code (rate_code)
) ENGINE=MyISAM;
INSERT INTO t2 VALUES ('rivercats','cust',20);
SELECT emp.rate_code, lr.base_rate FROM t1 AS emp LEFT JOIN t2 AS lr USING (siteid, rate_code) WHERE emp.emp_id = 'psmith' AND lr.siteid = 'rivercats';
rate_code	base_rate
cust	20
SELECT emp.rate_code, lr.base_rate FROM t1 AS emp LEFT JOIN t2 AS lr USING (siteid, rate_code) WHERE lr.siteid = 'rivercats' AND emp.emp_id = 'psmith';
rate_code	base_rate
cust	20
SELECT rate_code, lr.base_rate FROM t1 AS emp LEFT JOIN t2 AS lr USING (siteid, rate_code) WHERE emp.emp_id = 'psmith' AND siteid = 'rivercats';
rate_code	base_rate
cust	20
SELECT rate_code, lr.base_rate FROM t1 AS emp LEFT JOIN t2 AS lr USING (siteid, rate_code) WHERE siteid = 'rivercats' AND emp.emp_id = 'psmith';
rate_code	base_rate
cust	20
drop table t1,t2;
CREATE TABLE t1 (ID INTEGER NOT NULL PRIMARY KEY, Value1 VARCHAR(255));
CREATE TABLE t2 (ID INTEGER NOT NULL PRIMARY KEY, Value2 VARCHAR(255));
INSERT INTO t1 VALUES (1, 'A');
INSERT INTO t2 VALUES (1, 'B');
SELECT * FROM t1 NATURAL JOIN t2 WHERE 1 AND (Value1 = 'A' AND Value2 <> 'B');
ID	Value1	Value2
SELECT * FROM t1 NATURAL JOIN t2 WHERE 1 AND Value1 = 'A' AND Value2 <> 'B';
ID	Value1	Value2
SELECT * FROM t1 NATURAL JOIN t2 WHERE (Value1 = 'A' AND Value2 <> 'B') AND 1;
ID	Value1	Value2
drop table t1,t2;
CREATE TABLE t1 (a int);
CREATE TABLE t2 (b int);
CREATE TABLE t3 (c int);
SELECT * FROM t1 NATURAL JOIN t2 NATURAL JOIN t3;
a	b	c
DROP TABLE t1, t2, t3;
create table t1 (i int);
create table t2 (i int);
create table t3 (i int);
insert into t1 values(1),(2);
insert into t2 values(2),(3);
insert into t3 values (2),(4);
select * from t1 natural left join t2;
i
1
2
select * from t1 left join t2 on (t1.i=t2.i);
i	i
1	NULL
2	2
select * from t1 natural left join t2 natural left join t3;
i
1
2
select * from t1 left join t2 on (t1.i=t2.i) left join t3 on (t2.i=t3.i);
i	i	i
1	NULL	NULL
2	2	2
select * from t3 natural right join t2;
i
2
3
select * from t3 right join t2 on (t3.i=t2.i);
i	i
2	2
NULL	3
select * from t3 natural right join t2 natural right join t1;
i
1
2
select * from t3 right join t2 on (t3.i=t2.i) right join t1 on (t2.i=t1.i);
i	i	i
2	2	2
NULL	NULL	1
select * from t1,t2 natural left join t3 order by t1.i,t2.i,t3.i;
i	i
1	2
1	3
2	2
2	3
select * from t1,t2 left join t3 on (t2.i=t3.i) order by t1.i,t2.i,t3.i;
i	i	i
1	2	2
1	3	NULL
2	2	2
2	3	NULL
select t1.i,t2.i,t3.i from t2 natural left join t3,t1 order by t1.i,t2.i,t3.i;
i	i	i
1	2	2
1	3	NULL
2	2	2
2	3	NULL
select t1.i,t2.i,t3.i from t2 left join t3 on (t2.i=t3.i),t1 order by t1.i,t2.i,t3.i;
i	i	i
1	2	2
1	3	NULL
2	2	2
2	3	NULL
select * from t1,t2 natural right join t3 order by t1.i,t2.i,t3.i;
i	i
1	4
1	2
2	4
2	2
select * from t1,t2 right join t3 on (t2.i=t3.i) order by t1.i,t2.i,t3.i;
i	i	i
1	NULL	4
1	2	2
2	NULL	4
2	2	2
select t1.i,t2.i,t3.i from t2 natural right join t3,t1 order by t1.i,t2.i,t3.i;
i	i	i
1	NULL	4
1	2	2
2	NULL	4
2	2	2
select t1.i,t2.i,t3.i from t2 right join t3 on (t2.i=t3.i),t1 order by t1.i,t2.i,t3.i;
i	i	i
1	NULL	4
1	2	2
2	NULL	4
2	2	2
drop table t1,t2,t3;
CREATE TABLE t1 (a int, b int default 0, c int default 1);
INSERT INTO t1 (a) VALUES (1),(2),(3),(4),(5),(6),(7),(8);
INSERT INTO t1 (a) SELECT a + 8 FROM t1;
INSERT INTO t1 (a) SELECT a + 16 FROM t1;
CREATE TABLE t2 (a int, d int, e int default 0);
INSERT INTO t2 (a, d) VALUES (1,1),(2,2),(3,3),(4,4);
INSERT INTO t2 (a, d) SELECT a+4, a+4 FROM t2;
INSERT INTO t2 (a, d) SELECT a+8, a+8 FROM t2;
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN
SELECT STRAIGHT_JOIN t2.e FROM t1,t2 WHERE t2.d=1 AND t1.b=t2.e
ORDER BY t1.b, t1.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	32	100.00	Using temporary; Using filesort
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	16	6.25	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t2`.`e` AS `e` from `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`e` = `test`.`t1`.`b`) and (`test`.`t2`.`d` = 1)) order by `test`.`t1`.`b`,`test`.`t1`.`c`
SELECT STRAIGHT_JOIN t2.e FROM t1,t2 WHERE t2.d=1 AND t1.b=t2.e
ORDER BY t1.b, t1.c;
e
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
0
DROP TABLE t1,t2;
create table t1 (c int, b int);
create table t2 (a int, b int);
create table t3 (b int, c int);
create table t4 (y int, c int);
create table t5 (y int, z int);
create table t6 (a int, c int);
insert into t1 values (10,1);
insert into t1 values (3 ,1);
insert into t1 values (3 ,2);
insert into t2 values (2, 1);
insert into t3 values (1, 3);
insert into t3 values (1,10);
insert into t4 values (11,3);
insert into t4 values (2, 3);
insert into t5 values (11,4);
insert into t6 values (2, 3);
create algorithm=merge view v1a as
select * from t1 natural join t2;
create algorithm=merge view v1b(a,b,c) as
select * from t1 natural join t2;
create algorithm=merge view v1c as
select b as a, c as b, a as c from t1 natural join t2;
create algorithm=merge view v1d(b, a, c) as
select a as c, c as b, b as a from t1 natural join t2;
create algorithm=merge view v2a as
select t1.c, t1.b, t2.a from t1 join (t2 join t4 on b + 1 = y) on t1.c = t4.c;
create algorithm=merge view v2b as
select t1.c as b, t1.b as a, t2.a as c
from t1 join (t2 join t4 on b + 1 = y) on t1.c = t4.c;
create algorithm=merge view v3a as
select * from t1 natural join t2 natural join t3;
create algorithm=merge view v3b as
select * from t1 natural join (t2 natural join t3);
create algorithm=merge view v4 as
select * from v2a natural join v3a;
select * from (t1 natural join t2) natural join (t3 natural join t4);
b	c	a	y
1	3	2	11
1	3	2	2
select * from (t1 natural join t2) natural left join (t3 natural join t4);
b	c	a	y
1	10	2	NULL
1	3	2	11
1	3	2	2
select * from (t3 natural join t4) natural right join (t1 natural join t2);
b	c	a	y
1	10	2	NULL
1	3	2	11
1	3	2	2
select * from (t1 natural left join t2) natural left join (t3 natural left join t4);
b	c	a	y
1	10	2	NULL
1	3	2	11
1	3	2	2
2	3	NULL	NULL
select * from (t4 natural right join t3) natural right join (t2 natural right join t1);
b	c	a	y
1	10	2	NULL
1	3	2	11
1	3	2	2
2	3	NULL	NULL
select * from t1 natural join t2 natural join t3 natural join t4;
c	b	a	y
3	1	2	11
3	1	2	2
select * from ((t1 natural join t2) natural join t3) natural join t4;
c	b	a	y
3	1	2	11
3	1	2	2
select * from t1 natural join (t2 natural join (t3 natural join t4));
c	b	a	y
3	1	2	11
3	1	2	2
select * from t5 natural right join (t4 natural right join ((t2 natural right join t1) natural right join t3));
y	c	b	a	z
11	3	1	2	4
2	3	1	2	NULL
NULL	10	1	2	NULL
select * from (t1 natural join t2), (t3 natural join t4);
b	c	a	c	b	y
1	10	2	3	1	11
1	10	2	3	1	2
1	3	2	3	1	11
1	3	2	3	1	2
select * from t5 natural join ((t1 natural join t2), (t3 natural join t4));
y	z	b	c	a	c	b
11	4	1	10	2	3	1
11	4	1	3	2	3	1
select * from  ((t1 natural join t2),  (t3 natural join t4)) natural join t5;
y	b	c	a	c	b	z
11	1	10	2	3	1	4
11	1	3	2	3	1	4
select * from t5 natural join ((t1 natural join t2) cross join (t3 natural join t4));
y	z	b	c	a	c	b
11	4	1	10	2	3	1
11	4	1	3	2	3	1
select * from  ((t1 natural join t2) cross join (t3 natural join t4)) natural join t5;
y	b	c	a	c	b	z
11	1	10	2	3	1	4
11	1	3	2	3	1	4
select * from (t1 join t2 using (b)) join (t3 join t4 using (c)) using (c);
c	b	a	b	y
3	1	2	1	11
3	1	2	1	2
select * from (t1 join t2 using (b)) natural join (t3 join t4 using (c));
b	c	a	y
1	3	2	11
1	3	2	2
select a,b,c from (t1 natural join t2) natural join (t3 natural join t4)
where b + 1 = y or b + 10 = y group by b,c,a having min(b) < max(y) order by a;
a	b	c
2	1	3
select * from (t1 natural join t2) natural left join (t3 natural join t4)
where b + 1 = y or b + 10 = y group by b,c,a,y having min(b) < max(y) order by a, y;
b	c	a	y
1	3	2	2
1	3	2	11
select * from (t3 natural join t4) natural right join (t1 natural join t2)
where b + 1 = y or b + 10 = y group by b,c,a,y having min(b) < max(y) order by a, y;
b	c	a	y
1	3	2	2
1	3	2	11
select * from t1 natural join t2 where t1.c > t2.a;
b	c	a
1	10	2
1	3	2
select * from t1 natural join t2 where t1.b > t2.b;
b	c	a
select * from t1 natural left join (t4 natural join t5) where t5.z is not NULL;
c	b	y	z
3	1	11	4
3	2	11	4
select * from t1 join (t2 join t4 on b + 1 = y) on t1.c = t4.c;
c	b	a	b	y	c
3	1	2	1	2	3
3	2	2	1	2	3
select * from (t2 join t4 on b + 1 = y) join t1 on t1.c = t4.c;
a	b	y	c	c	b
2	1	2	3	3	1
2	1	2	3	3	2
select * from t1 natural join (t2 join t4 on b + 1 = y);
c	b	a	y
3	1	2	2
select * from (t1 cross join t2) join (t3 cross join t4) on (a < y and t2.b < t3.c);
c	b	a	b	b	c	y	c
10	1	2	1	1	10	11	3
10	1	2	1	1	3	11	3
3	1	2	1	1	10	11	3
3	1	2	1	1	3	11	3
3	2	2	1	1	10	11	3
3	2	2	1	1	3	11	3
select * from (t1, t2) join (t3, t4) on (a < y and t2.b < t3.c);
c	b	a	b	b	c	y	c
10	1	2	1	1	10	11	3
10	1	2	1	1	3	11	3
3	1	2	1	1	10	11	3
3	1	2	1	1	3	11	3
3	2	2	1	1	10	11	3
3	2	2	1	1	3	11	3
select * from (t1 natural join t2) join (t3 natural join t4) on a = y;
b	c	a	c	b	y
1	10	2	3	1	2
1	3	2	3	1	2
select * from ((t3 join (t1 join t2 on c > a) on t3.b < t2.a) join t4 on y > t1.c) join t5 on z = t1.b + 3;
b	c	c	b	a	b	y	c	y	z
1	10	10	1	2	1	11	3	11	4
1	10	3	1	2	1	11	3	11	4
1	3	10	1	2	1	11	3	11	4
1	3	3	1	2	1	11	3	11	4
select * from t1 natural join t2 where t1.b > 0;
b	c	a
1	10	2
1	3	2
select * from t1 natural join (t4 natural join t5) where t4.y > 7;
c	b	y	z
3	1	11	4
3	2	11	4
select * from (t4 natural join t5) natural join t1 where t4.y > 7;
c	y	z	b
3	11	4	1
3	11	4	2
select * from t1 natural left join (t4 natural join t5) where t4.y > 7;
c	b	y	z
3	1	11	4
3	2	11	4
select * from (t4 natural join t5) natural right join t1 where t4.y > 7;
c	b	y	z
3	1	11	4
3	2	11	4
select * from (t1 natural join t2) join (t3 natural join t4) on t1.b = t3.b;
b	c	a	c	b	y
1	10	2	3	1	11
1	10	2	3	1	2
1	3	2	3	1	11
1	3	2	3	1	2
select t1.*, t2.* from t1 natural join t2;
c	b	a	b
10	1	2	1
3	1	2	1
select t1.*, t2.*, t3.*, t4.* from (t1 natural join t2) natural join (t3 natural join t4);
c	b	a	b	b	c	y	c
3	1	2	1	1	3	11	3
3	1	2	1	1	3	2	3
select * from (select * from t1 natural join t2) as t12
natural join
(select * from t3 natural join t4) as t34;
b	c	a	y
1	3	2	11
1	3	2	2
select * from (select * from t1 natural join t2) as t12
natural left join
(select * from t3 natural join t4) as t34;
b	c	a	y
1	10	2	NULL
1	3	2	11
1	3	2	2
select * from (select * from t3 natural join t4) as t34
natural right join
(select * from t1 natural join t2) as t12;
b	c	a	y
1	10	2	NULL
1	3	2	11
1	3	2	2
select * from v1a;
b	c	a
1	10	2
1	3	2
select * from v1b;
a	b	c
1	10	2
1	3	2
select * from v1c;
a	b	c
1	10	2
1	3	2
select * from v1d;
b	a	c
2	10	1
2	3	1
select * from v2a;
c	b	a
3	1	2
3	2	2
select * from v2b;
b	a	c
3	1	2
3	2	2
select * from v3a;
b	c	a
1	10	2
1	3	2
select * from v3b;
c	b	a
10	1	2
3	1	2
select * from v4;
c	b	a
3	1	2
select * from v1a natural join v2a;
b	c	a
1	3	2
select v2a.* from v1a natural join v2a;
c	b	a
3	1	2
select * from v1b join v2a on v1b.b = v2a.c;
a	b	c	c	b	a
1	3	2	3	1	2
1	3	2	3	2	2
select * from v1c join v2a on v1c.b = v2a.c;
a	b	c	c	b	a
1	3	2	3	1	2
1	3	2	3	2	2
select * from v1d join v2a on v1d.a = v2a.c;
b	a	c	c	b	a
2	3	1	3	1	2
2	3	1	3	2	2
select * from v1a join (t3 natural join t4) on a = y;
b	c	a	c	b	y
1	10	2	3	1	2
1	3	2	3	1	2
select * from t1 natural join (t3 cross join t4);
ERROR 23000: Column 'c' in from clause is ambiguous
select * from (t3 cross join t4) natural join t1;
ERROR 23000: Column 'c' in from clause is ambiguous
select * from t1 join (t2, t3) using (b);
ERROR 23000: Column 'b' in from clause is ambiguous
select * from ((t1 natural join t2), (t3 natural join t4)) natural join t6;
ERROR 23000: Column 'c' in from clause is ambiguous
select * from ((t1 natural join t2), (t3 natural join t4)) natural join t6;
ERROR 23000: Column 'c' in from clause is ambiguous
select * from t6 natural join ((t1 natural join t2),  (t3 natural join t4));
ERROR 23000: Column 'c' in from clause is ambiguous
select * from (t1 join t2 on t1.b=t2.b) natural join (t3 natural join t4);
ERROR 23000: Column 'b' in from clause is ambiguous
select * from  (t3 natural join t4) natural join (t1 join t2 on t1.b=t2.b);
ERROR 23000: Column 'b' in from clause is ambiguous
select * from (t3 join (t4 natural join t5) on (b < z))
natural join
(t1 natural join t2);
ERROR 23000: Column 'c' in from clause is ambiguous
select * from (t1 natural join t2) natural join (t3 join (t4 natural join t5) on (b < z));
ERROR 23000: Column 'c' in from clause is ambiguous
select t1.b from v1a;
ERROR 42S22: Unknown column 't1.b' in 'field list'
select * from v1a join v1b on t1.b = t2.b;
ERROR 42S22: Unknown column 't1.b' in 'on clause'
ANALYZE TABLE mysql.user;
Table	Op	Msg_type	Msg_text
mysql.user	analyze	status	OK
select 
statistics.TABLE_NAME, statistics.COLUMN_NAME, statistics.TABLE_CATALOG, statistics.TABLE_SCHEMA, statistics.NON_UNIQUE, statistics.INDEX_SCHEMA, statistics.INDEX_NAME, statistics.SEQ_IN_INDEX, statistics.COLLATION, statistics.SUB_PART, statistics.PACKED, statistics.NULLABLE, statistics.INDEX_TYPE, statistics.COMMENT, 
columns.TABLE_CATALOG, columns.TABLE_SCHEMA, columns.COLUMN_DEFAULT, columns.IS_NULLABLE, columns.DATA_TYPE, columns.CHARACTER_MAXIMUM_LENGTH, columns.CHARACTER_OCTET_LENGTH, columns.NUMERIC_PRECISION, columns.NUMERIC_SCALE, columns.CHARACTER_SET_NAME, columns.COLLATION_NAME, columns.COLUMN_TYPE, columns.COLUMN_KEY, columns.EXTRA, columns.PRIVILEGES, columns.COLUMN_COMMENT
from information_schema.statistics join information_schema.columns using(table_name,column_name) where table_name='user';
TABLE_NAME	COLUMN_NAME	TABLE_CATALOG	TABLE_SCHEMA	NON_UNIQUE	INDEX_SCHEMA	INDEX_NAME	SEQ_IN_INDEX	COLLATION	SUB_PART	PACKED	NULLABLE	INDEX_TYPE	COMMENT	TABLE_CATALOG	TABLE_SCHEMA	COLUMN_DEFAULT	IS_NULLABLE	DATA_TYPE	CHARACTER_MAXIMUM_LENGTH	CHARACTER_OCTET_LENGTH	NUMERIC_PRECISION	NUMERIC_SCALE	CHARACTER_SET_NAME	COLLATION_NAME	COLUMN_TYPE	COLUMN_KEY	EXTRA	PRIVILEGES	COLUMN_COMMENT
user	Host	def	mysql	0	mysql	PRIMARY	1	A	NULL	NULL		BTREE		def	mysql		NO	char	255	255	NULL	NULL	ascii	ascii_general_ci	char(255)	PRI		select,insert,update,references	
user	User	def	mysql	0	mysql	PRIMARY	2	A	NULL	NULL		BTREE		def	mysql		NO	char	32	96	NULL	NULL	utf8mb3	utf8mb3_bin	char(32)	PRI		select,insert,update,references	
drop table t1;
drop table t2;
drop table t3;
drop table t4;
drop table t5;
drop table t6;
drop view v1a;
drop view v1b;
drop view v1c;
drop view v1d;
drop view v2a;
drop view v2b;
drop view v3a;
drop view v3b;
drop view v4;
create table t1 (a1 int, a2 int);
create table t2 (a1 int, b int);
create table t3 (c1 int, c2 int);
create table t4 (c2 int);
insert into t1 values (1,1);
insert into t2 values (1,1);
insert into t3 values (1,1);
insert into t4 values (1);
select * from t1 join t2 using (a1) join t3 on b=c1 join t4 using (c2);
c2	a1	a2	b	c1
1	1	1	1	1
select * from t3 join (t1 join t2 using (a1)) on b=c1 join t4 using (c2);
c2	c1	a1	a2	b
1	1	1	1	1
select a2 from t1 join t2 using (a1) join t3 on b=c1 join t4 using (c2);
a2
1
select a2 from t3 join (t1 join t2 using (a1)) on b=c1 join t4 using (c2);
a2
1
select a2 from ((t1 join t2 using (a1)) join t3 on b=c1) join t4 using (c2);
a2
1
select a2 from ((t1 natural join t2) join t3 on b=c1) natural join t4;
a2
1
drop table t1,t2,t3,t4;
create table t1 (c int, b int);
create table t2 (a int, b int);
create table t3 (b int, c int);
create table t4 (y int, c int);
create table t5 (y int, z int);
insert into t1 values (3,2);
insert into t2 values (1,2);
insert into t3 values (2,3);
insert into t4 values (1,3);
insert into t5 values (1,4);
prepare stmt1 from "select * from ((t3 natural join (t1 natural join t2))
natural join t4) natural join t5";
execute stmt1;
y	c	b	a	z
1	3	2	1	4
select * from ((t3 natural join (t1 natural join t2)) natural join t4)
natural join t5;
y	c	b	a	z
1	3	2	1	4
drop table t1, t2, t3, t4, t5;
CREATE TABLE t1 (ID INTEGER, Name VARCHAR(50));
CREATE TABLE t2 (Test_ID INTEGER);
CREATE VIEW v1 (Test_ID, Description) AS SELECT ID, Name FROM t1;
CREATE TABLE tv1 SELECT Description AS Name FROM v1 JOIN t2
USING (Test_ID);
DESCRIBE tv1;
Field	Type	Null	Key	Default	Extra
Name	varchar(50)	YES		NULL	
CREATE TABLE tv2 SELECT Description AS Name FROM v1 JOIN t2
ON v1.Test_ID = t2.Test_ID;
DESCRIBE tv2;
Field	Type	Null	Key	Default	Extra
Name	varchar(50)	YES		NULL	
DROP VIEW v1;
DROP TABLE t1,t2,tv1,tv2;
create table t1 (a int, b int);
insert into t1 values 
(NULL, 1),
(NULL, 2),
(NULL, 3),
(NULL, 4);
create table t2 (a int not null, primary key(a));
insert into t2 values (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
create table t3 (a int not null, primary key(a));
insert into t3 values (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
flush status;
select * from t1, t2, t3 where t3.a=t1.a and t2.a=t1.b;
a	b	a	a
explain select * from t1, t2, t3 where t3.a=t1.a and t2.a=t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	Using where
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	Using index
1	SIMPLE	t3	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.a	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t3`.`a` AS `a` from `test`.`t1` join `test`.`t2` join `test`.`t3` where ((`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`a`))
We expect rnd_next=5, and read_key must be 0 because of short-cutting:
show status like 'Handler_read%';
Variable_name	Value
Handler_read_first	1
Handler_read_key	1
Handler_read_last	0
Handler_read_next	0
Handler_read_prev	0
Handler_read_rnd	0
Handler_read_rnd_next	5
drop table t1, t2, t3;
create table t1 (a int);
insert into t1 values (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
create table t2 (a int, b int, filler char(100), key(a), key(b));
create table t3 (a int, b int, filler char(100), key(a), key(b));
insert into t2 
select @a:= A.a + 10*(B.a + 10*C.a), @a, 'filler' from t1 A, t1 B, t1 C;
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
insert into t3 select * from t2 where a < 800;
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
explain select * from t2,t3 where t2.a < 400 and t2.b=t3.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	a,b	NULL	NULL	NULL	1000	40.00	Using where
1	SIMPLE	t3	NULL	ref	b	b	5	test.t2.b	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`filler` AS `filler`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`filler` AS `filler` from `test`.`t2` join `test`.`t3` where ((`test`.`t3`.`b` = `test`.`t2`.`b`) and (`test`.`t2`.`a` < 400))
drop table t1, t2, t3;
create table t1 (a int);
insert into t1 values (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
create table t2 (a int, b int, primary key(a));
insert into t2 select @v:=A.a+10*B.a, @v  from t1 A, t1 B;
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
explain select * from t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	#	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1`
show status like '%cost%';
Variable_name	Value
Last_query_cost	1.249000
select 'The cost of accessing t1 (dont care if it changes' '^';
The cost of accessing t1 (dont care if it changes
The cost of accessing t1 (dont care if it changes^
select 'vv: Following query must use ALL(t1), eq_ref(A), eq_ref(B): vv' Z;
Z
vv: Following query must use ALL(t1), eq_ref(A), eq_ref(B): vv
explain select * from t1, t2 a, t2 b where a.a = t1.a and b.a=a.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using where
1	SIMPLE	a	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.a	1	100.00	Using where
1	SIMPLE	b	NULL	eq_ref	PRIMARY	PRIMARY	4	test.a.b	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`a`.`a` AS `a`,`test`.`a`.`b` AS `b`,`test`.`b`.`a` AS `a`,`test`.`b`.`b` AS `b` from `test`.`t1` join `test`.`t2` `a` join `test`.`t2` `b` where ((`test`.`b`.`a` = `test`.`a`.`b`) and (`test`.`a`.`a` = `test`.`t1`.`a`))
show status like '%cost%';
Variable_name	Value
Last_query_cost	8.249000
select '^^: The above should be ~= 8 + cost(select * from t1). Value less than 8 is an error' Z;
Z
^^: The above should be ~= 8 + cost(select * from t1). Value less than 8 is an error
drop table t1, t2;
CREATE TABLE t1 (a INT PRIMARY KEY, b INT);
CREATE TABLE t2 (c INT PRIMARY KEY, d INT);
INSERT INTO t1 VALUES(1,NULL),(2,NULL),(3,NULL),(4,NULL);
INSERT INTO t1 SELECT a + 4, b FROM t1;
INSERT INTO t1 SELECT a + 8, b FROM t1;
INSERT INTO t1 SELECT a + 16, b FROM t1;
INSERT INTO t1 SELECT a + 32, b FROM t1;
INSERT INTO t1 SELECT a + 64, b FROM t1;
INSERT INTO t2 SELECT a, b FROM t1;
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN SELECT * FROM t1 JOIN t2 ON b=c ORDER BY a LIMIT 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	PRIMARY	4	NULL	2	100.00	Using where
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`c` = `test`.`t1`.`b`) order by `test`.`t1`.`a` limit 2
EXPLAIN SELECT * FROM t1 JOIN t2 ON a=c ORDER BY a LIMIT 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	PRIMARY	PRIMARY	4	NULL	2	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.a	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`c` = `test`.`t1`.`a`) order by `test`.`t1`.`a` limit 2
SELECT * FROM t1 JOIN t2 ON b=c ORDER BY a LIMIT 2;
a	b	c	d
SELECT * FROM t1 JOIN t2 ON a=c ORDER BY a LIMIT 2;
a	b	c	d
1	NULL	1	NULL
2	NULL	2	NULL
EXPLAIN SELECT * FROM t1 JOIN t2 ON b=c ORDER BY a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	PRIMARY	4	NULL	128	100.00	Using where
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`c` = `test`.`t1`.`b`) order by `test`.`t1`.`a`
EXPLAIN SELECT * FROM t1 JOIN t2 ON a=c ORDER BY a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	PRIMARY	PRIMARY	4	NULL	128	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.a	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`c` = `test`.`t1`.`a`) order by `test`.`t1`.`a`
SELECT * FROM t1 JOIN t2 ON b=c ORDER BY a;
a	b	c	d
SELECT * FROM t1 JOIN t2 ON a=c ORDER BY a;
a	b	c	d
1	NULL	1	NULL
2	NULL	2	NULL
3	NULL	3	NULL
4	NULL	4	NULL
5	NULL	5	NULL
6	NULL	6	NULL
7	NULL	7	NULL
8	NULL	8	NULL
9	NULL	9	NULL
10	NULL	10	NULL
11	NULL	11	NULL
12	NULL	12	NULL
13	NULL	13	NULL
14	NULL	14	NULL
15	NULL	15	NULL
16	NULL	16	NULL
17	NULL	17	NULL
18	NULL	18	NULL
19	NULL	19	NULL
20	NULL	20	NULL
21	NULL	21	NULL
22	NULL	22	NULL
23	NULL	23	NULL
24	NULL	24	NULL
25	NULL	25	NULL
26	NULL	26	NULL
27	NULL	27	NULL
28	NULL	28	NULL
29	NULL	29	NULL
30	NULL	30	NULL
31	NULL	31	NULL
32	NULL	32	NULL
33	NULL	33	NULL
34	NULL	34	NULL
35	NULL	35	NULL
36	NULL	36	NULL
37	NULL	37	NULL
38	NULL	38	NULL
39	NULL	39	NULL
40	NULL	40	NULL
41	NULL	41	NULL
42	NULL	42	NULL
43	NULL	43	NULL
44	NULL	44	NULL
45	NULL	45	NULL
46	NULL	46	NULL
47	NULL	47	NULL
48	NULL	48	NULL
49	NULL	49	NULL
50	NULL	50	NULL
51	NULL	51	NULL
52	NULL	52	NULL
53	NULL	53	NULL
54	NULL	54	NULL
55	NULL	55	NULL
56	NULL	56	NULL
57	NULL	57	NULL
58	NULL	58	NULL
59	NULL	59	NULL
60	NULL	60	NULL
61	NULL	61	NULL
62	NULL	62	NULL
63	NULL	63	NULL
64	NULL	64	NULL
65	NULL	65	NULL
66	NULL	66	NULL
67	NULL	67	NULL
68	NULL	68	NULL
69	NULL	69	NULL
70	NULL	70	NULL
71	NULL	71	NULL
72	NULL	72	NULL
73	NULL	73	NULL
74	NULL	74	NULL
75	NULL	75	NULL
76	NULL	76	NULL
77	NULL	77	NULL
78	NULL	78	NULL
79	NULL	79	NULL
80	NULL	80	NULL
81	NULL	81	NULL
82	NULL	82	NULL
83	NULL	83	NULL
84	NULL	84	NULL
85	NULL	85	NULL
86	NULL	86	NULL
87	NULL	87	NULL
88	NULL	88	NULL
89	NULL	89	NULL
90	NULL	90	NULL
91	NULL	91	NULL
92	NULL	92	NULL
93	NULL	93	NULL
94	NULL	94	NULL
95	NULL	95	NULL
96	NULL	96	NULL
97	NULL	97	NULL
98	NULL	98	NULL
99	NULL	99	NULL
100	NULL	100	NULL
101	NULL	101	NULL
102	NULL	102	NULL
103	NULL	103	NULL
104	NULL	104	NULL
105	NULL	105	NULL
106	NULL	106	NULL
107	NULL	107	NULL
108	NULL	108	NULL
109	NULL	109	NULL
110	NULL	110	NULL
111	NULL	111	NULL
112	NULL	112	NULL
113	NULL	113	NULL
114	NULL	114	NULL
115	NULL	115	NULL
116	NULL	116	NULL
117	NULL	117	NULL
118	NULL	118	NULL
119	NULL	119	NULL
120	NULL	120	NULL
121	NULL	121	NULL
122	NULL	122	NULL
123	NULL	123	NULL
124	NULL	124	NULL
125	NULL	125	NULL
126	NULL	126	NULL
127	NULL	127	NULL
128	NULL	128	NULL
DROP TABLE IF EXISTS t1,t2;
#
# Bug #42116: Mysql crash on specific query
#
CREATE TABLE t1 (a INT);
CREATE TABLE t2 (a INT);
CREATE TABLE t3 (a INT, INDEX (a));
CREATE TABLE t4 (a INT);
CREATE TABLE t5 (a INT);
CREATE TABLE t6 (a INT);
INSERT INTO t1 VALUES (1), (1), (1);
INSERT INTO t2 VALUES
(2), (2), (2), (2), (2), (2), (2), (2), (2), (2);
INSERT INTO t3 VALUES
(3), (3), (3), (3), (3), (3), (3), (3), (3), (3);
ANALYZE TABLE t1, t2, t3, t4, t5, t6;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
test.t4	analyze	status	OK
test.t5	analyze	status	OK
test.t6	analyze	status	OK
EXPLAIN
SELECT * 
FROM 
t1 JOIN t2 ON t1.a = t2.a 
LEFT JOIN 
(
(
t3 LEFT JOIN t4 ON t3.a = t4.a
) 
LEFT JOIN 
(
t5 LEFT JOIN t6 ON t5.a = t6.a
) 
ON t4.a = t5.a
) 
ON t1.a = t3.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	10.00	Using where; Using join buffer (hash join)
1	SIMPLE	t3	NULL	ref	a	a	5	test.t1.a	10	100.00	Using where; Using index
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
1	SIMPLE	t5	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
1	SIMPLE	t6	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t2`.`a` AS `a`,`test`.`t3`.`a` AS `a`,`test`.`t4`.`a` AS `a`,`test`.`t5`.`a` AS `a`,`test`.`t6`.`a` AS `a` from `test`.`t1` join `test`.`t2` left join (`test`.`t3` left join `test`.`t4` on((`test`.`t4`.`a` = `test`.`t1`.`a`)) left join (`test`.`t5` left join `test`.`t6` on((`test`.`t6`.`a` = `test`.`t4`.`a`))) on((`test`.`t5`.`a` = `test`.`t4`.`a`))) on(((`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t3`.`a` = `test`.`t1`.`a`))) where (`test`.`t2`.`a` = `test`.`t1`.`a`)
SELECT * 
FROM 
t1 JOIN t2 ON t1.a = t2.a 
LEFT JOIN 
(
(
t3 LEFT JOIN t4 ON t3.a = t4.a
) 
LEFT JOIN 
(
t5 LEFT JOIN t6 ON t5.a = t6.a
) 
ON t4.a = t5.a
) 
ON t1.a = t3.a;
a	a	a	a	a	a
DROP TABLE t1,t2,t3,t4,t5,t6;
#
# Bug#48483: crash in get_best_combination()
#
CREATE TABLE t1(f1 INT);
INSERT INTO t1 VALUES (1),(2);
CREATE VIEW v1 AS SELECT 1 FROM t1 LEFT JOIN t1 AS t2 on 1=1;
EXPLAIN
SELECT 1 FROM v1 right join v1 AS v2 ON RAND();
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using where; Using join buffer (hash join)
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using join buffer (hash join)
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` left join `test`.`t1` `t2` on(true) left join (`test`.`t1` left join `test`.`t1` `t2` on(true)) on((0 <> rand())) where true
DROP VIEW v1;
DROP TABLE t1;
#
# Bug#52177 crash with explain, row comparison, join, text field
#
CREATE TABLE t1 (a TINYINT, b TEXT, KEY (a));
INSERT INTO t1 VALUES (0,''),(0,'');
FLUSH TABLES;
EXPLAIN SELECT 1 FROM t1 LEFT JOIN t1 a ON 1
WHERE ROW(t1.a, 1111.11) = ROW(1111.11, 1111.11) AND
ROW(t1.b, 1111.11) <=> ROW('','');
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` left join `test`.`t1` `a` on(true) where false
DROP TABLE t1;
#
# Bug #50335: Assertion `!(order->used & map)' in eq_ref_table
# 
CREATE TABLE t1 (a INT NOT NULL, b INT NOT NULL, PRIMARY KEY (a,b));
INSERT INTO t1 VALUES (0,0), (1,1);
SELECT * FROM t1 STRAIGHT_JOIN t1 t2 ON t1.a=t2.a AND t1.a=t2.b ORDER BY t2.a, t1.a;
a	b	a	b
0	0	0	0
1	1	1	1
DROP TABLE t1;
End of 5.0 tests.
CREATE TABLE t1 (f1 int);
CREATE TABLE t2 (f1 int);
INSERT INTO t2  VALUES (1);
CREATE VIEW v1 AS SELECT * FROM t2;
PREPARE stmt FROM 'UPDATE t2 AS A NATURAL JOIN v1 B SET B.f1 = 1';
EXECUTE stmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
DROP VIEW v1;
DROP TABLE t1, t2;
CREATE TABLE t1(a CHAR(9),b INT,KEY(b),KEY(a)) ENGINE=MYISAM;
CREATE TABLE t2(a CHAR(9),b INT,KEY(b),KEY(a)) ENGINE=MYISAM;
INSERT INTO t1 VALUES ('1',null),(null,null);
INSERT INTO t2 VALUES ('1',null),(null,null);
CREATE TABLE mm1(a CHAR(9),b INT,KEY(b),KEY(a))
ENGINE=MERGE  UNION=(t1,t2);
SELECT t1.a FROM mm1,t1;
a
1
1
1
1
NULL
NULL
NULL
NULL
DROP TABLE t1, t2, mm1;
#
# Bug #54468: crash after item's print() function when ordering/grouping 
#             by subquery
#
CREATE TABLE t1(a INT, b INT);
INSERT INTO t1 VALUES (), ();
SELECT 1 FROM t1
GROUP BY
GREATEST(t1.a,
(SELECT 1 FROM
(SELECT t1.b FROM t1,t1 t2
ORDER BY t1.a, t1.a LIMIT 1) AS d)
);
1
1
DROP TABLE t1;
#
# Bug #53544: Server hangs during JOIN query in stored procedure called
#             twice in a row
#
CREATE TABLE t1(c INT);
INSERT INTO t1 VALUES (1), (2);
PREPARE stmt FROM "SELECT t2.c AS f1 FROM t1 LEFT JOIN
                                        t1 t2 ON t1.c=t2.c RIGHT JOIN
                                        t1 t3 ON t1.c=t3.c 
                   GROUP BY f1;";
EXECUTE stmt;
f1
1
2
EXECUTE stmt;
f1
1
2
DEALLOCATE PREPARE stmt;
DROP TABLE t1;
End of 5.1 tests
#
# Bug #59696 Optimizer fails to move WHERE condition on JOIN column 
#            when joining with a view
#
CREATE TABLE t1 (
c1 INTEGER NOT NULL
);
INSERT INTO t1 VALUES (1),(2),(3);
CREATE TABLE t2 (
pk INTEGER NOT NULL,
c1 INTEGER NOT NULL,
PRIMARY KEY (pk)
);
INSERT INTO t2 VALUES (1,4),(3,5),(2,6);
ANALYZE  TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN SELECT t2.pk, t2.c1 FROM t2, t1 
WHERE t2.pk = t1.c1 AND t2.pk >= 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.c1	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c1` AS `c1` from `test`.`t2` join `test`.`t1` where ((`test`.`t2`.`pk` = `test`.`t1`.`c1`) and (`test`.`t1`.`c1` >= 2))
SELECT t2.pk, t2.c1 FROM t2, t1 
WHERE t2.pk = t1.c1 AND t2.pk >= 2;
pk	c1
2	6
3	5
CREATE VIEW v_t2 AS SELECT * FROM t2;
EXPLAIN SELECT v_t2.pk, v_t2.c1 FROM v_t2, t1 
WHERE v_t2.pk = t1.c1 AND v_t2.pk >= 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.c1	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`pk` AS `pk`,`test`.`t2`.`c1` AS `c1` from `test`.`t2` join `test`.`t1` where ((`test`.`t2`.`pk` = `test`.`t1`.`c1`) and (`test`.`t2`.`pk` >= 2))
SELECT v_t2.pk, v_t2.c1 FROM v_t2, t1 
WHERE v_t2.pk = t1.c1 AND v_t2.pk >= 2;
pk	c1
2	6
3	5
DROP VIEW v_t2;
DROP TABLE t1, t2;
#
# Bug 13102033 - CRASH IN COPY_FUNCS IN SQL_SELECT.CC ON JOIN + 
#                GROUP BY + ORDER BY
#
CREATE TABLE t1 (  
pk INTEGER NOT NULL,
i1 INTEGER NOT NULL,
i2 INTEGER NOT NULL,
PRIMARY KEY (pk)
);
INSERT INTO t1 VALUES (7,8,1), (8,2,2);
CREATE VIEW v1 AS SELECT * FROM t1;
EXPLAIN SELECT t1.pk
FROM v1, t1
WHERE v1.i2 = 211
AND v1.i2 > 7
OR t1.i1 < 3
GROUP BY t1.pk
ORDER BY v1.i2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using temporary; Using filesort
1	SIMPLE	t1	NULL	ALL	PRIMARY	NULL	NULL	NULL	2	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk` from `test`.`t1` join `test`.`t1` where (((`test`.`t1`.`i2` = 211) and (`test`.`t1`.`i2` > 7)) or (`test`.`t1`.`i1` < 3)) group by `test`.`t1`.`pk` order by `test`.`t1`.`i2`
SELECT t1.pk
FROM v1, t1
WHERE v1.i2 = 211
AND v1.i2 > 7
OR t1.i1 < 3
GROUP BY t1.pk
ORDER BY v1.i2;
pk
8
EXPLAIN SELECT t1.pk
FROM v1, t1
WHERE (v1.i2 = 211 AND v1.i2 > 7)
OR (t1.i1 < 3 AND v1.i2 < 10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	62.50	Using where
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk` from `test`.`t1` join `test`.`t1` where (((`test`.`t1`.`i2` = 211) and (`test`.`t1`.`i2` > 7)) or ((`test`.`t1`.`i1` < 3) and (`test`.`t1`.`i2` < 10)))
SELECT t1.pk
FROM v1, t1
WHERE (v1.i2 = 211 AND v1.i2 > 7)
OR (t1.i1 < 3 AND v1.i2 < 10);
pk
8
8
DROP VIEW v1;
DROP TABLE t1;
#
# BUG#11752239 - 43368: STRAIGHT_JOIN DOESN'T WORK FOR NESTED JOINS 
#
create table t1(c1 int primary key, c2 char(10)) engine=myisam;
create table t2(c1 int primary key, c2 char(10), ref_t1 int) engine=myisam;
create table t3(c1 int primary key, c2 char(10), ref_t1 int) engine=myisam;
create table t4(c1 int primary key, c2 char(10), ref_t1 int) engine=myisam;
insert into t1 values(1,'a');
insert into t2 values(1,'a', 1);
insert into t3 values(1,'a', 1);
insert into t3 values(2,'b',2);
insert into t4 values(1,'a', 1);
insert into t4 values(2,'a', 2);
insert into t4 values(3,'a', 3);
insert into t4 values(4,'a', 4);
insert into t1 values(2,'b');
insert into t1 values(3,'c');
EXPLAIN SELECT * FROM t4 JOIN (t1 join t3 on t3.ref_t1 =
t1.c1 join t2 on t2.ref_t1 = t1.c1) on t4.ref_t1 = t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
1	SIMPLE	t1	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	NULL
1	SIMPLE	t3	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	4	25.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t4`.`c1` AS `c1`,`test`.`t4`.`c2` AS `c2`,`test`.`t4`.`ref_t1` AS `ref_t1`,'1' AS `c1`,'a' AS `c2`,`test`.`t3`.`c1` AS `c1`,`test`.`t3`.`c2` AS `c2`,`test`.`t3`.`ref_t1` AS `ref_t1`,'1' AS `c1`,'a' AS `c2`,'1' AS `ref_t1` from `test`.`t4` join `test`.`t1` join `test`.`t3` where ((`test`.`t3`.`ref_t1` = '1') and (`test`.`t4`.`ref_t1` = '1'))
EXPLAIN SELECT STRAIGHT_JOIN * FROM t4 JOIN (t1 join t3 on t3.ref_t1 =
t1.c1 join t2 on t2.ref_t1 = t1.c1) on t4.ref_t1 = t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	Using where
1	SIMPLE	t1	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t4.ref_t1	1	100.00	NULL
1	SIMPLE	t3	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where; Using join buffer (hash join)
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t4`.`c1` AS `c1`,`test`.`t4`.`c2` AS `c2`,`test`.`t4`.`ref_t1` AS `ref_t1`,`test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t3`.`c1` AS `c1`,`test`.`t3`.`c2` AS `c2`,`test`.`t3`.`ref_t1` AS `ref_t1`,`test`.`t2`.`c1` AS `c1`,`test`.`t2`.`c2` AS `c2`,`test`.`t2`.`ref_t1` AS `ref_t1` from `test`.`t4` join `test`.`t1` join `test`.`t3` join `test`.`t2` where ((`test`.`t1`.`c1` = `test`.`t4`.`ref_t1`) and (`test`.`t3`.`ref_t1` = `test`.`t4`.`ref_t1`) and (`test`.`t2`.`ref_t1` = `test`.`t4`.`ref_t1`))
EXPLAIN SELECT * FROM t4 STRAIGHT_JOIN (t1 join t3 on t3.ref_t1 =
t1.c1 join t2 on t2.ref_t1 = t1.c1) on t4.ref_t1 = t1.c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t4	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	Using where
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where; Using join buffer (hash join)
1	SIMPLE	t3	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where; Using join buffer (hash join)
1	SIMPLE	t1	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t4.ref_t1	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t4`.`c1` AS `c1`,`test`.`t4`.`c2` AS `c2`,`test`.`t4`.`ref_t1` AS `ref_t1`,`test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t3`.`c1` AS `c1`,`test`.`t3`.`c2` AS `c2`,`test`.`t3`.`ref_t1` AS `ref_t1`,`test`.`t2`.`c1` AS `c1`,`test`.`t2`.`c2` AS `c2`,`test`.`t2`.`ref_t1` AS `ref_t1` from `test`.`t4` join `test`.`t1` join `test`.`t3` join `test`.`t2` where ((`test`.`t2`.`ref_t1` = `test`.`t4`.`ref_t1`) and (`test`.`t3`.`ref_t1` = `test`.`t4`.`ref_t1`) and (`test`.`t1`.`c1` = `test`.`t4`.`ref_t1`))
drop table t1,t2,t3,t4;
# Bug#20455184: Assertion failed: join_cond in optimizer.cc
CREATE TABLE t1(a INTEGER) engine=innodb;
explain SELECT 1
FROM (SELECT 1 FROM t1 WHERE a) AS q
NATURAL LEFT JOIN t1
NATURAL LEFT JOIN t1 AS t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where; Using join buffer (hash join)
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` left join `test`.`t1` on(true) left join `test`.`t1` `t2` on((`test`.`t2`.`a` = `test`.`t1`.`a`)) where (0 <> `test`.`t1`.`a`)
SELECT 1
FROM (SELECT 1 FROM t1 WHERE a) AS q
NATURAL LEFT JOIN t1
NATURAL LEFT JOIN t1 AS t2;
1
explain SELECT 1
FROM t1
NATURAL RIGHT JOIN t1 AS t2
NATURAL RIGHT JOIN (SELECT 1 FROM t1 WHERE a) AS q;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where; Using join buffer (hash join)
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` left join (`test`.`t1` `t2` left join `test`.`t1` on((`test`.`t1`.`a` = `test`.`t2`.`a`))) on(true) where (0 <> `test`.`t1`.`a`)
SELECT 1
FROM t1
NATURAL RIGHT JOIN t1 AS t2
NATURAL RIGHT JOIN (SELECT 1 FROM t1 WHERE a) AS q;
1
DROP TABLE t1;
# Bug#21045724: Assertion '!table || !table->read_set ...
CREATE TABLE t1
(pk INTEGER,
dummy VARCHAR(64),
col_check TINYINT,
PRIMARY KEY(pk)
) engine=innodb;
INSERT INTO t1 VALUES (13, '13', 13);
CREATE VIEW v1 AS
SELECT *
FROM t1
WHERE pk BETWEEN 13 AND 14;
PREPARE st1 FROM "
UPDATE v1 AS a NATURAL JOIN v1 AS b
SET a.dummy = '', b.col_check = NULL ";
EXECUTE st1;
EXECUTE st1;
DEALLOCATE PREPARE st1;
DROP VIEW v1;
DROP TABLE t1;
# Bug#25424289: Assert '!table->has_null_row()' failed in join_read_key
CREATE TABLE t1
(pk INT,
col_int_key INT DEFAULT NULL,
col_varchar_10_latin1_key VARCHAR(10) DEFAULT NULL,
col_varchar_255_utf8 VARCHAR(255) CHARACTER SET utf8mb3 DEFAULT NULL,
col_int INT DEFAULT NULL,
col_datetime_key datetime DEFAULT NULL,
col_varchar_255_latin1_key VARCHAR(255) DEFAULT NULL,
col_date_key date DEFAULT NULL,
col_datetime datetime DEFAULT NULL,
PRIMARY KEY (pk),
KEY col_date_key (col_date_key)
) charset latin1 ENGINE=MyISAM;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES
(8,9,'h','FDUMQ',-1356726272,'2007-09-02 05:48:23','she','2002-04-02','2002-12-20 17:54:07');
CREATE TABLE t2
(pk INT,
col_int INT DEFAULT NULL,
col_int_key INT DEFAULT NULL,
PRIMARY KEY (pk)
) charset latin1 ENGINE=MyISAM;
CREATE TABLE t3
(col_int INT DEFAULT NULL,
col_int_key INT DEFAULT NULL,
pk INT,
PRIMARY KEY (pk),
KEY test_idx (col_int_key,pk,col_int)
) charset latin1 ENGINE=InnoDB;
INSERT INTO t3 VALUES
(NULL,9, 41), (NULL,-1596719104, 48), (-1068105728,9, 49);
CREATE TABLE t4
(col_varchar_255_latin1_key VARCHAR(255) DEFAULT NULL,
pk INT,
PRIMARY KEY (pk)
) charset latin1 ENGINE=MyISAM;
INSERT INTO t4 VALUES ('RUXDY',8);
ANALYZE TABLE t1, t2, t3, t4;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	Table is already up to date
test.t3	analyze	status	OK
test.t4	analyze	status	OK
explain SELECT alias1.pk AS field5
FROM t3 AS alias1
LEFT JOIN t4 AS alias2
LEFT JOIN t4 AS alias3
LEFT JOIN t1 AS alias4
LEFT JOIN t2 AS alias5
ON alias4.pk = alias5.col_int_key
ON alias3.pk = alias5.col_int
ON alias2.col_varchar_255_latin1_key = alias4.col_varchar_10_latin1_key
RIGHT JOIN t1 AS alias6
ON alias4.pk = alias6.pk
RIGHT JOIN t4 AS alias7
ON alias6.pk = alias7.pk
ON alias1.col_int_key = alias6.col_int_key
WHERE alias1.col_int > 5 OR
alias5.col_int > 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	alias1	NULL	index	NULL	test_idx	14	NULL	3	100.00	Using index
1	SIMPLE	alias7	NULL	index	PRIMARY	PRIMARY	4	NULL	1	100.00	Using index
1	SIMPLE	alias6	NULL	eq_ref	PRIMARY	PRIMARY	4	test.alias7.pk	1	100.00	Using where
1	SIMPLE	alias4	NULL	eq_ref	PRIMARY	PRIMARY	4	test.alias6.pk	1	100.00	Using index condition
1	SIMPLE	alias5	NULL	ALL	NULL	NULL	NULL	NULL	0	0.00	Using where
1	SIMPLE	alias2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
1	SIMPLE	alias3	NULL	eq_ref	PRIMARY	PRIMARY	4	test.alias5.col_int	1	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`alias1`.`pk` AS `field5` from `test`.`t3` `alias1` left join (`test`.`t4` `alias7` join `test`.`t1` `alias6` left join (`test`.`t4` `alias2` join `test`.`t4` `alias3` join `test`.`t1` `alias4` join `test`.`t2` `alias5`) on(((`test`.`alias3`.`pk` = `test`.`alias5`.`col_int`) and (`test`.`alias4`.`pk` = `test`.`alias7`.`pk`) and (`test`.`alias5`.`col_int_key` = `test`.`alias7`.`pk`) and (`test`.`alias2`.`col_varchar_255_latin1_key` = `test`.`alias4`.`col_varchar_10_latin1_key`)))) on(((`test`.`alias6`.`pk` = `test`.`alias7`.`pk`) and (`test`.`alias6`.`col_int_key` = `test`.`alias1`.`col_int_key`))) where ((`test`.`alias1`.`col_int` > 5) or (`test`.`alias5`.`col_int` > 5))
SELECT alias1.pk AS field5
FROM t3 AS alias1
LEFT JOIN t4 AS alias2
LEFT JOIN t4 AS alias3
LEFT JOIN t1 AS alias4
LEFT JOIN t2 AS alias5
ON alias4.pk = alias5.col_int_key
ON alias3.pk = alias5.col_int
ON alias2.col_varchar_255_latin1_key = alias4.col_varchar_10_latin1_key
RIGHT JOIN t1 AS alias6
ON alias4.pk = alias6.pk
RIGHT JOIN t4 AS alias7
ON alias6.pk = alias7.pk
ON alias1.col_int_key = alias6.col_int_key
WHERE alias1.col_int > 5 OR
alias5.col_int > 5;
field5
DROP TABLE t1, t2, t3, t4;
# Bug#28727717: Duplicated 'is not null' predicates
create table parent(a int primary key, b int, c int, d int) engine=innodb;
create table eq_child(a int, b int, c int, d int, primary key(a,b)) engine=innodb;
insert into parent values (1,1,1,1);
insert into eq_child select * from parent;
explain format=json select straight_join count(*) from parent
join eq_child as c1 on c1.a = parent.b and c1.b = parent.b
join eq_child as c2 on c2.a = parent.b and c2.b = parent.b
join eq_child as c3 on c3.a = parent.b and c3.b = parent.b
join eq_child as c4 on c4.a = parent.b and c4.b = parent.b
;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.75"
    },
    "nested_loop": [
      {
        "table": {
          "table_name": "parent",
          "access_type": "ALL",
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 1,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.10",
            "prefix_cost": "0.35",
            "data_read_per_join": "24"
          },
          "used_columns": [
            "b"
          ],
          "attached_condition": "(`test`.`parent`.`b` is not null)"
        }
      },
      {
        "table": {
          "table_name": "c1",
          "access_type": "eq_ref",
          "possible_keys": [
            "PRIMARY"
          ],
          "key": "PRIMARY",
          "used_key_parts": [
            "a",
            "b"
          ],
          "key_length": "8",
          "ref": [
            "test.parent.b",
            "test.parent.b"
          ],
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 1,
          "filtered": "100.00",
          "using_index": true,
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.10",
            "prefix_cost": "0.70",
            "data_read_per_join": "24"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      },
      {
        "table": {
          "table_name": "c2",
          "access_type": "eq_ref",
          "possible_keys": [
            "PRIMARY"
          ],
          "key": "PRIMARY",
          "used_key_parts": [
            "a",
            "b"
          ],
          "key_length": "8",
          "ref": [
            "test.parent.b",
            "test.parent.b"
          ],
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 1,
          "filtered": "100.00",
          "using_index": true,
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.10",
            "prefix_cost": "1.05",
            "data_read_per_join": "24"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      },
      {
        "table": {
          "table_name": "c3",
          "access_type": "eq_ref",
          "possible_keys": [
            "PRIMARY"
          ],
          "key": "PRIMARY",
          "used_key_parts": [
            "a",
            "b"
          ],
          "key_length": "8",
          "ref": [
            "test.parent.b",
            "test.parent.b"
          ],
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 1,
          "filtered": "100.00",
          "using_index": true,
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.10",
            "prefix_cost": "1.40",
            "data_read_per_join": "24"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      },
      {
        "table": {
          "table_name": "c4",
          "access_type": "eq_ref",
          "possible_keys": [
            "PRIMARY"
          ],
          "key": "PRIMARY",
          "used_key_parts": [
            "a",
            "b"
          ],
          "key_length": "8",
          "ref": [
            "test.parent.b",
            "test.parent.b"
          ],
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 1,
          "filtered": "100.00",
          "using_index": true,
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.10",
            "prefix_cost": "1.75",
            "data_read_per_join": "24"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    ]
  }
}
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`parent` join `test`.`eq_child` `c1` join `test`.`eq_child` `c2` join `test`.`eq_child` `c3` join `test`.`eq_child` `c4` where ((`test`.`c1`.`a` = `test`.`parent`.`b`) and (`test`.`c1`.`b` = `test`.`parent`.`b`) and (`test`.`c2`.`a` = `test`.`parent`.`b`) and (`test`.`c2`.`b` = `test`.`parent`.`b`) and (`test`.`c3`.`a` = `test`.`parent`.`b`) and (`test`.`c3`.`b` = `test`.`parent`.`b`) and (`test`.`c4`.`a` = `test`.`parent`.`b`) and (`test`.`c4`.`b` = `test`.`parent`.`b`))
drop table parent, eq_child;
#
# Bug #29954680: BACKWARDS REF SCAN DOES NOT PERFORM LATE NULLS FILTERING
#
CREATE TABLE t (i INTEGER PRIMARY KEY AUTO_INCREMENT, j INTEGER, KEY(j)) ENGINE=InnoDB;
INSERT INTO t VALUES (NULL, NULL);
INSERT INTO t SELECT NULL, NULL FROM t;
INSERT INTO t SELECT NULL, NULL FROM t;
INSERT INTO t SELECT NULL, NULL FROM t;
INSERT INTO t SELECT NULL, NULL FROM t;
INSERT INTO t SELECT NULL, NULL FROM t;
INSERT INTO t SELECT NULL, NULL FROM t;
INSERT INTO t SELECT NULL, NULL FROM t;
INSERT INTO t SELECT NULL, NULL FROM t;
INSERT INTO t SELECT NULL, NULL FROM t;
INSERT INTO t SELECT NULL, NULL FROM t;
INSERT INTO t SELECT NULL, NULL FROM t;
INSERT INTO t SELECT NULL, NULL FROM t;
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=tree SELECT t1.i AS a, (SELECT t2.i FROM t t2 WHERE t1.j = t2.j ORDER BY j DESC, i DESC LIMIT 1) AS b FROM t t1;
EXPLAIN
-> Covering index scan on t1 using j  (rows=4096)
-> Select #2 (subquery in projection; dependent)
    -> Limit: 1 row(s)  (rows=1)
        -> Covering index lookup on t2 using j (j = t1.j) (reverse)  (rows=4096)

Warnings:
Note	1276	Field or reference 'test.t1.j' of SELECT #2 was resolved in SELECT #1
FLUSH STATUS;
SELECT t1.i AS a, (SELECT t2.i FROM t t2 WHERE t1.j = t2.j ORDER BY j DESC, i DESC LIMIT 1) AS b FROM t t1;
SHOW STATUS LIKE 'Handler_read_key';
Variable_name	Value
Handler_read_key	1
DROP TABLE t;
set global default_storage_engine=innodb;
set session default_storage_engine=innodb;
#
# Bug#30350696 REFERENCE FROM DERIVED TABLE JOIN CONDITION TO PRECEDING TABLE COLUMN FAILS
# Bug#11748138 ERROR 1054 (42S22): UNKNOWN COLUMN 'T2.I2' IN 'ON CLAUSE'
#
create table t(a int, b int);
insert into t values(1,10),(2,20);
set optimizer_switch="block_nested_loop=off";
explain format=tree select t1.*, (select count(*)
from t t2
left join t t3 on t3.a>t2.a-t1.a)
from t t1;
EXPLAIN
-> Table scan on t1  (rows=2)
-> Select #2 (subquery in projection; dependent)
    -> Aggregate: count(0)  (rows=1)
        -> Nested loop left join  (rows=4)
            -> Table scan on t2  (rows=2)
            -> Filter: (t3.a > (t2.a - t1.a))  (rows=2)
                -> Table scan on t3  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
select t1.*, (select count(*)
from t t2
left join t t3 on t3.a>t2.a-t1.a)
from t t1;
a	b	(select count(*)
from t t2
left join t t3 on t3.a>t2.a-t1.a)
1	10	3
2	20	4
explain format=tree select t1.a, (select count(*)
from t t2
left join t t3 on t3.a>t2.a-t1.a)
from t t1
group by t1.a;
EXPLAIN
-> Table scan on <temporary>  (rows=2)
    -> Temporary table with deduplication  (rows=2)
        -> Table scan on t1  (rows=2)
        -> Select #2 (subquery in projection; dependent)
            -> Aggregate: count(0)  (rows=1)
                -> Nested loop left join  (rows=4)
                    -> Table scan on t2  (rows=2)
                    -> Filter: (t3.a > (t2.a - t1.a))  (rows=2)
                        -> Table scan on t3  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
select t1.a, (select count(*)
from t t2
left join t t3 on t3.a>t2.a-t1.a)
from t t1
group by t1.a;
a	(select count(*)
from t t2
left join t t3 on t3.a>t2.a-t1.a)
1	3
2	4
create view v1 as select * from t;
explain format=tree select v1.*, (select count(*)
from t t2
left join t t3 on t3.a>t2.a-v1.a)
from v1;
EXPLAIN
-> Table scan on t  (rows=2)
-> Select #2 (subquery in projection; dependent)
    -> Aggregate: count(0)  (rows=1)
        -> Nested loop left join  (rows=4)
            -> Table scan on t2  (rows=2)
            -> Filter: (t3.a > (t2.a - t.a))  (rows=2)
                -> Table scan on t3  (rows=2)

Warnings:
Note	1276	Field or reference 'v1.a' of SELECT #2 was resolved in SELECT #1
select v1.*, (select count(*)
from t t2
left join t t3 on t3.a>t2.a-v1.a)
from v1;
a	b	(select count(*)
from t t2
left join t t3 on t3.a>t2.a-v1.a)
1	10	3
2	20	4
drop view v1;
explain format=tree select t1.*, (select * from
(select count(*)
from t t2
left join t t3 on t3.a>t2.a-t1.a) as dt)
from t t1;
EXPLAIN
-> Table scan on t1  (rows=2)
-> Select #2 (subquery in projection; dependent)
    -> Table scan on dt  (rows=1)
        -> Materialize  (rows=1)
            -> Aggregate: count(0)  (rows=1)
                -> Nested loop left join  (rows=4)
                    -> Table scan on t2  (rows=2)
                    -> Filter: (t3.a > (t2.a - t1.a))  (rows=2)
                        -> Table scan on t3  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #3 was resolved in SELECT #1
select t1.*, (select * from
(select count(*)
from t t2
left join t t3 on t3.a>t2.a-t1.a) as dt)
from t t1;
a	b	(select * from
(select count(*)
from t t2
left join t t3 on t3.a>t2.a-t1.a) as dt)
1	10	3
2	20	4
explain format=tree select t1.*, dt.c
from t t1,
(select count(*) as c
from t t2
left join t t3 on t3.a>t2.a-t1.a) as dt;
ERROR 42S22: Unknown column 't1.a' in 'on clause'
explain format=tree select t1.*, dt.c
from t t1,
lateral
(select count(*) as c
from t t2
left join t t3 on t3.a>t2.a-t1.a) as dt;
EXPLAIN
-> Nested loop inner join  (rows=2)
    -> Invalidate materialized tables (row from t1)  (rows=2)
        -> Table scan on t1  (rows=2)
    -> Table scan on dt  (rows=1)
        -> Materialize (invalidate on row from t1)  (rows=1)
            -> Aggregate: count(0)  (rows=1)
                -> Nested loop left join  (rows=4)
                    -> Table scan on t2  (rows=2)
                    -> Filter: (t3.a > (t2.a - t1.a))  (rows=2)
                        -> Table scan on t3  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
select t1.*, dt.c
from t t1,
lateral
(select count(*) as c
from t t2
left join t t3 on t3.a>t2.a-t1.a) as dt;
a	b	c
1	10	3
2	20	4
explain format=tree select t1.*, dt.c
from t t1 cross join
lateral
(select count(*) as c
from t t2
left join t t3 on t3.a>t2.a-t1.a) as dt;
EXPLAIN
-> Nested loop inner join  (rows=2)
    -> Invalidate materialized tables (row from t1)  (rows=2)
        -> Table scan on t1  (rows=2)
    -> Table scan on dt  (rows=1)
        -> Materialize (invalidate on row from t1)  (rows=1)
            -> Aggregate: count(0)  (rows=1)
                -> Nested loop left join  (rows=4)
                    -> Table scan on t2  (rows=2)
                    -> Filter: (t3.a > (t2.a - t1.a))  (rows=2)
                        -> Table scan on t3  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
select t1.*, dt.c
from t t1 cross join
lateral
(select count(*) as c
from t t2
left join t t3 on t3.a>t2.a-t1.a) as dt;
a	b	c
1	10	3
2	20	4
explain format=tree select t1.*, dt.c
from t t1 join
lateral
(select count(*) as c
from t t2
left join t t3 on t3.a>t2.a-t1.a) as dt
on true;
EXPLAIN
-> Nested loop inner join  (rows=2)
    -> Invalidate materialized tables (row from t1)  (rows=2)
        -> Table scan on t1  (rows=2)
    -> Table scan on dt  (rows=1)
        -> Materialize (invalidate on row from t1)  (rows=1)
            -> Aggregate: count(0)  (rows=1)
                -> Nested loop left join  (rows=4)
                    -> Table scan on t2  (rows=2)
                    -> Filter: (t3.a > (t2.a - t1.a))  (rows=2)
                        -> Table scan on t3  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
select t1.*, dt.c
from t t1 join
lateral
(select count(*) as c
from t t2
left join t t3 on t3.a>t2.a-t1.a) as dt
on true;
a	b	c
1	10	3
2	20	4
select t1.* from t t0 cross join (t t1 join t t2 on 100=t0.a);
ERROR 42S22: Unknown column 't0.a' in 'on clause'
select t1.* from t t0 cross join (t t1 join t t2
on 100=(select count(*)
from t t3
left join t t4
on t4.a>t3.a-t0.a));
ERROR 42S22: Unknown column 't0.a' in 'on clause'
select t1.* from t t0 cross join t t1 join t t2
on 100=(select count(*)
from t t3
left join t t4
on t4.a>t3.a-t0.a);
a	b
set optimizer_switch=default;
drop table t;
create table t(a int);
insert into t values(1),(2);
set optimizer_switch="firstmatch=off,block_nested_loop=off";
explain format=tree select * from t t1
where t1.a in (select dt.a
from t t2
left join (select * from t t3 where t3.a>t1.a) dt
on true);
EXPLAIN
-> Nested loop inner join  (rows=4)
    -> Filter: (t1.a > t1.a)  (rows=2)
        -> Table scan on t1  (rows=2)
    -> Limit: 1 row(s)  (rows=2)
        -> Nested loop inner join  (rows=2)
            -> Filter: (t3.a = t1.a)  (rows=1)
                -> Table scan on t3  (rows=2)
            -> Table scan on t2  (rows=2)

Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #3 was resolved in SELECT #1
select * from t t1
where t1.a in (select dt.a
from t t2
left join (select * from t t3 where t3.a>t1.a) dt
on true);
a
drop table t;
set optimizer_switch=default;
set global default_storage_engine=innodb;
#
# WL#14071: ASSERTION `!(USED_TABS & (~READ_TABLES & ~FILTER_FOR_TABLE))' FAILED
#
CREATE TABLE t1 (
a INTEGER
);
CREATE TABLE t2 (
pk INTEGER NOT NULL,
a INTEGER
);
SELECT * FROM
(
t2 LEFT JOIN t2 AS t3 ON t2.pk IS NULL
) LEFT JOIN t1 ON t1.a = t3.a;
pk	a	pk	a	a
DROP TABLE t1, t2;
#
# Bug #31924828: WL#14071: HYPERGAPH RETURNS DIFFERENT RESULTS WITH ONLY_FULL_GROUP_BY DISABLED
#
CREATE TABLE t1 ( pk INTEGER );
INSERT INTO t1 VALUES (1);
CREATE TABLE t2 ( pk INTEGER );
SELECT * FROM t1 LEFT JOIN t2 ON TRUE WHERE t2.pk <=> 3;
pk	pk
DROP TABLE t1, t2;
#
# Bug #31947917: WL#14071: ASAN LEAKS SEEN DURING SHUTDOWN
#
CREATE TABLE A ( pk INTEGER );
SELECT t1.pk FROM A t1, A t2, A t3, A t4, A t5 GROUP BY t1.pk;
pk
DROP TABLE A;
#
# Bug #32234666: HASHJOINITERATOR::READROWFROMPROBEITERATOR(): ASSERTION `!THD()->IS_ERROR()' FAILED.
#
CREATE TABLE t (
a BIT(50),
b VARCHAR(22772) character set ucs2,
c INTEGER,
d TINYBLOB,
PRIMARY KEY (a),
KEY i0001 (c,d(163))
);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t VALUES (1,'',1,'1');
INSERT INTO t VALUES (0x03ffffffffffff,'2',0,'2');
SELECT t2.b FROM t AS t1
LEFT JOIN t AS t2 ON t2.c < 70
WHERE t2.a - from_unixtime(0) > 0;
ERROR 22003: BIGINT UNSIGNED value is out of range in '(`test`.`t2`.`a` - <cache>(from_unixtime(0)))'
DROP TABLE t;
#
# Bug #32424884: MRR OR BATCHED KEY ACCESS WITH JOINS AND TIMESTAMPS PRODUCE WRONG RESULTS
#
CREATE TABLE t1 (
ts datetime,
x integer,
y integer
);
INSERT INTO t1 VALUES ('2020-11-20 10:38:31',1,2);
CREATE TABLE t2 (
x integer,
ts datetime,
KEY i1 (x)
);
INSERT INTO t2 VALUES (1,'2020-11-16 14:18:55');
CREATE TABLE t3 (
ts datetime,
y integer,
z INTEGER,
KEY i2 (y,ts)
);
INSERT INTO t3 VALUES ('2020-12-29 18:23:02',2,100);
CREATE TABLE t4 (
z INTEGER
);
SET optimizer_switch='mrr=on,batched_key_access=on,mrr_cost_based=off';
SELECT COUNT(*)
FROM
t1
LEFT JOIN t2 ON t1.x = t2.x AND t2.ts <= t1.ts
JOIN t3 ON t1.y = t3.y AND t3.ts <= t1.ts
LEFT JOIN t4 ON t3.z = t4.z;
COUNT(*)
0
SET optimizer_switch=DEFAULT;
DROP TABLE t1, t2, t3, t4;
#
# Bug #32623894: RESULT MISMATCH SEEN WITH HYPERGRAPH OPTIMIZER
#
CREATE TABLE t1 ( a INTEGER );
INSERT INTO t1 VALUES (0), (0), (0), (1), (1), (0), (1), (0);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT * FROM
t1, t1 AS t2, t1 AS t3
WHERE ((t1.a = 5 AND t3.a < t1.a) OR t1.a > 0) AND t3.a <= t2.a;
a	a	a
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	0	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	0
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
DROP TABLE t1;
#
# Bug #32637680: RESULT MISMATCH SEEN WITH HYPERGRAPH OPTIMIZER IN QUERIES WITH LEFT JOIN
#
CREATE TABLE t1 ( a VARCHAR(10) );
CREATE TABLE t2 ( a VARCHAR(10) );
INSERT INTO t1 VALUES ('x');
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
SELECT *
FROM t1
LEFT JOIN t2 ON TRUE
LEFT JOIN ( t1 AS t1i JOIN t2 AS t2i ON t1i.a = t2i.a ) ON t2.a = t2i.a
WHERE t1i.a = '' OR t2i.a = '';
a	a	a	a
DROP TABLE t1, t2;
#
# Bug #32599784: WL#14325: ASSERTION `FD_SET == IT_AND_INSERTED.FIRST->SECOND.ACTIVE_FUNCTIONAL_D
#
CREATE TABLE t1 (
a INTEGER NOT NULL,
KEY (a)
);
INSERT INTO t1 VALUES (1), (2);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT *
FROM t1 LEFT JOIN t1 AS t2 ON TRUE
WHERE t1.a IS NULL OR t1.a = t2.a
ORDER BY t2.a;
a	a
1	1
2	2
SELECT t1.a
FROM t1 LEFT JOIN t1 AS t2 ON t1.a = t2.a
WHERE t1.a IS NULL OR t1.a = t2.a
GROUP BY t1.a;
a
1
2
DROP TABLE t1;
#
# Bug #32663072: WL#14418: ASSERTION `PATH->TYPE == ACCESSPATH::FILTER' FAILED|JOIN_OPTIMIZER.CC
#
CREATE TABLE t1 (c1 INTEGER, c2 INTEGER, c3 INTEGER, c4 INTEGER, c5 INTEGER, c6 INTEGER, c7 INTEGER, c8 INTEGER, c9 INTEGER, c10 INTEGER, c11 INTEGER, c12 INTEGER, c13 INTEGER, c14 INTEGER, c15 INTEGER, c16 INTEGER, c17 INTEGER, c18 INTEGER, c19 INTEGER, c20 INTEGER, c21 INTEGER, c22 INTEGER, c23 INTEGER, c24 INTEGER, c25 INTEGER, c26 INTEGER, c27 INTEGER, c28 INTEGER, c29 INTEGER, c30 INTEGER, c31 INTEGER, c32 INTEGER, c33 INTEGER, c34 INTEGER, c35 INTEGER, c36 INTEGER, c37 INTEGER, c38 INTEGER, c39 INTEGER, c40 INTEGER, c41 INTEGER, c42 INTEGER, c43 INTEGER, c44 INTEGER, c45 INTEGER, c46 INTEGER, c47 INTEGER, c48 INTEGER, c49 INTEGER, c50 INTEGER, c51 INTEGER, c52 INTEGER, c53 INTEGER, c54 INTEGER, c55 INTEGER, c56 INTEGER, c57 INTEGER, c58 INTEGER, c59 INTEGER, c60 INTEGER, c61 INTEGER);
SELECT *
FROM t1
NATURAL JOIN (
SELECT t2.*
FROM t1 AS t2 JOIN t1 AS t3 ON t3.c1 = t2.c1
WHERE t2.c3 <> ANY (SELECT c3 FROM t1)
) AS d1;
c1	c2	c3	c4	c5	c6	c7	c8	c9	c10	c11	c12	c13	c14	c15	c16	c17	c18	c19	c20	c21	c22	c23	c24	c25	c26	c27	c28	c29	c30	c31	c32	c33	c34	c35	c36	c37	c38	c39	c40	c41	c42	c43	c44	c45	c46	c47	c48	c49	c50	c51	c52	c53	c54	c55	c56	c57	c58	c59	c60	c61
DROP TABLE t1;
#
# Bug #32672275: WL#14418: ASSERTION `ISSINGLEBITSET(PRED.TOTAL_ELIGIBILITY_SET)' FAILED.
#
CREATE TABLE t1 ( a INTEGER );
SELECT *
FROM t1 AS outer_t1
WHERE EXISTS (
SELECT t2.a
FROM t1
LEFT JOIN t1 AS t2 ON t2.a = outer_t1.a
);
a
DROP TABLE t1;
#
# Bug #32643386: WL#14418: ASSERTION `FD_SET == IT_AND_INSERTED.FIRST->SECOND.ACTIVE_FUNCTIONAL_D
#
CREATE TABLE t1 ( a INTEGER );
SELECT *
FROM (t1 STRAIGHT_JOIN t1 AS t2) JOIN t1 AS t3 ON t1.a = t3.a
WHERE t2.a = t3.a
ORDER BY t1.a;
a	a	a
DROP TABLE t1;
CREATE TABLE t1 (a INTEGER, b INTEGER);
SELECT *
FROM (t1 STRAIGHT_JOIN t1 AS t2 ON t1.b = t2.b) JOIN t1 AS t3 ON t1.a = t3.b
WHERE t1.a < t2.b AND t2.a < t3.b
ORDER BY t1.b;
a	b	a	b	a	b
DROP TABLE t1;
#
# Bug #32642893: WL#14418: ASSERTION `!ISMULTIPLEEQUALS(ITEM)' FAILED|MAKE_JOIN_HYPERGRAPH.CC
#
CREATE TABLE t1 ( a INTEGER );
SELECT *
FROM t1
LEFT JOIN t1 AS t2 ON t1.a = t2.a
LEFT JOIN t1 AS t3 ON t2.a = t3.a
WHERE t2.a = t3.a OR t2.a <> t2.a;
a	a	a
DROP TABLE t1;
#
# Bug #32707348: WL#14418: RESULT MISMATCHES SEEN WITH HYPERGRAPH
#
CREATE TABLE t1 ( a INTEGER, b INTEGER );
INSERT INTO t1 VALUES (1,2);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT * FROM t1, t1 AS t2, t1 AS t3 WHERE t1.a = t2.a AND t1.a = t3.b AND t1.a = t3.a;
a	b	a	b	a	b
DROP TABLE t1;
#
# Bug #32751315: WL#14418: ASSERTION `RIGHT != NULLPTR' FAILED|MAKE_JOIN_HYPERGRAPH.CC
#
CREATE TABLE t1 ( pk INTEGER );
SELECT * FROM
t1
JOIN t1 AS t2 ON t1.pk = t2.pk
JOIN t1 AS t3
LEFT JOIN t1 AS t4 ON t2.pk = t3.pk AND t4.pk = 135;
pk	pk	pk	pk
DROP TABLE t1;
#
# Bug #32757178: WL#14418: ASSERTION `!EQ_ITEMS.IS_EMPTY()' FAILED|MAKE_JOIN_HYPERGRAPH.CC
#
CREATE TABLE t1 ( a INTEGER, b INTEGER );
SELECT *
FROM
t1
JOIN t1 AS t2 ON t1.a = t2.b
LEFT JOIN t1 AS t3 ON t2.b = t3.b
LEFT JOIN (
t1 AS t4
JOIN t1 AS t5 ON t4.b = t5.b
) ON t2.b = t4.a
WHERE t1.b >= 6 OR t3.b <> t4.b;
a	b	a	b	a	b	a	b	a	b
DROP TABLE t1;
# Bug#32508878 - MYSQL SERVER CRASH - ASSERTION FAILURE IN
# DEPS_OF_REMAINING_LATERAL_DERIVED_TABLES::RECALCULATE
#
CREATE TABLE t1(a INT NOT NULL, b INT, PRIMARY KEY(a));
INSERT INTO t1 VALUES (1,1), (2,2), (3,3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT * FROM t1 AS x3 WHERE EXISTS
(SELECT * FROM t1 AS x1 JOIN t1 AS x2 ON x1.b=x2.b
JOIN LATERAL (SELECT COUNT(a) AS c FROM t1
WHERE t1.b=x1.b) AS d3 ON x1.b=c);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	x3	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
1	PRIMARY	x1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where; Rematerialize (<derived3>)
1	PRIMARY	x2	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
1	PRIMARY	<derived3>	NULL	ref	<auto_key0>	<auto_key0>	8	test.x1.b	2	100.00	Using where; Using index; FirstMatch(x3)
3	DEPENDENT DERIVED	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1276	Field or reference 'test.x1.b' of SELECT #3 was resolved in SELECT #2
Note	1003	/* select#1 */ select `test`.`x3`.`a` AS `a`,`test`.`x3`.`b` AS `b` from `test`.`t1` `x3` semi join (`test`.`t1` `x1` join `test`.`t1` `x2` join lateral (/* select#3 */ select count(`test`.`t1`.`a`) AS `c` from `test`.`t1` where (`test`.`t1`.`b` = `test`.`x1`.`b`)) `d3`) where ((`test`.`x2`.`b` = `test`.`x1`.`b`) and (`test`.`x1`.`b` = `d3`.`c`))
DROP TABLE t1;
#
# Bug #32917482: SIG11 IN FLATTENINNERJOINS|MAKE_JOIN_HYPERGRAPH.CC
#
CREATE TABLE t1 (
a VARCHAR(1),
b INTEGER
);
SELECT *
FROM
t1
JOIN t1 AS t2 ON t1.b = t2.b
JOIN t1 AS t3
JOIN (
t1 AS t4 STRAIGHT_JOIN t1 AS t5 ON t4.a = t5.b
) ON t1.b = t4.b
WHERE t1.a = t5.b;
a	b	a	b	a	b	a	b	a	b
DROP TABLE t1;
#
# Bug #32939191: HYPERGRAPH: DIFFERENT RESULTS WHEN HYPERGRAPH_OPTIMIZER IS ON/OFF
#
CREATE TABLE t1 (a INTEGER);
INSERT INTO t1 VALUES (1), (2), (3), (4);
SELECT *
FROM
t1
LEFT JOIN (
t1 AS t2
LEFT JOIN t1 AS t3 ON FALSE
) ON t1.a <=> t3.a
WHERE t1.a <=> t3.a;
a	a	a
DROP TABLE t1;
# Bug#32915307- RECENT REGRESSION: ASSERTION
# `JOIN->DEPS_OF_REMAINING_LATERAL_DERIVED_TABLES
# == CALCULATE_LATERAL_DEPS_OF_FINAL_PLAN(FIRST_TAB_NO)' FAILED.
#
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES(1);
CREATE TABLE t2(x INT);
INSERT INTO t2 VALUES(2),(3);
EXPLAIN SELECT 6 FROM t1 WHERE EXISTS
(SELECT 7 FROM (SELECT a) d1 WHERE EXISTS (SELECT 8 FROM t2));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Rematerialize (<derived3>)
1	PRIMARY	<derived3>	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	FirstMatch(t1)
3	DEPENDENT DERIVED	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #3 was resolved in SELECT #1
Note	1003	/* select#1 */ select 6 AS `6` from `test`.`t1` semi join (lateral (/* select#3 */ select `test`.`t1`.`a` AS `a`) `d1` join `test`.`t2`) where true
DROP TABLE t1,t2;
# Bug#32065606: Inconsistent results when running the same query
CREATE TABLE t1(
k INTEGER PRIMARY KEY,
t2ref INTEGER
);
INSERT INTO t1 VALUES(1,1), (2,NULL), (3,3);
CREATE TABLE t2(
k INTEGER PRIMARY KEY,
d INTEGER,
t3ref INTEGER NOT NULL
);
INSERT INTO t2 VALUES(1,11,1), (3,33,3);
CREATE TABLE t3(
k INTEGER UNIQUE,
e INTEGER
);
INSERT INTO t3 VALUES(1,111), (3,333);
SET optimizer_switch='block_nested_loop=off';
SELECT t1.k, dt.d, t3.e
FROM t1
LEFT JOIN (SELECT t2.*
FROM t2
) AS dt
ON t1.t2ref = dt.k
LEFT JOIN t3
ON dt.t3ref = t3.k
;
k	d	e
1	11	111
2	NULL	NULL
3	33	333
explain SELECT t1.k, dt.d, t3.e
FROM t1
LEFT JOIN (SELECT t2.*
FROM t2
) AS dt
ON t1.t2ref = dt.k
LEFT JOIN t3
ON dt.t3ref = t3.k
;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.t2ref	1	100.00	NULL
1	SIMPLE	t3	NULL	eq_ref	k	k	5	test.t2.t3ref	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`k` AS `k`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`e` AS `e` from `test`.`t1` left join (`test`.`t2`) on((`test`.`t2`.`k` = `test`.`t1`.`t2ref`)) left join `test`.`t3` on((`test`.`t3`.`k` = `test`.`t2`.`t3ref`)) where true
DROP TABLE t1, t2, t3;
#
# Bug #32976194: HYPERGRAPH: ASSERTION `!OVERLAPS(EXPR->LEFT->TABLES_IN_SUBTREE, EXPR->RIGHT->TAB
#
CREATE TABLE t1 ( a INTEGER );
SELECT *
FROM t1, t1 AS t2, t1 AS t3
WHERE rand() > 0.5 OR (t1.a = 1 AND t2.a = 1);
a	a	a
DROP TABLE t1;
#
# Bug #33003335: HYPERGRAPH: ASSERTION `FD_SET == IT_AND_INSERTED.FIRST->SECOND.ACTIVE_FUNCTIONAL
#
CREATE TABLE t1 ( a INTEGER, b INTEGER );
SELECT * FROM
t1
STRAIGHT_JOIN t1 AS t2 ON t1.b = t2.b
JOIN (t1 AS t3 JOIN t1 AS t4 ON t3.a = t4.a AND t3.b = t4.b) ON t2.b = t3.b AND t2.a < t3.a
ORDER BY t2.b;
a	b	a	b	a	b	a	b
DROP TABLE t1;
#
# Bug #33003581: HYPERGRAPH: ASSERTION `FALSE' FAILED|SQL/ITEM_CMPFUNC.CC
#
CREATE TABLE t1 ( i INTEGER );
INSERT INTO t1 VALUES (1), (2);
SELECT * FROM
t1 LEFT JOIN (SELECT i FROM t1 WHERE FALSE) AS d1 ON t1.i = d1.i
WHERE NOT EXISTS (SELECT 1 FROM t1 AS inner_t1 WHERE i = d1.i);
i	i
1	NULL
2	NULL
DROP TABLE t1;
CREATE TABLE t1 ( a INTEGER, b INTEGER, c INTEGER, d INTEGER, e INTEGER, f INTEGER, g INTEGER, h INTEGER, i INTEGER, j INTEGER, k INTEGER, l INTEGER, m INTEGER, n INTEGER, o INTEGER, p INTEGER, q INTEGER, r INTEGER, s INTEGER, t INTEGER, u INTEGER, v INTEGER, w INTEGER, KEY (a) );
SELECT 1
FROM t1 NATURAL JOIN t1 AS t2 NATURAL JOIN t1 AS t3
WHERE
t1.a <> ( SELECT SUM(a) FROM t1 );
1
DROP TABLE t1;
#
# Bug #32366946: USE-AFTER-FREE WITH HYPERGRAPH OPTIMIZER + SORT + REMATERIALIZATION
#
CREATE TABLE t1 (
f1 INTEGER
);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (
pk INTEGER,
blobfield LONGTEXT
);
INSERT INTO t2 VALUES (4, '');
SELECT *
FROM t1, t2, LATERAL (
SELECT pk, blobfield
GROUP BY pk, blobfield WITH ROLLUP
) AS d1
ORDER BY t1.f1, t2.pk;
f1	pk	blobfield	pk	blobfield
1	4		4	
1	4		4	NULL
1	4		NULL	NULL
2	4		4	
2	4		4	NULL
2	4		NULL	NULL
3	4		4	
3	4		4	NULL
3	4		NULL	NULL
DROP TABLE t1, t2;
#
# Bug #33148480: HG-OPTIMIZER: HIT ASSERT IN GETNODEMAPFROMTABLEMAP() WHILE MAKEJOINHYPERGRAPH
#
CREATE TABLE t ( a INTEGER, b INTEGER );
SELECT *
FROM
t AS t1
JOIN (
t AS t2
LEFT JOIN t AS t3 ON t2.a = t3.a AND t2.b = t3.b
JOIN t AS t4 ON t2.b = t4.a AND t2.b = t4.b
) ON t1.b = t2.b;
a	b	a	b	a	b	a	b
DROP TABLE t;
#
# Bug #33264928: Multi-equality is not fully applied by hypergraph optimizer with cycles
#
CREATE TABLE t1 ( a INTEGER, b INTEGER );
INSERT INTO t1 VALUES (3, 2);
CREATE TABLE t2 ( a INTEGER, b INTEGER, KEY (a) );
INSERT INTO t2 VALUES (3, 0);
INSERT INTO t2 VALUES (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0), (4, 0);
CREATE TABLE t3 ( a INTEGER, b INTEGER, c INTEGER );
INSERT INTO t3 VALUES (1, 0, 2);
ANALYZE TABLE t1,t2,t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
SELECT
t2.b, t3.a, t3.b
FROM t1
JOIN t2 ON t1.a = t2.a
JOIN t3 ON t1.b = t3.c AND t2.b = t3.a
WHERE t2.b = t3.b;
b	a	b
DROP TABLE t1, t2, t3;
#
# Bug #33330728: HYPERGRAPH: ASSERTION `RIGHT_PATH->TYPE == ACCESSPATH::ZERO_ROWS' FAILED
#
CREATE TABLE t1 ( a INTEGER );
SELECT
1
FROM
t1
JOIN t1 AS t2
LEFT JOIN (
t1 AS t3
JOIN ( SELECT * FROM t1 WHERE FALSE ) AS empty_subq
) ON empty_subq.a = t3.a
WHERE NOT EXISTS (SELECT 9) OR empty_subq.a = t2.a;
1
DROP TABLE t1;
#
# Bug #33383388: ASSERT: `!IsBitSet(it->second, left_path->applied_sargable_join_predicates())'
#
CREATE TABLE t1 ( a INTEGER, b INTEGER, KEY (b) );
INSERT INTO t1 VALUES (1,1), (2,2);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT 1
FROM
t1
JOIN (t1 AS t2 LEFT JOIN t1 AS t3 ON t2.b = t3.b) ON (t2.b = t3.b OR t1.a = 125)
WHERE
t1.a = 143;
1
DROP TABLE t1;
#
# Bug #32950670: Potentially wrong results
#
CREATE TABLE t1 (i SMALLINT, INDEX a (i));
CREATE TABLE t2 (i SMALLINT UNSIGNED);
INSERT INTO t1 VALUES (-32768), (32767);
INSERT INTO t2 VALUES (65535);
SELECT * FROM t1 JOIN t2 ON t2.i > t1.i;
i	i
-32768	65535
32767	65535
DROP TABLE t1, t2;
#
# Bug #33506809: Duplicate edges in the hypergraph
#
CREATE TABLE t1 ( a INTEGER );
CREATE TABLE t2 ( a INTEGER, b INTEGER, c INTEGER );
CREATE TABLE t3 ( a INTEGER );
CREATE TABLE t4 ( a INTEGER, b INTEGER, c INTEGER );
SELECT
1
FROM
t1
JOIN t2 ON t1.a = t2.a
JOIN t3 ON t2.a = t3.a
JOIN t4 ON t3.a = t4.a AND t2.b = t4.b AND t2.c = t4.c;
1
DROP TABLE t1,t2,t3,t4;
#
# Bug #33544652: WL#14806 fd_set == it_and_inserted.first->second.active_functional_dependencies
#
CREATE TABLE t1 (
pk INTEGER NOT NULL,
a INTEGER,
INDEX (pk)
);
SELECT
1
FROM
t1
LEFT JOIN t1 AS t2 ON t1.a = t2.a
JOIN t1 AS t3 ON t2.a = t3.a
WHERE
t2.a < t2.a OR t2.pk IS NULL
GROUP BY t2.a;
1
DROP TABLE t1;
#
# Bug #33603493: Multiple equalities get lost in hypergraph join optimizer pushdown
#
CREATE TABLE t1 ( a INTEGER );
INSERT INTO t1 VALUES (5);
CREATE TABLE t2 ( a INTEGER, b INTEGER );
INSERT INTO t2 VALUES (6, 1);
CREATE TABLE t3 ( a INTEGER, b INTEGER );
INSERT INTO t3 VALUES (2, 1);
SELECT t1.a AS field1, t2.a AS field2
FROM
t1
JOIN t2 ON t1.a = t2.a
WHERE t2.b IN (
SELECT inner_a1.b
FROM t3 AS inner_a1 JOIN t3 AS inner_a2 ON inner_a1.b = inner_a2.b
WHERE inner_a1.a = t2.a
);
field1	field2
DROP TABLE t1, t2, t3;
#
# Bug#33611740: Hypergraph optimizer loses conditions in pushdown
#
CREATE TABLE t1 (a INTEGER);
INSERT INTO t1 VALUES (1);
CREATE TABLE t2 (a INTEGER);
INSERT INTO t2 VALUES (1);
CREATE TABLE t3 (a INTEGER);
INSERT INTO t3 VALUES (1);
CREATE TABLE t4 (a INTEGER);
INSERT INTO t4 VALUES (2);
SELECT * FROM t1 LEFT JOIN (t2 LEFT JOIN (t3 JOIN t4 ON t3.a = t4.a)
ON t4.a = t2.a) ON t1.a = t2.a;
a	a	a	a
1	1	NULL	NULL
DROP TABLE t1, t2, t3, t4;
# Bug#34327798: Assertion `!thd->is_error()' failed in 8.0.29
CREATE TABLE t1 (k INT, KEY(k)) ENGINE=MyISAM;
INSERT INTO t1 VALUES (1);
SELECT ra1.k
FROM t1 AS ra1 LEFT JOIN t1 AS ra2
ON CAST(SHA(ra2.k >> 'fixme') AS JSON) = ra2.k
LEFT JOIN
t1 AS ra3 LEFT JOIN t1 AS ra4
ON ra3.k = ra4.k
ON ra3.k = ra3.k
WHERE ra3.k <= ra1.k;
ERROR 22032: Invalid JSON text in argument 1 to function cast_as_json: "The document root must not be followed by other values." at position 3.
DROP TABLE t1;
#
# Bug#35049440: Mysqld assert in Item_typecast_signed::val_int
#
CREATE TABLE t1 (pk INTEGER AUTO_INCREMENT, col_int INT, col_date DATE,
PRIMARY KEY (pk));
INSERT INTO t1 (pk, col_int, col_date) VALUES (1, NULL, '1997-04-21');
CREATE TABLE t2(pk INTEGER AUTO_INCREMENT, PRIMARY KEY (pk));
INSERT INTO t2 (pk) VALUES (1);
SELECT DISTINCT t1.col_int
FROM t2 LEFT OUTER JOIN t1 ON t2.pk = t1.col_int
WHERE CAST(t2.pk AS SIGNED INTEGER) <> t1.col_date;
col_int
SELECT DISTINCT t1.col_int
FROM t2 LEFT OUTER JOIN t1 ON t1.col_int = t2.pk
WHERE CAST(t2.pk AS SIGNED INTEGER) <> t1.col_date;
col_int
DROP TABLE t1;
DROP TABLE t2;
