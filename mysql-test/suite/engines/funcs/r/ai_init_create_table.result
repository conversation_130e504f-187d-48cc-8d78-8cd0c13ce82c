DROP TABLE IF EXISTS t1;
CREATE TABLE t1(c1 TINYINT   AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT   AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT   AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT   AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER   AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT   AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT   AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT   AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT   AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT   AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER   AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT   AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT   AUTO_INCREMENT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
NULL
NULL
NULL
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT   AUTO_INCREMENT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
NULL
NULL
NULL
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT   AUTO_INCREMENT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
NULL
NULL
NULL
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT   AUTO_INCREMENT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
NULL
NULL
NULL
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER   AUTO_INCREMENT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
NULL
NULL
NULL
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT   AUTO_INCREMENT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
NULL
NULL
NULL
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT   AUTO_INCREMENT NOT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT   AUTO_INCREMENT NOT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT   AUTO_INCREMENT NOT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT   AUTO_INCREMENT NOT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER   AUTO_INCREMENT NOT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT   AUTO_INCREMENT NOT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT   AUTO_INCREMENT NOT NULL PRIMARY KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT   AUTO_INCREMENT NOT NULL PRIMARY KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT   AUTO_INCREMENT NOT NULL PRIMARY KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT   AUTO_INCREMENT NOT NULL PRIMARY KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER   AUTO_INCREMENT NOT NULL PRIMARY KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT   AUTO_INCREMENT NOT NULL PRIMARY KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT UNSIGNED AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT UNSIGNED AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT UNSIGNED AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT UNSIGNED AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER UNSIGNED AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT UNSIGNED AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT UNSIGNED AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT UNSIGNED AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT UNSIGNED AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT UNSIGNED AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER UNSIGNED AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT UNSIGNED AUTO_INCREMENT NOT NULL KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT UNSIGNED AUTO_INCREMENT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint unsigned AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
NULL
NULL
NULL
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT UNSIGNED AUTO_INCREMENT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint unsigned AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
NULL
NULL
NULL
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT UNSIGNED AUTO_INCREMENT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint unsigned AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
NULL
NULL
NULL
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT UNSIGNED AUTO_INCREMENT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int unsigned AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
NULL
NULL
NULL
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER UNSIGNED AUTO_INCREMENT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int unsigned AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
NULL
NULL
NULL
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT UNSIGNED AUTO_INCREMENT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint unsigned AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
NULL
NULL
NULL
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT UNSIGNED AUTO_INCREMENT NOT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint unsigned NOT NULL AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT UNSIGNED AUTO_INCREMENT NOT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint unsigned NOT NULL AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT UNSIGNED AUTO_INCREMENT NOT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint unsigned NOT NULL AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT UNSIGNED AUTO_INCREMENT NOT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int unsigned NOT NULL AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER UNSIGNED AUTO_INCREMENT NOT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int unsigned NOT NULL AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT UNSIGNED AUTO_INCREMENT NOT NULL UNIQUE KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint unsigned NOT NULL AUTO_INCREMENT,
  UNIQUE KEY `c1` (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 TINYINT UNSIGNED AUTO_INCREMENT NOT NULL PRIMARY KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` tinyint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 SMALLINT UNSIGNED AUTO_INCREMENT NOT NULL PRIMARY KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` smallint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 MEDIUMINT UNSIGNED AUTO_INCREMENT NOT NULL PRIMARY KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` mediumint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INT UNSIGNED AUTO_INCREMENT NOT NULL PRIMARY KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 INTEGER UNSIGNED AUTO_INCREMENT NOT NULL PRIMARY KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` int unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BIGINT UNSIGNED AUTO_INCREMENT NOT NULL PRIMARY KEY ) AUTO_INCREMENT=10;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
INSERT INTO t1 VALUES(null);
SELECT * FROM t1;
c1
10
11
12
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
