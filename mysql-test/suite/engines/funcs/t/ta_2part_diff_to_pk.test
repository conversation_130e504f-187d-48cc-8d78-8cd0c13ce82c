--source include/no_valgrind_without_big.inc

--disable_warnings
let $ENGINE=`select variable_value from performance_schema.global_variables where variable_name='DEFAULT_STORAGE_ENGINE'`;
--enable_warnings

--disable_warnings
DROP TABLE IF EXISTS t1;
--enable_warnings
SET sql_mode='NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1(c1 BIT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 BIT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 TINYINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 SMALLINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 MEDIUMINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 INT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 INTEGER NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 BIGINT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 REAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 DOUBLE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 FLOAT NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 DECIMAL NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 NUMERIC NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 DATE NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 TIME NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 TIMESTAMP NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 YEAR NOT NULL, c3 INT NULL, c4 VARCHAR(10) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD PRIMARY KEY (c1,c2);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
SET sql_mode=default;
