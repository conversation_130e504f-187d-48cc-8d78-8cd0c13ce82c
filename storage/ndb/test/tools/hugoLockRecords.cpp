/*
   Copyright (c) 2003, 2025, Oracle and/or its affiliates.

   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.

   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.

   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
*/

#include <ndb_global.h>

#include <NdbOut.hpp>

#include <NdbSleep.h>
#include <getarg.h>
#include <NDBT.hpp>
#include <NdbApi.hpp>

#include <HugoTransactions.hpp>

int main(int argc, const char **argv) {
  ndb_init();

  int _records = 0;
  int _loops = 1;
  int _percentVal = 1;
  int _lockTime = 1000;
  const char *_tabname = NULL;
  const char *_dbname = "TEST_DB";
  int _help = 0;

  struct getargs args[] = {
      {"loops", 'l', arg_integer, &_loops,
       "number of times to run this program(0=infinite loop)", "loops"},
      {"records", 'r', arg_integer, &_records, "Number of records", "recs"},
      {"database", 'd', arg_string, &_dbname, "Name of database", "dbname"},
      {"locktime", 't', arg_integer, &_lockTime,
       "Time in ms to hold lock(default=1000)", "ms"},
      {"percent", 'p', arg_integer, &_percentVal,
       "Percent of records to lock(default=1%)", "%"},
      {"usage", '?', arg_flag, &_help, "Print help", ""}};
  int num_args = sizeof(args) / sizeof(args[0]);
  int optind = 0;
  char desc[] =
      "tabname\n"
      "This program will lock p% of the records in the table for x "
      "milliseconds\n"
      "then it will lock the next 1% and continue to do so until it has locked "
      "\n"
      "all records in the table\n";

  if (getarg(args, num_args, argc, argv, &optind) || argv[optind] == NULL ||
      _records == 0 || _help) {
    arg_printusage(args, num_args, argv[0], desc);
    return NDBT_ProgramExit(NDBT_WRONGARGS);
  }
  _tabname = argv[optind];

  // Connect to Ndb
  Ndb_cluster_connection con;
  con.configure_tls(opt_tls_search_path, opt_mgm_tls);
  if (con.connect(12, 5, 1) != 0) {
    return NDBT_ProgramExit(NDBT_FAILED);
  }
  Ndb MyNdb(&con, _dbname);

  if (MyNdb.init() != 0) {
    NDB_ERR(MyNdb.getNdbError());
    return NDBT_ProgramExit(NDBT_FAILED);
  }

  while (MyNdb.waitUntilReady() != 0)
    ndbout << "Waiting for ndb to become ready..." << endl;

  // Check if table exists in db
  const NdbDictionary::Table *pTab =
      NDBT_Table::discoverTableFromDb(&MyNdb, _tabname);
  if (pTab == NULL) {
    ndbout << " Table " << _tabname << " does not exist!" << endl;
    return NDBT_ProgramExit(NDBT_WRONGARGS);
  }

  HugoTransactions hugoTrans(*pTab);
  int i = 0;
  while (i < _loops || _loops == 0) {
    ndbout << i << ": ";
    if (hugoTrans.lockRecords(&MyNdb, _records, _percentVal, _lockTime) != 0) {
      return NDBT_ProgramExit(NDBT_FAILED);
    }
    i++;
  }

  return NDBT_ProgramExit(NDBT_OK);
}
