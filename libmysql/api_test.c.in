/* Copyright (c) 2013, 2025, Oracle and/or its affiliates.

   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.

   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.

   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */

/**
  @file

  Application for testing if all C API functions to be exported by the client 
  library (as declared by CLIENT_API_FUNCTIONS in libmysql/CMakeLists.txt) are
  declared by <mysql.h> header and indeed exported.

  This code should fail to compile if some API function is not declared in
  <mysql.h>. Assuming dynamic linking, it should fail to run if some of the
  functions is not exported by the shared library.

  Note: this source is generated during build configuration process from
  template in libmysql/api_test.c.in. Do not edit this file - edit the template
  instead.
*/

#include <mysql.h>
#include <stdio.h>

#include "my_compiler.h"

/*
  An array of pointers to C API calls to test that all of them are
  declared and exported by client library.
*/
static void const* api_calls[] = { @CLIENT_API_FUNCTION_LIST@ };

int main(int argc MY_ATTRIBUTE((unused)),
         char **argv MY_ATTRIBUTE((unused)))
{
  unsigned api_count= 0;
  unsigned i;

  for (i=0; i < sizeof(api_calls)/sizeof(void*); ++i)
  {
    if (api_calls[i])
      api_count++;
  }

  printf("There are %u API functions\n", api_count);

  if (mysql_library_init(0,NULL,NULL))
  {
    printf("Failed to initialize MySQL client library\n");
    return 1;
  }
  printf("MySQL client library initialized: %s\n", mysql_get_client_info());
  mysql_library_end();
  printf("Done!\n");
  return 0;
}
