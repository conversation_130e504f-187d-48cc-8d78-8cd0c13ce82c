// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: google/protobuf/compiler/plugin.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Google.Protobuf.Compiler {

  /// <summary>Holder for reflection information generated from google/protobuf/compiler/plugin.proto</summary>
  public static partial class PluginReflection {

    #region Descriptor
    /// <summary>File descriptor for google/protobuf/compiler/plugin.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static PluginReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiVnb29nbGUvcHJvdG9idWYvY29tcGlsZXIvcGx1Z2luLnByb3RvEhhnb29n",
            "bGUucHJvdG9idWYuY29tcGlsZXIaIGdvb2dsZS9wcm90b2J1Zi9kZXNjcmlw",
            "dG9yLnByb3RvIkYKB1ZlcnNpb24SDQoFbWFqb3IYASABKAUSDQoFbWlub3IY",
            "AiABKAUSDQoFcGF0Y2gYAyABKAUSDgoGc3VmZml4GAQgASgJIoECChRDb2Rl",
            "R2VuZXJhdG9yUmVxdWVzdBIYChBmaWxlX3RvX2dlbmVyYXRlGAEgAygJEhEK",
            "CXBhcmFtZXRlchgCIAEoCRI4Cgpwcm90b19maWxlGA8gAygLMiQuZ29vZ2xl",
            "LnByb3RvYnVmLkZpbGVEZXNjcmlwdG9yUHJvdG8SRQoXc291cmNlX2ZpbGVf",
            "ZGVzY3JpcHRvcnMYESADKAsyJC5nb29nbGUucHJvdG9idWYuRmlsZURlc2Ny",
            "aXB0b3JQcm90bxI7ChBjb21waWxlcl92ZXJzaW9uGAMgASgLMiEuZ29vZ2xl",
            "LnByb3RvYnVmLmNvbXBpbGVyLlZlcnNpb24i4AIKFUNvZGVHZW5lcmF0b3JS",
            "ZXNwb25zZRINCgVlcnJvchgBIAEoCRIaChJzdXBwb3J0ZWRfZmVhdHVyZXMY",
            "AiABKAQSQgoEZmlsZRgPIAMoCzI0Lmdvb2dsZS5wcm90b2J1Zi5jb21waWxl",
            "ci5Db2RlR2VuZXJhdG9yUmVzcG9uc2UuRmlsZRp/CgRGaWxlEgwKBG5hbWUY",
            "ASABKAkSFwoPaW5zZXJ0aW9uX3BvaW50GAIgASgJEg8KB2NvbnRlbnQYDyAB",
            "KAkSPwoTZ2VuZXJhdGVkX2NvZGVfaW5mbxgQIAEoCzIiLmdvb2dsZS5wcm90",
            "b2J1Zi5HZW5lcmF0ZWRDb2RlSW5mbyJXCgdGZWF0dXJlEhAKDEZFQVRVUkVf",
            "Tk9ORRAAEhsKF0ZFQVRVUkVfUFJPVE8zX09QVElPTkFMEAESHQoZRkVBVFVS",
            "RV9TVVBQT1JUU19FRElUSU9OUxACQnIKHGNvbS5nb29nbGUucHJvdG9idWYu",
            "Y29tcGlsZXJCDFBsdWdpblByb3Rvc1opZ29vZ2xlLmdvbGFuZy5vcmcvcHJv",
            "dG9idWYvdHlwZXMvcGx1Z2lucGKqAhhHb29nbGUuUHJvdG9idWYuQ29tcGls",
            "ZXI="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Google.Protobuf.Reflection.DescriptorReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Google.Protobuf.Compiler.Version), global::Google.Protobuf.Compiler.Version.Parser, new[]{ "Major", "Minor", "Patch", "Suffix" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Google.Protobuf.Compiler.CodeGeneratorRequest), global::Google.Protobuf.Compiler.CodeGeneratorRequest.Parser, new[]{ "FileToGenerate", "Parameter", "ProtoFile", "SourceFileDescriptors", "CompilerVersion" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Google.Protobuf.Compiler.CodeGeneratorResponse), global::Google.Protobuf.Compiler.CodeGeneratorResponse.Parser, new[]{ "Error", "SupportedFeatures", "File" }, null, new[]{ typeof(global::Google.Protobuf.Compiler.CodeGeneratorResponse.Types.Feature) }, null, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::Google.Protobuf.Compiler.CodeGeneratorResponse.Types.File), global::Google.Protobuf.Compiler.CodeGeneratorResponse.Types.File.Parser, new[]{ "Name", "InsertionPoint", "Content", "GeneratedCodeInfo" }, null, null, null, null)})
          }));
    }
    #endregion

  }
  #region Messages
  /// <summary>
  /// The version number of protocol compiler.
  /// </summary>
  public sealed partial class Version : pb::IMessage<Version>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Version> _parser = new pb::MessageParser<Version>(() => new Version());
    private pb::UnknownFieldSet _unknownFields;
    private int _hasBits0;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Version> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Google.Protobuf.Compiler.PluginReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Version() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Version(Version other) : this() {
      _hasBits0 = other._hasBits0;
      major_ = other.major_;
      minor_ = other.minor_;
      patch_ = other.patch_;
      suffix_ = other.suffix_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Version Clone() {
      return new Version(this);
    }

    /// <summary>Field number for the "major" field.</summary>
    public const int MajorFieldNumber = 1;
    private readonly static int MajorDefaultValue = 0;

    private int major_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Major {
      get { if ((_hasBits0 & 1) != 0) { return major_; } else { return MajorDefaultValue; } }
      set {
        _hasBits0 |= 1;
        major_ = value;
      }
    }
    /// <summary>Gets whether the "major" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasMajor {
      get { return (_hasBits0 & 1) != 0; }
    }
    /// <summary>Clears the value of the "major" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearMajor() {
      _hasBits0 &= ~1;
    }

    /// <summary>Field number for the "minor" field.</summary>
    public const int MinorFieldNumber = 2;
    private readonly static int MinorDefaultValue = 0;

    private int minor_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Minor {
      get { if ((_hasBits0 & 2) != 0) { return minor_; } else { return MinorDefaultValue; } }
      set {
        _hasBits0 |= 2;
        minor_ = value;
      }
    }
    /// <summary>Gets whether the "minor" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasMinor {
      get { return (_hasBits0 & 2) != 0; }
    }
    /// <summary>Clears the value of the "minor" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearMinor() {
      _hasBits0 &= ~2;
    }

    /// <summary>Field number for the "patch" field.</summary>
    public const int PatchFieldNumber = 3;
    private readonly static int PatchDefaultValue = 0;

    private int patch_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Patch {
      get { if ((_hasBits0 & 4) != 0) { return patch_; } else { return PatchDefaultValue; } }
      set {
        _hasBits0 |= 4;
        patch_ = value;
      }
    }
    /// <summary>Gets whether the "patch" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasPatch {
      get { return (_hasBits0 & 4) != 0; }
    }
    /// <summary>Clears the value of the "patch" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearPatch() {
      _hasBits0 &= ~4;
    }

    /// <summary>Field number for the "suffix" field.</summary>
    public const int SuffixFieldNumber = 4;
    private readonly static string SuffixDefaultValue = "";

    private string suffix_;
    /// <summary>
    /// A suffix for alpha, beta or rc release, e.g., "alpha-1", "rc2". It should
    /// be empty for mainline stable releases.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Suffix {
      get { return suffix_ ?? SuffixDefaultValue; }
      set {
        suffix_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "suffix" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasSuffix {
      get { return suffix_ != null; }
    }
    /// <summary>Clears the value of the "suffix" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearSuffix() {
      suffix_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Version);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Version other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Major != other.Major) return false;
      if (Minor != other.Minor) return false;
      if (Patch != other.Patch) return false;
      if (Suffix != other.Suffix) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasMajor) hash ^= Major.GetHashCode();
      if (HasMinor) hash ^= Minor.GetHashCode();
      if (HasPatch) hash ^= Patch.GetHashCode();
      if (HasSuffix) hash ^= Suffix.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasMajor) {
        output.WriteRawTag(8);
        output.WriteInt32(Major);
      }
      if (HasMinor) {
        output.WriteRawTag(16);
        output.WriteInt32(Minor);
      }
      if (HasPatch) {
        output.WriteRawTag(24);
        output.WriteInt32(Patch);
      }
      if (HasSuffix) {
        output.WriteRawTag(34);
        output.WriteString(Suffix);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasMajor) {
        output.WriteRawTag(8);
        output.WriteInt32(Major);
      }
      if (HasMinor) {
        output.WriteRawTag(16);
        output.WriteInt32(Minor);
      }
      if (HasPatch) {
        output.WriteRawTag(24);
        output.WriteInt32(Patch);
      }
      if (HasSuffix) {
        output.WriteRawTag(34);
        output.WriteString(Suffix);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasMajor) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Major);
      }
      if (HasMinor) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Minor);
      }
      if (HasPatch) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Patch);
      }
      if (HasSuffix) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Suffix);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Version other) {
      if (other == null) {
        return;
      }
      if (other.HasMajor) {
        Major = other.Major;
      }
      if (other.HasMinor) {
        Minor = other.Minor;
      }
      if (other.HasPatch) {
        Patch = other.Patch;
      }
      if (other.HasSuffix) {
        Suffix = other.Suffix;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Major = input.ReadInt32();
            break;
          }
          case 16: {
            Minor = input.ReadInt32();
            break;
          }
          case 24: {
            Patch = input.ReadInt32();
            break;
          }
          case 34: {
            Suffix = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Major = input.ReadInt32();
            break;
          }
          case 16: {
            Minor = input.ReadInt32();
            break;
          }
          case 24: {
            Patch = input.ReadInt32();
            break;
          }
          case 34: {
            Suffix = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// An encoded CodeGeneratorRequest is written to the plugin's stdin.
  /// </summary>
  public sealed partial class CodeGeneratorRequest : pb::IMessage<CodeGeneratorRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CodeGeneratorRequest> _parser = new pb::MessageParser<CodeGeneratorRequest>(() => new CodeGeneratorRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CodeGeneratorRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Google.Protobuf.Compiler.PluginReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CodeGeneratorRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CodeGeneratorRequest(CodeGeneratorRequest other) : this() {
      fileToGenerate_ = other.fileToGenerate_.Clone();
      parameter_ = other.parameter_;
      protoFile_ = other.protoFile_.Clone();
      sourceFileDescriptors_ = other.sourceFileDescriptors_.Clone();
      compilerVersion_ = other.compilerVersion_ != null ? other.compilerVersion_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CodeGeneratorRequest Clone() {
      return new CodeGeneratorRequest(this);
    }

    /// <summary>Field number for the "file_to_generate" field.</summary>
    public const int FileToGenerateFieldNumber = 1;
    private static readonly pb::FieldCodec<string> _repeated_fileToGenerate_codec
        = pb::FieldCodec.ForString(10);
    private readonly pbc::RepeatedField<string> fileToGenerate_ = new pbc::RepeatedField<string>();
    /// <summary>
    /// The .proto files that were explicitly listed on the command-line.  The
    /// code generator should generate code only for these files.  Each file's
    /// descriptor will be included in proto_file, below.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> FileToGenerate {
      get { return fileToGenerate_; }
    }

    /// <summary>Field number for the "parameter" field.</summary>
    public const int ParameterFieldNumber = 2;
    private readonly static string ParameterDefaultValue = "";

    private string parameter_;
    /// <summary>
    /// The generator parameter passed on the command-line.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Parameter {
      get { return parameter_ ?? ParameterDefaultValue; }
      set {
        parameter_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "parameter" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasParameter {
      get { return parameter_ != null; }
    }
    /// <summary>Clears the value of the "parameter" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearParameter() {
      parameter_ = null;
    }

    /// <summary>Field number for the "proto_file" field.</summary>
    public const int ProtoFileFieldNumber = 15;
    private static readonly pb::FieldCodec<global::Google.Protobuf.Reflection.FileDescriptorProto> _repeated_protoFile_codec
        = pb::FieldCodec.ForMessage(122, global::Google.Protobuf.Reflection.FileDescriptorProto.Parser);
    private readonly pbc::RepeatedField<global::Google.Protobuf.Reflection.FileDescriptorProto> protoFile_ = new pbc::RepeatedField<global::Google.Protobuf.Reflection.FileDescriptorProto>();
    /// <summary>
    /// FileDescriptorProtos for all files in files_to_generate and everything
    /// they import.  The files will appear in topological order, so each file
    /// appears before any file that imports it.
    ///
    /// Note: the files listed in files_to_generate will include runtime-retention
    /// options only, but all other files will include source-retention options.
    /// The source_file_descriptors field below is available in case you need
    /// source-retention options for files_to_generate.
    ///
    /// protoc guarantees that all proto_files will be written after
    /// the fields above, even though this is not technically guaranteed by the
    /// protobuf wire format.  This theoretically could allow a plugin to stream
    /// in the FileDescriptorProtos and handle them one by one rather than read
    /// the entire set into memory at once.  However, as of this writing, this
    /// is not similarly optimized on protoc's end -- it will store all fields in
    /// memory at once before sending them to the plugin.
    ///
    /// Type names of fields and extensions in the FileDescriptorProto are always
    /// fully qualified.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Google.Protobuf.Reflection.FileDescriptorProto> ProtoFile {
      get { return protoFile_; }
    }

    /// <summary>Field number for the "source_file_descriptors" field.</summary>
    public const int SourceFileDescriptorsFieldNumber = 17;
    private static readonly pb::FieldCodec<global::Google.Protobuf.Reflection.FileDescriptorProto> _repeated_sourceFileDescriptors_codec
        = pb::FieldCodec.ForMessage(138, global::Google.Protobuf.Reflection.FileDescriptorProto.Parser);
    private readonly pbc::RepeatedField<global::Google.Protobuf.Reflection.FileDescriptorProto> sourceFileDescriptors_ = new pbc::RepeatedField<global::Google.Protobuf.Reflection.FileDescriptorProto>();
    /// <summary>
    /// File descriptors with all options, including source-retention options.
    /// These descriptors are only provided for the files listed in
    /// files_to_generate.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Google.Protobuf.Reflection.FileDescriptorProto> SourceFileDescriptors {
      get { return sourceFileDescriptors_; }
    }

    /// <summary>Field number for the "compiler_version" field.</summary>
    public const int CompilerVersionFieldNumber = 3;
    private global::Google.Protobuf.Compiler.Version compilerVersion_;
    /// <summary>
    /// The version number of protocol compiler.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.Compiler.Version CompilerVersion {
      get { return compilerVersion_; }
      set {
        compilerVersion_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CodeGeneratorRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CodeGeneratorRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!fileToGenerate_.Equals(other.fileToGenerate_)) return false;
      if (Parameter != other.Parameter) return false;
      if(!protoFile_.Equals(other.protoFile_)) return false;
      if(!sourceFileDescriptors_.Equals(other.sourceFileDescriptors_)) return false;
      if (!object.Equals(CompilerVersion, other.CompilerVersion)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= fileToGenerate_.GetHashCode();
      if (HasParameter) hash ^= Parameter.GetHashCode();
      hash ^= protoFile_.GetHashCode();
      hash ^= sourceFileDescriptors_.GetHashCode();
      if (compilerVersion_ != null) hash ^= CompilerVersion.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      fileToGenerate_.WriteTo(output, _repeated_fileToGenerate_codec);
      if (HasParameter) {
        output.WriteRawTag(18);
        output.WriteString(Parameter);
      }
      if (compilerVersion_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(CompilerVersion);
      }
      protoFile_.WriteTo(output, _repeated_protoFile_codec);
      sourceFileDescriptors_.WriteTo(output, _repeated_sourceFileDescriptors_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      fileToGenerate_.WriteTo(ref output, _repeated_fileToGenerate_codec);
      if (HasParameter) {
        output.WriteRawTag(18);
        output.WriteString(Parameter);
      }
      if (compilerVersion_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(CompilerVersion);
      }
      protoFile_.WriteTo(ref output, _repeated_protoFile_codec);
      sourceFileDescriptors_.WriteTo(ref output, _repeated_sourceFileDescriptors_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += fileToGenerate_.CalculateSize(_repeated_fileToGenerate_codec);
      if (HasParameter) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Parameter);
      }
      size += protoFile_.CalculateSize(_repeated_protoFile_codec);
      size += sourceFileDescriptors_.CalculateSize(_repeated_sourceFileDescriptors_codec);
      if (compilerVersion_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(CompilerVersion);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CodeGeneratorRequest other) {
      if (other == null) {
        return;
      }
      fileToGenerate_.Add(other.fileToGenerate_);
      if (other.HasParameter) {
        Parameter = other.Parameter;
      }
      protoFile_.Add(other.protoFile_);
      sourceFileDescriptors_.Add(other.sourceFileDescriptors_);
      if (other.compilerVersion_ != null) {
        if (compilerVersion_ == null) {
          CompilerVersion = new global::Google.Protobuf.Compiler.Version();
        }
        CompilerVersion.MergeFrom(other.CompilerVersion);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            fileToGenerate_.AddEntriesFrom(input, _repeated_fileToGenerate_codec);
            break;
          }
          case 18: {
            Parameter = input.ReadString();
            break;
          }
          case 26: {
            if (compilerVersion_ == null) {
              CompilerVersion = new global::Google.Protobuf.Compiler.Version();
            }
            input.ReadMessage(CompilerVersion);
            break;
          }
          case 122: {
            protoFile_.AddEntriesFrom(input, _repeated_protoFile_codec);
            break;
          }
          case 138: {
            sourceFileDescriptors_.AddEntriesFrom(input, _repeated_sourceFileDescriptors_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            fileToGenerate_.AddEntriesFrom(ref input, _repeated_fileToGenerate_codec);
            break;
          }
          case 18: {
            Parameter = input.ReadString();
            break;
          }
          case 26: {
            if (compilerVersion_ == null) {
              CompilerVersion = new global::Google.Protobuf.Compiler.Version();
            }
            input.ReadMessage(CompilerVersion);
            break;
          }
          case 122: {
            protoFile_.AddEntriesFrom(ref input, _repeated_protoFile_codec);
            break;
          }
          case 138: {
            sourceFileDescriptors_.AddEntriesFrom(ref input, _repeated_sourceFileDescriptors_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// The plugin writes an encoded CodeGeneratorResponse to stdout.
  /// </summary>
  public sealed partial class CodeGeneratorResponse : pb::IMessage<CodeGeneratorResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CodeGeneratorResponse> _parser = new pb::MessageParser<CodeGeneratorResponse>(() => new CodeGeneratorResponse());
    private pb::UnknownFieldSet _unknownFields;
    private int _hasBits0;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CodeGeneratorResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Google.Protobuf.Compiler.PluginReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CodeGeneratorResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CodeGeneratorResponse(CodeGeneratorResponse other) : this() {
      _hasBits0 = other._hasBits0;
      error_ = other.error_;
      supportedFeatures_ = other.supportedFeatures_;
      file_ = other.file_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CodeGeneratorResponse Clone() {
      return new CodeGeneratorResponse(this);
    }

    /// <summary>Field number for the "error" field.</summary>
    public const int ErrorFieldNumber = 1;
    private readonly static string ErrorDefaultValue = "";

    private string error_;
    /// <summary>
    /// Error message.  If non-empty, code generation failed.  The plugin process
    /// should exit with status code zero even if it reports an error in this way.
    ///
    /// This should be used to indicate errors in .proto files which prevent the
    /// code generator from generating correct code.  Errors which indicate a
    /// problem in protoc itself -- such as the input CodeGeneratorRequest being
    /// unparseable -- should be reported by writing a message to stderr and
    /// exiting with a non-zero status code.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Error {
      get { return error_ ?? ErrorDefaultValue; }
      set {
        error_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }
    /// <summary>Gets whether the "error" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasError {
      get { return error_ != null; }
    }
    /// <summary>Clears the value of the "error" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearError() {
      error_ = null;
    }

    /// <summary>Field number for the "supported_features" field.</summary>
    public const int SupportedFeaturesFieldNumber = 2;
    private readonly static ulong SupportedFeaturesDefaultValue = 0UL;

    private ulong supportedFeatures_;
    /// <summary>
    /// A bitmask of supported features that the code generator supports.
    /// This is a bitwise "or" of values from the Feature enum.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong SupportedFeatures {
      get { if ((_hasBits0 & 1) != 0) { return supportedFeatures_; } else { return SupportedFeaturesDefaultValue; } }
      set {
        _hasBits0 |= 1;
        supportedFeatures_ = value;
      }
    }
    /// <summary>Gets whether the "supported_features" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasSupportedFeatures {
      get { return (_hasBits0 & 1) != 0; }
    }
    /// <summary>Clears the value of the "supported_features" field</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearSupportedFeatures() {
      _hasBits0 &= ~1;
    }

    /// <summary>Field number for the "file" field.</summary>
    public const int FileFieldNumber = 15;
    private static readonly pb::FieldCodec<global::Google.Protobuf.Compiler.CodeGeneratorResponse.Types.File> _repeated_file_codec
        = pb::FieldCodec.ForMessage(122, global::Google.Protobuf.Compiler.CodeGeneratorResponse.Types.File.Parser);
    private readonly pbc::RepeatedField<global::Google.Protobuf.Compiler.CodeGeneratorResponse.Types.File> file_ = new pbc::RepeatedField<global::Google.Protobuf.Compiler.CodeGeneratorResponse.Types.File>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Google.Protobuf.Compiler.CodeGeneratorResponse.Types.File> File {
      get { return file_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CodeGeneratorResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CodeGeneratorResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Error != other.Error) return false;
      if (SupportedFeatures != other.SupportedFeatures) return false;
      if(!file_.Equals(other.file_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasError) hash ^= Error.GetHashCode();
      if (HasSupportedFeatures) hash ^= SupportedFeatures.GetHashCode();
      hash ^= file_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasError) {
        output.WriteRawTag(10);
        output.WriteString(Error);
      }
      if (HasSupportedFeatures) {
        output.WriteRawTag(16);
        output.WriteUInt64(SupportedFeatures);
      }
      file_.WriteTo(output, _repeated_file_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasError) {
        output.WriteRawTag(10);
        output.WriteString(Error);
      }
      if (HasSupportedFeatures) {
        output.WriteRawTag(16);
        output.WriteUInt64(SupportedFeatures);
      }
      file_.WriteTo(ref output, _repeated_file_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasError) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Error);
      }
      if (HasSupportedFeatures) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(SupportedFeatures);
      }
      size += file_.CalculateSize(_repeated_file_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CodeGeneratorResponse other) {
      if (other == null) {
        return;
      }
      if (other.HasError) {
        Error = other.Error;
      }
      if (other.HasSupportedFeatures) {
        SupportedFeatures = other.SupportedFeatures;
      }
      file_.Add(other.file_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Error = input.ReadString();
            break;
          }
          case 16: {
            SupportedFeatures = input.ReadUInt64();
            break;
          }
          case 122: {
            file_.AddEntriesFrom(input, _repeated_file_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Error = input.ReadString();
            break;
          }
          case 16: {
            SupportedFeatures = input.ReadUInt64();
            break;
          }
          case 122: {
            file_.AddEntriesFrom(ref input, _repeated_file_codec);
            break;
          }
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the CodeGeneratorResponse message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      /// <summary>
      /// Sync with code_generator.h.
      /// </summary>
      public enum Feature {
        [pbr::OriginalName("FEATURE_NONE")] None = 0,
        [pbr::OriginalName("FEATURE_PROTO3_OPTIONAL")] Proto3Optional = 1,
        [pbr::OriginalName("FEATURE_SUPPORTS_EDITIONS")] SupportsEditions = 2,
      }

      /// <summary>
      /// Represents a single generated file.
      /// </summary>
      public sealed partial class File : pb::IMessage<File>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<File> _parser = new pb::MessageParser<File>(() => new File());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<File> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::Google.Protobuf.Compiler.CodeGeneratorResponse.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public File() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public File(File other) : this() {
          name_ = other.name_;
          insertionPoint_ = other.insertionPoint_;
          content_ = other.content_;
          generatedCodeInfo_ = other.generatedCodeInfo_ != null ? other.generatedCodeInfo_.Clone() : null;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public File Clone() {
          return new File(this);
        }

        /// <summary>Field number for the "name" field.</summary>
        public const int NameFieldNumber = 1;
        private readonly static string NameDefaultValue = "";

        private string name_;
        /// <summary>
        /// The file name, relative to the output directory.  The name must not
        /// contain "." or ".." components and must be relative, not be absolute (so,
        /// the file cannot lie outside the output directory).  "/" must be used as
        /// the path separator, not "\".
        ///
        /// If the name is omitted, the content will be appended to the previous
        /// file.  This allows the generator to break large files into small chunks,
        /// and allows the generated text to be streamed back to protoc so that large
        /// files need not reside completely in memory at one time.  Note that as of
        /// this writing protoc does not optimize for this -- it will read the entire
        /// CodeGeneratorResponse before writing files to disk.
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Name {
          get { return name_ ?? NameDefaultValue; }
          set {
            name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }
        /// <summary>Gets whether the "name" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasName {
          get { return name_ != null; }
        }
        /// <summary>Clears the value of the "name" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearName() {
          name_ = null;
        }

        /// <summary>Field number for the "insertion_point" field.</summary>
        public const int InsertionPointFieldNumber = 2;
        private readonly static string InsertionPointDefaultValue = "";

        private string insertionPoint_;
        /// <summary>
        /// If non-empty, indicates that the named file should already exist, and the
        /// content here is to be inserted into that file at a defined insertion
        /// point.  This feature allows a code generator to extend the output
        /// produced by another code generator.  The original generator may provide
        /// insertion points by placing special annotations in the file that look
        /// like:
        ///   @@protoc_insertion_point(NAME)
        /// The annotation can have arbitrary text before and after it on the line,
        /// which allows it to be placed in a comment.  NAME should be replaced with
        /// an identifier naming the point -- this is what other generators will use
        /// as the insertion_point.  Code inserted at this point will be placed
        /// immediately above the line containing the insertion point (thus multiple
        /// insertions to the same point will come out in the order they were added).
        /// The double-@ is intended to make it unlikely that the generated code
        /// could contain things that look like insertion points by accident.
        ///
        /// For example, the C++ code generator places the following line in the
        /// .pb.h files that it generates:
        ///   // @@protoc_insertion_point(namespace_scope)
        /// This line appears within the scope of the file's package namespace, but
        /// outside of any particular class.  Another plugin can then specify the
        /// insertion_point "namespace_scope" to generate additional classes or
        /// other declarations that should be placed in this scope.
        ///
        /// Note that if the line containing the insertion point begins with
        /// whitespace, the same whitespace will be added to every line of the
        /// inserted text.  This is useful for languages like Python, where
        /// indentation matters.  In these languages, the insertion point comment
        /// should be indented the same amount as any inserted code will need to be
        /// in order to work correctly in that context.
        ///
        /// The code generator that generates the initial file and the one which
        /// inserts into it must both run as part of a single invocation of protoc.
        /// Code generators are executed in the order in which they appear on the
        /// command line.
        ///
        /// If |insertion_point| is present, |name| must also be present.
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string InsertionPoint {
          get { return insertionPoint_ ?? InsertionPointDefaultValue; }
          set {
            insertionPoint_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }
        /// <summary>Gets whether the "insertion_point" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasInsertionPoint {
          get { return insertionPoint_ != null; }
        }
        /// <summary>Clears the value of the "insertion_point" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearInsertionPoint() {
          insertionPoint_ = null;
        }

        /// <summary>Field number for the "content" field.</summary>
        public const int ContentFieldNumber = 15;
        private readonly static string ContentDefaultValue = "";

        private string content_;
        /// <summary>
        /// The file contents.
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Content {
          get { return content_ ?? ContentDefaultValue; }
          set {
            content_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }
        /// <summary>Gets whether the "content" field is set</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool HasContent {
          get { return content_ != null; }
        }
        /// <summary>Clears the value of the "content" field</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void ClearContent() {
          content_ = null;
        }

        /// <summary>Field number for the "generated_code_info" field.</summary>
        public const int GeneratedCodeInfoFieldNumber = 16;
        private global::Google.Protobuf.Reflection.GeneratedCodeInfo generatedCodeInfo_;
        /// <summary>
        /// Information describing the file content being inserted. If an insertion
        /// point is used, this information will be appropriately offset and inserted
        /// into the code generation metadata for the generated files.
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public global::Google.Protobuf.Reflection.GeneratedCodeInfo GeneratedCodeInfo {
          get { return generatedCodeInfo_; }
          set {
            generatedCodeInfo_ = value;
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as File);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(File other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (Name != other.Name) return false;
          if (InsertionPoint != other.InsertionPoint) return false;
          if (Content != other.Content) return false;
          if (!object.Equals(GeneratedCodeInfo, other.GeneratedCodeInfo)) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (HasName) hash ^= Name.GetHashCode();
          if (HasInsertionPoint) hash ^= InsertionPoint.GetHashCode();
          if (HasContent) hash ^= Content.GetHashCode();
          if (generatedCodeInfo_ != null) hash ^= GeneratedCodeInfo.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (HasName) {
            output.WriteRawTag(10);
            output.WriteString(Name);
          }
          if (HasInsertionPoint) {
            output.WriteRawTag(18);
            output.WriteString(InsertionPoint);
          }
          if (HasContent) {
            output.WriteRawTag(122);
            output.WriteString(Content);
          }
          if (generatedCodeInfo_ != null) {
            output.WriteRawTag(130, 1);
            output.WriteMessage(GeneratedCodeInfo);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (HasName) {
            output.WriteRawTag(10);
            output.WriteString(Name);
          }
          if (HasInsertionPoint) {
            output.WriteRawTag(18);
            output.WriteString(InsertionPoint);
          }
          if (HasContent) {
            output.WriteRawTag(122);
            output.WriteString(Content);
          }
          if (generatedCodeInfo_ != null) {
            output.WriteRawTag(130, 1);
            output.WriteMessage(GeneratedCodeInfo);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (HasName) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
          }
          if (HasInsertionPoint) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(InsertionPoint);
          }
          if (HasContent) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Content);
          }
          if (generatedCodeInfo_ != null) {
            size += 2 + pb::CodedOutputStream.ComputeMessageSize(GeneratedCodeInfo);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(File other) {
          if (other == null) {
            return;
          }
          if (other.HasName) {
            Name = other.Name;
          }
          if (other.HasInsertionPoint) {
            InsertionPoint = other.InsertionPoint;
          }
          if (other.HasContent) {
            Content = other.Content;
          }
          if (other.generatedCodeInfo_ != null) {
            if (generatedCodeInfo_ == null) {
              GeneratedCodeInfo = new global::Google.Protobuf.Reflection.GeneratedCodeInfo();
            }
            GeneratedCodeInfo.MergeFrom(other.GeneratedCodeInfo);
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 10: {
                Name = input.ReadString();
                break;
              }
              case 18: {
                InsertionPoint = input.ReadString();
                break;
              }
              case 122: {
                Content = input.ReadString();
                break;
              }
              case 130: {
                if (generatedCodeInfo_ == null) {
                  GeneratedCodeInfo = new global::Google.Protobuf.Reflection.GeneratedCodeInfo();
                }
                input.ReadMessage(GeneratedCodeInfo);
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 10: {
                Name = input.ReadString();
                break;
              }
              case 18: {
                InsertionPoint = input.ReadString();
                break;
              }
              case 122: {
                Content = input.ReadString();
                break;
              }
              case 130: {
                if (generatedCodeInfo_ == null) {
                  GeneratedCodeInfo = new global::Google.Protobuf.Reflection.GeneratedCodeInfo();
                }
                input.ReadMessage(GeneratedCodeInfo);
                break;
              }
            }
          }
        }
        #endif

      }

    }
    #endregion

  }

  #endregion

}

#endregion Designer generated code
