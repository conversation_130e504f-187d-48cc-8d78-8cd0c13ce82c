CREATE TABLE tab(c1 int NOT NULL PRIMARY KEY,c2 POINT NOT NULL SRID 0,
c3 LINESTRING NOT NULL SRID 0,c4 POLYGON NOT NULL SRID 0,c5 GEOMETRY NOT NULL SRID 0) 
ENGINE=InnoDB ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=8;
CREATE SPATIAL INDEX idx1 on tab(c2);
CREATE SPATIAL INDEX idx2 on tab(c3 ASC) COMMENT 'wl6968';
ERROR HY000: Incorrect usage of spatial/fulltext/hash index and explicit index order
CREATE SPATIAL INDEX idx2 on tab(c3 DESC) COMMENT 'wl6968';
ERROR HY000: Incorrect usage of spatial/fulltext/hash index and explicit index order
CREATE SPATIAL INDEX idx3 on tab(c4) KEY_BLOCK_SIZE=8 ;
CREATE SPATIAL INDEX idx4 on tab(c5 DESC) KEY_BLOCK_SIZE=4
COMMENT 'Spatial index on Geometry type column';
ERROR HY000: Incorrect usage of spatial/fulltext/hash index and explicit index order
ANALYZE TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	analyze	status	OK
SHOW INDEXES FROM tab;
Table	Non_unique	Key_name	Seq_in_index	Column_name	Collation	Cardinality	Sub_part	Packed	Null	Index_type	Comment	Index_comment	Visible	Expression
tab	0	PRIMARY	1	c1	A	0	NULL	NULL		BTREE			YES	NULL
tab	1	idx1	1	c2	A	0	32	NULL		SPATIAL			YES	NULL
tab	1	idx3	1	c4	A	0	32	NULL		SPATIAL			YES	NULL
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(1,ST_GeomFromText('POINT(10 10)'),ST_GeomFromText('LINESTRING(5 5,20 20,30 30)'),
ST_GeomFromText('POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))'),
ST_GeomFromText('POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(2,ST_GeomFromText('POINT(20 20)'),ST_GeomFromText('LINESTRING(20 20,30 30,40 40)'),
ST_GeomFromText('POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))'),
ST_GeomFromText('POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(3,ST_GeomFromText('POINT(4 4)'),ST_GeomFromText('LINESTRING(130 130,140 140,150 150)'),
ST_GeomFromText('POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))'),
ST_GeomFromText('POLYGON((4 -2,5 -4,6 -5,7 -4,7 2,4 -2))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(4,ST_GeomFromText('POINT(50 50)'),ST_GeomFromText('LINESTRING(200 200,300 300,400 400)'),
ST_GeomFromText('POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))'),
ST_GeomFromText('POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(5,ST_GeomFromText('POINT(3 3)'),ST_GeomFromText('LINESTRING(400 400,500 500,600 700)'),
ST_GeomFromText('POLYGON((1010 1010,1020 1020,1030 1030,1040 1030,1020 1010,1010 1010))'),
ST_GeomFromText('POLYGON((1010 1010,1020 1020,1030 1030,1040 1030,1020 1010,1010 1010))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(6,ST_GeomFromText('POINT(3 3)'),ST_GeomFromText('LINESTRING(40 40,50 50,60 70)'),
ST_GeomFromText('POLYGON((2010 2010,2020 2020,2030 2030,2040 2030,2020 2010,2010 2010))'),
ST_GeomFromText('POLYGON((2010 2010,2020 2020,2030 2030,2040 2030,2020 2010,2010 2010))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(7,ST_GeomFromText('POINT(60 70)'),ST_GeomFromText('LINESTRING(40 40,50 50,60 70)'),
ST_GeomFromText('POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))'),
ST_GeomFromText('POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(8,ST_GeomFromText('POINT(0 0)'),ST_GeomFromText('LINESTRING(40 40,50 50,60 70)'),
ST_GeomFromText('POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))'),
ST_GeomFromText('POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(9,ST_GeomFromText('POINT(120 120)'),ST_GeomFromText('LINESTRING(100 100,110 110,120 120)'),
ST_GeomFromText('POLYGON((4010 4010,4020 4020,4030 4030,4040 4030,4020 4010,4010 4010))'),
ST_GeomFromText('POLYGON((4010 4010,4020 4020,4030 4030,4040 4030,4020 4010,4010 4010))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(10,ST_GeomFromText('POINT(160 160)'),ST_GeomFromText('LINESTRING(140 140,150 150,160 160)'),
ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))'),
ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))'));
ANALYZE TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	analyze	status	OK
SET @g1 = ST_GeomFromText( 'POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 300 300,400 400)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBRContains(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
4	POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((30 30,40 40,50 50,30 50,30 40,30 30)) ');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrwithin(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRWithin(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrwithin(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRWithin(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrwithin(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((100 200,200 300,400 500,500 300,300 200,100 300,100 200))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Crosses(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where st_crosses(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Crosses(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE ST_Crosses(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where st_crosses(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE ST_Crosses(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where st_crosses(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 10 10,30 30,40 40)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE ST_CRosses(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where st_crosses(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  ST_Crosses(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE ST_Crosses(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where st_crosses(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE ST_Crosses(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where st_crosses(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((4 -2,5 -4,6 -5,7 -4,7 2,4 -2))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRDisjoint(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	index	idx3	PRIMARY	4	NULL	10	90.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrdisjoint(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRDisjoint(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
4	POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))
5	POLYGON((1010 1010,1020 1020,1030 1030,1040 1030,1020 1010,1010 1010))
6	POLYGON((2010 2010,2020 2020,2030 2030,2040 2030,2020 2010,2010 2010))
7	POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))
8	POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))
9	POLYGON((4010 4010,4020 4020,4030 4030,4040 4030,4020 4010,4010 4010))
10	POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRDisjoint(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	index	idx3	PRIMARY	4	NULL	10	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrdisjoint(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRDisjoint(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	ALL	idx3	NULL	NULL	NULL	10	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrdisjoint(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrequals(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
10	POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBREquals(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrequals(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBREquals(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrequals(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 30,30 40,40 50,50 30,0 0))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	2	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 30 30,40 40,50 50)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 2,4 5,5 5,7 1,0 0 ))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING(7 1,30 30,1010 3010,1010 2010,3010 3010,4010 4010,5010 5010 )');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	index	idx3	PRIMARY	4	NULL	10	80.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBROverlaps(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	8	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	8	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 30,30 40,40 50,50 30,0 0))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Touches(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	2	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where st_touches(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Touches(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE ST_Touches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where st_touches(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE ST_Touches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where st_touches(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 100 100,200 200,300 300)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Touches(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where st_touches(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  ST_Touches(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
4	POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE ST_Touches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where st_touches(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE ST_Touches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where st_touches(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((30 30,40 40,50 50,30 50,30 40,30 30)) ');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrwithin(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRWithin(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrwithin(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRWithin(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrwithin(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((4 -2,5 -4,6 -5,7 -4,7 2,4 -2))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRDisjoint(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	index	idx3	PRIMARY	4	NULL	10	90.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrdisjoint(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRDisjoint(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
4	POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))
5	POLYGON((1010 1010,1020 1020,1030 1030,1040 1030,1020 1010,1010 1010))
6	POLYGON((2010 2010,2020 2020,2030 2030,2040 2030,2020 2010,2010 2010))
7	POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))
8	POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))
9	POLYGON((4010 4010,4020 4020,4030 4030,4040 4030,4020 4010,4010 4010))
10	POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRDisjoint(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	index	idx3	PRIMARY	4	NULL	10	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrdisjoint(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRDisjoint(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	ALL	idx3	NULL	NULL	NULL	10	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrdisjoint(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrequals(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
10	POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBREquals(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrequals(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBREquals(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrequals(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 30,30 40,40 50,50 30,0 0))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	2	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 30 30,40 40,50 50)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 2,4 5,5 5,7 1,0 0 ))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 30,30 40,40 50,50 30,0 0))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRTouches(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	2	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrtouches(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRTouches(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRTouches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrtouches(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRTouches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrtouches(`test`.`tab`.`c4`,(@`g1`))
CREATE PROCEDURE proc_wl6968()
BEGIN
SET @g1 = ST_GeomFromText( 'POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRContains(tab.c4, @g1);
EXPLAIN DELETE FROM tab WHERE MBRContains(tab.c4, @g1);
END |
CALL proc_wl6968();
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))');
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
DELETE FROM tab WHERE MBRContains(tab.c4, @g1);
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
CHECK TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	check	status	OK
SET @g1 = ST_GeomFromText('LINESTRING( 300 300,400 400)');
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBRContains(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
4	POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))
DELETE FROM tab WHERE MBRContains(tab.c4, @g1);
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBRContains(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
CHECK TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	check	status	OK
SET @g1 = ST_GeomFromText('POLYGON((100 200,1010 1010,1020 1020,500 300,300 200,100 300,100 200))');
SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Crosses(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
DELETE FROM tab WHERE ST_Crosses(tab.c4, @g1);
SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Crosses(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
CHECK TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	check	status	OK
SET @g1 = ST_GeomFromText('POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))');
SET @g2 = ST_GeomFromText( 'POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))');
UPDATE tab SET C4 = @g2  WHERE ST_Crosses(tab.c4, @g1);
SELECT c1,ST_Astext(c4) FROM tab WHERE  ST_Crosses(tab.c4, @g2) ORDER BY c1;
c1	ST_Astext(c4)
CHECK TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	check	status	OK
DROP TABLE tab;
DROP PROCEDURE proc_wl6968;
CREATE TABLE tab(c1 int ,c2 POINT NOT NULL SRID 0,
c3 LINESTRING NOT NULL SRID 0,c4 POLYGON NOT NULL SRID 0,c5 GEOMETRY NOT NULL SRID 0) 
ENGINE=InnoDB ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=16;
CREATE SPATIAL INDEX idx1 on tab(c2);
CREATE SPATIAL INDEX idx2 on tab(c3) COMMENT 'wl6968';
CREATE SPATIAL INDEX idx3 on tab(c4) KEY_BLOCK_SIZE=2 ;
CREATE SPATIAL INDEX idx4 on tab(c5) KEY_BLOCK_SIZE=8 
COMMENT 'Spatial index on Geometry type column';
ANALYZE TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	analyze	status	OK
SHOW INDEXES FROM tab;
Table	Non_unique	Key_name	Seq_in_index	Column_name	Collation	Cardinality	Sub_part	Packed	Null	Index_type	Comment	Index_comment	Visible	Expression
tab	1	idx1	1	c2	A	0	32	NULL		SPATIAL			YES	NULL
tab	1	idx2	1	c3	A	0	32	NULL		SPATIAL		wl6968	YES	NULL
tab	1	idx3	1	c4	A	0	32	NULL		SPATIAL			YES	NULL
tab	1	idx4	1	c5	A	0	32	NULL		SPATIAL		Spatial index on Geometry type column	YES	NULL
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(1,ST_GeomFromText('POINT(10 10)'),ST_GeomFromText('LINESTRING(5 5,20 20,30 30)'),
ST_GeomFromText('POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))'),
ST_GeomFromText('POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(2,ST_GeomFromText('POINT(20 20)'),ST_GeomFromText('LINESTRING(20 20,30 30,40 40)'),
ST_GeomFromText('POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))'),
ST_GeomFromText('POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(3,ST_GeomFromText('POINT(4 4)'),ST_GeomFromText('LINESTRING(130 130,140 140,150 150)'),
ST_GeomFromText('POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))'),
ST_GeomFromText('POLYGON((4 -2,5 -4,6 -5,7 -4,7 2,4 -2))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(4,ST_GeomFromText('POINT(50 50)'),ST_GeomFromText('LINESTRING(200 200,300 300,400 400)'),
ST_GeomFromText('POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))'),
ST_GeomFromText('POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(5,ST_GeomFromText('POINT(3 3)'),ST_GeomFromText('LINESTRING(400 400,500 500,600 700)'),
ST_GeomFromText('POLYGON((1010 1010,1020 1020,1030 1030,1040 1030,1020 1010,1010 1010))'),
ST_GeomFromText('POLYGON((1010 1010,1020 1020,1030 1030,1040 1030,1020 1010,1010 1010))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(6,ST_GeomFromText('POINT(3 3)'),ST_GeomFromText('LINESTRING(40 40,50 50,60 70)'),
ST_GeomFromText('POLYGON((2010 2010,2020 2020,2030 2030,2040 2030,2020 2010,2010 2010))'),
ST_GeomFromText('POLYGON((2010 2010,2020 2020,2030 2030,2040 2030,2020 2010,2010 2010))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(7,ST_GeomFromText('POINT(60 70)'),ST_GeomFromText('LINESTRING(40 40,50 50,60 70)'),
ST_GeomFromText('POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))'),
ST_GeomFromText('POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(8,ST_GeomFromText('POINT(0 0)'),ST_GeomFromText('LINESTRING(40 40,50 50,60 70)'),
ST_GeomFromText('POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))'),
ST_GeomFromText('POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(9,ST_GeomFromText('POINT(120 120)'),ST_GeomFromText('LINESTRING(100 100,110 110,120 120)'),
ST_GeomFromText('POLYGON((4010 4010,4020 4020,4030 4030,4040 4030,4020 4010,4010 4010))'),
ST_GeomFromText('POLYGON((4010 4010,4020 4020,4030 4030,4040 4030,4020 4010,4010 4010))'));
INSERT INTO tab(c1,c2,c3,c4,c5) 
VALUES(10,ST_GeomFromText('POINT(160 160)'),ST_GeomFromText('LINESTRING(140 140,150 150,160 160)'),
ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))'),
ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))'));
ANALYZE TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	analyze	status	OK
SET @g1 = ST_GeomFromText( 'POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 300 300,400 400)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBRContains(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
4	POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((30 30,40 40,50 50,30 50,30 40,30 30)) ');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrwithin(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRWithin(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrwithin(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRWithin(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrwithin(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((100 200,200 300,400 500,500 300,300 200,100 300,100 200))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Crosses(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where st_crosses(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Crosses(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE ST_Crosses(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where st_crosses(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE ST_Crosses(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where st_crosses(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 10 10,30 30,40 40)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE ST_CRosses(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where st_crosses(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  ST_Crosses(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE ST_Crosses(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where st_crosses(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE ST_Crosses(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where st_crosses(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((4 -2,5 -4,6 -5,7 -4,7 2,4 -2))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRDisjoint(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	ALL	idx3	NULL	NULL	NULL	10	90.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrdisjoint(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRDisjoint(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
4	POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))
5	POLYGON((1010 1010,1020 1020,1030 1030,1040 1030,1020 1010,1010 1010))
6	POLYGON((2010 2010,2020 2020,2030 2030,2040 2030,2020 2010,2010 2010))
7	POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))
8	POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))
9	POLYGON((4010 4010,4020 4020,4030 4030,4040 4030,4020 4010,4010 4010))
10	POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRDisjoint(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	ALL	idx3	NULL	NULL	NULL	10	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrdisjoint(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRDisjoint(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	ALL	idx3	NULL	NULL	NULL	10	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrdisjoint(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrequals(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
10	POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBREquals(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrequals(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBREquals(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrequals(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 30,30 40,40 50,50 30,0 0))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	2	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 30 30,40 40,50 50)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 2,4 5,5 5,7 1,0 0 ))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING(7 1,30 30,1010 3010,1010 2010,3010 3010,4010 4010,5010 5010 )');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	8	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBROverlaps(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	8	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	8	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 30,30 40,40 50,50 30,0 0))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Touches(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	2	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where st_touches(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Touches(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE ST_Touches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where st_touches(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE ST_Touches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where st_touches(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 100 100,200 200,300 300)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Touches(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where st_touches(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  ST_Touches(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
4	POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE ST_Touches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where st_touches(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE ST_Touches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where st_touches(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((30 30,40 40,50 50,30 50,30 40,30 30)) ');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrwithin(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRWithin(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrwithin(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRWithin(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrwithin(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((4 -2,5 -4,6 -5,7 -4,7 2,4 -2))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRDisjoint(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	ALL	idx3	NULL	NULL	NULL	10	90.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrdisjoint(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRDisjoint(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
4	POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))
5	POLYGON((1010 1010,1020 1020,1030 1030,1040 1030,1020 1010,1010 1010))
6	POLYGON((2010 2010,2020 2020,2030 2030,2040 2030,2020 2010,2010 2010))
7	POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))
8	POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))
9	POLYGON((4010 4010,4020 4020,4030 4030,4040 4030,4020 4010,4010 4010))
10	POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRDisjoint(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	ALL	idx3	NULL	NULL	NULL	10	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrdisjoint(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRDisjoint(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	ALL	idx3	NULL	NULL	NULL	10	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrdisjoint(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrequals(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
10	POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBREquals(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrequals(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBREquals(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrequals(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 30,30 40,40 50,50 30,0 0))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	2	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 30 30,40 40,50 50)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 2,4 5,5 5,7 1,0 0 ))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 30,30 40,40 50,50 30,0 0))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRTouches(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	2	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrtouches(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRTouches(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRTouches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrtouches(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRTouches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrtouches(`test`.`tab`.`c4`,(@`g1`))
CREATE PROCEDURE proc_wl6968()
BEGIN
SET @g1 = ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBREquals(tab.c4, @g1);
EXPLAIN DELETE FROM tab WHERE MBREquals(tab.c4, @g1);
END |
CALL proc_wl6968();
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrequals(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((30 30,40 40,50 50,30 50,30 40,30 30)) ');
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
DELETE FROM tab WHERE MBRWithin(tab.c4, @g1);
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
CHECK TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	check	status	OK
SET @g1 = ST_GeomFromText('POLYGON((100 200,200 300,400 500,500 300,300 200,100 300,100 200))');
SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Crosses(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
DELETE FROM tab WHERE ST_Crosses(tab.c4, @g1);
SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Crosses(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 2,4 5,5 5,7 1,0 0 ))');
SET @g2 = ST_GeomFromText( 'POLYGON((1 1,2 2,3 3,10 3,5 1,1 1))');
UPDATE tab SET C4 = @g2  WHERE MBROverlaps(tab.c4, @g1);
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBROverlaps(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((1 1,2 2,3 3,10 3,5 1,1 1))
CHECK TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	check	status	OK
DROP TABLE tab;
DROP PROCEDURE proc_wl6968;
CREATE TABLE tab(c1 int AUTO_INCREMENT PRIMARY KEY,c2 POINT NOT NULL SRID 0,
c3 LINESTRING NOT NULL SRID 0,c4 POLYGON NOT NULL SRID 0,c5 GEOMETRY NOT NULL SRID 0) 
ENGINE=InnoDB ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=16;
CREATE SPATIAL INDEX idx1 on tab(c2);
CREATE SPATIAL INDEX idx2 on tab(c3) COMMENT 'wl6968';
CREATE SPATIAL INDEX idx3 on tab(c4) KEY_BLOCK_SIZE=16 ;
CREATE SPATIAL INDEX idx4 on tab(c5) KEY_BLOCK_SIZE=16 
COMMENT 'Spatial index on Geometry type column';
ANALYZE TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	analyze	status	OK
SHOW INDEXES FROM tab;
Table	Non_unique	Key_name	Seq_in_index	Column_name	Collation	Cardinality	Sub_part	Packed	Null	Index_type	Comment	Index_comment	Visible	Expression
tab	0	PRIMARY	1	c1	A	0	NULL	NULL		BTREE			YES	NULL
tab	1	idx1	1	c2	A	0	32	NULL		SPATIAL			YES	NULL
tab	1	idx2	1	c3	A	0	32	NULL		SPATIAL		wl6968	YES	NULL
tab	1	idx3	1	c4	A	0	32	NULL		SPATIAL			YES	NULL
tab	1	idx4	1	c5	A	0	32	NULL		SPATIAL		Spatial index on Geometry type column	YES	NULL
INSERT INTO tab(c2,c3,c4,c5) 
VALUES(ST_GeomFromText('POINT(10 10)'),ST_GeomFromText('LINESTRING(5 5,20 20,30 30)'),
ST_GeomFromText('POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))'),
ST_GeomFromText('POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))'));
INSERT INTO tab(c2,c3,c4,c5) 
VALUES(ST_GeomFromText('POINT(20 20)'),ST_GeomFromText('LINESTRING(20 20,30 30,40 40)'),
ST_GeomFromText('POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))'),
ST_GeomFromText('POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))'));
INSERT INTO tab(c2,c3,c4,c5) 
VALUES(ST_GeomFromText('POINT(4 4)'),ST_GeomFromText('LINESTRING(130 130,140 140,150 150)'),
ST_GeomFromText('POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))'),
ST_GeomFromText('POLYGON((4 -2,5 -4,6 -5,7 -4,7 2,4 -2))'));
INSERT INTO tab(c2,c3,c4,c5) 
VALUES(ST_GeomFromText('POINT(50 50)'),ST_GeomFromText('LINESTRING(200 200,300 300,400 400)'),
ST_GeomFromText('POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))'),
ST_GeomFromText('POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))'));
INSERT INTO tab(c2,c3,c4,c5) 
VALUES(ST_GeomFromText('POINT(3 3)'),ST_GeomFromText('LINESTRING(400 400,500 500,600 700)'),
ST_GeomFromText('POLYGON((1010 1010,1020 1020,1030 1030,1040 1030,1020 1010,1010 1010))'),
ST_GeomFromText('POLYGON((1010 1010,1020 1020,1030 1030,1040 1030,1020 1010,1010 1010))'));
INSERT INTO tab(c2,c3,c4,c5) 
VALUES(ST_GeomFromText('POINT(3 3)'),ST_GeomFromText('LINESTRING(40 40,50 50,60 70)'),
ST_GeomFromText('POLYGON((2010 2010,2020 2020,2030 2030,2040 2030,2020 2010,2010 2010))'),
ST_GeomFromText('POLYGON((2010 2010,2020 2020,2030 2030,2040 2030,2020 2010,2010 2010))'));
INSERT INTO tab(c2,c3,c4,c5) 
VALUES(ST_GeomFromText('POINT(60 70)'),ST_GeomFromText('LINESTRING(40 40,50 50,60 70)'),
ST_GeomFromText('POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))'),
ST_GeomFromText('POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))'));
INSERT INTO tab(c2,c3,c4,c5) 
VALUES(ST_GeomFromText('POINT(0 0)'),ST_GeomFromText('LINESTRING(40 40,50 50,60 70)'),
ST_GeomFromText('POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))'),
ST_GeomFromText('POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))'));
INSERT INTO tab(c2,c3,c4,c5) 
VALUES(ST_GeomFromText('POINT(120 120)'),ST_GeomFromText('LINESTRING(100 100,110 110,120 120)'),
ST_GeomFromText('POLYGON((4010 4010,4020 4020,4030 4030,4040 4030,4020 4010,4010 4010))'),
ST_GeomFromText('POLYGON((4010 4010,4020 4020,4030 4030,4040 4030,4020 4010,4010 4010))'));
INSERT INTO tab(c2,c3,c4,c5) 
VALUES(ST_GeomFromText('POINT(160 160)'),ST_GeomFromText('LINESTRING(140 140,150 150,160 160)'),
ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))'),
ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))'));
ANALYZE TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	analyze	status	OK
SET @g1 = ST_GeomFromText( 'POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 300 300,400 400)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBRContains(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
4	POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((30 30,40 40,50 50,30 50,30 40,30 30)) ');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrwithin(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRWithin(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrwithin(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRWithin(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrwithin(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((100 200,200 300,400 500,500 300,300 200,100 300,100 200))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Crosses(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where st_crosses(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Crosses(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE ST_Crosses(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where st_crosses(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE ST_Crosses(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where st_crosses(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 10 10,30 30,40 40)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE ST_CRosses(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where st_crosses(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  ST_Crosses(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE ST_Crosses(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where st_crosses(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE ST_Crosses(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where st_crosses(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((4 -2,5 -4,6 -5,7 -4,7 2,4 -2))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRDisjoint(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	index	idx3	PRIMARY	4	NULL	10	90.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrdisjoint(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRDisjoint(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
4	POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))
5	POLYGON((1010 1010,1020 1020,1030 1030,1040 1030,1020 1010,1010 1010))
6	POLYGON((2010 2010,2020 2020,2030 2030,2040 2030,2020 2010,2010 2010))
7	POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))
8	POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))
9	POLYGON((4010 4010,4020 4020,4030 4030,4040 4030,4020 4010,4010 4010))
10	POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRDisjoint(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	index	idx3	PRIMARY	4	NULL	10	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrdisjoint(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRDisjoint(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	ALL	idx3	NULL	NULL	NULL	10	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrdisjoint(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrequals(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
10	POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBREquals(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrequals(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBREquals(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrequals(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 30,30 40,40 50,50 30,0 0))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	2	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 30 30,40 40,50 50)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 2,4 5,5 5,7 1,0 0 ))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING(7 1,30 30,1010 3010,1010 2010,3010 3010,4010 4010,5010 5010 )');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	index	idx3	PRIMARY	4	NULL	10	80.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBROverlaps(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	8	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	8	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 30,30 40,40 50,50 30,0 0))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Touches(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	2	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where st_touches(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Touches(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE ST_Touches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where st_touches(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE ST_Touches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where st_touches(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 100 100,200 200,300 300)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE ST_Touches(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where st_touches(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  ST_Touches(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
4	POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE ST_Touches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where st_touches(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE ST_Touches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where st_touches(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRContains(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRContains(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrcontains(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((30 30,40 40,50 50,30 50,30 40,30 30)) ');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrwithin(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRWithin(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRWithin(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrwithin(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRWithin(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrwithin(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((4 -2,5 -4,6 -5,7 -4,7 2,4 -2))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRDisjoint(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	index	idx3	PRIMARY	4	NULL	10	90.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrdisjoint(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRDisjoint(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
4	POLYGON((300 300,400 400,500 500,300 500,300 400,300 300))
5	POLYGON((1010 1010,1020 1020,1030 1030,1040 1030,1020 1010,1010 1010))
6	POLYGON((2010 2010,2020 2020,2030 2030,2040 2030,2020 2010,2010 2010))
7	POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))
8	POLYGON((3010 3010,3020 3020,3030 3030,3040 3030,3020 3010,3010 3010))
9	POLYGON((4010 4010,4020 4020,4030 4030,4040 4030,4020 4010,4010 4010))
10	POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRDisjoint(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	index	idx3	PRIMARY	4	NULL	10	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrdisjoint(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRDisjoint(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	ALL	idx3	NULL	NULL	NULL	10	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrdisjoint(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrequals(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
10	POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBREquals(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrequals(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBREquals(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrequals(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 30,30 40,40 50,50 30,0 0))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	2	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('LINESTRING( 30 30,40 40,50 50)');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE  MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRintersects(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrintersects(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 2,4 5,5 5,7 1,0 0 ))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	1	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBROverlaps(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBROverlaps(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbroverlaps(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 30,30 40,40 50,50 30,0 0))');
EXPLAIN SELECT c1,ST_Astext(c4) FROM tab WHERE MBRTouches(tab.c4, @g1) ORDER BY c1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	tab	NULL	range	idx3	idx3	34	NULL	2	100.00	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`tab`.`c1` AS `c1`,st_astext(`test`.`tab`.`c4`) AS `ST_Astext(c4)` from `test`.`tab` where mbrtouches(`test`.`tab`.`c4`,<cache>((@`g1`))) order by `test`.`tab`.`c1`
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRTouches(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
EXPLAIN UPDATE tab SET C2 = ST_GeomFromText('POINT(0 0)') 
WHERE MBRTouches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	update `test`.`tab` set `test`.`tab`.`c2` = st_geomfromtext('POINT(0 0)') where mbrtouches(`test`.`tab`.`c4`,(@`g1`))
EXPLAIN DELETE FROM tab WHERE MBRTouches(tab.c4, @g1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	tab	NULL	range	idx3	idx3	34	const	2	100.00	Using where
Warnings:
Note	1003	delete from `test`.`tab` where mbrtouches(`test`.`tab`.`c4`,(@`g1`))
SET @g1 = ST_GeomFromText('POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))');
SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
10	POLYGON((5010 5010,5020 5020,5030 5030,5040 5030,5020 5010,5010 5010))
DELETE FROM tab WHERE MBREquals(tab.c4, @g1);
SELECT c1,ST_Astext(c4) FROM tab WHERE MBREquals(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
CHECK TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	check	status	OK
SET @g1 = ST_GeomFromText( 'POLYGON((0 0,0 30,30 40,40 50,50 30,0 0))');
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
1	POLYGON((30 30,40 40,50 50,30 50,30 40,30 30))
2	POLYGON((40 50,40 70,50 100,70 100,80 80,70 50,40 50))
3	POLYGON((7 1,6 2,6 3,10 3,10 1,7 1))
DELETE FROM tab WHERE MBRIntersects(tab.c4, @g1);
SELECT c1,ST_Astext(c4) FROM tab WHERE MBRIntersects(tab.c4, @g1) ORDER BY c1;
c1	ST_Astext(c4)
CHECK TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	check	status	OK
DROP TABLE tab;
CREATE TABLE tab(c1 POINT NOT NULL SRID 0,
CONSTRAINT tab_const
CHECK(ST_Equals(c1, ST_GeomFromText("POINT(0 0)") = FALSE))
) ENGINE=InnoDB;
CREATE SPATIAL INDEX idx1 ON tab(c1) ;
SHOW CREATE TABLE tab;
Table	Create Table
tab	CREATE TABLE `tab` (
  `c1` point NOT NULL /*!80003 SRID 0 */,
  SPATIAL KEY `idx1` (`c1`),
  CONSTRAINT `tab_const` CHECK (st_equals(`c1`,(st_geomfromtext(_utf8mb4'POINT(0 0)') = false)))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ANALYZE TABLE tab;
Table	Op	Msg_type	Msg_text
test.tab	analyze	status	OK
SHOW INDEX FROM tab;
Table	Non_unique	Key_name	Seq_in_index	Column_name	Collation	Cardinality	Sub_part	Packed	Null	Index_type	Comment	Index_comment	Visible	Expression
tab	1	idx1	1	c1	A	0	32	NULL		SPATIAL			YES	NULL
set @g1 = ST_GeomFromText('POINT(-1 -2)');
SELECT ST_AsText(c1) FROM tab;
ST_AsText(c1)
DROP table tab;
create table `t1`(`a` geometry not null SRID 0,`b` linestring not null SRID 0,
spatial key (`b`)) engine=innodb;
insert into `t1` values(
point(1,1),
linestring(point(1,1),point(1,1))
);
insert into `t1` values
(
polygon(
linestring(point(1,1),point(1,1)),
linestring(point(1,1),point(11,1))
),
linestring(point(1,1),point(1,1))
);
ERROR 22023: Invalid GIS data provided to function polygon.
select 1 from t1 where st_intersects(
geometrycollection(point(1,-1)),b
);
1
drop table t1;
