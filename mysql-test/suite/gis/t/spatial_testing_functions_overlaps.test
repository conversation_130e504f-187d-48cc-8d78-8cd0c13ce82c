###############################################################################
#                                                                             #
# This test is aimed to focus on Functions for testing Relations between      #
# Geometry Objects.                                                           #
#                                                                             #
# These functions take 2 geometries as inpout parameters and return           #
# a qualitative or quantitavive realtion between themrealtion between them.   #
#                                                                             #
# Creation Date: 2013-12-16                                                   #
# Author : Horst Hunger                                                       #
#                                                                             #
###############################################################################


############################################################################################
# Creating the spatial objects                                                             #
############################################################################################

--echo #Creating the spatial Geometry object
USE test;

CREATE TABLE gis_geometrycollection (fid INTEGER NOT NULL PRIMARY KEY, g GEOMETRY);

############################################################################################
# Inserting the values specific to the spatial objects                                     #
############################################################################################

# Geometric elements constructing a star and some of its elememts as base
SET @star_elem_vertical= 'POLYGON((5 0,15 25,25 0,15 5,5 0))';
SET @star_elem_horizontal= 'POLYGON((25 0,0 15,30 15,22 10,25 0))';
SET @star_center= 'POINT(15 10)';
SET @star_top= 'POINT(15 25)';
SET @star_bottom_left= 'POINT(5 0)';
SET @star_bottom_right= 'POINT(25 0)';
SET @star_bottom_points= 'MULTIPOINT(5 0,25 0)';
SET @star_all_points= 'MULTIPOINT(5 0,25 0,15 10,15 25)';
SET @star_line_horizontal= 'LINESTRING(10 15,20 15)';
SET @star_line_vertical= 'LINESTRING(15 5,15 25)';
SET @star_top_to_center= 'LINESTRING(15 25,15 10)';
SET @star_lines_near_horizontal= 'MULTILINESTRING((25 0,0 15,15 30,0 5))';
SET @star_lines_near_vertical= 'MULTILINESTRING((0 5,15 25,0 25))';
SET @star= 'POLYGON((5 0,7 10,0 15,10 15,15 25,20 15,30 15,22 10,25 0,15 5,5 0))';
SET @star_elem_vertical_val= '((5 0,15 25,25 0,15 5,5 0))';
SET @star_elem_horizontal_val= '((25 0,0 15,30 15,22 10,25 0))';
SET @star_of_elems='MULTIPOLYGON(((5 0,15 25,25 0,15 5,5 0)),((25 0,0 15,30 15,22 10,25 0)))';
SET @star_collection_elems='GEOMETRYCOLLECTION(MULTIPOLYGON(((5 0,15 25,25 0,15 5,5 0)),((25 0,0 15,30 15,22 10,25 0))),POLYGON((5 0,7 10,0 15,10 15,15 25,20 15,30 15,22 10,25 0,15 5,5 0)),LINESTRING(15 25,15 10),MULTIPOINT(5 0,25 0),POINT(15 25))';
SET @star_collection_multilinestr='GEOMETRYCOLLECTION(MULTIPOLYGON(((5 0,15 25,25 0,15 5,5 0)),((25 0,0 15,30 15,22 10,25 0))),POLYGON((5 0,7 10,0 15,10 15,15 25,20 15,30 15,22 10,25 0,15 5,5 0)),MULTILINESTRING((25 0,0 15,15 30,0 5)),LINESTRING(15 25,15 10),MULTIPOINT(5 0,25 0),POINT(15 25))';

# Star identical to the base star 
SET @star_elem_vertical_1= 'POLYGON((5 0,15 25,25 0,15 5,5 0))';
SET @star_elem_horizontal_1= 'POLYGON((25 0,0 15,30 15,22 10,25 0))';
SET @star_center_1= 'POINT(15 10)';
SET @star_top_1= 'POINT(15 25)';
SET @star_bottom_left_1= 'POINT(5 0)';
SET @star_bottom_right_1= 'POINT(25 0)';
SET @star_bottom_points_1= 'MULTIPOINT(5 0,25 0)';
SET @star_all_points_1= 'MULTIPOINT(5 0,25 0,15 10,15 25)';
SET @star_line_horizontal_1= 'LINESTRING(10 15,20 15)';
SET @star_line_vertical_1= 'LINESTRING(15 5,15 25)';
SET @star_top_to_center_1= 'LINESTRING(15 25,15 10)';
SET @star_lines_near_horizontal_1= 'MULTILINESTRING((25 0,0 15,15 30,0 5))';
SET @star_lines_near_vertical_1= 'MULTILINESTRING((0 5,15 25,0 25))';
SET @star_1= 'POLYGON((5 0,7 10,0 15,10 15,15 25,20 15,30 15,22 10,25 0,15 5,5 0))';
SET @star_elem_vertical_val_1= '((5 0,15 25,25 0,15 5,5 0))';
SET @star_elem_horizontal_val_1= '((25 0,0 15,30 15,22 10,25 0))';
SET @star_of_elems_1='MULTIPOLYGON(((5 0,15 25,25 0,15 5,5 0)),((25 0,0 15,30 15,22 10,25 0)))';
SET @star_collection_elems_1='GEOMETRYCOLLECTION(MULTIPOLYGON(((5 0,15 25,25 0,15 5,5 0)),((25 0,0 15,30 15,22 10,25 0))),POLYGON((5 0,7 10,0 15,10 15,15 25,20 15,30 15,22 10,25 0,15 5,5 0)),LINESTRING(15 25,15 10),MULTIPOINT(5 0,25 0),POINT(15 25))';

# Star like the base star, but shifted to right by 10 on X to overlap base star
SET @star_elem_vertical_2= 'POLYGON((15 0,25 25,35 0,25 5,15 0))';
SET @star_elem_horizontal_2= 'POLYGON((35 0,10 15,40 15,32 10,35 0))';
SET @star_center_2= 'POINT(25 10)';
SET @star_top_2= 'POINT(25 25)';
SET @star_bottom_left_2= 'POINT(15 0)';
SET @star_bottom_right_2= 'POINT(35 0)';
SET @star_bottom_points_2= 'MULTIPOINT(15 0,35 0)';
SET @star_all_points_2= 'MULTIPOINT(15 0,35 0,35 10,25 25)';
SET @star_line_horizontal_2= 'LINESTRING(20 15,30 15)';
SET @star_line_vertical_2= 'LINESTRING(25 5,25 25)';
SET @star_top_to_center_2= 'LINESTRING(25 25,25 10)';
SET @star_lines_near_horizontal_2= 'MULTILINESTRING((35 0,10 15,25 30,10 5))';
SET @star_lines_near_vertical_2= 'MULTILINESTRING((10 5,25 25,10 25))';
SET @star_2= 'POLYGON((15 0,17 10,10 15,20 15,25 25,30 15,40 15,32 10,35 0,25 5,15 0))';
SET @star_elem_vertical_val_2= '((15 0,25 25,35 0,25 5,15 0))';
SET @star_elem_horizontal_val_2= '((35 0,10 15,40 15,32 10,35 0))';
SET @star_of_elems_2='MULTIPOLYGON(((15 0,25 25,35 0,25 5,15 0)),((35 0,10 15,40 15,32 10,35 0)))';
SET @star_collection_elems_2='GEOMETRYCOLLECTION(MULTIPOLYGON(((15 0,25 25,35 0,25 5,15 0)),((35 0,10 15,40 15,32 10,35 0))),POLYGON((15 0,17 10,10 15,20 15,25 25,30 15,40 15,32 10,35 0,25 5,15 0)),LINESTRING(25 25,25 10),MULTIPOINT(15 0,35 0),POINT(25 25))';

# Star like the base star, but shifted to right by 60 on X to be completly separate
SET @star_elem_vertical_3= 'POLYGON((65 0,75 25,85 0,75 5,65 0))';
SET @star_elem_horizontal_3= 'POLYGON((85 0,60 15,90 15,82 10,85 0))';
SET @star_center_3= 'POINT(75 10)';
SET @star_top_3= 'POINT(75 25)';
SET @star_bottom_left_3= 'POINT(65 0)';
SET @star_bottom_right_3= 'POINT(85 0)';
SET @star_bottom_points_3= 'MULTIPOINT(65 0,85 0)';
SET @star_all_points_3= 'MULTIPOINT(65 0,85 0,75 10,75 25)';
SET @star_line_horizontal_3= 'LINESTRING(70 15,80 15)';
SET @star_line_vertical_3= 'LINESTRING(75 5,75 25)';
SET @star_top_to_center_3= 'LINESTRING(75 25,75 10)';
SET @star_lines_near_horizontal_3= 'MULTILINESTRING((85 0,60 15,85 30,60 5))';
SET @star_lines_near_vertical_3= 'MULTILINESTRING((60 5,75 25,60 25))';
SET @star_3= 'POLYGON((65 0,67 10,60 15,70 15,75 25,80 15,90 15,82 10,85 0,75 5,65 0))';
SET @star_elem_vertical_val_3= '((65 0,75 25,85 0,75 5,65 0))';
SET @star_elem_horizontal_val_3= '((85 0,60 15,90 15,82 10,85 0))';
SET @star_of_elems_3='MULTIPOLYGON(((65 0,75 25,85 0,75 5,65 0)),((85 0,60 15,90 15,82 10,85 0)))';
SET @star_collection_elems_3='GEOMETRYCOLLECTION(MULTIPOLYGON(((65 0,75 25,85 0,75 5,65 0)),((85 0,60 15,90 15,82 10,85 0))),POLYGON((65 0,67 10,60 15,70 15,75 25,80 15,90 15,82 10,85 0,75 5,65 0)),LINESTRING(75 25,75 10),MULTIPOINT(65 0,85 0),POINT(75 25))';

--echo #INSERT base star
--disable_warnings
INSERT INTO gis_geometrycollection VALUES
 (100,ST_GEOMFROMTEXT(@star)),
 (101,ST_GEOMFROMTEXT(@star_elem_vertical)),
 (102,ST_GEOMFROMTEXT(@star_elem_horizontal)),
 (103,ST_GEOMFROMTEXT(@star_of_elems)),
 (104,ST_GEOMFROMTEXT(@star_top)),
 (105,ST_GEOMFROMTEXT(@star_center)),
 (106,ST_GEOMFROMTEXT(@star_bottom_left)),
 (107,ST_GEOMFROMTEXT(@star_bottom_right)),
 (108,ST_GEOMFROMTEXT(@star_bottom_points)),
 (109,ST_GEOMFROMTEXT(@star_all_points)),
 (110,ST_GEOMFROMTEXT(@star_line_horizontal)),
 (111,ST_GEOMFROMTEXT(@star_line_vertical)),
 (112,ST_GEOMFROMTEXT(@star_top_to_center)),
 (113,ST_GEOMFROMTEXT(@star_lines_near_horizontal)),
 (114,ST_GEOMFROMTEXT(@star_lines_near_vertical)),
 (115,ST_GEOMFROMTEXT(@star_collection_elems));
--enable_warnings

--echo #INSERT identical (to base) star
--disable_warnings
INSERT INTO gis_geometrycollection VALUES
 (200,ST_GEOMFROMTEXT(@star_1)),
 (201,ST_GEOMFROMTEXT(@star_elem_vertical_1)),
 (202,ST_GEOMFROMTEXT(@star_elem_horizontal_1)),
 (203,ST_GEOMFROMTEXT(@star_of_elems_1)),
 (204,ST_GEOMFROMTEXT(@star_top_1)),
 (205,ST_GEOMFROMTEXT(@star_center_1)),
 (206,ST_GEOMFROMTEXT(@star_bottom_left_1)),
 (207,ST_GEOMFROMTEXT(@star_bottom_right_1)),
 (208,ST_GEOMFROMTEXT(@star_bottom_points_1)),
 (209,ST_GEOMFROMTEXT(@star_all_points_1)),
 (210,ST_GEOMFROMTEXT(@star_line_horizontal_1)),
 (211,ST_GEOMFROMTEXT(@star_line_vertical_1)),
 (212,ST_GEOMFROMTEXT(@star_top_to_center_1)),
 (213,ST_GEOMFROMTEXT(@star_lines_near_horizontal_1)),
 (214,ST_GEOMFROMTEXT(@star_lines_near_vertical_1)),
 (215,ST_GEOMFROMTEXT(@star_collection_elems_1));
--enable_warnings

--echo #INSERT overlapping star
--disable_warnings
INSERT INTO gis_geometrycollection VALUES
 (300,ST_GEOMFROMTEXT(@star_2)),
 (301,ST_GEOMFROMTEXT(@star_elem_vertical_2)),
 (302,ST_GEOMFROMTEXT(@star_elem_horizontal_2)),
 (303,ST_GEOMFROMTEXT(@star_of_elems_2)),
 (304,ST_GEOMFROMTEXT(@star_top_2)),
 (305,ST_GEOMFROMTEXT(@star_center_2)),
 (306,ST_GEOMFROMTEXT(@star_bottom_left_2)),
 (307,ST_GEOMFROMTEXT(@star_bottom_right_2)),
 (308,ST_GEOMFROMTEXT(@star_bottom_points_2)),
 (309,ST_GEOMFROMTEXT(@star_all_points_2)),
 (310,ST_GEOMFROMTEXT(@star_line_horizontal_2)),
 (311,ST_GEOMFROMTEXT(@star_line_vertical_2)),
 (312,ST_GEOMFROMTEXT(@star_top_to_center_2)),
 (313,ST_GEOMFROMTEXT(@star_lines_near_horizontal_2)),
 (314,ST_GEOMFROMTEXT(@star_lines_near_vertical_2)),
 (315,ST_GEOMFROMTEXT(@star_collection_elems_2));
--enable_warnings

--echo #INSERT separate star
--disable_warnings
INSERT INTO gis_geometrycollection VALUES
 (400,ST_GEOMFROMTEXT(@star_3)),
 (401,ST_GEOMFROMTEXT(@star_elem_vertical_3)),
 (402,ST_GEOMFROMTEXT(@star_elem_horizontal_3)),
 (403,ST_GEOMFROMTEXT(@star_of_elems_3)),
 (404,ST_GEOMFROMTEXT(@star_top_3)),
 (405,ST_GEOMFROMTEXT(@star_center_3)),
 (406,ST_GEOMFROMTEXT(@star_bottom_left_3)),
 (407,ST_GEOMFROMTEXT(@star_bottom_right_3)),
 (408,ST_GEOMFROMTEXT(@star_bottom_points_3)),
 (409,ST_GEOMFROMTEXT(@star_all_points_3)),
 (410,ST_GEOMFROMTEXT(@star_line_horizontal_3)),
 (411,ST_GEOMFROMTEXT(@star_line_vertical_3)),
 (412,ST_GEOMFROMTEXT(@star_top_to_center_3)),
 (413,ST_GEOMFROMTEXT(@star_lines_near_horizontal_3)),
 (414,ST_GEOMFROMTEXT(@star_lines_near_vertical_3)),
 (415,ST_GEOMFROMTEXT(@star_collection_elems_3));
--enable_warnings

CREATE TABLE gis_geometrycollection_2 SELECT fid as fid2,g as g2 FROM gis_geometrycollection;

--echo #Checking the integrity of the above create/insert statements 

--echo #64 rows. 
SELECT count(ST_ASTEXT(g) != 'NULL') FROM gis_geometrycollection;
--echo #64 rows. 
SELECT count(ST_ASTEXT(g2) != 'NULL') FROM gis_geometrycollection_2;

############################################################################################
# Checking the spatial testing functions                                                   #
############################################################################################

--echo #####################################################################################
--echo # MBROVERLAPS(g1,g2)
--echo #####################################################################################

--echo #====================================================================================
--echo # point,point
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_center),ST_GEOMFROMTEXT(@star_center));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_center));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=105;

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POINT(0 0)'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POINT(10 0)'));

--echo #====================================================================================
--echo # point,multipoint
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_bottom_left),ST_GEOMFROMTEXT(@star_bottom_points));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_bottom_points));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=106;

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0)'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,10 0)'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'));

--echo #====================================================================================
--echo # point,linestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_top_to_center));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_line_horizontal));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=104;

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0,20 0)'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'));

--echo #====================================================================================
--echo # point,multilinestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_lines_near_vertical));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_lines_near_horizontal));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=104;

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,10 0))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(10 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0),(5 0,10 0))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0,10 0))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0),(5 0,10 0))'));

--echo #====================================================================================
--echo # point,polygon
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_elem_horizontal));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star)) FROM gis_geometrycollection WHERE fid=104;

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'));

--echo #====================================================================================
--echo # point,multipolygon
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_of_elems));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(-0.01 0)'),ST_GEOMFROMTEXT(@star_of_elems));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=104;

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)),((10 10,10 20,20 20,20 10,10 10)))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))'));

--echo #====================================================================================
--echo # point,geometrycollection
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(-0.01 0)'),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=104;

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_collection_multilinestr));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(5 5))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(0 0,5 5,10 10),GEOMETRYCOLLECTION())'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,10 0,10 10,0 10,0 0))))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,10 0,10 10,0 10,0 0))))'));

--echo #====================================================================================
--echo # multipoint,point
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_top));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_bottom_points),ST_GEOMFROMTEXT(@star_center));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_left)) FROM gis_geometrycollection WHERE fid=108;

--echo #====================================================================================
--echo # multipoint,multipoint
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_bottom_points));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_bottom_points),ST_GEOMFROMTEXT(@star_all_points));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=208;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=308;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=408;

--echo #====================================================================================
--echo # multipoint,linestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_top_to_center));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=109;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=209;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=309;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=409;

--echo #====================================================================================
--echo # multipoint,multilinestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_lines_near_vertical));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_lines_near_horizontal));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_horizontal)) FROM gis_geometrycollection WHERE fid=109;

--echo #====================================================================================
--echo # multipoint,polygon
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_elem_horizontal));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=109;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=209;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=309;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=409;

--echo #====================================================================================
--echo # multipoint,multipolygon
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 30,30 25)'),ST_GEOMFROMTEXT(@star_of_elems));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_of_elems));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=109;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=209;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=309;

--echo #====================================================================================
--echo # multipoint,geometrycollection
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 30,30 25)'),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=109;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=209;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=309;

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_collection_multilinestr));

--echo #====================================================================================
--echo # linestring,point
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_center));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_bottom_left));

SELECT MBROVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=110 and fid2=105;

--echo #====================================================================================
--echo # linestring,multipoint
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT(@star_all_points));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)')) FROM gis_geometrycollection WHERE fid=111;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)')) FROM gis_geometrycollection WHERE fid=311;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)')) FROM gis_geometrycollection WHERE fid=411;

--echo #====================================================================================
--echo # linestring,linestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT(@star_top_to_center));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_line_vertical));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=111;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=311;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=411;

--echo #====================================================================================
--echo # linestring,multilinestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_lines_near_horizontal));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_lines_near_vertical));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_horizontal)) FROM gis_geometrycollection WHERE fid=110;

--echo #====================================================================================
--echo # linestring,polygon
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT('POLYGON((11 15,19 15,19 10,11 15))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal_2));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal_3));

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT('POLYGON((11 15,19 15,11 15))')) FROM gis_geometrycollection WHERE fid=110;

--echo #====================================================================================
--echo # linestring,multipolygon
--echo #====================================================================================
--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT('MULTIPOLYGON(((11 15,19 15,11 15)),((25 0,0 15,25 0)))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems_2));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems_3));

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT('MULTIPOLYGON(((11 15,19 15,11 15)),((25 0,0 15,25 0)))')) FROM gis_geometrycollection WHERE fid=110;

--echo #====================================================================================
--echo # linestring,geometrycollection
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems_2));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems_3));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=110;

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_multilinestr));

--echo #====================================================================================
--echo # multilinestring,point
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_top));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center_2));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center_3));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=114;

--echo #====================================================================================
--echo # multilinestring,multipoint
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_bottom_points));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_all_points));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=114;

SELECT MBROVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=114 and fid2=108;

--echo #====================================================================================
--echo # multilinestring,linestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_vertical));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal_2));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal_3));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=114;

--echo #====================================================================================
--echo # multilinestring,multilinestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT('MULTILINESTRING((0 5,15 25,0 15))'));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal_3));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT('MULTILINESTRING((0 5,15 25,0 15))')) FROM gis_geometrycollection WHERE fid=114;

--echo #====================================================================================
--echo # multilinestring,polygon
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal_2));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal_3));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=114;

--echo #====================================================================================
--echo # multilinestring,multipolygon
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('MULTILINESTRING((0 25,25 0,25 30))'),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_of_elems));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=114;

--echo #====================================================================================
--echo # multilinestring,geometrycollection
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('MULTILINESTRING((0 25,25 0,25 30))'),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=114;

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_collection_multilinestr));

--echo #====================================================================================
--echo # polygon,point
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_center));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT('POINT(0 0)'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=100;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=200;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=300;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=400;

--echo #====================================================================================
--echo # polygon,multipoint
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_all_points));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT('MULTIPOINT(0 0,30 25)'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=100;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=300;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=400;

--echo #====================================================================================
--echo # polygon,linestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_line_vertical));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_line_horizontal));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=100;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=300;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=400;

SELECT MBROVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=100 and fid2=111;

--echo #====================================================================================
--echo # polygon,multilinestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_lines_near_vertical));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=100;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=300;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=400;

--echo #====================================================================================
--echo # polygon,polygon
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=100;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=300;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=400;

--echo #====================================================================================
--echo # polygon,multipolygon
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_of_elems));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_of_elems));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=100;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=300;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=400;

--echo #====================================================================================
--echo # polygon,geometrycollection
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=100;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=300;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=400;

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_collection_multilinestr));

--echo #====================================================================================
--echo # multipolygon,point
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_top));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POINT(30 30)'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=103;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=403;

--echo #====================================================================================
--echo # multipolygon,multipoint
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_bottom_points));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOINT(30 30,0 35)'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=103;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=403;

--echo #====================================================================================
--echo # multipolygon,linestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_top_to_center));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('LINESTRING(30 30,0 35)'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=103;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=403;

--echo #====================================================================================
--echo # multipolygon,multilinestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_lines_near_vertical));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTILINESTRING((0 30,30 30,0 35))'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=103;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=403;

--echo #====================================================================================
--echo # multipolygon,polygon
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POLYGON((0 0,15 25,35 0,0 0,0 0))'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=103;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=303;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=403;

--echo #====================================================================================
--echo # multipolygon,multipolygon
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_of_elems));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,15 25,35 0,0 0,0 0),(5 0,15 25,25 0,15 5,5 0)))'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=103;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=303;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=403;

SELECT MBROVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;

--echo #====================================================================================
--echo # multipolygon,geometrycollection
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=103;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=303;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=403;

SELECT MBROVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=115;

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_collection_multilinestr));

--echo #====================================================================================
--echo # geometrycollection,point
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_top));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POINT(30 30)'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=115;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=415;

--echo #====================================================================================
--echo # geometrycollection,multipoint
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_bottom_points));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOINT(30 30,0 35)'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=115;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=415;

--echo #====================================================================================
--echo # geometrycollection,linestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_top_to_center));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('LINESTRING(30 30,0 35)'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=115;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=415;

--echo #====================================================================================
--echo # geometrycollection,multilinestring
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_lines_near_vertical));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTILINESTRING((0 30,30 30,0 35))'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=115;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=415;

--echo #====================================================================================
--echo # geometrycollection,polygon
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POLYGON((0 0,15 25,35 0,0 0,0 0))'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=115;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=315;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=415;

--echo #====================================================================================
--echo # geometrycollection,multipolygon
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_of_elems));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,15 25,35 0,0 0,0 0),(5 0,15 25,25 0,15 5,5 0)))'));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=115;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=315;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=415;

SELECT MBROVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=115 and fid2=115;

--echo #====================================================================================
--echo # geometrycollection,geometrycollection
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=115;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=315;

SELECT MBROVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=415;

SELECT MBROVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=115 and fid2=115;

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_collection_multilinestr));

--echo #####################################################################################
--echo # ST_OVERLAPS(g1,g2)
--echo #####################################################################################

--echo #====================================================================================
--echo # point,point
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_center),ST_GEOMFROMTEXT(@star_center));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_center));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=105;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POINT(0 0)'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POINT(10 0)'));

--echo #====================================================================================
--echo # point,multipoint
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_bottom_left),ST_GEOMFROMTEXT(@star_bottom_points));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_bottom_points));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=106;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0)'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,10 0)'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'));

--echo #====================================================================================
--echo # point,linestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_top_to_center));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_line_horizontal));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=104;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0,20 0)'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'));

--echo #====================================================================================
--echo # point,multilinestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_lines_near_vertical));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_lines_near_horizontal));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=104;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,10 0))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(10 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0),(5 0,10 0))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0,10 0))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0),(5 0,10 0))'));

--echo #====================================================================================
--echo # point,polygon
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_elem_horizontal));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star)) FROM gis_geometrycollection WHERE fid=104;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'));

--echo #====================================================================================
--echo # point,multipolygon
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_of_elems));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(-0.01 0)'),ST_GEOMFROMTEXT(@star_of_elems));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=104;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)),((10 10,10 20,20 20,20 10,10 10)))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))'));

--echo #====================================================================================
--echo # point,geometrycollection
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(-0.01 0)'),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=104;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_collection_multilinestr));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(5 5))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(0 0,5 5,10 10),GEOMETRYCOLLECTION())'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,10 0,10 10,0 10,0 0))))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,10 0,10 10,0 10,0 0))))'));

--echo #====================================================================================
--echo # multipoint,point
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_top));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_bottom_points),ST_GEOMFROMTEXT(@star_center));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_left)) FROM gis_geometrycollection WHERE fid=108;

--echo #====================================================================================
--echo # multipoint,multipoint
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_bottom_points));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_bottom_points),ST_GEOMFROMTEXT(@star_all_points));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=208;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=308;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=408;

--echo #====================================================================================
--echo # multipoint,linestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_top_to_center));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=109;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=209;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=309;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=409;

--echo #====================================================================================
--echo # multipoint,multilinestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_lines_near_vertical));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_lines_near_horizontal));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_horizontal)) FROM gis_geometrycollection WHERE fid=109;

--echo #====================================================================================
--echo # multipoint,polygon
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_elem_horizontal));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=109;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=209;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=309;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=409;

--echo #====================================================================================
--echo # multipoint,multipolygon
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 30,30 25)'),ST_GEOMFROMTEXT(@star_of_elems));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_of_elems));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=109;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=209;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=309;

--echo #====================================================================================
--echo # multipoint,geometrycollection
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 30,30 25)'),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=109;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=209;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=309;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_collection_multilinestr));

--echo #====================================================================================
--echo # linestring,point
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_center));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_bottom_left));

SELECT ST_OVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=110 and fid2=105;

--echo #====================================================================================
--echo # linestring,multipoint
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT(@star_all_points));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)')) FROM gis_geometrycollection WHERE fid=111;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)')) FROM gis_geometrycollection WHERE fid=311;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)')) FROM gis_geometrycollection WHERE fid=411;

--echo #====================================================================================
--echo # linestring,linestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT(@star_top_to_center));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_line_vertical));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=111;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=311;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=411;

--echo #====================================================================================
--echo # linestring,multilinestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_lines_near_horizontal));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_lines_near_vertical));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_horizontal)) FROM gis_geometrycollection WHERE fid=110;

--echo #====================================================================================
--echo # linestring,polygon
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT('POLYGON((11 15,19 15,19 10,11 15))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal_2));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal_3));

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT('POLYGON((11 15,19 15,11 15))')) FROM gis_geometrycollection WHERE fid=110;

--echo #====================================================================================
--echo # linestring,multipolygon
--echo #====================================================================================
--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT('MULTIPOLYGON(((11 15,19 15,11 15)),((25 0,0 15,25 0)))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems_2));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems_3));

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT('MULTIPOLYGON(((11 15,19 15,11 15)),((25 0,0 15,25 0)))')) FROM gis_geometrycollection WHERE fid=110;

--echo #====================================================================================
--echo # linestring,geometrycollection
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems_2));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems_3));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=110;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_multilinestr));

--echo #====================================================================================
--echo # multilinestring,point
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_top));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center_2));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center_3));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=114;

--echo #====================================================================================
--echo # multilinestring,multipoint
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_bottom_points));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_all_points));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=114;

SELECT ST_OVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=114 and fid2=108;

--echo #====================================================================================
--echo # multilinestring,linestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_vertical));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal_2));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal_3));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=114;

--echo #====================================================================================
--echo # multilinestring,multilinestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT('MULTILINESTRING((0 5,15 25,0 15))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal_3));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT('MULTILINESTRING((0 5,15 25,0 15))')) FROM gis_geometrycollection WHERE fid=114;

--echo #====================================================================================
--echo # multilinestring,polygon
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal_2));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal_3));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=114;

--echo #====================================================================================
--echo # multilinestring,multipolygon
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('MULTILINESTRING((0 25,25 0,25 30))'),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_of_elems));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=114;

--echo #====================================================================================
--echo # multilinestring,geometrycollection
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('MULTILINESTRING((0 25,25 0,25 30))'),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=114;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_collection_multilinestr));

--echo #====================================================================================
--echo # polygon,point
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_center));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT('POINT(0 0)'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=100;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=200;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=300;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=400;

--echo #====================================================================================
--echo # polygon,multipoint
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_all_points));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT('MULTIPOINT(0 0,30 25)'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=100;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=300;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=400;

--echo #====================================================================================
--echo # polygon,linestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_line_vertical));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_line_horizontal));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=100;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=300;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=400;

SELECT ST_OVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=100 and fid2=111;

--echo #====================================================================================
--echo # polygon,multilinestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_lines_near_vertical));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=100;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=300;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=400;

--echo #====================================================================================
--echo # polygon,polygon
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=100;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=300;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=400;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2),(6 6,6 8,8 8,8 6,6 6))'),
                   ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'));

--echo #====================================================================================
--echo # polygon,multipolygon
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_of_elems));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_of_elems));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=100;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=300;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=400;

--echo #====================================================================================
--echo # polygon,geometrycollection
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=100;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=300;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=400;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_collection_multilinestr));

--echo #====================================================================================
--echo # multipolygon,point
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_top));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POINT(30 30)'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=103;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=403;

--echo #====================================================================================
--echo # multipolygon,multipoint
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_bottom_points));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOINT(30 30,0 35)'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=103;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=403;

--echo #====================================================================================
--echo # multipolygon,linestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_top_to_center));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('LINESTRING(30 30,0 35)'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=103;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=403;

--echo #====================================================================================
--echo # multipolygon,multilinestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_lines_near_vertical));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTILINESTRING((0 30,30 30,0 35))'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=103;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=403;

--echo #====================================================================================
--echo # multipolygon,polygon
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POLYGON((0 0,15 25,35 0,0 0,0 0))'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=103;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=303;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=403;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
                   ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
                   ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0))'));

--echo #====================================================================================
--echo # multipolygon,multipolygon
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_of_elems));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,15 25,35 0,0 0,0 0),(5 0,15 25,25 0,15 5,5 0)))'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=103;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=303;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=403;

SELECT ST_OVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;

--echo #====================================================================================
--echo # multipolygon,geometrycollection
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=103;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=303;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=403;

SELECT ST_OVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=115;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_collection_multilinestr));

--echo #====================================================================================
--echo # geometrycollection,point
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_top));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POINT(30 30)'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=115;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=415;

--echo #====================================================================================
--echo # geometrycollection,multipoint
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_bottom_points));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOINT(30 30,0 35)'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=115;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=415;

--echo #====================================================================================
--echo # geometrycollection,linestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_top_to_center));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('LINESTRING(30 30,0 35)'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=115;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=415;

--echo #====================================================================================
--echo # geometrycollection,multilinestring
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_lines_near_vertical));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTILINESTRING((0 30,30 30,0 35))'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=115;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=415;

--echo #====================================================================================
--echo # geometrycollection,polygon
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_elem_vertical));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POLYGON((0 0,15 25,35 0,0 0,0 0))'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=115;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=315;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=415;

--echo #====================================================================================
--echo # geometrycollection,multipolygon
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_of_elems));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,15 25,35 0,0 0,0 0),(5 0,15 25,25 0,15 5,5 0)))'));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=115;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=315;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=415;

SELECT ST_OVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=115 and fid2=115;

--echo #====================================================================================
--echo # geometrycollection,geometrycollection
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_collection_elems));

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=115;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=315;

SELECT ST_OVERLAPS(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=415;

SELECT ST_OVERLAPS(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=115 and fid2=115;

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_collection_multilinestr));

--echo #####################################################################################
--echo # Testing with 2 geometries of same SRID 
--echo #####################################################################################

--echo #====================================================================================
--echo # MBROVERLAPS(g1,g2)
--echo #====================================================================================
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,0),ST_GEOMFROMTEXT(@star_center,0));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4145));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,2000),ST_GEOMFROMTEXT(@star_center,2000));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,4326),ST_GEOMFROMTEXT(@star_center,4326));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,4135),ST_GEOMFROMTEXT(@star_center,4135));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4145));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,2000),ST_GEOMFROMTEXT(@star_center,2000));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,4326),ST_GEOMFROMTEXT(@star_center,4326));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,4135),ST_GEOMFROMTEXT(@star_center,4135));

--error ER_DATA_OUT_OF_RANGE
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,-1),ST_GEOMFROMTEXT(@star_center,-1));

--error ER_DATA_OUT_OF_RANGE
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,-1024),ST_GEOMFROMTEXT(@star_center,-1024));

--error ER_DATA_OUT_OF_RANGE
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,4294967295000),ST_GEOMFROMTEXT(@star_center,4294967295000));

--echo #====================================================================================
--echo # ST_OVERLAPS(g1,g2)
--echo #====================================================================================
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,0),ST_GEOMFROMTEXT(@star_center,0));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4145));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,2000),ST_GEOMFROMTEXT(@star_center,2000));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,4326),ST_GEOMFROMTEXT(@star_center,4326));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,4135),ST_GEOMFROMTEXT(@star_center,4135));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4145));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,2000),ST_GEOMFROMTEXT(@star_center,2000));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,4326),ST_GEOMFROMTEXT(@star_center,4326));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,4135),ST_GEOMFROMTEXT(@star_center,4135));

--error ER_DATA_OUT_OF_RANGE
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,-1),ST_GEOMFROMTEXT(@star_center,-1));

--error ER_DATA_OUT_OF_RANGE
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,-1024),ST_GEOMFROMTEXT(@star_center,-1024));

--error ER_DATA_OUT_OF_RANGE
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,4294967295000),ST_GEOMFROMTEXT(@star_center,4294967295000));

--echo #####################################################################################
--echo # Invalid function calls
--echo #####################################################################################

--echo #====================================================================================
--echo # ST_OVERLAPS(g1,g2)
--echo #====================================================================================
--error ER_GIS_DIFFERENT_SRIDS
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_center,4326));

--error ER_GIS_DIFFERENT_SRIDS
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4326));

--error ER_GIS_DIFFERENT_SRIDS
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4326));

SELECT ST_OVERLAPS(NULL,ST_GEOMFROMTEXT(@star_top));

SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),NULL);

SELECT ST_OVERLAPS(g,NULL) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;

SELECT ST_OVERLAPS(NULL,NULL);

SELECT ST_OVERLAPS(fid,NULL) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(fid,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),fid) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(a 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(! 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_PARSE_ERROR
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT('!' 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POINT(12,34 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('LINESTRING(a 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('LINESTRING(0 0,! 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_PARSE_ERROR
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('LINESTRING('!' 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('LINESTRING(12,34 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((a 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,! 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_PARSE_ERROR
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON(('!' 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((12,34 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((12,34 0,10 10,10 0,0 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_PARSE_ERROR
SELECT ST_OVERLAPS(,);

--error ER_PARSE_ERROR
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),);

--error ER_PARSE_ERROR
SELECT ST_OVERLAPS(,ST_GEOMFROMTEXT(@star_top));

--echo #====================================================================================
--echo # MBROVERLAPS(g1,g2)
--echo #====================================================================================
--error ER_GIS_DIFFERENT_SRIDS
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_center,4326));

--error ER_GIS_DIFFERENT_SRIDS
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4326));

--error ER_GIS_DIFFERENT_SRIDS
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4326));

SELECT MBROVERLAPS(NULL,ST_GEOMFROMTEXT(@star_top));

SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),NULL);

SELECT MBROVERLAPS(g,NULL) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;

SELECT MBROVERLAPS(NULL,NULL);

SELECT MBROVERLAPS(fid,NULL) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(fid,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),fid) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(a 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(! 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_PARSE_ERROR
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT('!' 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POINT(12,34 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('LINESTRING(a 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('LINESTRING(0 0,! 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_PARSE_ERROR
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('LINESTRING('!' 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('LINESTRING(12,34 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POLYGON((a 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,! 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_PARSE_ERROR
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POLYGON(('!' 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POLYGON((12,34 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_GIS_INVALID_DATA
SELECT MBROVERLAPS(ST_GEOMFROMTEXT('POLYGON((12,34 0,10 10,10 0,0 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));

--error ER_PARSE_ERROR
SELECT MBROVERLAPS(,);

--error ER_PARSE_ERROR
SELECT MBROVERLAPS(ST_GEOMFROMTEXT(@star_of_elems),);

--error ER_PARSE_ERROR
SELECT MBROVERLAPS(,ST_GEOMFROMTEXT(@star_top));

# Test with all combinations of geometry types, including types inside geometry collections

CREATE TABLE t1 (g GEOMETRY NOT NULL);

INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('POINT(1 1)'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('LINESTRING(1 1, 2 2)'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('POLYGON((0 0, 1 0, 1 1, 0 1, 0 0))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
    'POLYGON((0 0, 10 0, 10 10, 0 10, 0 0), (4 4, 6 4, 6 6, 4 6, 4 4))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('MULTIPOINT(1 1, 2 2)'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('MULTILINESTRING((1 1, 2 2),'
    '(3 3, 4 4))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
    'MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)),((5 5, 10 5, 10 10, 5 10, 5 5)))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
    'MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), ((5 5, 10 5, 10 10, 5 10, 5 5), '
    '(6 6, 7 6, 7 7, 6 7, 6 6)))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
    'MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), ((1 1, 10 1, 10 10, 1 10, 1 1), '
    '(4 4, 6 4, 6 6, 4 6, 4 4)))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(1 1))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
    'GEOMETRYCOLLECTION(LINESTRING(1 1, 2 2))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0, 1 0, 1'
    ' 1, 0 1, 0 0)))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
    'GEOMETRYCOLLECTION(POLYGON((0 0, 10 0, 10 10, 0 10, 0 0), '
    '(4 4, 6 4, 6 6, 4 6, 4 4)))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
    'GEOMETRYCOLLECTION(MULTIPOINT(1 1, 2 2))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
    'GEOMETRYCOLLECTION(MULTILINESTRING((1 1, 2 2), (3 3, 4 4)))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
    'GEOMETRYCOLLECTION(MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), '
    '((5 5, 10 5, 10 10, 5 10, 5 5))))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
    'GEOMETRYCOLLECTION(MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), '
    '((5 5, 10 5, 10 10, 5 10, 5 5), (6 6, 7 6, 7 7, 6 7, 6 6))))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
    'GEOMETRYCOLLECTION(MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), '
    '((1 1, 10 1, 10 10, 1 10, 1 1), (4 4, 6 4, 6 6, 4 6, 4 4))))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
    'GEOMETRYCOLLECTION(POINT(1 1),LINESTRING(1 1, 2 2),'
    'POLYGON((0 0, 1 0, 1 1, 0 1, 0 0)),'
    'POLYGON((0 0, 10 0, 10 10, 0 10, 0 0), (4 4, 6 4, 6 6, 4 6, 4 4)),'
    'MULTIPOINT(1 1, 2 2),MULTILINESTRING((1 1, 2 2), (3 3, 4 4)),'
    'MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), ((5 5, 10 5, 10 10, 5 10, 5 5))),'
    'MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), ((5 5, 10 5, 10 10, 5 10, 5 5), '
    '(6 6, 7 6, 7 7, 6 7, 6 6))))'));

INSERT INTO t1 VALUES(
    GEOMETRYCOLLECTION(POINT(1,1),LINESTRING(POINT(1,1),POINT(2,2)),
    POLYGON(LINESTRING(POINT(0,0),POINT(1,0),POINT(1,1),POINT(0,1),POINT(0,0))),
    POLYGON(LINESTRING(POINT(0,0),POINT(10,0),POINT(10,10),POINT(0,10),
    POINT(0,0)),LINESTRING(POINT(4,4),POINT(6,4),POINT(6,6),POINT(4,6),
    POINT(4,4))),MULTIPOINT(POINT(1,1),POINT(2,2)),
    MultiLINESTRING(LINESTRING(POINT(1,1),POINT(2,2)),LINESTRING(POINT(3,3),
    POINT(4,4))),MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0),POINT(1,0),POINT(1,1),
    POINT(0,1),POINT(0,0))),POLYGON(LINESTRING(POINT(5,5),POINT(10,5),
    POINT(10,10),POINT(5,10),POINT(5,5)))),
    MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0),POINT(1,0),POINT(1,1),POINT(0,1),
    POINT(0,0))),POLYGON(LINESTRING(POINT(5,5),POINT(10,5),POINT(10,10),
    POINT(5,10),POINT(5,5)),LINESTRING(POINT(6,6),POINT(7,6),POINT(7,7),
    POINT(6,7),POINT(6,6)))),GEOMETRYCOLLECTION(POINT(1,1)),
    GEOMETRYCOLLECTION(LINESTRING(POINT(1,1),POINT(2,2))),
    GEOMETRYCOLLECTION(POLYGON(LINESTRING(POINT(0,0),POINT(1,0),POINT(1,1),
    POINT(0,1),POINT(0,0)))),GEOMETRYCOLLECTION(POLYGON(LINESTRING(POINT(0,0),
    POINT(10,0),POINT(10,10),POINT(0,10),POINT(0,0)),LINESTRING(POINT(4,4),
    POINT(6,4),POINT(6,6),POINT(4,6),POINT(4,4)))),
    GEOMETRYCOLLECTION(MULTIPOINT(POINT(1,1),POINT(2,2))),
    GEOMETRYCOLLECTION(MultiLINESTRING(LINESTRING(POINT(1,1),POINT(2,2)),
    LINESTRING(POINT(3,3),POINT(4,4)))),
    GEOMETRYCOLLECTION(MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0),POINT(1,0),
    POINT(1,1),POINT(0,1),POINT(0,0))),POLYGON(LINESTRING(POINT(5,5),POINT(10,5),
    POINT(10,10),POINT(5,10),POINT(5,5))))),
    GEOMETRYCOLLECTION(MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0),POINT(1,0),
    POINT(1,1),POINT(0,1),POINT(0,0))),POLYGON(LINESTRING(POINT(5,5),POINT(10,5),
    POINT(10,10),POINT(5,10),POINT(5,5)),LINESTRING(POINT(6,6),POINT(7,6),
    POINT(7,7),POINT(6,7),POINT(6,6)))))));
INSERT INTO t1 VALUES (
    GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1,1)),
    GEOMETRYCOLLECTION(LINESTRING(POINT(1,1),POINT(2,2))),
    GEOMETRYCOLLECTION(POLYGON(LINESTRING(POINT(1,1), POINT(10,1), POINT(10,10),
    POINT(1,10), POINT(1,1)), LINESTRING(POINT(4,4),POINT(6,4),POINT(6,6),
    POINT(4,6),POINT(4,4))))));

# We don't care about the result. We just want to put all parameter type
# combinations through the function.
--disable_result_log
SELECT ST_OVERLAPS(a.g, b.g) FROM t1 AS a JOIN t1 AS b;
SELECT MBROVERLAPS(a.g, b.g) FROM t1 AS a JOIN t1 AS b;

SELECT ST_OVERLAPS(ST_SRID(a.g, 4326), ST_SRID(b.g, 4326))
FROM t1 AS a JOIN t1 AS b;
SELECT MBROVERLAPS(ST_SRID(a.g, 4326), ST_SRID(b.g, 4326))
FROM t1 AS a JOIN t1 AS b;
--enable_result_log

--echo # clean up
DROP TABLE t1;
DROP TABLE gis_geometrycollection;
DROP TABLE gis_geometrycollection_2;

--echo #
--echo # WL#8579 Spatial Reference Systems
--echo #

--echo # SRID 0 (should pass)
SELECT ST_OVERLAPS(
  ST_GEOMFROMTEXT('POINT(0 0)', 0),
  ST_GEOMFROMTEXT('POINT(0 0)', 0)
);
SELECT MBROVERLAPS(
  ST_GEOMFROMTEXT('POINT(0 0)', 0),
  ST_GEOMFROMTEXT('POINT(0 0)', 0)
);

--echo # Projected SRS (should pass)
SELECT ST_OVERLAPS(
  ST_GEOMFROMTEXT('POINT(0 0)', 2000),
  ST_GEOMFROMTEXT('POINT(0 0)', 2000)
);
SELECT MBROVERLAPS(
  ST_GEOMFROMTEXT('POINT(0 0)', 2000),
  ST_GEOMFROMTEXT('POINT(0 0)', 2000)
);

--echo # Geographic SRS (should pass)
SELECT ST_OVERLAPS(
  ST_GEOMFROMTEXT('POINT(0 0)', 4326),
  ST_GEOMFROMTEXT('POINT(0 0)', 4326)
);
SELECT MBROVERLAPS(
  ST_GEOMFROMTEXT('POINT(0 0)', 4326),
  ST_GEOMFROMTEXT('POINT(0 0)', 4326)
);

--echo #
--echo # WL#10827 Ellipsoidal R-tree support functions
--echo #

SET @pt = ST_GeomFromText('POINT(0 0)', 4326);

# SRID=4326, long=0, lat=-91.
SET @pt_lat_minus_91 = x'E6100000010100000000000000000000000000000000C056C0';

# SRID=4326, long=0, lat=91.
SET @pt_lat_plus_91 = x'E6100000010100000000000000000000000000000000C05640';

# SRID=4326, long=-181, lat=0.
SET @pt_long_minus_181 = x'E610000001010000000000000000A066C00000000000000000';

# SRID=4326, long=181, lat=0.
SET @pt_long_plus_181 = x'E610000001010000000000000000A066400000000000000000';

--error ER_GEOMETRY_PARAM_LATITUDE_OUT_OF_RANGE
SELECT ST_Overlaps(@pt_lat_minus_91, @pt);
--error ER_GEOMETRY_PARAM_LATITUDE_OUT_OF_RANGE
SELECT ST_Overlaps(@pt_lat_plus_91, @pt);
--error ER_GEOMETRY_PARAM_LONGITUDE_OUT_OF_RANGE
SELECT ST_Overlaps(@pt_long_minus_181, @pt);
--error ER_GEOMETRY_PARAM_LONGITUDE_OUT_OF_RANGE
SELECT ST_Overlaps(@pt_long_plus_181, @pt);
--error ER_GEOMETRY_PARAM_LATITUDE_OUT_OF_RANGE
SELECT MBROverlaps(@pt_lat_minus_91, @pt);
--error ER_GEOMETRY_PARAM_LATITUDE_OUT_OF_RANGE
SELECT MBROverlaps(@pt_lat_plus_91, @pt);
--error ER_GEOMETRY_PARAM_LONGITUDE_OUT_OF_RANGE
SELECT MBROverlaps(@pt_long_minus_181, @pt);
--error ER_GEOMETRY_PARAM_LONGITUDE_OUT_OF_RANGE
SELECT MBROverlaps(@pt_long_plus_181, @pt);

CREATE TABLE t1 (
  id INT,
  c GEOMETRY NOT NULL SRID 0,
  g GEOMETRY AS (ST_SRID(c, 4326)) STORED NOT NULL SRID 4326,
  SPATIAL INDEX c_idx (c),
  SPATIAL INDEX g_idx (g)
);

INSERT INTO t1 (id, c) VALUES
  (0, ST_GeomFromText('POLYGON((0 0, 1 0, 1 1, 0 1, 0 0))')),
  (1, ST_GeomFromText('POLYGON((1 1, 2 1, 2 2, 1 2, 1 1))')),
  (2, ST_GeomFromText('POLYGON((2 2, 3 2, 3 3, 2 3, 2 2))')),
  (3, ST_GeomFromText('POLYGON((3 3, 4 3, 4 4, 3 4, 3 3))')),
  (4, ST_GeomFromText('POLYGON((4 4, 5 4, 5 5, 4 5, 4 4))')),
  (5, ST_GeomFromText('POLYGON((5 5.00001, 6 5.00001, 6 10, 5 10, 5 5.00001))')),
  (6, ST_GeomFromText('POLYGON((6 6, 7 6, 7 7, 6 7, 6 6))')),
  (7, ST_GeomFromText('POLYGON((7 7, 8 7, 8 8, 7 8, 7 7))'));

SET @py = 'POLYGON((0 0, 10 0, 10 5, 0 5, 0 0))';
SET @cart_py = ST_GeomFromText(@py);
SET @geo_py = ST_GeomFromText(@py, 4326, 'axis-order=long-lat');

SET @mpt = 'MULTIPOINT((0 0), (10 5))';
SET @cart_mpt = ST_GeomFromText(@mpt);
SET @geo_mpt = ST_GeomFromText(@mpt, 4326, 'axis-order=long-lat');

--echo # Cartesian ST_Overlaps
SELECT id, ST_AsText(c)
  FROM t1 IGNORE INDEX(c_idx)
  WHERE ST_Overlaps(c, @cart_py)
  ORDER BY id;
SELECT id, ST_AsText(c)
  FROM t1 FORCE INDEX(c_idx)
  WHERE ST_Overlaps(c, @cart_py)
  ORDER BY id;

--echo # Geographic ST_Overlaps
SELECT id, ST_AsText(g)
  FROM t1 IGNORE INDEX(g_idx)
  WHERE ST_Overlaps(g, @geo_py)
  ORDER BY id;
SELECT id, ST_AsText(g)
  FROM t1 FORCE INDEX(g_idx)
  WHERE ST_Overlaps(g, @geo_py)
  ORDER BY id;

--echo # Cartesian MBROverlaps
SELECT id, ST_AsText(c)
  FROM t1 IGNORE INDEX(c_idx)
  WHERE MBROverlaps(c, @cart_py)
  ORDER BY id;
SELECT id, ST_AsText(c)
  FROM t1 FORCE INDEX(c_idx)
  WHERE MBROverlaps(c, @cart_py)
  ORDER BY id;
SELECT id, ST_AsText(c)
  FROM t1 IGNORE INDEX(c_idx)
  WHERE MBROverlaps(c, @cart_mpt)
  ORDER BY id;
SELECT id, ST_AsText(c)
  FROM t1 FORCE INDEX(c_idx)
  WHERE MBROverlaps(c, @cart_mpt)
  ORDER BY id;

--echo # Geographic MBROverlaps
SELECT id, ST_AsText(g)
  FROM t1 IGNORE INDEX(g_idx)
  WHERE MBROverlaps(g, @geo_py)
  ORDER BY id;
SELECT id, ST_AsText(g)
  FROM t1 FORCE INDEX(g_idx)
  WHERE MBROverlaps(g, @geo_py)
  ORDER BY id;
SELECT id, ST_AsText(g)
  FROM t1 IGNORE INDEX(g_idx)
  WHERE MBROverlaps(g, @geo_mpt)
  ORDER BY id;
SELECT id, ST_AsText(g)
  FROM t1 FORCE INDEX(g_idx)
  WHERE MBROverlaps(g, @geo_mpt)
  ORDER BY id;

DROP TABLE t1;

--echo #
--echo # Bug #26188118 MBROVERLAPS(LS, LS) INCORRECT FOR PERPENDICULAR LINES
--echo #

SELECT MBROverlaps(
  ST_GeomFromText('LINESTRING(0 0, 0 2)'),
  ST_GeomFromText('LINESTRING(-1 1, 1 1)')
) AS should_be_true;

SELECT MBROverlaps(
  ST_GeomFromText('LINESTRING(-1 0, 1 0)', 4326),
  ST_GeomFromText('LINESTRING(0 -1, 0 1)', 4326)
) AS should_be_true;

--echo #
--echo # WL#11096 Don't do Cartesian computations on geographic geometries
--echo #

--echo # Assume SRID 10 is not defined.
--error ER_SRS_NOT_FOUND
DO MBROVERLAPS(
  x'0A000000010100000000000000000000000000000000000000',
  x'0A000000010100000000000000000000000000000000000000'
);
--error ER_SRS_NOT_FOUND
DO ST_OVERLAPS(
  x'0A000000010100000000000000000000000000000000000000',
  x'0A000000010100000000000000000000000000000000000000'
);
