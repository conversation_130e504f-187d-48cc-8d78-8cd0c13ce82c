# Test of SQL window functions.
# ----------------------------------------------------------------------
SET NAMES utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead

Flag unsupported aggregates as window functions

select group_concat('3') over ();
ERROR 42000: This version of MySQL doesn't yet support 'group_concat as window function'
Single window function (plus ORDER BY).
CREATE TABLE t(i INT, j INT);
INSERT INTO t VALUES (1,1);
INSERT INTO t VALUES (1,4);
INSERT INTO t VALUES (1,2);
INSERT INTO t VALUES (1,4);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
Single partition, no sorting
SELECT i, j, SUM(i+j) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t;
i	j	foo
1	1	2
1	4	7
1	2	10
1	4	15
SELECT i, j, SUM(i+j) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) foo FROM t;
i	j	foo
1	1	2
1	4	7
1	2	10
1	4	15
SELECT i, j, SUM(i+j) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo;
i	j	foo
1	1	2
1	4	7
1	2	10
1	4	15
SELECT i, j, SUM(i+j) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo DESC;
i	j	foo
1	4	15
1	2	10
1	4	7
1	1	2
Check that we eliminate redundant sorting in ORDER BY even with wfs
Also check that EXPLAIN prints the right number of "using_temporary_table"
FLUSH STATUS;
SELECT i, j, SUM(i+j) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY NULL DESC;
i	j	foo
1	1	2
1	4	7
1	2	10
1	4	15
SHOW STATUS LIKE 'Created_tmp_tables';
Variable_name	Value
Created_tmp_tables	0
EXPLAIN FORMAT=JSON SELECT i, j, SUM(i+j) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY NULL DESC;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.65"
    },
    "ordering_operation": {
      "using_filesort": false,
      "windowing": {
        "windows": [
          {
            "name": "<unnamed window>",
            "functions": [
              "sum"
            ]
          }
        ],
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 4,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.40",
            "prefix_cost": "0.65",
            "data_read_per_join": "64"
          },
          "used_columns": [
            "i",
            "j"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i`,`test`.`t`.`j` AS `j`,sum((`test`.`t`.`i` + `test`.`t`.`j`)) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `test`.`t` order by NULL desc
With LIMIT
SELECT i, j, SUM(i+j) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo DESC LIMIT 3;
i	j	foo
1	4	15
1	2	10
1	4	7
With LIMIT when last tmp file step is optimized away
CREATE TABLE t1 (i INT) ;
INSERT INTO t1 (i) VALUES (1);
INSERT INTO t1 (i) VALUES (2);
INSERT INTO t1 (i) VALUES (3);
INSERT INTO t1 (i) VALUES (4);
INSERT INTO t1 (i) VALUES (5);
SELECT i, SUM(i) OVER (ORDER BY i ROWS BETWEEN 2 PRECEDING AND 2 FOLLOWING) FROM t1 LIMIT 3;
i	SUM(i) OVER (ORDER BY i ROWS BETWEEN 2 PRECEDING AND 2 FOLLOWING)
1	6
2	10
3	15
DROP TABLE t1;
Single ordered partition
SELECT i, j, SUM(i+j) OVER (ORDER BY j ROWS UNBOUNDED PRECEDING) foo FROM t;
i	j	foo
1	1	2
1	2	5
1	4	10
1	4	15
SELECT i, j, SUM(i+j) OVER (ORDER BY j ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo;
i	j	foo
1	1	2
1	2	5
1	4	10
1	4	15
SELECT i, j, SUM(i+j) OVER (ORDER BY j ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo DESC;
i	j	foo
1	4	15
1	4	10
1	2	5
1	1	2
SELECT i, j, SUM(i+j) OVER (ORDER BY j DESC ROWS UNBOUNDED PRECEDING) foo FROM t;
i	j	foo
1	4	5
1	4	10
1	2	13
1	1	15
SELECT i, j, SUM(i+j) OVER (ORDER BY jj DESC ROWS UNBOUNDED PRECEDING) foo FROM t;
ERROR 42S22: Unknown column 'jj' in 'window order by'
View with window function
CREATE VIEW v AS
SELECT i, j, SUM(i+j) OVER (ORDER BY j DESC ROWS UNBOUNDED PRECEDING) foo FROM t;
SHOW CREATE VIEW v;
View	Create View	character_set_client	collation_connection
v	CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v` AS select `t`.`i` AS `i`,`t`.`j` AS `j`,sum((`t`.`i` + `t`.`j`)) OVER (ORDER BY `t`.`j` desc ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `foo` from `t`	utf8mb3	utf8mb3_general_ci
SELECT * FROM v;
i	j	foo
1	4	5
1	4	10
1	2	13
1	1	15
DROP VIEW v;
SELECT i, j, SUM(i+j) OVER (ORDER BY j DESC ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo;
i	j	foo
1	4	5
1	4	10
1	2	13
1	1	15
SELECT i, j, SUM(i+j) OVER (ORDER BY j DESC ROWS UNBOUNDED PRECEDING) foo FROM t ORDER BY foo DESC;
i	j	foo
1	1	15
1	2	13
1	4	10
1	4	5
TRUNCATE TABLE t;
Check my_decimal bug: no warning if c=a+b and c is one of a,b... just fails over 9 digits
INSERT INTO t VALUES (999961560, DEFAULT);
INSERT INTO t VALUES (44721, DEFAULT);
SELECT SUM(i) OVER () FROM t;
SUM(i) OVER ()
1000006281
1000006281
DROP TABLE t;
CREATE TABLE t(i INT, j INT, k INT);
INSERT INTO t VALUES (1,1,1);
INSERT INTO t VALUES (1,4,1);
INSERT INTO t VALUES (1,2,1);
INSERT INTO t VALUES (1,4,1);
INSERT INTO t VALUES (1,1,2);
INSERT INTO t VALUES (1,4,2);
INSERT INTO t VALUES (1,2,2);
INSERT INTO t VALUES (1,4,2);
INSERT INTO t VALUES (1,1,3);
INSERT INTO t VALUES (1,4,3);
INSERT INTO t VALUES (1,2,3);
INSERT INTO t VALUES (1,4,3);
INSERT INTO t VALUES (1,1,4);
INSERT INTO t VALUES (1,4,4);
INSERT INTO t VALUES (1,2,4);
INSERT INTO t VALUES (1,4,4);
----------------------------------------------------------------------
-    Combination with GROUP BY
----------------------------------------------------------------------
Show difference in binding of colums
CREATE TABLE tb(a INT, b INT);
Grouping version of SUM can refer to ungrouped column
SELECT a, SUM(b) FROM tb GROUP BY a;
a	SUM(b)
Windowing version of SUM cannot refer to ungrouped column since the
grouping has already happened by the the the windowing starts
and each row now represents multiple aggregated values of b
SELECT a, SUM(b) OVER () FROM tb GROUP BY a;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.tb.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
DROP TABLE tb;
SELECT k, SUM(k) OVER (ROWS UNBOUNDED PRECEDING) wf FROM t;
k	wf
1	1
1	2
1	3
1	4
2	6
2	8
2	10
2	12
3	15
3	18
3	21
3	24
4	28
4	32
4	36
4	40
SELECT k, MIN(i), SUM(j), SUM(k) OVER (ROWS UNBOUNDED PRECEDING) wf FROM t GROUP BY (k);
k	MIN(i)	SUM(j)	wf
1	1	11	1
2	1	11	3
3	1	11	6
4	1	11	10
SELECT k, MIN(i), SUM(j), SUM(k) OVER (ROWS UNBOUNDED PRECEDING) wf FROM t GROUP BY (k) ORDER BY wf DESC;
k	MIN(i)	SUM(j)	wf
4	1	11	10
3	1	11	6
2	1	11	3
1	1	11	1
SELECT k, GROUP_CONCAT(j ORDER BY j), SUM(k) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t GROUP BY (k);
k	GROUP_CONCAT(j ORDER BY j)	foo
1	1,2,4,4	1
2	1,2,4,4	3
3	1,2,4,4	6
4	1,2,4,4	10
SELECT k, AVG(DISTINCT j), SUM(k) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t GROUP BY (k);
k	AVG(DISTINCT j)	foo
1	2.3333	1
2	2.3333	3
3	2.3333	6
4	2.3333	10
SELECT k, GROUP_CONCAT(j ORDER BY j), SUM(k+1) OVER (ROWS UNBOUNDED PRECEDING) foo FROM t GROUP BY (k);
k	GROUP_CONCAT(j ORDER BY j)	foo
1	1,2,4,4	2
2	1,2,4,4	5
3	1,2,4,4	9
4	1,2,4,4	14
SELECT k, GROUP_CONCAT(j ORDER BY j), SUM(k+1) OVER (ORDER BY k DESC ROWS UNBOUNDED PRECEDING) foo FROM t GROUP BY (k);
k	GROUP_CONCAT(j ORDER BY j)	foo
4	1,2,4,4	5
3	1,2,4,4	9
2	1,2,4,4	12
1	1,2,4,4	14
SELECT i/SUM(j) OVER (PARTITION BY k) AS x FROM t GROUP BY x;
ERROR 42000: Can't group on 'x'
SELECT i/SUM(j) OVER (PARTITION BY kk) AS x FROM t;
ERROR 42S22: Unknown column 'kk' in 'window partition by'
SELECT i/SUM(j) OVER (PARTITION BY 1) AS x FROM t;
ERROR HY000: Window '<unnamed window>': ORDER BY or PARTITION BY uses legacy position indication which is not supported, use expression.
CREATE TABLE t1 (id INTEGER, sex CHAR(1));
INSERT INTO t1 VALUES (1, 'M');
INSERT INTO t1 VALUES (2, 'F');
INSERT INTO t1 VALUES (3, 'F');
INSERT INTO t1 VALUES (4, 'F');
INSERT INTO t1 VALUES (5, 'M');
INSERT INTO t1 VALUES (10, NULL);
INSERT INTO t1 VALUES (11, NULL);
CREATE TABLE ss(c CHAR(1));
INSERT INTO ss VALUES ('M');
This is grouped aggregate in conjunction with a streaming wfs
SELECT sex, AVG(id), ROW_NUMBER() OVER w FROM t1
GROUP BY sex
WINDOW w AS () ORDER BY sex DESC;
sex	AVG(id)	ROW_NUMBER() OVER w
M	3.0000	1
F	3.0000	2
NULL	10.5000	3
SELECT sex, AVG(id), SUM(AVG(id)) OVER w FROM t1
GROUP BY sex
WINDOW w AS (ROWS UNBOUNDED PRECEDING) ORDER BY sex DESC;
sex	AVG(id)	SUM(AVG(id)) OVER w
M	3.0000	3.0000
F	3.0000	6.0000
NULL	10.5000	16.5000
This is grouped aggregate with HAVING in conjunction with a streaming wf
which will initially adds a dummy window. However, the HAVING is pushed
to a table condition: This exercises the try_remove_dummy_windowing_step
which gets called from make_tmp_table_info, so make sure it works on
second execution since we change initial windowing decision made in
setup_windows only during prepare.
PREPARE p FROM "SELECT sex, AVG(id), ROW_NUMBER() OVER w FROM t1
    GROUP BY sex HAVING sex='M' OR sex IS NULL
    WINDOW w AS () ORDER BY sex DESC";
EXECUTE p;
sex	AVG(id)	ROW_NUMBER() OVER w
M	3.0000	1
NULL	10.5000	2
EXECUTE p;
sex	AVG(id)	ROW_NUMBER() OVER w
M	3.0000	1
NULL	10.5000	2
DROP PREPARE p;
SELECT sex, AVG(id), ROW_NUMBER() OVER w FROM t1
GROUP BY sex HAVING sex='M' OR sex IS NULL
WINDOW w AS () ORDER BY sex DESC;
sex	AVG(id)	ROW_NUMBER() OVER w
M	3.0000	1
NULL	10.5000	2
SELECT sex, AVG(id), SUM(AVG(id)) OVER w FROM t1
GROUP BY sex HAVING sex='M' OR sex='F' OR sex IS NULL
WINDOW w AS (ROWS UNBOUNDED PRECEDING) ORDER BY sex DESC;
sex	AVG(id)	SUM(AVG(id)) OVER w
M	3.0000	3.0000
F	3.0000	6.0000
NULL	10.5000	16.5000
Ditto, but HAVING using subquery
SELECT sex, AVG(id), ROW_NUMBER() OVER w FROM t1
GROUP BY sex HAVING sex=(SELECT c FROM ss LIMIT 1) OR sex IS NULL
WINDOW w AS () ORDER BY sex DESC;
sex	AVG(id)	ROW_NUMBER() OVER w
M	3.0000	1
NULL	10.5000	2
SELECT sex, AVG(id), SUM(AVG(id)) OVER w FROM t1
GROUP BY sex HAVING sex=(SELECT c FROM ss LIMIT 1) OR sex='F' OR sex IS NULL
WINDOW w AS (ROWS UNBOUNDED PRECEDING) ORDER BY sex DESC;
sex	AVG(id)	SUM(AVG(id)) OVER w
M	3.0000	3.0000
F	3.0000	6.0000
NULL	10.5000	16.5000
This is a grouped aggregate in conjunction with a buffered wf
which generates an extra tmp file step
SELECT sex, AVG(id), NTILE(2) OVER w FROM t1
GROUP BY sex
WINDOW w AS (ORDER BY sex) ORDER BY sex DESC;
sex	AVG(id)	NTILE(2) OVER w
M	3.0000	2
F	3.0000	1
NULL	10.5000	1
SELECT sex, AVG(id), SUM(AVG(id)) OVER w, NTILE(2) OVER w FROM t1
GROUP BY sex
WINDOW w AS (ORDER BY sex ROWS UNBOUNDED PRECEDING) ORDER BY sex DESC;
sex	AVG(id)	SUM(AVG(id)) OVER w	NTILE(2) OVER w
M	3.0000	16.5000	2
F	3.0000	13.5000	1
NULL	10.5000	10.5000	1
This is a grouped aggregate with HAVING in conjunction with a buffered wf
which generates an extra tmp file step
SELECT sex, AVG(id), NTILE(2) OVER w FROM t1
GROUP BY sex HAVING sex=(SELECT c FROM ss LIMIT 1) OR sex IS NULL
WINDOW w AS (ORDER BY sex) ORDER BY sex DESC;
sex	AVG(id)	NTILE(2) OVER w
M	3.0000	2
NULL	10.5000	1
SELECT sex, AVG(id), SUM(AVG(id)) OVER w, NTILE(2) OVER w FROM t1
GROUP BY sex HAVING sex=(SELECT c FROM ss LIMIT 1) OR sex='F' OR sex IS NULL
WINDOW w AS (ORDER BY sex ROWS UNBOUNDED PRECEDING) ORDER BY sex DESC;
sex	AVG(id)	SUM(AVG(id)) OVER w	NTILE(2) OVER w
M	3.0000	16.5000	2
F	3.0000	13.5000	1
NULL	10.5000	10.5000	1
Pure HAVING: In absence of filtering in the grouping sort step,
make sure we filter before windowing steps
SELECT sex, NTILE(2) OVER w, SUM(ASCII(sex)) OVER w s FROM t1
HAVING sex=(SELECT c FROM ss LIMIT 1)
WINDOW w AS (ORDER BY sex ROWS UNBOUNDED PRECEDING);
sex	NTILE(2) OVER w	s
M	1	77
M	2	154
Bug fix for prepared statements
PREPARE p FROM "SELECT sex, AVG(id), SUM(AVG(id)) OVER w, NTILE(2) OVER w FROM t1
    GROUP BY sex HAVING sex=(SELECT c FROM ss LIMIT 1) OR sex='F' OR sex IS NULL
    WINDOW w AS (ORDER BY sex ROWS UNBOUNDED PRECEDING) ORDER BY sex DESC";
EXECUTE p;
sex	AVG(id)	SUM(AVG(id)) OVER w	NTILE(2) OVER w
M	3.0000	16.5000	2
F	3.0000	13.5000	1
NULL	10.5000	10.5000	1
EXECUTE p;
sex	AVG(id)	SUM(AVG(id)) OVER w	NTILE(2) OVER w
M	3.0000	16.5000	2
F	3.0000	13.5000	1
NULL	10.5000	10.5000	1
DROP PREPARE p;
Tests with ROLLUP
SELECT k, MIN(i), SUM(j), SUM(k) OVER (ROWS UNBOUNDED PRECEDING) wf FROM t
GROUP BY (k) WITH ROLLUP;
k	MIN(i)	SUM(j)	wf
1	1	11	1
2	1	11	3
3	1	11	6
4	1	11	10
NULL	1	44	10
SELECT    MIN(i), SUM(j), SUM(k) OVER (ROWS UNBOUNDED PRECEDING) wf FROM t
GROUP BY (k) WITH ROLLUP;
MIN(i)	SUM(j)	wf
1	11	1
1	11	3
1	11	6
1	11	10
1	44	10
SELECT k, MIN(i), SUM(j), SUM(k) OVER (ROWS UNBOUNDED PRECEDING) wf FROM t
GROUP BY (k) WITH ROLLUP ORDER BY wf DESC;
k	MIN(i)	SUM(j)	wf
1	1	11	1
2	1	11	3
3	1	11	6
4	1	11	10
NULL	1	44	10
SELECT k, MIN(i), SUM(j), SUM(k) OVER (ROWS UNBOUNDED PRECEDING) wf FROM t
GROUP BY k,j WITH ROLLUP;
k	MIN(i)	SUM(j)	wf
1	1	1	1
1	1	2	2
1	1	8	3
1	1	11	4
2	1	1	6
2	1	2	8
2	1	8	10
2	1	11	12
3	1	1	15
3	1	2	18
3	1	8	21
3	1	11	24
4	1	1	28
4	1	2	32
4	1	8	36
4	1	11	40
NULL	1	44	40
SELECT    MIN(i), SUM(j), SUM(k) OVER (ROWS UNBOUNDED PRECEDING) wf FROM t
GROUP BY k,j WITH ROLLUP;
MIN(i)	SUM(j)	wf
1	1	1
1	2	2
1	8	3
1	11	4
1	1	6
1	2	8
1	8	10
1	11	12
1	1	15
1	2	18
1	8	21
1	11	24
1	1	28
1	2	32
1	8	36
1	11	40
1	44	40
SELECT sex, AVG(id), ROW_NUMBER() OVER w FROM t1
GROUP BY sex WITH ROLLUP WINDOW w AS ();
sex	AVG(id)	ROW_NUMBER() OVER w
NULL	10.5000	1
F	3.0000	2
M	3.0000	3
NULL	5.1429	4
SELECT sex, AVG(id), ROW_NUMBER() OVER w FROM t1
GROUP BY sex WITH ROLLUP HAVING sex='M' OR sex IS NULL
WINDOW w AS ();
sex	AVG(id)	ROW_NUMBER() OVER w
NULL	10.5000	1
M	3.0000	2
NULL	5.1429	3
SELECT sex, AVG(id) FROM t1
GROUP BY sex WITH ROLLUP
HAVING (sex='M' OR sex IS NULL) AND AVG(id)=3.0
ORDER BY GROUPING(sex), sex;
sex	AVG(id)
M	3.0000
SELECT sex, AVG(id), ROW_NUMBER() OVER w FROM t1
GROUP BY sex WITH ROLLUP
HAVING (sex='M' OR sex IS NULL) AND AVG(id)=3.0
WINDOW w AS ();
sex	AVG(id)	ROW_NUMBER() OVER w
M	3.0000	1

Bug#25756549

SELECT id, FIRST_VALUE(id) OVER w first, LAST_VALUE(id) OVER w last, sex FROM t1
WINDOW w AS (PARTITION BY sex
ORDER BY id ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
id	first	last	sex
10	11	11	NULL
11	NULL	NULL	NULL
2	3	4	F
3	4	4	F
4	NULL	NULL	F
1	5	5	M
5	NULL	NULL	M
SELECT id, FIRST_VALUE(id) OVER w first, LAST_VALUE(id) OVER w last, sex FROM t1
WINDOW w AS (PARTITION BY sex
ORDER BY id RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
id	first	last	sex
10	11	11	NULL
11	NULL	NULL	NULL
2	3	4	F
3	4	4	F
4	NULL	NULL	F
1	NULL	NULL	M
5	NULL	NULL	M
Subquery which causes reuse of window requiring state reset,
cf. Window::reset_round.
CREATE TABLE t_a (a INT, b INT);
INSERT INTO t_a VALUES (4, 40), (1, 10), (2, 20), (2, 20), (3, 30);
CREATE TABLE t_b SELECT DISTINCT a FROM t_a;
SELECT (SELECT SUM(t_b.a) OVER () FROM t_b WHERE t_b.a = t_a.a) aa, b FROM t_a GROUP BY aa, b;
aa	b
1	10
2	20
3	30
4	40
DROP TABLE t_a, t_b;
----------------------------------------------------------------------
Test of legal frame border value, including prepared statement and dynamic ?
parameters
----------------------------------------------------------------------
Static version of checking is caught at parsing time, unless we have INTERVAL.
For subquery in border, cf. test cases in Bug#25907777
SELECT sex, COUNT(id) OVER (ORDER BY id RANGE -1 PRECEDING) FROM t1;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '-1 PRECEDING) FROM t1' at line 1
SELECT sex, COUNT(id) OVER (ORDER BY id RANGE BETWEEN -1 PRECEDING and 2 PRECEDING) FROM t1;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '-1 PRECEDING and 2 PRECEDING) FROM t1' at line 1
SELECT sex, COUNT(id) OVER (ORDER BY id RANGE BETWEEN 1 PRECEDING and -1 PRECEDING) FROM t1;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '-1 PRECEDING) FROM t1' at line 1
OK, even if empty frame
SELECT sex, COUNT(id) OVER (ORDER BY id RANGE BETWEEN 1 PRECEDING and 2 PRECEDING) FROM t1;
sex	COUNT(id) OVER (ORDER BY id RANGE BETWEEN 1 PRECEDING and 2 PRECEDING)
M	0
F	0
F	0
F	0
M	0
NULL	0
NULL	0
CREATE TABLE t_time(t TIME, ts TIMESTAMP);
INSERT INTO t_time VALUES ('12:30', '2016-07-05 08:30:42');
SELECT t, FIRST_VALUE(t) OVER (ORDER BY t ) FROM t_time;
t	FIRST_VALUE(t) OVER (ORDER BY t )
12:30:00	12:30:00
coverage for ::get_time
SELECT ADDTIME(FIRST_VALUE(time'18:00:00') OVER (ORDER BY NULL), '01:00:00');
ADDTIME(FIRST_VALUE(time'18:00:00') OVER (ORDER BY NULL), '01:00:00')
19:00:00
SELECT ADDTIME(NTH_VALUE(time'18:00:00', 1) OVER (ORDER BY NULL), '01:00:00');
ADDTIME(NTH_VALUE(time'18:00:00', 1) OVER (ORDER BY NULL), '01:00:00')
19:00:00
SELECT t, FIRST_VALUE(t) OVER (ORDER BY t RANGE INTERVAL -1 HOUR PRECEDING) FROM t_time;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
SELECT t, FIRST_VALUE(t) OVER (ORDER BY t RANGE INTERVAL NULL HOUR PRECEDING) FROM t_time;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
SELECT t, FIRST_VALUE(t) OVER (ORDER BY t RANGE BETWEEN INTERVAL -1 HOUR PRECEDING AND INTERVAL 2 HOUR PRECEDING) FROM t_time;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
SELECT t, FIRST_VALUE(t) OVER (ORDER BY t RANGE BETWEEN INTERVAL 1 HOUR PRECEDING AND INTERVAL -2 HOUR PRECEDING) FROM t_time;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
SELECT t, FIRST_VALUE(t) OVER (ORDER BY t RANGE BETWEEN INTERVAL 1 HOUR PRECEDING AND INTERVAL '-2:2' HOUR_MINUTE PRECEDING) FROM t_time;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
SELECT t, FIRST_VALUE(t) OVER (ORDER BY t RANGE BETWEEN INTERVAL 1 HOUR PRECEDING AND INTERVAL NULL HOUR PRECEDING) FROM t_time;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
SELECT t, FIRST_VALUE(t) OVER (ORDER BY t RANGE BETWEEN INTERVAL NULL HOUR PRECEDING AND INTERVAL 1 HOUR PRECEDING) FROM t_time;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
Dynamic ? parameter checking
PREPARE p FROM "SELECT sex, COUNT(id) OVER (ORDER BY id ROWS BETWEEN ? PRECEDING and ? PRECEDING) FROM t1";
SET @p1= 1;
SET @p2= 2;
Empty frame is legal according to standard
EXECUTE p USING @p1, @p2;
sex	COUNT(id) OVER (ORDER BY id ROWS BETWEEN ? PRECEDING and ? PRECEDING)
M	0
F	0
F	0
F	0
M	0
NULL	0
NULL	0
OK, not empty frame
EXECUTE p USING @p2, @p1;
sex	COUNT(id) OVER (ORDER BY id ROWS BETWEEN ? PRECEDING and ? PRECEDING)
M	0
F	1
F	2
F	2
M	2
NULL	2
NULL	2
DROP PREPARE p;
PREPARE p FROM "SELECT sex, COUNT(id) OVER (ORDER BY id ROWS BETWEEN ? FOLLOWING and ? FOLLOWING) FROM t1";
SET @p1= 1;
SET @p2= 2;
Empty frame is legal according to standard
EXECUTE p USING @p2, @p1;
sex	COUNT(id) OVER (ORDER BY id ROWS BETWEEN ? FOLLOWING and ? FOLLOWING)
M	0
F	0
F	0
F	0
M	0
NULL	0
NULL	0
OK, not empty frame
EXECUTE p USING @p1, @p2;
sex	COUNT(id) OVER (ORDER BY id ROWS BETWEEN ? FOLLOWING and ? FOLLOWING)
M	2
F	2
F	2
F	2
M	2
NULL	1
NULL	0
DROP PREPARE p;
PREPARE p FROM "SELECT sex, COUNT(id) OVER (ORDER BY id ROWS BETWEEN ? FOLLOWING and ? FOLLOWING) FROM t1";
SET @p1= -1;
SET @p2= 2;
EXECUTE p USING @p2, @p1;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
EXECUTE p USING @p1, @p2;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
DROP PREPARE p;
PREPARE p FROM "SELECT sex, COUNT(id) OVER (ORDER BY id ROWS ? PRECEDING) FROM t1";
SET @p1= -1;
EXECUTE p USING @p1;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
DROP PREPARE p;
Negative floating-point bounds not ok
PREPARE p FROM "SELECT sex, COUNT(id) OVER (ORDER BY id RANGE ? PRECEDING) FROM t1";
SET @p1= -0.1;
EXECUTE p USING @p1;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
DROP PREPARE p;
PREPARE p FROM "SELECT sex, COUNT(id) OVER (ORDER BY id ROWS BETWEEN CURRENT ROW AND ? FOLLOWING) FROM t1";
SET @p1= -1;
EXECUTE p USING @p1;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
DROP PREPARE p;
PREPARE p FROM "SELECT t, FIRST_VALUE(t) OVER (ORDER BY t RANGE INTERVAL ? HOUR PRECEDING) FROM t_time";
SET @p1= -1;
EXECUTE p USING @p1;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
PREPARE p FROM "SELECT t, FIRST_VALUE(t) OVER (ORDER BY t RANGE BETWEEN INTERVAL ? HOUR PRECEDING AND INTERVAL ? HOUR PRECEDING) FROM t_time";
SET @p2= 2;
EXECUTE p USING @p1, @p2;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
EXECUTE p USING @p2, @p1;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
SET @p1= NULL;
EXECUTE p USING @p2, @p1;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
PREPARE p FROM "SELECT t, FIRST_VALUE(t) OVER (ORDER BY t RANGE BETWEEN INTERVAL ? HOUR PRECEDING AND INTERVAL ? HOUR_MINUTE PRECEDING) FROM t_time";
SET @p1= '-2:2';
EXECUTE p USING @p2, @p1;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
SET @p1= '2:2';
EXECUTE p USING @p2, @p1;
t	FIRST_VALUE(t) OVER (ORDER BY t RANGE BETWEEN INTERVAL ? HOUR PRECEDING AND INTERVAL ? HOUR_MINUTE PRECEDING)
12:30:00	NULL
DROP TABLE t, t1, ss, t_time;
----------------------------------------------------------------------
-    Some RANK, DENSE_RANK, PERCENT_RANK, CUME_DIST tests
----------------------------------------------------------------------
CREATE TABLE t1 (id INTEGER, sex CHAR(1));
INSERT INTO t1 VALUES (1, 'M');
INSERT INTO t1 VALUES (2, 'F');
INSERT INTO t1 VALUES (3, 'F');
INSERT INTO t1 VALUES (4, 'F');
INSERT INTO t1 VALUES (5, 'M');
CREATE TABLE t2 (user_id INTEGER NOT NULL, date DATE);
INSERT INTO t2 VALUES (1, '2002-06-09');
INSERT INTO t2 VALUES (2, '2002-06-09');
INSERT INTO t2 VALUES (1, '2002-06-09');
INSERT INTO t2 VALUES (3, '2002-06-09');
INSERT INTO t2 VALUES (4, '2002-06-09');
INSERT INTO t2 VALUES (4, '2002-06-09');
INSERT INTO t2 VALUES (5, '2002-06-09');
SELECT RANK() OVER (ORDER BY user_id) r FROM t2;
r
1
1
3
4
5
5
7
SELECT DENSE_RANK() OVER (ORDER BY user_id) r FROM t2;
r
1
1
2
3
4
4
5
SELECT PERCENT_RANK() OVER (ORDER BY user_id) r FROM t2;
r
0
0
0.3333333333333333
0.5
0.6666666666666666
0.6666666666666666
1
SELECT CUME_DIST() OVER (ORDER BY user_id) cd FROM t2;
cd
0.2857142857142857
0.2857142857142857
0.42857142857142855
0.5714285714285714
0.8571428571428571
0.8571428571428571
1
SELECT RANK() OVER () r FROM t2;
r
1
1
1
1
1
1
1
SELECT DENSE_RANK() OVER () r FROM t2;
r
1
1
1
1
1
1
1
SELECT PERCENT_RANK() OVER () r FROM t2;
r
0
0
0
0
0
0
0
SELECT CUME_DIST() OVER () cd FROM t2;
cd
1
1
1
1
1
1
1
SELECT sex, SUM(DISTINCT id) AS uids FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex ORDER BY uids;
sex	uids
M	6
F	9
SELECT id, sex, RANK() OVER (ORDER BY sex) FROM t1 ORDER BY id;
id	sex	RANK() OVER (ORDER BY sex)
1	M	4
2	F	1
3	F	1
4	F	1
5	M	4
SELECT id, sex, DENSE_RANK() OVER (ORDER BY sex) FROM t1 ORDER BY id;
id	sex	DENSE_RANK() OVER (ORDER BY sex)
1	M	2
2	F	1
3	F	1
4	F	1
5	M	2
SELECT id, sex, PERCENT_RANK() OVER (ORDER BY sex) FROM t1 ORDER BY id;
id	sex	PERCENT_RANK() OVER (ORDER BY sex)
1	M	0.75
2	F	0
3	F	0
4	F	0
5	M	0.75
SELECT id, sex, CUME_DIST() OVER (ORDER BY sex) FROM t1 ORDER BY id;
id	sex	CUME_DIST() OVER (ORDER BY sex)
1	M	1
2	F	0.6
3	F	0.6
4	F	0.6
5	M	1
SELECT sex, RANK() OVER (ORDER BY sex DESC) `rank`, AVG(DISTINCT id) AS uids FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex ORDER BY sex;
sex	rank	uids
F	2	3.0000
M	1	3.0000
SELECT sex, PERCENT_RANK() OVER (ORDER BY sex DESC) `rank`, AVG(DISTINCT id) AS uids FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex ORDER BY sex;
sex	rank	uids
F	1	3.0000
M	0	3.0000
SELECT sex, CUME_DIST() OVER (ORDER BY sex DESC) `cume_dist`, AVG(DISTINCT id) AS uids FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex ORDER BY sex;
sex	cume_dist	uids
F	1	3.0000
M	0.5	3.0000
Explicit window definition, WINDOW DESC ordering by GROUP BY
SELECT  sex, AVG(id) AS uids, RANK() OVER w `rank` FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex
WINDOW w AS (ORDER BY AVG(id));
sex	uids	rank
M	2.3333	1
F	3.2500	2
SELECT  sex, AVG(id) AS uids, PERCENT_RANK() OVER w `p_rank` FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex
WINDOW w AS (ORDER BY AVG(id));
sex	uids	p_rank
M	2.3333	0
F	3.2500	1
SELECT  sex, AVG(id) AS uids, CUME_DIST() OVER w `c_dist` FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex
WINDOW w AS (ORDER BY AVG(id));
sex	uids	c_dist
M	2.3333	0.5
F	3.2500	1
Explicit window definition, window ordering by DISTINCT GROUP BY
SELECT  sex, AVG(DISTINCT id) AS uids, RANK() OVER w `rank` FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex
WINDOW w AS (ORDER BY AVG(DISTINCT id) DESC) ORDER BY sex;
sex	uids	rank
F	3.0000	1
M	3.0000	1
SELECT  sex, AVG(DISTINCT id) AS uids, PERCENT_RANK() OVER w `p_rank` FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex
WINDOW w AS (ORDER BY AVG(DISTINCT id) DESC) ORDER BY sex;
sex	uids	p_rank
F	3.0000	0
M	3.0000	0
SELECT  sex, AVG(DISTINCT id) AS uids, CUME_DIST() OVER w `c_dist` FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex
WINDOW w AS (ORDER BY AVG(DISTINCT id) DESC) ORDER BY sex;
sex	uids	c_dist
F	3.0000	1
M	3.0000	1
Explicit window definition, window ordering by GROUP BY, final ORDER BY
SELECT  sex, AVG(id) AS uids, RANK() OVER w `rank` FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex
WINDOW w AS (ORDER BY AVG(id) DESC)
ORDER BY `rank` DESC;
sex	uids	rank
M	2.3333	2
F	3.2500	1
SELECT  sex, AVG(id) AS uids, PERCENT_RANK() OVER w `p_rank`, CUME_DIST() OVER w `c_dist`
   FROM t1 u, t2
WHERE t2.user_id = u.id GROUP BY sex
WINDOW w AS (ORDER BY AVG(id) DESC)
ORDER BY `p_rank` DESC;
sex	uids	p_rank	c_dist
F	3.2500	0	0.5
M	2.3333	1	1
With NULLs
INSERT INTO t1 VALUES (10, NULL);
INSERT INTO t1 VALUES (11, NULL);
SELECT id, sex, RANK() OVER w, DENSE_RANK() OVER w FROM t1
WINDOW w AS (ORDER BY sex) ORDER BY id;
id	sex	RANK() OVER w	DENSE_RANK() OVER w
1	M	6	3
2	F	3	2
3	F	3	2
4	F	3	2
5	M	6	3
10	NULL	1	1
11	NULL	1	1
SELECT id, sex, PERCENT_RANK() OVER w, CUME_DIST() OVER w FROM t1
WINDOW w AS (ORDER BY sex) ORDER BY id;
id	sex	PERCENT_RANK() OVER w	CUME_DIST() OVER w
1	M	0.8333333333333334	1
2	F	0.3333333333333333	0.7142857142857143
3	F	0.3333333333333333	0.7142857142857143
4	F	0.3333333333333333	0.7142857142857143
5	M	0.8333333333333334	1
10	NULL	0	0.2857142857142857
11	NULL	0	0.2857142857142857
SELECT id, sex, RANK() OVER (ORDER BY sex DESC) FROM t1 ORDER BY id;
id	sex	RANK() OVER (ORDER BY sex DESC)
1	M	1
2	F	3
3	F	3
4	F	3
5	M	1
10	NULL	6
11	NULL	6
SELECT id, sex, PERCENT_RANK() OVER (ORDER BY sex DESC) FROM t1 ORDER BY id;
id	sex	PERCENT_RANK() OVER (ORDER BY sex DESC)
1	M	0
2	F	0.3333333333333333
3	F	0.3333333333333333
4	F	0.3333333333333333
5	M	0
10	NULL	0.8333333333333334
11	NULL	0.8333333333333334
SELECT id, sex, CUME_DIST() OVER (ORDER BY sex DESC) FROM t1 ORDER BY id;
id	sex	CUME_DIST() OVER (ORDER BY sex DESC)
1	M	0.2857142857142857
2	F	0.7142857142857143
3	F	0.7142857142857143
4	F	0.7142857142857143
5	M	0.2857142857142857
10	NULL	1
11	NULL	1
SELECT id value,
SUM(id) OVER (ROWS UNBOUNDED PRECEDING)
FROM t1 u LEFT JOIN t2 ON t2.user_id = u.id;
value	SUM(id) OVER (ROWS UNBOUNDED PRECEDING)
1	1
1	2
2	4
3	7
4	11
4	15
5	20
10	30
11	41
Aggregate with GROUP BY arguments to window function
SELECT AVG(id) average,
SUM(AVG(id)) OVER (ORDER BY sex DESC ROWS UNBOUNDED PRECEDING)
FROM t1 u, t2 WHERE t2.user_id = u.id GROUP BY sex;
average	SUM(AVG(id)) OVER (ORDER BY sex DESC ROWS UNBOUNDED PRECEDING)
2.3333	2.3333
3.2500	5.5833
Aggregate with GROUP BY in window's ORDER BY clause, with aggregate present in
SELECT list or not.
SELECT sex, AVG(id), RANK() OVER (ORDER BY AVG(id) DESC) FROM t1 GROUP BY sex ORDER BY sex;
sex	AVG(id)	RANK() OVER (ORDER BY AVG(id) DESC)
NULL	10.5000	1
F	3.0000	2
M	3.0000	2
SELECT sex, PERCENT_RANK() OVER (ORDER BY AVG(id) DESC) FROM t1 GROUP BY sex ORDER BY sex;
sex	PERCENT_RANK() OVER (ORDER BY AVG(id) DESC)
NULL	0
F	0.5
M	0.5
SELECT sex, CUME_DIST() OVER (ORDER BY AVG(id) DESC) FROM t1 GROUP BY sex ORDER BY sex;
sex	CUME_DIST() OVER (ORDER BY AVG(id) DESC)
NULL	0.3333333333333333
F	1
M	1
SELECT sex, RANK() OVER (ORDER BY AVG(id) DESC) FROM t1 GROUP BY sex ORDER BY sex;
sex	RANK() OVER (ORDER BY AVG(id) DESC)
NULL	1
F	2
M	2
SELECT sex, CUME_DIST() OVER (ORDER BY AVG(id) DESC) FROM t1 GROUP BY sex ORDER BY sex;
sex	CUME_DIST() OVER (ORDER BY AVG(id) DESC)
NULL	0.3333333333333333
F	1
M	1
Implicit group aggregate arguments to window function and in
window's ORDER BY clause
SELECT          RANK() OVER (ORDER BY AVG(id)) FROM t1;
RANK() OVER (ORDER BY AVG(id))
1
SELECT          PERCENT_RANK() OVER (ORDER BY AVG(id)) FROM t1;
PERCENT_RANK() OVER (ORDER BY AVG(id))
0
SELECT          CUME_DIST() OVER (ORDER BY AVG(id)) FROM t1;
CUME_DIST() OVER (ORDER BY AVG(id))
1
SELECT AVG(id), RANK() OVER (ORDER BY AVG(id)) FROM t1;
AVG(id)	RANK() OVER (ORDER BY AVG(id))
5.1429	1
SELECT AVG(id), PERCENT_RANK() OVER (ORDER BY AVG(id)) FROM t1;
AVG(id)	PERCENT_RANK() OVER (ORDER BY AVG(id))
5.1429	0
SELECT AVG(id), CUME_DIST() OVER (ORDER BY AVG(id)) FROM t1;
AVG(id)	CUME_DIST() OVER (ORDER BY AVG(id))
5.1429	1
SELECT AVG(id), SUM(AVG(id)) OVER (ORDER BY AVG(id) ROWS UNBOUNDED PRECEDING) FROM t1;
AVG(id)	SUM(AVG(id)) OVER (ORDER BY AVG(id) ROWS UNBOUNDED PRECEDING)
5.1429	5.1429
Several partitions, several window functions over the same window
SELECT sex, id, RANK() OVER (PARTITION BY sex ORDER BY id DESC) FROM t1;
sex	id	RANK() OVER (PARTITION BY sex ORDER BY id DESC)
NULL	11	1
NULL	10	2
F	4	1
F	3	2
F	2	3
M	5	1
M	1	2
SELECT sex, id, PERCENT_RANK() OVER (PARTITION BY sex ORDER BY id DESC) FROM t1;
sex	id	PERCENT_RANK() OVER (PARTITION BY sex ORDER BY id DESC)
NULL	11	0
NULL	10	1
F	4	0
F	3	0.5
F	2	1
M	5	0
M	1	1
SELECT sex, id, CUME_DIST() OVER (PARTITION BY sex ORDER BY id DESC) FROM t1;
sex	id	CUME_DIST() OVER (PARTITION BY sex ORDER BY id DESC)
NULL	11	0.5
NULL	10	1
F	4	0.3333333333333333
F	3	0.6666666666666666
F	2	1
M	5	0.5
M	1	1
SELECT sex, id, RANK() OVER (PARTITION BY sex ORDER BY id ASC) FROM t1;
sex	id	RANK() OVER (PARTITION BY sex ORDER BY id ASC)
NULL	10	1
NULL	11	2
F	2	1
F	3	2
F	4	3
M	1	1
M	5	2
SELECT sex, id, PERCENT_RANK() OVER (PARTITION BY sex ORDER BY id ASC) FROM t1;
sex	id	PERCENT_RANK() OVER (PARTITION BY sex ORDER BY id ASC)
NULL	10	0
NULL	11	1
F	2	0
F	3	0.5
F	4	1
M	1	0
M	5	1
SELECT sex, id, CUME_DIST() OVER (PARTITION BY sex ORDER BY id ASC) FROM t1;
sex	id	CUME_DIST() OVER (PARTITION BY sex ORDER BY id ASC)
NULL	10	0.5
NULL	11	1
F	2	0.3333333333333333
F	3	0.6666666666666666
F	4	1
M	1	0.5
M	5	1
SELECT sex, id, SUM(id) OVER w summ, RANK() OVER w `rank` FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id ASC ROWS UNBOUNDED PRECEDING);
sex	id	summ	rank
NULL	10	10	1
NULL	11	21	2
F	2	2	1
F	3	5	2
F	4	9	3
M	1	1	1
M	5	6	2
SELECT sex, id, SUM(id) OVER w summ, PERCENT_RANK() OVER w `p_rank`,
CUME_DIST() OVER w `c_dist` FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id ASC ROWS UNBOUNDED PRECEDING);
sex	id	summ	p_rank	c_dist
NULL	10	10	0	0.5
NULL	11	21	1	1
F	2	2	0	0.3333333333333333
F	3	5	0.5	0.6666666666666666
F	4	9	1	1
M	1	1	0	0.5
M	5	6	1	1
SELECT sex, id, SUM(id) OVER w summ, RANK() OVER w `rank` FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id ASC ROWS UNBOUNDED PRECEDING) ORDER BY summ;
sex	id	summ	rank
M	1	1	1
F	2	2	1
F	3	5	2
M	5	6	2
F	4	9	3
NULL	10	10	1
NULL	11	21	2
SELECT sex, id, SUM(id) OVER w summ, PERCENT_RANK() OVER w `p_rank`,
CUME_DIST() OVER w `c_dist` FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id ASC ROWS UNBOUNDED PRECEDING) ORDER BY summ;
sex	id	summ	p_rank	c_dist
M	1	1	0	0.5
F	2	2	0	0.3333333333333333
F	3	5	0.5	0.6666666666666666
M	5	6	1	1
F	4	9	1	1
NULL	10	10	0	0.5
NULL	11	21	1	1
SQL 2011 7.11 <window clause>, SR 4. Window specification's ORDER BY or
PARTITION BY cannot reference SELECT list aliases
SELECT  sex, AVG(DISTINCT id),
RANK() OVER w `uids`
    FROM t1 u, t2 WHERE t2.user_id = u.id GROUP BY sex
WINDOW w AS (ORDER BY uids DESC) ORDER BY sex;
ERROR 42S22: Unknown column 'uids' in 'window order by'
SELECT  sex, AVG(DISTINCT id),
RANK() OVER (ORDER BY uids DESC) `uids`
    FROM t1 u, t2 WHERE t2.user_id = u.id
GROUP BY sex  ORDER BY sex;
ERROR 42S22: Unknown column 'uids' in 'window order by'
CREATE TABLE t(d decimal(10,2), date DATE);
INSERT INTO t values (10.4, '2002-06-09');
INSERT INTO t values (20.5, '2002-06-09');
INSERT INTO t values (10.4, '2002-06-10');
INSERT INTO t values (3,    '2002-06-09');
INSERT INTO t values (40.2, '2015-08-01');
INSERT INTO t values (40.2, '2002-06-09');
INSERT INTO t values (5,    '2015-08-01');
SELECT * FROM (SELECT  RANK() OVER (ORDER BY d) AS `rank`, d, date FROM t) alias ORDER BY `rank`, d, date;
rank	d	date
1	3.00	2002-06-09
2	5.00	2015-08-01
3	10.40	2002-06-09
3	10.40	2002-06-10
5	20.50	2002-06-09
6	40.20	2002-06-09
6	40.20	2015-08-01
SELECT * FROM (SELECT  PERCENT_RANK() OVER (ORDER BY d) AS `p_rank`, d, date FROM t) alias ORDER BY `p_rank`, d, date;
p_rank	d	date
0	3.00	2002-06-09
0.16666666666666666	5.00	2015-08-01
0.3333333333333333	10.40	2002-06-09
0.3333333333333333	10.40	2002-06-10
0.6666666666666666	20.50	2002-06-09
0.8333333333333334	40.20	2002-06-09
0.8333333333333334	40.20	2015-08-01
SELECT * FROM (SELECT  CUME_DIST() OVER (ORDER BY d) AS `c_dist`, d, date FROM t) alias ORDER BY `c_dist`, d, date;
c_dist	d	date
0.14285714285714285	3.00	2002-06-09
0.2857142857142857	5.00	2015-08-01
0.5714285714285714	10.40	2002-06-09
0.5714285714285714	10.40	2002-06-10
0.7142857142857143	20.50	2002-06-09
1	40.20	2002-06-09
1	40.20	2015-08-01
SELECT * FROM (SELECT RANK() OVER (ORDER BY date) AS `rank`, date, d FROM t) alias ORDER BY `rank`, d DESC;
rank	date	d
1	2002-06-09	40.20
1	2002-06-09	20.50
1	2002-06-09	10.40
1	2002-06-09	3.00
5	2002-06-10	10.40
6	2015-08-01	40.20
6	2015-08-01	5.00
SELECT * FROM (SELECT PERCENT_RANK() OVER (ORDER BY date) AS `p_rank`, date, d FROM t) alias ORDER BY `p_rank`, d DESC;
p_rank	date	d
0	2002-06-09	40.20
0	2002-06-09	20.50
0	2002-06-09	10.40
0	2002-06-09	3.00
0.6666666666666666	2002-06-10	10.40
0.8333333333333334	2015-08-01	40.20
0.8333333333333334	2015-08-01	5.00
SELECT * FROM (SELECT CUME_DIST() OVER (ORDER BY date) AS `c_dist`, date, d FROM t) alias ORDER BY `c_dist`, d DESC;
c_dist	date	d
0.5714285714285714	2002-06-09	40.20
0.5714285714285714	2002-06-09	20.50
0.5714285714285714	2002-06-09	10.40
0.5714285714285714	2002-06-09	3.00
0.7142857142857143	2002-06-10	10.40
1	2015-08-01	40.20
1	2015-08-01	5.00
DROP TABLE t;
Check that SUM stays that same when it sees NULL values
CREATE TABLE t(i INT, j INT);
INSERT INTO t VALUES (1,NULL);
INSERT INTO t VALUES (1,NULL);
INSERT INTO t VALUES (1,1);
INSERT INTO t VALUES (1,NULL);
INSERT INTO t VALUES (1,2);
INSERT INTO t VALUES (2,1);
INSERT INTO t VALUES (2,2);
INSERT INTO t VALUES (2,NULL);
INSERT INTO t VALUES (2,NULL);
SELECT i, j, SUM(j) OVER (PARTITION BY i  ORDER BY j ROWS UNBOUNDED PRECEDING) FROM t;
i	j	SUM(j) OVER (PARTITION BY i  ORDER BY j ROWS UNBOUNDED PRECEDING)
1	NULL	NULL
1	NULL	NULL
1	NULL	NULL
1	1	1
1	2	3
2	NULL	NULL
2	NULL	NULL
2	1	1
2	2	3
SELECT SUM(id), SUM(SUM(id)) OVER (ORDER BY sex ROWS UNBOUNDED PRECEDING) FROM t1,t2 WHERE t1.id=t2.user_id GROUP BY sex;
SUM(id)	SUM(SUM(id)) OVER (ORDER BY sex ROWS UNBOUNDED PRECEDING)
13	13
7	20
SELECT id, SUM(SUM(id)) OVER (ORDER BY sex ROWS UNBOUNDED PRECEDING) FROM t1,t2 WHERE t1.id=t2.user_id GROUP BY sex;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.id' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT SUM(id) OVER (ORDER BY sex ROWS UNBOUNDED PRECEDING) FROM t1,t2 WHERE t1.id=t2.user_id GROUP BY sex;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.id' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT RANK() OVER w FROM t1,t2 WHERE t1.id=t2.user_id WINDOW w AS (PARTITION BY id ORDER BY sex);
RANK() OVER w
1
1
1
1
1
1
1
SELECT PERCENT_RANK() OVER w FROM t1,t2 WHERE t1.id=t2.user_id WINDOW w AS (PARTITION BY id ORDER BY sex);
PERCENT_RANK() OVER w
0
0
0
0
0
0
0
SELECT CUME_DIST() OVER w FROM t1,t2 WHERE t1.id=t2.user_id WINDOW w AS (PARTITION BY id ORDER BY sex);
CUME_DIST() OVER w
1
1
1
1
1
1
1
SELECT RANK() OVER w FROM (SELECT * FROM t1,t2 WHERE t1.id=t2.user_id) t WINDOW w AS (PARTITION BY id ORDER BY sex);
RANK() OVER w
1
1
1
1
1
1
1
SELECT PERCENT_RANK() OVER w FROM (SELECT * FROM t1,t2 WHERE t1.id=t2.user_id) t WINDOW w AS (PARTITION BY id ORDER BY sex);
PERCENT_RANK() OVER w
0
0
0
0
0
0
0
SELECT CUME_DIST() OVER w FROM (SELECT * FROM t1,t2 WHERE t1.id=t2.user_id) t WINDOW w AS (PARTITION BY id ORDER BY sex);
CUME_DIST() OVER w
1
1
1
1
1
1
1
Two more tests related to fix_fields on arguments and frame clause
in prepared statements.
SELECT NTH_VALUE(id, id) OVER w FROM (SELECT * FROM t1,t2 WHERE t1.id=t2.user_id) t WINDOW w AS (PARTITION BY id ORDER BY sex);
ERROR HY000: Incorrect arguments to nth_value
SELECT SUM(1) OVER w FROM (SELECT * FROM t1,t2 WHERE t1.id=t2.user_id) t
WINDOW w AS (PARTITION BY id ORDER BY sex ROWS id PRECEDING);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'id PRECEDING)' at line 2
Check that aggregate window functions that reference columns not in the SELECT list work
SELECT  SUM(id) OVER (PARTITION BY sex ORDER BY id ROWS UNBOUNDED PRECEDING) summ, sex FROM t1;
summ	sex
10	NULL
21	NULL
2	F
5	F
9	F
1	M
6	M
CREATE TABLE t3(t3_id INT, k INT);
INSERT INTO t3 VALUES (0, 0);
INSERT INTO t3 VALUES (0, 0);
INSERT INTO t3 VALUES (2, 0);
INSERT INTO t3 VALUES (2, 0);
INSERT INTO t3 VALUES (4, 0);
INSERT INTO t3 VALUES (4, 0);
INSERT INTO t3 VALUES (6, 0);
INSERT INTO t3 VALUES (6, 0);
INSERT INTO t3 VALUES (8, 0);
INSERT INTO t3 VALUES (8, 0);
INSERT INTO t3 VALUES (1, 1);
INSERT INTO t3 VALUES (1, 1);
INSERT INTO t3 VALUES (3, 1);
INSERT INTO t3 VALUES (3, 1);
INSERT INTO t3 VALUES (5, 1);
INSERT INTO t3 VALUES (5, 1);
INSERT INTO t3 VALUES (7, 1);
INSERT INTO t3 VALUES (7, 1);
INSERT INTO t3 VALUES (9, 1);
INSERT INTO t3 VALUES (9, 1);
Broke initial CUME_DIST frame buffer positioning locality (DBUG assert)
SELECT t3_id, AVG(t3_id) OVER w,
CUME_DIST() OVER w,
k FROM t3
WINDOW w AS (PARTITION BY k ORDER BY t3_id
RANGE BETWEEN 4 PRECEDING AND 2 PRECEDING);
t3_id	AVG(t3_id) OVER w	CUME_DIST() OVER w	k
0	NULL	0.2	0
0	NULL	0.2	0
2	0.0000	0.4	0
2	0.0000	0.4	0
4	1.0000	0.6	0
4	1.0000	0.6	0
6	3.0000	0.8	0
6	3.0000	0.8	0
8	5.0000	1	0
8	5.0000	1	0
1	NULL	0.2	1
1	NULL	0.2	1
3	1.0000	0.4	1
3	1.0000	0.4	1
5	2.0000	0.6	1
5	2.0000	0.6	1
7	4.0000	0.8	1
7	4.0000	0.8	1
9	6.0000	1	1
9	6.0000	1	1
Reuse of already evaluated peer of current row due to dynamic_aggregate present
SELECT t3_id, SUM(t3_id) OVER w,
CUME_DIST() OVER w,
LEAD(t3_id, 2) OVER w `lead2`,
NTH_VALUE(t3_id, 3) OVER w `nth`,
k FROM t3
WINDOW w AS (PARTITION BY k ORDER BY t3_id);
t3_id	SUM(t3_id) OVER w	CUME_DIST() OVER w	lead2	nth	k
0	0	0.2	2	NULL	0
0	0	0.2	2	NULL	0
2	4	0.4	4	2	0
2	4	0.4	4	2	0
4	12	0.6	6	2	0
4	12	0.6	6	2	0
6	24	0.8	8	2	0
6	24	0.8	8	2	0
8	40	1	NULL	2	0
8	40	1	NULL	2	0
1	2	0.2	3	NULL	1
1	2	0.2	3	NULL	1
3	8	0.4	5	3	1
3	8	0.4	5	3	1
5	18	0.6	7	3	1
5	18	0.6	7	3	1
7	32	0.8	9	3	1
7	32	0.8	9	3	1
9	50	1	NULL	3	1
9	50	1	NULL	3	1
Same semantics without reuse p.t.
SELECT t3_id, SUM(t3_id) OVER w,
CUME_DIST() OVER w,
LEAD(t3_id, 2) OVER w `lead2`,
NTH_VALUE(t3_id, 3) OVER w `nth`,
k FROM t3
WINDOW w AS (PARTITION BY k ORDER BY t3_id RANGE UNBOUNDED PRECEDING);
t3_id	SUM(t3_id) OVER w	CUME_DIST() OVER w	lead2	nth	k
0	0	0.2	2	NULL	0
0	0	0.2	2	NULL	0
2	4	0.4	4	2	0
2	4	0.4	4	2	0
4	12	0.6	6	2	0
4	12	0.6	6	2	0
6	24	0.8	8	2	0
6	24	0.8	8	2	0
8	40	1	NULL	2	0
8	40	1	NULL	2	0
1	2	0.2	3	NULL	1
1	2	0.2	3	NULL	1
3	8	0.4	5	3	1
3	8	0.4	5	3	1
5	18	0.6	7	3	1
5	18	0.6	7	3	1
7	32	0.8	9	3	1
7	32	0.8	9	3	1
9	50	1	NULL	3	1
9	50	1	NULL	3	1
Followup to Bug#25756549
SELECT t3_id, LAST_VALUE(t3_id) OVER w, k FROM t3
WINDOW w AS (PARTITION BY k ORDER BY t3_id RANGE UNBOUNDED PRECEDING);
t3_id	LAST_VALUE(t3_id) OVER w	k
0	0	0
0	0	0
2	2	0
2	2	0
4	4	0
4	4	0
6	6	0
6	6	0
8	8	0
8	8	0
1	1	1
1	1	1
3	3	1
3	3	1
5	5	1
5	5	1
7	7	1
7	7	1
9	9	1
9	9	1
SELECT t3_id, LAST_VALUE(t3_id) OVER w, k FROM t3
WINDOW w AS (PARTITION BY k ORDER BY t3_id RANGE 2 PRECEDING);
t3_id	LAST_VALUE(t3_id) OVER w	k
0	0	0
0	0	0
2	2	0
2	2	0
4	4	0
4	4	0
6	6	0
6	6	0
8	8	0
8	8	0
1	1	1
1	1	1
3	3	1
3	3	1
5	5	1
5	5	1
7	7	1
7	7	1
9	9	1
9	9	1
DROP TABLE t3;
----------------------------------------------------------------------
-    Some ROW_NUMBER tests
----------------------------------------------------------------------
SELECT user_id, ROW_NUMBER() OVER (PARTITION BY user_id) FROM t2 t1;
user_id	ROW_NUMBER() OVER (PARTITION BY user_id)
1	1
1	2
2	1
3	1
4	1
4	2
5	1
SELECT * FROM t1,t2 WHERE t1.id=t2.user_id;
id	sex	user_id	date
1	M	1	2002-06-09
1	M	1	2002-06-09
2	F	2	2002-06-09
3	F	3	2002-06-09
4	F	4	2002-06-09
4	F	4	2002-06-09
5	M	5	2002-06-09
SELECT sex, id, date, ROW_NUMBER() OVER w AS row_no, RANK() OVER w AS `rank` FROM t1,t2
WHERE t1.id=t2.user_id
WINDOW w AS (PARTITION BY id ORDER BY sex);
sex	id	date	row_no	rank
M	1	2002-06-09	1	1
M	1	2002-06-09	2	1
F	2	2002-06-09	1	1
F	3	2002-06-09	1	1
F	4	2002-06-09	1	1
F	4	2002-06-09	2	1
M	5	2002-06-09	1	1
SELECT sex, id, date, ROW_NUMBER() OVER w AS row_no, RANK() OVER w AS `rank` FROM t1,t2
WHERE t1.id=t2.user_id
WINDOW w AS (PARTITION BY date ORDER BY id);
sex	id	date	row_no	rank
M	1	2002-06-09	1	1
M	1	2002-06-09	2	1
F	2	2002-06-09	3	3
F	3	2002-06-09	4	4
F	4	2002-06-09	5	5
F	4	2002-06-09	6	5
M	5	2002-06-09	7	7
Coverage for ::val_str
SELECT CONCAT(ROW_NUMBER() OVER (), 1);
CONCAT(ROW_NUMBER() OVER (), 1)
11
SELECT CONCAT(RANK() OVER (ORDER BY NULL), 1);
CONCAT(RANK() OVER (ORDER BY NULL), 1)
11
SELECT CONCAT(CUME_DIST() OVER (ORDER BY NULL), 1);
CONCAT(CUME_DIST() OVER (ORDER BY NULL), 1)
11
SELECT CONCAT(PERCENT_RANK() OVER (ORDER BY NULL), 1);
CONCAT(PERCENT_RANK() OVER (ORDER BY NULL), 1)
01
SELECT CONCAT(NTILE(3) OVER (ORDER BY NULL), 1);
CONCAT(NTILE(3) OVER (ORDER BY NULL), 1)
11
----------------------------------------------------------------------
-    Window function in subquery
----------------------------------------------------------------------
SELECT  date,id, RANK() OVER w AS `rank` FROM t1,t2 WINDOW w AS (PARTITION BY date ORDER BY id);
date	id	rank
2002-06-09	1	1
2002-06-09	1	1
2002-06-09	1	1
2002-06-09	1	1
2002-06-09	1	1
2002-06-09	1	1
2002-06-09	1	1
2002-06-09	2	8
2002-06-09	2	8
2002-06-09	2	8
2002-06-09	2	8
2002-06-09	2	8
2002-06-09	2	8
2002-06-09	2	8
2002-06-09	3	15
2002-06-09	3	15
2002-06-09	3	15
2002-06-09	3	15
2002-06-09	3	15
2002-06-09	3	15
2002-06-09	3	15
2002-06-09	4	22
2002-06-09	4	22
2002-06-09	4	22
2002-06-09	4	22
2002-06-09	4	22
2002-06-09	4	22
2002-06-09	4	22
2002-06-09	5	29
2002-06-09	5	29
2002-06-09	5	29
2002-06-09	5	29
2002-06-09	5	29
2002-06-09	5	29
2002-06-09	5	29
2002-06-09	10	36
2002-06-09	10	36
2002-06-09	10	36
2002-06-09	10	36
2002-06-09	10	36
2002-06-09	10	36
2002-06-09	10	36
2002-06-09	11	43
2002-06-09	11	43
2002-06-09	11	43
2002-06-09	11	43
2002-06-09	11	43
2002-06-09	11	43
2002-06-09	11	43
SELECT * from (SELECT  date,id, RANK() OVER w AS `rank` FROM t1,t2 WINDOW w AS (PARTITION BY date ORDER BY id)) t;
date	id	rank
2002-06-09	1	1
2002-06-09	1	1
2002-06-09	1	1
2002-06-09	1	1
2002-06-09	1	1
2002-06-09	1	1
2002-06-09	1	1
2002-06-09	2	8
2002-06-09	2	8
2002-06-09	2	8
2002-06-09	2	8
2002-06-09	2	8
2002-06-09	2	8
2002-06-09	2	8
2002-06-09	3	15
2002-06-09	3	15
2002-06-09	3	15
2002-06-09	3	15
2002-06-09	3	15
2002-06-09	3	15
2002-06-09	3	15
2002-06-09	4	22
2002-06-09	4	22
2002-06-09	4	22
2002-06-09	4	22
2002-06-09	4	22
2002-06-09	4	22
2002-06-09	4	22
2002-06-09	5	29
2002-06-09	5	29
2002-06-09	5	29
2002-06-09	5	29
2002-06-09	5	29
2002-06-09	5	29
2002-06-09	5	29
2002-06-09	10	36
2002-06-09	10	36
2002-06-09	10	36
2002-06-09	10	36
2002-06-09	10	36
2002-06-09	10	36
2002-06-09	10	36
2002-06-09	11	43
2002-06-09	11	43
2002-06-09	11	43
2002-06-09	11	43
2002-06-09	11	43
2002-06-09	11	43
2002-06-09	11	43
SELECT * from (SELECT  date,id, PERCENT_RANK() OVER w AS `p_rank`, CUME_DIST() OVER w as `c_dist` FROM t1,t2 WINDOW w AS (PARTITION BY date ORDER BY id)) t;
date	id	p_rank	c_dist
2002-06-09	1	0	0.14285714285714285
2002-06-09	1	0	0.14285714285714285
2002-06-09	1	0	0.14285714285714285
2002-06-09	1	0	0.14285714285714285
2002-06-09	1	0	0.14285714285714285
2002-06-09	1	0	0.14285714285714285
2002-06-09	1	0	0.14285714285714285
2002-06-09	2	0.14583333333333334	0.2857142857142857
2002-06-09	2	0.14583333333333334	0.2857142857142857
2002-06-09	2	0.14583333333333334	0.2857142857142857
2002-06-09	2	0.14583333333333334	0.2857142857142857
2002-06-09	2	0.14583333333333334	0.2857142857142857
2002-06-09	2	0.14583333333333334	0.2857142857142857
2002-06-09	2	0.14583333333333334	0.2857142857142857
2002-06-09	3	0.2916666666666667	0.42857142857142855
2002-06-09	3	0.2916666666666667	0.42857142857142855
2002-06-09	3	0.2916666666666667	0.42857142857142855
2002-06-09	3	0.2916666666666667	0.42857142857142855
2002-06-09	3	0.2916666666666667	0.42857142857142855
2002-06-09	3	0.2916666666666667	0.42857142857142855
2002-06-09	3	0.2916666666666667	0.42857142857142855
2002-06-09	4	0.4375	0.5714285714285714
2002-06-09	4	0.4375	0.5714285714285714
2002-06-09	4	0.4375	0.5714285714285714
2002-06-09	4	0.4375	0.5714285714285714
2002-06-09	4	0.4375	0.5714285714285714
2002-06-09	4	0.4375	0.5714285714285714
2002-06-09	4	0.4375	0.5714285714285714
2002-06-09	5	0.5833333333333334	0.7142857142857143
2002-06-09	5	0.5833333333333334	0.7142857142857143
2002-06-09	5	0.5833333333333334	0.7142857142857143
2002-06-09	5	0.5833333333333334	0.7142857142857143
2002-06-09	5	0.5833333333333334	0.7142857142857143
2002-06-09	5	0.5833333333333334	0.7142857142857143
2002-06-09	5	0.5833333333333334	0.7142857142857143
2002-06-09	10	0.7291666666666666	0.8571428571428571
2002-06-09	10	0.7291666666666666	0.8571428571428571
2002-06-09	10	0.7291666666666666	0.8571428571428571
2002-06-09	10	0.7291666666666666	0.8571428571428571
2002-06-09	10	0.7291666666666666	0.8571428571428571
2002-06-09	10	0.7291666666666666	0.8571428571428571
2002-06-09	10	0.7291666666666666	0.8571428571428571
2002-06-09	11	0.875	1
2002-06-09	11	0.875	1
2002-06-09	11	0.875	1
2002-06-09	11	0.875	1
2002-06-09	11	0.875	1
2002-06-09	11	0.875	1
2002-06-09	11	0.875	1
----------------------------------------------------------------------
-    Window function in parent and subquery
----------------------------------------------------------------------
SELECT t.*, SUM(t.`rank`) OVER (ROWS UNBOUNDED PRECEDING) FROM
(SELECT sex, id, date, ROW_NUMBER() OVER w AS row_no, RANK() OVER w AS `rank` FROM t1,t2
WHERE t1.id=t2.user_id
WINDOW w AS (PARTITION BY date ORDER BY id)
) AS t;
sex	id	date	row_no	rank	SUM(t.`rank`) OVER (ROWS UNBOUNDED PRECEDING)
M	1	2002-06-09	1	1	1
M	1	2002-06-09	2	1	2
F	2	2002-06-09	3	3	5
F	3	2002-06-09	4	4	9
F	4	2002-06-09	5	5	14
F	4	2002-06-09	6	5	19
M	5	2002-06-09	7	7	26
SELECT t.*, SUM(t.`p_rank`) OVER (ROWS UNBOUNDED PRECEDING) FROM
(SELECT sex, id, date, ROW_NUMBER() OVER w AS row_no, PERCENT_RANK() OVER w AS `p_rank`,
CUME_DIST() OVER w as `c_dist` FROM t1,t2
WHERE t1.id=t2.user_id
WINDOW w AS (PARTITION BY date ORDER BY id)
) AS t;
sex	id	date	row_no	p_rank	c_dist	SUM(t.`p_rank`) OVER (ROWS UNBOUNDED PRECEDING)
M	1	2002-06-09	1	0	0.2857142857142857	0
M	1	2002-06-09	2	0	0.2857142857142857	0
F	2	2002-06-09	3	0.3333333333333333	0.42857142857142855	0.3333333333333333
F	3	2002-06-09	4	0.5	0.5714285714285714	0.8333333333333333
F	4	2002-06-09	5	0.6666666666666666	0.8571428571428571	1.5
F	4	2002-06-09	6	0.6666666666666666	0.8571428571428571	2.1666666666666665
M	5	2002-06-09	7	1	1	3.1666666666666665
----------------------------------------------------------------------
-    Multiple windows
----------------------------------------------------------------------
SELECT t1.*, RANK() OVER (ORDER BY sex), SUM(id) OVER (ORDER BY sex,id ROWS UNBOUNDED PRECEDING) FROM t1;
id	sex	RANK() OVER (ORDER BY sex)	SUM(id) OVER (ORDER BY sex,id ROWS UNBOUNDED PRECEDING)
10	NULL	1	10
11	NULL	1	21
2	F	3	23
3	F	3	26
4	F	3	30
1	M	6	31
5	M	6	36
SELECT t1.*, PERCENT_RANK() OVER (ORDER BY sex), SUM(id) OVER (ORDER BY sex,id ROWS UNBOUNDED PRECEDING) FROM t1;
id	sex	PERCENT_RANK() OVER (ORDER BY sex)	SUM(id) OVER (ORDER BY sex,id ROWS UNBOUNDED PRECEDING)
10	NULL	0	10
11	NULL	0	21
2	F	0.3333333333333333	23
3	F	0.3333333333333333	26
4	F	0.3333333333333333	30
1	M	0.8333333333333334	31
5	M	0.8333333333333334	36
SELECT t1.*, CUME_DIST() OVER (ORDER BY sex), SUM(id) OVER (ORDER BY sex,id ROWS UNBOUNDED PRECEDING) FROM t1;
id	sex	CUME_DIST() OVER (ORDER BY sex)	SUM(id) OVER (ORDER BY sex,id ROWS UNBOUNDED PRECEDING)
10	NULL	0.2857142857142857	10
11	NULL	0.2857142857142857	21
2	F	0.7142857142857143	23
3	F	0.7142857142857143	26
4	F	0.7142857142857143	30
1	M	1	31
5	M	1	36
SELECT * from (SELECT t1.*, SUM(id) OVER (ROWS UNBOUNDED PRECEDING), RANK() OVER (ORDER BY sex) FROM t1) alias ORDER BY id;
id	sex	SUM(id) OVER (ROWS UNBOUNDED PRECEDING)	RANK() OVER (ORDER BY sex)
1	M	1	6
2	F	3	3
3	F	6	3
4	F	10	3
5	M	15	6
10	NULL	25	1
11	NULL	36	1
SELECT * from (SELECT t1.*, SUM(id) OVER (ROWS UNBOUNDED PRECEDING), PERCENT_RANK() OVER (ORDER BY sex) FROM t1) alias ORDER BY id;
id	sex	SUM(id) OVER (ROWS UNBOUNDED PRECEDING)	PERCENT_RANK() OVER (ORDER BY sex)
1	M	1	0.8333333333333334
2	F	3	0.3333333333333333
3	F	6	0.3333333333333333
4	F	10	0.3333333333333333
5	M	15	0.8333333333333334
10	NULL	25	0
11	NULL	36	0
SELECT * from (SELECT t1.*, SUM(id) OVER (ROWS UNBOUNDED PRECEDING), CUME_DIST() OVER (ORDER BY sex) FROM t1) alias ORDER BY id;
id	sex	SUM(id) OVER (ROWS UNBOUNDED PRECEDING)	CUME_DIST() OVER (ORDER BY sex)
1	M	1	1
2	F	3	0.7142857142857143
3	F	6	0.7142857142857143
4	F	10	0.7142857142857143
5	M	15	1
10	NULL	25	0.2857142857142857
11	NULL	36	0.2857142857142857
SELECT t1.*, SUM(id) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING),
RANK() OVER (ORDER BY sex,id),
ROW_NUMBER() OVER (ORDER BY sex,id)
FROM t1;
id	sex	SUM(id) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING)	RANK() OVER (ORDER BY sex,id)	ROW_NUMBER() OVER (ORDER BY sex,id)
1	M	1	6	6
10	NULL	25	1	1
11	NULL	36	2	2
2	F	3	3	3
3	F	6	4	4
4	F	10	5	5
5	M	15	7	7
SELECT t1.*, SUM(id) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING),
PERCENT_RANK() OVER (ORDER BY sex,id),
CUME_DIST() OVER (ORDER BY sex,id),
ROW_NUMBER() OVER (ORDER BY sex,id)
FROM t1;
id	sex	SUM(id) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING)	PERCENT_RANK() OVER (ORDER BY sex,id)	CUME_DIST() OVER (ORDER BY sex,id)	ROW_NUMBER() OVER (ORDER BY sex,id)
1	M	1	0.8333333333333334	0.8571428571428571	6
10	NULL	25	0	0.14285714285714285	1
11	NULL	36	0.16666666666666666	0.2857142857142857	2
2	F	3	0.3333333333333333	0.42857142857142855	3
3	F	6	0.5	0.5714285714285714	4
4	F	10	0.6666666666666666	0.7142857142857143	5
5	M	15	1	1	7
a little more windows + subquery
SELECT t.*, SUM(id + r00 + r01) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING) AS s FROM (
SELECT t1.*,
RANK() OVER (ORDER BY sex, id) AS r00,
RANK() OVER (ORDER BY sex, id DESC) AS r01,
RANK() OVER (ORDER BY sex, id DESC) AS r02,
RANK() OVER (PARTITION BY id ORDER BY sex) AS r03,
RANK() OVER (ORDER BY sex,id) AS r04,
RANK() OVER (ORDER BY sex,id) AS r05,
RANK() OVER (ORDER BY sex, id) AS r06,
RANK() OVER (ORDER BY sex, id) AS r07,
RANK() OVER (ORDER BY sex, id) AS r08,
RANK() OVER (ORDER BY sex, id) AS r09,
RANK() OVER (ORDER BY sex, id) AS r10,
RANK() OVER (ORDER BY sex, id) AS r11,
RANK() OVER (ORDER BY sex, id) AS r12,
RANK() OVER (ORDER BY sex, id) AS r13,
RANK() OVER (ORDER BY sex, id) AS r14
FROM t1) t;
id	sex	r00	r01	r02	r03	r04	r05	r06	r07	r08	r09	r10	r11	r12	r13	r14	s
1	M	6	7	7	1	6	6	6	6	6	6	6	6	6	6	6	14
2	F	3	5	5	1	3	3	3	3	3	3	3	3	3	3	3	24
3	F	4	4	4	1	4	4	4	4	4	4	4	4	4	4	4	35
4	F	5	3	3	1	5	5	5	5	5	5	5	5	5	5	5	47
5	M	7	6	6	1	7	7	7	7	7	7	7	7	7	7	7	65
10	NULL	1	2	2	1	1	1	1	1	1	1	1	1	1	1	1	78
11	NULL	2	1	1	1	2	2	2	2	2	2	2	2	2	2	2	92
With LIMIT
SELECT t.*, SUM(id + r00 + r01) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING) AS s FROM (
SELECT t1.*,
RANK() OVER (ORDER BY sex, id) AS r00,
RANK() OVER (ORDER BY sex DESC, id) AS r01,
RANK() OVER (ORDER BY sex, id DESC) AS r02,
RANK() OVER (PARTITION BY id ORDER BY sex) AS r03,
RANK() OVER (ORDER BY sex, id) AS r04,
RANK() OVER (ORDER BY sex, id) AS r05,
RANK() OVER (ORDER BY sex, id) AS r06,
RANK() OVER (ORDER BY sex, id) AS r07,
RANK() OVER (ORDER BY sex, id) AS r08,
RANK() OVER (ORDER BY sex, id) AS r09,
RANK() OVER (ORDER BY sex, id) AS r10,
RANK() OVER (ORDER BY sex, id) AS r11,
RANK() OVER (ORDER BY sex, id) AS r12,
RANK() OVER (ORDER BY sex, id) AS r13,
RANK() OVER (ORDER BY sex, id) AS r14
FROM t1 LIMIT 4) t;
id	sex	r00	r01	r02	r03	r04	r05	r06	r07	r08	r09	r10	r11	r12	r13	r14	s
3	F	4	4	4	1	4	4	4	4	4	4	4	4	4	4	4	11
4	F	5	5	3	1	5	5	5	5	5	5	5	5	5	5	5	25
10	NULL	1	6	2	1	1	1	1	1	1	1	1	1	1	1	1	42
11	NULL	2	7	1	1	2	2	2	2	2	2	2	2	2	2	2	62
SELECT t.*, SUM(id + r00 + r01) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING) AS s FROM (
SELECT t1.*,
PERCENT_RANK() OVER (ORDER BY sex, id) AS r00,
PERCENT_RANK() OVER (ORDER BY sex DESC, id) AS r01,
PERCENT_RANK() OVER (ORDER BY sex, id DESC) AS r02,
PERCENT_RANK() OVER (PARTITION BY id ORDER BY sex) AS r03,
PERCENT_RANK() OVER (ORDER BY sex, id) AS r04,
PERCENT_RANK() OVER (ORDER BY sex, id) AS r05,
PERCENT_RANK() OVER (ORDER BY sex, id) AS r06,
PERCENT_RANK() OVER (ORDER BY sex, id) AS r07,
PERCENT_RANK() OVER (ORDER BY sex, id) AS r08,
PERCENT_RANK() OVER (ORDER BY sex, id) AS r09,
PERCENT_RANK() OVER (ORDER BY sex, id) AS r10,
PERCENT_RANK() OVER (ORDER BY sex, id) AS r11,
PERCENT_RANK() OVER (ORDER BY sex, id) AS r12,
PERCENT_RANK() OVER (ORDER BY sex, id) AS r13,
PERCENT_RANK() OVER (ORDER BY sex, id) AS r14
FROM t1) t;
id	sex	r00	r01	r02	r03	r04	r05	r06	r07	r08	r09	r10	r11	r12	r13	r14	s
1	M	0.8333333333333334	0	1	0	0.8333333333333334	0.8333333333333334	0.8333333333333334	0.8333333333333334	0.8333333333333334	0.8333333333333334	0.8333333333333334	0.8333333333333334	0.8333333333333334	0.8333333333333334	0.8333333333333334	1.8333333333333335
2	F	0.3333333333333333	0.3333333333333333	0.6666666666666666	0	0.3333333333333333	0.3333333333333333	0.3333333333333333	0.3333333333333333	0.3333333333333333	0.3333333333333333	0.3333333333333333	0.3333333333333333	0.3333333333333333	0.3333333333333333	0.3333333333333333	4.5
3	F	0.5	0.5	0.5	0	0.5	0.5	0.5	0.5	0.5	0.5	0.5	0.5	0.5	0.5	0.5	8.5
4	F	0.6666666666666666	0.6666666666666666	0.3333333333333333	0	0.6666666666666666	0.6666666666666666	0.6666666666666666	0.6666666666666666	0.6666666666666666	0.6666666666666666	0.6666666666666666	0.6666666666666666	0.6666666666666666	0.6666666666666666	0.6666666666666666	13.833333333333334
5	M	1	0.16666666666666666	0.8333333333333334	0	1	1	1	1	1	1	1	1	1	1	1	20
10	NULL	0	0.8333333333333334	0.16666666666666666	0	0	0	0	0	0	0	0	0	0	0	0	30.833333333333336
11	NULL	0.16666666666666666	1	0	0	0.16666666666666666	0.16666666666666666	0.16666666666666666	0.16666666666666666	0.16666666666666666	0.16666666666666666	0.16666666666666666	0.16666666666666666	0.16666666666666666	0.16666666666666666	0.16666666666666666	43
SELECT t.*, SUM(id + r00 + r01) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING) AS s FROM (
SELECT t1.*,
CUME_DIST() OVER (ORDER BY sex, id) AS r00,
CUME_DIST() OVER (ORDER BY sex DESC, id) AS r01,
CUME_DIST() OVER (ORDER BY sex, id DESC) AS r02,
CUME_DIST() OVER (PARTITION BY id ORDER BY sex) AS r03,
CUME_DIST() OVER (ORDER BY sex, id) AS r04,
CUME_DIST() OVER (ORDER BY sex, id) AS r05,
CUME_DIST() OVER (ORDER BY sex, id) AS r06,
CUME_DIST() OVER (ORDER BY sex, id) AS r07,
CUME_DIST() OVER (ORDER BY sex, id) AS r08,
CUME_DIST() OVER (ORDER BY sex, id) AS r09,
CUME_DIST() OVER (ORDER BY sex, id) AS r10,
CUME_DIST() OVER (ORDER BY sex, id) AS r11,
CUME_DIST() OVER (ORDER BY sex, id) AS r12,
CUME_DIST() OVER (ORDER BY sex, id) AS r13,
CUME_DIST() OVER (ORDER BY sex, id) AS r14
FROM t1) t;
id	sex	r00	r01	r02	r03	r04	r05	r06	r07	r08	r09	r10	r11	r12	r13	r14	s
1	M	0.8571428571428571	0.14285714285714285	1	1	0.8571428571428571	0.8571428571428571	0.8571428571428571	0.8571428571428571	0.8571428571428571	0.8571428571428571	0.8571428571428571	0.8571428571428571	0.8571428571428571	0.8571428571428571	0.8571428571428571	2
2	F	0.42857142857142855	0.42857142857142855	0.7142857142857143	1	0.42857142857142855	0.42857142857142855	0.42857142857142855	0.42857142857142855	0.42857142857142855	0.42857142857142855	0.42857142857142855	0.42857142857142855	0.42857142857142855	0.42857142857142855	0.42857142857142855	4.857142857142857
3	F	0.5714285714285714	0.5714285714285714	0.5714285714285714	1	0.5714285714285714	0.5714285714285714	0.5714285714285714	0.5714285714285714	0.5714285714285714	0.5714285714285714	0.5714285714285714	0.5714285714285714	0.5714285714285714	0.5714285714285714	0.5714285714285714	9
4	F	0.7142857142857143	0.7142857142857143	0.42857142857142855	1	0.7142857142857143	0.7142857142857143	0.7142857142857143	0.7142857142857143	0.7142857142857143	0.7142857142857143	0.7142857142857143	0.7142857142857143	0.7142857142857143	0.7142857142857143	0.7142857142857143	14.428571428571429
5	M	1	0.2857142857142857	0.8571428571428571	1	1	1	1	1	1	1	1	1	1	1	1	20.714285714285715
10	NULL	0.14285714285714285	0.8571428571428571	0.2857142857142857	1	0.14285714285714285	0.14285714285714285	0.14285714285714285	0.14285714285714285	0.14285714285714285	0.14285714285714285	0.14285714285714285	0.14285714285714285	0.14285714285714285	0.14285714285714285	0.14285714285714285	31.714285714285715
11	NULL	0.2857142857142857	1	0.14285714285714285	1	0.2857142857142857	0.2857142857142857	0.2857142857142857	0.2857142857142857	0.2857142857142857	0.2857142857142857	0.2857142857142857	0.2857142857142857	0.2857142857142857	0.2857142857142857	0.2857142857142857	44
FLUSH STATUS;
SELECT t.*, SUM(id + r00 + r01) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING) AS s FROM (
SELECT t1.*,
RANK() OVER w00 AS r00,
RANK() OVER w01 AS r01,
RANK() OVER w02 AS r02,
RANK() OVER w03 AS r03,
RANK() OVER w04 AS r04,
RANK() OVER w05 AS r05,
RANK() OVER w06 AS r06,
RANK() OVER w07 AS r07,
RANK() OVER w08 AS r08,
RANK() OVER w09 AS r09,
RANK() OVER w10 AS r10,
RANK() OVER w11 AS r11,
RANK() OVER w12 AS r12,
RANK() OVER w13 AS r13,
RANK() OVER w14 AS r14
FROM t1
WINDOW w00 AS (ORDER BY sex),
w01 AS (ORDER BY sex DESC),
w02 AS (ORDER BY sex, id DESC),
w03 AS (PARTITION BY id ORDER BY sex),
w04 AS (ORDER BY sex),
w05 AS (ORDER BY sex),
w06 AS (ORDER BY sex),
w07 AS (ORDER BY sex),
w08 AS (ORDER BY sex),
w09 AS (ORDER BY sex),
w10 AS (ORDER BY sex),
w11 AS (ORDER BY sex),
w12 AS (ORDER BY sex),
w13 AS (ORDER BY sex),
w14 AS (ORDER BY sex)) t;
id	sex	r00	r01	r02	r03	r04	r05	r06	r07	r08	r09	r10	r11	r12	r13	r14	s
1	M	6	1	7	1	6	6	6	6	6	6	6	6	6	6	6	8
2	F	3	3	5	1	3	3	3	3	3	3	3	3	3	3	3	16
3	F	3	3	4	1	3	3	3	3	3	3	3	3	3	3	3	25
4	F	3	3	3	1	3	3	3	3	3	3	3	3	3	3	3	35
5	M	6	1	6	1	6	6	6	6	6	6	6	6	6	6	6	47
10	NULL	1	6	2	1	1	1	1	1	1	1	1	1	1	1	1	64
11	NULL	1	6	1	1	1	1	1	1	1	1	1	1	1	1	1	82
SHOW STATUS LIKE 'Created_tmp_tables';
Variable_name	Value
Created_tmp_tables	15
Show sort elimination for the above
EXPLAIN FORMAT=JSON SELECT t.*, SUM(id + r00 + r01) OVER (ORDER BY id ROWS UNBOUNDED PRECEDING) AS s FROM (
SELECT t1.*,
RANK() OVER w00 AS r00,
RANK() OVER w01 AS r01,
RANK() OVER w02 AS r02,
RANK() OVER w03 AS r03,
RANK() OVER w04 AS r04,
RANK() OVER w05 AS r05,
RANK() OVER w06 AS r06,
RANK() OVER w07 AS r07,
RANK() OVER w08 AS r08,
RANK() OVER w09 AS r09,
RANK() OVER w10 AS r10,
RANK() OVER w11 AS r11,
RANK() OVER w12 AS r12,
RANK() OVER w13 AS r13,
RANK() OVER w14 AS r14
FROM t1
WINDOW w00 AS (ORDER BY sex),
w01 AS (ORDER BY sex DESC),
w02 AS (ORDER BY sex, id DESC),
w03 AS (PARTITION BY id ORDER BY sex),
w04 AS (ORDER BY sex),
w05 AS (ORDER BY sex),
w06 AS (ORDER BY sex),
w07 AS (ORDER BY sex),
w08 AS (ORDER BY sex),
w09 AS (ORDER BY sex),
w10 AS (ORDER BY sex),
w11 AS (ORDER BY sex),
w12 AS (ORDER BY sex),
w13 AS (ORDER BY sex),
w14 AS (ORDER BY sex)) t;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.29"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_filesort": true,
          "filesort_key": [
            "`id`"
          ],
          "functions": [
            "sum"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "7.00"
      },
      "table": {
        "table_name": "t",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "2.59",
          "eval_cost": "0.70",
          "prefix_cost": "3.29",
          "data_read_per_join": "952"
        },
        "used_columns": [
          "id",
          "sex",
          "r00",
          "r01",
          "r02",
          "r03",
          "r04",
          "r05",
          "r06",
          "r07",
          "r08",
          "r09",
          "r10",
          "r11",
          "r12",
          "r13",
          "r14"
        ],
        "materialized_from_subquery": {
          "using_temporary_table": true,
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "28.95"
            },
            "windowing": {
              "windows": [
                {
                  "name": "w00",
                  "definition_position": 1,
                  "using_temporary_table": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`sex`"
                  ],
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w04",
                  "definition_position": 5,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w05",
                  "definition_position": 6,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w06",
                  "definition_position": 7,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w07",
                  "definition_position": 8,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w08",
                  "definition_position": 9,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w09",
                  "definition_position": 10,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w10",
                  "definition_position": 11,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w11",
                  "definition_position": 12,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w12",
                  "definition_position": 13,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w13",
                  "definition_position": 14,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w14",
                  "definition_position": 15,
                  "using_temporary_table": true,
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w03",
                  "definition_position": 4,
                  "using_temporary_table": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`id`",
                    "`sex`"
                  ],
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w01",
                  "definition_position": 2,
                  "using_temporary_table": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`sex` desc"
                  ],
                  "functions": [
                    "rank"
                  ]
                },
                {
                  "name": "w02",
                  "definition_position": 3,
                  "last_executed_window": true,
                  "using_filesort": true,
                  "filesort_key": [
                    "`sex`",
                    "`id` desc"
                  ],
                  "functions": [
                    "rank"
                  ]
                }
              ],
              "cost_info": {
                "sort_cost": "28.00"
              },
              "table": {
                "table_name": "t1",
                "access_type": "ALL",
                "rows_examined_per_scan": 7,
                "rows_produced_per_join": 7,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.70",
                  "prefix_cost": "0.95",
                  "data_read_per_join": "112"
                },
                "used_columns": [
                  "id",
                  "sex"
                ]
              }
            }
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `t`.`id` AS `id`,`t`.`sex` AS `sex`,`t`.`r00` AS `r00`,`t`.`r01` AS `r01`,`t`.`r02` AS `r02`,`t`.`r03` AS `r03`,`t`.`r04` AS `r04`,`t`.`r05` AS `r05`,`t`.`r06` AS `r06`,`t`.`r07` AS `r07`,`t`.`r08` AS `r08`,`t`.`r09` AS `r09`,`t`.`r10` AS `r10`,`t`.`r11` AS `r11`,`t`.`r12` AS `r12`,`t`.`r13` AS `r13`,`t`.`r14` AS `r14`,sum(((`t`.`id` + `t`.`r00`) + `t`.`r01`)) OVER (ORDER BY `t`.`id` ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)  AS `s` from (/* select#2 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`sex` AS `sex`,rank() OVER `w00` AS `r00`,rank() OVER `w01` AS `r01`,rank() OVER `w02` AS `r02`,rank() OVER `w03` AS `r03`,rank() OVER `w04` AS `r04`,rank() OVER `w05` AS `r05`,rank() OVER `w06` AS `r06`,rank() OVER `w07` AS `r07`,rank() OVER `w08` AS `r08`,rank() OVER `w09` AS `r09`,rank() OVER `w10` AS `r10`,rank() OVER `w11` AS `r11`,rank() OVER `w12` AS `r12`,rank() OVER `w13` AS `r13`,rank() OVER `w14` AS `r14` from `test`.`t1` window `w00` AS (ORDER BY `test`.`t1`.`sex` ) , `w04` AS (ORDER BY `test`.`t1`.`sex` ) , `w05` AS (ORDER BY `test`.`t1`.`sex` ) , `w06` AS (ORDER BY `test`.`t1`.`sex` ) , `w07` AS (ORDER BY `test`.`t1`.`sex` ) , `w08` AS (ORDER BY `test`.`t1`.`sex` ) , `w09` AS (ORDER BY `test`.`t1`.`sex` ) , `w10` AS (ORDER BY `test`.`t1`.`sex` ) , `w11` AS (ORDER BY `test`.`t1`.`sex` ) , `w12` AS (ORDER BY `test`.`t1`.`sex` ) , `w13` AS (ORDER BY `test`.`t1`.`sex` ) , `w14` AS (ORDER BY `test`.`t1`.`sex` ) , `w03` AS (PARTITION BY `test`.`t1`.`id` ORDER BY `test`.`t1`.`sex` ) , `w01` AS (ORDER BY `test`.`t1`.`sex` desc ) , `w02` AS (ORDER BY `test`.`t1`.`sex`,`test`.`t1`.`id` desc ) ) `t`
----------------------------------------------------------------------
-    SUM, AVG, COUNT with frames
----------------------------------------------------------------------
SELECT SUM(id) OVER w * 2, AVG(id) OVER w, COUNT(id) OVER w FROM t1
WINDOW w AS (PARTITION BY sex);
SUM(id) OVER w * 2	AVG(id) OVER w	COUNT(id) OVER w
42	10.5000	2
42	10.5000	2
18	3.0000	3
18	3.0000	3
18	3.0000	3
12	3.0000	2
12	3.0000	2
SELECT * FROM (
SELECT id, SUM(id) OVER w, COUNT(*) OVER w, sex FROM t1
WINDOW w AS (PARTITION BY sex)
) alias ORDER BY id;
id	SUM(id) OVER w	COUNT(*) OVER w	sex
1	6	2	M
2	9	3	F
3	9	3	F
4	9	3	F
5	6	2	M
10	21	2	NULL
11	21	2	NULL
SELECT SUM(id) OVER w FROM t1 WINDOW w AS (PARTITION BY sex);
SUM(id) OVER w
21
21
9
9
9
6
6
SELECT id, SUM(id) OVER w, sex FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING);
id	SUM(id) OVER w	sex
10	NULL	NULL
11	10	NULL
2	NULL	F
3	2	F
4	5	F
1	NULL	M
5	1	M
try the same as a view
CREATE VIEW v AS
SELECT id, SUM(id) OVER w, sex FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING);
SHOW CREATE VIEW v;
View	Create View	character_set_client	collation_connection
v	CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v` AS select `t1`.`id` AS `id`,sum(`t1`.`id`) OVER `w` AS `SUM(id) OVER w`,`t1`.`sex` AS `sex` from `t1` window `w` AS (PARTITION BY `t1`.`sex` ORDER BY `t1`.`id` ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING) 	utf8mb3	utf8mb3_general_ci
SELECT * FROM v;
id	SUM(id) OVER w	sex
10	NULL	NULL
11	10	NULL
2	NULL	F
3	2	F
4	5	F
1	NULL	M
5	1	M
DROP VIEW v;
SELECT SUM(id) OVER w FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING);
SUM(id) OVER w
NULL
10
NULL
2
5
NULL
1
SELECT id, SUM(id) OVER w, sex FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
id	SUM(id) OVER w	sex
10	11	NULL
11	NULL	NULL
2	7	F
3	4	F
4	NULL	F
1	5	M
5	NULL	M
SELECT SUM(id) OVER w, COUNT(*) OVER w FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
SUM(id) OVER w	COUNT(*) OVER w
11	1
NULL	0
7	2
4	1
NULL	0
5	1
NULL	0
SELECT id, AVG(id) OVER (ROWS UNBOUNDED PRECEDING) FROM t1;
id	AVG(id) OVER (ROWS UNBOUNDED PRECEDING)
1	1.0000
2	1.5000
3	2.0000
4	2.5000
5	3.0000
10	4.1667
11	5.1429
SELECT id, AVG(id) OVER w, COUNT(id) OVER w FROM t1
WINDOW w AS (ORDER BY id ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING);
id	AVG(id) OVER w	COUNT(id) OVER w
1	1.5000	2
2	2.0000	3
3	3.0000	3
4	4.0000	3
5	6.3333	3
10	8.6667	3
11	10.5000	2
AVG, SUM with double type
CREATE TABLE td(d DOUBLE);
INSERT INTO td VALUES (2);
INSERT INTO td VALUES (2);
INSERT INTO td VALUES (3);
INSERT INTO td VALUES (1);
INSERT INTO td VALUES (1.2);
INSERT INTO td VALUES (NULL);
SELECT d, SUM(d) OVER (ORDER BY d), AVG(d) OVER (ORDER BY d) FROM td;
d	SUM(d) OVER (ORDER BY d)	AVG(d) OVER (ORDER BY d)
NULL	NULL	NULL
1	1	1
1.2	2.2	1.1
2	6.2	1.55
2	6.2	1.55
3	9.2	1.8399999999999999
SELECT d, SUM(d) OVER (ORDER BY d), AVG(d) OVER () FROM td;
d	SUM(d) OVER (ORDER BY d)	AVG(d) OVER ()
NULL	NULL	1.8399999999999999
1	1	1.8399999999999999
1.2	2.2	1.8399999999999999
2	6.2	1.8399999999999999
2	6.2	1.8399999999999999
3	9.2	1.8399999999999999
SELECT d, SUM(d) OVER (ORDER BY d), AVG(d) OVER (ORDER BY d ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING) FROM td;
d	SUM(d) OVER (ORDER BY d)	AVG(d) OVER (ORDER BY d ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING)
NULL	NULL	1
1	1	1.1
1.2	2.2	1.4000000000000001
2	6.2	1.7333333333333334
2	6.2	2.3333333333333335
3	9.2	2.5
Check system variable "windowing_use_high_precision"
TRUNCATE td;
INSERT INTO td VALUES (1.7976931348623157E+307);
INSERT INTO td VALUES (1);
should be default off:
SHOW VARIABLES LIKE 'windowing_use_high_precision';
Variable_name	Value
windowing_use_high_precision	ON
SELECT d, SUM(d) OVER (ROWS BETWEEN CURRENT ROW AND 1 FOLLOWING) FROM td;
d	SUM(d) OVER (ROWS BETWEEN CURRENT ROW AND 1 FOLLOWING)
1.7976931348623158e307	1.7976931348623158e307
1	1
allow unsafe optimization: result changes
SET SESSION windowing_use_high_precision=FALSE;
SELECT d, SUM(d) OVER (ROWS BETWEEN CURRENT ROW AND 1 FOLLOWING) FROM td;
d	SUM(d) OVER (ROWS BETWEEN CURRENT ROW AND 1 FOLLOWING)
1.7976931348623158e307	1.7976931348623158e307
1	0
SET SESSION windowing_use_high_precision=TRUE;
bugfix: AVG for moving range frame
TRUNCATE td;
INSERT INTO td VALUES (10);
INSERT INTO td VALUES (1);
INSERT INTO td VALUES (2);
INSERT INTO td VALUES (3);
INSERT INTO td VALUES (4);
INSERT INTO td VALUES (5);
INSERT INTO td VALUES (6);
INSERT INTO td VALUES (7);
INSERT INTO td VALUES (8);
INSERT INTO td VALUES (9);
SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND CURRENT ROW);
d	SUM(d) OVER w	AVG(d) OVER w
1	1	1
2	3	1.5
3	6	2
4	9	3
5	12	4
6	15	5
7	18	6
8	21	7
9	24	8
10	27	9
SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND 2 FOLLOWING);
d	SUM(d) OVER w	AVG(d) OVER w
1	6	2
2	10	2.5
3	15	3
4	20	4
5	25	5
6	30	6
7	35	7
8	40	8
9	34	8.5
10	27	9
SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN CURRENT ROW AND 2 FOLLOWING);
d	SUM(d) OVER w	AVG(d) OVER w
1	6	2
2	9	3
3	12	4
4	15	5
5	18	6
6	21	7
7	24	8
8	27	9
9	19	9.5
10	10	10
SET SESSION windowing_use_high_precision=FALSE;
SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND CURRENT ROW);
d	SUM(d) OVER w	AVG(d) OVER w
1	1	1
2	3	1.5
3	6	2
4	9	3
5	12	4
6	15	5
7	18	6
8	21	7
9	24	8
10	27	9
SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND 2 FOLLOWING);
d	SUM(d) OVER w	AVG(d) OVER w
1	6	2
2	10	2.5
3	15	3
4	20	4
5	25	5
6	30	6
7	35	7
8	40	8
9	34	8.5
10	27	9
SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN CURRENT ROW AND 2 FOLLOWING);
d	SUM(d) OVER w	AVG(d) OVER w
1	6	2
2	9	3
3	12	4
4	15	5
5	18	6
6	21	7
7	24	8
8	27	9
9	19	9.5
10	10	10
SET SESSION windowing_use_high_precision=TRUE;
INSERT INTO td SELECT * FROM td;
SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND CURRENT ROW);
d	SUM(d) OVER w	AVG(d) OVER w
1	2	1
1	2	1
2	6	1.5
2	6	1.5
3	12	2
3	12	2
4	18	3
4	18	3
5	24	4
5	24	4
6	30	5
6	30	5
7	36	6
7	36	6
8	42	7
8	42	7
9	48	8
9	48	8
10	54	9
10	54	9
SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND 2 FOLLOWING);
d	SUM(d) OVER w	AVG(d) OVER w
1	12	2
1	12	2
2	20	2.5
2	20	2.5
3	30	3
3	30	3
4	40	4
4	40	4
5	50	5
5	50	5
6	60	6
6	60	6
7	70	7
7	70	7
8	80	8
8	80	8
9	68	8.5
9	68	8.5
10	54	9
10	54	9
SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN CURRENT ROW AND 2 FOLLOWING);
d	SUM(d) OVER w	AVG(d) OVER w
1	12	2
1	12	2
2	18	3
2	18	3
3	24	4
3	24	4
4	30	5
4	30	5
5	36	6
5	36	6
6	42	7
6	42	7
7	48	8
7	48	8
8	54	9
8	54	9
9	38	9.5
9	38	9.5
10	20	10
10	20	10
SET SESSION windowing_use_high_precision=FALSE;
SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND CURRENT ROW);
d	SUM(d) OVER w	AVG(d) OVER w
1	2	1
1	2	1
2	6	1.5
2	6	1.5
3	12	2
3	12	2
4	18	3
4	18	3
5	24	4
5	24	4
6	30	5
6	30	5
7	36	6
7	36	6
8	42	7
8	42	7
9	48	8
9	48	8
10	54	9
10	54	9
SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN 2 PRECEDING AND 2 FOLLOWING);
d	SUM(d) OVER w	AVG(d) OVER w
1	12	2
1	12	2
2	20	2.5
2	20	2.5
3	30	3
3	30	3
4	40	4
4	40	4
5	50	5
5	50	5
6	60	6
6	60	6
7	70	7
7	70	7
8	80	8
8	80	8
9	68	8.5
9	68	8.5
10	54	9
10	54	9
SELECT d, SUM(d) OVER w, AVG(d) OVER w FROM td
WINDOW w AS (ORDER BY d RANGE BETWEEN CURRENT ROW AND 2 FOLLOWING);
d	SUM(d) OVER w	AVG(d) OVER w
1	12	2
1	12	2
2	18	3
2	18	3
3	24	4
3	24	4
4	30	5
4	30	5
5	36	6
5	36	6
6	42	7
6	42	7
7	48	8
7	48	8
8	54	9
8	54	9
9	38	9.5
9	38	9.5
10	20	10
10	20	10
SET SESSION windowing_use_high_precision=TRUE;
DROP TABLE td;
----------------------------------------------------------------------
-    NTILE (requires two passes over partition).
-    Currently suboptimal in that it causes N*N reads of tmp buffer
----------------------------------------------------------------------
SELECT id, NTILE(0) OVER w FROM t1 WINDOW w AS ();
ERROR HY000: Incorrect arguments to ntile
SELECT id, NTILE(NULL) OVER w FROM t1 WINDOW w AS (ORDER BY id);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'NULL) OVER w FROM t1 WINDOW w AS (ORDER BY id)' at line 1
PREPARE s FROM
'SELECT id, NTILE(?) OVER w FROM t1 WINDOW w AS (ORDER BY id)';
SET @a=NULL;
EXECUTE s USING @a;
ERROR HY000: Incorrect arguments to ntile
SELECT id, NTILE(@s) OVER w FROM t1 WINDOW w AS (ORDER BY id);
ERROR HY000: Incorrect arguments to ntile
SELECT id, NTILE(1) OVER w FROM t1 WINDOW w AS ();
id	NTILE(1) OVER w
1	1
2	1
3	1
4	1
5	1
10	1
11	1
SELECT id, NTILE(5) OVER w FROM t1 WINDOW w AS ();
id	NTILE(5) OVER w
1	1
2	1
3	2
4	2
5	3
10	4
11	5
SELECT id, NTILE(1) OVER w FROM t1 WINDOW w AS (ORDER BY id);
id	NTILE(1) OVER w
1	1
2	1
3	1
4	1
5	1
10	1
11	1
SELECT id, NTILE(2) OVER w FROM t1 WINDOW w AS (ORDER BY id);
id	NTILE(2) OVER w
1	1
2	1
3	1
4	1
5	2
10	2
11	2
SELECT id, NTILE(5) OVER w FROM t1 WINDOW w AS (ORDER BY id);
id	NTILE(5) OVER w
1	1
2	1
3	2
4	2
5	3
10	4
11	5
SELECT id, NTILE(11) OVER w FROM t1 WINDOW w AS (ORDER BY id);
id	NTILE(11) OVER w
1	1
2	2
3	3
4	4
5	5
10	6
11	7
combo with frame
SELECT id, ROW_NUMBER() OVER w, NTILE(4) OVER w, SUM(id) OVER w FROM t1
WINDOW w AS (ORDER BY id ROWS 1 PRECEDING);
id	ROW_NUMBER() OVER w	NTILE(4) OVER w	SUM(id) OVER w
1	1	1	1
2	2	1	3
3	3	2	5
4	4	2	7
5	5	3	9
10	6	3	15
11	7	4	21
Try one where there are no extras
DELETE FROM t1 WHERE id=11;
SELECT id, NTILE(3) OVER w FROM t1 WINDOW w AS (ORDER BY id);
id	NTILE(3) OVER w
1	1
2	1
3	2
4	2
5	3
10	3
INSERT INTO t1 VALUES (11, NULL);
PREPARE p FROM "SELECT id, NTILE(?) OVER w FROM t1 WINDOW w AS (ORDER BY id)";
SET @p1= 3;
EXECUTE p USING @p1;
id	NTILE(?) OVER w
1	1
2	1
3	1
4	2
5	2
10	3
11	3
SET @p1= '1';
EXECUTE p USING @p1;
ERROR HY000: Incorrect arguments to EXECUTE
SET @p1= NULL;
EXECUTE p USING @p1;
ERROR HY000: Incorrect arguments to ntile
DROP PREPARE p;
Simulated NTILE via other SQL window functions. Exercises an
an expression containing window functions defined on different
windows
SELECT (ROW_NUMBER() OVER w1 * 5 - 1) DIV (COUNT(*) OVER w2) + 1 AS cnt
FROM t1 WINDOW w1 AS (ORDER BY id ASC),
w2 AS ();
cnt
1
2
3
3
4
5
5
SELECT  (ROW_NUMBER() OVER w1 * 5 - 1) DIV (COUNT(*) OVER w2) + 1 AS ntile_manually,
COUNT(*) OVER w3
FROM t1 WINDOW w1 AS (ORDER BY id ASC),
w2 AS (), w3 AS ();
ntile_manually	COUNT(*) OVER w3
1	7
2	7
3	7
3	7
4	7
5	7
5	7
NTILE in combination with a frame that doesn't cover current row (was bug)
SELECT id, ROW_NUMBER() OVER w, SUM(id) OVER w, NTILE(5) OVER w FROM t1
WINDOW w AS (ORDER BY id ROWS BETWEEN UNBOUNDED PRECEDING AND 2 PRECEDING);
id	ROW_NUMBER() OVER w	SUM(id) OVER w	NTILE(5) OVER w
1	1	NULL	1
2	2	NULL	1
3	3	1	2
4	4	3	2
5	5	6	3
10	6	10	4
11	7	15	5
----------------------------------------------------------------------
-    SUM with frames in combination with non-framing window functions
-    ROW_NUMBER and RANK
----------------------------------------------------------------------
SELECT ROW_NUMBER() OVER w, id, SUM(id) OVER w, sex FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING);
ROW_NUMBER() OVER w	id	SUM(id) OVER w	sex
1	10	21	NULL
2	11	21	NULL
1	2	9	F
2	3	9	F
3	4	9	F
1	1	6	M
2	5	6	M
SELECT ROW_NUMBER() OVER w, SUM(id) OVER w FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
ROW_NUMBER() OVER w	SUM(id) OVER w
1	11
2	NULL
1	7
2	4
3	NULL
1	5
2	NULL
INSERT INTO t1 VALUES (10, NULL);
SELECT RANK() OVER w, id, SUM(id) OVER w, sex FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id);
RANK() OVER w	id	SUM(id) OVER w	sex
1	10	20	NULL
1	10	20	NULL
3	11	31	NULL
1	2	2	F
2	3	5	F
3	4	9	F
1	1	1	M
2	5	6	M
SELECT RANK() OVER w, SUM(id) OVER w FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id
ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
RANK() OVER w	SUM(id) OVER w
1	21
1	11
3	NULL
1	7
2	4
3	NULL
1	5
2	NULL
SELECT id, sex, SUM(id) OVER w,
ROW_NUMBER() OVER w,
RANK() OVER w  FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING);
id	sex	SUM(id) OVER w	ROW_NUMBER() OVER w	RANK() OVER w
10	NULL	NULL	1	1
10	NULL	10	2	1
11	NULL	20	3	3
2	F	NULL	1	1
3	F	2	2	2
4	F	5	3	3
1	M	NULL	1	1
5	M	1	2	2
SELECT id, sex, SUM(id) OVER w,
ROW_NUMBER() OVER w,
CUME_DIST() OVER w  FROM t1
WINDOW w AS (PARTITION BY sex ORDER BY id ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING);
id	sex	SUM(id) OVER w	ROW_NUMBER() OVER w	CUME_DIST() OVER w
10	NULL	NULL	1	0.6666666666666666
10	NULL	10	2	0.6666666666666666
11	NULL	20	3	1
2	F	NULL	1	0.3333333333333333
3	F	2	2	0.6666666666666666
4	F	5	3	1
1	M	NULL	1	0.5
5	M	1	2	1
Bug: if we have *any* window frame buffers, we need to add any fields
only referenced in expressions to the select list, so they get stored and
restored to/from the window frame buffer; which only uses copy_field.
Unfortunately, we don't know whther we have frame buffer at the time of
setup_fields so we must presume they are always used.

Used to work (no frame buffer)
SELECT id+2, ROW_NUMBER() OVER () FROM t1;
id+2	ROW_NUMBER() OVER ()
3	1
4	2
5	3
6	4
7	5
12	6
13	7
12	8
Used to work
SELECT id+2, FIRST_VALUE(sex) OVER (ORDER BY sex ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING) FROM t1;
id+2	FIRST_VALUE(sex) OVER (ORDER BY sex ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING)
12	NULL
13	NULL
12	NULL
4	NULL
5	F
6	F
3	F
7	M
Used to fail
SELECT id+2, NTILE(2) OVER (ORDER BY sex) FROM t1;
id+2	NTILE(2) OVER (ORDER BY sex)
12	1
13	1
12	1
4	1
5	2
6	2
3	2
7	2
SELECT NTILE(2) OVER (ORDER BY sex) FROM t1 ORDER BY id+2 DESC;
NTILE(2) OVER (ORDER BY sex)
1
1
1
2
2
2
1
2
----------------------------------------------------------------------
-    FIRST_VALUE
----------------------------------------------------------------------
INSERT INTO t1 VALUES (NULL, 'M');
SELECT FIRST_VALUE(6) OVER ();
FIRST_VALUE(6) OVER ()
6
SELECT FIRST_VALUE(6.0) OVER ();
FIRST_VALUE(6.0) OVER ()
6.0
SELECT FIRST_VALUE(CAST(6.0 AS DECIMAL(4,2))) OVER ();
FIRST_VALUE(CAST(6.0 AS DECIMAL(4,2))) OVER ()
6.00
SELECT FIRST_VALUE('6') OVER ();
FIRST_VALUE('6') OVER ()
6
SELECT FIRST_VALUE(NULL) OVER ();
FIRST_VALUE(NULL) OVER ()
NULL
SELECT FIRST_VALUE(6)    OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
FIRST_VALUE(6)    OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
6
SELECT FIRST_VALUE(NULL) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
FIRST_VALUE(NULL) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
NULL
SELECT LAST_VALUE(6)    OVER ();
LAST_VALUE(6)    OVER ()
6
SELECT LAST_VALUE(NULL) OVER ();
LAST_VALUE(NULL) OVER ()
NULL
SELECT LAST_VALUE(6)    OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
LAST_VALUE(6)    OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
6
SELECT LAST_VALUE(NULL) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
LAST_VALUE(NULL) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
NULL
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS ();
id	FIRST_VALUE(id) OVER w
1	1
2	1
3	1
4	1
5	1
10	1
11	1
10	1
NULL	1
select id, FIRST_VALUE(id) OVER (ROWS UNBOUNDED PRECEDING) FROM t1;
id	FIRST_VALUE(id) OVER (ROWS UNBOUNDED PRECEDING)
1	1
2	1
3	1
4	1
5	1
10	1
11	1
10	1
NULL	1
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
10	NULL
10	NULL
11	NULL
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (PARTITION BY sex ORDER BY id);
id	FIRST_VALUE(id) OVER w
10	10
10	10
11	10
2	2
3	2
4	2
NULL	NULL
1	NULL
5	NULL
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id DESC);
id	FIRST_VALUE(id) OVER w
11	11
10	11
10	11
5	11
4	11
3	11
2	11
1	11
NULL	11
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id ROWS  2 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	NULL
2	NULL
3	1
4	2
5	3
10	4
10	5
11	10
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id RANGE 2 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	1
2	1
3	1
4	2
5	3
10	10
10	10
11	10
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	NULL
2	NULL
3	1
4	2
5	3
10	4
10	5
11	10
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	NULL
2	1
3	1
4	2
5	3
10	NULL
10	NULL
11	10
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	1
2	2
3	3
4	4
5	5
10	10
10	10
11	11
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	1
2	2
3	3
4	4
5	5
10	10
10	10
11	11
CREATE VIEW v AS
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
SHOW CREATE VIEW v;
View	Create View	character_set_client	collation_connection
v	CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v` AS select `t1`.`id` AS `id`,first_value(`t1`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `t1` window `w` AS (ORDER BY `t1`.`id` RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 	utf8mb3	utf8mb3_general_ci
SELECT * FROM v;
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	1
2	2
3	3
4	4
5	5
10	10
10	10
11	11
DROP VIEW v;
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	2
1	3
2	4
3	5
4	10
5	10
10	11
10	NULL
11	NULL
SELECT id, FIRST_VALUE(id) OVER w FROM t1 WINDOW w AS (ORDER BY id RANGE BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	3
2	4
3	5
4	NULL
5	NULL
10	NULL
10	NULL
11	NULL
CREATE TABLE td1 (id DOUBLE, sex CHAR(1));
INSERT INTO td1 SELECT * FROM t1;
SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS ();
id	FIRST_VALUE(id) OVER w
1	1
2	1
3	1
4	1
5	1
10	1
11	1
10	1
NULL	1
SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
10	NULL
10	NULL
11	NULL
SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (PARTITION BY sex ORDER BY id);
id	FIRST_VALUE(id) OVER w
10	10
10	10
11	10
2	2
3	2
4	2
NULL	NULL
1	NULL
5	NULL
SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id DESC);
id	FIRST_VALUE(id) OVER w
11	11
10	11
10	11
5	11
4	11
3	11
2	11
1	11
NULL	11
SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id ROWS  2 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	NULL
2	NULL
3	1
4	2
5	3
10	4
10	5
11	10
SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id RANGE 2 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	1
2	1
3	1
4	2
5	3
10	10
10	10
11	10
SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	NULL
2	NULL
3	1
4	2
5	3
10	4
10	5
11	10
SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	NULL
2	1
3	1
4	2
5	3
10	NULL
10	NULL
11	10
SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	1
2	2
3	3
4	4
5	5
10	10
10	10
11	11
SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	1
2	2
3	3
4	4
5	5
10	10
10	10
11	11
SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	2
1	3
2	4
3	5
4	10
5	10
10	11
10	NULL
11	NULL
SELECT id, FIRST_VALUE(id) OVER w, CUME_DIST() OVER w FROM td1 WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
id	FIRST_VALUE(id) OVER w	CUME_DIST() OVER w
NULL	2	0.1111111111111111
1	3	0.2222222222222222
2	4	0.3333333333333333
3	5	0.4444444444444444
4	10	0.5555555555555556
5	10	0.6666666666666666
10	11	0.8888888888888888
10	NULL	0.8888888888888888
11	NULL	1
SELECT id, FIRST_VALUE(id) OVER w FROM td1 WINDOW w AS (ORDER BY id RANGE BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	3
2	4
3	5
4	NULL
5	NULL
10	NULL
10	NULL
11	NULL
SELECT id, FIRST_VALUE(id) OVER w, CUME_DIST() OVER w FROM td1 WINDOW w AS (ORDER BY id RANGE BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
id	FIRST_VALUE(id) OVER w	CUME_DIST() OVER w
NULL	NULL	0.1111111111111111
1	3	0.2222222222222222
2	4	0.3333333333333333
3	5	0.4444444444444444
4	NULL	0.5555555555555556
5	NULL	0.6666666666666666
10	NULL	0.8888888888888888
10	NULL	0.8888888888888888
11	NULL	1
DROP TABLE td1;
CREATE TABLE td_dec (id DECIMAL(10,2), sex CHAR(1));
INSERT INTO td_dec SELECT * FROM t1;
SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS ();
id	FIRST_VALUE(id) OVER w
1.00	1.00
2.00	1.00
3.00	1.00
4.00	1.00
5.00	1.00
10.00	1.00
11.00	1.00
10.00	1.00
NULL	1.00
SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1.00	NULL
2.00	NULL
3.00	NULL
4.00	NULL
5.00	NULL
10.00	NULL
10.00	NULL
11.00	NULL
SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (PARTITION BY sex ORDER BY id);
id	FIRST_VALUE(id) OVER w
10.00	10.00
10.00	10.00
11.00	10.00
2.00	2.00
3.00	2.00
4.00	2.00
NULL	NULL
1.00	NULL
5.00	NULL
SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id DESC);
id	FIRST_VALUE(id) OVER w
11.00	11.00
10.00	11.00
10.00	11.00
5.00	11.00
4.00	11.00
3.00	11.00
2.00	11.00
1.00	11.00
NULL	11.00
SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id ROWS  2 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1.00	NULL
2.00	NULL
3.00	1.00
4.00	2.00
5.00	3.00
10.00	4.00
10.00	5.00
11.00	10.00
SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id RANGE 2 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1.00	1.00
2.00	1.00
3.00	1.00
4.00	2.00
5.00	3.00
10.00	10.00
10.00	10.00
11.00	10.00
SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1.00	NULL
2.00	NULL
3.00	1.00
4.00	2.00
5.00	3.00
10.00	4.00
10.00	5.00
11.00	10.00
SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1.00	NULL
2.00	1.00
3.00	1.00
4.00	2.00
5.00	3.00
10.00	NULL
10.00	NULL
11.00	10.00
SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1.00	1.00
2.00	2.00
3.00	3.00
4.00	4.00
5.00	5.00
10.00	10.00
10.00	10.00
11.00	11.00
SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1.00	1.00
2.00	2.00
3.00	3.00
4.00	4.00
5.00	5.00
10.00	10.00
10.00	10.00
11.00	11.00
SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	2.00
1.00	3.00
2.00	4.00
3.00	5.00
4.00	10.00
5.00	10.00
10.00	11.00
10.00	NULL
11.00	NULL
SELECT id, FIRST_VALUE(id) OVER w FROM td_dec WINDOW w AS (ORDER BY id RANGE BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1.00	3.00
2.00	4.00
3.00	5.00
4.00	NULL
5.00	NULL
10.00	NULL
10.00	NULL
11.00	NULL
DROP TABLE td_dec;
CREATE TABLE td_str (id VARCHAR(20), sex CHAR(1));
INSERT INTO td_str SELECT * FROM t1;
SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS ();
id	FIRST_VALUE(id) OVER w
1	1
2	1
3	1
4	1
5	1
10	1
11	1
10	1
NULL	1
SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	NULL
10	NULL
10	NULL
11	NULL
2	NULL
3	NULL
4	NULL
5	NULL
SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (PARTITION BY sex ORDER BY id);
id	FIRST_VALUE(id) OVER w
10	10
10	10
11	10
2	2
3	2
4	2
NULL	NULL
1	NULL
5	NULL
SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id DESC);
id	FIRST_VALUE(id) OVER w
5	5
4	5
3	5
2	5
11	5
10	5
10	5
1	5
NULL	5
SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id ROWS  2 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	NULL
10	NULL
10	1
11	10
2	10
3	11
4	2
5	3
SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id RANGE 2 PRECEDING);
ERROR HY000: Window 'w' with RANGE N PRECEDING/FOLLOWING frame requires exactly one ORDER BY expression, of numeric or temporal type
SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	NULL
10	NULL
10	1
11	10
2	10
3	11
4	2
5	3
SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING);
ERROR HY000: Window 'w' with RANGE N PRECEDING/FOLLOWING frame requires exactly one ORDER BY expression, of numeric or temporal type
SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	1
10	10
10	10
11	11
2	2
3	3
4	4
5	5
SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	NULL
1	1
10	10
10	10
11	11
2	2
3	3
4	4
5	5
SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
id	FIRST_VALUE(id) OVER w
NULL	10
1	10
10	11
10	2
11	3
2	4
3	5
4	NULL
5	NULL
SELECT id, FIRST_VALUE(id) OVER w FROM td_str WINDOW w AS (ORDER BY id RANGE BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
ERROR HY000: Window 'w' with RANGE N PRECEDING/FOLLOWING frame requires exactly one ORDER BY expression, of numeric or temporal type
DROP TABLE td_str;
CREATE TABLE t_date(id DATE);
INSERT INTO t_date VALUES ('2002-06-09');
INSERT INTO t_date VALUES ('2002-06-09');
INSERT INTO t_date VALUES ('2002-06-10');
INSERT INTO t_date VALUES ('2002-06-09');
INSERT INTO t_date VALUES ('2015-08-01');
INSERT INTO t_date VALUES ('2002-06-09');
INSERT INTO t_date VALUES ('2015-08-01');
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS ();
id	FIRST_VALUE(id) OVER w
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-10	2002-06-09
2002-06-09	2002-06-09
2015-08-01	2002-06-09
2002-06-09	2002-06-09
2015-08-01	2002-06-09
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id);
id	FIRST_VALUE(id) OVER w
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-10	2002-06-09
2015-08-01	2002-06-09
2015-08-01	2002-06-09
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id DESC);
id	FIRST_VALUE(id) OVER w
2015-08-01	2015-08-01
2015-08-01	2015-08-01
2002-06-10	2015-08-01
2002-06-09	2015-08-01
2002-06-09	2015-08-01
2002-06-09	2015-08-01
2002-06-09	2015-08-01
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id ROWS  2 PRECEDING);
id	FIRST_VALUE(id) OVER w
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-10	2002-06-09
2015-08-01	2002-06-09
2015-08-01	2002-06-10
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id RANGE INTERVAL 2 DAY PRECEDING);
id	FIRST_VALUE(id) OVER w
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-10	2002-06-09
2015-08-01	2015-08-01
2015-08-01	2015-08-01
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
id	FIRST_VALUE(id) OVER w
2002-06-09	NULL
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-10	2002-06-09
2015-08-01	2002-06-09
2015-08-01	2002-06-10
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id RANGE BETWEEN INTERVAL 2 DAY PRECEDING AND INTERVAL 1 DAY PRECEDING);
id	FIRST_VALUE(id) OVER w
2002-06-09	NULL
2002-06-09	NULL
2002-06-09	NULL
2002-06-09	NULL
2002-06-10	2002-06-09
2015-08-01	NULL
2015-08-01	NULL
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
id	FIRST_VALUE(id) OVER w
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-10	2002-06-10
2015-08-01	2015-08-01
2015-08-01	2015-08-01
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
id	FIRST_VALUE(id) OVER w
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-10	2002-06-10
2015-08-01	2015-08-01
2015-08-01	2015-08-01
CREATE VIEW v AS
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
SHOW CREATE VIEW v;
View	Create View	character_set_client	collation_connection
v	CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v` AS select `t_date`.`id` AS `id`,first_value(`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `t_date` window `w` AS (ORDER BY `t_date`.`id` RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) 	utf8mb3	utf8mb3_general_ci
SELECT * FROM v;
id	FIRST_VALUE(id) OVER w
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-10	2002-06-10
2015-08-01	2015-08-01
2015-08-01	2015-08-01
DROP VIEW v;
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
id	FIRST_VALUE(id) OVER w
2002-06-09	2002-06-09
2002-06-09	2002-06-09
2002-06-09	2002-06-10
2002-06-09	2015-08-01
2002-06-10	2015-08-01
2015-08-01	NULL
2015-08-01	NULL
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id RANGE BETWEEN INTERVAL 2 DAY FOLLOWING AND INTERVAL 3 DAY FOLLOWING);
id	FIRST_VALUE(id) OVER w
2002-06-09	NULL
2002-06-09	NULL
2002-06-09	NULL
2002-06-09	NULL
2002-06-10	NULL
2015-08-01	NULL
2015-08-01	NULL
CREATE VIEW v AS
SELECT id, FIRST_VALUE(id) OVER w FROM t_date WINDOW w AS (ORDER BY id RANGE BETWEEN INTERVAL 2 DAY FOLLOWING AND INTERVAL 3 DAY FOLLOWING);
SHOW CREATE VIEW v;
View	Create View	character_set_client	collation_connection
v	CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v` AS select `t_date`.`id` AS `id`,first_value(`t_date`.`id`) OVER `w` AS `FIRST_VALUE(id) OVER w` from `t_date` window `w` AS (ORDER BY `t_date`.`id` RANGE BETWEEN INTERVAL 2 day  FOLLOWING AND INTERVAL 3 day  FOLLOWING) 	utf8mb3	utf8mb3_general_ci
SELECT * FROM v;
id	FIRST_VALUE(id) OVER w
2002-06-09	NULL
2002-06-09	NULL
2002-06-09	NULL
2002-06-09	NULL
2002-06-10	NULL
2015-08-01	NULL
2015-08-01	NULL
DROP VIEW v;
DROP TABLE t_date;
CREATE TABLE t_time(t TIME, ts TIMESTAMP);
INSERT INTO t_time VALUES ('12:30', '2016-07-05 08:30:42');
INSERT INTO t_time VALUES ('22:30', '2015-07-05 08:30:43');
INSERT INTO t_time VALUES ('13:30', '2014-07-05 08:30:44');
INSERT INTO t_time VALUES ('01:30', '2013-07-05 08:30:45');
INSERT INTO t_time VALUES ('15:30', '2016-08-05 08:31:42');
INSERT INTO t_time VALUES ('20:30', '2016-09-05 08:32:42');
INSERT INTO t_time VALUES ('04:30', '2016-10-05 08:33:42');
INSERT INTO t_time VALUES ('06:30', '2016-11-05 08:34:42');
INSERT INTO t_time VALUES ('18:30', '2016-07-05 09:30:42');
INSERT INTO t_time VALUES ('21:30', '2016-07-06 10:30:42');
INSERT INTO t_time VALUES ('00:30', '2016-07-07 11:30:42');
INSERT INTO t_time VALUES ('00:31', '2016-07-08 12:30:42');
CREATE TABLE t_time2(t TIME, ts TIMESTAMP, p INTEGER DEFAULT 1);
INSERT INTO t_time2(t, ts) SELECT * FROM t_time;
UPDATE t_time2 SET p=p+1;
INSERT INTO t_time2(t, ts) SELECT * FROM t_time;
SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS ();
t	FIRST_VALUE(t) OVER w
12:30:00	12:30:00
22:30:00	12:30:00
13:30:00	12:30:00
01:30:00	12:30:00
15:30:00	12:30:00
20:30:00	12:30:00
04:30:00	12:30:00
06:30:00	12:30:00
18:30:00	12:30:00
21:30:00	12:30:00
00:30:00	12:30:00
00:31:00	12:30:00
SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t);
t	FIRST_VALUE(t) OVER w
00:30:00	00:30:00
00:31:00	00:30:00
01:30:00	00:30:00
04:30:00	00:30:00
06:30:00	00:30:00
12:30:00	00:30:00
13:30:00	00:30:00
15:30:00	00:30:00
18:30:00	00:30:00
20:30:00	00:30:00
21:30:00	00:30:00
22:30:00	00:30:00
SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t DESC);
t	FIRST_VALUE(t) OVER w
22:30:00	22:30:00
21:30:00	22:30:00
20:30:00	22:30:00
18:30:00	22:30:00
15:30:00	22:30:00
13:30:00	22:30:00
12:30:00	22:30:00
06:30:00	22:30:00
04:30:00	22:30:00
01:30:00	22:30:00
00:31:00	22:30:00
00:30:00	22:30:00
SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t ROWS  2 PRECEDING);
t	FIRST_VALUE(t) OVER w
00:30:00	00:30:00
00:31:00	00:30:00
01:30:00	00:30:00
04:30:00	00:31:00
06:30:00	01:30:00
12:30:00	04:30:00
13:30:00	06:30:00
15:30:00	12:30:00
18:30:00	13:30:00
20:30:00	15:30:00
21:30:00	18:30:00
22:30:00	20:30:00
SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t RANGE INTERVAL 2 HOUR PRECEDING);
t	FIRST_VALUE(t) OVER w
00:30:00	00:30:00
00:31:00	00:30:00
01:30:00	00:30:00
04:30:00	04:30:00
06:30:00	04:30:00
12:30:00	12:30:00
13:30:00	12:30:00
15:30:00	13:30:00
18:30:00	18:30:00
20:30:00	18:30:00
21:30:00	20:30:00
22:30:00	20:30:00
SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
t	FIRST_VALUE(t) OVER w
00:30:00	NULL
00:31:00	00:30:00
01:30:00	00:30:00
04:30:00	00:31:00
06:30:00	01:30:00
12:30:00	04:30:00
13:30:00	06:30:00
15:30:00	12:30:00
18:30:00	13:30:00
20:30:00	15:30:00
21:30:00	18:30:00
22:30:00	20:30:00
SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t RANGE BETWEEN INTERVAL 2 HOUR PRECEDING AND INTERVAL 1 HOUR PRECEDING);
t	FIRST_VALUE(t) OVER w
00:30:00	NULL
00:31:00	NULL
01:30:00	00:30:00
04:30:00	NULL
06:30:00	04:30:00
12:30:00	NULL
13:30:00	12:30:00
15:30:00	13:30:00
18:30:00	NULL
20:30:00	18:30:00
21:30:00	20:30:00
22:30:00	20:30:00
SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
t	FIRST_VALUE(t) OVER w
00:30:00	00:30:00
00:31:00	00:31:00
01:30:00	01:30:00
04:30:00	04:30:00
06:30:00	06:30:00
12:30:00	12:30:00
13:30:00	13:30:00
15:30:00	15:30:00
18:30:00	18:30:00
20:30:00	20:30:00
21:30:00	21:30:00
22:30:00	22:30:00
SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
t	FIRST_VALUE(t) OVER w
00:30:00	00:30:00
00:31:00	00:31:00
01:30:00	01:30:00
04:30:00	04:30:00
06:30:00	06:30:00
12:30:00	12:30:00
13:30:00	13:30:00
15:30:00	15:30:00
18:30:00	18:30:00
20:30:00	20:30:00
21:30:00	21:30:00
22:30:00	22:30:00
SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
t	FIRST_VALUE(t) OVER w
00:30:00	01:30:00
00:31:00	04:30:00
01:30:00	06:30:00
04:30:00	12:30:00
06:30:00	13:30:00
12:30:00	15:30:00
13:30:00	18:30:00
15:30:00	20:30:00
18:30:00	21:30:00
20:30:00	22:30:00
21:30:00	NULL
22:30:00	NULL
SELECT t, FIRST_VALUE(t) OVER w FROM t_time WINDOW w AS (ORDER BY t RANGE BETWEEN INTERVAL 2 HOUR FOLLOWING AND INTERVAL 3 HOUR FOLLOWING);
t	FIRST_VALUE(t) OVER w
00:30:00	NULL
00:31:00	NULL
01:30:00	04:30:00
04:30:00	06:30:00
06:30:00	NULL
12:30:00	15:30:00
13:30:00	15:30:00
15:30:00	18:30:00
18:30:00	20:30:00
20:30:00	22:30:00
21:30:00	NULL
22:30:00	NULL
SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t);
p	t	FIRST_VALUE(t) OVER w
1	00:30:00	00:30:00
1	00:31:00	00:30:00
1	01:30:00	00:30:00
1	04:30:00	00:30:00
1	06:30:00	00:30:00
1	12:30:00	00:30:00
1	13:30:00	00:30:00
1	15:30:00	00:30:00
1	18:30:00	00:30:00
1	20:30:00	00:30:00
1	21:30:00	00:30:00
1	22:30:00	00:30:00
2	00:30:00	00:30:00
2	00:31:00	00:30:00
2	01:30:00	00:30:00
2	04:30:00	00:30:00
2	06:30:00	00:30:00
2	12:30:00	00:30:00
2	13:30:00	00:30:00
2	15:30:00	00:30:00
2	18:30:00	00:30:00
2	20:30:00	00:30:00
2	21:30:00	00:30:00
2	22:30:00	00:30:00
SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t DESC);
p	t	FIRST_VALUE(t) OVER w
1	22:30:00	22:30:00
1	21:30:00	22:30:00
1	20:30:00	22:30:00
1	18:30:00	22:30:00
1	15:30:00	22:30:00
1	13:30:00	22:30:00
1	12:30:00	22:30:00
1	06:30:00	22:30:00
1	04:30:00	22:30:00
1	01:30:00	22:30:00
1	00:31:00	22:30:00
1	00:30:00	22:30:00
2	22:30:00	22:30:00
2	21:30:00	22:30:00
2	20:30:00	22:30:00
2	18:30:00	22:30:00
2	15:30:00	22:30:00
2	13:30:00	22:30:00
2	12:30:00	22:30:00
2	06:30:00	22:30:00
2	04:30:00	22:30:00
2	01:30:00	22:30:00
2	00:31:00	22:30:00
2	00:30:00	22:30:00
SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t ROWS  2 PRECEDING);
p	t	FIRST_VALUE(t) OVER w
1	00:30:00	00:30:00
1	00:31:00	00:30:00
1	01:30:00	00:30:00
1	04:30:00	00:31:00
1	06:30:00	01:30:00
1	12:30:00	04:30:00
1	13:30:00	06:30:00
1	15:30:00	12:30:00
1	18:30:00	13:30:00
1	20:30:00	15:30:00
1	21:30:00	18:30:00
1	22:30:00	20:30:00
2	00:30:00	00:30:00
2	00:31:00	00:30:00
2	01:30:00	00:30:00
2	04:30:00	00:31:00
2	06:30:00	01:30:00
2	12:30:00	04:30:00
2	13:30:00	06:30:00
2	15:30:00	12:30:00
2	18:30:00	13:30:00
2	20:30:00	15:30:00
2	21:30:00	18:30:00
2	22:30:00	20:30:00
SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t RANGE INTERVAL 2 HOUR PRECEDING);
p	t	FIRST_VALUE(t) OVER w
1	00:30:00	00:30:00
1	00:31:00	00:30:00
1	01:30:00	00:30:00
1	04:30:00	04:30:00
1	06:30:00	04:30:00
1	12:30:00	12:30:00
1	13:30:00	12:30:00
1	15:30:00	13:30:00
1	18:30:00	18:30:00
1	20:30:00	18:30:00
1	21:30:00	20:30:00
1	22:30:00	20:30:00
2	00:30:00	00:30:00
2	00:31:00	00:30:00
2	01:30:00	00:30:00
2	04:30:00	04:30:00
2	06:30:00	04:30:00
2	12:30:00	12:30:00
2	13:30:00	12:30:00
2	15:30:00	13:30:00
2	18:30:00	18:30:00
2	20:30:00	18:30:00
2	21:30:00	20:30:00
2	22:30:00	20:30:00
SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t ROWS  BETWEEN 2 PRECEDING AND 1 PRECEDING);
p	t	FIRST_VALUE(t) OVER w
1	00:30:00	NULL
1	00:31:00	00:30:00
1	01:30:00	00:30:00
1	04:30:00	00:31:00
1	06:30:00	01:30:00
1	12:30:00	04:30:00
1	13:30:00	06:30:00
1	15:30:00	12:30:00
1	18:30:00	13:30:00
1	20:30:00	15:30:00
1	21:30:00	18:30:00
1	22:30:00	20:30:00
2	00:30:00	NULL
2	00:31:00	00:30:00
2	01:30:00	00:30:00
2	04:30:00	00:31:00
2	06:30:00	01:30:00
2	12:30:00	04:30:00
2	13:30:00	06:30:00
2	15:30:00	12:30:00
2	18:30:00	13:30:00
2	20:30:00	15:30:00
2	21:30:00	18:30:00
2	22:30:00	20:30:00
SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t RANGE BETWEEN INTERVAL 2 HOUR PRECEDING AND INTERVAL 1 HOUR PRECEDING);
p	t	FIRST_VALUE(t) OVER w
1	00:30:00	NULL
1	00:31:00	NULL
1	01:30:00	00:30:00
1	04:30:00	NULL
1	06:30:00	04:30:00
1	12:30:00	NULL
1	13:30:00	12:30:00
1	15:30:00	13:30:00
1	18:30:00	NULL
1	20:30:00	18:30:00
1	21:30:00	20:30:00
1	22:30:00	20:30:00
2	00:30:00	NULL
2	00:31:00	NULL
2	01:30:00	00:30:00
2	04:30:00	NULL
2	06:30:00	04:30:00
2	12:30:00	NULL
2	13:30:00	12:30:00
2	15:30:00	13:30:00
2	18:30:00	NULL
2	20:30:00	18:30:00
2	21:30:00	20:30:00
2	22:30:00	20:30:00
SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t ROWS  BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
p	t	FIRST_VALUE(t) OVER w
1	00:30:00	00:30:00
1	00:31:00	00:31:00
1	01:30:00	01:30:00
1	04:30:00	04:30:00
1	06:30:00	06:30:00
1	12:30:00	12:30:00
1	13:30:00	13:30:00
1	15:30:00	15:30:00
1	18:30:00	18:30:00
1	20:30:00	20:30:00
1	21:30:00	21:30:00
1	22:30:00	22:30:00
2	00:30:00	00:30:00
2	00:31:00	00:31:00
2	01:30:00	01:30:00
2	04:30:00	04:30:00
2	06:30:00	06:30:00
2	12:30:00	12:30:00
2	13:30:00	13:30:00
2	15:30:00	15:30:00
2	18:30:00	18:30:00
2	20:30:00	20:30:00
2	21:30:00	21:30:00
2	22:30:00	22:30:00
SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
p	t	FIRST_VALUE(t) OVER w
1	00:30:00	00:30:00
1	00:31:00	00:31:00
1	01:30:00	01:30:00
1	04:30:00	04:30:00
1	06:30:00	06:30:00
1	12:30:00	12:30:00
1	13:30:00	13:30:00
1	15:30:00	15:30:00
1	18:30:00	18:30:00
1	20:30:00	20:30:00
1	21:30:00	21:30:00
1	22:30:00	22:30:00
2	00:30:00	00:30:00
2	00:31:00	00:31:00
2	01:30:00	01:30:00
2	04:30:00	04:30:00
2	06:30:00	06:30:00
2	12:30:00	12:30:00
2	13:30:00	13:30:00
2	15:30:00	15:30:00
2	18:30:00	18:30:00
2	20:30:00	20:30:00
2	21:30:00	21:30:00
2	22:30:00	22:30:00
SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t ROWS  BETWEEN 2 FOLLOWING AND 3 FOLLOWING);
p	t	FIRST_VALUE(t) OVER w
1	00:30:00	01:30:00
1	00:31:00	04:30:00
1	01:30:00	06:30:00
1	04:30:00	12:30:00
1	06:30:00	13:30:00
1	12:30:00	15:30:00
1	13:30:00	18:30:00
1	15:30:00	20:30:00
1	18:30:00	21:30:00
1	20:30:00	22:30:00
1	21:30:00	NULL
1	22:30:00	NULL
2	00:30:00	01:30:00
2	00:31:00	04:30:00
2	01:30:00	06:30:00
2	04:30:00	12:30:00
2	06:30:00	13:30:00
2	12:30:00	15:30:00
2	13:30:00	18:30:00
2	15:30:00	20:30:00
2	18:30:00	21:30:00
2	20:30:00	22:30:00
2	21:30:00	NULL
2	22:30:00	NULL
SELECT p, t, FIRST_VALUE(t) OVER w FROM t_time2 WINDOW w AS (PARTITION by p ORDER BY t RANGE BETWEEN INTERVAL 2 HOUR FOLLOWING AND INTERVAL 3 HOUR FOLLOWING);
p	t	FIRST_VALUE(t) OVER w
1	00:30:00	NULL
1	00:31:00	NULL
1	01:30:00	04:30:00
1	04:30:00	06:30:00
1	06:30:00	NULL
1	12:30:00	15:30:00
1	13:30:00	15:30:00
1	15:30:00	18:30:00
1	18:30:00	20:30:00
1	20:30:00	22:30:00
1	21:30:00	NULL
1	22:30:00	NULL
2	00:30:00	NULL
2	00:31:00	NULL
2	01:30:00	04:30:00
2	04:30:00	06:30:00
2	06:30:00	NULL
2	12:30:00	15:30:00
2	13:30:00	15:30:00
2	15:30:00	18:30:00
2	18:30:00	20:30:00
2	20:30:00	22:30:00
2	21:30:00	NULL
2	22:30:00	NULL
DROP TABLE t_time, t_time2;
----------------------------------------------------------------------
-    Aggregates with RANGE frame specification
----------------------------------------------------------------------
SELECT * FROM t1;
id	sex
1	M
2	F
3	F
4	F
5	M
10	NULL
11	NULL
10	NULL
NULL	M
Make t11 a clone of t1 but with an extra partitioning column, but other values
repeated, so we can test it the same frames work on more than one partition
CREATE TABLE t11 (id INTEGER, sex CHAR(1), p INTEGER DEFAULT 1);
INSERT INTO t11(id, sex) SELECT * FROM t1;
UPDATE t11 SET p=p+1;
INSERT INTO t11(id, sex) SELECT * FROM t1;
Make t22 a clone of t2 but with an extra partitioning column, but other values
repeated, so we can test it the same frames work on more than one partition
CREATE TABLE t22 (user_id INTEGER NOT NULL, date DATE, p INTEGER DEFAULT 1);
INSERT INTO t22(user_id, date) SELECT * FROM t2;
UPDATE t22 SET p=p+1;
INSERT INTO t22(user_id, date) SELECT * FROM t2;
SELECT id, SUM(id) OVER (ORDER BY id RANGE 2 PRECEDING) FROM t1 ORDER BY id;
id	SUM(id) OVER (ORDER BY id RANGE 2 PRECEDING)
NULL	NULL
1	1
2	3
3	6
4	9
5	12
10	20
10	20
11	31
SELECT id, SUM(id) OVER (ORDER BY id RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING) FROM t1 ORDER BY id;
id	SUM(id) OVER (ORDER BY id RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING)
NULL	NULL
1	3
2	6
3	9
4	12
5	9
10	31
10	31
11	31
SELECT id, SUM(id) OVER (ORDER BY id RANGE UNBOUNDED PRECEDING) FROM t1 ORDER BY id;
id	SUM(id) OVER (ORDER BY id RANGE UNBOUNDED PRECEDING)
NULL	NULL
1	1
2	3
3	6
4	10
5	15
10	35
10	35
11	46
SELECT p, id, SUM(id) OVER (PARTITION BY p ORDER BY id RANGE 2 PRECEDING) FROM t11 ORDER BY p,id;
p	id	SUM(id) OVER (PARTITION BY p ORDER BY id RANGE 2 PRECEDING)
1	NULL	NULL
1	1	1
1	2	3
1	3	6
1	4	9
1	5	12
1	10	20
1	10	20
1	11	31
2	NULL	NULL
2	1	1
2	2	3
2	3	6
2	4	9
2	5	12
2	10	20
2	10	20
2	11	31
SELECT p, id, SUM(id) OVER (PARTITION BY p ORDER BY id RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING) FROM t11 ORDER BY p,id;
p	id	SUM(id) OVER (PARTITION BY p ORDER BY id RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING)
1	NULL	NULL
1	1	3
1	2	6
1	3	9
1	4	12
1	5	9
1	10	31
1	10	31
1	11	31
2	NULL	NULL
2	1	3
2	2	6
2	3	9
2	4	12
2	5	9
2	10	31
2	10	31
2	11	31
SELECT p, id, SUM(id) OVER (PARTITION BY p ORDER BY id RANGE UNBOUNDED PRECEDING) FROM t11 ORDER BY p,id;
p	id	SUM(id) OVER (PARTITION BY p ORDER BY id RANGE UNBOUNDED PRECEDING)
1	NULL	NULL
1	1	1
1	2	3
1	3	6
1	4	10
1	5	15
1	10	35
1	10	35
1	11	46
2	NULL	NULL
2	1	1
2	2	3
2	3	6
2	4	10
2	5	15
2	10	35
2	10	35
2	11	46
Implicit frame due to ORDER BY, with last in peer group as upper bound
SELECT user_id, SUM(user_id) OVER w, AVG(user_id) OVER w FROM t2 WINDOW w AS (ORDER BY user_id);
user_id	SUM(user_id) OVER w	AVG(user_id) OVER w
1	2	1.0000
1	2	1.0000
2	4	1.3333
3	7	1.7500
4	15	2.5000
4	15	2.5000
5	20	2.8571
SELECT p, user_id, SUM(user_id) OVER w, AVG(user_id) OVER w FROM t22 WINDOW w AS (PARTITION BY p ORDER BY user_id) ORDER BY p;
p	user_id	SUM(user_id) OVER w	AVG(user_id) OVER w
1	1	2	1.0000
1	1	2	1.0000
1	2	4	1.3333
1	3	7	1.7500
1	4	15	2.5000
1	4	15	2.5000
1	5	20	2.8571
2	1	2	1.0000
2	1	2	1.0000
2	2	4	1.3333
2	3	7	1.7500
2	4	15	2.5000
2	4	15	2.5000
2	5	20	2.8571
Window function use of same field in different windows, both of which
need buffering. In this case we need subsequent rewrites of arg fields
Field pointer in tmp files for window 2..n The intervening internal
window buffering in each step used to mess that up.
SELECT user_id, SUM(user_id) OVER w, AVG(user_id) OVER w1 FROM t2
WINDOW w AS (ORDER BY user_id), w1 AS (ORDER BY user_id);
user_id	SUM(user_id) OVER w	AVG(user_id) OVER w1
1	2	1.0000
1	2	1.0000
2	4	1.3333
3	7	1.7500
4	15	2.5000
4	15	2.5000
5	20	2.8571
Check descending order by with RANGE: 2 PRECEDING in this case means larger than
current row.
SELECT NTILE(5) OVER w, ROW_NUMBER() OVER w, id, SUM(id) OVER w FROM t1
WINDOW w AS (ORDER BY id DESC RANGE 2 PRECEDING);
NTILE(5) OVER w	ROW_NUMBER() OVER w	id	SUM(id) OVER w
1	1	11	11
1	2	10	31
2	3	10	31
2	4	5	5
3	5	4	9
3	6	3	12
4	7	2	9
4	8	1	6
5	9	NULL	NULL
SELECT p, NTILE(5) OVER w, ROW_NUMBER() OVER w, id, SUM(id) OVER w FROM t11
WINDOW w AS (PARTITION BY p ORDER BY id DESC RANGE 2 PRECEDING);
p	NTILE(5) OVER w	ROW_NUMBER() OVER w	id	SUM(id) OVER w
1	1	1	11	11
1	1	2	10	31
1	2	3	10	31
1	2	4	5	5
1	3	5	4	9
1	3	6	3	12
1	4	7	2	9
1	4	8	1	6
1	5	9	NULL	NULL
2	1	1	11	11
2	1	2	10	31
2	2	3	10	31
2	2	4	5	5
2	3	5	4	9
2	3	6	3	12
2	4	7	2	9
2	4	8	1	6
2	5	9	NULL	NULL
SELECT NTILE(5) OVER w, ROW_NUMBER() OVER w, id, SUM(id) OVER w FROM t1
WINDOW w AS (ORDER BY id DESC RANGE INTERVAL 2 MONTH PRECEDING);
ERROR HY000: Window 'w' with RANGE frame has ORDER BY expression of numeric type, INTERVAL bound value not allowed.
update t2 set date=date + user_id;
SELECT user_id, date, COUNT(*) OVER (ORDER BY date RANGE INTERVAL 1 DAY PRECEDING) FROM t2;
user_id	date	COUNT(*) OVER (ORDER BY date RANGE INTERVAL 1 DAY PRECEDING)
1	2002-06-10	2
1	2002-06-10	2
2	2002-06-11	3
3	2002-06-12	2
4	2002-06-13	3
4	2002-06-13	3
5	2002-06-14	3
SELECT user_id, date, COUNT(*) OVER (ORDER BY date RANGE 1 PRECEDING) FROM t2;
ERROR HY000: Window '<unnamed window>' with RANGE frame has ORDER BY expression of datetime type. Only INTERVAL bound value allowed.
CREATE TABLE t3(d DOUBLE);
INSERT INTO t3 VALUES (1.1);
INSERT INTO t3 VALUES (1.9);
INSERT INTO t3 VALUES (4.0);
INSERT INTO t3 VALUES (8.3);
INSERT INTO t3 VALUES (16.0);
INSERT INTO t3 VALUES (24.0);
INSERT INTO t3 VALUES (20.1);
INSERT INTO t3 VALUES (22.0);
INSERT INTO t3 VALUES (23.0);
SELECT d, SUM(d) OVER w, COUNT(*) OVER w FROM t3 WINDOW w AS (ORDER BY d RANGE BETWEEN 2.1 PRECEDING AND 1.1 FOLLOWING);
d	SUM(d) OVER w	COUNT(*) OVER w
1.1	3	2
1.9	3	2
4	5.9	2
8.3	8.3	1
16	16	1
20.1	20.1	1
22	65.1	3
23	69	3
24	69	3
Illegal range ORDER BY type, cf. SQL 2011 7.11 <window clause>, SR 11.a.ii
CREATE TABLE t4(c VARCHAR(30), i INT, j INT);
SELECT COUNT(*) OVER (ORDER BY c RANGE 3 PRECEDING) FROM t4;
ERROR HY000: Window '<unnamed window>' with RANGE N PRECEDING/FOLLOWING frame requires exactly one ORDER BY expression, of numeric or temporal type
SELECT COUNT(*) OVER (ORDER BY i,j RANGE 3 PRECEDING) FROM t4;
ERROR HY000: Window '<unnamed window>' with RANGE N PRECEDING/FOLLOWING frame requires exactly one ORDER BY expression, of numeric or temporal type
----------------------------------------------------------------------
-    wf over JSON
----------------------------------------------------------------------
CREATE TABLE tj(j JSON, i INT DEFAULT 7);
INSERT INTO tj(j) VALUES ('1');
INSERT INTO tj(j) VALUES ('2');
INSERT INTO tj(j) VALUES ('3');
INSERT INTO tj(j) VALUES ('4');
INSERT INTO tj(j) VALUES ('5');
INSERT INTO tj(j) VALUES (NULL);
INSERT INTO tj(j) VALUES ('3.14');
INSERT INTO tj(j) VALUES ('[1,2,3]');
SELECT CAST(SUM(j) OVER () AS JSON) FROM tj;
CAST(SUM(j) OVER () AS JSON)
18.14
18.14
18.14
18.14
18.14
18.14
18.14
18.14
Warnings:
Warning	3156	Invalid JSON value for CAST to DOUBLE from column j at row 1
----------------------------------------------------------------------
-    SELECT DISTINCT
----------------------------------------------------------------------
One window
SELECT DISTINCT i,COUNT(*) OVER () FROM tj;
i	COUNT(*) OVER ()
7	8
Several windows with final ORDER BY also
SELECT DISTINCT i,NTILE(3) OVER (ORDER BY i), SUM(i) OVER (), COUNT(*) OVER () FROM tj ORDER BY NTILE(3) OVER (ORDER BY i);
i	NTILE(3) OVER (ORDER BY i)	SUM(i) OVER ()	COUNT(*) OVER ()
7	1	56	8
7	2	56	8
7	3	56	8
UPDATE tj SET i=i+CASE WHEN JSON_TYPE(j) = 'ARRAY' THEN 1 ELSE j END;
UPDATE tj SET i=7 where i=8 AND JSON_TYPE(j) != 'ARRAY';
CREATE TABLE tj2 AS SELECT * FROM tj;
UPDATE tj2 SET i=MOD(i,3);
SELECT * FROM tj2;
j	i
1	1
2	0
3	1
4	2
5	0
NULL	NULL
3.14	1
[1, 2, 3]	2
With GROUP BY
SELECT          COUNT(*) OVER (), MOD(SUM(i),2) FROM tj2 GROUP BY i;
COUNT(*) OVER ()	MOD(SUM(i),2)
4	0
4	0
4	1
4	NULL
SELECT DISTINCT COUNT(*) OVER (), MOD(SUM(i),2) FROM tj2 GROUP BY i;
COUNT(*) OVER ()	MOD(SUM(i),2)
4	0
4	1
4	NULL
Bug fix GROUP BY with window function referring column used in grouping expression
SELECT i, SUM(i) OVER (), MOD(SUM(i),2) FROM tj2 GROUP BY i;
i	SUM(i) OVER ()	MOD(SUM(i),2)
0	3	0
1	3	1
2	3	0
NULL	3	NULL
SELECT i, SUM(SUM(i)) OVER (), SUM(i) OVER (ORDER BY i), MOD(SUM(i),2), SUM(i) FROM tj2 GROUP BY i;
i	SUM(SUM(i)) OVER ()	SUM(i) OVER (ORDER BY i)	MOD(SUM(i),2)	SUM(i)
0	7	0	0	0
1	7	1	1	3
2	7	3	0	4
NULL	7	NULL	NULL	NULL
DROP TABLE tj2;
----------------------------------------------------------------------
-    Bug fixes
----------------------------------------------------------------------
Bug fix for FIRST_VALUE, LAST_VALUE when not buffered processing
SELECT LAST_VALUE(j) OVER w, FIRST_VALUE(j) OVER w FROM tj WINDOW w AS (PARTITION BY i ORDER BY j ROWS UNBOUNDED PRECEDING);
LAST_VALUE(j) OVER w	FIRST_VALUE(j) OVER w
NULL	NULL
1	1
[1, 2, 3]	[1, 2, 3]
2	2
3	3
3.14	3
4	4
5	5
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
Bug missing hidden column (j) induction to select list: FIRST_VALUE/LAST_VALUE
SELECT i, LAST_VALUE((CAST(j AS UNSIGNED))) OVER w, FIRST_VALUE(CAST(j AS UNSIGNED)) OVER w FROM tj
WINDOW w AS (PARTITION BY i ORDER BY CAST(j AS UNSIGNED) RANGE UNBOUNDED PRECEDING);
i	LAST_VALUE((CAST(j AS UNSIGNED))) OVER w	FIRST_VALUE(CAST(j AS UNSIGNED)) OVER w
NULL	NULL	NULL
7	1	1
8	0	0
9	2	2
10	3	3
10	3	3
11	4	4
12	5	5
Warnings:
Warning	3156	Invalid JSON value for CAST to INTEGER from column j at row 1
Warning	3156	Invalid JSON value for CAST to INTEGER from column j at row 2
Warning	3156	Invalid JSON value for CAST to INTEGER from column j at row 2
Warning	3156	Invalid JSON value for CAST to INTEGER from column j at row 2
Fix for lineno in warnings buffered and unbuffered windows
SELECT j,CAST(SUM(j) OVER (PARTITION BY i) AS JSON), CAST(SUM(j) OVER () AS JSON) FROM tj;
j	CAST(SUM(j) OVER (PARTITION BY i) AS JSON)	CAST(SUM(j) OVER () AS JSON)
NULL	NULL	18.14
1	1.0	18.14
[1, 2, 3]	0.0	18.14
2	2.0	18.14
3	6.140000000000001	18.14
3.14	6.140000000000001	18.14
4	4.0	18.14
5	5.0	18.14
Warnings:
Warning	3156	Invalid JSON value for CAST to DOUBLE from column j at row 1
Warning	3156	Invalid JSON value for CAST to DOUBLE from column j at row 1
SELECT j,CAST(SUM(j) OVER (PARTITION BY i ROWS UNBOUNDED PRECEDING) AS JSON), CAST(SUM(j) OVER (PARTITION BY i ROWS UNBOUNDED PRECEDING) AS JSON) FROM tj;
j	CAST(SUM(j) OVER (PARTITION BY i ROWS UNBOUNDED PRECEDING) AS JSON)	CAST(SUM(j) OVER (PARTITION BY i ROWS UNBOUNDED PRECEDING) AS JSON)
NULL	NULL	NULL
1	1.0	1.0
[1, 2, 3]	0.0	0.0
2	2.0	2.0
3	3.0	3.0
3.14	6.140000000000001	6.140000000000001
4	4.0	4.0
5	5.0	5.0
Warnings:
Warning	3156	Invalid JSON value for CAST to DOUBLE from column j at row 1
Warning	3156	Invalid JSON value for CAST to DOUBLE from column j at row 3
Bug fix for UNION
SELECT i, ROW_NUMBER() OVER () FROM tj UNION ALL SELECT i, ROW_NUMBER() OVER () FROM tj;
i	ROW_NUMBER() OVER ()
7	1
9	2
10	3
11	4
12	5
NULL	6
10	7
8	8
7	1
9	2
10	3
11	4
12	5
NULL	6
10	7
8	8
SELECT * FROM (SELECT i, j, ROW_NUMBER() OVER (ORDER BY j) FROM tj UNION SELECT i, j, ROW_NUMBER() OVER (ORDER BY j) FROM tj) alias;
i	j	ROW_NUMBER() OVER (ORDER BY j)
NULL	NULL	1
7	1	2
9	2	3
10	3	4
10	3.14	5
11	4	6
12	5	7
8	[1, 2, 3]	8
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
Bug fixes after field_type refactorings on trunk triggered an error in
the above statements. The WFs didn't all have m_data_type set up.
SELECT i, RANK() OVER (ORDER BY i) FROM tj UNION ALL SELECT i, RANK() OVER (ORDER BY i) FROM tj;
i	RANK() OVER (ORDER BY i)
NULL	1
7	2
8	3
9	4
10	5
10	5
11	7
12	8
NULL	1
7	2
8	3
9	4
10	5
10	5
11	7
12	8
SELECT i, DENSE_RANK() OVER (ORDER BY i) FROM tj UNION ALL SELECT i, DENSE_RANK() OVER (ORDER BY i) FROM tj;
i	DENSE_RANK() OVER (ORDER BY i)
NULL	1
7	2
8	3
9	4
10	5
10	5
11	6
12	7
NULL	1
7	2
8	3
9	4
10	5
10	5
11	6
12	7
SELECT i, CUME_DIST() OVER (ORDER BY i) FROM tj UNION ALL SELECT i, CUME_DIST() OVER (ORDER BY i) FROM tj;
i	CUME_DIST() OVER (ORDER BY i)
NULL	0.125
7	0.25
8	0.375
9	0.5
10	0.75
10	0.75
11	0.875
12	1
NULL	0.125
7	0.25
8	0.375
9	0.5
10	0.75
10	0.75
11	0.875
12	1
SELECT i, PERCENT_RANK() OVER (ORDER BY i) FROM tj UNION ALL SELECT i, PERCENT_RANK() OVER (ORDER BY i) FROM tj;
i	PERCENT_RANK() OVER (ORDER BY i)
NULL	0
7	0.14285714285714285
8	0.2857142857142857
9	0.42857142857142855
10	0.5714285714285714
10	0.5714285714285714
11	0.8571428571428571
12	1
NULL	0
7	0.14285714285714285
8	0.2857142857142857
9	0.42857142857142855
10	0.5714285714285714
10	0.5714285714285714
11	0.8571428571428571
12	1
SELECT i, NTILE(3) OVER (ORDER BY i) FROM tj UNION ALL SELECT i, NTILE(3) OVER (ORDER BY i) FROM tj;
i	NTILE(3) OVER (ORDER BY i)
NULL	1
7	1
8	1
9	2
10	2
10	2
11	3
12	3
NULL	1
7	1
8	1
9	2
10	2
10	2
11	3
12	3
SELECT i, SUM(i) OVER (ORDER BY i) FROM tj UNION ALL SELECT i, SUM(i) OVER (ORDER BY i) FROM tj;
i	SUM(i) OVER (ORDER BY i)
NULL	NULL
7	7
8	15
9	24
10	44
10	44
11	55
12	67
NULL	NULL
7	7
8	15
9	24
10	44
10	44
11	55
12	67
SELECT i, NTH_VALUE(i,3) OVER (ORDER BY i) FROM tj UNION ALL SELECT i, NTH_VALUE(i, 3) OVER (ORDER BY i) FROM tj;
i	NTH_VALUE(i,3) OVER (ORDER BY i)
NULL	NULL
7	NULL
8	8
9	8
10	8
10	8
11	8
12	8
NULL	NULL
7	NULL
8	8
9	8
10	8
10	8
11	8
12	8
SELECT i, NTH_VALUE(i + 3,3) OVER (ORDER BY i) FROM tj UNION ALL SELECT i, NTH_VALUE(i + 3, 3) OVER (ORDER BY i) FROM tj;
i	NTH_VALUE(i + 3,3) OVER (ORDER BY i)
NULL	NULL
7	NULL
8	11
9	11
10	11
10	11
11	11
12	11
NULL	NULL
7	NULL
8	11
9	11
10	11
10	11
11	11
12	11
SELECT i, LEAD(i,3) OVER (ORDER BY i) FROM tj UNION ALL SELECT i, LEAD(i, 3) OVER (ORDER BY i) FROM tj;
i	LEAD(i,3) OVER (ORDER BY i)
NULL	9
7	10
8	10
9	11
10	12
10	NULL
11	NULL
12	NULL
NULL	9
7	10
8	10
9	11
10	12
10	NULL
11	NULL
12	NULL
SELECT i, FIRST_VALUE(i) OVER (ORDER BY i) FROM tj UNION ALL SELECT i, FIRST_VALUE(i) OVER (ORDER BY i) FROM tj;
i	FIRST_VALUE(i) OVER (ORDER BY i)
NULL	NULL
7	NULL
8	NULL
9	NULL
10	NULL
10	NULL
11	NULL
12	NULL
NULL	NULL
7	NULL
8	NULL
9	NULL
10	NULL
10	NULL
11	NULL
12	NULL
SELECT i, LAST_VALUE(i) OVER (ORDER BY i) FROM tj UNION ALL SELECT i, LAST_VALUE(i) OVER (ORDER BY i) FROM tj;
i	LAST_VALUE(i) OVER (ORDER BY i)
NULL	NULL
7	7
8	8
9	9
10	10
10	10
11	11
12	12
NULL	NULL
7	7
8	8
9	9
10	10
10	10
11	11
12	12
DROP TABLE tj;
----------------------------------------------------------------------
-    More JSON
----------------------------------------------------------------------
CREATE TABLE tj(j JSON);
INSERT INTO tj VALUES ('1');
INSERT INTO tj VALUES ('2');
INSERT INTO tj VALUES ('3');
INSERT INTO tj VALUES ('4');
INSERT INTO tj VALUES ('5');
INSERT INTO tj VALUES (NULL);
SELECT j, JSON_TYPE(j), SUM(j) OVER (ORDER BY j ROWS 3 PRECEDING) FROM tj;
j	JSON_TYPE(j)	SUM(j) OVER (ORDER BY j ROWS 3 PRECEDING)
NULL	NULL	NULL
1	INTEGER	1
2	INTEGER	3
3	INTEGER	6
4	INTEGER	10
5	INTEGER	14
SELECT j, JSON_TYPE(j), SUM(j) OVER (ORDER BY j RANGE 3 PRECEDING) FROM tj;
ERROR HY000: Window '<unnamed window>' with RANGE N PRECEDING/FOLLOWING frame requires exactly one ORDER BY expression, of numeric or temporal type
INSERT INTO tj VALUES ('3.14');
SELECT j, JSON_TYPE(j), SUM(j) OVER (ORDER BY j ROWS 3 PRECEDING) FROM tj;
j	JSON_TYPE(j)	SUM(j) OVER (ORDER BY j ROWS 3 PRECEDING)
NULL	NULL	NULL
1	INTEGER	1
2	INTEGER	3
3	INTEGER	6
3.14	DOUBLE	9.14
4	INTEGER	12.14
5	INTEGER	15.14
INSERT INTO tj VALUES ('[1,2,3]');
SELECT j,
JSON_TYPE(j),
SUM(CASE WHEN JSON_TYPE(j) = 'ARRAY' THEN j->"$[0]" ELSE j END)
OVER (ORDER BY j ROWS 3 PRECEDING)
FROM tj;
j	JSON_TYPE(j)	SUM(CASE WHEN JSON_TYPE(j) = 'ARRAY' THEN j->"$[0]" ELSE j END)
OVER (ORDER BY j ROWS 3 PRECEDING)
NULL	NULL	NULL
1	INTEGER	1
2	INTEGER	3
3	INTEGER	6
3.14	DOUBLE	9.14
4	INTEGER	12.14
5	INTEGER	15.14
[1, 2, 3]	ARRAY	13.14
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
CREATE TABLE t5(b BIGINT UNSIGNED);
INSERT INTO t5 VALUES (1);
INSERT INTO t5 VALUES (2);
INSERT INTO t5 VALUES (3);
INSERT INTO t5 VALUES (4);
INSERT INTO t5 VALUES (5);
INSERT INTO t5 VALUES (6);
INSERT INTO t5 VALUES (7);
last row should have COUNT(*) == 0 , not 1 (bug fix)
SELECT b, COUNT(*) OVER (ORDER BY b RANGE BETWEEN 1 FOLLOWING AND  100 FOLLOWING) bb FROM t5;
b	bb
1	6
2	5
3	4
4	3
5	2
6	1
7	0
CREATE TABLE t6(t TIME, ts TIMESTAMP);
INSERT INTO t6 VALUES ('12:30', '2016-07-05 08:30:42');
INSERT INTO t6 VALUES ('22:30', '2015-07-05 08:30:43');
INSERT INTO t6 VALUES ('13:30', '2014-07-05 08:30:44');
INSERT INTO t6 VALUES ('01:30', '2013-07-05 08:30:45');
INSERT INTO t6 VALUES ('15:30', '2016-08-05 08:31:42');
INSERT INTO t6 VALUES ('20:30', '2016-09-05 08:32:42');
INSERT INTO t6 VALUES ('04:30', '2016-10-05 08:33:42');
INSERT INTO t6 VALUES ('06:30', '2016-11-05 08:34:42');
INSERT INTO t6 VALUES ('18:30', '2016-07-05 09:30:42');
INSERT INTO t6 VALUES ('21:30', '2016-07-06 10:30:42');
INSERT INTO t6 VALUES ('00:30', '2016-07-07 11:30:42');
INSERT INTO t6 VALUES ('00:31', '2016-07-08 12:30:42');
INTERVAL specified with string as below failed
SELECT t, COUNT(*) OVER (ORDER BY t RANGE
BETWEEN INTERVAL 1 HOUR PRECEDING AND INTERVAL '2:2' MINUTE_SECOND FOLLOWING) AS cnt FROM t6;
t	cnt
00:30:00	2
00:31:00	2
01:30:00	3
04:30:00	1
06:30:00	1
12:30:00	1
13:30:00	2
15:30:00	1
18:30:00	1
20:30:00	1
21:30:00	2
22:30:00	2
----------------------------------------------------------------------
-    Window spec inheritance
----------------------------------------------------------------------
SELECT COUNT(*) OVER w0,
COUNT(*) OVER w,
COUNT(*) OVER w1 FROM t6
WINDOW w0 AS (),
w  AS (w0 ORDER BY t),
w1 AS (w RANGE BETWEEN INTERVAL 24 HOUR  PRECEDING AND
INTERVAL '2:2' MINUTE_SECOND FOLLOWING);
COUNT(*) OVER w0	COUNT(*) OVER w	COUNT(*) OVER w1
12	1	2
12	2	2
12	3	3
12	4	4
12	5	5
12	6	6
12	7	7
12	8	8
12	9	9
12	10	10
12	11	11
12	12	12
SELECT t, COUNT(t) OVER w1 FROM t6
WINDOW w1 AS (),
w2 AS (w1 PARTITION BY t);
ERROR HY000: A window which depends on another cannot define partitioning.
SELECT t, COUNT(t) OVER w1 FROM t6
WINDOW w1 AS (ORDER BY t),
w2 AS (w1 ORDER BY t);
ERROR HY000: Window 'w2' cannot inherit 'w1' since both contain an ORDER BY clause.
SELECT t, COUNT(t) OVER w1 FROM t6
WINDOW w1 AS (ORDER BY t RANGE BETWEEN CURRENT ROW AND
INTERVAL 1 DAY FOLLOWING),
w2 AS (w1);
ERROR HY000: Window 'w1' has a frame definition, so cannot be referenced by another window.
CREATE VIEW v AS
SELECT COUNT(*) OVER w0,
COUNT(*) OVER w,
COUNT(*) OVER w1 FROM t6
WINDOW w0 AS (),
w  AS (w0 ORDER BY t),
w1 AS (w RANGE BETWEEN INTERVAL 24 HOUR  PRECEDING AND
INTERVAL '2:2' MINUTE_SECOND FOLLOWING);
SHOW CREATE VIEW v;
View	Create View	character_set_client	collation_connection
v	CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v` AS select count(0) OVER `w0` AS `COUNT(*) OVER w0`,count(0) OVER `w` AS `COUNT(*) OVER w`,count(0) OVER `w1` AS `COUNT(*) OVER w1` from `t6` window `w0` AS () , `w` AS (`w0` ORDER BY `t6`.`t` ) , `w1` AS (`w` RANGE BETWEEN INTERVAL 24 hour  PRECEDING AND INTERVAL '2:2' minute_second  FOLLOWING) 	utf8mb3	utf8mb3_general_ci
SELECT * FROM v;
COUNT(*) OVER w0	COUNT(*) OVER w	COUNT(*) OVER w1
12	1	2
12	2	2
12	3	3
12	4	4
12	5	5
12	6	6
12	7	7
12	8	8
12	9	9
12	10	10
12	11	11
12	12	12
DROP VIEW v;
----------------------------------------------------------------------
- Bugs with induction of hidden fields from window function also used
- in ORDER BY/PARTITION BY
----------------------------------------------------------------------
SELECT id, AVG(id) OVER (PARTITION BY id) summ FROM t1;
id	summ
NULL	NULL
1	1.0000
2	2.0000
3	3.0000
4	4.0000
5	5.0000
10	10.0000
10	10.0000
11	11.0000
SELECT     AVG(id) OVER (PARTITION BY id) summ FROM t1;
summ
NULL
1.0000
2.0000
3.0000
4.0000
5.0000
10.0000
10.0000
11.0000
SELECT id, AVG(id) OVER (PARTITION BY id) summ,
AVG(id) OVER (PARTITION BY id) summ2 FROM t1;
id	summ	summ2
NULL	NULL	NULL
1	1.0000	1.0000
2	2.0000	2.0000
3	3.0000	3.0000
4	4.0000	4.0000
5	5.0000	5.0000
10	10.0000	10.0000
10	10.0000	10.0000
11	11.0000	11.0000
SELECT     AVG(id) OVER (PARTITION BY id) summ,
AVG(id) OVER (PARTITION BY id) summ2 FROM t1;
summ	summ2
NULL	NULL
1.0000	1.0000
2.0000	2.0000
3.0000	3.0000
4.0000	4.0000
5.0000	5.0000
10.0000	10.0000
10.0000	10.0000
11.0000	11.0000
Bug for AVG in presence of several NULLs
INSERT INTO t1 VALUES (NULL, 'F');
SELECT COUNT(id) OVER w, id, AVG(id) OVER w, SUM(id) OVER w, FIRST_VALUE(id) OVER w FROM t1
WINDOW w AS (ORDER BY id RANGE 1 PRECEDING);
COUNT(id) OVER w	id	AVG(id) OVER w	SUM(id) OVER w	FIRST_VALUE(id) OVER w
0	NULL	NULL	NULL	NULL
0	NULL	NULL	NULL	NULL
1	1	1.0000	1	1
2	2	1.5000	3	1
2	3	2.5000	5	2
2	4	3.5000	7	3
2	5	4.5000	9	4
2	10	10.0000	20	10
2	10	10.0000	20	10
3	11	10.3333	31	10
Repeat previous test, just with REAL to get coverage
CREATE TABLE t1r (id REAL, sex CHAR(1));
INSERT INTO t1r VALUES (1.0, 'M');
INSERT INTO t1r VALUES (2.0, 'F');
INSERT INTO t1r VALUES (3.0, 'F');
INSERT INTO t1r VALUES (4.0, 'F');
INSERT INTO t1r VALUES (5.0, 'M');
INSERT INTO t1r VALUES (10.0, NULL);
INSERT INTO t1r VALUES (11.0, NULL);
INSERT INTO t1r VALUES (10.0, NULL);
INSERT INTO t1r VALUES (NULL, 'M');
INSERT INTO t1r VALUES (NULL, 'F');
SET windowing_use_high_precision= OFF;
SELECT COUNT(id) OVER w, id, AVG(id) OVER w, SUM(id) OVER w, FIRST_VALUE(id) OVER w FROM t1r
WINDOW w AS (ORDER BY id RANGE 1 PRECEDING);
COUNT(id) OVER w	id	AVG(id) OVER w	SUM(id) OVER w	FIRST_VALUE(id) OVER w
0	NULL	NULL	NULL	NULL
0	NULL	NULL	NULL	NULL
1	1	1	1	1
2	2	1.5	3	1
2	3	2.5	5	2
2	4	3.5	7	3
2	5	4.5	9	4
2	10	10	20	10
2	10	10	20	10
3	11	10.333333333333334	31	10
SET windowing_use_high_precision= ON;
DROP TABLE t1r;
Check frame size, COUNT(*) vs COUNT(<column>) in frames with NULLs
SELECT id, count(id) over w, count(*) over w, FIRST_VALUE(id) OVER w FROM t1
WINDOW w AS (ORDER BY id ASC  RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING);
id	count(id) over w	count(*) over w	FIRST_VALUE(id) OVER w
NULL	0	2	NULL
NULL	0	2	NULL
1	0	0	NULL
2	1	1	1
3	2	2	1
4	2	2	2
5	2	2	3
10	0	0	NULL
10	0	0	NULL
11	2	2	10
SELECT id, count(id) over w, count(*) over w, FIRST_VALUE(id) OVER w FROM t1
WINDOW w AS (ORDER BY id DESC RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING);
id	count(id) over w	count(*) over w	FIRST_VALUE(id) OVER w
11	0	0	NULL
10	1	1	11
10	1	1	11
5	0	0	NULL
4	1	1	5
3	2	2	5
2	2	2	4
1	2	2	3
NULL	0	2	NULL
NULL	0	2	NULL
DROP TABLE t1, t11, t2, t22, t3, t4, t5, t6, tj;
DROP TABLE t;
----------------------------------------------------------------------
-    Test NULL handling with RANGE
----------------------------------------------------------------------
CREATE TABLE t(i INT);
INSERT INTO t VALUES (NULL), (NULL), (1), (2), (3), (4), (5);
SELECT COUNT(*) OVER (ORDER BY i RANGE BETWEEN 1 FOLLOWING AND UNBOUNDED FOLLOWING) FROM t;
COUNT(*) OVER (ORDER BY i RANGE BETWEEN 1 FOLLOWING AND UNBOUNDED FOLLOWING)
7
7
4
3
2
1
0
SELECT COUNT(*) OVER (ORDER BY i RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING) FROM t;
COUNT(*) OVER (ORDER BY i RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING)
2
2
2
2
2
1
0
SELECT COUNT(*) OVER (ORDER BY i RANGE BETWEEN 1 PRECEDING AND 2 FOLLOWING) FROM t;
COUNT(*) OVER (ORDER BY i RANGE BETWEEN 1 PRECEDING AND 2 FOLLOWING)
2
2
3
4
4
3
2
SELECT COUNT(*) OVER (ORDER BY i RANGE BETWEEN 1 PRECEDING AND UNBOUNDED FOLLOWING) FROM  t;
COUNT(*) OVER (ORDER BY i RANGE BETWEEN 1 PRECEDING AND UNBOUNDED FOLLOWING)
7
7
5
5
4
3
2
SELECT COUNT(*) OVER (ORDER BY i RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING) FROM  t;
COUNT(*) OVER (ORDER BY i RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING)
2
2
0
1
2
2
2
SELECT COUNT(*) OVER (ORDER BY i RANGE BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) FROM  t;
COUNT(*) OVER (ORDER BY i RANGE BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)
7
7
7
7
7
7
7
SELECT COUNT(*) OVER (ORDER BY i RANGE BETWEEN UNBOUNDED PRECEDING AND 2 PRECEDING) FROM  t;
COUNT(*) OVER (ORDER BY i RANGE BETWEEN UNBOUNDED PRECEDING AND 2 PRECEDING)
2
2
2
2
3
4
5
SELECT COUNT(*) OVER (ORDER BY i RANGE BETWEEN UNBOUNDED PRECEDING AND 2 FOLLOWING) FROM  t;
COUNT(*) OVER (ORDER BY i RANGE BETWEEN UNBOUNDED PRECEDING AND 2 FOLLOWING)
2
2
5
6
7
7
7
SELECT COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN 1 FOLLOWING AND UNBOUNDED FOLLOWING) FROM t;
COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN 1 FOLLOWING AND UNBOUNDED FOLLOWING)
6
5
4
3
2
2
2
SELECT COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING) FROM t;
COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING)
2
2
2
1
0
2
2
SELECT COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN 1 PRECEDING AND 2 FOLLOWING) FROM t;
COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN 1 PRECEDING AND 2 FOLLOWING)
3
4
4
3
2
2
2
SELECT COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN 1 PRECEDING AND UNBOUNDED FOLLOWING) FROM  t;
COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN 1 PRECEDING AND UNBOUNDED FOLLOWING)
7
7
6
5
4
2
2
SELECT COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING) FROM  t;
COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN 2 PRECEDING AND 1 PRECEDING)
0
1
2
2
2
2
2
SELECT COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) FROM  t;
COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)
7
7
7
7
7
7
7
SELECT COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN UNBOUNDED PRECEDING AND 2 PRECEDING) FROM  t;
COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN UNBOUNDED PRECEDING AND 2 PRECEDING)
0
0
1
2
3
7
7
SELECT COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN UNBOUNDED PRECEDING AND 2 FOLLOWING) FROM  t;
COUNT(*) OVER (ORDER BY i DESC RANGE BETWEEN UNBOUNDED PRECEDING AND 2 FOLLOWING)
3
4
5
5
5
7
7
DROP TABLE t;
----------------------------------------------------------------------
-    ORDER BY + RANK with more than one ordering expression
----------------------------------------------------------------------
CREATE TABLE t(i INT, j INT, k INT);
INSERT INTO t VALUES (1,1,1);
INSERT INTO t VALUES (1,1,2);
INSERT INTO t VALUES (1,1,2);
INSERT INTO t VALUES (1,2,1);
INSERT INTO t VALUES (1,2,2);
INSERT INTO t VALUES (2,1,1);
INSERT INTO t VALUES (2,1,1);
INSERT INTO t VALUES (2,1,2);
INSERT INTO t VALUES (2,2,1);
INSERT INTO t VALUES (2,2,2);
SELECT *, RANK() OVER (ORDER BY i,j,k) AS O_IJK,
RANK() OVER (ORDER BY j) AS O_J,
RANK() OVER (ORDER BY k,j) AS O_KJ FROM t ORDER BY i,j,k;
i	j	k	O_IJK	O_J	O_KJ
1	1	1	1	1	1
1	1	2	2	1	6
1	1	2	2	1	6
1	2	1	4	7	4
1	2	2	5	7	9
2	1	1	6	1	1
2	1	1	6	1	1
2	1	2	8	1	6
2	2	1	9	7	4
2	2	2	10	7	9
DROP TABLE t;
----------------------------------------------------------------------
-    Gulutzan's sanity tests in
-    http://ocelot.ca/blog/blog/2016/04/18/mariadb-10-2-window-functions/
-    His comments are quoted.
----------------------------------------------------------------------
CREATE TABLE t1 (s1 INT, s2 CHAR(5));
INSERT INTO t1 VALUES (1, 'a');
INSERT INTO t1 VALUES (NULL, NULL);
INSERT INTO t1 VALUES (1, NULL);
INSERT INTO t1 VALUES (NULL, 'a');
INSERT INTO t1 VALUES (2, 'b');
INSERT INTO t1 VALUES (-1, '');
"The following statements all cause the MariaDB server to crash"
MySQL doesn't crash
SELECT ROW_NUMBER() OVER ();
ROW_NUMBER() OVER ()
1
SELECT 1 AS a, ROW_NUMBER() OVER (ORDER BY a) FROM dual;
ERROR 42S22: Unknown column 'a' in 'window order by'
SELECT *, ABS(ROW_NUMBER() OVER (ORDER BY s1,s2))
- ROW_NUMBER() OVER (ORDER BY s1,s2) AS X FROM t1;
s1	s2	X
NULL	NULL	0
NULL	a	0
-1		0
1	NULL	0
1	a	0
2	b	0
SELECT RANK() OVER (ORDER BY AVG(s1)) FROM t1;
RANK() OVER (ORDER BY AVG(s1))
1
"The following statements all give the wrong answers with MariaDB"
Correct with MySQL.
SELECT COUNT(*) OVER (ORDER BY s2) FROM t1 WHERE s2 IS NULL;
COUNT(*) OVER (ORDER BY s2)
2
2
SELECT * FROM (
SELECT *,DENSE_RANK() OVER (ORDER BY s2 DESC),
DENSE_RANK() OVER (ORDER BY s2) FROM t1
) alias ORDER BY s1,s2;
s1	s2	DENSE_RANK() OVER (ORDER BY s2 DESC)	DENSE_RANK() OVER (ORDER BY s2)
NULL	NULL	4	1
NULL	a	2	3
-1		3	2
1	NULL	4	1
1	a	2	3
2	b	1	4
SELECT * FROM (
SELECT *, SUM(s1) OVER (ORDER BY s1) FROM t1 ORDER BY s1
) alias ORDER BY s1,s2;
s1	s2	SUM(s1) OVER (ORDER BY s1)
NULL	NULL	NULL
NULL	a	NULL
-1		-1
1	NULL	1
1	a	1
2	b	3
SELECT AVG(s1), RANK() OVER (ORDER BY s1) FROM t1;
ERROR 42000: In aggregated query without GROUP BY, expression #1 of PARTITION BY or ORDER BY clause of window '<unnamed window>' contains nonaggregated column 'test.t1.s1'; this is incompatible with sql_mode=only_full_group_by
"The following statement causes the client to hang (it loops in
mysql_store_result, I think this is the first time I've seen this type of
error)"
No issue with MySQL
SELECT *, AVG(s1) OVER () FROM t1;
s1	s2	AVG(s1) OVER ()
1	a	0.7500
NULL	NULL	0.7500
1	NULL	0.7500
NULL	a	0.7500
2	b	0.7500
-1		0.7500
SELECT *, AVG(s1) OVER (ROWS UNBOUNDED PRECEDING) FROM t1;
s1	s2	AVG(s1) OVER (ROWS UNBOUNDED PRECEDING)
1	a	1.0000
NULL	NULL	1.0000
1	NULL	1.0000
NULL	a	1.0000
2	b	1.3333
-1		0.7500
DROP TABLE t1;
Some negative tests (from Srikanth)
CREATE TABLE t (a INT, b INT, c INT);
INSERT INTO t VALUES (1,1,1);
INSERT INTO t VALUES (1,1,2);
INSERT INTO t VALUES (1,1,3);
INSERT INTO t VALUES (1,2,1);
INSERT INTO t VALUES (1,2,2);
INSERT INTO t VALUES (1,2,3);
INSERT INTO t VALUES (1,3,1);
INSERT INTO t VALUES (1,3,2);
INSERT INTO t VALUES (1,3,3);
INSERT INTO t VALUES (2,1,1);
INSERT INTO t VALUES (2,1,2);
INSERT INTO t VALUES (2,1,3);
INSERT INTO t VALUES (2,2,1);
INSERT INTO t VALUES (2,2,2);
INSERT INTO t VALUES (2,2,3);
INSERT INTO t VALUES (2,3,1);
INSERT INTO t VALUES (2,3,2);
INSERT INTO t VALUES (2,3,3);
Wfs OK in ORDER BY, but not in WHERE or HAVING clauses
SELECT * FROM t ORDER BY RANK() OVER (ORDER BY a DESC,b,c);
a	b	c
2	1	1
2	1	2
2	1	3
2	2	1
2	2	2
2	2	3
2	3	1
2	3	2
2	3	3
1	1	1
1	1	2
1	1	3
1	2	1
1	2	2
1	2	3
1	3	1
1	3	2
1	3	3
SELECT *, RANK() OVER (ORDER BY a DESC,b,c) AS `rank` FROM t ORDER BY `rank`;
a	b	c	rank
2	1	1	1
2	1	2	2
2	1	3	3
2	2	1	4
2	2	2	5
2	2	3	6
2	3	1	7
2	3	2	8
2	3	3	9
1	1	1	10
1	1	2	11
1	1	3	12
1	2	1	13
1	2	2	14
1	2	3	15
1	3	1	16
1	3	2	17
1	3	3	18
SELECT * FROM t WHERE 1 = RANK() OVER (ORDER BY a);
ERROR HY000: You cannot use the window function 'rank' in this context.'
SELECT * FROM t HAVING 1 = rank() OVER (ORDER BY a);
ERROR HY000: You cannot use the window function 'rank' in this context.'
SELECT 1 FROM t HAVING 1=(SELECT 1 FROM (SELECT 1) foo) AND COUNT(a) OVER ();
ERROR HY000: You cannot use the window function 'count' in this context.'
#
# Bug#26502118: WINDOW FUNCTIONS: CRASH AND LARGE MEMORY ALLOCATION,
#               FIRST_VALUE, BLOBS
#
# This was really missing error detection
#
CREATE TABLE tb(a LONGBLOB NOT NULL);
INSERT INTO tb VALUES ('1'), ('2'), ('3'), ('4'), ('5');
SELECT (SELECT 1 FROM tb WHERE (SELECT 1 FROM tb WHERE FIRST_VALUE(a) OVER()));
ERROR HY000: You cannot use the window function 'first_value' in this context.'
DROP TABLE tb;
Windows should only be allowed in order by of a simple table query
(select a from t) union (select a from t) order by (row_number() over ());
ERROR HY000: Expression #1 of ORDER BY contains aggregate function and applies to a UNION, EXCEPT or INTERSECT
(select a from t) union (select a from t) order by (1+row_number() over ());
ERROR HY000: Expression #1 of ORDER BY contains aggregate function and applies to a UNION, EXCEPT or INTERSECT
This is legal, though:
(select a from t) union (select a from t order by (row_number() over ()));
a
1
2
Non constants as frame bounds
SELECT a AS foo, SUM(a) OVER (ORDER BY a ROWS foo PRECEDING)  FROM t;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'foo PRECEDING)  FROM t' at line 1
SELECT a, SUM(a) OVER (ORDER BY a ROWS a PRECEDING)  FROM t;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'a PRECEDING)  FROM t' at line 1
Non-unique window name
SELECT count(*) OVER w FROM t WINDOW w AS (ORDER BY a), w AS (ORDER BY b);
ERROR HY000: Window 'w' is defined twice.
Illegal legacy position indication in window's ORDER BY clause
SELECT RANK() OVER (ORDER BY 1) FROM t;
ERROR HY000: Window '<unnamed window>': ORDER BY or PARTITION BY uses legacy position indication which is not supported, use expression.
SELECT * FROM (
SELECT a,b,c, RANK() OVER (ORDER BY 1*1) FROM t
) alias ORDER BY a,b,c;
a	b	c	RANK() OVER (ORDER BY 1*1)
1	1	1	1
1	1	2	1
1	1	3	1
1	2	1	1
1	2	2	1
1	2	3	1
1	3	1	1
1	3	2	1
1	3	3	1
2	1	1	1
2	1	2	1
2	1	3	1
2	2	1	1
2	2	2	1
2	2	3	1
2	3	1	1
2	3	2	1
2	3	3	1
Crashed: more than one window in subquery
SELECT * FROM (SELECT count(*) OVER (), sum(c) OVER () AS sum1, a from t) as alias;
count(*) OVER ()	sum1	a
18	36	1
18	36	1
18	36	1
18	36	1
18	36	1
18	36	1
18	36	1
18	36	1
18	36	1
18	36	2
18	36	2
18	36	2
18	36	2
18	36	2
18	36	2
18	36	2
18	36	2
18	36	2
Crashed: expression containing window function(s) in subquery
SELECT * FROM (SELECT count(*) OVER () + sum(c) OVER () AS sum1, a from t) as alias;
sum1	a
54	1
54	1
54	1
54	1
54	1
54	1
54	1
54	1
54	1
54	2
54	2
54	2
54	2
54	2
54	2
54	2
54	2
54	2
Wrong result if subquery window function referenced another column in the select list
This was OK, but:
SELECT * FROM (SELECT SUM(b) OVER (), a FROM t) AS alias;
SUM(b) OVER ()	a
36	1
36	1
36	1
36	1
36	1
36	1
36	1
36	1
36	1
36	2
36	2
36	2
36	2
36	2
36	2
36	2
36	2
36	2
this one failed with NULL as sum
SELECT * FROM (SELECT SUM(b) OVER (), b FROM t) AS alias;
SUM(b) OVER ()	b
36	1
36	1
36	1
36	2
36	2
36	2
36	3
36	3
36	3
36	1
36	1
36	1
36	2
36	2
36	2
36	3
36	3
36	3
Crash due to unguarded access for window name string for an unnamed
window while producing the error message
SELECT a, b, c, rank() OVER ( w  ORDER BY c DESC )  FROM t WINDOW w AS (ORDER BY a);
ERROR HY000: Window '<unnamed window>' cannot inherit 'w' since both contain an ORDER BY clause.
Check that DISTINCT is not allowed in wfs
SELECT SUM(DISTINCT b) OVER () FROM t;
ERROR 42000: This version of MySQL doesn't yet support '<window function>(DISTINCT ..)'
Check that GROUPS bounds unit is not supported yet
SELECT SUM(b) OVER (ORDER by a GROUPS 2 PRECEDING) FROM t;
ERROR 42000: This version of MySQL doesn't yet support 'GROUPS'
UPDATE t set a= SUM(b) OVER ();
ERROR HY000: You cannot use the window function 'sum' in this context.'
DELETE FROM t WHERE SUM(b) OVER () = 10;
ERROR HY000: You cannot use the window function 'sum' in this context.'
Check that EXCLUDE in frames is not supported yet
SELECT SUM(b) OVER (ORDER by a ROWS 2 PRECEDING EXCLUDE CURRENT ROW) FROM t;
ERROR 42000: This version of MySQL doesn't yet support 'EXCLUDE'
SELECT SUM(b) OVER (ORDER by a ROWS 2 PRECEDING EXCLUDE TIES) FROM t;
ERROR 42000: This version of MySQL doesn't yet support 'EXCLUDE'
SELECT SUM(b) OVER (ORDER by a ROWS 2 PRECEDING EXCLUDE GROUP) FROM t;
ERROR 42000: This version of MySQL doesn't yet support 'EXCLUDE'
SELECT SUM(b) OVER (ORDER by a ROWS 2 PRECEDING EXCLUDE NO OTHERS) FROM t;
ERROR 42000: This version of MySQL doesn't yet support 'EXCLUDE'
Check Nested wfs
SELECT a, b, FIRST_VALUE(SUM(a+b) OVER()) OVER () AS sum FROM t;
ERROR HY000: You cannot use the window function 'sum' in this context.'
SELECT a, b, FIRST_VALUE(1+SUM(a+b) OVER()) OVER () AS sum FROM t;
ERROR HY000: You cannot use the window function 'sum' in this context.'
SELECT a, b, SUM(1+SUM(a+b) OVER()) OVER () AS sum FROM t;
ERROR HY000: You cannot use the window function 'sum' in this context.'
SELECT a, b, FIRST_VALUE(a) OVER (PARTITION BY ROW_NUMBER() OVER ()) AS sum FROM t;
ERROR HY000: You cannot nest a window function in the specification of window '<unnamed window>'.
SELECT a, b, FIRST_VALUE(a) OVER (PARTITION BY 1+ROW_NUMBER() OVER ()) AS sum FROM t;
ERROR HY000: You cannot nest a window function in the specification of window '<unnamed window>'.
SELECT a, b, FIRST_VALUE(a) OVER (ORDER BY ROW_NUMBER() OVER ()) AS sum FROM t;
ERROR HY000: You cannot nest a window function in the specification of window '<unnamed window>'.
CREATE TABLE t_time(t TIME, ts TIMESTAMP);
SELECT t, FIRST_VALUE(t) OVER (ORDER BY t RANGE INTERVAL ROW_NUMBER() OVER () HOUR PRECEDING) FROM t_time;
ERROR HY000: You cannot use the window function 'row_number' in this context.'
SELECT t, FIRST_VALUE(t) OVER (ORDER BY t RANGE INTERVAL ABS(t) HOUR PRECEDING) FROM t_time;
ERROR HY000: Window '<unnamed window>' has a non-constant frame bound.
SELECT t, FIRST_VALUE(t) OVER (ORDER BY t RANGE BETWEEN INTERVAL 3 HOUR PRECEDING AND INTERVAL ABS(t) HOUR FOLLOWING) FROM t_time;
ERROR HY000: Window '<unnamed window>' has a non-constant frame bound.
DROP TABLE t, t_time;
Crash report (Srikanth)
CREATE TABLE t(a int, b int);
INSERT INTO t VALUES (1,1);
INSERT INTO t VALUES (2,1);
INSERT INTO t VALUES (3,2);
INSERT INTO t VALUES (4,2);
INSERT INTO t VALUES (5,3);
INSERT INTO t VALUES (6,3);
SELECT SUM(a) OVER (ORDER BY b) FROM t;
SUM(a) OVER (ORDER BY b)
3
3
10
10
21
21
SELECT COUNT(*) OVER (ORDER BY b) FROM t;
COUNT(*) OVER (ORDER BY b)
2
2
4
4
6
6
SELECT AVG(b) OVER (ORDER BY b) FROM t;
AVG(b) OVER (ORDER BY b)
1.0000
1.0000
1.5000
1.5000
2.0000
2.0000
SELECT a,b,LAST_VALUE(a) OVER (ORDER BY b,a) FROM t;
a	b	LAST_VALUE(a) OVER (ORDER BY b,a)
1	1	1
2	1	2
3	2	3
4	2	4
5	3	5
6	3	6
SELECT NTILE(2) OVER (ORDER BY b) FROM t;
NTILE(2) OVER (ORDER BY b)
1
1
1
2
2
2
DROP TABLE t;
Wrong result (Srikanth)
CREATE TABLE t1(a INT, b INT);
INSERT INTO t1 VALUES (1,2);
INSERT INTO t1 VALUES (1,3);
SELECT a, b, COUNT(a) OVER w count,
SUM(a) OVER w sum,
AVG(a) over w average,
LAST_VALUE(a) OVER w lastval FROM t1
WINDOW w as (PARTITION BY a ORDER BY b ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING);
a	b	count	sum	average	lastval
1	2	2	2	1.0000	1
1	3	2	2	1.0000	1
INSERT INTO t1 VALUES (1,3);
SELECT a, b, COUNT(a) OVER w count,
SUM(a) OVER w sum,
AVG(a) OVER w average,
LAST_VALUE(a) OVER w lastval FROM t1
WINDOW w as (PARTITION BY a ORDER BY b ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING);
a	b	count	sum	average	lastval
1	2	2	2	1.0000	1
1	3	3	3	1.0000	1
1	3	2	2	1.0000	1
SELECT a, b, COUNT(a) OVER w count,
SUM(a) OVER w sum,
AVG(a) OVER w average,
LAST_VALUE(a) OVER w lastval FROM t1
WINDOW w as (PARTITION BY a ORDER BY b ROWS BETWEEN 1 PRECEDING AND 2 FOLLOWING);
a	b	count	sum	average	lastval
1	2	3	3	1.0000	1
1	3	3	3	1.0000	1
1	3	2	2	1.0000	1
DROP TABLE t1;
frame buffer navigation assert
CREATE TABLE ta (a INT(11) DEFAULT NULL, b INT(11) DEFAULT NULL);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO  ta VALUES (1,1);
INSERT INTO  ta VALUES (1,2);
INSERT INTO  ta VALUES (1,3);
INSERT INTO  ta VALUES (2,1);
INSERT INTO  ta VALUES (2,2);
INSERT INTO  ta VALUES (2,3);
SELECT last_value(b) OVER (ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) FROM ta;
last_value(b) OVER (ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING)
3
3
3
3
3
3
DROP TABLE ta;
Nullability fix bug for COUNT OVER in non optimized eval strategy
CREATE TABLE t(d DOUBLE);
INSERT INTO t VALUES (1.0);
INSERT INTO t VALUES (2.0);
INSERT INTO t VALUES (3.0);
SELECT SUM(d) OVER w, COUNT(*) OVER w FROM t WINDOW W AS (ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
SUM(d) OVER w	COUNT(*) OVER w
5	2
3	1
NULL	0
DROP TABLE t;
Bug in inverse logic with initial NULL and RANGE BETWEEN N FOLLOWING AND M FOLLOWING
CREATE TABLE t1 (d DOUBLE, id INT, sex CHAR(1), n INT NOT NULL AUTO_INCREMENT, PRIMARY KEY(n));
INSERT INTO t1(d, id, sex) VALUES (1.0, 1, 'M');
INSERT INTO t1(d, id, sex) VALUES (2.0, 2, 'F');
INSERT INTO t1(d, id, sex) VALUES (3.0, 3, 'F');
INSERT INTO t1(d, id, sex) VALUES (4.0, 4, 'F');
INSERT INTO t1(d, id, sex) VALUES (5.0, 5, 'M');
INSERT INTO t1(d, id, sex) VALUES (NULL, NULL, 'M');
INSERT INTO t1(d, id, sex) VALUES (10.0, 10, NULL);
INSERT INTO t1(d, id, sex) VALUES (10.0, 10, NULL);
INSERT INTO t1(d, id, sex) VALUES (11.0, 11, NULL);
SELECT id, AVG(id) over w `avg`, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt FROM t1 WINDOW w as (ORDER BY id RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
id	avg	sum	cnt
NULL	NULL	NULL	1
1	2.5000	5	2
2	3.5000	7	2
3	4.5000	9	2
4	5.0000	5	1
5	NULL	NULL	0
10	11.0000	11	1
10	11.0000	11	1
11	NULL	NULL	0
SET windowing_use_high_precision= OFF;
SELECT d, AVG(d) over w `avg`, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt FROM t1 WINDOW w as (ORDER BY d RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING);
d	avg	sum	cnt
NULL	NULL	NULL	1
1	2.5	5	2
2	3.5	7	2
3	4.5	9	2
4	5	5	1
5	NULL	NULL	0
10	11	11	1
10	11	11	1
11	NULL	NULL	0
SET windowing_use_high_precision= ON;
DROP TABLE t1;
Bug in inverse logic with e.g. ROWS BETWEEN UNBOUNDED PRECEDING AND 1
FOLLOWING: at end of partition, when no rows are removed or added we
lacked initialization of aggregates in optimized mode.
CREATE TABLE t (i char(10), j int);
INSERT INTO t VALUES('A', 1);
INSERT INTO t VALUES('A', 3);
INSERT INTO t VALUES('A', 5);
INSERT INTO t VALUES('B', 1);
INSERT INTO t VALUES('B', 7);
SELECT i, j, SUM(j) OVER w FROM t
WINDOW w AS (PARTITION BY i ORDER BY j
ROWS BETWEEN UNBOUNDED PRECEDING AND 1 FOLLOWING);
i	j	SUM(j) OVER w
A	1	4
A	3	9
A	5	9
B	1	8
B	7	8
DROP TABLE t;
Test that we force use of a final temporary table in the last windowing step
if SQL_BUFFER_RESULT is set, even if we would otherwise optimize it away
CREATE TABLE t1 (t1_id INT) ;
INSERT INTO t1 (t1_id) VALUES (1);
INSERT INTO t1 (t1_id) VALUES (2);
INSERT INTO t1 (t1_id) VALUES (3);
INSERT INTO t1 (t1_id) VALUES (4);
INSERT INTO t1 (t1_id) VALUES (5);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
Still unset
SELECT t1_id, ROW_NUMBER() OVER () FROM t1;
t1_id	ROW_NUMBER() OVER ()
1	1
2	2
3	3
4	4
5	5
EXPLAIN FORMAT=JSON SELECT t1_id, ROW_NUMBER() OVER () FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "functions": [
            "row_number"
          ]
        }
      ],
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 5,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.50",
          "prefix_cost": "0.75",
          "data_read_per_join": "40"
        },
        "used_columns": [
          "t1_id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`t1_id` AS `t1_id`,row_number() OVER ()  AS `ROW_NUMBER() OVER ()` from `test`.`t1`
Now set
SET SQL_BUFFER_RESULT=TRUE;
FLUSH STATUS;
SELECT t1_id, ROW_NUMBER() OVER () FROM t1;
t1_id	ROW_NUMBER() OVER ()
1	1
2	2
3	3
4	4
5	5
SHOW STATUS LIKE 'Created_tmp_tables';
Variable_name	Value
Created_tmp_tables	1
EXPLAIN FORMAT=JSON SELECT t1_id, ROW_NUMBER() OVER () FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "using_temporary_table": true,
          "functions": [
            "row_number"
          ]
        }
      ],
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 5,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.50",
          "prefix_cost": "0.75",
          "data_read_per_join": "40"
        },
        "used_columns": [
          "t1_id"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sql_buffer_result `test`.`t1`.`t1_id` AS `t1_id`,row_number() OVER ()  AS `ROW_NUMBER() OVER ()` from `test`.`t1`
SET SQL_BUFFER_RESULT=FALSE;
DROP TABLE t1;
Optimization of last tmp file made this query fail before
(found by Guilhem during review)
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES(1),(1),(2),(2);
SELECT (SELECT COUNT(a) OVER (PARTITION BY a) FROM t1) FROM t1;
ERROR 21000: Subquery returns more than 1 row
DROP TABLE t1;
SELECT SUM(1) OVER ();
SUM(1) OVER ()
1
SELECT SUM(1.2) OVER ();
SUM(1.2) OVER ()
1.2
SELECT SUM(CAST(4 as JSON)) OVER ();
SUM(CAST(4 as JSON)) OVER ()
4
SELECT SUM('e') OVER ();
SUM('e') OVER ()
0
Warnings:
Warning	1292	Truncated incorrect DOUBLE value: 'e'
SELECT SUM(1)   OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SUM(1)   OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
1
SELECT SUM(1.2) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SUM(1.2) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
1.2
SELECT SUM(CAST(4 as JSON)) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SUM(CAST(4 as JSON)) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
4
SELECT SUM('e') OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SUM('e') OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
0
Warnings:
Warning	1292	Truncated incorrect DOUBLE value: 'e'
SELECT AVG(1) OVER ();
AVG(1) OVER ()
1.0000
SELECT AVG(1.2) OVER ();
AVG(1.2) OVER ()
1.20000
SELECT AVG(CAST(4 as JSON)) OVER ();
AVG(CAST(4 as JSON)) OVER ()
4
SELECT AVG('e') OVER ();
AVG('e') OVER ()
0
Warnings:
Warning	1292	Truncated incorrect DOUBLE value: 'e'
SELECT AVG(1)   OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
AVG(1)   OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
1.0000
SELECT AVG(1.2) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
AVG(1.2) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
1.20000
SELECT AVG(CAST(4 as JSON)) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
AVG(CAST(4 as JSON)) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
4
SELECT AVG('e') OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
AVG('e') OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
0
Warnings:
Warning	1292	Truncated incorrect DOUBLE value: 'e'
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (1),(2);
SELECT i FROM t1 WHERE i IN ( SELECT CAST(  SUM(i) OVER (ROWS CURRENT ROW) AS UNSIGNED) FROM t1);
i
1
2
SELECT i FROM t1 WHERE i IN ( SELECT CAST(0+SUM(i) OVER (ROWS CURRENT ROW) AS UNSIGNED) FROM t1);
i
1
2
SELECT FIRST_VALUE(i) IGNORE NULLS OVER () FROM t1;
ERROR 42000: This version of MySQL doesn't yet support 'IGNORE NULLS'
DROP TABLE t1;
Regression bug introduced by the first patch for Bug#25363694 for empty
result set in the presence of buffered windowing.
CREATE TABLE t1(i INT, j INT);
SELECT SUM(i) OVER w FROM t1
WINDOW w AS (PARTITION BY j ORDER BY i ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING);
SUM(i) OVER w
DROP TABLE t1;
CREATE TABLE t1 (pk INT, j INT, PRIMARY KEY (pk), KEY(j));
INSERT INTO t1 values (1,2);
SELECT LAST_VALUE(1) OVER (PARTITION BY t1.j), t2.pk FROM t1 LEFT JOIN
t1 as t2 ON t1.pk = t2.pk WHERE t1.pk=1;
LAST_VALUE(1) OVER (PARTITION BY t1.j)	pk
1	1
DROP TABLE t1;
CREATE TABLE t1 (a INTEGER);
INSERT INTO t1 VALUES (1),(2),(3),(4),(5);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT a, RANK() OVER (ORDER BY a) rank_asc,
RANK() OVER (ORDER BY a desc) rank_desc,
RANK() OVER (ORDER BY a) + RANK() OVER (ORDER BY a desc) rank_asc_desc FROM t1;
a	rank_asc	rank_desc	rank_asc_desc
5	5	1	6
4	4	2	6
3	3	3	6
2	2	4	6
1	1	5	6
EXPLAIN FORMAT=JSON SELECT a, RANK() OVER (ORDER BY a) rank_asc,
RANK() OVER (ORDER BY a desc) rank_desc,
RANK() OVER (ORDER BY a) + RANK() OVER (ORDER BY a desc) rank_asc_desc FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.75"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`a`"
          ],
          "functions": [
            "rank"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 3,
          "using_temporary_table": true,
          "functions": [
            "rank"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`a` desc"
          ],
          "functions": [
            "rank"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 4,
          "last_executed_window": true,
          "functions": [
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "10.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 5,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.50",
          "prefix_cost": "0.75",
          "data_read_per_join": "40"
        },
        "used_columns": [
          "a"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,rank() OVER (ORDER BY `test`.`t1`.`a` )  AS `rank_asc`,rank() OVER (ORDER BY `test`.`t1`.`a` desc )  AS `rank_desc`,(rank() OVER (ORDER BY `test`.`t1`.`a` )  + rank() OVER (ORDER BY `test`.`t1`.`a` desc ) ) AS `rank_asc_desc` from `test`.`t1`
DROP TABLE t1;
CREATE TABLE t(a INT);
INSERT INTO t VALUES(5);
SELECT ROW_NUMBER () OVER (), COUNT(*) FROM t WHERE a < 5;
ROW_NUMBER () OVER ()	COUNT(*)
1	0
DROP TABLE t;
CREATE TABLE t1(i INT, j INT, k INT);
INSERT INTO t1 VALUES (1,1,1),(2,2,2),(3,3,3),(4,4,4);
INSERT INTO t1 SELECT 10*i,j,5*j FROM t1 UNION SELECT 20*i,j,5*j FROM t1
UNION SELECT 30*i,j,5*j FROM t1;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT SUM(i) OVER W FROM t1 WINDOW w AS (PARTITION BY j ORDER BY i)
ORDER BY SUM(i) OVER w;
SUM(i) OVER W
1
2
3
4
11
22
31
33
44
61
62
93
122
124
183
244
SELECT SUM(i) OVER W FROM t1 WINDOW w AS (PARTITION BY j ORDER BY i)
ORDER BY 1+SUM(i) OVER w;
SUM(i) OVER W
1
2
3
4
11
22
31
33
44
61
62
93
122
124
183
244
SELECT SUM(SUM(i)) OVER W FROM t1 GROUP BY i WINDOW w AS (PARTITION BY i ORDER BY i)
ORDER BY SUM(SUM(i)) OVER w;
SUM(SUM(i)) OVER W
1
2
3
4
10
40
60
80
80
90
120
120
SELECT 1+SUM(SUM(i)) OVER W FROM t1 GROUP BY i WINDOW w AS (PARTITION BY i ORDER BY i)
ORDER BY 1+SUM(SUM(i)) OVER w;
1+SUM(SUM(i)) OVER W
2
3
4
5
11
41
61
81
81
91
121
121
SELECT 1+SUM(i) OVER W FROM t1 WINDOW w AS (PARTITION BY j ORDER BY i)
ORDER BY SUM(i) OVER w;
1+SUM(i) OVER W
2
3
4
5
12
23
32
34
45
62
63
94
123
125
184
245
SELECT SUM(2+SUM(i)) OVER W FROM t1 GROUP BY j WINDOW w AS (PARTITION BY j ORDER BY j)
ORDER BY SUM(2+SUM(i)) OVER w DESC;
SUM(2+SUM(i)) OVER W
246
185
124
63
DROP TABLE t1;
CREATE TABLE t(a INT);
INSERT INTO t VALUES (1),(2),(3);
SELECT ROW_NUMBER() OVER () AS num FROM t HAVING (num = '2');
ERROR HY000: You cannot use the alias 'num' of an expression containing a window function in this context.'
SELECT ROW_NUMBER() OVER () FROM t HAVING ( ROW_NUMBER() OVER () = '2');
ERROR HY000: You cannot use the window function 'row_number' in this context.'
DROP TABLE t;
Error due to missing fix_items of window's ORDER BY when
eliminating redundant sorts
CREATE TABLE t1 (a INT, b INT);
INSERT INTO t1 VALUES (1,1),(2,2),(3,3),
(3,1),(2,2),(1,3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
This one was ok
SELECT a,b, RANK() OVER (ORDER BY a), RANK() OVER (order BY a) FROM t1;
a	b	RANK() OVER (ORDER BY a)	RANK() OVER (order BY a)
1	1	1	1
1	3	1	1
2	2	3	3
2	2	3	3
3	3	5	5
3	1	5	5
EXPLAIN FORMAT=JSON SELECT a,b, RANK() OVER (ORDER BY a), RANK() OVER (ORDER BY a) FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`a`"
          ],
          "functions": [
            "rank"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "functions": [
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "6.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "96"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,rank() OVER (ORDER BY `test`.`t1`.`a` )  AS `RANK() OVER (ORDER BY a)`,rank() OVER (ORDER BY `test`.`t1`.`a` )  AS `RANK() OVER (ORDER BY a)` from `test`.`t1`
But here the ORDER BY b was erroneously removed.
FLUSH STATUS;
SELECT a,b, RANK() OVER (ORDER BY a), RANK() OVER (ORDER BY b) FROM t1;
a	b	RANK() OVER (ORDER BY a)	RANK() OVER (ORDER BY b)
1	1	1	1
3	1	5	1
2	2	3	3
2	2	3	3
1	3	1	5
3	3	5	5
SHOW STATUS LIKE 'Created_tmp_tables';
Variable_name	Value
Created_tmp_tables	1
EXPLAIN FORMAT=JSON SELECT a,b, RANK() OVER (ORDER BY a), RANK() OVER (ORDER BY b) FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "12.85"
    },
    "windowing": {
      "windows": [
        {
          "name": "<unnamed window>",
          "definition_position": 1,
          "using_temporary_table": true,
          "using_filesort": true,
          "filesort_key": [
            "`a`"
          ],
          "functions": [
            "rank"
          ]
        },
        {
          "name": "<unnamed window>",
          "definition_position": 2,
          "last_executed_window": true,
          "using_filesort": true,
          "filesort_key": [
            "`b`"
          ],
          "functions": [
            "rank"
          ]
        }
      ],
      "cost_info": {
        "sort_cost": "12.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 6,
        "rows_produced_per_join": 6,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.60",
          "prefix_cost": "0.85",
          "data_read_per_join": "96"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,rank() OVER (ORDER BY `test`.`t1`.`a` )  AS `RANK() OVER (ORDER BY a)`,rank() OVER (ORDER BY `test`.`t1`.`b` )  AS `RANK() OVER (ORDER BY b)` from `test`.`t1`
DROP TABLE t1;

Test of SOME/ANY/ALL subqueries.

CREATE TABLE t(a INT);
INSERT INTO t VALUES (1),(2),(3);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
SELECT * FROM t WHERE a IN (SELECT ROW_NUMBER() OVER () FROM t);
a
1
2
3
SELECT * FROM t WHERE a IN (SELECT ROW_NUMBER() OVER () + 1 FROM t);
a
2
3
EXPLAIN FORMAT=JSON SELECT * FROM t WHERE a IN (SELECT ROW_NUMBER() OVER () FROM t);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.55"
    },
    "table": {
      "table_name": "t",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 3,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.30",
        "prefix_cost": "0.55",
        "data_read_per_join": "24"
      },
      "used_columns": [
        "a"
      ],
      "attached_condition": "<in_optimizer>(`test`.`t`.`a`,`test`.`t`.`a` in ( <materialize> (/* select#2 */ select row_number() OVER ()  from `test`.`t` having true ), <primary_index_lookup>(`test`.`t`.`a` in <temporary table> on <auto_distinct_key> where ((`test`.`t`.`a` = `<materialized_subquery>`.`ROW_NUMBER() OVER ()`)))))",
      "attached_subqueries": [
        {
          "table": {
            "table_name": "<materialized_subquery>",
            "access_type": "eq_ref",
            "key": "<auto_key>",
            "key_length": "8",
            "rows_examined_per_scan": 1,
            "materialized_from_subquery": {
              "using_temporary_table": true,
              "dependent": true,
              "cacheable": false,
              "query_block": {
                "select_id": 2,
                "cost_info": {
                  "query_cost": "0.55"
                },
                "windowing": {
                  "windows": [
                    {
                      "name": "<unnamed window>",
                      "functions": [
                        "row_number"
                      ]
                    }
                  ],
                  "table": {
                    "table_name": "t",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 3,
                    "rows_produced_per_join": 3,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "0.25",
                      "eval_cost": "0.30",
                      "prefix_cost": "0.55",
                      "data_read_per_join": "24"
                    }
                  }
                }
              }
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`a` AS `a` from `test`.`t` where <in_optimizer>(`test`.`t`.`a`,`test`.`t`.`a` in ( <materialize> (/* select#2 */ select row_number() OVER ()  from `test`.`t` having true ), <primary_index_lookup>(`test`.`t`.`a` in <temporary table> on <auto_distinct_key> where ((`test`.`t`.`a` = `<materialized_subquery>`.`ROW_NUMBER() OVER ()`)))))
SELECT * FROM t WHERE a = SOME(SELECT ROW_NUMBER() OVER () + 1 FROM t);
a
2
3
SELECT * FROM t WHERE a = ANY(SELECT ROW_NUMBER() OVER () + 1 FROM t);
a
2
3
SELECT * FROM t WHERE a <> ALL(SELECT ROW_NUMBER() OVER () + 1 FROM t);
a
1
SELECT * FROM t WHERE a >= ALL(SELECT ROW_NUMBER() OVER () + 1 FROM t);
a
SELECT * FROM t WHERE a >= SOME(SELECT ROW_NUMBER() OVER () + 1 FROM t);
a
2
3
SELECT * from t WHERE EXISTS(SELECT ROW_NUMBER() OVER () FROM t);
a
1
2
3
EXPLAIN FORMAT=JSON SELECT * from t WHERE EXISTS(SELECT ROW_NUMBER() OVER () FROM t);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.55"
    },
    "table": {
      "table_name": "t",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 3,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.30",
        "prefix_cost": "0.55",
        "data_read_per_join": "24"
      },
      "used_columns": [
        "a"
      ]
    },
    "optimized_away_subqueries": [
      {
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "0.55"
          },
          "windowing": {
            "windows": [
              {
                "name": "<unnamed window>",
                "functions": [
                  "row_number"
                ]
              }
            ],
            "table": {
              "table_name": "t",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.30",
                "prefix_cost": "0.55",
                "data_read_per_join": "24"
              }
            }
          }
        }
      }
    ]
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`a` AS `a` from `test`.`t` where true
FLUSH STATUS;
SELECT * FROM t upper
WHERE EXISTS(SELECT rn FROM (SELECT ROW_NUMBER() OVER () AS rn FROM t) ta
WHERE rn > upper.a);
a
1
2
SHOW STATUS LIKE 'Created_tmp_tables';
Variable_name	Value
Created_tmp_tables	1
EXPLAIN FORMAT=JSON SELECT * FROM t upper
WHERE EXISTS(SELECT rn FROM (SELECT ROW_NUMBER() OVER () AS rn FROM t) ta
WHERE rn > upper.a);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.99"
    },
    "nested_loop": [
      {
        "table": {
          "table_name": "upper",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.30",
            "prefix_cost": "0.55",
            "data_read_per_join": "24"
          },
          "used_columns": [
            "a"
          ]
        }
      },
      {
        "table": {
          "table_name": "ta",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "33.33",
          "first_match": "upper",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "2.54",
            "eval_cost": "0.30",
            "prefix_cost": "3.99",
            "data_read_per_join": "48"
          },
          "used_columns": [
            "rn"
          ],
          "attached_condition": "(`ta`.`rn` > `test`.`upper`.`a`)",
          "materialized_from_subquery": {
            "using_temporary_table": true,
            "dependent": false,
            "cacheable": true,
            "query_block": {
              "select_id": 3,
              "cost_info": {
                "query_cost": "0.55"
              },
              "windowing": {
                "windows": [
                  {
                    "name": "<unnamed window>",
                    "functions": [
                      "row_number"
                    ]
                  }
                ],
                "table": {
                  "table_name": "t",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 3,
                  "rows_produced_per_join": 3,
                  "filtered": "100.00",
                  "cost_info": {
                    "read_cost": "0.25",
                    "eval_cost": "0.30",
                    "prefix_cost": "0.55",
                    "data_read_per_join": "24"
                  }
                }
              }
            }
          }
        }
      }
    ]
  }
}
Warnings:
Note	1276	Field or reference 'test.upper.a' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`upper`.`a` AS `a` from `test`.`t` `upper` semi join ((/* select#3 */ select row_number() OVER ()  AS `rn` from `test`.`t`) `ta`) where (`ta`.`rn` > `test`.`upper`.`a`)
DROP TABLE t;
#
# Check window aggregate referencing columns from an outside query
#
CREATE TABLE t1(i INT, j INT, k INT);
INSERT INTO t1 VALUES (1,1,1),(2,2,2);
SELECT (SELECT ROW_NUMBER() OVER (ORDER BY upper.j) FROM t1 LIMIT 1)
FROM t1 AS upper;
ERROR HY000: Outer reference test.upper.j in window's ORDER BY or PARTITION BY clause not allowed.
SELECT (SELECT ROW_NUMBER() OVER (PARTITION BY upper.j) FROM t1 LIMIT 1)
FROM t1 AS upper;
ERROR HY000: Outer reference test.upper.j in window's ORDER BY or PARTITION BY clause not allowed.
SELECT (SELECT FIRST_VALUE(upper.j) OVER (ORDER BY t1.j DESC)
FROM t1 LIMIT 1)
FROM t1 AS upper;
(SELECT FIRST_VALUE(upper.j) OVER (ORDER BY t1.j DESC)
FROM t1 LIMIT 1)
1
2
SELECT * FROM t1 AS upper
WHERE (SELECT FIRST_VALUE(upper.j) OVER (ORDER BY t1.i)
FROM t1 LIMIT 1) = 1
;
i	j	k
1	1	1
SELECT * FROM t1 AS upper
WHERE (SELECT FIRST_VALUE(upper.j) OVER (ORDER BY t1.i)
FROM t1 LIMIT 1) = 2;
i	j	k
2	2	2
SELECT FIRST_VALUE(j) OVER (ORDER BY 0 +
(SELECT FIRST_VALUE(upper.j) OVER (ORDER BY t1.i)
FROM t1 LIMIT 1))
FROM t1 AS upper;
FIRST_VALUE(j) OVER (ORDER BY 0 +
(SELECT FIRST_VALUE(upper.j) OVER (ORDER BY t1.i)
FROM t1 LIMIT 1))
1
1
SELECT LAST_VALUE(j)  OVER (ORDER BY 0 +
(SELECT FIRST_VALUE(upper.j) OVER (ORDER BY t1.i)
FROM t1 LIMIT 1))
FROM t1 AS upper;
LAST_VALUE(j)  OVER (ORDER BY 0 +
(SELECT FIRST_VALUE(upper.j) OVER (ORDER BY t1.i)
FROM t1 LIMIT 1))
1
2
SELECT * FROM t1 AS upper ORDER BY (0 + (SELECT FIRST_VALUE(upper.j) OVER (ORDER BY t1.i)
FROM t1 LIMIT 1));
i	j	k
1	1	1
2	2	2
SELECT LAST_VALUE((SELECT upper.j FROM t1 LIMIT 1)) OVER (ORDER BY i)
FROM t1 AS upper;
LAST_VALUE((SELECT upper.j FROM t1 LIMIT 1)) OVER (ORDER BY i)
1
2
SELECT FIRST_VALUE((SELECT upper.j FROM t1 LIMIT 1)) OVER (ORDER BY i)
FROM t1 AS upper;
FIRST_VALUE((SELECT upper.j FROM t1 LIMIT 1)) OVER (ORDER BY i)
1
1
SELECT SUM(j + (SELECT upper.j FROM t1 LIMIT 1)) OVER (ORDER BY i)
FROM t1 AS upper;
SUM(j + (SELECT upper.j FROM t1 LIMIT 1)) OVER (ORDER BY i)
2
6
SELECT LAST_VALUE(1 IN (SELECT upper.j FROM t1)) OVER (ORDER BY i)
FROM t1 AS upper;
LAST_VALUE(1 IN (SELECT upper.j FROM t1)) OVER (ORDER BY i)
1
0
SELECT LAST_VALUE(upper.j IN (SELECT 2 FROM t1)) OVER (ORDER BY i)
FROM t1 AS upper;
LAST_VALUE(upper.j IN (SELECT 2 FROM t1)) OVER (ORDER BY i)
0
1
DROP TABLE t1;

Example of usefulness of expression in partition clause

CREATE TABLE t(i INT, c VARCHAR(20));
INSERT INTO t VALUES (1, 'abra'),(2, 'akaba'),(3, 'bravo'),(4, 'beg');
SELECT i, SUBSTR(c,1,2), SUM(i) OVER (PARTITION BY SUBSTR(c,1,2)) `sum` FROM t;
i	SUBSTR(c,1,2)	sum
1	ab	1
2	ak	2
4	be	4
3	br	3
select I, substr(c,1,2), SUM(i) OVER (PARTITION BY SUBSTR(c,1,1)) `sum` FROM t;
I	substr(c,1,2)	sum
1	ab	3
2	ak	3
3	br	7
4	be	7
DROP TABLE t;

Example of usefulness of grouped aggregate in partition clause

CREATE TABLE t1( i INT, j INT);
INSERT INTO t1 VALUES (1,1),
(2,1),
(3,2),
(4,2),
(1,3),
(2,3),
(3,4),
(4,4);
SELECT j AS Having_same_sum_of_i, SUM(i), SUM(SUM(i)) OVER (PARTITION BY SUM(i)) AS sum_sum
FROM t1 GROUP BY j;
Having_same_sum_of_i	SUM(i)	sum_sum
1	3	6
3	3	6
2	7	14
4	7	14
DROP TABLE t1;
CREATE TABLE t1(a INT, b INT);
CREATE TABLE t2(c INT, d INT);
INSERT INTO t1 VALUES(1,1),(2,2);
SELECT ROW_NUMBER() OVER (), c
FROM t1 LEFT JOIN t2 ON a = c GROUP BY c;
ROW_NUMBER() OVER ()	c
1	NULL
SELECT ROW_NUMBER() OVER (), 'c'
FROM t1 LEFT JOIN t2 ON a = c GROUP BY 'c';
ROW_NUMBER() OVER ()	c
1	c
DROP TABLE t1,t2;

Bug#25461670 CRASHES OR WRONG RESULTS WITH WINDOW FUNCTIONS IN SUBQUERY

CREATE TABLE t(a INT, b INT);
INSERT INTO t VALUES (5,6), (1,7);
SELECT (SELECT SUM(a) OVER ())  FROM t;
(SELECT SUM(a) OVER ())
5
1
SELECT (SELECT SUM(a) OVER () FROM t LIMIT 1)  FROM t;
(SELECT SUM(a) OVER () FROM t LIMIT 1)
6
6
CREATE TABLE t2(i INT);
INSERT INTO t2 VALUES (10),(100);
SELECT (SELECT SUM(a) OVER () FROM t2 LIMIT 1)  FROM t;
(SELECT SUM(a) OVER () FROM t2 LIMIT 1)
10
2
DROP TABLE t, t2;
CREATE TABLE t(a INT, b INT);
INSERT INTO t VALUES (1,2), (4,5);
Check that a grouped aggregate argument doesn't directly contain a window
function.
SELECT AVG(SUM(a) OVER ()) FROM t;
ERROR HY000: You cannot use the window function 'sum' in this context.'
Check that a grouped aggregate argument doesn't directly contain a window
function by alias.
SELECT SUM(a) OVER () AS c, (SELECT SUM(c))  FROM t;
ERROR HY000: You cannot use the alias 'c' of an expression containing a window function in this context.'
At same query level, alias isn't even visible; the visibility
of alias in subquery is a MySQL extension
SELECT SUM(a) OVER () AS c, SUM(c)  FROM t;
ERROR 42S22: Unknown column 'c' in 'field list'
but ok in final ORDER BY
SELECT SUM(a) OVER () AS c FROM t ORDER BY c;
c
5
5
One more nesting level makes the wf legal inside a grouped aggregate
argument.
SELECT AVG(a+(SELECT SUM(a) OVER () FROM t LIMIT 1)) FROM t;
AVG(a+(SELECT SUM(a) OVER () FROM t LIMIT 1))
7.5000
But not if it's a window function alias from an outer level
SELECT SUM(a) OVER () AS c, (SELECT SUM(1 + (SELECT c FROM DUAL)))  FROM t;
ERROR HY000: You cannot use the alias 'c' of an expression containing a window function in this context.'
SELECT 1+SUM(a) OVER () AS c, (SELECT SUM(1 + (SELECT c FROM DUAL)))  FROM t;
ERROR HY000: You cannot use the alias 'c' of an expression containing a window function in this context.'
This is illegal even if not referenced from a grouped aggregate since
logically all windowing happens later.
SELECT SUM(a) OVER () AS c, (SELECT 1 + (SELECT c FROM DUAL))  FROM t;
ERROR HY000: You cannot use the alias 'c' of an expression containing a window function in this context.'
SELECT 1+SUM(a) OVER () AS c, (SELECT 1 + (SELECT c FROM DUAL))  FROM t;
ERROR HY000: You cannot use the alias 'c' of an expression containing a window function in this context.'
DROP TABLE t;
CREATE TABLE t(a INT, b INT, c INT);
INSERT INTO t VALUES (1,1,1),(1,2,3),(2,1,2),(2,2,3);
SELECT RANK() OVER (PARTITION BY c ORDER BY c) FROM t GROUP BY a,b;
ERROR 42000: Expression #1 of PARTITION BY or ORDER BY clause of window '<unnamed window>' is not in GROUP BY clause and contains nonaggregated column 'test.t.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT RANK() OVER (PARTITION BY c ORDER BY c) FROM t;
RANK() OVER (PARTITION BY c ORDER BY c)
1
1
1
1
SELECT RANK() OVER (PARTITION BY a ORDER BY b) FROM t GROUP BY a,b;
RANK() OVER (PARTITION BY a ORDER BY b)
1
2
1
2
SELECT RANK() OVER (PARTITION BY a ORDER BY b) FROM t;
RANK() OVER (PARTITION BY a ORDER BY b)
1
2
1
2
SELECT RANK() OVER (PARTITION BY (a+b) ORDER BY (b+a)) FROM t GROUP BY a,b;
RANK() OVER (PARTITION BY (a+b) ORDER BY (b+a))
1
1
1
1
SELECT AVG(a), RANK() OVER (ORDER BY a) FROM t;
ERROR 42000: In aggregated query without GROUP BY, expression #1 of PARTITION BY or ORDER BY clause of window '<unnamed window>' contains nonaggregated column 'test.t.a'; this is incompatible with sql_mode=only_full_group_by
SELECT AVG(a), SUM(AVG(a)) OVER (PARTITION BY a) FROM t;
ERROR 42000: In aggregated query without GROUP BY, expression #1 of PARTITION BY or ORDER BY clause of window '<unnamed window>' contains nonaggregated column 'test.t.a'; this is incompatible with sql_mode=only_full_group_by
SELECT AVG(a), SUM(a) OVER () FROM t;
ERROR 42000: In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'test.t.a'; this is incompatible with sql_mode=only_full_group_by
DROP TABLE t;

Bug#25643783 EXPRESSION INVOLVING WF WITH GROUPED AGGREGATE ARG GIVES WRONG VALUE

CREATE TABLE t1(i INT, j INT, k INT);
INSERT INTO t1 VALUES (1,1,1),(2,2,2),(3,3,3),(4,4,4);
SELECT i, SUM(i), 1+SUM(i), SUM(SUM(i)) OVER w, 1+SUM(SUM(i)) OVER w FROM t1
GROUP BY i WINDOW w AS (PARTITION BY i);
i	SUM(i)	1+SUM(i)	SUM(SUM(i)) OVER w	1+SUM(SUM(i)) OVER w
1	1	2	1	2
2	2	3	2	3
3	3	4	3	4
4	4	5	4	5
DROP TABLE t1;

Bug with missing update of cached example after split_sum_func
for FIRST_VALUE, LAST_VALUE

CREATE TABLE t(a INT, b INT, c INT, d INT);
INSERT INTO t VALUES (1,1,1,1), (2,2,4,2), (3,3,9,3);
SELECT SUM(c/d), LAST_VALUE(SUM(c/d)) OVER (ORDER BY a)  FROM t GROUP BY a,b;
SUM(c/d)	LAST_VALUE(SUM(c/d)) OVER (ORDER BY a)
1.0000	1.0000
2.0000	2.0000
3.0000	3.0000
SELECT LAST_VALUE(SUM(c/d)) OVER (ORDER BY a)  FROM t GROUP BY a,b;
LAST_VALUE(SUM(c/d)) OVER (ORDER BY a)
1.0000
2.0000
3.0000
SELECT 1+FIRST_VALUE(SUM(c/d)) OVER (ORDER BY a)  FROM t GROUP BY a,b;
1+FIRST_VALUE(SUM(c/d)) OVER (ORDER BY a)
2.0000
2.0000
2.0000
SELECT ROW_NUMBER() OVER () rn,
1+FIRST_VALUE(SUM(c/d)) OVER (ORDER BY a) plus_fv,
1+LAST_VALUE(SUM(c/d)) OVER (ORDER BY a) plus_lv FROM t GROUP BY a,b;
rn	plus_fv	plus_lv
1	2.0000	2.0000
2	2.0000	3.0000
3	2.0000	4.0000
DROP TABLE t;

Bug 25724779

CREATE TABLE t1(a INT, b INT);
INSERT INTO t1 VALUES (1,2),(3,4);
SELECT a, SUM(b) FROM t1 GROUP BY a;
a	SUM(b)
1	2
3	4
SELECT a, SUM(b) FROM t1 GROUP BY a HAVING a=1;
a	SUM(b)
1	2
SELECT a, SUM(SUM(b)) OVER () FROM t1 GROUP BY a HAVING a=1;
a	SUM(SUM(b)) OVER ()
1	2
DROP TABLE t1;

Bug#25365929 PROBLEM TRYING TO ADD GCOL USING A
WINDOW FUNCTION
CREATE TABLE t (a INT , b INT as (ROW_NUMBER() OVER (ORDER BY a)));
ERROR HY000: You cannot use the window function 'row_number' in this context.'
CREATE TABLE t (a INT DEFAULT NULL);
INSERT INTO t VALUES (1),(2),(3),(4),(5);
ALTER TABLE t ADD b INT AS (ROW_NUMBER() OVER (ORDER BY a));
ERROR HY000: You cannot use the window function 'row_number' in this context.'
DROP TABLE t;
CREATE TABLE t (a INT DEFAULT NULL);
ALTER TABLE t ADD COLUMN b INT as (ROW_NUMBER() OVER (ORDER BY a));
ERROR HY000: You cannot use the window function 'row_number' in this context.'
DROP TABLE t;

Bug exposed by PERCENT_RANK() and CUME_DIST() in non-optimized mode
(default when we add a DOUBLE SUM to the mix)

CREATE TABLE t1 (id INTEGER, sex CHAR(1), d DOUBLE);
INSERT INTO t1 VALUES (1, 'M', 1.0);
INSERT INTO t1 VALUES (2, 'F', 2.0);
INSERT INTO t1 VALUES (3, 'F', 3.0);
INSERT INTO t1 VALUES (4, 'F', 4.0);
INSERT INTO t1 VALUES (5, 'M', 5.0);
INSERT INTO t1 VALUES (10, NULL, 10.0);
INSERT INTO t1 VALUES (11, NULL, 11.0);
SELECT sex, id, PERCENT_RANK() OVER w, CUME_DIST() OVER w, SUM(d) OVER w
FROM t1 WINDOW w AS (PARTITION BY sex ORDER BY id DESC ROWS
BETWEEN 1 PRECEDING AND CURRENT ROW);
sex	id	PERCENT_RANK() OVER w	CUME_DIST() OVER w	SUM(d) OVER w
NULL	11	0	0.5	11
NULL	10	1	1	21
F	4	0	0.3333333333333333	4
F	3	0.5	0.6666666666666666	7
F	2	1	1	5
M	5	0	0.5	5
M	1	1	1	6
DROP TABLE t1;

Bug#25478832

SET @savmode=@@SESSION.SQL_MODE;
SET SESSION SQL_MODE='TRADITIONAL';
CREATE TABLE t(a int);
INSERT INTO t VALUES (1),(2);
SELECT COUNT(*), ROW_NUMBER() OVER (ORDER BY a) AS rownum
FROM t ORDER BY rownum;
COUNT(*)	rownum
2	1
SET SESSION SQL_MODE=@savmode;
DROP TABLE t;
follow-up bug repro
CREATE TABLE t1 (id INTEGER, sex CHAR(1));
PREPARE p from 'SELECT sex, PERCENT_RANK() OVER (ORDER BY AVG(id) DESC)
                FROM t1 GROUP BY sex ORDER BY sex';
EXECUTE p;
sex	PERCENT_RANK() OVER (ORDER BY AVG(id) DESC)
DROP TABLE t1;

Bug#25819164

CREATE TABLE t1 (a INT PRIMARY KEY);
CREATE TABLE t2 LIKE t1;
INSERT INTO t1 VALUES(1);
ANALYZE TABLE t1,t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
SELECT ROW_NUMBER() OVER ()
FROM t1 AS alias1, t2 AS alias2
WHERE alias1.a = 1
ORDER BY alias2.a;
ROW_NUMBER() OVER ()
query's ORDER BY with index order without filesort
EXPLAIN SELECT ROW_NUMBER() OVER ()
FROM t1 AS alias1, t2 AS alias2
WHERE alias1.a = 1
ORDER BY alias2.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	alias1	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	Using index
1	SIMPLE	alias2	NULL	index	NULL	PRIMARY	4	NULL	1	100.00	Using index
Warnings:
Note	3598	To get information about window functions use EXPLAIN FORMAT=JSON
Note	1003	/* select#1 */ select row_number() OVER ()  AS `ROW_NUMBER() OVER ()` from `test`.`t1` `alias1` join `test`.`t2` `alias2` where true order by `test`.`alias2`.`a`
Add more rows
INSERT INTO t1 VALUES (2);
INSERT INTO t2 VALUES (1),(2);
ANALYZE TABLE t1,t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
SELECT alias1.a, alias2.a as ord_key_asc,
ROW_NUMBER() OVER ()
FROM t1 AS alias1, t2 AS alias2
WHERE alias1.a = 1
ORDER BY alias2.a;
a	ord_key_asc	ROW_NUMBER() OVER ()
1	1	1
1	2	2
query's ORDER BY with index order without filesort
EXPLAIN SELECT alias1.a, alias2.a as ord_key_asc,
ROW_NUMBER() OVER ()
FROM t1 AS alias1, t2 AS alias2
WHERE alias1.a = 1
ORDER BY alias2.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	alias1	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	Using index
1	SIMPLE	alias2	NULL	index	NULL	PRIMARY	4	NULL	2	100.00	Using index
Warnings:
Note	3598	To get information about window functions use EXPLAIN FORMAT=JSON
Note	1003	/* select#1 */ select '1' AS `a`,`test`.`alias2`.`a` AS `ord_key_asc`,row_number() OVER ()  AS `ROW_NUMBER() OVER ()` from `test`.`t1` `alias1` join `test`.`t2` `alias2` where true order by `test`.`alias2`.`a`
SELECT alias1.a, alias2.a as ord_key_asc,
ROW_NUMBER() OVER (ORDER BY -alias2.a)
FROM t1 AS alias1, t2 AS alias2
WHERE alias1.a = 1
ORDER BY alias2.a;
a	ord_key_asc	ROW_NUMBER() OVER (ORDER BY -alias2.a)
1	1	2
1	2	1
query's ORDER BY with filesort as WF has ORDER BY which shuffles rows
EXPLAIN SELECT alias1.a, alias2.a as ord_key_asc,
ROW_NUMBER() OVER (ORDER BY -alias2.a)
FROM t1 AS alias1, t2 AS alias2
WHERE alias1.a = 1
ORDER BY alias2.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	alias1	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	Using index; Using filesort
1	SIMPLE	alias2	NULL	index	NULL	PRIMARY	4	NULL	2	100.00	Using index
Warnings:
Note	3598	To get information about window functions use EXPLAIN FORMAT=JSON
Note	1003	/* select#1 */ select '1' AS `a`,`test`.`alias2`.`a` AS `ord_key_asc`,row_number() OVER (ORDER BY -(`test`.`alias2`.`a`) )  AS `ROW_NUMBER() OVER (ORDER BY -alias2.a)` from `test`.`t1` `alias1` join `test`.`t2` `alias2` where true order by `test`.`alias2`.`a`
DROP TABLE t1, t2;

Bug#25472253

CREATE TABLE t (
col_date date
);
INSERT INTO t VALUES ('2017-01-10'), ('2017-01-18');
SELECT
MIN(alias1.col_date) OVER () FV,
DENSE_RANK() OVER (ORDER BY alias2.col_date
RANGE UNBOUNDED PRECEDING) DR,
RANK() OVER (ORDER BY alias1.col_date
RANGE BETWEEN INTERVAL 1 WEEK PRECEDING AND CURRENT ROW) R
FROM (t AS alias1, t AS alias2);
FV	DR	R
2017-01-10	1	1
2017-01-10	1	3
2017-01-10	2	1
2017-01-10	2	3
DROP TABLE t;

Bug#25819199

CREATE TABLE t (a INT PRIMARY KEY, b INT);
INSERT INTO t VALUES(1, 1);
SELECT NTILE (3) OVER (ORDER BY alias1.a), AVG(alias1.a)  OVER ()
FROM t AS alias1 RIGHT JOIN t AS alias2 ON (alias1.a = alias2.b);
NTILE (3) OVER (ORDER BY alias1.a)	AVG(alias1.a)  OVER ()
1	1.0000
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES(1),(1),(2),(2);
CREATE TABLE t2(a INT NOT NULL);
INSERT INTO t2 VALUES(3);
SELECT t1.a, t2.a FROM t1 LEFT JOIN t2 ON 0;
a	a
1	NULL
1	NULL
2	NULL
2	NULL
SELECT t1.a, FIRST_VALUE(t2.a) OVER () FROM t1 LEFT JOIN t2 ON 0;
a	FIRST_VALUE(t2.a) OVER ()
1	NULL
1	NULL
2	NULL
2	NULL
DROP TABLE t, t1, t2;

Bug#25551456

CREATE TABLE t1 (id INT);
INSERT INTO t1 VALUES (1), (2), (3), (2);
SELECT SUM(MAX(id)) OVER (ORDER BY MAX(id)) FROM t1;
SUM(MAX(id)) OVER (ORDER BY MAX(id))
3
CREATE INDEX idx ON t1(id);
Used to be NULL rather than 3:
SELECT SUM(MAX(id)) OVER (ORDER BY MAX(id)) AS ss FROM t1;
ss
3
DROP TABLE t1;

Bug#25835846

CREATE TABLE t(a INT PRIMARY KEY);
INSERT INTO t VALUES (1);
SELECT NTILE(2) OVER (ORDER BY a) FROM t WHERE a = 1;
NTILE(2) OVER (ORDER BY a)
1
SELECT CUME_DIST() OVER (ORDER BY a) FROM t WHERE a = 1;
CUME_DIST() OVER (ORDER BY a)
1
DROP TABLE t;

Bug#25835149

SET @savmode=@@SESSION.SQL_MODE;
SET SESSION SQL_MODE='';
CREATE TABLE `test`(
`pk` INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
`dt` DATETIME DEFAULT NULL,
`ge` GEOMETRY DEFAULT NULL
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `test` VALUES
(1, '2007-05-08 12:10:55', ST_GeomFromText('POINT(1 1)')),
(2, NULL, NULL), (3, NULL, NULL),
(4, '2001-01-18 00:00:00', ST_GeomFromText('POINT(4 4)')),
(5, '2009-11-24 00:00:00', ST_GeomFromText('POINT(5 5)')),
(6, '2001-11-22 21:41:15', ST_GeomFromText('POINT(6 6)')),
(7, NULL, NULL),
(8, '0000-00-00 00:00:00', ST_GeomFromText('POINT(8 8)')),
(9, '2003-05-13 18:03:04', ST_GeomFromText('POINT(9 9)')),
(10, '2008-04-15 09:44:20', ST_GeomFromText('POINT(10 10)')),
(11, '2009-07-15 00:00:00', ST_GeomFromText('POINT(11 11)')),
(12, '2007-04-27 13:53:37', ST_GeomFromText('POINT(12 12)')),
(13, '0000-00-00 00:00:00', ST_GeomFromText('POINT(13 13)')),
(14, '2000-02-02 02:15:28', ST_GeomFromText('POINT(14 14)')),
(15, '2004-06-06 00:00:00', ST_GeomFromText('POINT(15 15)')),
(16, NULL, NULL),
(17, '2002-06-21 00:00:00', ST_GeomFromText('POINT(17 17)')),
(18, '2007-03-23 00:00:00', ST_GeomFromText('POINT(18 18)')),
(19, '2006-10-06 00:00:00', ST_GeomFromText('POINT(19 19)')),
(20, '2008-07-07 00:00:00', ST_GeomFromText('POINT(20 20)'));
SELECT dt, FIRST_VALUE(dt) OVER w1 fv,
CAST(FIRST_VALUE(ge) OVER w1 AS JSON) ge FROM test WHERE `pk` = 2 WINDOW w1 AS ();
dt	fv	ge
NULL	NULL	NULL
SELECT dt, FIRST_VALUE(dt) OVER w1,
CAST(FIRST_VALUE(ge) OVER w1 AS JSON) ge FROM test WHERE `pk` > 3 WINDOW w1 AS ();
dt	FIRST_VALUE(dt) OVER w1	ge
2001-01-18 00:00:00	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
2009-11-24 00:00:00	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
2001-11-22 21:41:15	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
NULL	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
0000-00-00 00:00:00	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
2003-05-13 18:03:04	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
2008-04-15 09:44:20	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
2009-07-15 00:00:00	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
2007-04-27 13:53:37	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
0000-00-00 00:00:00	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
2000-02-02 02:15:28	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
2004-06-06 00:00:00	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
NULL	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
2002-06-21 00:00:00	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
2007-03-23 00:00:00	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
2006-10-06 00:00:00	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
2008-07-07 00:00:00	2001-01-18 00:00:00	{"type": "Point", "coordinates": [4.0, 4.0]}
SELECT dt, FIRST_VALUE(CAST(dt AS TIME)) OVER w1 FROM test WHERE `pk` > 3 WINDOW w1 AS ();
dt	FIRST_VALUE(CAST(dt AS TIME)) OVER w1
2001-01-18 00:00:00	00:00:00
2009-11-24 00:00:00	00:00:00
2001-11-22 21:41:15	00:00:00
NULL	00:00:00
0000-00-00 00:00:00	00:00:00
2003-05-13 18:03:04	00:00:00
2008-04-15 09:44:20	00:00:00
2009-07-15 00:00:00	00:00:00
2007-04-27 13:53:37	00:00:00
0000-00-00 00:00:00	00:00:00
2000-02-02 02:15:28	00:00:00
2004-06-06 00:00:00	00:00:00
NULL	00:00:00
2002-06-21 00:00:00	00:00:00
2007-03-23 00:00:00	00:00:00
2006-10-06 00:00:00	00:00:00
2008-07-07 00:00:00	00:00:00
SET SESSION SQL_MODE=@savmode;
DROP TABLE `test`;

Bug#25835329

CREATE TABLE t(a int PRIMARY KEY, b date);
INSERT INTO t VALUES (1, '1979-01-01');
SELECT STRAIGHT_JOIN CUME_DIST() OVER (ORDER BY alias1.a)
FROM t AS alias1 RIGHT JOIN t AS alias2 ON alias1.a = alias2.a
WHERE  alias1.a = 1 ;
CUME_DIST() OVER (ORDER BY alias1.a)
1
INSERT INTO t VALUES (2, '1979-01-02'), (3, '1979-01-03');
SELECT STRAIGHT_JOIN CUME_DIST() OVER (ORDER BY alias1.a)
FROM t AS alias1 RIGHT JOIN t AS alias2 ON alias1.a = alias2.a
WHERE  alias1.a > 1 ;
CUME_DIST() OVER (ORDER BY alias1.a)
0.5
1
DROP TABLE t;

Bug#25840140

CREATE TABLE t(a int);
INSERT INTO t VALUES (1),(2);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
SELECT ROW_NUMBER() OVER (ORDER BY AVG(a)) AS rn FROM t ORDER BY rn ;
rn
1
EXPLAIN             SELECT ROW_NUMBER() OVER (ORDER BY AVG(a)) AS rn FROM t ORDER BY rn ;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (ORDER BY avg(`test`.`t`.`a`) )  AS `rn` from `test`.`t`
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (ORDER BY AVG(a)) AS rn FROM t ORDER BY rn ;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.45"
    },
    "table": {
      "table_name": "t",
      "access_type": "ALL",
      "rows_examined_per_scan": 2,
      "rows_produced_per_join": 2,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.20",
        "prefix_cost": "0.45",
        "data_read_per_join": "16"
      },
      "used_columns": [
        "a"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (ORDER BY avg(`test`.`t`.`a`) )  AS `rn` from `test`.`t`
Even multiple windows lead to no windowing steps when we have implicit grouping
SELECT ROW_NUMBER() OVER (ORDER BY AVG(a)) AS rn, SUM(AVG(a)) OVER (),
CUME_DIST() OVER (ORDER BY AVG(a)) FROM t ORDER BY rn;
rn	SUM(AVG(a)) OVER ()	CUME_DIST() OVER (ORDER BY AVG(a))
1	1.5000	1
EXPLAIN FORMAT=JSON SELECT ROW_NUMBER() OVER (ORDER BY AVG(a)) AS rn, SUM(AVG(a)) OVER (),
CUME_DIST() OVER (ORDER BY AVG(a)) FROM t ORDER BY rn;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.45"
    },
    "table": {
      "table_name": "t",
      "access_type": "ALL",
      "rows_examined_per_scan": 2,
      "rows_produced_per_join": 2,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.20",
        "prefix_cost": "0.45",
        "data_read_per_join": "16"
      },
      "used_columns": [
        "a"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select row_number() OVER (ORDER BY avg(`test`.`t`.`a`) )  AS `rn`,sum(avg(`test`.`t`.`a`)) OVER ()  AS `SUM(AVG(a)) OVER ()`,cume_dist() OVER (ORDER BY avg(`test`.`t`.`a`) )  AS `CUME_DIST() OVER (ORDER BY AVG(a))` from `test`.`t`
DROP TABLE t;
Test for circular dependency in graph of windows
CREATE TABLE t1 (a INT PRIMARY KEY);
SELECT ROW_NUMBER() OVER w
FROM t1
WINDOW w AS (w2), w1 AS (), w2 as (w1);
ROW_NUMBER() OVER w
SELECT ROW_NUMBER() OVER w
FROM t1
WINDOW w AS (w2), w1 AS (w), w2 as (w1);
ERROR HY000: There is a circularity in the window dependency graph.
SELECT ROW_NUMBER() OVER w
FROM t1
WINDOW w AS (w1), w1 AS (w2), w2 as (w1);
ERROR HY000: There is a circularity in the window dependency graph.
SELECT ROW_NUMBER() OVER w FROM t1 WINDOW w AS (w);
ERROR HY000: There is a circularity in the window dependency graph.
SELECT ROW_NUMBER() OVER w
FROM t1
WINDOW w AS (w4), w5 AS (w4), w4 AS (), w3 AS (w2), w1 AS (w3), w2 as (w1);
ERROR HY000: There is a circularity in the window dependency graph.
Test for bad name
SELECT ROW_NUMBER() OVER w FROM t1 WINDOW w1 AS ();
ERROR HY000: Window name 'w' is not defined.
Test for good name with other unnamed window present
SELECT SUM(a) OVER w, ROW_NUMBER() OVER () FROM t1 WINDOW w AS ();
SUM(a) OVER w	ROW_NUMBER() OVER ()
Test for bad name with other unnamed window present
SELECT SUM(a) OVER w2, ROW_NUMBER() OVER () FROM t1 WINDOW w AS ();
ERROR HY000: Window name 'w2' is not defined.
Test for bad window name in window building on other window
OK
SELECT SUM(a) OVER w1, ROW_NUMBER() OVER w2 FROM t1
WINDOW w2 AS (),
w1 AS (w2 ORDER BY a);
SUM(a) OVER w1	ROW_NUMBER() OVER w2
Bad
SELECT SUM(a) OVER w1, ROW_NUMBER() OVER w2 FROM t1
WINDOW w2 AS (),
w1 AS (w22 ORDER BY a);
ERROR HY000: Window name 'w22' is not defined.
Tests without ORDER BY (corner cases)
CREATE TABLE tno (a INT);
SELECT RANK() OVER () FROM tno;
RANK() OVER ()
SELECT DENSE_RANK() OVER () FROM tno;
DENSE_RANK() OVER ()
SELECT PERCENT_RANK() OVER () FROM tno;
PERCENT_RANK() OVER ()
SELECT CUME_DIST() OVER () FROM tno;
CUME_DIST() OVER ()
INSERT INTO tno VALUES(20);
SELECT RANK() OVER () FROM tno;
RANK() OVER ()
1
SELECT DENSE_RANK() OVER () FROM tno;
DENSE_RANK() OVER ()
1
SELECT PERCENT_RANK() OVER () FROM tno;
PERCENT_RANK() OVER ()
0
SELECT CUME_DIST() OVER () FROM tno;
CUME_DIST() OVER ()
1
INSERT INTO tno VALUES(20),(21),(21),(null);
SELECT RANK() OVER (PARTITION BY a) FROM tno;
RANK() OVER (PARTITION BY a)
1
1
1
1
1
SELECT DENSE_RANK() OVER (PARTITION BY a) FROM tno;
DENSE_RANK() OVER (PARTITION BY a)
1
1
1
1
1
SELECT PERCENT_RANK() OVER (PARTITION BY a) FROM tno;
PERCENT_RANK() OVER (PARTITION BY a)
0
0
0
0
0
SELECT CUME_DIST() OVER (PARTITION BY a) FROM tno;
CUME_DIST() OVER (PARTITION BY a)
1
1
1
1
1
DROP TABLE tno;
# constant table is a special case (window without tmp table)
SELECT RANK() OVER () FROM (SELECT 1) t;
RANK() OVER ()
1
SELECT DENSE_RANK() OVER () FROM (SELECT 1) t;
DENSE_RANK() OVER ()
1
SELECT PERCENT_RANK() OVER () FROM (SELECT 1) t;
PERCENT_RANK() OVER ()
0
SELECT CUME_DIST() OVER () FROM (SELECT 1) t;
CUME_DIST() OVER ()
1
Test for bad frame bounds
SELECT ROW_NUMBER() OVER (ROWS BETWEEN UNBOUNDED FOLLOWING AND CURRENT ROW)
FROM t1;
ERROR HY000: Window '<unnamed window>': frame start cannot be UNBOUNDED FOLLOWING.
SELECT ROW_NUMBER() OVER (ROWS BETWEEN CURRENT ROW AND UNBOUNDED PRECEDING)
FROM t1;
ERROR HY000: Window '<unnamed window>': frame end cannot be UNBOUNDED PRECEDING.
SELECT ROW_NUMBER() OVER (ORDER BY a RANGE BETWEEN CURRENT ROW AND UNBOUNDED PRECEDING)
FROM t1;
ERROR HY000: Window '<unnamed window>': frame end cannot be UNBOUNDED PRECEDING.
SELECT ROW_NUMBER() OVER (ROWS BETWEEN INTERVAL 2 DAY PRECEDING AND UNBOUNDED FOLLOWING)
FROM t1;
ERROR HY000: Window '<unnamed window>': INTERVAL can only be used with RANGE frames.
SELECT ROW_NUMBER() OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND INTERVAL 2 DAY FOLLOWING)
FROM t1;
ERROR HY000: Window '<unnamed window>': INTERVAL can only be used with RANGE frames.
DROP TABLE t1;

Bug#25879669 SIG11 AT ADD_FROM_ITEM IN SQL/WINDOW.CC

CREATE TABLE t(a int, b int);
INSERT INTO t VALUES (1,2),(3,4);
SELECT COUNT(*) AS count,
ROW_NUMBER() OVER (ORDER BY b) AS rn
FROM t ORDER BY b;
ERROR 42000: In aggregated query without GROUP BY, expression #1 of PARTITION BY or ORDER BY clause of window '<unnamed window>' contains nonaggregated column 'test.t.b'; this is incompatible with sql_mode=only_full_group_by
SELECT 1 UNION
(SELECT ROW_NUMBER() OVER ( ORDER BY b ) AS rn    FROM t ORDER BY b);
1
1
2
DROP TABLE t;
#
# Bug#25874481 WL9603:ASSERTION `JOIN()->ORDERED_INDEX_USAGE != (FILESORT->ORDER == JOIN()->ORD
#
CREATE TABLE AA (
pk int(11) NOT NULL AUTO_INCREMENT,
col_dec_key decimal(20,4) NOT NULL,
col_varchar_key varchar(1) NOT NULL,
PRIMARY KEY (pk),
KEY col_dec_key (col_dec_key),
KEY col_varchar_key (col_varchar_key,pk)
) ENGINE=InnoDB AUTO_INCREMENT=11;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO AA VALUES(10,6473.2230,'a');
CREATE TABLE D (
pk int(11) NOT NULL AUTO_INCREMENT,
col_int int(11) NOT NULL,
col_int_key int(11) NOT NULL,
col_dec decimal(20,4) NOT NULL,
col_dec_key decimal(20,4) NOT NULL,
col_varchar_key varchar(1) NOT NULL,
col_varchar varchar(1) NOT NULL,
PRIMARY KEY (pk),
KEY col_int_key (col_int_key),
KEY col_dec_key (col_dec_key),
KEY col_varchar_key (col_varchar_key,col_int_key)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO D VALUES
(1,5,4,4090.3920,5170.7060,'q','q'),(2,8,3,2634.3480,695.3360,'e','e');
SET SQL_MODE='';
UPDATE
D AS OUTR1 LEFT JOIN D AS OUTR2
ON ( OUTR1 . pk = OUTR2 . col_int_key )
SET OUTR1.col_varchar_key = 0
WHERE OUTR1 . col_int_key < (
SELECT DISTINCT FIRST_VALUE(7) OVER (  ) AS y
FROM AA AS INNR1
WHERE OUTR2 . col_dec_key <= 6
ORDER BY INNR1 . col_varchar_key LIMIT 1);
SET SQL_MODE=DEFAULT;
DROP TABLE AA, D;
#
# Bug#25880362 WL9603:ASSERTION `TAB->TYPE() == JT_REF || TAB->TYPE() == JT_EQ_REF' FAILED.
#
CREATE TABLE G (
pk int(11) NOT NULL AUTO_INCREMENT,
col_int int(11) DEFAULT NULL,
PRIMARY KEY (pk)
) ENGINE=innodb AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO G VALUES (1,7);
CREATE TABLE H (
col_varchar_10_utf8 varchar(10) CHARACTER SET utf8mb3 DEFAULT NULL,
col_varchar_10_latin1 varchar(10) DEFAULT NULL,
pk int(11) NOT NULL AUTO_INCREMENT,
PRIMARY KEY (pk)
) ENGINE=innodb AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO H VALUES('RDDGR','so',1);
SELECT
alias1 . col_varchar_10_latin1 AS field1,
NTH_VALUE(alias2.col_int, 4) OVER ( ORDER BY alias1.col_varchar_10_utf8 ASC )
AS field2
FROM  ( SELECT * FROM  H  ) AS  alias1  LEFT  JOIN ( SELECT * FROM  G  ) AS
alias2
ON  alias1 . pk =  alias2 . pk
WHERE  alias1 . pk = 1;
field1	field2
so	NULL
DROP TABLE G,H;
#
# Bug#25840052: PROBLEM WITH FIELD::REAL_MAYBE_NULL IN SQL/FIELD.H
#               FOR MIN/MAX(<WF>)
#
CREATE TABLE t(a int);
INSERT INTO t VALUES  (1),(2);
SELECT MAX(row_number() OVER ()) FROM t;
ERROR HY000: You cannot use the window function 'row_number' in this context.'
SELECT MIN(rank() OVER (ORDER BY a)) FROM t;
ERROR HY000: You cannot use the window function 'rank' in this context.'
SELECT BIT_AND(rank() OVER (ORDER BY a)) FROM t;
ERROR HY000: You cannot use the window function 'rank' in this context.'
SELECT MIN(a),SUM(rank() OVER (ORDER BY a)) FROM t GROUP BY a;
ERROR HY000: You cannot use the window function 'rank' in this context.'
DROP TABLE t;
#
# Bug#25868905: PROBLEM AT FILESORT::MAKE_SORTORDER IN SQL/FILESORT.CC
#               FOR PREPARED STMTS
#
CREATE TABLE t(pk int PRIMARY KEY, col_int int, col_varchar varchar(10));
PREPARE ps FROM "SELECT
RANK() OVER ( PARTITION BY pk ORDER BY col_int, col_varchar, pk) AS rnk,
CUME_DIST() OVER ( PARTITION BY col_int ORDER BY pk ) AS c_dist
FROM t";
EXECUTE ps;
rnk	c_dist
DROP PREPARE ps;
DROP TABLE t;
#
# Bug#25877151: PROBLEM IN ITEM_CACHE* MAKE_RESULT_ITEM(ITEM*)
#
CREATE TABLE t1 (a INT, d CHAR(1));
PREPARE ps FROM "
SELECT ROW_NUMBER() OVER ( ORDER BY d ) AS rn
FROM t1
WINDOW  w1 AS ( ORDER BY d ),
        w2 AS ( ORDER BY a RANGE 5 PRECEDING )";
EXECUTE ps;
rn
DROP PREPARE ps;
DROP TABLE t1;
#
# Bug#25889341 WL#9603: RANK FUNCTIONS RETURN INCORRECT RESULT IN PREPARED SATEMENT
#
CREATE TABLE t (a int, b int, c int);
INSERT INTO t VALUES (5,6,1),(NULL,6,1),(4,6,1),(5,6,1),(NULL,6,1);
SELECT  ROW_NUMBER() OVER ( ORDER BY a ) AS f1,
RANK() OVER ( ORDER BY c ) AS f2,
LEAD(c) OVER ( ORDER BY a ROWS UNBOUNDED PRECEDING ) AS f7
FROM t;
f1	f2	f7
1	1	1
2	1	1
3	1	1
4	1	1
5	1	NULL
DROP TABLE t;
#
# Bug#25886572 WL#9603: SIG11 AT ITEM_REF::VAL_INT IN SQL/ITEM.CC
#
SET sql_mode='';
CREATE TABLE t (a int, b int);
INSERT INTO t values (1,2),(3,4);
SELECT RANK() OVER w1 AS rnk FROM t
WINDOW w1 AS (ORDER BY AVG(a))
ORDER BY b;
rnk
1
SET sql_mode=DEFAULT;
DROP TABLE t;
#
# Bug#25880999 : WL#9603: ASSERT `SELECT_LEX->ACTIVE_OPTIONS()
#                & (1ULL << 17)' IN SQL_SELECT.CC
CREATE TABLE t1(a int, b int);
CREATE TABLE t2(a int, b int);
SELECT ROW_NUMBER() OVER ( ORDER BY t1.a ) AS rn
FROM t1, t2 WHERE t1.a = 1 GROUP BY t1.a;
rn
DROP TABLE t1,t2;
#
# Bug#25907777  WL#9603: ASSERT `SELECT_LEX->LEAF_TABLE_COUNT == 0...' IN SQL/SQL_OPTIMIZER.CC
#
CREATE TABLE t1 (a date);
CREATE TABLE t2 (a int);
INSERT INTO t1 SELECT CURRENT_DATE();
INSERT INTO t2 VALUES (5);
SELECT RANK() OVER (ORDER BY a RANGE INTERVAL (SELECT a FROM t2) MINUTE PRECEDING) FROM t1;
ERROR HY000: Window '<unnamed window>' has a non-constant frame bound.
SELECT RANK() OVER (ORDER BY a RANGE BETWEEN INTERVAL 1 MINUTE PRECEDING AND INTERVAL (SELECT a FROM t2) MINUTE FOLLOWING) FROM t1;
ERROR HY000: Window '<unnamed window>' has a non-constant frame bound.
SELECT RANK() OVER (ORDER BY a RANGE INTERVAL 1+(SELECT a FROM t2) MINUTE PRECEDING) FROM t1;
ERROR HY000: Window '<unnamed window>' has a non-constant frame bound.
SELECT RANK() OVER (ORDER BY a ROWS (SELECT a FROM t2) PRECEDING) FROM t1;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '(SELECT a FROM t2) PRECEDING) FROM t1' at line 1
SELECT RANK() OVER (ORDER BY a ROWS BETWEEN 1 PRECEDING AND (SELECT a FROM t2) FOLLOWING) FROM t1;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '(SELECT a FROM t2) FOLLOWING) FROM t1' at line 1
DROP TABLE t1, t2;
#
# Bug#25921353 WL#9603: EMPTY RESULT WITH WF IN SELECT, UNGROUPED HAVING AND ORDER BY
#
CREATE TABLE t (a int, b varchar(10));
INSERT INTO t VALUES (1, 'are'), (2, 'not'), (3, 'have');
SELECT a AS field1, ROW_NUMBER() OVER () AS field2 FROM t
HAVING field1 >= 2;
field1	field2
2	1
3	2
Used to give empty set
SELECT a AS field1, ROW_NUMBER() OVER () AS field2 FROM t
HAVING field1 >= 2 ORDER BY field1;
field1	field2
2	1
3	2
DROP TABLE t;
#
# Bug#25902905 WL9603:ASSERTION `!(TAB->TABLE()->REGINFO.NOT_EXISTS_OPTIMIZE && !TAB->CONDITION
#
CREATE TABLE a (pk int PRIMARY KEY, c varchar(10));
INSERT INTO a VALUES (6, 's');
CREATE TABLE b (pk int PRIMARY KEY,  c varchar(255));
INSERT INTO b VALUES (9,'s');
EXPLAIN FORMAT=tree SELECT ROW_NUMBER() OVER (ORDER BY a.c)
FROM  a LEFT JOIN b
ON a.c = b.c
WHERE (b.pk IS NULL AND a.pk IN (6));
EXPLAIN
-> Window aggregate: row_number() OVER (ORDER BY 's' )   (rows=1)
    -> Filter: (b.pk is null)  (rows=1)
        -> Nested loop left join  (rows=1)
            -> Rows fetched before execution  (rows=1)
            -> Sort: 's'  (rows=1)
                -> Filter: ('s' = b.c)  (rows=1)
                    -> Table scan on b  (rows=1)

SELECT ROW_NUMBER() OVER (ORDER BY a.c)
FROM  a LEFT JOIN b
ON a.c = b.c
WHERE (b.pk IS NULL AND a.pk IN (6));
ROW_NUMBER() OVER (ORDER BY a.c)
DROP TABLE a, b;
#
# Bug#25907063 WL#9603:ASSERT `INITED == INDEX' AT HANDLER::HA_INDEX_READ_MAP IN SQL/HANDLER.CC
#
SET SQL_MODE='';
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SET tmp_table_size= 16384;
SELECT DISTINCT MAX( table2.`col_varchar_255_utf8` ) AS max1 ,
MIN( table1.`col_date` ) AS min1 ,
AVG( table2.`col_int` ) AS avg1 ,
MAX( table1.`col_varchar_255_utf8_2` ) AS max2 ,
table2.`col_varchar_255_utf8`  ,
FIRST_VALUE( table1. `col_varchar_255_utf8` )  OVER (ORDER BY MAX( table2.`col_varchar_255_utf8` ),
MIN( table1.`col_date` ),
AVG( table2.`col_int` ),
MAX( table1.`col_varchar_255_utf8_2` ),
table2.`col_varchar_255_utf8` ) AS 1st_val
FROM  C AS table1 LEFT JOIN
B AS table2 ON  table1.`col_int_2` < table2.`col_int_2`
    GROUP BY  table2.`col_varchar_255_utf8`,  table1.`col_varchar_255_utf8`;
max1	min1	avg1	max2	col_varchar_255_utf8	1st_val
NULL	NULL	NULL	JGMQN	NULL	that
NULL	NULL	NULL	why	NULL	that
NULL	2002-11-15	NULL	n	NULL	that
NULL	2002-12-16	NULL	e	NULL	that
NULL	2003-05-16	NULL	JXEJC	NULL	that
NDQUO	0000-00-00	1827930112.0000	crutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqj	NDQUO	that
NDQUO	0000-00-00	1827930112.0000	f	NDQUO	that
NDQUO	0000-00-00	1827930112.0000	gvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqjyzxeehuuwqvnyfejvjqzdvjjatukdbpiiabrdqveoesihupydtk	NDQUO	that
NDQUO	0000-00-00	1827930112.0000	so	NDQUO	that
pxkwzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdf	NULL	1662451712.0000	MBLMH	pxkwzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdf	that
pxkwzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdf	0000-00-00	1662451712.0000	crutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqj	pxkwzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdf	that
pxkwzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdf	0000-00-00	1662451712.0000	f	pxkwzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdf	that
pxkwzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdf	0000-00-00	1662451712.0000	gvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqjyzxeehuuwqvnyfejvjqzdvjjatukdbpiiabrdqveoesihupydtk	pxkwzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdf	that
pxkwzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdf	0000-00-00	1662451712.0000	so	pxkwzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdf	that
wzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqjyzxeehuuwqvnyfejvjqzdvjjatukdbpiiabrdqveoesihupydtktqmkglyemowxmzkymvicqwnriwvvsarcpejhamxqxr	0000-00-00	8.0000	crutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqj	wzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqjyzxeehuuwqvnyfejvjqzdvjjatukdbpiiabrdqveoesihupydtktqmkglyemowxmzkymvicqwnriwvvsarcpejhamxqxr	that
wzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqjyzxeehuuwqvnyfejvjqzdvjjatukdbpiiabrdqveoesihupydtktqmkglyemowxmzkymvicqwnriwvvsarcpejhamxqxr	0000-00-00	8.0000	f	wzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqjyzxeehuuwqvnyfejvjqzdvjjatukdbpiiabrdqveoesihupydtktqmkglyemowxmzkymvicqwnriwvvsarcpejhamxqxr	that
wzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqjyzxeehuuwqvnyfejvjqzdvjjatukdbpiiabrdqveoesihupydtktqmkglyemowxmzkymvicqwnriwvvsarcpejhamxqxr	0000-00-00	8.0000	gvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqjyzxeehuuwqvnyfejvjqzdvjjatukdbpiiabrdqveoesihupydtk	wzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqjyzxeehuuwqvnyfejvjqzdvjjatukdbpiiabrdqveoesihupydtktqmkglyemowxmzkymvicqwnriwvvsarcpejhamxqxr	that
wzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqjyzxeehuuwqvnyfejvjqzdvjjatukdbpiiabrdqveoesihupydtktqmkglyemowxmzkymvicqwnriwvvsarcpejhamxqxr	0000-00-00	8.0000	so	wzyojewtgvasnskcrutdamugrogyzzsaemysmyzomflzilnslxzvupfxrajygqpclheykudxgaloqoaaeirdfcljrcqdrciywuiaapvivsmnlvcsfognnabeubyprqcfadvzrjzhaanyybmbhzwohfockjujddqwqjyzxeehuuwqvnyfejvjqzdvjjatukdbpiiabrdqveoesihupydtktqmkglyemowxmzkymvicqwnriwvvsarcpejhamxqxr	that
SET tmp_table_size=DEFAULT, SQL_MODE=DEFAULT;
DROP TABLE C,B;
#
# Bug#25894860: PROBLEM IN PROTOCOL_SEND
CREATE TABLE t (a int PRIMARY KEY, b varchar(10), KEY idx1 (b));
INSERT INTO t VALUES (2,'b'),(1,'back'),(5,'think'),(4,'v'),(3,'y');
SELECT  LAG(b) OVER () AS wf_lag,
ROW_NUMBER() OVER () AS wf_rn
FROM t
WHERE b LIKE ('_') AND a=2;
wf_lag	wf_rn
NULL	1
DROP TABLE t;
#
# Bug#25895300: PROBLEM IN PROTOCOL::SEND
CREATE TABLE t1 (
pk int(11) NOT NULL AUTO_INCREMENT,
col_int int(11) DEFAULT NULL,
col_datetime datetime DEFAULT NULL,
PRIMARY KEY (pk)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1,8,'2004-03-26 11:59:45'),(2,-692387840,'2000-11-03
14:56:50'),(3,8,'2008-04-11 14:04:45'),(4,-660865024,'2004-08-11
07:07:20'),(5,9,'2001-04-11 00:00:00');
Warnings:
Warning	4095	Delimiter '\n' in position 10 in datetime value '2000-11-03
14:56:50' at row 2 is deprecated. Prefer the standard ' '.
Warning	4095	Delimiter '\n' in position 10 in datetime value '2004-08-11
07:07:20' at row 4 is deprecated. Prefer the standard ' '.
CREATE TEMPORARY TABLE t2 (
pk int(11) NOT NULL AUTO_INCREMENT,
col1 varchar(10) CHARACTER SET utf8mb3 DEFAULT NULL,
PRIMARY KEY (pk)
) ENGINE=Memory;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t2 VALUES (1,'ntdajwwdda');
SELECT LEAD(t2.col1, 5)
OVER ( ORDER BY t1.col_int DESC ) as lead1 ,
t1.pk, LEAD(t1.pk, 4) OVER ( PARTITION BY t1.col_int ORDER BY t1.col_int
) as lead2 FROM  t1 RIGHT JOIN t2 ON t1.pk=t2.pk
WHERE  t2.pk IS NOT NULL;
lead1	pk	lead2
NULL	1	NULL
DROP TABLE t1,t2;
CREATE TABLE t1 (
pk INTEGER NOT NULL AUTO_INCREMENT,
col1 varchar(255) DEFAULT NULL,
PRIMARY KEY (pk)
)DEFAULT CHARSET=latin1;
INSERT INTO t1 VALUES (1,'I\'ll'),(2,'BORKU'),(3,'HZISF'),(4,'q'),(5,'o'),
       (6,'now'),(7,'WIBYB'),(8,'could'),(9,'were'),(10,'on'),(11,'HYADL'),
       (12,'l'),(13,'asdas'),(14,'n'),(16,'my'),(17,'NNTTK'),(18,'as'),
       (19,'TOAAB'),(20,'asdas'),(21,'well'),(22,'i'),(23,'u'),(24,'e'),
       (25,'o'),(26,'c'),(28,'YLZRI'),(29,'well'),(30,'want'),(31,'with'),
       (32,'VMVLI'),(33,'right'),(34,'llotd'),(35,'DNLWV'),(36,'SIPKW'),
       (37,'o'),(38,'mean'),(39,'asdas'),(40,'asdas');
CREATE VIEW view_t1 AS SELECT * FROM t1;
SELECT NTH_VALUE(view_t1.col1,2) OVER (), view_t1.col1, view_t1.pk FROM view_t1 LEFT JOIN t1
ON  view_t1.pk = t1.pk WHERE t1.pk BETWEEN 4 AND 10 AND t1.pk IN (4);
NTH_VALUE(view_t1.col1,2) OVER ()	col1	pk
NULL	q	4
DROP TABLE t1;
DROP VIEW view_t1;
#
# Bug#25914495: WL#9603: SIG11 AT ITEM_FIELD::ITEM_FIELD IN SQL/ITEM.CC
#
CREATE TABLE t1(a int);
CREATE TABLE t2(b int);
INSERT INTO t1 VALUES (1);
INSERT INTO t2 VALUES (3);
SELECT  RANK() OVER ( ORDER BY a ) + 1 AS rank_expr FROM t1, t2;
rank_expr
2
DROP TABLE t1,t2;
#
# Bug#25960114 WL#9603: RESULT DIFF SEEN WITHOUT PARTITION CLAUSE AND PARTITION BY NULL
#
CREATE TABLE t1 (a int, b float);
INSERT INTO t1 VALUES
(4,12),(5,2),(9,14),(12,10),(6,8),(5,7),(2,15),(2,15),(15,11),(14,5),(14,240),
(1,10),(14,5),(5,9),(1,11),(2,5),(11,9),(13,13),(6,187),(8,12),(10,12),(15,14),
(50,4),(14,3),(166,2),(15,13),(10,12),(48,4),(13,3),(7,10);
SELECT a, b, LEAD (b, 1) OVER (ORDER BY a,b ASC) AS c FROM t1 a ORDER BY a, b, c;
a	b	c
1	10	11
1	11	5
2	5	15
2	15	12
2	15	15
4	12	2
5	2	7
5	7	9
5	9	8
6	8	187
6	187	10
7	10	12
8	12	14
9	14	12
10	12	9
10	12	12
11	9	10
12	10	3
13	3	13
13	13	3
14	3	5
14	5	5
14	5	240
14	240	11
15	11	13
15	13	14
15	14	4
48	4	4
50	4	2
166	2	NULL
SELECT a, b, LEAD (b, 1) OVER (PARTITION BY NULL ORDER BY a,b ASC) AS c FROM t1 a ORDER BY a, b, c;
a	b	c
1	10	11
1	11	5
2	5	15
2	15	12
2	15	15
4	12	2
5	2	7
5	7	9
5	9	8
6	8	187
6	187	10
7	10	12
8	12	14
9	14	12
10	12	9
10	12	12
11	9	10
12	10	3
13	3	13
13	13	3
14	3	5
14	5	5
14	5	240
14	240	11
15	11	13
15	13	14
15	14	4
48	4	4
50	4	2
166	2	NULL
DROP TABLE t1;
#
# Bug#26781725 INCORRECT RESULTS FOR QUERY(MAX FUNC+HAVING CLAUSE) WHEN USED INSIDE VIEW
#
CREATE TABLE CC (
pk int(11) NOT NULL AUTO_INCREMENT,
col_varchar_key varchar(1) NOT NULL,
col_varchar varchar(1) NOT NULL,
PRIMARY KEY (pk),
KEY col_varchar_key (col_varchar_key)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO CC VALUES
(10,'v','v'),(18,'a','a'),(19,'v','v'),(20,'u','u'),(21,'s','s'),(22,'y','y'),
(23,'z','z'),(28,'y','y');
SELECT  MAX( col_varchar_key  )  AS field1  FROM CC  AS alias1
HAVING field1 <>  5;
field1
z
CREATE OR REPLACE VIEW v1 AS
SELECT  MAX( col_varchar_key  )  AS field1  FROM CC  AS alias1
HAVING field1  <>  5;
SELECT * FROM v1;
field1
z
DROP VIEW v1;
DROP TABLE CC;
#
# Tests added to improve GCOV Coverage data
#
Item_sum_sum::val_str "if (null_value): test
CREATE TABLE t(v INT);
INSERT INTO t VALUES (NULL), (3);
SELECT SUM((SELECT v FROM t LIMIT 1)) OVER ();
SUM((SELECT v FROM t LIMIT 1)) OVER ()
NULL
DROP TABLE t;
#
# Bug#25976248 WL#9603 ASSERT ERROR IN ITEM_SUM::FIX_AFTER_PULLOUT
#
CREATE TABLE t1(a TEXT);
INSERT INTO t1 VALUES('1');
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select row_number() over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select row_number() over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select ntile(3) over (order by null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select ntile(3) over (order by null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select avg(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select avg(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select sum(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select sum(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select count(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select count(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select dense_rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select dense_rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a - 1 IN ( (select percent_rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select percent_rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select cume_dist() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select cume_dist() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select lead(1, 0) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select lead(1, 0) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select nth_value(1, 1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select nth_value(1, 1) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select first_value(1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a +1  IN ( (select first_value(1) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select last_value(1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a +1  IN ( (select last_value(1) over (ORDER BY null)));
SUM( distinct a )
DROP TABLE t1;
CREATE TABLE t1(a DECIMAL(4,2));
INSERT INTO t1 VALUES(1);
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select row_number() over ()));
SUM( distinct a )
1.00
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select row_number() over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select ntile(3) over (order by null)));
SUM( distinct a )
1.00
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select ntile(3) over (order by null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select avg(1) over ()));
SUM( distinct a )
1.00
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select avg(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select sum(1) over ()));
SUM( distinct a )
1.00
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select sum(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select count(1) over ()));
SUM( distinct a )
1.00
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select count(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select rank() over (ORDER BY null)));
SUM( distinct a )
1.00
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select dense_rank() over (ORDER BY null)));
SUM( distinct a )
1.00
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select dense_rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a - 1 IN ( (select percent_rank() over (ORDER BY null)));
SUM( distinct a )
1.00
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select percent_rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select cume_dist() over (ORDER BY null)));
SUM( distinct a )
1.00
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select cume_dist() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select lead(1, 0) over (ORDER BY null)));
SUM( distinct a )
1.00
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select lead(1, 0) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select nth_value(1, 1) over (ORDER BY null)));
SUM( distinct a )
1.00
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select nth_value(1, 1) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select first_value(1) over (ORDER BY null)));
SUM( distinct a )
1.00
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a +1  IN ( (select first_value(1) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select last_value(1) over (ORDER BY null)));
SUM( distinct a )
1.00
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a +1  IN ( (select last_value(1) over (ORDER BY null)));
SUM( distinct a )
DROP TABLE t1;
CREATE TABLE t1(a REAL);
INSERT INTO t1 VALUES(1.0);
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select row_number() over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select row_number() over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select ntile(3) over (order by null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select ntile(3) over (order by null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select avg(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select avg(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select sum(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select sum(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select count(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select count(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select dense_rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select dense_rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a - 1 IN ( (select percent_rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select percent_rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select cume_dist() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select cume_dist() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select lead(1, 0) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select lead(1, 0) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select nth_value(1, 1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select nth_value(1, 1) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select first_value(1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a +1  IN ( (select first_value(1) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select last_value(1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a +1  IN ( (select last_value(1) over (ORDER BY null)));
SUM( distinct a )
DROP TABLE t1;
CREATE TABLE t1(a TIME);
INSERT INTO t1 VALUES('00:00:01');
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select row_number() over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select row_number() over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select ntile(3) over (order by null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select ntile(3) over (order by null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select avg(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select avg(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select sum(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select sum(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select count(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select count(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select dense_rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select dense_rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a - 1 IN ( (select percent_rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select percent_rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select cume_dist() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select cume_dist() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select lead(1, 0) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select lead(1, 0) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select nth_value(1, 1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select nth_value(1, 1) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select first_value(1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a +1  IN ( (select first_value(1) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select last_value(1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a +1  IN ( (select last_value(1) over (ORDER BY null)));
SUM( distinct a )
DROP TABLE t1;
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES(1);
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select row_number() over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select row_number() over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select ntile(3) over (order by null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select ntile(3) over (order by null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select avg(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select avg(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select sum(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select sum(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select count(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select count(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select dense_rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select dense_rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a - 1 IN ( (select percent_rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select percent_rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select cume_dist() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select cume_dist() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select lead(1, 0) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select lead(1, 0) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select nth_value(1, 1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select nth_value(1, 1) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select first_value(1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a +1  IN ( (select first_value(1) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select last_value(1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a +1  IN ( (select last_value(1) over (ORDER BY null)));
SUM( distinct a )
DROP TABLE t1;
CREATE TABLE t1(a JSON);
INSERT INTO t1 VALUES('1');
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select row_number() over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select row_number() over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select ntile(3) over (order by null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select ntile(3) over (order by null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select avg(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select avg(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select sum(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select sum(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select count(1) over ()));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select count(1) over ()));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select dense_rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select dense_rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a - 1 IN ( (select percent_rank() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select percent_rank() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select cume_dist() over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select cume_dist() over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select lead(1, 0) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select lead(1, 0) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select nth_value(1, 1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a + 1 IN ( (select nth_value(1, 1) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select first_value(1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a +1  IN ( (select first_value(1) over (ORDER BY null)));
SUM( distinct a )
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a     IN ( (select last_value(1) over (ORDER BY null)));
SUM( distinct a )
1
SELECT SUM( distinct a ) FROM t1 GROUP BY a HAVING a +1  IN ( (select last_value(1) over (ORDER BY null)));
SUM( distinct a )
DROP TABLE t1;
#
# Problem with ROLLUP and WINDOW functions
#
CREATE TABLE t1 (i INTEGER);
INSERT INTO t1 values (1);
INSERT INTO t1 values (1);
INSERT INTO t1 values (2);
INSERT INTO t1 values (2);
INSERT INTO t1 values (3);
INSERT INTO t1 values (3);
INSERT INTO t1 values (4);
INSERT INTO t1 values (4);
SELECT i, i+1, SUM(i) OVER () FROM t1 GROUP BY i WITH ROLLUP;
i	i+1	SUM(i) OVER ()
1	2	10
2	3	10
3	4	10
4	5	10
NULL	NULL	10
DROP TABLE t1;
#
# Bug#26033662 WL#9603: ZERO RESULT FOR WINDOW FUNCTIONS WITH DERIVED TABLE HAVING GROUP BY
#
CREATE TABLE t(a INT, b INT);
INSERT INTO t VALUES (1,1), (1,2), (2,3), (2,4), (2,5), (2,6), (3,7), (3,11), (4,8);
SELECT * FROM (SELECT b, RANK() OVER (ORDER BY b) AS c FROM t GROUP BY a,b) s1 UNION
(SELECT b, MAX(a) AS c FROM t GROUP BY a,b ) ORDER BY b;
b	c
1	1
11	3
11	9
2	1
2	2
3	2
3	3
4	2
4	4
5	2
5	5
6	2
6	6
7	3
7	7
8	4
8	8
DROP TABLE t;
#
# Bug#26035785 WL#9603: NUMBER OF ROWS PER NTILE BUCKET DIFFER UNEVENLY
#
CREATE TABLE t (col_int int) ;
INSERT INTO t VALUES
(7),(184),(12),(8),(14),(14),(2),(11),(6),(113),(4),(9),(2),(6),(10),(178),(1)
,(2),(8),(204),(4),(15),(7),(253),(14),(3),(10),(11),(15),(15);
SELECT nt, COUNT(*) FROM
(SELECT  col_int, NTILE( 9 ) OVER ( ORDER BY col_int) nt FROM t ) AS s1
GROUP BY nt;
nt	COUNT(*)
1	4
2	4
3	4
4	3
5	3
6	3
7	3
8	3
9	3
DROP TABLE t;
#
# Bug#26048785 WL9603: RANGE UNBOUNDED PRECEDING IS TOO STRICT ON ORDER BY
# Tests for RANGE w/o ORDER BY, and with ORDER BY <char/blob>
#
CREATE TABLE t1(a INT, b CHAR(1), c DATETIME, d BLOB);
INSERT INTO t1 VALUES (1,'x','2010-01-01','blob'), (2, 'y', '2011-01-01', ''),
(3, 'y', '2012-01-01', ''), (4, 't', '2012-01-01', 'blob'),
(5, null, '2013-01-01', null);
SELECT a, b, c, SUM(a) OVER
(RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
FROM t1;
a	b	c	SUM(a) OVER
(RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
1	x	2010-01-01 00:00:00	15
2	y	2011-01-01 00:00:00	15
3	y	2012-01-01 00:00:00	15
4	t	2012-01-01 00:00:00	15
5	NULL	2013-01-01 00:00:00	15
SELECT a, b, c, SUM(a) OVER
(ORDER BY b RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
FROM t1;
a	b	c	SUM(a) OVER
(ORDER BY b RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
5	NULL	2013-01-01 00:00:00	5
4	t	2012-01-01 00:00:00	9
1	x	2010-01-01 00:00:00	10
2	y	2011-01-01 00:00:00	15
3	y	2012-01-01 00:00:00	15
SELECT a, b, c, SUM(a) OVER
(w RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
FROM t1
WINDOW w AS (ORDER BY b);
a	b	c	SUM(a) OVER
(w RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
5	NULL	2013-01-01 00:00:00	5
4	t	2012-01-01 00:00:00	9
1	x	2010-01-01 00:00:00	10
2	y	2011-01-01 00:00:00	15
3	y	2012-01-01 00:00:00	15
SELECT a, b, c, d, SUM(a) OVER
(ORDER BY d RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
FROM t1;
a	b	c	d	SUM(a) OVER
(ORDER BY d RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
5	NULL	2013-01-01 00:00:00	NULL	5
2	y	2011-01-01 00:00:00		10
3	y	2012-01-01 00:00:00		10
1	x	2010-01-01 00:00:00	blob	15
4	t	2012-01-01 00:00:00	blob	15
SELECT a, b, c, SUM(a) OVER
(RANGE UNBOUNDED PRECEDING)
FROM t1;
a	b	c	SUM(a) OVER
(RANGE UNBOUNDED PRECEDING)
1	x	2010-01-01 00:00:00	15
2	y	2011-01-01 00:00:00	15
3	y	2012-01-01 00:00:00	15
4	t	2012-01-01 00:00:00	15
5	NULL	2013-01-01 00:00:00	15
SELECT a, b, c, SUM(a) OVER
(w RANGE UNBOUNDED PRECEDING)
FROM t1
WINDOW w AS ();
a	b	c	SUM(a) OVER
(w RANGE UNBOUNDED PRECEDING)
1	x	2010-01-01 00:00:00	15
2	y	2011-01-01 00:00:00	15
3	y	2012-01-01 00:00:00	15
4	t	2012-01-01 00:00:00	15
5	NULL	2013-01-01 00:00:00	15
SELECT a, b, c, SUM(a) OVER
(RANGE BETWEEN 2 PRECEDING AND CURRENT ROW)
FROM t1;
ERROR HY000: Window '<unnamed window>' with RANGE N PRECEDING/FOLLOWING frame requires exactly one ORDER BY expression, of numeric or temporal type
SELECT a, b, c, SUM(a) OVER
(ORDER BY b RANGE BETWEEN 2 PRECEDING AND CURRENT ROW)
FROM t1;
ERROR HY000: Window '<unnamed window>' with RANGE N PRECEDING/FOLLOWING frame requires exactly one ORDER BY expression, of numeric or temporal type
SELECT a, b, c, SUM(a) OVER
(ORDER BY a RANGE BETWEEN 2 PRECEDING AND CURRENT ROW)
FROM t1;
a	b	c	SUM(a) OVER
(ORDER BY a RANGE BETWEEN 2 PRECEDING AND CURRENT ROW)
1	x	2010-01-01 00:00:00	1
2	y	2011-01-01 00:00:00	3
3	y	2012-01-01 00:00:00	6
4	t	2012-01-01 00:00:00	9
5	NULL	2013-01-01 00:00:00	12
SELECT a, b, c, SUM(a) OVER
(ORDER BY c RANGE BETWEEN 2 PRECEDING AND CURRENT ROW)
FROM t1;
ERROR HY000: Window '<unnamed window>' with RANGE frame has ORDER BY expression of datetime type. Only INTERVAL bound value allowed.
SELECT a, b, c, SUM(a) OVER
(ORDER BY c RANGE BETWEEN INTERVAL 1 HOUR PRECEDING AND CURRENT ROW)
FROM t1;
a	b	c	SUM(a) OVER
(ORDER BY c RANGE BETWEEN INTERVAL 1 HOUR PRECEDING AND CURRENT ROW)
1	x	2010-01-01 00:00:00	1
2	y	2011-01-01 00:00:00	2
3	y	2012-01-01 00:00:00	7
4	t	2012-01-01 00:00:00	7
5	NULL	2013-01-01 00:00:00	5
DROP TABLE t1;
#
# Bug#26022124 WL#9603: SIG11 AT AGGREGATOR_DISTINCT::ENDUP IN SQL/ITEM_SUM.CC
#
CREATE TABLE t1 (pk int NOT NULL, col_int int DEFAULT NULL);
CREATE TABLE t2 (pk int NOT NULL AUTO_INCREMENT PRIMARY KEY,
col_int int DEFAULT NULL,
col_int2 int DEFAULT NULL);
INSERT INTO t1 VALUES (1,88), (2,99);
INSERT INTO t2 VALUES (1,2,3) , (4,5,6);
SELECT ROW_NUMBER() OVER (ORDER BY t2.col_int) AS rn,
COUNT(DISTINCT t1.col_int) AS cnt,
SUM(DISTINCT t1.col_int) AS `sum`
   FROM t1 LEFT JOIN t2 ON t1.pk = t2.pk WHERE t1.pk IN (1)
GROUP BY t1.pk;
rn	cnt	sum
1	1	88
SELECT ROW_NUMBER() OVER () AS rn,
COUNT(DISTINCT t1.col_int) AS cnt,
SUM(DISTINCT t1.col_int) AS `sum`
   FROM t1 LEFT JOIN t2 ON t1.pk = t2.pk WHERE t1.pk IN (1)
GROUP BY t1.pk;
rn	cnt	sum
1	1	88
SELECT ROW_NUMBER() OVER (),
FIRST_VALUE(SUM(DISTINCT t1.col_int)) OVER (ORDER BY t1.pk),
FIRST_VALUE(SUM(DISTINCT t1.col_int) + 1) OVER (ORDER BY t1.pk),
SUM(DISTINCT t1.col_int),
RANK() OVER (ORDER BY t1.pk)
FROM t1 LEFT JOIN t2 ON t1.pk = t2.pk WHERE t1.pk IN (1)
GROUP BY t1.pk;
ROW_NUMBER() OVER ()	FIRST_VALUE(SUM(DISTINCT t1.col_int)) OVER (ORDER BY t1.pk)	FIRST_VALUE(SUM(DISTINCT t1.col_int) + 1) OVER (ORDER BY t1.pk)	SUM(DISTINCT t1.col_int)	RANK() OVER (ORDER BY t1.pk)
1	88	89	88	1
DROP TABLE t1, t2;
#
# Bug #26115726 WL#9727: RESULT DIFF FOR STD_SAMP IN A VIEW
#
CREATE TABLE t(a int);
INSERT INTO t VALUES (NULL), (1), (3), (6), (10);
CREATE VIEW v1 AS
SELECT STDDEV_SAMP(a) OVER ( ORDER BY a ROWS CURRENT ROW) AS std_dev_samp FROM t;
SELECT STDDEV_SAMP(a) OVER ( ORDER BY a ROWS CURRENT ROW ) AS std_dev_samp FROM t;
std_dev_samp
NULL
NULL
NULL
NULL
NULL
SELECT * FROM v1;
std_dev_samp
NULL
NULL
NULL
NULL
NULL
CREATE OR REPLACE VIEW v1 AS SELECT STDDEV_SAMP(a) FROM  t;
SELECT STDDEV_SAMP(a) FROM t;
STDDEV_SAMP(a)
3.9157800414902435
SELECT * FROM v1;
STDDEV_SAMP(a)
3.9157800414902435
DROP VIEW v1;
DROP TABLE t;
#
# Bug #26132963 WL#9603: SIG11 AT SORTLENGTH() SQL/FILESORT.CC
#
CREATE TABLE t1(c1 int, c2 int);
CREATE TABLE t2(c1 int, c2 int);
INSERT INTO t1 VALUES
(1,-1208352768),(2,NULL),(3,212140032),(4,5),(5,3),(6,NULL),(7,1),(8,9),
(9,-1563688960),(10,-288358400),(11,NULL),(12,9),(13,9),(14,NULL),(15,NULL),
(16,NULL),(17,5),(18,1),(19,4),(20,0),(21,-2036334592),(22,1),
(23,1961558016),(24,0),(25,NULL);
INSERT INTO t2 VALUES
(1,553648128),(2,0),(3,3),(4,8),(5,1662844928),(6,0),(7,NULL),
(8,7),(9,NULL),(10,NULL);
SELECT RANK() OVER ( ORDER BY MIN( t2 . c1 ) + MAX( t1 . c2 )  ) AS rnk
FROM t1 RIGHT OUTER JOIN t2 ON t1.c1 = t2.c2
ORDER BY rnk;
rnk
1
SELECT 2 FROM t1
ORDER BY SUM(c1) OVER
(PARTITION BY COUNT(c2)+(SELECT SUM(c1) FROM t2)) DESC;
2
2
DROP TABLE t1, t2;
#
# Bug #26115664 WL#9603: WRONG RESULT WITH STORED PROGRAM+AGG WFS ON SINGLE ROW MEMORY TABLE
#
CREATE TABLE te2 (c1 int, c2 int) ENGINE=Memory;
INSERT INTO te2 VALUES(1,1351614464 );
PREPARE ps FROM "SELECT  AVG(c2)  OVER ( ) AS res FROM  te2";
EXECUTE ps;
res
1351614464.0000
EXECUTE ps;
res
1351614464.0000
EXECUTE ps;
res
1351614464.0000
CREATE PROCEDURE p1() SELECT  AVG(c2)  OVER ( ) AS res FROM  te2;
CALL p1();
res
1351614464.0000
CALL p1();
res
1351614464.0000
CALL p1();
res
1351614464.0000
CREATE FUNCTION f1()
RETURNS char(255)
BEGIN
DECLARE ret char(255);
SELECT  AVG(c2)  OVER ( ) AS res FROM  te2 INTO ret;
RETURN ret;
END!
SELECT f1();
f1()
1351614464
SELECT f1();
f1()
1351614464
SELECT f1();
f1()
1351614464
DROP FUNCTION f1;
DROP PROCEDURE p1;
DROP TABLE te2;
#
# Bug#26129809 WL#9603: VALGRIND REPORTS INVALID READ AT MY_STRNNCOLLSP_SIMPLE|CTYPE-SIMPLE.CC
#
set sql_mode='';
CREATE TABLE `A` (
`col_varchar_10_utf8` varchar(10) CHARACTER SET utf8mb3 DEFAULT NULL,
`col_varchar_10_latin1_key` varchar(10) DEFAULT NULL,
`col_int` int(11) DEFAULT NULL,
`col_date` date DEFAULT NULL,
`col_date_key` date DEFAULT NULL,
`col_varchar_255_utf8` varchar(255) CHARACTER SET utf8mb3 DEFAULT NULL,
`pk` int(11) NOT NULL AUTO_INCREMENT,
`col_varchar_255_latin1` varchar(255) DEFAULT NULL,
`col_datetime_key` datetime DEFAULT NULL,
`col_varchar_10_utf8_key` varchar(10) CHARACTER SET utf8mb3 DEFAULT NULL,
`col_varchar_255_utf8_key` varchar(255) CHARACTER SET utf8mb3 DEFAULT NULL,
`col_varchar_10_latin1` varchar(10) DEFAULT NULL,
`col_datetime` datetime DEFAULT NULL,
`col_varchar_255_latin1_key` varchar(255) DEFAULT NULL,
`col_int_key` int(11) DEFAULT NULL,
PRIMARY KEY (`pk`),
KEY `col_varchar_10_latin1_key` (`col_varchar_10_latin1_key`),
KEY `col_date_key` (`col_date_key`),
KEY `col_datetime_key` (`col_datetime_key`),
KEY `col_varchar_10_utf8_key` (`col_varchar_10_utf8_key`),
KEY `col_varchar_255_utf8_key` (`col_varchar_255_utf8_key`),
KEY `col_varchar_255_latin1_key` (`col_varchar_255_latin1_key`),
KEY `col_int_key` (`col_int_key`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `A` VALUES ('nznrrizpfk','znrrizpfkx',66,'2006-07-12','2004-06-08','nrrizpfkxceksatefqsdksjijcszxwbjjvvkymalukqukkoeiwsgpmfyvvuqvtjncdsvqhlhtrovamzqrdcenchyhuoowityzgktbkjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrgeozxnbycjzxiecurgmhbubpijrllquntppirzdphpduhwqhnsmujjjbldnkiwsrsxndolpujdnozrhh',1,'TWLFR',NULL,'CROGF','the','back','0000-00-00 00:00:00','LHIBY',217),('c','GQPFO',28,NULL,'0000-00-00','well',2,'or',NULL,'rrizpfkxce','rizpfkxceksatef','izpfkxceks','0000-00-00 00:00:00','zpfkxceksatefqsdksjijcszxwbjjvvkymalukqukkoeiwsgpmfyvvuqvtjncdsvqhlhtrovamzqrdcenchyhuoowityzgktbkjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrgeozxnbycjzxiecurgmhbubpijrllquntppirzdphpduhwqhnsmujjjbldnkiwsrsxndolpujdnozrhhcxsx',179),('but','XXDMG',44,'2002-09-18',NULL,'some',3,'come','2004-10-05 00:00:00','pfkxceksat','it','p','2001-02-20 00:00:00','YOTTT',238),('fkxceksate','WVXCD',168,'2009-12-23','2005-04-16','kxceksatefqsdksjijcszxwbjjvvkymalukqukkoeiwsgpmfyvvuqvtjncdsvqhlhtrovamzqrdcenchyhuoowityzgktbkjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrgeozxnbycjzxiecurgmhbubpijrllquntppirzdphpduhwqhnsmujjjbldnkiwsrsxndolpujdnozrhhcxsxwuj',4,'SJYOI',NULL,'y','xceksatefqsdksjijcszxwbjjvvkymalukqukkoeiwsgpmfyvvuqvtjncdsvqhlhtrovamzqrdcenchyhuoowityzgktbkjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrgeozxnbycjzxiecurgmhbubpijrllquntppirzdphpduhwqhnsmujjjbldnkiwsrsxndolpujdnozrhhcxsxwuju','have','0000-00-00 00:00:00','ceksatefqsdksjijcszxwbjjvvkymalukqukkoeiwsgpmfyvvuqvtjncdsvqhlhtrovamzqrdcenchyhuoowityzgktbkjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrgeozxnbycjzxiecurgmhbubpijrllquntppirzdphpduhwqhnsmujjjbldnkiwsrsxndolpujdnozrhhcxsxwujul',99),('just','i',0,'2003-11-07','0000-00-00','come',5,'can\'t','2006-03-17 18:41:01','not','eksatefqsdksjijcszxwbjjvvkymalukqukkoeiwsgpmfyvvuqvtjncdsvqhlhtrovamzqrdcenchyhuoowityzgktbkjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrgeozxnbycjzxiecurgmhbubpijrllquntppirzdphpduhwqhnsmujjjbldnkiwsrsxndolpujdnozrhhcxsxwujulp','ksatefqsdk','2000-11-05 22:46:38','k',75);
CREATE TABLE `AA` (
`col_datetime` datetime DEFAULT NULL,
`col_date` date DEFAULT NULL,
`col_varchar_255_latin1` varchar(255) DEFAULT NULL,
`col_varchar_255_utf8_key` varchar(255) CHARACTER SET utf8mb3 DEFAULT NULL,
`col_datetime_key` datetime DEFAULT NULL,
`col_int` int(11) DEFAULT NULL,
`col_varchar_255_utf8` varchar(255) CHARACTER SET utf8mb3 DEFAULT NULL,
`col_date_key` date DEFAULT NULL,
`col_varchar_255_latin1_key` varchar(255) DEFAULT NULL,
`col_varchar_10_utf8_key` varchar(10) CHARACTER SET utf8mb3 DEFAULT NULL,
`col_varchar_10_latin1_key` varchar(10) DEFAULT NULL,
`pk` int(11) NOT NULL AUTO_INCREMENT,
`col_varchar_10_latin1` varchar(10) DEFAULT NULL,
`col_varchar_10_utf8` varchar(10) CHARACTER SET utf8mb3 DEFAULT NULL,
`col_int_key` int(11) DEFAULT NULL,
PRIMARY KEY (`pk`),
KEY `col_varchar_255_utf8_key` (`col_varchar_255_utf8_key`),
KEY `col_datetime_key` (`col_datetime_key`),
KEY `col_date_key` (`col_date_key`),
KEY `col_varchar_255_latin1_key` (`col_varchar_255_latin1_key`),
KEY `col_varchar_10_utf8_key` (`col_varchar_10_utf8_key`),
KEY `col_varchar_10_latin1_key` (`col_varchar_10_latin1_key`),
KEY `col_int_key` (`col_int_key`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `AA` VALUES (NULL,'0000-00-00','b','r','2003-08-19 23:01:25',154,'zgktbkjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrgeozxnbycjzxiecurgmhbubpijrllquntppirzdphpduhwqhnsmujjjbldnkiwsrsxndolpujdnozrhhcxsxwujulpjdzvkpaijursprnwgrpquarwkazzjeiwvdmdivjqsxmhjwagewclcfykywlcnemiuaabrrifnhuufzasunkrcp','0000-00-00','is','gktbkjrkmq','o',1,'ktbkjrkmqm','tbkjrkmqmk',215),(NULL,'2007-05-17','e','VNIVI','2000-11-03 02:44:07',205,'u','0000-00-00','IXNLZ','bkjrkmqmkn','k',2,'hey','BDYIT',25),('2003-11-09 08:17:05','2000-12-18','DTSDH','ZPWFP','2005-05-15 11:04:35',1,'t','2005-12-07','kjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrge','CHSSV','AILLJ',3,'w','IRGCS',56),('2002-12-28 21:31:59','2006-12-11','jrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrgeozxnbycjzxiecurgmhbubpijrllquntppirzdphpduhwqhnsmujjjbldnkiwsrsxndolpujdnozrhhcxsxwujulpjdzvkpaijursprnwgrpquarwkazzjeiwvdmdivjqsxmhjwagewclcfykywlcnemiuaabrrifnhuufzasunkrcpvasdqk','c',NULL,55,'c','2009-07-13','w','b','o',4,'were','her',200),('0000-00-00 00:00:00','0000-00-00','e','FBJDG','0000-00-00 00:00:00',52,'WPOMI','2003-09-05','HZSNI','all','know',5,'KQHJW','rkmqmknbto',252);
CREATE TABLE `B` (
`pk` int(11) NOT NULL AUTO_INCREMENT,
`col_int` int(11) DEFAULT NULL,
`col_varchar_255_utf8_key` varchar(255) CHARACTER SET utf8mb3 DEFAULT NULL,
`col_datetime` datetime DEFAULT NULL,
`col_varchar_255_latin1_key` varchar(255) DEFAULT NULL,
`col_datetime_key` datetime DEFAULT NULL,
`col_varchar_255_utf8` varchar(255) CHARACTER SET utf8mb3 DEFAULT NULL,
`col_varchar_10_utf8_key` varchar(10) CHARACTER SET utf8mb3 DEFAULT NULL,
`col_varchar_10_latin1_key` varchar(10) DEFAULT NULL,
`col_date` date DEFAULT NULL,
`col_varchar_10_utf8` varchar(10) CHARACTER SET utf8mb3 DEFAULT NULL,
`col_varchar_255_latin1` varchar(255) DEFAULT NULL,
`col_varchar_10_latin1` varchar(10) DEFAULT NULL,
`col_int_key` int(11) DEFAULT NULL,
`col_date_key` date DEFAULT NULL,
PRIMARY KEY (`pk`),
KEY `col_varchar_255_utf8_key` (`col_varchar_255_utf8_key`),
KEY `col_varchar_255_latin1_key` (`col_varchar_255_latin1_key`),
KEY `col_datetime_key` (`col_datetime_key`),
KEY `col_varchar_10_utf8_key` (`col_varchar_10_utf8_key`),
KEY `col_varchar_10_latin1_key` (`col_varchar_10_latin1_key`),
KEY `col_int_key` (`col_int_key`),
KEY `col_date_key` (`col_date_key`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO `B` VALUES (1,229,'a','2003-05-12 00:00:00','x','2002-07-16 00:00:00','p','satefqsdks','can\'t','0000-00-00','atefqsdksj','tefqsdksjijcszxwbjjvvkymalukqukkoeiwsgpmfyvvuqvt','my',92,NULL),(2,159,'efqsdksjijcszxwbjjvvkymalukqukkoeiwsgpmfyvvuqvtjncdsvqhlhtrovamzqrdcenchyhuoowityzgktbkjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrgeozxnbycjzxiecurgmhbubpijrllquntppirzdphpduhwqhnsmujjjbldnkiwsrsxndolpujdnoz','0000-00-00 00:00:00','fqsdksjijcszxwbjjvvkymalukqukkoeiwsgpmfyvvuqvtjncdsvqhlhtrovamzqrdcenchyhuoowityzgktbkjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrgeozxnbycjzxiecurgmhbubpijrllquntppirzdphpduhwqhnsmujjjbldnkiwsrsxnd','2007-04-08 09:43:38','her','qsdksjijcs','sdksjijcsz','2006-03-26','VXECY','why','JNAFV',70,'0000-00-00'),(3,42,'dksjijcszxwbjjvvkymalukqukkoeiwsgpmfyvvuqvtjncdsvqhlhtrovamzqrdcenchyhuoowityzgktbkjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrgeozxnbycjzxiecurgmhbubpijrllquntppirzdphpduhwqhnsmujjjbldnkiwsrsxndolpujdnozrhhcxsxwujulpjdzvkpaij','2009-02-12 04:14:15','back','2000-07-26 07:47:30','r','go','ksjijcszxw','0000-00-00','s','o','sjijcszxwb',200,'0000-00-00'),(4,146,'jijcszxwbjjvvkymalukqukkoeiwsgpmfyvvuqvtjncdsvqhlhtrovamzqrdcenchyhuoowityzgktbkjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrgeozxnbycjzxiecurgmhbubpijrllquntppirzdphpduhwqhnsmujjjbldnkiwsrsxndolpujdnozrhhcxsxwujulpjdzvkpaijurs','0000-00-00 00:00:00','EXVCL','0000-00-00 00:00:00','DFVNQ','ijcszxwbjj','NMTOX','2004-12-17','p','jcszxwbjjvvkymalukqukkoeiwsgpmfyvvuqvtjncdsvqhlhtrovamzqrdcenchyhuoowityzgktbkjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyciakhxptzfpjwrgeozxnbycjzxiecurgmhbubpijrllquntppirzdphpduhwqhnsmujjjbldnkiwsrsxndolpujdnozrhhcxsxwujulpjdzvkpaijurspr','i',104,'2002-03-06'),(5,235,'i','0000-00-00 00:00:00','he\'s','2000-01-06 20:32:48','cszxwbjjvvkymalukqukkoeiwsgpmfyvvuqvtjncdsvqhlhtrovamzqrdcenchyhuoowityzgktbkjrkmqmknbtoervqlzsvasurqdhucjxdoygxqxnvgqmwcidtumxwcftedxqyci','YGVHA','EFEOY','2000-01-18','szxwbjjvvk','oh','not',239,'2001-07-22');
ALTER TABLE A ADD COLUMN col_varchar_10_latin1_gckey VARCHAR(20) GENERATED ALWAYS AS (CONCAT(col_varchar_10_latin1_key,'x')) VIRTUAL;
ALTER TABLE B ADD COLUMN col_int_gckey INT GENERATED ALWAYS AS (col_int_key + col_int_key) VIRTUAL;
ALTER TABLE B ADD COLUMN col_varchar_255_utf8_gckey TEXT GENERATED ALWAYS AS (CONCAT(col_varchar_255_utf8_key,'x')) VIRTUAL;
set sql_mode=default;
SELECT
PERCENT_RANK() OVER ( PARTITION BY  alias1 . `col_int`,
alias2 . `col_int`,
alias1 . `col_int_key`,
alias1 . `col_varchar_10_utf8_key`
                         ORDER BY  alias1 . `col_varchar_255_utf8_key`,
alias1 . `col_varchar_10_latin1_gckey`,
alias2 . `col_varchar_255_utf8_gckey`,
alias1 . `pk`
                         ROWS 430 PRECEDING  ) AS pr
FROM  A AS alias1  RIGHT OUTER JOIN
B AS alias2
LEFT  JOIN
AA AS alias3
ON  alias2 . `col_int_key` =  alias3 . `pk`
         ON  alias1 . `pk` =  alias2 . `col_int_gckey`
   WHERE  alias2 . `pk` > 5 AND alias2 . `pk` <= ( 5 + 5 )
AND alias2 . `pk` > 5 AND alias2 . `pk` <= ( 5 + 2 )
AND alias3 . `pk`  IN (5, 0)
AND alias3 . `pk` >= 5
AND alias3 . `pk` < ( 8 + 4 )
AND alias1 . `col_int` >= 5
AND alias1 . `col_int` <= ( 4 + 0 )
OR  alias2 . `col_int` != 5
OR  alias3 . `col_int` <> 7
OR  alias3 . `col_int` >= 5
AND alias3 . `col_int` < ( 5 + 2 )
ORDER BY pr  LIMIT 1000 OFFSET 4;
pr
0
DROP TABLE `A`, `AA`, `B`;
#
# Bug#26114804: WL#9603: ER_WINDOW_NESTED_WINDOW_FUNC_USE_IN_WINDOW_SPEC
#               FOR NESTED AGGREGATE
CREATE TABLE t (a INT, b INT);
INSERT INTO t VALUES(1,2),(3,4),(4,6),(4,7);
SELECT SUM(a) OVER (ORDER BY SUM(a)) FROM t GROUP BY a;
SUM(a) OVER (ORDER BY SUM(a))
1
4
8
SELECT SUM(a) OVER (ORDER BY 1+SUM(a)) FROM t GROUP BY a;
SUM(a) OVER (ORDER BY 1+SUM(a))
1
4
8
SELECT SUM(a) OVER (PARTITION BY 1+SUM(a)) FROM t GROUP BY a;
SUM(a) OVER (PARTITION BY 1+SUM(a))
1
3
4
SELECT SUM(a) OVER (ORDER BY AVG(a)) FROM t GROUP BY a,b;
SUM(a) OVER (ORDER BY AVG(a))
1
4
12
12
SELECT SUM(a) OVER (ORDER BY SUM(b)) FROM t GROUP BY a,b;
SUM(a) OVER (ORDER BY SUM(b))
1
4
8
12
DROP TABLE t;
#
# Bug#26162009: WL#9603: WRONG RESULT WITH MULTIPLE EXECUTIONS OF A
#               STORED PROGRAM
CREATE TABLE t (
col_int INT,
pk INT NOT NULL AUTO_INCREMENT,
col_int_key INT GENERATED ALWAYS AS (col_int+col_int)
VIRTUAL, PRIMARY KEY (pk));
INSERT INTO t (col_int, pk) VALUES
(7,6),(6,5),(4,4),(3,3),(2,2),(1,1);
PREPARE ps FROM  "SELECT AVG(col_int_key) OVER () FROM t
WHERE pk=3 GROUP BY col_int_key";
EXECUTE ps;
AVG(col_int_key) OVER ()
6.0000
EXECUTE ps;
AVG(col_int_key) OVER ()
6.0000
PREPARE ps FROM "SELECT AVG(12) OVER () FROM dual";
EXECUTE ps;
AVG(12) OVER ()
12.0000
EXECUTE ps;
AVG(12) OVER ()
12.0000
DROP PREPARE ps;
DROP TABLE t;
#
# Bug#26174648: WL9603: ASSERTION FAILURE IN ITEM_SUM_SUM::VAL_INT
#
SELECT 0 & (SUM(1) OVER w) FROM (select 1) as dt  WINDOW w as ();
0 & (SUM(1) OVER w)
0
SELECT 1 & (SUM(1) OVER w) FROM (select 1) as dt  WINDOW w as ();
1 & (SUM(1) OVER w)
1
#
# Bug#26178061: WL#9603: RANK AND UNCORRELATED SUBQUERY: WRONG RESULT
#
CREATE TABLE t(a int);
INSERT INTO t VALUES
(NULL),(NULL),(NULL),(NULL),(NULL),
(NULL),(NULL),(NULL),(NULL),(NULL),
(-1802764288),(-1438121984),(-1237843968),
(6),(9),(10),(11),(12),(13),(15),
(476839936),(780206080),(1887961088);
CREATE TABLE twf AS SELECT RANK() OVER ( ORDER BY a ) AS rnk FROM t  GROUP BY a;
SELECT RANK() OVER ( ORDER BY a ) AS rnk FROM t GROUP BY a;
rnk
1
2
3
4
5
6
7
8
9
10
11
12
13
14
SELECT * FROM twf WHERE rnk IN (SELECT RANK() OVER ( ORDER BY a ) AS rnk FROM
t  GROUP BY a);
rnk
1
2
3
4
5
6
7
8
9
10
11
12
13
14
SELECT * FROM twf WHERE rnk NOT IN (SELECT RANK() OVER ( ORDER BY a ) AS rnk
FROM t  GROUP BY a);
rnk
DROP TABLE twf,t;
#
# Bug#26802696: DENSE_RANK WRONG WITH BUFFERED PROCESSING
#
CREATE TABLE employee(department_id INT, salary INT);
INSERT INTO employee VALUES (10, NULL),
(10, 100000),
(10, 60000),
(10, 60000),
(10, 70000),
(20, 80000),
(20, 65000),
(20, 65000),
(30, 300000),
(30, 70000),
(NULL, 75000);
with partitions
SELECT department_id, salary,
RANK() OVER w AS rnk,
DENSE_RANK() OVER w AS dense,
NTILE(4) over w AS ntil,
CUME_DIST() OVER w AS cume,
PERCENT_RANK() over w AS `%rnk`
  FROM employee
WINDOW w AS (PARTITION BY department_id
ORDER BY salary DESC)
ORDER BY department_id, salary DESC, ntil;
department_id	salary	rnk	dense	ntil	cume	%rnk
NULL	75000	1	1	1	1	0
10	100000	1	1	1	0.2	0
10	70000	2	2	1	0.4	0.25
10	60000	3	3	2	0.8	0.5
10	60000	3	3	3	0.8	0.5
10	NULL	5	4	4	1	1
20	80000	1	1	1	0.3333333333333333	0
20	65000	2	2	2	1	0.5
20	65000	2	2	3	1	0.5
30	300000	1	1	1	0.5	0
30	70000	2	2	2	1	1
just default partition
SELECT department_id, salary,
RANK() OVER w AS rnk,
DENSE_RANK() OVER w AS dense,
NTILE(4) over w AS ntil,
CUME_DIST() OVER w AS cume,
PERCENT_RANK() over w AS `%rnk`
  FROM employee
WINDOW w AS (ORDER BY salary DESC)
ORDER BY salary DESC, department_id, ntil;
department_id	salary	rnk	dense	ntil	cume	%rnk
30	300000	1	1	1	0.09090909090909091	0
10	100000	2	2	1	0.18181818181818182	0.1
20	80000	3	3	1	0.2727272727272727	0.2
NULL	75000	4	4	2	0.36363636363636365	0.3
10	70000	5	5	2	0.5454545454545454	0.4
30	70000	5	5	2	0.5454545454545454	0.4
20	65000	7	6	3	0.7272727272727273	0.6
20	65000	7	6	3	0.7272727272727273	0.6
10	60000	9	7	3	0.9090909090909091	0.8
10	60000	9	7	4	0.9090909090909091	0.8
10	NULL	11	8	4	1	1
DROP TABLE employee;
#
# Bug#26500442: WINDOW FUNCTIONS: CRASH IN WINDOW::HAS_WINDOWING_STEPS
#
CREATE TABLE t(a INT);
INSERT INTO t VALUES (1), (2), (3), (4);
SELECT ISNULL(COS(RANK() OVER())) FROM t;
ISNULL(COS(RANK() OVER()))
0
0
0
0
DROP TABLE t;
#
# Bug#26164633 WL#9603: WRONG RESULT WHEN PARTITION EXPR USING AGGREGATES EVALUATES TO NULL
#
CREATE TABLE t1 (
pk int NOT NULL DEFAULT '0',
col_int int DEFAULT NULL,
col_int_key int DEFAULT NULL
);
INSERT INTO t1 VALUES
(1,4,858718208),          (2,-28508160,723386368),
(3,6,3),                  (4,828112896,-409141248),
(5,1454702592,856424448), (6,7,7),
(7,2,4),                  (8,7,4),
(9,1990590464,8),         (10,-538705920,2),
(11,7,2002124800),        (12,776273920,-1472200704),
(13,7,8),                 (14,0,952041472),
(15,9,-427819008),        (16,8,-686096384),
(17,397934592,7),         (18,-768671744,6),
(19,3,5),                 (20,1533739008,5),
(21,1,-1301872640),       (22,798425088,4),
(23,5,-561971200),        (24,9,-1901854720),
(25,9,811401216),         (26,1856700416,0),
(27,7,4),                 (28,9,-1491992576),
(29,9,1),                 (30,1724252160,7);
SELECT MIN(table2.pk) + table2.col_int AS part_expr,
DENSE_RANK() OVER (PARTITION BY MIN(table2.pk) + table2.col_int
ORDER BY  table1.col_int_key) AS field1
FROM  t1 AS table1 LEFT JOIN t1 AS table2
ON table1.pk = table2.col_int
GROUP BY table2.col_int, table1.col_int_key;
part_expr	field1
NULL	1
NULL	2
NULL	3
NULL	4
NULL	5
NULL	6
NULL	7
NULL	8
NULL	9
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
NULL	15
NULL	16
NULL	17
NULL	18
5	1
9	1
9	2
13	1
22	1
22	2
24	1
24	2
28	1
DROP TABLE t1;
#
# Bug#26188578 - WL#9603: HAVING CONDITION IS OPTIMIZED OUT FOR ALIAS ON AGGREGATE W/O GROUP BY
#
CREATE TABLE E (
col_int int(11) DEFAULT NULL,
col_varchar_10_utf8 varchar(10) CHARACTER SET utf8mb3 DEFAULT NULL,
pk int(11) NOT NULL AUTO_INCREMENT,
col_varchar_10_latin1 varchar(10) DEFAULT NULL,
col_varchar_255_utf8 varchar(255) CHARACTER SET utf8mb3 DEFAULT NULL,
col_varchar_255_latin1 varchar(255) DEFAULT NULL,
col_int_key int(11) GENERATED ALWAYS AS ((col_int + col_int)) VIRTUAL,
col_varchar_255_utf8_key varchar(255) CHARACTER SET utf8mb3
GENERATED ALWAYS AS
(concat(repeat(col_varchar_10_utf8,3),repeat(col_varchar_10_utf8,3)))
VIRTUAL,
col_varchar_255_latin1_key varchar(255)
GENERATED ALWAYS AS
(concat(repeat(col_varchar_10_latin1,3),repeat(col_varchar_10_latin1,3)))
VIRTUAL,
col_varchar_10_utf8_key varchar(10) CHARACTER SET utf8mb3
GENERATED ALWAYS AS
(repeat(substr(col_varchar_10_utf8,-(1)),5)) VIRTUAL,
col_varchar_10_latin1_key varchar(10) GENERATED ALWAYS AS
(repeat(substr(col_varchar_10_latin1,-(1)),7)) VIRTUAL,
PRIMARY KEY (pk DESC),
UNIQUE KEY ucover_key1
(pk DESC, col_int_key DESC, col_varchar_255_utf8_key DESC,
col_varchar_255_latin1_key DESC, col_varchar_10_utf8_key DESC,
col_varchar_10_latin1_key DESC),
UNIQUE KEY ucover_key2
(pk, col_int_key, col_varchar_255_utf8_key, col_varchar_255_latin1_key,
col_varchar_10_utf8_key, col_varchar_10_latin1_key),
KEY col_int_key (col_int_key DESC),
KEY col_varchar_255_utf8_key (col_varchar_255_utf8_key DESC),
KEY col_varchar_255_latin1_key (col_varchar_255_latin1_key DESC),
KEY col_varchar_10_utf8_key (col_varchar_10_utf8_key DESC),
KEY col_varchar_10_latin1_key (col_varchar_10_latin1_key),
KEY multi_key1 (col_int_key DESC, col_varchar_10_latin1_key),
KEY multi_key1a (col_int_key DESC, col_varchar_10_latin1_key DESC),
KEY multi_key2 (col_int_key,col_int DESC),
KEY multi_key3 (col_varchar_255_utf8_key DESC, col_varchar_255_utf8),
KEY multi_key4 (col_varchar_255_latin1_key, col_varchar_255_latin1),
KEY multi_key5 (pk DESC,col_int_key DESC, col_varchar_10_latin1_key),
KEY cover_key1
(pk DESC,col_int_key DESC, col_varchar_255_utf8_key,
col_varchar_255_latin1_key DESC, col_varchar_10_utf8_key DESC,
col_varchar_10_latin1_key)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO E (col_int, col_varchar_10_utf8, pk, col_varchar_10_latin1,
col_varchar_255_utf8, col_varchar_255_latin1)
VALUES
(NULL,NULL,10,'this','really','mxwcftedxq'),
(22216704,'159973376',9,'l','mean','cidtumxwcf'),
(-693376,'1',8,'of','gqmwcidtum','qmwcidtumx'),
(-183840,'5',7,'xnvgqmwcid','b','nvgqmwcidt'),
(2,NULL,6,'is','f','gxqxnvgqmw'),
(4,NULL,5,'xdoygxqxnv','h','good'),
(3,'1074462720',4,'z','cjxdoygxqx','m'),
(-584581120,'-1176634',3,'urqdhj','rhjxdo','but'),
(-19295040,'1235025920',2,'svasurqdhu','can','a'),
(9,'951910400',1,'qlzsvasurq','lzsvasurqd','in');
CREATE TABLE C (
col_int int(11) DEFAULT NULL,
col_varchar_10_utf8 varchar(10) CHARACTER SET utf8mb3 DEFAULT NULL,
pk int(11) NOT NULL AUTO_INCREMENT,
col_varchar_10_latin1 varchar(10) DEFAULT NULL,
col_varchar_255_utf8 varchar(255) CHARACTER SET utf8mb3 DEFAULT NULL,
col_varchar_255_latin1 varchar(255) DEFAULT NULL,
col_int_key int(11) GENERATED ALWAYS AS ((col_int + col_int)) VIRTUAL,
col_varchar_255_utf8_key varchar(255) CHARACTER SET utf8mb3
GENERATED ALWAYS AS
(concat(repeat(col_varchar_10_utf8,3),repeat(col_varchar_10_utf8,3)))
VIRTUAL,
col_varchar_255_latin1_key varchar(255) GENERATED ALWAYS AS
(concat(repeat(col_varchar_10_latin1,3),repeat(col_varchar_10_latin1,3)))
VIRTUAL,
col_varchar_10_utf8_key varchar(10) CHARACTER SET utf8mb3 GENERATED ALWAYS AS
(repeat(substr(col_varchar_10_utf8,-(1)),5)) VIRTUAL,
col_varchar_10_latin1_key varchar(10) GENERATED ALWAYS AS
(repeat(substr(col_varchar_10_latin1,-(1)),7)) VIRTUAL,
PRIMARY KEY (pk DESC),
UNIQUE KEY ucover_key1
(pk DESC, col_int_key DESC, col_varchar_255_utf8_key DESC,
col_varchar_255_latin1_key DESC, col_varchar_10_utf8_key DESC,
col_varchar_10_latin1_key DESC),
UNIQUE KEY ucover_key2
(pk, col_int_key, col_varchar_255_utf8_key, col_varchar_255_latin1_key,
col_varchar_10_utf8_key, col_varchar_10_latin1_key),
KEY col_int_key (col_int_key DESC),
KEY col_varchar_255_utf8_key (col_varchar_255_utf8_key DESC),
KEY col_varchar_255_latin1_key (col_varchar_255_latin1_key DESC),
KEY col_varchar_10_utf8_key (col_varchar_10_utf8_key DESC),
KEY col_varchar_10_latin1_key (col_varchar_10_latin1_key),
KEY multi_key1 (col_int_key DESC, col_varchar_10_latin1_key),
KEY multi_key1a (col_int_key DESC, col_varchar_10_latin1_key DESC),
KEY multi_key2 (col_int_key, col_int DESC),
KEY multi_key3 (col_varchar_255_utf8_key DESC, col_varchar_255_utf8),
KEY multi_key4 (col_varchar_255_latin1_key, col_varchar_255_latin1),
KEY multi_key5
(pk DESC, col_int_key DESC, col_varchar_10_latin1_key),
KEY cover_key1
(pk DESC, col_int_key DESC, col_varchar_255_utf8_key,
col_varchar_255_latin1_key DESC, col_varchar_10_utf8_key DESC,
col_varchar_10_latin1_key)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO C
(col_int, col_varchar_10_utf8, pk, col_varchar_10_latin1,
col_varchar_255_utf8, col_varchar_255_latin1)
VALUES
(5,'pmfyvvuqvt',6,'so','he','mfyvvuq'),
(5,'q',5,'iwsgpmfyvv','wsgpmf','s'),
(9,'oeiwsgpmfy',4,'at','can','come'),
(-108947046,'kkoeiwsgpm',3,'koeiwsgpmf','o','t'),
(NULL,'p',2,'ukkoeiwsgp','now','is'),
(NULL,'now',1,'up','u','u');
CREATE VIEW view_E AS SELECT * FROM E;
SELECT  MAX( alias1 .col_int )  AS field1  FROM view_E  AS alias1  LEFT
JOIN C  AS alias2  ON alias1 .col_int  = alias2 .pk  WHERE alias1 .pk
IN (  5  )  HAVING field1  <=  6;
field1
4
SELECT * FROM (SELECT MAX( alias1.col_int) AS field1 FROM view_E AS alias1
LEFT JOIN C                                       AS alias2
ON alias1 .col_int  = alias2 .pk
WHERE alias1 .pk IN (5)  HAVING field1  <=  6) s1;
field1
4
SELECT * FROM (SELECT MAX( alias1.col_int) AS field1 FROM view_E AS alias1
LEFT JOIN C                                       AS alias2
ON alias1 .col_int  = alias2 .pk
WHERE alias1 .pk IN (5)  HAVING MAX(alias1 .col_int) <=  6) s1;
field1
4
DROP TABLE E,C;
DROP VIEW view_E;
#
# Bug #26496733: WINDOW FUNCTIONS: ASSERT FALSE WITH JSON COLUMN
#
SET @savmode=@@SESSION.SQL_MODE;
SET SQL_MODE='';
CREATE TABLE t(a JSON NOT NULL);
INSERT INTO t VALUES();
Warnings:
Warning	1364	Field 'a' doesn't have a default value
SELECT PERCENT_RANK() OVER (ORDER BY a RANGE CURRENT ROW) FROM t;
PERCENT_RANK() OVER (ORDER BY a RANGE CURRENT ROW)
0
SET SESSION SQL_MODE=@savmode;
DROP TABLE t;
# End of test for Bug#26496733
#
# Bug#26497353: ASSERTION FAILED: M_OPENED_TABLE != NULLPTR
#
CREATE TABLE t(a INTEGER);
INSERT INTO t VALUES (1), (2), (3);
SELECT DENSE_RANK() OVER w2 FROM t GROUP BY 'a' WITH ROLLUP
WINDOW w2 AS (ROWS UNBOUNDED PRECEDING);
DENSE_RANK() OVER w2
1
1
DROP TABLE t;
# End of test for Bug#26497353
#
# Bug #26496645: WINDOW FUNCTIONS: CRASH IN
#                WINDOW::RESTORE_SPECIAL_RECORD
#
CREATE TABLE t (
b VARCHAR(20) DEFAULT NULL,
d INT DEFAULT NULL);
INSERT INTO t VALUES(1,-1);
INSERT INTO t VALUES(1,-1);
SELECT b,d,LAST_VALUE(b) OVER(ORDER BY d RANGE BETWEEN 1 FOLLOWING AND
UNBOUNDED FOLLOWING) FROM t;
b	d	LAST_VALUE(b) OVER(ORDER BY d RANGE BETWEEN 1 FOLLOWING AND
UNBOUNDED FOLLOWING)
1	-1	NULL
1	-1	NULL
DROP TABLE t;
# End of test for Bug#26496645
#
# Bug#26411055: WINDOW FRAME ACCEPTS NON-INTEGER ARG FOR ROWS
#
CREATE TABLE t(w INT);
INSERT INTO t VALUES (1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT w, SUM(w) OVER (ROWS 3.14 PRECEDING) FROM t;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
SELECT w, SUM(w) OVER (ROWS BETWEEN 3 PRECEDING AND 3.4 FOLLOWING) FROM t;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
SELECT w, SUM(w) OVER (ROWS BETWEEN CURRENT ROW AND 3.4 FOLLOWING) FROM t;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
PREPARE stmt FROM "SELECT w, SUM(w) OVER (ROWS 3.14 PRECEDING) FROM t";
EXECUTE stmt;
ERROR HY000: Window '<unnamed window>': frame start or end is negative, NULL or of non-integral type
PREPARE stmt FROM "SELECT w, SUM(w) OVER (ROWS ? PRECEDING) FROM t";
SET @p1= 2;
EXECUTE stmt USING @p1;
w	SUM(w) OVER (ROWS ? PRECEDING)
1	1
2	3
3	6
4	9
5	12
6	15
7	18
8	21
9	24
SET @p1= 2.3;
EXECUTE stmt USING @p1;
ERROR HY000: Incorrect arguments to EXECUTE
DROP PREPARE stmt;
DROP TABLE t;
# End of test for Bug#26411055
#
# Bug#26497247:WINDOW FUNCTIONS: CRASH IN DO_COPY_MAYBE_NULL
#
CREATE TABLE t(a INTEGER NOT NULL);
INSERT INTO t VALUES ('1'), ('2'), ('3');
SELECT LEAD(a,1) OVER() FROM t GROUP BY a WITH ROLLUP;
LEAD(a,1) OVER()
2
3
NULL
NULL
DROP TABLE t;
#End of test for Bug#26497247
#
# Bug #26496880: CRASH IN FIELD_BLOB::GET_KEY_IMAGE
#
CREATE TABLE t(
a INTEGER,
b BLOB,
PRIMARY KEY (b(1))
);
INSERT INTO t(b) VALUES('a'),('b'),('c'),('d');
FLUSH TABLES;
SELECT LEAD(1,1,1) OVER(PARTITION BY a) FROM t;
LEAD(1,1,1) OVER(PARTITION BY a)
1
1
1
1
DROP TABLE t;
#End of test for Bug#26496880
#
# Bug#26730020 REEXECUTE PREPARED STATEMENT CRASH WITH WINDOW FUNCTIONS
#
CREATE TABLE t(a INT);
PREPARE s FROM 'DO (SELECT a FROM t WINDOW w2 AS (w1), w1 AS (ORDER BY a, a));';
EXECUTE s;
EXECUTE s;
DROP TABLE t;
#
# Bug#26907753 COMBINING CTE AND WINDOW FUNCTION GIVES WRONG RESULT
#
CREATE TABLE t1 (i INTEGER);
INSERT INTO t1 VALUES (1),(1),(2);
SELECT * FROM
(SELECT LEAD(i) OVER w AS a, i AS b FROM t1
WINDOW w AS
(ORDER BY i ROWS CURRENT ROW))
AS t WHERE a = b;
a	b
1	1
WITH t2(i) AS (SELECT i+1 FROM t1)
SELECT * FROM
(SELECT LEAD(i) OVER w AS a, i AS b FROM t2
WINDOW w AS
(ORDER BY i ROWS CURRENT ROW))
AS t3 WHERE a = b;
a	b
2	2
DROP TABLE t1;
#
# Bug#26813454 UNWARRANTED ERROR INDICATING WINDOW NAME NOT DEFINED
#
CREATE TABLE sales(id INT AUTO_INCREMENT PRIMARY KEY, `date` DATE, sale INT);
INSERT INTO sales(`date`, sale) VALUES
('2017-03-01', 200),
('2017-04-01', 300),
('2017-05-01', 400),
('2017-06-01', 200),
('2017-07-01', 600),
('2017-08-01', 100),
('2017-03-01', 400),
('2017-04-01', 300),
('2017-05-01', 500),
('2017-06-01', 400),
('2017-07-01', 600),
('2017-08-01', 150);
SELECT MONTH(date), SUM(sale),
AVG(SUM(sale)) OVER w AS sliding_avg FROM sales GROUP BY MONTH(date)
WINDOW w AS (ORDER BY MONTH(date)
RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING)
ORDER BY AVG(SUM(sale)) OVER (ORDER BY MONTH(date)
RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING),
MONTH(date);
MONTH(date)	SUM(sale)	sliding_avg
3	600	600.0000
7	1200	683.3333
4	600	700.0000
5	900	700.0000
8	250	725.0000
6	600	900.0000
DROP TABLE sales;
#
# Bug#26739028: WINDOW FUNC + FROM_UNIXTIME CRASH
#
SELECT FROM_UNIXTIME(LAG('',99)OVER(ROWS UNBOUNDED PRECEDING),'%I %l %I');
FROM_UNIXTIME(LAG('',99)OVER(ROWS UNBOUNDED PRECEDING),'%I %l %I')
NULL
SELECT FROM_UNIXTIME(FIRST_VALUE(@b)RESPECT NULLS OVER(),
REPEAT('1',32))IS NOT FALSE;
FROM_UNIXTIME(FIRST_VALUE(@b)RESPECT NULLS OVER(),
REPEAT('1',32))IS NOT FALSE
1
SELECT ((NTILE(70)OVER())<<(FROM_UNIXTIME(LEAD('',67) RESPECT NULLS
OVER(),SHA(''))));
((NTILE(70)OVER())<<(FROM_UNIXTIME(LEAD('',67) RESPECT NULLS
OVER(),SHA(''))))
NULL
SELECT FROM_UNIXTIME(LAG('-778:36:16.905133',246) RESPECT NULLS OVER(),
REPLACE('%M%V   ','',''));
FROM_UNIXTIME(LAG('-778:36:16.905133',246) RESPECT NULLS OVER(),
REPLACE('%M%V   ','',''))
NULL
# End of test for Bug#26739028
#
# Bug#26389508: INT JOIN_READ_KEY(QEP_TAB*): ASSERTION
#               `!TABLE->HAS_NULL_ROW()' FAILED
#
CREATE TABLE t1 (
pk int(11) NOT NULL AUTO_INCREMENT,
col_int int(11) DEFAULT NULL,
PRIMARY KEY (pk)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES (1,NULL),(2,4),(3,-501481472),(4,NULL),(5,3);
CREATE TABLE t2 (
col_int_key int(11) DEFAULT NULL,
KEY col_int_key (col_int_key)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t2 VALUES (NULL),(NULL),(NULL),(NULL),(5),(5);
This JOIN needs the fix
SELECT FIRST_VALUE( alias1.pk ) OVER
(ROWS BETWEEN UNBOUNDED PRECEDING AND 1 FOLLOWING) AS field1
FROM  t1 AS alias1 RIGHT JOIN t2 AS alias2
ON  alias1.pk = alias2.col_int_key;
field1
NULL
NULL
NULL
NULL
NULL
NULL
This doesn't and shouldn't do save/restore it (not JT_EQ_REF). Verify
with debugger.
SELECT FIRST_VALUE( alias1.pk ) OVER
(ROWS BETWEEN UNBOUNDED PRECEDING AND 1 FOLLOWING) AS field1
FROM  t1 AS alias1 RIGHT JOIN t2 AS alias2
ON  alias1.pk > alias2.col_int_key;
field1
NULL
NULL
NULL
NULL
NULL
NULL
Nor this one (not first window). Verify with debugger.
SELECT ROW_NUMBER() OVER () AS `row#`,
FIRST_VALUE( alias1.pk ) OVER
(ROWS BETWEEN UNBOUNDED PRECEDING AND 1 FOLLOWING) AS field1
FROM  t1 AS alias1 RIGHT JOIN t2 AS alias2
ON  alias1.pk = alias2.col_int_key;
row#	field1
1	NULL
2	NULL
3	NULL
4	NULL
5	NULL
6	NULL
DROP TABLE t1, t2;
#
# Bug#26975882: CRASH IN STRING::COPY, USUALLY WITH WKB FUNCTIONS
#               / WINDOW FUNC
#
SELECT ST_BUFFER(ST_ASBINARY(1), ST_ASBINARY(NTH_VALUE('4714-05-04',1)OVER(),'axis-order=long-lat'));
ERROR 22023: Invalid GIS data provided to function st_aswkb.
# End of test for Bug#26975882
#
# Bug#26740557: WINDOW FUNC + JSON: ASSERTION FAILED:
#               FALSE IN ITEM::VAL_JSON
#
SELECT ((MAKETIME(((QUARTER('| !*c>*{/'))<=>
(FIRST_VALUE(JSON_OBJECTAGG('key4',0x067c13d0d0d7d8c8d768aef7)
)OVER())),'9236-05-27',0xe2a7d4))^(0x1109));
((MAKETIME(((QUARTER('| !*c>*{/'))<=>
(FIRST_VALUE(JSON_OBJECTAGG('key4',0x067c13d0d0d7d8c8d768aef7)
)OVER())),'9236-05-27',0xe2a7d4))^(0x1109))
NULL
Warnings:
Warning	1292	Incorrect datetime value: '| !*c>*{/'
Warning	1292	Incorrect datetime value: '| !*c>*{/'
Warning	1292	Truncated incorrect INTEGER value: '9236-05-27'
SELECT ((FIRST_VALUE(JSON_MERGE_PATCH(1.755913e+308,'{ }'))OVER())<=(1));
ERROR 22032: Invalid data type for JSON data in argument 1 to function json_merge_patch; a JSON string or JSON type is required.
SELECT ((QUOTE(JSON_KEYS(FIRST_VALUE(JSON_KEYS(EXP(-15676),ABS('d0'))
)OVER())))>=(CONNECTION_ID()));
ERROR 22032: Invalid data type for JSON data in argument 1 to function json_keys; a JSON string or JSON type is required.
SELECT JSON_LENGTH(FIRST_VALUE(JSON_OBJECTAGG('key2','*B'))OVER());
JSON_LENGTH(FIRST_VALUE(JSON_OBJECTAGG('key2','*B'))OVER())
1
# End of test for Bug#26740557
#
# Check for errors
#
CREATE TABLE t1 (i INTEGER);
SELECT AVG(i) FROM t1 WINDOW w AS (ORDER BY i);
ERROR 42000: In aggregated query without GROUP BY, expression #1 of PARTITION BY or ORDER BY clause of window 'w' contains nonaggregated column 'test.t1.i'; this is incompatible with sql_mode=only_full_group_by
SELECT AVG(i), RANK() OVER (ORDER BY i) FROM t1;
ERROR 42000: In aggregated query without GROUP BY, expression #1 of PARTITION BY or ORDER BY clause of window '<unnamed window>' contains nonaggregated column 'test.t1.i'; this is incompatible with sql_mode=only_full_group_by
SELECT AVG(i) FROM t1 ORDER BY RANK() OVER (PARTITION BY AVG(i) ORDER BY i);
ERROR 42000: In aggregated query without GROUP BY, expression #1 of PARTITION BY or ORDER BY clause of window '<unnamed window>' contains nonaggregated column 'test.t1.i'; this is incompatible with sql_mode=only_full_group_by
SELECT AVG(i), RANK() OVER w FROM t1 WINDOW w AS (ORDER BY i);
ERROR 42000: In aggregated query without GROUP BY, expression #1 of PARTITION BY or ORDER BY clause of window 'w' contains nonaggregated column 'test.t1.i'; this is incompatible with sql_mode=only_full_group_by
SELECT (SELECT AVG(i)+RANK() OVER (ORDER BY i)) FROM t1;
ERROR HY000: Outer reference test.t1.i in window's ORDER BY or PARTITION BY clause not allowed.
DROP TABLE t1;
# End of checking for errors
#
# Bug#27010574 WINDOW FUNCTIONS: BUG WITH LAST_VALUE AND FRAME
#
CREATE TABLE t(a INT, b INT);
INSERT INTO t VALUES (1,1),
(2,1),
(3,2),
(4,2),
(5,3),
(6,3);
SELECT a, b,
LAST_VALUE(a) OVER (ORDER BY b,a) AS `last` FROM t;
a	b	last
1	1	1
2	1	2
3	2	3
4	2	4
5	3	5
6	3	6
SELECT a, b,
LAST_VALUE(a) OVER (ORDER BY b,a
RANGE BETWEEN UNBOUNDED PRECEDING AND
CURRENT ROW) AS `last`
  FROM t;
a	b	last
1	1	1
2	1	2
3	2	3
4	2	4
5	3	5
6	3	6
INSERT INTO t VALUES (1,1),
(4,2),
(NULL, 2),
(NULL, NULL),
(2, NULL);
SELECT a, b, COUNT(a) OVER w AS cnt,
COUNT(*) OVER w AS `cnt(*)`,
FIRST_VALUE(a) OVER w AS first,
LAST_VALUE (a) OVER w AS last
FROM t WINDOW w AS (ORDER BY b,a DESC);
a	b	cnt	cnt(*)	first	last
2	NULL	1	1	2	2
NULL	NULL	1	2	2	NULL
2	1	2	3	2	2
1	1	4	5	2	1
1	1	4	5	2	1
4	2	6	7	2	4
4	2	6	7	2	4
3	2	7	8	2	3
NULL	2	7	9	2	NULL
6	3	8	10	2	6
5	3	9	11	2	5
SELECT a, b, COUNT(a) OVER w AS cnt,
COUNT(*) OVER w AS `cnt(*)`,
FIRST_VALUE(a) OVER w AS first,
LAST_VALUE (a) OVER w AS last
FROM t WINDOW w AS (ORDER BY b,a DESC
RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
a	b	cnt	cnt(*)	first	last
2	NULL	1	1	2	2
NULL	NULL	1	2	2	NULL
2	1	2	3	2	2
1	1	4	5	2	1
1	1	4	5	2	1
4	2	6	7	2	4
4	2	6	7	2	4
3	2	7	8	2	3
NULL	2	7	9	2	NULL
6	3	8	10	2	6
5	3	9	11	2	5
SELECT a, b, COUNT(a) OVER w AS cnt,
COUNT(*) OVER w AS `cnt(*)`,
FIRST_VALUE(a) OVER w AS first,
LAST_VALUE (a) OVER w AS last
FROM t WINDOW w AS (ORDER BY b,a DESC
RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING);
a	b	cnt	cnt(*)	first	last
2	NULL	9	11	2	5
NULL	NULL	8	10	NULL	5
2	1	8	9	2	5
1	1	7	8	1	5
1	1	7	8	1	5
4	2	5	6	4	5
4	2	5	6	4	5
3	2	3	4	3	5
NULL	2	2	3	NULL	5
6	3	2	2	6	5
5	3	1	1	5	5
DROP TABLE t;
#
# Bug#26975848: IFNULL: CRASHES WITH FIRST_VALUE
# Bug#27062796: WINDOW FUNC: CRASH IN STRING::COPY WITH WKB FUNCTIONS
# Bug#27062694: WINDOW FUNC: MUCH CRASHING WITH FIRST_VALUE/LAST_VALUE!!
#
SELECT IFNULL(JSON_TYPE(CASE WHEN(1) THEN(1) ELSE(1) END),
LAST_VALUE('')OVER());
ERROR 22032: Invalid data type for JSON data in argument 1 to function json_type; a JSON string or JSON type is required.
SELECT IFNULL((UUID_TO_BIN(0xAB8631)),(FIRST_VALUE(2.469566E+307)OVER()));
ERROR HY000: Incorrect string value: '\xAB\x861' for function uuid_to_bin
SELECT IFNULL((JSON_TYPE(@c)),(FIRST_VALUE(@b)OVER()));
IFNULL((JSON_TYPE(@c)),(FIRST_VALUE(@b)OVER()))
NULL
DO ST_WITHIN(ST_ASWKB(ST_ASBINARY(1,'axis-order=srid-defined')), ST_ASWKT(
ST_ASBINARY(LAST_VALUE(4732)OVER()),'axis-order=srid-defined'));
ERROR 22023: Invalid GIS data provided to function st_aswkb.
DO ST_OVERLAPS(ST_ASWKB(ST_ASBINARY(1,'axis-order=lat-long'),
'axis-order=srid-defined'), ST_ASWKT(LAST_VALUE('*7') OVER()));
ERROR 22023: Invalid GIS data provided to function st_aswkb.
DO ST_POINTN(ST_ASWKT(ST_ASWKT(ST_ASWKB(6565))),
ST_ASBINARY(LAST_VALUE(0xfd8b9af2bedb16c0d7f1cca63b5c9e) OVER()));
ERROR 22023: Invalid GIS data provided to function st_aswkb.
DO POW(-8714,REPEAT('1',32)) OR
VALIDATE_PASSWORD_STRENGTH(LAST_VALUE(1) OVER());
ERROR 22003: DOUBLE value is out of range in 'pow(-(8714),repeat('1',32))'
DO SUBSTRING_INDEX(((((RADIANS(0xBD)) && (ST_SRID(1, 4326)))) OR
(COT(UUID_SHORT()))), FIRST_VALUE(ROW_COUNT())RESPECT NULLS OVER(), 171);
ERROR 22023: Invalid GIS data provided to function st_srid.
DO ((JSON_TYPE('4:8')) AND (((ROLES_GRAPHML()) LIKE (FIRST_VALUE(31543)
RESPECT NULLS OVER()))));
ERROR 22032: Invalid JSON text in argument 1 to function json_type: "The document root must not be followed by other values." at position 1.
DO CONCAT_WS( JSON_OBJECTAGG( ROW_COUNT(), BIT_COUNT(-23163)),
DEGREES((1.595545e+308)), REPEAT(FIRST_VALUE('%0') OVER(), 30));
ERROR 22003: DOUBLE value is out of range in 'degrees(1.595545e+308)'
DO REPLACE((( RELEASE_LOCK( ACOS(0x41))) OR (1)), LAST_VALUE(9.750062e+306)
RESPECT NULLS OVER(), UUID_SHORT());
ERROR 42000: Incorrect user-level lock name 'NULL'. The name is empty, NULL, or can not be expressed in the current character-set.
# End of test for Bug#26975848, Bug#27062796, Bug#27062694
# Bug#31361428: Assertion `rowno != 0' failed. in bring_back_frame_row
CREATE TABLE t(a INTEGER);
INSERT INTO t VALUES(1),(2),(3),(4);
SELECT PERCENT_RANK() OVER(),
LAG(1,35,1) OVER w2
FROM t
WINDOW w1 AS (ROWS UNBOUNDED PRECEDING),
w2 AS (ROWS BETWEEN 18446744073709551615 FOLLOWING AND 1 FOLLOWING);
PERCENT_RANK() OVER()	LAG(1,35,1) OVER w2
0	1
0	1
0	1
0	1
SELECT PERCENT_RANK() OVER(),
LAG(1,35,1) OVER w2
FROM t
WINDOW w1 AS (ROWS UNBOUNDED PRECEDING),
w2 AS (ROWS BETWEEN 1 FOLLOWING AND 18446744073709551615 FOLLOWING);
PERCENT_RANK() OVER()	LAG(1,35,1) OVER w2
0	1
0	1
0	1
0	1
# Prove that 18446744073709551615 is not seen as -1:
SELECT a, SUM(a) OVER (ROWS BETWEEN
18446744073709551615 FOLLOWING AND 1 FOLLOWING)
FROM t;
a	SUM(a) OVER (ROWS BETWEEN
18446744073709551615 FOLLOWING AND 1 FOLLOWING)
1	NULL
2	NULL
3	NULL
4	NULL
SELECT a, SUM(a) OVER (ROWS BETWEEN
1 FOLLOWING AND 18446744073709551615 FOLLOWING)
FROM t;
a	SUM(a) OVER (ROWS BETWEEN
1 FOLLOWING AND 18446744073709551615 FOLLOWING)
1	9
2	7
3	4
4	NULL
DROP TABLE t;
#
# Bug#31361748 ASSERTION `W->FRAME_BUFFER()->S->DB_TYPE()->DB_TYPE..
#
# Minimal repro
CREATE TABLE t(a INT);
INSERT INTO t VALUES (3), (10), (100), (103);
SELECT a, COUNT(a) OVER (ORDER BY a RANGE
BETWEEN 4 FOLLOWING AND 6 FOLLOWING)
FROM t;
a	COUNT(a) OVER (ORDER BY a RANGE
BETWEEN 4 FOLLOWING AND 6 FOLLOWING)
3	0
10	0
100	0
103	0
DROP TABLE t;
# Original repro
CREATE TABLE t (a INT,b INT);
INSERT INTO t(a) VALUES(2147483647),(0),(-2),(-1),(0);
INSERT INTO t(a) VALUES(5430600),(0),(-91),(2147483647),(0);
INSERT INTO t(a) VALUES(-1),(0),(-26164),(37583),(-1),(79);
SELECT LAST_VALUE(b) RESPECT NULLS
OVER (PARTITION BY b ORDER BY a
RANGE BETWEEN 253 FOLLOWING AND 118 FOLLOWING)
FROM t;
LAST_VALUE(b) RESPECT NULLS
OVER (PARTITION BY b ORDER BY a
RANGE BETWEEN 253 FOLLOWING AND 118 FOLLOWING)
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL
DROP TABLE t;
#
# Bug#31393719: SIGFPE, ARITHMETIC EXCEPTION IN ITEM_NTILE::VAL_INT
#
DO NTILE(MAX(NOT 1)) OVER();
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'MAX(NOT 1)) OVER()' at line 1
DO LAG(1, 9223372036854775807) OVER();
DO LEAD(1, 9223372036854775807) OVER();
DO NTILE(9223372036854775807) OVER();
DO LAG(1, 18446744073709551615) OVER();
ERROR HY000: Incorrect arguments to lag
DO LEAD(1, 18446744073709551615) OVER();
ERROR HY000: Incorrect arguments to lead
DO NTILE(18446744073709551615) OVER();
ERROR HY000: Incorrect arguments to ntile
CREATE PROCEDURE p1(n INTEGER) DO NTILE(n) OVER();
CALL p1(NULL);
ERROR HY000: Incorrect arguments to ntile
DROP PROCEDURE p1;
CREATE PROCEDURE p1(n INTEGER) DO LEAD('x', n) OVER();
CALL p1(NULL);
ERROR HY000: Incorrect arguments to lead
CALL p1(0);
CALL p1(-1);
ERROR HY000: Incorrect arguments to lead
DROP PROCEDURE p1;
CREATE PROCEDURE p1(n INTEGER) DO LAG('x', n) OVER();
CALL p1(NULL);
ERROR HY000: Incorrect arguments to lag
CALL p1(0);
CALL p1(-1);
ERROR HY000: Incorrect arguments to lag
DROP PROCEDURE p1;
CREATE PROCEDURE p1(n DATE) DO NTILE(n) OVER();
ERROR 42000: The variable "n" has a non-integer based type near 'n) OVER()' at line 1
CREATE PROCEDURE p1()
BEGIN
DECLARE d DATE;
DO NTILE(d) OVER();
END
//
ERROR 42000: The variable "d" has a non-integer based type near 'd) OVER();
END' at line 4
CREATE PROCEDURE p2()
BEGIN
DECLARE d DATE;
DO LEAD('x', d) OVER();
END
//
ERROR 42000: The variable "d" has a non-integer based type near 'd) OVER();
END' at line 4
CREATE PROCEDURE p3()
BEGIN
DECLARE d DATE;
DO LAG('x', d) OVER();
END
//
ERROR 42000: The variable "d" has a non-integer based type near 'd) OVER();
END' at line 4
CREATE PROCEDURE p1(n INTEGER)
BEGIN
DECLARE i INTEGER;
SET i = n;
DO NTILE(i) OVER();
END
//
CREATE PROCEDURE p2(n INTEGER)
BEGIN
DECLARE i INTEGER;
SET i = n;
DO LEAD('x', i) OVER();
END
//
CREATE PROCEDURE p3(n INTEGER)
BEGIN
DECLARE i INTEGER;
SET i = n;
DO LAG('x', i) OVER();
END
//
CALL p1(NULL);
ERROR HY000: Incorrect arguments to ntile
CALL p1(0);
ERROR HY000: Incorrect arguments to ntile
CALL p1(1);
CALL p2(NULL);
ERROR HY000: Incorrect arguments to lead
CALL p2(0);
CALL p2(-1);
ERROR HY000: Incorrect arguments to lead
CALL p3(NULL);
ERROR HY000: Incorrect arguments to lag
CALL p3(0);
CALL p3(-1);
ERROR HY000: Incorrect arguments to lag
DROP PROCEDURE p1;
DROP PROCEDURE p2;
DROP PROCEDURE p3;
SET @v = NULL;
PREPARE stmt FROM 'DO NTILE(?) OVER()';
EXECUTE stmt USING @v;
ERROR HY000: Incorrect arguments to ntile
EXECUTE stmt USING @undefined;
ERROR HY000: Incorrect arguments to ntile
DO NTILE(@v) OVER();
ERROR HY000: Incorrect arguments to ntile
DO NTILE(@undefined) OVER();
ERROR HY000: Incorrect arguments to ntile
CREATE PROCEDURE p2(n INT) DO LEAD(1, n) OVER();
CALL p2(NULL);
ERROR HY000: Incorrect arguments to lead
DROP PROCEDURE p2;
CREATE PROCEDURE p3(n INT) DO LAG(1, n) OVER();
CALL p3(NULL);
ERROR HY000: Incorrect arguments to lag
DROP PROCEDURE p3;
PREPARE stmt FROM 'DO LAG(1, ?) OVER()';
EXECUTE stmt USING @v;
ERROR HY000: Incorrect arguments to lag
PREPARE stmt FROM 'DO LEAD(1, ?) OVER()';
EXECUTE stmt USING @v;
ERROR HY000: Incorrect arguments to lead
DO LAG(1, @v) OVER();
ERROR HY000: Incorrect arguments to lag
DO LEAD(1, @v) OVER();
ERROR HY000: Incorrect arguments to lead
SET @v = '1';
DO LAG(1, @v) OVER();
ERROR HY000: Incorrect arguments to lag
DO LEAD(1, @v) OVER();
ERROR HY000: Incorrect arguments to lead
DO NTILE(@v) OVER();
ERROR HY000: Incorrect arguments to ntile
SET @v = 1.2;
DO LAG(1, @v) OVER();
ERROR HY000: Incorrect arguments to lag
DO LEAD(1, @v) OVER();
ERROR HY000: Incorrect arguments to lead
DO NTILE(@v) OVER();
ERROR HY000: Incorrect arguments to ntile
SET @v = 10;
DO LAG(1, @v) OVER();
DO LAG(1, @v) OVER(), @v:=20;
ERROR HY000: Incorrect arguments to lag
DO LEAD(1, @v) OVER();
DO LEAD(1, @v) OVER(), @v:=20;
ERROR HY000: Incorrect arguments to lead
DO NTILE(@v) OVER();
DO NTILE(@v) OVER(), @v:=20;
ERROR HY000: Incorrect arguments to ntile
# End of test for Bug#31393719
#
# Bug#31546816 - QUERY RETURNS DIFFERENT RESULTS DEPENDING ON INTERNAL_TMP_MEM_STORAGE_ENGINE
#
CREATE TABLE `t1` (`c1` INT, `c2` INT, `c3` CHAR(255), `c4` CHAR(255));
INSERT INTO t1 VALUES
(1,1,'t1','t1'), (2,2,'t1','t1'), (3,3,'t1','t1'), (4,4,'t1','t1'), (5,5,'t1','t1'),
(6,6,'t1','t1'), (7,7,'t1','t1'), (8,8,'t1','t1'), (9,9,'t1','t1'), (10,10,'t1','t1'),
(11,11,'t1','t1'), (12,12,'t1','t1'), (13,13,'t1','t1'), (14,14,'t1','t1'), (15,15,'t1','t1'),
(16,16,'t1','t1'), (17,17,'t1','t1'), (18,18,'t1','t1'), (19,19,'t1','t1'), (20,20,'t1','t1'),
(21,21,'t1','t1'), (22,22,'t1','t1'), (23,23,'t1','t1'), (24,24,'t1','t1'), (25,25,'t1','t1');
SET @@session.max_heap_table_size=16*1024;
SET @@session.tmp_table_size=1024;
SET optimizer_switch="derived_condition_pushdown=off";
SET SESSION internal_tmp_mem_storage_engine=MEMORY;
SELECT * FROM (SELECT c2, c3, c4, SUM(c1) OVER (PARTITION BY c2) AS wcol FROM t1)o WHERE c2=10;
c2	c3	c4	wcol
10	t1	t1	10
SET SESSION internal_tmp_mem_storage_engine=TempTable;
SELECT * FROM (SELECT c2, c3, c4, SUM(c1) OVER (PARTITION BY c2) AS wcol FROM t1)o WHERE c2=10;
c2	c3	c4	wcol
10	t1	t1	10
SET optimizer_switch=default;
SET @@session.max_heap_table_size=default;
SET @@session.tmp_table_size=default;
SET @@session.internal_tmp_mem_storage_engine=default;
DROP TABLE t1;
#
# Bug #32497850 RECENT CRASH IN MY_DECIMAL::MY_DECIMAL
#
SELECT (3.14 DIV (
FIRST_VALUE(
FROM_DAYS(2486378174430980553)
)
OVER()
)
);
(3.14 DIV (
FIRST_VALUE(
FROM_DAYS(2486378174430980553)
)
OVER()
)
)
NULL
Warnings:
Warning	1292	Incorrect datetime value: '0000-00-00'
SELECT (3.14 DIV (
NTH_VALUE(
FROM_DAYS(2486378174430980553), 1
)
OVER()
)
);
(3.14 DIV (
NTH_VALUE(
FROM_DAYS(2486378174430980553), 1
)
OVER()
)
)
NULL
Warnings:
Warning	1292	Incorrect datetime value: '0000-00-00'
