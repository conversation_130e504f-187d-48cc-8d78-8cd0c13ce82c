/* Copyright (c) 2000, 2025, Oracle and/or its affiliates.

   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.

   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   Without limiting anything contained in the foregoing, this file,
   which is part of C Driver for MySQL (Connector/C), is also subject to the
   Universal FOSS Exception, version 1.0, a copy of which can be found at
   http://oss.oracle.com/licenses/universal-foss-exception.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.

   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */

/* This file is for Chinese EUC character sets (GB2312), and created by <PERSON> (<EMAIL>).
 */

/*
 * This comment is parsed by configure to create ctype.c,
 * so don't change it unless you know what you are doing.
 *
 * .configure. mbmaxlen_gb2312=2
 */

#include <cstddef>
#include <cstdint>

#include "mysql/strings/m_ctype.h"
#include "strings/m_ctype_internals.h"

static const uint8_t ctype_gb2312[257] = {
    0, /* For standard library */
    32,  32,  32,  32,  32,  32,  32,  32,  32,  40,  40, 40, 40, 40, 32, 32,
    32,  32,  32,  32,  32,  32,  32,  32,  32,  32,  32, 32, 32, 32, 32, 32,
    72,  16,  16,  16,  16,  16,  16,  16,  16,  16,  16, 16, 16, 16, 16, 16,
    132, 132, 132, 132, 132, 132, 132, 132, 132, 132, 16, 16, 16, 16, 16, 16,
    16,  129, 129, 129, 129, 129, 129, 1,   1,   1,   1,  1,  1,  1,  1,  1,
    1,   1,   1,   1,   1,   1,   1,   1,   1,   1,   1,  16, 16, 16, 16, 16,
    16,  130, 130, 130, 130, 130, 130, 2,   2,   2,   2,  2,  2,  2,  2,  2,
    2,   2,   2,   2,   2,   2,   2,   2,   2,   2,   2,  16, 16, 16, 16, 32,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,  0,  0,  0,  0,  0,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,  0,  0,  0,  0,  0,
    0,   3,   3,   3,   3,   3,   3,   3,   3,   3,   3,  3,  3,  3,  3,  3,
    3,   3,   3,   3,   3,   3,   3,   3,   3,   3,   3,  3,  3,  3,  3,  3,
    3,   3,   3,   3,   3,   3,   3,   3,   3,   3,   3,  3,  3,  3,  3,  3,
    3,   3,   3,   3,   3,   3,   3,   3,   3,   3,   3,  3,  3,  3,  3,  3,
    3,   3,   3,   3,   3,   3,   3,   3,   3,   3,   3,  3,  3,  3,  3,  3,
    3,   3,   3,   3,   3,   3,   3,   3,   3,   3,   3,  3,  3,  3,  3,  0,
};

// clang-format off
static const uint8_t to_lower_gb2312[] = {
    '\000',        '\001',        '\002',        '\003',        '\004',
    '\005',        '\006',        '\007',        '\010',        '\011',
    '\012',        '\013',        '\014',        '\015',        '\016',
    '\017',        '\020',        '\021',        '\022',        '\023',
    '\024',        '\025',        '\026',        '\027',        '\030',
    '\031',        '\032',        '\033',        '\034',        '\035',
    '\036',        '\037',        ' ',           '!',           '"',
    '#',           '$',           '%',           '&',           '\'',
    '(',           ')',           '*',           '+',           ',',
    '-',           '.',           '/',           '0',           '1',
    '2',           '3',           '4',           '5',           '6',
    '7',           '8',           '9',           ':',           ';',
    '<',           '=',           '>',           '?',           '@',
    'a',           'b',           'c',           'd',           'e',
    'f',           'g',           'h',           'i',           'j',
    'k',           'l',           'm',           'n',           'o',
    'p',           'q',           'r',           's',           't',
    'u',           'v',           'w',           'x',           'y',
    'z',           '[',           '\\',          ']',           '^',
    '_',           '`',           'a',           'b',           'c',
    'd',           'e',           'f',           'g',           'h',
    'i',           'j',           'k',           'l',           'm',
    'n',           'o',           'p',           'q',           'r',
    's',           't',           'u',           'v',           'w',
    'x',           'y',           'z',           '{',           '|',
    '}',           '~',           '\177',        u'\200',       u'\201',
    u'\202',       u'\203',       u'\204',       u'\205',       u'\206',
    u'\207',       u'\210',       u'\211',       u'\212',       u'\213',
    u'\214',       u'\215',       u'\216',       u'\217',       u'\220',
    u'\221',       u'\222',       u'\223',       u'\224',       u'\225',
    u'\226',       u'\227',       u'\230',       u'\231',       u'\232',
    u'\233',       u'\234',       u'\235',       u'\236',       u'\237',
    u'\240',       u'\241',       u'\242',       u'\243',       u'\244',
    u'\245',       u'\246',       u'\247',       u'\250',       u'\251',
    u'\252',       u'\253',       u'\254',       u'\255',       u'\256',
    u'\257',       u'\260',       u'\261',       u'\262',       u'\263',
    u'\264',       u'\265',       u'\266',       u'\267',       u'\270',
    u'\271',       u'\272',       u'\273',       u'\274',       u'\275',
    u'\276',       u'\277',       u'\300',       u'\301',       u'\302',
    u'\303',       u'\304',       u'\305',       u'\306',       u'\307',
    u'\310',       u'\311',       u'\312',       u'\313',       u'\314',
    u'\315',       u'\316',       u'\317',       u'\320',       u'\321',
    u'\322',       u'\323',       u'\324',       u'\325',       u'\326',
    u'\327',       u'\330',       u'\331',       u'\332',       u'\333',
    u'\334',       u'\335',       u'\336',       u'\337',       u'\340',
    u'\341',       u'\342',       u'\343',       u'\344',       u'\345',
    u'\346',       u'\347',       u'\350',       u'\351',       u'\352',
    u'\353',       u'\354',       u'\355',       u'\356',       u'\357',
    u'\360',       u'\361',       u'\362',       u'\363',       u'\364',
    u'\365',       u'\366',       u'\367',       u'\370',       u'\371',
    u'\372',       u'\373',       u'\374',       u'\375',       u'\376',
    u'\377',
};
// clang-format on

// clang-format off
static const uint8_t to_upper_gb2312[] = {
    '\000',        '\001',        '\002',        '\003',        '\004',
    '\005',        '\006',        '\007',        '\010',        '\011',
    '\012',        '\013',        '\014',        '\015',        '\016',
    '\017',        '\020',        '\021',        '\022',        '\023',
    '\024',        '\025',        '\026',        '\027',        '\030',
    '\031',        '\032',        '\033',        '\034',        '\035',
    '\036',        '\037',        ' ',           '!',           '"',
    '#',           '$',           '%',           '&',           '\'',
    '(',           ')',           '*',           '+',           ',',
    '-',           '.',           '/',           '0',           '1',
    '2',           '3',           '4',           '5',           '6',
    '7',           '8',           '9',           ':',           ';',
    '<',           '=',           '>',           '?',           '@',
    'A',           'B',           'C',           'D',           'E',
    'F',           'G',           'H',           'I',           'J',
    'K',           'L',           'M',           'N',           'O',
    'P',           'Q',           'R',           'S',           'T',
    'U',           'V',           'W',           'X',           'Y',
    'Z',           '[',           '\\',          ']',           '^',
    '_',           '`',           'A',           'B',           'C',
    'D',           'E',           'F',           'G',           'H',
    'I',           'J',           'K',           'L',           'M',
    'N',           'O',           'P',           'Q',           'R',
    'S',           'T',           'U',           'V',           'W',
    'X',           'Y',           'Z',           '{',           '|',
    '}',           '~',           '\177',        u'\200',       u'\201',
    u'\202',       u'\203',       u'\204',       u'\205',       u'\206',
    u'\207',       u'\210',       u'\211',       u'\212',       u'\213',
    u'\214',       u'\215',       u'\216',       u'\217',       u'\220',
    u'\221',       u'\222',       u'\223',       u'\224',       u'\225',
    u'\226',       u'\227',       u'\230',       u'\231',       u'\232',
    u'\233',       u'\234',       u'\235',       u'\236',       u'\237',
    u'\240',       u'\241',       u'\242',       u'\243',       u'\244',
    u'\245',       u'\246',       u'\247',       u'\250',       u'\251',
    u'\252',       u'\253',       u'\254',       u'\255',       u'\256',
    u'\257',       u'\260',       u'\261',       u'\262',       u'\263',
    u'\264',       u'\265',       u'\266',       u'\267',       u'\270',
    u'\271',       u'\272',       u'\273',       u'\274',       u'\275',
    u'\276',       u'\277',       u'\300',       u'\301',       u'\302',
    u'\303',       u'\304',       u'\305',       u'\306',       u'\307',
    u'\310',       u'\311',       u'\312',       u'\313',       u'\314',
    u'\315',       u'\316',       u'\317',       u'\320',       u'\321',
    u'\322',       u'\323',       u'\324',       u'\325',       u'\326',
    u'\327',       u'\330',       u'\331',       u'\332',       u'\333',
    u'\334',       u'\335',       u'\336',       u'\337',       u'\340',
    u'\341',       u'\342',       u'\343',       u'\344',       u'\345',
    u'\346',       u'\347',       u'\350',       u'\351',       u'\352',
    u'\353',       u'\354',       u'\355',       u'\356',       u'\357',
    u'\360',       u'\361',       u'\362',       u'\363',       u'\364',
    u'\365',       u'\366',       u'\367',       u'\370',       u'\371',
    u'\372',       u'\373',       u'\374',       u'\375',       u'\376',
    u'\377',
};
// clang-format on

// clang-format off
static const uint8_t sort_order_gb2312[] = {
    '\000',        '\001',        '\002',        '\003',        '\004',
    '\005',        '\006',        '\007',        '\010',        '\011',
    '\012',        '\013',        '\014',        '\015',        '\016',
    '\017',        '\020',        '\021',        '\022',        '\023',
    '\024',        '\025',        '\026',        '\027',        '\030',
    '\031',        '\032',        '\033',        '\034',        '\035',
    '\036',        '\037',        ' ',           '!',           '"',
    '#',           '$',           '%',           '&',           '\'',
    '(',           ')',           '*',           '+',           ',',
    '-',           '.',           '/',           '0',           '1',
    '2',           '3',           '4',           '5',           '6',
    '7',           '8',           '9',           ':',           ';',
    '<',           '=',           '>',           '?',           '@',
    'A',           'B',           'C',           'D',           'E',
    'F',           'G',           'H',           'I',           'J',
    'K',           'L',           'M',           'N',           'O',
    'P',           'Q',           'R',           'S',           'T',
    'U',           'V',           'W',           'X',           'Y',
    'Z',           '\\',          ']',           '[',           '^',
    '_',           '`',           'A',           'B',           'C',
    'D',           'E',           'F',           'G',           'H',
    'I',           'J',           'K',           'L',           'M',
    'N',           'O',           'P',           'Q',           'R',
    'S',           'T',           'U',           'V',           'W',
    'X',           'Y',           'Z',           '{',           '|',
    '}',           'Y',           '\177',        u'\200',       u'\201',
    u'\202',       u'\203',       u'\204',       u'\205',       u'\206',
    u'\207',       u'\210',       u'\211',       u'\212',       u'\213',
    u'\214',       u'\215',       u'\216',       u'\217',       u'\220',
    u'\221',       u'\222',       u'\223',       u'\224',       u'\225',
    u'\226',       u'\227',       u'\230',       u'\231',       u'\232',
    u'\233',       u'\234',       u'\235',       u'\236',       u'\237',
    u'\240',       u'\241',       u'\242',       u'\243',       u'\244',
    u'\245',       u'\246',       u'\247',       u'\250',       u'\251',
    u'\252',       u'\253',       u'\254',       u'\255',       u'\256',
    u'\257',       u'\260',       u'\261',       u'\262',       u'\263',
    u'\264',       u'\265',       u'\266',       u'\267',       u'\270',
    u'\271',       u'\272',       u'\273',       u'\274',       u'\275',
    u'\276',       u'\277',       u'\300',       u'\301',       u'\302',
    u'\303',       u'\304',       u'\305',       u'\306',       u'\307',
    u'\310',       u'\311',       u'\312',       u'\313',       u'\314',
    u'\315',       u'\316',       u'\317',       u'\320',       u'\321',
    u'\322',       u'\323',       u'\324',       u'\325',       u'\326',
    u'\327',       u'\330',       u'\331',       u'\332',       u'\333',
    u'\334',       u'\335',       u'\336',       u'\337',       u'\340',
    u'\341',       u'\342',       u'\343',       u'\344',       u'\345',
    u'\346',       u'\347',       u'\350',       u'\351',       u'\352',
    u'\353',       u'\354',       u'\355',       u'\356',       u'\357',
    u'\360',       u'\361',       u'\362',       u'\363',       u'\364',
    u'\365',       u'\366',       u'\367',       u'\370',       u'\371',
    u'\372',       u'\373',       u'\374',       u'\375',       u'\376',
    u'\377',
};
// clang-format on

/* Support for Chinese(GB2312) characters, by Miles Tsai (<EMAIL>)
  modified by Wei He (<EMAIL>) */

#define isgb2312head(c) (0xa1 <= (uint8_t)(c) && (uint8_t)(c) <= 0xf7)
#define isgb2312tail(c) (0xa1 <= (uint8_t)(c) && (uint8_t)(c) <= 0xfe)

extern "C" {
static unsigned ismbchar_gb2312(const CHARSET_INFO *cs [[maybe_unused]],
                                const char *p, const char *e) {
  return (isgb2312head(*(p)) && (e) - (p) > 1 && isgb2312tail(*((p) + 1)) ? 2
                                                                          : 0);
}

static unsigned mbcharlen_gb2312(const CHARSET_INFO *cs [[maybe_unused]],
                                 unsigned c) {
  return (isgb2312head(c) ? 2 : 1);
}
}  // extern "C"

static const MY_UNICASE_CHARACTER cA2[256] = {
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx00 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx10 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx20 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx30 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx40 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx50 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx60 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx70 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx80 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx90 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0xA2A0, 0xA2A0, 0xA2A0}, /* A2A0 */
    {0xA2A1, 0xA2A1, 0x003F},
    {0xA2A2, 0xA2A2, 0x003F},
    {0xA2A3, 0xA2A3, 0x003F},
    {0xA2A4, 0xA2A4, 0x003F},
    {0xA2A5, 0xA2A5, 0x003F},
    {0xA2A6, 0xA2A6, 0x003F},
    {0xA2A7, 0xA2A7, 0x003F},
    {0xA2A8, 0xA2A8, 0x003F},
    {0xA2A9, 0xA2A9, 0x003F},
    {0xA2AA, 0xA2AA, 0x003F},
    {0xA2AB, 0xA2AB, 0x003F},
    {0xA2AC, 0xA2AC, 0x003F},
    {0xA2AD, 0xA2AD, 0x003F},
    {0xA2AE, 0xA2AE, 0x003F},
    {0xA2AF, 0xA2AF, 0x003F},
    {0xA2B0, 0xA2B0, 0x003F}, /* A2B0 */
    {0xA2B1, 0xA2B1, 0x2488},
    {0xA2B2, 0xA2B2, 0x2489},
    {0xA2B3, 0xA2B3, 0x248A},
    {0xA2B4, 0xA2B4, 0x248B},
    {0xA2B5, 0xA2B5, 0x248C},
    {0xA2B6, 0xA2B6, 0x248D},
    {0xA2B7, 0xA2B7, 0x248E},
    {0xA2B8, 0xA2B8, 0x248F},
    {0xA2B9, 0xA2B9, 0x2490},
    {0xA2BA, 0xA2BA, 0x2491},
    {0xA2BB, 0xA2BB, 0x2492},
    {0xA2BC, 0xA2BC, 0x2493},
    {0xA2BD, 0xA2BD, 0x2494},
    {0xA2BE, 0xA2BE, 0x2495},
    {0xA2BF, 0xA2BF, 0x2496},
    {0xA2C0, 0xA2C0, 0x2497}, /* A2C0 */
    {0xA2C1, 0xA2C1, 0x2498},
    {0xA2C2, 0xA2C2, 0x2499},
    {0xA2C3, 0xA2C3, 0x249A},
    {0xA2C4, 0xA2C4, 0x249B},
    {0xA2C5, 0xA2C5, 0x2474},
    {0xA2C6, 0xA2C6, 0x2475},
    {0xA2C7, 0xA2C7, 0x2476},
    {0xA2C8, 0xA2C8, 0x2477},
    {0xA2C9, 0xA2C9, 0x2478},
    {0xA2CA, 0xA2CA, 0x2479},
    {0xA2CB, 0xA2CB, 0x247A},
    {0xA2CC, 0xA2CC, 0x247B},
    {0xA2CD, 0xA2CD, 0x247C},
    {0xA2CE, 0xA2CE, 0x247D},
    {0xA2CF, 0xA2CF, 0x247E},
    {0xA2D0, 0xA2D0, 0x247F}, /* A2D0 */
    {0xA2D1, 0xA2D1, 0x2480},
    {0xA2D2, 0xA2D2, 0x2481},
    {0xA2D3, 0xA2D3, 0x2482},
    {0xA2D4, 0xA2D4, 0x2483},
    {0xA2D5, 0xA2D5, 0x2484},
    {0xA2D6, 0xA2D6, 0x2485},
    {0xA2D7, 0xA2D7, 0x2486},
    {0xA2D8, 0xA2D8, 0x2487},
    {0xA2D9, 0xA2D9, 0x2460},
    {0xA2DA, 0xA2DA, 0x2461},
    {0xA2DB, 0xA2DB, 0x2462},
    {0xA2DC, 0xA2DC, 0x2463},
    {0xA2DD, 0xA2DD, 0x2464},
    {0xA2DE, 0xA2DE, 0x2465},
    {0xA2DF, 0xA2DF, 0x2466},
    {0xA2E0, 0xA2E0, 0x2467}, /* A2E0 */
    {0xA2E1, 0xA2E1, 0x2468},
    {0xA2E2, 0xA2E2, 0x2469},
    {0xA2E3, 0xA2E3, 0x003F},
    {0xA2E4, 0xA2E4, 0x003F},
    {0xA2E5, 0xA2E5, 0x3220},
    {0xA2E6, 0xA2E6, 0x3221},
    {0xA2E7, 0xA2E7, 0x3222},
    {0xA2E8, 0xA2E8, 0x3223},
    {0xA2E9, 0xA2E9, 0x3224},
    {0xA2EA, 0xA2EA, 0x3225},
    {0xA2EB, 0xA2EB, 0x3226},
    {0xA2EC, 0xA2EC, 0x3227},
    {0xA2ED, 0xA2ED, 0x3228},
    {0xA2EE, 0xA2EE, 0x3229},
    {0xA2EF, 0xA2EF, 0x003F},
    {0xA2F0, 0xA2F0, 0x003F}, /* A2F0 */
    {0xA2F1, 0xA2F1, 0x2160},
    {0xA2F2, 0xA2F2, 0x2161},
    {0xA2F3, 0xA2F3, 0x2162},
    {0xA2F4, 0xA2F4, 0x2163},
    {0xA2F5, 0xA2F5, 0x2164},
    {0xA2F6, 0xA2F6, 0x2165},
    {0xA2F7, 0xA2F7, 0x2166},
    {0xA2F8, 0xA2F8, 0x2167},
    {0xA2F9, 0xA2F9, 0x2168},
    {0xA2FA, 0xA2FA, 0x2169},
    {0xA2FB, 0xA2FB, 0x216A},
    {0xA2FC, 0xA2FC, 0x216B},
    {0xA2FD, 0xA2FD, 0x003F},
    {0xA2FE, 0xA2FE, 0x003F},
    {0xA2FF, 0xA2FF, 0xA2FF}};

static const MY_UNICASE_CHARACTER cA3[256] = {
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx00 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx10 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx20 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx30 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx40 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx50 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx60 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx70 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx80 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx90 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0xA3A0, 0xA3A0, 0xA3A0}, /* A3A0 */
    {0xA3A1, 0xA3A1, 0xFF01},
    {0xA3A2, 0xA3A2, 0xFF02},
    {0xA3A3, 0xA3A3, 0xFF03},
    {0xA3A4, 0xA3A4, 0xFFE5},
    {0xA3A5, 0xA3A5, 0xFF05},
    {0xA3A6, 0xA3A6, 0xFF06},
    {0xA3A7, 0xA3A7, 0xFF07},
    {0xA3A8, 0xA3A8, 0xFF08},
    {0xA3A9, 0xA3A9, 0xFF09},
    {0xA3AA, 0xA3AA, 0xFF0A},
    {0xA3AB, 0xA3AB, 0xFF0B},
    {0xA3AC, 0xA3AC, 0xFF0C},
    {0xA3AD, 0xA3AD, 0xFF0D},
    {0xA3AE, 0xA3AE, 0xFF0E},
    {0xA3AF, 0xA3AF, 0xFF0F},
    {0xA3B0, 0xA3B0, 0xFF10}, /* A3B0 */
    {0xA3B1, 0xA3B1, 0xFF11},
    {0xA3B2, 0xA3B2, 0xFF12},
    {0xA3B3, 0xA3B3, 0xFF13},
    {0xA3B4, 0xA3B4, 0xFF14},
    {0xA3B5, 0xA3B5, 0xFF15},
    {0xA3B6, 0xA3B6, 0xFF16},
    {0xA3B7, 0xA3B7, 0xFF17},
    {0xA3B8, 0xA3B8, 0xFF18},
    {0xA3B9, 0xA3B9, 0xFF19},
    {0xA3BA, 0xA3BA, 0xFF1A},
    {0xA3BB, 0xA3BB, 0xFF1B},
    {0xA3BC, 0xA3BC, 0xFF1C},
    {0xA3BD, 0xA3BD, 0xFF1D},
    {0xA3BE, 0xA3BE, 0xFF1E},
    {0xA3BF, 0xA3BF, 0xFF1F},
    {0xA3C0, 0xA3C0, 0xFF20}, /* A3C0 */
    {0xA3C1, 0xA3E1, 0xFF21},
    {0xA3C2, 0xA3E2, 0xFF22},
    {0xA3C3, 0xA3E3, 0xFF23},
    {0xA3C4, 0xA3E4, 0xFF24},
    {0xA3C5, 0xA3E5, 0xFF25},
    {0xA3C6, 0xA3E6, 0xFF26},
    {0xA3C7, 0xA3E7, 0xFF27},
    {0xA3C8, 0xA3E8, 0xFF28},
    {0xA3C9, 0xA3E9, 0xFF29},
    {0xA3CA, 0xA3EA, 0xFF2A},
    {0xA3CB, 0xA3EB, 0xFF2B},
    {0xA3CC, 0xA3EC, 0xFF2C},
    {0xA3CD, 0xA3ED, 0xFF2D},
    {0xA3CE, 0xA3EE, 0xFF2E},
    {0xA3CF, 0xA3EF, 0xFF2F},
    {0xA3D0, 0xA3F0, 0xFF30}, /* A3D0 */
    {0xA3D1, 0xA3F1, 0xFF31},
    {0xA3D2, 0xA3F2, 0xFF32},
    {0xA3D3, 0xA3F3, 0xFF33},
    {0xA3D4, 0xA3F4, 0xFF34},
    {0xA3D5, 0xA3F5, 0xFF35},
    {0xA3D6, 0xA3F6, 0xFF36},
    {0xA3D7, 0xA3F7, 0xFF37},
    {0xA3D8, 0xA3F8, 0xFF38},
    {0xA3D9, 0xA3F9, 0xFF39},
    {0xA3DA, 0xA3FA, 0xFF3A},
    {0xA3DB, 0xA3DB, 0xFF3B},
    {0xA3DC, 0xA3DC, 0xFF3C},
    {0xA3DD, 0xA3DD, 0xFF3D},
    {0xA3DE, 0xA3DE, 0xFF3E},
    {0xA3DF, 0xA3DF, 0xFF3F},
    {0xA3E0, 0xA3E0, 0xFF40}, /* A3E0 */
    {0xA3C1, 0xA3E1, 0xFF41},
    {0xA3C2, 0xA3E2, 0xFF42},
    {0xA3C3, 0xA3E3, 0xFF43},
    {0xA3C4, 0xA3E4, 0xFF44},
    {0xA3C5, 0xA3E5, 0xFF45},
    {0xA3C6, 0xA3E6, 0xFF46},
    {0xA3C7, 0xA3E7, 0xFF47},
    {0xA3C8, 0xA3E8, 0xFF48},
    {0xA3C9, 0xA3E9, 0xFF49},
    {0xA3CA, 0xA3EA, 0xFF4A},
    {0xA3CB, 0xA3EB, 0xFF4B},
    {0xA3CC, 0xA3EC, 0xFF4C},
    {0xA3CD, 0xA3ED, 0xFF4D},
    {0xA3CE, 0xA3EE, 0xFF4E},
    {0xA3CF, 0xA3EF, 0xFF4F},
    {0xA3D0, 0xA3F0, 0xFF50}, /* A3F0 */
    {0xA3D1, 0xA3F1, 0xFF51},
    {0xA3D2, 0xA3F2, 0xFF52},
    {0xA3D3, 0xA3F3, 0xFF53},
    {0xA3D4, 0xA3F4, 0xFF54},
    {0xA3D5, 0xA3F5, 0xFF55},
    {0xA3D6, 0xA3F6, 0xFF56},
    {0xA3D7, 0xA3F7, 0xFF57},
    {0xA3D8, 0xA3F8, 0xFF58},
    {0xA3D9, 0xA3F9, 0xFF59},
    {0xA3DA, 0xA3FA, 0xFF5A},
    {0xA3FB, 0xA3FB, 0xFF5B},
    {0xA3FC, 0xA3FC, 0xFF5C},
    {0xA3FD, 0xA3FD, 0xFF5D},
    {0xA3FE, 0xA3FE, 0xFFE3},
    {0xA3FF, 0xA3FF, 0xA3FF}};

static const MY_UNICASE_CHARACTER cA6[256] = {
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx00 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx10 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx20 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx30 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx40 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx50 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx60 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx70 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx80 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx90 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0xA6A0, 0xA6A0, 0xA6A0}, /* A6A0 */
    {0xA6A1, 0xA6C1, 0x0391},
    {0xA6A2, 0xA6C2, 0x0392},
    {0xA6A3, 0xA6C3, 0x0393},
    {0xA6A4, 0xA6C4, 0x0394},
    {0xA6A5, 0xA6C5, 0x0395},
    {0xA6A6, 0xA6C6, 0x0396},
    {0xA6A7, 0xA6C7, 0x0397},
    {0xA6A8, 0xA6C8, 0x0398},
    {0xA6A9, 0xA6C9, 0x0399},
    {0xA6AA, 0xA6CA, 0x039A},
    {0xA6AB, 0xA6CB, 0x039B},
    {0xA6AC, 0xA6CC, 0x039C},
    {0xA6AD, 0xA6CD, 0x039D},
    {0xA6AE, 0xA6CE, 0x039E},
    {0xA6AF, 0xA6CF, 0x039F},
    {0xA6B0, 0xA6D0, 0x03A0}, /* A6B0 */
    {0xA6B1, 0xA6D1, 0x03A1},
    {0xA6B2, 0xA6D2, 0x03A3},
    {0xA6B3, 0xA6D3, 0x03A4},
    {0xA6B4, 0xA6D4, 0x03A5},
    {0xA6B5, 0xA6D5, 0x03A6},
    {0xA6B6, 0xA6D6, 0x03A7},
    {0xA6B7, 0xA6D7, 0x03A8},
    {0xA6B8, 0xA6D8, 0x03A9},
    {0xA6B9, 0xA6B9, 0x003F},
    {0xA6BA, 0xA6BA, 0x003F},
    {0xA6BB, 0xA6BB, 0x003F},
    {0xA6BC, 0xA6BC, 0x003F},
    {0xA6BD, 0xA6BD, 0x003F},
    {0xA6BE, 0xA6BE, 0x003F},
    {0xA6BF, 0xA6BF, 0x003F},
    {0xA6C0, 0xA6C0, 0x003F}, /* A6C0 */
    {0xA6A1, 0xA6C1, 0x03B1},
    {0xA6A2, 0xA6C2, 0x03B2},
    {0xA6A3, 0xA6C3, 0x03B3},
    {0xA6A4, 0xA6C4, 0x03B4},
    {0xA6A5, 0xA6C5, 0x03B5},
    {0xA6A6, 0xA6C6, 0x03B6},
    {0xA6A7, 0xA6C7, 0x03B7},
    {0xA6A8, 0xA6C8, 0x03B8},
    {0xA6A9, 0xA6C9, 0x03B9},
    {0xA6AA, 0xA6CA, 0x03BA},
    {0xA6AB, 0xA6CB, 0x03BB},
    {0xA6AC, 0xA6CC, 0x03BC},
    {0xA6AD, 0xA6CD, 0x03BD},
    {0xA6AE, 0xA6CE, 0x03BE},
    {0xA6AF, 0xA6CF, 0x03BF},
    {0xA6B0, 0xA6D0, 0x03C0}, /* A6D0 */
    {0xA6B1, 0xA6D1, 0x03C1},
    {0xA6B2, 0xA6D2, 0x03C3},
    {0xA6B3, 0xA6D3, 0x03C4},
    {0xA6B4, 0xA6D4, 0x03C5},
    {0xA6B5, 0xA6D5, 0x03C6},
    {0xA6B6, 0xA6D6, 0x03C7},
    {0xA6B7, 0xA6D7, 0x03C8},
    {0xA6B8, 0xA6D8, 0x03C9},
    {0xA6D9, 0xA6D9, 0x003F},
    {0xA6DA, 0xA6DA, 0x003F},
    {0xA6DB, 0xA6DB, 0x003F},
    {0xA6DC, 0xA6DC, 0x003F},
    {0xA6DD, 0xA6DD, 0x003F},
    {0xA6DE, 0xA6DE, 0x003F},
    {0xA6DF, 0xA6DF, 0x003F},
    {0xA6E0, 0xA6E0, 0x003F}, /* A6E0 */
    {0xA6E1, 0xA6E1, 0x003F},
    {0xA6E2, 0xA6E2, 0x003F},
    {0xA6E3, 0xA6E3, 0x003F},
    {0xA6E4, 0xA6E4, 0x003F},
    {0xA6E5, 0xA6E5, 0x003F},
    {0xA6E6, 0xA6E6, 0x003F},
    {0xA6E7, 0xA6E7, 0x003F},
    {0xA6E8, 0xA6E8, 0x003F},
    {0xA6E9, 0xA6E9, 0x003F},
    {0xA6EA, 0xA6EA, 0x003F},
    {0xA6EB, 0xA6EB, 0x003F},
    {0xA6EC, 0xA6EC, 0x003F},
    {0xA6ED, 0xA6ED, 0x003F},
    {0xA6EE, 0xA6EE, 0x003F},
    {0xA6EF, 0xA6EF, 0x003F},
    {0xA6F0, 0xA6F0, 0x003F}, /* A6F0 */
    {0xA6F1, 0xA6F1, 0x003F},
    {0xA6F2, 0xA6F2, 0x003F},
    {0xA6F3, 0xA6F3, 0x003F},
    {0xA6F4, 0xA6F4, 0x003F},
    {0xA6F5, 0xA6F5, 0x003F},
    {0xA6F6, 0xA6F6, 0x003F},
    {0xA6F7, 0xA6F7, 0x003F},
    {0xA6F8, 0xA6F8, 0x003F},
    {0xA6F9, 0xA6F9, 0x003F},
    {0xA6FA, 0xA6FA, 0x003F},
    {0xA6FB, 0xA6FB, 0x003F},
    {0xA6FC, 0xA6FC, 0x003F},
    {0xA6FD, 0xA6FD, 0x003F},
    {0xA6FE, 0xA6FE, 0x003F},
    {0xA6FF, 0xA6FF, 0xA6FF}};

static const MY_UNICASE_CHARACTER cA7[256] = {
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx00 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx10 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx20 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx30 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx40 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx50 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx60 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx70 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx80 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx90 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0xA7A0, 0xA7A0, 0xA7A0}, /* A7A0 */
    {0xA7A1, 0xA7D1, 0x0410},
    {0xA7A2, 0xA7D2, 0x0411},
    {0xA7A3, 0xA7D3, 0x0412},
    {0xA7A4, 0xA7D4, 0x0413},
    {0xA7A5, 0xA7D5, 0x0414},
    {0xA7A6, 0xA7D6, 0x0415},
    {0xA7A7, 0xA7D7, 0x0401},
    {0xA7A8, 0xA7D8, 0x0416},
    {0xA7A9, 0xA7D9, 0x0417},
    {0xA7AA, 0xA7DA, 0x0418},
    {0xA7AB, 0xA7DB, 0x0419},
    {0xA7AC, 0xA7DC, 0x041A},
    {0xA7AD, 0xA7DD, 0x041B},
    {0xA7AE, 0xA7DE, 0x041C},
    {0xA7AF, 0xA7DF, 0x041D},
    {0xA7B0, 0xA7E0, 0x041E}, /* A7B0 */
    {0xA7B1, 0xA7E1, 0x041F},
    {0xA7B2, 0xA7E2, 0x0420},
    {0xA7B3, 0xA7E3, 0x0421},
    {0xA7B4, 0xA7E4, 0x0422},
    {0xA7B5, 0xA7E5, 0x0423},
    {0xA7B6, 0xA7E6, 0x0424},
    {0xA7B7, 0xA7E7, 0x0425},
    {0xA7B8, 0xA7E8, 0x0426},
    {0xA7B9, 0xA7E9, 0x0427},
    {0xA7BA, 0xA7EA, 0x0428},
    {0xA7BB, 0xA7EB, 0x0429},
    {0xA7BC, 0xA7EC, 0x042A},
    {0xA7BD, 0xA7ED, 0x042B},
    {0xA7BE, 0xA7EE, 0x042C},
    {0xA7BF, 0xA7EF, 0x042D},
    {0xA7C0, 0xA7F0, 0x042E}, /* A7C0 */
    {0xA7C1, 0xA7F1, 0x042F},
    {0xA7C2, 0xA7C2, 0x003F},
    {0xA7C3, 0xA7C3, 0x003F},
    {0xA7C4, 0xA7C4, 0x003F},
    {0xA7C5, 0xA7C5, 0x003F},
    {0xA7C6, 0xA7C6, 0x003F},
    {0xA7C7, 0xA7C7, 0x003F},
    {0xA7C8, 0xA7C8, 0x003F},
    {0xA7C9, 0xA7C9, 0x003F},
    {0xA7CA, 0xA7CA, 0x003F},
    {0xA7CB, 0xA7CB, 0x003F},
    {0xA7CC, 0xA7CC, 0x003F},
    {0xA7CD, 0xA7CD, 0x003F},
    {0xA7CE, 0xA7CE, 0x003F},
    {0xA7CF, 0xA7CF, 0x003F},
    {0xA7D0, 0xA7D0, 0x003F}, /* A7D0 */
    {0xA7A1, 0xA7D1, 0x0430},
    {0xA7A2, 0xA7D2, 0x0431},
    {0xA7A3, 0xA7D3, 0x0432},
    {0xA7A4, 0xA7D4, 0x0433},
    {0xA7A5, 0xA7D5, 0x0434},
    {0xA7A6, 0xA7D6, 0x0435},
    {0xA7A7, 0xA7D7, 0x0451},
    {0xA7A8, 0xA7D8, 0x0436},
    {0xA7A9, 0xA7D9, 0x0437},
    {0xA7AA, 0xA7DA, 0x0438},
    {0xA7AB, 0xA7DB, 0x0439},
    {0xA7AC, 0xA7DC, 0x043A},
    {0xA7AD, 0xA7DD, 0x043B},
    {0xA7AE, 0xA7DE, 0x043C},
    {0xA7AF, 0xA7DF, 0x043D},
    {0xA7B0, 0xA7E0, 0x043E}, /* A7E0 */
    {0xA7B1, 0xA7E1, 0x043F},
    {0xA7B2, 0xA7E2, 0x0440},
    {0xA7B3, 0xA7E3, 0x0441},
    {0xA7B4, 0xA7E4, 0x0442},
    {0xA7B5, 0xA7E5, 0x0443},
    {0xA7B6, 0xA7E6, 0x0444},
    {0xA7B7, 0xA7E7, 0x0445},
    {0xA7B8, 0xA7E8, 0x0446},
    {0xA7B9, 0xA7E9, 0x0447},
    {0xA7BA, 0xA7EA, 0x0448},
    {0xA7BB, 0xA7EB, 0x0449},
    {0xA7BC, 0xA7EC, 0x044A},
    {0xA7BD, 0xA7ED, 0x044B},
    {0xA7BE, 0xA7EE, 0x044C},
    {0xA7BF, 0xA7EF, 0x044D},
    {0xA7C0, 0xA7F0, 0x044E}, /* A7F0 */
    {0xA7C1, 0xA7F1, 0x044F},
    {0xA7F2, 0xA7F2, 0x003F},
    {0xA7F3, 0xA7F3, 0x003F},
    {0xA7F4, 0xA7F4, 0x003F},
    {0xA7F5, 0xA7F5, 0x003F},
    {0xA7F6, 0xA7F6, 0x003F},
    {0xA7F7, 0xA7F7, 0x003F},
    {0xA7F8, 0xA7F8, 0x003F},
    {0xA7F9, 0xA7F9, 0x003F},
    {0xA7FA, 0xA7FA, 0x003F},
    {0xA7FB, 0xA7FB, 0x003F},
    {0xA7FC, 0xA7FC, 0x003F},
    {0xA7FD, 0xA7FD, 0x003F},
    {0xA7FE, 0xA7FE, 0x003F},
    {0xA7FF, 0xA7FF, 0xA7FF}};

static const MY_UNICASE_CHARACTER cA8[256] = {
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx00 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx10 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx20 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx30 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx40 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx50 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx60 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx70 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx80 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0}, /* xx90 */
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0, 0, 0},
    {0xA8A0, 0xA8A0, 0xA8A0}, /* A8A0 */
    {0xA8A1, 0xA8A1, 0x0101},
    {0xA8A2, 0xA8A2, 0x00E1},
    {0xA8A3, 0xA8A3, 0x01CE},
    {0xA8A4, 0xA8A4, 0x00E0},
    {0xA8A5, 0xA8A5, 0x0113},
    {0xA8A6, 0xA8A6, 0x00E9},
    {0xA8A7, 0xA8A7, 0x011B},
    {0xA8A8, 0xA8A8, 0x00E8},
    {0xA8A9, 0xA8A9, 0x012B},
    {0xA8AA, 0xA8AA, 0x00ED},
    {0xA8AB, 0xA8AB, 0x01D0},
    {0xA8AC, 0xA8AC, 0x00EC},
    {0xA8AD, 0xA8AD, 0x014D},
    {0xA8AE, 0xA8AE, 0x00F3},
    {0xA8AF, 0xA8AF, 0x01D2},
    {0xA8B0, 0xA8B0, 0x00F2}, /* A8B0 */
    {0xA8B1, 0xA8B1, 0x016B},
    {0xA8B2, 0xA8B2, 0x00FA},
    {0xA8B3, 0xA8B3, 0x01D4},
    {0xA8B4, 0xA8B4, 0x00F9},
    {0xA8B5, 0xA8B5, 0x01D6},
    {0xA8B6, 0xA8B6, 0x01D8},
    {0xA8B7, 0xA8B7, 0x01DA},
    {0xA8B8, 0xA8B8, 0x01DC},
    {0xA8B9, 0xA8B9, 0x00FC},
    {0xA8BA, 0xA8BA, 0x00EA},
    {0xA8BB, 0xA8BB, 0x003F},
    {0xA8BC, 0xA8BC, 0x003F},
    {0xA8BD, 0xA8BD, 0x003F},
    {0xA8BE, 0xA8BE, 0x003F},
    {0xA8BF, 0xA8BF, 0x003F},
    {0xA8C0, 0xA8C0, 0x003F}, /* A8C0 */
    {0xA8C1, 0xA8C1, 0x003F},
    {0xA8C2, 0xA8C2, 0x003F},
    {0xA8C3, 0xA8C3, 0x003F},
    {0xA8C4, 0xA8C4, 0x003F},
    {0xA8C5, 0xA8C5, 0x3105},
    {0xA8C6, 0xA8C6, 0x3106},
    {0xA8C7, 0xA8C7, 0x3107},
    {0xA8C8, 0xA8C8, 0x3108},
    {0xA8C9, 0xA8C9, 0x3109},
    {0xA8CA, 0xA8CA, 0x310A},
    {0xA8CB, 0xA8CB, 0x310B},
    {0xA8CC, 0xA8CC, 0x310C},
    {0xA8CD, 0xA8CD, 0x310D},
    {0xA8CE, 0xA8CE, 0x310E},
    {0xA8CF, 0xA8CF, 0x310F},
    {0xA8D0, 0xA8D0, 0x3110}, /* A8D0 */
    {0xA8D1, 0xA8D1, 0x3111},
    {0xA8D2, 0xA8D2, 0x3112},
    {0xA8D3, 0xA8D3, 0x3113},
    {0xA8D4, 0xA8D4, 0x3114},
    {0xA8D5, 0xA8D5, 0x3115},
    {0xA8D6, 0xA8D6, 0x3116},
    {0xA8D7, 0xA8D7, 0x3117},
    {0xA8D8, 0xA8D8, 0x3118},
    {0xA8D9, 0xA8D9, 0x3119},
    {0xA8DA, 0xA8DA, 0x311A},
    {0xA8DB, 0xA8DB, 0x311B},
    {0xA8DC, 0xA8DC, 0x311C},
    {0xA8DD, 0xA8DD, 0x311D},
    {0xA8DE, 0xA8DE, 0x311E},
    {0xA8DF, 0xA8DF, 0x311F},
    {0xA8E0, 0xA8E0, 0x3120}, /* A8E0 */
    {0xA8E1, 0xA8E1, 0x3121},
    {0xA8E2, 0xA8E2, 0x3122},
    {0xA8E3, 0xA8E3, 0x3123},
    {0xA8E4, 0xA8E4, 0x3124},
    {0xA8E5, 0xA8E5, 0x3125},
    {0xA8E6, 0xA8E6, 0x3126},
    {0xA8E7, 0xA8E7, 0x3127},
    {0xA8E8, 0xA8E8, 0x3128},
    {0xA8E9, 0xA8E9, 0x3129},
    {0xA8EA, 0xA8EA, 0x003F},
    {0xA8EB, 0xA8EB, 0x003F},
    {0xA8EC, 0xA8EC, 0x003F},
    {0xA8ED, 0xA8ED, 0x003F},
    {0xA8EE, 0xA8EE, 0x003F},
    {0xA8EF, 0xA8EF, 0x003F},
    {0xA8F0, 0xA8F0, 0x003F}, /* A8F0 */
    {0xA8F1, 0xA8F1, 0x003F},
    {0xA8F2, 0xA8F2, 0x003F},
    {0xA8F3, 0xA8F3, 0x003F},
    {0xA8F4, 0xA8F4, 0x003F},
    {0xA8F5, 0xA8F5, 0x003F},
    {0xA8F6, 0xA8F6, 0x003F},
    {0xA8F7, 0xA8F7, 0x003F},
    {0xA8F8, 0xA8F8, 0x003F},
    {0xA8F9, 0xA8F9, 0x003F},
    {0xA8FA, 0xA8FA, 0x003F},
    {0xA8FB, 0xA8FB, 0x003F},
    {0xA8FC, 0xA8FC, 0x003F},
    {0xA8FD, 0xA8FD, 0x003F},
    {0xA8FE, 0xA8FE, 0x003F},
    {0xA8FF, 0xA8FF, 0xA8FF}};

static const MY_UNICASE_CHARACTER *my_caseinfo_pages_gb2312[256] = {
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, /* 0 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 1 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 2 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 3 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 4 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 5 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 6 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 7 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 8 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* 9 */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, cA2,     cA3,
    nullptr, nullptr, cA6,     cA7, /* A */
    cA8,     nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* B */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* C */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* D */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* E */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr, nullptr, nullptr, /* F */
    nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
    nullptr, nullptr};

static MY_UNICASE_INFO my_caseinfo_gb2312 = {0xFFFF, my_caseinfo_pages_gb2312};

/* page 0 0x2121-0x2658 */
static const uint16_t tab_gb2312_uni0[] = {
    0x3000, 0x3001, 0x3002, 0x30FB, 0x02C9, 0x02C7, 0x00A8, 0x3003, 0x3005,
    0x2015, 0xFF5E, 0x2016, 0x2026, 0x2018, 0x2019, 0x201C, 0x201D, 0x3014,
    0x3015, 0x3008, 0x3009, 0x300A, 0x300B, 0x300C, 0x300D, 0x300E, 0x300F,
    0x3016, 0x3017, 0x3010, 0x3011, 0x00B1, 0x00D7, 0x00F7, 0x2236, 0x2227,
    0x2228, 0x2211, 0x220F, 0x222A, 0x2229, 0x2208, 0x2237, 0x221A, 0x22A5,
    0x2225, 0x2220, 0x2312, 0x2299, 0x222B, 0x222E, 0x2261, 0x224C, 0x2248,
    0x223D, 0x221D, 0x2260, 0x226E, 0x226F, 0x2264, 0x2265, 0x221E, 0x2235,
    0x2234, 0x2642, 0x2640, 0x00B0, 0x2032, 0x2033, 0x2103, 0xFF04, 0x00A4,
    0xFFE0, 0xFFE1, 0x2030, 0x00A7, 0x2116, 0x2606, 0x2605, 0x25CB, 0x25CF,
    0x25CE, 0x25C7, 0x25C6, 0x25A1, 0x25A0, 0x25B3, 0x25B2, 0x203B, 0x2192,
    0x2190, 0x2191, 0x2193, 0x3013, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x2488, 0x2489, 0x248A, 0x248B, 0x248C, 0x248D, 0x248E,
    0x248F, 0x2490, 0x2491, 0x2492, 0x2493, 0x2494, 0x2495, 0x2496, 0x2497,
    0x2498, 0x2499, 0x249A, 0x249B, 0x2474, 0x2475, 0x2476, 0x2477, 0x2478,
    0x2479, 0x247A, 0x247B, 0x247C, 0x247D, 0x247E, 0x247F, 0x2480, 0x2481,
    0x2482, 0x2483, 0x2484, 0x2485, 0x2486, 0x2487, 0x2460, 0x2461, 0x2462,
    0x2463, 0x2464, 0x2465, 0x2466, 0x2467, 0x2468, 0x2469, 0,      0,
    0x3220, 0x3221, 0x3222, 0x3223, 0x3224, 0x3225, 0x3226, 0x3227, 0x3228,
    0x3229, 0,      0,      0x2160, 0x2161, 0x2162, 0x2163, 0x2164, 0x2165,
    0x2166, 0x2167, 0x2168, 0x2169, 0x216A, 0x216B, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0xFF01,
    0xFF02, 0xFF03, 0xFFE5, 0xFF05, 0xFF06, 0xFF07, 0xFF08, 0xFF09, 0xFF0A,
    0xFF0B, 0xFF0C, 0xFF0D, 0xFF0E, 0xFF0F, 0xFF10, 0xFF11, 0xFF12, 0xFF13,
    0xFF14, 0xFF15, 0xFF16, 0xFF17, 0xFF18, 0xFF19, 0xFF1A, 0xFF1B, 0xFF1C,
    0xFF1D, 0xFF1E, 0xFF1F, 0xFF20, 0xFF21, 0xFF22, 0xFF23, 0xFF24, 0xFF25,
    0xFF26, 0xFF27, 0xFF28, 0xFF29, 0xFF2A, 0xFF2B, 0xFF2C, 0xFF2D, 0xFF2E,
    0xFF2F, 0xFF30, 0xFF31, 0xFF32, 0xFF33, 0xFF34, 0xFF35, 0xFF36, 0xFF37,
    0xFF38, 0xFF39, 0xFF3A, 0xFF3B, 0xFF3C, 0xFF3D, 0xFF3E, 0xFF3F, 0xFF40,
    0xFF41, 0xFF42, 0xFF43, 0xFF44, 0xFF45, 0xFF46, 0xFF47, 0xFF48, 0xFF49,
    0xFF4A, 0xFF4B, 0xFF4C, 0xFF4D, 0xFF4E, 0xFF4F, 0xFF50, 0xFF51, 0xFF52,
    0xFF53, 0xFF54, 0xFF55, 0xFF56, 0xFF57, 0xFF58, 0xFF59, 0xFF5A, 0xFF5B,
    0xFF5C, 0xFF5D, 0xFFE3, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x3041, 0x3042, 0x3043, 0x3044, 0x3045, 0x3046,
    0x3047, 0x3048, 0x3049, 0x304A, 0x304B, 0x304C, 0x304D, 0x304E, 0x304F,
    0x3050, 0x3051, 0x3052, 0x3053, 0x3054, 0x3055, 0x3056, 0x3057, 0x3058,
    0x3059, 0x305A, 0x305B, 0x305C, 0x305D, 0x305E, 0x305F, 0x3060, 0x3061,
    0x3062, 0x3063, 0x3064, 0x3065, 0x3066, 0x3067, 0x3068, 0x3069, 0x306A,
    0x306B, 0x306C, 0x306D, 0x306E, 0x306F, 0x3070, 0x3071, 0x3072, 0x3073,
    0x3074, 0x3075, 0x3076, 0x3077, 0x3078, 0x3079, 0x307A, 0x307B, 0x307C,
    0x307D, 0x307E, 0x307F, 0x3080, 0x3081, 0x3082, 0x3083, 0x3084, 0x3085,
    0x3086, 0x3087, 0x3088, 0x3089, 0x308A, 0x308B, 0x308C, 0x308D, 0x308E,
    0x308F, 0x3090, 0x3091, 0x3092, 0x3093, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x30A1, 0x30A2,
    0x30A3, 0x30A4, 0x30A5, 0x30A6, 0x30A7, 0x30A8, 0x30A9, 0x30AA, 0x30AB,
    0x30AC, 0x30AD, 0x30AE, 0x30AF, 0x30B0, 0x30B1, 0x30B2, 0x30B3, 0x30B4,
    0x30B5, 0x30B6, 0x30B7, 0x30B8, 0x30B9, 0x30BA, 0x30BB, 0x30BC, 0x30BD,
    0x30BE, 0x30BF, 0x30C0, 0x30C1, 0x30C2, 0x30C3, 0x30C4, 0x30C5, 0x30C6,
    0x30C7, 0x30C8, 0x30C9, 0x30CA, 0x30CB, 0x30CC, 0x30CD, 0x30CE, 0x30CF,
    0x30D0, 0x30D1, 0x30D2, 0x30D3, 0x30D4, 0x30D5, 0x30D6, 0x30D7, 0x30D8,
    0x30D9, 0x30DA, 0x30DB, 0x30DC, 0x30DD, 0x30DE, 0x30DF, 0x30E0, 0x30E1,
    0x30E2, 0x30E3, 0x30E4, 0x30E5, 0x30E6, 0x30E7, 0x30E8, 0x30E9, 0x30EA,
    0x30EB, 0x30EC, 0x30ED, 0x30EE, 0x30EF, 0x30F0, 0x30F1, 0x30F2, 0x30F3,
    0x30F4, 0x30F5, 0x30F6, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x0391, 0x0392, 0x0393, 0x0394, 0x0395, 0x0396, 0x0397,
    0x0398, 0x0399, 0x039A, 0x039B, 0x039C, 0x039D, 0x039E, 0x039F, 0x03A0,
    0x03A1, 0x03A3, 0x03A4, 0x03A5, 0x03A6, 0x03A7, 0x03A8, 0x03A9, 0,
    0,      0,      0,      0,      0,      0,      0,      0x03B1, 0x03B2,
    0x03B3, 0x03B4, 0x03B5, 0x03B6, 0x03B7, 0x03B8, 0x03B9, 0x03BA, 0x03BB,
    0x03BC, 0x03BD, 0x03BE, 0x03BF, 0x03C0, 0x03C1, 0x03C3, 0x03C4, 0x03C5,
    0x03C6, 0x03C7, 0x03C8, 0x03C9};

/* page 1 0x2721-0x296F */
static const uint16_t tab_gb2312_uni1[] = {
    0x0410, 0x0411, 0x0412, 0x0413, 0x0414, 0x0415, 0x0401, 0x0416, 0x0417,
    0x0418, 0x0419, 0x041A, 0x041B, 0x041C, 0x041D, 0x041E, 0x041F, 0x0420,
    0x0421, 0x0422, 0x0423, 0x0424, 0x0425, 0x0426, 0x0427, 0x0428, 0x0429,
    0x042A, 0x042B, 0x042C, 0x042D, 0x042E, 0x042F, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x0430, 0x0431, 0x0432, 0x0433, 0x0434, 0x0435,
    0x0451, 0x0436, 0x0437, 0x0438, 0x0439, 0x043A, 0x043B, 0x043C, 0x043D,
    0x043E, 0x043F, 0x0440, 0x0441, 0x0442, 0x0443, 0x0444, 0x0445, 0x0446,
    0x0447, 0x0448, 0x0449, 0x044A, 0x044B, 0x044C, 0x044D, 0x044E, 0x044F,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x0101, 0x00E1, 0x01CE, 0x00E0, 0x0113,
    0x00E9, 0x011B, 0x00E8, 0x012B, 0x00ED, 0x01D0, 0x00EC, 0x014D, 0x00F3,
    0x01D2, 0x00F2, 0x016B, 0x00FA, 0x01D4, 0x00F9, 0x01D6, 0x01D8, 0x01DA,
    0x01DC, 0x00FC, 0x00EA, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x3105, 0x3106, 0x3107, 0x3108, 0x3109,
    0x310A, 0x310B, 0x310C, 0x310D, 0x310E, 0x310F, 0x3110, 0x3111, 0x3112,
    0x3113, 0x3114, 0x3115, 0x3116, 0x3117, 0x3118, 0x3119, 0x311A, 0x311B,
    0x311C, 0x311D, 0x311E, 0x311F, 0x3120, 0x3121, 0x3122, 0x3123, 0x3124,
    0x3125, 0x3126, 0x3127, 0x3128, 0x3129, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x2500, 0x2501, 0x2502, 0x2503, 0x2504, 0x2505, 0x2506,
    0x2507, 0x2508, 0x2509, 0x250A, 0x250B, 0x250C, 0x250D, 0x250E, 0x250F,
    0x2510, 0x2511, 0x2512, 0x2513, 0x2514, 0x2515, 0x2516, 0x2517, 0x2518,
    0x2519, 0x251A, 0x251B, 0x251C, 0x251D, 0x251E, 0x251F, 0x2520, 0x2521,
    0x2522, 0x2523, 0x2524, 0x2525, 0x2526, 0x2527, 0x2528, 0x2529, 0x252A,
    0x252B, 0x252C, 0x252D, 0x252E, 0x252F, 0x2530, 0x2531, 0x2532, 0x2533,
    0x2534, 0x2535, 0x2536, 0x2537, 0x2538, 0x2539, 0x253A, 0x253B, 0x253C,
    0x253D, 0x253E, 0x253F, 0x2540, 0x2541, 0x2542, 0x2543, 0x2544, 0x2545,
    0x2546, 0x2547, 0x2548, 0x2549, 0x254A, 0x254B};

/* page 2 0x3021-0x777E */
static const uint16_t tab_gb2312_uni2[] = {
    0x554A, 0x963F, 0x57C3, 0x6328, 0x54CE, 0x5509, 0x54C0, 0x7691, 0x764C,
    0x853C, 0x77EE, 0x827E, 0x788D, 0x7231, 0x9698, 0x978D, 0x6C28, 0x5B89,
    0x4FFA, 0x6309, 0x6697, 0x5CB8, 0x80FA, 0x6848, 0x80AE, 0x6602, 0x76CE,
    0x51F9, 0x6556, 0x71AC, 0x7FF1, 0x8884, 0x50B2, 0x5965, 0x61CA, 0x6FB3,
    0x82AD, 0x634C, 0x6252, 0x53ED, 0x5427, 0x7B06, 0x516B, 0x75A4, 0x5DF4,
    0x62D4, 0x8DCB, 0x9776, 0x628A, 0x8019, 0x575D, 0x9738, 0x7F62, 0x7238,
    0x767D, 0x67CF, 0x767E, 0x6446, 0x4F70, 0x8D25, 0x62DC, 0x7A17, 0x6591,
    0x73ED, 0x642C, 0x6273, 0x822C, 0x9881, 0x677F, 0x7248, 0x626E, 0x62CC,
    0x4F34, 0x74E3, 0x534A, 0x529E, 0x7ECA, 0x90A6, 0x5E2E, 0x6886, 0x699C,
    0x8180, 0x7ED1, 0x68D2, 0x78C5, 0x868C, 0x9551, 0x508D, 0x8C24, 0x82DE,
    0x80DE, 0x5305, 0x8912, 0x5265, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x8584, 0x96F9, 0x4FDD, 0x5821, 0x9971,
    0x5B9D, 0x62B1, 0x62A5, 0x66B4, 0x8C79, 0x9C8D, 0x7206, 0x676F, 0x7891,
    0x60B2, 0x5351, 0x5317, 0x8F88, 0x80CC, 0x8D1D, 0x94A1, 0x500D, 0x72C8,
    0x5907, 0x60EB, 0x7119, 0x88AB, 0x5954, 0x82EF, 0x672C, 0x7B28, 0x5D29,
    0x7EF7, 0x752D, 0x6CF5, 0x8E66, 0x8FF8, 0x903C, 0x9F3B, 0x6BD4, 0x9119,
    0x7B14, 0x5F7C, 0x78A7, 0x84D6, 0x853D, 0x6BD5, 0x6BD9, 0x6BD6, 0x5E01,
    0x5E87, 0x75F9, 0x95ED, 0x655D, 0x5F0A, 0x5FC5, 0x8F9F, 0x58C1, 0x81C2,
    0x907F, 0x965B, 0x97AD, 0x8FB9, 0x7F16, 0x8D2C, 0x6241, 0x4FBF, 0x53D8,
    0x535E, 0x8FA8, 0x8FA9, 0x8FAB, 0x904D, 0x6807, 0x5F6A, 0x8198, 0x8868,
    0x9CD6, 0x618B, 0x522B, 0x762A, 0x5F6C, 0x658C, 0x6FD2, 0x6EE8, 0x5BBE,
    0x6448, 0x5175, 0x51B0, 0x67C4, 0x4E19, 0x79C9, 0x997C, 0x70B3, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x75C5,
    0x5E76, 0x73BB, 0x83E0, 0x64AD, 0x62E8, 0x94B5, 0x6CE2, 0x535A, 0x52C3,
    0x640F, 0x94C2, 0x7B94, 0x4F2F, 0x5E1B, 0x8236, 0x8116, 0x818A, 0x6E24,
    0x6CCA, 0x9A73, 0x6355, 0x535C, 0x54FA, 0x8865, 0x57E0, 0x4E0D, 0x5E03,
    0x6B65, 0x7C3F, 0x90E8, 0x6016, 0x64E6, 0x731C, 0x88C1, 0x6750, 0x624D,
    0x8D22, 0x776C, 0x8E29, 0x91C7, 0x5F69, 0x83DC, 0x8521, 0x9910, 0x53C2,
    0x8695, 0x6B8B, 0x60ED, 0x60E8, 0x707F, 0x82CD, 0x8231, 0x4ED3, 0x6CA7,
    0x85CF, 0x64CD, 0x7CD9, 0x69FD, 0x66F9, 0x8349, 0x5395, 0x7B56, 0x4FA7,
    0x518C, 0x6D4B, 0x5C42, 0x8E6D, 0x63D2, 0x53C9, 0x832C, 0x8336, 0x67E5,
    0x78B4, 0x643D, 0x5BDF, 0x5C94, 0x5DEE, 0x8BE7, 0x62C6, 0x67F4, 0x8C7A,
    0x6400, 0x63BA, 0x8749, 0x998B, 0x8C17, 0x7F20, 0x94F2, 0x4EA7, 0x9610,
    0x98A4, 0x660C, 0x7316, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x573A, 0x5C1D, 0x5E38, 0x957F, 0x507F, 0x80A0,
    0x5382, 0x655E, 0x7545, 0x5531, 0x5021, 0x8D85, 0x6284, 0x949E, 0x671D,
    0x5632, 0x6F6E, 0x5DE2, 0x5435, 0x7092, 0x8F66, 0x626F, 0x64A4, 0x63A3,
    0x5F7B, 0x6F88, 0x90F4, 0x81E3, 0x8FB0, 0x5C18, 0x6668, 0x5FF1, 0x6C89,
    0x9648, 0x8D81, 0x886C, 0x6491, 0x79F0, 0x57CE, 0x6A59, 0x6210, 0x5448,
    0x4E58, 0x7A0B, 0x60E9, 0x6F84, 0x8BDA, 0x627F, 0x901E, 0x9A8B, 0x79E4,
    0x5403, 0x75F4, 0x6301, 0x5319, 0x6C60, 0x8FDF, 0x5F1B, 0x9A70, 0x803B,
    0x9F7F, 0x4F88, 0x5C3A, 0x8D64, 0x7FC5, 0x65A5, 0x70BD, 0x5145, 0x51B2,
    0x866B, 0x5D07, 0x5BA0, 0x62BD, 0x916C, 0x7574, 0x8E0C, 0x7A20, 0x6101,
    0x7B79, 0x4EC7, 0x7EF8, 0x7785, 0x4E11, 0x81ED, 0x521D, 0x51FA, 0x6A71,
    0x53A8, 0x8E87, 0x9504, 0x96CF, 0x6EC1, 0x9664, 0x695A, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x7840, 0x50A8,
    0x77D7, 0x6410, 0x89E6, 0x5904, 0x63E3, 0x5DDD, 0x7A7F, 0x693D, 0x4F20,
    0x8239, 0x5598, 0x4E32, 0x75AE, 0x7A97, 0x5E62, 0x5E8A, 0x95EF, 0x521B,
    0x5439, 0x708A, 0x6376, 0x9524, 0x5782, 0x6625, 0x693F, 0x9187, 0x5507,
    0x6DF3, 0x7EAF, 0x8822, 0x6233, 0x7EF0, 0x75B5, 0x8328, 0x78C1, 0x96CC,
    0x8F9E, 0x6148, 0x74F7, 0x8BCD, 0x6B64, 0x523A, 0x8D50, 0x6B21, 0x806A,
    0x8471, 0x56F1, 0x5306, 0x4ECE, 0x4E1B, 0x51D1, 0x7C97, 0x918B, 0x7C07,
    0x4FC3, 0x8E7F, 0x7BE1, 0x7A9C, 0x6467, 0x5D14, 0x50AC, 0x8106, 0x7601,
    0x7CB9, 0x6DEC, 0x7FE0, 0x6751, 0x5B58, 0x5BF8, 0x78CB, 0x64AE, 0x6413,
    0x63AA, 0x632B, 0x9519, 0x642D, 0x8FBE, 0x7B54, 0x7629, 0x6253, 0x5927,
    0x5446, 0x6B79, 0x50A3, 0x6234, 0x5E26, 0x6B86, 0x4EE3, 0x8D37, 0x888B,
    0x5F85, 0x902E, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6020, 0x803D, 0x62C5, 0x4E39, 0x5355, 0x90F8, 0x63B8,
    0x80C6, 0x65E6, 0x6C2E, 0x4F46, 0x60EE, 0x6DE1, 0x8BDE, 0x5F39, 0x86CB,
    0x5F53, 0x6321, 0x515A, 0x8361, 0x6863, 0x5200, 0x6363, 0x8E48, 0x5012,
    0x5C9B, 0x7977, 0x5BFC, 0x5230, 0x7A3B, 0x60BC, 0x9053, 0x76D7, 0x5FB7,
    0x5F97, 0x7684, 0x8E6C, 0x706F, 0x767B, 0x7B49, 0x77AA, 0x51F3, 0x9093,
    0x5824, 0x4F4E, 0x6EF4, 0x8FEA, 0x654C, 0x7B1B, 0x72C4, 0x6DA4, 0x7FDF,
    0x5AE1, 0x62B5, 0x5E95, 0x5730, 0x8482, 0x7B2C, 0x5E1D, 0x5F1F, 0x9012,
    0x7F14, 0x98A0, 0x6382, 0x6EC7, 0x7898, 0x70B9, 0x5178, 0x975B, 0x57AB,
    0x7535, 0x4F43, 0x7538, 0x5E97, 0x60E6, 0x5960, 0x6DC0, 0x6BBF, 0x7889,
    0x53FC, 0x96D5, 0x51CB, 0x5201, 0x6389, 0x540A, 0x9493, 0x8C03, 0x8DCC,
    0x7239, 0x789F, 0x8776, 0x8FED, 0x8C0D, 0x53E0, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x4E01, 0x76EF, 0x53EE,
    0x9489, 0x9876, 0x9F0E, 0x952D, 0x5B9A, 0x8BA2, 0x4E22, 0x4E1C, 0x51AC,
    0x8463, 0x61C2, 0x52A8, 0x680B, 0x4F97, 0x606B, 0x51BB, 0x6D1E, 0x515C,
    0x6296, 0x6597, 0x9661, 0x8C46, 0x9017, 0x75D8, 0x90FD, 0x7763, 0x6BD2,
    0x728A, 0x72EC, 0x8BFB, 0x5835, 0x7779, 0x8D4C, 0x675C, 0x9540, 0x809A,
    0x5EA6, 0x6E21, 0x5992, 0x7AEF, 0x77ED, 0x953B, 0x6BB5, 0x65AD, 0x7F0E,
    0x5806, 0x5151, 0x961F, 0x5BF9, 0x58A9, 0x5428, 0x8E72, 0x6566, 0x987F,
    0x56E4, 0x949D, 0x76FE, 0x9041, 0x6387, 0x54C6, 0x591A, 0x593A, 0x579B,
    0x8EB2, 0x6735, 0x8DFA, 0x8235, 0x5241, 0x60F0, 0x5815, 0x86FE, 0x5CE8,
    0x9E45, 0x4FC4, 0x989D, 0x8BB9, 0x5A25, 0x6076, 0x5384, 0x627C, 0x904F,
    0x9102, 0x997F, 0x6069, 0x800C, 0x513F, 0x8033, 0x5C14, 0x9975, 0x6D31,
    0x4E8C, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x8D30, 0x53D1, 0x7F5A, 0x7B4F, 0x4F10, 0x4E4F, 0x9600, 0x6CD5,
    0x73D0, 0x85E9, 0x5E06, 0x756A, 0x7FFB, 0x6A0A, 0x77FE, 0x9492, 0x7E41,
    0x51E1, 0x70E6, 0x53CD, 0x8FD4, 0x8303, 0x8D29, 0x72AF, 0x996D, 0x6CDB,
    0x574A, 0x82B3, 0x65B9, 0x80AA, 0x623F, 0x9632, 0x59A8, 0x4EFF, 0x8BBF,
    0x7EBA, 0x653E, 0x83F2, 0x975E, 0x5561, 0x98DE, 0x80A5, 0x532A, 0x8BFD,
    0x5420, 0x80BA, 0x5E9F, 0x6CB8, 0x8D39, 0x82AC, 0x915A, 0x5429, 0x6C1B,
    0x5206, 0x7EB7, 0x575F, 0x711A, 0x6C7E, 0x7C89, 0x594B, 0x4EFD, 0x5FFF,
    0x6124, 0x7CAA, 0x4E30, 0x5C01, 0x67AB, 0x8702, 0x5CF0, 0x950B, 0x98CE,
    0x75AF, 0x70FD, 0x9022, 0x51AF, 0x7F1D, 0x8BBD, 0x5949, 0x51E4, 0x4F5B,
    0x5426, 0x592B, 0x6577, 0x80A4, 0x5B75, 0x6276, 0x62C2, 0x8F90, 0x5E45,
    0x6C1F, 0x7B26, 0x4F0F, 0x4FD8, 0x670D, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x6D6E, 0x6DAA, 0x798F, 0x88B1,
    0x5F17, 0x752B, 0x629A, 0x8F85, 0x4FEF, 0x91DC, 0x65A7, 0x812F, 0x8151,
    0x5E9C, 0x8150, 0x8D74, 0x526F, 0x8986, 0x8D4B, 0x590D, 0x5085, 0x4ED8,
    0x961C, 0x7236, 0x8179, 0x8D1F, 0x5BCC, 0x8BA3, 0x9644, 0x5987, 0x7F1A,
    0x5490, 0x5676, 0x560E, 0x8BE5, 0x6539, 0x6982, 0x9499, 0x76D6, 0x6E89,
    0x5E72, 0x7518, 0x6746, 0x67D1, 0x7AFF, 0x809D, 0x8D76, 0x611F, 0x79C6,
    0x6562, 0x8D63, 0x5188, 0x521A, 0x94A2, 0x7F38, 0x809B, 0x7EB2, 0x5C97,
    0x6E2F, 0x6760, 0x7BD9, 0x768B, 0x9AD8, 0x818F, 0x7F94, 0x7CD5, 0x641E,
    0x9550, 0x7A3F, 0x544A, 0x54E5, 0x6B4C, 0x6401, 0x6208, 0x9E3D, 0x80F3,
    0x7599, 0x5272, 0x9769, 0x845B, 0x683C, 0x86E4, 0x9601, 0x9694, 0x94EC,
    0x4E2A, 0x5404, 0x7ED9, 0x6839, 0x8DDF, 0x8015, 0x66F4, 0x5E9A, 0x7FB9,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x57C2, 0x803F, 0x6897, 0x5DE5, 0x653B, 0x529F, 0x606D, 0x9F9A, 0x4F9B,
    0x8EAC, 0x516C, 0x5BAB, 0x5F13, 0x5DE9, 0x6C5E, 0x62F1, 0x8D21, 0x5171,
    0x94A9, 0x52FE, 0x6C9F, 0x82DF, 0x72D7, 0x57A2, 0x6784, 0x8D2D, 0x591F,
    0x8F9C, 0x83C7, 0x5495, 0x7B8D, 0x4F30, 0x6CBD, 0x5B64, 0x59D1, 0x9F13,
    0x53E4, 0x86CA, 0x9AA8, 0x8C37, 0x80A1, 0x6545, 0x987E, 0x56FA, 0x96C7,
    0x522E, 0x74DC, 0x5250, 0x5BE1, 0x6302, 0x8902, 0x4E56, 0x62D0, 0x602A,
    0x68FA, 0x5173, 0x5B98, 0x51A0, 0x89C2, 0x7BA1, 0x9986, 0x7F50, 0x60EF,
    0x704C, 0x8D2F, 0x5149, 0x5E7F, 0x901B, 0x7470, 0x89C4, 0x572D, 0x7845,
    0x5F52, 0x9F9F, 0x95FA, 0x8F68, 0x9B3C, 0x8BE1, 0x7678, 0x6842, 0x67DC,
    0x8DEA, 0x8D35, 0x523D, 0x8F8A, 0x6EDA, 0x68CD, 0x9505, 0x90ED, 0x56FD,
    0x679C, 0x88F9, 0x8FC7, 0x54C8, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x9AB8, 0x5B69, 0x6D77, 0x6C26, 0x4EA5,
    0x5BB3, 0x9A87, 0x9163, 0x61A8, 0x90AF, 0x97E9, 0x542B, 0x6DB5, 0x5BD2,
    0x51FD, 0x558A, 0x7F55, 0x7FF0, 0x64BC, 0x634D, 0x65F1, 0x61BE, 0x608D,
    0x710A, 0x6C57, 0x6C49, 0x592F, 0x676D, 0x822A, 0x58D5, 0x568E, 0x8C6A,
    0x6BEB, 0x90DD, 0x597D, 0x8017, 0x53F7, 0x6D69, 0x5475, 0x559D, 0x8377,
    0x83CF, 0x6838, 0x79BE, 0x548C, 0x4F55, 0x5408, 0x76D2, 0x8C89, 0x9602,
    0x6CB3, 0x6DB8, 0x8D6B, 0x8910, 0x9E64, 0x8D3A, 0x563F, 0x9ED1, 0x75D5,
    0x5F88, 0x72E0, 0x6068, 0x54FC, 0x4EA8, 0x6A2A, 0x8861, 0x6052, 0x8F70,
    0x54C4, 0x70D8, 0x8679, 0x9E3F, 0x6D2A, 0x5B8F, 0x5F18, 0x7EA2, 0x5589,
    0x4FAF, 0x7334, 0x543C, 0x539A, 0x5019, 0x540E, 0x547C, 0x4E4E, 0x5FFD,
    0x745A, 0x58F6, 0x846B, 0x80E1, 0x8774, 0x72D0, 0x7CCA, 0x6E56, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x5F27,
    0x864E, 0x552C, 0x62A4, 0x4E92, 0x6CAA, 0x6237, 0x82B1, 0x54D7, 0x534E,
    0x733E, 0x6ED1, 0x753B, 0x5212, 0x5316, 0x8BDD, 0x69D0, 0x5F8A, 0x6000,
    0x6DEE, 0x574F, 0x6B22, 0x73AF, 0x6853, 0x8FD8, 0x7F13, 0x6362, 0x60A3,
    0x5524, 0x75EA, 0x8C62, 0x7115, 0x6DA3, 0x5BA6, 0x5E7B, 0x8352, 0x614C,
    0x9EC4, 0x78FA, 0x8757, 0x7C27, 0x7687, 0x51F0, 0x60F6, 0x714C, 0x6643,
    0x5E4C, 0x604D, 0x8C0E, 0x7070, 0x6325, 0x8F89, 0x5FBD, 0x6062, 0x86D4,
    0x56DE, 0x6BC1, 0x6094, 0x6167, 0x5349, 0x60E0, 0x6666, 0x8D3F, 0x79FD,
    0x4F1A, 0x70E9, 0x6C47, 0x8BB3, 0x8BF2, 0x7ED8, 0x8364, 0x660F, 0x5A5A,
    0x9B42, 0x6D51, 0x6DF7, 0x8C41, 0x6D3B, 0x4F19, 0x706B, 0x83B7, 0x6216,
    0x60D1, 0x970D, 0x8D27, 0x7978, 0x51FB, 0x573E, 0x57FA, 0x673A, 0x7578,
    0x7A3D, 0x79EF, 0x7B95, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x808C, 0x9965, 0x8FF9, 0x6FC0, 0x8BA5, 0x9E21,
    0x59EC, 0x7EE9, 0x7F09, 0x5409, 0x6781, 0x68D8, 0x8F91, 0x7C4D, 0x96C6,
    0x53CA, 0x6025, 0x75BE, 0x6C72, 0x5373, 0x5AC9, 0x7EA7, 0x6324, 0x51E0,
    0x810A, 0x5DF1, 0x84DF, 0x6280, 0x5180, 0x5B63, 0x4F0E, 0x796D, 0x5242,
    0x60B8, 0x6D4E, 0x5BC4, 0x5BC2, 0x8BA1, 0x8BB0, 0x65E2, 0x5FCC, 0x9645,
    0x5993, 0x7EE7, 0x7EAA, 0x5609, 0x67B7, 0x5939, 0x4F73, 0x5BB6, 0x52A0,
    0x835A, 0x988A, 0x8D3E, 0x7532, 0x94BE, 0x5047, 0x7A3C, 0x4EF7, 0x67B6,
    0x9A7E, 0x5AC1, 0x6B7C, 0x76D1, 0x575A, 0x5C16, 0x7B3A, 0x95F4, 0x714E,
    0x517C, 0x80A9, 0x8270, 0x5978, 0x7F04, 0x8327, 0x68C0, 0x67EC, 0x78B1,
    0x7877, 0x62E3, 0x6361, 0x7B80, 0x4FED, 0x526A, 0x51CF, 0x8350, 0x69DB,
    0x9274, 0x8DF5, 0x8D31, 0x89C1, 0x952E, 0x7BAD, 0x4EF6, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x5065, 0x8230,
    0x5251, 0x996F, 0x6E10, 0x6E85, 0x6DA7, 0x5EFA, 0x50F5, 0x59DC, 0x5C06,
    0x6D46, 0x6C5F, 0x7586, 0x848B, 0x6868, 0x5956, 0x8BB2, 0x5320, 0x9171,
    0x964D, 0x8549, 0x6912, 0x7901, 0x7126, 0x80F6, 0x4EA4, 0x90CA, 0x6D47,
    0x9A84, 0x5A07, 0x56BC, 0x6405, 0x94F0, 0x77EB, 0x4FA5, 0x811A, 0x72E1,
    0x89D2, 0x997A, 0x7F34, 0x7EDE, 0x527F, 0x6559, 0x9175, 0x8F7F, 0x8F83,
    0x53EB, 0x7A96, 0x63ED, 0x63A5, 0x7686, 0x79F8, 0x8857, 0x9636, 0x622A,
    0x52AB, 0x8282, 0x6854, 0x6770, 0x6377, 0x776B, 0x7AED, 0x6D01, 0x7ED3,
    0x89E3, 0x59D0, 0x6212, 0x85C9, 0x82A5, 0x754C, 0x501F, 0x4ECB, 0x75A5,
    0x8BEB, 0x5C4A, 0x5DFE, 0x7B4B, 0x65A4, 0x91D1, 0x4ECA, 0x6D25, 0x895F,
    0x7D27, 0x9526, 0x4EC5, 0x8C28, 0x8FDB, 0x9773, 0x664B, 0x7981, 0x8FD1,
    0x70EC, 0x6D78, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x5C3D, 0x52B2, 0x8346, 0x5162, 0x830E, 0x775B, 0x6676,
    0x9CB8, 0x4EAC, 0x60CA, 0x7CBE, 0x7CB3, 0x7ECF, 0x4E95, 0x8B66, 0x666F,
    0x9888, 0x9759, 0x5883, 0x656C, 0x955C, 0x5F84, 0x75C9, 0x9756, 0x7ADF,
    0x7ADE, 0x51C0, 0x70AF, 0x7A98, 0x63EA, 0x7A76, 0x7EA0, 0x7396, 0x97ED,
    0x4E45, 0x7078, 0x4E5D, 0x9152, 0x53A9, 0x6551, 0x65E7, 0x81FC, 0x8205,
    0x548E, 0x5C31, 0x759A, 0x97A0, 0x62D8, 0x72D9, 0x75BD, 0x5C45, 0x9A79,
    0x83CA, 0x5C40, 0x5480, 0x77E9, 0x4E3E, 0x6CAE, 0x805A, 0x62D2, 0x636E,
    0x5DE8, 0x5177, 0x8DDD, 0x8E1E, 0x952F, 0x4FF1, 0x53E5, 0x60E7, 0x70AC,
    0x5267, 0x6350, 0x9E43, 0x5A1F, 0x5026, 0x7737, 0x5377, 0x7EE2, 0x6485,
    0x652B, 0x6289, 0x6398, 0x5014, 0x7235, 0x89C9, 0x51B3, 0x8BC0, 0x7EDD,
    0x5747, 0x83CC, 0x94A7, 0x519B, 0x541B, 0x5CFB, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x4FCA, 0x7AE3, 0x6D5A,
    0x90E1, 0x9A8F, 0x5580, 0x5496, 0x5361, 0x54AF, 0x5F00, 0x63E9, 0x6977,
    0x51EF, 0x6168, 0x520A, 0x582A, 0x52D8, 0x574E, 0x780D, 0x770B, 0x5EB7,
    0x6177, 0x7CE0, 0x625B, 0x6297, 0x4EA2, 0x7095, 0x8003, 0x62F7, 0x70E4,
    0x9760, 0x5777, 0x82DB, 0x67EF, 0x68F5, 0x78D5, 0x9897, 0x79D1, 0x58F3,
    0x54B3, 0x53EF, 0x6E34, 0x514B, 0x523B, 0x5BA2, 0x8BFE, 0x80AF, 0x5543,
    0x57A6, 0x6073, 0x5751, 0x542D, 0x7A7A, 0x6050, 0x5B54, 0x63A7, 0x62A0,
    0x53E3, 0x6263, 0x5BC7, 0x67AF, 0x54ED, 0x7A9F, 0x82E6, 0x9177, 0x5E93,
    0x88E4, 0x5938, 0x57AE, 0x630E, 0x8DE8, 0x80EF, 0x5757, 0x7B77, 0x4FA9,
    0x5FEB, 0x5BBD, 0x6B3E, 0x5321, 0x7B50, 0x72C2, 0x6846, 0x77FF, 0x7736,
    0x65F7, 0x51B5, 0x4E8F, 0x76D4, 0x5CBF, 0x7AA5, 0x8475, 0x594E, 0x9B41,
    0x5080, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x9988, 0x6127, 0x6E83, 0x5764, 0x6606, 0x6346, 0x56F0, 0x62EC,
    0x6269, 0x5ED3, 0x9614, 0x5783, 0x62C9, 0x5587, 0x8721, 0x814A, 0x8FA3,
    0x5566, 0x83B1, 0x6765, 0x8D56, 0x84DD, 0x5A6A, 0x680F, 0x62E6, 0x7BEE,
    0x9611, 0x5170, 0x6F9C, 0x8C30, 0x63FD, 0x89C8, 0x61D2, 0x7F06, 0x70C2,
    0x6EE5, 0x7405, 0x6994, 0x72FC, 0x5ECA, 0x90CE, 0x6717, 0x6D6A, 0x635E,
    0x52B3, 0x7262, 0x8001, 0x4F6C, 0x59E5, 0x916A, 0x70D9, 0x6D9D, 0x52D2,
    0x4E50, 0x96F7, 0x956D, 0x857E, 0x78CA, 0x7D2F, 0x5121, 0x5792, 0x64C2,
    0x808B, 0x7C7B, 0x6CEA, 0x68F1, 0x695E, 0x51B7, 0x5398, 0x68A8, 0x7281,
    0x9ECE, 0x7BF1, 0x72F8, 0x79BB, 0x6F13, 0x7406, 0x674E, 0x91CC, 0x9CA4,
    0x793C, 0x8389, 0x8354, 0x540F, 0x6817, 0x4E3D, 0x5389, 0x52B1, 0x783E,
    0x5386, 0x5229, 0x5088, 0x4F8B, 0x4FD0, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x75E2, 0x7ACB, 0x7C92, 0x6CA5,
    0x96B6, 0x529B, 0x7483, 0x54E9, 0x4FE9, 0x8054, 0x83B2, 0x8FDE, 0x9570,
    0x5EC9, 0x601C, 0x6D9F, 0x5E18, 0x655B, 0x8138, 0x94FE, 0x604B, 0x70BC,
    0x7EC3, 0x7CAE, 0x51C9, 0x6881, 0x7CB1, 0x826F, 0x4E24, 0x8F86, 0x91CF,
    0x667E, 0x4EAE, 0x8C05, 0x64A9, 0x804A, 0x50DA, 0x7597, 0x71CE, 0x5BE5,
    0x8FBD, 0x6F66, 0x4E86, 0x6482, 0x9563, 0x5ED6, 0x6599, 0x5217, 0x88C2,
    0x70C8, 0x52A3, 0x730E, 0x7433, 0x6797, 0x78F7, 0x9716, 0x4E34, 0x90BB,
    0x9CDE, 0x6DCB, 0x51DB, 0x8D41, 0x541D, 0x62CE, 0x73B2, 0x83F1, 0x96F6,
    0x9F84, 0x94C3, 0x4F36, 0x7F9A, 0x51CC, 0x7075, 0x9675, 0x5CAD, 0x9886,
    0x53E6, 0x4EE4, 0x6E9C, 0x7409, 0x69B4, 0x786B, 0x998F, 0x7559, 0x5218,
    0x7624, 0x6D41, 0x67F3, 0x516D, 0x9F99, 0x804B, 0x5499, 0x7B3C, 0x7ABF,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x9686, 0x5784, 0x62E2, 0x9647, 0x697C, 0x5A04, 0x6402, 0x7BD3, 0x6F0F,
    0x964B, 0x82A6, 0x5362, 0x9885, 0x5E90, 0x7089, 0x63B3, 0x5364, 0x864F,
    0x9C81, 0x9E93, 0x788C, 0x9732, 0x8DEF, 0x8D42, 0x9E7F, 0x6F5E, 0x7984,
    0x5F55, 0x9646, 0x622E, 0x9A74, 0x5415, 0x94DD, 0x4FA3, 0x65C5, 0x5C65,
    0x5C61, 0x7F15, 0x8651, 0x6C2F, 0x5F8B, 0x7387, 0x6EE4, 0x7EFF, 0x5CE6,
    0x631B, 0x5B6A, 0x6EE6, 0x5375, 0x4E71, 0x63A0, 0x7565, 0x62A1, 0x8F6E,
    0x4F26, 0x4ED1, 0x6CA6, 0x7EB6, 0x8BBA, 0x841D, 0x87BA, 0x7F57, 0x903B,
    0x9523, 0x7BA9, 0x9AA1, 0x88F8, 0x843D, 0x6D1B, 0x9A86, 0x7EDC, 0x5988,
    0x9EBB, 0x739B, 0x7801, 0x8682, 0x9A6C, 0x9A82, 0x561B, 0x5417, 0x57CB,
    0x4E70, 0x9EA6, 0x5356, 0x8FC8, 0x8109, 0x7792, 0x9992, 0x86EE, 0x6EE1,
    0x8513, 0x66FC, 0x6162, 0x6F2B, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x8C29, 0x8292, 0x832B, 0x76F2, 0x6C13,
    0x5FD9, 0x83BD, 0x732B, 0x8305, 0x951A, 0x6BDB, 0x77DB, 0x94C6, 0x536F,
    0x8302, 0x5192, 0x5E3D, 0x8C8C, 0x8D38, 0x4E48, 0x73AB, 0x679A, 0x6885,
    0x9176, 0x9709, 0x7164, 0x6CA1, 0x7709, 0x5A92, 0x9541, 0x6BCF, 0x7F8E,
    0x6627, 0x5BD0, 0x59B9, 0x5A9A, 0x95E8, 0x95F7, 0x4EEC, 0x840C, 0x8499,
    0x6AAC, 0x76DF, 0x9530, 0x731B, 0x68A6, 0x5B5F, 0x772F, 0x919A, 0x9761,
    0x7CDC, 0x8FF7, 0x8C1C, 0x5F25, 0x7C73, 0x79D8, 0x89C5, 0x6CCC, 0x871C,
    0x5BC6, 0x5E42, 0x68C9, 0x7720, 0x7EF5, 0x5195, 0x514D, 0x52C9, 0x5A29,
    0x7F05, 0x9762, 0x82D7, 0x63CF, 0x7784, 0x85D0, 0x79D2, 0x6E3A, 0x5E99,
    0x5999, 0x8511, 0x706D, 0x6C11, 0x62BF, 0x76BF, 0x654F, 0x60AF, 0x95FD,
    0x660E, 0x879F, 0x9E23, 0x94ED, 0x540D, 0x547D, 0x8C2C, 0x6478, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6479,
    0x8611, 0x6A21, 0x819C, 0x78E8, 0x6469, 0x9B54, 0x62B9, 0x672B, 0x83AB,
    0x58A8, 0x9ED8, 0x6CAB, 0x6F20, 0x5BDE, 0x964C, 0x8C0B, 0x725F, 0x67D0,
    0x62C7, 0x7261, 0x4EA9, 0x59C6, 0x6BCD, 0x5893, 0x66AE, 0x5E55, 0x52DF,
    0x6155, 0x6728, 0x76EE, 0x7766, 0x7267, 0x7A46, 0x62FF, 0x54EA, 0x5450,
    0x94A0, 0x90A3, 0x5A1C, 0x7EB3, 0x6C16, 0x4E43, 0x5976, 0x8010, 0x5948,
    0x5357, 0x7537, 0x96BE, 0x56CA, 0x6320, 0x8111, 0x607C, 0x95F9, 0x6DD6,
    0x5462, 0x9981, 0x5185, 0x5AE9, 0x80FD, 0x59AE, 0x9713, 0x502A, 0x6CE5,
    0x5C3C, 0x62DF, 0x4F60, 0x533F, 0x817B, 0x9006, 0x6EBA, 0x852B, 0x62C8,
    0x5E74, 0x78BE, 0x64B5, 0x637B, 0x5FF5, 0x5A18, 0x917F, 0x9E1F, 0x5C3F,
    0x634F, 0x8042, 0x5B7D, 0x556E, 0x954A, 0x954D, 0x6D85, 0x60A8, 0x67E0,
    0x72DE, 0x51DD, 0x5B81, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x62E7, 0x6CDE, 0x725B, 0x626D, 0x94AE, 0x7EBD,
    0x8113, 0x6D53, 0x519C, 0x5F04, 0x5974, 0x52AA, 0x6012, 0x5973, 0x6696,
    0x8650, 0x759F, 0x632A, 0x61E6, 0x7CEF, 0x8BFA, 0x54E6, 0x6B27, 0x9E25,
    0x6BB4, 0x85D5, 0x5455, 0x5076, 0x6CA4, 0x556A, 0x8DB4, 0x722C, 0x5E15,
    0x6015, 0x7436, 0x62CD, 0x6392, 0x724C, 0x5F98, 0x6E43, 0x6D3E, 0x6500,
    0x6F58, 0x76D8, 0x78D0, 0x76FC, 0x7554, 0x5224, 0x53DB, 0x4E53, 0x5E9E,
    0x65C1, 0x802A, 0x80D6, 0x629B, 0x5486, 0x5228, 0x70AE, 0x888D, 0x8DD1,
    0x6CE1, 0x5478, 0x80DA, 0x57F9, 0x88F4, 0x8D54, 0x966A, 0x914D, 0x4F69,
    0x6C9B, 0x55B7, 0x76C6, 0x7830, 0x62A8, 0x70F9, 0x6F8E, 0x5F6D, 0x84EC,
    0x68DA, 0x787C, 0x7BF7, 0x81A8, 0x670B, 0x9E4F, 0x6367, 0x78B0, 0x576F,
    0x7812, 0x9739, 0x6279, 0x62AB, 0x5288, 0x7435, 0x6BD7, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x5564, 0x813E,
    0x75B2, 0x76AE, 0x5339, 0x75DE, 0x50FB, 0x5C41, 0x8B6C, 0x7BC7, 0x504F,
    0x7247, 0x9A97, 0x98D8, 0x6F02, 0x74E2, 0x7968, 0x6487, 0x77A5, 0x62FC,
    0x9891, 0x8D2B, 0x54C1, 0x8058, 0x4E52, 0x576A, 0x82F9, 0x840D, 0x5E73,
    0x51ED, 0x74F6, 0x8BC4, 0x5C4F, 0x5761, 0x6CFC, 0x9887, 0x5A46, 0x7834,
    0x9B44, 0x8FEB, 0x7C95, 0x5256, 0x6251, 0x94FA, 0x4EC6, 0x8386, 0x8461,
    0x83E9, 0x84B2, 0x57D4, 0x6734, 0x5703, 0x666E, 0x6D66, 0x8C31, 0x66DD,
    0x7011, 0x671F, 0x6B3A, 0x6816, 0x621A, 0x59BB, 0x4E03, 0x51C4, 0x6F06,
    0x67D2, 0x6C8F, 0x5176, 0x68CB, 0x5947, 0x6B67, 0x7566, 0x5D0E, 0x8110,
    0x9F50, 0x65D7, 0x7948, 0x7941, 0x9A91, 0x8D77, 0x5C82, 0x4E5E, 0x4F01,
    0x542F, 0x5951, 0x780C, 0x5668, 0x6C14, 0x8FC4, 0x5F03, 0x6C7D, 0x6CE3,
    0x8BAB, 0x6390, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6070, 0x6D3D, 0x7275, 0x6266, 0x948E, 0x94C5, 0x5343,
    0x8FC1, 0x7B7E, 0x4EDF, 0x8C26, 0x4E7E, 0x9ED4, 0x94B1, 0x94B3, 0x524D,
    0x6F5C, 0x9063, 0x6D45, 0x8C34, 0x5811, 0x5D4C, 0x6B20, 0x6B49, 0x67AA,
    0x545B, 0x8154, 0x7F8C, 0x5899, 0x8537, 0x5F3A, 0x62A2, 0x6A47, 0x9539,
    0x6572, 0x6084, 0x6865, 0x77A7, 0x4E54, 0x4FA8, 0x5DE7, 0x9798, 0x64AC,
    0x7FD8, 0x5CED, 0x4FCF, 0x7A8D, 0x5207, 0x8304, 0x4E14, 0x602F, 0x7A83,
    0x94A6, 0x4FB5, 0x4EB2, 0x79E6, 0x7434, 0x52E4, 0x82B9, 0x64D2, 0x79BD,
    0x5BDD, 0x6C81, 0x9752, 0x8F7B, 0x6C22, 0x503E, 0x537F, 0x6E05, 0x64CE,
    0x6674, 0x6C30, 0x60C5, 0x9877, 0x8BF7, 0x5E86, 0x743C, 0x7A77, 0x79CB,
    0x4E18, 0x90B1, 0x7403, 0x6C42, 0x56DA, 0x914B, 0x6CC5, 0x8D8B, 0x533A,
    0x86C6, 0x66F2, 0x8EAF, 0x5C48, 0x9A71, 0x6E20, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x53D6, 0x5A36, 0x9F8B,
    0x8DA3, 0x53BB, 0x5708, 0x98A7, 0x6743, 0x919B, 0x6CC9, 0x5168, 0x75CA,
    0x62F3, 0x72AC, 0x5238, 0x529D, 0x7F3A, 0x7094, 0x7638, 0x5374, 0x9E4A,
    0x69B7, 0x786E, 0x96C0, 0x88D9, 0x7FA4, 0x7136, 0x71C3, 0x5189, 0x67D3,
    0x74E4, 0x58E4, 0x6518, 0x56B7, 0x8BA9, 0x9976, 0x6270, 0x7ED5, 0x60F9,
    0x70ED, 0x58EC, 0x4EC1, 0x4EBA, 0x5FCD, 0x97E7, 0x4EFB, 0x8BA4, 0x5203,
    0x598A, 0x7EAB, 0x6254, 0x4ECD, 0x65E5, 0x620E, 0x8338, 0x84C9, 0x8363,
    0x878D, 0x7194, 0x6EB6, 0x5BB9, 0x7ED2, 0x5197, 0x63C9, 0x67D4, 0x8089,
    0x8339, 0x8815, 0x5112, 0x5B7A, 0x5982, 0x8FB1, 0x4E73, 0x6C5D, 0x5165,
    0x8925, 0x8F6F, 0x962E, 0x854A, 0x745E, 0x9510, 0x95F0, 0x6DA6, 0x82E5,
    0x5F31, 0x6492, 0x6D12, 0x8428, 0x816E, 0x9CC3, 0x585E, 0x8D5B, 0x4E09,
    0x53C1, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x4F1E, 0x6563, 0x6851, 0x55D3, 0x4E27, 0x6414, 0x9A9A, 0x626B,
    0x5AC2, 0x745F, 0x8272, 0x6DA9, 0x68EE, 0x50E7, 0x838E, 0x7802, 0x6740,
    0x5239, 0x6C99, 0x7EB1, 0x50BB, 0x5565, 0x715E, 0x7B5B, 0x6652, 0x73CA,
    0x82EB, 0x6749, 0x5C71, 0x5220, 0x717D, 0x886B, 0x95EA, 0x9655, 0x64C5,
    0x8D61, 0x81B3, 0x5584, 0x6C55, 0x6247, 0x7F2E, 0x5892, 0x4F24, 0x5546,
    0x8D4F, 0x664C, 0x4E0A, 0x5C1A, 0x88F3, 0x68A2, 0x634E, 0x7A0D, 0x70E7,
    0x828D, 0x52FA, 0x97F6, 0x5C11, 0x54E8, 0x90B5, 0x7ECD, 0x5962, 0x8D4A,
    0x86C7, 0x820C, 0x820D, 0x8D66, 0x6444, 0x5C04, 0x6151, 0x6D89, 0x793E,
    0x8BBE, 0x7837, 0x7533, 0x547B, 0x4F38, 0x8EAB, 0x6DF1, 0x5A20, 0x7EC5,
    0x795E, 0x6C88, 0x5BA1, 0x5A76, 0x751A, 0x80BE, 0x614E, 0x6E17, 0x58F0,
    0x751F, 0x7525, 0x7272, 0x5347, 0x7EF3, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x7701, 0x76DB, 0x5269, 0x80DC,
    0x5723, 0x5E08, 0x5931, 0x72EE, 0x65BD, 0x6E7F, 0x8BD7, 0x5C38, 0x8671,
    0x5341, 0x77F3, 0x62FE, 0x65F6, 0x4EC0, 0x98DF, 0x8680, 0x5B9E, 0x8BC6,
    0x53F2, 0x77E2, 0x4F7F, 0x5C4E, 0x9A76, 0x59CB, 0x5F0F, 0x793A, 0x58EB,
    0x4E16, 0x67FF, 0x4E8B, 0x62ED, 0x8A93, 0x901D, 0x52BF, 0x662F, 0x55DC,
    0x566C, 0x9002, 0x4ED5, 0x4F8D, 0x91CA, 0x9970, 0x6C0F, 0x5E02, 0x6043,
    0x5BA4, 0x89C6, 0x8BD5, 0x6536, 0x624B, 0x9996, 0x5B88, 0x5BFF, 0x6388,
    0x552E, 0x53D7, 0x7626, 0x517D, 0x852C, 0x67A2, 0x68B3, 0x6B8A, 0x6292,
    0x8F93, 0x53D4, 0x8212, 0x6DD1, 0x758F, 0x4E66, 0x8D4E, 0x5B70, 0x719F,
    0x85AF, 0x6691, 0x66D9, 0x7F72, 0x8700, 0x9ECD, 0x9F20, 0x5C5E, 0x672F,
    0x8FF0, 0x6811, 0x675F, 0x620D, 0x7AD6, 0x5885, 0x5EB6, 0x6570, 0x6F31,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x6055, 0x5237, 0x800D, 0x6454, 0x8870, 0x7529, 0x5E05, 0x6813, 0x62F4,
    0x971C, 0x53CC, 0x723D, 0x8C01, 0x6C34, 0x7761, 0x7A0E, 0x542E, 0x77AC,
    0x987A, 0x821C, 0x8BF4, 0x7855, 0x6714, 0x70C1, 0x65AF, 0x6495, 0x5636,
    0x601D, 0x79C1, 0x53F8, 0x4E1D, 0x6B7B, 0x8086, 0x5BFA, 0x55E3, 0x56DB,
    0x4F3A, 0x4F3C, 0x9972, 0x5DF3, 0x677E, 0x8038, 0x6002, 0x9882, 0x9001,
    0x5B8B, 0x8BBC, 0x8BF5, 0x641C, 0x8258, 0x64DE, 0x55FD, 0x82CF, 0x9165,
    0x4FD7, 0x7D20, 0x901F, 0x7C9F, 0x50F3, 0x5851, 0x6EAF, 0x5BBF, 0x8BC9,
    0x8083, 0x9178, 0x849C, 0x7B97, 0x867D, 0x968B, 0x968F, 0x7EE5, 0x9AD3,
    0x788E, 0x5C81, 0x7A57, 0x9042, 0x96A7, 0x795F, 0x5B59, 0x635F, 0x7B0B,
    0x84D1, 0x68AD, 0x5506, 0x7F29, 0x7410, 0x7D22, 0x9501, 0x6240, 0x584C,
    0x4ED6, 0x5B83, 0x5979, 0x5854, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x736D, 0x631E, 0x8E4B, 0x8E0F, 0x80CE,
    0x82D4, 0x62AC, 0x53F0, 0x6CF0, 0x915E, 0x592A, 0x6001, 0x6C70, 0x574D,
    0x644A, 0x8D2A, 0x762B, 0x6EE9, 0x575B, 0x6A80, 0x75F0, 0x6F6D, 0x8C2D,
    0x8C08, 0x5766, 0x6BEF, 0x8892, 0x78B3, 0x63A2, 0x53F9, 0x70AD, 0x6C64,
    0x5858, 0x642A, 0x5802, 0x68E0, 0x819B, 0x5510, 0x7CD6, 0x5018, 0x8EBA,
    0x6DCC, 0x8D9F, 0x70EB, 0x638F, 0x6D9B, 0x6ED4, 0x7EE6, 0x8404, 0x6843,
    0x9003, 0x6DD8, 0x9676, 0x8BA8, 0x5957, 0x7279, 0x85E4, 0x817E, 0x75BC,
    0x8A8A, 0x68AF, 0x5254, 0x8E22, 0x9511, 0x63D0, 0x9898, 0x8E44, 0x557C,
    0x4F53, 0x66FF, 0x568F, 0x60D5, 0x6D95, 0x5243, 0x5C49, 0x5929, 0x6DFB,
    0x586B, 0x7530, 0x751C, 0x606C, 0x8214, 0x8146, 0x6311, 0x6761, 0x8FE2,
    0x773A, 0x8DF3, 0x8D34, 0x94C1, 0x5E16, 0x5385, 0x542C, 0x70C3, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6C40,
    0x5EF7, 0x505C, 0x4EAD, 0x5EAD, 0x633A, 0x8247, 0x901A, 0x6850, 0x916E,
    0x77B3, 0x540C, 0x94DC, 0x5F64, 0x7AE5, 0x6876, 0x6345, 0x7B52, 0x7EDF,
    0x75DB, 0x5077, 0x6295, 0x5934, 0x900F, 0x51F8, 0x79C3, 0x7A81, 0x56FE,
    0x5F92, 0x9014, 0x6D82, 0x5C60, 0x571F, 0x5410, 0x5154, 0x6E4D, 0x56E2,
    0x63A8, 0x9893, 0x817F, 0x8715, 0x892A, 0x9000, 0x541E, 0x5C6F, 0x81C0,
    0x62D6, 0x6258, 0x8131, 0x9E35, 0x9640, 0x9A6E, 0x9A7C, 0x692D, 0x59A5,
    0x62D3, 0x553E, 0x6316, 0x54C7, 0x86D9, 0x6D3C, 0x5A03, 0x74E6, 0x889C,
    0x6B6A, 0x5916, 0x8C4C, 0x5F2F, 0x6E7E, 0x73A9, 0x987D, 0x4E38, 0x70F7,
    0x5B8C, 0x7897, 0x633D, 0x665A, 0x7696, 0x60CB, 0x5B9B, 0x5A49, 0x4E07,
    0x8155, 0x6C6A, 0x738B, 0x4EA1, 0x6789, 0x7F51, 0x5F80, 0x65FA, 0x671B,
    0x5FD8, 0x5984, 0x5A01, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x5DCD, 0x5FAE, 0x5371, 0x97E6, 0x8FDD, 0x6845,
    0x56F4, 0x552F, 0x60DF, 0x4E3A, 0x6F4D, 0x7EF4, 0x82C7, 0x840E, 0x59D4,
    0x4F1F, 0x4F2A, 0x5C3E, 0x7EAC, 0x672A, 0x851A, 0x5473, 0x754F, 0x80C3,
    0x5582, 0x9B4F, 0x4F4D, 0x6E2D, 0x8C13, 0x5C09, 0x6170, 0x536B, 0x761F,
    0x6E29, 0x868A, 0x6587, 0x95FB, 0x7EB9, 0x543B, 0x7A33, 0x7D0A, 0x95EE,
    0x55E1, 0x7FC1, 0x74EE, 0x631D, 0x8717, 0x6DA1, 0x7A9D, 0x6211, 0x65A1,
    0x5367, 0x63E1, 0x6C83, 0x5DEB, 0x545C, 0x94A8, 0x4E4C, 0x6C61, 0x8BEC,
    0x5C4B, 0x65E0, 0x829C, 0x68A7, 0x543E, 0x5434, 0x6BCB, 0x6B66, 0x4E94,
    0x6342, 0x5348, 0x821E, 0x4F0D, 0x4FAE, 0x575E, 0x620A, 0x96FE, 0x6664,
    0x7269, 0x52FF, 0x52A1, 0x609F, 0x8BEF, 0x6614, 0x7199, 0x6790, 0x897F,
    0x7852, 0x77FD, 0x6670, 0x563B, 0x5438, 0x9521, 0x727A, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x7A00, 0x606F,
    0x5E0C, 0x6089, 0x819D, 0x5915, 0x60DC, 0x7184, 0x70EF, 0x6EAA, 0x6C50,
    0x7280, 0x6A84, 0x88AD, 0x5E2D, 0x4E60, 0x5AB3, 0x559C, 0x94E3, 0x6D17,
    0x7CFB, 0x9699, 0x620F, 0x7EC6, 0x778E, 0x867E, 0x5323, 0x971E, 0x8F96,
    0x6687, 0x5CE1, 0x4FA0, 0x72ED, 0x4E0B, 0x53A6, 0x590F, 0x5413, 0x6380,
    0x9528, 0x5148, 0x4ED9, 0x9C9C, 0x7EA4, 0x54B8, 0x8D24, 0x8854, 0x8237,
    0x95F2, 0x6D8E, 0x5F26, 0x5ACC, 0x663E, 0x9669, 0x73B0, 0x732E, 0x53BF,
    0x817A, 0x9985, 0x7FA1, 0x5BAA, 0x9677, 0x9650, 0x7EBF, 0x76F8, 0x53A2,
    0x9576, 0x9999, 0x7BB1, 0x8944, 0x6E58, 0x4E61, 0x7FD4, 0x7965, 0x8BE6,
    0x60F3, 0x54CD, 0x4EAB, 0x9879, 0x5DF7, 0x6A61, 0x50CF, 0x5411, 0x8C61,
    0x8427, 0x785D, 0x9704, 0x524A, 0x54EE, 0x56A3, 0x9500, 0x6D88, 0x5BB5,
    0x6DC6, 0x6653, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x5C0F, 0x5B5D, 0x6821, 0x8096, 0x5578, 0x7B11, 0x6548,
    0x6954, 0x4E9B, 0x6B47, 0x874E, 0x978B, 0x534F, 0x631F, 0x643A, 0x90AA,
    0x659C, 0x80C1, 0x8C10, 0x5199, 0x68B0, 0x5378, 0x87F9, 0x61C8, 0x6CC4,
    0x6CFB, 0x8C22, 0x5C51, 0x85AA, 0x82AF, 0x950C, 0x6B23, 0x8F9B, 0x65B0,
    0x5FFB, 0x5FC3, 0x4FE1, 0x8845, 0x661F, 0x8165, 0x7329, 0x60FA, 0x5174,
    0x5211, 0x578B, 0x5F62, 0x90A2, 0x884C, 0x9192, 0x5E78, 0x674F, 0x6027,
    0x59D3, 0x5144, 0x51F6, 0x80F8, 0x5308, 0x6C79, 0x96C4, 0x718A, 0x4F11,
    0x4FEE, 0x7F9E, 0x673D, 0x55C5, 0x9508, 0x79C0, 0x8896, 0x7EE3, 0x589F,
    0x620C, 0x9700, 0x865A, 0x5618, 0x987B, 0x5F90, 0x8BB8, 0x84C4, 0x9157,
    0x53D9, 0x65ED, 0x5E8F, 0x755C, 0x6064, 0x7D6E, 0x5A7F, 0x7EEA, 0x7EED,
    0x8F69, 0x55A7, 0x5BA3, 0x60AC, 0x65CB, 0x7384, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x9009, 0x7663, 0x7729,
    0x7EDA, 0x9774, 0x859B, 0x5B66, 0x7A74, 0x96EA, 0x8840, 0x52CB, 0x718F,
    0x5FAA, 0x65EC, 0x8BE2, 0x5BFB, 0x9A6F, 0x5DE1, 0x6B89, 0x6C5B, 0x8BAD,
    0x8BAF, 0x900A, 0x8FC5, 0x538B, 0x62BC, 0x9E26, 0x9E2D, 0x5440, 0x4E2B,
    0x82BD, 0x7259, 0x869C, 0x5D16, 0x8859, 0x6DAF, 0x96C5, 0x54D1, 0x4E9A,
    0x8BB6, 0x7109, 0x54BD, 0x9609, 0x70DF, 0x6DF9, 0x76D0, 0x4E25, 0x7814,
    0x8712, 0x5CA9, 0x5EF6, 0x8A00, 0x989C, 0x960E, 0x708E, 0x6CBF, 0x5944,
    0x63A9, 0x773C, 0x884D, 0x6F14, 0x8273, 0x5830, 0x71D5, 0x538C, 0x781A,
    0x96C1, 0x5501, 0x5F66, 0x7130, 0x5BB4, 0x8C1A, 0x9A8C, 0x6B83, 0x592E,
    0x9E2F, 0x79E7, 0x6768, 0x626C, 0x4F6F, 0x75A1, 0x7F8A, 0x6D0B, 0x9633,
    0x6C27, 0x4EF0, 0x75D2, 0x517B, 0x6837, 0x6F3E, 0x9080, 0x8170, 0x5996,
    0x7476, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x6447, 0x5C27, 0x9065, 0x7A91, 0x8C23, 0x59DA, 0x54AC, 0x8200,
    0x836F, 0x8981, 0x8000, 0x6930, 0x564E, 0x8036, 0x7237, 0x91CE, 0x51B6,
    0x4E5F, 0x9875, 0x6396, 0x4E1A, 0x53F6, 0x66F3, 0x814B, 0x591C, 0x6DB2,
    0x4E00, 0x58F9, 0x533B, 0x63D6, 0x94F1, 0x4F9D, 0x4F0A, 0x8863, 0x9890,
    0x5937, 0x9057, 0x79FB, 0x4EEA, 0x80F0, 0x7591, 0x6C82, 0x5B9C, 0x59E8,
    0x5F5D, 0x6905, 0x8681, 0x501A, 0x5DF2, 0x4E59, 0x77E3, 0x4EE5, 0x827A,
    0x6291, 0x6613, 0x9091, 0x5C79, 0x4EBF, 0x5F79, 0x81C6, 0x9038, 0x8084,
    0x75AB, 0x4EA6, 0x88D4, 0x610F, 0x6BC5, 0x5FC6, 0x4E49, 0x76CA, 0x6EA2,
    0x8BE3, 0x8BAE, 0x8C0A, 0x8BD1, 0x5F02, 0x7FFC, 0x7FCC, 0x7ECE, 0x8335,
    0x836B, 0x56E0, 0x6BB7, 0x97F3, 0x9634, 0x59FB, 0x541F, 0x94F6, 0x6DEB,
    0x5BC5, 0x996E, 0x5C39, 0x5F15, 0x9690, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x5370, 0x82F1, 0x6A31, 0x5A74,
    0x9E70, 0x5E94, 0x7F28, 0x83B9, 0x8424, 0x8425, 0x8367, 0x8747, 0x8FCE,
    0x8D62, 0x76C8, 0x5F71, 0x9896, 0x786C, 0x6620, 0x54DF, 0x62E5, 0x4F63,
    0x81C3, 0x75C8, 0x5EB8, 0x96CD, 0x8E0A, 0x86F9, 0x548F, 0x6CF3, 0x6D8C,
    0x6C38, 0x607F, 0x52C7, 0x7528, 0x5E7D, 0x4F18, 0x60A0, 0x5FE7, 0x5C24,
    0x7531, 0x90AE, 0x94C0, 0x72B9, 0x6CB9, 0x6E38, 0x9149, 0x6709, 0x53CB,
    0x53F3, 0x4F51, 0x91C9, 0x8BF1, 0x53C8, 0x5E7C, 0x8FC2, 0x6DE4, 0x4E8E,
    0x76C2, 0x6986, 0x865E, 0x611A, 0x8206, 0x4F59, 0x4FDE, 0x903E, 0x9C7C,
    0x6109, 0x6E1D, 0x6E14, 0x9685, 0x4E88, 0x5A31, 0x96E8, 0x4E0E, 0x5C7F,
    0x79B9, 0x5B87, 0x8BED, 0x7FBD, 0x7389, 0x57DF, 0x828B, 0x90C1, 0x5401,
    0x9047, 0x55BB, 0x5CEA, 0x5FA1, 0x6108, 0x6B32, 0x72F1, 0x80B2, 0x8A89,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x6D74, 0x5BD3, 0x88D5, 0x9884, 0x8C6B, 0x9A6D, 0x9E33, 0x6E0A, 0x51A4,
    0x5143, 0x57A3, 0x8881, 0x539F, 0x63F4, 0x8F95, 0x56ED, 0x5458, 0x5706,
    0x733F, 0x6E90, 0x7F18, 0x8FDC, 0x82D1, 0x613F, 0x6028, 0x9662, 0x66F0,
    0x7EA6, 0x8D8A, 0x8DC3, 0x94A5, 0x5CB3, 0x7CA4, 0x6708, 0x60A6, 0x9605,
    0x8018, 0x4E91, 0x90E7, 0x5300, 0x9668, 0x5141, 0x8FD0, 0x8574, 0x915D,
    0x6655, 0x97F5, 0x5B55, 0x531D, 0x7838, 0x6742, 0x683D, 0x54C9, 0x707E,
    0x5BB0, 0x8F7D, 0x518D, 0x5728, 0x54B1, 0x6512, 0x6682, 0x8D5E, 0x8D43,
    0x810F, 0x846C, 0x906D, 0x7CDF, 0x51FF, 0x85FB, 0x67A3, 0x65E9, 0x6FA1,
    0x86A4, 0x8E81, 0x566A, 0x9020, 0x7682, 0x7076, 0x71E5, 0x8D23, 0x62E9,
    0x5219, 0x6CFD, 0x8D3C, 0x600E, 0x589E, 0x618E, 0x66FE, 0x8D60, 0x624E,
    0x55B3, 0x6E23, 0x672D, 0x8F67, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x94E1, 0x95F8, 0x7728, 0x6805, 0x69A8,
    0x548B, 0x4E4D, 0x70B8, 0x8BC8, 0x6458, 0x658B, 0x5B85, 0x7A84, 0x503A,
    0x5BE8, 0x77BB, 0x6BE1, 0x8A79, 0x7C98, 0x6CBE, 0x76CF, 0x65A9, 0x8F97,
    0x5D2D, 0x5C55, 0x8638, 0x6808, 0x5360, 0x6218, 0x7AD9, 0x6E5B, 0x7EFD,
    0x6A1F, 0x7AE0, 0x5F70, 0x6F33, 0x5F20, 0x638C, 0x6DA8, 0x6756, 0x4E08,
    0x5E10, 0x8D26, 0x4ED7, 0x80C0, 0x7634, 0x969C, 0x62DB, 0x662D, 0x627E,
    0x6CBC, 0x8D75, 0x7167, 0x7F69, 0x5146, 0x8087, 0x53EC, 0x906E, 0x6298,
    0x54F2, 0x86F0, 0x8F99, 0x8005, 0x9517, 0x8517, 0x8FD9, 0x6D59, 0x73CD,
    0x659F, 0x771F, 0x7504, 0x7827, 0x81FB, 0x8D1E, 0x9488, 0x4FA6, 0x6795,
    0x75B9, 0x8BCA, 0x9707, 0x632F, 0x9547, 0x9635, 0x84B8, 0x6323, 0x7741,
    0x5F81, 0x72F0, 0x4E89, 0x6014, 0x6574, 0x62EF, 0x6B63, 0x653F, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x5E27,
    0x75C7, 0x90D1, 0x8BC1, 0x829D, 0x679D, 0x652F, 0x5431, 0x8718, 0x77E5,
    0x80A2, 0x8102, 0x6C41, 0x4E4B, 0x7EC7, 0x804C, 0x76F4, 0x690D, 0x6B96,
    0x6267, 0x503C, 0x4F84, 0x5740, 0x6307, 0x6B62, 0x8DBE, 0x53EA, 0x65E8,
    0x7EB8, 0x5FD7, 0x631A, 0x63B7, 0x81F3, 0x81F4, 0x7F6E, 0x5E1C, 0x5CD9,
    0x5236, 0x667A, 0x79E9, 0x7A1A, 0x8D28, 0x7099, 0x75D4, 0x6EDE, 0x6CBB,
    0x7A92, 0x4E2D, 0x76C5, 0x5FE0, 0x949F, 0x8877, 0x7EC8, 0x79CD, 0x80BF,
    0x91CD, 0x4EF2, 0x4F17, 0x821F, 0x5468, 0x5DDE, 0x6D32, 0x8BCC, 0x7CA5,
    0x8F74, 0x8098, 0x5E1A, 0x5492, 0x76B1, 0x5B99, 0x663C, 0x9AA4, 0x73E0,
    0x682A, 0x86DB, 0x6731, 0x732A, 0x8BF8, 0x8BDB, 0x9010, 0x7AF9, 0x70DB,
    0x716E, 0x62C4, 0x77A9, 0x5631, 0x4E3B, 0x8457, 0x67F1, 0x52A9, 0x86C0,
    0x8D2E, 0x94F8, 0x7B51, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x4F4F, 0x6CE8, 0x795D, 0x9A7B, 0x6293, 0x722A,
    0x62FD, 0x4E13, 0x7816, 0x8F6C, 0x64B0, 0x8D5A, 0x7BC6, 0x6869, 0x5E84,
    0x88C5, 0x5986, 0x649E, 0x58EE, 0x72B6, 0x690E, 0x9525, 0x8FFD, 0x8D58,
    0x5760, 0x7F00, 0x8C06, 0x51C6, 0x6349, 0x62D9, 0x5353, 0x684C, 0x7422,
    0x8301, 0x914C, 0x5544, 0x7740, 0x707C, 0x6D4A, 0x5179, 0x54A8, 0x8D44,
    0x59FF, 0x6ECB, 0x6DC4, 0x5B5C, 0x7D2B, 0x4ED4, 0x7C7D, 0x6ED3, 0x5B50,
    0x81EA, 0x6E0D, 0x5B57, 0x9B03, 0x68D5, 0x8E2A, 0x5B97, 0x7EFC, 0x603B,
    0x7EB5, 0x90B9, 0x8D70, 0x594F, 0x63CD, 0x79DF, 0x8DB3, 0x5352, 0x65CF,
    0x7956, 0x8BC5, 0x963B, 0x7EC4, 0x94BB, 0x7E82, 0x5634, 0x9189, 0x6700,
    0x7F6A, 0x5C0A, 0x9075, 0x6628, 0x5DE6, 0x4F50, 0x67DE, 0x505A, 0x4F5C,
    0x5750, 0x5EA7, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x4E8D, 0x4E0C,
    0x5140, 0x4E10, 0x5EFF, 0x5345, 0x4E15, 0x4E98, 0x4E1E, 0x9B32, 0x5B6C,
    0x5669, 0x4E28, 0x79BA, 0x4E3F, 0x5315, 0x4E47, 0x592D, 0x723B, 0x536E,
    0x6C10, 0x56DF, 0x80E4, 0x9997, 0x6BD3, 0x777E, 0x9F17, 0x4E36, 0x4E9F,
    0x9F10, 0x4E5C, 0x4E69, 0x4E93, 0x8288, 0x5B5B, 0x556C, 0x560F, 0x4EC4,
    0x538D, 0x539D, 0x53A3, 0x53A5, 0x53AE, 0x9765, 0x8D5D, 0x531A, 0x53F5,
    0x5326, 0x532E, 0x533E, 0x8D5C, 0x5366, 0x5363, 0x5202, 0x5208, 0x520E,
    0x522D, 0x5233, 0x523F, 0x5240, 0x524C, 0x525E, 0x5261, 0x525C, 0x84AF,
    0x527D, 0x5282, 0x5281, 0x5290, 0x5293, 0x5182, 0x7F54, 0x4EBB, 0x4EC3,
    0x4EC9, 0x4EC2, 0x4EE8, 0x4EE1, 0x4EEB, 0x4EDE, 0x4F1B, 0x4EF3, 0x4F22,
    0x4F64, 0x4EF5, 0x4F25, 0x4F27, 0x4F09, 0x4F2B, 0x4F5E, 0x4F67, 0x6538,
    0x4F5A, 0x4F5D, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x4F5F, 0x4F57, 0x4F32, 0x4F3D, 0x4F76, 0x4F74, 0x4F91,
    0x4F89, 0x4F83, 0x4F8F, 0x4F7E, 0x4F7B, 0x4FAA, 0x4F7C, 0x4FAC, 0x4F94,
    0x4FE6, 0x4FE8, 0x4FEA, 0x4FC5, 0x4FDA, 0x4FE3, 0x4FDC, 0x4FD1, 0x4FDF,
    0x4FF8, 0x5029, 0x504C, 0x4FF3, 0x502C, 0x500F, 0x502E, 0x502D, 0x4FFE,
    0x501C, 0x500C, 0x5025, 0x5028, 0x507E, 0x5043, 0x5055, 0x5048, 0x504E,
    0x506C, 0x507B, 0x50A5, 0x50A7, 0x50A9, 0x50BA, 0x50D6, 0x5106, 0x50ED,
    0x50EC, 0x50E6, 0x50EE, 0x5107, 0x510B, 0x4EDD, 0x6C3D, 0x4F58, 0x4F65,
    0x4FCE, 0x9FA0, 0x6C46, 0x7C74, 0x516E, 0x5DFD, 0x9EC9, 0x9998, 0x5181,
    0x5914, 0x52F9, 0x530D, 0x8A07, 0x5310, 0x51EB, 0x5919, 0x5155, 0x4EA0,
    0x5156, 0x4EB3, 0x886E, 0x88A4, 0x4EB5, 0x8114, 0x88D2, 0x7980, 0x5B34,
    0x8803, 0x7FB8, 0x51AB, 0x51B1, 0x51BD, 0x51BC, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x51C7, 0x5196, 0x51A2,
    0x51A5, 0x8BA0, 0x8BA6, 0x8BA7, 0x8BAA, 0x8BB4, 0x8BB5, 0x8BB7, 0x8BC2,
    0x8BC3, 0x8BCB, 0x8BCF, 0x8BCE, 0x8BD2, 0x8BD3, 0x8BD4, 0x8BD6, 0x8BD8,
    0x8BD9, 0x8BDC, 0x8BDF, 0x8BE0, 0x8BE4, 0x8BE8, 0x8BE9, 0x8BEE, 0x8BF0,
    0x8BF3, 0x8BF6, 0x8BF9, 0x8BFC, 0x8BFF, 0x8C00, 0x8C02, 0x8C04, 0x8C07,
    0x8C0C, 0x8C0F, 0x8C11, 0x8C12, 0x8C14, 0x8C15, 0x8C16, 0x8C19, 0x8C1B,
    0x8C18, 0x8C1D, 0x8C1F, 0x8C20, 0x8C21, 0x8C25, 0x8C27, 0x8C2A, 0x8C2B,
    0x8C2E, 0x8C2F, 0x8C32, 0x8C33, 0x8C35, 0x8C36, 0x5369, 0x537A, 0x961D,
    0x9622, 0x9621, 0x9631, 0x962A, 0x963D, 0x963C, 0x9642, 0x9649, 0x9654,
    0x965F, 0x9667, 0x966C, 0x9672, 0x9674, 0x9688, 0x968D, 0x9697, 0x96B0,
    0x9097, 0x909B, 0x909D, 0x9099, 0x90AC, 0x90A1, 0x90B4, 0x90B3, 0x90B6,
    0x90BA, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x90B8, 0x90B0, 0x90CF, 0x90C5, 0x90BE, 0x90D0, 0x90C4, 0x90C7,
    0x90D3, 0x90E6, 0x90E2, 0x90DC, 0x90D7, 0x90DB, 0x90EB, 0x90EF, 0x90FE,
    0x9104, 0x9122, 0x911E, 0x9123, 0x9131, 0x912F, 0x9139, 0x9143, 0x9146,
    0x520D, 0x5942, 0x52A2, 0x52AC, 0x52AD, 0x52BE, 0x54FF, 0x52D0, 0x52D6,
    0x52F0, 0x53DF, 0x71EE, 0x77CD, 0x5EF4, 0x51F5, 0x51FC, 0x9B2F, 0x53B6,
    0x5F01, 0x755A, 0x5DEF, 0x574C, 0x57A9, 0x57A1, 0x587E, 0x58BC, 0x58C5,
    0x58D1, 0x5729, 0x572C, 0x572A, 0x5733, 0x5739, 0x572E, 0x572F, 0x575C,
    0x573B, 0x5742, 0x5769, 0x5785, 0x576B, 0x5786, 0x577C, 0x577B, 0x5768,
    0x576D, 0x5776, 0x5773, 0x57AD, 0x57A4, 0x578C, 0x57B2, 0x57CF, 0x57A7,
    0x57B4, 0x5793, 0x57A0, 0x57D5, 0x57D8, 0x57DA, 0x57D9, 0x57D2, 0x57B8,
    0x57F4, 0x57EF, 0x57F8, 0x57E4, 0x57DD, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x580B, 0x580D, 0x57FD, 0x57ED,
    0x5800, 0x581E, 0x5819, 0x5844, 0x5820, 0x5865, 0x586C, 0x5881, 0x5889,
    0x589A, 0x5880, 0x99A8, 0x9F19, 0x61FF, 0x8279, 0x827D, 0x827F, 0x828F,
    0x828A, 0x82A8, 0x8284, 0x828E, 0x8291, 0x8297, 0x8299, 0x82AB, 0x82B8,
    0x82BE, 0x82B0, 0x82C8, 0x82CA, 0x82E3, 0x8298, 0x82B7, 0x82AE, 0x82CB,
    0x82CC, 0x82C1, 0x82A9, 0x82B4, 0x82A1, 0x82AA, 0x829F, 0x82C4, 0x82CE,
    0x82A4, 0x82E1, 0x8309, 0x82F7, 0x82E4, 0x830F, 0x8307, 0x82DC, 0x82F4,
    0x82D2, 0x82D8, 0x830C, 0x82FB, 0x82D3, 0x8311, 0x831A, 0x8306, 0x8314,
    0x8315, 0x82E0, 0x82D5, 0x831C, 0x8351, 0x835B, 0x835C, 0x8308, 0x8392,
    0x833C, 0x8334, 0x8331, 0x839B, 0x835E, 0x832F, 0x834F, 0x8347, 0x8343,
    0x835F, 0x8340, 0x8317, 0x8360, 0x832D, 0x833A, 0x8333, 0x8366, 0x8365,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x8368, 0x831B, 0x8369, 0x836C, 0x836A, 0x836D, 0x836E, 0x83B0, 0x8378,
    0x83B3, 0x83B4, 0x83A0, 0x83AA, 0x8393, 0x839C, 0x8385, 0x837C, 0x83B6,
    0x83A9, 0x837D, 0x83B8, 0x837B, 0x8398, 0x839E, 0x83A8, 0x83BA, 0x83BC,
    0x83C1, 0x8401, 0x83E5, 0x83D8, 0x5807, 0x8418, 0x840B, 0x83DD, 0x83FD,
    0x83D6, 0x841C, 0x8438, 0x8411, 0x8406, 0x83D4, 0x83DF, 0x840F, 0x8403,
    0x83F8, 0x83F9, 0x83EA, 0x83C5, 0x83C0, 0x8426, 0x83F0, 0x83E1, 0x845C,
    0x8451, 0x845A, 0x8459, 0x8473, 0x8487, 0x8488, 0x847A, 0x8489, 0x8478,
    0x843C, 0x8446, 0x8469, 0x8476, 0x848C, 0x848E, 0x8431, 0x846D, 0x84C1,
    0x84CD, 0x84D0, 0x84E6, 0x84BD, 0x84D3, 0x84CA, 0x84BF, 0x84BA, 0x84E0,
    0x84A1, 0x84B9, 0x84B4, 0x8497, 0x84E5, 0x84E3, 0x850C, 0x750D, 0x8538,
    0x84F0, 0x8539, 0x851F, 0x853A, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x8556, 0x853B, 0x84FF, 0x84FC, 0x8559,
    0x8548, 0x8568, 0x8564, 0x855E, 0x857A, 0x77A2, 0x8543, 0x8572, 0x857B,
    0x85A4, 0x85A8, 0x8587, 0x858F, 0x8579, 0x85AE, 0x859C, 0x8585, 0x85B9,
    0x85B7, 0x85B0, 0x85D3, 0x85C1, 0x85DC, 0x85FF, 0x8627, 0x8605, 0x8629,
    0x8616, 0x863C, 0x5EFE, 0x5F08, 0x593C, 0x5941, 0x8037, 0x5955, 0x595A,
    0x5958, 0x530F, 0x5C22, 0x5C25, 0x5C2C, 0x5C34, 0x624C, 0x626A, 0x629F,
    0x62BB, 0x62CA, 0x62DA, 0x62D7, 0x62EE, 0x6322, 0x62F6, 0x6339, 0x634B,
    0x6343, 0x63AD, 0x63F6, 0x6371, 0x637A, 0x638E, 0x63B4, 0x636D, 0x63AC,
    0x638A, 0x6369, 0x63AE, 0x63BC, 0x63F2, 0x63F8, 0x63E0, 0x63FF, 0x63C4,
    0x63DE, 0x63CE, 0x6452, 0x63C6, 0x63BE, 0x6445, 0x6441, 0x640B, 0x641B,
    0x6420, 0x640C, 0x6426, 0x6421, 0x645E, 0x6484, 0x646D, 0x6496, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x647A,
    0x64B7, 0x64B8, 0x6499, 0x64BA, 0x64C0, 0x64D0, 0x64D7, 0x64E4, 0x64E2,
    0x6509, 0x6525, 0x652E, 0x5F0B, 0x5FD2, 0x7519, 0x5F11, 0x535F, 0x53F1,
    0x53FD, 0x53E9, 0x53E8, 0x53FB, 0x5412, 0x5416, 0x5406, 0x544B, 0x5452,
    0x5453, 0x5454, 0x5456, 0x5443, 0x5421, 0x5457, 0x5459, 0x5423, 0x5432,
    0x5482, 0x5494, 0x5477, 0x5471, 0x5464, 0x549A, 0x549B, 0x5484, 0x5476,
    0x5466, 0x549D, 0x54D0, 0x54AD, 0x54C2, 0x54B4, 0x54D2, 0x54A7, 0x54A6,
    0x54D3, 0x54D4, 0x5472, 0x54A3, 0x54D5, 0x54BB, 0x54BF, 0x54CC, 0x54D9,
    0x54DA, 0x54DC, 0x54A9, 0x54AA, 0x54A4, 0x54DD, 0x54CF, 0x54DE, 0x551B,
    0x54E7, 0x5520, 0x54FD, 0x5514, 0x54F3, 0x5522, 0x5523, 0x550F, 0x5511,
    0x5527, 0x552A, 0x5567, 0x558F, 0x55B5, 0x5549, 0x556D, 0x5541, 0x5555,
    0x553F, 0x5550, 0x553C, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x5537, 0x5556, 0x5575, 0x5576, 0x5577, 0x5533,
    0x5530, 0x555C, 0x558B, 0x55D2, 0x5583, 0x55B1, 0x55B9, 0x5588, 0x5581,
    0x559F, 0x557E, 0x55D6, 0x5591, 0x557B, 0x55DF, 0x55BD, 0x55BE, 0x5594,
    0x5599, 0x55EA, 0x55F7, 0x55C9, 0x561F, 0x55D1, 0x55EB, 0x55EC, 0x55D4,
    0x55E6, 0x55DD, 0x55C4, 0x55EF, 0x55E5, 0x55F2, 0x55F3, 0x55CC, 0x55CD,
    0x55E8, 0x55F5, 0x55E4, 0x8F94, 0x561E, 0x5608, 0x560C, 0x5601, 0x5624,
    0x5623, 0x55FE, 0x5600, 0x5627, 0x562D, 0x5658, 0x5639, 0x5657, 0x562C,
    0x564D, 0x5662, 0x5659, 0x565C, 0x564C, 0x5654, 0x5686, 0x5664, 0x5671,
    0x566B, 0x567B, 0x567C, 0x5685, 0x5693, 0x56AF, 0x56D4, 0x56D7, 0x56DD,
    0x56E1, 0x56F5, 0x56EB, 0x56F9, 0x56FF, 0x5704, 0x570A, 0x5709, 0x571C,
    0x5E0F, 0x5E19, 0x5E14, 0x5E11, 0x5E31, 0x5E3B, 0x5E3C, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x5E37, 0x5E44,
    0x5E54, 0x5E5B, 0x5E5E, 0x5E61, 0x5C8C, 0x5C7A, 0x5C8D, 0x5C90, 0x5C96,
    0x5C88, 0x5C98, 0x5C99, 0x5C91, 0x5C9A, 0x5C9C, 0x5CB5, 0x5CA2, 0x5CBD,
    0x5CAC, 0x5CAB, 0x5CB1, 0x5CA3, 0x5CC1, 0x5CB7, 0x5CC4, 0x5CD2, 0x5CE4,
    0x5CCB, 0x5CE5, 0x5D02, 0x5D03, 0x5D27, 0x5D26, 0x5D2E, 0x5D24, 0x5D1E,
    0x5D06, 0x5D1B, 0x5D58, 0x5D3E, 0x5D34, 0x5D3D, 0x5D6C, 0x5D5B, 0x5D6F,
    0x5D5D, 0x5D6B, 0x5D4B, 0x5D4A, 0x5D69, 0x5D74, 0x5D82, 0x5D99, 0x5D9D,
    0x8C73, 0x5DB7, 0x5DC5, 0x5F73, 0x5F77, 0x5F82, 0x5F87, 0x5F89, 0x5F8C,
    0x5F95, 0x5F99, 0x5F9C, 0x5FA8, 0x5FAD, 0x5FB5, 0x5FBC, 0x8862, 0x5F61,
    0x72AD, 0x72B0, 0x72B4, 0x72B7, 0x72B8, 0x72C3, 0x72C1, 0x72CE, 0x72CD,
    0x72D2, 0x72E8, 0x72EF, 0x72E9, 0x72F2, 0x72F4, 0x72F7, 0x7301, 0x72F3,
    0x7303, 0x72FA, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x72FB, 0x7317, 0x7313, 0x7321, 0x730A, 0x731E, 0x731D,
    0x7315, 0x7322, 0x7339, 0x7325, 0x732C, 0x7338, 0x7331, 0x7350, 0x734D,
    0x7357, 0x7360, 0x736C, 0x736F, 0x737E, 0x821B, 0x5925, 0x98E7, 0x5924,
    0x5902, 0x9963, 0x9967, 0x9968, 0x9969, 0x996A, 0x996B, 0x996C, 0x9974,
    0x9977, 0x997D, 0x9980, 0x9984, 0x9987, 0x998A, 0x998D, 0x9990, 0x9991,
    0x9993, 0x9994, 0x9995, 0x5E80, 0x5E91, 0x5E8B, 0x5E96, 0x5EA5, 0x5EA0,
    0x5EB9, 0x5EB5, 0x5EBE, 0x5EB3, 0x8D53, 0x5ED2, 0x5ED1, 0x5EDB, 0x5EE8,
    0x5EEA, 0x81BA, 0x5FC4, 0x5FC9, 0x5FD6, 0x5FCF, 0x6003, 0x5FEE, 0x6004,
    0x5FE1, 0x5FE4, 0x5FFE, 0x6005, 0x6006, 0x5FEA, 0x5FED, 0x5FF8, 0x6019,
    0x6035, 0x6026, 0x601B, 0x600F, 0x600D, 0x6029, 0x602B, 0x600A, 0x603F,
    0x6021, 0x6078, 0x6079, 0x607B, 0x607A, 0x6042, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x606A, 0x607D, 0x6096,
    0x609A, 0x60AD, 0x609D, 0x6083, 0x6092, 0x608C, 0x609B, 0x60EC, 0x60BB,
    0x60B1, 0x60DD, 0x60D8, 0x60C6, 0x60DA, 0x60B4, 0x6120, 0x6126, 0x6115,
    0x6123, 0x60F4, 0x6100, 0x610E, 0x612B, 0x614A, 0x6175, 0x61AC, 0x6194,
    0x61A7, 0x61B7, 0x61D4, 0x61F5, 0x5FDD, 0x96B3, 0x95E9, 0x95EB, 0x95F1,
    0x95F3, 0x95F5, 0x95F6, 0x95FC, 0x95FE, 0x9603, 0x9604, 0x9606, 0x9608,
    0x960A, 0x960B, 0x960C, 0x960D, 0x960F, 0x9612, 0x9615, 0x9616, 0x9617,
    0x9619, 0x961A, 0x4E2C, 0x723F, 0x6215, 0x6C35, 0x6C54, 0x6C5C, 0x6C4A,
    0x6CA3, 0x6C85, 0x6C90, 0x6C94, 0x6C8C, 0x6C68, 0x6C69, 0x6C74, 0x6C76,
    0x6C86, 0x6CA9, 0x6CD0, 0x6CD4, 0x6CAD, 0x6CF7, 0x6CF8, 0x6CF1, 0x6CD7,
    0x6CB2, 0x6CE0, 0x6CD6, 0x6CFA, 0x6CEB, 0x6CEE, 0x6CB1, 0x6CD3, 0x6CEF,
    0x6CFE, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x6D39, 0x6D27, 0x6D0C, 0x6D43, 0x6D48, 0x6D07, 0x6D04, 0x6D19,
    0x6D0E, 0x6D2B, 0x6D4D, 0x6D2E, 0x6D35, 0x6D1A, 0x6D4F, 0x6D52, 0x6D54,
    0x6D33, 0x6D91, 0x6D6F, 0x6D9E, 0x6DA0, 0x6D5E, 0x6D93, 0x6D94, 0x6D5C,
    0x6D60, 0x6D7C, 0x6D63, 0x6E1A, 0x6DC7, 0x6DC5, 0x6DDE, 0x6E0E, 0x6DBF,
    0x6DE0, 0x6E11, 0x6DE6, 0x6DDD, 0x6DD9, 0x6E16, 0x6DAB, 0x6E0C, 0x6DAE,
    0x6E2B, 0x6E6E, 0x6E4E, 0x6E6B, 0x6EB2, 0x6E5F, 0x6E86, 0x6E53, 0x6E54,
    0x6E32, 0x6E25, 0x6E44, 0x6EDF, 0x6EB1, 0x6E98, 0x6EE0, 0x6F2D, 0x6EE2,
    0x6EA5, 0x6EA7, 0x6EBD, 0x6EBB, 0x6EB7, 0x6ED7, 0x6EB4, 0x6ECF, 0x6E8F,
    0x6EC2, 0x6E9F, 0x6F62, 0x6F46, 0x6F47, 0x6F24, 0x6F15, 0x6EF9, 0x6F2F,
    0x6F36, 0x6F4B, 0x6F74, 0x6F2A, 0x6F09, 0x6F29, 0x6F89, 0x6F8D, 0x6F8C,
    0x6F78, 0x6F72, 0x6F7C, 0x6F7A, 0x6FD1, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x6FC9, 0x6FA7, 0x6FB9, 0x6FB6,
    0x6FC2, 0x6FE1, 0x6FEE, 0x6FDE, 0x6FE0, 0x6FEF, 0x701A, 0x7023, 0x701B,
    0x7039, 0x7035, 0x704F, 0x705E, 0x5B80, 0x5B84, 0x5B95, 0x5B93, 0x5BA5,
    0x5BB8, 0x752F, 0x9A9E, 0x6434, 0x5BE4, 0x5BEE, 0x8930, 0x5BF0, 0x8E47,
    0x8B07, 0x8FB6, 0x8FD3, 0x8FD5, 0x8FE5, 0x8FEE, 0x8FE4, 0x8FE9, 0x8FE6,
    0x8FF3, 0x8FE8, 0x9005, 0x9004, 0x900B, 0x9026, 0x9011, 0x900D, 0x9016,
    0x9021, 0x9035, 0x9036, 0x902D, 0x902F, 0x9044, 0x9051, 0x9052, 0x9050,
    0x9068, 0x9058, 0x9062, 0x905B, 0x66B9, 0x9074, 0x907D, 0x9082, 0x9088,
    0x9083, 0x908B, 0x5F50, 0x5F57, 0x5F56, 0x5F58, 0x5C3B, 0x54AB, 0x5C50,
    0x5C59, 0x5B71, 0x5C63, 0x5C66, 0x7FBC, 0x5F2A, 0x5F29, 0x5F2D, 0x8274,
    0x5F3C, 0x9B3B, 0x5C6E, 0x5981, 0x5983, 0x598D, 0x59A9, 0x59AA, 0x59A3,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x5997, 0x59CA, 0x59AB, 0x599E, 0x59A4, 0x59D2, 0x59B2, 0x59AF, 0x59D7,
    0x59BE, 0x5A05, 0x5A06, 0x59DD, 0x5A08, 0x59E3, 0x59D8, 0x59F9, 0x5A0C,
    0x5A09, 0x5A32, 0x5A34, 0x5A11, 0x5A23, 0x5A13, 0x5A40, 0x5A67, 0x5A4A,
    0x5A55, 0x5A3C, 0x5A62, 0x5A75, 0x80EC, 0x5AAA, 0x5A9B, 0x5A77, 0x5A7A,
    0x5ABE, 0x5AEB, 0x5AB2, 0x5AD2, 0x5AD4, 0x5AB8, 0x5AE0, 0x5AE3, 0x5AF1,
    0x5AD6, 0x5AE6, 0x5AD8, 0x5ADC, 0x5B09, 0x5B17, 0x5B16, 0x5B32, 0x5B37,
    0x5B40, 0x5C15, 0x5C1C, 0x5B5A, 0x5B65, 0x5B73, 0x5B51, 0x5B53, 0x5B62,
    0x9A75, 0x9A77, 0x9A78, 0x9A7A, 0x9A7F, 0x9A7D, 0x9A80, 0x9A81, 0x9A85,
    0x9A88, 0x9A8A, 0x9A90, 0x9A92, 0x9A93, 0x9A96, 0x9A98, 0x9A9B, 0x9A9C,
    0x9A9D, 0x9A9F, 0x9AA0, 0x9AA2, 0x9AA3, 0x9AA5, 0x9AA7, 0x7E9F, 0x7EA1,
    0x7EA3, 0x7EA5, 0x7EA8, 0x7EA9, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x7EAD, 0x7EB0, 0x7EBE, 0x7EC0, 0x7EC1,
    0x7EC2, 0x7EC9, 0x7ECB, 0x7ECC, 0x7ED0, 0x7ED4, 0x7ED7, 0x7EDB, 0x7EE0,
    0x7EE1, 0x7EE8, 0x7EEB, 0x7EEE, 0x7EEF, 0x7EF1, 0x7EF2, 0x7F0D, 0x7EF6,
    0x7EFA, 0x7EFB, 0x7EFE, 0x7F01, 0x7F02, 0x7F03, 0x7F07, 0x7F08, 0x7F0B,
    0x7F0C, 0x7F0F, 0x7F11, 0x7F12, 0x7F17, 0x7F19, 0x7F1C, 0x7F1B, 0x7F1F,
    0x7F21, 0x7F22, 0x7F23, 0x7F24, 0x7F25, 0x7F26, 0x7F27, 0x7F2A, 0x7F2B,
    0x7F2C, 0x7F2D, 0x7F2F, 0x7F30, 0x7F31, 0x7F32, 0x7F33, 0x7F35, 0x5E7A,
    0x757F, 0x5DDB, 0x753E, 0x9095, 0x738E, 0x7391, 0x73AE, 0x73A2, 0x739F,
    0x73CF, 0x73C2, 0x73D1, 0x73B7, 0x73B3, 0x73C0, 0x73C9, 0x73C8, 0x73E5,
    0x73D9, 0x987C, 0x740A, 0x73E9, 0x73E7, 0x73DE, 0x73BA, 0x73F2, 0x740F,
    0x742A, 0x745B, 0x7426, 0x7425, 0x7428, 0x7430, 0x742E, 0x742C, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x741B,
    0x741A, 0x7441, 0x745C, 0x7457, 0x7455, 0x7459, 0x7477, 0x746D, 0x747E,
    0x749C, 0x748E, 0x7480, 0x7481, 0x7487, 0x748B, 0x749E, 0x74A8, 0x74A9,
    0x7490, 0x74A7, 0x74D2, 0x74BA, 0x97EA, 0x97EB, 0x97EC, 0x674C, 0x6753,
    0x675E, 0x6748, 0x6769, 0x67A5, 0x6787, 0x676A, 0x6773, 0x6798, 0x67A7,
    0x6775, 0x67A8, 0x679E, 0x67AD, 0x678B, 0x6777, 0x677C, 0x67F0, 0x6809,
    0x67D8, 0x680A, 0x67E9, 0x67B0, 0x680C, 0x67D9, 0x67B5, 0x67DA, 0x67B3,
    0x67DD, 0x6800, 0x67C3, 0x67B8, 0x67E2, 0x680E, 0x67C1, 0x67FD, 0x6832,
    0x6833, 0x6860, 0x6861, 0x684E, 0x6862, 0x6844, 0x6864, 0x6883, 0x681D,
    0x6855, 0x6866, 0x6841, 0x6867, 0x6840, 0x683E, 0x684A, 0x6849, 0x6829,
    0x68B5, 0x688F, 0x6874, 0x6877, 0x6893, 0x686B, 0x68C2, 0x696E, 0x68FC,
    0x691F, 0x6920, 0x68F9, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x6924, 0x68F0, 0x690B, 0x6901, 0x6957, 0x68E3,
    0x6910, 0x6971, 0x6939, 0x6960, 0x6942, 0x695D, 0x6984, 0x696B, 0x6980,
    0x6998, 0x6978, 0x6934, 0x69CC, 0x6987, 0x6988, 0x69CE, 0x6989, 0x6966,
    0x6963, 0x6979, 0x699B, 0x69A7, 0x69BB, 0x69AB, 0x69AD, 0x69D4, 0x69B1,
    0x69C1, 0x69CA, 0x69DF, 0x6995, 0x69E0, 0x698D, 0x69FF, 0x6A2F, 0x69ED,
    0x6A17, 0x6A18, 0x6A65, 0x69F2, 0x6A44, 0x6A3E, 0x6AA0, 0x6A50, 0x6A5B,
    0x6A35, 0x6A8E, 0x6A79, 0x6A3D, 0x6A28, 0x6A58, 0x6A7C, 0x6A91, 0x6A90,
    0x6AA9, 0x6A97, 0x6AAB, 0x7337, 0x7352, 0x6B81, 0x6B82, 0x6B87, 0x6B84,
    0x6B92, 0x6B93, 0x6B8D, 0x6B9A, 0x6B9B, 0x6BA1, 0x6BAA, 0x8F6B, 0x8F6D,
    0x8F71, 0x8F72, 0x8F73, 0x8F75, 0x8F76, 0x8F78, 0x8F77, 0x8F79, 0x8F7A,
    0x8F7C, 0x8F7E, 0x8F81, 0x8F82, 0x8F84, 0x8F87, 0x8F8B, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x8F8D, 0x8F8E,
    0x8F8F, 0x8F98, 0x8F9A, 0x8ECE, 0x620B, 0x6217, 0x621B, 0x621F, 0x6222,
    0x6221, 0x6225, 0x6224, 0x622C, 0x81E7, 0x74EF, 0x74F4, 0x74FF, 0x750F,
    0x7511, 0x7513, 0x6534, 0x65EE, 0x65EF, 0x65F0, 0x660A, 0x6619, 0x6772,
    0x6603, 0x6615, 0x6600, 0x7085, 0x66F7, 0x661D, 0x6634, 0x6631, 0x6636,
    0x6635, 0x8006, 0x665F, 0x6654, 0x6641, 0x664F, 0x6656, 0x6661, 0x6657,
    0x6677, 0x6684, 0x668C, 0x66A7, 0x669D, 0x66BE, 0x66DB, 0x66DC, 0x66E6,
    0x66E9, 0x8D32, 0x8D33, 0x8D36, 0x8D3B, 0x8D3D, 0x8D40, 0x8D45, 0x8D46,
    0x8D48, 0x8D49, 0x8D47, 0x8D4D, 0x8D55, 0x8D59, 0x89C7, 0x89CA, 0x89CB,
    0x89CC, 0x89CE, 0x89CF, 0x89D0, 0x89D1, 0x726E, 0x729F, 0x725D, 0x7266,
    0x726F, 0x727E, 0x727F, 0x7284, 0x728B, 0x728D, 0x728F, 0x7292, 0x6308,
    0x6332, 0x63B0, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x643F, 0x64D8, 0x8004, 0x6BEA, 0x6BF3, 0x6BFD, 0x6BF5,
    0x6BF9, 0x6C05, 0x6C07, 0x6C06, 0x6C0D, 0x6C15, 0x6C18, 0x6C19, 0x6C1A,
    0x6C21, 0x6C29, 0x6C24, 0x6C2A, 0x6C32, 0x6535, 0x6555, 0x656B, 0x724D,
    0x7252, 0x7256, 0x7230, 0x8662, 0x5216, 0x809F, 0x809C, 0x8093, 0x80BC,
    0x670A, 0x80BD, 0x80B1, 0x80AB, 0x80AD, 0x80B4, 0x80B7, 0x80E7, 0x80E8,
    0x80E9, 0x80EA, 0x80DB, 0x80C2, 0x80C4, 0x80D9, 0x80CD, 0x80D7, 0x6710,
    0x80DD, 0x80EB, 0x80F1, 0x80F4, 0x80ED, 0x810D, 0x810E, 0x80F2, 0x80FC,
    0x6715, 0x8112, 0x8C5A, 0x8136, 0x811E, 0x812C, 0x8118, 0x8132, 0x8148,
    0x814C, 0x8153, 0x8174, 0x8159, 0x815A, 0x8171, 0x8160, 0x8169, 0x817C,
    0x817D, 0x816D, 0x8167, 0x584D, 0x5AB5, 0x8188, 0x8182, 0x8191, 0x6ED5,
    0x81A3, 0x81AA, 0x81CC, 0x6726, 0x81CA, 0x81BB, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x81C1, 0x81A6, 0x6B24,
    0x6B37, 0x6B39, 0x6B43, 0x6B46, 0x6B59, 0x98D1, 0x98D2, 0x98D3, 0x98D5,
    0x98D9, 0x98DA, 0x6BB3, 0x5F40, 0x6BC2, 0x89F3, 0x6590, 0x9F51, 0x6593,
    0x65BC, 0x65C6, 0x65C4, 0x65C3, 0x65CC, 0x65CE, 0x65D2, 0x65D6, 0x7080,
    0x709C, 0x7096, 0x709D, 0x70BB, 0x70C0, 0x70B7, 0x70AB, 0x70B1, 0x70E8,
    0x70CA, 0x7110, 0x7113, 0x7116, 0x712F, 0x7131, 0x7173, 0x715C, 0x7168,
    0x7145, 0x7172, 0x714A, 0x7178, 0x717A, 0x7198, 0x71B3, 0x71B5, 0x71A8,
    0x71A0, 0x71E0, 0x71D4, 0x71E7, 0x71F9, 0x721D, 0x7228, 0x706C, 0x7118,
    0x7166, 0x71B9, 0x623E, 0x623D, 0x6243, 0x6248, 0x6249, 0x793B, 0x7940,
    0x7946, 0x7949, 0x795B, 0x795C, 0x7953, 0x795A, 0x7962, 0x7957, 0x7960,
    0x796F, 0x7967, 0x797A, 0x7985, 0x798A, 0x799A, 0x79A7, 0x79B3, 0x5FD1,
    0x5FD0, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x603C, 0x605D, 0x605A, 0x6067, 0x6041, 0x6059, 0x6063, 0x60AB,
    0x6106, 0x610D, 0x615D, 0x61A9, 0x619D, 0x61CB, 0x61D1, 0x6206, 0x8080,
    0x807F, 0x6C93, 0x6CF6, 0x6DFC, 0x77F6, 0x77F8, 0x7800, 0x7809, 0x7817,
    0x7818, 0x7811, 0x65AB, 0x782D, 0x781C, 0x781D, 0x7839, 0x783A, 0x783B,
    0x781F, 0x783C, 0x7825, 0x782C, 0x7823, 0x7829, 0x784E, 0x786D, 0x7856,
    0x7857, 0x7826, 0x7850, 0x7847, 0x784C, 0x786A, 0x789B, 0x7893, 0x789A,
    0x7887, 0x789C, 0x78A1, 0x78A3, 0x78B2, 0x78B9, 0x78A5, 0x78D4, 0x78D9,
    0x78C9, 0x78EC, 0x78F2, 0x7905, 0x78F4, 0x7913, 0x7924, 0x791E, 0x7934,
    0x9F9B, 0x9EF9, 0x9EFB, 0x9EFC, 0x76F1, 0x7704, 0x770D, 0x76F9, 0x7707,
    0x7708, 0x771A, 0x7722, 0x7719, 0x772D, 0x7726, 0x7735, 0x7738, 0x7750,
    0x7751, 0x7747, 0x7743, 0x775A, 0x7768, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x7762, 0x7765, 0x777F, 0x778D,
    0x777D, 0x7780, 0x778C, 0x7791, 0x779F, 0x77A0, 0x77B0, 0x77B5, 0x77BD,
    0x753A, 0x7540, 0x754E, 0x754B, 0x7548, 0x755B, 0x7572, 0x7579, 0x7583,
    0x7F58, 0x7F61, 0x7F5F, 0x8A48, 0x7F68, 0x7F74, 0x7F71, 0x7F79, 0x7F81,
    0x7F7E, 0x76CD, 0x76E5, 0x8832, 0x9485, 0x9486, 0x9487, 0x948B, 0x948A,
    0x948C, 0x948D, 0x948F, 0x9490, 0x9494, 0x9497, 0x9495, 0x949A, 0x949B,
    0x949C, 0x94A3, 0x94A4, 0x94AB, 0x94AA, 0x94AD, 0x94AC, 0x94AF, 0x94B0,
    0x94B2, 0x94B4, 0x94B6, 0x94B7, 0x94B8, 0x94B9, 0x94BA, 0x94BC, 0x94BD,
    0x94BF, 0x94C4, 0x94C8, 0x94C9, 0x94CA, 0x94CB, 0x94CC, 0x94CD, 0x94CE,
    0x94D0, 0x94D1, 0x94D2, 0x94D5, 0x94D6, 0x94D7, 0x94D9, 0x94D8, 0x94DB,
    0x94DE, 0x94DF, 0x94E0, 0x94E2, 0x94E4, 0x94E5, 0x94E7, 0x94E8, 0x94EA,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x94E9, 0x94EB, 0x94EE, 0x94EF, 0x94F3, 0x94F4, 0x94F5, 0x94F7, 0x94F9,
    0x94FC, 0x94FD, 0x94FF, 0x9503, 0x9502, 0x9506, 0x9507, 0x9509, 0x950A,
    0x950D, 0x950E, 0x950F, 0x9512, 0x9513, 0x9514, 0x9515, 0x9516, 0x9518,
    0x951B, 0x951D, 0x951E, 0x951F, 0x9522, 0x952A, 0x952B, 0x9529, 0x952C,
    0x9531, 0x9532, 0x9534, 0x9536, 0x9537, 0x9538, 0x953C, 0x953E, 0x953F,
    0x9542, 0x9535, 0x9544, 0x9545, 0x9546, 0x9549, 0x954C, 0x954E, 0x954F,
    0x9552, 0x9553, 0x9554, 0x9556, 0x9557, 0x9558, 0x9559, 0x955B, 0x955E,
    0x955F, 0x955D, 0x9561, 0x9562, 0x9564, 0x9565, 0x9566, 0x9567, 0x9568,
    0x9569, 0x956A, 0x956B, 0x956C, 0x956F, 0x9571, 0x9572, 0x9573, 0x953A,
    0x77E7, 0x77EC, 0x96C9, 0x79D5, 0x79ED, 0x79E3, 0x79EB, 0x7A06, 0x5D47,
    0x7A03, 0x7A02, 0x7A1E, 0x7A14, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x7A39, 0x7A37, 0x7A51, 0x9ECF, 0x99A5,
    0x7A70, 0x7688, 0x768E, 0x7693, 0x7699, 0x76A4, 0x74DE, 0x74E0, 0x752C,
    0x9E20, 0x9E22, 0x9E28, 0x9E29, 0x9E2A, 0x9E2B, 0x9E2C, 0x9E32, 0x9E31,
    0x9E36, 0x9E38, 0x9E37, 0x9E39, 0x9E3A, 0x9E3E, 0x9E41, 0x9E42, 0x9E44,
    0x9E46, 0x9E47, 0x9E48, 0x9E49, 0x9E4B, 0x9E4C, 0x9E4E, 0x9E51, 0x9E55,
    0x9E57, 0x9E5A, 0x9E5B, 0x9E5C, 0x9E5E, 0x9E63, 0x9E66, 0x9E67, 0x9E68,
    0x9E69, 0x9E6A, 0x9E6B, 0x9E6C, 0x9E71, 0x9E6D, 0x9E73, 0x7592, 0x7594,
    0x7596, 0x75A0, 0x759D, 0x75AC, 0x75A3, 0x75B3, 0x75B4, 0x75B8, 0x75C4,
    0x75B1, 0x75B0, 0x75C3, 0x75C2, 0x75D6, 0x75CD, 0x75E3, 0x75E8, 0x75E6,
    0x75E4, 0x75EB, 0x75E7, 0x7603, 0x75F1, 0x75FC, 0x75FF, 0x7610, 0x7600,
    0x7605, 0x760C, 0x7617, 0x760A, 0x7625, 0x7618, 0x7615, 0x7619, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x761B,
    0x763C, 0x7622, 0x7620, 0x7640, 0x762D, 0x7630, 0x763F, 0x7635, 0x7643,
    0x763E, 0x7633, 0x764D, 0x765E, 0x7654, 0x765C, 0x7656, 0x766B, 0x766F,
    0x7FCA, 0x7AE6, 0x7A78, 0x7A79, 0x7A80, 0x7A86, 0x7A88, 0x7A95, 0x7AA6,
    0x7AA0, 0x7AAC, 0x7AA8, 0x7AAD, 0x7AB3, 0x8864, 0x8869, 0x8872, 0x887D,
    0x887F, 0x8882, 0x88A2, 0x88C6, 0x88B7, 0x88BC, 0x88C9, 0x88E2, 0x88CE,
    0x88E3, 0x88E5, 0x88F1, 0x891A, 0x88FC, 0x88E8, 0x88FE, 0x88F0, 0x8921,
    0x8919, 0x8913, 0x891B, 0x890A, 0x8934, 0x892B, 0x8936, 0x8941, 0x8966,
    0x897B, 0x758B, 0x80E5, 0x76B2, 0x76B4, 0x77DC, 0x8012, 0x8014, 0x8016,
    0x801C, 0x8020, 0x8022, 0x8025, 0x8026, 0x8027, 0x8029, 0x8028, 0x8031,
    0x800B, 0x8035, 0x8043, 0x8046, 0x804D, 0x8052, 0x8069, 0x8071, 0x8983,
    0x9878, 0x9880, 0x9883, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x9889, 0x988C, 0x988D, 0x988F, 0x9894, 0x989A,
    0x989B, 0x989E, 0x989F, 0x98A1, 0x98A2, 0x98A5, 0x98A6, 0x864D, 0x8654,
    0x866C, 0x866E, 0x867F, 0x867A, 0x867C, 0x867B, 0x86A8, 0x868D, 0x868B,
    0x86AC, 0x869D, 0x86A7, 0x86A3, 0x86AA, 0x8693, 0x86A9, 0x86B6, 0x86C4,
    0x86B5, 0x86CE, 0x86B0, 0x86BA, 0x86B1, 0x86AF, 0x86C9, 0x86CF, 0x86B4,
    0x86E9, 0x86F1, 0x86F2, 0x86ED, 0x86F3, 0x86D0, 0x8713, 0x86DE, 0x86F4,
    0x86DF, 0x86D8, 0x86D1, 0x8703, 0x8707, 0x86F8, 0x8708, 0x870A, 0x870D,
    0x8709, 0x8723, 0x873B, 0x871E, 0x8725, 0x872E, 0x871A, 0x873E, 0x8748,
    0x8734, 0x8731, 0x8729, 0x8737, 0x873F, 0x8782, 0x8722, 0x877D, 0x877E,
    0x877B, 0x8760, 0x8770, 0x874C, 0x876E, 0x878B, 0x8753, 0x8763, 0x877C,
    0x8764, 0x8759, 0x8765, 0x8793, 0x87AF, 0x87A8, 0x87D2, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x87C6, 0x8788,
    0x8785, 0x87AD, 0x8797, 0x8783, 0x87AB, 0x87E5, 0x87AC, 0x87B5, 0x87B3,
    0x87CB, 0x87D3, 0x87BD, 0x87D1, 0x87C0, 0x87CA, 0x87DB, 0x87EA, 0x87E0,
    0x87EE, 0x8816, 0x8813, 0x87FE, 0x880A, 0x881B, 0x8821, 0x8839, 0x883C,
    0x7F36, 0x7F42, 0x7F44, 0x7F45, 0x8210, 0x7AFA, 0x7AFD, 0x7B08, 0x7B03,
    0x7B04, 0x7B15, 0x7B0A, 0x7B2B, 0x7B0F, 0x7B47, 0x7B38, 0x7B2A, 0x7B19,
    0x7B2E, 0x7B31, 0x7B20, 0x7B25, 0x7B24, 0x7B33, 0x7B3E, 0x7B1E, 0x7B58,
    0x7B5A, 0x7B45, 0x7B75, 0x7B4C, 0x7B5D, 0x7B60, 0x7B6E, 0x7B7B, 0x7B62,
    0x7B72, 0x7B71, 0x7B90, 0x7BA6, 0x7BA7, 0x7BB8, 0x7BAC, 0x7B9D, 0x7BA8,
    0x7B85, 0x7BAA, 0x7B9C, 0x7BA2, 0x7BAB, 0x7BB4, 0x7BD1, 0x7BC1, 0x7BCC,
    0x7BDD, 0x7BDA, 0x7BE5, 0x7BE6, 0x7BEA, 0x7C0C, 0x7BFE, 0x7BFC, 0x7C0F,
    0x7C16, 0x7C0B, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x7C1F, 0x7C2A, 0x7C26, 0x7C38, 0x7C41, 0x7C40, 0x81FE,
    0x8201, 0x8202, 0x8204, 0x81EC, 0x8844, 0x8221, 0x8222, 0x8223, 0x822D,
    0x822F, 0x8228, 0x822B, 0x8238, 0x823B, 0x8233, 0x8234, 0x823E, 0x8244,
    0x8249, 0x824B, 0x824F, 0x825A, 0x825F, 0x8268, 0x887E, 0x8885, 0x8888,
    0x88D8, 0x88DF, 0x895E, 0x7F9D, 0x7F9F, 0x7FA7, 0x7FAF, 0x7FB0, 0x7FB2,
    0x7C7C, 0x6549, 0x7C91, 0x7C9D, 0x7C9C, 0x7C9E, 0x7CA2, 0x7CB2, 0x7CBC,
    0x7CBD, 0x7CC1, 0x7CC7, 0x7CCC, 0x7CCD, 0x7CC8, 0x7CC5, 0x7CD7, 0x7CE8,
    0x826E, 0x66A8, 0x7FBF, 0x7FCE, 0x7FD5, 0x7FE5, 0x7FE1, 0x7FE6, 0x7FE9,
    0x7FEE, 0x7FF3, 0x7CF8, 0x7D77, 0x7DA6, 0x7DAE, 0x7E47, 0x7E9B, 0x9EB8,
    0x9EB4, 0x8D73, 0x8D84, 0x8D94, 0x8D91, 0x8DB1, 0x8D67, 0x8D6D, 0x8C47,
    0x8C49, 0x914A, 0x9150, 0x914E, 0x914F, 0x9164, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x9162, 0x9161, 0x9170,
    0x9169, 0x916F, 0x917D, 0x917E, 0x9172, 0x9174, 0x9179, 0x918C, 0x9185,
    0x9190, 0x918D, 0x9191, 0x91A2, 0x91A3, 0x91AA, 0x91AD, 0x91AE, 0x91AF,
    0x91B5, 0x91B4, 0x91BA, 0x8C55, 0x9E7E, 0x8DB8, 0x8DEB, 0x8E05, 0x8E59,
    0x8E69, 0x8DB5, 0x8DBF, 0x8DBC, 0x8DBA, 0x8DC4, 0x8DD6, 0x8DD7, 0x8DDA,
    0x8DDE, 0x8DCE, 0x8DCF, 0x8DDB, 0x8DC6, 0x8DEC, 0x8DF7, 0x8DF8, 0x8DE3,
    0x8DF9, 0x8DFB, 0x8DE4, 0x8E09, 0x8DFD, 0x8E14, 0x8E1D, 0x8E1F, 0x8E2C,
    0x8E2E, 0x8E23, 0x8E2F, 0x8E3A, 0x8E40, 0x8E39, 0x8E35, 0x8E3D, 0x8E31,
    0x8E49, 0x8E41, 0x8E42, 0x8E51, 0x8E52, 0x8E4A, 0x8E70, 0x8E76, 0x8E7C,
    0x8E6F, 0x8E74, 0x8E85, 0x8E8F, 0x8E94, 0x8E90, 0x8E9C, 0x8E9E, 0x8C78,
    0x8C82, 0x8C8A, 0x8C85, 0x8C98, 0x8C94, 0x659B, 0x89D6, 0x89DE, 0x89DA,
    0x89DC, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x89E5, 0x89EB, 0x89EF, 0x8A3E, 0x8B26, 0x9753, 0x96E9, 0x96F3,
    0x96EF, 0x9706, 0x9701, 0x9708, 0x970F, 0x970E, 0x972A, 0x972D, 0x9730,
    0x973E, 0x9F80, 0x9F83, 0x9F85, 0x9F86, 0x9F87, 0x9F88, 0x9F89, 0x9F8A,
    0x9F8C, 0x9EFE, 0x9F0B, 0x9F0D, 0x96B9, 0x96BC, 0x96BD, 0x96CE, 0x96D2,
    0x77BF, 0x96E0, 0x928E, 0x92AE, 0x92C8, 0x933E, 0x936A, 0x93CA, 0x938F,
    0x943E, 0x946B, 0x9C7F, 0x9C82, 0x9C85, 0x9C86, 0x9C87, 0x9C88, 0x7A23,
    0x9C8B, 0x9C8E, 0x9C90, 0x9C91, 0x9C92, 0x9C94, 0x9C95, 0x9C9A, 0x9C9B,
    0x9C9E, 0x9C9F, 0x9CA0, 0x9CA1, 0x9CA2, 0x9CA3, 0x9CA5, 0x9CA6, 0x9CA7,
    0x9CA8, 0x9CA9, 0x9CAB, 0x9CAD, 0x9CAE, 0x9CB0, 0x9CB1, 0x9CB2, 0x9CB3,
    0x9CB4, 0x9CB5, 0x9CB6, 0x9CB7, 0x9CBA, 0x9CBB, 0x9CBC, 0x9CBD, 0x9CC4,
    0x9CC5, 0x9CC6, 0x9CC7, 0x9CCA, 0x9CCB, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x9CCC, 0x9CCD, 0x9CCE, 0x9CCF,
    0x9CD0, 0x9CD3, 0x9CD4, 0x9CD5, 0x9CD7, 0x9CD8, 0x9CD9, 0x9CDC, 0x9CDD,
    0x9CDF, 0x9CE2, 0x977C, 0x9785, 0x9791, 0x9792, 0x9794, 0x97AF, 0x97AB,
    0x97A3, 0x97B2, 0x97B4, 0x9AB1, 0x9AB0, 0x9AB7, 0x9E58, 0x9AB6, 0x9ABA,
    0x9ABC, 0x9AC1, 0x9AC0, 0x9AC5, 0x9AC2, 0x9ACB, 0x9ACC, 0x9AD1, 0x9B45,
    0x9B43, 0x9B47, 0x9B49, 0x9B48, 0x9B4D, 0x9B51, 0x98E8, 0x990D, 0x992E,
    0x9955, 0x9954, 0x9ADF, 0x9AE1, 0x9AE6, 0x9AEF, 0x9AEB, 0x9AFB, 0x9AED,
    0x9AF9, 0x9B08, 0x9B0F, 0x9B13, 0x9B1F, 0x9B23, 0x9EBD, 0x9EBE, 0x7E3B,
    0x9E82, 0x9E87, 0x9E88, 0x9E8B, 0x9E92, 0x93D6, 0x9E9D, 0x9E9F, 0x9EDB,
    0x9EDC, 0x9EDD, 0x9EE0, 0x9EDF, 0x9EE2, 0x9EE9, 0x9EE7, 0x9EE5, 0x9EEA,
    0x9EEF, 0x9F22, 0x9F2C, 0x9F2F, 0x9F39, 0x9F37, 0x9F3D, 0x9F3E, 0x9F44};

static int func_gb2312_uni_onechar(int code) {
  if ((code >= 0x2121) && (code <= 0x2658))
    return (tab_gb2312_uni0[code - 0x2121]);
  if ((code >= 0x2721) && (code <= 0x296F))
    return (tab_gb2312_uni1[code - 0x2721]);
  if ((code >= 0x3021) && (code <= 0x777E))
    return (tab_gb2312_uni2[code - 0x3021]);
  return (0);
}

/* page 0 0x00A4-0x01DC */
static const uint16_t tab_uni_gb23120[] = {
    0x2168, 0,      0,      0x216C, 0x2127, 0,      0,      0, 0,      0,
    0,      0,      0x2163, 0x2140, 0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0x2141, 0,      0,      0,      0,      0,      0, 0,      0,
    0x2824, 0x2822, 0,      0,      0,      0,      0,      0, 0x2828, 0x2826,
    0x283A, 0,      0x282C, 0x282A, 0,      0,      0,      0, 0x2830, 0x282E,
    0,      0,      0,      0x2142, 0,      0x2834, 0x2832, 0, 0x2839, 0,
    0,      0,      0,      0x2821, 0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0x2825, 0,      0,      0,      0,      0,      0, 0,      0x2827,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0x2829, 0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0x282D,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0x2831,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0, 0x2823, 0,
    0x282B, 0,      0x282F, 0,      0x2833, 0,      0x2835, 0, 0x2836, 0,
    0x2837, 0,      0x2838};

/* page 1 0x02C7-0x0451 */
static const uint16_t tab_uni_gb23121[] = {
    0x2126, 0,      0x2125, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x2621, 0x2622, 0x2623, 0x2624, 0x2625,
    0x2626, 0x2627, 0x2628, 0x2629, 0x262A, 0x262B, 0x262C, 0x262D, 0x262E,
    0x262F, 0x2630, 0x2631, 0,      0x2632, 0x2633, 0x2634, 0x2635, 0x2636,
    0x2637, 0x2638, 0,      0,      0,      0,      0,      0,      0,
    0x2641, 0x2642, 0x2643, 0x2644, 0x2645, 0x2646, 0x2647, 0x2648, 0x2649,
    0x264A, 0x264B, 0x264C, 0x264D, 0x264E, 0x264F, 0x2650, 0x2651, 0,
    0x2652, 0x2653, 0x2654, 0x2655, 0x2656, 0x2657, 0x2658, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x2727,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x2721, 0x2722, 0x2723, 0x2724,
    0x2725, 0x2726, 0x2728, 0x2729, 0x272A, 0x272B, 0x272C, 0x272D, 0x272E,
    0x272F, 0x2730, 0x2731, 0x2732, 0x2733, 0x2734, 0x2735, 0x2736, 0x2737,
    0x2738, 0x2739, 0x273A, 0x273B, 0x273C, 0x273D, 0x273E, 0x273F, 0x2740,
    0x2741, 0x2751, 0x2752, 0x2753, 0x2754, 0x2755, 0x2756, 0x2758, 0x2759,
    0x275A, 0x275B, 0x275C, 0x275D, 0x275E, 0x275F, 0x2760, 0x2761, 0x2762,
    0x2763, 0x2764, 0x2765, 0x2766, 0x2767, 0x2768, 0x2769, 0x276A, 0x276B,
    0x276C, 0x276D, 0x276E, 0x276F, 0x2770, 0x2771, 0,      0x2757};

/* page 2 0x2015-0x2312 */
static const uint16_t tab_uni_gb23122[] = {
    0x212A, 0x212C, 0,      0x212E, 0x212F, 0,      0,      0x2130, 0x2131,
    0,      0,      0,      0,      0,      0,      0,      0,      0x212D,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x216B, 0,      0x2164, 0x2165, 0,      0,      0,      0,      0,
    0,      0,      0x2179, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x2166, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x216D, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x2271, 0x2272,
    0x2273, 0x2274, 0x2275, 0x2276, 0x2277, 0x2278, 0x2279, 0x227A, 0x227B,
    0x227C, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x217B, 0x217C, 0x217A, 0x217D, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x214A, 0,      0,      0,      0,
    0,      0,      0x2147, 0,      0x2146, 0,      0,      0,      0,
    0,      0,      0,      0,      0x214C, 0,      0,      0x2158, 0x215E,
    0,      0x214F, 0,      0,      0,      0,      0x214E, 0,      0x2144,
    0x2145, 0x2149, 0x2148, 0x2152, 0,      0,      0x2153, 0,      0,
    0,      0,      0,      0x2160, 0x215F, 0x2143, 0x214B, 0,      0,
    0,      0,      0,      0x2157, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x2156, 0,      0,      0,
    0x2155, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x2159, 0x2154, 0,      0,      0x215C, 0x215D, 0,
    0,      0,      0,      0,      0,      0,      0,      0x215A, 0x215B,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x2151, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x214D,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x2150};

/* page 3 0x2460-0x2642 */
static const uint16_t tab_uni_gb23123[] = {
    0x2259, 0x225A, 0x225B, 0x225C, 0x225D, 0x225E, 0x225F, 0x2260, 0x2261,
    0x2262, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x2245, 0x2246, 0x2247, 0x2248, 0x2249, 0x224A, 0x224B,
    0x224C, 0x224D, 0x224E, 0x224F, 0x2250, 0x2251, 0x2252, 0x2253, 0x2254,
    0x2255, 0x2256, 0x2257, 0x2258, 0x2231, 0x2232, 0x2233, 0x2234, 0x2235,
    0x2236, 0x2237, 0x2238, 0x2239, 0x223A, 0x223B, 0x223C, 0x223D, 0x223E,
    0x223F, 0x2240, 0x2241, 0x2242, 0x2243, 0x2244, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x2924, 0x2925,
    0x2926, 0x2927, 0x2928, 0x2929, 0x292A, 0x292B, 0x292C, 0x292D, 0x292E,
    0x292F, 0x2930, 0x2931, 0x2932, 0x2933, 0x2934, 0x2935, 0x2936, 0x2937,
    0x2938, 0x2939, 0x293A, 0x293B, 0x293C, 0x293D, 0x293E, 0x293F, 0x2940,
    0x2941, 0x2942, 0x2943, 0x2944, 0x2945, 0x2946, 0x2947, 0x2948, 0x2949,
    0x294A, 0x294B, 0x294C, 0x294D, 0x294E, 0x294F, 0x2950, 0x2951, 0x2952,
    0x2953, 0x2954, 0x2955, 0x2956, 0x2957, 0x2958, 0x2959, 0x295A, 0x295B,
    0x295C, 0x295D, 0x295E, 0x295F, 0x2960, 0x2961, 0x2962, 0x2963, 0x2964,
    0x2965, 0x2966, 0x2967, 0x2968, 0x2969, 0x296A, 0x296B, 0x296C, 0x296D,
    0x296E, 0x296F, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x2176, 0x2175, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x2178, 0x2177, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x2174, 0x2173,
    0,      0,      0,      0x2170, 0,      0,      0x2172, 0x2171, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x216F, 0x216E,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x2162, 0,      0x2161};

/* page 4 0x3000-0x3129 */
static const uint16_t tab_uni_gb23124[] = {
    0x2121, 0x2122, 0x2123, 0x2128, 0,      0x2129, 0,      0,      0x2134,
    0x2135, 0x2136, 0x2137, 0x2138, 0x2139, 0x213A, 0x213B, 0x213E, 0x213F,
    0,      0x217E, 0x2132, 0x2133, 0x213C, 0x213D, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x2421, 0x2422, 0x2423, 0x2424, 0x2425, 0x2426, 0x2427,
    0x2428, 0x2429, 0x242A, 0x242B, 0x242C, 0x242D, 0x242E, 0x242F, 0x2430,
    0x2431, 0x2432, 0x2433, 0x2434, 0x2435, 0x2436, 0x2437, 0x2438, 0x2439,
    0x243A, 0x243B, 0x243C, 0x243D, 0x243E, 0x243F, 0x2440, 0x2441, 0x2442,
    0x2443, 0x2444, 0x2445, 0x2446, 0x2447, 0x2448, 0x2449, 0x244A, 0x244B,
    0x244C, 0x244D, 0x244E, 0x244F, 0x2450, 0x2451, 0x2452, 0x2453, 0x2454,
    0x2455, 0x2456, 0x2457, 0x2458, 0x2459, 0x245A, 0x245B, 0x245C, 0x245D,
    0x245E, 0x245F, 0x2460, 0x2461, 0x2462, 0x2463, 0x2464, 0x2465, 0x2466,
    0x2467, 0x2468, 0x2469, 0x246A, 0x246B, 0x246C, 0x246D, 0x246E, 0x246F,
    0x2470, 0x2471, 0x2472, 0x2473, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x2521,
    0x2522, 0x2523, 0x2524, 0x2525, 0x2526, 0x2527, 0x2528, 0x2529, 0x252A,
    0x252B, 0x252C, 0x252D, 0x252E, 0x252F, 0x2530, 0x2531, 0x2532, 0x2533,
    0x2534, 0x2535, 0x2536, 0x2537, 0x2538, 0x2539, 0x253A, 0x253B, 0x253C,
    0x253D, 0x253E, 0x253F, 0x2540, 0x2541, 0x2542, 0x2543, 0x2544, 0x2545,
    0x2546, 0x2547, 0x2548, 0x2549, 0x254A, 0x254B, 0x254C, 0x254D, 0x254E,
    0x254F, 0x2550, 0x2551, 0x2552, 0x2553, 0x2554, 0x2555, 0x2556, 0x2557,
    0x2558, 0x2559, 0x255A, 0x255B, 0x255C, 0x255D, 0x255E, 0x255F, 0x2560,
    0x2561, 0x2562, 0x2563, 0x2564, 0x2565, 0x2566, 0x2567, 0x2568, 0x2569,
    0x256A, 0x256B, 0x256C, 0x256D, 0x256E, 0x256F, 0x2570, 0x2571, 0x2572,
    0x2573, 0x2574, 0x2575, 0x2576, 0,      0,      0,      0,      0x2124,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x2845, 0x2846, 0x2847, 0x2848, 0x2849, 0x284A, 0x284B, 0x284C, 0x284D,
    0x284E, 0x284F, 0x2850, 0x2851, 0x2852, 0x2853, 0x2854, 0x2855, 0x2856,
    0x2857, 0x2858, 0x2859, 0x285A, 0x285B, 0x285C, 0x285D, 0x285E, 0x285F,
    0x2860, 0x2861, 0x2862, 0x2863, 0x2864, 0x2865, 0x2866, 0x2867, 0x2868,
    0x2869};

/* page 5 0x3220-0x3229 */
static const uint16_t tab_uni_gb23125[] = {0x2265, 0x2266, 0x2267, 0x2268,
                                           0x2269, 0x226A, 0x226B, 0x226C,
                                           0x226D, 0x226E};

/* page 6 0x4E00-0x9B54 */
static const uint16_t tab_uni_gb23126[] = {
    0x523B, 0x3621, 0,      0x465F, 0,      0,      0,      0x4D72, 0x5549,
    0x487D, 0x494F, 0x4F42, 0x5822, 0x323B, 0x536B, 0,      0x5824, 0x3373,
    0,      0x5728, 0x4752, 0x5827, 0x4A40, 0,      0x4770, 0x317B, 0x5235,
    0x3454, 0x362B, 0x4B3F, 0x5829, 0,      0,      0,      0x362A, 0,
    0x413D, 0x514F, 0,      0x4925, 0x582D, 0,      0x3876, 0x513E, 0x635C,
    0x5650, 0,      0,      0x3761, 0,      0x342E, 0,      0x4159, 0,
    0x583C, 0,      0x4D68, 0x3524, 0x4E2A, 0x5677, 0,      0x4076, 0x3E59,
    0x582F, 0,      0,      0,      0x444B, 0,      0x3E43, 0,      0x5831,
    0x4334, 0x5265, 0,      0x562E, 0x4E5A, 0x5527, 0x3A75, 0x3726, 0x4056,
    0,      0x4639, 0x4552, 0x4747, 0,      0x3954, 0,      0x334B, 0x5252,
    0,      0,      0x583F, 0x3E45, 0x4672, 0x5232, 0x4F30, 0x4F67, 0,
    0,      0,      0,      0x4A69, 0,      0,      0x5840, 0,      0,
    0,      0,      0,      0,      0x4272, 0x4252, 0,      0x4869, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x472C, 0,      0,      0,      0,      0,      0,      0,      0x414B,
    0,      0x5368, 0x5579, 0,      0x4A42, 0x367E, 0x5821, 0x535A, 0x3F77,
    0,      0x5446, 0x3B25, 0x5841, 0x4E65, 0x3E2E, 0,      0,      0x5828,
    0,      0x5147, 0x5029, 0,      0,      0,      0x583D, 0x596F, 0x4D76,
    0x3F3A, 0,      0x3D3B, 0x3A25, 0x5260, 0x327A, 0x3A60, 0x4436, 0,
    0x4F6D, 0x3E29, 0x4D24, 0x4141, 0,      0,      0,      0x4757, 0x5971,
    0,      0x5974, 0,      0,      0,      0,      0x484B, 0x5869, 0,
    0,      0,      0x525A, 0x4A32, 0x484A, 0x586C, 0x586A, 0x5846, 0x3D76,
    0x464D, 0x3370, 0,      0x586B, 0x3D71, 0x3D69, 0,      0x4854, 0x3453,
    0,      0,      0x4258, 0,      0x3256, 0x5750, 0x4A4B, 0x4B7B, 0x554C,
    0x3836, 0x4F49, 0,      0,      0,      0x595A, 0x5870, 0x472A, 0,
    0x586E, 0,      0x347A, 0x416E, 0x5254, 0,      0,      0x586D, 0,
    0x5247, 0x586F, 0x4347, 0,      0,      0,      0x5176, 0,      0x5659,
    0x5872, 0,      0x5875, 0x3C7E, 0x3C5B, 0,      0,      0,      0x484E,
    0,      0x375D, 0,      0x3742, 0,      0x4673, 0,      0,      0,
    0,      0,      0,      0,      0x5878, 0x5241, 0,      0,      0x4E69,
    0x3C3F, 0x377C, 0x3725, 0x505D, 0,      0,      0,      0,      0,
    0x565A, 0x5345, 0x3B6F, 0x3B61, 0x5871, 0,      0,      0x4921, 0x4E30,
    0x342B, 0,      0x5873, 0,      0x494B, 0x5876, 0x4257, 0x5877, 0,
    0,      0x4E31, 0x5879, 0,      0,      0,      0x322E, 0x3940, 0,
    0x5923, 0,      0x3069, 0,      0x4166, 0,      0x496C, 0,      0x4B45,
    0,      0x4B46, 0x5924, 0,      0,      0,      0,      0,      0x3568,
    0,      0,      0x352B, 0,      0,      0,      0,      0,      0,
    0x4E3B, 0x354D, 0x5721, 0x5774, 0x5353, 0,      0x4C65, 0,      0x3A4E,
    0,      0x5922, 0x595C, 0x5360, 0x587D, 0x3770, 0x5777, 0x587E, 0x587A,
    0x5921, 0x4463, 0,      0,      0x5336, 0x5874, 0x595D, 0,      0x587B,
    0,      0x4565, 0,      0,      0x4050, 0,      0,      0x5170, 0x305B,
    0,      0,      0x3C51, 0x5926, 0,      0x5925, 0,      0,      0,
    0,      0x592C, 0x592E, 0,      0x592B, 0x4A39, 0,      0,      0,
    0x5929, 0x5636, 0,      0,      0,      0x335E, 0x5928, 0,      0x407D,
    0,      0x4A4C, 0,      0x592A, 0,      0x5927, 0,      0,      0x5930,
    0,      0,      0x3631, 0,      0,      0,      0x3929, 0,      0x5240,
    0,      0,      0x4F40, 0,      0,      0x4242, 0,      0x3D44, 0x556C,
    0x3260, 0x4748, 0x3F6B, 0x592D, 0,      0x592F, 0,      0x4E6A, 0x3A6E,
    0,      0,      0,      0,      0,      0x4756, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x3163, 0,      0,
    0,      0x3459, 0x366D, 0x5934, 0,      0,      0,      0,      0x3F21,
    0,      0,      0,      0x595E, 0x474E, 0x407E, 0x5938, 0,      0,
    0,      0,      0,      0x4B57, 0x377D, 0,      0x5935, 0,      0x5937,
    0x3123, 0x5361, 0x5939, 0,      0x5045, 0,      0x5936, 0,      0,
    0x5931, 0,      0x5932, 0x4129, 0x5933, 0,      0,      0x3C73, 0x505E,
    0x3829, 0,      0x3E63, 0,      0x593D, 0,      0,      0,      0,
    0x593A, 0,      0x3033, 0,      0,      0,      0x5942, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x5944, 0x3136, 0,      0x593F, 0,      0,      0x3539,
    0,      0x3E73, 0,      0,      0,      0x4C48, 0x3A72, 0x5250, 0,
    0x5943, 0,      0,      0x3D68, 0,      0x332B, 0,      0,      0,
    0x5945, 0x3E6B, 0,      0x5946, 0x593B, 0x445F, 0,      0x593E, 0x5941,
    0x5940, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x552E, 0,      0x5635, 0,      0x4763, 0,
    0,      0,      0,      0x5948, 0,      0,      0,      0x3C59, 0x594A,
    0,      0,      0,      0x593C, 0,      0x594B, 0x462B, 0,      0,
    0,      0,      0,      0x5949, 0,      0,      0,      0,      0x5776,
    0,      0x4D23, 0,      0,      0,      0,      0,      0,      0,
    0,      0x3D21, 0,      0,      0,      0,      0,      0,      0x594C,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x453C, 0x4D35, 0,      0,      0,      0x594D, 0,      0,      0x5947,
    0x3325, 0x3F7E, 0,      0,      0,      0,      0x3835, 0,      0,
    0x407C, 0,      0,      0,      0,      0x3078, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x3476, 0,      0x594E, 0,      0x594F, 0x3422, 0x5950, 0,      0,
    0x345F, 0,      0,      0,      0,      0,      0x3041, 0,      0,
    0,      0,      0,      0,      0,      0x5951, 0x4935, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x4F71,
    0,      0,      0,      0,      0,      0,      0x5952, 0,      0,
    0,      0x4145, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x5956, 0x492E, 0,      0,      0,
    0,      0x5955, 0x5954, 0x5957, 0,      0,      0,      0,      0x4B5B,
    0,      0x3D29, 0,      0,      0,      0,      0,      0x4627, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x5953, 0x5958, 0,      0,      0,      0x5959, 0,      0,      0,
    0,      0,      0,      0x4865, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x405C, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x3679, 0x5823, 0x544A, 0,      0x542A, 0x5056,
    0x3364, 0x5557, 0,      0x4F48, 0x3962, 0,      0x3F4B, 0,      0x4362,
    0,      0,      0,      0x3652, 0,      0,      0x4D43, 0x596E, 0x5970,
    0,      0,      0,      0x3533, 0,      0x3635, 0,      0,      0,
    0,      0,      0x3E24, 0,      0,      0x486B, 0,      0,      0x482B,
    0,      0,      0x304B, 0x392B, 0x4179, 0x5962, 0,      0x403C, 0x3932,
    0,      0x3958, 0x504B, 0x3178, 0x4664, 0x3E5F, 0x3564, 0x5748, 0,
    0x5178, 0x3C66, 0x4A5E, 0,      0,      0x3C3D, 0x5966, 0x5867, 0,
    0,      0x445A, 0,      0,      0x3854, 0x483D, 0,      0,      0x3261,
    0x5459, 0,      0,      0,      0,      0x4330, 0,      0,      0x4361,
    0x5A22, 0x485F, 0,      0x5034, 0,      0x3E7C, 0x4529, 0,      0,
    0,      0x395A, 0,      0x5A23, 0,      0x5429, 0x5A24, 0,      0,
    0,      0,      0,      0x597B, 0x362C, 0,      0,      0x376B, 0x3179,
    0x597C, 0x3365, 0x3E76, 0,      0x3F76, 0x5231, 0x4064, 0,      0,
    0,      0x3633, 0x597E, 0x597D, 0,      0,      0x3E3B, 0,      0,
    0,      0x4660, 0,      0x573C, 0x5A21, 0,      0x4139, 0,      0x3572,
    0x4168, 0,      0,      0x3C75, 0,      0x3455, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x415D, 0,      0x447D,
    0,      0,      0x3C38, 0x3732, 0,      0,      0x376F, 0,      0,
    0,      0,      0,      0,      0x596C, 0,      0x463E, 0,      0x3F2D,
    0x3B4B, 0,      0,      0x354A, 0,      0x5B49, 0x5057, 0,      0x4D39,
    0x303C, 0x3376, 0x3B77, 0x5B4A, 0x3A2F, 0,      0x5464, 0x3536, 0x3573,
    0x5856, 0x4850, 0,      0,      0x3756, 0x4750, 0x5857, 0,      0x3F2F,
    0,      0,      0x5B3B, 0x5858, 0,      0,      0x504C, 0x3B2E, 0,
    0,      0,      0x6B3E, 0x4150, 0x4175, 0x5472, 0x3855, 0x3434, 0,
    0x3375, 0,      0,      0x493E, 0,      0,      0,      0x4550, 0,
    0,      0,      0x4559, 0x407B, 0,      0x3170, 0,      0x5859, 0x394E,
    0,      0x353D, 0,      0,      0x585A, 0,      0,      0x5646, 0x4B22,
    0x482F, 0x4932, 0x344C, 0x3F4C, 0,      0x3974, 0,      0x585B, 0x585C,
    0x3667, 0x3C41, 0x4C6A, 0,      0,      0,      0,      0,      0,
    0x4F77, 0,      0x585D, 0x4730, 0,      0,      0x3950, 0x3D23, 0,
    0,      0x4C5E, 0,      0x464A, 0,      0,      0,      0,      0,
    0x5860, 0,      0x585E, 0,      0,      0x585F, 0,      0,      0,
    0x307E, 0,      0x3E67, 0,      0x4A23, 0x3C74, 0,      0,      0,
    0,      0x3831, 0,      0,      0x386E, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x5862, 0,      0x3D4B,
    0,      0x5864, 0x5863, 0,      0,      0,      0,      0,      0x457C,
    0,      0,      0,      0,      0,      0,      0,      0x5865, 0,
    0,      0x5866, 0,      0,      0,      0,      0,      0,      0,
    0x4126, 0,      0x4830, 0x306C, 0x3926, 0x3C53, 0x4E71, 0x5B3D, 0x4153,
    0,      0,      0,      0,      0x362F, 0x567A, 0x452C, 0x3D59, 0x5B3E,
    0x5B3F, 0,      0,      0,      0x4078, 0x3E22, 0x404D, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x5B40,
    0x4A46, 0,      0,      0,      0x322A, 0,      0,      0,      0x5342,
    0,      0x4363, 0,      0x512B, 0,      0,      0,      0,      0x5B42,
    0,      0x4055, 0,      0,      0,      0x5B43, 0,      0x3F31, 0,
    0,      0,      0,      0,      0,      0x443C, 0,      0,      0,
    0,      0x475A, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x5B44, 0,      0,      0,      0,
    0,      0,      0,      0,      0x5968, 0x4957, 0,      0,      0,
    0x3934, 0x4E70, 0x5448, 0,      0,      0,      0,      0x307C, 0x3452,
    0,      0x5059, 0,      0,      0,      0,      0x5969, 0,      0x5E4B,
    0x596B, 0,      0,      0,      0,      0x5830, 0x3B2F, 0x3131, 0,
    0x3357, 0x584E, 0,      0,      0x5451, 0,      0,      0x3D33, 0x3F6F,
    0,      0x4F3B, 0,      0,      0x5850, 0,      0,      0,      0x374B,
    0,      0,      0,      0x5851, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x4625, 0x4778, 0x523D, 0,
    0,      0x5852, 0x4464, 0,      0x4A2E, 0,      0x4727, 0,      0x5826,
    0,      0x497D, 0x4E67, 0x3B5C, 0x306B, 0,      0,      0,      0x3B2A,
    0x502D, 0,      0x3130, 0x5764, 0x573F, 0,      0x3525, 0x4274, 0x444F,
    0,      0,      0x3229, 0,      0x3237, 0,      0x3165, 0x5F32, 0x553C,
    0x3F28, 0x422C, 0x5855, 0x4231, 0,      0x5854, 0x4E54, 0,      0x5A60,
    0,      0x4E40, 0,      0,      0x5834, 0x432E, 0x5321, 0x4E23, 0,
    0x3C34, 0x4834, 0x4251, 0,      0x3E6D, 0x5036, 0,      0x5A61, 0,
    0,      0,      0,      0x4764, 0,      0,      0x3327, 0,      0x3672,
    0x4C7C, 0x407A, 0,      0,      0x4077, 0,      0x5139, 0x5161, 0x5847,
    0,      0,      0,      0,      0,      0,      0,      0x325E, 0,
    0,      0x4065, 0,      0x3A71, 0,      0,      0x5848, 0,      0x542D,
    0,      0,      0x4F61, 0x5849, 0,      0x584A, 0x4F43, 0,      0x3378,
    0x3E47, 0,      0,      0,      0,      0x584B, 0,      0,      0,
    0,      0,      0,      0,      0x5B4C, 0,      0,      0,      0,
    0x4825, 0,      0,      0,      0x4F58, 0,      0x487E, 0x324E, 0,
    0,      0,      0,      0,      0x5356, 0x3266, 0x3C30, 0x5351, 0x4B2B,
    0x3734, 0,      0,      0,      0x3722, 0,      0,      0x4A65, 0,
    0x4821, 0x4A5C, 0x3164, 0x5070, 0,      0x4551, 0,      0,      0,
    0x5B45, 0x357E, 0,      0,      0x3F5A, 0x3945, 0x3E64, 0x416D, 0,
    0x5F36, 0x5F35, 0x563B, 0x3D50, 0x5559, 0x3048, 0x3623, 0x3F49, 0x4C28,
    0x5F33, 0x4A37, 0x5352, 0,      0x584F, 0x5236, 0x3A45, 0x4B3E, 0x4C3E,
    0,      0x5F37, 0x3570, 0x5F34, 0,      0,      0,      0x5375, 0,
    0x3354, 0x3877, 0,      0x5F3A, 0,      0x3A4F, 0x3C2A, 0x3575, 0,
    0x4D2C, 0x437B, 0x3A73, 0x4074, 0x4D42, 0x4F72, 0x5F38, 0x4F45, 0,
    0x4240, 0x5F39, 0x4270, 0,      0,      0,      0x3E7D, 0,      0x415F,
    0x4D4C, 0x5277, 0x374D, 0x5F41, 0,      0x5F44, 0,      0,      0x3771,
    0x3049, 0x3656, 0x3754, 0,      0x3A2C, 0x4C7D, 0x3F54, 0x4B31, 0x4674,
    0,      0x5628, 0x5F45, 0,      0x4E62, 0x3333, 0,      0,      0x4E7C,
    0x3435, 0,      0x4E47, 0x3A70, 0,      0x4E61, 0,      0x513D, 0,
    0,      0x5F40, 0,      0,      0x3474, 0,      0x334A, 0,      0x3866,
    0x5F3B, 0,      0,      0,      0,      0x4445, 0,      0x5F3C, 0x5F3D,
    0x5F3E, 0x453B, 0x5F3F, 0x5F42, 0x5431, 0x5F43, 0,      0x473A, 0x4E58,
    0,      0,      0,      0,      0,      0x4458, 0,      0x5F4A, 0,
    0x5F4F, 0,      0x565C, 0,      0,      0,      0,      0,      0,
    0,      0,      0x5F49, 0x5F5A, 0x4E36, 0,      0x3A47, 0x5F4E, 0x5F48,
    0x455E, 0,      0,      0x496B, 0x3A74, 0x437C, 0,      0,      0x3E57,
    0,      0x5F46, 0,      0x5F4D, 0,      0x4558, 0,      0,      0,
    0,      0x5526, 0x3A4D, 0,      0x3E4C, 0x533D, 0x3840, 0,      0x5664,
    0,      0x5F47, 0x393E, 0x3F27, 0,      0,      0x417C, 0x5F4B, 0x5F4C,
    0,      0x5F50, 0,      0,      0,      0,      0,      0x5F5B, 0x5F65,
    0,      0x5F57, 0x5F56, 0x5749, 0x5F63, 0x5F64, 0x656B, 0x5227, 0x5F52,
    0,      0x3F29, 0,      0x545B, 0,      0x3F48, 0x5F54, 0,      0,
    0,      0x4F4C, 0,      0,      0x5F5D, 0,      0x514A, 0,      0x5F5E,
    0x3027, 0x4637, 0x5F53, 0,      0x3A65, 0,      0x365F, 0x4D5B, 0x397E,
    0x5455, 0,      0,      0x5F5F, 0x4F6C, 0x3025, 0x5F67, 0x5F51, 0x5146,
    0x5F55, 0x5F58, 0x5F59, 0x5F5C, 0,      0x3B29, 0,      0x5F60, 0x5F61,
    0,      0x5F62, 0x5F66, 0x5F68, 0x5334, 0,      0,      0,      0,
    0,      0x3867, 0x4536, 0x5F6A, 0x495A, 0x4128, 0x4444, 0,      0,
    0x3F5E, 0x4F78, 0,      0,      0,      0x555C, 0x5F6E, 0,      0,
    0,      0,      0,      0,      0x3238, 0,      0x3A5F, 0x5F6C, 0,
    0x5B41, 0,      0x5164, 0,      0,      0,      0,      0x4B74, 0x343D,
    0,      0x3026, 0,      0,      0,      0,      0,      0x5F71, 0x4C46,
    0x5F72, 0,      0,      0x5F6D, 0,      0,      0,      0,      0,
    0,      0x5F69, 0,      0,      0,      0,      0x5F6B, 0,      0x5F6F,
    0x5F70, 0x3B3D, 0,      0,      0x5F73, 0,      0,      0x5F74, 0,
    0x3B23, 0,      0x4A5B, 0x4E28, 0x6027, 0x332A, 0,      0x6026, 0,
    0,      0,      0x6021, 0,      0,      0,      0,      0x5F7E, 0,
    0x4D59, 0x5F7C, 0,      0x5F7A, 0,      0x3F50, 0x5744, 0,      0x494C,
    0,      0,      0x5F78, 0x3021, 0,      0,      0,      0,      0,
    0x5F7D, 0,      0,      0,      0,      0x5F7B, 0x6022, 0,      0,
    0,      0,      0,      0x6028, 0,      0,      0,      0,      0x3748,
    0,      0,      0x4621, 0x4936, 0x4032, 0x5F75, 0,      0,      0x453E,
    0,      0x5844, 0x5F79, 0x4476, 0,      0,      0,      0,      0,
    0,      0x6023, 0x6024, 0x6025, 0x5025, 0,      0,      0x6034, 0x4C64,
    0,      0x6031, 0,      0x3F26, 0x602F, 0x4E39, 0x602B, 0x4946, 0,
    0,      0x402E, 0x602E, 0x3A6D, 0x3A30, 0x6029, 0,      0,      0,
    0x5F76, 0,      0x6033, 0,      0,      0x6038, 0,      0,      0,
    0x342D, 0x6039, 0,      0,      0x4F32, 0x3A48, 0,      0x6030, 0,
    0,      0,      0,      0,      0,      0,      0x507A, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x602C, 0,
    0x547B, 0,      0x5F77, 0,      0x4567, 0,      0x602D, 0,      0x5377,
    0,      0x6036, 0x6037, 0,      0,      0,      0,      0,      0x6044,
    0x5061, 0,      0,      0,      0x603C, 0,      0,      0x6049, 0x604A,
    0,      0,      0,      0x603E, 0x602A, 0x4924, 0x6041, 0,      0x6032,
    0,      0,      0,      0,      0,      0x4A48, 0x6043, 0,      0x6035,
    0,      0x4E4B, 0,      0x4B43, 0x604D, 0x6046, 0x6042, 0,      0x604B,
    0,      0x603A, 0x603F, 0x6040, 0,      0,      0x6045, 0,      0,
    0x6047, 0x6048, 0,      0x604C, 0,      0x603B, 0,      0,      0,
    0,      0,      0x4B54, 0x6055, 0,      0x6056, 0x6052, 0,      0,
    0,      0,      0,      0,      0x6050, 0x3C4E, 0,      0,      0x6051,
    0,      0x3842, 0x5845, 0,      0,      0,      0,      0,      0,
    0,      0,      0x506A, 0,      0,      0x426F, 0,      0,      0x604F,
    0x603D, 0,      0,      0,      0x6054, 0x6053, 0,      0,      0x6057,
    0,      0,      0,      0,      0x605C, 0x6058, 0,      0,      0,
    0x5676, 0x3330, 0,      0x576C, 0,      0x4B3B, 0,      0,      0x605A,
    0,      0x4E7B, 0,      0,      0,      0x3A59, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x6061, 0x605D, 0x522D, 0,      0,      0,      0,      0,      0x6062,
    0,      0,      0x605B, 0x6059, 0x605F, 0,      0,      0x6060, 0,
    0,      0,      0,      0,      0x605E, 0,      0x6064, 0,      0,
    0,      0x4677, 0x582C, 0x546B, 0x6066, 0x4A49, 0,      0,      0,
    0,      0x6065, 0,      0,      0,      0,      0x3841, 0,      0,
    0,      0,      0x6067, 0x6068, 0,      0,      0,      0,      0,
    0,      0,      0,      0x6069, 0x6063, 0,      0,      0,      0,
    0,      0,      0,      0x3A3F, 0x4C67, 0,      0,      0,      0x606A,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x4F79, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x606B, 0,      0,      0,      0,      0,      0,      0,      0x4842,
    0,      0,      0,      0,      0x3D40, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x4452, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x606C, 0,      0,      0x606D, 0,      0,      0x4774, 0x4B44,
    0,      0x606E, 0x3B58, 0x5836, 0x5272, 0x606F, 0x4D45, 0,      0x365A,
    0,      0,      0,      0,      0,      0,      0x6071, 0,      0x5430,
    0,      0,      0x4027, 0x3451, 0,      0,      0x4E27, 0x6070, 0,
    0,      0,      0x6072, 0x394C, 0,      0,      0x397A, 0x4D3C, 0x6073,
    0,      0,      0,      0x4654, 0x6074, 0,      0x5432, 0,      0x4826,
    0x6076, 0x6075, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x6077, 0,      0,      0x4D41, 0,      0,      0,      0x4A25,
    0,      0,      0,      0,      0x545A, 0x5B57, 0x5B59, 0,      0x5B58,
    0x3967, 0x5B5C, 0x5B5D, 0x3558, 0,      0,      0x5B5A, 0,      0,
    0,      0,      0,      0x5B5B, 0x3321, 0x5B5F, 0,      0,      0x3B78,
    0,      0x5637, 0,      0x5B60, 0,      0,      0,      0,      0x3E79,
    0,      0,      0x373B, 0,      0x5B50, 0x4C2E, 0x3F32, 0x3B35, 0x5778,
    0x3F53, 0,      0,      0,      0,      0,      0x3F69, 0,      0,
    0x3C61, 0x4C33, 0x5B5E, 0x3053, 0x4E6B, 0x3758, 0x5739, 0x4642, 0,
    0,      0x4024, 0,      0x4C39, 0,      0x5B67, 0x5B61, 0x463A, 0x5B63,
    0,      0x5B68, 0,      0x4577, 0,      0,      0,      0x5B6A, 0,
    0,      0x5B69, 0x3F40, 0,      0,      0,      0x5B66, 0x5B65, 0,
    0,      0,      0,      0,      0x3439, 0x402C, 0x4222, 0x5B62, 0x5B64,
    0,      0,      0,      0,      0x504D, 0x5B6D, 0,      0,      0,
    0,      0,      0x405D, 0x5B72, 0,      0,      0,      0,      0,
    0,      0,      0x3662, 0,      0,      0,      0,      0x5B73, 0x5B52,
    0x3938, 0x542B, 0x5B6C, 0,      0x3F51, 0x5B70, 0,      0x5B51, 0,
    0x3566, 0,      0x5B6B, 0x3F65, 0,      0,      0,      0x5B6E, 0,
    0x5B71, 0,      0,      0,      0x5B79, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x3921, 0x3023, 0,      0,
    0,      0,      0,      0,      0,      0x4271, 0,      0,      0x3347,
    0x5B6F, 0,      0,      0x5B78, 0,      0x4652, 0x5B74, 0,      0,
    0x5B75, 0x5B77, 0x5B76, 0,      0,      0x5B7E, 0,      0x5372, 0x323A,
    0,      0,      0,      0x5B7D, 0,      0,      0,      0,      0,
    0,      0,      0,      0x5C24, 0,      0x5B7B, 0,      0,      0,
    0,      0x5B7A, 0,      0,      0,      0x5B7C, 0x4560, 0x3B79, 0,
    0,      0x5C23, 0,      0,      0x5C25, 0,      0x4C43, 0,      0,
    0,      0x3651, 0x5D40, 0,      0,      0,      0x5C21, 0,      0x5C22,
    0,      0,      0,      0x4735, 0,      0,      0,      0x3669, 0,
    0,      0,      0x5C27, 0,      0,      0,      0,      0x5C26, 0,
    0x5C29, 0x3124, 0,      0,      0x354C, 0,      0,      0,      0,
    0,      0x3F30, 0,      0,      0,      0,      0,      0x515F, 0,
    0,      0,      0,      0x3642, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x5C28, 0,      0,      0,      0,      0,      0,      0,      0x4B7A,
    0x6B73, 0,      0,      0,      0x4B5C, 0,      0,      0x4B7E, 0,
    0,      0,      0x4C41, 0,      0,      0,      0,      0,      0x487B,
    0,      0,      0,      0,      0,      0,      0x5C2A, 0,      0,
    0,      0,      0,      0x4C6E, 0x5C2B, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x5B53, 0,      0x5C2F, 0x5C2C, 0,
    0x3E33, 0,      0x4A7B, 0,      0,      0,      0x5C2D, 0,      0,
    0,      0,      0,      0,      0,      0,      0x494A, 0x4439, 0,
    0,      0,      0,      0,      0x473D, 0x5C2E, 0,      0,      0,
    0x5476, 0x5066, 0,      0,      0,      0,      0,      0,      0,
    0,      0x442B, 0x3655, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x5B54, 0,      0,      0,      0,      0x315A,
    0,      0,      0,      0x5B55, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x5B56, 0,      0,
    0,      0x3A3E, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x4840, 0,
    0,      0,      0,      0,      0,      0x4A3F, 0x4849, 0,      0x5733,
    0,      0x4979, 0,      0,      0x3F47, 0,      0,      0x3A78, 0,
    0,      0x523C, 0,      0,      0,      0,      0,      0,      0,
    0,      0x623A, 0,      0x3426, 0,      0,      0x3138, 0,      0,
    0,      0,      0,      0x3834, 0,      0x4F44, 0,      0,      0,
    0,      0x5967, 0x4F26, 0x4D62, 0,      0,      0x596D, 0x3660, 0,
    0x5239, 0,      0,      0x393B, 0,      0,      0,      0,      0x6239,
    0x6237, 0,      0x3473, 0,      0x4C6C, 0x4C2B, 0x3772, 0,      0x5832,
    0x516B, 0x3A3B, 0,      0x4A27, 0,      0,      0x4D37, 0,      0,
    0x5244, 0x3F64, 0x3C50, 0x3661, 0,      0x5E45, 0,      0,      0,
    0,      0x5E46, 0x5B3C, 0,      0x5159, 0,      0,      0x4666, 0x444E,
    0x376E, 0,      0x375C, 0,      0,      0x3F7C, 0x5760, 0,      0x4675,
    0,      0,      0x313C, 0x5E48, 0x3D31, 0x4C57, 0x5E4A, 0,      0x5E49,
    0,      0,      0,      0,      0,      0x356C, 0,      0x495D, 0,
    0,      0x3042, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x452E, 0x452B, 0,
    0x444C, 0,      0x3C69, 0x4B7D, 0,      0,      0,      0x3A43, 0,
    0,      0,      0x6579, 0x4867, 0x657A, 0x4D7D, 0,      0x5731, 0x383E,
    0x4268, 0,      0x4851, 0,      0,      0x657B, 0,      0,      0,
    0,      0x364A, 0x3C4B, 0,      0,      0x517D, 0x6621, 0,      0x436E,
    0,      0,      0,      0,      0x6624, 0,      0,      0,      0,
    0x657E, 0x6625, 0x4D57, 0,      0,      0x3741, 0x657C, 0x657D, 0x6623,
    0,      0,      0x445D, 0x6628, 0,      0,      0x6627, 0,      0,
    0,      0,      0,      0,      0x4343, 0,      0x465E, 0,      0,
    0x662A, 0,      0,      0,      0,      0,      0,      0,      0x4437,
    0,      0,      0,      0x6622, 0x4A3C, 0,      0,      0,      0,
    0x3D63, 0x3943, 0x6626, 0x5055, 0x4E2F, 0,      0,      0x6629, 0x6630,
    0,      0x5226, 0,      0x3D2A, 0x662D, 0,      0,      0,      0,
    0,      0x662F, 0,      0x4051, 0,      0,      0x524C, 0,      0,
    0,      0x3C27, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x6631, 0,      0x5276, 0,
    0,      0,      0x574B, 0,      0x4D7E, 0,      0x4D5E, 0x4226, 0x662B,
    0x662C, 0x3D3F, 0x662E, 0x6633, 0,      0,      0x6632, 0,      0,
    0,      0,      0x6636, 0,      0x6638, 0,      0,      0,      0,
    0x446F, 0,      0,      0,      0x4448, 0,      0,      0x3E6A, 0x496F,
    0,      0,      0x6637, 0,      0x3670, 0,      0,      0,      0x4364,
    0,      0,      0,      0,      0,      0,      0,      0x5369, 0x6634,
    0,      0x6635, 0,      0x4822, 0,      0,      0,      0,      0,
    0x663D, 0,      0,      0,      0x6639, 0,      0,      0,      0,
    0,      0x4645, 0,      0,      0x4D71, 0x663B, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x663C, 0,
    0,      0,      0,      0x3B69, 0,      0,      0,      0,      0,
    0,      0,      0x663E, 0,      0,      0,      0,      0x663A, 0,
    0,      0x4037, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x5324, 0x663F, 0x4974, 0x6643, 0,      0,      0x6644,
    0,      0,      0,      0,      0x5076, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x433D, 0,      0,      0,
    0,      0,      0,      0,      0x4344, 0x6642, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6641, 0,      0,      0,      0,      0,      0,
    0,      0x6647, 0x4F31, 0,      0x6B74, 0,      0,      0x664A, 0,
    0,      0,      0,      0,      0x6645, 0,      0,      0x3C5E, 0x4929,
    0,      0,      0,      0,      0,      0,      0x3C35, 0,      0,
    0x4F53, 0,      0,      0,      0,      0,      0x6648, 0,      0x6649,
    0,      0x664E, 0,      0x6650, 0,      0,      0,      0x6651, 0,
    0,      0,      0x664B, 0x3555, 0,      0x664C, 0,      0,      0x664F,
    0,      0,      0x445B, 0,      0x6646, 0,      0,      0,      0,
    0,      0x664D, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x6652, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6654, 0x6653, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x6655, 0,      0x5978, 0,      0,      0x6656,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6657,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x5753, 0x665D, 0,
    0x665E, 0x3F57, 0x5450, 0,      0x5756, 0x3466, 0x4B6F, 0x665A, 0x5843,
    0x574E, 0x5022, 0,      0x434F, 0,      0,      0x665F, 0x3C3E, 0x3942,
    0x665B, 0x5127, 0,      0,      0x3A22, 0x424F, 0,      0x582B, 0,
    0,      0,      0x4A6B, 0x656E, 0,      0x665C, 0,      0x3775, 0,
    0,      0,      0,      0x4866, 0,      0,      0x4475, 0,      0,
    0x6532, 0x447E, 0,      0x4B7C, 0x6533, 0x552C, 0,      0x536E, 0x4A58,
    0x3032, 0,      0x4B4E, 0x4D6A, 0,      0,      0x3A6A, 0,      0,
    0,      0x6535, 0,      0x6534, 0,      0x575A, 0x3959, 0x5666, 0x3628,
    0x4D70, 0x524B, 0x3126, 0x4A35, 0,      0x3368, 0x4973, 0x3F4D, 0x507B,
    0x4A52, 0x6536, 0x3B42, 0,      0,      0,      0x4F5C, 0x392C, 0,
    0,      0,      0,      0x5457, 0,      0,      0x3A26, 0x5167, 0x4F7C,
    0x3C52, 0,      0x6537, 0x485D, 0,      0,      0,      0x3F6D, 0x3176,
    0x4B5E, 0,      0,      0x3C45, 0,      0x3C44, 0x527A, 0x435C, 0x3F5C,
    0,      0,      0,      0,      0x383B, 0,      0,      0,      0x4342,
    0,      0x3A2E, 0x5422, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x475E, 0x442F, 0x326C, 0,      0x3951, 0,
    0,      0x653B, 0x4148, 0,      0,      0x552F, 0,      0,      0,
    0,      0,      0x653C, 0,      0x653E, 0,      0,      0,      0,
    0,      0,      0,      0x3467, 0x3654, 0x4B42, 0x5130, 0x353C, 0,
    0,      0x4A59, 0,      0x3762, 0,      0,      0x4964, 0,      0x3D2B,
    0,      0,      0x4E3E, 0x5770, 0,      0,      0,      0,      0x5021,
    0,      0x4959, 0,      0,      0x367B, 0x6658, 0x3C62, 0,      0x333E,
    0,      0x4950, 0,      0x6659, 0x3322, 0,      0,      0,      0,
    0x5E4C, 0,      0x5348, 0x5E4D, 0,      0x5222, 0,      0,      0,
    0,      0x5E4E, 0,      0,      0,      0,      0x3E4D, 0,      0,
    0x5E4F, 0,      0,      0,      0x4A2C, 0x527C, 0x335F, 0x656A, 0x4461,
    0x3E21, 0x4E32, 0x4472, 0x3E56, 0x4628, 0x3263, 0,      0,      0x3E53,
    0,      0,      0x477C, 0x4C6B, 0x3D6C, 0x4E5D, 0,      0,      0x4A3A,
    0x4641, 0x656C, 0x503C, 0,      0,      0,      0x5539, 0,      0,
    0,      0x656D, 0,      0,      0,      0,      0x4A74, 0,      0x4D40,
    0x4245, 0,      0x656F, 0,      0x4244, 0x6570, 0,      0,      0,
    0,      0,      0,      0,      0x6578, 0x4D4D, 0,      0x493D, 0,
    0,      0,      0,      0,      0,      0,      0x5259, 0x6128, 0,
    0,      0,      0,      0x536C, 0,      0x4B6A, 0x4671, 0,      0,
    0,      0,      0,      0x612C, 0,      0,      0,      0x6127, 0x6129,
    0,      0,      0x612A, 0x612F, 0,      0,      0x326D, 0,      0x612B,
    0x385A, 0x612D, 0x612E, 0x6130, 0x353A, 0x6131, 0,      0,      0,
    0,      0,      0x6133, 0x6138, 0,      0,      0,      0,      0,
    0x5152, 0,      0x6136, 0x6135, 0x416B, 0,      0,      0,      0x6137,
    0,      0x5440, 0,      0x6132, 0,      0x613A, 0x3036, 0,      0,
    0,      0,      0x6134, 0,      0x3F79, 0,      0x6139, 0,      0,
    0x613B, 0,      0,      0,      0,      0,      0,      0x613E, 0,
    0,      0,      0,      0,      0,      0x613C, 0,      0,      0,
    0,      0,      0,      0x5645, 0,      0,      0,      0,      0,
    0,      0,      0x4F3F, 0,      0,      0x613D, 0x613F, 0x424D, 0,
    0x366B, 0,      0x5378, 0,      0,      0x474D, 0,      0,      0x3765,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x3E7E, 0,      0,      0,      0,      0,      0,      0x6140,
    0x6141, 0,      0,      0x6147, 0x3367, 0,      0,      0,      0,
    0,      0,      0x4669, 0,      0,      0,      0,      0,      0x345E,
    0,      0x5142, 0,      0,      0,      0,      0x6148, 0,      0,
    0x6146, 0,      0,      0,      0,      0,      0x6145, 0,      0x6143,
    0x6142, 0,      0x3140, 0,      0,      0,      0x5538, 0x6144, 0,
    0,      0,      0,      0,      0x614B, 0,      0,      0,      0,
    0,      0,      0,      0,      0x614C, 0x614A, 0,      0,      0,
    0,      0,      0,      0,      0,      0x6F7A, 0,      0,      0x6153,
    0x6152, 0x4736, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x6149, 0,      0,      0x614E, 0,
    0x6150, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x6154, 0,      0x6151, 0x614D, 0,      0,
    0x614F, 0,      0,      0,      0,      0x6155, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x6156, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x6157, 0,      0,
    0,      0x6158, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x615A, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x615B, 0,      0,      0,
    0,      0,      0,      0,      0x4E21, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x675D, 0,      0x3428, 0x565D, 0,      0,      0x5132, 0x3332, 0,
    0,      0x3924, 0x5773, 0x4749, 0x3E5E, 0x392E, 0,      0x4E57, 0,
    0,      0x326E, 0x5B4F, 0,      0x3C3A, 0x5251, 0x4B48, 0x304D, 0,
    0,      0x4F6F, 0,      0,      0,      0,      0,      0x5963, 0x3D6D,
    0,      0,      0x3152, 0x4A50, 0x323C, 0,      0x4B27, 0x372B, 0,
    0x4A26, 0,      0,      0,      0x4F23, 0,      0,      0x6078, 0x554A,
    0x607B, 0,      0,      0x607A, 0x4541, 0x4C7B, 0,      0x4131, 0x6079,
    0x5663, 0x322F, 0x5644, 0x355B, 0,      0,      0,      0,      0,
    0,      0,      0,      0x3478, 0x5621, 0,      0,      0,      0,
    0,      0x4F2F, 0x306F, 0,      0,      0x607C, 0,      0,      0,
    0,      0,      0x6121, 0x3323, 0,      0,      0x607D, 0x607E, 0x4331,
    0,      0,      0,      0,      0x435D, 0,      0x6122, 0x3779, 0,
    0,      0,      0,      0,      0,      0x3B4F, 0,      0,      0,
    0,      0,      0,      0,      0x6123, 0x443B, 0,      0,      0,
    0,      0,      0x6124, 0,      0,      0x6125, 0,      0,      0x6126,
    0x3431, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x3849, 0x463D,
    0x446A, 0,      0x3222, 0,      0x5052, 0,      0x675B, 0x3B43, 0x5357,
    0x5344, 0,      0x3963, 0x624F, 0,      0,      0,      0x572F, 0,
    0x476C, 0x3153, 0,      0,      0x3432, 0x6251, 0,      0,      0,
    0x5072, 0x422E, 0x6250, 0,      0x3F62, 0x5326, 0x3557, 0x6252, 0x356A,
    0,      0x436D, 0x387D, 0,      0x382E, 0,      0x4553, 0x374F, 0x6254,
    0,      0,      0,      0,      0x6253, 0x3648, 0x5779, 0,      0,
    0,      0,      0,      0x4D25, 0,      0,      0,      0,      0,
    0x6258, 0,      0x6256, 0x4A7C, 0x3F35, 0x5339, 0x6255, 0,      0,
    0,      0,      0x6257, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x412E, 0x4048, 0,      0,      0,
    0,      0,      0,      0x625B, 0x625A, 0x402A, 0,      0,      0x414E,
    0,      0,      0,      0,      0x625C, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x625D,
    0,      0x625E, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x5B48, 0,      0x5153, 0x4D22, 0,      0,      0x3D28,
    0,      0,      0,      0x5E43, 0x5825, 0x3F2A, 0x5B4D, 0x526C, 0x467A,
    0x452A, 0,      0,      0,      0x5E44, 0,      0x3157, 0x5F2E, 0,
    0,      0,      0x4A3D, 0,      0x5F31, 0,      0x392D, 0,      0x527D,
    0,      0x3825, 0x3A6B, 0,      0,      0x335A, 0,      0,      0,
    0x355C, 0x5545, 0,      0,      0,      0,      0x4356, 0x4F52, 0x3B21,
    0,      0x6573, 0x6572, 0,      0,      0x6574, 0,      0x4D64, 0,
    0x4875, 0,      0,      0,      0,      0,      0,      0,      0x352F,
    0x473F, 0,      0x6576, 0,      0,      0,      0x6C30, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x6566, 0,      0x3969, 0x3531, 0,
    0x423C, 0x6568, 0x6567, 0x6569, 0,      0,      0,      0,      0x524D,
    0,      0,      0,      0x616A, 0x504E, 0,      0x4D2E, 0,      0x5165,
    0,      0,      0x324A, 0x316B, 0,      0x3172, 0x456D, 0,      0,
    0x5543, 0x5330, 0,      0x615C, 0,      0,      0,      0x615D, 0,
    0x525B, 0,      0x3339, 0x314B, 0,      0,      0,      0x4D79, 0x5577,
    0x615E, 0,      0x3E36, 0x347D, 0,      0x615F, 0x3A5C, 0x6160, 0x3B32,
    0x4249, 0x6161, 0,      0,      0,      0x506C, 0,      0x4D3D, 0,
    0,      0x6162, 0,      0x3543, 0x4547, 0x6163, 0,      0,      0x6164,
    0,      0,      0,      0,      0x5379, 0,      0,      0,      0,
    0,      0,      0x6165, 0,      0x512D, 0,      0,      0x6166, 0x4E22,
    0,      0,      0,      0,      0,      0,      0x6167, 0,      0x3542,
    0,      0,      0,      0,      0x6168, 0x3B55, 0,      0,      0,
    0,      0,      0x5044, 0x6260, 0x3158, 0x5264, 0,      0,      0x6261,
    0,      0,      0x3C49, 0x484C, 0,      0x6263, 0x6C7E, 0x6C7D, 0x5F2F,
    0,      0,      0,      0x6262, 0x563E, 0x4D7C, 0x4326, 0,      0,
    0,      0x6343, 0,      0,      0x5652, 0x6267, 0,      0,      0x6268,
    0,      0,      0x5347, 0,      0,      0x626C, 0x3F6C, 0,      0x626D,
    0x6265, 0,      0,      0x3340, 0,      0,      0,      0x446E, 0,
    0,      0x626E, 0,      0,      0x5043, 0,      0x3A76, 0x6269, 0x375E,
    0x3B33, 0x4C2C, 0x4B4B, 0x6264, 0x6266, 0x626A, 0x626B, 0,      0,
    0,      0x6277, 0,      0,      0x6274, 0x5475, 0x6273, 0,      0,
    0x452D, 0,      0x557A, 0x4542, 0x3240, 0,      0,      0x626F, 0,
    0x6272, 0x412F, 0x4B3C, 0,      0,      0x3521, 0x6279, 0,      0,
    0,      0x3C31, 0x6271, 0x5054, 0x5439, 0x6275, 0x3956, 0x6276, 0,
    0,      0,      0x4753, 0,      0,      0,      0,      0,      0x6270,
    0,      0,      0,      0,      0,      0x575C, 0x6D21, 0,      0,
    0x6278, 0,      0x6D25, 0x627E, 0x4A51, 0,      0,      0,      0,
    0,      0,      0,      0x4135, 0,      0x3B50, 0,      0,      0x3F56,
    0,      0x3A63, 0,      0,      0x4B21, 0,      0,      0,      0x6D26,
    0x6D23, 0,      0,      0x6D22, 0,      0,      0,      0,      0x3B56,
    0x6D27, 0x5074, 0,      0,      0x6D24, 0x3A5E, 0x3677, 0x6321, 0x3632,
    0x4C71, 0x3927, 0,      0x4F22, 0x4721, 0,      0,      0x3F52, 0,
    0,      0x3671, 0,      0x627A, 0x627B, 0x627D, 0x627C, 0x4455, 0x6322,
    0,      0x5341, 0,      0,      0,      0x6327, 0x4744, 0,      0,
    0,      0,      0x4F24, 0,      0,      0x6329, 0x3A37, 0,      0,
    0,      0,      0x6328, 0,      0x3B5A, 0,      0x6323, 0,      0,
    0,      0x6324, 0x632A, 0,      0x6326, 0,      0x4E72, 0x5346, 0,
    0,      0x3B3C, 0,      0,      0x5443, 0,      0x447A, 0,      0,
    0x6D28, 0x507C, 0x6325, 0,      0x4375, 0,      0x632D, 0x312F, 0,
    0x6332, 0,      0,      0,      0x3C42, 0,      0,      0x632C, 0x353F,
    0,      0,      0,      0,      0,      0,      0,      0,      0x4769,
    0x6330, 0,      0,      0,      0x3E2A, 0x4D6F, 0,      0,      0,
    0,      0,      0x3B73, 0,      0,      0,      0x4C68, 0,      0,
    0x632F, 0,      0x6331, 0,      0x4F27, 0x632E, 0,      0x4E29, 0x3B5D,
    0,      0,      0,      0,      0,      0x356B, 0x3E65, 0x3252, 0x334D,
    0,      0x3139, 0x632B, 0x3251, 0x352C, 0x395F, 0x3668, 0,      0,
    0x4F6B, 0x6337, 0,      0x3B4C, 0,      0,      0x4847, 0x504A, 0,
    0,      0,      0,      0,      0x6338, 0x336E, 0,      0,      0,
    0,      0x6D29, 0,      0x537A, 0x5364, 0,      0,      0,      0x6D2A,
    0x6339, 0x5262, 0,      0,      0,      0,      0,      0x6335, 0,
    0,      0,      0,      0x535E, 0,      0,      0,      0,      0x3850,
    0x6333, 0,      0,      0x6336, 0x375F, 0,      0x6334, 0x4022, 0,
    0,      0,      0x633A, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x5438, 0,      0,      0,      0,
    0,      0,      0,      0,      0x3448, 0,      0x633B, 0,      0x3B45,
    0,      0x4977, 0,      0,      0x4965, 0,      0,      0,      0x443D,
    0,      0,      0,      0,      0,      0,      0,      0x6D2B, 0,
    0,      0,      0,      0x427D, 0,      0,      0,      0,      0x3B5B,
    0x3F2E, 0,      0,      0,      0,      0,      0,      0,      0x4E3F,
    0,      0,      0,      0,      0x633C, 0,      0x3F36, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x316F,
    0,      0,      0x5477, 0,      0,      0,      0,      0,      0x633E,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6D2D,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x633F, 0x3A29, 0x6D2C, 0,      0,      0x633D, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x6340, 0,
    0,      0,      0,      0,      0,      0x3A36, 0,      0,      0,
    0x362E, 0,      0,      0,      0,      0,      0x5038, 0,      0x3043,
    0x6D2E, 0,      0,      0,      0,      0,      0x6D2F, 0x4041, 0,
    0x6341, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x4533, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x6342, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x5C32, 0,
    0,      0,      0,      0,      0,      0x6D30, 0,      0x386A, 0,
    0x4E6C, 0x6A27, 0x5067, 0x4A79, 0x4856, 0x4F37, 0x3349, 0x4E52, 0x3D64,
    0,      0,      0x635E, 0x3B72, 0x6A28, 0x553D, 0,      0x465D, 0x6A29,
    0,      0,      0,      0x6A2A, 0,      0x6A2C, 0x6A2B, 0,      0x6A2E,
    0x6A2D, 0,      0,      0,      0,      0x3D58, 0,      0x6A2F, 0,
    0x423E, 0,      0,      0,      0,      0x3441, 0x3477, 0,      0,
    0x3B27, 0,      0,      0,      0,      0,      0x6C66, 0x6C65, 0x373F,
    0x4B79, 0x3162, 0,      0x6C67, 0,      0,      0,      0x4948, 0x6C68,
    0x6C69, 0,      0x4A56, 0x5E50, 0x3245, 0x547A, 0,      0,      0x464B,
    0x3047, 0x3472, 0x4853, 0,      0,      0,      0x4D50, 0,      0,
    0x3F38, 0,      0,      0,      0,      0,      0,      0,      0x3F5B,
    0,      0,      0x4724, 0x5634, 0,      0x4029, 0x5E51, 0x4928, 0x516F,
    0x4524, 0x3067, 0x3336, 0x4845, 0,      0,      0x3062, 0,      0,
    0x3776, 0,      0,      0x457A, 0,      0,      0x3673, 0,      0x5552,
    0x3350, 0x3C3C, 0,      0,      0,      0x332D, 0,      0,      0,
    0,      0x3E71, 0x3051, 0,      0,      0,      0,      0,      0,
    0x5256, 0x4A63, 0x5725, 0,      0x4D36, 0x3636, 0x3F39, 0x555B, 0,
    0x3827, 0x4557, 0,      0,      0,      0x5E52, 0x3F59, 0x4255, 0x4740,
    0,      0x3B24, 0x3128, 0,      0,      0x456A, 0,      0,      0x457B,
    0x4C27, 0,      0,      0,      0,      0x3127, 0,      0,      0,
    0x3556, 0,      0,      0,      0x4428, 0,      0x5E53, 0x513A, 0x3369,
    0,      0x4372, 0,      0,      0x3777, 0,      0x5674, 0x3523, 0x3270,
    0x4434, 0x4469, 0x402D, 0x5E54, 0,      0x3068, 0x4544, 0x4160, 0,
    0x3955, 0,      0x3E5C, 0x4D58, 0x304E, 0,      0x4D4F, 0x5E56, 0x3E50,
    0x573E, 0x5E55, 0x5550, 0x305D, 0,      0,      0x4462, 0,      0,
    0x4223, 0x3C70, 0,      0x5335, 0x4039, 0x4521, 0x3226, 0x5471, 0,
    0,      0x4028, 0x4A43, 0x5E57, 0x557C, 0,      0x3930, 0,      0x482D,
    0x4B29, 0,      0x5E59, 0x3F3D, 0,      0,      0,      0,      0x4634,
    0x5727, 0x4A30, 0x4443, 0,      0x3356, 0x3952, 0,      0,      0,
    0,      0x5638, 0x6A7C, 0x3034, 0,      0,      0,      0,      0x3F66,
    0,      0,      0x4C74, 0,      0,      0,      0,      0x4D5A, 0,
    0,      0,      0x563F, 0x424E, 0,      0x4E4E, 0x4C22, 0x502E, 0x4453,
    0x3532, 0x5E58, 0x5575, 0x3C37, 0x3B53, 0,      0,      0x3024, 0,
    0x4532, 0x346C, 0,      0,      0,      0x5571, 0,      0,      0x6A7D,
    0,      0,      0,      0,      0,      0,      0x5E5A, 0x4D26, 0,
    0,      0x4D6C, 0,      0,      0,      0,      0x4E66, 0x5E5C, 0,
    0x4D31, 0x4026, 0,      0,      0x573D, 0,      0x5E5B, 0x3046, 0x3A34,
    0x4953, 0x4473, 0x3E68, 0,      0,      0,      0,      0x3236, 0,
    0,      0,      0,      0,      0,      0,      0,      0x404C, 0x4B70,
    0,      0x3C71, 0x3B3B, 0x3537, 0,      0,      0,      0x4575, 0,
    0x5E66, 0,      0,      0,      0x5E63, 0x3E5D, 0,      0,      0x5E5F,
    0,      0,      0,      0,      0x3437, 0x3D5D, 0,      0,      0x5E60,
    0x446D, 0,      0,      0,      0,      0x4F46, 0,      0x3560, 0,
    0,      0,      0,      0x365E, 0x4A5A, 0x3574, 0x5E65, 0,      0x5546,
    0,      0x5E61, 0x4C4D, 0x467E, 0,      0x4545, 0,      0,      0,
    0x5234, 0,      0x3E72, 0,      0,      0,      0,      0,      0,
    0,      0x4253, 0,      0x4C3D, 0x3338, 0,      0x3D53, 0,      0x3F58,
    0x4D46, 0x515A, 0x346B, 0,      0x5E64, 0x5E5D, 0x5E67, 0,      0x6A7E,
    0,      0,      0x4230, 0x5E62, 0,      0,      0x5640, 0x3527, 0,
    0x3274, 0,      0x5E68, 0,      0x5E72, 0,      0,      0,      0,
    0,      0x5E6D, 0,      0x5E71, 0,      0,      0x4860, 0,      0,
    0,      0x5761, 0x5E6F, 0x4368, 0x4C61, 0,      0x3265, 0,      0,
    0,      0x523E, 0,      0,      0,      0,      0,      0,      0,
    0x5E6E, 0,      0x5E6B, 0x4E55, 0,      0x3427, 0,      0,      0,
    0,      0,      0x3F2B, 0x3E3E, 0,      0,      0x3D52, 0,      0,
    0,      0,      0x5E69, 0,      0x542E, 0,      0x5E5E, 0,      0x5E6A,
    0,      0,      0,      0,      0x403F, 0,      0x5E6C, 0x3273, 0x3869,
    0x4227, 0,      0,      0x3D41, 0,      0,      0,      0,      0,
    0x5E75, 0x5E78, 0,      0,      0x322B, 0x3424, 0,      0,      0x346A,
    0x4926, 0,      0,      0,      0,      0,      0,      0x5E76, 0x4B51,
    0,      0x3863, 0,      0x5E77, 0x5E7A, 0,      0,      0,      0,
    0x5E79, 0,      0,      0,      0x4C42, 0,      0x3061, 0x346E, 0,
    0,      0,      0,      0,      0,      0x653A, 0,      0,      0,
    0,      0,      0x502F, 0,      0,      0x326B, 0,      0x6B21, 0,
    0x5E74, 0,      0,      0x4963, 0x5E73, 0x305A, 0x5221, 0x3177, 0,
    0x4C2F, 0,      0,      0,      0,      0,      0,      0,      0x5E70,
    0,      0x4B24, 0,      0,      0,      0x552A, 0,      0,      0,
    0,      0,      0x5E7B, 0,      0,      0,      0,      0,      0,
    0,      0,      0x345D, 0,      0x4426, 0,      0,      0,      0x5E7D,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x437E, 0x4421, 0x5F21, 0,      0,      0,      0,      0,
    0,      0,      0x414C, 0,      0x5E7C, 0x3E6F, 0,      0x4632, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x3345,
    0x4876, 0,      0,      0x4B3A, 0x5E7E, 0,      0,      0x5F24, 0,
    0,      0,      0,      0x5732, 0,      0,      0,      0,      0,
    0x3337, 0,      0,      0,      0,      0x4143, 0,      0,      0x474B,
    0x3225, 0x3469, 0,      0x572B, 0,      0,      0,      0,      0x446C,
    0,      0x5F22, 0x5F23, 0,      0x5F25, 0,      0x3A33, 0,      0,
    0,      0x5F26, 0,      0x405E, 0,      0,      0x4943, 0,      0,
    0,      0,      0,      0,      0,      0x3259, 0x4766, 0,      0x5F27,
    0,      0x475C, 0,      0,      0,      0,      0x5F28, 0x6B22, 0,
    0,      0,      0,      0,      0x4B53, 0,      0,      0,      0x5F2A,
    0,      0x5F29, 0,      0x3241, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x454A, 0,      0,      0,      0,      0,      0,
    0,      0,      0x5F2B, 0,      0,      0,      0,      0,      0,
    0,      0,      0x545C, 0,      0,      0,      0,      0,      0x4841,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x5F2C, 0,      0,      0,      0,      0,
    0x3E70, 0,      0,      0x5F2D, 0x5627, 0,      0,      0,      0,
    0x6A37, 0x6B36, 0x4A55, 0,      0x587C, 0x3844, 0,      0x3925, 0,
    0,      0x3745, 0x557E, 0,      0,      0,      0,      0,      0x394A,
    0,      0,      0x5027, 0x744D, 0,      0,      0x3550, 0,      0,
    0x4374, 0,      0x3E48, 0,      0,      0,      0x6B37, 0x303D, 0,
    0,      0x3D4C, 0,      0x4132, 0,      0x3156, 0x3328, 0,      0,
    0,      0x3852, 0x4922, 0,      0,      0x3658, 0,      0,      0,
    0,      0x6B38, 0x3E34, 0,      0,      0,      0x4A7D, 0,      0x4743,
    0,      0x557B, 0,      0,      0x3773, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x4E44, 0,      0,      0,      0x552B, 0x3173, 0,
    0,      0,      0x6C33, 0x305F, 0,      0x6C35, 0,      0,      0,
    0x3637, 0,      0x414F, 0,      0x757A, 0x5031, 0,      0,      0x5565,
    0,      0x4E53, 0,      0,      0x3D6F, 0x3362, 0,      0x382B, 0,
    0x5536, 0,      0x6D3D, 0,      0x364F, 0,      0x4B39, 0x5042, 0,
    0,      0,      0,      0,      0,      0,      0,      0x373D, 0,
    0,      0x6C36, 0x4A29, 0,      0,      0,      0x4554, 0,      0x6C39,
    0x6C38, 0x4243, 0x6C37, 0,      0,      0,      0,      0x507D, 0x6C3A,
    0,      0x6C3B, 0x5765, 0,      0,      0x6C3C, 0,      0,      0,
    0x6C3D, 0x466C, 0,      0,      0,      0,      0,      0,      0,
    0,      0x4E5E, 0,      0x3C48, 0,      0,      0x4855, 0x3529, 0x3E49,
    0x563C, 0x5467, 0,      0,      0x512E, 0x5071, 0x6A38, 0x6A39, 0x6A3A,
    0x3A35, 0,      0,      0,      0,      0x4A31, 0x3F75, 0,      0,
    0x4D7A, 0,      0,      0,      0,      0,      0x6A40, 0,      0x303A,
    0x6A3E, 0,      0,      0x4025, 0,      0,      0,      0x6A3B, 0,
    0x327D, 0,      0x4377, 0x3B68, 0,      0,      0,      0x5257, 0x4E74,
    0x6A3F, 0,      0,      0,      0x6A3C, 0,      0,      0,      0x6A43,
    0,      0x5047, 0x5333, 0,      0,      0,      0,      0x343A, 0,
    0x4341, 0x5772, 0,      0,      0,      0,      0x5551, 0,      0x4A47,
    0,      0x6A45, 0,      0,      0x6A44, 0x6A47, 0x6A46, 0,      0,
    0,      0,      0,      0x5667, 0,      0x4F54, 0,      0,      0x6A4B,
    0,      0x3B4E, 0,      0,      0,      0,      0,      0,      0,
    0x3D7A, 0x494E, 0,      0,      0x6A4C, 0,      0,      0x4939, 0x4F7E,
    0x6A4A, 0x544E, 0x6A4D, 0x6A4F, 0,      0,      0x4D6D, 0,      0,
    0,      0,      0x6A49, 0,      0x6A4E, 0,      0,      0x4E6E, 0,
    0x3B5E, 0,      0x333F, 0,      0,      0,      0,      0,      0x4655,
    0x3E30, 0x4E7A, 0,      0,      0,      0x4767, 0,      0x3E27, 0x6A50,
    0,      0,      0x5647, 0,      0,      0,      0x4140, 0,      0,
    0,      0x545D, 0,      0x6A51, 0,      0,      0x4F3E, 0,      0,
    0,      0,      0x6A52, 0,      0,      0,      0,      0x4A6E, 0,
    0,      0,      0,      0x452F, 0x3035, 0,      0,      0,      0,
    0,      0x6A54, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6A53, 0x745F, 0,      0,      0,      0,      0,
    0x443A, 0,      0,      0,      0,      0,      0x3129, 0,      0,
    0,      0,      0x655F, 0,      0,      0,      0,      0x6A55, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x4A6F, 0,
    0x6A56, 0x6A57, 0x4658, 0,      0,      0,      0,      0,      0,
    0,      0,      0x6A58, 0,      0,      0x6A59, 0,      0,      0,
    0,      0,      0,      0x543B, 0,      0x477A, 0x5237, 0x387C, 0,
    0,      0x6A42, 0,      0x325C, 0,      0,      0x427C, 0,      0x5478,
    0x4C66, 0x576E, 0,      0,      0,      0,      0,      0,      0,
    0x5442, 0x5350, 0x6B43, 0x4573, 0,      0x377E, 0,      0,      0x6B54,
    0,      0,      0,      0x4B37, 0x6B5E, 0,      0x404A, 0,      0,
    0,      0x4D7B, 0,      0x332F, 0,      0x465A, 0,      0,      0,
    0,      0,      0,      0x6B7C, 0,      0x443E, 0,      0x4E34, 0x4429,
    0x313E, 0x547D, 0,      0x4A75, 0,      0x566C, 0,      0,      0x4653,
    0x3664, 0,      0,      0,      0,      0x3B7A, 0,      0,      0x5060,
    0,      0,      0x4931, 0,      0x5453, 0x4828, 0,      0,      0x384B,
    0,      0x683E, 0x493C, 0,      0,      0x683B, 0,      0x406E, 0x5053,
    0x3244, 0x3465, 0,      0x683C, 0,      0,      0x5548, 0,      0,
    0,      0,      0,      0x3645, 0,      0x683D, 0x4A78, 0x385C, 0x4C75,
    0,      0,      0,      0x4034, 0,      0,      0x516E, 0x683F, 0x6842,
    0,      0,      0x3A3C, 0,      0x312D, 0x3D5C, 0,      0x6A3D, 0x6843,
    0,      0x6846, 0,      0x684B, 0,      0,      0,      0,      0x684C,
    0,      0x4B49, 0x3065, 0,      0x3C2B, 0,      0,      0x3939, 0,
    0,      0x6841, 0,      0x4D77, 0,      0x684A, 0,      0,      0,
    0,      0x4E76, 0,      0,      0,      0,      0x556D, 0,      0x4156,
    0x6844, 0,      0x4336, 0,      0x397B, 0x5626, 0x6848, 0,      0,
    0,      0x4A60, 0x5466, 0,      0x6840, 0,      0x6845, 0x6847, 0,
    0x4739, 0x3763, 0,      0x6849, 0,      0x3F5D, 0x6852, 0,      0,
    0x6857, 0,      0x6855, 0x3C5C, 0x3C4F, 0x685B, 0,      0,      0,
    0,      0,      0,      0,      0,      0x685E, 0,      0x685A, 0x317A,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x3058, 0x4433, 0x384C, 0x4662, 0x483E, 0x4861, 0,      0,
    0,      0x684F, 0x6854, 0x6856, 0,      0x3971, 0x6858, 0x5775, 0,
    0x447B, 0,      0x685C, 0,      0,      0x3269, 0,      0,      0,
    0x6851, 0,      0,      0x3C6D, 0,      0,      0x3F42, 0x684D, 0x5679,
    0,      0x4178, 0x3271, 0,      0,      0,      0,      0,      0,
    0,      0,      0x685F, 0,      0x4A41, 0x6859, 0,      0,      0,
    0,      0x5524, 0,      0x316A, 0x553B, 0x684E, 0x6850, 0x3630, 0x6853,
    0,      0x685D, 0x4038, 0,      0x4A77, 0,      0x4B28, 0,      0,
    0x465C, 0x4075, 0,      0,      0,      0,      0,      0x6869, 0,
    0,      0,      0x5023, 0,      0,      0,      0,      0,      0,
    0,      0x6872, 0x566A, 0,      0,      0,      0,      0,      0,
    0,      0x6860, 0x6861, 0,      0,      0,      0x5179, 0x3A4B, 0x3879,
    0,      0,      0x3871, 0x5454, 0x686F, 0,      0x686E, 0x686C, 0x3970,
    0x4C52, 0x6866, 0x4E26, 0x3F72, 0,      0x3038, 0x6871, 0x6870, 0,
    0x5740, 0,      0x6864, 0,      0x4D29, 0x4923, 0,      0x3B38, 0x3D5B,
    0x686A, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6862, 0x6863, 0x6865, 0x3535, 0x6867, 0x4745, 0x686B,
    0x686D, 0x3D30, 0x572E, 0,      0x6878, 0,      0,      0,      0,
    0,      0,      0,      0,      0x6875, 0,      0x4D30, 0x6876, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x413A,
    0,      0x6868, 0,      0x4337, 0x3070, 0,      0,      0,      0,
    0,      0,      0,      0,      0x6874, 0,      0,      0,      0x6877,
    0,      0,      0,      0x3923, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x4952, 0,      0,      0,
    0x434E, 0x4E60, 0x4066, 0,      0,      0,      0,      0x4B73, 0,
    0x4C5D, 0x5035, 0,      0,      0x4A61, 0,      0x6873, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x3C6C,
    0,      0x6879, 0,      0,      0,      0,      0,      0,      0x435E,
    0,      0x4665, 0,      0x3977, 0,      0,      0,      0,      0x3074,
    0,      0,      0x5758, 0,      0,      0x3C2C, 0,      0x456F, 0,
    0,      0,      0,      0,      0x4C44, 0,      0,      0x6926, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x492D, 0,      0x6922, 0x4062, 0,      0,      0,      0x3F43, 0,
    0,      0,      0x687E, 0x3957, 0,      0x687B, 0,      0,      0,
    0,      0x6924, 0,      0,      0,      0x524E, 0,      0,      0,
    0,      0,      0x6923, 0,      0x5632, 0x5735, 0,      0x6927, 0,
    0x3D37, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x687C, 0x687D, 0,      0,      0,
    0x6921, 0,      0,      0,      0,      0,      0,      0,      0,
    0x4D56, 0,      0,      0x522C, 0,      0,      0,      0x6932, 0,
    0,      0,      0,      0x6929, 0,      0,      0,      0x342A, 0,
    0x343B, 0,      0,      0x692B, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x5028, 0,      0,      0x6925, 0,      0,
    0x337E, 0,      0,      0x692C, 0x4063, 0,      0x692A, 0,      0,
    0x6939, 0,      0,      0x6938, 0,      0,      0,      0,      0x692E,
    0,      0,      0x687A, 0,      0,      0x6928, 0,      0,      0,
    0,      0,      0x3F2C, 0x6931, 0x693A, 0,      0,      0x4225, 0,
    0,      0,      0x692F, 0,      0x3845, 0,      0x692D, 0,      0x535C,
    0x6934, 0x6935, 0x6937, 0,      0,      0,      0x6947, 0,      0,
    0,      0,      0,      0,      0x4046, 0x6945, 0,      0,      0x6930,
    0,      0,      0x693B, 0x3071, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x693C, 0x5525, 0,      0,
    0x693E, 0,      0x693F, 0,      0,      0,      0x6941, 0,      0,
    0x4171, 0,      0,      0x4836, 0,      0,      0,      0x693D, 0,
    0,      0,      0,      0,      0x6942, 0,      0,      0,      0,
    0,      0,      0,      0,      0x6943, 0,      0x6933, 0,      0x6936,
    0,      0x3B31, 0,      0,      0,      0x6940, 0,      0,      0,
    0,      0,      0,      0x3C77, 0,      0,      0,      0x6944, 0x6946,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x694A, 0,      0,      0,      0,      0x694E,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x325B, 0,      0x6948, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x372E, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x694B, 0x694C, 0,      0,      0,      0,      0,      0,      0x5541,
    0,      0x4423, 0,      0,      0,      0,      0,      0,      0x6958,
    0,      0x3A61, 0,      0,      0,      0,      0x6949, 0,      0x5323,
    0,      0,      0,      0x6954, 0,      0,      0,      0,      0,
    0,      0,      0x6957, 0x6950, 0,      0,      0,      0,      0,
    0x694F, 0,      0,      0x4741, 0,      0,      0,      0,      0,
    0,      0,      0,      0x6952, 0,      0,      0,      0,      0,
    0,      0,      0x6959, 0x3348, 0,      0x6953, 0,      0,      0,
    0,      0,      0x4F70, 0,      0,      0,      0x694D, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x3377, 0,      0,      0,      0,      0,      0,      0,      0x6956,
    0,      0,      0x695A, 0,      0,      0,      0x4C34, 0,      0,
    0,      0x4F2D, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6955, 0,      0x695C, 0x695B, 0,      0,      0,
    0,      0,      0x695E, 0,      0,      0,      0,      0,      0,
    0,      0,      0x6951, 0,      0,      0,      0,      0,      0,
    0,      0,      0x695D, 0,      0x695F, 0x434A, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x4737, 0x344E, 0x3B36, 0x5040, 0x6C23,
    0,      0,      0x4537, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x537B, 0,      0,      0,      0,
    0x6C24, 0,      0x6C25, 0x465B, 0,      0,      0,      0x3F6E, 0,
    0,      0,      0,      0x6C26, 0,      0,      0x6C27, 0x502A, 0,
    0x4738, 0,      0,      0x3868, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x6C28, 0,
    0,      0,      0,      0,      0,      0,      0,      0x5639, 0x557D,
    0x344B, 0x323D, 0x4E64, 0x4667, 0,      0,      0x4D61, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x3475, 0,      0x4B40, 0x3C5F, 0,      0,
    0,      0,      0x6962, 0x6963, 0x516A, 0x6965, 0,      0x3479, 0x6964,
    0,      0x5133, 0x4A62, 0x3250, 0,      0x6968, 0,      0,      0,
    0,      0x6966, 0x6967, 0,      0,      0x5633, 0,      0,      0,
    0x6969, 0x696A, 0,      0,      0,      0,      0,      0x696B, 0,
    0,      0,      0,      0,      0,      0,      0,      0x696C, 0,
    0,      0,      0,      0,      0,      0,      0,      0x6C2F, 0x4539,
    0x364E, 0,      0x5273, 0,      0,      0,      0,      0,      0,
    0,      0x356E, 0,      0x3B59, 0x6C31, 0,      0,      0x5263, 0,
    0,      0,      0,      0,      0x4E63, 0,      0x4438, 0,      0x433F,
    0,      0,      0x363E, 0x5839, 0x3148, 0x314F, 0x3151, 0x457E, 0,
    0x3150, 0,      0x432B, 0,      0,      0,      0,      0,      0x5531,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6B24,
    0x3A41, 0,      0,      0,      0x4C3A, 0,      0,      0,      0x6B25,
    0,      0x6B27, 0,      0,      0,      0x6B28, 0,      0,      0,
    0x6B26, 0,      0,      0,      0,      0,      0,      0,      0x6B29,
    0x6B2B, 0x6B2A, 0,      0,      0,      0,      0,      0x6B2C, 0,
    0x4A4F, 0x5835, 0x4371, 0,      0x4325, 0x4678, 0x6B2D, 0x444A, 0,
    0x6B2E, 0x6B2F, 0x6B30, 0x3755, 0,      0,      0,      0x377A, 0,
    0x6B31, 0x4762, 0,      0x6B33, 0,      0x3A24, 0x5175, 0x3031, 0x6B32,
    0x6B34, 0,      0,      0,      0x352A, 0x4248, 0x4768, 0,      0x6B35,
    0,      0x4B2E, 0x635F, 0,      0,      0x5340, 0,      0,      0,
    0,      0x595B, 0,      0,      0x4D21, 0x562D, 0x4773, 0,      0,
    0,      0x5960, 0x3B63, 0,      0x3A3A, 0x6362, 0,      0,      0,
    0,      0,      0x4F2B, 0,      0,      0,      0x6360, 0x4947, 0,
    0x3A39, 0,      0,      0,      0x5134, 0x6361, 0x486A, 0x392F, 0x3D2D,
    0x3358, 0x4E5B, 0,      0,      0x4C40, 0,      0,      0,      0x6368,
    0x6369, 0x4D74, 0,      0,      0,      0,      0,      0x4C2D, 0,
    0x3C33, 0,      0x636A, 0,      0x636B, 0,      0,      0x505A, 0,
    0,      0,      0x467B, 0x375A, 0,      0,      0x475F, 0x524A, 0x4E56,
    0,      0x6364, 0x636C, 0,      0x4972, 0x3341, 0,      0,      0x6367,
    0,      0,      0x4663, 0x6365, 0,      0,      0x6D33, 0x6366, 0,
    0,      0,      0,      0x4933, 0,      0x4566, 0,      0,      0,
    0x3935, 0,      0x433B, 0,      0x6363, 0x453D, 0x4124, 0x4259, 0x3257,
    0,      0x636D, 0x3B26, 0x442D, 0,      0x6370, 0x3E5A, 0,      0,
    0x637B, 0x6375, 0x3A53, 0,      0,      0,      0,      0x3750, 0x534D,
    0,      0x564E, 0x5553, 0x3941, 0x5534, 0x5158, 0,      0,      0,
    0,      0x5039, 0x4776, 0,      0,      0,      0x482A, 0x3234, 0,
    0x435A, 0,      0,      0,      0x636E, 0,      0,      0x637C, 0x636F,
    0x3728, 0x6377, 0x6374, 0,      0,      0,      0x373A, 0,      0,
    0x4522, 0,      0x6376, 0x455D, 0x3228, 0x467C, 0,      0x4460, 0,
    0,      0x5722, 0,      0x4061, 0x6379, 0,      0,      0x637A, 0x637D,
    0x4C29, 0x6373, 0,      0x533E, 0,      0x3143, 0x6D34, 0x6371, 0x6372,
    0,      0x6378, 0x503A, 0x4643, 0x5473, 0x637E, 0,      0,      0x3D60,
    0,      0,      0x6427, 0,      0,      0x6426, 0,      0,      0,
    0x5173, 0x6423, 0,      0x6429, 0,      0,      0,      0x4877, 0,
    0,      0,      0,      0x4F34, 0,      0x6428, 0x642E, 0x4265, 0,
    0,      0x3634, 0,      0,      0,      0,      0,      0,      0x3D72,
    0,      0x6422, 0,      0,      0x3A69, 0x642A, 0,      0,      0x642C,
    0,      0,      0x367D, 0x565E, 0x6432, 0,      0x642D, 0,      0,
    0,      0x6421, 0,      0x3B6E, 0x4D5D, 0x4722, 0x4549, 0,      0,
    0x4177, 0,      0x6424, 0,      0x4733, 0x3D2C, 0x3D3D, 0x6425, 0,
    0x5747, 0x3262, 0,      0x642B, 0x3C43, 0x642F, 0,      0x3B6B, 0x6430,
    0x4528, 0x6431, 0,      0,      0,      0,      0x5563, 0x3F23, 0,
    0x643A, 0,      0x6437, 0,      0x643B, 0,      0,      0x643D, 0,
    0,      0x4656, 0,      0,      0x3A46, 0x404B, 0,      0,      0,
    0x3821, 0x6434, 0,      0,      0,      0,      0x5421, 0,      0,
    0x3A23, 0x3D7E, 0,      0,      0,      0x643C, 0,      0,      0,
    0,      0,      0x4D3F, 0,      0,      0x4479, 0,      0,      0x4F7B,
    0x4966, 0,      0,      0x533F, 0,      0x4F51, 0,      0,      0x6433,
    0,      0x6438, 0x6439, 0x4C69, 0,      0,      0,      0,      0,
    0x4C4E, 0,      0x4054, 0x6435, 0x4130, 0x6436, 0x4E50, 0,      0x3B41,
    0x3553, 0,      0x4873, 0x3D27, 0x5547, 0x492C, 0x3822, 0x644A, 0,
    0,      0x644C, 0x5144, 0,      0,      0x523A, 0,      0,      0x3A2D,
    0,      0,      0x3A54, 0,      0,      0,      0,      0,      0,
    0x6443, 0x356D, 0,      0,      0,      0x574D, 0x6440, 0x4F7D, 0x643F,
    0,      0,      0,      0x415C, 0x4C4A, 0,      0,      0,      0,
    0x4A67, 0,      0,      0,      0,      0x4457, 0,      0x4C54, 0x6448,
    0,      0,      0,      0x6447, 0x6441, 0,      0x6444, 0x352D, 0,
    0,      0x5359, 0,      0x6446, 0,      0,      0,      0,      0x5279,
    0x3463, 0,      0x3B34, 0,      0,      0x496E, 0,      0x343E, 0,
    0,      0,      0x3B6C, 0,      0x514D, 0,      0x4C6D, 0x6D35, 0,
    0,      0,      0,      0,      0,      0,      0,      0x4765, 0,
    0,      0,      0,      0x5428, 0,      0x644B, 0x5755, 0x6442, 0,
    0x3D25, 0x6445, 0,      0,      0x5366, 0,      0x6449, 0x4978, 0,
    0,      0x643E, 0,      0,      0x5365, 0,      0,      0x477E, 0x3649,
    0,      0x547C, 0x3233, 0x6457, 0,      0,      0,      0x4E42, 0,
    0x644D, 0,      0x4E3C, 0,      0x385B, 0,      0,      0x6456, 0,
    0x3F4A, 0,      0,      0,      0x534E, 0,      0x436C, 0,      0,
    0,      0,      0,      0,      0,      0,      0x4548, 0x6458, 0,
    0,      0,      0,      0,      0,      0,      0,      0x4D44, 0x644F,
    0,      0,      0,      0,      0x6454, 0x6455, 0,      0x3A7E, 0,
    0x4F66, 0,      0,      0x553F, 0,      0,      0,      0x6452, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x6450, 0,      0,      0x644E, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x4D65, 0x4A2A, 0,      0,      0,      0x4023, 0,
    0x3D26, 0x6453, 0,      0,      0x3848, 0,      0,      0,      0,
    0,      0x6467, 0x5434, 0,      0,      0,      0,      0,      0,
    0,      0x645B, 0,      0,      0,      0x416F, 0,      0,      0x6469,
    0,      0,      0x5267, 0,      0,      0x645F, 0,      0x6460, 0,
    0,      0x4F2A, 0,      0,      0,      0,      0x4B5D, 0,      0x645A,
    0x6451, 0,      0x6465, 0,      0x485C, 0x6463, 0,      0,      0x4467,
    0x6462, 0,      0x6461, 0,      0,      0,      0x337C, 0x6468, 0,
    0,      0,      0,      0x3561, 0,      0,      0,      0x574C, 0,
    0,      0,      0x6466, 0,      0x3B2C, 0,      0x5752, 0x4C4F, 0x6B78,
    0,      0x6464, 0,      0,      0x3976, 0,      0,      0,      0x564D,
    0x6459, 0x645C, 0x427A, 0x645E, 0,      0x424B, 0x4044, 0x4250, 0,
    0x3175, 0x4C32, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x354E, 0,      0,      0,      0,      0x646F,
    0,      0,      0,      0,      0,      0,      0,      0,      0x462F,
    0,      0,      0,      0x4661, 0,      0,      0x6475, 0,      0,
    0,      0,      0,      0x4229, 0,      0,      0,      0x406C, 0x515D,
    0x646E, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x442E, 0,      0,      0,      0x646D, 0,      0,
    0,      0,      0x6476, 0x6474, 0x427E, 0,      0x645D, 0,      0x6470,
    0,      0x4A7E, 0,      0x5544, 0,      0,      0x6471, 0,      0,
    0,      0,      0,      0,      0,      0x517A, 0,      0,      0,
    0,      0,      0,      0,      0x646B, 0x646C, 0,      0,      0,
    0x6472, 0,      0x4E2B, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x454B, 0,      0,      0,      0x4731,
    0,      0x423A, 0,      0,      0,      0x646A, 0,      0,      0,
    0x414A, 0,      0,      0,      0,      0,      0,      0x4C36, 0x3331,
    0,      0,      0,      0x647B, 0,      0x6473, 0,      0,      0,
    0x647A, 0,      0x647D, 0,      0x647C, 0,      0,      0,      0,
    0,      0,      0,      0x334E, 0,      0,      0,      0x333A, 0x6477,
    0,      0,      0x6479, 0x6478, 0x456C, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x403D, 0,      0,      0,      0,      0x5468, 0,      0,      0,
    0,      0,      0x6522, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x3044, 0,      0,      0x6524,
    0,      0,      0x6523, 0,      0,      0,      0,      0,      0,
    0x3C24, 0,      0x6525, 0,      0,      0,      0,      0,      0,
    0x6521, 0,      0,      0,      0,      0,      0,      0,      0x647E,
    0x3174, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x6528, 0,      0x6529, 0x6526, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x6527, 0x652A, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x4659, 0,      0,      0,      0,      0,      0,      0,      0,
    0x652B, 0x652D, 0,      0,      0,      0,      0,      0,      0,
    0x652C, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x652F, 0,      0,      0,      0x652E, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x3960, 0,      0,      0x6530,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x6531, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x3B70, 0x6C61, 0x4370, 0,      0x3546, 0x3B52, 0,      0,      0,
    0,      0x4169, 0x546E, 0,      0x3E44, 0,      0,      0,      0x5746,
    0,      0x5456, 0x3253, 0x6C3E, 0,      0,      0,      0,      0x6A41,
    0,      0,      0,      0x422F, 0x3436, 0,      0,      0,      0x5157,
    0,      0,      0,      0x3334, 0,      0x4832, 0x3F3B, 0x6C40, 0,
    0,      0x564B, 0,      0,      0x6C3F, 0x6C41, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x6C45, 0x3E66, 0x4C3F, 0x455A, 0x3E3C, 0,      0x6C46, 0,
    0x317E, 0,      0,      0,      0x6C44, 0x5528, 0x3563, 0,      0x6C42,
    0x4136, 0x3363, 0,      0,      0x6C43, 0x4B38, 0x4043, 0x4C7E, 0,
    0,      0,      0,      0x4152, 0,      0x6C48, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x3A66, 0x4053, 0,      0x5672, 0,      0,      0,      0x514C,
    0,      0,      0,      0,      0x3F3E, 0,      0x3733, 0x4955, 0x6C47,
    0x3B62, 0,      0x4C4C, 0x3D7D, 0x4848, 0,      0x4F29, 0,      0,
    0,      0,      0,      0,      0,      0x4D69, 0,      0x456B, 0,
    0,      0,      0x3769, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x5149, 0x3A38, 0,      0,
    0,      0,      0,      0x6C49, 0,      0,      0x6C4A, 0,      0x3B40,
    0x6C4B, 0,      0x6C62, 0x313A, 0x3759, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x3D39, 0,
    0,      0,      0,      0,      0,      0,      0,      0x6C4C, 0x5166,
    0x6C4D, 0,      0,      0,      0,      0x483B, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6C51, 0,      0,      0,      0,      0x6C53, 0,
    0x3B4D, 0,      0x3C65, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x6C4F, 0,
    0x4937, 0,      0,      0,      0,      0,      0x433A, 0,      0x6C63,
    0x5555, 0x6C50, 0,      0,      0,      0,      0,      0x5673, 0,
    0,      0,      0x6C52, 0x6C4E, 0,      0,      0,      0,      0x6C54,
    0,      0x6C55, 0,      0,      0x493F, 0,      0,      0,      0,
    0,      0,      0x4F28, 0,      0,      0,      0,      0,      0x505C,
    0,      0,      0,      0,      0x512C, 0,      0,      0,      0,
    0x485B, 0,      0,      0,      0x6C56, 0x4E75, 0,      0,      0,
    0,      0,      0x4A6C, 0x6C5A, 0,      0,      0,      0,      0,
    0,      0,      0x6C59, 0,      0,      0,      0x303E, 0,      0,
    0,      0,      0,      0,      0x6C57, 0,      0x6C58, 0,      0,
    0,      0x6C64, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x483C, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x4147, 0,      0,      0,      0,
    0,      0x6C5C, 0x5160, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x6C5B, 0,      0,      0,      0,
    0x546F, 0,      0x6C5D, 0,      0,      0,      0,      0,      0,
    0x5B46, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6C5E, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x312C, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6C5F, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x6C60, 0,      0x5726, 0,      0x4540,
    0,      0,      0,      0x6B3C, 0x302E, 0,      0,      0,      0x3E74,
    0x3838, 0x522F, 0x3056, 0x3579, 0,      0x5833, 0,      0x4B2C, 0,
    0x635D, 0,      0,      0,      0,      0,      0,      0,      0x462C,
    0x3066, 0,      0,      0,      0x4546, 0x6B39, 0,      0,      0,
    0,      0x6B3A, 0,      0,      0,      0x6B3B, 0,      0,      0x5140,
    0,      0x4523, 0,      0x6A72, 0,      0x4432, 0,      0x4435, 0x404E,
    0,      0,      0,      0x6A73, 0x4441, 0,      0x4E6F, 0,      0,
    0,      0,      0x6A70, 0x6A74, 0,      0,      0x497C, 0,      0,
    0x4723, 0,      0,      0,      0x4C58, 0x4E7E, 0,      0,      0,
    0x6A75, 0x6A76, 0x4F2C, 0x4067, 0,      0,      0x6A77, 0,      0,
    0,      0,      0,      0x363F, 0x6A78, 0,      0x6A79, 0,      0x6A7A,
    0,      0,      0x6A7B, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x6A71, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x482E, 0x616B, 0,      0x3738, 0x616C, 0,      0,      0,
    0x616D, 0,      0x5734, 0x616E, 0x616F, 0x534C, 0,      0,      0,
    0,      0,      0,      0,      0x6171, 0x3F71, 0x6170, 0x3552, 0,
    0,      0,      0x3137, 0,      0,      0,      0,      0x6173, 0x6172,
    0,      0x3A7C, 0,      0x6174, 0,      0,      0,      0,      0x3937,
    0,      0x3E51, 0,      0,      0,      0,      0x447C, 0,      0x3A5D,
    0x3D46, 0,      0,      0,      0,      0,      0,      0x6175, 0x6177,
    0,      0,      0x3640, 0x4F41, 0x4A28, 0x6176, 0x5578, 0x537C, 0x6178,
    0x617C, 0x6179, 0,      0,      0x617A, 0x406A, 0,      0x617E, 0x6221,
    0x4047, 0,      0,      0,      0,      0x617B, 0,      0x617D, 0,
    0,      0,      0,      0,      0,      0x6225, 0,      0,      0,
    0x4154, 0,      0,      0,      0,      0x6223, 0,      0x6228, 0x327E,
    0x6222, 0,      0,      0,      0x434D, 0x3242, 0x6227, 0x6226, 0,
    0,      0x6224, 0x6229, 0,      0,      0x622B, 0,      0,      0,
    0x5049, 0x566D, 0x4328, 0x622C, 0,      0x4F57, 0,      0,      0x622E,
    0,      0,      0x3A6F, 0,      0,      0x6960, 0x622D, 0x622A, 0,
    0,      0,      0,      0x3B2B, 0x5433, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x6230, 0,      0,      0x622F, 0,      0x6961, 0,      0,      0,
    0,      0x6231, 0,      0,      0,      0,      0,      0,      0,
    0,      0x6232, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x6233, 0x4C21, 0,      0x6234, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x6235, 0,      0,      0,      0,
    0,      0x507E, 0,      0,      0x424A, 0,      0x5371, 0,      0x4D75,
    0,      0,      0x6760, 0,      0,      0x6761, 0,      0,      0,
    0,      0x3E41, 0,      0,      0,      0,      0x426A, 0,      0,
    0,      0x6764, 0,      0,      0x6763, 0,      0,      0,      0,
    0,      0,      0x4D66, 0,      0x4335, 0,      0,      0x6762, 0x3B37,
    0x4F56, 0,      0x4161, 0x6769, 0,      0,      0,      0x6768, 0,
    0,      0x6774, 0x3223, 0,      0,      0,      0,      0x676A, 0,
    0x6766, 0,      0,      0,      0,      0,      0x676C, 0x676B, 0x493A,
    0,      0,      0x5564, 0,      0x6765, 0x3729, 0x6767, 0,      0,
    0,      0,      0,      0,      0,      0x676E, 0,      0,      0,
    0,      0x6773, 0,      0x5669, 0,      0,      0,      0,      0x676D,
    0,      0x6772, 0,      0x6771, 0,      0,      0,      0x3060, 0,
    0,      0,      0,      0x6775, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x4772, 0,      0x4045, 0x406D, 0,      0,      0x4170,
    0x6770, 0,      0,      0,      0,      0x6776, 0x4B76, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x6822, 0x6821,
    0,      0,      0,      0,      0,      0,      0x5741, 0,      0,
    0x677A, 0x6779, 0,      0x677B, 0,      0x6777, 0,      0x677E, 0,
    0x677D, 0,      0x677C, 0,      0,      0x4155, 0x4759, 0x457D, 0x4543,
    0,      0,      0,      0,      0,      0x476D, 0,      0,      0,
    0,      0x6823, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x6826, 0,      0x6825, 0,      0x6827, 0x3A77,
    0x6778, 0x6824, 0,      0x4870, 0x492A, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x6829, 0,      0,      0x3965, 0,      0,      0,      0,      0,
    0x517E, 0x6828, 0,      0,      0,      0,      0,      0,      0x682A,
    0,      0x682D, 0x682E, 0,      0x4127, 0,      0,      0,      0x682F,
    0,      0,      0,      0x6830, 0,      0,      0x682C, 0,      0x6834,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x682B, 0,      0x6831, 0,      0,      0,      0,
    0,      0,      0,      0,      0x6835, 0x6832, 0x6833, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x6837, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6836, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x394F, 0,      0x702C, 0,      0x702D, 0,
    0x4630, 0x306A, 0x483F, 0,      0x4D5F, 0,      0,      0,      0,
    0,      0,      0,      0x4E4D, 0x6A31, 0,      0,      0,      0,
    0x6A32, 0,      0x463F, 0x3449, 0,      0,      0,      0,      0,
    0,      0,      0x6A33, 0,      0,      0,      0,      0x5567, 0,
    0,      0,      0,      0,      0,      0,      0,      0x5D79, 0,
    0x6A34, 0,      0x6A35, 0,      0x6A36, 0,      0,      0,      0,
    0x384A, 0x5F30, 0x4975, 0,      0x4C70, 0,      0,      0x497A, 0,
    0,      0,      0,      0,      0x497B, 0,      0,      0x5343, 0x4B26,
    0,      0x3826, 0x702E, 0x3142, 0,      0x6538, 0x4C6F, 0x5349, 0x3C57,
    0x496A, 0,      0x3567, 0,      0x4450, 0x3569, 0,      0x6E2E, 0x3B2D,
    0,      0,      0x675E, 0,      0x6E2F, 0,      0,      0,      0,
    0x3329, 0,      0,      0x6E32, 0,      0,      0x6E31, 0x3D67, 0,
    0x6E30, 0x4E37, 0,      0,      0,      0,      0x454F, 0,      0,
    0,      0,      0x4174, 0x5B4E, 0x6E33, 0x5073, 0,      0,      0,
    0,      0,      0,      0,      0,      0x4254, 0x4668, 0,      0,
    0,      0x372C, 0,      0,      0,      0,      0,      0,      0,
    0x6E34, 0,      0x336B, 0,      0,      0,      0x3B7B, 0x6E35, 0,
    0,      0,      0,      0,      0x675C, 0,      0,      0,      0x6E36,
    0,      0,      0x3D2E, 0,      0,      0,      0,      0x7162, 0,
    0,      0,      0x4A68, 0,      0x5249, 0x705A, 0,      0x705B, 0,
    0x705C, 0x4146, 0,      0x386D, 0x3E4E, 0,      0,      0x705E, 0,
    0x4531, 0x705D, 0x5171, 0,      0x7060, 0x304C, 0x3D6A, 0,      0,
    0,      0,      0,      0x525F, 0x705F, 0,      0x342F, 0x3768, 0x7066,
    0x7065, 0x4623, 0x7061, 0x7062, 0x3443, 0,      0,      0x7063, 0x556E,
    0,      0,      0x4C5B, 0x3E52, 0x3C32, 0,      0,      0,      0x7068,
    0x7067, 0x7064, 0x3221, 0,      0x5622, 0x5338, 0x3E37, 0x482C, 0,
    0,      0x706A, 0,      0,      0,      0,      0x5177, 0,      0x564C,
    0x3A5B, 0x7069, 0,      0x363B, 0,      0,      0x4D34, 0,      0,
    0x4626, 0,      0,      0,      0x4121, 0x706B, 0x706E, 0,      0x706D,
    0x7070, 0x706C, 0,      0x3B3E, 0x706F, 0,      0,      0,      0,
    0x4C35, 0x7072, 0,      0,      0x3355, 0,      0,      0,      0,
    0x3154, 0,      0,      0x7073, 0,      0,      0x7074, 0x7076, 0x3461,
    0,      0x7071, 0,      0x7077, 0,      0,      0,      0,      0x707A,
    0,      0x7078, 0,      0,      0,      0x7075, 0,      0,      0,
    0,      0x707D, 0,      0x7079, 0x707C, 0x707E, 0,      0x7121, 0,
    0,      0,      0x4E41, 0x7124, 0,      0x7123, 0,      0x4176, 0x707B,
    0x4A5D, 0,      0,      0x3471, 0x3171, 0x4C31, 0,      0x7126, 0,
    0,      0x7127, 0,      0,      0x712C, 0x554E, 0x7129, 0,      0,
    0x4833, 0,      0,      0,      0x7122, 0,      0x712B, 0x7128, 0x7125,
    0,      0,      0x712A, 0,      0,      0,      0,      0,      0,
    0,      0,      0x3029, 0x712D, 0,      0,      0,      0,      0,
    0,      0x712F, 0,      0x7131, 0,      0,      0,      0,      0,
    0x7130, 0,      0x712E, 0,      0,      0,      0,      0x5122, 0,
    0,      0,      0,      0,      0,      0,      0x7132, 0,      0,
    0,      0x7133, 0,      0,      0,      0,      0,      0,      0,
    0,      0x396F, 0,      0,      0x3547, 0,      0x3057, 0x3059, 0,
    0,      0,      0x546D, 0,      0x3544, 0,      0x3D54, 0x3B4A, 0x7027,
    0,      0,      0x385E, 0,      0,      0x7028, 0,      0,      0x3028,
    0,      0x7029, 0,      0,      0x4D6E, 0,      0,      0x702A, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x702B, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x4624, 0,      0,      0x5665, 0x7164, 0,      0x7165, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x4373, 0,      0,      0x535B, 0,      0,      0x5651, 0x4568, 0,
    0x532F, 0,      0x5266, 0,      0,      0x6E41, 0x303B, 0x5535, 0x514E,
    0x3C60, 0x3A50, 0,      0x3F78, 0,      0x3847, 0x3541, 0x454C, 0,
    0,      0x4A22, 0,      0,      0,      0x434B, 0,      0,      0,
    0,      0,      0x6E42, 0,      0,      0,      0,      0,      0,
    0,      0,      0x443F, 0x3622, 0,      0x6D6C, 0x4324, 0,      0x5631,
    0,      0,      0,      0x4F60, 0x6D6F, 0,      0,      0x454E, 0,
    0x365C, 0,      0,      0x4A21, 0,      0,      0x6D6D, 0,      0,
    0x6D70, 0x6D71, 0x433C, 0,      0x3F34, 0,      0x6D6E, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x6D74, 0x6D72, 0,      0,      0,      0,      0x5566, 0x435F, 0,
    0x6D73, 0,      0,      0,      0x6D76, 0,      0x5523, 0x5123, 0,
    0,      0,      0x6D75, 0,      0x4350, 0,      0,      0,      0,
    0,      0x6D77, 0x3F74, 0x3E6C, 0x6D78, 0,      0x4C77, 0,      0x515B,
    0,      0,      0,      0x5745, 0x5576, 0,      0x6D7C, 0,      0,
    0,      0x6D7B, 0,      0,      0,      0,      0,      0,      0,
    0,      0x6D79, 0x6D7A, 0,      0,      0,      0,      0,      0,
    0,      0,      0x6D7D, 0x3E26, 0,      0,      0,      0,      0,
    0x4B2F, 0x6E21, 0x363D, 0,      0x6E22, 0x4440, 0,      0x6D7E, 0,
    0,      0x3D5E, 0x3247, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x3643, 0,      0,
    0,      0x6E25, 0x583A, 0x6E23, 0x6E26, 0,      0,      0,      0x4369,
    0x3372, 0,      0,      0,      0,      0,      0,      0x6E27, 0x6E24,
    0x4F39, 0,      0,      0x6E28, 0x4277, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6E29,
    0x6E2A, 0,      0x5E2B, 0,      0,      0x4633, 0,      0x4746, 0,
    0x5675, 0x3549, 0,      0x4B32, 0,      0,      0,      0x6E2B, 0,
    0,      0x4D2B, 0,      0x6E2C, 0,      0,      0,      0,      0,
    0x5530, 0,      0x6E2D, 0,      0x7644, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x5B47, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x3423, 0,      0,      0,      0x432C, 0x7166, 0,      0,
    0,      0,      0,      0x4A38, 0x5253, 0,      0x562A, 0,      0x6F72,
    0,      0x3E58, 0,      0x3D43, 0x6F73, 0x364C, 0x302B, 0,      0,
    0,      0,      0x4A2F, 0,      0,      0x6D36, 0,      0x6D37, 0,
    0,      0,      0,      0x4E79, 0x372F, 0x3F73, 0x6D38, 0x426B, 0x4930,
    0,      0,      0,      0,      0,      0,      0x6D39, 0,      0,
    0x4676, 0x3F33, 0,      0,      0,      0x6D3C, 0x4578, 0,      0x5150,
    0,      0x5729, 0x6D3A, 0x6D3B, 0,      0x5162, 0,      0x6D3F, 0x6D40,
    0,      0x6D44, 0,      0,      0,      0x6D48, 0,      0x6D46, 0x6D4E,
    0x5568, 0,      0x6D49, 0,      0,      0x6D47, 0x6D3E, 0,      0,
    0x4569, 0,      0,      0,      0x4646, 0,      0,      0x4969, 0x5452,
    0x6D41, 0x6D42, 0x6D43, 0x6D45, 0,      0x4079, 0,      0x3421, 0,
    0,      0,      0,      0x3968, 0,      0x6D50, 0,      0,      0,
    0,      0x6D51, 0,      0x6D4A, 0,      0x6D4F, 0,      0x4E78, 0,
    0,      0x4B36, 0x6D4C, 0x6D4D, 0,      0,      0,      0,      0,
    0x4F75, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x6D52, 0x4172, 0x5332, 0x6D4B, 0x4837,
    0,      0,      0,      0,      0,      0,      0,      0,      0x3C6F,
    0,      0,      0,      0,      0x4570, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x6D56, 0,      0x356F,
    0,      0,      0x4235, 0x302D, 0x4B69, 0,      0,      0x312E, 0,
    0x6D54, 0,      0,      0,      0x4D6B, 0x3562, 0,      0x6D55, 0x6D53,
    0x6D57, 0,      0,      0x357A, 0,      0x6D58, 0,      0x6D59, 0,
    0x6D5C, 0,      0x314C, 0,      0,      0,      0,      0,      0,
    0,      0,      0x4576, 0x3C6E, 0x6D5A, 0x4C3C, 0x326A, 0,      0,
    0,      0,      0x6D5B, 0,      0,      0,      0,      0x446B, 0,
    0,      0x3445, 0,      0,      0,      0x3075, 0,      0,      0,
    0x6D5F, 0x405A, 0x3468, 0,      0,      0,      0,      0x454D, 0,
    0,      0,      0x6D5D, 0x3F44, 0,      0,      0,      0x6D5E, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x4425, 0,      0,      0,      0x6D60,
    0,      0,      0,      0,      0,      0x6D61, 0,      0x6D63, 0,
    0,      0x4157, 0,      0,      0x3B47, 0,      0,      0,      0,
    0,      0,      0x3D38, 0,      0,      0,      0x6D62, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6D64, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x6D66, 0,      0,      0,      0,
    0,      0x6D65, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6D67,
    0,      0,      0,      0,      0,      0x4A3E, 0x6C6A, 0x4071, 0,
    0x4967, 0,      0x6C6B, 0x466E, 0,      0,      0,      0,      0x6C6C,
    0,      0x466D, 0x6C6D, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x6C70, 0,      0,      0x5766, 0x6C73, 0,
    0,      0x6C71, 0x6C6E, 0x6C6F, 0x5723, 0x4971, 0x4B6E, 0x6C74, 0,
    0x6C72, 0,      0,      0x4F69, 0,      0x6C76, 0x4631, 0,      0,
    0,      0,      0x3C40, 0,      0x6C75, 0,      0,      0,      0,
    0,      0,      0,      0x353B, 0x3B76, 0,      0x6C77, 0,      0,
    0,      0,      0,      0x5977, 0x3D7B, 0,      0,      0x423B, 0x6C78,
    0,      0,      0,      0,      0x6C79, 0,      0,      0,      0,
    0x3823, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6C7A, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x6C7B, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x6C7C, 0,      0,      0,      0,      0,      0x536D, 0x582E, 0x406B,
    0,      0x475D, 0x3A4C, 0,      0x5063, 0x4B3D, 0,      0x4D3A, 0,
    0,      0x3851, 0,      0,      0x317C, 0,      0x476F, 0,      0x5656,
    0,      0,      0,      0x3F46, 0x436B, 0,      0,      0x6F75, 0,
    0,      0x4358, 0,      0,      0,      0,      0,      0,      0x5762,
    0,      0,      0,      0x6F77, 0x3353, 0,      0x4758, 0x516D, 0,
    0x5648, 0,      0x6F78, 0,      0x6F76, 0,      0x3B7D, 0x3346, 0,
    0,      0,      0,      0,      0,      0,      0x3D55, 0,      0,
    0x5246, 0,      0x3B60, 0,      0,      0x4F21, 0,      0x6F7C, 0x6F7B,
    0,      0,      0x6F79, 0,      0,      0,      0,      0x334C, 0,
    0x4954, 0x4B30, 0,      0,      0,      0,      0,      0x6F7E, 0,
    0,      0x305E, 0,      0,      0x5649, 0,      0,      0,      0x6F7D,
    0,      0x336D, 0,      0,      0x7655, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x4E48, 0,      0,      0,      0x7022, 0,      0x7021,
    0,      0x353E, 0x3C5A, 0x3B7C, 0,      0x3865, 0,      0,      0,
    0,      0,      0,      0x4442, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x7023, 0,      0,      0,
    0,      0,      0x4B6B, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x7026, 0,      0,      0,      0x5128, 0,      0x3E3F, 0x476E, 0x7136,
    0x7137, 0x3F55, 0,      0,      0,      0,      0x3429, 0x7138, 0x4D3B,
    0,      0x4754, 0x552D, 0,      0x7139, 0,      0x713A, 0,      0,
    0,      0,      0x474F, 0,      0,      0,      0x5224, 0x564F, 0,
    0,      0x713B, 0x3D51, 0x3430, 0x3E3D, 0,      0,      0,      0x345C,
    0x4E51, 0,      0x3F5F, 0x713D, 0,      0,      0,      0,      0x3F7A,
    0x713C, 0,      0x713F, 0,      0,      0,      0x713E, 0x7140, 0,
    0,      0,      0,      0,      0x7141, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x417E, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x4122, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x4A7A, 0,      0,      0x553E, 0,      0,
    0,      0,      0x3E3A, 0x3E39, 0x5542, 0,      0,      0x3F22, 0,
    0x4D2F, 0x7135, 0,      0,      0,      0,      0,      0,      0x3D5F,
    0,      0x364B, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x5671, 0x7343, 0,      0,      0x7344, 0,      0x384D,
    0,      0,      0,      0x7346, 0x7347, 0,      0x304A, 0,      0x7345,
    0,      0x7349, 0x4B71, 0,      0,      0,      0x734B, 0,      0x5026,
    0,      0,      0x314A, 0x7348, 0,      0,      0,      0x734F, 0,
    0x3551, 0,      0,      0x7357, 0,      0x7352, 0,      0,      0,
    0x7354, 0x7353, 0x377B, 0,      0x313F, 0,      0x734E, 0x734A, 0x355A,
    0,      0x7350, 0,      0,      0x7351, 0,      0x7355, 0,      0,
    0,      0,      0x734D, 0,      0x3C63, 0,      0x417D, 0,      0x7356,
    0,      0,      0,      0,      0,      0,      0x735A, 0,      0x734C,
    0,      0x3548, 0,      0x3D6E, 0x735C, 0,      0,      0x3724, 0x3F70,
    0x567E, 0x4D32, 0,      0x3470, 0,      0x325F, 0,      0x7358, 0,
    0x7359, 0x4938, 0,      0x735D, 0,      0,      0x735E, 0,      0x7361,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x735F, 0,      0,      0x7363, 0x7362, 0,      0,
    0x735B, 0,      0x3F6A, 0,      0x336F, 0,      0x7360, 0,      0,
    0x4729, 0,      0x3C72, 0,      0,      0,      0,      0x736B, 0,
    0,      0,      0,      0,      0,      0,      0x393F, 0,      0,
    0x7364, 0,      0,      0,      0x322D, 0x3B7E, 0,      0x4B63, 0,
    0,      0,      0,      0x736D, 0x7369, 0,      0,      0,      0x395C,
    0x736E, 0,      0,      0,      0x7365, 0x7366, 0x736A, 0x4261, 0x736C,
    0x736F, 0x7368, 0x3C7D, 0,      0,      0,      0x4F64, 0,      0,
    0x7370, 0,      0,      0,      0x7367, 0,      0,      0,      0,
    0,      0,      0,      0,      0x7372, 0,      0,      0,      0,
    0x572D, 0x462A, 0,      0,      0,      0,      0x7373, 0,      0,
    0,      0,      0x7371, 0,      0x4228, 0,      0,      0,      0,
    0,      0x385D, 0x7375, 0,      0,      0x7374, 0,      0,      0,
    0x345B, 0,      0,      0,      0x7376, 0x7377, 0,      0,      0,
    0x7378, 0,      0,      0,      0x403A, 0,      0,      0x4069, 0,
    0,      0,      0,      0,      0x4571, 0,      0,      0,      0,
    0x737B, 0,      0x737A, 0,      0,      0,      0,      0,      0,
    0,      0,      0x3458, 0,      0,      0,      0x737E, 0x7379, 0,
    0,      0x737C, 0,      0,      0,      0,      0,      0,      0x737D,
    0,      0,      0,      0,      0,      0,      0,      0,      0x7421,
    0,      0,      0,      0,      0,      0,      0x7423, 0x3B49, 0,
    0,      0x7422, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x7424, 0,      0,
    0,      0,      0,      0,      0x323E, 0x7426, 0x7425, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x3C2E, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x4357, 0x5961, 0,      0,      0,      0,      0,
    0,      0x4060, 0x744C, 0x5751, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x375B, 0,      0,
    0,      0,      0,      0,      0,      0x744E, 0x4123, 0,      0,
    0x4649, 0,      0x3456, 0x5533, 0,      0,      0,      0x7450, 0x744F,
    0x7451, 0x4B5A, 0,      0,      0x7452, 0,      0x5441, 0x5660, 0,
    0,      0,      0,      0x3760, 0,      0,      0,      0x4138, 0,
    0,      0x413B, 0x7453, 0x3E2C, 0,      0,      0,      0,      0,
    0x3462, 0,      0,      0x7454, 0x7455, 0x3E2B, 0,      0,      0x7456,
    0,      0,      0,      0x745B, 0,      0x7457, 0x745A, 0,      0x3A7D,
    0,      0x7458, 0x7459, 0,      0,      0,      0,      0,      0,
    0,      0x3862, 0x4C47, 0x745C, 0,      0x325A, 0,      0,      0x4353,
    0,      0,      0x5463, 0x3F37, 0,      0,      0,      0,      0,
    0,      0,      0x745D, 0,      0,      0,      0,      0,      0,
    0x4534, 0,      0,      0,      0,      0,      0,      0,      0,
    0x7469, 0,      0,      0x4F35, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x4E49, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x4B58, 0,      0x4B77, 0,      0,
    0,      0,      0x3D74, 0,      0,      0,      0x574F, 0,      0,
    0,      0x405B, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x5075, 0,      0,      0,      0,      0,      0,      0,
    0,      0x746A, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x746B, 0,      0,      0,      0,      0,
    0,      0,      0x746C, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x7763,
    0,      0,      0,      0,      0,      0x3731, 0,      0,      0,
    0,      0,      0x746D, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x576B, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x746E, 0,      0,      0,
    0x6679, 0x3E40, 0x667A, 0x3A6C, 0x667B, 0x4F4B, 0x667C, 0x543C, 0x3C36,
    0x667D, 0x667E, 0x3C4D, 0x4852, 0x4E33, 0x6721, 0,      0x343F, 0x6722,
    0x4934, 0x3859, 0x4449, 0,      0x575D, 0x425A, 0x3757, 0x563D, 0x4E46,
    0x3744, 0,      0,      0x4526, 0x6723, 0x4F5F, 0x6724, 0x6725, 0x6726,
    0x4137, 0x5769, 0x4970, 0x4F38, 0x562F, 0x5655, 0x6727, 0x306D, 0x6728,
    0x6729, 0x495C, 0x526F, 0x3E2D, 0x672A, 0x3073, 0x485E, 0x3D61, 0x672B,
    0x4846, 0,      0x672C, 0x3B66, 0x3878, 0x5124, 0x672D, 0x4267, 0x3E78,
    0x3D4A, 0x4D33, 0x672E, 0x672F, 0x3E6E, 0x5065, 0,      0x4B67, 0x4C50,
    0x3C4C, 0x6730, 0x3C28, 0x5077, 0x6731, 0,      0x5078, 0x6732, 0x6733,
    0x3442, 0x6734, 0x6735, 0x497E, 0x4E2C, 0x4360, 0x6737, 0x3141, 0x3371,
    0,      0x6738, 0x6739, 0x575B, 0x5540, 0x673A, 0x424C, 0x573A, 0x673B,
    0x673C, 0x673D, 0x3C6A, 0x4365, 0x4042, 0x673E, 0x673F, 0x3C29, 0,
    0x6740, 0x6741, 0x6736, 0x3650, 0x6742, 0,      0x6743, 0x6744, 0x3B3A,
    0x355E, 0x4246, 0x3160, 0x6745, 0x5435, 0x6746, 0x383F, 0x6748, 0x6747,
    0x376C, 0,      0x6749, 0x3278, 0x674A, 0x674B, 0x674C, 0x674D, 0x674E,
    0x674F, 0x6750, 0x5327, 0x4B75, 0x6751, 0x6752, 0x6753, 0x6754, 0x4949,
    0x6755, 0x6756, 0x6757, 0x6758, 0x6759, 0x3D49, 0x675A, 0x733E, 0,
    0x3857, 0,      0x4831, 0,      0,      0,      0,      0,      0,
    0,      0x733F, 0,      0x7340, 0x7341, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x395E, 0x4D78, 0,
    0,      0x5868, 0x3A31, 0,      0x425E, 0x6E37, 0,      0x3723, 0,
    0,      0,      0,      0x6E39, 0,      0x6E38, 0x3055, 0,      0,
    0,      0,      0,      0x6E3B, 0x5556, 0x576F, 0,      0,      0,
    0x5643, 0,      0,      0x6E3D, 0x4A70, 0,      0x6E3C, 0,      0,
    0,      0,      0x6E3E, 0,      0,      0,      0,      0x6E40, 0,
    0,      0x6E3F, 0,      0,      0,      0,      0,      0,      0,
    0,      0x5172, 0,      0x473C, 0,      0x4340, 0,      0,      0,
    0,      0,      0x3861, 0,      0,      0,      0,      0,      0x4167,
    0,      0,      0x7446, 0x505F, 0x7447, 0,      0x4F5B, 0,      0,
    0x483A, 0,      0,      0x7448, 0,      0,      0,      0,      0,
    0,      0,      0x7449, 0x744A, 0,      0x744B, 0,      0,      0,
    0,      0,      0x597A, 0x387E, 0,      0,      0x6571, 0x5370, 0,
    0x7460, 0,      0x4E4C, 0,      0,      0,      0x3361, 0,      0,
    0,      0,      0x7134, 0,      0x526E, 0,      0x7461, 0,      0,
    0,      0,      0,      0x4F68, 0x7462, 0,      0,      0x474C, 0,
    0,      0,      0,      0,      0,      0x3554, 0x3464, 0x7464, 0,
    0,      0,      0x7463, 0x7465, 0,      0,      0x7466, 0,      0,
    0,      0,      0x7467, 0,      0x3A32, 0x303F, 0,      0x7468, 0,
    0,      0,      0,      0,      0,      0,      0x372D, 0x526D, 0,
    0,      0,      0x522B, 0x404F, 0,      0x3F3C, 0x6B23, 0x555F, 0x6A48,
    0,      0,      0,      0,      0x7173, 0x3678, 0x4B23, 0,      0,
    0x444D, 0,      0x7167, 0,      0x7168, 0x387B, 0x7169, 0x3A44, 0x5445,
    0x3052, 0,      0,      0x716A, 0,      0,      0,      0x716B, 0,
    0x716C, 0,      0,      0x716D, 0x716E, 0x716F, 0x7171, 0x7170, 0x4555,
    0,      0,      0,      0,      0,      0,      0x7172, 0,      0x367A,
    0,      0x7174, 0x522E, 0x5E47, 0x4B4A, 0,      0,      0x335C, 0,
    0x3522, 0,      0x3922, 0,      0,      0x4474, 0x7175, 0,      0,
    0x7176, 0,      0,      0,      0x4144, 0x417B, 0x5630, 0x7177, 0,
    0,      0,      0,      0x7178, 0,      0x412A, 0,      0,      0,
    0x4638, 0,      0x3E5B, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x7179,
    0x344F, 0,      0,      0,      0,      0,      0,      0x717A, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x6D32, 0x6D31, 0,      0,      0x4B60, 0x525E,
    0,      0x4B41, 0x5558, 0,      0x4862, 0,      0x405F, 0x3C21, 0,
    0,      0,      0,      0,      0,      0x6B41, 0,      0,      0x5024,
    0,      0x5662, 0,      0x3647, 0x3858, 0x6B40, 0x384E, 0,      0x6B3F,
    0x3326, 0x3949, 0x562B, 0,      0x3774, 0x374A, 0,      0,      0,
    0x3C67, 0x373E, 0x6B46, 0,      0x6B47, 0x3039, 0x3F4F, 0,      0x6B45,
    0x537D, 0,      0x6B48, 0,      0,      0x6B49, 0,      0,      0x374E,
    0,      0x6B42, 0x6B44, 0x4976, 0x5657, 0x554D, 0x5032, 0x6B4F, 0x4E38,
    0x6B50, 0,      0x3528, 0,      0,      0,      0,      0,      0x3133,
    0x6B52, 0x4C25, 0,      0,      0,      0,      0,      0,      0,
    0x4556, 0x6B53, 0,      0x6B51, 0x455F, 0x6B4E, 0x4A24, 0x6B55, 0x307B,
    0,      0,      0x3A7A, 0,      0,      0x5837, 0x7163, 0,      0x6B4A,
    0x6B4B, 0x6B4C, 0x6B4D, 0x6B56, 0x6640, 0x6B59, 0,      0x3F68, 0x5248,
    0x6B57, 0x6B5C, 0x386C, 0x6B58, 0,      0x3D3A, 0,      0x5058, 0,
    0x3037, 0,      0x6B5D, 0x445C, 0,      0,      0,      0,      0x562C,
    0,      0,      0,      0x3460, 0,      0,      0x4276, 0x3C39, 0,
    0,      0x6B5A, 0x6B5B, 0x5460, 0x466A, 0x4454, 0x6B5F, 0x4527, 0x5975,
    0,      0x3231, 0,      0x6B64, 0,      0x3D45, 0,      0,      0,
    0x6B62, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x6B63, 0,      0,      0x382C,
    0,      0x4D51, 0x6B65, 0,      0,      0,      0x6B61, 0,      0x4133,
    0,      0,      0,      0,      0,      0x4622, 0,      0,      0,
    0,      0,      0,      0,      0x4C73, 0,      0x6B66, 0,      0x4030,
    0x5238, 0x6B67, 0,      0,      0,      0x382F, 0x382D, 0,      0x6B68,
    0x473B, 0x4D73, 0,      0,      0,      0x6B6A, 0x6B6B, 0,      0,
    0,      0,      0,      0x6B6D, 0,      0,      0,      0,      0x5048,
    0,      0x6B72, 0,      0x6B6E, 0,      0,      0,      0x6B71, 0x4879,
    0,      0x517C, 0x6B6C, 0,      0,      0x6B69, 0,      0,      0,
    0,      0x3839, 0x4F59, 0x4465, 0x6B6F, 0x6B70, 0x4C5A, 0x4D48, 0x3072,
    0,      0x6B76, 0,      0,      0,      0,      0,      0x6B75, 0,
    0x3232, 0,      0,      0,      0,      0x3860, 0,      0x6B77, 0,
    0,      0,      0,      0,      0,      0x316C, 0,      0,      0x4C45,
    0x4424, 0x4F25, 0,      0,      0,      0,      0,      0x6B79, 0,
    0,      0x6C22, 0,      0x4572, 0,      0x6B7A, 0,      0,      0,
    0,      0,      0,      0,      0,      0x4945, 0,      0,      0,
    0,      0,      0,      0x625F, 0x6B7E, 0,      0,      0,      0,
    0x4D4E, 0x6C21, 0x315B, 0x5337, 0,      0,      0x525C, 0,      0,
    0,      0x6B7D, 0,      0x6B7B, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x333C,
    0,      0,      0,      0x6A30, 0,      0,      0x5754, 0,      0x742B,
    0x3374, 0,      0,      0,      0,      0,      0x5641, 0x5642, 0,
    0,      0,      0,      0,      0,      0x5569, 0x3E4A, 0,      0x7427,
    0,      0x5228, 0x7428, 0x7429, 0,      0x742A, 0x3E4B, 0x535F, 0,
    0,      0,      0,      0,      0x4960, 0x4961, 0,      0,      0x7342,
    0,      0x4A66, 0,      0x4C72, 0,      0,      0,      0,      0,
    0,      0x6236, 0x4B34, 0,      0x4E68, 0x565B, 0,      0x742D, 0x742E,
    0x742F, 0,      0,      0,      0,      0x7432, 0,      0x3A3D, 0x7433,
    0x3063, 0x7430, 0,      0x7431, 0x3D22, 0x3255, 0,      0x7436, 0x7437,
    0x3666, 0x3230, 0x4F4F, 0x7434, 0x342C, 0,      0x7435, 0,      0,
    0x7438, 0,      0,      0,      0,      0,      0x7439, 0,      0,
    0x4D27, 0,      0x743A, 0,      0x743B, 0,      0,      0,      0x743C,
    0,      0,      0,      0,      0,      0,      0,      0,      0x4B52,
    0,      0x743D, 0,      0,      0,      0,      0x743E, 0,      0,
    0,      0,      0,      0,      0,      0,      0x743F, 0,      0,
    0,      0,      0,      0x745E, 0x413C, 0x3C68, 0,      0x492B, 0x515E,
    0x6575, 0,      0,      0,      0,      0x5C33, 0x5255, 0,      0,
    0x5C34, 0x302C, 0x5C35, 0,      0,      0x3D5A, 0,      0x5C39, 0,
    0,      0,      0x5842, 0,      0x5C37, 0x5373, 0,      0x4956, 0x5C3A,
    0x5C36, 0,      0x5C3B, 0x4322, 0,      0,      0,      0,      0x5C3C,
    0x5C45, 0x5C3D, 0,      0,      0x4E5F, 0x5625, 0,      0x5C4F, 0,
    0x5C4D, 0,      0,      0x5C52, 0x3D66, 0x422B, 0,      0x5C38, 0x5C4B,
    0x5C4E, 0x5C3E, 0x3752, 0x3045, 0x5C47, 0x503E, 0x5C41, 0x3B28, 0,
    0x373C, 0x5C4C, 0,      0,      0x5C46, 0x5C3F, 0x475B, 0,      0,
    0,      0x513F, 0x5C40, 0,      0,      0x5C4A, 0,      0,      0x5C50,
    0,      0,      0x4E2D, 0x5C42, 0,      0x5C43, 0x5C48, 0x5C49, 0x3254,
    0x5C51, 0x4B55, 0,      0x5437, 0x5C5B, 0x5C5F, 0x4C26, 0x5C66, 0,
    0x4367, 0x5C5C, 0,      0,      0x3F41, 0x5C59, 0,      0x307A, 0x3936,
    0x5C65, 0x5C53, 0,      0x5C44, 0x5C56, 0x4874, 0x3F60, 0,      0,
    0,      0,      0x493B, 0,      0,      0,      0x313D, 0,      0x5322,
    0,      0,      0x5C5A, 0,      0,      0x5C55, 0,      0x463B, 0,
    0x5C5E, 0,      0,      0,      0,      0,      0x5742, 0x432F, 0x3736,
    0x4751, 0x4329, 0x5C62, 0x5C58, 0x5C6B, 0x5C54, 0,      0,      0x5C5D,
    0,      0x3E25, 0x5C57, 0,      0x5C60, 0,      0,      0x5C63, 0x5C64,
    0,      0x5C78, 0,      0,      0x5C61, 0x5D22, 0x5C67, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x3C6B,
    0x3444, 0,      0,      0x4323, 0x3267, 0x5C7A, 0,      0x5C72, 0,
    0x5C6F, 0,      0x5C7C, 0x5C6E, 0x5270, 0x3268, 0,      0x4857, 0x4863,
    0x5C7B, 0,      0x5C6D, 0,      0,      0,      0x5C77, 0,      0,
    0x5C75, 0,      0,      0x3E23, 0x5C74, 0,      0x325D, 0,      0,
    0,      0,      0,      0x5C73, 0x3C76, 0x5C68, 0x3B44, 0,      0x4073,
    0,      0,      0,      0,      0,      0x3C54, 0x5C69, 0x5C6A, 0,
    0x5C71, 0x5C76, 0x5C79, 0x3534, 0,      0x4859, 0x3B67, 0x5C7E, 0x5C7D,
    0x532B, 0x5D21, 0x5D23, 0x5D25, 0x5271, 0x5D24, 0x5D26, 0x5D27, 0x5229,
    0,      0,      0,      0,      0,      0,      0,      0x3A49, 0x5D29,
    0,      0,      0x5D36, 0x5D31, 0x5D34, 0,      0,      0,      0,
    0,      0,      0,      0x5D30, 0x464E, 0,      0,      0x4072, 0,
    0,      0,      0,      0x492F, 0,      0,      0,      0x5C6C, 0x5D2E,
    0,      0,      0,      0,      0x5D37, 0,      0,      0x5C70, 0x5D2F,
    0,      0x5D38, 0,      0x5D2C, 0,      0,      0,      0,      0,
    0,      0,      0x5D39, 0x5D33, 0x5D2D, 0x442A, 0,      0,      0,
    0,      0x5D28, 0x4033, 0x412B, 0x5D2A, 0x5D2B, 0,      0x5D32, 0x3B71,
    0x5D35, 0x5328, 0x5D3A, 0,      0x5D3B, 0x4327, 0,      0,      0x5D52,
    0x5D3C, 0,      0,      0,      0x5D51, 0,      0x393D, 0,      0,
    0x3E55, 0,      0x3E7A, 0,      0,      0x3A4A, 0,      0,      0,
    0,      0x5D4A, 0,      0x5D45, 0,      0x5D3F, 0,      0,      0,
    0x324B, 0x5D43, 0,      0x5D4B, 0x3224, 0x5D55, 0,      0,      0,
    0x5D3E, 0,      0,      0,      0x4650, 0x5D50, 0,      0,      0,
    0,      0,      0x5D54, 0x4162, 0x3746, 0,      0,      0,      0,
    0,      0x5D4E, 0x5D4F, 0,      0,      0,      0x5D44, 0,      0,
    0,      0x5D3D, 0,      0x5D4D, 0x4C51, 0,      0x5D49, 0,      0,
    0,      0,      0x5D42, 0x4348, 0x463C, 0x4E2E, 0x5D4C, 0,      0x5D48,
    0,      0,      0,      0,      0,      0,      0x5D41, 0,      0,
    0,      0x5D46, 0x425C, 0,      0,      0,      0,      0,      0,
    0x5329, 0x532A, 0x5D53, 0x4F74, 0x4878, 0,      0,      0,      0,
    0,      0,      0,      0,      0x5D66, 0,      0,      0,      0,
    0,      0,      0x5D47, 0,      0,      0,      0x5D60, 0x4264, 0,
    0,      0,      0,      0,      0,      0,      0,      0x5D61, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x5D57, 0,      0,      0,      0,      0,      0x5678, 0,      0x5D59,
    0x5D58, 0x3870, 0x5D56, 0,      0,      0,      0,      0x464F, 0,
    0x362D, 0,      0,      0,      0,      0,      0x5D62, 0,      0x3A79,
    0x5461, 0x5D67, 0,      0,      0,      0x3450, 0,      0x5D5A, 0,
    0x3F7B, 0x5D63, 0,      0x5D5F, 0,      0x5D5D, 0,      0,      0,
    0,      0,      0,      0,      0x3559, 0,      0,      0,      0,
    0x5D5B, 0x5D5C, 0x5D5E, 0,      0x3D2F, 0x5D64, 0,      0x5D65, 0,
    0,      0,      0,      0,      0,      0,      0,      0x5D75, 0,
    0x4349, 0,      0,      0x4B62, 0,      0,      0,      0,      0x5D72,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x5861, 0,      0,      0x4651, 0,
    0x5D74, 0,      0,      0,      0x5574, 0x5D73, 0x5D70, 0,      0,
    0x5D6C, 0,      0x5D6F, 0,      0x5D68, 0,      0,      0x506E, 0,
    0,      0,      0,      0x4858, 0x5D6E, 0,      0,      0x5D69, 0,
    0,      0x5D6A, 0x4B72, 0,      0x5D6D, 0,      0,      0x314D, 0,
    0,      0,      0,      0,      0,      0x4036, 0,      0x3C3B, 0x5D71,
    0,      0,      0x5D77, 0,      0x5D76, 0x5D6B, 0,      0,      0,
    0,      0,      0x456E, 0,      0,      0,      0x5D7B, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x5E24, 0,      0,      0x5E23, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x5D78, 0,
    0,      0,      0,      0x436F, 0,      0x427B, 0,      0,      0,
    0x5561, 0,      0,      0x4E35, 0,      0,      0,      0,      0x5D7D,
    0,      0x324C, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x4468, 0x4A5F, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x473E, 0x5D7A, 0x5D7C, 0x5D7E,
    0x5E22, 0x302A, 0x314E, 0,      0,      0,      0,      0,      0x5E2C,
    0,      0,      0,      0,      0x5E26, 0x3D36, 0x486F, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x5E21, 0,      0,      0x5E25, 0,      0,      0,      0,      0x5E29,
    0,      0,      0,      0,      0,      0x5E28, 0,      0,      0,
    0x5E27, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x5E2D, 0,      0x544C, 0,      0,      0,      0,      0x5E33,
    0x5E2A, 0x5E2E, 0,      0,      0x4059, 0,      0,      0,      0,
    0,      0x3121, 0x5E36, 0,      0x5E31, 0,      0,      0,      0,
    0,      0,      0,      0x5E32, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x5126, 0x5E35, 0,
    0,      0,      0,      0,      0,      0,      0x5E2F, 0,      0,
    0,      0x5E30, 0,      0x503D, 0,      0,      0,      0x5E34, 0x4A6D,
    0x5E39, 0,      0,      0,      0,      0,      0,      0x5E38, 0,
    0x5E37, 0,      0,      0,      0,      0,      0,      0,      0x5E3B,
    0,      0,      0,      0,      0,      0,      0,      0x3D65, 0,
    0,      0,      0,      0,      0x3258, 0x436A, 0,      0,      0x5E3A,
    0,      0x453A, 0,      0,      0,      0,      0,      0,      0x5E3C,
    0,      0,      0,      0,      0,      0,      0,      0x4C59, 0,
    0,      0,      0,      0x372A, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x5465, 0,      0,      0,      0x5E3D, 0,
    0,      0,      0,      0,      0x5E3F, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x4422, 0,
    0,      0,      0,      0x5E41, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x5E3E, 0,      0x5E40, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x553A, 0,      0,      0,      0x5E42, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x722E, 0x3B22, 0x4232, 0x4530, 0x4247,
    0,      0,      0x722F, 0,      0,      0,      0,      0,      0x5069,
    0,      0,      0,      0x535D, 0,      0,      0,      0x6B3D, 0,
    0,      0,      0,      0,      0,      0,      0,      0x3366, 0x7230,
    0,      0x7231, 0,      0,      0x4A2D, 0,      0,      0,      0,
    0,      0,      0,      0x3A67, 0x7233, 0x7235, 0x7234, 0x4B64, 0x4F3A,
    0x7232, 0x4A34, 0x524F, 0x426C, 0,      0,      0,      0,      0,
    0,      0,      0x4E43, 0x7238, 0x3076, 0x7237, 0,      0,      0,
    0,      0,      0x723E, 0,      0x324F, 0,      0,      0,      0,
    0,      0,      0x5141, 0x723A, 0,      0,      0,      0,      0,
    0x723C, 0x5469, 0,      0,      0x723B, 0x7236, 0x723F, 0x723D, 0,
    0x7239, 0,      0,      0x7247, 0x7244, 0x7246, 0,      0,      0x724A,
    0x7242, 0x7240, 0,      0,      0,      0x7245, 0,      0,      0,
    0,      0,      0x567B, 0,      0,      0,      0x7241, 0,      0x4779,
    0x495F, 0,      0x7248, 0x3946, 0x3530, 0,      0,      0x7243, 0x7249,
    0x7250, 0x7256, 0,      0,      0x3B57, 0,      0,      0,      0x7255,
    0x4D5C, 0,      0x566B, 0,      0,      0x7252, 0x7254, 0,      0,
    0,      0,      0x3872, 0,      0,      0,      0,      0x724B, 0,
    0,      0,      0x724E, 0x4279, 0,      0x555D, 0x724C, 0x724D, 0x724F,
    0x7253, 0,      0,      0,      0x7259, 0x533C, 0,      0,      0,
    0,      0x366A, 0,      0x4A71, 0,      0x3764, 0x7257, 0,      0,
    0,      0x7258, 0x725A, 0x725D, 0x725B, 0,      0,      0x725C, 0,
    0,      0,      0,      0x5151, 0x7251, 0,      0x4D49, 0,      0x4E4F,
    0x5629, 0,      0x7263, 0,      0x435B, 0,      0x7260, 0,      0,
    0x402F, 0x726C, 0x725E, 0,      0x7261, 0,      0,      0,      0x7268,
    0,      0,      0,      0,      0x7262, 0,      0,      0x7267, 0,
    0,      0x7266, 0,      0,      0x7269, 0,      0,      0,      0x725F,
    0,      0,      0x7264, 0x726A, 0,      0,      0,      0,      0,
    0,      0,      0x532C, 0x7265, 0x3275, 0,      0,      0x7272, 0,
    0x502B, 0,      0,      0,      0,      0x7275, 0,      0,      0,
    0x3B48, 0,      0x7279, 0,      0,      0,      0,      0,      0,
    0x7270, 0,      0,      0x7276, 0x7278, 0x727A, 0,      0,      0,
    0,      0,      0,      0,      0,      0x7273, 0,      0x7271, 0,
    0,      0,      0x3A7B, 0,      0x357B, 0,      0,      0,      0,
    0x726F, 0x7277, 0x726D, 0x726E, 0,      0,      0,      0x726B, 0x7326,
    0,      0x7323, 0,      0,      0x7322, 0,      0,      0x7274, 0,
    0x485A, 0,      0,      0,      0,      0,      0x727B, 0,      0,
    0,      0x7325, 0,      0,      0,      0,      0,      0,      0,
    0x4378, 0,      0,      0,      0,      0,      0,      0,      0,
    0x727D, 0,      0,      0x7327, 0x7329, 0x7324, 0,      0x727C, 0,
    0,      0,      0x732B, 0,      0x732A, 0,      0,      0,      0,
    0x425D, 0,      0,      0x732E, 0,      0,      0x7330, 0,      0,
    0,      0,      0,      0x7321, 0,      0,      0,      0x7331, 0x732C,
    0,      0,      0,      0,      0,      0x732F, 0x727E, 0x732D, 0,
    0,      0,      0,      0,      0,      0,      0x7332, 0,      0,
    0,      0,      0x7334, 0,      0,      0,      0,      0x7328, 0,
    0,      0,      0,      0x7333, 0,      0,      0,      0x7335, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x5037, 0,      0,      0,      0,      0x7338, 0,      0,      0,
    0,      0x5979, 0,      0,      0,      0,      0,      0,      0x7339,
    0,      0,      0,      0,      0,      0,      0,      0,      0x7337,
    0,      0x4864, 0x7336, 0,      0,      0,      0,      0x733A, 0,
    0,      0,      0,      0,      0x733B, 0x3440, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x6E43, 0,      0,      0,      0,      0,
    0,      0x733C, 0,      0,      0x733D, 0,      0,      0,      0x512A,
    0,      0,      0,      0x742C, 0x5046, 0,      0,      0,      0,
    0,      0,      0x5050, 0x515C, 0,      0,      0,      0,      0,
    0,      0x4F4E, 0,      0,      0x3D56, 0,      0x5143, 0,      0,
    0,      0,      0,      0,      0,      0x3A62, 0x6169, 0x5242, 0x7142,
    0x3239, 0,      0,      0x316D, 0x7143, 0,      0x4940, 0x3344, 0,
    0x5972, 0,      0x4B25, 0,      0x7144, 0,      0,      0,      0,
    0x5654, 0,      0,      0,      0,      0,      0x7145, 0x7440, 0x7146,
    0,      0x542C, 0x7147, 0,      0x3040, 0x7441, 0,      0,      0x7442,
    0,      0,      0x347C, 0,      0x455B, 0,      0,      0,      0,
    0x4C3B, 0,      0,      0,      0x5064, 0,      0,      0,      0,
    0,      0x4D60, 0,      0,      0,      0,      0,      0x7148, 0,
    0x5973, 0,      0,      0,      0,      0,      0,      0x313B, 0,
    0x4F2E, 0,      0,      0,      0x3824, 0,      0,      0,      0,
    0,      0x714A, 0,      0,      0,      0,      0x714B, 0,      0,
    0,      0,      0x3243, 0x4151, 0,      0,      0x5730, 0x7149, 0,
    0,      0x714C, 0,      0,      0,      0,      0x714E, 0,      0,
    0,      0x5976, 0,      0x5261, 0x5423, 0,      0,      0x7443, 0x4839,
    0,      0,      0,      0,      0,      0x7444, 0,      0,      0x714D,
    0x714F, 0x3F63, 0x7150, 0,      0,      0x7154, 0,      0,      0,
    0,      0,      0,      0,      0x7156, 0x7151, 0,      0x4951, 0x4561,
    0,      0,      0,      0x4263, 0x397C, 0,      0,      0x7153, 0,
    0x7155, 0,      0,      0,      0x3953, 0,      0,      0,      0,
    0,      0,      0,      0x715B, 0,      0,      0,      0,      0,
    0x3A56, 0,      0x307D, 0x7159, 0,      0,      0,      0,      0,
    0x7158, 0x7152, 0x715A, 0,      0,      0,      0,      0,      0x7157,
    0,      0,      0,      0x486C, 0,      0,      0,      0,      0x4D4A,
    0x715D, 0,      0,      0,      0,      0x653D, 0,      0,      0,
    0x715C, 0,      0x715E, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x715F, 0,      0,      0x4F65, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x7445, 0x3D73, 0,
    0,      0,      0,      0,      0,      0x7160, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x7161,
    0,      0,      0,      0x4E77, 0,      0x522A, 0,      0x717B, 0,
    0,      0x3832, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x3C7B, 0x395B, 0,
    0x3966, 0x4359, 0x4A53, 0x6A68, 0x4040, 0x3E75, 0x6A69, 0x6A6A, 0x6A6B,
    0,      0x6A6C, 0x6A6D, 0x6A6E, 0x6A6F, 0x3D47, 0,      0,      0,
    0x757B, 0,      0,      0,      0x757D, 0,      0x757E, 0,      0x757C,
    0,      0,      0,      0,      0x3D62, 0,      0x7621, 0x3425, 0,
    0,      0,      0,      0x7622, 0,      0,      0,      0x7623, 0,
    0,      0,      0x6C32, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x5154, 0,      0,
    0,      0,      0,      0,      0x596A, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x7624, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x6E3A, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x5532, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x537E,
    0x4C5C, 0,      0,      0,      0,      0,      0,      0,      0,
    0x4A44, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6540,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x7625, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x3E2F, 0,      0,      0,      0,
    0,      0x4629, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x5A25,
    0x3C46, 0x3629, 0x383C, 0x484F, 0x3C25, 0x5A26, 0x5A27, 0x4C56, 0x4843,
    0x5A28, 0x467D, 0,      0x5135, 0x5269, 0x5136, 0x3C47, 0,      0x3D32,
    0x3B64, 0x5A29, 0x5A2A, 0x5148, 0x5A2B, 0x506D, 0x366F, 0x425B, 0,
    0x4B4F, 0x376D, 0x4968, 0x3743, 0x3E77, 0x5624, 0x5A2C, 0x5A2D, 0x4640,
    0x5767, 0x4A36, 0,      0x5529, 0x4B5F, 0x556F, 0x5A2E, 0x565F, 0x344A,
    0x5A30, 0x5A2F, 0,      0x526B, 0x5A31, 0x5A32, 0x5A33, 0x4A54, 0x5A34,
    0x4A2B, 0x5A35, 0x5A36, 0x334F, 0x566F, 0x5A37, 0x3B30, 0x352E, 0x5A38,
    0x5A39, 0x396E, 0x512F, 0x5268, 0x5A3A, 0x3843, 0x4F6A, 0x326F, 0x5A3B,
    0x5A3C, 0,      0x3D6B, 0x4E5C, 0x536F, 0x5A3D, 0x4E73, 0x5A3E, 0x5355,
    0x3B65, 0x5A3F, 0x4B35, 0x4B50, 0x5A40, 0x476B, 0x566E, 0x5A41, 0x4535,
    0x3641, 0x5A42, 0x374C, 0x3F4E, 0x5A43, 0x5A44, 0x4B2D, 0x5A45, 0x3577,
    0x5A46, 0x4142, 0x573B, 0x5A47, 0x4C38, 0,      0x526A, 0x4431, 0x5A48,
    0x357D, 0x3B51, 0x5A49, 0x5033, 0x5A4A, 0x5A4B, 0x4E3D, 0x5A4C, 0x5A4D,
    0x5A4E, 0x3277, 0x5A51, 0x5A4F, 0x5168, 0x5A50, 0x4355, 0x5A52, 0,
    0x5A53, 0x5A54, 0x5A55, 0x503B, 0x5225, 0x3079, 0x5A56, 0x472B, 0x5A57,
    0x3D77, 0x4321, 0x5A58, 0x5A59, 0x437D, 0x4C37, 0x5A5A, 0x5A5B, 0x403E,
    0x4657, 0x5A5C, 0x5A5D, 0x4734, 0x5A5E, 0x5A5F, 0x3948, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x3B6D, 0,
    0,      0,      0,      0x3639, 0x7478, 0,      0x7479, 0,      0,
    0x4D63, 0,      0,      0,      0,      0,      0,      0,      0,
    0x7539, 0,      0,      0,      0,      0x6B60, 0,      0,      0,
    0,      0,      0,      0x4F73, 0x3B3F, 0,      0,      0,      0,
    0,      0,      0,      0x3A40, 0x5425, 0,      0,      0,      0,
    0,      0,      0,      0x6159, 0,      0,      0,      0,      0x7574,
    0x312A, 0x3272, 0,      0,      0,      0,      0,      0,      0,
    0x7575, 0,      0,      0x7577, 0,      0,      0,      0x3A51, 0x7576,
    0,      0x4332, 0,      0,      0,      0,      0,      0,      0,
    0x7579, 0,      0,      0,      0x7578, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x3134, 0x556A, 0x383A, 0,      0x3931, 0x3246, 0x5470,
    0x4F4D, 0x305C, 0x554B, 0x3B75, 0x564A, 0x3737, 0x4C30, 0x4636, 0x3161,
    0x393A, 0x567C, 0x3961, 0x3721, 0x3C7A, 0x6A5A, 0x6A5B, 0x4C79, 0x3973,
    0x6A5C, 0x347B, 0x4333, 0x3751, 0x3A58, 0x6A5D, 0x5474, 0x6A5E, 0x3C56,
    0x3B5F, 0x6A5F, 0x415E, 0x4238, 0x545F, 0x574A, 0x6A60, 0x6A61, 0x6A64,
    0x6A62, 0x6A63, 0x495E, 0x3833, 0x3644, 0x6A65, 0x4A6A, 0x494D, 0x344D,
    0,      0,      0x6259, 0x4562, 0x6A66, 0x4035, 0,      0x5738, 0x6A67,
    0x572C, 0x487C, 0x5853, 0x584D, 0x545E, 0,      0x5479, 0x4944, 0x532E,
    0x3853, 0x3360, 0,      0x4962, 0x7476, 0,      0,      0,      0x3A55,
    0,      0x7477, 0,      0,      0x575F, 0,      0,      0x7471, 0x3830,
    0x5554, 0x384F, 0x4670, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x3343, 0,      0,      0x7472, 0x332C, 0,
    0,      0,      0,      0x543D, 0x4777, 0,      0,      0,      0,
    0,      0x7474, 0,      0,      0x7473, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x4C4B, 0,      0,
    0,      0x4824, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x7475, 0,      0x5763,
    0x453F, 0x7540, 0,      0,      0x753B, 0,      0x7543, 0,      0x7542,
    0,      0x563A, 0x7541, 0,      0,      0,      0x543E, 0x7544, 0,
    0x754C, 0,      0,      0,      0,      0x304F, 0x3578, 0,      0x7549,
    0x754A, 0,      0x455C, 0,      0,      0,      0,      0x7545, 0x7546,
    0,      0,      0x7547, 0x754B, 0,      0x3E60, 0x7548, 0x387A, 0,
    0,      0,      0x7550, 0x7553, 0,      0,      0,      0x3F67, 0,
    0x3972, 0x753C, 0x754D, 0,      0,      0x4237, 0,      0,      0,
    0x4C78, 0,      0x3C79, 0,      0x754E, 0x754F, 0x7551, 0x3665, 0x7552,
    0,      0x7555, 0,      0,      0,      0,      0,      0,      0,
    0x753D, 0,      0,      0,      0x7554, 0x533B, 0,      0x336C, 0,
    0,      0x4C24, 0,      0,      0,      0,      0x7556, 0,      0,
    0,      0,      0,      0,      0,      0,      0x7557, 0x3E61, 0x7558,
    0,      0,      0x4C5F, 0x755B, 0,      0,      0,      0,      0,
    0x3248, 0x5759, 0,      0x7559, 0,      0x755A, 0x755C, 0,      0x7562,
    0,      0,      0,      0x7560, 0,      0,      0,      0x755F, 0x755D,
    0,      0,      0x7561, 0,      0,      0x755E, 0x7564, 0x7565, 0,
    0x4C63, 0,      0,      0x653F, 0x3538, 0x7563, 0x7568, 0x4C23, 0,
    0,      0,      0,      0,      0x7566, 0x7567, 0,      0,      0,
    0,      0,      0,      0x753E, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x3144, 0,
    0,      0x753F, 0,      0,      0x3545, 0x3264, 0,      0x756C, 0x7569,
    0,      0x3657, 0,      0x756D, 0,      0x756A, 0,      0,      0,
    0,      0,      0x756B, 0,      0,      0x345A, 0,      0x546A, 0,
    0,      0,      0x756E, 0,      0x3379, 0,      0,      0,      0,
    0,      0,      0,      0x756F, 0x7571, 0,      0,      0,      0x7570,
    0,      0,      0,      0,      0,      0,      0,      0x7572, 0,
    0x7573, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x496D, 0x392A, 0,      0,      0x477B,
    0,      0,      0x3663, 0,      0,      0,      0,      0,      0,
    0,      0x4C49, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x6A26, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x3335, 0x547E, 0x396C, 0x5079, 0,      0x696D, 0x572A,
    0x696E, 0x4256, 0x486D, 0x3A64, 0x696F, 0x6970, 0x6971, 0x5661, 0x6972,
    0x6973, 0x6975, 0x6974, 0x6976, 0x6977, 0x4761, 0x6978, 0x5458, 0x6979,
    0x3D4E, 0,      0x697A, 0x697B, 0x3D4F, 0x697C, 0x3828, 0x413E, 0x697D,
    0x3132, 0x3B54, 0x3975, 0x697E, 0,      0x6A21, 0x6A22, 0x6A23, 0x3778,
    0x3C2D, 0,      0x4A64, 0x604E, 0x542F, 0x4F3D, 0x5537, 0x6A24, 0x555E,
    0x6A25, 0x5041, 0x393C, 0,      0x3447, 0x3159, 0,      0,      0,
    0x4031, 0,      0,      0,      0,      0x3166, 0x3167, 0,      0x3168,
    0,      0,      0,      0,      0x333D, 0x4868, 0,      0,      0,
    0,      0x6541, 0,      0,      0x315F, 0,      0,      0,      0x4149,
    0x346F, 0,      0,      0x4728, 0x5358, 0,      0x4679, 0x5138, 0,
    0x397D, 0x4275, 0,      0,      0,      0,      0,      0x532D, 0,
    0x544B, 0x3D7C, 0,      0x6542, 0x3735, 0x6543, 0,      0,      0x3B39,
    0x5562, 0,      0x3D78, 0x5436, 0x4E25, 0x412C, 0x3359, 0,      0,
    0x4C76, 0,      0x6546, 0x6544, 0x6548, 0,      0x654A, 0x6547, 0x354F,
    0x4648, 0,      0x357C, 0x6545, 0,      0x4A76, 0,      0,      0x6549,
    0,      0,      0,      0x4354, 0x3145, 0x3C23, 0,      0,      0,
    0x5737, 0,      0,      0x4D4B, 0x4B4D, 0x4A4A, 0x4C53, 0x654C, 0x654B,
    0x4466, 0,      0,      0x5121, 0x5137, 0x654D, 0,      0x6550, 0,
    0x4D38, 0x5670, 0x654F, 0x355D, 0,      0x4D3E, 0,      0x6551, 0x363A,
    0,      0,      0x4D28, 0x3964, 0,      0x4A45, 0x3351, 0x4B59, 0x546C,
    0x6552, 0x376A, 0,      0,      0,      0x654E, 0,      0,      0,
    0,      0,      0,      0x6555, 0x347E, 0x6556, 0,      0,      0,
    0,      0,      0x6553, 0x6554, 0,      0x525D, 0,      0,      0x425F,
    0x3146, 0,      0x5362, 0,      0,      0x365D, 0x4B6C, 0,      0x6557,
    0,      0,      0x5376, 0,      0,      0,      0,      0,      0x3169,
    0,      0x3674, 0x655A, 0x6558, 0x6559, 0x3540, 0,      0,      0,
    0x5245, 0x655C, 0,      0,      0x655E, 0,      0,      0,      0,
    0,      0,      0x655D, 0x4732, 0,      0x5223, 0,      0,      0x655B,
    0,      0,      0,      0,      0x5462, 0x555A, 0,      0,      0,
    0,      0,      0x6560, 0x5771, 0,      0,      0,      0,      0,
    0,      0,      0x6561, 0,      0x315C, 0x517B, 0,      0x6562, 0x6564,
    0,      0,      0,      0,      0x6563, 0,      0,      0x6565, 0,
    0,      0,      0,      0,      0x5258, 0,      0x354B, 0,      0x675F,
    0,      0x5A75, 0,      0x5A78, 0,      0x5A76, 0,      0x5A77, 0,
    0,      0,      0x5A7A, 0x504F, 0x4447, 0,      0,      0x306E, 0,
    0,      0,      0x5030, 0,      0x5A79, 0,      0x534A, 0x3A2A, 0x5B22,
    0x4771, 0,      0x5A7C, 0x5A7B, 0x495B, 0x5A7D, 0,      0x5B21, 0x575E,
    0x5A7E, 0x415A, 0,      0,      0x5B25, 0,      0,      0x5374, 0,
    0,      0x5B27, 0x5B24, 0,      0x5B28, 0,      0,      0x3D3C, 0,
    0,      0,      0x4049, 0x5B23, 0x5B26, 0x5623, 0,      0x5B29, 0,
    0,      0,      0x5B2D, 0,      0,      0,      0x5B2E, 0x5B2C, 0x3A42,
    0,      0,      0,      0x3F24, 0x5B2B, 0,      0,      0,      0x5B2A,
    0x5447, 0x323F, 0,      0,      0x5B2F, 0,      0x3979, 0,      0x5B30,
    0,      0,      0,      0,      0x333B, 0,      0,      0,      0x3526,
    0,      0,      0,      0,      0x363C, 0x5B31, 0,      0,      0,
    0x3675, 0,      0x5B32, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x3149, 0,      0,      0,
    0,      0x5B34, 0,      0,      0,      0x5B33, 0x5B35, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x5B37, 0,      0x5B36, 0,      0,      0,      0,      0,      0,
    0,      0x5B38, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x5B39, 0,      0,      0x5B3A, 0,      0,      0x534F,
    0x747A, 0x4775, 0x5743, 0x4564, 0x747C, 0x747D, 0x747B, 0,      0x3E46,
    0,      0,      0,      0,      0x506F, 0,      0,      0x3753, 0,
    0,      0x544D, 0x4C2A, 0,      0,      0x7522, 0x7521, 0x3A28, 0x747E,
    0x4B56, 0,      0,      0,      0x7524, 0x4052, 0,      0x336A, 0,
    0x4D2A, 0x7525, 0x7523, 0x3D34, 0x7528, 0,      0x7529, 0x3D4D, 0x4338,
    0x3F61, 0x4B61, 0x752A, 0,      0,      0,      0x7526, 0x7527, 0x4470,
    0,      0,      0,      0,      0,      0x752C, 0,      0x343C, 0,
    0x576D, 0,      0x3457, 0x752B, 0x752E, 0,      0,      0x752D, 0x752F,
    0x5051, 0,      0,      0,      0,      0,      0,      0,      0x4351,
    0x4829, 0,      0,      0,      0,      0,      0,      0x7530, 0x7531,
    0,      0,      0,      0,      0,      0,      0x7532, 0,      0,
    0x7533, 0x7534, 0x7535, 0,      0,      0,      0,      0x7537, 0x7536,
    0,      0,      0,      0,      0x7538, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x3249,
    0,      0x5354, 0x4A4D, 0,      0x406F, 0x5658, 0x5230, 0x413F, 0,
    0x3D70, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x382A, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x3C78, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x7646, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x7647, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x7648, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x7649, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x764A, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x764C, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x764B, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x7769, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x764D, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x764E, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x6E44,
    0x6E45, 0x6E46, 0x556B, 0x3624, 0x6E48, 0x6E47, 0x6E49, 0x6E4A, 0x4725,
    0x6E4B, 0x6E4C, 0,      0x3730, 0x3576, 0x6E4D, 0x6E4F, 0,      0x6E4E,
    0,      0x3846, 0x6E50, 0x6E51, 0x6E52, 0x365B, 0x332E, 0x5653, 0x4446,
    0x3135, 0x3856, 0x6E53, 0x6E54, 0x543F, 0x4755, 0x3E7B, 0x4E59, 0x3933,
    0x6E56, 0x6E55, 0x6E58, 0x6E57, 0x4525, 0x6E59, 0x6E5A, 0x472E, 0x6E5B,
    0x472F, 0x6E5C, 0x3227, 0x6E5D, 0x6E5E, 0x6E5F, 0x6E60, 0x6E61, 0x576A,
    0x6E62, 0x6E63, 0x3C58, 0x6E64, 0x534B, 0x4C7A, 0x322C, 0x4165, 0x6E65,
    0x4726, 0x432D, 0,      0x6E66, 0x6E67, 0x6E68, 0x6E69, 0x6E6A, 0x6E6B,
    0x6E6C, 0,      0x6E6D, 0x6E6E, 0x6E6F, 0,      0,      0x6E70, 0x6E71,
    0x6E72, 0x6E74, 0x6E73, 0,      0x6E75, 0x4D2D, 0x4241, 0x6E76, 0x6E77,
    0x6E78, 0x5521, 0x6E79, 0x4F33, 0x6E7A, 0x6E7B, 0,      0x6E7C, 0x6E7D,
    0x6F21, 0x6E7E, 0x6F22, 0x3875, 0x437A, 0x6F23, 0x6F24, 0x3D42, 0x523F,
    0x3279, 0x6F25, 0x6F26, 0x6F27, 0x5278, 0x6F28, 0x567D, 0x6F29, 0x464C,
    0,      0x6F2A, 0x6F2B, 0x4134, 0x6F2C, 0x4F7A, 0x4B78, 0x6F2E, 0x6F2D,
    0x337A, 0x3978, 0x6F2F, 0x6F30, 0x5062, 0x6F31, 0x6F32, 0x3766, 0x503F,
    0x6F33, 0x6F34, 0x6F35, 0x4871, 0x4C60, 0x6F36, 0x6F37, 0x6F38, 0x6F39,
    0x6F3A, 0x5560, 0x6F3B, 0x346D, 0x432A, 0x6F3C, 0,      0x6F3D, 0x6F3E,
    0x6F3F, 0,      0x4E7D, 0x6F40, 0x4260, 0x3438, 0x5736, 0x3D75, 0,
    0x4F47, 0x6F43, 0x6F41, 0x6F42, 0x6F44, 0x3627, 0x3C7C, 0x3E62, 0x434C,
    0x6F45, 0x6F46, 0,      0x6F47, 0x6F4F, 0x6F48, 0x6F49, 0x6F4A, 0x4742,
    0x6F71, 0x364D, 0x6F4B, 0,      0x6F4C, 0x6F4D, 0x3646, 0x433E, 0x6F4E,
    0,      0x6F50, 0x6F51, 0x6F52, 0x5572, 0,      0x6F53, 0x4477, 0,
    0x6F54, 0x4478, 0x6F55, 0x6F56, 0x3864, 0x3077, 0x6F57, 0x6F58, 0x6F59,
    0,      0x6F5A, 0x6F5B, 0x6F5C, 0x6F5D, 0,      0x6F5E, 0x3E35, 0x6F61,
    0x6F5F, 0x6F60, 0,      0x6F62, 0x6F63, 0x414D, 0x6F64, 0x6F65, 0x6F66,
    0x6F67, 0x6F68, 0x6F69, 0x6F6A, 0x6F6B, 0x6F6C, 0x4058, 0,      0x6F6D,
    0x412D, 0x6F6E, 0x6F6F, 0x6F70, 0,      0,      0x4F62, 0,      0,
    0,      0,      0,      0,      0,      0,      0x3324, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x4345, 0x6345, 0x4941, 0x6346, 0,      0x3155,
    0x4E4A, 0x3433, 0x4872, 0x6347, 0x4F50, 0x6348, 0x3C64, 0x6349, 0x634A,
    0x4346, 0x5522, 0x4456, 0x396B, 0x4E45, 0x634B, 0x4376, 0x634C, 0,
    0x3727, 0x3873, 0x3A52, 0x634D, 0x634E, 0x5444, 0x634F, 0,      0x6350,
    0x514B, 0x6351, 0x6352, 0x6353, 0x6354, 0x5156, 0x6355, 0x327B, 0x403B,
    0x6356, 0,      0x402B, 0x6357, 0x6358, 0x6359, 0,      0x635A, 0x635B,
    0,      0x3837, 0x5A62, 0,      0x3653, 0,      0x5A64, 0x5A63, 0,
    0,      0,      0,      0,      0,      0,      0x5A66, 0,      0,
    0,      0x486E, 0,      0,      0x5A65, 0x3740, 0x5174, 0x5275, 0x5573,
    0x3D57, 0,      0,      0,      0,      0x5768, 0x5A68, 0x5A67, 0,
    0x3022, 0x4D53, 0,      0x5A69, 0,      0x383D, 0x3C4A, 0x423D, 0x4224,
    0x3342, 0x5A6A, 0,      0x422A, 0x4430, 0x3D35, 0,      0,      0x4F5E,
    0,      0,      0,      0x5A6B, 0x4942, 0,      0,      0,      0,
    0,      0x315D, 0,      0,      0,      0x5A6C, 0,      0x3638, 0x543A,
    0,      0x337D, 0,      0,      0x5A6D, 0x5449, 0x4F55, 0x4563, 0,
    0x5A6E, 0,      0,      0,      0,      0,      0x5A6F, 0,      0x5A70,
    0x416A, 0x4C55, 0x4F5D, 0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x5367, 0x4221,
    0,      0x5A71, 0,      0,      0x4B65, 0,      0x5A72, 0,      0x4B66,
    0x527E, 0,      0,      0,      0x3874, 0,      0,      0x5A73, 0x302F,
    0x4F36, 0,      0,      0x554F, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x4B6D, 0,      0,      0,
    0,      0,      0,      0,      0,      0x5A74, 0,      0,      0x6344,
    0,      0,      0x4125, 0,      0,      0x763F, 0,      0,      0x7640,
    0x7641, 0x4451, 0,      0x4838, 0x5163, 0,      0,      0x505B, 0x5145,
    0x3C2F, 0x394D, 0,      0x6F74, 0,      0,      0x3446, 0x533A, 0x7642,
    0x337B, 0,      0,      0x7643, 0,      0,      0x3571, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x7645,
    0,      0,      0,      0,      0,      0,      0,      0x536A, 0x7627,
    0x5129, 0,      0,      0,      0,      0x7629, 0,      0,      0,
    0x7628, 0,      0,      0x4163, 0x4057, 0,      0x3122, 0,      0,
    0,      0,      0x4E6D, 0,      0x5068, 0x762B, 0,      0,      0x4F76,
    0,      0x762A, 0x5570, 0x762C, 0x4339, 0,      0,      0,      0x3B74,
    0x762E, 0x762D, 0,      0,      0,      0x445E, 0,      0,      0x4158,
    0,      0,      0,      0,      0,      0x4B2A, 0,      0x4F3C, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x762F, 0,      0,      0x7630, 0,      0,      0x7631, 0,
    0x4236, 0,      0,      0,      0,      0,      0x3054, 0x4579, 0,
    0,      0,      0,      0x7632, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x4760, 0x7626, 0,      0,
    0x3E38, 0,      0,      0x3E32, 0,      0x3565, 0,      0,      0x3747,
    0,      0x3F3F, 0x4352, 0x4366, 0,      0,      0x584C, 0,      0,
    0,      0x386F, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x3D79, 0x5125, 0,      0x3050, 0,      0,      0,
    0,      0,      0x7730, 0,      0,      0,      0,      0,      0,
    0,      0,      0x7731, 0,      0,      0,      0,      0,      0x502C,
    0,      0x3030, 0,      0,      0,      0x7732, 0x7733, 0,      0x7734,
    0,      0,      0,      0x474A, 0,      0,      0,      0,      0,
    0,      0,      0x3E4F, 0,      0,      0x7737, 0,      0,      0,
    0,      0,      0,      0,      0x7736, 0,      0x315E, 0,      0x7735,
    0,      0,      0x7738, 0,      0x7739, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x4E24, 0x484D, 0,      0x3A2B, 0x6838, 0x6839, 0x683A, 0x3E42, 0,
    0,      0,      0,      0,      0x5274, 0,      0x544F, 0x4958, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x5233,
    0x3625, 0x476A, 0x717C, 0x4F6E, 0x4B33, 0x506B, 0x676F, 0x4D67, 0x394B,
    0x3659, 0x717D, 0x3064, 0x4B4C, 0x717E, 0x5424, 0x422D, 0x416C, 0x4644,
    0x3E31, 0x7221, 0x3C55, 0,      0x7222, 0x7223, 0,      0x7224, 0x5243,
    0x4635, 0,      0x4D47, 0x7225, 0,      0x5331, 0x3F45, 0x4C62, 0,
    0x7226, 0x7227, 0x5155, 0x366E, 0x7228, 0x7229, 0x355F, 0x722A, 0x722B,
    0,      0x327C, 0x722C, 0x722D, 0x4827, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x3767, 0,
    0,      0x6C29, 0x6C2A, 0x6C2B, 0,      0x6C2C, 0,      0,      0x462E,
    0x6C2D, 0x6C2E, 0,      0,      0,      0x3749, 0x4A33, 0,      0,
    0,      0,      0,      0,      0,      0x6238, 0x774F, 0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x7750, 0,
    0,      0x324D, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x7751, 0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x7753, 0x7752, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0x623B, 0,      0x3C22, 0,      0x623C, 0x623D,
    0x623E, 0x623F, 0x6240, 0x6241, 0x3739, 0x527B, 0x3D24, 0x4A4E, 0x3125,
    0x4B47, 0,      0x6242, 0x367C, 0x4844, 0x6243, 0,      0,      0x3D48,
    0,      0x317D, 0x6244, 0,      0x3676, 0x6245, 0x4459, 0,      0,
    0x6246, 0x4F5A, 0x395D, 0x6247, 0x4021, 0,      0x6248, 0x3276, 0,
    0x6249, 0,      0x4173, 0x624A, 0x624B, 0x4278, 0x624C, 0x624D, 0x624E,
    0x4A57, 0x5838, 0x5965, 0x4F63, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x7025, 0,      0,
    0x5C30, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x426D, 0x5426,
    0x4D54, 0x5131, 0x335B, 0x477D, 0,      0x3235, 0x423F, 0x6660, 0x4A3B,
    0x6661, 0x6662, 0x3E54, 0x6663, 0x5724, 0x4D55, 0x6665, 0x3C5D, 0x6664,
    0x6666, 0x6667, 0x426E, 0,      0x3D3E, 0x6668, 0x4266, 0x3A27, 0x6669,
    0,      0x666A, 0x3352, 0x5169, 0,      0,      0x3F25, 0x666B, 0x466F,
    0x666C, 0x666D, 0,      0,      0x666E, 0x462D, 0x666F, 0,      0x4927,
    0x6670, 0x6671, 0x6672, 0x6539, 0x6673, 0x6674, 0x4262, 0x6675, 0x6676,
    0x5668, 0x6677, 0,      0x6678, 0x3947, 0,      0,      0,      0,
    0,      0,      0,      0x773B, 0x773A, 0,      0,      0,      0,
    0x773E, 0x773C, 0x3A21, 0,      0x773F, 0,      0x7740, 0,      0,
    0,      0x7742, 0x7741, 0x7744, 0,      0,      0x7743, 0,      0,
    0,      0,      0,      0x7745, 0x7746, 0,      0,      0,      0,
    0x7747, 0,      0x4B68, 0,      0,      0,      0,      0x385F, 0,
    0,      0,      0,      0,      0,      0x7754, 0,      0x7755, 0,
    0,      0,      0,      0x7756, 0,      0,      0,      0,      0x7758,
    0,      0x775A, 0,      0x7757, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x775B, 0,      0x7759, 0,      0,
    0,      0,      0,      0,      0,      0x5757, 0,      0,      0,
    0,      0x775C, 0,      0,      0,      0,      0,      0,      0x775D,
    0,      0,      0,      0x775E, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0x775F, 0,      0,
    0,      0x7760, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0x5B4B, 0,      0,      0x582A, 0,
    0,      0,      0,      0,      0,      0,      0,      0x6577, 0x396D,
    0,      0,      0,      0,      0x3F7D, 0x3B6A, 0x7749, 0x4647, 0x7748,
    0,      0x774A, 0x774C, 0x774B, 0,      0,      0,      0x774D, 0,
    0x4E3A, 0,      0x774E, 0,      0,      0x4427};

/* page 7 0x9C7C-0x9CE2 */
static const uint16_t tab_uni_gb23127[] = {
    0x5363, 0,      0,      0x764F, 0,      0x4233, 0x7650, 0,      0,
    0x7651, 0x7652, 0x7653, 0x7654, 0,      0,      0x7656, 0,      0x312B,
    0x7657, 0,      0x7658, 0x7659, 0x765A, 0,      0x765B, 0x765C, 0,
    0,      0,      0,      0x765D, 0x765E, 0x4F4A, 0,      0x765F, 0x7660,
    0x7661, 0x7662, 0x7663, 0x7664, 0x4070, 0x7665, 0x7666, 0x7667, 0x7668,
    0x7669, 0,      0x766A, 0,      0x766B, 0x766C, 0,      0x766D, 0x766E,
    0x766F, 0x7670, 0x7671, 0x7672, 0x7673, 0x7674, 0x3E28, 0,      0x7675,
    0x7676, 0x7677, 0x7678, 0,      0,      0,      0,      0,      0x487A,
    0x7679, 0x767A, 0x767B, 0x767C, 0,      0,      0x767D, 0x767E, 0x7721,
    0x7722, 0x7723, 0x7724, 0x7725, 0,      0,      0x7726, 0x7727, 0x7728,
    0x316E, 0x7729, 0x772A, 0x772B, 0,      0,      0x772C, 0x772D, 0x415B,
    0x772E, 0,      0,      0x772F};

/* page 8 0x9E1F-0x9FA0 */
static const uint16_t tab_uni_gb23128[] = {
    0x4471, 0x702F, 0x3C26, 0x7030, 0x4379, 0,      0x4538, 0x513B, 0,
    0x7031, 0x7032, 0x7033, 0x7034, 0x7035, 0x513C, 0,      0x516C, 0,
    0x7037, 0x7036, 0x5427, 0,      0x4D52, 0x7038, 0x703A, 0x7039, 0x703B,
    0x703C, 0,      0,      0x386B, 0x703D, 0x3A68, 0,      0x703E, 0x703F,
    0x3E69, 0x7040, 0x366C, 0x7041, 0x7042, 0x7043, 0x7044, 0x4835, 0x7045,
    0x7046, 0,      0x7047, 0x4574, 0,      0x7048, 0,      0,      0,
    0x7049, 0,      0x704A, 0x773D, 0,      0x704B, 0x704C, 0x704D, 0,
    0x704E, 0,      0,      0,      0,      0x704F, 0x3A57, 0,      0x7050,
    0x7051, 0x7052, 0x7053, 0x7054, 0x7055, 0x7056, 0x7058, 0,      0,
    0x5325, 0x7057, 0,      0x7059, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x753A, 0x4239, 0,      0,
    0x7764, 0,      0,      0,      0,      0x7765, 0x7766, 0,      0,
    0x7767, 0,      0,      0,      0,      0,      0,      0x7768, 0x4234,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x776A, 0,      0x776B, 0,      0,      0,      0,      0,      0,
    0x4273, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0x7470, 0,      0,      0,
    0x746F, 0,      0,      0x4269, 0,      0x7761, 0x7762, 0,      0,
    0,      0,      0,      0x3B46, 0,      0,      0,      0,      0x5964,
    0,      0,      0,      0x4A72, 0x4068, 0x7024, 0,      0x3A5A, 0,
    0,      0x472D, 0,      0,      0,      0x442C, 0,      0,      0x776C,
    0x776D, 0x776E, 0,      0x7770, 0x776F, 0,      0x7771, 0,      0,
    0x7774, 0,      0x7773, 0,      0x7772, 0x7775, 0,      0,      0,
    0,      0x7776, 0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x6D69, 0,      0x6D6A, 0x6D6B, 0,      0x763C, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0x763D, 0,      0x763E, 0x3626, 0,      0x583E, 0,
    0,      0x3944, 0,      0,      0,      0x583B, 0,      0x5C31, 0,
    0,      0,      0,      0,      0,      0x4A73, 0,      0x7777, 0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x7778,
    0,      0,      0x7779, 0,      0,      0,      0,      0,      0,
    0,      0x777B, 0,      0x777A, 0,      0x3147, 0,      0x777C, 0x777D,
    0,      0,      0,      0,      0,      0x777E, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0x466B,
    0x6C34, 0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0x335D, 0x7633, 0,      0,      0x7634, 0x4164, 0x7635, 0x7636,
    0x7637, 0x7638, 0x7639, 0x763A, 0x4823, 0x763B, 0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0x417A, 0x3928, 0x6D68, 0,      0,      0,      0x396A, 0x595F};

/* page 9 0xFF01-0xFFE5 */
static const uint16_t tab_uni_gb23129[] = {
    0x2321, 0x2322, 0x2323, 0x2167, 0x2325, 0x2326, 0x2327, 0x2328, 0x2329,
    0x232A, 0x232B, 0x232C, 0x232D, 0x232E, 0x232F, 0x2330, 0x2331, 0x2332,
    0x2333, 0x2334, 0x2335, 0x2336, 0x2337, 0x2338, 0x2339, 0x233A, 0x233B,
    0x233C, 0x233D, 0x233E, 0x233F, 0x2340, 0x2341, 0x2342, 0x2343, 0x2344,
    0x2345, 0x2346, 0x2347, 0x2348, 0x2349, 0x234A, 0x234B, 0x234C, 0x234D,
    0x234E, 0x234F, 0x2350, 0x2351, 0x2352, 0x2353, 0x2354, 0x2355, 0x2356,
    0x2357, 0x2358, 0x2359, 0x235A, 0x235B, 0x235C, 0x235D, 0x235E, 0x235F,
    0x2360, 0x2361, 0x2362, 0x2363, 0x2364, 0x2365, 0x2366, 0x2367, 0x2368,
    0x2369, 0x236A, 0x236B, 0x236C, 0x236D, 0x236E, 0x236F, 0x2370, 0x2371,
    0x2372, 0x2373, 0x2374, 0x2375, 0x2376, 0x2377, 0x2378, 0x2379, 0x237A,
    0x237B, 0x237C, 0x237D, 0x212B, 0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0,      0,
    0,      0,      0,      0,      0,      0,      0,      0x2169, 0x216A,
    0,      0x237E, 0,      0x2324};

static int func_uni_gb2312_onechar(int code) {
  if ((code >= 0x00A4) && (code <= 0x01DC))
    return (tab_uni_gb23120[code - 0x00A4]);
  if ((code >= 0x02C7) && (code <= 0x0451))
    return (tab_uni_gb23121[code - 0x02C7]);
  if ((code >= 0x2015) && (code <= 0x2312))
    return (tab_uni_gb23122[code - 0x2015]);
  if ((code >= 0x2460) && (code <= 0x2642))
    return (tab_uni_gb23123[code - 0x2460]);
  if ((code >= 0x3000) && (code <= 0x3129))
    return (tab_uni_gb23124[code - 0x3000]);
  if ((code >= 0x3220) && (code <= 0x3229))
    return (tab_uni_gb23125[code - 0x3220]);
  if ((code >= 0x4E00) && (code <= 0x9B54))
    return (tab_uni_gb23126[code - 0x4E00]);
  if ((code >= 0x9C7C) && (code <= 0x9CE2))
    return (tab_uni_gb23127[code - 0x9C7C]);
  if ((code >= 0x9E1F) && (code <= 0x9FA0))
    return (tab_uni_gb23128[code - 0x9E1F]);
  if ((code >= 0xFF01) && (code <= 0xFFE5))
    return (tab_uni_gb23129[code - 0xFF01]);
  return (0);
}

extern "C" {
static int my_wc_mb_gb2312(const CHARSET_INFO *cs [[maybe_unused]], my_wc_t wc,
                           uint8_t *s, uint8_t *e) {
  int code;

  if (s >= e) return MY_CS_TOOSMALL;

  if ((unsigned)wc < 0x80) {
    s[0] = (uint8_t)wc;
    return 1;
  }

  if (!(code = func_uni_gb2312_onechar(wc))) return MY_CS_ILUNI;

  if (s + 2 > e) return MY_CS_TOOSMALL2;

  code |= 0x8080;
  s[0] = code >> 8;
  s[1] = code & 0xFF;
  return 2;
}

static int my_mb_wc_gb2312(const CHARSET_INFO *cs [[maybe_unused]],
                           my_wc_t *pwc, const uint8_t *s, const uint8_t *e) {
  int hi;

  if (s >= e) return MY_CS_TOOSMALL;

  if ((hi = s[0]) < 0x80) {
    pwc[0] = hi;
    return 1;
  }

  if (s + 2 > e) return MY_CS_TOOSMALL2;

  if (!(pwc[0] = func_gb2312_uni_onechar(((hi << 8) + s[1]) & 0x7F7F)))
    return -2;

  return 2;
}

/*
  Returns well formed length of a EUC-KR string.
*/
static size_t my_well_formed_len_gb2312(const CHARSET_INFO *cs [[maybe_unused]],
                                        const char *b, const char *e,
                                        size_t pos, int *error) {
  const char *b0 = b;
  const char *emb = e - 1; /* Last possible end of an MB character */

  *error = 0;
  while (pos-- && b < e) {
    if ((uint8_t)b[0] < 128) {
      /* Single byte ascii character */
      b++;
    } else if (b < emb && isgb2312head(*b) && isgb2312tail(b[1])) {
      /* Double byte character */
      b += 2;
    } else {
      /* Wrong byte sequence */
      *error = 1;
      break;
    }
  }
  return (size_t)(b - b0);
}
}  // extern "C"

static MY_COLLATION_HANDLER my_collation_ci_handler = {
    nullptr, /* init */
    nullptr,
    my_strnncoll_simple, /* strnncoll  */
    my_strnncollsp_simple,
    my_strnxfrm_mb, /* strnxfrm   */
    my_strnxfrmlen_simple,
    my_like_range_mb, /* like_range */
    my_wildcmp_mb,    /* wildcmp    */
    my_strcasecmp_mb, /* instr      */
    my_instr_mb,
    my_hash_sort_simple,
    my_propagate_simple};

static MY_CHARSET_HANDLER my_charset_handler = {nullptr, /* init */
                                                ismbchar_gb2312,
                                                mbcharlen_gb2312,
                                                my_numchars_mb,
                                                my_charpos_mb3,
                                                my_well_formed_len_gb2312,
                                                my_lengthsp_8bit,
                                                my_numcells_8bit,
                                                my_mb_wc_gb2312, /* mb_wc */
                                                my_wc_mb_gb2312, /* wc_mb */
                                                my_mb_ctype_mb,
                                                my_caseup_str_mb,
                                                my_casedn_str_mb,
                                                my_caseup_mb,
                                                my_casedn_mb,
                                                my_snprintf_8bit,
                                                my_long10_to_str_8bit,
                                                my_longlong10_to_str_8bit,
                                                my_fill_8bit,
                                                my_strntol_8bit,
                                                my_strntoul_8bit,
                                                my_strntoll_8bit,
                                                my_strntoull_8bit,
                                                my_strntod_8bit,
                                                my_strtoll10_8bit,
                                                my_strntoull10rnd_8bit,
                                                my_scan_8bit};

CHARSET_INFO my_charset_gb2312_chinese_ci = {
    24,
    0,
    0,                              /* number */
    MY_CS_COMPILED | MY_CS_PRIMARY, /* state      */
    "gb2312",                       /* cs name    */
    "gb2312_chinese_ci",            /* m_coll_name */
    "GB2312 Simplified Chinese",    /* comment    */
    nullptr,                        /* tailoring */
    nullptr,                        /* coll_param */
    ctype_gb2312,
    to_lower_gb2312,
    to_upper_gb2312,
    sort_order_gb2312,
    nullptr,             /* uca          */
    nullptr,             /* tab_to_uni   */
    nullptr,             /* tab_from_uni */
    &my_caseinfo_gb2312, /* caseinfo     */
    nullptr,             /* state_map    */
    nullptr,             /* ident_map    */
    1,                   /* strxfrm_multiply */
    1,                   /* caseup_multiply  */
    1,                   /* casedn_multiply  */
    1,                   /* mbminlen   */
    2,                   /* mbmaxlen   */
    1,                   /* mbmaxlenlen */
    0,                   /* min_sort_char */
    0xF7FE,              /* max_sort_char */
    ' ',                 /* pad char      */
    false,               /* escape_with_backslash_is_dangerous */
    1,                   /* levels_for_compare */
    &my_charset_handler,
    &my_collation_ci_handler,
    PAD_SPACE};

CHARSET_INFO my_charset_gb2312_bin = {
    86,
    0,
    0,                              /* number */
    MY_CS_COMPILED | MY_CS_BINSORT, /* state      */
    "gb2312",                       /* cs name    */
    "gb2312_bin",                   /* m_coll_name */
    "GB2312 Simplified Chinese",    /* comment    */
    nullptr,                        /* tailoring */
    nullptr,                        /* coll_param */
    ctype_gb2312,
    to_lower_gb2312,
    to_upper_gb2312,
    nullptr,             /* sort_order   */
    nullptr,             /* uca          */
    nullptr,             /* tab_to_uni   */
    nullptr,             /* tab_from_uni */
    &my_caseinfo_gb2312, /* caseinfo     */
    nullptr,             /* state_map    */
    nullptr,             /* ident_map    */
    1,                   /* strxfrm_multiply */
    1,                   /* caseup_multiply  */
    1,                   /* casedn_multiply  */
    1,                   /* mbminlen   */
    2,                   /* mbmaxlen   */
    1,                   /* mbmaxlenlen */
    0,                   /* min_sort_char */
    0xF7FE,              /* max_sort_char */
    ' ',                 /* pad char      */
    false,               /* escape_with_backslash_is_dangerous */
    1,                   /* levels_for_compare */
    &my_charset_handler,
    &my_collation_mb_bin_handler,
    PAD_SPACE};
