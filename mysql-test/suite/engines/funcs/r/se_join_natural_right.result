DROP TABLE IF EXISTS t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 < 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 < 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 < 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 < 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 < 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 < 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 < 5;
c1	c1
1	1
3	3
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 < 5;
c1	c1
1	1
3	3
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 < 5;
c1	c1
1	1
3	3
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 < 5;
c1	c1
1	1
3	3
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 < 5;
c1	c1
1	1
3	3
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 < 5;
c1	c1
1	1
3	3
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 < 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <= 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <= 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <= 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <= 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <= 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <= 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <= 5;
c1	c1
1	1
3	3
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <= 5;
c1	c1
1	1
3	3
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <= 5;
c1	c1
1	1
3	3
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <= 5;
c1	c1
1	1
3	3
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <= 5;
c1	c1
1	1
3	3
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <= 5;
c1	c1
1	1
3	3
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 = 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 = 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 = 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 = 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 = 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 = 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 = 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 = 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 = 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 = 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 = 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 = 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 = 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 >= 5;
c1	c1
5	5
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 >= 5;
c1	c1
5	5
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 >= 5;
c1	c1
5	5
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 >= 5;
c1	c1
5	5
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 >= 5;
c1	c1
5	5
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 >= 5;
c1	c1
5	5
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 >= 5;
c1	c1
5	5
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 >= 5;
c1	c1
5	5
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 >= 5;
c1	c1
5	5
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 >= 5;
c1	c1
5	5
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 >= 5;
c1	c1
5	5
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 >= 5;
c1	c1
5	5
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 >= 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 != 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 != 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 != 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 != 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 != 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 != 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 != 5;
c1	c1
1	1
3	3
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 != 5;
c1	c1
1	1
3	3
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 != 5;
c1	c1
1	1
3	3
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 != 5;
c1	c1
1	1
3	3
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 != 5;
c1	c1
1	1
3	3
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 != 5;
c1	c1
1	1
3	3
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 != 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <> 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <> 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <> 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <> 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <> 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <> 5;
c1	c1
0	0
1	1
2	2
3	3
4	4
6	6
7	7
8	8
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <> 5;
c1	c1
1	1
3	3
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <> 5;
c1	c1
1	1
3	3
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <> 5;
c1	c1
1	1
3	3
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <> 5;
c1	c1
1	1
3	3
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <> 5;
c1	c1
1	1
3	3
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <> 5;
c1	c1
1	1
3	3
7	7
9	9
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <=> 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <=> 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <=> 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <=> 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <=> 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2;
c1	c1
0	0
1	1
2	2
3	3
4	4
5	5
6	6
7	7
8	8
9	9
SELECT t1.c1,t2.c1 FROM t1 NATURAL RIGHT JOIN t2  WHERE t1.c1 <=> 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <=> 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <=> 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <=> 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <=> 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <=> 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3;
c1	c1
1	1
3	3
5	5
7	7
9	9
SELECT t1.c1,t3.c1 FROM t1 NATURAL RIGHT JOIN t3  WHERE t1.c1 <=> 5;
c1	c1
5	5
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4;
c1	c1
NULL	10
SELECT t1.c1,t4.c1 FROM t1 NATURAL RIGHT JOIN t4  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5;
c1	c1
NULL	10
NULL	11
NULL	12
NULL	13
NULL	14
SELECT t1.c1,t5.c1 FROM t1 NATURAL RIGHT JOIN t5  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 TINYINT NOT NULL);
CREATE TABLE t2 (c1 TINYINT NOT NULL);
CREATE TABLE t3 (c1 TINYINT NOT NULL);
CREATE TABLE t4 (c1 TINYINT NOT NULL);
CREATE TABLE t5 (c1 TINYINT NOT NULL);
CREATE TABLE t6 (c1 TINYINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 SMALLINT NOT NULL);
CREATE TABLE t2 (c1 SMALLINT NOT NULL);
CREATE TABLE t3 (c1 SMALLINT NOT NULL);
CREATE TABLE t4 (c1 SMALLINT NOT NULL);
CREATE TABLE t5 (c1 SMALLINT NOT NULL);
CREATE TABLE t6 (c1 SMALLINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t2 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t3 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t4 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t5 (c1 MEDIUMINT NOT NULL);
CREATE TABLE t6 (c1 MEDIUMINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INT NOT NULL);
CREATE TABLE t2 (c1 INT NOT NULL);
CREATE TABLE t3 (c1 INT NOT NULL);
CREATE TABLE t4 (c1 INT NOT NULL);
CREATE TABLE t5 (c1 INT NOT NULL);
CREATE TABLE t6 (c1 INT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 INTEGER NOT NULL);
CREATE TABLE t2 (c1 INTEGER NOT NULL);
CREATE TABLE t3 (c1 INTEGER NOT NULL);
CREATE TABLE t4 (c1 INTEGER NOT NULL);
CREATE TABLE t5 (c1 INTEGER NOT NULL);
CREATE TABLE t6 (c1 INTEGER NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (c1 BIGINT NOT NULL);
CREATE TABLE t2 (c1 BIGINT NOT NULL);
CREATE TABLE t3 (c1 BIGINT NOT NULL);
CREATE TABLE t4 (c1 BIGINT NOT NULL);
CREATE TABLE t5 (c1 BIGINT NOT NULL);
CREATE TABLE t6 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t3 (c1) VALUES(1),(3),(5),(7),(9);
INSERT INTO t4 (c1) VALUES(10);
INSERT INTO t5 (c1) VALUES(10),(11),(12),(13),(14);
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6;
c1	c1
SELECT t1.c1,t6.c1 FROM t1 NATURAL RIGHT JOIN t6  WHERE t1.c1 <=> 5;
c1	c1
DROP TABLE t1,t2,t3,t4,t5,t6;
