###############################################################################
# WL#11512
# Perform some simple tests about getting/setting write_concurrency.
#
# This test uses 2 servers M1 and M2
# M1 -> server1,  M2 -> server2
#
# Test:
# 1. Start 2 servers and add them into group replication group
# 2. Default value on M1, M2
# 3. Increase write_concurrency via M1, confirm on M1, M2
# 4. Decrease write_concurrency via M2, confirm on M1, M2
# 5. Invalid calls to group_replication_get_write_concurrency on M1
# 6. Invalid calls to group_replication_set_write_concurrency on M1
# 7. Check that it is not possible to modify/query write_concurrency value when
#    a server is in RECOVERING state.
# 8. Check that it is not possible to update wc value when a member is in ERROR
#    state.
# 9. Stop GR and try to call group_replication_{get,set}_write_concurrency
#    outside of a group
################################################################################
--source include/have_debug_sync.inc
--source include/have_group_replication_plugin.inc
--let $rpl_server_count= 2
--source include/group_replication.inc

# Create table with integers to test UDF further down.
CREATE TABLE t1 (i INT, v INT, PRIMARY KEY (i));
INSERT INTO t1 VALUES (1, NULL);
INSERT INTO t1 VALUES (2, 9);
INSERT INTO t1 VALUES (3, 42);
INSERT INTO t1 VALUES (4, 201);

--let $gr_write_concurrency_default= 10

--echo ###############################################################################
--echo # 2. Confirm default value on M1, M2
--let $rpl_connection_name= server1
--source include/connection.inc

--let $assert_text = write_concurrency is supposed to be $gr_write_concurrency_default by default
--let $assert_cond = [SELECT group_replication_get_write_concurrency()] = $gr_write_concurrency_default
--source include/assert.inc

--let $write_concurrency_table= query_get_value(SELECT write_concurrency FROM performance_schema.replication_group_communication_information, write_concurrency, 1)
--let $assert_text= write_concurrency ($write_concurrency_table) should be $gr_write_concurrency_default
--let $assert_cond= "$write_concurrency_table" = "$gr_write_concurrency_default"
--source include/assert.inc

--let $rpl_connection_name= server2
--source include/connection.inc
SET SESSION sql_log_bin = 0;
call mtr.add_suppression("Could not execute Write_rows event on table test.t2; Duplicate entry '11'*");
call mtr.add_suppression("The applier thread execution was aborted. Unable to process more transactions, this member will now leave the group.");
call mtr.add_suppression("Fatal error during execution on the Applier process of Group Replication. The server will now leave the group.");
call mtr.add_suppression("The server was automatically set into read only mode after an error was detected.");
call mtr.add_suppression("The server was automatically set into offline mode after an error was detected.");
call mtr.add_suppression("Skipping leave operation: concurrent attempt to leave the group is on-going.");
call mtr.add_suppression(".*Replica SQL for channel 'group_replication_applier': ... The replica coordinator and worker threads are stopped, possibly leaving data in inconsistent state*");
SET SESSION sql_log_bin = 1;

--let $assert_text = write_concurrency is supposed to be $gr_write_concurrency_default by default
--let $assert_cond = [SELECT group_replication_get_write_concurrency()] = $gr_write_concurrency_default
--source include/assert.inc

--let $write_concurrency_table= query_get_value(SELECT write_concurrency FROM performance_schema.replication_group_communication_information, write_concurrency, 1)
--let $assert_text= write_concurrency ($write_concurrency_table) should be $gr_write_concurrency_default
--let $assert_cond= "$write_concurrency_table" = "$gr_write_concurrency_default"
--source include/assert.inc

--echo ###############################################################################
--echo # 3. Increase write_concurrency via M1, confirm on M1, M2
--let $gr_new_write_concurrency= 42

--let $rpl_connection_name= server1
--source include/connection.inc

--eval SELECT group_replication_set_write_concurrency($gr_new_write_concurrency)

--let $wait_condition= SELECT group_replication_get_write_concurrency() = $gr_new_write_concurrency
--source include/wait_condition.inc

--let $write_concurrency_table= query_get_value(SELECT write_concurrency FROM performance_schema.replication_group_communication_information, write_concurrency, 1)
--let $assert_text= write_concurrency ($write_concurrency_table) should be $gr_new_write_concurrency
--let $assert_cond= "$write_concurrency_table" = "$gr_new_write_concurrency"
--source include/assert.inc

--let $rpl_connection_name= server2
--source include/connection.inc

--let $wait_condition= SELECT group_replication_get_write_concurrency() = $gr_new_write_concurrency
--source include/wait_condition.inc

--let $write_concurrency_table= query_get_value(SELECT write_concurrency FROM performance_schema.replication_group_communication_information, write_concurrency, 1)
--let $assert_text= write_concurrency ($write_concurrency_table) should be $gr_new_write_concurrency
--let $assert_cond= "$write_concurrency_table" = "$gr_new_write_concurrency"
--source include/assert.inc

--echo ###############################################################################
--echo # 4. Decrease write_concurrency via M2, confirm on M1, M2
--let $gr_new_write_concurrency= 10

--eval SELECT group_replication_set_write_concurrency($gr_new_write_concurrency)

--let $wait_condition= SELECT group_replication_get_write_concurrency() = $gr_new_write_concurrency
--source include/wait_condition.inc

--let $write_concurrency_table= query_get_value(SELECT write_concurrency FROM performance_schema.replication_group_communication_information, write_concurrency, 1)
--let $assert_text= write_concurrency ($write_concurrency_table) should be $gr_new_write_concurrency
--let $assert_cond= "$write_concurrency_table" = "$gr_new_write_concurrency"
--source include/assert.inc

--let $rpl_connection_name= server1
--source include/connection.inc

--let $wait_condition= SELECT group_replication_get_write_concurrency() = $gr_new_write_concurrency
--source include/wait_condition.inc

--let $write_concurrency_table= query_get_value(SELECT write_concurrency FROM performance_schema.replication_group_communication_information, write_concurrency, 1)
--let $assert_text= write_concurrency ($write_concurrency_table) should be $gr_new_write_concurrency
--let $assert_cond= "$write_concurrency_table" = "$gr_new_write_concurrency"
--source include/assert.inc

--echo ###############################################################################
--echo # 5. Confirm invalid calls to group_replication_get_write_concurrency
--error ER_CANT_INITIALIZE_UDF
SELECT group_replication_get_write_concurrency(1);

--error ER_CANT_INITIALIZE_UDF
SELECT group_replication_get_write_concurrency(1.1);

--error ER_CANT_INITIALIZE_UDF
SELECT group_replication_get_write_concurrency("hehe");

--error ER_CANT_INITIALIZE_UDF
SELECT group_replication_get_write_concurrency(NULL);

--echo ###############################################################################
--echo # 6. Confirm invalid calls to group_replication_set_write_concurrency
--error ER_CANT_INITIALIZE_UDF
SELECT group_replication_set_write_concurrency();

--error ER_CANT_INITIALIZE_UDF
SELECT group_replication_set_write_concurrency(NULL);

--error ER_CANT_INITIALIZE_UDF
SELECT group_replication_set_write_concurrency(1.1);

--error ER_CANT_INITIALIZE_UDF
SELECT group_replication_set_write_concurrency("hehe");

# out of [10, 200] domain
--error ER_CANT_INITIALIZE_UDF
SELECT group_replication_set_write_concurrency(9);

--error ER_CANT_INITIALIZE_UDF
SELECT group_replication_set_write_concurrency(4097);

# With dynamic arguments, the error returned is ER_GRP_RPL_UDF_ERROR

# NULL
--error ER_GRP_RPL_UDF_ERROR
SELECT group_replication_set_write_concurrency(v) FROM test.t1 WHERE i=1;

# 9
--error ER_GRP_RPL_UDF_ERROR
SELECT group_replication_set_write_concurrency(v) FROM test.t1 WHERE i=2;

# 42
SELECT group_replication_set_write_concurrency(v) FROM test.t1 WHERE i=3;

# 201
--error ER_GRP_RPL_UDF_ERROR
SELECT group_replication_set_write_concurrency(v) FROM test.t1 WHERE i=4;

# Stop GR on M2
--let $rpl_connection_name= server2
--source include/connection.inc
--source include/stop_group_replication.inc

--echo ###############################################################################
--echo # 7. Check that it is not possible to modify/query write_concurrency value when server is in RECOVERING state.
# Start GR on M2, It should be in RECOVERING state.
--let $rpl_connection_name= server2
--source include/connection.inc
SET @@GLOBAL.DEBUG='+d,recovery_thread_wait_before_finish';

--let $group_replication_start_member_state= RECOVERING
--source include/start_group_replication.inc

--let $gr_new_write_concurrency= 111
# Since M2 is in RECOVERY state, it shouldn't allow to modify write concurrency value.
--error ER_CANT_INITIALIZE_UDF
--eval SELECT group_replication_set_write_concurrency($gr_new_write_concurrency)

# Check that it is not possible to query write_concurrency value either.
--error ER_CANT_INITIALIZE_UDF
--eval SELECT group_replication_get_write_concurrency()

--echo ###############################################################################
--echo # 8. Check that it is not possible to update wc value when a member is in ERROR state.
--let $rpl_connection_name= server2
--source include/connection.inc
SET DEBUG_SYNC= "now WAIT_FOR signal.recovery_thread_wait_before_finish_reached";
SET @@GLOBAL.DEBUG='-d,recovery_thread_wait_before_finish';
SET DEBUG_SYNC= "now SIGNAL signal.recovery_end";

# Wait until M2 becomes ONLINE
--echo Wait until server2 becomes ONLINE
--let $wait_condition=SELECT COUNT(*)=2 FROM performance_schema.replication_group_members where MEMBER_STATE="ONLINE"
--source include/wait_condition.inc

--let $group_replication_member_state= ONLINE
--source include/gr_wait_for_member_state.inc

SET DEBUG_SYNC= 'RESET';

# Create a table.
CREATE TABLE t2 (a INT PRIMARY KEY);
--source include/rpl/sync.inc

# Insert some data onto t2 with sql_log_bin=0 so that M2 goes to ERROR state.
--let $rpl_connection_name= server2
--source include/connection.inc
SET SESSION sql_log_bin=0;
INSERT INTO t2 values(11);
SET SESSION sql_log_bin=1;

--let $rpl_connection_name= server1
--source include/connection.inc
INSERT INTO t2 values(11);

# Wait until M2 goes to ERROR state.
--let $rpl_connection_name= server2
--source include/connection.inc
--echo wait until server2 goes to ERROR state.
--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.replication_group_members where MEMBER_STATE="ERROR"
--source include/wait_condition.inc

--let $gr_new_write_concurrency= 11
# Since M2 is in ERROR state, it shouldn't allow to modify write concurrency value.
--error ER_CANT_INITIALIZE_UDF
--eval SELECT group_replication_set_write_concurrency($gr_new_write_concurrency)

# Ensure that, on M2 we cannot query WC value
--error ER_CANT_INITIALIZE_UDF
--eval SELECT group_replication_get_write_concurrency()

--source include/stop_group_replication.inc

# Add M2 back to group.
SET SESSION sql_log_bin=0;
DELETE FROM t2;
SET SESSION sql_log_bin=1;
--source include/start_group_replication.inc

DROP TABLE t1;
DROP TABLE t2;
--source include/rpl/sync.inc

--echo ###############################################################################
--echo # 9. Stop GR and try to call group_replication_{get,set}_write_concurrency outside of a group
--let $rpl_connection_name= server1
--source include/connection.inc

--source include/group_replication_end.inc

--error ER_CANT_INITIALIZE_UDF
SELECT group_replication_get_write_concurrency();

--error ER_CANT_INITIALIZE_UDF
SELECT group_replication_set_write_concurrency(10);
