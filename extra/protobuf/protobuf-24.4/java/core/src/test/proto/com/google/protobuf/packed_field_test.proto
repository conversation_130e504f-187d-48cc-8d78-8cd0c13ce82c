// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

package packed_field_test;

option java_package = "com.google.protobuf";
option java_outer_classname = "PackedFieldTestProto";

message TestAllTypes {
  enum NestedEnum {
    FOO = 0;
    BAR = 1;
    BAZ = 2;
  }

  repeated int32 repeated_int32 = 31;
  repeated int64 repeated_int64 = 32;
  repeated uint32 repeated_uint32 = 33;
  repeated uint64 repeated_uint64 = 34;
  repeated sint32 repeated_sint32 = 35;
  repeated sint64 repeated_sint64 = 36;
  repeated fixed32 repeated_fixed32 = 37;
  repeated fixed64 repeated_fixed64 = 38;
  repeated sfixed32 repeated_sfixed32 = 39;
  repeated sfixed64 repeated_sfixed64 = 40;
  repeated float repeated_float = 41;
  repeated double repeated_double = 42;
  repeated bool repeated_bool = 43;
  repeated NestedEnum repeated_nested_enum = 51;
}

message TestUnpackedTypes {
  repeated int32 repeated_int32 = 1 [packed = false];
  repeated int64 repeated_int64 = 2 [packed = false];
  repeated uint32 repeated_uint32 = 3 [packed = false];
  repeated uint64 repeated_uint64 = 4 [packed = false];
  repeated sint32 repeated_sint32 = 5 [packed = false];
  repeated sint64 repeated_sint64 = 6 [packed = false];
  repeated fixed32 repeated_fixed32 = 7 [packed = false];
  repeated fixed64 repeated_fixed64 = 8 [packed = false];
  repeated sfixed32 repeated_sfixed32 = 9 [packed = false];
  repeated sfixed64 repeated_sfixed64 = 10 [packed = false];
  repeated float repeated_float = 11 [packed = false];
  repeated double repeated_double = 12 [packed = false];
  repeated bool repeated_bool = 13 [packed = false];
  repeated TestAllTypes.NestedEnum repeated_nested_enum = 14 [packed = false];
}
