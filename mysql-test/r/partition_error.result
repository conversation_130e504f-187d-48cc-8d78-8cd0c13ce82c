drop table if exists t1, t2;
#
# Bug#60039: crash when exchanging a partition on
#            nonpartitioned table with a view
#
CREATE TABLE t1 (a int);
CREATE OR REPLACE VIEW v1 AS SELECT * FROM t1;
ALTER TABLE t1 EXCHANGE PARTITION p0 WITH TABLE v1;
ERROR 42000: Can't open table
DROP VIEW v1;
DROP TABLE t1;
#
# Bug#57924: crash when creating partitioned table with
#            multiple columns in the partition key
#
CREATE TABLE t1 (a INT, b INT, PRIMARY KEY (a,b))
PARTITION BY KEY(a, b, a);
ERROR HY000: Duplicate partition field name 'a'
CREATE TABLE t1 (a INT, b INT, PRIMARY KEY (a,b))
PARTITION BY KEY(A, b);
DROP TABLE t1;
CREATE TABLE t1 (a INT, b INT, PRIMARY KEY (a,b))
PARTITION BY KEY(a, b, A);
ERROR HY000: Duplicate partition field name 'a'
#
# Bug#54483: valgrind errors when making warnings for multiline inserts
#            into partition
#
CREATE TABLE t1 (a VARBINARY(10))
PARTITION BY RANGE (DAYOFWEEK(a))
(PARTITION a1 VALUES LESS THAN (60));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a CHAR(10))
PARTITION BY RANGE (DAYOFWEEK(a))
(PARTITION a1 VALUES LESS THAN (60));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY RANGE (DAYOFWEEK(a))
(PARTITION a1 VALUES LESS THAN (60));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIMESTAMP)
PARTITION BY RANGE (DAYOFWEEK(a))
(PARTITION a1 VALUES LESS THAN (60));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY RANGE (DAYOFWEEK(a))
(PARTITION a1 VALUES LESS THAN (60));
INSERT IGNORE INTO t1 VALUES ('test'),('a'),('5');
Warnings:
Warning	1292	Incorrect date value: 'test' for column 'a' at row 1
Warning	1292	Incorrect date value: 'a' for column 'a' at row 1
Warning	1265	Data truncated for column 'a' at row 1
Warning	1265	Data truncated for column 'a' at row 2
Warning	1265	Data truncated for column 'a' at row 3
SHOW WARNINGS;
Level	Code	Message
Warning	1292	Incorrect date value: 'test' for column 'a' at row 1
Warning	1292	Incorrect date value: 'a' for column 'a' at row 1
Warning	1265	Data truncated for column 'a' at row 1
Warning	1265	Data truncated for column 'a' at row 2
Warning	1265	Data truncated for column 'a' at row 3
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY RANGE (DAYOFWEEK(a))
(PARTITION a1 VALUES LESS THAN (60));
INSERT IGNORE INTO t1 VALUES ('test'),('a'),('5');
Warnings:
Warning	1292	Incorrect datetime value: 'test' for column 'a' at row 1
Warning	1292	Incorrect datetime value: 'a' for column 'a' at row 1
Warning	1265	Data truncated for column 'a' at row 1
Warning	1265	Data truncated for column 'a' at row 2
Warning	1265	Data truncated for column 'a' at row 3
SHOW WARNINGS;
Level	Code	Message
Warning	1292	Incorrect datetime value: 'test' for column 'a' at row 1
Warning	1292	Incorrect datetime value: 'a' for column 'a' at row 1
Warning	1265	Data truncated for column 'a' at row 1
Warning	1265	Data truncated for column 'a' at row 2
Warning	1265	Data truncated for column 'a' at row 3
DROP TABLE t1;
CREATE TABLE t1 (a TIME)
PARTITION BY RANGE (DAYOFWEEK(a))
(PARTITION a1 VALUES LESS THAN (60));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
SHOW WARNINGS;
Level	Code	Message
Error	1486	Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (TO_DAYS(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (TO_DAYS(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (TO_DAYS(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (TO_DAYS(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (TO_DAYS(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (DAYOFMONTH(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (DAYOFMONTH(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (DAYOFMONTH(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (DAYOFMONTH(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (DAYOFMONTH(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (MONTH(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (MONTH(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (MONTH(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (MONTH(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (MONTH(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (DAYOFYEAR(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (DAYOFYEAR(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (DAYOFYEAR(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (DAYOFYEAR(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (DAYOFYEAR(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (HOUR(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (HOUR(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (HOUR(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (HOUR(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (HOUR(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (MINUTE(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (MINUTE(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (MINUTE(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (MINUTE(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (MINUTE(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (QUARTER(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (QUARTER(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (QUARTER(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (QUARTER(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (QUARTER(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (SECOND(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (SECOND(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (SECOND(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (SECOND(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (SECOND(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (YEARWEEK(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (YEARWEEK(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (YEARWEEK(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (YEARWEEK(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (YEARWEEK(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (WEEKDAY(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (WEEKDAY(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (WEEKDAY(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (WEEKDAY(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (WEEKDAY(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
# TO_SECONDS() is added in 5.5.
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (TO_SECONDS(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (TO_SECONDS(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (TO_SECONDS(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (TO_SECONDS(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (TO_SECONDS(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (TIME_TO_SEC(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (TIME_TO_SEC(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (TIME_TO_SEC(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (TIME_TO_SEC(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (TIME_TO_SEC(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (FROM_DAYS(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (FROM_DAYS(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (FROM_DAYS(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (TO_DAYS(FROM_DAYS(a)));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (FROM_DAYS(a));
ERROR HY000: The PARTITION function returns the wrong type
CREATE TABLE t1 (a INT)
PARTITION BY HASH (TO_DAYS(FROM_DAYS(a)));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (FROM_DAYS(a));
ERROR HY000: The PARTITION function returns the wrong type
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (MICROSECOND(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (MICROSECOND(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (MICROSECOND(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (MICROSECOND(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (MICROSECOND(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
# Bug#57071
CREATE TABLE t1
(`date` date,
`extracted_week` int,
`yearweek` int,
`week` int,
`default_week_format` int)
PARTITION BY LIST (EXTRACT(WEEK FROM date) % 3)
(PARTITION p0 VALUES IN (0),
PARTITION p1 VALUES IN (1),
PARTITION p2 VALUES IN (2));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1
(`date` date,
`extracted_week` int,
`yearweek` int,
`week` int,
`default_week_format` int);
SET @old_default_week_format := @@default_week_format;
SET default_week_format = 0;
INSERT INTO t1 VALUES ('2000-01-01', EXTRACT(WEEK FROM '2000-01-01'), YEARWEEK('2000-01-01'), WEEK('2000-01-01'), @@default_week_format);
SET default_week_format = 1;
INSERT INTO t1 VALUES ('2000-01-01', EXTRACT(WEEK FROM '2000-01-01'), YEARWEEK('2000-01-01'), WEEK('2000-01-01'), @@default_week_format);
SET default_week_format = 2;
INSERT INTO t1 VALUES ('2000-01-01', EXTRACT(WEEK FROM '2000-01-01'), YEARWEEK('2000-01-01'), WEEK('2000-01-01'), @@default_week_format);
SET default_week_format = 3;
INSERT INTO t1 VALUES ('2000-01-01', EXTRACT(WEEK FROM '2000-01-01'), YEARWEEK('2000-01-01'), WEEK('2000-01-01'), @@default_week_format);
SET default_week_format = 4;
INSERT INTO t1 VALUES ('2000-01-01', EXTRACT(WEEK FROM '2000-01-01'), YEARWEEK('2000-01-01'), WEEK('2000-01-01'), @@default_week_format);
SET default_week_format = 5;
INSERT INTO t1 VALUES ('2000-01-01', EXTRACT(WEEK FROM '2000-01-01'), YEARWEEK('2000-01-01'), WEEK('2000-01-01'), @@default_week_format);
SET default_week_format = 6;
INSERT INTO t1 VALUES ('2000-01-01', EXTRACT(WEEK FROM '2000-01-01'), YEARWEEK('2000-01-01'), WEEK('2000-01-01'), @@default_week_format);
SET default_week_format = 7;
INSERT INTO t1 VALUES ('2000-01-01', EXTRACT(WEEK FROM '2000-01-01'), YEARWEEK('2000-01-01'), WEEK('2000-01-01'), @@default_week_format);
SELECT * FROM t1;
date	extracted_week	yearweek	week	default_week_format
2000-01-01	0	199952	0	0
2000-01-01	0	199952	0	1
2000-01-01	52	199952	52	2
2000-01-01	52	199952	52	3
2000-01-01	0	199952	0	4
2000-01-01	0	199952	0	5
2000-01-01	52	199952	52	6
2000-01-01	52	199952	52	7
SET default_week_format = @old_default_week_format;
DROP TABLE t1;
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(YEAR FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(YEAR FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(YEAR FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(YEAR FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(YEAR FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(YEAR_MONTH FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(YEAR_MONTH FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(YEAR_MONTH FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(YEAR_MONTH FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(YEAR_MONTH FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(QUARTER FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(QUARTER FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(QUARTER FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(QUARTER FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(QUARTER FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(MONTH FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(MONTH FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(MONTH FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(MONTH FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(MONTH FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
# EXTRACT(WEEK...) is disallowed, see bug#57071.
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(WEEK FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(WEEK FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(WEEK FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(WEEK FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(WEEK FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(DAY FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(DAY FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(DAY FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(DAY FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(DAY FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(DAY_HOUR FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(DAY_HOUR FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(DAY_HOUR FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(DAY_HOUR FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(DAY_HOUR FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(DAY_MINUTE FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(DAY_MINUTE FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(DAY_MINUTE FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(DAY_MINUTE FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(DAY_MINUTE FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(DAY_SECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(DAY_SECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(DAY_SECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(DAY_SECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(DAY_SECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(HOUR FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(HOUR FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(HOUR FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(HOUR FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(HOUR FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(HOUR_MINUTE FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(HOUR_MINUTE FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(HOUR_MINUTE FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(HOUR_MINUTE FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(HOUR_MINUTE FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(HOUR_SECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(HOUR_SECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(HOUR_SECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(HOUR_SECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(HOUR_SECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(MINUTE FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(MINUTE FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(MINUTE FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(MINUTE FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(MINUTE FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(MINUTE_SECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(MINUTE_SECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(MINUTE_SECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(MINUTE_SECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(MINUTE_SECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(SECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(SECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(SECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(SECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(SECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(MICROSECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(MICROSECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(DAY_MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(DAY_MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(DAY_MICROSECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(DAY_MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(DAY_MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(HOUR_MICROSECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(HOUR_MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(HOUR_MICROSECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(HOUR_MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(HOUR_MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(MINUTE_MICROSECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(MINUTE_MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(MINUTE_MICROSECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(MINUTE_MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(MINUTE_MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (EXTRACT(SECOND_MICROSECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (EXTRACT(SECOND_MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (EXTRACT(SECOND_MICROSECOND FROM a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (EXTRACT(SECOND_MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (EXTRACT(SECOND_MICROSECOND FROM a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME, b DATE)
PARTITION BY HASH (DATEDIFF(a, b));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATE, b DATETIME)
PARTITION BY HASH (DATEDIFF(a, b));
DROP TABLE t1;
CREATE TABLE t1 (a DATETIME, b DATE)
PARTITION BY HASH (DATEDIFF(a, b));
DROP TABLE t1;
CREATE TABLE t1 (a DATE, b VARCHAR(10))
PARTITION BY HASH (DATEDIFF(a, b));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT, b DATETIME)
PARTITION BY HASH (DATEDIFF(a, b));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a TIME)
PARTITION BY HASH (TIME_TO_SEC(a));
DROP TABLE t1;
CREATE TABLE t1 (a DATE)
PARTITION BY HASH (TIME_TO_SEC(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a DATETIME)
PARTITION BY HASH (TIME_TO_SEC(a));
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10))
PARTITION BY HASH (TIME_TO_SEC(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (a INT)
PARTITION BY HASH (TIME_TO_SEC(a));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
#
# Bug#50036: Inconsistent errors when using TIMESTAMP
#            columns/expressions
# 1. correct and appropriate errors in light of
#    the fix for BUG#42849:
CREATE TABLE t1 (c TIMESTAMP)
PARTITION BY RANGE (TO_DAYS(c))
(PARTITION p0 VALUES LESS THAN (10000),
PARTITION p1 VALUES LESS THAN (MAXVALUE));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t2 (c TIMESTAMP);
ALTER TABLE t2
PARTITION BY RANGE (TO_DAYS(c))
(PARTITION p0 VALUES LESS THAN (10000),
PARTITION p1 VALUES LESS THAN (MAXVALUE));
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (c TIMESTAMP)
PARTITION BY RANGE COLUMNS(c)
(PARTITION p0 VALUES LESS THAN ('2000-01-01 00:00:00'),
PARTITION p1 VALUES LESS THAN (MAXVALUE));
ERROR HY000: Field 'c' is of a not allowed type for this type of partitioning
ALTER TABLE t2 PARTITION BY RANGE COLUMNS(c)
(PARTITION p0 VALUES LESS THAN ('2000-01-01 00:00:00'),
PARTITION p1 VALUES LESS THAN (MAXVALUE));
ERROR HY000: Field 'c' is of a not allowed type for this type of partitioning
DROP TABLE t2;
# 2. These errors where questionable before the fix:
# VALUES clause are checked first, clearified the error message.
CREATE TABLE t1 (c TIMESTAMP)
PARTITION BY RANGE (c)
(PARTITION p0 VALUES LESS THAN ('2000-01-01 00:00:00'),
PARTITION p1 VALUES LESS THAN (MAXVALUE));
ERROR HY000: VALUES value for partition 'p0' must have type INT
# TIMESTAMP is not INT (e.g. UNIX_TIMESTAMP).
CREATE TABLE t1 (c TIMESTAMP)
PARTITION BY RANGE (UNIX_TIMESTAMP(c))
(PARTITION p0 VALUES LESS THAN ('2000-01-01 00:00:00'),
PARTITION p1 VALUES LESS THAN (MAXVALUE));
ERROR HY000: VALUES value for partition 'p0' must have type INT
CREATE TABLE t1 (c TIMESTAMP)
PARTITION BY RANGE (UNIX_TIMESTAMP(c))
(PARTITION p0 VALUES LESS THAN (UNIX_TIMESTAMP('2000-01-01 00:00:00')),
PARTITION p1 VALUES LESS THAN (MAXVALUE));
DROP TABLE t1;
# Changed error from ER_INCONSISTENT_TYPE_OF_FUNCTIONS_ERROR
CREATE TABLE t1 (c TIMESTAMP)
PARTITION BY HASH (c) PARTITIONS 4;
ERROR HY000: Field 'c' is of a not allowed type for this type of partitioning
# Moved to partition_myisam, since it was MyISAM specific
# Added test with existing TIMESTAMP partitioning (when it was allowed).
#
# Bug#49477: Assertion `0' failed in ha_partition.cc:5530
# with temporary table and partitions
#
CREATE TABLE t1 (a INT) PARTITION BY HASH(a);
CREATE TEMPORARY TABLE tmp_t1 LIKE t1;
ERROR HY000: Cannot create temporary table with partitions
DROP TABLE t1;
#
# Bug#42954: SQL MODE 'NO_DIR_IN_CREATE' does not work with
#            subpartitions
SET @org_mode=@@sql_mode;
SET @@sql_mode='NO_DIR_IN_CREATE';
SELECT @@sql_mode;
@@sql_mode
NO_DIR_IN_CREATE
CREATE TABLE t1 (id INT, purchased DATE)
PARTITION BY RANGE(YEAR(purchased))
SUBPARTITION BY HASH(TO_DAYS(purchased))
(PARTITION p0 VALUES LESS THAN MAXVALUE
DATA DIRECTORY = '/tmp/not-existing'
  INDEX DIRECTORY = '/tmp/not-existing');
Warnings:
Warning	1618	<DATA DIRECTORY> option ignored
Warning	1618	<INDEX DIRECTORY> option ignored
Warning	1618	<DATA DIRECTORY> option ignored
Warning	1618	<INDEX DIRECTORY> option ignored
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `id` int DEFAULT NULL,
  `purchased` date DEFAULT NULL
) ENGINE=<curr_engine> DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY RANGE (year(`purchased`))
SUBPARTITION BY HASH (to_days(`purchased`))
(PARTITION p0 VALUES LESS THAN MAXVALUE ENGINE = <curr_engine>) */
DROP TABLE t1;
CREATE TABLE t1 (id INT, purchased DATE)
PARTITION BY RANGE(YEAR(purchased))
SUBPARTITION BY HASH(TO_DAYS(purchased)) SUBPARTITIONS 2
(PARTITION p0 VALUES LESS THAN MAXVALUE
(SUBPARTITION sp0
DATA DIRECTORY = '/tmp/not-existing'
   INDEX DIRECTORY = '/tmp/not-existing',
SUBPARTITION sp1));
Warnings:
Warning	1618	<DATA DIRECTORY> option ignored
Warning	1618	<INDEX DIRECTORY> option ignored
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `id` int DEFAULT NULL,
  `purchased` date DEFAULT NULL
) ENGINE=<curr_engine> DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY RANGE (year(`purchased`))
SUBPARTITION BY HASH (to_days(`purchased`))
(PARTITION p0 VALUES LESS THAN MAXVALUE
 (SUBPARTITION sp0 ENGINE = <curr_engine>,
  SUBPARTITION sp1 ENGINE = <curr_engine>)) */
DROP TABLE t1;
CREATE TABLE t1 (id INT, purchased DATE)
PARTITION BY RANGE(YEAR(purchased))
(PARTITION p0 VALUES LESS THAN MAXVALUE
DATA DIRECTORY = '/tmp/not-existing'
  INDEX DIRECTORY = '/tmp/not-existing');
Warnings:
Warning	1618	<DATA DIRECTORY> option ignored
Warning	1618	<INDEX DIRECTORY> option ignored
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `id` int DEFAULT NULL,
  `purchased` date DEFAULT NULL
) ENGINE=<curr_engine> DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY RANGE (year(`purchased`))
(PARTITION p0 VALUES LESS THAN MAXVALUE ENGINE = <curr_engine>) */
DROP TABLE t1;
SET @@sql_mode= @org_mode;
CREATE TABLE t1 (a INTEGER NOT NULL, PRIMARY KEY (a));
INSERT INTO t1 VALUES (1),(1);
ERROR 23000: Duplicate entry '1' for key 't1.PRIMARY'
DROP TABLE t1;
CREATE TABLE t1 (a INTEGER NOT NULL, PRIMARY KEY (a))
PARTITION BY KEY (a) PARTITIONS 2;
INSERT INTO t1 VALUES (1),(1);
ERROR 23000: Duplicate entry '1' for key 't1.PRIMARY'
DROP TABLE t1;
CREATE TABLESPACE ts1 ADD DATAFILE 'ts1.ibd';
CREATE TABLESPACE ts2 ADD DATAFILE 'ts2.ibd';
CREATE TABLESPACE ts3 ADD DATAFILE 'ts3.ibd';
CREATE TABLE t1 (
a int
)
PARTITION BY RANGE (a)
(
PARTITION p0 VALUES LESS THAN (1),
PARTITION p1 VALU ES LESS THAN (2)
);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'VALU ES LESS THAN (2)
)' at line 7
partition by list (a)
partitions 3
(partition x1 values in (1,2,9,4) tablespace ts1,
partition x2 values in (3, 11, 5, 7) tablespace ts2,
partition x3 values in (16, 8, 5+19, 70-43) tablespace ts3);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'partition by list (a)
partitions 3
(partition x1 values in (1,2,9,4) tablespace ' at line 1
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (a)
partitions 2;
ERROR HY000: For LIST partitions each partition must be defined
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (sin(a))
partitions 3
(partition x1 values in (1,2,9,4) tablespace ts1,
partition x2 values in (3, 11, 5, 7) tablespace ts2,
partition x3 values in (16, 8, 5+19, 70-43) tablespace ts3);
ERROR HY000: This partition function is not allowed
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by key (a+2)
partitions 3
(partition x1 tablespace ts1,
partition x2 tablespace ts2,
partition x3 tablespace ts3);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '+2)
partitions 3
(partition x1 tablespace ts1,
partition x2 tablespace ts2,
part' at line 6
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by key (a)
partitions 3
(partition tablespace ts1,
partition x2 tablespace ts2,
partition x3 tablespace ts3);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'ts1,
partition x2 tablespace ts2,
partition x3 tablespace ts3)' at line 8
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by range columns (a,d)
(partition x1 VALUES LESS THAN (1,1),
partition x2 VALUES LESS THAN (2,2),
partition x3 VALUES LESS THAN (3,3));
ERROR HY000: Field in list of fields for partition function not found in table
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by key (a,d)
partitions 3
(partition x1 tablespace ts1,
partition x2 tablespace ts2,
partition x3 tablespace ts3);
ERROR HY000: Field in list of fields for partition function not found in table
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by hash (a + d)
partitions 3
(partition x1 tablespace ts1,
partition x2 tablespace ts2,
partition x3 tablespace ts3);
ERROR 42S22: Unknown column 'd' in 'partition function'
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by hash (sin(a))
partitions 3
(partition x1 tablespace ts1,
partition x2 tablespace ts2,
partition x3 tablespace ts3);
ERROR HY000: This partition function is not allowed
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by key (a)
partitions 3
(partition x1, partition x2);
ERROR 42000: Wrong number of partitions defined, mismatch with previous setting near '(partition x1, partition x2)' at line 8
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by hash (rand(a))
partitions 2
(partition x1, partition x2);
ERROR 42000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed near 'rand(a))
partitions 2
(partition x1, partition x2)' at line 6
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by range (rand(a))
partitions 2
(partition x1 values less than (0), partition x2 values less than (2));
ERROR 42000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed near 'rand(a))
partitions 2
(partition x1 values less than (0), partition x2 values le' at line 6
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (rand(a))
partitions 2
(partition x1 values in (1), partition x2 values in (2));
ERROR 42000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed near 'rand(a))
partitions 2
(partition x1 values in (1), partition x2 values in (2))' at line 6
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by hash (a)
partitions 2
(partition x1 values less than (4),
partition x2 values less than (5));
ERROR HY000: Only RANGE PARTITIONING can use VALUES LESS THAN in partition definition
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by hash (a)
partitions 2
(partition x1 values in (4),
partition x2 values in (5));
ERROR HY000: Only LIST PARTITIONING can use VALUES IN in partition definition
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by hash (a)
partitions 2
(partition x1 values in (4,6),
partition x2 values in (5,7));
ERROR HY000: Only LIST PARTITIONING can use VALUES IN in partition definition
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by key (a)
subpartition by key (b);
ERROR HY000: It is only possible to mix RANGE/LIST partitioning with HASH/KEY partitioning for subpartitioning
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by key (a)
subpartition by key (a, b);
ERROR HY000: It is only possible to mix RANGE/LIST partitioning with HASH/KEY partitioning for subpartitioning
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by key (a)
subpartition by hash (a+b);
ERROR HY000: It is only possible to mix RANGE/LIST partitioning with HASH/KEY partitioning for subpartitioning
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by key (a)
subpartition by key (b);
ERROR HY000: It is only possible to mix RANGE/LIST partitioning with HASH/KEY partitioning for subpartitioning
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by key (a)
subpartition by key (a, b);
ERROR HY000: It is only possible to mix RANGE/LIST partitioning with HASH/KEY partitioning for subpartitioning
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by key (a)
subpartition by hash (a+b);
ERROR HY000: It is only possible to mix RANGE/LIST partitioning with HASH/KEY partitioning for subpartitioning
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by key (a)
subpartition by hash (rand(a+b));
ERROR 42000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed near 'rand(a+b))' at line 7
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by key (a)
subpartition by hash (sin(a+b))
(partition x1 (subpartition x11, subpartition x12),
partition x2 (subpartition x21, subpartition x22));
ERROR HY000: This partition function is not allowed
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by range (a)
subpartition by key (a+b)
(partition x1 values less than (1) (subpartition x11, subpartition x12),
partition x2 values less than (2) (subpartition x21, subpartition x22));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '+b)
(partition x1 values less than (1) (subpartition x11, subpartition x12),
par' at line 7
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by range (a)
subpartition by key (a,d)
(partition x1 values less than (1) (subpartition x11, subpartition x12),
partition x2 values less than (2) (subpartition x21, subpartition x22));
ERROR HY000: Field in list of fields for partition function not found in table
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by key (a)
subpartition by hash (3+4);
ERROR HY000: It is only possible to mix RANGE/LIST partitioning with HASH/KEY partitioning for subpartitioning
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by range (a)
subpartition by hash (a+d)
(partition x1 values less than (1) (subpartition x11, subpartition x12),
partition x2 values less than (2) (subpartition x21, subpartition x22));
ERROR 42S22: Unknown column 'd' in 'partition function'
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by range (a);
ERROR HY000: For RANGE partitions each partition must be defined
select load_file('$MYSQLD_DATADIR/test/t1.par');
load_file('$MYSQLD_DATADIR/test/t1.par')
NULL
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by range (a+d)
partitions 2
(partition x1 values less than (4) tablespace ts1,
partition x2 values less than (8) tablespace ts2);
ERROR 42S22: Unknown column 'd' in 'partition function'
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by range (a)
partitions 2
(partition x1 values less than (4.0) tablespace ts1,
partition x2 values less than (8) tablespace ts2);
ERROR HY000: VALUES value for partition 'x1' must have type INT
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by range (3+4)
partitions 2
(partition x1 values less than (4) tablespace ts1,
partition x2 values less than (8) tablespace ts2);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by range (a)
partitions 2
(partition x1 values less than (4),
partition x2);
ERROR 42000: Syntax error: RANGE PARTITIONING requires definition of VALUES LESS THAN for each partition near ',
partition x2)' at line 8
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by range (a)
partitions 2
(partition x1 values in (4),
partition x2);
ERROR HY000: Only LIST PARTITIONING can use VALUES IN in partition definition
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by range (a)
partitions 2
(partition x1 values in (4),
partition x2 values less than (5));
ERROR HY000: Only LIST PARTITIONING can use VALUES IN in partition definition
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (a)
partitions 2
(partition x1 values less than 4,
partition x2 values less than (5));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '4,
partition x2 values less than (5))' at line 8
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (a)
partitions 2
(partition x1 values less than (4),
partition x2 values less than (5));
ERROR HY000: Only RANGE PARTITIONING can use VALUES LESS THAN in partition definition
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by range (a)
partitions 2
(partition x1 values less than maxvalue,
partition x2 values less than (5));
ERROR HY000: MAXVALUE can only be used in last partition definition
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by range (a)
partitions 2
(partition x1 values less than maxvalue,
partition x2 values less than maxvalue);
ERROR HY000: MAXVALUE can only be used in last partition definition
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by range (a)
partitions 2
(partition x1 values less than (4),
partition x2 values less than (3));
ERROR HY000: VALUES LESS THAN value must be strictly increasing for each partition
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by range (sin(a))
partitions 2
(partition x1 values less than (4),
partition x2 values less than (5));
ERROR HY000: This partition function is not allowed
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by list (a)
subpartition by hash (a+b)
subpartitions 3
( partition x1 values in (1,2,4)
( subpartition x11 nodegroup 0,
subpartition x12 nodegroup 1),
partition x2 values in (3,5,6)
( subpartition x21 nodegroup 0,
subpartition x22 nodegroup 1)
);
ERROR 42000: Wrong number of subpartitions defined, mismatch with previous setting near '( subpartition x11 nodegroup 0,
subpartition x12 nodegroup 1),
partition x2 valu' at line 10
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by list (a)
subpartition by hash (a+b)
( partition x1 values in (1)
( subpartition x11 nodegroup 0,
subpartition xextra,
subpartition x12 nodegroup 1),
partition x2 values in (2)
( subpartition x21 nodegroup 0,
subpartition x22 nodegroup 1)
);
ERROR 42000: Wrong number of subpartitions defined, mismatch with previous setting near '( subpartition x21 nodegroup 0,
subpartition x22 nodegroup 1)
)' at line 13
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by key (a)
subpartition by list (a+b)
( partition x1
( subpartition x11,
subpartition x12),
partition x2
( subpartition x21,
subpartition x22)
);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'list (a+b)
( partition x1
( subpartition x11,
subpartition x12),
partition x2
( ' at line 7
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key (a,b))
partition by key (a)
subpartition by list (a+b)
( partition x1
( subpartition x11 values in (0),
subpartition x12 values in (1)),
partition x2
( subpartition x21 values in (0),
subpartition x22 values in (1))
);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'list (a+b)
( partition x1
( subpartition x11 values in (0),
subpartition x12 val' at line 7
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (a);
ERROR HY000: For LIST partitions each partition must be defined
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (3+4)
partitions 2
(partition x1 values in (4) tablespace ts1,
partition x2 values in (8) tablespace ts2);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (a+d)
partitions 2
(partition x1 values in (4) tablespace ts1,
partition x2 values in (8) tablespace ts2);
ERROR 42S22: Unknown column 'd' in 'partition function'
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (a)
partitions 2
(partition x1 values in (4),
partition x2);
ERROR 42000: Syntax error: LIST PARTITIONING requires definition of VALUES IN for each partition near ',
partition x2)' at line 8
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (a)
partitions 2
(partition x1 values in (4),
partition x2 values less than (5));
ERROR HY000: Only RANGE PARTITIONING can use VALUES LESS THAN in partition definition
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (a)
partitions 2
(partition x1 values in (4,6),
partition x2);
ERROR 42000: Syntax error: LIST PARTITIONING requires definition of VALUES IN for each partition near ',
partition x2)' at line 8
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (a)
partitions 2
(partition x1 values in (4, 12+9),
partition x2 values in (3, 21));
ERROR HY000: Multiple definition of same constant in list partitioning
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (a)
partitions 2
(partition x1 values in (4.0, 12+8),
partition x2 values in (3, 21));
ERROR HY000: VALUES value for partition 'x1' must have type INT
CREATE TABLE t1 (
a int not null,
b int not null,
c int not null,
primary key(a,b))
partition by list (a)
partitions 2
(partition x1 values in 4,
partition x2 values in (5));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '4,
partition x2 values in (5))' at line 8
DROP TABLESPACE ts1;
DROP TABLESPACE ts2;
DROP TABLESPACE ts3;
CREATE TABLE t1 (a int)
PARTITION BY RANGE (a)
(PARTITION p0 VALUES LESS THAN (x1));
ERROR 42S22: Unknown column 'x1' in 'partition function'
CREATE TABLE t1(a int)
PARTITION BY RANGE (a) (PARTITION p1 VALUES LESS THAN(5));
insert into t1 values (10);
ERROR HY000: Table has no partition for value 10
drop table t1;
create table t1 (a bigint unsigned)
partition by range (a)
(partition p0 values less than (-1));
ERROR HY000: Partition constant is out of partition function domain
create table t1 (v varchar(12))
partition by range (ascii(v))
(partition p0 values less than (10));
ERROR HY000: This partition function is not allowed
create table t1 (a int)
partition by hash (rand(a));
ERROR 42000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed near 'rand(a))' at line 2
create table t1 (a int)
partition by hash(CURTIME() + a);
ERROR 42000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed near 'CURTIME() + a)' at line 2
create table t1 (a int)
partition by hash (NOW()+a);
ERROR 42000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed near 'NOW()+a)' at line 2
create table t1 (a int)
partition by hash (extract(hour from convert_tz(a, '+00:00', '+00:00')));
ERROR HY000: This partition function is not allowed
create table t1 (a int)
partition by range (a + (select count(*) from t1))
(partition p1 values less than (1));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '(select count(*) from t1))
(partition p1 values less than (1))' at line 2
create table t1 (a char(10))
partition by hash (extractvalue(a,'a'));
ERROR HY000: This partition function is not allowed
#
# Bug #42849: innodb crash with varying time_zone on partitioned
#             timestamp primary key
#
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE old (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (UNIX_TIMESTAMP(a)) (
PARTITION p VALUES LESS THAN (1219089600),
PARTITION pmax VALUES LESS THAN MAXVALUE);
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (a) (
PARTITION p VALUES LESS THAN (20080819),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Field 'a' is of a not allowed type for this type of partitioning
ALTER TABLE old
PARTITION BY RANGE (a) (
PARTITION p VALUES LESS THAN (20080819),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Field 'a' is of a not allowed type for this type of partitioning
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (a+0) (
PARTITION p VALUES LESS THAN (20080819),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (a+0) (
PARTITION p VALUES LESS THAN (20080819),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (a % 2) (
PARTITION p VALUES LESS THAN (20080819),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (a % 2) (
PARTITION p VALUES LESS THAN (20080819),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (ABS(a)) (
PARTITION p VALUES LESS THAN (20080819),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (ABS(a)) (
PARTITION p VALUES LESS THAN (20080819),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (CEILING(a)) (
PARTITION p VALUES LESS THAN (20080819),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (CEILING(a)) (
PARTITION p VALUES LESS THAN (20080819),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (FLOOR(a)) (
PARTITION p VALUES LESS THAN (20080819),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (FLOOR(a)) (
PARTITION p VALUES LESS THAN (20080819),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (TO_DAYS(a)) (
PARTITION p VALUES LESS THAN (733638),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (TO_DAYS(a)) (
PARTITION p VALUES LESS THAN (733638),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (DAYOFYEAR(a)) (
PARTITION p VALUES LESS THAN (231),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (DAYOFYEAR(a)) (
PARTITION p VALUES LESS THAN (231),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (DAYOFMONTH(a)) (
PARTITION p VALUES LESS THAN (19),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (DAYOFMONTH(a)) (
PARTITION p VALUES LESS THAN (19),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (DAYOFWEEK(a)) (
PARTITION p VALUES LESS THAN (3),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (DAYOFWEEK(a)) (
PARTITION p VALUES LESS THAN (3),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (MONTH(a)) (
PARTITION p VALUES LESS THAN (8),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (MONTH(a)) (
PARTITION p VALUES LESS THAN (8),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (HOUR(a)) (
PARTITION p VALUES LESS THAN (17),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (HOUR(a)) (
PARTITION p VALUES LESS THAN (17),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (MINUTE(a)) (
PARTITION p VALUES LESS THAN (55),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (MINUTE(a)) (
PARTITION p VALUES LESS THAN (55),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (QUARTER(a)) (
PARTITION p VALUES LESS THAN (3),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (QUARTER(a)) (
PARTITION p VALUES LESS THAN (3),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (SECOND(a)) (
PARTITION p VALUES LESS THAN (7),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (SECOND(a)) (
PARTITION p VALUES LESS THAN (7),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (YEARWEEK(a)) (
PARTITION p VALUES LESS THAN (200833),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (YEARWEEK(a)) (
PARTITION p VALUES LESS THAN (200833),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (YEAR(a)) (
PARTITION p VALUES LESS THAN (2008),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (YEAR(a)) (
PARTITION p VALUES LESS THAN (2008),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (WEEKDAY(a)) (
PARTITION p VALUES LESS THAN (3),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (WEEKDAY(a)) (
PARTITION p VALUES LESS THAN (3),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (TIME_TO_SEC(a)) (
PARTITION p VALUES LESS THAN (64507),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (TIME_TO_SEC(a)) (
PARTITION p VALUES LESS THAN (64507),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (EXTRACT(DAY FROM a)) (
PARTITION p VALUES LESS THAN (18),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (EXTRACT(DAY FROM a)) (
PARTITION p VALUES LESS THAN (18),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL, b TIMESTAMP NOT NULL, PRIMARY KEY(a,b))
PARTITION BY RANGE (DATEDIFF(a, a)) (
PARTITION p VALUES LESS THAN (18),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (DATEDIFF(a, a)) (
PARTITION p VALUES LESS THAN (18),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (YEAR(a + 0)) (
PARTITION p VALUES LESS THAN (2008),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (YEAR(a + 0)) (
PARTITION p VALUES LESS THAN (2008),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (TO_DAYS(a + '2008-01-01')) (
PARTITION p VALUES LESS THAN (733638),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (TO_DAYS(a + '2008-01-01')) (
PARTITION p VALUES LESS THAN (733638),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP NOT NULL PRIMARY KEY)
PARTITION BY RANGE (YEAR(a + '2008-01-01')) (
PARTITION p VALUES LESS THAN (2008),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (YEAR(a + '2008-01-01')) (
PARTITION p VALUES LESS THAN (2008),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old ADD COLUMN b DATE;
CREATE TABLE new (a TIMESTAMP, b DATE)
PARTITION BY RANGE (YEAR(a + b)) (
PARTITION p VALUES LESS THAN (2008),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (YEAR(a + b)) (
PARTITION p VALUES LESS THAN (2008),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP, b DATE)
PARTITION BY RANGE (TO_DAYS(a + b)) (
PARTITION p VALUES LESS THAN (733638),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (TO_DAYS(a + b)) (
PARTITION p VALUES LESS THAN (733638),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP, b date)
PARTITION BY RANGE (UNIX_TIMESTAMP(a + b)) (
PARTITION p VALUES LESS THAN (1219089600),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old
PARTITION BY RANGE (UNIX_TIMESTAMP(a + b)) (
PARTITION p VALUES LESS THAN (1219089600),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
CREATE TABLE new (a TIMESTAMP, b TIMESTAMP)
PARTITION BY RANGE (UNIX_TIMESTAMP(a + b)) (
PARTITION p VALUES LESS THAN (1219089600),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
ALTER TABLE old MODIFY b TIMESTAMP;
ALTER TABLE old
PARTITION BY RANGE (UNIX_TIMESTAMP(a + b)) (
PARTITION p VALUES LESS THAN (1219089600),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
DROP TABLE old;
#
# Bug #56709: Memory leaks at running the 5.1 test suite
#
CREATE TABLE t1 (a TIMESTAMP NOT NULL PRIMARY KEY);
ALTER TABLE t1
PARTITION BY RANGE (EXTRACT(DAY FROM a)) (
PARTITION p VALUES LESS THAN (18),
PARTITION pmax VALUES LESS THAN MAXVALUE);
ERROR HY000: Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed
DROP TABLE t1;
End of 5.1 tests
CREATE TABLE t1 (a INT)
PARTITION BY LIST (a)
SUBPARTITION BY HASH (a) SUBPARTITIONS 2
(PARTITION p1 VALUES IN (1) COMMENT "Comment in p1"
 (SUBPARTITION p1spFirst COMMENT "SubPartition comment in p1spFirst",
SUBPARTITION p1spSecond COMMENT "SubPartition comment in p1spSecond"),
PARTITION p2 VALUES IN (2) COMMENT "Comment in p2"
 (SUBPARTITION p2spFirst COMMENT "SubPartition comment in p2spFirst",
SUBPARTITION p2spSecond COMMENT "SubPartition comment in p2spSecond"));
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `a` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY LIST (`a`)
SUBPARTITION BY HASH (`a`)
(PARTITION p1 VALUES IN (1)
 (SUBPARTITION p1spFirst COMMENT = 'SubPartition comment in p1spFirst' ENGINE = InnoDB,
  SUBPARTITION p1spSecond COMMENT = 'SubPartition comment in p1spSecond' ENGINE = InnoDB),
 PARTITION p2 VALUES IN (2)
 (SUBPARTITION p2spFirst COMMENT = 'SubPartition comment in p2spFirst' ENGINE = InnoDB,
  SUBPARTITION p2spSecond COMMENT = 'SubPartition comment in p2spSecond' ENGINE = InnoDB)) */
SELECT PARTITION_NAME, SUBPARTITION_NAME, PARTITION_COMMENT FROM INFORMATION_SCHEMA.PARTITIONS
WHERE TABLE_NAME = 't1' AND TABLE_SCHEMA = 'test';
PARTITION_NAME	SUBPARTITION_NAME	PARTITION_COMMENT
p1	p1spFirst	SubPartition comment in p1spFirst
p1	p1spSecond	SubPartition comment in p1spSecond
p2	p2spFirst	SubPartition comment in p2spFirst
p2	p2spSecond	SubPartition comment in p2spSecond
DROP TABLE t1;
CREATE TABLE t1 (a INT)
PARTITION BY LIST (a)
SUBPARTITION BY HASH (a) SUBPARTITIONS 2
(PARTITION p1 VALUES IN (1)
(SUBPARTITION p1spFirst COMMENT "SubPartition comment in p1spFirst",
SUBPARTITION p1spSecond),
PARTITION p2 VALUES IN (2) COMMENT "Comment in p2"
 (SUBPARTITION p2spFirst,
SUBPARTITION p2spSecond COMMENT "SubPartition comment in p2spSecond"));
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `a` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY LIST (`a`)
SUBPARTITION BY HASH (`a`)
(PARTITION p1 VALUES IN (1)
 (SUBPARTITION p1spFirst COMMENT = 'SubPartition comment in p1spFirst' ENGINE = InnoDB,
  SUBPARTITION p1spSecond ENGINE = InnoDB),
 PARTITION p2 VALUES IN (2)
 (SUBPARTITION p2spFirst COMMENT = 'Comment in p2' ENGINE = InnoDB,
  SUBPARTITION p2spSecond COMMENT = 'SubPartition comment in p2spSecond' ENGINE = InnoDB)) */
SELECT PARTITION_NAME, SUBPARTITION_NAME, PARTITION_COMMENT FROM INFORMATION_SCHEMA.PARTITIONS
WHERE TABLE_NAME = 't1' AND TABLE_SCHEMA = 'test';
PARTITION_NAME	SUBPARTITION_NAME	PARTITION_COMMENT
p1	p1spFirst	SubPartition comment in p1spFirst
p1	p1spSecond	
p2	p2spFirst	Comment in p2
p2	p2spSecond	SubPartition comment in p2spSecond
DROP TABLE t1;
CREATE TABLE t1
(a INT ,
KEY inx_a (a) )
PARTITION BY RANGE (a)
SUBPARTITION BY HASH (a) SUBPARTITIONS 2
(PARTITION pUpTo10 VALUES LESS THAN (10) COMMENT
"This is a long comment (2050 ascii characters)   50 pUpTo10 partition ......80-!.................. 100 ................................................................................................ 200....................................................................................................................................................................................................................................................................................................... 500 ............................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................... 1000 ..............1024-|.......................................................................................................................................................................................................................................................................................................................................................................................................................................................................................... 1500 .............................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................. 2000 ......................................2048-|++"
 (SUBPARTITION `p-10sp0` ,SUBPARTITION `p-10sp1` ),
PARTITION pMax VALUES LESS THAN MAXVALUE COMMENT
"This is a long comment (2050 ascii characters)   50 pMax partition comment .80-!.................. 100 ................................................................................................ 200....................................................................................................................................................................................................................................................................................................... 500 ............................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................... 1000 ..............1024-|.......................................................................................................................................................................................................................................................................................................................................................................................................................................................................................... 1500 .............................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................. 2000 ......................................2048-|++"
 (SUBPARTITION `pMaxsp0` ,SUBPARTITION `pMaxsp1` ));
Warnings:
Warning	1793	Comment for table partition 'pUpTo10' is too long (max = 1024)
Warning	1793	Comment for table partition 'pMax' is too long (max = 1024)
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `a` int DEFAULT NULL,
  KEY `inx_a` (`a`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY RANGE (`a`)
SUBPARTITION BY HASH (`a`)
(PARTITION pUpTo10 VALUES LESS THAN (10)
 (SUBPARTITION `p-10sp0` COMMENT = 'This is a long comment (2050 ascii characters)   50 pUpTo10 partition ......80-!.................. 100 ................................................................................................ 200....................................................................................................................................................................................................................................................................................................... 500 ............................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................... 1000 ..............1024-|' ENGINE = InnoDB,
  SUBPARTITION `p-10sp1` COMMENT = 'This is a long comment (2050 ascii characters)   50 pUpTo10 partition ......80-!.................. 100 ................................................................................................ 200....................................................................................................................................................................................................................................................................................................... 500 ............................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................... 1000 ..............1024-|' ENGINE = InnoDB),
 PARTITION pMax VALUES LESS THAN MAXVALUE
 (SUBPARTITION pMaxsp0 COMMENT = 'This is a long comment (2050 ascii characters)   50 pMax partition comment .80-!.................. 100 ................................................................................................ 200....................................................................................................................................................................................................................................................................................................... 500 ............................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................... 1000 ..............1024-|' ENGINE = InnoDB,
  SUBPARTITION pMaxsp1 COMMENT = 'This is a long comment (2050 ascii characters)   50 pMax partition comment .80-!.................. 100 ................................................................................................ 200....................................................................................................................................................................................................................................................................................................... 500 ............................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................... 1000 ..............1024-|' ENGINE = InnoDB)) */
SELECT PARTITION_NAME, SUBPARTITION_NAME, PARTITION_COMMENT FROM INFORMATION_SCHEMA.PARTITIONS
WHERE TABLE_NAME = 't1' AND TABLE_SCHEMA = 'test' ORDER BY PARTITION_NAME, SUBPARTITION_NAME DESC;
PARTITION_NAME	SUBPARTITION_NAME	PARTITION_COMMENT
pMax	pMaxsp1	This is a long comment (2050 ascii characters)   50 pMax partition comment .80-!.................. 100 ................................................................................................ 200....................................................................................................................................................................................................................................................................................................... 500 ............................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................... 1000 ..............1024-|
pMax	pMaxsp0	This is a long comment (2050 ascii characters)   50 pMax partition comment .80-!.................. 100 ................................................................................................ 200....................................................................................................................................................................................................................................................................................................... 500 ............................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................... 1000 ..............1024-|
pUpTo10	p-10sp1	This is a long comment (2050 ascii characters)   50 pUpTo10 partition ......80-!.................. 100 ................................................................................................ 200....................................................................................................................................................................................................................................................................................................... 500 ............................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................... 1000 ..............1024-|
pUpTo10	p-10sp0	This is a long comment (2050 ascii characters)   50 pUpTo10 partition ......80-!.................. 100 ................................................................................................ 200....................................................................................................................................................................................................................................................................................................... 500 ............................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................... 1000 ..............1024-|
DROP TABLE t1;
SET sql_mode = default;
#
# Test for increased coverage of misplaced record handling.
#
CREATE TABLE t1 (a int)
PARTITION BY LIST (a)
(PARTITION p1 VALUES IN (1),
PARTITION p3 VALUES IN (3,5),
PARTITION p6 VALUES IN (6,7));
# Intentionally insert illegal rows into p1.
CREATE TABLE t2 LIKE t1;
ALTER TABLE t2 REMOVE PARTITIONING;
INSERT INTO t2 VALUES (2),(3),(4),(6);
ALTER TABLE t1 EXCHANGE PARTITION p1 WITH TABLE t2 WITHOUT VALIDATION;
# These should fail gracefully (WHERE used to not trigger pruning!)
CALL mtr.add_suppression(" corrupted: row in wrong partition: ");
INSERT INTO t1 VALUES (2);
ERROR HY000: Table has no partition for value 2
INSERT INTO t1 PARTITION (p1) VALUES (3);
ERROR HY000: Found a row not matching the given partition set
UPDATE t1 SET a = 3 WHERE (a % 3) != 0;
ERROR HY000: Table has no partition for value 2
UPDATE t1 PARTITION (p1) SET a = 3 WHERE (a % 3) != 0;
ERROR HY000: Table has no partition for value 2
UPDATE t1 PARTITION (p1) SET a = 5 WHERE (a % 3) = 0;
ERROR HY000: Found a row not matching the given partition set
UPDATE t1 PARTITION (p1) SET a = 3 WHERE (a % 5) > 3;
ERROR HY000: Table has no partition for value 4
UPDATE t1 PARTITION (p1,p6) SET a = 7 WHERE (a % 7) > 5;
ERROR HY000: Found a row in wrong partition 0. Correct is 2 a:6
DELETE FROM t1 PARTITION (p1) WHERE (a % 7) > 2;
ERROR HY000: Found a row not matching the given partition set
DELETE FROM t1 PARTITION (p1,p6) WHERE (a % 7) > 5;
ERROR HY000: Found a row in wrong partition 0. Correct is 2 a:6
DELETE FROM t1 WHERE a > 0;
ERROR HY000: Table has no partition for value 2
DROP TABLE t1, t2;
#
# Bug #30455845 ASSERTION "!CHECK_DATETIME_RANGE.MY_TIME"
#
CREATE TABLE t1(a DATETIME)
PARTITION BY HASH (EXTRACT(HOUR_MICROSECOND FROM a));
SET sql_mode='';
SELECT FROM_DAYS(3652499), FROM_DAYS(3652500), FROM_DAYS(3652501);
FROM_DAYS(3652499)	FROM_DAYS(3652500)	FROM_DAYS(3652501)
NULL	0000-00-00	0000-00-00
Warnings:
Warning	1441	Datetime function: from_days field overflow
SELECT FROM_DAYS(4294967660), FROM_DAYS(4294967661), FROM_DAYS(4294967663);
FROM_DAYS(4294967660)	FROM_DAYS(4294967661)	FROM_DAYS(4294967663)
0000-00-00	0000-00-00	0000-00-00
INSERT t1 VALUES(ADDTIME(0,0)), (FROM_DAYS(3652499));
Warnings:
Warning	4095	Delimiter ':' in position 2 in datetime value '00:00:00' at row 1 is deprecated. Prefer the standard '-'.
Warning	1441	Datetime function: from_days field overflow
Warning	4095	Delimiter ':' in position 2 in datetime value '00:00:00' at row 1 is deprecated. Prefer the standard '-'.
Warning	1441	Datetime function: from_days field overflow
DROP TABLE t1;
SET sql_mode=default;
# Bug#35476172: MySQL server fails when executing query
CREATE TABLE t0(c1 INTEGER);
CREATE TABLE t1(c3 INTEGER)
PARTITION BY HASH ((SELECT c1 FROM t0));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '(SELECT c1 FROM t0))' at line 2
CREATE TABLE t1(c3 INTEGER)
PARTITION BY HASH ((SELECT c1 FROM t0))
AS TABLE t0;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '(SELECT c1 FROM t0))
AS TABLE t0' at line 2
CREATE TABLE t1(c3 INTEGER)
PARTITION BY RANGE ((SELECT c1 FROM t0)) (PARTITION a1 VALUES LESS THAN (1));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '(SELECT c1 FROM t0)) (PARTITION a1 VALUES LESS THAN (1))' at line 2
CREATE TABLE t1(c3 INTEGER)
PARTITION BY RANGE ((SELECT c1 FROM t0)) (PARTITION a1 VALUES LESS THAN (1))
AS TABLE t0;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '(SELECT c1 FROM t0)) (PARTITION a1 VALUES LESS THAN (1))
AS TABLE t0' at line 2
CREATE TABLE t1(c3 INTEGER)
PARTITION BY LIST ((SELECT c1 FROM t0)) (PARTITION p0 VALUES IN (0));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '(SELECT c1 FROM t0)) (PARTITION p0 VALUES IN (0))' at line 2
CREATE TABLE t1(c3 INTEGER)
PARTITION BY LIST ((SELECT c1 FROM t0)) (PARTITION p0 VALUES IN (0))
AS TABLE t0;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '(SELECT c1 FROM t0)) (PARTITION p0 VALUES IN (0))
AS TABLE t0' at line 2
DROP TABLE t0;
