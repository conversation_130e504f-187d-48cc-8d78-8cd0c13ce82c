/*
 Copyright (c) 2010, 2025, Oracle and/or its affiliates.

 This program is free software; you can redistribute it and/or modify
 it under the terms of the GNU General Public License, version 2.0,
 as published by the Free Software Foundation.

 This program is designed to work with certain software (including
 but not limited to OpenSSL) that is licensed under separate terms,
 as designated in a particular file or component or in included license
 documentation.  The authors of MySQL hereby grant you an additional
 permission to link the program and your derivative works with the
 separately licensed software that they have either included with
 the program or referenced in the documentation.

 This program is distributed in the hope that it will be useful,
 but WITHOUT ANY WARRANTY; without even the implied warranty of
 MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 GNU General Public License, version 2.0, for more details.

 You should have received a copy of the GNU General Public License
 along with this program; if not, write to the Free Software
 Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
*/
/*
 * myjapi_MyJapi.hpp
 */

#ifndef myjapi_MyJapi_hpp
#define myjapi_MyJapi_hpp

#include <jni.h>
#include <stdint.h>

// API to implement against
#include "myapi.hpp"

// libraries
#include "helpers.hpp"
#include "jtie.hpp"

// The API stub functions in this file have mangled names that adhere
// to the JVM specification.  It is not necessary to include the
// function prototypes generated by the javah tool from the Java source,
//   #include "myjapi_MyJapi.h"
// if they are declared to receive "C" linkage here.
extern "C" {

// ---------------------------------------------------------------------------
// API JNI function stubs
// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f0(JNIEnv *env, jclass cls) {
  TRACE("void Java_myjapi_MyJapi_f010(JNIEnv *, jclass)");
  gcall_fv<f0>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT jstring JNICALL Java_myjapi_MyJapi_s012s(JNIEnv *env, jclass cls) {
  TRACE("jstring Java_myjapi_MyJapi_s012s(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_char_cp_jutf8null, s012>(env, cls);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s112s(JNIEnv *env, jclass cls,
                                                jstring p0) {
  TRACE("void Java_myjapi_MyJapi_s112s(JNIEnv *, jclass, jstring)");
  gcall_fv<ttrait_char_cp_jutf8null, s112>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s152s(JNIEnv *env, jclass cls,
                                                jstring p0) {
  TRACE("void Java_myjapi_MyJapi_s152s(JNIEnv *, jclass, jstring)");
  gcall_fv<ttrait_char_cpc_jutf8null, s152>(env, cls, p0);
}

JNIEXPORT jstring JNICALL Java_myjapi_MyJapi_s032s(JNIEnv *env, jclass cls) {
  TRACE("jstring Java_myjapi_MyJapi_s032s(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_char_p_jutf8null, s032>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_s010bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_s010bb0(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_void_0cp_bb, s010>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_s012bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_s012bb0(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_char_0cp_bb, s012>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_s030bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_s030bb0(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_void_0p_bb, s030>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_s032bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_s032bb0(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_char_0p_bb, s032>(env, cls);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s110bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s110bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_void_0cp_bb, s110>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s112bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s112bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0cp_bb, s112>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s130bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s130bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_void_0p_bb, s130>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s132bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s132bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0p_bb, s132>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s150bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s150bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_void_0cpc_bb, s150>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s152bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s152bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0cpc_bb, s152>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s170bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s170bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_void_0pc_bb, s170>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s172bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s172bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0pc_bb, s172>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_s010bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_s010bb1(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_void_1cp_bb, s010>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_s012bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_s012bb1(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_char_1cp_bb, s012>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_s030bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_s030bb1(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_void_1p_bb, s030>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_s032bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_s032bb1(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_char_1p_bb, s032>(env, cls);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s110bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s110bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_void_1cp_bb, s110>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s112bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s112bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_1cp_bb, s112>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s130bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s130bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_void_1p_bb, s130>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s132bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s132bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_1p_bb, s132>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s150bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s150bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_void_1cpc_bb, s150>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s152bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s152bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_1cpc_bb, s152>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s170bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s170bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_void_1pc_bb, s170>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s172bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s172bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_1pc_bb, s172>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_s210bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_s210bb(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_void_0cp_bb, s210>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_s212bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_s212bb(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_char_0cp_bb, s212>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_s230bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_s230bb(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_void_0p_bb, s230>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_s232bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_s232bb(JNIEnv * env, jclass cls)");
  return gcall_fr<ttrait_char_0p_bb, s232>(env, cls);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s310bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s310bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_void_0cp_bb, s310>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s312bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s312bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0cp_bb, s312>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s330bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s330bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_void_0p_bb, s330>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s332bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s332bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0p_bb, s332>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s350bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s350bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_void_0cpc_bb, s350>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s352bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s352bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0cpc_bb, s352>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s370bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s370bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_void_0pc_bb, s370>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_s372bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_s372bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0pc_bb, s372>(env, cls, p0);
}

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

JNIEXPORT jboolean JNICALL Java_myjapi_MyJapi_f031(JNIEnv *env, jclass cls) {
  TRACE("jboolean Java_myjapi_MyJapi_f031(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool, f031>(env, cls);
}

JNIEXPORT jbyte JNICALL Java_myjapi_MyJapi_f032(JNIEnv *env, jclass cls) {
  TRACE("jbyte Java_myjapi_MyJapi_f032(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char, f032>(env, cls);
}

JNIEXPORT jbyte JNICALL Java_myjapi_MyJapi_f033(JNIEnv *env, jclass cls) {
  TRACE("jbyte Java_myjapi_MyJapi_f033(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8, f033>(env, cls);
}

JNIEXPORT jbyte JNICALL Java_myjapi_MyJapi_f034(JNIEnv *env, jclass cls) {
  TRACE("jbyte Java_myjapi_MyJapi_f034(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8, f034>(env, cls);
}

JNIEXPORT jshort JNICALL Java_myjapi_MyJapi_f035(JNIEnv *env, jclass cls) {
  TRACE("jshort Java_myjapi_MyJapi_f035(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16, f035>(env, cls);
}

JNIEXPORT jshort JNICALL Java_myjapi_MyJapi_f036(JNIEnv *env, jclass cls) {
  TRACE("jshort Java_myjapi_MyJapi_f036(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16, f036>(env, cls);
}

JNIEXPORT jint JNICALL Java_myjapi_MyJapi_f037(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_MyJapi_f037(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32, f037>(env, cls);
}

JNIEXPORT jint JNICALL Java_myjapi_MyJapi_f038(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_MyJapi_f038(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32, f038>(env, cls);
}

JNIEXPORT jlong JNICALL Java_myjapi_MyJapi_f041(JNIEnv *env, jclass cls) {
  TRACE("jlong Java_myjapi_MyJapi_f041(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64, f041>(env, cls);
}

JNIEXPORT jlong JNICALL Java_myjapi_MyJapi_f042(JNIEnv *env, jclass cls) {
  TRACE("jlong Java_myjapi_MyJapi_f042(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64, f042>(env, cls);
}

JNIEXPORT jfloat JNICALL Java_myjapi_MyJapi_f043(JNIEnv *env, jclass cls) {
  TRACE("jfloat Java_myjapi_MyJapi_f043(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float, f043>(env, cls);
}

JNIEXPORT jdouble JNICALL Java_myjapi_MyJapi_f044(JNIEnv *env, jclass cls) {
  TRACE("jdouble Java_myjapi_MyJapi_f044(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double, f044>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f111(JNIEnv *env, jclass cls,
                                               jboolean p0) {
  TRACE("void Java_myjapi_MyJapi_f111(JNIEnv *, jclass, jboolean)");
  gcall_fv<ttrait_bool_c, f111>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f112(JNIEnv *env, jclass cls,
                                               jbyte p0) {
  TRACE("void Java_myjapi_MyJapi_f112(JNIEnv *, jclass, jbyte)");
  gcall_fv<ttrait_char_c, f112>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f113(JNIEnv *env, jclass cls,
                                               jbyte p0) {
  TRACE("void Java_myjapi_MyJapi_f113(JNIEnv *, jclass, jbyte)");
  gcall_fv<ttrait_int8_c, f113>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f114(JNIEnv *env, jclass cls,
                                               jbyte p0) {
  TRACE("void Java_myjapi_MyJapi_f114(JNIEnv *, jclass, jbyte)");
  gcall_fv<ttrait_uint8_c, f114>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f115(JNIEnv *env, jclass cls,
                                               jshort p0) {
  TRACE("void Java_myjapi_MyJapi_f115(JNIEnv *, jclass, jshort)");
  gcall_fv<ttrait_int16_c, f115>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f116(JNIEnv *env, jclass cls,
                                               jshort p0) {
  TRACE("void Java_myjapi_MyJapi_f116(JNIEnv *, jclass, jshort)");
  gcall_fv<ttrait_uint16_c, f116>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f117(JNIEnv *env, jclass cls,
                                               jint p0) {
  TRACE("void Java_myjapi_MyJapi_f117(JNIEnv *, jclass, jint)");
  gcall_fv<ttrait_int32_c, f117>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f118(JNIEnv *env, jclass cls,
                                               jint p0) {
  TRACE("void Java_myjapi_MyJapi_f118(JNIEnv *, jclass, jint)");
  gcall_fv<ttrait_uint32_c, f118>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f121(JNIEnv *env, jclass cls,
                                               jlong p0) {
  TRACE("void Java_myjapi_MyJapi_f121(JNIEnv *, jclass, jlong)");
  gcall_fv<ttrait_int64_c, f121>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f122(JNIEnv *env, jclass cls,
                                               jlong p0) {
  TRACE("void Java_myjapi_MyJapi_f122(JNIEnv *, jclass, jlong)");
  gcall_fv<ttrait_uint64_c, f122>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f123(JNIEnv *env, jclass cls,
                                               jfloat p0) {
  TRACE("void Java_myjapi_MyJapi_f123(JNIEnv *, jclass, jfloat)");
  gcall_fv<ttrait_float_c, f123>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f124(JNIEnv *env, jclass cls,
                                               jdouble p0) {
  TRACE("void Java_myjapi_MyJapi_f124(JNIEnv *, jclass, jdouble)");
  gcall_fv<ttrait_double_c, f124>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f131(JNIEnv *env, jclass cls,
                                               jboolean p0) {
  TRACE("void Java_myjapi_MyJapi_f131(JNIEnv *, jclass, jboolean)");
  gcall_fv<ttrait_bool, f131>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f132(JNIEnv *env, jclass cls,
                                               jbyte p0) {
  TRACE("void Java_myjapi_MyJapi_f132(JNIEnv *, jclass, jbyte)");
  gcall_fv<ttrait_char, f132>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f133(JNIEnv *env, jclass cls,
                                               jbyte p0) {
  TRACE("void Java_myjapi_MyJapi_f133(JNIEnv *, jclass, jbyte)");
  gcall_fv<ttrait_int8, f133>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f134(JNIEnv *env, jclass cls,
                                               jbyte p0) {
  TRACE("void Java_myjapi_MyJapi_f134(JNIEnv *, jclass, jbyte)");
  gcall_fv<ttrait_uint8, f134>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f135(JNIEnv *env, jclass cls,
                                               jshort p0) {
  TRACE("void Java_myjapi_MyJapi_f135(JNIEnv *, jclass, jshort)");
  gcall_fv<ttrait_int16, f135>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f136(JNIEnv *env, jclass cls,
                                               jshort p0) {
  TRACE("void Java_myjapi_MyJapi_f136(JNIEnv *, jclass, jshort)");
  gcall_fv<ttrait_uint16, f136>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f137(JNIEnv *env, jclass cls,
                                               jint p0) {
  TRACE("void Java_myjapi_MyJapi_f137(JNIEnv *, jclass, jint)");
  gcall_fv<ttrait_int32, f137>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f138(JNIEnv *env, jclass cls,
                                               jint p0) {
  TRACE("void Java_myjapi_MyJapi_f138(JNIEnv *, jclass, jint)");
  gcall_fv<ttrait_uint32, f138>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f141(JNIEnv *env, jclass cls,
                                               jlong p0) {
  TRACE("void Java_myjapi_MyJapi_f141(JNIEnv *, jclass, jlong)");
  gcall_fv<ttrait_int64, f141>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f142(JNIEnv *env, jclass cls,
                                               jlong p0) {
  TRACE("void Java_myjapi_MyJapi_f142(JNIEnv *, jclass, jlong)");
  gcall_fv<ttrait_uint64, f142>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f143(JNIEnv *env, jclass cls,
                                               jfloat p0) {
  TRACE("void Java_myjapi_MyJapi_f143(JNIEnv *, jclass, jfloat)");
  gcall_fv<ttrait_float, f143>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f144(JNIEnv *env, jclass cls,
                                               jdouble p0) {
  TRACE("void Java_myjapi_MyJapi_f144(JNIEnv *, jclass, jdouble)");
  gcall_fv<ttrait_double, f144>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f211bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f211bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_cr_bb, f211>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f212bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f212bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_cr_bb, f212>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f213bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f213bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_cr_bb, f213>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f214bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f214bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_cr_bb, f214>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f215bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f215bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_cr_bb, f215>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f216bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f216bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_cr_bb, f216>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f217bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f217bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_cr_bb, f217>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f218bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f218bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_cr_bb, f218>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f221bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f221bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_cr_bb, f221>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f222bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f222bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_cr_bb, f222>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f223bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f223bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_cr_bb, f223>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f224bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f224bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_cr_bb, f224>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f231bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f231bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_r_bb, f231>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f232bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f232bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_r_bb, f232>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f233bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f233bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_r_bb, f233>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f234bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f234bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_r_bb, f234>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f235bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f235bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_r_bb, f235>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f236bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f236bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_r_bb, f236>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f237bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f237bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_r_bb, f237>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f238bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f238bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_r_bb, f238>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f241bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f241bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_r_bb, f241>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f242bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f242bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_r_bb, f242>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f243bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f243bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_r_bb, f243>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f244bb(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f244bb(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_r_bb, f244>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f311bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f311bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_cr_bb, f311>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f312bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f312bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_cr_bb, f312>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f313bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f313bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_cr_bb, f313>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f314bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f314bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_cr_bb, f314>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f315bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f315bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_cr_bb, f315>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f316bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f316bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_cr_bb, f316>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f317bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f317bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_cr_bb, f317>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f318bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f318bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_cr_bb, f318>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f321bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f321bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_cr_bb, f321>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f322bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f322bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_cr_bb, f322>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f323bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f323bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_cr_bb, f323>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f324bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f324bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_cr_bb, f324>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f331bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f331bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_r_bb, f331>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f332bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f332bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_r_bb, f332>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f333bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f333bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_r_bb, f333>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f334bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f334bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_r_bb, f334>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f335bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f335bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_r_bb, f335>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f336bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f336bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_r_bb, f336>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f337bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f337bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_r_bb, f337>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f338bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f338bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_r_bb, f338>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f341bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f341bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_r_bb, f341>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f342bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f342bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_r_bb, f342>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f343bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f343bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_r_bb, f343>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f344bb(JNIEnv *env, jclass cls,
                                                 jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f344bb(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_r_bb, f344>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT jboolean JNICALL Java_myjapi_MyJapi_f211v(JNIEnv *env, jclass cls) {
  TRACE("jboolean Java_myjapi_MyJapi_f211v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_cr_v, f211>(env, cls);
}

JNIEXPORT jbyte JNICALL Java_myjapi_MyJapi_f212v(JNIEnv *env, jclass cls) {
  TRACE("jbyte Java_myjapi_MyJapi_f212v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_cr_v, f212>(env, cls);
}

JNIEXPORT jbyte JNICALL Java_myjapi_MyJapi_f213v(JNIEnv *env, jclass cls) {
  TRACE("jbyte Java_myjapi_MyJapi_f213v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_cr_v, f213>(env, cls);
}

JNIEXPORT jbyte JNICALL Java_myjapi_MyJapi_f214v(JNIEnv *env, jclass cls) {
  TRACE("jbyte Java_myjapi_MyJapi_f214v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_cr_v, f214>(env, cls);
}

JNIEXPORT jshort JNICALL Java_myjapi_MyJapi_f215v(JNIEnv *env, jclass cls) {
  TRACE("jshort Java_myjapi_MyJapi_f215v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_cr_v, f215>(env, cls);
}

JNIEXPORT jshort JNICALL Java_myjapi_MyJapi_f216v(JNIEnv *env, jclass cls) {
  TRACE("jshort Java_myjapi_MyJapi_f216v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_cr_v, f216>(env, cls);
}

JNIEXPORT jint JNICALL Java_myjapi_MyJapi_f217v(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_MyJapi_f217v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_cr_v, f217>(env, cls);
}

JNIEXPORT jint JNICALL Java_myjapi_MyJapi_f218v(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_MyJapi_f218v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_cr_v, f218>(env, cls);
}

JNIEXPORT jlong JNICALL Java_myjapi_MyJapi_f221v(JNIEnv *env, jclass cls) {
  TRACE("jlong Java_myjapi_MyJapi_f221v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_cr_v, f221>(env, cls);
}

JNIEXPORT jlong JNICALL Java_myjapi_MyJapi_f222v(JNIEnv *env, jclass cls) {
  TRACE("jlong Java_myjapi_MyJapi_f222v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_cr_v, f222>(env, cls);
}

JNIEXPORT jfloat JNICALL Java_myjapi_MyJapi_f223v(JNIEnv *env, jclass cls) {
  TRACE("jfloat Java_myjapi_MyJapi_f223v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_cr_v, f223>(env, cls);
}

JNIEXPORT jdouble JNICALL Java_myjapi_MyJapi_f224v(JNIEnv *env, jclass cls) {
  TRACE("jdouble Java_myjapi_MyJapi_f224v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_cr_v, f224>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT jboolean JNICALL Java_myjapi_MyJapi_f231v(JNIEnv *env, jclass cls) {
  TRACE("jboolean Java_myjapi_MyJapi_f231v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_r_v, f231>(env, cls);
}

JNIEXPORT jbyte JNICALL Java_myjapi_MyJapi_f232v(JNIEnv *env, jclass cls) {
  TRACE("jbyte Java_myjapi_MyJapi_f232v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_r_v, f232>(env, cls);
}

JNIEXPORT jbyte JNICALL Java_myjapi_MyJapi_f233v(JNIEnv *env, jclass cls) {
  TRACE("jbyte Java_myjapi_MyJapi_f233v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_r_v, f233>(env, cls);
}

JNIEXPORT jbyte JNICALL Java_myjapi_MyJapi_f234v(JNIEnv *env, jclass cls) {
  TRACE("jbyte Java_myjapi_MyJapi_f234v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_r_v, f234>(env, cls);
}

JNIEXPORT jshort JNICALL Java_myjapi_MyJapi_f235v(JNIEnv *env, jclass cls) {
  TRACE("jshort Java_myjapi_MyJapi_f235v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_r_v, f235>(env, cls);
}

JNIEXPORT jshort JNICALL Java_myjapi_MyJapi_f236v(JNIEnv *env, jclass cls) {
  TRACE("jshort Java_myjapi_MyJapi_f236v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_r_v, f236>(env, cls);
}

JNIEXPORT jint JNICALL Java_myjapi_MyJapi_f237v(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_MyJapi_f237v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_r_v, f237>(env, cls);
}

JNIEXPORT jint JNICALL Java_myjapi_MyJapi_f238v(JNIEnv *env, jclass cls) {
  TRACE("jint Java_myjapi_MyJapi_f238v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_r_v, f238>(env, cls);
}

JNIEXPORT jlong JNICALL Java_myjapi_MyJapi_f241v(JNIEnv *env, jclass cls) {
  TRACE("jlong Java_myjapi_MyJapi_f241v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_r_v, f241>(env, cls);
}

JNIEXPORT jlong JNICALL Java_myjapi_MyJapi_f242v(JNIEnv *env, jclass cls) {
  TRACE("jlong Java_myjapi_MyJapi_f242v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_r_v, f242>(env, cls);
}

JNIEXPORT jfloat JNICALL Java_myjapi_MyJapi_f243v(JNIEnv *env, jclass cls) {
  TRACE("jfloat Java_myjapi_MyJapi_f243v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_r_v, f243>(env, cls);
}

JNIEXPORT jdouble JNICALL Java_myjapi_MyJapi_f244v(JNIEnv *env, jclass cls) {
  TRACE("jdouble Java_myjapi_MyJapi_f244v(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_r_v, f244>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f311v(JNIEnv *env, jclass cls,
                                                jboolean p0) {
  TRACE("void Java_myjapi_MyJapi_f311v(JNIEnv *, jclass, jboolean)");
  gcall_fv<ttrait_bool_cr_v, f311>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f312v(JNIEnv *env, jclass cls,
                                                jbyte p0) {
  TRACE("void Java_myjapi_MyJapi_f312v(JNIEnv *, jclass, jbyte)");
  gcall_fv<ttrait_char_cr_v, f312>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f313v(JNIEnv *env, jclass cls,
                                                jbyte p0) {
  TRACE("void Java_myjapi_MyJapi_f313v(JNIEnv *, jclass, jbyte)");
  gcall_fv<ttrait_int8_cr_v, f313>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f314v(JNIEnv *env, jclass cls,
                                                jbyte p0) {
  TRACE("void Java_myjapi_MyJapi_f314v(JNIEnv *, jclass, jbyte)");
  gcall_fv<ttrait_uint8_cr_v, f314>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f315v(JNIEnv *env, jclass cls,
                                                jshort p0) {
  TRACE("void Java_myjapi_MyJapi_f315v(JNIEnv *, jclass, jshort)");
  gcall_fv<ttrait_int16_cr_v, f315>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f316v(JNIEnv *env, jclass cls,
                                                jshort p0) {
  TRACE("void Java_myjapi_MyJapi_f316v(JNIEnv *, jclass, jshort)");
  gcall_fv<ttrait_uint16_cr_v, f316>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f317v(JNIEnv *env, jclass cls,
                                                jint p0) {
  TRACE("void Java_myjapi_MyJapi_f317v(JNIEnv *, jclass, jint)");
  gcall_fv<ttrait_int32_cr_v, f317>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f318v(JNIEnv *env, jclass cls,
                                                jint p0) {
  TRACE("void Java_myjapi_MyJapi_f318v(JNIEnv *, jclass, jint)");
  gcall_fv<ttrait_uint32_cr_v, f318>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f321v(JNIEnv *env, jclass cls,
                                                jlong p0) {
  TRACE("void Java_myjapi_MyJapi_f321v(JNIEnv *, jclass, jlong)");
  gcall_fv<ttrait_int64_cr_v, f321>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f322v(JNIEnv *env, jclass cls,
                                                jlong p0) {
  TRACE("void Java_myjapi_MyJapi_f322v(JNIEnv *, jclass, jlong)");
  gcall_fv<ttrait_uint64_cr_v, f322>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f323v(JNIEnv *env, jclass cls,
                                                jfloat p0) {
  TRACE("void Java_myjapi_MyJapi_f323v(JNIEnv *, jclass, jfloat)");
  gcall_fv<ttrait_float_cr_v, f323>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f324v(JNIEnv *env, jclass cls,
                                                jdouble p0) {
  TRACE("void Java_myjapi_MyJapi_f324v(JNIEnv *, jclass, jdouble)");
  gcall_fv<ttrait_double_cr_v, f324>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f331v(JNIEnv *env, jclass cls,
                                                jbooleanArray p0) {
  TRACE("void Java_myjapi_MyJapi_f331v(JNIEnv *, jclass, jbooleanArray)");
  gcall_fv<ttrait_bool_r_a, f331>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f332v(JNIEnv *env, jclass cls,
                                                jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f332v(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_char_r_a, f332>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f333v(JNIEnv *env, jclass cls,
                                                jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f333v(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_int8_r_a, f333>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f334v(JNIEnv *env, jclass cls,
                                                jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f334v(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_uint8_r_a, f334>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f335v(JNIEnv *env, jclass cls,
                                                jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f335v(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_int16_r_a, f335>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f336v(JNIEnv *env, jclass cls,
                                                jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f336v(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_uint16_r_a, f336>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f337v(JNIEnv *env, jclass cls,
                                                jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f337v(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_int32_r_a, f337>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f338v(JNIEnv *env, jclass cls,
                                                jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f338v(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_uint32_r_a, f338>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f341v(JNIEnv *env, jclass cls,
                                                jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f341v(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_int64_r_a, f341>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f342v(JNIEnv *env, jclass cls,
                                                jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f342v(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_uint64_r_a, f342>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f343v(JNIEnv *env, jclass cls,
                                                jfloatArray p0) {
  TRACE("void Java_myjapi_MyJapi_f343v(JNIEnv *, jclass, jfloatArray)");
  gcall_fv<ttrait_float_r_a, f343>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f344v(JNIEnv *env, jclass cls,
                                                jdoubleArray p0) {
  TRACE("void Java_myjapi_MyJapi_f344v(JNIEnv *, jclass, jdoubleArray)");
  gcall_fv<ttrait_double_r_a, f344>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f411bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f411bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_0cp_bb, f411>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f412bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f412bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_0cp_bb, f412>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f413bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f413bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_0cp_bb, f413>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f414bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f414bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_0cp_bb, f414>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f415bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f415bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_0cp_bb, f415>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f416bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f416bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_0cp_bb, f416>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f417bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f417bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_0cp_bb, f417>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f418bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f418bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_0cp_bb, f418>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f421bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f421bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_0cp_bb, f421>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f422bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f422bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_0cp_bb, f422>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f423bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f423bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_0cp_bb, f423>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f424bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f424bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_0cp_bb, f424>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f431bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f431bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_0p_bb, f431>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f432bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f432bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_0p_bb, f432>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f433bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f433bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_0p_bb, f433>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f434bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f434bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_0p_bb, f434>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f435bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f435bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_0p_bb, f435>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f436bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f436bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_0p_bb, f436>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f437bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f437bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_0p_bb, f437>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f438bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f438bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_0p_bb, f438>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f441bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f441bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_0p_bb, f441>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f442bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f442bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_0p_bb, f442>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f443bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f443bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_0p_bb, f443>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f444bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f444bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_0p_bb, f444>(env, cls);
}

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f511bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f511bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_0cp_bb, f511>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f512bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f512bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0cp_bb, f512>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f513bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f513bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_0cp_bb, f513>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f514bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f514bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_0cp_bb, f514>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f515bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f515bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_0cp_bb, f515>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f516bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f516bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_0cp_bb, f516>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f517bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f517bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_0cp_bb, f517>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f518bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f518bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_0cp_bb, f518>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f521bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f521bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_0cp_bb, f521>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f522bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f522bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_0cp_bb, f522>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f523bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f523bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_0cp_bb, f523>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f524bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f524bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_0cp_bb, f524>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f531bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f531bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_0p_bb, f531>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f532bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f532bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0p_bb, f532>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f533bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f533bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_0p_bb, f533>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f534bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f534bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_0p_bb, f534>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f535bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f535bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_0p_bb, f535>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f536bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f536bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_0p_bb, f536>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f537bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f537bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_0p_bb, f537>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f538bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f538bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_0p_bb, f538>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f541bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f541bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_0p_bb, f541>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f542bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f542bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_0p_bb, f542>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f543bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f543bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_0p_bb, f543>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f544bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f544bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_0p_bb, f544>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f551bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f551bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_0cpc_bb, f551>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f552bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f552bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0cpc_bb, f552>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f553bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f553bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_0cpc_bb, f553>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f554bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f554bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_0cpc_bb, f554>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f555bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f555bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_0cpc_bb, f555>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f556bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f556bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_0cpc_bb, f556>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f557bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f557bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_0cpc_bb, f557>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f558bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f558bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_0cpc_bb, f558>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f561bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f561bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_0cpc_bb, f561>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f562bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f562bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_0cpc_bb, f562>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f563bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f563bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_0cpc_bb, f563>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f564bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f564bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_0cpc_bb, f564>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f571bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f571bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_0pc_bb, f571>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f572bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f572bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0pc_bb, f572>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f573bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f573bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_0pc_bb, f573>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f574bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f574bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_0pc_bb, f574>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f575bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f575bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_0pc_bb, f575>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f576bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f576bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_0pc_bb, f576>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f577bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f577bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_0pc_bb, f577>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f578bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f578bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_0pc_bb, f578>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f581bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f581bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_0pc_bb, f581>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f582bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f582bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_0pc_bb, f582>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f583bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f583bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_0pc_bb, f583>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f584bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f584bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_0pc_bb, f584>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f411bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f411bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_1cp_bb, f411>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f412bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f412bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_1cp_bb, f412>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f413bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f413bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_1cp_bb, f413>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f414bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f414bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_1cp_bb, f414>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f415bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f415bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_1cp_bb, f415>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f416bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f416bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_1cp_bb, f416>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f417bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f417bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_1cp_bb, f417>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f418bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f418bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_1cp_bb, f418>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f421bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f421bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_1cp_bb, f421>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f422bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f422bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_1cp_bb, f422>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f423bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f423bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_1cp_bb, f423>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f424bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f424bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_1cp_bb, f424>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f431bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f431bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_1p_bb, f431>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f432bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f432bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_1p_bb, f432>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f433bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f433bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_1p_bb, f433>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f434bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f434bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_1p_bb, f434>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f435bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f435bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_1p_bb, f435>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f436bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f436bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_1p_bb, f436>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f437bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f437bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_1p_bb, f437>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f438bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f438bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_1p_bb, f438>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f441bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f441bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_1p_bb, f441>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f442bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f442bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_1p_bb, f442>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f443bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f443bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_1p_bb, f443>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f444bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f444bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_1p_bb, f444>(env, cls);
}

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f511bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f511bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_1cp_bb, f511>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f512bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f512bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_1cp_bb, f512>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f513bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f513bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_1cp_bb, f513>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f514bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f514bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_1cp_bb, f514>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f515bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f515bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_1cp_bb, f515>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f516bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f516bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_1cp_bb, f516>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f517bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f517bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_1cp_bb, f517>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f518bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f518bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_1cp_bb, f518>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f521bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f521bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_1cp_bb, f521>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f522bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f522bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_1cp_bb, f522>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f523bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f523bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_1cp_bb, f523>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f524bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f524bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_1cp_bb, f524>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f531bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f531bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_1p_bb, f531>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f532bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f532bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_1p_bb, f532>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f533bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f533bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_1p_bb, f533>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f534bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f534bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_1p_bb, f534>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f535bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f535bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_1p_bb, f535>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f536bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f536bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_1p_bb, f536>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f537bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f537bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_1p_bb, f537>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f538bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f538bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_1p_bb, f538>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f541bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f541bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_1p_bb, f541>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f542bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f542bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_1p_bb, f542>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f543bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f543bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_1p_bb, f543>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f544bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f544bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_1p_bb, f544>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f551bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f551bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_1cpc_bb, f551>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f552bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f552bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_1cpc_bb, f552>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f553bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f553bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_1cpc_bb, f553>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f554bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f554bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_1cpc_bb, f554>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f555bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f555bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_1cpc_bb, f555>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f556bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f556bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_1cpc_bb, f556>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f557bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f557bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_1cpc_bb, f557>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f558bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f558bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_1cpc_bb, f558>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f561bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f561bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_1cpc_bb, f561>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f562bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f562bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_1cpc_bb, f562>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f563bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f563bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_1cpc_bb, f563>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f564bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f564bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_1cpc_bb, f564>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f571bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f571bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_1pc_bb, f571>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f572bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f572bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_1pc_bb, f572>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f573bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f573bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_1pc_bb, f573>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f574bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f574bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_1pc_bb, f574>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f575bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f575bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_1pc_bb, f575>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f576bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f576bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_1pc_bb, f576>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f577bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f577bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_1pc_bb, f577>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f578bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f578bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_1pc_bb, f578>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f581bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f581bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_1pc_bb, f581>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f582bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f582bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_1pc_bb, f582>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f583bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f583bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_1pc_bb, f583>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f584bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f584bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_1pc_bb, f584>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT jbooleanArray JNICALL Java_myjapi_MyJapi_f411v1(JNIEnv *env,
                                                          jclass cls) {
  TRACE("jbooleanArray Java_myjapi_MyJapi_f411v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_1cp_a, f411>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f412v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f412v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_1cp_a, f412>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f413v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f413v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_1cp_a, f413>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f414v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f414v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_1cp_a, f414>(env, cls);
}

JNIEXPORT jshortArray JNICALL Java_myjapi_MyJapi_f415v1(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jshortArray Java_myjapi_MyJapi_f415v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_1cp_a, f415>(env, cls);
}

JNIEXPORT jshortArray JNICALL Java_myjapi_MyJapi_f416v1(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jshortArray Java_myjapi_MyJapi_f416v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_1cp_a, f416>(env, cls);
}

JNIEXPORT jintArray JNICALL Java_myjapi_MyJapi_f417v1(JNIEnv *env, jclass cls) {
  TRACE("jintArray Java_myjapi_MyJapi_f417v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_1cp_a, f417>(env, cls);
}

JNIEXPORT jintArray JNICALL Java_myjapi_MyJapi_f418v1(JNIEnv *env, jclass cls) {
  TRACE("jintArray Java_myjapi_MyJapi_f418v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_1cp_a, f418>(env, cls);
}

JNIEXPORT jlongArray JNICALL Java_myjapi_MyJapi_f421v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jlongArray Java_myjapi_MyJapi_f421v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_1cp_a, f421>(env, cls);
}

JNIEXPORT jlongArray JNICALL Java_myjapi_MyJapi_f422v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jlongArray Java_myjapi_MyJapi_f422v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_1cp_a, f422>(env, cls);
}

JNIEXPORT jfloatArray JNICALL Java_myjapi_MyJapi_f423v1(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jfloatArray Java_myjapi_MyJapi_f423v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_1cp_a, f423>(env, cls);
}

JNIEXPORT jdoubleArray JNICALL Java_myjapi_MyJapi_f424v1(JNIEnv *env,
                                                         jclass cls) {
  TRACE("jdoubleArray Java_myjapi_MyJapi_f424v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_1cp_a, f424>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT jbooleanArray JNICALL Java_myjapi_MyJapi_f431v1(JNIEnv *env,
                                                          jclass cls) {
  TRACE("jbooleanArray Java_myjapi_MyJapi_f431v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_1p_a, f431>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f432v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f432v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_1p_a, f432>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f433v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f433v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_1p_a, f433>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f434v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f434v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_1p_a, f434>(env, cls);
}

JNIEXPORT jshortArray JNICALL Java_myjapi_MyJapi_f435v1(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jshortArray Java_myjapi_MyJapi_f435v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_1p_a, f435>(env, cls);
}

JNIEXPORT jshortArray JNICALL Java_myjapi_MyJapi_f436v1(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jshortArray Java_myjapi_MyJapi_f436v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_1p_a, f436>(env, cls);
}

JNIEXPORT jintArray JNICALL Java_myjapi_MyJapi_f437v1(JNIEnv *env, jclass cls) {
  TRACE("jintArray Java_myjapi_MyJapi_f437v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_1p_a, f437>(env, cls);
}

JNIEXPORT jintArray JNICALL Java_myjapi_MyJapi_f438v1(JNIEnv *env, jclass cls) {
  TRACE("jintArray Java_myjapi_MyJapi_f438v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_1p_a, f438>(env, cls);
}

JNIEXPORT jlongArray JNICALL Java_myjapi_MyJapi_f441v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jlongArray Java_myjapi_MyJapi_f441v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_1p_a, f441>(env, cls);
}

JNIEXPORT jlongArray JNICALL Java_myjapi_MyJapi_f442v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jlongArray Java_myjapi_MyJapi_f442v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_1p_a, f442>(env, cls);
}

JNIEXPORT jfloatArray JNICALL Java_myjapi_MyJapi_f443v1(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jfloatArray Java_myjapi_MyJapi_f443v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_1p_a, f443>(env, cls);
}

JNIEXPORT jdoubleArray JNICALL Java_myjapi_MyJapi_f444v1(JNIEnv *env,
                                                         jclass cls) {
  TRACE("jdoubleArray Java_myjapi_MyJapi_f444v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_1p_a, f444>(env, cls);
}

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f511v1(JNIEnv *env, jclass cls,
                                                 jbooleanArray p0) {
  TRACE("void Java_myjapi_MyJapi_f511v1(JNIEnv *, jclass, jbooleanArray)");
  gcall_fv<ttrait_bool_1cp_a, f511>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f512v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f512v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_char_1cp_a, f512>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f513v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f513v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_int8_1cp_a, f513>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f514v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f514v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_uint8_1cp_a, f514>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f515v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f515v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_int16_1cp_a, f515>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f516v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f516v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_uint16_1cp_a, f516>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f517v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f517v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_int32_1cp_a, f517>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f518v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f518v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_uint32_1cp_a, f518>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f521v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f521v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_int64_1cp_a, f521>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f522v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f522v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_uint64_1cp_a, f522>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f523v1(JNIEnv *env, jclass cls,
                                                 jfloatArray p0) {
  TRACE("void Java_myjapi_MyJapi_f523v1(JNIEnv *, jclass, jfloatArray)");
  gcall_fv<ttrait_float_1cp_a, f523>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f524v1(JNIEnv *env, jclass cls,
                                                 jdoubleArray p0) {
  TRACE("void Java_myjapi_MyJapi_f524v1(JNIEnv *, jclass, jdoubleArray)");
  gcall_fv<ttrait_double_1cp_a, f524>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f531v1(JNIEnv *env, jclass cls,
                                                 jbooleanArray p0) {
  TRACE("void Java_myjapi_MyJapi_f531v1(JNIEnv *, jclass, jbooleanArray)");
  gcall_fv<ttrait_bool_1p_a, f531>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f532v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f532v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_char_1p_a, f532>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f533v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f533v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_int8_1p_a, f533>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f534v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f534v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_uint8_1p_a, f534>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f535v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f535v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_int16_1p_a, f535>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f536v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f536v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_uint16_1p_a, f536>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f537v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f537v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_int32_1p_a, f537>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f538v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f538v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_uint32_1p_a, f538>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f541v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f541v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_int64_1p_a, f541>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f542v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f542v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_uint64_1p_a, f542>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f543v1(JNIEnv *env, jclass cls,
                                                 jfloatArray p0) {
  TRACE("void Java_myjapi_MyJapi_f543v1(JNIEnv *, jclass, jfloatArray)");
  gcall_fv<ttrait_float_1p_a, f543>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f544v1(JNIEnv *env, jclass cls,
                                                 jdoubleArray p0) {
  TRACE("void Java_myjapi_MyJapi_f544v1(JNIEnv *, jclass, jdoubleArray)");
  gcall_fv<ttrait_double_1p_a, f544>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f551v1(JNIEnv *env, jclass cls,
                                                 jbooleanArray p0) {
  TRACE("void Java_myjapi_MyJapi_f551v1(JNIEnv *, jclass, jbooleanArray)");
  gcall_fv<ttrait_bool_1cpc_a, f551>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f552v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f552v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_char_1cpc_a, f552>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f553v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f553v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_int8_1cpc_a, f553>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f554v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f554v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_uint8_1cpc_a, f554>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f555v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f555v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_int16_1cpc_a, f555>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f556v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f556v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_uint16_1cpc_a, f556>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f557v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f557v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_int32_1cpc_a, f557>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f558v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f558v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_uint32_1cpc_a, f558>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f561v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f561v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_int64_1cpc_a, f561>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f562v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f562v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_uint64_1cpc_a, f562>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f563v1(JNIEnv *env, jclass cls,
                                                 jfloatArray p0) {
  TRACE("void Java_myjapi_MyJapi_f563v1(JNIEnv *, jclass, jfloatArray)");
  gcall_fv<ttrait_float_1cpc_a, f563>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f564v1(JNIEnv *env, jclass cls,
                                                 jdoubleArray p0) {
  TRACE("void Java_myjapi_MyJapi_f564v1(JNIEnv *, jclass, jdoubleArray)");
  gcall_fv<ttrait_double_1cpc_a, f564>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f571v1(JNIEnv *env, jclass cls,
                                                 jbooleanArray p0) {
  TRACE("void Java_myjapi_MyJapi_f571v1(JNIEnv *, jclass, jbooleanArray)");
  gcall_fv<ttrait_bool_1pc_a, f571>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f572v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f572v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_char_1pc_a, f572>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f573v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f573v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_int8_1pc_a, f573>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f574v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f574v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_uint8_1pc_a, f574>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f575v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f575v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_int16_1pc_a, f575>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f576v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f576v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_uint16_1pc_a, f576>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f577v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f577v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_int32_1pc_a, f577>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f578v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f578v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_uint32_1pc_a, f578>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f581v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f581v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_int64_1pc_a, f581>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f582v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f582v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_uint64_1pc_a, f582>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f583v1(JNIEnv *env, jclass cls,
                                                 jfloatArray p0) {
  TRACE("void Java_myjapi_MyJapi_f583v1(JNIEnv *, jclass, jfloatArray)");
  gcall_fv<ttrait_float_1pc_a, f583>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f584v1(JNIEnv *env, jclass cls,
                                                 jdoubleArray p0) {
  TRACE("void Java_myjapi_MyJapi_f584v1(JNIEnv *, jclass, jdoubleArray)");
  gcall_fv<ttrait_double_1pc_a, f584>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f611bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f611bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_0cp_bb, f611>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f612bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f612bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_0cp_bb, f612>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f613bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f613bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_0cp_bb, f613>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f614bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f614bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_0cp_bb, f614>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f615bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f615bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_0cp_bb, f615>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f616bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f616bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_0cp_bb, f616>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f617bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f617bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_0cp_bb, f617>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f618bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f618bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_0cp_bb, f618>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f621bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f621bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_0cp_bb, f621>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f622bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f622bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_0cp_bb, f622>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f623bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f623bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_0cp_bb, f623>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f624bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f624bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_0cp_bb, f624>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f631bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f631bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_0p_bb, f631>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f632bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f632bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_0p_bb, f632>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f633bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f633bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_0p_bb, f633>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f634bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f634bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_0p_bb, f634>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f635bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f635bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_0p_bb, f635>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f636bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f636bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_0p_bb, f636>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f637bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f637bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_0p_bb, f637>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f638bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f638bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_0p_bb, f638>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f641bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f641bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_0p_bb, f641>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f642bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f642bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_0p_bb, f642>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f643bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f643bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_0p_bb, f643>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f644bb0(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f644bb0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_0p_bb, f644>(env, cls);
}

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f711bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f711bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_0cp_bb, f711>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f712bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f712bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0cp_bb, f712>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f713bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f713bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_0cp_bb, f713>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f714bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f714bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_0cp_bb, f714>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f715bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f715bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_0cp_bb, f715>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f716bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f716bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_0cp_bb, f716>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f717bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f717bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_0cp_bb, f717>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f718bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f718bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_0cp_bb, f718>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f721bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f721bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_0cp_bb, f721>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f722bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f722bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_0cp_bb, f722>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f723bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f723bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_0cp_bb, f723>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f724bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f724bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_0cp_bb, f724>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f731bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f731bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_0p_bb, f731>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f732bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f732bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0p_bb, f732>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f733bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f733bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_0p_bb, f733>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f734bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f734bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_0p_bb, f734>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f735bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f735bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_0p_bb, f735>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f736bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f736bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_0p_bb, f736>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f737bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f737bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_0p_bb, f737>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f738bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f738bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_0p_bb, f738>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f741bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f741bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_0p_bb, f741>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f742bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f742bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_0p_bb, f742>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f743bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f743bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_0p_bb, f743>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f744bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f744bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_0p_bb, f744>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f751bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f751bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_0cpc_bb, f751>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f752bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f752bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0cpc_bb, f752>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f753bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f753bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_0cpc_bb, f753>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f754bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f754bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_0cpc_bb, f754>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f755bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f755bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_0cpc_bb, f755>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f756bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f756bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_0cpc_bb, f756>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f757bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f757bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_0cpc_bb, f757>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f758bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f758bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_0cpc_bb, f758>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f761bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f761bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_0cpc_bb, f761>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f762bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f762bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_0cpc_bb, f762>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f763bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f763bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_0cpc_bb, f763>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f764bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f764bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_0cpc_bb, f764>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f771bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f771bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_0pc_bb, f771>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f772bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f772bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_0pc_bb, f772>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f773bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f773bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_0pc_bb, f773>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f774bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f774bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_0pc_bb, f774>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f775bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f775bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_0pc_bb, f775>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f776bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f776bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_0pc_bb, f776>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f777bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f777bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_0pc_bb, f777>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f778bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f778bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_0pc_bb, f778>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f781bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f781bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_0pc_bb, f781>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f782bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f782bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_0pc_bb, f782>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f783bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f783bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_0pc_bb, f783>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f784bb0(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f784bb0(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_0pc_bb, f784>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f611bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f611bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_1cp_bb, f611>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f612bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f612bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_1cp_bb, f612>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f613bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f613bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_1cp_bb, f613>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f614bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f614bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_1cp_bb, f614>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f615bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f615bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_1cp_bb, f615>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f616bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f616bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_1cp_bb, f616>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f617bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f617bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_1cp_bb, f617>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f618bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f618bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_1cp_bb, f618>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f621bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f621bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_1cp_bb, f621>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f622bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f622bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_1cp_bb, f622>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f623bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f623bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_1cp_bb, f623>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f624bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f624bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_1cp_bb, f624>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f631bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f631bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_1p_bb, f631>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f632bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f632bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_1p_bb, f632>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f633bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f633bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_1p_bb, f633>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f634bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f634bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_1p_bb, f634>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f635bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f635bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_1p_bb, f635>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f636bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f636bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_1p_bb, f636>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f637bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f637bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_1p_bb, f637>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f638bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f638bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_1p_bb, f638>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f641bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f641bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_1p_bb, f641>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f642bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f642bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_1p_bb, f642>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f643bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f643bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_1p_bb, f643>(env, cls);
}

JNIEXPORT jobject JNICALL Java_myjapi_MyJapi_f644bb1(JNIEnv *env, jclass cls) {
  TRACE("jobject Java_myjapi_MyJapi_f644bb1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_1p_bb, f644>(env, cls);
}

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f711bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f711bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_1cp_bb, f711>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f712bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f712bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_1cp_bb, f712>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f713bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f713bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_1cp_bb, f713>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f714bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f714bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_1cp_bb, f714>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f715bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f715bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_1cp_bb, f715>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f716bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f716bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_1cp_bb, f716>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f717bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f717bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_1cp_bb, f717>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f718bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f718bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_1cp_bb, f718>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f721bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f721bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_1cp_bb, f721>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f722bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f722bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_1cp_bb, f722>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f723bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f723bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_1cp_bb, f723>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f724bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f724bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_1cp_bb, f724>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f731bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f731bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_1p_bb, f731>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f732bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f732bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_1p_bb, f732>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f733bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f733bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_1p_bb, f733>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f734bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f734bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_1p_bb, f734>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f735bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f735bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_1p_bb, f735>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f736bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f736bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_1p_bb, f736>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f737bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f737bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_1p_bb, f737>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f738bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f738bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_1p_bb, f738>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f741bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f741bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_1p_bb, f741>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f742bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f742bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_1p_bb, f742>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f743bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f743bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_1p_bb, f743>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f744bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f744bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_1p_bb, f744>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f751bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f751bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_1cpc_bb, f751>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f752bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f752bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_1cpc_bb, f752>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f753bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f753bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_1cpc_bb, f753>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f754bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f754bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_1cpc_bb, f754>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f755bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f755bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_1cpc_bb, f755>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f756bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f756bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_1cpc_bb, f756>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f757bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f757bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_1cpc_bb, f757>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f758bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f758bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_1cpc_bb, f758>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f761bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f761bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_1cpc_bb, f761>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f762bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f762bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_1cpc_bb, f762>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f763bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f763bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_1cpc_bb, f763>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f764bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f764bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_1cpc_bb, f764>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f771bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f771bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_bool_1pc_bb, f771>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f772bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f772bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_char_1pc_bb, f772>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f773bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f773bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int8_1pc_bb, f773>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f774bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f774bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint8_1pc_bb, f774>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f775bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f775bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int16_1pc_bb, f775>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f776bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f776bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint16_1pc_bb, f776>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f777bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f777bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int32_1pc_bb, f777>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f778bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f778bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint32_1pc_bb, f778>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f781bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f781bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_int64_1pc_bb, f781>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f782bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f782bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_uint64_1pc_bb, f782>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f783bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f783bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_float_1pc_bb, f783>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f784bb1(JNIEnv *env, jclass cls,
                                                  jobject p0) {
  TRACE("void Java_myjapi_MyJapi_f784bb1(JNIEnv *, jclass, jobject)");
  gcall_fv<ttrait_double_1pc_bb, f784>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT jbooleanArray JNICALL Java_myjapi_MyJapi_f611v0(JNIEnv *env,
                                                          jclass cls) {
  TRACE("jbooleanArray Java_myjapi_MyJapi_f611v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_0cp_a, f611>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f612v0(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f612v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_0cp_a, f612>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f613v0(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f613v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_0cp_a, f613>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f614v0(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f614v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_0cp_a, f614>(env, cls);
}

JNIEXPORT jshortArray JNICALL Java_myjapi_MyJapi_f615v0(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jshortArray Java_myjapi_MyJapi_f615v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_0cp_a, f615>(env, cls);
}

JNIEXPORT jshortArray JNICALL Java_myjapi_MyJapi_f616v0(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jshortArray Java_myjapi_MyJapi_f616v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_0cp_a, f616>(env, cls);
}

JNIEXPORT jintArray JNICALL Java_myjapi_MyJapi_f617v0(JNIEnv *env, jclass cls) {
  TRACE("jintArray Java_myjapi_MyJapi_f617v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_0cp_a, f617>(env, cls);
}

JNIEXPORT jintArray JNICALL Java_myjapi_MyJapi_f618v0(JNIEnv *env, jclass cls) {
  TRACE("jintArray Java_myjapi_MyJapi_f618v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_0cp_a, f618>(env, cls);
}

JNIEXPORT jlongArray JNICALL Java_myjapi_MyJapi_f621v0(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jlongArray Java_myjapi_MyJapi_f621v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_0cp_a, f621>(env, cls);
}

JNIEXPORT jlongArray JNICALL Java_myjapi_MyJapi_f622v0(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jlongArray Java_myjapi_MyJapi_f622v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_0cp_a, f622>(env, cls);
}

JNIEXPORT jfloatArray JNICALL Java_myjapi_MyJapi_f623v0(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jfloatArray Java_myjapi_MyJapi_f623v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_0cp_a, f623>(env, cls);
}

JNIEXPORT jdoubleArray JNICALL Java_myjapi_MyJapi_f624v0(JNIEnv *env,
                                                         jclass cls) {
  TRACE("jdoubleArray Java_myjapi_MyJapi_f624v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_0cp_a, f624>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT jbooleanArray JNICALL Java_myjapi_MyJapi_f631v0(JNIEnv *env,
                                                          jclass cls) {
  TRACE("jbooleanArray Java_myjapi_MyJapi_f631v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_0p_a, f631>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f632v0(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f632v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_0p_a, f632>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f633v0(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f633v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_0p_a, f633>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f634v0(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f634v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_0p_a, f634>(env, cls);
}

JNIEXPORT jshortArray JNICALL Java_myjapi_MyJapi_f635v0(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jshortArray Java_myjapi_MyJapi_f635v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_0p_a, f635>(env, cls);
}

JNIEXPORT jshortArray JNICALL Java_myjapi_MyJapi_f636v0(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jshortArray Java_myjapi_MyJapi_f636v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_0p_a, f636>(env, cls);
}

JNIEXPORT jintArray JNICALL Java_myjapi_MyJapi_f637v0(JNIEnv *env, jclass cls) {
  TRACE("jintArray Java_myjapi_MyJapi_f637v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_0p_a, f637>(env, cls);
}

JNIEXPORT jintArray JNICALL Java_myjapi_MyJapi_f638v0(JNIEnv *env, jclass cls) {
  TRACE("jintArray Java_myjapi_MyJapi_f638v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_0p_a, f638>(env, cls);
}

JNIEXPORT jlongArray JNICALL Java_myjapi_MyJapi_f641v0(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jlongArray Java_myjapi_MyJapi_f641v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_0p_a, f641>(env, cls);
}

JNIEXPORT jlongArray JNICALL Java_myjapi_MyJapi_f642v0(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jlongArray Java_myjapi_MyJapi_f642v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_0p_a, f642>(env, cls);
}

JNIEXPORT jfloatArray JNICALL Java_myjapi_MyJapi_f643v0(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jfloatArray Java_myjapi_MyJapi_f643v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_0p_a, f643>(env, cls);
}

JNIEXPORT jdoubleArray JNICALL Java_myjapi_MyJapi_f644v0(JNIEnv *env,
                                                         jclass cls) {
  TRACE("jdoubleArray Java_myjapi_MyJapi_f644v0(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_0p_a, f644>(env, cls);
}

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f711v0(JNIEnv *env, jclass cls,
                                                 jbooleanArray p0) {
  TRACE("void Java_myjapi_MyJapi_f711v0(JNIEnv *, jclass, jbooleanArray)");
  gcall_fv<ttrait_bool_0cp_a, f711>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f712v0(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f712v0(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_char_0cp_a, f712>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f713v0(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f713v0(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_int8_0cp_a, f713>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f714v0(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f714v0(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_uint8_0cp_a, f714>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f715v0(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f715v0(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_int16_0cp_a, f715>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f716v0(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f716v0(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_uint16_0cp_a, f716>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f717v0(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f717v0(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_int32_0cp_a, f717>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f718v0(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f718v0(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_uint32_0cp_a, f718>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f721v0(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f721v0(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_int64_0cp_a, f721>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f722v0(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f722v0(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_uint64_0cp_a, f722>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f723v0(JNIEnv *env, jclass cls,
                                                 jfloatArray p0) {
  TRACE("void Java_myjapi_MyJapi_f723v0(JNIEnv *, jclass, jfloatArray)");
  gcall_fv<ttrait_float_0cp_a, f723>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f724v0(JNIEnv *env, jclass cls,
                                                 jdoubleArray p0) {
  TRACE("void Java_myjapi_MyJapi_f724v0(JNIEnv *, jclass, jdoubleArray)");
  gcall_fv<ttrait_double_0cp_a, f724>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f731v0(JNIEnv *env, jclass cls,
                                                 jbooleanArray p0) {
  TRACE("void Java_myjapi_MyJapi_f731v0(JNIEnv *, jclass, jbooleanArray)");
  gcall_fv<ttrait_bool_0p_a, f731>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f732v0(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f732v0(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_char_0p_a, f732>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f733v0(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f733v0(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_int8_0p_a, f733>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f734v0(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f734v0(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_uint8_0p_a, f734>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f735v0(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f735v0(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_int16_0p_a, f735>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f736v0(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f736v0(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_uint16_0p_a, f736>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f737v0(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f737v0(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_int32_0p_a, f737>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f738v0(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f738v0(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_uint32_0p_a, f738>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f741v0(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f741v0(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_int64_0p_a, f741>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f742v0(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f742v0(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_uint64_0p_a, f742>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f743v0(JNIEnv *env, jclass cls,
                                                 jfloatArray p0) {
  TRACE("void Java_myjapi_MyJapi_f743v0(JNIEnv *, jclass, jfloatArray)");
  gcall_fv<ttrait_float_0p_a, f743>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f744v0(JNIEnv *env, jclass cls,
                                                 jdoubleArray p0) {
  TRACE("void Java_myjapi_MyJapi_f744v0(JNIEnv *, jclass, jdoubleArray)");
  gcall_fv<ttrait_double_0p_a, f744>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f751v0(JNIEnv *env, jclass cls,
                                                 jbooleanArray p0) {
  TRACE("void Java_myjapi_MyJapi_f751v0(JNIEnv *, jclass, jbooleanArray)");
  gcall_fv<ttrait_bool_0cpc_a, f751>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f752v0(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f752v0(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_char_0cpc_a, f752>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f753v0(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f753v0(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_int8_0cpc_a, f753>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f754v0(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f754v0(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_uint8_0cpc_a, f754>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f755v0(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f755v0(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_int16_0cpc_a, f755>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f756v0(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f756v0(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_uint16_0cpc_a, f756>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f757v0(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f757v0(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_int32_0cpc_a, f757>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f758v0(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f758v0(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_uint32_0cpc_a, f758>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f761v0(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f761v0(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_int64_0cpc_a, f761>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f762v0(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f762v0(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_uint64_0cpc_a, f762>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f763v0(JNIEnv *env, jclass cls,
                                                 jfloatArray p0) {
  TRACE("void Java_myjapi_MyJapi_f763v0(JNIEnv *, jclass, jfloatArray)");
  gcall_fv<ttrait_float_0cpc_a, f763>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f764v0(JNIEnv *env, jclass cls,
                                                 jdoubleArray p0) {
  TRACE("void Java_myjapi_MyJapi_f764v0(JNIEnv *, jclass, jdoubleArray)");
  gcall_fv<ttrait_double_0cpc_a, f764>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f771v0(JNIEnv *env, jclass cls,
                                                 jbooleanArray p0) {
  TRACE("void Java_myjapi_MyJapi_f771v0(JNIEnv *, jclass, jbooleanArray)");
  gcall_fv<ttrait_bool_0pc_a, f771>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f772v0(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f772v0(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_char_0pc_a, f772>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f773v0(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f773v0(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_int8_0pc_a, f773>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f774v0(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f774v0(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_uint8_0pc_a, f774>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f775v0(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f775v0(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_int16_0pc_a, f775>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f776v0(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f776v0(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_uint16_0pc_a, f776>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f777v0(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f777v0(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_int32_0pc_a, f777>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f778v0(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f778v0(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_uint32_0pc_a, f778>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f781v0(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f781v0(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_int64_0pc_a, f781>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f782v0(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f782v0(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_uint64_0pc_a, f782>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f783v0(JNIEnv *env, jclass cls,
                                                 jfloatArray p0) {
  TRACE("void Java_myjapi_MyJapi_f783v0(JNIEnv *, jclass, jfloatArray)");
  gcall_fv<ttrait_float_0pc_a, f783>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f784v0(JNIEnv *env, jclass cls,
                                                 jdoubleArray p0) {
  TRACE("void Java_myjapi_MyJapi_f784v0(JNIEnv *, jclass, jdoubleArray)");
  gcall_fv<ttrait_double_0pc_a, f784>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT jbooleanArray JNICALL Java_myjapi_MyJapi_f611v1(JNIEnv *env,
                                                          jclass cls) {
  TRACE("jbooleanArray Java_myjapi_MyJapi_f611v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_1cp_a, f611>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f612v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f612v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_1cp_a, f612>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f613v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f613v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_1cp_a, f613>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f614v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f614v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_1cp_a, f614>(env, cls);
}

JNIEXPORT jshortArray JNICALL Java_myjapi_MyJapi_f615v1(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jshortArray Java_myjapi_MyJapi_f615v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_1cp_a, f615>(env, cls);
}

JNIEXPORT jshortArray JNICALL Java_myjapi_MyJapi_f616v1(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jshortArray Java_myjapi_MyJapi_f616v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_1cp_a, f616>(env, cls);
}

JNIEXPORT jintArray JNICALL Java_myjapi_MyJapi_f617v1(JNIEnv *env, jclass cls) {
  TRACE("jintArray Java_myjapi_MyJapi_f617v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_1cp_a, f617>(env, cls);
}

JNIEXPORT jintArray JNICALL Java_myjapi_MyJapi_f618v1(JNIEnv *env, jclass cls) {
  TRACE("jintArray Java_myjapi_MyJapi_f618v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_1cp_a, f618>(env, cls);
}

JNIEXPORT jlongArray JNICALL Java_myjapi_MyJapi_f621v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jlongArray Java_myjapi_MyJapi_f621v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_1cp_a, f621>(env, cls);
}

JNIEXPORT jlongArray JNICALL Java_myjapi_MyJapi_f622v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jlongArray Java_myjapi_MyJapi_f622v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_1cp_a, f622>(env, cls);
}

JNIEXPORT jfloatArray JNICALL Java_myjapi_MyJapi_f623v1(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jfloatArray Java_myjapi_MyJapi_f623v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_1cp_a, f623>(env, cls);
}

JNIEXPORT jdoubleArray JNICALL Java_myjapi_MyJapi_f624v1(JNIEnv *env,
                                                         jclass cls) {
  TRACE("jdoubleArray Java_myjapi_MyJapi_f624v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_1cp_a, f624>(env, cls);
}

// ---------------------------------------------------------------------------

JNIEXPORT jbooleanArray JNICALL Java_myjapi_MyJapi_f631v1(JNIEnv *env,
                                                          jclass cls) {
  TRACE("jbooleanArray Java_myjapi_MyJapi_f631v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_bool_1p_a, f631>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f632v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f632v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_char_1p_a, f632>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f633v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f633v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int8_1p_a, f633>(env, cls);
}

JNIEXPORT jbyteArray JNICALL Java_myjapi_MyJapi_f634v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jbyteArray Java_myjapi_MyJapi_f634v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint8_1p_a, f634>(env, cls);
}

JNIEXPORT jshortArray JNICALL Java_myjapi_MyJapi_f635v1(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jshortArray Java_myjapi_MyJapi_f635v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int16_1p_a, f635>(env, cls);
}

JNIEXPORT jshortArray JNICALL Java_myjapi_MyJapi_f636v1(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jshortArray Java_myjapi_MyJapi_f636v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint16_1p_a, f636>(env, cls);
}

JNIEXPORT jintArray JNICALL Java_myjapi_MyJapi_f637v1(JNIEnv *env, jclass cls) {
  TRACE("jintArray Java_myjapi_MyJapi_f637v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int32_1p_a, f637>(env, cls);
}

JNIEXPORT jintArray JNICALL Java_myjapi_MyJapi_f638v1(JNIEnv *env, jclass cls) {
  TRACE("jintArray Java_myjapi_MyJapi_f638v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint32_1p_a, f638>(env, cls);
}

JNIEXPORT jlongArray JNICALL Java_myjapi_MyJapi_f641v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jlongArray Java_myjapi_MyJapi_f641v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_int64_1p_a, f641>(env, cls);
}

JNIEXPORT jlongArray JNICALL Java_myjapi_MyJapi_f642v1(JNIEnv *env,
                                                       jclass cls) {
  TRACE("jlongArray Java_myjapi_MyJapi_f642v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_uint64_1p_a, f642>(env, cls);
}

JNIEXPORT jfloatArray JNICALL Java_myjapi_MyJapi_f643v1(JNIEnv *env,
                                                        jclass cls) {
  TRACE("jfloatArray Java_myjapi_MyJapi_f643v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_float_1p_a, f643>(env, cls);
}

JNIEXPORT jdoubleArray JNICALL Java_myjapi_MyJapi_f644v1(JNIEnv *env,
                                                         jclass cls) {
  TRACE("jdoubleArray Java_myjapi_MyJapi_f644v1(JNIEnv *, jclass)");
  return gcall_fr<ttrait_double_1p_a, f644>(env, cls);
}

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f711v1(JNIEnv *env, jclass cls,
                                                 jbooleanArray p0) {
  TRACE("void Java_myjapi_MyJapi_f711v1(JNIEnv *, jclass, jbooleanArray)");
  gcall_fv<ttrait_bool_1cp_a, f711>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f712v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f712v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_char_1cp_a, f712>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f713v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f713v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_int8_1cp_a, f713>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f714v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f714v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_uint8_1cp_a, f714>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f715v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f715v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_int16_1cp_a, f715>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f716v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f716v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_uint16_1cp_a, f716>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f717v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f717v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_int32_1cp_a, f717>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f718v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f718v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_uint32_1cp_a, f718>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f721v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f721v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_int64_1cp_a, f721>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f722v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f722v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_uint64_1cp_a, f722>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f723v1(JNIEnv *env, jclass cls,
                                                 jfloatArray p0) {
  TRACE("void Java_myjapi_MyJapi_f723v1(JNIEnv *, jclass, jfloatArray)");
  gcall_fv<ttrait_float_1cp_a, f723>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f724v1(JNIEnv *env, jclass cls,
                                                 jdoubleArray p0) {
  TRACE("void Java_myjapi_MyJapi_f724v1(JNIEnv *, jclass, jdoubleArray)");
  gcall_fv<ttrait_double_1cp_a, f724>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f731v1(JNIEnv *env, jclass cls,
                                                 jbooleanArray p0) {
  TRACE("void Java_myjapi_MyJapi_f731v1(JNIEnv *, jclass, jbooleanArray)");
  gcall_fv<ttrait_bool_1p_a, f731>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f732v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f732v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_char_1p_a, f732>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f733v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f733v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_int8_1p_a, f733>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f734v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f734v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_uint8_1p_a, f734>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f735v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f735v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_int16_1p_a, f735>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f736v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f736v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_uint16_1p_a, f736>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f737v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f737v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_int32_1p_a, f737>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f738v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f738v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_uint32_1p_a, f738>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f741v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f741v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_int64_1p_a, f741>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f742v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f742v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_uint64_1p_a, f742>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f743v1(JNIEnv *env, jclass cls,
                                                 jfloatArray p0) {
  TRACE("void Java_myjapi_MyJapi_f743v1(JNIEnv *, jclass, jfloatArray)");
  gcall_fv<ttrait_float_1p_a, f743>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f744v1(JNIEnv *env, jclass cls,
                                                 jdoubleArray p0) {
  TRACE("void Java_myjapi_MyJapi_f744v1(JNIEnv *, jclass, jdoubleArray)");
  gcall_fv<ttrait_double_1p_a, f744>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f751v1(JNIEnv *env, jclass cls,
                                                 jbooleanArray p0) {
  TRACE("void Java_myjapi_MyJapi_f751v1(JNIEnv *, jclass, jbooleanArray)");
  gcall_fv<ttrait_bool_1cpc_a, f751>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f752v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f752v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_char_1cpc_a, f752>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f753v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f753v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_int8_1cpc_a, f753>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f754v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f754v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_uint8_1cpc_a, f754>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f755v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f755v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_int16_1cpc_a, f755>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f756v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f756v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_uint16_1cpc_a, f756>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f757v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f757v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_int32_1cpc_a, f757>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f758v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f758v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_uint32_1cpc_a, f758>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f761v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f761v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_int64_1cpc_a, f761>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f762v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f762v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_uint64_1cpc_a, f762>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f763v1(JNIEnv *env, jclass cls,
                                                 jfloatArray p0) {
  TRACE("void Java_myjapi_MyJapi_f763v1(JNIEnv *, jclass, jfloatArray)");
  gcall_fv<ttrait_float_1cpc_a, f763>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f764v1(JNIEnv *env, jclass cls,
                                                 jdoubleArray p0) {
  TRACE("void Java_myjapi_MyJapi_f764v1(JNIEnv *, jclass, jdoubleArray)");
  gcall_fv<ttrait_double_1cpc_a, f764>(env, cls, p0);
}

// ---------------------------------------------------------------------------

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f771v1(JNIEnv *env, jclass cls,
                                                 jbooleanArray p0) {
  TRACE("void Java_myjapi_MyJapi_f771v1(JNIEnv *, jclass, jbooleanArray)");
  gcall_fv<ttrait_bool_1pc_a, f771>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f772v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f772v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_char_1pc_a, f772>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f773v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f773v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_int8_1pc_a, f773>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f774v1(JNIEnv *env, jclass cls,
                                                 jbyteArray p0) {
  TRACE("void Java_myjapi_MyJapi_f774v1(JNIEnv *, jclass, jbyteArray)");
  gcall_fv<ttrait_uint8_1pc_a, f774>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f775v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f775v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_int16_1pc_a, f775>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f776v1(JNIEnv *env, jclass cls,
                                                 jshortArray p0) {
  TRACE("void Java_myjapi_MyJapi_f776v1(JNIEnv *, jclass, jshortArray)");
  gcall_fv<ttrait_uint16_1pc_a, f776>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f777v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f777v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_int32_1pc_a, f777>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f778v1(JNIEnv *env, jclass cls,
                                                 jintArray p0) {
  TRACE("void Java_myjapi_MyJapi_f778v1(JNIEnv *, jclass, jintArray)");
  gcall_fv<ttrait_uint32_1pc_a, f778>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f781v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f781v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_int64_1pc_a, f781>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f782v1(JNIEnv *env, jclass cls,
                                                 jlongArray p0) {
  TRACE("void Java_myjapi_MyJapi_f782v1(JNIEnv *, jclass, jlongArray)");
  gcall_fv<ttrait_uint64_1pc_a, f782>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f783v1(JNIEnv *env, jclass cls,
                                                 jfloatArray p0) {
  TRACE("void Java_myjapi_MyJapi_f783v1(JNIEnv *, jclass, jfloatArray)");
  gcall_fv<ttrait_float_1pc_a, f783>(env, cls, p0);
}

JNIEXPORT void JNICALL Java_myjapi_MyJapi_f784v1(JNIEnv *env, jclass cls,
                                                 jdoubleArray p0) {
  TRACE("void Java_myjapi_MyJapi_f784v1(JNIEnv *, jclass, jdoubleArray)");
  gcall_fv<ttrait_double_1pc_a, f784>(env, cls, p0);
}

// ---------------------------------------------------------------------------

}  // extern "C"

#endif  // myjapi_MyJapi_hpp
