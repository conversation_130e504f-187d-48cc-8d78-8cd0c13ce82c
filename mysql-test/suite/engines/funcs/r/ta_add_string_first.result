DROP TABLE IF EXISTS t5;
CREATE TABLE t5(c1 CHAR(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` char(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 CHAR(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` char(20) DEFAULT NULL,
  `c1` char(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARCHAR(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varchar(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 CHAR(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` char(20) DEFAULT NULL,
  `c1` varchar(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 BINARY(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` binary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 CHAR(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` char(20) DEFAULT NULL,
  `c1` binary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARBINARY(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varbinary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 CHAR(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` char(20) DEFAULT NULL,
  `c1` varbinary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 CHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` char(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 CHAR(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` char(20) NOT NULL,
  `c1` char(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 CHAR(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` char(20) NOT NULL,
  `c1` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 BINARY(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` binary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 CHAR(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` char(20) NOT NULL,
  `c1` binary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARBINARY(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varbinary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 CHAR(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` char(20) NOT NULL,
  `c1` varbinary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 CHAR(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` char(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARCHAR(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varchar(20) DEFAULT NULL,
  `c1` char(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARCHAR(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varchar(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARCHAR(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varchar(20) DEFAULT NULL,
  `c1` varchar(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 BINARY(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` binary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARCHAR(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varchar(20) DEFAULT NULL,
  `c1` binary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARBINARY(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varbinary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARCHAR(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varchar(20) DEFAULT NULL,
  `c1` varbinary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 CHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` char(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARCHAR(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varchar(20) NOT NULL,
  `c1` char(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARCHAR(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varchar(20) NOT NULL,
  `c1` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 BINARY(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` binary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARCHAR(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varchar(20) NOT NULL,
  `c1` binary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARBINARY(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varbinary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARCHAR(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varchar(20) NOT NULL,
  `c1` varbinary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 CHAR(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` char(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 BINARY(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` binary(20) DEFAULT NULL,
  `c1` char(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARCHAR(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varchar(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 BINARY(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` binary(20) DEFAULT NULL,
  `c1` varchar(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 BINARY(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` binary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 BINARY(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` binary(20) DEFAULT NULL,
  `c1` binary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARBINARY(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varbinary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 BINARY(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` binary(20) DEFAULT NULL,
  `c1` varbinary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 CHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` char(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 BINARY(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` binary(20) NOT NULL,
  `c1` char(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 BINARY(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` binary(20) NOT NULL,
  `c1` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 BINARY(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` binary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 BINARY(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` binary(20) NOT NULL,
  `c1` binary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARBINARY(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varbinary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 BINARY(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` binary(20) NOT NULL,
  `c1` varbinary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 CHAR(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` char(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARBINARY(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varbinary(20) DEFAULT NULL,
  `c1` char(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARCHAR(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varchar(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARBINARY(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varbinary(20) DEFAULT NULL,
  `c1` varchar(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 BINARY(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` binary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARBINARY(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varbinary(20) DEFAULT NULL,
  `c1` binary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARBINARY(10) NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varbinary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARBINARY(20)  NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varbinary(20) DEFAULT NULL,
  `c1` varbinary(10) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 CHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` char(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARBINARY(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varbinary(20) NOT NULL,
  `c1` char(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARCHAR(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARBINARY(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varbinary(20) NOT NULL,
  `c1` varchar(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 BINARY(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` binary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARBINARY(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varbinary(20) NOT NULL,
  `c1` binary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
CREATE TABLE t5(c1 VARBINARY(10) NOT NULL);
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c1` varbinary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t5 ADD c2 VARBINARY(20)  NOT NULL FIRST;
SHOW TABLES;
Tables_in_test
t5
SHOW CREATE TABLE t5;
Table	Create Table
t5	CREATE TABLE `t5` (
  `c2` varbinary(20) NOT NULL,
  `c1` varbinary(10) NOT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t5;
SHOW TABLES;
Tables_in_test
