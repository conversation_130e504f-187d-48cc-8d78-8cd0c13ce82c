#include "univ.i"
static char *yytext;
#line 2 "lexyy.cc"

#line 4 "lexyy.cc"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 5
#define YY_FLEX_SUBMINOR_VERSION 39
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>

/* end standard C headers. */

/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* <PERSON>9<PERSON> says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

#ifdef __cplusplus

/* The "const" storage-class-modifier is valid. */
#define YY_USE_CONST

#else   /* ! __cplusplus */

/* C99 requires __STDC__ to be defined as 1. */
#if defined (__STDC__)

#define YY_USE_CONST

#endif  /* defined (__STDC__) */
#endif  /* ! __cplusplus */

#ifdef YY_USE_CONST
#define yyconst const
#else
#define yyconst
#endif

/* Returned upon end-of-file. */
#define YY_NULL 0

/* Promotes a possibly negative, possibly signed char to an unsigned
 * integer for use as an array index.  If the signed char is negative,
 * we want to instead treat it as an 8-bit unsigned char, hence the
 * double cast.
 */
#define YY_SC_TO_UI(c) ((unsigned int) (unsigned char) c)

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN (yy_start) = 1 + 2 *

/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START (((yy_start) - 1) / 2)
#define YYSTATE YY_START

/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)

/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE yyrestart(yyin  )

#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

extern yy_size_t yyleng;

static FILE *yyin, *yyout;

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2

    #define YY_LESS_LINENO(n)
    #define YY_LINENO_REWIND_TO(ptr)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
        do \
                { \
                /* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
                *yy_cp = (yy_hold_char); \
                YY_RESTORE_YY_MORE_OFFSET \
                (yy_c_buf_p) = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
                YY_DO_BEFORE_ACTION; /* set up yytext again */ \
                } \
        while ( 0 )

#define unput(c) yyunput( c, (yytext_ptr)  )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
        {
        FILE *yy_input_file;

        char *yy_ch_buf;                /* input buffer */
        char *yy_buf_pos;               /* current position in input buffer */

        /* Size of input buffer in bytes, not including room for EOB
         * characters.
         */
        yy_size_t yy_buf_size;

        /* Number of characters read into yy_ch_buf, not including EOB
         * characters.
         */
        yy_size_t yy_n_chars;

        /* Whether we "own" the buffer - i.e., we know we created it,
         * and can realloc() it to grow it, and should free() it to
         * delete it.
         */
        int yy_is_our_buffer;

        /* Whether this is an "interactive" input source; if so, and
         * if we're using stdio for input, then we want to use getc()
         * instead of fread(), to make sure we stop fetching input after
         * each newline.
         */
        int yy_is_interactive;

        /* Whether we're considered to be at the beginning of a line.
         * If so, '^' rules will be active on the next match, otherwise
         * not.
         */
        int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */
    
        /* Whether to try to fill the input buffer when we reach the
         * end of it.
         */
        int yy_fill_buffer;

        int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
        /* When an EOF's been seen but there's still some text to process
         * then we mark the buffer as YY_EOF_PENDING, to indicate that we
         * shouldn't try reading from the input source any more.  We might
         * still have a bunch of tokens to match, though, because of
         * possible backing-up.
         *
         * When we actually see the EOF, we change the status to "new"
         * (via yyrestart()), so that the user can continue scanning by
         * just pointing yyin at a new input file.
         */
#define YY_BUFFER_EOF_PENDING 2

        };
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* Stack of input buffers. */
static size_t yy_buffer_stack_top = 0; /**< index of top of stack. */
static size_t yy_buffer_stack_max = 0; /**< capacity of stack. */
static YY_BUFFER_STATE *yy_buffer_stack = nullptr; /**< Stack as an array. */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( (yy_buffer_stack) \
                          ? (yy_buffer_stack)[(yy_buffer_stack_top)] \
                          : NULL)

/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE (yy_buffer_stack)[(yy_buffer_stack_top)]

/* yy_hold_char holds the character lost when yytext is formed. */
static char yy_hold_char;
static yy_size_t yy_n_chars;            /* number of characters read into yy_ch_buf */
yy_size_t yyleng;

/* Points to current character in buffer. */
static char *yy_c_buf_p = (char *)nullptr;
static int yy_init = 0;         /* whether we need to initialize */
static int yy_start = 0;        /* start state number */

/* Flag which is used to allow yywrap()'s to do buffer switches
 * instead of setting up a fresh yyin.  A bit of a hack ...
 */
static int yy_did_buffer_switch_on_eof;

static void yyrestart (FILE *input_file  );
[[maybe_unused]] static void yy_switch_to_buffer (YY_BUFFER_STATE new_buffer  );
static YY_BUFFER_STATE yy_create_buffer (FILE *file,int size  );
static void yy_delete_buffer (YY_BUFFER_STATE b  );
static void yy_flush_buffer (YY_BUFFER_STATE b  );
[[maybe_unused]] static void yypush_buffer_state (YY_BUFFER_STATE new_buffer  );
[[maybe_unused]] static void yypop_buffer_state (void );

static void yyensure_buffer_stack (void );
static void yy_load_buffer_state (void );
static void yy_init_buffer (YY_BUFFER_STATE b,FILE *file  );

#define YY_FLUSH_BUFFER yy_flush_buffer(YY_CURRENT_BUFFER )

YY_BUFFER_STATE yy_scan_buffer (char *base,yy_size_t size  );
YY_BUFFER_STATE yy_scan_string (yyconst char *yy_str  );
YY_BUFFER_STATE yy_scan_bytes (yyconst char *bytes,yy_size_t len  );

static void *yyalloc (yy_size_t  );
static void *yyrealloc (void *,yy_size_t  );
static void yyfree (void *  );

#define yy_new_buffer yy_create_buffer

#define yy_set_interactive(is_interactive) \
        { \
        if ( ! YY_CURRENT_BUFFER ){ \
        yyensure_buffer_stack (); \
                YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer(yyin,YY_BUF_SIZE ); \
        } \
        YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
        }

#define yy_set_bol(at_bol) \
        { \
        if ( ! YY_CURRENT_BUFFER ){\
        yyensure_buffer_stack (); \
                YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer(yyin,YY_BUF_SIZE ); \
        } \
        YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
        }

#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* Begin user sect3 */

#define yywrap() 1
#define YY_SKIP_YYWRAP

typedef unsigned char YY_CHAR;



typedef int yy_state_type;



static int yylineno = 1;


#define yytext_ptr yytext

static yy_state_type yy_get_previous_state (void );
static yy_state_type yy_try_NUL_trans (yy_state_type current_state  );
static int yy_get_next_buffer (void );
static void yy_fatal_error (yyconst char msg[]  );

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
        (yytext_ptr) = yy_bp; \
        yyleng = (size_t) (yy_cp - yy_bp); \
        (yy_hold_char) = *yy_cp; \
        *yy_cp = '\0'; \
        (yy_c_buf_p) = yy_cp;

#define YY_NUM_RULES 114
#define YY_END_OF_BUFFER 115
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
        {
        flex_int32_t yy_verify;
        flex_int32_t yy_nxt;
        };
static yyconst flex_int16_t yy_accept[372] =
    {   0,
        0,    0,  109,  109,    0,    0,    0,    0,  115,  113,
      112,  112,    8,  113,  104,    5,   93,   99,  102,  100,
       97,  101,  113,  103,    1,  113,   98,   96,   94,   95,
      107,   86,   86,   86,   86,   86,   86,   86,   86,   86,
       86,   86,   86,   86,   86,   86,   86,   86,   86,   86,
      105,  106,  109,  110,    6,    7,    9,   10,  112,    4,
       88,  108,    2,    1,    3,   89,   90,   92,   91,    0,
       86,    0,   86,   86,   86,   86,   86,   44,   86,   86,
       86,   86,   86,   86,   86,   86,   86,   86,   86,   86,
       86,   86,   86,   86,   28,   17,   25,   86,   86,   86,

       86,   86,   86,   54,   63,   86,   14,   86,   86,   86,
       86,   86,   86,   86,   86,   86,   86,   86,   86,   86,
       86,   86,   86,  109,  110,  110,  111,    6,    7,    9,
       10,    2,    0,   87,   13,   45,   86,   86,   86,   86,
       86,   86,   86,   86,   86,   86,   86,   86,   86,   86,
       86,   86,   86,   86,   27,   86,   86,   86,   41,   86,
       86,   86,   86,   21,   86,   86,   86,   86,   86,   15,
       86,   86,   86,   18,   86,   86,   86,   86,   86,   51,
       86,   12,   86,   36,   86,   86,   86,   86,   86,   86,
       86,   86,   86,   86,    0,   87,   86,   86,   86,   20,

       86,   24,   86,   86,   86,   86,   86,   86,   86,   86,
       86,   86,   86,   46,   86,   86,   30,   86,   79,   86,
       86,   39,   86,   86,   86,   86,   86,   48,   86,   84,
       81,   32,   83,   86,   11,   66,   86,   86,   42,   86,
       86,   86,   86,   86,   86,   29,   86,   86,   86,   86,
       86,   86,   86,   77,    0,   26,   86,   86,   86,   68,
       86,   86,   86,   86,   37,   86,   86,   86,   86,   86,
       86,   86,   31,   67,   23,   86,   59,   86,   73,   86,
       86,   86,   43,   86,   86,   86,   86,   82,   86,   56,
       86,   86,   86,   86,   86,   40,   33,    0,   85,   19,

       86,   86,   75,   86,   72,   55,   86,   65,   86,   52,
       86,   86,   86,   47,   86,   74,   86,   86,   34,   86,
       35,   71,   86,   60,   86,   50,   49,   86,   86,   57,
       53,   64,   86,   86,   86,   22,   86,   86,   86,   86,
       86,   86,   86,   38,   86,   80,   69,   86,   76,   86,
       78,   86,   61,   86,   16,   70,   58,   86,   86,   86,
       86,   86,   86,   86,   86,   86,   86,   86,   86,   62,
        0
    } ;

static yyconst flex_int32_t yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    3,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    2,    1,    4,    5,    6,    7,    1,    8,    9,
       10,   11,   12,   13,   14,   15,   16,   17,   17,   17,
       17,   17,   17,   17,   17,   17,   17,   18,   19,   20,
       21,   22,   23,   24,   25,   26,   27,   28,   29,   30,
       31,   32,   33,   34,   35,   36,   37,   38,   39,   40,
       41,   42,   43,   44,   45,   46,   47,   48,   49,   50,
        1,    1,    1,    1,   51,    1,   34,   34,   34,   34,

       34,   34,   34,   34,   34,   34,   34,   52,   34,   34,
       34,   34,   53,   34,   54,   34,   34,   34,   34,   34,
       34,   34,   55,    1,   56,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static yyconst flex_int32_t yy_meta[57] =
    {   0,
        1,    1,    1,    2,    3,    1,    1,    4,    1,    1,
        5,    1,    1,    1,    1,    6,    7,    1,    1,    1,
        8,    1,    1,    6,    9,    9,    9,    9,    9,    9,
        9,    9,    9,    9,    9,    9,    9,    9,    9,    9,
        9,    9,    9,    9,    9,    9,    9,    9,    9,    9,
        9,    9,    9,    9,    1,    1
    } ;

static yyconst flex_int16_t yy_base[385] =
    {   0,
        0,    0,  304,  293,  283,  277,  278,  265,  260, 1174,
       55,   57, 1174,    0, 1174, 1174, 1174, 1174, 1174, 1174,
     1174, 1174,  240,  242,   46,  206, 1174,   43, 1174,  202,
     1174,   46,   50,   56,   52,   66,   64,   51,   81,   92,
       91,   94,   96,  111,  113,  116,  130,  127,   53,  134,
     1174, 1174,    0,  106,    0,  213,    0,  216,  157,    0,
     1174, 1174,  192,   56,  185, 1174, 1174, 1174, 1174,  195,
      140,  179,  147,  152,  154,  156,  158,  165,  167,  172,
      177,  174,  183,  192,  186,  189,  188,  208,  210,  214,
      217,  221,  223,  226,  224,  233,  228,  230,  235,  251,

      255,  249,  250,  256,  265,  268,  271,  272,  278,  282,
      284,  285,  287,  289,  298,  307,  303,  294,  314,  312,
      318,  327,  325,    0,  112,  133, 1174,    0,  178,    0,
      180,  160,   93,    0,  328,  334,  337,  339,  349,  352,
      353,  359,  364,  366,  365,  370,  375,  369,  380,  381,
      393,  397,  399,  398,  402,  408,  409,  413,  419,  423,
      424,  429,  430,  433,  439,  440,  449,  451,  452,  455,
      462,  464,  466,  473,  476,  477,  480,  483,  492,  490,
      493,  494,  496,  499,  506,  508,  510,  513,  517,  524,
      527,  528,  529,  531,   60,    0,  540,  543,  544,  545,

      547,  559,  560,  561,  563,  574,  576,  564,  578,  587,
      588,  590,  593,  594,  597,  604,  607,  608,  609,  610,
      619,  620,  623,  624,  629,  633,  630,  634,  635,  636,
      640,  646,  649,  650,  652,  653,  662,  664,  666,  667,
      668,  671,  681,  683,  684,  687,  690,  696,  700,  701,
      705,  706,  720,  702,   42,  716,  722,  723,  726,  727,
      732,  736,  738,  739,  743,  752,  754,  755,  758,  769,
      770,  771,  772,  775,  786,  787,  788,  789,  790,  799,
      800,  801,  802,  803,  805,  814,  818,  820,  821,  824,
      831,  825,  834,  835,  836,  837,  841,   73,  850,  851,

      852,  854,  857,  861,  864,  866,  867,  868,  870,  873,
      877,  882,  884,  886,  891,  893,  896,  898,  900,  902,
      903,  912,  914,  916,  921,  925,  928,  930,  931,  932,
      935,  937,  941,  942,  946,  948,  951,  952,  953,  957,
      967,  958,  973,  974,  976,  980,  986,  987,  989,  990,
      991,  993,  996, 1002, 1005, 1007, 1009, 1012, 1014, 1018,
     1019, 1024, 1025, 1030, 1031, 1035, 1040, 1041, 1037, 1047,
     1174, 1086, 1095, 1104, 1107, 1110, 1114, 1123, 1132, 1141,
     1150, 1157, 1161, 1164
    } ;

static yyconst flex_int16_t yy_def[385] =
    {   0,
      371,    1,  372,  372,  373,  373,  374,  374,  371,  371,
      371,  371,  371,  375,  371,  371,  371,  371,  371,  371,
      371,  371,  371,  371,  371,  376,  371,  371,  371,  371,
      371,  377,  377,  377,  377,  377,   34,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      371,  371,  378,  379,  380,  371,  381,  371,  371,  375,
      371,  371,  371,  371,  376,  371,  371,  371,  371,  382,
      377,  383,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,

      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  378,  379,  379,  371,  380,  371,  381,
      371,  371,  371,  384,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  371,  384,  377,  377,  377,  377,

      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  371,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  371,  377,  377,

      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
      377,  377,  377,  377,  377,  377,  377,  377,  377,  377,
        0,  371,  371,  371,  371,  371,  371,  371,  371,  371,
      371,  371,  371,  371
    } ;

static yyconst flex_int16_t yy_nxt[1231] =
    {   0,
       10,   11,   12,   13,   10,   14,   15,   16,   17,   18,
       19,   20,   21,   22,   23,   24,   25,   26,   27,   28,
       29,   30,   31,   10,   32,   33,   34,   35,   36,   37,
       38,   38,   39,   38,   38,   40,   41,   42,   43,   44,
       38,   45,   46,   47,   48,   49,   50,   38,   38,   38,
       38,   38,   38,   38,   51,   52,   59,   59,   59,   59,
       63,   70,   64,   67,   68,   70,   70,   70,   70,   72,
       63,   70,   64,   72,   72,   72,   72,  121,   75,   72,
       84,   70,   76,   73,   85,   77,  134,   79,   74,   72,
       86,   80,   90,  298,   81,   71,   70,   82,   78,   91,

       83,   87,   92,   88,   72,   93,   70,   70,   94,   70,
       95,   70,  255,   89,   72,   72,  126,   72,   96,   72,
       98,  127,  371,   97,   99,  104,   70,  371,   70,  101,
      100,   70,  102,  105,   72,  106,   72,  107,  103,   72,
      108,  110,   70,  126,  112,   70,  195,  113,  127,   70,
       72,  111,  109,   72,  116,   70,  114,   72,   59,   59,
      115,  117,   70,   72,  119,  122,  120,   70,  118,   70,
       72,   70,  123,   70,  135,   72,  132,   72,  136,   72,
       70,   72,   70,  131,  137,  129,  138,   70,   72,   70,
       72,  141,   70,  139,   70,   72,  140,   72,   70,  133,

       72,   70,  147,   70,   70,  371,   72,   70,  132,   72,
      142,   72,   72,  144,  145,   72,  143,  153,  149,  131,
      129,  146,   69,   70,  148,   70,   66,  150,  152,   70,
      154,   72,   70,   72,  151,  155,   70,   72,   70,   70,
       72,   70,  156,   70,   72,   70,   72,   72,   70,   72,
       70,   72,   62,   72,   61,  158,   72,  157,   72,  371,
      162,  160,  159,  161,   70,   70,   70,  165,   58,  166,
       70,   70,   72,   72,   72,  163,  164,  167,   72,   72,
       70,   58,  169,   70,   56,  171,   70,   70,   72,  168,
       56,   72,  170,   70,   72,   72,  172,   70,  173,   70,

       70,   72,   70,   54,   70,   72,  176,   72,   72,   70,
       72,  181,   72,   70,   54,  174,  175,   72,   70,  178,
      179,   72,   70,  183,  182,  177,   72,   70,  180,   70,
       72,  186,  185,   70,  184,   72,  371,   72,  371,  190,
       70,   72,   70,   70,  187,  371,  188,  371,   72,   70,
       72,   72,   70,  191,   70,  192,  189,   72,  371,  193,
       72,  371,   72,  371,   70,  371,  194,   70,   70,  197,
      371,  198,   72,  199,   70,   72,   72,  200,  201,   70,
       70,   70,   72,  371,   70,   70,  371,   72,   72,   72,
       70,  207,   72,   72,  202,   70,   70,  371,   72,  209,

      371,  203,  205,   72,   72,  206,  204,  208,   70,  213,
      210,  211,   70,   70,   70,  212,   72,   70,  371,  214,
       72,   72,   72,   70,   70,   72,  217,  371,   70,  371,
      218,   72,   72,  371,   70,  220,   72,  221,   70,   70,
      215,  216,   72,  371,   70,   70,   72,   72,   70,  371,
      223,  219,   72,   72,   70,   70,   72,  224,  225,  222,
      371,  227,   72,   72,   70,  371,   70,   70,  230,  229,
       70,  228,   72,  226,   72,   72,  371,   70,   72,   70,
      233,   70,  371,  231,  234,   72,  371,   72,   70,   72,
      232,   70,   70,  371,  237,   70,   72,  235,   70,   72,

       72,  236,  238,   72,  239,   70,   72,   70,   70,   70,
      371,   70,  371,   72,   70,   72,   72,   72,  241,   72,
      242,   70,   72,   70,  240,   70,  371,  371,   70,   72,
      371,   72,   70,   72,  243,  247,   72,  371,  244,   70,
       72,  245,   70,   70,   70,  246,   70,   72,  250,  249,
       72,   72,   72,  248,   72,   70,  371,  371,   70,   70,
       70,  371,   70,   72,  253,  254,   72,   72,   72,  252,
       72,  251,  371,  371,   70,   70,   70,  256,   70,   70,
      257,  259,   72,   72,   72,  258,   72,   72,  260,   70,
      371,   70,  371,   70,  371,  262,  371,   72,  263,   72,

      264,   72,   70,   70,  261,   70,  371,  265,   70,   70,
       72,   72,   70,   72,  269,  267,   72,   72,  371,   70,
       72,  266,   70,   70,   70,   70,  268,   72,  371,  271,
       72,   72,   72,   72,   70,   70,  270,  273,   70,   70,
      371,  274,   72,   72,   70,   70,   72,   72,   70,   70,
       70,   70,   72,   72,  272,   70,   72,   72,   72,   72,
      280,   70,  275,   72,   70,   70,  276,   70,   70,   72,
      278,  277,   72,   72,  279,   72,   72,   70,  281,   70,
      371,   70,   70,   70,  371,   72,   70,   72,  282,   72,
       72,   72,  284,  286,   72,  371,   70,  287,   70,   70,

      371,  371,   70,  283,   72,   70,   72,   72,  285,  288,
       72,   70,  290,   72,  371,   70,   70,   70,  371,   72,
       70,   70,  291,   72,   72,   72,  289,  371,   72,   72,
      293,   70,  371,  295,  296,   70,  371,   70,   70,   72,
      292,   70,   70,   72,  294,   72,   72,   70,  297,   72,
       72,   70,  371,   70,   70,   72,  371,  371,   70,   72,
      302,   72,   72,  371,  304,  299,   72,   70,  371,   70,
       70,  300,  371,   70,  371,   72,  301,   72,   72,  303,
      306,   72,  305,  371,   70,   70,   70,   70,  371,  371,
       70,  307,   72,   72,   72,   72,  308,  310,   72,  309,

      371,   70,   70,   70,   70,   70,  371,  311,  312,   72,
       72,   72,   72,   72,   70,   70,   70,   70,   70,  313,
       70,  371,   72,   72,   72,   72,   72,  315,   72,   70,
      318,  316,  314,   70,  371,   70,   70,   72,  320,   70,
       70,   72,  319,   72,   72,  317,   70,   72,   72,   70,
       70,   70,   70,  324,   72,  371,   70,   72,   72,   72,
       72,  321,  322,  326,   72,   70,   70,   70,  323,   70,
      371,  325,   70,   72,   72,   72,   70,   72,  327,   70,
       72,   70,   70,   70,   72,   70,  371,   72,   70,   72,
       72,   72,   70,   72,  328,  329,   72,   70,  332,   70,

       72,   70,  371,  333,  330,   72,   70,   72,   70,   72,
      331,   70,  371,   70,   72,   70,   72,   70,   70,   72,
      334,   72,  335,   72,  371,   72,   72,   70,  339,   70,
      371,   70,  336,  337,  371,   72,   70,   72,  340,   72,
       70,  371,  338,   70,   72,   70,   70,   70,   72,  341,
       70,   72,   70,   72,   72,   72,   70,   70,   72,  343,
       72,   70,  342,   70,   72,   72,   70,   70,   70,   72,
      371,   72,   70,   70,   72,   72,   72,  371,  347,  371,
       72,   72,   70,  346,  344,  345,  371,  349,   70,   70,
       72,   70,  371,  348,  351,   70,   72,   72,  350,   72,

      353,   70,   70,   72,   70,   70,   70,  352,   70,   72,
       72,   70,   72,   72,   72,  355,   72,   70,  371,   72,
       70,  357,   70,  371,   70,   72,  354,   70,   72,   70,
       72,  358,   72,   70,   70,   72,  371,   72,  356,   70,
       70,   72,   72,  371,  359,   70,   70,   72,   72,  371,
       70,  362,   70,   72,   72,   70,   70,  360,   72,  366,
       72,  363,   70,   72,   72,  371,  365,  371,  361,  371,
       72,  367,  371,  371,  371,  364,  371,  371,  368,  371,
      371,  371,  369,  371,  371,  370,   53,   53,   53,   53,
       53,   53,   53,   53,   53,   55,   55,   55,   55,   55,

       55,   55,   55,   55,   57,   57,   57,   57,   57,   57,
       57,   57,   57,   60,  371,   60,   65,   65,   65,   71,
       71,  371,   71,  124,  124,  124,  124,  371,  124,  124,
      124,  124,  125,  125,  125,  125,  125,  125,  125,  125,
      125,  128,  128,  128,  371,  128,  128,  128,  128,  128,
      130,  371,  130,  130,  130,  130,  130,  130,  130,  134,
      371,  371,  371,  371,  371,  134,   72,   72,  371,   72,
      196,  371,  196,    9,  371,  371,  371,  371,  371,  371,
      371,  371,  371,  371,  371,  371,  371,  371,  371,  371,
      371,  371,  371,  371,  371,  371,  371,  371,  371,  371,

      371,  371,  371,  371,  371,  371,  371,  371,  371,  371,
      371,  371,  371,  371,  371,  371,  371,  371,  371,  371,
      371,  371,  371,  371,  371,  371,  371,  371,  371,  371
    } ;

static yyconst flex_int16_t yy_chk[1231] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,   11,   11,   12,   12,
       25,   32,   25,   28,   28,   33,   38,   35,   49,   32,
       64,   34,   64,   33,   38,   35,   49,   49,   33,   34,
       35,   36,   33,   32,   35,   33,  298,   34,   32,   36,
       35,   34,   37,  255,   34,   37,   39,   34,   33,   37,

       34,   36,   37,   36,   39,   37,   41,   40,   37,   42,
       39,   43,  195,   36,   41,   40,   54,   42,   39,   43,
       40,   54,  125,   39,   40,   43,   44,  125,   45,   41,
       40,   46,   42,   43,   44,   43,   45,   43,   42,   46,
       43,   45,   48,  126,   46,   47,  133,   46,  126,   50,
       48,   45,   44,   47,   47,   71,   46,   50,   59,   59,
       46,   47,   73,   71,   48,   50,   48,   74,   47,   75,
       73,   76,   50,   77,   73,   74,  132,   75,   74,   76,
       78,   77,   79,  131,   75,  129,   76,   80,   78,   82,
       79,   79,   81,   76,   72,   80,   77,   82,   83,   70,

       81,   85,   82,   87,   86,   65,   83,   84,   63,   85,
       80,   87,   86,   81,   81,   84,   80,   86,   84,   58,
       56,   81,   30,   88,   83,   89,   26,   84,   85,   90,
       87,   88,   91,   89,   84,   88,   92,   90,   93,   95,
       91,   94,   89,   97,   92,   98,   93,   95,   96,   94,
       99,   97,   24,   98,   23,   91,   96,   90,   99,    9,
       96,   93,   92,   94,  102,  103,  100,   98,    8,   99,
      101,  104,  102,  103,  100,   96,   96,  100,  101,  104,
      105,    7,  101,  106,    6,  103,  107,  108,  105,  100,
        5,  106,  102,  109,  107,  108,  106,  110,  107,  111,

      112,  109,  113,    4,  114,  110,  110,  111,  112,  118,
      113,  113,  114,  115,    3,  108,  109,  118,  117,  111,
      112,  115,  116,  115,  114,  110,  117,  120,  112,  119,
      116,  117,  116,  121,  115,  120,    0,  119,    0,  120,
      123,  121,  122,  135,  118,    0,  119,    0,  123,  136,
      122,  135,  137,  121,  138,  122,  119,  136,    0,  122,
      137,    0,  138,    0,  139,    0,  123,  140,  141,  137,
        0,  138,  139,  139,  142,  140,  141,  140,  140,  143,
      145,  144,  142,    0,  148,  146,    0,  143,  145,  144,
      147,  145,  148,  146,  141,  149,  150,    0,  147,  147,

        0,  142,  144,  149,  150,  144,  143,  146,  151,  150,
      148,  148,  152,  154,  153,  149,  151,  155,    0,  151,
      152,  154,  153,  156,  157,  155,  154,    0,  158,    0,
      154,  156,  157,    0,  159,  157,  158,  158,  160,  161,
      152,  153,  159,    0,  162,  163,  160,  161,  164,    0,
      161,  156,  162,  163,  165,  166,  164,  162,  163,  160,
        0,  164,  165,  166,  167,    0,  168,  169,  166,  165,
      170,  164,  167,  163,  168,  169,    0,  171,  170,  172,
      169,  173,    0,  167,  170,  171,    0,  172,  174,  173,
      168,  175,  176,    0,  173,  177,  174,  171,  178,  175,

      176,  172,  175,  177,  176,  180,  178,  179,  181,  182,
        0,  183,    0,  180,  184,  179,  181,  182,  178,  183,
      179,  185,  184,  186,  177,  187,    0,    0,  188,  185,
        0,  186,  189,  187,  181,  187,  188,    0,  183,  190,
      189,  185,  191,  192,  193,  186,  194,  190,  190,  189,
      191,  192,  193,  188,  194,  197,    0,    0,  198,  199,
      200,    0,  201,  197,  193,  194,  198,  199,  200,  192,
      201,  191,    0,    0,  202,  203,  204,  197,  205,  208,
      198,  201,  202,  203,  204,  199,  205,  208,  203,  206,
        0,  207,    0,  209,    0,  205,    0,  206,  206,  207,

      207,  209,  210,  211,  204,  212,    0,  208,  213,  214,
      210,  211,  215,  212,  212,  210,  213,  214,    0,  216,
      215,  209,  217,  218,  219,  220,  211,  216,    0,  215,
      217,  218,  219,  220,  221,  222,  213,  218,  223,  224,
        0,  220,  221,  222,  225,  227,  223,  224,  226,  228,
      229,  230,  225,  227,  216,  231,  226,  228,  229,  230,
      227,  232,  221,  231,  233,  234,  223,  235,  236,  232,
      225,  224,  233,  234,  226,  235,  236,  237,  229,  238,
        0,  239,  240,  241,    0,  237,  242,  238,  234,  239,
      240,  241,  238,  241,  242,    0,  243,  242,  244,  245,

        0,    0,  246,  237,  243,  247,  244,  245,  240,  243,
      246,  248,  245,  247,    0,  249,  250,  254,    0,  248,
      251,  252,  247,  249,  250,  254,  244,    0,  251,  252,
      249,  256,    0,  251,  252,  253,    0,  257,  258,  256,
      248,  259,  260,  253,  250,  257,  258,  261,  253,  259,
      260,  262,    0,  263,  264,  261,    0,    0,  265,  262,
      261,  263,  264,    0,  263,  257,  265,  266,    0,  267,
      268,  258,    0,  269,    0,  266,  259,  267,  268,  262,
      266,  269,  264,    0,  270,  271,  272,  273,    0,    0,
      274,  267,  270,  271,  272,  273,  268,  270,  274,  269,

        0,  275,  276,  277,  278,  279,    0,  271,  272,  275,
      276,  277,  278,  279,  280,  281,  282,  283,  284,  276,
      285,    0,  280,  281,  282,  283,  284,  280,  285,  286,
      284,  281,  278,  287,    0,  288,  289,  286,  286,  290,
      292,  287,  285,  288,  289,  282,  291,  290,  292,  293,
      294,  295,  296,  292,  291,    0,  297,  293,  294,  295,
      296,  287,  289,  294,  297,  299,  300,  301,  291,  302,
        0,  293,  303,  299,  300,  301,  304,  302,  295,  305,
      303,  306,  307,  308,  304,  309,    0,  305,  310,  306,
      307,  308,  311,  309,  301,  302,  310,  312,  309,  313,

      311,  314,    0,  311,  304,  312,  315,  313,  316,  314,
      307,  317,    0,  318,  315,  319,  316,  320,  321,  317,
      312,  318,  313,  319,    0,  320,  321,  322,  320,  323,
        0,  324,  315,  317,    0,  322,  325,  323,  323,  324,
      326,    0,  318,  327,  325,  328,  329,  330,  326,  325,
      331,  327,  332,  328,  329,  330,  333,  334,  331,  329,
      332,  335,  328,  336,  333,  334,  337,  338,  339,  335,
        0,  336,  340,  342,  337,  338,  339,    0,  337,    0,
      340,  342,  341,  335,  333,  334,    0,  339,  343,  344,
      341,  345,    0,  338,  341,  346,  343,  344,  340,  345,

      343,  347,  348,  346,  349,  350,  351,  342,  352,  347,
      348,  353,  349,  350,  351,  348,  352,  354,    0,  353,
      355,  352,  356,    0,  357,  354,  345,  358,  355,  359,
      356,  354,  357,  360,  361,  358,    0,  359,  350,  362,
      363,  360,  361,    0,  358,  364,  365,  362,  363,    0,
      366,  361,  369,  364,  365,  367,  368,  359,  366,  365,
      369,  362,  370,  367,  368,    0,  364,    0,  360,    0,
      370,  366,    0,    0,    0,  363,    0,    0,  367,    0,
        0,    0,  368,    0,    0,  369,  372,  372,  372,  372,
      372,  372,  372,  372,  372,  373,  373,  373,  373,  373,

      373,  373,  373,  373,  374,  374,  374,  374,  374,  374,
      374,  374,  374,  375,    0,  375,  376,  376,  376,  377,
      377,    0,  377,  378,  378,  378,  378,    0,  378,  378,
      378,  378,  379,  379,  379,  379,  379,  379,  379,  379,
      379,  380,  380,  380,    0,  380,  380,  380,  380,  380,
      381,    0,  381,  381,  381,  381,  381,  381,  381,  382,
        0,    0,    0,    0,    0,  382,  383,  383,    0,  383,
      384,    0,  384,  371,  371,  371,  371,  371,  371,  371,
      371,  371,  371,  371,  371,  371,  371,  371,  371,  371,
      371,  371,  371,  371,  371,  371,  371,  371,  371,  371,

      371,  371,  371,  371,  371,  371,  371,  371,  371,  371,
      371,  371,  371,  371,  371,  371,  371,  371,  371,  371,
      371,  371,  371,  371,  371,  371,  371,  371,  371,  371
    } ;

static yy_state_type yy_last_accepting_state;
static char *yy_last_accepting_cpos;


static int yy_flex_debug = 0;

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET

#line 1 "pars0lex.l"
/*****************************************************************************

Copyright (c) 1997, 2025, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License, version 2.0, as published by the
Free Software Foundation.

This program is also distributed with certain software (including but not
limited to OpenSSL) that is licensed under separate terms, as designated in a
particular file or component or in included license documentation. The authors
of MySQL hereby grant you an additional permission to link the program and
your derivative works with the separately licensed software that they have
included with MySQL.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0,
for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

*****************************************************************************/
/******************************************************
SQL parser lexical analyzer: input file for the GNU Flex lexer generator

The InnoDB parser is frozen because MySQL takes care of SQL parsing.
Therefore we normally keep the InnoDB parser C files as they are, and do
not automatically generate them from pars0grm.y and pars0lex.l.

How to make the InnoDB parser and lexer C files:

1. Run ./make_flex.sh to generate lexer files.

2. Run ./make_bison.sh to generate parser files.

These instructions seem to work at least with bison-1.875d and flex-2.5.31 on
Linux.

Created 12/14/1997 Heikki Tuuri
*******************************************************/
#define YY_NO_INPUT 1
#define YY_NO_UNISTD_H 1
#line 53 "pars0lex.l"
#define YYSTYPE que_node_t*

#include "univ.i"
#include "pars0pars.h"
#include "pars0grm.h"
#include "pars0sym.h"
#include "mem0mem.h"
#include "os0proc.h"

#define malloc(A) ut::malloc_withkey(UT_NEW_THIS_FILE_PSI_KEY, A)
#define free(A) ut::free(A)
#define realloc(P, A) ut::realloc(P, A)
#define exit(A)         ut_error

/* Note: We cast result from int to yy_size_t */
#define YY_INPUT(buf, result, max_size) \
        ((result) = pars_get_lex_chars(buf, max_size))

/* String buffer for removing quotes */
static ulint    stringbuf_len_alloc = 0; /* Allocated length */
static ulint    stringbuf_len = 0; /* Current length */
static char*    stringbuf; /* Start of buffer */
/** Appends a string to the buffer. */
static
void
string_append(
/*==========*/
        const char*     str,    /*!< in: string to be appended */
        ulint           len)    /*!< in: length of the string */
{
  if (stringbuf == nullptr) {
    stringbuf = static_cast<char *>(malloc(1));
    stringbuf_len_alloc = 1;
  }

        if (stringbuf_len + len > stringbuf_len_alloc) {
                while (stringbuf_len + len > stringbuf_len_alloc) {
                        stringbuf_len_alloc <<= 1;
                }

                stringbuf = static_cast<char*>(
                        realloc(stringbuf, stringbuf_len_alloc));
        }

        memcpy(stringbuf + stringbuf_len, str, len);
        stringbuf_len += len;
}




#line 947 "lexyy.cc"

#define INITIAL 0
#define comment 1
#define quoted 2
#define id 3

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
#include <unistd.h>
#endif

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

static int yy_init_globals (void );

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

[[maybe_unused]] static int yylex_destroy (void );

[[maybe_unused]] static int yyget_debug (void );

[[maybe_unused]] static void yyset_debug (int debug_flag  );

YY_EXTRA_TYPE yyget_extra (void );



[[maybe_unused]] static FILE *yyget_in (void );

[[maybe_unused]] static void yyset_in  (FILE * in_str  );

[[maybe_unused]] static FILE *yyget_out (void );

[[maybe_unused]] static void yyset_out  (FILE * out_str  );

yy_size_t yyget_leng (void );

[[maybe_unused]] static char *yyget_text (void );

[[maybe_unused]] static int yyget_lineno (void );

[[maybe_unused]] static void yyset_lineno (int line_number  );

/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int yywrap (void );
#else
extern int yywrap (void );
#endif
#endif

#ifndef yytext_ptr
static void yy_flex_strncpy (char *,yyconst char *,int );
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (yyconst char * );
#endif

#ifndef YY_NO_INPUT

#ifdef __cplusplus
static int yyinput (void );
#else
static int input (void );
#endif

#endif

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* This used to be an fputs(), but since the string might contain NUL's,
 * we now use fwrite().
 */
#define ECHO do { if (fwrite( yytext, yyleng, 1, yyout )) {} } while (0)
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
        if ( YY_CURRENT_BUFFER_LVALUE->yy_is_interactive ) \
                { \
                int c = '*'; \
                size_t n; \
                for ( n = 0; n < max_size && \
                             (c = getc( yyin )) != EOF && c != '\n'; ++n ) \
                        buf[n] = (char) c; \
                if ( c == '\n' ) \
                        buf[n++] = (char) c; \
                if ( c == EOF && ferror( yyin ) ) \
                        YY_FATAL_ERROR( "input in flex scanner failed" ); \
                result = n; \
                } \
        else \
                { \
                errno=0; \
                while ( (result = fread(buf, 1, max_size, yyin))==0 && ferror(yyin)) \
                        { \
                        if( errno != EINTR) \
                                { \
                                YY_FATAL_ERROR( "input in flex scanner failed" ); \
                                break; \
                                } \
                        errno=0; \
                        clearerr(yyin); \
                        } \
                }\
\

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
#define YY_FATAL_ERROR(msg) yy_fatal_error( msg )
#endif

/* end tables serialization structures and prototypes */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1

extern int yylex (void);

#define YY_DECL int yylex (void)
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK break;
#endif

#define YY_RULE_SETUP \
        YY_USER_ACTION

/** The main scanner function which does all the work.
 */
YY_DECL
{
        yy_state_type yy_current_state;
        char *yy_cp, *yy_bp;
        int yy_act;
    
        if ( !(yy_init) )
                {
                (yy_init) = 1;

#ifdef YY_USER_INIT
                YY_USER_INIT;
#endif

                if ( ! (yy_start) )
                        (yy_start) = 1; /* first start state */

                if ( ! yyin )
                        yyin = stdin;

                if ( ! yyout )
                        yyout = stdout;

                if ( ! YY_CURRENT_BUFFER ) {
                        yyensure_buffer_stack ();
                        YY_CURRENT_BUFFER_LVALUE =
                                yy_create_buffer(yyin,YY_BUF_SIZE );
                }

                yy_load_buffer_state( );
                }

        {
#line 112 "pars0lex.l"


#line 1165 "lexyy.cc"

        while ( true )          /* loops until end-of-file is reached */
                {
                yy_cp = (yy_c_buf_p);

                /* Support of yytext. */
                *yy_cp = (yy_hold_char);

                /* yy_bp points to the position in yy_ch_buf of the start of
                 * the current run.
                 */
                yy_bp = yy_cp;

                yy_current_state = (yy_start);
yy_match:
                do
                        {
                        YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
                        if ( yy_accept[yy_current_state] )
                                {
                                (yy_last_accepting_state) = yy_current_state;
                                (yy_last_accepting_cpos) = yy_cp;
                                }
                        while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
                                {
                                yy_current_state = (int) yy_def[yy_current_state];
                                if ( yy_current_state >= 372 )
                                        yy_c = yy_meta[(unsigned int) yy_c];
                                }
                        yy_current_state = yy_nxt[yy_base[yy_current_state] + (unsigned int) yy_c];
                        ++yy_cp;
                        }
                while ( yy_current_state != 371 );
                yy_cp = (yy_last_accepting_cpos);
                yy_current_state = (yy_last_accepting_state);

yy_find_action:
                yy_act = yy_accept[yy_current_state];

                YY_DO_BEFORE_ACTION;

do_action:      /* This label is used only to access EOF actions. */

                switch ( yy_act )
        { /* beginning of action switch */
                        case 0: /* must back up */
                        /* undo the effects of YY_DO_BEFORE_ACTION */
                        *yy_cp = (yy_hold_char);
                        yy_cp = (yy_last_accepting_cpos);
                        yy_current_state = (yy_last_accepting_state);
                        goto yy_find_action;

case 1:
YY_RULE_SETUP
#line 114 "pars0lex.l"
{
                        yylval = sym_tab_add_int_lit(pars_sym_tab_global,
                                                                atoi(yytext));
                        return(PARS_INT_LIT);
}
        YY_BREAK
case 2:
YY_RULE_SETUP
#line 120 "pars0lex.l"
{
                        ut_error;       /* not implemented */

                        return(PARS_FLOAT_LIT);
}
        YY_BREAK
case 3:
YY_RULE_SETUP
#line 126 "pars0lex.l"
{
                        ulint   type;

                        yylval = sym_tab_add_bound_lit(pars_sym_tab_global,
                                yytext + 1, &type);

                        return((int) type);
}
        YY_BREAK
case 4:
YY_RULE_SETUP
#line 135 "pars0lex.l"
{
                        yylval = sym_tab_add_bound_id(pars_sym_tab_global,
                                yytext + 1);

                        return(PARS_ID_TOKEN);
}
        YY_BREAK
case 5:
YY_RULE_SETUP
#line 142 "pars0lex.l"
{
/* Quoted character string literals are handled in an explicit
start state 'quoted'.  This state is entered and the buffer for
the scanned string is emptied upon encountering a starting quote.

In the state 'quoted', only two actions are possible (defined below). */
                        BEGIN(quoted);
                        stringbuf_len = 0;
}
        YY_BREAK
case 6:
/* rule 6 can match eol */
YY_RULE_SETUP
#line 151 "pars0lex.l"
{
                        /* Got a sequence of characters other than "'":
                        append to string buffer */
                        string_append(yytext, yyleng);
}
        YY_BREAK
case 7:
YY_RULE_SETUP
#line 156 "pars0lex.l"
{
                        /* Got a sequence of "'" characters:
                        append half of them to string buffer,
                        as "''" represents a single "'".
                        We apply truncating division,
                        so that "'''" will result in "'". */

                        string_append(yytext, yyleng / 2);

                        /* If we got an odd number of quotes, then the
                        last quote we got is the terminating quote.
                        At the end of the string, we return to the
                        initial start state and report the scanned
                        string literal. */

                        if (yyleng % 2) {
                                BEGIN(INITIAL);
                                yylval = sym_tab_add_str_lit(
                                        pars_sym_tab_global,
                                        (byte*) stringbuf, stringbuf_len);
                                return(PARS_STR_LIT);
                        }
}
        YY_BREAK
case 8:
YY_RULE_SETUP
#line 180 "pars0lex.l"
{
/* Quoted identifiers are handled in an explicit start state 'id'.
This state is entered and the buffer for the scanned string is emptied
upon encountering a starting quote.

In the state 'id', only two actions are possible (defined below). */
                        BEGIN(id);
                        stringbuf_len = 0;
}
        YY_BREAK
case 9:
/* rule 9 can match eol */
YY_RULE_SETUP
#line 189 "pars0lex.l"
{
                        /* Got a sequence of characters other than '"':
                        append to string buffer */
                        string_append(yytext, yyleng);
}
        YY_BREAK
case 10:
YY_RULE_SETUP
#line 194 "pars0lex.l"
{
                        /* Got a sequence of '"' characters:
                        append half of them to string buffer,
                        as '""' represents a single '"'.
                        We apply truncating division,
                        so that '"""' will result in '"'. */

                        string_append(yytext, yyleng / 2);

                        /* If we got an odd number of quotes, then the
                        last quote we got is the terminating quote.
                        At the end of the string, we return to the
                        initial start state and report the scanned
                        identifier. */

                        if (yyleng % 2) {
                                BEGIN(INITIAL);
                                yylval = sym_tab_add_id(
                                        pars_sym_tab_global,
                                        (byte*) stringbuf, stringbuf_len);

                                return(PARS_ID_TOKEN);
                        }
}
        YY_BREAK
case 11:
YY_RULE_SETUP
#line 219 "pars0lex.l"
{
                        yylval = sym_tab_add_null_lit(pars_sym_tab_global);

                        return(PARS_NULL_LIT);
}
        YY_BREAK
case 12:
YY_RULE_SETUP
#line 225 "pars0lex.l"
{
                        /* Implicit cursor name */
                        yylval = sym_tab_add_str_lit(pars_sym_tab_global,
                                                        (byte*) yytext, yyleng);
                        return(PARS_SQL_TOKEN);
}
        YY_BREAK
case 13:
YY_RULE_SETUP
#line 232 "pars0lex.l"
{
                        return(PARS_AND_TOKEN);
}
        YY_BREAK
case 14:
YY_RULE_SETUP
#line 236 "pars0lex.l"
{
                        return(PARS_OR_TOKEN);
}
        YY_BREAK
case 15:
YY_RULE_SETUP
#line 240 "pars0lex.l"
{
                        return(PARS_NOT_TOKEN);
}
        YY_BREAK
case 16:
YY_RULE_SETUP
#line 244 "pars0lex.l"
{
                        return(PARS_PROCEDURE_TOKEN);
}
        YY_BREAK
case 17:
YY_RULE_SETUP
#line 248 "pars0lex.l"
{
                        return(PARS_IN_TOKEN);
}
        YY_BREAK
case 18:
YY_RULE_SETUP
#line 252 "pars0lex.l"
{
                        return(PARS_OUT_TOKEN);
}
        YY_BREAK
case 19:
YY_RULE_SETUP
#line 256 "pars0lex.l"
{
                        return(PARS_BINARY_TOKEN);
}
        YY_BREAK
case 20:
YY_RULE_SETUP
#line 260 "pars0lex.l"
{
                        return(PARS_BLOB_TOKEN);
}
        YY_BREAK
case 21:
YY_RULE_SETUP
#line 264 "pars0lex.l"
{
                        return(PARS_INT_TOKEN);
}
        YY_BREAK
case 22:
YY_RULE_SETUP
#line 268 "pars0lex.l"
{
                        return(PARS_INT_TOKEN);
}
        YY_BREAK
case 23:
YY_RULE_SETUP
#line 272 "pars0lex.l"
{
                        return(PARS_FLOAT_TOKEN);
}
        YY_BREAK
case 24:
YY_RULE_SETUP
#line 276 "pars0lex.l"
{
                        return(PARS_CHAR_TOKEN);
}
        YY_BREAK
case 25:
YY_RULE_SETUP
#line 280 "pars0lex.l"
{
                        return(PARS_IS_TOKEN);
}
        YY_BREAK
case 26:
YY_RULE_SETUP
#line 284 "pars0lex.l"
{
                        return(PARS_BEGIN_TOKEN);
}
        YY_BREAK
case 27:
YY_RULE_SETUP
#line 288 "pars0lex.l"
{
                        return(PARS_END_TOKEN);
}
        YY_BREAK
case 28:
YY_RULE_SETUP
#line 292 "pars0lex.l"
{
                        return(PARS_IF_TOKEN);
}
        YY_BREAK
case 29:
YY_RULE_SETUP
#line 296 "pars0lex.l"
{
                        return(PARS_THEN_TOKEN);
}
        YY_BREAK
case 30:
YY_RULE_SETUP
#line 300 "pars0lex.l"
{
                        return(PARS_ELSE_TOKEN);
}
        YY_BREAK
case 31:
YY_RULE_SETUP
#line 304 "pars0lex.l"
{
                        return(PARS_ELSIF_TOKEN);
}
        YY_BREAK
case 32:
YY_RULE_SETUP
#line 308 "pars0lex.l"
{
                        return(PARS_LOOP_TOKEN);
}
        YY_BREAK
case 33:
YY_RULE_SETUP
#line 312 "pars0lex.l"
{
                        return(PARS_WHILE_TOKEN);
}
        YY_BREAK
case 34:
YY_RULE_SETUP
#line 316 "pars0lex.l"
{
                        return(PARS_RETURN_TOKEN);
}
        YY_BREAK
case 35:
YY_RULE_SETUP
#line 320 "pars0lex.l"
{
                        return(PARS_SELECT_TOKEN);
}
        YY_BREAK
case 36:
YY_RULE_SETUP
#line 324 "pars0lex.l"
{
                        return(PARS_SUM_TOKEN);
}
        YY_BREAK
case 37:
YY_RULE_SETUP
#line 328 "pars0lex.l"
{
                        return(PARS_COUNT_TOKEN);
}
        YY_BREAK
case 38:
YY_RULE_SETUP
#line 332 "pars0lex.l"
{
                        return(PARS_DISTINCT_TOKEN);
}
        YY_BREAK
case 39:
YY_RULE_SETUP
#line 336 "pars0lex.l"
{
                        return(PARS_FROM_TOKEN);
}
        YY_BREAK
case 40:
YY_RULE_SETUP
#line 340 "pars0lex.l"
{
                        return(PARS_WHERE_TOKEN);
}
        YY_BREAK
case 41:
YY_RULE_SETUP
#line 344 "pars0lex.l"
{
                        return(PARS_FOR_TOKEN);
}
        YY_BREAK
case 42:
YY_RULE_SETUP
#line 348 "pars0lex.l"
{
                        return(PARS_READ_TOKEN);
}
        YY_BREAK
case 43:
YY_RULE_SETUP
#line 352 "pars0lex.l"
{
                        return(PARS_ORDER_TOKEN);
}
        YY_BREAK
case 44:
YY_RULE_SETUP
#line 356 "pars0lex.l"
{
                        return(PARS_BY_TOKEN);
}
        YY_BREAK
case 45:
YY_RULE_SETUP
#line 360 "pars0lex.l"
{
                        return(PARS_ASC_TOKEN);
}
        YY_BREAK
case 46:
YY_RULE_SETUP
#line 364 "pars0lex.l"
{
                        return(PARS_DESC_TOKEN);
}
        YY_BREAK
case 47:
YY_RULE_SETUP
#line 368 "pars0lex.l"
{
                        return(PARS_INSERT_TOKEN);
}
        YY_BREAK
case 48:
YY_RULE_SETUP
#line 372 "pars0lex.l"
{
                        return(PARS_INTO_TOKEN);
}
        YY_BREAK
case 49:
YY_RULE_SETUP
#line 376 "pars0lex.l"
{
                        return(PARS_VALUES_TOKEN);
}
        YY_BREAK
case 50:
YY_RULE_SETUP
#line 380 "pars0lex.l"
{
                        return(PARS_UPDATE_TOKEN);
}
        YY_BREAK
case 51:
YY_RULE_SETUP
#line 384 "pars0lex.l"
{
                        return(PARS_SET_TOKEN);
}
        YY_BREAK
case 52:
YY_RULE_SETUP
#line 388 "pars0lex.l"
{
                        return(PARS_DELETE_TOKEN);
}
        YY_BREAK
case 53:
YY_RULE_SETUP
#line 392 "pars0lex.l"
{
                        return(PARS_CURRENT_TOKEN);
}
        YY_BREAK
case 54:
YY_RULE_SETUP
#line 396 "pars0lex.l"
{
                        return(PARS_OF_TOKEN);
}
        YY_BREAK
case 55:
YY_RULE_SETUP
#line 400 "pars0lex.l"
{
                        return(PARS_CREATE_TOKEN);
}
        YY_BREAK
case 56:
YY_RULE_SETUP
#line 404 "pars0lex.l"
{
                        return(PARS_TABLE_TOKEN);
}
        YY_BREAK
case 57:
YY_RULE_SETUP
#line 408 "pars0lex.l"
{
                        return(PARS_COMPACT_TOKEN);
}
        YY_BREAK
case 58:
YY_RULE_SETUP
#line 412 "pars0lex.l"
{
                        return(PARS_BLOCK_SIZE_TOKEN);
}
        YY_BREAK
case 59:
YY_RULE_SETUP
#line 416 "pars0lex.l"
{
                        return(PARS_INDEX_TOKEN);
}
        YY_BREAK
case 60:
YY_RULE_SETUP
#line 420 "pars0lex.l"
{
                        return(PARS_UNIQUE_TOKEN);
}
        YY_BREAK
case 61:
YY_RULE_SETUP
#line 424 "pars0lex.l"
{
                        return(PARS_CLUSTERED_TOKEN);
}
        YY_BREAK
case 62:
YY_RULE_SETUP
#line 428 "pars0lex.l"
{
                        return(PARS_DOES_NOT_FIT_IN_MEM_TOKEN);
}
        YY_BREAK
case 63:
YY_RULE_SETUP
#line 432 "pars0lex.l"
{
                        return(PARS_ON_TOKEN);
}
        YY_BREAK
case 64:
YY_RULE_SETUP
#line 436 "pars0lex.l"
{
                        return(PARS_DECLARE_TOKEN);
}
        YY_BREAK
case 65:
YY_RULE_SETUP
#line 440 "pars0lex.l"
{
                        return(PARS_CURSOR_TOKEN);
}
        YY_BREAK
case 66:
YY_RULE_SETUP
#line 444 "pars0lex.l"
{
                        return(PARS_OPEN_TOKEN);
}
        YY_BREAK
case 67:
YY_RULE_SETUP
#line 448 "pars0lex.l"
{
                        return(PARS_FETCH_TOKEN);
}
        YY_BREAK
case 68:
YY_RULE_SETUP
#line 452 "pars0lex.l"
{
                        return(PARS_CLOSE_TOKEN);
}
        YY_BREAK
case 69:
YY_RULE_SETUP
#line 456 "pars0lex.l"
{
                        return(PARS_NOTFOUND_TOKEN);
}
        YY_BREAK
case 70:
YY_RULE_SETUP
#line 460 "pars0lex.l"
{
                        return(PARS_TO_BINARY_TOKEN);
}
        YY_BREAK
case 71:
YY_RULE_SETUP
#line 464 "pars0lex.l"
{
                        return(PARS_SUBSTR_TOKEN);
}
        YY_BREAK
case 72:
YY_RULE_SETUP
#line 468 "pars0lex.l"
{
                        return(PARS_CONCAT_TOKEN);
}
        YY_BREAK
case 73:
YY_RULE_SETUP
#line 472 "pars0lex.l"
{
                        return(PARS_INSTR_TOKEN);
}
        YY_BREAK
case 74:
YY_RULE_SETUP
#line 476 "pars0lex.l"
{
                        return(PARS_LENGTH_TOKEN);
}
        YY_BREAK
case 75:
YY_RULE_SETUP
#line 480 "pars0lex.l"
{
                        return(PARS_COMMIT_TOKEN);
}
        YY_BREAK
case 76:
YY_RULE_SETUP
#line 484 "pars0lex.l"
{
                        return(PARS_ROLLBACK_TOKEN);
}
        YY_BREAK
case 77:
YY_RULE_SETUP
#line 488 "pars0lex.l"
{
                        return(PARS_WORK_TOKEN);
}
        YY_BREAK
case 78:
YY_RULE_SETUP
#line 492 "pars0lex.l"
{
                        return(PARS_UNSIGNED_TOKEN);
}
        YY_BREAK
case 79:
YY_RULE_SETUP
#line 496 "pars0lex.l"
{
                        return(PARS_EXIT_TOKEN);
}
        YY_BREAK
case 80:
YY_RULE_SETUP
#line 500 "pars0lex.l"
{
                        return(PARS_FUNCTION_TOKEN);
}
        YY_BREAK
case 81:
YY_RULE_SETUP
#line 504 "pars0lex.l"
{
                        return(PARS_LOCK_TOKEN);
}
        YY_BREAK
case 82:
YY_RULE_SETUP
#line 508 "pars0lex.l"
{
                        return(PARS_SHARE_TOKEN);
}
        YY_BREAK
case 83:
YY_RULE_SETUP
#line 512 "pars0lex.l"
{
                        return(PARS_MODE_TOKEN);
}
        YY_BREAK
case 84:
YY_RULE_SETUP
#line 516 "pars0lex.l"
{
                        return(PARS_LIKE_TOKEN);
}
        YY_BREAK
case 85:
YY_RULE_SETUP
#line 520 "pars0lex.l"
{
                        return(PARS_BIGINT_TOKEN);
}
        YY_BREAK
case 86:
YY_RULE_SETUP
#line 524 "pars0lex.l"
{
                        yylval = sym_tab_add_id(pars_sym_tab_global,
                                                        (byte*) yytext,
                                                        ut_strlen(yytext));
                        return(PARS_ID_TOKEN);
}
        YY_BREAK
case 87:
YY_RULE_SETUP
#line 531 "pars0lex.l"
{
                        yylval = sym_tab_add_id(pars_sym_tab_global,
                                                        (byte*) yytext,
                                                        ut_strlen(yytext));
                        return(PARS_TABLE_NAME_TOKEN);
}
        YY_BREAK
case 88:
YY_RULE_SETUP
#line 538 "pars0lex.l"
{
                        return(PARS_DDOT_TOKEN);
}
        YY_BREAK
case 89:
YY_RULE_SETUP
#line 542 "pars0lex.l"
{
                        return(PARS_ASSIGN_TOKEN);
}
        YY_BREAK
case 90:
YY_RULE_SETUP
#line 546 "pars0lex.l"
{
                        return(PARS_LE_TOKEN);
}
        YY_BREAK
case 91:
YY_RULE_SETUP
#line 550 "pars0lex.l"
{
                        return(PARS_GE_TOKEN);
}
        YY_BREAK
case 92:
YY_RULE_SETUP
#line 554 "pars0lex.l"
{
                        return(PARS_NE_TOKEN);
}
        YY_BREAK
case 93:
YY_RULE_SETUP
#line 558 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 94:
YY_RULE_SETUP
#line 563 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 95:
YY_RULE_SETUP
#line 568 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 96:
YY_RULE_SETUP
#line 573 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 97:
YY_RULE_SETUP
#line 578 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 98:
YY_RULE_SETUP
#line 583 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 99:
YY_RULE_SETUP
#line 588 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 100:
YY_RULE_SETUP
#line 593 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 101:
YY_RULE_SETUP
#line 598 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 102:
YY_RULE_SETUP
#line 603 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 103:
YY_RULE_SETUP
#line 608 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 104:
YY_RULE_SETUP
#line 613 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 105:
YY_RULE_SETUP
#line 618 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 106:
YY_RULE_SETUP
#line 623 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 107:
YY_RULE_SETUP
#line 628 "pars0lex.l"
{

                        return((int)(*yytext));
}
        YY_BREAK
case 108:
YY_RULE_SETUP
#line 633 "pars0lex.l"
BEGIN(comment); /* eat up comment */
        YY_BREAK
case 109:
/* rule 109 can match eol */
YY_RULE_SETUP
#line 635 "pars0lex.l"

        YY_BREAK
case 110:
/* rule 110 can match eol */
YY_RULE_SETUP
#line 636 "pars0lex.l"

        YY_BREAK
case 111:
YY_RULE_SETUP
#line 637 "pars0lex.l"
BEGIN(INITIAL);
        YY_BREAK
case 112:
/* rule 112 can match eol */
YY_RULE_SETUP
#line 639 "pars0lex.l"
/* eat up whitespace */
        YY_BREAK
case 113:
YY_RULE_SETUP
#line 642 "pars0lex.l"
{
                        fprintf(stderr,"Unrecognized character: %02x\n",
                                *yytext);

                        ut_error;

                        return(0);
}
        YY_BREAK
case 114:
YY_RULE_SETUP
#line 651 "pars0lex.l"
YY_FATAL_ERROR( "flex scanner jammed" );
        YY_BREAK
#line 2109 "lexyy.cc"
case YY_STATE_EOF(INITIAL):
case YY_STATE_EOF(comment):
case YY_STATE_EOF(quoted):
case YY_STATE_EOF(id):
        yyterminate();

        case YY_END_OF_BUFFER:
                {
                /* Amount of text matched not including the EOB char. */
                int yy_amount_of_matched_text = (int) (yy_cp - (yytext_ptr)) - 1;

                /* Undo the effects of YY_DO_BEFORE_ACTION. */
                *yy_cp = (yy_hold_char);
                YY_RESTORE_YY_MORE_OFFSET

                if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
                        {
                        /* We're scanning a new file or input source.  It's
                         * possible that this happened because the user
                         * just pointed yyin at a new source and called
                         * yylex().  If so, then we have to assure
                         * consistency between YY_CURRENT_BUFFER and our
                         * globals.  Here is the right place to do so, because
                         * this is the first action (other than possibly a
                         * back-up) that will match for the new input source.
                         */
                        (yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
                        YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin;
                        YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
                        }

                /* Note that here we test for yy_c_buf_p "<=" to the position
                 * of the first EOB in the buffer, since yy_c_buf_p will
                 * already have been incremented past the NUL character
                 * (since all states make transitions on EOB to the
                 * end-of-buffer state).  Contrast this with the test
                 * in input().
                 */
                if ( (yy_c_buf_p) <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
                        { /* This was really a NUL. */
                        yy_state_type yy_next_state;

                        (yy_c_buf_p) = (yytext_ptr) + yy_amount_of_matched_text;

                        yy_current_state = yy_get_previous_state(  );

                        /* Okay, we're now positioned to make the NUL
                         * transition.  We couldn't have
                         * yy_get_previous_state() go ahead and do it
                         * for us because it doesn't know how to deal
                         * with the possibility of jamming (and we don't
                         * want to build jamming into it because then it
                         * will run more slowly).
                         */

                        yy_next_state = yy_try_NUL_trans( yy_current_state );

                        yy_bp = (yytext_ptr) + YY_MORE_ADJ;

                        if ( yy_next_state )
                                {
                                /* Consume the NUL. */
                                yy_cp = ++(yy_c_buf_p);
                                yy_current_state = yy_next_state;
                                goto yy_match;
                                }

                        else
                                {
                                yy_cp = (yy_last_accepting_cpos);
                                yy_current_state = (yy_last_accepting_state);
                                goto yy_find_action;
                                }
                        }

                else switch ( yy_get_next_buffer(  ) )
                        {
                        case EOB_ACT_END_OF_FILE:
                                {
                                (yy_did_buffer_switch_on_eof) = 0;

                                if ( yywrap( ) )
                                        {
                                        /* Note: because we've taken care in
                                         * yy_get_next_buffer() to have set up
                                         * yytext, we can now set up
                                         * yy_c_buf_p so that if some total
                                         * hoser (like flex itself) wants to
                                         * call the scanner after we return the
                                         * YY_NULL, it'll still work - another
                                         * YY_NULL will get returned.
                                         */
                                        (yy_c_buf_p) = (yytext_ptr) + YY_MORE_ADJ;

                                        yy_act = YY_STATE_EOF(YY_START);
                                        goto do_action;
                                        }

                                else
                                        {
                                        if ( ! (yy_did_buffer_switch_on_eof) )
                                                YY_NEW_FILE;
                                        }
                                break;
                                }

                        case EOB_ACT_CONTINUE_SCAN:
                                (yy_c_buf_p) =
                                        (yytext_ptr) + yy_amount_of_matched_text;

                                yy_current_state = yy_get_previous_state(  );

                                yy_cp = (yy_c_buf_p);
                                yy_bp = (yytext_ptr) + YY_MORE_ADJ;
                                goto yy_match;

                        case EOB_ACT_LAST_MATCH:
                                (yy_c_buf_p) =
                                &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)];

                                yy_current_state = yy_get_previous_state(  );

                                yy_cp = (yy_c_buf_p);
                                yy_bp = (yytext_ptr) + YY_MORE_ADJ;
                                goto yy_find_action;
                        }
                break;
                }

        default:
                YY_FATAL_ERROR(
                        "fatal flex scanner internal error--no action found" );
        } /* end of action switch */
                } /* end of scanning one token */
        } /* end of user's declarations */
} /* end of yylex */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *      EOB_ACT_LAST_MATCH -
 *      EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *      EOB_ACT_END_OF_FILE - end of file
 */
static int yy_get_next_buffer (void)
{
        char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
        char *source = (yytext_ptr);
        int number_to_move, i;
        int ret_val;

        if ( (yy_c_buf_p) > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] )
                YY_FATAL_ERROR(
                "fatal flex scanner internal error--end of buffer missed" );

        if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
                { /* Don't try to fill the buffer, so this is an EOF. */
                if ( (yy_c_buf_p) - (yytext_ptr) - YY_MORE_ADJ == 1 )
                        {
                        /* We matched a single character, the EOB, so
                         * treat this as a final EOF.
                         */
                        return EOB_ACT_END_OF_FILE;
                        }

                else
                        {
                        /* We matched some text prior to the EOB, first
                         * process it.
                         */
                        return EOB_ACT_LAST_MATCH;
                        }
                }

        /* Try to read more data. */

        /* First move last chars to start of buffer. */
        number_to_move = (int) ((yy_c_buf_p) - (yytext_ptr)) - 1;

        for ( i = 0; i < number_to_move; ++i )
                *(dest++) = *(source++);

        if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
                /* don't do the read, it's not guaranteed to return an EOF,
                 * just force an EOF
                 */
                YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars) = 0;

        else
                {
                        yy_size_t num_to_read =
                        YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

                while ( num_to_read <= 0 )
                        { /* Not enough room in the buffer - grow it. */

                        /* just a shorter name for the current buffer */
                        YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

                        int yy_c_buf_p_offset =
                                (int) ((yy_c_buf_p) - b->yy_ch_buf);

                        if ( b->yy_is_our_buffer )
                                {
                                yy_size_t new_size = b->yy_buf_size * 2;

                                if ( new_size <= 0 )
                                        b->yy_buf_size += b->yy_buf_size / 8;
                                else
                                        b->yy_buf_size *= 2;

                                b->yy_ch_buf = (char *)
                                        /* Include room in for 2 EOB chars. */
                                        yyrealloc((void *) b->yy_ch_buf,b->yy_buf_size + 2  );
                                }
                        else
                                /* Can't grow it, we don't own it. */
                                b->yy_ch_buf = nullptr;

                        if ( ! b->yy_ch_buf )
                                YY_FATAL_ERROR(
                                "fatal error - scanner input buffer overflow" );

                        (yy_c_buf_p) = &b->yy_ch_buf[yy_c_buf_p_offset];

                        num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
                                                number_to_move - 1;

                        }

                if ( num_to_read > YY_READ_BUF_SIZE )
                        num_to_read = YY_READ_BUF_SIZE;

                /* Read in more data. */
                YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
                        (yy_n_chars), num_to_read );

                YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
                }

        if ( (yy_n_chars) == 0 )
                {
                if ( number_to_move == YY_MORE_ADJ )
                        {
                        ret_val = EOB_ACT_END_OF_FILE;
                        yyrestart(yyin  );
                        }

                else
                        {
                        ret_val = EOB_ACT_LAST_MATCH;
                        YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
                                YY_BUFFER_EOF_PENDING;
                        }
                }

        else
                ret_val = EOB_ACT_CONTINUE_SCAN;

        if ((yy_size_t) ((yy_n_chars) + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
                /* Extend the array by 50%, plus the number we really need. */
                yy_size_t new_size = (yy_n_chars) + number_to_move + ((yy_n_chars) >> 1);
                YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) yyrealloc((void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf,new_size  );
                if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
                        YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
        }

        (yy_n_chars) += number_to_move;
        YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] = YY_END_OF_BUFFER_CHAR;
        YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] = YY_END_OF_BUFFER_CHAR;

        (yytext_ptr) = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

        return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

    static yy_state_type yy_get_previous_state (void)
{
        yy_state_type yy_current_state;
        char *yy_cp;
    
        yy_current_state = (yy_start);

        for ( yy_cp = (yytext_ptr) + YY_MORE_ADJ; yy_cp < (yy_c_buf_p); ++yy_cp )
                {
                YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
                if ( yy_accept[yy_current_state] )
                        {
                        (yy_last_accepting_state) = yy_current_state;
                        (yy_last_accepting_cpos) = yy_cp;
                        }
                while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
                        {
                        yy_current_state = (int) yy_def[yy_current_state];
                        if ( yy_current_state >= 372 )
                                yy_c = yy_meta[(unsigned int) yy_c];
                        }
                yy_current_state = yy_nxt[yy_base[yy_current_state] + (unsigned int) yy_c];
                }

        return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *      next_state = yy_try_NUL_trans( current_state );
 */
    static yy_state_type yy_try_NUL_trans  (yy_state_type yy_current_state )
{
        int yy_is_jam;
        char *yy_cp = (yy_c_buf_p);

        YY_CHAR yy_c = 1;
        if ( yy_accept[yy_current_state] )
                {
                (yy_last_accepting_state) = yy_current_state;
                (yy_last_accepting_cpos) = yy_cp;
                }
        while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
                {
                yy_current_state = (int) yy_def[yy_current_state];
                if ( yy_current_state >= 372 )
                        yy_c = yy_meta[(unsigned int) yy_c];
                }
        yy_current_state = yy_nxt[yy_base[yy_current_state] + (unsigned int) yy_c];
        yy_is_jam = (yy_current_state == 371);

                return yy_is_jam ? 0 : yy_current_state;
}

#ifndef YY_NO_INPUT
#ifdef __cplusplus
    static int yyinput (void)
#else
    static int input  (void)
#endif

{
        int c;
    
        *(yy_c_buf_p) = (yy_hold_char);

        if ( *(yy_c_buf_p) == YY_END_OF_BUFFER_CHAR )
                {
                /* yy_c_buf_p now points to the character we want to return.
                 * If this occurs *before* the EOB characters, then it's a
                 * valid NUL; if not, then we've hit the end of the buffer.
                 */
                if ( (yy_c_buf_p) < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
                        /* This was really a NUL. */
                        *(yy_c_buf_p) = '\0';

                else
                        { /* need more input */
                        yy_size_t offset = (yy_c_buf_p) - (yytext_ptr);
                        ++(yy_c_buf_p);

                        switch ( yy_get_next_buffer(  ) )
                                {
                                case EOB_ACT_LAST_MATCH:
                                        /* This happens because yy_g_n_b()
                                         * sees that we've accumulated a
                                         * token and flags that we need to
                                         * try matching the token before
                                         * proceeding.  But for input(),
                                         * there's no matching to consider.
                                         * So convert the EOB_ACT_LAST_MATCH
                                         * to EOB_ACT_END_OF_FILE.
                                         */

                                        /* Reset buffer status. */
                                        yyrestart(yyin );

                                        /*FALLTHROUGH*/

                                case EOB_ACT_END_OF_FILE:
                                        {
                                        if ( yywrap( ) )
                                                return EOF;

                                        if ( ! (yy_did_buffer_switch_on_eof) )
                                                YY_NEW_FILE;
#ifdef __cplusplus
                                        return yyinput();
#else
                                        return input();
#endif
                                        }

                                case EOB_ACT_CONTINUE_SCAN:
                                        (yy_c_buf_p) = (yytext_ptr) + offset;
                                        break;
                                }
                        }
                }

        c = *(unsigned char *) (yy_c_buf_p);    /* cast for 8-bit char's */
        *(yy_c_buf_p) = '\0';   /* preserve yytext */
        (yy_hold_char) = *++(yy_c_buf_p);

        return c;
}
#endif  /* ifndef YY_NO_INPUT */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * 
 * @note This function does not reset the start condition to @c INITIAL .
 */
    static void yyrestart  (FILE * input_file )
{
    
        if ( ! YY_CURRENT_BUFFER ){
        yyensure_buffer_stack ();
                YY_CURRENT_BUFFER_LVALUE =
            yy_create_buffer(yyin,YY_BUF_SIZE );
        }

        yy_init_buffer(YY_CURRENT_BUFFER,input_file );
        yy_load_buffer_state( );
}

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * 
 */
    [[maybe_unused]] static void yy_switch_to_buffer  (YY_BUFFER_STATE  new_buffer )
{
    
        /* TODO. We should be able to replace this entire function body
         * with
         *              yypop_buffer_state();
         *              yypush_buffer_state(new_buffer);
     */
        yyensure_buffer_stack ();
        if ( YY_CURRENT_BUFFER == new_buffer )
                return;

        if ( YY_CURRENT_BUFFER )
                {
                /* Flush out information for old buffer. */
                *(yy_c_buf_p) = (yy_hold_char);
                YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
                YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
                }

        YY_CURRENT_BUFFER_LVALUE = new_buffer;
        yy_load_buffer_state( );

        /* We don't actually know whether we did this switch during
         * EOF (yywrap()) processing, but the only time this flag
         * is looked at is after yywrap() is called, so it's safe
         * to go ahead and always set it.
         */
        (yy_did_buffer_switch_on_eof) = 1;
}

static void yy_load_buffer_state  (void)
{
        (yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
        (yytext_ptr) = (yy_c_buf_p) = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
        yyin = YY_CURRENT_BUFFER_LVALUE->yy_input_file;
        (yy_hold_char) = *(yy_c_buf_p);
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * 
 * @return the allocated buffer state.
 */
    static YY_BUFFER_STATE yy_create_buffer  (FILE * file, int  size )
{
        YY_BUFFER_STATE b;
    
        b = (YY_BUFFER_STATE) yyalloc(sizeof( struct yy_buffer_state )  );
        if ( ! b )
                YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

        b->yy_buf_size = size;

        /* yy_ch_buf has to be 2 characters longer than the size given because
         * we need to put in 2 end-of-buffer characters.
         */
        b->yy_ch_buf = (char *) yyalloc(b->yy_buf_size + 2  );
        if ( ! b->yy_ch_buf )
                YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

        b->yy_is_our_buffer = 1;

        yy_init_buffer(b,file );

        return b;
}

/** Destroy the buffer.
 * @param b a buffer created with yy_create_buffer()
 * 
 */
    static void yy_delete_buffer (YY_BUFFER_STATE  b )
{
    
        if ( ! b )
                return;

        if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
          YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) nullptr;

        if ( b->yy_is_our_buffer )
                yyfree((void *) b->yy_ch_buf  );

        yyfree((void *) b  );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a yyrestart() or at EOF.
 */
    static void yy_init_buffer  (YY_BUFFER_STATE  b, FILE * file )

{
        int oerrno = errno;
    
        yy_flush_buffer(b );

        b->yy_input_file = file;
        b->yy_fill_buffer = 1;

    /* If b is the current buffer, then yy_init_buffer was _probably_
     * called from yyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

        b->yy_is_interactive = 0;
    
        errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * 
 */
    static void yy_flush_buffer (YY_BUFFER_STATE  b )
{
        if ( ! b )
                return;

        b->yy_n_chars = 0;

        /* We always need two end-of-buffer characters.  The first causes
         * a transition to the end-of-buffer state.  The second causes
         * a jam in that state.
         */
        b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
        b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

        b->yy_buf_pos = &b->yy_ch_buf[0];

        b->yy_at_bol = 1;
        b->yy_buffer_status = YY_BUFFER_NEW;

        if ( b == YY_CURRENT_BUFFER )
                yy_load_buffer_state( );
}

/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  
 */
[[maybe_unused]] static void yypush_buffer_state (YY_BUFFER_STATE new_buffer )
{
  if (new_buffer == nullptr) return;

  yyensure_buffer_stack();

  /* This block is copied from yy_switch_to_buffer. */
  if (YY_CURRENT_BUFFER) {
    /* Flush out information for old buffer. */
    *(yy_c_buf_p) = (yy_hold_char);
    YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
    YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
                }

        /* Only push if top exists. Otherwise, replace top. */
        if (YY_CURRENT_BUFFER)
                (yy_buffer_stack_top)++;
        YY_CURRENT_BUFFER_LVALUE = new_buffer;

        /* copied from yy_switch_to_buffer. */
        yy_load_buffer_state( );
        (yy_did_buffer_switch_on_eof) = 1;
}

/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  
 */
[[maybe_unused]] static void yypop_buffer_state (void)
{
        if (!YY_CURRENT_BUFFER)
                return;

        yy_delete_buffer(YY_CURRENT_BUFFER );
        YY_CURRENT_BUFFER_LVALUE = nullptr;
        if ((yy_buffer_stack_top) > 0)
                --(yy_buffer_stack_top);

        if (YY_CURRENT_BUFFER) {
                yy_load_buffer_state( );
                (yy_did_buffer_switch_on_eof) = 1;
        }
}

/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
static void yyensure_buffer_stack (void)
{
        yy_size_t num_to_alloc;
    
        if (!(yy_buffer_stack)) {

                /* First allocation is just for 2 elements, since we don't know if this
                 * scanner will even need a stack. We use 2 instead of 1 to avoid an
                 * immediate realloc on the next call.
         */
                num_to_alloc = 1;
                (yy_buffer_stack) = (struct yy_buffer_state**)yyalloc
                                                                (num_to_alloc * sizeof(struct yy_buffer_state*)
                                                                );
                if ( ! (yy_buffer_stack) )
                        YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );
                                                                  
                memset((yy_buffer_stack), 0, num_to_alloc * sizeof(struct yy_buffer_state*));
                                
                (yy_buffer_stack_max) = num_to_alloc;
                (yy_buffer_stack_top) = 0;
                return;
        }

        if ((yy_buffer_stack_top) >= ((yy_buffer_stack_max)) - 1){

                /* Increase the buffer to prepare for a possible push. */
                int grow_size = 8 /* arbitrary grow size */;

                num_to_alloc = (yy_buffer_stack_max) + grow_size;
                (yy_buffer_stack) = (struct yy_buffer_state**)yyrealloc
                                                                ((yy_buffer_stack),
                                                                num_to_alloc * sizeof(struct yy_buffer_state*)
                                                                );
                if ( ! (yy_buffer_stack) )
                        YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

                /* zero only the new slots.*/
                memset((yy_buffer_stack) + (yy_buffer_stack_max), 0, grow_size * sizeof(struct yy_buffer_state*));
                (yy_buffer_stack_max) = num_to_alloc;
        }
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

static void yy_fatal_error (yyconst char* msg )
{
        (void) fprintf( stderr, "%s\n", msg );
        exit( YY_EXIT_FAILURE );
}

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
        do \
                { \
                /* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
                yytext[yyleng] = (yy_hold_char); \
                (yy_c_buf_p) = yytext + yyless_macro_arg; \
                (yy_hold_char) = *(yy_c_buf_p); \
                *(yy_c_buf_p) = '\0'; \
                yyleng = yyless_macro_arg; \
                } \
        while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/** Get the current line number.
 * 
 */
[[maybe_unused]] static int yyget_lineno  (void)
{
        
    return yylineno;
}

/** Get the input stream.
 * 
 */
[[maybe_unused]] static FILE *yyget_in  (void)
{
        return yyin;
}

/** Get the output stream.
 * 
 */
[[maybe_unused]] static FILE *yyget_out  (void)
{
        return yyout;
}

/** Get the length of the current token.
 * 
 */
yy_size_t yyget_leng  (void)
{
        return yyleng;
}

/** Get the current token.
 * 
 */

[[maybe_unused]] static char *yyget_text  (void)
{
        return yytext;
}

/** Set the current line number.
 * @param line_number line number
 * 
 */
[[maybe_unused]] static void yyset_lineno (int  line_number )
{
    
    yylineno = line_number;
}

/** Set the input stream. This does not discard the current
 * input buffer.
 * @param in_str A readable stream.
 * 
 * @see yy_switch_to_buffer
 */
[[maybe_unused]] static void yyset_in (FILE *  in_str )
{
        yyin = in_str ;
}

[[maybe_unused]] static void yyset_out (FILE *  out_str )
{
        yyout = out_str ;
}

[[maybe_unused]] static int yyget_debug  (void)
{
        return yy_flex_debug;
}

[[maybe_unused]] static void yyset_debug (int  bdebug )
{
        yy_flex_debug = bdebug ;
}

static int yy_init_globals (void)
{
        /* Initialization is the same as for the non-reentrant scanner.
     * This function is called from yylex_destroy(), so don't allocate here.
     */

        (yy_buffer_stack) = nullptr;
        (yy_buffer_stack_top) = 0;
        (yy_buffer_stack_max) = 0;
        (yy_c_buf_p) = (char *)nullptr;
        (yy_init) = 0;
        (yy_start) = 0;

/* Defined in main.c */
#ifdef YY_STDINIT
    yyin = stdin;
    yyout = stdout;
#else
  yyin = (FILE *)nullptr;
  yyout = (FILE *)nullptr;
#endif

    /* For future reference: Set errno on error, since we are called by
     * yylex_init()
     */
    return 0;
}

/* yylex_destroy is for both reentrant and non-reentrant scanners. */
[[maybe_unused]] static int yylex_destroy  (void)
{
    
    /* Pop the buffer stack, destroying each element. */
        while(YY_CURRENT_BUFFER){
                yy_delete_buffer(YY_CURRENT_BUFFER  );
                YY_CURRENT_BUFFER_LVALUE = nullptr;
                yypop_buffer_state();
        }

        /* Destroy the stack itself. */
        yyfree((yy_buffer_stack) );
        (yy_buffer_stack) = nullptr;

        /* Reset the globals. This is important in a non-reentrant scanner so
         * the next time yylex() is called, initialization will occur. */
        yy_init_globals();

        return 0;
}

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, yyconst char * s2, int n )
{
        int i;
        for ( i = 0; i < n; ++i )
                s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (yyconst char * s )
{
        int n;
        for ( n = 0; s[n]; ++n )
                ;

        return n;
}
#endif

static void *yyalloc (yy_size_t  size )
{
        return (void *) malloc( size );
}

static void *yyrealloc  (void * ptr, yy_size_t  size )
{
        /* The cast to (char *) in the following accommodates both
         * implementations that use char* generic pointers, and those
         * that use void* generic pointers.  It works with the latter
         * because both ANSI C and C++ allow castless assignment from
         * any pointer type to void*, and deal with argument conversions
         * as though doing an assignment.
         */
        return (void *) realloc( (char *) ptr, size );
}

static void yyfree (void * ptr )
{
        free( (char *) ptr );   /* see yyrealloc() for (char *) cast */
}

#define YYTABLES_NAME "yytables"

#line 650 "pars0lex.l"



/**********************************************************************
Release any resources used by the lexer. */
void
pars_lexer_close(void)
/*==================*/
{
        yylex_destroy();
        free(stringbuf);
        stringbuf = nullptr;
        stringbuf_len_alloc = stringbuf_len = 0;
}

