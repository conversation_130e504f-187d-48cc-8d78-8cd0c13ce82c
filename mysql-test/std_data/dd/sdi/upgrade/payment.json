["ibd2sdi"
,
{
	"type": 1,
	"id": X,
	"object":
		{
    "mysqld_version_id": X,
    "dd_version": X,
    "sdi_version": X,
    "dd_object_type": "Table",
    "dd_object": {
        "name": "payment",
        "mysql_version_id": X,
        "created": NNN,
        "last_altered": NNN,
        "hidden": 1,
        "options": "avg_row_length=0;encrypt_type=N;key_block_size=0;keys_disabled=0;pack_record=0;stats_auto_recalc=0;stats_sample_pages=0;",
        "columns": [
            {
                "name": "payment_id",
                "type": 3,
                "is_nullable": false,
                "is_zerofill": false,
                "is_unsigned": true,
                "is_auto_increment": true,
                "is_virtual": false,
                "hidden": 1,
                "ordinal_position": 1,
                "char_length": 5,
                "numeric_precision": 5,
                "numeric_scale": 0,
                "numeric_scale_null": false,
                "datetime_precision": 0,
                "datetime_precision_null": 1,
                "has_no_default": false,
                "default_value_null": false,
                "srs_id_null": true,
                "srs_id": 0,
                "default_value": "",
                "default_value_utf8_null": true,
                "default_value_utf8": "",
                "default_option": "",
                "update_option": "",
                "comment": "",
                "generation_expression": "",
                "generation_expression_utf8": "",
                "options": "interval_count=0;",
                "se_private_data": "table_id=X",
                "engine_attribute": "",
                "secondary_engine_attribute": "",
                "column_key": 2,
                "column_type_utf8": "smallint unsigned",
                "elements": [],
                "collation_id": 8,
                "is_explicit_collation": false
            },
            {
                "name": "customer_id",
                "type": 3,
                "is_nullable": false,
                "is_zerofill": false,
                "is_unsigned": true,
                "is_auto_increment": false,
                "is_virtual": false,
                "hidden": 1,
                "ordinal_position": 2,
                "char_length": 5,
                "numeric_precision": 5,
                "numeric_scale": 0,
                "numeric_scale_null": false,
                "datetime_precision": 0,
                "datetime_precision_null": 1,
                "has_no_default": true,
                "default_value_null": false,
                "srs_id_null": true,
                "srs_id": 0,
                "default_value": "",
                "default_value_utf8_null": true,
                "default_value_utf8": "",
                "default_option": "",
                "update_option": "",
                "comment": "",
                "generation_expression": "",
                "generation_expression_utf8": "",
                "options": "interval_count=0;",
                "se_private_data": "table_id=X",
                "engine_attribute": "",
                "secondary_engine_attribute": "",
                "column_key": 4,
                "column_type_utf8": "smallint unsigned",
                "elements": [],
                "collation_id": 8,
                "is_explicit_collation": false
            },
            {
                "name": "staff_id",
                "type": 2,
                "is_nullable": false,
                "is_zerofill": false,
                "is_unsigned": true,
                "is_auto_increment": false,
                "is_virtual": false,
                "hidden": 1,
                "ordinal_position": 3,
                "char_length": 3,
                "numeric_precision": 3,
                "numeric_scale": 0,
                "numeric_scale_null": false,
                "datetime_precision": 0,
                "datetime_precision_null": 1,
                "has_no_default": true,
                "default_value_null": false,
                "srs_id_null": true,
                "srs_id": 0,
                "default_value": "",
                "default_value_utf8_null": true,
                "default_value_utf8": "",
                "default_option": "",
                "update_option": "",
                "comment": "",
                "generation_expression": "",
                "generation_expression_utf8": "",
                "options": "interval_count=0;",
                "se_private_data": "table_id=X",
                "engine_attribute": "",
                "secondary_engine_attribute": "",
                "column_key": 4,
                "column_type_utf8": "tinyint unsigned",
                "elements": [],
                "collation_id": 8,
                "is_explicit_collation": false
            },
            {
                "name": "rental_id",
                "type": 4,
                "is_nullable": true,
                "is_zerofill": false,
                "is_unsigned": false,
                "is_auto_increment": false,
                "is_virtual": false,
                "hidden": 1,
                "ordinal_position": 4,
                "char_length": 11,
                "numeric_precision": 10,
                "numeric_scale": 0,
                "numeric_scale_null": false,
                "datetime_precision": 0,
                "datetime_precision_null": 1,
                "has_no_default": false,
                "default_value_null": true,
                "srs_id_null": true,
                "srs_id": 0,
                "default_value": "",
                "default_value_utf8_null": true,
                "default_value_utf8": "",
                "default_option": "",
                "update_option": "",
                "comment": "",
                "generation_expression": "",
                "generation_expression_utf8": "",
                "options": "interval_count=0;",
                "se_private_data": "table_id=X",
                "engine_attribute": "",
                "secondary_engine_attribute": "",
                "column_key": 4,
                "column_type_utf8": "int",
                "elements": [],
                "collation_id": 8,
                "is_explicit_collation": false
            },
            {
                "name": "amount",
                "type": 21,
                "is_nullable": false,
                "is_zerofill": false,
                "is_unsigned": false,
                "is_auto_increment": false,
                "is_virtual": false,
                "hidden": 1,
                "ordinal_position": 5,
                "char_length": 7,
                "numeric_precision": 5,
                "numeric_scale": 2,
                "numeric_scale_null": false,
                "datetime_precision": 0,
                "datetime_precision_null": 1,
                "has_no_default": true,
                "default_value_null": false,
                "srs_id_null": true,
                "srs_id": 0,
                "default_value": "",
                "default_value_utf8_null": true,
                "default_value_utf8": "",
                "default_option": "",
                "update_option": "",
                "comment": "",
                "generation_expression": "",
                "generation_expression_utf8": "",
                "options": "interval_count=0;",
                "se_private_data": "table_id=X",
                "engine_attribute": "",
                "secondary_engine_attribute": "",
                "column_key": 1,
                "column_type_utf8": "decimal(5,2)",
                "elements": [],
                "collation_id": 8,
                "is_explicit_collation": false
            },
            {
                "name": "payment_date",
                "type": 19,
                "is_nullable": false,
                "is_zerofill": false,
                "is_unsigned": false,
                "is_auto_increment": false,
                "is_virtual": false,
                "hidden": 1,
                "ordinal_position": 6,
                "char_length": 19,
                "numeric_precision": 0,
                "numeric_scale": 0,
                "numeric_scale_null": true,
                "datetime_precision": 0,
                "datetime_precision_null": 0,
                "has_no_default": true,
                "default_value_null": false,
                "srs_id_null": true,
                "srs_id": 0,
                "default_value": "",
                "default_value_utf8_null": true,
                "default_value_utf8": "",
                "default_option": "",
                "update_option": "",
                "comment": "",
                "generation_expression": "",
                "generation_expression_utf8": "",
                "options": "interval_count=0;",
                "se_private_data": "table_id=X",
                "engine_attribute": "",
                "secondary_engine_attribute": "",
                "column_key": 1,
                "column_type_utf8": "datetime",
                "elements": [],
                "collation_id": 8,
                "is_explicit_collation": false
            },
            {
                "name": "last_update",
                "type": 18,
                "is_nullable": false,
                "is_zerofill": false,
                "is_unsigned": false,
                "is_auto_increment": false,
                "is_virtual": false,
                "hidden": 1,
                "ordinal_position": 7,
                "char_length": 19,
                "numeric_precision": 0,
                "numeric_scale": 0,
                "numeric_scale_null": true,
                "datetime_precision": 0,
                "datetime_precision_null": 0,
                "has_no_default": false,
                "default_value_null": false,
                "srs_id_null": true,
                "srs_id": 0,
                "default_value": "",
                "default_value_utf8_null": false,
                "default_value_utf8": "CURRENT_TIMESTAMP",
                "default_option": "CURRENT_TIMESTAMP",
                "update_option": "CURRENT_TIMESTAMP",
                "comment": "",
                "generation_expression": "",
                "generation_expression_utf8": "",
                "options": "interval_count=0;",
                "se_private_data": "table_id=X",
                "engine_attribute": "",
                "secondary_engine_attribute": "",
                "column_key": 1,
                "column_type_utf8": "timestamp",
                "elements": [],
                "collation_id": 8,
                "is_explicit_collation": false
            },
            {
                "name": "DB_TRX_ID",
                "type": 10,
                "is_nullable": false,
                "is_zerofill": false,
                "is_unsigned": false,
                "is_auto_increment": false,
                "is_virtual": false,
                "hidden": 2,
                "ordinal_position": 8,
                "char_length": 6,
                "numeric_precision": 0,
                "numeric_scale": 0,
                "numeric_scale_null": true,
                "datetime_precision": 0,
                "datetime_precision_null": 1,
                "has_no_default": false,
                "default_value_null": true,
                "srs_id_null": true,
                "srs_id": 0,
                "default_value": "",
                "default_value_utf8_null": true,
                "default_value_utf8": "",
                "default_option": "",
                "update_option": "",
                "comment": "",
                "generation_expression": "",
                "generation_expression_utf8": "",
                "options": "",
                "se_private_data": "table_id=X",
                "engine_attribute": "",
                "secondary_engine_attribute": "",
                "column_key": 1,
                "column_type_utf8": "",
                "elements": [],
                "collation_id": 63,
                "is_explicit_collation": false
            },
            {
                "name": "DB_ROLL_PTR",
                "type": 9,
                "is_nullable": false,
                "is_zerofill": false,
                "is_unsigned": false,
                "is_auto_increment": false,
                "is_virtual": false,
                "hidden": 2,
                "ordinal_position": 9,
                "char_length": 7,
                "numeric_precision": 0,
                "numeric_scale": 0,
                "numeric_scale_null": true,
                "datetime_precision": 0,
                "datetime_precision_null": 1,
                "has_no_default": false,
                "default_value_null": true,
                "srs_id_null": true,
                "srs_id": 0,
                "default_value": "",
                "default_value_utf8_null": true,
                "default_value_utf8": "",
                "default_option": "",
                "update_option": "",
                "comment": "",
                "generation_expression": "",
                "generation_expression_utf8": "",
                "options": "",
                "se_private_data": "table_id=X",
                "engine_attribute": "",
                "secondary_engine_attribute": "",
                "column_key": 1,
                "column_type_utf8": "",
                "elements": [],
                "collation_id": 63,
                "is_explicit_collation": false
            }
        ],
        "schema_ref": "sakila",
        "se_private_id":NNN,
        "engine": "InnoDB",
        "last_checked_for_upgrade_version_id": X,
        "comment": "",
        "se_private_data": "autoinc=16049;version=0;",
        "engine_attribute": "",
        "secondary_engine_attribute": "",
        "row_format": 2,
        "partition_type": 0,
        "partition_expression": "",
        "partition_expression_utf8": "",
        "default_partitioning": 0,
        "subpartition_type": 0,
        "subpartition_expression": "",
        "subpartition_expression_utf8": "",
        "default_subpartitioning": 0,
        "indexes": [
            {
                "name": "PRIMARY",
                "hidden": false,
                "is_generated": false,
                "ordinal_position": 1,
                "comment": "",
                "options": "flags=0;",
                "se_private_data": "id=A;root=B;space_id=C;table_id=D;trx_id=E",
                "type": 1,
                "algorithm": 2,
                "is_algorithm_explicit": false,
                "is_visible": true,
                "engine": "InnoDB",
                "engine_attribute": "",
                "secondary_engine_attribute": "",
                "elements": [
                    {
                        "ordinal_position": 1,
                        "length": 2,
                        "order": 2,
                        "hidden": false,
                        "column_opx": 0
                    },
                    {
                        "ordinal_position": 2,
                        "length": 4294967295,
                        "order": 2,
                        "hidden": true,
                        "column_opx": 7
                    },
                    {
                        "ordinal_position": 3,
                        "length": 4294967295,
                        "order": 2,
                        "hidden": true,
                        "column_opx": 8
                    },
                    {
                        "ordinal_position": 4,
                        "length": 4294967295,
                        "order": 2,
                        "hidden": true,
                        "column_opx": 1
                    },
                    {
                        "ordinal_position": 5,
                        "length": 4294967295,
                        "order": 2,
                        "hidden": true,
                        "column_opx": 2
                    },
                    {
                        "ordinal_position": 6,
                        "length": 4294967295,
                        "order": 2,
                        "hidden": true,
                        "column_opx": 3
                    },
                    {
                        "ordinal_position": 7,
                        "length": 4294967295,
                        "order": 2,
                        "hidden": true,
                        "column_opx": 4
                    },
                    {
                        "ordinal_position": 8,
                        "length": 4294967295,
                        "order": 2,
                        "hidden": true,
                        "column_opx": 5
                    },
                    {
                        "ordinal_position": 9,
                        "length": 4294967295,
                        "order": 2,
                        "hidden": true,
                        "column_opx": 6
                    }
                ],
                "tablespace_ref": "sakila/payment"
            },
            {
                "name": "idx_fk_staff_id",
                "hidden": false,
                "is_generated": false,
                "ordinal_position": 2,
                "comment": "",
                "options": "flags=0;",
                "se_private_data": "id=A;root=B;space_id=C;table_id=D;trx_id=E",
                "type": 3,
                "algorithm": 2,
                "is_algorithm_explicit": false,
                "is_visible": true,
                "engine": "InnoDB",
                "engine_attribute": "",
                "secondary_engine_attribute": "",
                "elements": [
                    {
                        "ordinal_position": 1,
                        "length": 1,
                        "order": 2,
                        "hidden": false,
                        "column_opx": 2
                    },
                    {
                        "ordinal_position": 2,
                        "length": 4294967295,
                        "order": 2,
                        "hidden": true,
                        "column_opx": 0
                    }
                ],
                "tablespace_ref": "sakila/payment"
            },
            {
                "name": "idx_fk_customer_id",
                "hidden": false,
                "is_generated": false,
                "ordinal_position": 3,
                "comment": "",
                "options": "flags=0;",
                "se_private_data": "id=A;root=B;space_id=C;table_id=D;trx_id=E",
                "type": 3,
                "algorithm": 2,
                "is_algorithm_explicit": false,
                "is_visible": true,
                "engine": "InnoDB",
                "engine_attribute": "",
                "secondary_engine_attribute": "",
                "elements": [
                    {
                        "ordinal_position": 1,
                        "length": 2,
                        "order": 2,
                        "hidden": false,
                        "column_opx": 1
                    },
                    {
                        "ordinal_position": 2,
                        "length": 4294967295,
                        "order": 2,
                        "hidden": true,
                        "column_opx": 0
                    }
                ],
                "tablespace_ref": "sakila/payment"
            },
            {
                "name": "fk_payment_rental",
                "hidden": false,
                "is_generated": true,
                "ordinal_position": 4,
                "comment": "",
                "options": "flags=0;",
                "se_private_data": "id=A;root=B;space_id=C;table_id=D;trx_id=E",
                "type": 3,
                "algorithm": 2,
                "is_algorithm_explicit": false,
                "is_visible": true,
                "engine": "InnoDB",
                "engine_attribute": "",
                "secondary_engine_attribute": "",
                "elements": [
                    {
                        "ordinal_position": 1,
                        "length": 4,
                        "order": 2,
                        "hidden": false,
                        "column_opx": 3
                    },
                    {
                        "ordinal_position": 2,
                        "length": 4294967295,
                        "order": 2,
                        "hidden": true,
                        "column_opx": 0
                    }
                ],
                "tablespace_ref": "sakila/payment"
            }
        ],
        "foreign_keys": [
            {
                "name": "fk_payment_customer",
                "match_option": 1,
                "update_rule": 3,
                "delete_rule": 2,
                "unique_constraint_name": "PRIMARY",
                "referenced_table_catalog_name": "def",
                "referenced_table_schema_name": "sakila",
                "referenced_table_name": "customer",
                "elements": [
                    {
                        "column_opx": 1,
                        "ordinal_position": 1,
                        "referenced_column_name": "customer_id"
                    }
                ]
            },
            {
                "name": "fk_payment_rental",
                "match_option": 1,
                "update_rule": 3,
                "delete_rule": 4,
                "unique_constraint_name": "PRIMARY",
                "referenced_table_catalog_name": "def",
                "referenced_table_schema_name": "sakila",
                "referenced_table_name": "rental",
                "elements": [
                    {
                        "column_opx": 3,
                        "ordinal_position": 1,
                        "referenced_column_name": "rental_id"
                    }
                ]
            },
            {
                "name": "fk_payment_staff",
                "match_option": 1,
                "update_rule": 3,
                "delete_rule": 2,
                "unique_constraint_name": "PRIMARY",
                "referenced_table_catalog_name": "def",
                "referenced_table_schema_name": "sakila",
                "referenced_table_name": "staff",
                "elements": [
                    {
                        "column_opx": 2,
                        "ordinal_position": 1,
                        "referenced_column_name": "staff_id"
                    }
                ]
            }
        ],
        "check_constraints": [],
        "partitions": [],
        "collation_id": 33
    }
}
}
,
{
	"type": 2,
	"id": X,
	"object":
		{
    "mysqld_version_id": X,
    "dd_version": X,
    "sdi_version": X,
    "dd_object_type": "Tablespace",
    "dd_object": {
        "name": "sakila/payment",
        "comment": "",
        "options": "encryption=N;",
        "se_private_data": "flags=X;id=Y;server_version=Z;space_version=M;state=normal;",
        "engine": "InnoDB",
        "engine_attribute": "",
        "files": [
            {
                "ordinal_position": 1,
                "filename": "./sakila/payment.ibd",
                "se_private_data": ""
            }
        ]
    }
}
}
]
